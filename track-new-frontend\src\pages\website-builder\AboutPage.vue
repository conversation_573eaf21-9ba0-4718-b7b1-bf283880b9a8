<template>
  <div class="builder-page">
    <div class="flex flex-col md:flex-row justify-between items-center">
      <h1 class="web-head">About Us Page <span class="text-red-500">*</span></h1>
      <div class="w-full md:w-auto md:self-end">
        <EnablePageName :pages="is_onSetting" @updatePagesEnabled="handleToggle"></EnablePageName>
      </div>
    </div>

    <!-- Head Title -->
    <div class="mb-4">
      <label class="block font-bold mb-2">Head Title <span class="text-red-500">*</span></label>
      <input type="text" v-model="localAboutData.headTitle" class="border p-2 rounded w-full"
        placeholder="Enter title here, like Abouts" required />
    </div>

    <!-- Description -->
    <div class="mb-6 relative">
      <label class="block font-bold mb-2">Description <span class="text-red-500">*</span></label>
      <!-- <textarea v-model="localAboutData.description" class="border p-2 rounded w-full" placeholder="Description" rows="6"
          required></textarea> -->
      <div class="grid grid-cols-1">
        <CkEditorForm label="Description" :textData="localAboutData.description || ''"
          @editorSubmit="handleEditorSubmit" :company_id="companyId" />
      </div>

    </div>
    <!--image-->
    <div class="my-5">
      <label class="block font-normal text-xs mb-1">Image <span class="text-gray-500 text-xs">500 <font-awesome-icon
            icon="fa-solid fa-xmark" /> 500</span></label>
      <input type="file" @change="onFileChange" ref="imageServices" class="border p-2 rounded w-full text-xs"
        accept="image/png, image/jpeg" />
      <div v-if="circle_loader_photo"
        class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
        <CircleLoader :loading="true"></CircleLoader>
      </div>
      <div v-if="localAboutData.imageUrl" class="mt-4 relative flex justify-center items-center h-1/4 ">
        <img :src="localAboutData.imageUrl" alt="Uploaded Image"
          class="w-1/4 object-cover text-sm rounded-lg cursor-pointer items-center" @click="viewImage" />
        <button @click="removeImage"
          class="absolute top-0 right-[25%] bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
          &times;
        </button>
      </div>
    </div>

    <!-- Bottom Navigation Buttons -->
    <NavigationButtons :pageTitle="'About Us Page'" @goToNextPage="goToNextPage" @goToPrevPage="goToPrevPage" />
    <Toaster :show="showModal" :message="message" :type="type_toaster" @update:show="showModal = false"></Toaster>
    <Loader :showModal="open_loader"></Loader>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>

  </div>
</template>

<script>
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import EnablePageName from './EnablePageName.vue';
import CkEditorForm from './CkEditorForm.vue';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import imageService from '@/services/imageService';

export default {
  props: {
    aboutData: {
      type: Object,
      default: () => ({ headTitle: '', description: '' }),
    },
    is_updated: { type: Boolean, required: true },
    pages: {
      type: Object,
      required: true
    },
    companyId: {
      type: String,
      required: true
    },
  },
  components: {
    NavigationButtons,
    EnablePageName,
    CkEditorForm,
    confirmbox,
  },
  data() {
    return {
      localAboutData: { ...this.aboutData },
      //----is on settings---
      is_onSetting: { is_on: true, name: 'About US' },
      circle_loader_photo: false,
      open_loader: false,
      //--toaster---
      showModal: false,
      type_toaster: 'success',
      message: '',
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
    };
  },

  watch: {
    localAboutData: {
      handler(newData) {
        this.$emit('updateAboutData', newData); // Emit updated data to parent
      },
      deep: true,
    },
    is_updated: {
      deep: true,
      handler(newValue) {
        this.$emit('updateAboutData', this.localAboutData);
        this.$emit('updatePagesSetting', { aboutus: this.is_onSetting });
      }
    },
    is_onSetting: {
      deep: true,
      handler(newValue) {
        this.$emit('updatePagesSetting', { aboutus: newValue });
      }
    }
  },
  mounted() {
    if (this.pages && this.pages.aboutus !== undefined) {
      this.is_onSetting = this.pages.aboutus;
    }
  },

  methods: {
    handleSubmit() {
      console.log('About Us Page Data:', this.aboutData);
      // Handle form submission, send data to API if needed
    },
    goToNextPage() {
      this.$emit('updateAboutData', this.localAboutData);
      this.$emit('updatePagesSetting', { aboutus: this.is_onSetting });
      this.$emit('submitData');
      this.$emit('goToNextPage'); // Emit an event to the parent to move to the next page (like Testimonial Page)
    },
    goToPrevPage() {
      this.$emit('updateAboutData', this.localAboutData);
      this.$emit('updatePagesSetting', { aboutus: this.is_onSetting });
      this.$emit('submitData');
      this.$emit('goToPrevPage'); // Emit an event to the parent to move to the previous page
    },
    encodeBase64(data) {
      return btoa(data); // Encode data to Base64
    },
    decodeBase64(data) {
      return atob(data); // Decode data from Base64
    },
    //--handle switch--
    handleToggle(data) {
      // Perform any additional actions when toggled
      // this.is_onSetting.is_on = !this.is_onSetting.is_on;
      if (data) {
        this.is_onSetting = data;
      }
    },
    //--editorData--
    handleEditorSubmit(data) {
      if (data) {
        this.localAboutData.description = data;
      } else {
        this.localAboutData.description = '';
      }
    },
    //---image--
    async onFileChange(event) {
      const file = event.target.files[0];
      if (file) {
        this.circle_loader_photo = true;
        try {
          if (this.localAboutData.imageUrl && this.localAboutData.imageUrl !== '') {
            const response = await imageService.deleteImage(this.localAboutData.imageUrl, 'about', this.companyId);
            this.toasterMessages({ msg: 'Exist iamge removed successfully', type: 'success' });
          }
          const response = await imageService.uploadImage(file, 'about', this.companyId);
          this.localAboutData.imageUrl = response.media_url;
          this.circle_loader_photo = false;
          this.toasterMessages({ msg: 'Image uploaded successfully', type: 'success' });
        } catch (error) {
          console.error("Error uploading image:", error);
          this.toasterMessages({ msg: 'Failed to upload image.', type: 'warning' });
          this.circle_loader_photo = false;
        }
      }
    },
    async removeImage() {
      this.open_confirmBox = true;
    },
    async deleteRecord() {
      if (this.localAboutData.imageUrl) {
        this.open_loader = true;
        try {
          await imageService.deleteImage(this.localAboutData.imageUrl, 'about'); // Delete image from server
          this.localAboutData.imageUrl = null; // Clear image URL    
          this.$refs.imageServices.value = '';
          this.toasterMessages({ msg: 'Image Deleted successfully', type: 'success' });
          this.closeconfirmBoxData();
        } catch (error) {
          console.error('Error deleting image:', error);
          if (error.response.data && error.response.data.error === 'Image not found') {
            if (this.localAboutData.imageUrl) {
              this.localAboutData.imageUrl = null;
            }
          }
          this.toasterMessages({ msg: error.response.data.error, type: 'warning' });
          this.closeconfirmBoxData();
        }
      } else {
        this.closeconfirmBoxData();
      }
    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
      this.open_loader = false;
    },
    //--toaster message---
    toasterMessages(data) {
      this.message = data.msg;
      this.type_toaster = data.type;
      this.showModal = true;
    },
  },
};
</script>

<style scoped></style>
