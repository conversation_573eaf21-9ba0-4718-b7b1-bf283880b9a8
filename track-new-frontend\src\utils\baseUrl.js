// utils/baseUrl.js
export function getBaseUrl() {
    const hostname = window.location.hostname;
    if (hostname === 'app.track-new.com') {
        return 'https://api.track-new.com/api'; // Production URL
    } else if (hostname === 'devapp.track-new.com') {
        return 'https://devapi.track-new.com/api'; // Development URL
    } else if (hostname === '************' || hostname === '************:8000') {
        return 'https://devapi.track-new.com/api'; // Local Development
    } else {
        return 'https://devapi.track-new.com/api'; // Default fallback URL
    }
}
// Function to get query parameters as an object from the current URL
export function getQueryParams() {
    const queryString = window.location.search; // Get the query string part (e.g., ?type=validate&user=admin)
    const queryParams = {};

    if (queryString) {
        const params = new URLSearchParams(queryString);
        for (const [key, value] of params.entries()) {
            queryParams[key] = value; // Add each key-value pair to the object
        }
    }

    return queryParams; // Return the query parameters as an object
}
