// store/modules/invoice_setting.js
import axios from "axios";

const state = {
  invoice_setting: [],
  lastFetchTime: null,
  isFetching: false,
};

const mutations = {
  SET_INVOICESETTING(state, invoiceData) {    
    state.invoice_setting = invoiceData;
  },
  RESET_STATE(state) {
    state.invoice_setting = [];  
    state.lastFetchTime = null;
    state.isFetching = false;
  },
  SET_LAST_FETCH_TIME(state, time) {
    state.lastFetchTime = time;
  },
  SET_IS_FETCHING(state, status) {
    state.isFetching = status;
  },
  UPDATE_INVOICE_SETTING(state, updatedSetting) {
    // Find and update specific invoice setting
    const index = state.invoice_setting.findIndex(
      item => item.id === updatedSetting.id
    );
    if (index !== -1) {
      state.invoice_setting.splice(index, 1, updatedSetting);
    }
  }
};

const actions = {
  async updateInvoice({ commit }, invoiceData) {
    try {
      commit('SET_IS_FETCHING', true);
      const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
      
      // Make API call to update invoice setting
      const response = await axios.put('/invoice_settings', {
        ...invoiceData,
        company_id
      });
      
      // Update the specific invoice setting in state
      commit('UPDATE_INVOICE_SETTING', response.data.data);
      commit('SET_IS_FETCHING', false);
      return response.data;
    } catch (error) {
      commit('SET_IS_FETCHING', false);
      console.error('Error updating invoice:', error);
      throw error;
    }
  },

  async fetchInvoiceSetting({ commit }, is_updated = false) {
    try {
      const now = new Date().toISOString();
      const cacheTime = is_updated ? 10 : 60 * 1000; // 10ms if forced update, else 1 minute
      
      if (state.isFetching || 
          (state.lastFetchTime && 
           (new Date(now) - new Date(state.lastFetchTime) < cacheTime))) {
        return;
      }
      
      const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
      if (!company_id) return;
      
      commit('SET_IS_FETCHING', true);
      const response = await axios.get('/invoice_settings', { 
        params: { company_id } 
      });
      
      commit('SET_INVOICESETTING', response.data.data);
      commit('SET_LAST_FETCH_TIME', now);
      commit('SET_IS_FETCHING', false);
      return response.data.data;
    } catch (error) {
      commit('SET_IS_FETCHING', false);
      console.error('Error fetching invoice settings:', error);
      throw error;
    }
  },    
};

const getters = {
  currentInvoice: state => state.invoice_setting,
  getInvoiceById: state => id => state.invoice_setting.find(item => item.id === id)
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
};
