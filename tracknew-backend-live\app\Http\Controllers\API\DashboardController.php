<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Dashboard;
use App\Models\Services;
use App\Models\ServiceAssigns;
use App\Models\Amc;
use App\Models\AmcUsers;
use App\Models\User;
use App\Models\Estimation;
use App\Models\Customer;
use App\Models\leads;
use App\Models\Proforma;
use App\Models\Expenses;
use App\Models\Sales;
use App\Models\Rma;

use Auth;


class DashboardController extends Controller
{ 
    /**
 * @param Request $request
 * @return Response
 *
 * @OA\Get(
 *      path="/dashboard/values",
 *      summary="get Values",
 *      tags={"User"},
 *      description="Dashboard Values",
 *      @OA\RequestBody(
 *          description="Dashboard values",
 *          required=true,
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(
 *                  type="object",
 * 
 *                  @OA\Property(
 *                      property="company_id",
 *                      description="User's shop id",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="dates",
 *                      description="dates",
 *                      type="string"
 *                  ),
 *             )
 *          )
 *  
 *      ),
 *      @OA\Response(
 *          response=200,
 *          description="successful operation",
 *          @OA\Schema(
 *              type="object",
 *              @OA\Property(
 *                  property="success",
 *                  type="boolean"
 *              ),
 *              @OA\Property(
 *                  property="data",
 *                  ref="#/definitions/Dashboard"
 *              ),
 *              @OA\Property(
 *                  property="message",
 *                  type="string"
 *              )
 *          )
 *      )
 * )
 */   
  
  
    public function dashboard_values(Request $request){

        $companyId = $request->query('company_id');
        $dates = $request->query('dates');

        $data = Dashboard::breadboardValues($companyId, $dates);
       
       
        echo json_encode($data);
    }
    
    public function dashboardData()
    {

        if(Auth::check()){
            $user = Auth::user();
            $company_id = $user->company_id;
            
             $isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();

            // $data['total_services']          = Services::where('company_id', $company_id)->count();  
            // $data['total_complete_services']          = Services::where('company_id', $company_id)->where('status','5')->count(); 
            // $data['total_canceled_services']          = Services::where('company_id', $company_id)->where('status','6')->count();
            // $data['total_ordertaken_services']          = Services::where('company_id', $company_id)->where('status','0')->count();
            // $data['total_hold_services']          = Services::where('company_id', $company_id)->where('status','1')->count();
            // $data['total_progres_services']          = Services::where('company_id', $company_id)->where('status','2')->count();
            // $data['total_estimate_services']          = Services::where('company_id', $company_id)->where('status','3')->count();
            // $data['total_todelivered_services']          = Services::where('company_id', $company_id)->where('status','4')->count();    
          
            // $data['total_amc']          = Amc::where('company_id', $company_id)->count(); 
            // $data['total_complete_amc']          = Amc::where('company_id', $company_id)->where('amc_status',2)->count(); 
            // $data['total_active_amc']          = Amc::where('company_id', $company_id)->whereIn('amc_status', [0, 1])->count(); 


            // $data['total_estimation']          = Estimation::where('company_id', $company_id)->count(); 
            // $data['total_complete_estimation']          = Estimation::where('company_id', $company_id)->where('status',2)->count(); 
            // $data['total_active_estimation']          = Estimation::where('company_id', $company_id)->whereIn('status',[0, 1])->count();
            
            // $data['total_leads']          = Leads::where('company_id', $company_id)->count(); 
            // $data['total_complete_leads']          = Estimation::where('company_id', $company_id)->where('status',2)->count(); 
            // $data['total_active_leads']          = Estimation::where('company_id', $company_id)->whereIn('status', [0, 1])->count(); 
            
            $data['service_statics'] =[
                [
                    'name' => 'Order Taken Services',
                    'value' => Services::where('company_id', $company_id)->where('status','0')->count()
                    ],
                    ['name' => 'Total Hold Service',
                    'value' => Services::where('company_id', $company_id)->where('status','1')->count()
                    ],
                    ['name' => 'In Progress Service',
                    'value' => Services::where('company_id', $company_id)->where('status','2')->count()
                    ],
                    ['name' => 'Material Approve Service',
                    'value' => Services::where('company_id', $company_id)->where('status','3')->count()
                    ],
                    ['name' => 'To be delivered',
                    'value' => Services::where('company_id', $company_id)->where('status','4')->count()
                    ],
                    ['name' => 'Total Delivered',
                    'value' => Services::where('company_id', $company_id)->where('status','5')->count()
                    ],
                    ['name' => 'Cancel/Return Services',
                    'value' => Services::where('company_id', $company_id)->where('status','6')->count()
                    ],
              		['name' => 'Total Complete Service',
                    'value' => Services::where('company_id', $company_id)->where('status','7')->count()
                    ]
                ];


            $data['overview'] = [
                [       'id' => "Services",
                        'total' => Services::where('company_id', $company_id)
                                           ->when(!$isAdmin, function ($query) use ($user) {
                                               $query->whereHas('users', function ($query) use ($user) {
                                                   $query->where('users.id', $user->id);
                                               });
                                           })
                                           ->count(),
                        'complete' => Services::where('company_id', $company_id)
                                              ->whereIn('status', ['5', '7'])
                                              ->when(!$isAdmin, function ($query) use ($user) {
                                                  $query->whereHas('users', function ($query) use ($user) {
                                                      $query->where('users.id', $user->id);
                                                  });
                                              })
                                              ->count(),
                        'progress' => Services::where('company_id', $company_id)
                                              ->whereIn('status', ['0', '2', '3', '1'])
                                              ->when(!$isAdmin, function ($query) use ($user) {
                                                  $query->whereHas('users', function ($query) use ($user) {
                                                      $query->where('users.id', $user->id);
                                                  });
                                              })
                                              ->count(),
                ],
                [
                    'id' => "Leads",
                     'total' => Leads::where('company_id', $company_id)
                        ->when(!$isAdmin, function ($query) use ($user) {
                            $query->where('assign_to', 'like', '%' . $user->id . '%');
                        })
                        ->count(),
                    'complete' => Leads::where('company_id', $company_id)
                           ->where('leadstatus_id', 2)
                           ->when(!$isAdmin, function ($query) use ($user) {
                               $query->where('assign_to', 'like', '%' . $user->id . '%');
                           })
                           ->count(),
                    'progress' => Leads::where('company_id', $company_id)
                           ->whereIn('leadstatus_id', [0, 1, 3])
                           ->when(!$isAdmin, function ($query) use ($user) {
                               $query->where('assign_to', 'like', '%' . $user->id . '%');
                           })
                           ->count(), 
                ],
                [
                    'id' => "Amc",
                    'total' => Amc::where('company_id', $company_id)
                                  ->when(!$isAdmin, function ($query) use ($user) {
                                      $query->whereHas('users', function ($query) use ($user) {
                                          $query->where('users.id', $user->id);
                                      });
                                  })
                                  ->count(),
                    'complete' => Amc::where('company_id', $company_id)
                                     ->where('amc_status', 2)
                                     ->when(!$isAdmin, function ($query) use ($user) {
                                         $query->whereHas('users', function ($query) use ($user) {
                                             $query->where('users.id', $user->id);
                                         });
                                     })
                                     ->count(),
                    'progress' => Amc::where('company_id', $company_id)
                                     ->whereIn('amc_status', [0, 1])
                                     ->when(!$isAdmin, function ($query) use ($user) {
                                         $query->whereHas('users', function ($query) use ($user) {
                                             $query->where('users.id', $user->id);
                                         });
                                     })
                                     ->count(),
                ],
                [
                    'id' => "Estimations",
                    'total' =>  Estimation::where('company_id', $company_id)->count(),
                    'complete' =>  Estimation::where('company_id', $company_id)->where('status', 0)->count(),
                    'progress' => Estimation::where('company_id', $company_id)->whereIn('status', [0, 1])->count(), // Assuming 'value' is a column in the Estimation table
                ],
               [
                    'id' => "Rma's",
                    'total' =>  Rma::where('company_id', $company_id)->count(),
                    'complete' =>  Rma::where('company_id', $company_id)->where('rma_status', 13)->count(),
                    'progress' => Rma::where('company_id', $company_id)->whereIn('rma_status', [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12])->count(), // Assuming 'value' is a column in the Estimation table
                ],
               [
                    'id' => "Proforma's",
                    'total' =>  Proforma::where('company_id', $company_id)->count(),
                    'complete' =>  Proforma::where('company_id', $company_id)->where('status', 1)->count(),
                    'progress' => Proforma::where('company_id', $company_id)->where('status', 0)->count(), // Assuming 'value' is a column in the Estimation table
                ],
              [
                    'id' => "Expenses",
                    'total' =>  Expenses::where('company_id', $company_id)->count(),
                    'complete' =>  Expenses::where('company_id', $company_id)->sum('amount'),
                    'progress' => Expenses::where('company_id', $company_id)->count(), // Assuming 'value' is a column in the Estimation table
                ],
              [
                    'id' => "Sales",
                    'total' =>  Sales::where('company_id', $company_id)->count(),
                    'complete' =>  Sales::where('company_id', $company_id)->where('status', 'Success')->sum('grand_total'),
                    'progress' => Sales::where('company_id', $company_id)->where('status', 'Success')->sum('balance_amount'), // Assuming 'value' is a column in the Estimation table
                ]
                
            ];
            $data['recent_services'] = Services::with('customers')->leftJoin('servicecategory', 'services.servicecategory_id', '=', 'servicecategory.id')
                            ->orderBy('services.created_at', 'desc')
                            ->take(5)
                            ->where('services.company_id', $company_id)
                             ->when(!$isAdmin, function ($query) use ($user) {
                                $query->whereHas('users', function ($query) use ($user) {
                                    $query->where('users.id', $user->id);
                                });
                            })
                            ->get([
                                'services.id',  
                                'services.customer_id',// Add any additional fields you need
                                'services.service_code', 
                                'services.status', 
                                'services.company_id',
                                'services.service_data',
                                'services.created_at',
                              	'servicecategory.id as category_id',
                                'servicecategory.service_category as category_name'
                            ])
                            ->map(function ($service) {
                                // Load associated users (technicians) for each service
                                $technicians = $service->users->pluck('name')->toArray();
                                
                                // Append the technicians to the service data
                                $service['technicians'] = $technicians;
                                $customerName = $service->customers->first_name ?? '';
                                $contactNumber = $service->customers->contact_number ?? '';
                            
                                  
                                $service['customer_name'] = $customerName;
                                $service['contact_number'] = $contactNumber;
                                unset($service['users']);
                                unset($service['customers']);
                                
                                // Log the service data
                             //   \Log::info('Service Data: ' .  json_encode($service));
                                
                                return $service;
                            })
                            ->toArray();
                                            
            $data['recent_amcs'] = Amc::with('customerData')->latest()->where('company_id', $company_id)->take(5)
                                         ->when(!$isAdmin, function ($query) use ($user) {
                                            $query->whereHas('users', function ($query) use ($user) {
                                                $query->where('users.id', $user->id);
                                            });
                                        })
                                        ->get()->map(function ($services) {
                                            // Pluck the 'name' from the 'users' relationship and convert it to an array
                                            $services['technicians'] = $services->users->pluck('name')->toArray();
                                             $services['customer_name'] = $services->customerData->first_name ?? '';
                                        $services['contact_number'] =  $services->customerData->contact_number ?? '';
                                             
        // \Log::info('Assigned User IDs: ' . implode(',', $assigned_user_ids));
        // \Log::info('Technicians: ' . json_encode($technicians));
                                            
                                            // Remove the 'users' key from the service array
                                            unset($services['users']);
                                               unset($services['customerData']);
                                            
                                            return $services;
    
                                        })->toArray();
          	
          	 $data['recent_estimations'] = Estimation::with('customers')->latest()->where('company_id', $company_id)->take(5)
                                         ->when(!$isAdmin, function ($query) use ($user) {
                                            $query->whereHas('users', function ($query) use ($user) {
                                                $query->where('users.id', $user->id);
                                            });
                                        })
                                        ->get()->map(function ($services) {
                                            // Pluck the 'name' from the 'users' relationship and convert it to an array
                                            $services['technicians'] = $services->users->pluck('name')->toArray();
                                             $services['customer_name'] = $services->customers->first_name ?? '';
                                        $services['contact_number'] =  $services->customers->contact_number ?? '';
                                             
       
                                            
                                           
                                            unset($services['users']);
                                               unset($services['customers']);
                                            
                                            return $services;
    
                                        })->toArray();
          
          $data['recent_proforma'] = Proforma::with('customers')->latest()->where('company_id', $company_id)->take(5)
                                         ->when(!$isAdmin, function ($query) use ($user) {
                                            $query->whereHas('users', function ($query) use ($user) {
                                                $query->where('users.id', $user->id);
                                            });
                                        })
                                        ->get()->map(function ($services) {
                                    
                                             $services['customer_name'] = $services->customers->first_name ?? '';
                                        $services['contact_number'] =  $services->customers->contact_number ?? '';
                                             
       
                                            
                                           
                                            unset($services['users']);
                                               unset($services['customers']);
                                            
                                            return $services;
    
                                        })->toArray();
          
          
            $data['recent_expenses'] = Expenses::with('expenseTypes')->latest()
                                      ->where('company_id', $company_id)
                                      ->take(5)
                                      ->get()
                                      
                                     ->toArray();
          
          $data['recent_rmas'] = Rma::with(['assignedUsers', 'customer'])
                                  ->latest()
                                  ->where('company_id', $company_id)
                                  ->when(!$isAdmin, function ($query) use ($user) {
                                      $query->whereHas('assignedUsers', function ($query) use ($user) {
                                          $query->where('users.id', $user->id);
                                      });
                                  })
                                  ->take(5)
                                  ->get()
                                  ->map(function ($rma) {
                                      $rma['customer_name'] = $rma->customer->first_name ?? '' ;
                                      $rma['customer_phone'] = $rma->customer->contact_number ?? '';
                                      unset($rma['customer']); // Optional: Remove the customer relation from the result
                                      return $rma;
                                  })
                                  ->toArray();
           $employers = ( $isAdmin) 
                ? User::where('company_id', $company_id)->get() 
                : collect([$user]); // Employers only see their own data

            $employerIds = $employers->pluck('id');

            // Get service counts grouped by employer and status using a JOIN
            $serviceCounts = ServiceAssigns::whereIn('user_id', $employerIds)
              ->join('services', 'services.id', '=', 'service_assigns.service_id')
              ->whereNull('services.deleted_at') // Exclude soft-deleted services
              ->selectRaw('service_assigns.user_id, services.status, COUNT(*) as count')
              ->groupBy('service_assigns.user_id', 'services.status')
              ->get()
              ->groupBy('user_id');

          
          

            // Prepare response
            $responseData = [];
            foreach ($employers as $employer) {
                // Retrieve counts for the current employer
                $counts = $serviceCounts->get($employer->id, collect());

                // Calculate counts for each status (default to 0 if not present)
                $pending = $counts->whereIn('status', ['0', '1', '2', '3'])->sum('count');
                $completed = $counts->whereIn('status', ['4','5', '7'])->sum('count');
                $canceled = $counts->where('status', 6)->sum('count');
                $total = $counts->sum('count');

                // Add employer data to response
                $responseData[] = [
                    'employer_id' => $employer->id,
                    'employer_name' => $employer->name,
                    'pending_services' => $pending,
                    'completed_services' => $completed,
                    'canceled_services' => $canceled,
                    'total_services' => $total,
                ];
            }

            $data['assign_service_list'] = $responseData;
          $data['recent_sales'] = Sales::with(['customer'])
                                  ->latest()
                                  ->where('company_id', $company_id)
                                  ->when(!$isAdmin, function ($query) use ($user) {
                                    $query->where(function ($query) use ($user) {
                                      $query->where('updated_by', $user->id)
                                        ->orWhere('user_id', $user->id);
                                    });
                                  })
                                  ->take(5)
                                  ->get()
                                  ->map(function ($sale) {
                                      $sale['customer_name'] = $sale->customer?->first_name ?? '' . $sale->customer?->last_name ?? '';
                                      $sale['customer_phone'] = $sale->customer?->contact_number ?? '';
                                      unset($sale['customer']); // Optional: Remove the customer relation from the result
                                      return $sale;
                                  })
                                  ->toArray();


                                        
                                        
            $users = User::where('company_id', $company_id)->get();
            $data['recent_leads'] = leads::with('users','customers')
                                    ->where('company_id', $company_id)
                                    ->latest()
                                    ->take(5)
                                    ->when(!$isAdmin, function ($query) use ($user) {
                                           $query->where('assign_to', 'like', '%' . $user->id . '%');
                                       })
                                    ->get()
                                    ->map(function ($lead) use ($users) {
                                        $assigned_user_ids = explode(',', $lead->assign_to);
                                        $technicians = $users->whereIn('id', $assigned_user_ids)->pluck('name')->toArray();
                                
                                        // // Debug output
                                        // \Log::info('Lead ID: ' . $lead->id);
                                        // \Log::info('Assigned User IDs: ' . implode(',', $assigned_user_ids));
                                        // \Log::info('Technicians: ' . json_encode($technicians));
                                        $lead['customer_name'] = $lead->customers->first_name ?? '';
                                        $lead['contact_number'] =  $lead->customers->contact_number ?? '';
                                        $lead['technicians'] = $technicians;
                                        unset($lead['customers']);
                                        unset($lead['users']);
                                        return $lead;
                                    })
                                    ->toArray();
                                
                                
                                    $monthlyData = [];
                                        for ($i = 0; $i < 12; $i++) {
                                            // Get the date range for the current month
                                            $startDate = now()->subMonths($i)->startOfMonth();
                                            $endDate = now()->subMonths($i)->endOfMonth();
                                        
                                            // Query and count services for the current month
                                            $monthlyData[$startDate->format('M Y')] = [
                                                'total_services' => Customer::where('company_id', $company_id)
                                                    ->whereBetween('created_at', [$startDate, $endDate])->count()
                                            ];
                                        }
                                    
                                    $data['sales_barchart'] = $monthlyData;



            return response()->json($data);
            exit();
        }
        else{

        }
    }
  
	public function getLastDaysCounts($company_id, $user, $isAdmin, $days) {
        $count = intVal($days) - 1;
        
        // Get the last X days' dates
        $dates = [];
        for ($i = $count; $i >= 0; $i--) {
            $dates[] = date('Y-m-d', strtotime("-$i days"));
        }

        // Function to get counts for a model
        $getCounts = function($model, $company_id, $user, $isAdmin, $count, $table = null) {
            $query = $model::selectRaw('DATE(created_at) as service_date, COUNT(*) as service_count')
                ->where('company_id', $company_id)
                ->where('created_at', '>=', date('Y-m-d', strtotime("-$count days")))
                ->groupBy('service_date')
                ->orderBy('service_date', 'asc');
			 if (!$isAdmin) {
              if($table == 'exp'){
              $query->where('created_by', $user->id);
              }elseif($table == 'sale'){
                 $query->where('user_id', $user->id);
              }
              elseif($table == 'rma'){
                 $query->whereHas('assignedUsers', function ($query) use ($user) {
                   $query->where('rma_users.user_id', $user->id);
                });
              }
              else{
               $query->whereHas('users', function ($query) use ($user) {
                   $query->where('users.id', $user->id);
                });
              }
            }

            return $query->get()->keyBy('service_date')->toArray();
        };

        // Get counts for each model
        $leads = $getCounts(new leads, $company_id, $user, $isAdmin, $count);
        $estimations = $getCounts(new Estimation, $company_id, $user, $isAdmin, $count);
        $amcs = $getCounts(new Amc, $company_id, $user, $isAdmin, $count);
      	$services = $getCounts(new Services, $company_id, $user, $isAdmin, $count);
      	$expenses = $getCounts(new Expenses, $company_id, $user, $isAdmin, $count, 'exp');
      	$sales = $getCounts(new Sales, $company_id, $user, $isAdmin, $count, 'sale');
      	$rmas = $getCounts(new Rma, $company_id, $user, $isAdmin, $count, 'rma');
      	$proformas = $getCounts(new Proforma, $company_id, $user, $isAdmin, $count);

        // Initialize counts arrays with zeros
        $leads_counts = array_fill(0, intVal($days), 0);
        $estimations_counts = array_fill(0, intVal($days), 0);
        $amcs_counts = array_fill(0, intVal($days), 0);
		$service_counts = array_fill(0, intVal($days), 0);
      	$expense_counts = array_fill(0, intVal($days), 0);
      	$sales_counts = array_fill(0, intVal($days), 0);
      	$rmas_counts = array_fill(0, intVal($days), 0);
        $proformas_counts = array_fill(0, intVal($days), 0);
        // Populate the counts arrays with the query results
        foreach ($dates as $index => $date) {
            if (isset($leads[$date])) {
                $leads_counts[$index] = $leads[$date]['service_count'];
            }
            if (isset($estimations[$date])) {
                $estimations_counts[$index] = $estimations[$date]['service_count'];
            }
            if (isset($amcs[$date])) {
                $amcs_counts[$index] = $amcs[$date]['service_count'];
            }
          	if (isset($services[$date])) {
                $service_counts[$index] = $services[$date]['service_count'];
            }
          	if (isset($expenses[$date])) {
                $expense_counts[$index] = $expenses[$date]['service_count'];
            }
          	if (isset($sales[$date])) {
                $sales_counts[$index] = $sales[$date]['service_count'];
            }          	
          	if (isset($rmas[$date])) {
                $rmas_counts[$index] = $rmas[$date]['service_count'];
            }
          	if (isset($proformas[$date])) {
                $proformas_counts[$index] = $proformas[$date]['service_count'];
            }
        }

        // Prepare the data for the chart
        $data = [
            'labels' => $dates,
            'leads_counts' => $leads_counts,
            'estimations_counts' => $estimations_counts,
            'amcs_counts' => $amcs_counts,
          	'service_counts' => $service_counts,
          	'expenses_counts' => $expense_counts,
          	'sales_counts' => $sales_counts,
          	'rmas_counts' => $rmas_counts,
          	'proformas_counts' => $proformas_counts,
          
          
        ];


        return response()->json($data);
    }

      // Example usage in a controller method
      public function showChartData(Request $request) {
          $company_id = Auth::user()->company_id;
          $user = Auth::user(); // Assuming the user is authenticated
          $isAdmin = $user->hasRole('admin');// Replace with your actual admin check
          $days = $request->input('day_count');     
          return $this->getLastDaysCounts($company_id, $user, $isAdmin, $days);
      }
  


    public function salesOverView(Request $request)
    {
        $range = $request->type;
        $company_id = Auth::user()->company_id;

        $orders = Services::query()->where('company_id', $company_id);

        if ($range == 'Monthly') {
            $orders = $orders->whereYear('created_at', now()->year)
                            ->whereMonth('created_at', now()->month)
                            ->selectRaw('date(created_at) date, sum(amount) amount')
                            ->groupBy('date')->get();  
        }
        elseif ($range == 'Yearly') {
            $orders = $orders->whereYear('created_at', now()->year)
                            ->selectRaw('monthname(created_at) date, sum(amount) amount')
                            ->groupBy('date')->get();
        }
        elseif ($range == 'Weekly') {
            $orders = $orders->whereBetween('created_at', [now()->startOfWeek(), now()->endOfWeek()])
                            ->selectRaw('date(created_at) date, sum(amount) amount')
                            ->groupBy('date')->get();
        }

        return response()->json(['orders'=> $orders]);

    }
    public function employeeDashboardData()
    {
        if(Auth::check()){
            $user = Auth::user();
            $user_id = $user->id;
            $company_id = $user->company_id;
           // Retrieve AMC assignments with user IDs
            $total_assigned_amcs = AmcUsers::where('user_id', $user_id)->count();

            // Retrieve the number of completed AMCs assigned to the employee
            $completed_amcs = AmcUsers::where('user_id', $user_id)
                ->whereHas('amc', function ($query) use ($company_id) {
                    $query->where('company_id', $company_id)
                          ->where('amc_status', 2); // Assuming 'amc_status' is a column in the Amc table
                })
                ->count();
            
            // Retrieve the number of AMCs in progress assigned to the employee
            $in_progress_amcs = AmcUsers::where('user_id', $user_id)
                ->whereHas('amc', function ($query) use ($company_id) {
                    $query->where('company_id', $company_id)
                          ->whereIn('amc_status', [0, 1]); // Assuming 'amc_status' is a column in the Amc table
                })
                ->count();
            
            // Retrieve Estimation assignments with user IDs
            // $total_assigned_estimation= EstimationUser::where('user_id', $user_id)->get();
            
            
            //  $completed_amcs = EstimationUser::where('user_id', $user_id)
            //     ->whereHas('estimation', function ($query) use ($company_id) {
            //         $query->where('company_id', $company_id)
            //               ->where('status', 2); // Assuming 'amc_status' is a column in the Amc table
            //     })
            //     ->count();
            
            // // Retrieve the number of AMCs in progress assigned to the employee
            // $in_progress_amcs = EstimationUser::where('user_id', $user_id)
            //     ->whereHas('estimation', function ($query) use ($company_id) {
            //         $query->where('company_id', $company_id)
            //               ->whereIn('status', [0, 1]); // Assuming 'amc_status' is a column in the Amc table
            //     })
            //     ->count();
                
                
             // Retrieve Service assignments with user IDs
            $total_assigned_services = ServiceAssigns::where('user_id', $user_id)->count();
    
            // Retrieve completed Service assignments
            $completed_services = ServiceAssigns::where('user_id', $user_id)
                ->whereHas('services', function ($query) use ($company_id) {
                    $query->where('company_id', $company_id)
                        ->where('status', 5); // Assuming 'status' is a column in the Service table
                })
                ->count();
    
            // Retrieve Service assignments in progress
            $in_progress_services = ServiceAssigns::where('user_id', $user_id)
                ->whereHas('services', function ($query) use ($company_id) {
                    $query->where('company_id', $company_id)
                        ->whereIn('status', [0, 2, 3, 4]); // Assuming 'status' is a column in the Service table
                })
                ->count();
                
                
            
       
            
            // Retrieve Lead assignments with user IDs
            $total_assigned_leads = Leads::where('assign_to', 'like', '%' . $user_id . '%')->where('company_id', $company_id)->count();
            $completed_leads = Leads::where('assign_to', 'like', '%' . $user_id . '%')->where('leadstatus_id', 2)->where('company_id', $company_id)->count();
            $in_progress_leads = Leads::where('assign_to', 'like', '%' . $user_id . '%')->whereIn('leadstatus_id', [0, 1])->where('company_id', $company_id)->count();
            
            // Populate the dashboard data
            $data['overview'] = [
                [
                    'id' => "AMC",
                    'total' => $total_assigned_amcs,
                    'compelete' =>  $completed_amcs, 
                    'progress' =>  $in_progress_amcs
                  
                ],
                // [
                //     'id' => "Estimations",
                //     'total' => $total_assigned_estimation,
                //     'complete' => $completed_amcs, 
                //     'progress' =>    $in_progress_amcs
                // ],
                [
                    'id' => "Leads",
                    'total' => $total_assigned_leads,
                    'complete' => $completed_leads,
                    'progress' => $in_progress_leads
                ],
                [
                    'id' => "Services",
                    'total' => $total_assigned_services,
                    'complete' => $completed_services,
                    'progress' => $in_progress_services
                ]
            ];

            // Return the dashboard data as JSON
            return response()->json($data);
        } else {
            // Handle the case when the user is not authenticated
        }
    }
    
    
}
