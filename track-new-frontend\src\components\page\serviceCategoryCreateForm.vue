<template>
    <div class="flex h-screen relative">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" :category_name="category_name"></headbar> -->

            <!-- services home -->
            <div class="relative py-5">
                <!--loader-->
                <skeleton v-if="open_skeleton" :isLoading="open_skeleton" :cols="isMobile ? 1 : number_of_columns"
                    :rows="number_of_rows" :gap="gap" :type="'grid'">
                </skeleton>
                <createForm :class="{ 'hidden': open_skeleton }" :category_name="category_name"
                    :dataForm="viewCustomerData" :companyId="companyId" :service_categories="data"
                    :category_id="category_id" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </createForm>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/categories/create_form/headbar.vue';
import createForm from '../supporting/categories/create_form/createForm.vue';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';

export default {
    name: 'service_category_create_form',
    components: {
        // sidebar,
        // headbar,
        createForm,
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            showViewCustomer: false,
            viewCustomerData: null,
            category_name: null,
            route_item: 3,
            data: [],
            category_id: null,
            //---api integration---
            companyId: null,
            userId: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 20,
            gap: 5,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Add / Update Service Form';
        const pageDescription = 'Create or update dynamic service forms with customizable fields tailored to each service, ensuring flexibility and precision in capturing service details.';
        useMeta(pageTitle, pageDescription);
        return { pageTitle };
    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        showViewCustomerComponent(data) {
            // console.log(data, 'what about data..!');
            this.showViewCustomer = true;
            this.viewCustomerData = data;
        },
        // Method to go back to home
        goBackToHome() {
            this.showViewCustomer = false;
        },
        // Method to parse the URL
        parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const category = urlParts[urlParts.length - 2];
            const id = urlParts[urlParts.length - 1];
            this.category_id = Number(id);
            // console.log(id, 'What happening.......!@#');
            this.category_name = decodeURIComponent(category);
            // console.log(this.data, 'YYYY');
            if (this.data.length > 0) {
                const existData = this.data.find((record => record.id === Number(id)));
                // console.log(existData, 'What happening the exist data....');
                if (existData) {
                    // console.log('Is it working...! ', existData); 
                    this.viewCustomerData = existData;
                    // console.log(this.viewServiceData, 'Is it updated..!');
                }
                this.open_skeleton = false;
            } else {
                this.open_skeleton = false;
            }
        },
        //---service category---
        serviceCategoryList() {
            this.open_skeleton = true;
            axios.get('/service_categories', { params: { company_id: this.companyId, per_page: 'all' } })
                .then(response => {
                    console.log(response.data, 'service category get..!', this.data);
                    const newData = response.data.data; // Store response data in a local variable
                    this.data = [...newData]; // Update this.data with the new data
                    if (this.data.length > 0) {
                        this.parseUrl();
                    } else {
                        this.open_skeleton = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    this.open_skeleton = false;
                });
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

        if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
            this.data = [...this.currentServiceCategory];
            this.parseUrl();
            this.fetchServiceCategoryList();
            // this.serviceCategoryList();
        } else {
            this.fetchServiceCategoryList();
        }
        this.updateIsMobile(); // Initial check        
        this.updateIsEnableBottom(false);
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        this.updateIsEnableBottom(true);
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.data = [...newValue]
                    this.parseUrl();
                }
            }
        },
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnableBottom(true);
            // If no modals are open, allow the navigation
            next();
        }
    },

};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>
