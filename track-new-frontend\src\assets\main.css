/* @import './base.css'; */

@tailwind base;
@tailwind components;
@tailwind utilities;


body {
    font-family: "Roboto", sans-serif;
    font-weight: 300;
    font-style: normal;
    /* background-color: #f8f9fe;  #F7F7F9*/
    background-color: #f4f4f5;
    text-transform: capitalize; /* it was addded new */
}
/* main.css */
.no-capitalize {
    text-transform: none;
  }
  

.headBar {
    background-color: #324148;
    box-shadow: inset 0 0 5px rgba(180, 180, 180, 0.1);
}
.add-button {
    @apply bg-teal-600 border border-teal-600 hover:bg-teal-500 hover:border-teal-500;
}

.sideBar {
    background-color: #263238;
    box-shadow: inset 0 0 10px rgba(180, 180, 180, 0.1);
    min-height: 110%;
}
.sideBarWebsite {
    background-color: white;
    border: 1px solid #dee2e6;
    box-shadow: inset 0 0 10px rgba(180, 180, 180, 0.1);
}


/* Style for the radio button */
input[type="radio"] {
    /* Hide the default radio button */
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    /* Use a custom indicator (a green dot in this example) */
    width: 16px;
    height: 16px;
    border: 2px solid #bebdbd;
    /* Green border color */
    border-radius: 50%;
    /* Circle shape */
    margin-right: 5px;
    position: relative;
}

input[type="radio"]:hover {
    border: 2px solid #4CAF50;
}


/* Style for the radio button when it is checked */
input[type="radio"]:checked {
    background-color: #4CAF50;
    border: 2px solid #4CAF50;
    /* Green background color */
    /* Add any other styles you want for the checked state */
}

/* Style for the tick symbol when the radio button is checked */
input[type="radio"]:checked::before {
    content: '\2714';
    /* Unicode checkmark symbol */
    font-size: 14px;
    color: white;
    /* White color for the checkmark */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

/*--Checkbox----*/
/* Style for the checkbox */
input[type="checkbox"] {
    /* Hide the default checkbox */
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    /* Use a custom indicator (a green box in this example) */
    width: 16px;
    height: 16px;
    border: 2px solid #bebdbd;
    /* Initial border color */
    border-radius: 3px;
    transition: border-color 0.3s;
    position: relative;
}

/* Change border color on hover */
input[type="checkbox"]:hover {
    border: 2px solid #4CAF50;
    /* Border color on hover */
}

/* Style for the checkbox when it is checked */
input[type="checkbox"]:checked {
    background-color: #4CAF50;
    /* Checked background color */
    border-color: #4CAF50;
    /* Checked border color */
}

/* Style for the tick symbol when the checkbox is checked */
input[type="checkbox"]:checked::before {
    content: '\2713';
    /* Unicode for tick symbol */
    font-size: 14px;
    color: white;
    /* Tick symbol color */
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.divStyle {
    border-radius: 10px;
    background-color: rgb(255, 255, 255);
    margin-top: 10px;
    margin-bottom: 10px;
    box-shadow: 2px 5px 15px 3px rgba(8, 8, 8, 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}
.divStyleCard {
    border-radius: 10px;
    background-color: rgb(255, 255, 255);
    margin-top: 0px;
    margin-bottom: 0px;
    box-shadow: 2px 5px 15px 3px rgba(8, 8, 8, 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    overflow: hidden;
}

.text-blue-700 {
    color: #0288D1;
}

/*triangle down */
.triangle-down {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-top: 10px solid black;
}

.triangle-up {
    width: 0;
    height: 0;
    border-left: 6px solid transparent;
    border-right: 6px solid transparent;
    border-bottom: 10px solid black;
}

.-top-3 {
    top: -7px;
}

.-mt-3 {
    margin-top: -7px;
}

input,
textarea,
select {
    border-radius: 5px;
    outline: none;
}

input:focus,
textarea:focus,
select:focus {
    border: 1px solid rgb(41, 88, 240);
}


.material-icons {
    font-variation-settings:
        'FILL' 0,
        'wght' 400,
        'GRAD' 0,
        'opsz' 24
}

table{
    background-color: white;   
}
.image-container {
    width: 160px;
    height: 160px;
    overflow: hidden;
position: relative;
display: flex;
justify-content: center; /* Horizontally center the image */
align-items: center; /* Vertically center the image */
border-radius: 8px;
}

.image-container img{
    width: 100%;
    height: 100%;
    object-fit: cover;
border-radius: 8px;
}

/*---set background color---*/
.set-header-background {
    background-color: #dd2c00; /* Mobile color */
}

/*---set text color---*/
.set-header-color {
    color: #dd2c00; /* Mobile color */
}

/*---Non-mobile view styles---*/
@media (min-width: 768px) {
    .set-header-background {
        background-color: #324148;
    box-shadow: inset 0 0 5px rgba(180, 180, 180, 0.1); /* Non-mobile color */
    }

    .set-header-color {
        color: #324148; /* Non-mobile color */
    }
}

input[type="date"], input[type="datetime-local"], select, input[type='number'] {
    background-color: white;
}
.manualStyle {
    overflow: auto;
    height: 100vh;
}
@media (max-width: 768px) {
    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }
}

/* Style the scrollbar */
::-webkit-scrollbar {
width: 3px;
/* Set the width of the scrollbar */
height: 4px;
}

/* Track (the area around the scrollbar) */
::-webkit-scrollbar-track {
background: #CFD8DC;
/* Background color of the scrollbar track */
}

/* Handle (the draggable part of the scrollbar) */
::-webkit-scrollbar-thumb {
background: #90A4AE;
/* Color of the scrollbar handle */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
background: #90A4AE;
/* Color of the scrollbar handle on hover */
}
/*table body make left of text alignment*/
tbody.text-center{
    text-align: left;
}
td.text-center{
    text-align: left;
}

/*Start Animations*/
@-webkit-keyframes animatetop {
	from {
		top: -300px;
		opacity: 0;
	}
	to {
		top: 0;
		opacity: 1;
	}
}
@keyframes animatetop {
	from {
		top: -300px;
		opacity: 0;
	}
	to {
		top: 0;
		opacity: 1;
	}
}
@-webkit-keyframes zoomIn {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(0.3, 0.3, 0.3);
		transform: scale3d(0.3, 0.3, 0.3);
	}
	50% {
		opacity: 1;
	}
}
@keyframes zoomIn {
	0% {
		opacity: 0;
		-webkit-transform: scale3d(0.3, 0.3, 0.3);
		transform: scale3d(0.3, 0.3, 0.3);
	}
	50% {
		opacity: 1;
	}
}
/*End Animations*/
/*
-- Start BackGround Animation 
*/
.area {
	background: #324148;
	background: -webkit-linear-gradient(to left, #8f94fb, #4e54c8);
	width: 100%;
	height: 100vh;
	position: absolute;
	z-index: -1;
}

.circles {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 96%;
	overflow: hidden;
}

.circles li {
	position: absolute;
	display: block;
	list-style: none;
	width: 20px;
	height: 20px;
	background: rgba(255, 255, 255, 0.2);
	animation: animate 25s linear infinite;
	bottom: -150px;
    color:  rgba(255, 255, 255, 0.5);
}

.circles li:nth-child(1) {
	left: 25%;
	width: 80px;
	height: 80px;
	animation-delay: 0s;
    text-align: center;
}

.circles li:nth-child(2) {
	left: 10%;
	width: 20px;
	height: 20px;
	animation-delay: 2s;
	animation-duration: 12s;
}

.circles li:nth-child(3) {
	left: 70%;
	width: 20px;
	height: 20px;
	animation-delay: 4s;
}

.circles li:nth-child(4) {
	left: 40%;
	width: 60px;
	height: 60px;
	animation-delay: 0s;
	animation-duration: 18s;
}

.circles li:nth-child(5) {
	left: 65%;
	width: 20px;
	height: 20px;
	animation-delay: 0s;
}

.circles li:nth-child(6) {
	left: 75%;
	width: 110px;
	height: 110px;
	animation-delay: 3s;
}

.circles li:nth-child(7) {
	left: 35%;
	width: 150px;
	height: 150px;
	animation-delay: 7s;
}

.circles li:nth-child(8) {
	left: 50%;
	width: 25px;
	height: 25px;
	animation-delay: 15s;
	animation-duration: 45s;
}

.circles li:nth-child(9) {
	left: 20%;
	width: 15px;
	height: 15px;
	animation-delay: 2s;
	animation-duration: 35s;
}

.circles li:nth-child(10) {
	left: 85%;
	width: 150px;
	height: 150px;
	animation-delay: 0s;
	animation-duration: 11s;
}

@keyframes animate {
	0% {
		transform: translateY(0) rotate(0deg);
		opacity: 1;
		border-radius: 0;
	}

	100% {
		transform: translateY(-1000px) rotate(720deg);
		opacity: 0;
		border-radius: 50%;
	}
}
/*
-- End BackGround Animation 
*/

/*---table header style--*/
.icon-color{
    color: #263238;
}
.icon-color-border{
    border-color: #263238;
}
/*---Home margin----*/
.my-custom-margin {
  @apply m-1 sm:mx-5 xl:mx-5 2xl:mx-8 3xl:mx-12;
}

/*---table header and bordder color--*/
.table-head{
    @apply bg-[#eff1f3];
}
.table-border{
    @apply border;
}
.created-at-color{
    @apply text-blue-800 hover:text-blue-700;
}
/* Target all images except those inside elements with the ck-content class */
/* img:not(.ck-content img) {
    margin: 0rem; 
    padding: 0; 
} */
/*---website builder---*/
.builder-page{
    @apply p-3 sm:p-6 rounded shadow-md text-sm bg-[#ffffff];

}
.web-head{
    @apply text-lg font-bold mb-2 sm:mb-6 text-center;
}
.margin-web{
    @apply mt-10 sm:mt-14;
}
.web-modal-head{
    @apply flex justify-between bg-gray-300 items-center  border-b mb-4 p-1 px-2 rounded;
}
.dragging {
    opacity: 0.5; /* Make the dragged item semi-transparent */
  }
  .ecommerce-page{
    @apply rounded shadow-md text-sm bg-[#ffffff];
  }

  /*--whatsapp setting--*/
  .whatsapp-bg{
    background-color: #324148;
    box-shadow: inset 0 0 5px rgba(180, 180, 180, 0.1);
  }

  /*---service label--*/
  .service-label{
    @apply block text-sm font-semibold pb-2;
  }

  /* Global styling in*/
.capitalize-first-letter {
    text-transform: lowercase; /* Make everything lowercase */
  }
  
  .capitalize-first-letter::first-letter {
    text-transform: uppercase; /* Capitalize first letter */
  }

  /* .font-semibold, .font-bold{
    @apply font-light;
  } */
  

  
  

  

  