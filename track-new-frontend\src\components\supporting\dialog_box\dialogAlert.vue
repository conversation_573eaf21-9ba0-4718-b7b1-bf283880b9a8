<!-- MessageDialog.vue -->
<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <!-- <div v-if="showModal" class="overlay"></div> -->
        <div class="bg-white w-full max-w-md p-4 transform transition-transform-custom ease-in-out duration-300 rounded rouded-full"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full': !isOpen }">
            <!-- Blurred overlay -->
            <div class="absolute backdrop-filter bg-gray-500 opacity-75" @click="closeDialog"></div>

            <!-- Message box -->
            <div class="bg-white pt-5 pl-8 pr-8 rounded shadow-lg z-10">
                <p class="text-[24px] text-center mb-4 font-bold text-gray-600">Message</p>
                <p class="text-lg">{{ message }}</p>
                <div class="flex justify-center pb-5">
                    <button class="mt-5 px-4 py-1 bg-blue-500 text-white rounded hover:bg-blue-400"
                        @click="closeDialog">OK</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        message: String,
        showModal: Boolean
    },
    data() {
        return {
            isOpen: false,
        }
    },
    methods: {
        closeDialog() {
            // this.$emit('close');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close');
            }, 300);
        },
        printmsg() {
            // console.log('pppprwrwrpwr');
        }
    },
    mounted() {
        this.printmsg();
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },

    }
};
</script>

<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(7px);
    z-index: 51;
    transition: opacity 0.3s ease;
}

.overlay-active {
    opacity: 1;
}

.dialog {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 60;
    /* Lower z-index for the modal */
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}
</style>