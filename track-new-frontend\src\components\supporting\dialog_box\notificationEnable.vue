<!-- ModalBox.vue -->
<template>
    <div v-if="isVisible" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
        <div class="bg-white rounded-lg shadow-lg p-6 w-11/12 md:w-1/3">
            <h2 class="text-xl font-semibold mb-4">Enable Notifications</h2>
            <p class="mb-4">
                To get notifications, you need to allow them in your browser settings. Please follow the instructions
                below based on your browser:
            </p>
            <p class="font-bold mb-2">{{ browser }}</p>
            <p v-if="browser === 'Chrome'">
                Go to <strong>Settings &gt; Privacy and security &gt; Site Settings &gt; Notifications</strong> and
                allow notifications for this site.
            </p>
            <p v-if="browser === 'Firefox'">
                Go to <strong>Options &gt; Privacy & Security &gt; Permissions &gt; Notifications</strong> and allow
                notifications for this site.
            </p>
            <p v-if="browser === 'Edge'">
                Go to <strong>Settings &gt; Cookies and site permissions &gt; Notifications</strong> and allow
                notifications for this site.
            </p>
            <p v-if="browser === 'Safari'">
                Open <strong>Safari &gt; Preferences &gt; Websites &gt; Notifications</strong>, find this site, and
                allow notifications.
            </p>

            <div class="mt-6 text-right">
                <button @click="closeModal" class="bg-blue-500 hover:bg-blue-700 text-white py-2 px-4 rounded">
                    Close
                </button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        isVisible: Boolean, // Control the visibility of the modal
    },
    data() {
        return {
            browser: this.getBrowserName(), // Determine the browser
        };
    },
    methods: {
        // Function to close the modal
        closeModal() {
            this.$emit('close');
        },
        // Function to detect the browser
        getBrowserName() {
            const agent = window.navigator.userAgent.toLowerCase();
            if (agent.indexOf('edge') > -1) return 'Edge';
            if (agent.indexOf('chrome') > -1) return 'Chrome';
            if (agent.indexOf('firefox') > -1) return 'Firefox';
            if (agent.indexOf('safari') > -1) return 'Safari';
            return 'Unknown';
        },
    },
};
</script>

<style scoped>
/* Custom styling can go here */
</style>