<template>
  <div v-if="isLoading">
    <!-- Render skeleton grid if type is not 'table' -->
    <div v-if="type !== 'table'"
      :class="'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-' + cols + ' gap-' + gap + ' overflow-auto 2xl:grid-cols-5'">
      <div v-for="rowIndex in rows" :key="rowIndex" class="flex">
        <div class="skeleton h-36 rounded w-full"></div>
      </div>
    </div>
    <!-- Render skeleton table if type is 'table' -->
    <table v-else class="skeleton-table">
      <tbody>
        <tr v-for="rowIndex in rows" :key="rowIndex">
          <td v-for="colIndex in cols" :key="colIndex" class="border border-white skeleton h-[50px] rounded"
            :class="{ 'h-[5px]': is_time }"></td>
        </tr>
      </tbody>

    </table>
  </div>
</template>

<script>
export default {
  props: {
    isLoading: {
      type: Boolean,
      default: false
    },
    cols: {
      type: Number,
      default: 1// Default number of columns
    },
    rows: {
      type: Number,
      default: 2 // Default number of rows
    },
    gap: {
      type: Number,
      default: 0
    },
    type: {
      type: String,
      default: 'grid'
    },
    is_time: {
      type: Boolean,
      default: undefined
    }
  },
  computed: {
    totalItems() {
      // console.log(this.cols, 'RRRR', this.rows, 'YYYYY');
      return this.cols * this.rows;
    }
  }
};
</script>

<style scoped>
/* Define CSS for the skeleton loading effect */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0f0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 5s infinite;
  margin-top: 20px;
}

/* Additional styles for skeleton table */
.skeleton-table {
  width: 100%;
  border-collapse: collapse;
}

.skeleton-table td {
  padding: 10px;
}

/* Add any additional styles here */
@keyframes loading {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}
</style>