<template>
    <div v-if="isOpen" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpenModal, 'translate-x-full right-12': !isOpenModal, 'pb-[60px]': isMobile }">
            <!-- Close button -->
            <div class="justify-between items-center flex py-4 set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Import Inventory Data
                </p>
                <p class="close pr-5" @click="closeModal">&times;</p>

            </div>
            <!-- <button @click="closeModal" class="absolute top-4 right-4">
                <img :src="close_icon" alt="Close" class="w-4 h-4">
            </button> -->
            <!-- Download Sample CSV button -->
            <div CLASS="p-4">
                <div class="flex justify-between">
                    <div>
                        <button @click="downloadSampleCSV"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 mt-4 rounded">Download Sample
                            CSV</button>
                    </div>
                    <div>
                        <button @click="openAvailableOptions"
                            class="bg-gray-300 hover:bg-gray-400 border px-4 py-2 mt-4 rounded"><font-awesome-icon
                                icon="fa-solid fa-circle-info" /> Info</button>
                    </div>
                </div>
                <!-- File upload and preview section -->
                <input type="file" @change="handleFileUpload" class="mt-5 block" id="fileInput" />

                <div v-if="importedData && importedData.length > 0 && !open_loader"
                    class="table-container overflow-x-auto mt-4">
                    <table class="table w-full">
                        <!-- Table headers -->
                        <thead>
                            <tr>
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    class="border text-sm py-1 set-header-background text-white font-normal"
                                    :class="{ 'hidden': column.label === 'Editing' || column.label === 'Product Type' || column.label === 'Category Id' || column.label === 'Brand Id' || column.label === 'Gst Type' || column.label === 'Gst Value' || column.label === 'Tax Name' || column.label === 'Selected Tax' }">
                                    {{ column.label }} <span class="text-red-600"
                                        :class="{ 'hidden': column.label !== 'Product Name' && column.label !== 'Unit' && column.label !== 'Sales Price' }">*</span>
                                </th>
                                <th
                                    class="border px-1 py-1 set-header-background text-white text-sm leading-none font-normal">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <!-- Table body -->
                        <tbody>
                            <tr v-for="(record, index) in importedData" :key="index" class="py-2"
                                :ref="'invalidRow' + index" :class="{ 'highlight-row': invalidRows.includes(index) }">
                                <td v-for="(value, key) in record" :key="key" class="px-1 py-1 text-sm text-center"
                                    :class="{ 'hidden': key === 'editing' || key === 'product_type' || key === 'category_id' || key === 'brand_id' || key === 'gst_type' || key === 'gst_value' || key === 'tax_name' || key === 'selected_tax' }">
                                    <div v-if="key !== 'category_name' && key !== 'brand_name' && key !== 'unit' && key !== 'discount_type' && key !== 'discount' && key !== 'tax_type' && key !== 'tax_value'"
                                        class="w-36">
                                        <!-- Turn each value into an input field -->
                                        <input v-if="key === 'product_name' || key === 'hsn_code' || key === 'notes'"
                                            type="text" v-model="importedData[index][key]"
                                            @keypress.enter="focusNextInvalidRow(index)"
                                            class="w-full p-1 text-sm border rounded" />
                                        <input v-else type="number" v-model="importedData[index][key]"
                                            @input="preventNegativeValue" class="w-full p-1 text-sm border rounded" />
                                    </div>
                                    <div v-if="key === 'category_name'" class="flex w-36">
                                        <select v-model="importedData[index]['category_id']"
                                            :ref="'category_id' + index"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full outline-none focus:border-blue-500 rounded rounded-r-none"
                                            title="Click &#43; icon to add new or edit">
                                            <option v-for="(cat, j) in category_list" :key="j" :value="cat.id">
                                                {{ cat.category_name }}</option>
                                        </select>
                                        <button @click="openModelCategory('category')"
                                            class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>
                                    </div>
                                    <div v-if="key === 'brand_name'" class="flex w-36">
                                        <select v-model="importedData[index]['brand_id']" :ref="'brand' + index"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full  outline-none focus:border-blue-500 rounded rounded rounded-r-none"
                                            title="Click &#43; icon to add new or edit">
                                            <option v-for="(brand, k) in brand_list" :key="k" :value="brand.id">
                                                {{ brand.brand_name }}
                                            </option>
                                        </select>
                                        <button @click="openModelCategory('brand')"
                                            class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>
                                    </div>
                                    <div v-if="key === 'unit'" class="flex w-36">
                                        <select v-model="importedData[index][key]" :ref="'unit' + index"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full outline-none focus:border-blue-500 rounded rounded rounded-r-none"
                                            title="Click &#43; icon to add new or edit">
                                            <option v-for="(unit, l) in filterUnits()" :key="l" :value="unit">
                                                {{ unit }}
                                            </option>
                                        </select>
                                        <button @click="openModelCategory('unit')"
                                            class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>
                                    </div>
                                    <div v-if="key === 'discount_type'" class="flex w-36">
                                        <select id="discount_type" v-model="importedData[index][key]"
                                            @change="validateMax(index)"
                                            class="text-sm p-1  mt-1 border border-gray-300 w-full  focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none">
                                            <!-- <option value="">select type</option> -->
                                            <option value="Percentage">percentage (%)</option>
                                            <option value="Fixed">Fixed (₹)</option>
                                        </select>
                                    </div>
                                    <div v-if="key === 'discount'" class="flex w-36">
                                        <input v-model="importedData[index][key]" @input="validateMax(index)"
                                            class="text-sm p-1  mt-1 border border-gray-300 w-full  focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none " />
                                    </div>
                                    <div v-if="key === 'tax_type'" class="flex w-36">
                                        <select id="gst_type" v-model="importedData[index]['gst_type']"
                                            @change="calculatePurchasePrice(importedData[index]['gst_type'], index)"
                                            class="text-sm p-1  mt-1 border border-gray-300 w-full  focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none ">
                                            <!-- <option value="">select type</option> -->
                                            <option value="Exclusive">Exclusive</option>
                                            <option value="Inclusive">Inclusive</option>
                                        </select>
                                    </div>
                                    <div v-if="key === 'tax_value'" class="flex w-36">
                                        <!-- <select id="gst_value" v-model="importedData[index]['gst_value']"
                                            @change="calculatePurchasePrice(importedData[index]['gst_value'], index)"
                                            class="text-sm p-1  mt-1 border border-gray-300 w-full  focus:border-blue-500 rounded rounded-r-none outline-none"
                                            title="Click &#43; icon to add new or edit">
                                            <option v-for="(tax, m) in tax_value_list" :key="m" :value="tax.value">
                                                {{ tax.tax_name + ' @ ' + tax.value }}</option>
                                        </select> -->
                                        <select id="gst_value" v-model="importedData[index]['selected_tax']"
                                            @change="updateTaxName(importedData[index]['selected_tax'], index)"
                                            class="text-sm p-1  mt-1 border border-gray-300 w-full  focus:border-blue-500 rounded rounded-r-none outline-none"
                                            title="Click &#43; icon to add new or edit">
                                            <option v-for="(tax, m) in tax_value_list" :key="m"
                                                :value="{ tax_name: tax.tax_name, value: tax.value }">
                                                {{ tax.tax_name + ' @ ' + tax.value }}
                                            </option>
                                        </select>
                                        <button @click="openTaxModel"
                                            class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>
                                    </div>
                                </td>
                                <td class="border px-1 py-1 text-sm text-center">
                                    <div class="flex">
                                        <button @click="saveImportedData(record, index)"
                                            class="text-green-500 px-2 py-1 ml-2 border rounded hover:bg-green-500 hover:text-white">
                                            <font-awesome-icon icon="fa-regular fa-floppy-disk" />
                                        </button>
                                        <button @click="confirmDelete(index)"
                                            class="text-red-700 px-2 py-1 ml-1 border rounded hover:bg-red-500  hover:text-white">
                                            <font-awesome-icon icon="fa-solid fa-trash-can" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td :colspan="Object.keys(importedData[importedData.length - 1]).length - 1"
                                    class="px-2 py-2 flex justify-center items-center border-2 border-green-800 hover:bg-green-100 rounded">
                                    <button
                                        @click="addNewItem(Object.keys(importedData[importedData.length - 1]).length - 1)">+
                                        ADD ITEM</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="flex w-full space-x-2">
                    <!-- Save button (conditionally rendered) -->
                    <button v-if="importedData && importedData.length > 0" @click="validateAndClose"
                        class="bg-green-600 text-white px-4 py-2 mt-4 rounded">
                        Save All
                    </button>
                    <!-- remove button (conditionally rendered) -->
                    <button v-if="importedData && importedData.length > 0" @click="removeItemsAlreadyExist"
                        class="bg-red-600 text-white px-4 py-2 mt-4 lg:ml-5 rounded">
                        Remove All already Exist Items
                    </button>
                    <button v-if="importedData && importedData.length > 0" @click="resetAllData"
                        class="bg-pink-600 text-white px-4 py-2 mt-4 lg:ml-5 rounded">
                        Reset
                    </button>
                </div>
            </div>
        </div>
        <!--add category brand unit model-->
        <categoryBrandUnit :showModal="open_model" :type="type_model" :categoriesData="list_data"
            @close-modal="closecategoryBrandUnit"></categoryBrandUnit>
        <!--add tax list model-->
        <addTaxList :showModal="open_tax_model" :categoriesData="tax_value_list" @close-modal="closeTaxList"
            :invoice_setting="invoice_setting">
        </addTaxList>
        <dialogAlert :showModal="open_message" :message="message" @close="closeAlert"></dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="removeImportedData" @onCancel="cancelDelete"></confirmbox>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <Loader :showModal="open_loader"></Loader>
        <!--available options details are-->
        <showavailableOptions :show-modal="show_available" @close-Modal="closeAvailableOptions"
            :category_list="category_list" :brand_list="brand_list" :unit_list="filterUnits()"
            :tax_list="tax_value_list" :isMobile="isMobile">
        </showavailableOptions>
    </div>
</template>

<script>
import dialogAlert from './dialogAlert.vue';
import confirmbox from './confirmbox.vue';
import categoryBrandUnit from './categoryBrandUnit.vue';
import addTaxList from './addTaxList.vue';
import showavailableOptions from './showavailableOptions.vue';
import { mapGetters, mapActions } from 'vuex';
import invoice_setting from '@/store/modules/invoice_setting';
import Papa from 'papaparse';
export default {
    components: {
        dialogAlert,
        confirmbox,
        categoryBrandUnit,
        addTaxList,
        showavailableOptions
    },
    props: {
        isOpen: Boolean,
        companyId: String,
        userId: String,
        isMobile: Boolean
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            showSaveButton: false,
            importedData: null,
            open_message: false,
            message: '',
            existInventory: [],
            deleteIndex: null,
            open_confirmBox: false,
            category_list: [],
            brand_list: [],
            unit_list: [],
            tax_value_list: [],
            units_list: [
                // Your existing units (unchanged)
                'Bags (BAG)',
                'Bale (BAL)',
                'Bundles (BDL)',
                'Buckles (BKL)',
                'Box (BOX)',
                'Bottles (BTL)',
                'Cans (CAN)',
                'Cartons (CTN)',
                'Cubic centimeters (CCM)',
                'Cubic meters (CBM)',
                'Centimeters (CMS)',
                'Drums (DRM)',
                'Dozens (DOZ)',
                'Great gross (GGK)',
                'Gross (GRS)',
                'Kilometre (KME)',
                'Kilograms (KGS)',
                'Kilo litre (KLR)',
                'Metric ton (MTS)',
                'Milli litre (MLT)',
                'Meters (MTR)',
                'Numbers (NOS)',
                'Packs (PAC)',
                'Pieces (PCS)',
                'Pairs (PRS)',
                'Quintal (QTL)',
                'Rolls (ROL)',
                'Square Yards (SQY)',
                'Sets (SET)',
                'Square feet (SQF)',
                'Square meters (SQM)',
                'Tablets (TBS)',
                'Tubes (TUB)',
                'Ten Gross (TGM)',
                'Thousands (THD)',
                'Tonnes (TON)',
                'Units (UNT)',
                'US Gallons (UGS)',
                'Yards (YDS)',

                // New additions in same format
                'Each (EA)',
                'Gram (GRM)',
                'Liter (LTR)',
                'Milligram (MGM)',
                'Ounce (OZ)',
                'Pound (LB)',
                'Gallon (GLN)',
                'Inch (IN)',
                'Foot (FT)',
                'Mile (MI)',
                'Acre (ACR)',
                'Hectare (HEC)',
                'Case (CAS)',
                'Pallet (PAL)',
                'Bundle (BUN)',
                'Container (CNT)',
                'Barrel (BRL)',
                'Sack (SAK)',
                'Crate (CRT)',
                'Jar (JAR)',
                'Vial (VIL)',
                'Canister (CAN)',
                'Packet (PKT)',
                'Envelope (ENV)',
                'Ream (REM)',
                'Spool (SPL)',
                'Bunch (BNC)',
                'Cluster (CLS)',
                'Lot (LOT)',
                'Batch (BCH)',
                'Hour (HUR)',
                'Day (DAY)',
                'Week (WEK)',
                'Month (MTH)',
                'Year (YER)',
                'Service (SRV)',
                'Session (SES)',
                'License (LIC)',
                'Subscription (SUB)'
            ],
            //---open model data---
            isOpenModal: false,
            open_model: false,
            type_model: null,
            list_data: null,
            //--open tax --
            open_tax_model: false,
            validation_message: '',
            //---invoice setting---
            invoice_setting: [],
            //--toaster---
            show: false,
            type_toaster: 'warning',
            //---loader--
            open_loader: false,
            //dropdown options---
            showSuggestions: null,
            selectedIndex: 0,
            //---reset--
            reset_All: false,
            //--available options-
            show_available: false,
            default_tax: null,
            invalidRows: [], // Stores invalid row indexes
            focusedInvalidIndex: null, // Tracks the focused invalid row index
            isDisplayed: false,
            //--find tax details--
            selected_taxName: 'GST',
        }
    },
    computed: {
        ...mapGetters('brandUnitCategoryItem', ['currentUnitList']),
        ...mapGetters('brandUnitCategoryItem', ['currentBrandList']),
        ...mapGetters('brandUnitCategoryItem', ['currentCategoryList']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the specified order to create fields
            if (this.importedData && this.importedData.length !== 0) {

                for (const key of Object.keys(this.importedData[0])) {
                    const label = formatLabel(key);
                    fields.push({ label, field: key, visible: true });
                }
                this.columns = fields;
                return fields;
            }
        },
    },
    methods: {
        ...mapActions('brandUnitCategoryItem', ['fetchUnitList']),
        ...mapActions('brandUnitCategoryItem', ['fetchBrandList']),
        ...mapActions('brandUnitCategoryItem', ['fetchCategoryList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('items', ['fetchItemList']),
        closeModal() {
            if (this.importedData) {
                this.$emit('close', this.importedData);
                this.importedData = [];
            } else {
                this.$emit('close');
            }
        },
        async handleFileUpload(event) {
            this.isDisplayed = false;
            const file = event.target.files[0];
            if (file) {
                this.open_loader = true;
                const reader = new FileReader();
                reader.onload = async () => {
                    // Parse the CSV and remove empty rows
                    const parsedData = await this.parseCSV(reader.result);
                    if (parsedData) {
                        const nonEmptyRows = parsedData.filter(row => Object.values(row).some(value => value !== undefined && value !== null && value !== ''));
                        // Add editing key and set its value to false for each item
                        const importedDataWithEditing = nonEmptyRows.map(row => ({ product_type: 'Product', ...row, editing: false }));

                        this.importedData = importedDataWithEditing;
                        if (this.importedData.length > 0) {
                            const uniqueBarcodes = new Set();
                            const uniqueProductNames = new Set();
                            const duplicateItems = new Set();
                            // Iterate over the imported data
                            for (let index = this.importedData.length - 1; index >= 0; index--) {
                                const importedItem = this.importedData[index];
                                const product_name = importedItem.product_name;
                                let barcode = importedItem.barcode;
                                let category = importedItem.category_name;
                                let unit = importedItem.unit;
                                let brand = importedItem.brand_name;
                                let tax_type = importedItem.tax_type;
                                let tax_value = importedItem.tax_value;
                                let discount_type = importedItem.discount_type;
                                let discount_value = importedItem.discount;
                                let dealer_price = importedItem.dealer_price;

                                // Handle empty barcode by generating a unique one
                                if (!barcode) {
                                    barcode = this.generateUniqueBarcodeSync(uniqueBarcodes);
                                    importedItem.barcode = barcode;
                                }
                                if (!uniqueBarcodes.has(barcode)) {
                                    uniqueBarcodes.add(barcode);
                                } else {
                                    barcode = this.generateUniqueBarcodeSync(uniqueBarcodes);
                                    importedItem.barcode = barcode;
                                }
                                // Check for duplicate product names
                                if (!uniqueProductNames.has(product_name.toLowerCase())) {
                                    uniqueProductNames.add(product_name.toLowerCase());
                                } else {
                                    // Remove item with duplicate product name
                                    // console.log(product_name, 'RRRRRRRRRRRRRR');
                                    duplicateItems.add(product_name);
                                    this.importedData.splice(index, 1);
                                }
                                if (category) {
                                    category = this.getCategoryId(category);
                                    importedItem.category_id = category;
                                }
                                if (brand) {
                                    brand = this.getBrandId(brand);
                                    importedItem.brand_id = brand;
                                }
                                if (unit) {
                                    unit = this.getUnitName(unit);
                                    importedItem.unit = unit;
                                } else {
                                    importedItem.unit = 'Pieces (PCS)';
                                }
                                if (tax_type) {
                                    tax_type = this.classifyTaxType(tax_type);
                                    importedItem.gst_type = tax_type;
                                }
                                if (tax_value) {
                                    tax_value = this.IsValidtax(tax_value);
                                    importedItem.gst_value = tax_value;
                                    importedItem.tax_name = this.selected_taxName;
                                    importedItem.selected_tax = { tax_name: this.selected_taxName, value: tax_value };
                                }
                                if (discount_type) {
                                    discount_type = this.classifyDiscountType(discount_type);
                                    importedItem.discount_type = discount_type;
                                }
                                if (discount_value) {
                                    discount_value = this.validateMax(index);
                                    importedItem.discount = discount_value;
                                }
                                if (index === 0) {
                                    this.open_loader = false;
                                }
                                if (!dealer_price) {
                                    importedItem.dealer_price = 0;
                                }
                            }
                            // console.log(duplicateItems, 'RRRRSFS');
                            if (Array.from(duplicateItems).length > 0) {
                                // console.log(duplicateItems);
                                // Handle duplicate product names
                                this.message = `Duplicate product names found: ${Array.from(duplicateItems).join(', ')}. These are removed successfully`;
                                this.open_message = true;
                                // return false;
                            }

                        }
                    } else {
                        this.open_loader = false;
                    }
                };
                reader.readAsText(file);
            }
        },
        parseCSV(csvData) {
            // const lines = csvData.split('\n').map(line => line.trim()).filter(line => line.length > 0);
            // const headers = lines[0].split(',').map(header => header.trim());

            // const parsedData = [];
            // for (let i = 1; i < lines.length; i++) {
            //     const row = {};
            //     const values = lines[i].split(',').map(value => value.trim()); 
            //     for (let j = 0; j < headers.length; j++) {                    
            //         row[headers[j]] = values[j] ? values[j] : ''; 
            //     }
            //     parsedData.push(row);
            // }

            // return parsedData;
            return new Promise((resolve) => {
                Papa.parse(csvData, {
                    header: true,  // Returns data as objects
                    skipEmptyLines: true,
                    complete: (result) => resolve(result.data),
                });
            });
        },
        saveData() {
            // Handle saving data logic here
            this.closeModal();
        },
        downloadSampleCSV() {
            const sampleData = [
                { product_name: 'Mouse', category_name: '', brand_name: '', unit: 'Pieces (PCS)', hsn_code: '', alert_qty: 0, barcode: '', discount_type: 'Percentage', discount: 0, purchase_price: 0, tax_type: 'Exclusive', tax_value: this.default_tax ? this.default_tax.value : 0, sales_price: 0, dealer_price: 0, total_qty: 0, warranty: 0, notes: 'Enter the notes' },
                { product_name: 'Keyboard', category_name: '', brand_name: '', unit: 'Pieces (PCS)', hsn_code: '', alert_qty: 0, barcode: '', discount_type: 'Fixed', discount: 0, purchase_price: 0, tax_type: 'Inclusive', tax_value: this.default_tax ? this.default_tax.value : 0, sales_price: 0, dealer_price: 0, total_qty: 0, warranty: 0, notes: 'Enter the notes' },
            ];

            // Convert sampleData to CSV format
            // const csvContent = "Product Name, Category Name, Brand Name, Unit, HSN Code, Alert Qty, Barcode, Discount Type, Discount Value, Purchase Price, Tax Type, Tax Value, Sales Price, Dealer Price, Total Qty, Warranty, Notes\n" +
            //     sampleData.map(row => Object.values(row).join(",")).join("\n");
            // Extract keys from the first object in the sampleData array for headers
            const headers = Object.keys(sampleData[0]);

            // Convert sampleData to CSV format
            const csvContent = headers.join(",") + "\n" +
                sampleData.map(row => headers.map(header => row[header]).join(",")).join("\n");

            // Create a Blob from the CSV content
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

            // Create a URL object using the Blob
            const url = URL.createObjectURL(blob);

            // Create a download link and trigger the download
            const link = document.createElement("a");
            link.setAttribute("href", url);
            link.setAttribute("download", "product_import.csv");
            document.body.appendChild(link);
            link.click();

            // Cleanup
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        },

        saveImportedData(record, index) {
            if (index >= 0 && record && Object.keys(record).length > 0 && this.validateObjectKey(record, 'product_name')) {
                this.open_loader = true;
                if (!record.category_id) {
                    record.category_id = '';
                }
                if (!record.brand_id) {
                    record.brand_id = '';
                }
                if (!record.product_code) {
                    record.product_code = '1';
                }
                if (!record.user_id) {
                    record.user_id = this.userId;
                }
                if (!record.hsn_code) {
                    record.hsn_code = '';
                }
                if (!record.tax_type) {
                    record.tax_type = 'Inclusive';
                }
                if (!record.tax_value) {
                    record.tax_value = 0;
                }
                if (!record.total_qty) {
                    record.total_qty = 0;
                }
                if (!record.alert_qty) {
                    record.alert_qty = 0;
                }
                if (!record.purchase_price) {
                    record.purchase_price = 0;
                }
                if (!record.warranty) {
                    record.warranty = 0;
                }
                if (!record.discount) {
                    record.discount = 0;
                }
                if (!record.image) {
                    record.image = null;
                }
                if (!record.product_type) {
                    record.product_type = 'Product';
                }
                axios.post('/products_details', { ...record, company_id: this.companyId, user_id: this.userId, image: '' })
                    .then(response => {
                        // console.log('waht about response', response.data);
                        // this.closeModal(response.data.data);
                        this.importedData.splice(index, 1);
                        // console.log(this.importedData, 'EEEEEEEEEEEEEEEE happening...........');
                        this.open_loader = false;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                        this.fetchItemList(100);
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })
            } else {
                this.message = 'Please validate product name fill must..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        //---remove data--
        removeImportedData() {
            if (this.importedData.length !== 0 && this.deleteIndex !== null) {
                // Remove the saved record from the importedData
                this.importedData.splice(this.deleteIndex, 1);
                if (this.importedData.length === 0) {
                    this.importedData = [];
                    document.getElementById('fileInput').value = '';
                }
            } else {
                this.importedData = [];
                document.getElementById('fileInput').value = '';
            }
            this.deleteIndex = null;
            this.open_confirmBox = false;
            this.reset_All = false;
        },
        //---close alert--
        closeAlert() {
            this.open_message = false;
        },
        //---save all button function---
        async validateAndClose() {
            // if (this.validateDuplication('all')) {
            //     this.closeModal();
            // }
            if (this.importedData.length > 0 && Array.isArray(this.invalidRows) && this.invalidRows.length === 0) {
                this.open_loader = true;
                await this.importedData.map(record => {
                    if (!record.category_id) {
                        record.category_id = '';
                    }
                    if (!record.brand_id) {
                        record.brand_id = '';
                    }
                    if (!record.product_code) {
                        record.product_code = '1';
                    }
                    if (!record.user_id) {
                        record.user_id = this.userId;
                    }
                    if (!record.hsn_code) {
                        record.hsn_code = '';
                    }
                    if (!record.tax_type) {
                        record.tax_type = 'Inclusive';
                    }
                    if (!record.tax_value) {
                        record.tax_value = 0;
                    }
                    if (!record.total_qty) {
                        record.total_qty = 0;
                    }
                    if (!record.alert_qty) {
                        record.alert_qty = 0;
                    }
                    if (!record.purchase_price) {
                        record.purchase_price = 0;
                    }
                    if (!record.warranty) {
                        record.warranty = 0;
                    }
                    if (!record.discount) {
                        record.discount = 0;
                    }
                    if (!record.image) {
                        record.image = null;
                    }
                    if (!record.product_type) {
                        record.product_type = 'Product';
                    }
                })
                axios.post('/product-imports', { products: this.importedData, company_id: this.companyId, user_id: this.userId })
                    .then(response => {
                        // console.log('waht about response', response.data);
                        // this.closeModal(response.data.data);
                        this.open_loader = false;
                        this.importedData = [];
                        document.getElementById('fileInput').value = '';
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                        this.isDisplayed = false;
                        this.invalidRows = [];
                        this.focusedInvalidIndex = null;
                        this.fetchItemList(100);
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })
            }
            else {
                this.message = this.importedData.length === 0 ? 'Please add import data' : 'Please validate product name fill must..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        //---Validate imported--
        validateDuplication(type, record) {
            if (type === 'all') {
                // Validate imported data for uniqueness based on product name and barcode
                if (this.importedData.length > 0) {
                    const existingProducts = this.existInventory.map(item => ({
                        product_name: item.product_name,
                        barcode: item.barcode
                    }));

                    for (const importedItem of this.importedData) {
                        let { product_name, barcode } = importedItem;
                        // Handle empty barcode by generating a unique one
                        if (!barcode) {
                            barcode = this.generateUniqueBarcode();
                            record.barcode = barcode;
                        }
                        const isDuplicate = existingProducts.some(item => item.product_name.toLowerCase() === product_name.toLowerCase() || item.barcode === barcode);
                        if (isDuplicate) {
                            this.message = `Duplicate entry found for product: ${product_name} ${barcode ? 'with barcode :' + barcode : ''}`;
                            this.open_message = true;
                            return false;
                        } else {
                            return true;
                        }
                    }
                }
            }
            else {
                if (record) {
                    const existingProducts = this.existInventory.map(item => ({
                        product_name: item.product_name,
                        barcode: item.barcode
                    }));
                    let { product_name, barcode } = record;
                    // Handle empty barcode by generating a unique one
                    if (!barcode) {
                        barcode = this.generateUniqueBarcode();
                        record.barcode = barcode;
                    }
                    const isDuplicate = existingProducts.some(item => item.product_name.toLowerCase() === product_name.toLowerCase() || item.barcode === barcode);
                    if (isDuplicate) {
                        // Handle duplicate entry, for example, you can ignore, prompt user, etc.
                        this.message = `Duplicate entry found for product: ${product_name} ${barcode ? 'with barcode :' + barcode : ''}`;
                        this.open_message = true;
                        return false;
                    } else {
                        return true;
                    }
                }

            }

        },
        //--generate barcode
        generateUniqueBarcode() {
            return Date.now().toString();
        },
        //--generate unique barcode---
        generateUniqueBarcodeSync(uniqueBarcodes) {
            let barcode;
            do {
                barcode = this.generateUniqueBarcode();
            } while (uniqueBarcodes.has(barcode));
            uniqueBarcodes.add(barcode);
            return barcode;
        },
        //----remove--
        removeItemsAlreadyExist() {
            if (this.importedData.length > 0) {
                const existingProducts = this.existInventory.map(item => ({
                    product_name: item.product_name.toLowerCase(),
                    barcode: item.barcode
                }));

                this.importedData = this.importedData.filter(importedItem => {
                    let { product_name, barcode } = importedItem;

                    // Handle empty barcode by generating a unique one
                    if (!barcode) {
                        barcode = this.generateUniqueBarcode();
                        importedItem.barcode = barcode;
                    }

                    const isDuplicate = existingProducts.some(item =>
                        item.product_name === product_name.toLowerCase() || item.barcode === barcode
                    );

                    if (isDuplicate) {
                        this.message = `Duplicate entry deleted successfully..!`;
                        this.open_message = true;
                        return false; // Remove the item
                    } else {
                        return true; // Keep the item
                    }
                });
                if (this.importedData.length === 0) {
                    document.getElementById('fileInput').value = '';
                }
            }
        },
        //---delete the record

        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.deleteIndex = null;
            this.open_confirmBox = false;
            this.reset_All = false;
        },
        //---basic data load-----
        getInitialListData() {
            //---category list---
            if (this.currentCategoryList && this.currentCategoryList.length > 0) {
                this.category_list = [...this.currentCategoryList];
                this.fetchCategoryList();
            } else {
                this.fetchCategoryList();
            }
            // //---brands list---

            if (this.currentBrandList && this.currentBrandList.length > 0) {
                this.brand_list = [...this.currentBrandList];
                this.fetchBrandList();
            } else {
                this.fetchBrandList();
            }
            //---unit list---           
            if (this.currentUnitList && this.currentUnitList.length > 0) {
                this.unit_list = [...this.currentUnitList];
                this.fetchUnitList();
            } else {
                this.fetchUnitList();
            }
            //---tax and invoice setting---
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                this.invoice_setting = [...this.currentInvoice];
                this.tax_value_list = JSON.parse(this.invoice_setting[0].selected_tax);
                if (this.tax_value_list && this.tax_value_list.length > 0) {
                    let find_data = this.tax_value_list.find(opt => opt.status == true);
                    if (this.find_data && this.find_data.tax_name) {
                        this.selected_taxName = this.find_data.tax_name;
                    }
                }
                // this.fetchInvoiceSetting();
            } else {
                // this.tooltip_focus = "general";
                this.fetchInvoiceSetting();
            }

        },
        //---category brand unit model---
        openModelCategory(type) {
            if (type === 'category') {
                this.open_model = true;
                this.type_model = type;
                this.list_data = this.category_list;
            } else if (type === 'brand') {
                this.open_model = true;
                this.type_model = type;
                this.list_data = this.brand_list;
            } else {
                this.open_model = true;
                this.type_model = type;
                this.list_data = this.unit_list;
            }
        },
        closecategoryBrandUnit() {
            this.open_model = false;
            this.type_model = null;
            this.list_data = null;
        },
        //---tax list model----
        openTaxModel() {
            this.open_tax_model = true;
        },
        closeTaxList(data) {
            if (data && data.length > 0) {
                // console.log(data, 'Tax list');
                this.tax_value_list = data;
            }
            this.open_tax_model = false;
        },
        //----filter the unit list----
        //----dropdown---
        filterUnits() {
            let mergeList = [];
            if (this.unit_list.length > 0) {
                return mergeList = [...this.units_list, ...(this.unit_list.map(item => item.unit_name))];
            }
            else {
                return mergeList = [...this.units_list];
            }
        },
        validateMax(index) {
            const { discount_type, sales_price, discount } = this.importedData[index];

            // Check if both discount_type and sales_price exist
            if (discount_type && sales_price) {
                if (discount_type === 'Fixed' && discount > sales_price) {
                    // For 'Fixed' discount type, max is the sales price
                    this.message = 'The discount exceeds the sales price.';
                    this.show = true;
                    this.importedData[index].discount = sales_price;
                    return 0;
                } else if (discount_type === 'Percentage' && discount > 100) {
                    // For 'Fixed' discount type, max is the sales price
                    this.message = 'The discount exceeds the sales price.';
                    this.show = true;
                    this.importedData[index].discount = 100;
                    return 0;
                } else {
                    return discount;

                }
            } else {
                this.message = 'Please enter the sales price.';
                this.show = true;
                return 0;
            }
        },
        calculatePurchasePrice(value, index) {
            // if (index >= 0) {
            //     if (value === 'Exclusive' && this.importedData[index].tax_value >= 0) {
            //         this.importedData[index].purchase_price = this.importedData[index].purchase_price + (this.importedData[index].purchase_price * (this.importedData[index].tax_value / 100));
            //     } else {
            //         this.importedData[index].purchase_price = this.importedData[index].purchase_price;
            //     }
            // }
        },
        preventNegativeValue(event) {
            if (event.target.value < 0) {
                event.target.value = 0; // Prevents negative input
            }
        },
        //---filter data---
        getCategoryId(inputCategoryName) {
            if (this.category_list && Array.isArray(this.category_list) && this.category_list.length > 0) {
                // Normalize the inputCategoryName to lowercase
                const normalizedInput = inputCategoryName.toLowerCase();

                // Find the matching category in the category_list
                const category = this.category_list.find(item => item.category_name.toLowerCase() === normalizedInput);

                // If a category is found, return its ID; otherwise, return null or an appropriate response
                if (category) {
                    return category.id;
                } else {
                    return null; // or return 'Category not found'
                }
            }
        },
        getBrandId(inputBrandName) {
            if (this.brand_list && Array.isArray(this.brand_list) && this.brand_list.length > 0) {
                // Normalize the inputCategoryName to lowercase
                const normalizedInput = inputBrandName.toLowerCase();

                // Find the matching brand_list in the brand_list
                const brand_list = this.brand_list.find(item => item.brand_name.toLowerCase() === normalizedInput);

                // If a brand_list is found, return its ID; otherwise, return null or an appropriate response
                if (brand_list) {
                    return brand_list.id;
                } else {
                    return null; // or return 'Category not found'
                }
            }
        },
        getUnitName(inputUnit) {
            let array_data = this.filterUnits();
            // console.log(array_data, 'RRRRRRRRRRRRR UNITTTTTTTTTTTT');

            if (array_data && Array.isArray(array_data) && array_data.length > 0) {
                // Normalize the inputCategoryName to lowercase
                const normalizedInput = inputUnit.toLowerCase();

                // Find the matching unit in the unit
                const unit = array_data.find(unit => unit.toLowerCase().includes(normalizedInput));

                // If a brand_list is found, return its ID; otherwise, return null or an appropriate response
                if (unit) {
                    return unit;
                } else {
                    return null; // or return 'Category not found'
                }
            }
        },
        classifyTaxType(userInput) {
            // Normalize the input by converting it to lowercase
            const normalizedInput = userInput.toLowerCase();

            // Check if the input contains 'exclusive' or 'excluding'
            if (normalizedInput.includes('exclusive') || normalizedInput.includes('excluding') || normalizedInput.includes('exc')) {
                return 'Exclusive';
            } else {
                // If it contains 'inclusive' or 'including', return 'Inclusive'
                return 'Inclusive';
            }
        },
        IsValidtax(taxValue) {
            if (this.tax_value_list && Array.isArray(this.tax_value_list) && this.tax_value_list.length > 0) {
                const tax_data = this.tax_value_list.find(item => item.value == taxValue);
                if (tax_data) {
                    return tax_data.value;
                } else {
                    return 0;
                }
            }
        },
        classifyDiscountType(userInput) {
            // Normalize the input by converting it to lowercase
            const normalizedInput = userInput.toLowerCase();
            if (normalizedInput.includes('percent') || normalizedInput.includes('percentage') || normalizedInput.includes('p')) {
                return 'Percentage';
            } else {
                return 'Fixed';
            }
        },
        //---reset---
        resetAllData() {
            if (this.importedData && Array.isArray(this.importedData) && this.importedData.length > 0) {
                this.isDisplayed = false;
                this.reset_All = true;
                this.open_confirmBox = true;
                this.invalidRows = [];
                this.focusedInvalidIndex = null;
            }
        },
        //---display available options---
        openAvailableOptions() {
            this.show_available = true;
        },
        closeAvailableOptions() {
            this.show_available = false;
        },
        //---add items---
        addNewItem(index) {
            if (index > 0) {
                let obj_data = { product_name: '', category_name: '', brand_name: '', unit: 'Pieces (PCS)', hsn_code: '', alert_qty: 0, barcode: this.generateUniqueBarcode(), discount_type: 'Percentage', discount: 0, purchase_price: 0, tax_type: 'Exclusive', tax_value: this.default_tax ? this.default_tax.value : 0, sales_price: 0, dealer_price: 0, total_qty: 0, warranty: 0, notes: 'Enter the notes' };
                this.importedData.push(obj_data);
            }
        },
        fetchDefaultTax() {
            if (this.invoice_setting && this.invoice_setting.length > 0) {
                let tax_list = JSON.parse(this.invoice_setting[0].selected_tax);
                if (tax_list && tax_list.length > 0) {
                    let find_data = tax_list.find(opt => opt.status === true);
                    if (find_data) {
                        this.default_tax = find_data;
                    }
                }
            }
        },
        //---vaidate in product name---
        validateArrayKey() {
            // Validate all objects in the array
            return this.importedData.every(item => this.validateObjectKey(item, 'product_name'));
        },
        validateObjectKey(object, key) {
            // Check if the object has a value for the given key
            return object[key] !== undefined && object[key] !== null && object[key] !== '';
        },
        //--heightlight in invalid data row--
        // Method to validate imported data
        validateAndHighlight() {
            this.invalidRows = [];

            // Regular expression to validate contact number with digits, +, -, and spaces
            const contactNumberPattern = /^[0-9+\- ]+$/;

            // Check for invalid first_name or contact_number
            this.importedData.forEach((data, index) => {
                // Validate first_name and contact_number
                if (!data.product_name || data.product_name === '') {
                    this.invalidRows.push(index); // Add invalid index to array
                }
            });

            // Set focus on the first invalid row if any
            if (this.invalidRows.length > 0) {
                if (!this.isDisplayed) {
                    this.isDisplayed = true;
                    this.focusedInvalidIndex = this.invalidRows[0];
                    this.focusOnInvalidRow();
                    this.message = 'Some rows have Product name empty. Please review and update the highlighted fields.';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            }
        },
        // Method to handle Enter key press and move to next invalid row
        focusNextInvalidRow(currentIndex) {
            // Find the index of the next invalid row
            const nextInvalidIndex = this.invalidRows.find((index) => index > currentIndex);

            // If there's a next invalid row, focus on it, otherwise focus the next row
            if (nextInvalidIndex !== undefined) {
                this.focusedInvalidIndex = nextInvalidIndex;
                this.focusOnInvalidRow();
            } else {
                // If no next invalid row, focus the next row in the table
                const nextRow = currentIndex + 1;
                this.focusedInvalidIndex = nextRow;
                if (nextRow < this.importedData.length) {
                    this.focusOnInvalidRow();
                }
            }
        },
        focusOnInvalidRow() {
            if (this.focusedInvalidIndex !== null) {
                // Make sure that the row is fully rendered before interacting with it
                this.$nextTick(() => {
                    const row = this.$refs[`invalidRow${this.focusedInvalidIndex}`]; // Ensure the ref is correct

                    // Check if the row exists before trying to call scrollIntoView
                    if (Array.isArray(row) && row.length > 0) {
                        // Scroll the row into view smoothly
                        row[0].scrollIntoView({ behavior: 'smooth', block: 'center' });

                        // Wait a bit before focusing to ensure scroll happens first
                        // setTimeout(() => {
                        const inputField = row[0].querySelector('input'); // Query for input field
                        if (inputField) {
                            inputField.focus(); // Focus on the input element
                        } else {
                            console.warn('No input field found in the row.');
                        }
                        // }, 300);  Adjust delay as needed to ensure smooth scrolling happens first
                    } else {
                        console.error('Row not found with ref: ', `invalidRow${this.focusedInvalidIndex}`);
                    }
                });
            }
        },
        //---update tax selection--
        updateTaxName(selectedTax, index) {
            if (selectedTax) {
                this.importedData[index]['gst_value'] = selectedTax.value;
                this.importedData[index]['tax_name'] = selectedTax.tax_name;
                this.calculatePurchasePrice(this.importedData[index]['gst_value'], index);
            }
        }
    },
    mounted() {
        this.fetchDefaultTax();
    },
    watch: {
        isOpen(newValue) {
            if (newValue) {
                this.getInitialListData();
            }
            setTimeout(() => {
                this.isOpenModal = newValue;
            }, 100);
        },
        currentCategoryList: {
            deep: true,
            handler(newValue) {
                this.category_list = [...newValue];
            }
        },
        currentBrandList: {
            deep: true,
            handler(newValue) {
                this.brand_list = [...newValue];
            }
        },
        currentUnitList: {
            deep: true,
            handler(newValue) {
                this.unit_list = [...newValue];
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.invoice_setting = [...newValue];
                    this.tax_value_list = JSON.parse(this.invoice_setting[0].selected_tax);
                }
            }
        },
        // importedData: {
        //     deep: true,
        //     handler(newValue) {
        //         if (Array.isArray(newValue) && newValue.length === 0 && document.getElementById('fileInput').value !== '') {
        //             document.getElementById('fileInput').value = '';
        //         }
        //     }
        // }
        // Watch for changes in importedData to revalidate
        importedData: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.validateAndHighlight();
                }
            }
        },
        invoice_setting: {
            deep: true,
            handler(newValue) {
                this.fetchDefaultTax();
            }
        }
    }
};
</script>

<style scoped>
/* Add your custom styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.3s;
}

.highlight-row {
    background-color: rgba(255, 0, 0, 0.2);
    /* Light red background for invalid rows */
}
</style>