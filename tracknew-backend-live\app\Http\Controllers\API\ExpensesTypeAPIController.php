<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateExpensesTypeAPIRequest;
use App\Http\Requests\API\UpdateExpensesTypeAPIRequest;
use App\Models\Expenses_type;
use App\Repositories\Expenses_typeRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;
/**
 * Class Expenses_typeController
 * @package App\Http\Controllers\API
 */

class ExpensesTypeAPIController extends AppBaseController
{
    /** @var  Expenses_typeRepository */
    private $expensesTypeRepository;

    public function __construct(Expenses_typeRepository $expensesTypeRepo)
    {
        $this->expensesTypeRepository = $expensesTypeRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/expensesTypes",
     *      summary="getExpenses_typeList",
     *      tags={"Expenses_type"},
     *      description="Get all Expenses_types",
     *      @OA\Parameter(
     *          name="company_id",
     *          description="ID of the company whose services are to be fetched",
     *          @OA\Schema(
     *              type="string"
     *          ),
     *          required=true,
     *          in="query"
     *      ),
     *      @OA\Parameter(
     *          name="page",
     *          description="Page number for pagination",
     *          @OA\Schema(
     *              type="integer"
     *          ),
     *          in="query"
     *      ),
     *      @OA\Parameter(
     *          name="per_page",
     *          description="Number of items per page",
     *          @OA\Schema(
     *              type="integer"
     *          ),
     *          in="query"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Expenses_type")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $expensesQuery = Expenses_type::where('company_id', $companyId);

        if ($perPage === 'all') {
            $perPage =  $expensesQuery->count();
        }

        $expenses =  $expensesQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $expenses->items(), // Get the paginated items
            'pagination' => [
                'total' => $expenses->total(),
                'per_page' => $expenses->perPage(),
                'current_page' => $expenses->currentPage(),
                'last_page' => $expenses->lastPage(),
                'from' => $expenses->firstItem(),
                'to' => $expenses->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/expensesTypes",
     *      summary="createExpenses_type",
     *      tags={"Expenses_type"},
     *      description="Create Expenses_type",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Expenses_type")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Expenses_type"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateExpensesTypeAPIRequest $request)
    {
        $input = $request->all();

        $expensesType = $this->expensesTypeRepository->create($input);

        return $this->sendResponse($expensesType->toArray(), 'Expenses Type saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/expensesTypes/{id}",
     *      summary="getExpenses_typeItem",
     *      tags={"Expenses_type"},
     *      description="Get Expenses_type",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Expenses_type",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Expenses_type"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Expenses_type $expensesType */
        $expensesType = $this->expensesTypeRepository->find($id);

        if (empty($expensesType)) {
            return $this->sendError('Expenses Type not found');
        }

        return $this->sendResponse($expensesType->toArray(), 'Expenses Type retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/expensesTypes/{id}",
     *      summary="updateExpenses_type",
     *      tags={"Expenses_type"},
     *      description="Update Expenses_type",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Expenses_type",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Expenses_type")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Expenses_type"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateExpensesTypeAPIRequest $request)
    {
        $input = $request->all();

        /** @var Expenses_type $expensesType */
        $expensesType = $this->expensesTypeRepository->find($id);

        if (empty($expensesType)) {
            return $this->sendError('Expenses Type not found');
        }

        $expensesType = $this->expensesTypeRepository->update($input, $id);

        return $this->sendResponse($expensesType->toArray(), 'Expenses_type updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/expensesTypes/{id}",
     *      summary="deleteExpenses_type",
     *      tags={"Expenses_type"},
     *      description="Delete Expenses_type",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Expenses_type",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Expenses_type $expensesType */
        $expensesType = $this->expensesTypeRepository->find($id);

        if (empty($expensesType)) {
            return $this->sendError('Expenses Type not found');
        }

        $expensesType->delete();

        return $this->sendSuccess('Expenses Type deleted successfully');
    }
}
