<template>
    <div class="flex h-screen relative">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" :category_name="category_name" :customerData="originalData"
                @searchData="getFiteredDataList"></headbar> -->

            <!-- services home -->
            <div class="p-1 m-1 relative overflow-y-auto">
                <!-- Display home component by default -->
                <home :data="data" :category_id="category_id" :category_name="category_name" :labelsName="labelsName"
                    :fieldKey="fieldKey" :isMobile="isMobile" :searchedData="getFilteredData"
                    @filter_update="filteredData" :category_data="category_data" :pagination="pagination"
                    @paginationGetData="paginationGetData" :companyId="companyId" :open_skeleton="open_skeleton"
                    :serviceTrack="serviceTrack" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </home>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/services/services_home/headbar.vue';
import home from '../supporting/services/services_home/home.vue';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'services_category_list',
    components: {
        // sidebar,
        // headbar,
        home,
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            typeofService: null,
            route_item: 3,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            data: [],
            labelsName: [],
            fieldKey: [],
            dataFromChild: [],
            getFilteredData: [],
            originalData: [],
            category_data: [],
            //---api integration--
            companyId: null,
            userId: null,
            pagination: {},
            open_skeleton: true,
            serviceTrack: [],
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    methods: {
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        // Method to parse the URL
        async parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const category = urlParts[urlParts.length - 2];
            const id = urlParts[urlParts.length - 1];
            this.category_name = decodeURIComponent(category);
            this.category_id = id;

            // Check if servicecategory_data is defined
            if (this.servicecategory_data.length !== 0) {
                const categoryData = this.servicecategory_data.find(data => data.id === Number(id));
                // console.log(categoryData, 'EEEE');
                if (categoryData) {
                    await this.serviceDataList(categoryData.id, 1, 10);
                    //--backup data--
                    // console.log(this.data, 'DADADAAD');

                    if (categoryData.form) {
                        const labelsNameSubset = JSON.parse(categoryData.form).map((opt) => opt.lableName);
                        const fieldKeySubset = JSON.parse(categoryData.form).map((opt) => opt.fieldKey);

                        // Collect values at indices 0 to 3
                        this.labelsName = labelsNameSubset.slice(0, 4);
                        this.fieldKey = fieldKeySubset.slice(0, 4);

                        let find_status_list = JSON.parse(categoryData.form).find(opt => opt.fieldKey === 'status');
                        if (find_status_list) {
                            // if (find_status_list.option && find_status_list.option_status) {
                            //     this.serviceTrack = find_status_list.option.map((opt, i) => {
                            //         if (find_status_list.option_status[i] === true) {
                            //             return { name: opt, date: '', status: false };
                            //         }
                            //     }).filter(track => track);
                            // } else if (find_status_list.option) {
                            this.serviceTrack = find_status_list.option.map((opt, i) => {
                                return { name: opt, date: '', status: false };
                            });
                            // }

                            // console.log(this.serviceTrack, 'WWWWWWWWWWWWWWWWWWWWWWWW');
                        }
                    }
                    this.category_data = categoryData;
                }
                // Set category_name and category_data
                // this.category_name = categoryData.name;

            }
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        getFiteredDataList(data) {
            // console.log(data, 'WWWW');
            // this.getFilteredData = data;
            // console.log(this.originalData, 'RRRRR');
            if (!this.isEmptyObject(data)) {
                this.data = this.originalData.filter(opt => opt.customer.contact_number.includes(data.contact_number));
                // console.log(this.data, 'EEEEE');
            }
            else {
                this.data = this.originalData;
            }
        },
        filteredData(type, objData) {
            if (type === 'filter') {
                this.data = objData;
            } else {
                this.data = this.originalData;
            }
        },
        //---service category---
        serviceCategoryList() {
            axios.get(`/service_categories`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'service gatgory get..!');
                    this.servicecategory_data = response.data.data;
                    this.parseUrl();
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---service category---
        serviceDataList(id, page, per_page) {
            this.open_skeleton = true;
            axios.get(`/services`, { params: { company_id: this.companyId, category_id: id, page: page, per_page: per_page } })
                .then(response => {
                    console.log(response.data, 'service gatgory get..!');
                    this.open_skeleton = false;
                    this.pagination = response.data.pagination;
                    this.data = response.data.data;
                    this.originalData = this.data;
                    // return response.data.data;
                    // console.log(typeof this.data[0].customer);
                })
                .catch(error => {
                    console.error('Error:', error);
                    this.open_skeleton = false;

                })
        },
        //---get next Data--
        paginationGetData(data, per_page) {
            // console.log(data, 'TTTT', per_page);
            this.open_skeleton = true;
            this.serviceDataList(this.category_id, data, per_page);
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile(); // Initial check
        if (this.servicecategory_data && this.servicecategory_data.length === 0) {
            this.serviceCategoryList();
        }

        // const collectForm = localStorage.getItem('CategoriesForm');
        // if (collectForm) {
        //     let dataParse = JSON.parse(collectForm);
        //     // console.log(dataParse, 'WWWWWWWWWWWW');
        //     this.servicecategory_data = dataParse;
        // }
        // this.parseUrl(); // Call the function to parse the URL
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },

};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>
