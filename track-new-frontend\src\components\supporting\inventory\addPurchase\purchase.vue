<template>
    <div :class="{ 'manualStyle text-sm mt-[60px] mb-[50px]': isMobile, 'text-sm p-1': !isMobile }">
        <!-- Modal -->
        <div class="relative my-custom-margin">
            <!-- Content based on selected option -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-4 mt-3">
                <!--Supplier details-->
                <div class="relative" ref="supplierInputWrapper">
                    <label for="serviceCategoryDropdown"
                        class="absolute left-5 text-xs bg-white px-1 text-gray-500 z-10 -mt-1">Supplier
                        Name<span class="text-red-700">*</span></label>
                    <div class="flex justify-end relative">
                        <!-- Input field -->
                        <input v-model="formValues.supplier" @input="handleSupplierChange(formValues.supplier)"
                            @focus="handleSupplierChange(formValues.supplier), isDropdownsupplier = true"
                            list="productList"
                            class="py-2 px-2 mt-1 border border-gray-300 w-full rounded-tr-none rounded-br-none"
                            @blur="preventBlur" @keydown.enter="handleEnterKey(index, 'supplier')"
                            @keydown.down.prevent="handleDownArrow(filteredSupplierList)"
                            @keydown.up.prevent="handleUpArrow(filteredSupplierList)" />

                        <!-- Dropdown icon -->
                        <p class="absolute top-0 right-0 flex items-center h-full px-3 mr-8 cursor-pointer"
                            @click="isDropdownsupplier = !isDropdownsupplier">
                            <font-awesome-icon v-if="isDropdownsupplier" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </p>

                        <!-- Button -->
                        <button
                            class="border border-teal-600 px-2 mt-1 hover:border-teal-700 text-white bg-teal-600 rounded-r"
                            @click="addNewSupplier(formValues.supplier)"><font-awesome-icon
                                icon="fa-solid fa-plus" /></button>
                    </div>

                    <div class="w-full relative">
                        <div v-if="isDropdownsupplier"
                            class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                            style="z-index: 100;">
                            <p v-for="(option, index) in filteredSupplierList" :key="index"
                                @click="selectedSupplierData(option)"
                                :class="{ 'bg-gray-100': index === selectedIndex }"
                                class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                                {{ option.name }}
                            </p>
                            <!-- Add New Customer button -->
                            <button v-if="filteredSupplierList.length === 0"
                                @click="addNewSupplier(formValues.supplier)"
                                class="w-full text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                                + Add Supplier
                            </button>
                        </div>
                    </div>
                </div>
                <div class="relative">
                    <label for="date" class="absolute left-5 text-xs bg-white px-1 text-gray-500 z-10 -mt-1">Purchase
                        date
                        <span class="text-red-700">*</span></label>
                    <input id="date" v-model="formValues.purchase_order_date" type="date" @change="is_updated = true"
                        @click="isDropdownsupplier = false" class="py-2 px-1 mt-1  border border-gray-300 w-full" />
                </div>
                <div class="relative">
                    <label for="purchase_order"
                        class="absolute left-5 text-xs bg-white px-1 text-gray-500 z-10 -mt-1">Purchase Invoice Number
                        <span class="text-red-700">*</span></label>
                    <input id="purchase_order" v-model="formValues.purchase_order" type="text"
                        @change="is_updated = true" class="py-2 px-1 mt-1  border border-gray-300 w-full"
                        ref="purchasename" />
                </div>

                <!--due days-->
                <div class="relative">
                    <label for="day" class="absolute left-5 text-xs bg-white px-1 text-gray-500 z-10 -mt-1">Due
                        Days</label>
                    <div class="flex mt-1 text-sm">
                        <select v-model="formValues.due_interval" @change="is_updated = true"
                            class="py-2 px-1 border border-gray-300 w-full">
                            <option disabled value="" class="text-gray-400">select due days</option>
                            <option value="0 Days">0 Days</option>
                            <option value="1 Days">1 Days</option>
                            <option value="3 Days">3 Days</option>
                            <option value="5 Days">5 Days</option>
                            <option value="7 Days">7 Days</option>
                            <option value="9 Days">9 Days</option>
                            <option value="12 Days">12 Days</option>
                            <option value="15 Days">15 Days</option>
                            <option value="20 Days">20 Days</option>
                            <option value="30 Days">30 Days</option>
                            <option value="45 Days">45 Days</option>
                            <option value="60 Days">60 Days</option>
                            <option value="90 Days">90 Days</option>
                            <option value="100 Days">100 Days</option>
                            <option value="120 Days">120 Days</option>
                            <option value="200 Days">200 Days</option>

                        </select>
                    </div>
                </div>
                <!---SOS-->
                <div class="relative">
                    <label for="sos" class="absolute left-5 text-xs bg-white px-1 text-gray-500 z-10 -mt-1">SOS (Source
                        of Supply)</label>

                    <select v-model="formValues.sos" @change="is_updated = true"
                        class="mt-1 py-2 px-1 border border-gray-300 w-full">
                        <option disabled value="" class="text-gray-400">Select Source of supply</option>
                        <option v-if="state_code_list.length > 0" v-for="(opt, index) in state_code_list" :key="index"
                            :value="opt.code + ' ' + opt.name">{{ opt.code + ' - ' + opt.name }}</option>
                    </select>
                </div>
                <!--Warehouse -->
                <div class="relative" ref="warehouseInputWrapper">
                    <label for="serviceCategoryDropdown"
                        class="absolute left-5 text-xs bg-white px-1 text-gray-500 z-10 -mt-1">Warehouse<span
                            class="text-red-700">*</span></label>
                    <div class="flex justify-end relative">

                        <input v-model="formValues.warehouse" @input="handleWarehouseChange" list="productList"
                            @focus="handleFocus"
                            class="py-2 px-1 mt-1  border border-gray-300 w-full rounded-br-none rounded-tr-none"
                            @keydown.enter="handleEnterKey(index, 'warehouse')"
                            @keydown.down.prevent="handleDownArrow(filteredWarehouseList)"
                            @keydown.up.prevent="handleUpArrow(filteredWarehouseList)" />
                        <!-- <span class="absolute -ml-5 mt-3 justify-center cursor-pointer" style="z-index: 100;"
                        @click="isDropdownwarehouse = !isDropdownwarehouse">
                        {{ isDropdownwarehouse ? '&#11165;' : '&#11167;' }}
                    </span> -->
                        <!-- Dropdown icon -->
                        <p class="absolute top-0 right-0 flex items-center h-full px-3 mr-8 cursor-pointer"
                            @click="isDropdownwarehouse = !isDropdownwarehouse">
                            <!-- Adjust the styling of the icon -->
                            <!-- <span v-html="isDropdownwarehouse ? '&#11165;' : '&#11167;'" class="text-black"></span> -->
                            <font-awesome-icon v-if="isDropdownwarehouse" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </p>

                        <!-- Button -->
                        <button
                            class="border border-teal-600 px-2 mt-1 hover:border-teal-600 text-white bg-teal-600 font-bold rounded-r"
                            @click="addNewWarehouse"><font-awesome-icon icon="fa-solid fa-plus" /></button>
                    </div>
                    <div class="w-full relative">
                        <div v-if="isDropdownwarehouse"
                            class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                            style="z-index: 100;">
                            <p v-for="(option, index) in filteredWarehouseList" :key="index"
                                @click="selectedWarehouseData(option)"
                                :class="{ 'bg-gray-100': index === selectedIndex }"
                                class="cursor-pointer hover:bg-gray-100 p-2 border-b">{{ option.name }}
                            </p>
                            <!-- Add New Customer button -->
                            <button v-if="filteredWarehouseList.length === 0" @click="addNewWarehouse"
                                class="w-full text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                                + Add Warehouse
                            </button>
                        </div>
                    </div>
                </div>
                <!--select product-->
                <div v-if="!isAndroid" class="relative sm:col-span-2"
                    :class="{ 'flex justify-center items-center': !isMobile }">
                    <!--searh product-->
                    <div class="flex relative w-full" @mouseover="tooltip.item = true"
                        @mouseleave="tooltip.item = false" :class="{ 'mt-3': isMobile }">
                        <div class="border border-r-0 py-2 px-2"><img :src="barcode_icon" alt="label icon"
                                class="w-[20px] h-[20px] justify-center items-center" /></div>
                        <!---search item--->
                        <input class="flex border justify-between py-2 px-2 w-full outline-none rounded-none"
                            type="text" v-model="selected_item" ref="selectItemField" @input="handleProductChange"
                            @keydown.enter="handleEnterKey(0, 'product',)"
                            @keydown.down.prevent="handleDownArrow(filteredProductList)"
                            @keydown.up.prevent="handleUpArrow(filteredProductList)"
                            @focus="isFormFocus.selected_item = true" @blur="blurItemDropdown"
                            :class="{ 'border-blue-600': isFormFocus.selected_item }"
                            placeholder="Item name / barcode" />
                        <!--Add new-->
                        <div class="border border-l-0 py-2 px-2 cursor-pointer" @click="openModal"><img
                                :src="add_item_icon" alt="add item"
                                class="w-[20px] h-[20px] justify-center items-center" />
                        </div>
                        <!--Item dropdown-->
                        <ul v-if="isDropdownOpenProduct && selected_item && selected_item.length > 1"
                            class="absolute mt-10 ml-8 sm:ml-8 lg:ml-9 w-1/4 max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm cursor-pointer"
                            style="z-index: 150;" :style="{ width: $refs.selectItemField.offsetWidth + 'px' }">
                            <li class="hover:bg-gray-300 px-3 py-1 border" v-for="(product, i) in filteredProductList"
                                :key="i" :class="{ 'bg-gray-200': i === selectedIndex }"
                                @click="selectedProductData(0, product)">
                                {{ product.barcodes.barcode + ' - ' + product.products.product_name }}
                            </li>
                            <li v-if="filteredProductList.length === 0 && selected_item && selected_item.length > 1 && findExistItem()"
                                @click="openModal"
                                class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">+
                                New Product</li>
                        </ul>
                        <!---tooltip-->
                        <div v-if="tooltip.item"
                            class="absolute flex flex-col items-center group-hover:flex -mt-8 ml-[100px] lg:ml-[150px]">
                            <span
                                class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                <p>Select Item</p>
                            </span>
                            <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!--Items-->
            <div class="mt-3 relative">
                <div v-if="!isAndroid"
                    class="overflow-x-auto sm:h-[400px] lg:h-[300px] xl:h-[280px] 2xl:h-[350px] 3xl:h-[550px] table-container">
                    <table class="w-full text-sm"> <!-- Apply class to the table -->
                        <thead class="sticky top-0 z-10 set-header-background">
                            <tr class="set-header-background text-white font-normal">
                                <th class="border p-2 font-normal">Product Name</th>
                                <!-- <th class="border p-2">Barcode</th> -->
                                <!-- <th class="border p-2">Product Code</th> -->
                                <th class="border font-normal">Total Qty</th>
                                <th class="border font-normal">Price/qty</th>
                                <th class="px-2 py-2 border border-white font-normal">Discount({{ currentCompanyList &&
                                    currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }})
                                </th>
                                <th class="px-2 py-2 border border-white font-normal">Tax</th>
                                <th class="px-2 py-2 border border-white font-normal">Subtotal</th>
                                <!-- <th class="border">selling Price/qty</th> -->
                                <!-- <th class="border">Tax</th> -->
                                <th class="border font-normal">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(item, index) in formValues.items" :key="index" class="text-center py-2">
                                <td class="border p-1">
                                    <div class="px-1">
                                        <input type="text" class="text-center w-full" v-model="item.product_name"
                                            readonly />

                                        <!---add serial number-->
                                        <!-- v-if="item.product && item.product.product_type === 'Product'" -->
                                        <div class="flex justify-center px-1">
                                            <button
                                                v-if="(item.serial_no === undefined || item.serial_no === '' || !item.serial_no || (Array.isArray(item.serial_no) && item.serial_no.length === 0)) && (!item.notes || item.notes == '')"
                                                class="text-[10px] bg-blue-400 text-white px-3 rounded rounded-full p-[2px] hover:bg-blue-500 line-clamp-1"
                                                @click="openSerialNumber(item)">Add Serial No</button>
                                            <div
                                                v-if="(item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0) || (item.notes && item.notes !== '')">
                                                <p v-if="typeOfInvoice !== 'estimation' && (item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0)"
                                                    class="flex flex-wrap items-center space-x-1 text-xs">
                                                    <span class="font-semibold">Serial No:</span>
                                                    <span v-for="serial in item.serial_no" :key="serial"
                                                        class="truncate inline-block max-w-[30ch]">
                                                        {{ serial }},
                                                    </span>
                                                </p>
                                                <p v-if="item.notes && item.notes !== ''"
                                                    class="flex flex-wrap items-center space-x-1">
                                                    <span class="font-semibold">Des:</span>
                                                    <span class="truncate inline-block max-w-[50ch]">
                                                        {{ item.notes }}
                                                    </span>
                                                </p>
                                                <button
                                                    class="text-[10px] text-green-700 border px-2 border-gray-700 rounded hover:border-green-700 ml-1"
                                                    @click="openSerialNumber(item)">Edit</button>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                                <td class="border text-sm">
                                    <div class="flex justify-center items-center px-2">
                                        <div @click="removeQuantity(index)"
                                            class="cursor-pointer border border-red-300 flex justify-center items-center px-2 py-2 font-bold bg-red-100 hover:bg-red-100 text-red-700 rounded-l ">
                                            <font-awesome-icon icon="fa-solid fa-minus" />
                                        </div>
                                        <input v-model="item.total_qty" type="number"
                                            class="text-center w-[30%] rounded-none py-1"
                                            @input="handleProductChangetotal(item)" @change="is_updated = true"
                                            :readonly="!item.editing"
                                            :class="{ 'border rounded': item.editing, 'outline-none': !item.editing }"
                                            ref="enterQuantity" />
                                        <div @click="addQuantity(index)"
                                            class="cursor-pointer border border-green-300 flex justify-center px-2 py-2 font-bold bg-green-100 hover:bg-green-200 text-green-700 rounded-r ">
                                            <font-awesome-icon icon="fa-solid fa-plus" />
                                        </div>
                                    </div>
                                </td>
                                <td class="border text-sm">
                                    <div class="px-2">
                                        <input v-model="item.price_per_qty" @change="is_updated = true" type="number"
                                            class="text-center w-3/4 py-1" @input="handleProductChangetotal(item)"
                                            :readonly="!item.editing" @keydown.enter.prevent="focusItemFields"
                                            :class="{ 'border rounded': item.editing, 'outline-none': !item.editing }" />
                                    </div>
                                </td>
                                <!--discount-->
                                <td class="border w-[100px] items-center justify-center items-center text-center px-1 relative"
                                    @mouseover="tooltip['discount' + 'formData' + index] = true"
                                    @mouseleave="tooltip['discount' + 'formData' + index] = false">
                                    <!--tooltip-->
                                    <div v-if="tooltip['discount' + 'formData' + index]"
                                        class="absolute flex flex-col items-center group-hover:flex -mt-8">
                                        <span
                                            class="relative rounded-md z-10 px-2 py-1 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                            <p>Click to change</p>
                                        </span>
                                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                    </div>
                                    <div clss="flex jsutify-center items-center">
                                        <p class="border rounded py-1 bg-gray-100 m-1 px-2"
                                            @click="openItemTaxDiscountDialog(item, index, 'discount')">
                                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }}
                                            {{ item.discount }}
                                        </p>
                                    </div>
                                </td>
                                <!--tax-->
                                <td class="border relative" @mouseover="tooltip['tax' + 'formData' + index] = true"
                                    @mouseleave="tooltip['tax' + 'formData' + index] = false">
                                    <!--tooltip-->
                                    <div v-if="tooltip['tax' + 'formData' + index]"
                                        class="absolute flex flex-col items-center group-hover:flex -mt-5">
                                        <span
                                            class="relative rounded-md z-10 px-2 py-1 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                            <p>Click to change</p>
                                        </span>
                                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                    </div>
                                    <div class="flex justify-center items-center">
                                        <p class="border w-[100px] py-1 px-2 rounded m-1 bg-gray-100"
                                            @click="openItemTaxDiscountDialog(item, index, 'tax')">
                                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }}
                                            {{ item.tax }}
                                        </p>
                                    </div>
                                </td>
                                <!--sub total-->
                                <td class="border text-sm">
                                    <div class="px-2">
                                        <input v-model="item.total_price" type="number" class="text-center py-1 w-3/4"
                                            @keydown.enter.prevent="focusItemFields"
                                            :class="{ 'border rounded': item.editing, 'outline-none': !item.editing }"
                                            readonly />
                                    </div>
                                </td>
                                <td class="border text-sm">
                                    <div class="flex justify-between items-center px-1">
                                        <button @click="moveUp(index)" :class="{ 'hidden': index === 0 }"
                                            title="Move Up"
                                            class="w-full flex justify-center text-blue-700 hover:text-blue-600">
                                            <font-awesome-icon icon="fa-solid fa-arrow-up" />
                                        </button>
                                        <button @click="moveDown(index)"
                                            :class="{ 'hidden': index === formValues.items.length - 1 }"
                                            title="Move Down"
                                            class="w-full flex justify-center text-blue-700 hover:text-blue-600">
                                            <font-awesome-icon icon="fa-solid fa-arrow-down" />
                                        </button>
                                        <button @click="confirmDelete(index)"
                                            class="text-red-700 font-bold cursor-pointer hover:bg-gray-100 w-full flex justify-center">
                                            <font-awesome-icon icon="fa-solid fa-trash-can" style="color: red" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <!-- <tr class="text-center border">
                                <td colspan="7" class="py-2">
                                    <button @click="addNewProductsList" class="bg-blue-700 text-white px-2 py-1 rounded"
                                        ref='addRow'>+
                                        Add Row</button>
                                </td>
                            </tr> -->
                        </tbody>
                    </table>
                </div>
                <!--Mobile view card design-->
                <div v-if="isAndroid && isMobile" class="flex justify-center items-center mb-3">
                    <button @click="editProduct"
                        class="text-blue-700 px-4 rounded border-2 border-dashed border-blue-600">
                        {{ formValues.items && formValues.items.length === 0 ? '+ Add Item' : '+ Add / Edit Items' }}
                    </button>
                </div>
                <div v-if="isAndroid && isMobile" class="mt-1 flex flex-wrap justify-center table-container"
                    :class="{ 'h-[250px] overflow-y-auto': formValues.items && formValues.items.length > 7 && isAndroid && isMobile }">
                    <table class="table-auto w-full border-collapse bg-white">
                        <!-- Table Header -->
                        <thead>
                            <tr class="font-bold text-center border-b">
                                <th class="text-left px-3 py-2 col-span-2">Product</th>
                                <th class="py-2">QTY</th>
                                <th class="py-2">Total</th>
                                <th class="py-2">Actions</th>
                            </tr>
                        </thead>

                        <!-- Table Body -->
                        <tbody>
                            <tr v-for="(item, index) in formValues.items" :key="index" class="border-b text-center">
                                <!-- Product Name -->
                                <td class="text-left px-3 py-2 font-semibold">{{ item.product_name }}</td>

                                <!-- Quantity and Total -->
                                <td class="py-2">{{ item.total_qty }}</td>
                                <td class="py-2">{{ item.total }}</td>

                                <!-- Actions -->
                                <td class="flex justify-center space-x-2 py-2 px-1">
                                    <button @click="editProduct(index)" title="Edit product"
                                        class="text-blue-700 hover:text-blue-600">
                                        <font-awesome-icon icon="fa-solid fa-pencil" />
                                    </button>
                                    <button v-if="index > 0" @click="moveUp(index)" title="Move Up"
                                        class="text-blue-700 hover:text-blue-600">
                                        <font-awesome-icon icon="fa-solid fa-arrow-up" />
                                    </button>
                                    <button v-if="index < formValues.items.length - 1" @click="moveDown(index)"
                                        title="Move Down" class="text-blue-700 hover:text-blue-600">
                                        <font-awesome-icon icon="fa-solid fa-arrow-down" />
                                    </button>
                                    <button @click="confirmDelete(index)" title="Delete"
                                        class="text-red-700 hover:bg-gray-100">
                                        <font-awesome-icon icon="fa-solid fa-trash-can" />
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
            <div class="py-2 mt-1 bg-gray-200 px-3 relative w-full rounded shadow-inner">
                <div class="grid grid-cols-2 sm:grid-cols-6  gap-2">
                    <!--total qantity-->
                    <div :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <p>Total Quantity:</p>
                        <p class="text-md items-center font-semibold text-blue-800">
                            <font-awesome-icon icon="fa-solid fa-tags" />
                            {{ totalQuantity }}
                        </p>
                    </div>
                    <!--total amount-->
                    <div :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <p>Total Amount:</p>
                        <p class="text-md font-semibold text-blue-800">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }}
                            {{ formValues.total }}
                        </p>
                    </div>
                    <!--shipping charges-->
                    <div :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <div class="flex cursor-pointer" @click="openShippingDialog" title="click to change">
                            <p>Shipping Charge:</p>
                            <font-awesome-icon icon="fa-regular fa-pen-to-square" size="lg"
                                class="ml-1 text-blue-600" />
                        </div>
                        <p class="font-semibold text-md text-sky-600">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }}
                            {{ this.shipping_details.shipping ? this.shipping_details.shipping.toFixed(2) :
                                this.shipping_details.shipping }}
                        </p>
                    </div>
                    <!--total discount-->
                    <div :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <div class="flex cursor-pointer" @click="openOverallDiscountDialog" title="click to change">
                            <p>Total Discount:</p>
                            <font-awesome-icon icon="fa-regular fa-pen-to-square" size="lg"
                                class="ml-1 text-blue-600" />
                        </div>
                        <div class="items-center">
                            <div class="text-xs items-center">Items:
                                <span class="font-semibold text-red-500">
                                    {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}
                                    {{ totalDiscount }}
                                </span>
                            </div>
                            <div v-if="over_all_discount !== null" class="text-xs font-semibold items-center">
                                OverAll:
                                <span class="font-semibold text-red-500">
                                    {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}
                                    {{ overAllDiscount }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <!--save and print --- && typeOfInvoice !== 'proforma'-->
                    <div class="col-span-2 items-center w-full relative"
                        :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <!--grand total-->
                        <div class="flex justify-between items-center py-1">
                            <p>Grand Total:</p>
                            <p class="flex items-center font-semibold text-green-800 px-2">
                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                    currentCompanyList.currency }}
                                {{ grandTotal.toFixed(2) }}
                            </p>

                        </div>
                        <div>
                            <div class="flex justify-end items-center text-xs">
                                <p>Mark as fully payment</p>
                                <input type="checkbox" class="ml-2" v-model="formValues.mark_full_pay"
                                    @change="is_updated = true" />
                            </div>
                            <div class="flex justify-end py-2 relative">
                                <p class="absolute text-gray-400 left-3 top-3">{{ currentCompanyList &&
                                    currentCompanyList.currency === 'INR' ? '\u20b9' :
                                    currentCompanyList.currency
                                }}
                                </p>

                                <input type="number" placeholder="0" v-model="formValues.paid"
                                    @input="calculateBalance(), validateFullPay()"
                                    class="text-end px-5 border py-1 bg-gray-100 rounded rounded-tr-none rounded-br-none w-full" />
                                <select class="border py-1 px-2 rounded rounded-tl-none rounded-bl-none"
                                    v-model="formValues.payment_type" @change="is_updated = true">
                                    <option v-if="invoice_setting && invoice_setting[0].payment_opt"
                                        v-for="(opt, index) in JSON.parse(invoice_setting[0].payment_opt)" :key="index"
                                        :value="opt.type">{{ opt.type }}</option>
                                </select>
                            </div>
                            <div class="flex justify-between">
                                <p class="text-start">{{ !formValues.return_amount ?
                                    'Balance Amount' :
                                    'Return Amount' }}</p>
                                <p class="text-end text-red-700 font-bold">{{ currentCompanyList &&
                                    currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }}
                                    {{ !formValues.return_amount ?
                                        formValues.balance_amount :
                                        formValues.return_amount }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Buttons -->
            <div :class="{ 'fixed bottom-0 left-0 w-full bg-white grid grid-cols-2 text-sm lg:text-lg z-20': isMobile, 'fixed bottom-0 flex justify-center items-center mt-7 bg-white': !isMobile }"
                class="w-full">
                <button @click="this.$router.go(-1)" class="bg-red-700 hover:bg-red-600 py-2 text-white"
                    :class="{ 'rounded rounded-lg px-10': !isMobile }">
                    <font-awesome-icon icon="fa-solid fa-xmark" class="mr-1 text-sm lg:text-lg" />
                    Cancel</button>
                <button @click="sendModal" class="bg-green-700 hover:bg-green-600 py-2 text-white"
                    :class="{ 'rounded rounded-lg px-10 ml-8': !isMobile, 'grayscale': !showButton, 'grasale-0': showButton }"
                    ref="saveData">
                    <font-awesome-icon icon="fa-regular fa-floppy-disk" class="mr-1 text-sm lg:text-lg" />
                    {{ type === 'edit' ? 'Update' : 'Save' }}</button>
            </div>
            <!-- </div> -->
        </div>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <supplierRegister :show-modal="open_supplier" :userName="formValues.supplier" @close-modal="closeSupplier"
            :companyId="companyId">
        </supplierRegister>
        <warehouse :show-modal="open_warehouse" :userName="formValues.warehouse" @close-modal="closeWarehouse"
            :companyId="companyId">
        </warehouse>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <!-- <addName :show-modal="showModalProduct" :title="'Add Product Name'" :userName="newProduct"
            @close-modal="closeModal"></addName> -->
        <addNewItem :show-modal="showModalProduct" :product_name="newProduct" @close-modal="closeModal"></addNewItem>
        <posAddSerialNumberItem :showModal="openSerial" @close-modal="closeSerialModel" :typeOfInvoice="'purchase'"
            :item_data="selected_serial_item">
        </posAddSerialNumberItem>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <!---overall discount-->
        <posAddDiscount :showModal="open_overall_discount" @close-modal="closeOverallDiscountDialog"
            :over_all_discount="JSON.parse(JSON.stringify(over_all_discount))" :grandTotal="formValues.total"
            :currentCompanyList="currentCompanyList">
        </posAddDiscount>
        <!---change each item tax and discount-->
        <posAddTaxDiscount :showModal="open_item_tax_discount" @close-modal="closeItemTaxDiscountDialog"
            :itemData="selected_item_data" :currentCompanyList="currentCompanyList" :invoice_setting="invoice_setting"
            :type="type_tax_or_discount">
        </posAddTaxDiscount>
        <!---shipping charges-->
        <shippingchargesPos :showModal="open_shippingcharges" @close-modal="closeShippingDialog"
            :itemData="shipping_details">
        </shippingchargesPos>
        <!-- in mobile view-->
        <addPurchaseItems :show-modal="addSalesItem" @close-modal="closesalesItems" :formData="{}" :isMobile="isMobile"
            :isAndroid="isAndroid" @getProduct="getProductDetails" :invoice_setting="invoice_setting"
            :currentCompanyList="currentCompanyList" :sales_items="formValues.items"
            :editData="editProductIndex >= 0 && editProductIndex !== null ? formValues.items[editProductIndex] : undefined"
            :editIndex="editProductIndex" :over_all_discount="isNaN(over_all_discount) ? 0 : over_all_discount"
            :shipping_details="shipping_details" :companyId01="companyId">
        </addPurchaseItems>
    </div>
</template>

<script>
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import supplierRegister from '../../dialog_box/supplierRegister.vue';
import warehouse from '../../dialog_box/warehouse.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
// import addName from '../../dialog_box/addName.vue';
import addNewItem from '../../dialog_box/addNewItem.vue';
import { mapGetters, mapActions } from 'vuex';
import posAddSerialNumberItem from '../../dialog_box/posAddSerialNumberItem.vue';
import posAddTaxDiscount from '../../dialog_box/posAddTaxDiscount.vue';
import posAddDiscount from '../../dialog_box/posAddDiscount.vue';
import shippingchargesPos from '../../dialog_box/shippingchargesPos.vue';
import addPurchaseItems from '../../dialog_box/addPurchaseItems.vue';
export default {
    name: 'purchase',
    emits: ['updateIsOpen', 'is-sales-save', 'updatesalesData'],
    components: {
        dialogAlert,
        supplierRegister,
        warehouse,
        confirmbox,
        addNewItem,
        posAddSerialNumberItem,
        posAddTaxDiscount,
        posAddDiscount,
        shippingchargesPos,
        addPurchaseItems
    },
    props: {
        showModal: Boolean,
        editData: Object,
        type: String,
        userName: String,
        companyId: String,
        createdBy: String,
        store_refresh: Boolean,
        updateModalOpen: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            isMobile: false,
            isAndroid: false,
            formValues: { items: [] },
            paid: { amount: 0, type: '', balance: 0, return: 0 },
            due: { count: 0, type: 'Day' },
            isMessageDialogVisible: false,
            message: null,
            isOpen: false,
            updatedData: null,
            //---new---
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            barcode_icon: '/images/pos/barcode.png',
            add_item_icon: '/images/pos/plus.png',
            open_warehouse: false,
            open_supplier: false,
            isDropdownsupplier: false,
            isDropdownwarehouse: false,
            supplierList: [],
            warehouseList: [],
            isDropdownOpenProduct: false,
            filteredProductList: [],
            productList: [],
            deleteIndex: null,
            open_confirmBox: false,
            filteredSupplierList: [],
            supplierID: null,
            warehouseID: null,
            filteredWarehouseList: [],
            showButton: true,
            showModalProduct: false,
            newProduct: '',
            selectedIndex: 0,
            mouseDownOnDropdown: false,
            mark_as_full_pay: false,
            //---add new item---
            item_index: null,
            //---api integration--
            pagination: {},
            invoice_setting: null,
            state_list: [],
            state_code_list: [
                { name: 'JAMMU AND KASHMIR', code: 1 },
                { name: 'HIMACHAL PRADESH', code: 2 },
                { name: 'PUNJAB', code: 3 },
                { name: 'CHANDIGARH', code: 4 },
                { name: 'UTTARAKHAND', code: 5 },
                { name: 'HARYANA', code: 6 },
                { name: 'DELHI', code: 7 },
                { name: 'RAJASTHAN', code: 8 },
                { name: 'UTTAR PRADESH', code: 9 },
                { name: 'BIHAR', code: 10 },
                { name: 'SIKKIM', code: 11 },
                { name: 'ARUNACHAL PRADESH', code: 12 },
                { name: 'NAGALAND', code: 13 },
                { name: 'MANIPUR', code: 14 },
                { name: 'MIZORAM', code: 15 },
                { name: 'TRIPURA', code: 16 },
                { name: 'MEGHALAYA', code: 17 },
                { name: 'ASSAM', code: 18 },
                { name: 'WEST BENGAL', code: 19 },
                { name: 'JHARKHAND', code: 20 },
                { name: 'ODISHA', code: 21 },
                { name: 'CHATTISGARH', code: 22 },
                { name: 'MADHYA PRADESH', code: 23 },
                { name: 'GUJARAT', code: 24 },
                { name: 'DADRA AND NAGAR HAVELI AND DAMAN AND DIU (NEWLY MERGED UT)', code: 26 },
                { name: 'MAHARASHTRA', code: 27 },
                { name: 'ANDHRA PRADESH(BEFORE DIVISION)', code: 28 },
                { name: 'KARNATAKA', code: 29 },
                { name: 'GOA', code: 30 },
                { name: 'LAKSHADWEEP', code: 31 },
                { name: 'KERALA', code: 32 },
                { name: 'TAMIL NADU', code: 33 },
                { name: 'PUDUCHERRY', code: 34 },
                { name: 'ANDAMAN AND NICOBAR ISLANDS', code: 35 },
                { name: 'TELANGANA', code: 36 },
                { name: 'ANDHRA PRADESH (NEWLY ADDED)', code: 37 },
                { name: 'LADAKH (NEWLY ADDED)', code: 38 },
                { name: 'OTHER TERRITORY', code: 97 },
                { name: 'CENTRE JURISDICTION', code: 99 }
            ],
            open_loader: false,
            tooltip: {},
            isFormFocus: {},
            selected_item: '',
            //----is any updated---
            is_updated: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---add serial number---
            openSerial: false,
            selected_serial_item: null,
            typeOfInvoice: 'purchase',
            //---mobile view--
            addSalesItem: false,
            editProductIndex: null,
            //--tax / discount / shipping--
            selected_item_data: null,
            open_item_tax_discount_index: null,
            open_overall_discount: false,
            open_item_tax_discount: false,
            open_shippingcharges: false,
            over_all_discount: null,
            shipping_details: {},
            get_all_data: {},
            //----tax or discount---
            type_tax_or_discount: '',
        }
    },
    computed: {
        ...mapGetters('supplier', ['currentSupplier']),
        ...mapGetters('warehouse', ['currentWarehouse', 'currentWarehousePagination']),
        ...mapGetters('items', ['currentItems', 'currentItemsPagination']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),
        //--total qty---
        totalQuantity() {
            if (this.formValues.items && this.formValues.items.length > 0) {
                let count_total = this.formValues.items.reduce((sum, item) => sum + item.total_qty, 0);
                if (this.get_all_data === null) {
                    this.get_all_data = { total_qty: count_total };
                } else {
                    this.get_all_data.total_qty = count_total;
                }
                return count_total;
            } else {
                return 0;
            }
        },
        //---total amount--
        totalAmount() {
            if (this.formValues.items && this.formValues.items.length > 0) {
                let sub_total = this.formValues.items.reduce((sum, item) => (1 * sum) + (1 * item.total_price), 0);
                if (this.get_all_data === null) {
                    this.get_all_data = { sub_total: sub_total };
                } else {
                    this.get_all_data.sub_total = sub_total;
                }
                return sub_total;
            } else {
                return 0;
            }
        },
        //---total discount--
        totalDiscount() {
            if (this.formValues.items && this.formValues.items.length > 0) {
                let total = this.formValues.items.reduce((sum, item) => sum + (Number(item.discount) || 0), 0);
                return total.toFixed(2);
            }
            return "0.00";
        },
        //--grandTotal--
        grandTotal() {
            if (this.formValues.items && this.formValues.items.length > 0) {
                let totalDiscount = this.totalDiscount;
                let overallDiscount = this.over_all_discount !== null ? this.overAllDiscount : 0;
                let totalAmount = this.formValues.items.reduce((sum, item) => sum + parseFloat(item.total_price), 0);
                let shipping_charge = this.shipping_details.shipping && this.shipping_details.shipping > 0 ? this.shipping_details.shipping : 0;
                let total_tax = this.overAllTax.toFixed(2);
                let grandTotal = (totalAmount + shipping_charge) - (1 * overallDiscount);
                if (this.get_all_data === null) {
                    this.get_all_data = { discount_total: (overallDiscount).toFixed(2), shipping: shipping_charge, grand_total: Math.round(grandTotal), total_tax: total_tax };
                } else {
                    this.get_all_data.discount_total = (1 * overallDiscount).toFixed(2);
                    this.get_all_data.shipping = shipping_charge;
                    this.get_all_data.grand_total = Math.round(grandTotal);
                    this.get_all_data.total_tax = total_tax;
                }
                return Math.round(grandTotal);
            } else {
                return 0
            }
        },
        //---over all discount---
        overAllDiscount() {
            if (this.formValues.items && this.formValues.items.length > 0) {
                if (this.over_all_discount !== null) {
                    return this.over_all_discount.type === 'Percentage' ? (this.totalAmount * (this.over_all_discount.value / 100)).toFixed(2) : this.over_all_discount.value;
                } else {
                    return 0;
                }
            } else {
                return 0;
            }
        },
        //--total tax---
        overAllTax() {
            if (this.formValues.items && this.formValues.items.length > 0) {
                let calculate_tax = this.formValues.items.reduce((sum, item) => (1 * sum) + (1 * item.tax), 0);
                return calculate_tax;
            } else {
                return 0;
            }
        },
    },
    methods: {
        ...mapActions('items', ['fetchItemList']),
        ...mapActions('supplier', ['fetchISupplierList']),
        ...mapActions('warehouse', ['fetchWarehouseList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        //---product name model---
        openModal(name, index) {
            this.isDropdownOpenProduct = false;
            this.newProduct = this.selected_item;
            // this.item_index = index;
            this.showModalProduct = true;
            // this.newProduct = this.formValues.item[index].product_name; 
            // console.log(this.newProduct, 'Waht happening..!');
        },
        closeModal(selectedProduct) {
            // console.log(selectedProduct, 'WWWWWWWWW');
            if (selectedProduct && selectedProduct.products) {
                let get_data = {
                    product_name: selectedProduct.products.product_name,
                    product_id: selectedProduct.products.id,
                    barcode: selectedProduct.barcodes.barcode,
                    barcode_id: selectedProduct.barcodes.id,
                    total_qty: 1,
                    price_per_qty: selectedProduct.purchase_price,
                    taxvalue: selectedProduct.gst_value,
                    tax_name: selectedProduct.tax_name ? selectedProduct.tax_name : 'GST',
                    tax_type: selectedProduct.gst_type,
                    tax: selectedProduct.sales_price * (selectedProduct.gst_value / 100),
                    discount_data: { type: selectedProduct.discount_type, value: selectedProduct.discount },
                    discount: selectedProduct.discount_type === 'Percentage' ? selectedProduct.sales_price * (selectedProduct.discount / 100) : selectedProduct.discount,

                    editing: true,
                };
                if (this.formValues.items && this.formValues.items.length > 0) {
                    this.formValues.items.push(get_data);
                }
                else {
                    this.formValues.items = [{ ...get_data }];
                }
                // console.log(this.formValues.items[this.item_index], 'EEEEEEE');
                // this.formValues.items[this.item_index].product_name = data.products.product_name;
                // this.formValues.items[this.item_index].product_id = data.products.id;
                // this.formValues.items[this.item_index].barcode = data.barcodes.barcode;
                // this.formValues.items[this.item_index].barcode_id = data.barcodes.id;
                // this.formValues.items[this.item_index].total_qty = 1;
                // this.formValues.items[this.item_index].price_per_qty = data.purchase_price;
                this.selected_item = '';
                this.handleProductChangetotal(selectedProduct);
                this.item_index = null;
            }
            this.showModalProduct = false;
            this.isDropdownOpenProduct = false;
        },

        sendModal() {
            this.hideButton();
            let copyofformvalues = JSON.parse(JSON.stringify(this.formValues));
            if (copyofformvalues.purchase_order && copyofformvalues.purchase_order_date && copyofformvalues.supplier_id && copyofformvalues.items && copyofformvalues.total && copyofformvalues.paid >= 0 && copyofformvalues.warehouse_id && !this.showButton) {
                // this.open_loader = true;
                copyofformvalues.items = copyofformvalues.items.filter(opt => opt.product_id !== '' && opt.product_name !== '' && opt.barcode_id !== '' && opt.total_qty !== '' && opt.price_per_qty !== '');
                if (typeof copyofformvalues.balance_amount === 'string') {
                    copyofformvalues.balance_amount = 1 * copyofformvalues.balance_amount;
                }
                if (typeof copyofformvalues.return_amount === 'string') {
                    copyofformvalues.return_amount = 1 * copyofformvalues.return_amount;
                }
                if (!copyofformvalues.return_amount) {
                    copyofformvalues.return_amount = 0;
                }

                let payments = { payment_date: this.getCurrentDate(), payment_amount: copyofformvalues.paid ? copyofformvalues.paid : 0, payment_type: 'purchase', payment_notes: '' };

                let payload = { ...copyofformvalues, company_id: this.companyId, created_by: this.createdBy };
                // Stringify each item's serial_no in payload.items
                payload.items.forEach(opt => {
                    if (opt.serial_no) {
                        opt.serial_no = JSON.stringify(opt.serial_no);
                    }
                    if (opt.discount_data) {
                        opt.discount_data = JSON.stringify(opt.discount_data);
                    }
                    if (opt.taxvalue !== undefined) {
                        opt.tax_value = opt.taxvalue;
                    }
                });
                if (this.over_all_discount) {
                    payload.discount = this.over_all_discount.value;
                    payload.discount_type = this.over_all_discount.type;
                }
                if (this.shipping_details && Object.keys(this.shipping_details).length > 0) {
                    payload.shipping = this.shipping_details.shipping;
                    payload.shipping_type = this.shipping_details.shipping_type;
                    payload.cod = this.shipping_details.cod;
                }

                if (this.type === 'edit') {
                    // Handle existing payments
                    if (copyofformvalues.paid && copyofformvalues.paid > 0 && this.editData.purchase_payments && this.editData.purchase_payments.length > 0) {
                        this.editData.purchase_payments.forEach((opt, index) => {
                            if (opt.payment_type === 'purchase' && opt.payment_amount !== copyofformvalues.paid) {
                                opt.payment_amount = copyofformvalues.paid;
                                opt.payment_date = this.getCurrentDate();  // Ensure the payment date is set correctly
                            }
                        });
                        payload.payments = this.editData.purchase_payments;
                    }
                    // Handle new payment when no existing payments
                    else if (copyofformvalues.paid > 0 && this.editData && this.editData.purchase_payments && this.editData.purchase_payments.length === 0) {
                        let newPayment = {
                            payment_type: 'purchase',
                            payment_amount: copyofformvalues.paid,
                            payment_date: this.getCurrentDate(),
                            // Add other necessary payment details if required
                        };
                        payload.balance_amount = copyofformvalues.balance_amount;
                        payload.payments = [newPayment];
                    }
                    // Handle case where existing payments are present but no new payment amount provided
                    else if (this.editData && this.editData.purchase_payments && this.editData.purchase_payments.length > 0) {
                        payload.payments = this.editData.purchase_payments;
                    } else {
                        payload.payments = [];
                    }
                    payload.items.forEach(opt => JSON.stringify(opt.serial_no));
                    // Send the update request
                    axios.put(`/purchase_orders/${this.editData.id}`, payload)
                        .then(response => {
                            this.updatedData = response.data;
                            this.open_loader = false;
                            this.formValues = {};
                            // this.openMessageDialog(response.data.message);
                            this.updateKeyWithTime('purchase_update');
                            this.message = response.data.message;
                            this.show = true;
                            setTimeout(() => {
                                this.$router.go(-1);
                            }, 300);
                            this.$emit('is-sales-save', false);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                        });
                } else {

                    if (copyofformvalues.paid && copyofformvalues.paid > 0) {
                        payload.payments = [payments];
                    } else {
                        payload.payments = [];
                    }
                    axios.post('/purchase_orders', payload)
                        .then(response => {
                            // console.log(response.data);
                            this.updatedData = response.data;
                            this.open_loader = false;
                            this.formValues = {};
                            // this.openMessageDialog(response.data.message);
                            this.updateKeyWithTime('purchase_update');
                            this.message = response.data.message;
                            this.show = true;
                            // setTimeout(() => {
                            this.$router.go(-1);
                            // }, 300);
                            this.$emit('is-sales-save', false);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                        })
                }

                // this.formValues = {};
            } else {
                this.openMessageDialog(!this.formValues.purchase_order ? 'Enter the purchase invoice number' : !this.formValues.purchase_order_date ? 'Please fill the purchase date' : !this.formValues.supplier_id ? 'Please select the supplier' : !this.formValues.items ? 'Please select the purchase items' : !this.formValues.total ? 'Please select the items' : this.formValues.paid === 0 ? 'Please enter the paid amount' : !this.formValues.warehouse_id ? 'Please select the warehouse' : 'Please fill all fields..!');
                this.$emit('updatesalesData');
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
            this.isAndroid = window.innerWidth < 768;
        },
        //--serial no validation--
        parseNestedJson(value) {
            try {
                let parsedValue = value;
                while (typeof parsedValue === 'string' && this.isJsonString(parsedValue)) {
                    parsedValue = JSON.parse(parsedValue);
                }
                return parsedValue;
            } catch (e) {
                return value; // Return the original value if parsing fails
            }
        },
        isJsonString(str) {
            try {
                JSON.parse(str);
                return true;
            } catch (e) {
                return false;
            }
        },
        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                let { purchase_order, purchase_order_date, supplier_id, purchase_items, total, paid, warehouse_id, due_interval, sos, payment_type, shipping, shipping_type, cod, discount, discount_type } = this.editData;
                // console.log(paid, 'RRRRRRRRRRRR');
                if (Array.isArray(purchase_items) && purchase_items.length > 0) {
                    purchase_items = purchase_items.map(opt => ({
                        ...opt,
                        serial_no: this.parseNestedJson(opt.serial_no),
                        discount_data: this.parseNestedJson(opt.discount_data),
                        taxvalue: opt.tax_value
                    }));
                }
                this.formValues = { purchase_order: purchase_order, purchase_order_date: this.getDateData(purchase_order_date), supplier_id: supplier_id, items: purchase_items, total: total, paid: paid, warehouse_id: warehouse_id, due_interval: due_interval, sos: sos, payment_type: payment_type };
                this.over_all_discount = { value: discount ? discount : 0, type: discount_type ? discount_type : 'Fixed' };
                this.shipping_details.shipping = shipping ? shipping : 0;
                this.shipping_details.shipping_type = shipping_type ? shipping_type : '';
                this.shipping_details.cod = cod ? cod : '';

                if (this.formValues.supplier_id && this.supplierList.length > 0) {
                    // console.log('Supplier..................');
                    let find_name = this.supplierList.find(opt => opt.id === this.formValues.supplier_id);
                    if (find_name) {
                        // console.log(find_name, 'WWWWWWW');
                        this.formValues.supplier = find_name.name;
                    } else {
                        // this.getSupplier(1, 'all');
                        // setTimeout(() => {
                        //     this.initializeData();
                        // }, 500)
                    }
                } else {
                    // this.getSupplier(1, 'all');
                    // setTimeout(() => {
                    //     this.initializeData();
                    // }, 500)

                }
                // console.log(this.warehouseList.length, 'p', this.supplierList.length, 'RRRRRRRRRRRRRRR');
                if (this.formValues.warehouse_id && this.warehouseList.length > 0) {
                    // console.log(this.formValues.warehouse_id, 'ggggggggggggggggggggg', this.warehouseList.length > 0);
                    let find_name = this.warehouseList.find(opt => opt.id === this.formValues.warehouse_id);
                    if (find_name) {
                        this.formValues.warehouse = find_name.name;
                    }
                    else {
                        // this.getWarehouses(this.pagination.warehouse.current_page + 1, 50);
                        // setTimeout(() => {
                        //     this.initializeData();
                        // }, 500)
                    }
                } else {
                    // this.getWarehouses(1, 50);
                    // setTimeout(() => {
                    //     this.initializeData();
                    // }, 500)
                }
                if (this.editData.due_interval) {

                }
            } else {
                this.formValues = {};
            }
        },
        //---Add new user--
        userAddNew() {
            // console.log(this.userName);
            if (/^\d+$/.test(this.userName)) {
                this.formValues.contactNumber = this.userName;
            } else {
                if (this.userName) {
                    const strSplit = this.userName.split(' ');
                    // console.log(strSplit, 'PPPPPPPP');
                    this.formValues.firstName = strSplit[0];
                    if (strSplit[1]) {
                        this.formValues.lastName = strSplit[1];
                    }
                }

            }
        },
        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },
        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            // this.$router.go(-1);
        },
        //------new----
        closeSupplier(data) {
            if (data) {
                this.formValues.supplier = data.name;
                this.formValues.supplier_id = data.id;
                // this.supplierID = data.id;
                this.supplierList.push(data);
            }
            this.open_supplier = false;
            this.isDropdownsupplier = false;
        },
        closeWarehouse(data) {
            if (data) {
                this.formValues.warehouse = data.name;
                this.formValues.warehouse_id = data.id;
                // this.warehouseID = data.id;
                this.warehouseList.push(data);
            }
            this.open_warehouse = false;
            this.isDropdownwarehouse = false;
        },
        addNewWarehouse(data) {
            this.open_warehouse = true;
            this.isDropdownwarehouse = false;
        },
        //----table--
        handleEnterKey(index, type, product_name) {
            // Check if filteredProductList has at least one item
            if (type === 'product') {
                if (this.filteredProductList.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectedProductData(index, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    this.openModal();
                }
            }
            if (type === 'supplier') {
                if (this.filteredSupplierList.length > 0) {
                    this.selectedSupplierData(this.filteredSupplierList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.addRow.focus();
                } else {
                    this.addNewSupplier();
                }
            }
            if (type === 'warehouse') {
                if (this.filteredWarehouseList.length > 0) {
                    this.selectedWarehouseData(this.filteredWarehouseList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.saveData.focus();
                } else {
                    this.addNewWarehouse();
                }
            }
        },
        //---add new product
        addNewProductsList() {
            if (!this.formValues.items) {
                this.formValues.items = [];
            }
            this.formValues.items.push({
                product_name: '',
                barcode: '',
                total_qty: 0,
                price_per_qty: 0,
                // tax: 0,
                editing: true,
            });
            // Set focus on the product name input field of the last row
            this.$nextTick(() => {
                const lastRowIndex = this.formValues.items.length - 1;
                // console.log(this.$refs, 'What happeninf....');
                const productNameInput = this.$refs['productNameInputs' + lastRowIndex][0];
                if (productNameInput) {
                    productNameInput.focus();
                    this.isDropdownOpenProduct = lastRowIndex;
                } else {
                    console.error("productNameInput is undefined.");
                }
            });
        },
        //---item product name--
        handleProductChange() {
            this.isDropdownOpenProduct = true;
            const enteredProductName = this.selected_item.toLowerCase();
            // console.log(enteredProductName, 'Product name');
            if (this.productList && this.productList.length !== 0 && enteredProductName.length > 1) {
                // console.log('validation is done...1');
                let existingProducts = [];
                let existingProductCodes = [];
                if (this.formValues.items && this.formValues.items.length > 0) {
                    existingProducts = !this.formValues.items.map((item, i) => item.product_name.toLowerCase());
                    existingProductCodes = !this.formValues.items.map((item, i) => item.barcode && typeof item.barcode === 'string' ? item.barcode && item.barcode.toLowerCase() : null).filter(code => code !== null);
                }
                this.filteredProductList = this.productList.filter(opt => {
                    if (opt.products.product_type === 'Product') {
                        let isExistingName = false;
                        let isExistingCode = false;
                        if (existingProducts.length > 0 && opt.products && opt.products.product_name) {
                            isExistingName = existingProducts.includes(opt.products.product_name.toLowerCase());
                        }
                        if (existingProductCodes.length > 0 && opt.barcodes && opt.barcodes.barcode) {
                            isExistingCode = existingProductCodes.includes(opt.barcodes.barcode.toLowerCase());
                        }
                        // Check if the entered product name or product code matches any existing product name or product code
                        const nameMatch = opt.products && opt.products.product_name ? opt.products.product_name.toLowerCase().includes(enteredProductName) : false;
                        const codeMatch = opt.barcodes && opt.barcodes.barcode ? opt.barcodes.barcode.toLowerCase().includes(enteredProductName) : false;
                        // return nameMatch || codeMatch;
                        return (!isExistingName || !isExistingCode) && (nameMatch || codeMatch);
                    }
                });
                if (this.filteredProductList.length === 0 && this.pagination.items && Number(this.pagination.items.current_page) !== this.pagination.items.last_page && Number(this.pagination.items.current_page) <= this.pagination.items.last_page) {
                    this.getProductDetails(Number(this.pagination.items.current_page) + 1, 1000);
                }

            }
        },
        //---add product option controll--
        findExistItem() {
            const enteredProductName = this.selected_item.toLowerCase();
            if (this.productList && this.productList.length !== 0 && enteredProductName.length > 1) {
                const existingProducts = this.productList.map(item => item.products.product_name.toLowerCase());
                const existingProductCodes = this.productList.map(item => item.barcodes && item.barcodes.barcode.toLowerCase());

                // Use a separate array to store existing product names and codes
                const existingNamesAndCodes = [...existingProducts, ...existingProductCodes];

                // Check if the entered product name or product code matches any existing product name or product code
                const isExisting = existingNamesAndCodes.includes(enteredProductName);
                // console.log(isExisting, 'Waht happening....!');
                return !isExisting;
            }
            return true;
        },

        //---selected product 
        selectedProductData(index, selectedProduct) {
            let selectIndex = null;
            //--validate duplicate--
            let validateDuplicate = [];
            if (this.formValues.items && this.formValues.items.length > 0) {
                validateDuplicate = this.formValues.items.map((item, i) => {
                    if (item.product_name === selectedProduct.products.product_name && item.product_id === selectedProduct.product_id && item.barcode_id === selectedProduct.barcode_id) {
                        selectIndex = i;
                        return true;
                    }
                });
            }
            // console.log(selectIndex !== null && selectIndex >= 0, 'EEEEEEEEEEEE');
            if (selectIndex !== null && selectIndex >= 0) {
                this.formValues.items[selectIndex] = { ...this.formValues.items[selectIndex], total_qty: this.formValues.items[selectIndex].total_qty + 1 };
                this.selected_item = '';
                this.handleProductChangetotal(this.formValues.items[selectIndex]);
                //---reset---
                validateDuplicate = [];
                selectIndex = null;
            } else {
                // console.log(selectedProduct, 'What happening...!');
                let get_data = {
                    product_name: selectedProduct.products.product_name,
                    product_id: selectedProduct.products.id,
                    barcode: selectedProduct.barcodes.barcode,
                    barcode_id: selectedProduct.barcodes.id,
                    total_qty: 1,
                    price_per_qty: selectedProduct.purchase_price,
                    taxvalue: selectedProduct.gst_value,
                    tax_name: selectedProduct.tax_name ? selectedProduct.tax_name : 'GST',
                    tax_type: selectedProduct.gst_type,
                    tax: 0,
                    discount_data: { type: selectedProduct.discount_type, value: 0 },
                    discount: 0,
                    editing: true,
                    // product: { product_type: selectedProduct.products.product_type },
                };
                if (this.formValues.items && this.formValues.items.length > 0) {
                    this.formValues.items.push(get_data);
                }
                else {
                    this.formValues.items = [{ ...get_data }];
                }
                this.handleProductChangetotal(get_data);
                this.selected_item = '';
            }
            this.isDropdownOpenProduct = false;
            this.filteredProductList = [];

            this.focusItemFields();
            if (this.pagination && this.pagination.items && Number(this.pagination.items.current_page) !== 1) {
                this.getProductDetails(Number(this.pagination.items.current_page) + 1, 1000);
            }
            this.is_updated = true;
            // item.product_name = option.products.product_name;
            // item.product_id = option.products.id;
            // item.barcode = option.barcodes.barcode;
            // item.barcode_id = option.barcodes.id;
            // item.total_qty = 1;
            // item.price_per_qty = option.purchase_price;
            // this.isDropdownOpenProduct = false;
            // this.handleProductChangetotal(item);
        },
        //--always focus item fields--
        focusItemFields() {
            this.$nextTick(() => {
                if (this.$refs.selectItemField) {
                    this.$refs.selectItemField.focus();
                }
            });
        },
        //---blur item dropdown--
        blurItemDropdown() {
            this.isFormFocus.selected_item = false;
            if (!this.isMouseInOption) {
                this.productDropdown = false;
            }
        },
        //edit row--
        startEdit(item) {
            item.editing = true;
        },
        //--save row data
        saveRecord(item) {
            // console.log(item, 'RRRR');
            // item.editing = false;
            this.filteredProductList = [];
            this.handleProductChangetotal(item);
        },
        //--cancel edit status--
        cancelEdit(item, index) {
            if (item.hsn_code === '' && item.product_name === '' && item.total_qty === 0 && item.price_per_qty === 0 && item.price_per_qty === 0 && item.tax === 0) {
                this.formValues.items.splice(index, 1);
            } else {
                item.editing = false;
            }
        },
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                // console.log(this.deleteIndex, 'Whatjjjjjj');
                const indexToDelete = this.formValues.items.findIndex((record, i) => i === this.deleteIndex);
                // console.log(indexToDelete, 'Waht happening the data...!');
                if (indexToDelete !== -1) {
                    this.formValues.items.splice(indexToDelete, 1);
                } else {
                    // console.log('id is not matched in the data...!');
                    this.open_message = true;
                    this.message = 'Record is not matched!';
                }
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        //--move up & down functions---
        moveUp(index) {
            if (index > 0) {
                let temp = this.formValues.items[index - 1];
                this.formValues.items.splice(index - 1, 1, this.items[index]);
                this.formValues.items.splice(index, 1, temp);
            }
        },
        moveDown(index) {
            if (index < this.items.length - 1) {
                let temp = this.formValues.items[index + 1];
                this.formValues.items.splice(index + 1, 1, this.items[index]);
                this.formValues.items.splice(index, 1, temp);
            }
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
            this.is_updated = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---supplier---
        handleSupplierChange(enteredValue) {
            if (enteredValue) {
                const enteredText = enteredValue.trim().toLowerCase();
                this.filteredSupplierList = this.supplierList.filter(opt => {
                    const nameMatch = opt.name.toLowerCase().includes(enteredText);
                    const contactMatch = opt.contact_number.includes(enteredText);
                    return nameMatch || contactMatch || opt.name.toLowerCase() === enteredText;
                });
                // console.log(this.filteredSupplierList, 'Waht happening.......@@@');
                // if (this.filteredSupplierList.length === 0 && this.pagination.supplier && Number(this.pagination.supplier.current_page) === this.pagination.supplier.last_page) {
                //     this.getSupplier(this.pagination.supplier.current_page + 1, 'all');
                // }
            } else {
                if (this.supplierList.length > 0) {
                    this.filteredSupplierList = this.supplierList;
                }
                // else {
                //     this.addNewSupplier();
                // }
            }
        },
        selectedSupplierData(option) {
            // console.log(option, 'What happening.....Q');
            this.formValues.supplier = option.name;
            // this.supplierID = option.id;
            this.formValues.supplier_id = option.id;
            this.isDropdownsupplier = false;
            this.is_updated = true;
        },
        addNewSupplier() {
            this.open_supplier = true;
            this.isDropdownsupplier = false;
        },
        //---warehouse
        handleWarehouseChange() {
            // console.log(this.formValues.warehouse, 'ware house..!');
            if (this.formValues.warehouse) {
                const enteredText = this.formValues.warehouse.trim().toLowerCase();
                this.filteredWarehouseList = this.warehouseList.filter(opt => {
                    const nameMatch = opt.name.toLowerCase().includes(enteredText);
                    // console.log(opt.name.toLowerCase() === enteredText, 'Whattttt');
                    return nameMatch;
                });
                // console.log(this.filteredWarehouseList, 'Waht happening.......@@@');
                if (this.filteredWarehouseList.length === 0 && this.pagination.warehouse && Number(this.pagination.warehouse.current_page) < this.pagination.warehouse.last_page) {
                    this.getWarehouses(this.pagination.warehouse.current_page + 1, 100);
                }
            } else {
                // console.log(this.warehouseList.length);
                if (this.warehouseList.length > 0) {
                    this.filteredWarehouseList = this.warehouseList;
                    // console.log(this.filteredWarehouseList);
                }
                // else {
                //     this.addNewWarehouse();
                // }
            }
        },
        selectedWarehouseData(option) {
            if (option) {
                this.formValues.warehouse = option.name;
                // this.warehouseID = option.id;
                this.formValues.warehouse_id = option.id;
                this.isDropdownwarehouse = false;
                this.is_updated = true;
            }
        },
        addNewWarehouse() {
            this.open_warehouse = true;
            this.isDropdownwarehouse = false;
        },
        //---submit button---
        hideButton() {
            this.showButton = false;
            setTimeout(() => {
                this.showButton = true;
            }, 10000); // 10 seconds in milliseconds
        },
        //---on close--
        closeModel(data) {
            if (data) {
                if (this.formValues.items) {

                } else {
                    this.formValues
                }
            }
        },
        calculateItemTotal(item) {
            // Calculate the total price for the item
            if (item.total_qty && item.price_per_qty) {
                if (item.tax_type === "Exclusive") {
                    // Calculate tax value    
                    item.discount = item.discount_data && item.discount_data.type === 'Percentage' ? ((item.total_qty * item.price_per_qty) * ((1 * item.discount_data.value) / 100)).toFixed(2) : item.discount_data ? item.discount_data.value : 0;
                    let make_price = (item.total_qty * item.price_per_qty) - (1 * item.discount);
                    let qty_price = 1 * make_price;
                    let tax_amount = qty_price * (1 + ((1 * item.taxvalue) / 100));
                    item.tax = (tax_amount - qty_price).toFixed(2);
                    //--get total
                    item.total_price = (tax_amount).toFixed(2);
                } else {
                    item.discount = item.discount_data && item.discount_data.type === 'Percentage' ? ((item.total_qty * item.price_per_qty) * ((1 * item.discount_data.value) / 100).toFixed(2)) : item.discount_data ? item.discount_data.value : 0;
                    let make_price = (item.total_qty * item.price_per_qty) - (1 * item.discount);
                    item.total_price = (make_price).toFixed(2);
                    // Calculate tax value
                    item.tax = ((make_price) - ((make_price) / (1 + (1 * item.taxvalue) / 100))).toFixed(2);
                }
                // const totalPrice = item.total_qty * item.price_per_qty;
                // return totalPrice.toFixed(2);
            }
            return 0; // Return 0 if either total_qty or price_per_qty is not set
        },

        async handleProductChangetotal(item) {
            // Your existing code to handle product change
            let index = this.formValues.items.findIndex((opt) => opt.product_id === item.product_id && opt.barcode_id === item.barcode_id);

            await this.calculateItemTotal(this.formValues.items[index]);

            // Calculate overall total
            await this.calculateOverallTotal();

        },
        //--calculat total--
        calculateOverallTotal() {
            if (this.formValues.items) { // Check if formValues.items is defined
                let total = 0;
                this.formValues.items.forEach(item => {
                    total += parseFloat(item.total_price || 0); // Add total price of each item
                });
                this.formValues.total = Math.round(total); // Round to 2 decimal places               
                // this.formValues.paid = total.toFixed(2);
                this.calculateBalance();
            }
        },
        //----up and down arrows functions

        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----control dropdown--
        //----customer dropdown--
        closeDropdown() {
            if (!this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownsupplier = false;
                this.isInputFocused.supplier = false;
            }
        },
        preventBlur() {
            this.mouseDownOnDropdown = true;
            setTimeout(() => {
                this.mouseDownOnDropdown = false;
            });
        },
        //---get list--
        getSupplier(page, per_page) {
            axios.get('/suppliers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'supplier....');
                    this.supplierList = response.data.data;
                    this.pagination.supplier = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---get list data
        getWarehouses(page, per_page) {
            axios.get('/warehouses', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'warehouse');
                    this.warehouseList = response.data.data;
                    this.pagination.warehouse = response.data.pagination;
                    if (this.type !== 'edit' && this.warehouseList.length > 0) {
                        this.selectedWarehouseData(this.warehouseList[0]);
                    }
                })
                .catch(error => {
                    console.error('Error get purchase', error);
                })
        },
        //--get product list ----
        getProductDetails(page, per_page) {
            axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // Filter the response data to include only items where product_type is 'Product'
                    const filteredProducts = response.data.data.filter(product => product.products.product_type === 'Product');

                    // Assign the filtered products to productList
                    // this.productList = filteredProducts;
                    // this.pagination.items = response.data.pagination;
                    this.fetchItemList(this.pagination.items && Object.keys(this.pagination.items).length > 0 ? (Number(this.pagination.items.total) + 50) : 1000)
                })
                .catch(error => {
                    console.error('Error response', error);
                })
        },
        getDateData(dateData) {
            if (dateData) {
                const currentDateObj = new Date(dateData);
                const year = currentDateObj.getFullYear();
                const month = String(currentDateObj.getMonth() + 1).padStart(2, '0');
                const day = String(currentDateObj.getDate()).padStart(2, '0');
                let current_date_data = `${year}-${month}-${day}`;
                return current_date_data;
            } else {
                const currentDateObj = new Date();

                const year = currentDateObj.getFullYear();
                const month = String(currentDateObj.getMonth() + 1).padStart(2, '0');
                const day = String(currentDateObj.getDate()).padStart(2, '0');
                const hours = String(currentDateObj.getHours()).padStart(2, '0');
                const minutes = String(currentDateObj.getMinutes()).padStart(2, '0');
                const seconds = String(currentDateObj.getSeconds()).padStart(2, '0');

                // Format the date and time as YYYY-MM-DD HH:MM:SS
                let current_date_data = `${year}-${month}-${day}`;
                return current_date_data;
            }

        },

        handleFocus() {
            this.handleWarehouseChange();
            this.isDropdownwarehouse = true;

        },
        handleClickOutside(event) {
            // Check if the clicked element is not within the supplier input wrapper
            try {
                if (this.isDropdownsupplier && !this.$refs.supplierInputWrapper.contains(event.target)) {
                    this.isDropdownsupplier = false;
                }
            } catch {
                this.isDropdownsupplier = false;
            }
            try {
                if (this.isDropdownOpenProduct >= 0 && this.$refs['productInputWrappers' + this.isDropdownOpenProduct] && !this.$refs['productInputWrappers' + this.isDropdownOpenProduct][0].contains(event.target)) {
                    this.isDropdownOpenProduct = false;
                }
            } catch {
                this.isDropdownOpenProduct = false;
            }
            try {
                // Check if the clicked element is not within the warehouse input wrapper
                if (this.isDropdownwarehouse && !this.$refs.warehouseInputWrapper.contains(event.target)) {
                    this.isDropdownwarehouse = false;
                }
            } catch {
                this.isDropdownwarehouse = false;
            }
        },
        getInvoiceSetting() {
            axios.get('/invoice_settings', { params: { company_id: this.companyId } })
                .then(response => {
                    if (response.data.data.length > 0) {
                        this.invoice_setting = response.data.data[0];
                    } else {
                        this.invoice_setting = null;
                        this.$router.push({ path: '/setting', query: { page: 'Sales' } });
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //--get state list ----
        getStateList() {
            if (this.state_list.length === 0) {
                axios.post('https://www.nammav2app.in/api/state-list', { country_id: 101 })
                    .then(response => {
                        this.state_list = response.data;
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }
        },
        calculateBalance() {
            if (this.grandTotal) {
                if (this.grandTotal >= this.formValues.paid || this.grandTotal === this.formValues.paid) {
                    this.formValues.balance_amount = this.grandTotal - this.formValues.paid;
                    this.formValues.return_amount = 0;
                } else {
                    this.formValues.balance_amount = 0;
                    this.formValues.return_amount = this.formValues.paid - this.grandTotal;

                }
            } else {
                this.formValues.paid = 0;
                this.formValues.balance_amount = this.grandTotal;
            }
        },
        //---validate full pay---
        validateFullPay() {
            if (this.formValues.mark_full_pay && this.grandTotal > this.formValues.paid) {
                this.formValues.mark_full_pay = false;
            } else if (this.grandTotal < this.formValues.paid) {
                this.formValues.mark_full_pay = true;
            }
        },
        addQuantity(index) {
            this.formValues.items[index].total_qty = this.formValues.items[index].total_qty + 1;
            this.handleProductChangetotal(this.formValues.items[index]);
            this.is_updated = true;
        },
        removeQuantity(index) {
            if (this.formValues.items[index].total_qty > 1) {
                this.formValues.items[index].total_qty = this.formValues.items[index].total_qty - 1;
                this.handleProductChangetotal(this.formValues.items[index]);
                this.is_updated = true;
            }
        },
        //---get current data--
        getCurrentDate() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        //--close all modals---
        closeAllModals() {
            this.isMessageDialogVisible = false;
            this.open_supplier = false;
            this.open_warehouse = false;
            this.open_confirmBox = false;
            this.showModalProduct = false;
        },
        //---add serial number---
        openSerialNumber(item) {
            this.selected_serial_item = item;
            this.openSerial = true;
        },
        closeSerialModel(data) {
            // if (Array.isArray(data)) {
            // console.log(data, 'WWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWW happening.......');
            if (data) {
                let findIndexData = this.formValues.items.findIndex(opt => opt.product_id === this.selected_serial_item.product_id);
                if (findIndexData !== -1) {
                    if (this.formValues.items[findIndexData].hasOwnProperty('serial_no')) {
                        // If 'serial_no' property exists, update it with the new data
                        this.formValues.items[findIndexData].serial_no = data.serial_no;
                        this.formValues.items[findIndexData].notes = data.notes;
                    } else {
                        // If 'serial_no' property doesn't exist, add it and set its value to 'data'
                        this.formValues.items[findIndexData].serial_no = data.serial_no;
                        this.formValues.items[findIndexData].notes = data.notes;
                        // console.log(this.items[findIndexData], 'how to updated data..!');
                    }
                    this.is_updated = true;

                }
            }
            this.openSerial = false; // Close the modal
        },
        //---shipping, tax & discount---
        //---item tax and discount modal box---
        openItemTaxDiscountDialog(data, index, from) {
            this.type_tax_or_discount = from;
            this.selected_item_data = data;
            this.open_item_tax_discount = true;
            this.open_item_tax_discount_index = index;
        },
        closeItemTaxDiscountDialog(data) {
            // && data.discount_type && this.open_item_tax_discount_index !== null
            if (this.open_item_tax_discount_index >= 0 && this.open_item_tax_discount_index !== null) {
                if (data) {
                    // console.log(data, 'Item tax and discount updaed succesfully');
                    this.formValues.items[this.open_item_tax_discount_index].tax_type = data.tax_type ? data.tax_type : 'Inclusive';
                    this.formValues.items[this.open_item_tax_discount_index].taxvalue = data.tax_value >= 0 ? data.tax_value : 0;
                    this.formValues.items[this.open_item_tax_discount_index].tax_name = data.tax_name ? data.tax_name : '';
                    if (this.formValues.items[this.open_item_tax_discount_index].discount_data) {
                        this.formValues.items[this.open_item_tax_discount_index].discount_data.type = data.discount_type ? data.discount_type : 'Fixed';
                        this.formValues.items[this.open_item_tax_discount_index].discount_data.value = data.discount_value >= 0 ? data.discount_value : 0;
                    } else {
                        this.formValues.items[this.open_item_tax_discount_index].discount_data = { type: data.discount_type ? data.discount_type : 'Fixed', value: data.discount_value >= 0 ? data.discount_value : 0 };
                    }
                    this.handleProductChangetotal(this.formValues.items[this.open_item_tax_discount_index]);
                }
                this.open_item_tax_discount = false;
                // this.open_item_tax_discount_index = null;
                // this.focusItemFields();
                // this.is_updated = true;
            }
            this.open_item_tax_discount = false;
        },
        //---overall discount modal box---
        openOverallDiscountDialog() {
            this.open_overall_discount = true;
        },
        closeOverallDiscountDialog(data) {
            if (data && data.value >= 0) {
                this.over_all_discount = data;
            }
            this.open_overall_discount = false;
            this.focusItemFields();
            this.calculateBalance();
            this.is_updated = true;
        },
        //----shipping---
        openShippingDialog() {
            this.open_shippingcharges = true;
        },
        closeShippingDialog(data) {
            if (data && (data.shipping >= 0 || data.shipping_type || data.cod)) {
                // console.log(data, 'Shipping charges added successfully..!');
                this.shipping_details.shipping = data.shipping;
                this.shipping_details.shipping_type = data.shipping_type;
                this.shipping_details.cod = data.cod;
            }
            this.open_shippingcharges = false;
            this.focusItemFields();
            this.calculateBalance();

            this.is_updated = true;
        },
        //---edit product--
        editProduct(index) {
            if (index >= 0) {
                this.editProductIndex = index;
                this.addSalesItem = true;
            } else {
                this.addSalesItem = true;
            }
        },
        //--close sales items---
        closesalesItems(data) {
            if (data && data.length >= 0) {
                this.formValues.items = data;
            }
            this.addSalesItem = false;
            this.editProductIndex = null;
        },
    },
    mounted() {
        this.updateIsMobile();
        this.fetchCompanyList();
        window.addEventListener('resize', this.updateIsMobile);

        this.$nextTick(() => {
            if (this.$refs.purchasename) {
                this.$refs.purchasename.focus();
                this.$refs.purchasename.click();
            }
        })
        if (this.type !== 'edit') {
            this.formValues.purchase_order_date = this.getDateData();
            this.formValues.sos = '33 TAMIL NADU';
            this.formValues.due_interval = '0 Days';
            this.formValues.payment_type = 'Cash';
            this.formValues.paid = 0;
        }
        if (this.companyId) {
            // this.getSupplier(1, 'all');
            if (this.currentSupplier && this.currentSupplier.length > 0) {
                this.supplierList = this.currentSupplier;
                this.fetchISupplierList();
            } else {
                this.fetchISupplierList();
            }
            // this.getWarehouses(1, 100);
            if (this.currentWarehouse && this.currentWarehouse.length > 0) {
                this.warehouseList = this.currentWarehouse;
                this.pagination.warehouse = this.currentWarehousePagination;
                this.fetchWarehouseList();
            } else {
                this.fetchWarehouseList();
            }

            this.getProductDetails(1, 2);
            if (this.currentItems && this.currentItems.length > 0) {
                this.productList = this.currentItems;
                this.pagination.items = this.currentItemsPagination;
            }

            // this.getInvoiceSetting();
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                this.invoice_setting = this.currentInvoice;
                this.fetchInvoiceSetting();
            } else {
                this.fetchInvoiceSetting();
            }
        }
        // if (this.state_list.length === 0) {
        //     this.getStateList();
        // }
        document.addEventListener('click', this.handleClickOutside);
    },
    beforeDestroy() {
        // Remove event listener when the component is destroyed
        document.removeEventListener('click', this.handleClickOutside);
        window.removeEventListener('resize', this.updateIsMobile);

    },
    watch: {
        // Watch for changes in the editData prop and initialize data accordingly
        companyId: {
            deep: true,
            handler(newValue) {
                if (this.type !== 'edit') {
                    this.formValues.purchase_order_date = this.getDateData();
                }
                if (this.supplierList.length === 0 && this.companyId) {
                    // this.getSupplier(1, 'all');
                    this.fetchISupplierList();
                } else {
                    this.fetchISupplierList();
                }
                if (this.warehouseList.length === 0 && this.companyId) {
                    // this.getWarehouses(1, 100);
                    this.fetchWarehouseList();
                } else {
                    this.fetchWarehouseList();
                }
                if (this.productList.length === 0 && this.companyId) {
                    // this.getProductDetails(1, 1000);
                    this.fetchItemList(this.pagination.items && Object.keys(this.pagination.items).length > 0 ? (Number(this.pagination.items.total) + 50) : 1000);
                } else {
                    this.fetchItemList(this.pagination.items && Object.keys(this.pagination.items).length > 0 ? (Number(this.pagination.items.total) + 50) : 1000);
                }
                if (this.invoice_setting === null && this.companyId) {
                    // this.getInvoiceSetting();
                    this.fetchInvoiceSetting();
                } else {
                    this.fetchInvoiceSetting();
                }
            }
        },
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
        userName: {
            handler() {
                this.userAddNew();
            },
        },
        'formValues.items': {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.calculateOverallTotal(newValue);
                }
            },
        },
        'formValues.mark_full_pay': {
            deep: true,
            handler(newValue) {
                if (this.grandTotal) {
                    if (newValue) {
                        this.formValues.paid = this.grandTotal;
                    }
                    this.calculateBalance();
                }
            }
        },
        currentSupplier: {
            deep: true,
            handler(newValue) {
                this.supplierList = newValue;
                if (this.editData) {
                    this.initializeData();
                }
            }
        },
        currentWarehouse: {
            deep: true,
            handler(newValue) {
                this.warehouseList = newValue;
                this.pagination.warehouse = this.currentWarehousePagination;
                if (this.editData) {
                    this.initializeData();
                }
            }
        },
        currentItems: {
            deep: true,
            handler(newValue) {
                this.productList = newValue;
                this.pagination.items = this.currentItemsPagination;
                this.handleProductChange(true);
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                this.invoice_setting = newValue;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchISupplierList();
                this.fetchWarehouseList();
                this.fetchItemList(this.pagination.items && Object.keys(this.pagination.items).length > 0 ? (Number(this.pagination.items.total) + 50) : 1000);
                this.fetchInvoiceSetting();
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        isMessageDialogVisible: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_supplier: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_warehouse: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showModalProduct: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.sendModal();
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.is_updated = false;
                    this.$emit('is-sales-save', true);
                }
            }
        }
    },
}
</script>
<style scoped>
.table-container {
    /* Set the desired fixed height */
    overflow-y: auto;
    /* Enable vertical scrolling */
    overflow-x: auto;
    /* Hide horizontal scrollbar if not needed */
}

/* Optional: Customize the scrollbar */
.table-container::-webkit-scrollbar {
    width: 3px;
    /* Width of the scrollbar */
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* Track color */
}

.table-container::-webkit-scrollbar-thumb {
    background: #888;
    /* Thumb color */
    border-radius: 3px;
    /* Rounded corners */
}

/* Modal styling */
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>
