<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-auto">
        <div class="bg-white w-full sm:w-3/4 transform transition-transform ease-in-out duration-300 h-screen overflow-auto"
            :class="{
                'scale-100': isOpen, 'scale-0': !isOpen, 'pb-[60px]': isMobile
            }">
            <div class="flex justify-between items-center relative w-full px-4 py-4 set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Available Options Are
                </p>
                <p class="close" @click="closeModal">&times;</p>
            </div>
            <div class="text-sm p-5 grid grid-cols-1 sm:grid-cols-1 gap-2 sm:gap-6 no-capitalize">
                <!--display options-->
                <div v-if="variables && Object.keys(variables).length > 0">
                    <div v-for="(keydata, keys) in Object.keys(variables)" :key="keys">
                        <p class="text-center font-bold bg-gray-300 py-1">{{ formatKey(keydata) }} List</p>
                        <table
                            v-if="variables[keydata] && Array.isArray(variables[keydata]) && variables[keydata].length > 0"
                            class="mb-3 w-full">
                            <thead>
                                <tr class="text-white set-header-background">
                                    <th class="text-left px-2">Label</th>
                                    <th class="text-left px-2">Key</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(list, index) in variables[keydata]" :key="index">
                                    <td class="px-2 border">{{ list.label }}</td>
                                    <td class="px-2 border text-[#c90546] relative">
                                        <button class="cursor-pointer" @click="copyToClipboard(list.key, index)"
                                        @mouseover="hoverIndex = index"
                                        @mouseleave="hoverIndex = null"><span>{{ '{' }}</span>{{
                                              list.key }}<span>{{'}' }}</span>
                                            <font-awesome-icon icon="fa-solid fa-copy" 
                                            :class="['pl-2', hoverIndex === index ? 'text-gray-500' : 'text-gray-400']" />
                                        
                                        <!-- Hover Tooltip -->
                                        <div v-if="hoverIndex === index && copiedIndex !== index"
                                            class="absolute -top-6 left-[25%] p-1 bg-black text-white text-xs rounded">
                                            Copy
                                        </div>
                                        
                                        <!-- Copied Feedback Tooltip -->
                                        <div v-if="copiedIndex === index"
                                            class="absolute -top-6 left-[25%] p-1 bg-black text-white text-xs rounded">
                                            Copied! ✅
                                        </div>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        isMobile: Boolean,
        variables: Object,
    },
    data() {
        return {
            isOpen: false,
            message: '',
            copiedIndex: null,
    hoverIndex: null,
    copyTimeout: null

        };
    },
    methods: {
        formatKey(key) {
            return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        },
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event

            setTimeout(() => {
                this.$emit('close-Modal');
            }, 300);
        },
        copyToClipboard(text, index) {
  if (navigator.clipboard) {
    const textFormat = `{${text}}`;
    navigator.clipboard.writeText(textFormat).then(() => {
      this.copiedIndex = index;  // Use the passed index directly
      
      // Clear previous timeout if any
      if (this.copyTimeout) clearTimeout(this.copyTimeout);
      
      this.copyTimeout = setTimeout(() => {
        this.copiedIndex = null;
      }, 2000);
    }).catch((err) => {
      console.error('Failed to copy text:', err);
    });
  } else {
    alert('Clipboard API is not supported in this browser.');
  }
}

    },
    watch: {
        showModal(newValue) {
            // console.log(newValue, 'Waht happening..!', this.unit_list);
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },


    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>
