<template>
    <div class="p-6 bg-white shadow-md rounded-md my-1">
        <!-- Buttons for selecting date range -->
        <div v-if="selected_option !== 'GSTR'" class="flex space-x-4 mb-4">
            <button @click="setDateRange('last_month', true)" :title="setDateRange('last_month')"
                :class="{ 'bg-green-700': selected_category === 'last_month' }"
                class="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                Last Month
            </button>
            <button @click="setDateRange('this_month', true)" :title="setDateRange('this_month')"
                :class="{ 'bg-green-700': selected_category === 'this_month' }"
                class="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                This Month
            </button>
            <button @click="setDateRange('last_week', true)" :title="setDateRange('last_week')"
                :class="{ 'bg-green-700': selected_category === 'last_week' }"
                class="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                Last Week
            </button>
            <button @click="setDateRange('this_week', true)" :title="setDateRange('this_week')"
                :class="{ 'bg-green-700': selected_category === 'this_week' }"
                class="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                This Week
            </button>
            <button @click="setDateRange('yesterday', true)" :title="setDateRange('yesterday')"
                :class="{ 'bg-green-700': selected_category === 'yesterday' }"
                class="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                Yesterday
            </button>
            <button @click="setDateRange('today', true)" :title="setDateRange('today')"
                :class="{ 'bg-green-700': selected_category === 'today' }"
                class="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                Today
            </button>
            <button @click="showCustomDateRange = true, selected_category = 'custom'"
                :class="{ 'bg-green-700': selected_category === 'custom' }"
                class="px-5 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded">
                Custom
            </button>
        </div>
        <!--sales options-->
        <div v-if="selected_option === 'GSTR'" class="space-y-6">
            <!-- GSTR-1 Section -->
            <div class="bg-white shadow-lg rounded-lg p-2 sm:p-4 lg:p-6 border border-t-4 border-indigo-600">
                <h3 class="text-lg sm:text-xl font-semibold mb-4">GSTR-1 - Details of Outward Supplies</h3>
                <div class="grid grid-cols-1 sm:grid-cols-4 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-red-700">Acc. Year</label>
                        <select v-model="gstr1form.selectedYear"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option v-for="year in financialYears" :key="year" :value="year">{{ year }}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-red-700">Mode</label>
                        <select v-model="gstr1form.selectedMode"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option v-for="mode in gstrMode" :key="mode.value" :value="mode.value">{{
                                mode.label }}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-red-700">Month</label>
                        <select v-model="gstr1form.selectedMonth"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option :class="{ 'hidden': gstr1form.selectedMode !== 'month' }" v-for="month in allMonths"
                                :key="month.value" :value="month.value">{{ month.label }}
                            </option>
                            <option :class="{ 'hidden': gstr1form.selectedMode === 'month' }"
                                v-for="quarter in quarterMonths" :key="quarter.value" :value="quarter.value">{{
                                    quarter.label }}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-red-700">Type</label>
                        <select v-model="gstr1form.selectedType"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option value="all">All</option>
                            <option value="services">Services</option>
                            <option value="sales">Sales</option>

                        </select>
                    </div>
                </div>
                <div class="flex sm:flex-row flex-col justify-start items-center space-x-4 py-2">
                    <button @click="exportGstr1Excel" class="mt-4 px-4 py-2 bg-orange-500 text-white rounded-md">Excel
                        Export</button>
                    <p class="text-sm text-red-600 mt-2">Note: Date interval should not exceed 3 months</p>
                </div>
            </div>

            <!-- GSTR-2 Section -->
            <div class="bg-white shadow-lg rounded-lg p-2 sm:p-4 lg:p-6 border border-t-4 border-indigo-600">
                <h3 class="text-lg sm:text-xl font-semibold mb-4">GSTR-2 - Purchase Report</h3>
                <p class="py-1 text-orange-500 text-sm">⚠️ This feature is under development. Please check back later.
                </p>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-red-700">Acc. Year</label>
                        <select v-model="gstr2form.selectedYear"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option v-for="year in financialYears" :key="year" :value="year">{{ year }}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-red-700">Mode</label>
                        <select v-model="gstr2form.selectedMode"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option v-for="mode in gstrMode" :key="mode.value" :value="mode.value">{{
                                mode.label }}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-red-700">Month</label>
                        <select v-model="gstr2form.selectedMonth"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option :class="{ 'hidden': gstr2form.selectedMode !== 'month' }" v-for="month in allMonths"
                                :key="month.value" :value="month.value">{{ month.label }}
                            </option>
                            <option :class="{ 'hidden': gstr2form.selectedMode === 'month' }"
                                v-for="quarter in quarterMonths" :key="quarter.value" :value="quarter.value">{{
                                    quarter.label }}</option>
                        </select>
                    </div>
                </div>
                <div class="flex sm:flex-row flex-col justify-start items-center space-x-4 py-2">
                    <button @click="exportGstr2Excel" class="mt-4 px-4 py-2 bg-orange-500 text-white rounded-md">Excel
                        Export</button>
                    <p class="text-sm text-red-600 mt-2">Note: Date interval should not exceed 3 months</p>
                </div>
            </div>

            <!-- GSTR-3B Section -->
            <div class="bg-white shadow-lg rounded-lg p-2 sm:p-4 lg:p-6 border border-t-4 border-indigo-600">
                <h3 class="text-lg sm:text-xl font-semibold mb-4">GSTR-3B Monthly Return</h3>
                <p class="py-1 text-orange-500 text-sm">⚠️ This feature is under development. Please check back later.
                </p>
                <div class="grid grid-cols-1 sm:grid-cols-3 gap-6">
                    <div>
                        <label class="block text-sm font-medium text-red-700">Acc. Year</label>
                        <select v-model="gstr3bform.selectedYear"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option v-for="year in financialYears" :key="year" :value="year">{{ year }}</option>
                        </select>
                    </div>
                    <div>
                        <label class="block text-sm font-medium text-red-700">Month</label>
                        <select v-model="gstr3bform.selectedMonth"
                            class="mt-2 block w-full rounded border border-red-300 shadow-sm p-1">
                            <option v-for="month in allMonths" :key="month.value" :value="month.value">{{ month.label }}
                            </option>
                        </select>
                    </div>
                </div>
                <div class="flex sm:flex-row flex-col justify-start items-center space-x-4 py-2">
                    <button class="mt-4 px-4 py-2 bg-orange-500 text-white rounded-md">Excel Export</button>
                    <p class="text-sm text-red-600 mt-2">Note: Date interval should not exceed month</p>
                </div>
            </div>
        </div>

        <!-- Custom Date Range Picker (conditionally shown) -->
        <div v-if="(showCustomDateRange || selected_option === 'Customer Ledger') && selected_option !== 'GSTR'"
            class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            <div class="grid grid-cols-3 gap-3">
                <label for="fromDate" class="flex items-center space-x-2 whitespace-nowrap">
                    <i class="fas fa-calendar-alt text-gray-700"></i>
                    <span>From Date<span class="text-red-600 font-bold">*</span></span>
                </label>
                <div class="col-span-2">
                    <input type="date" v-datepicker id="fromDate" v-model="formValues.from" :max="formValues.to"
                        @change="updateMinToDate"
                        class="border px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full" />
                </div>
            </div>
            <div class="grid grid-cols-3 gap-3">

                <label for="toDate" class="flex items-center space-x-2 whitespace-nowrap">
                    <i class="fas fa-calendar-alt text-gray-700"></i>
                    <span>To Date<span class="text-red-600 font-bold">*</span></span>
                </label>
                <div class="col-span-2">
                    <input type="date" v-datepicker id="toDate" v-model="formValues.to" :min="minDate" :max="maxDate"
                        class="border px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full" />
                </div>
            </div>
            <!---customer----->
            <div v-if="selected_option !== 'Purchase' && selected_option !== 'Expense' && selected_option !== 'Stock'"
                class="relative justify-center items-center">
                <div class="grid grid-cols-3 gap-3 justify-center items-center">
                    <div class="items-center space-x-4 mr-2">
                        <label for="customer" class="flex items-center space-x-2 whitespace-nowrap">
                            <i class="fas fa-user text-gray-700"></i>
                            <span>Customer</span>
                        </label>
                    </div>
                    <input id="customer" v-model="formValues.customer" @input="handleDropdownInput(fields)"
                        @focus="isDropdownOpen = true, handleDropdownInput(fields)" @blur="closeDropdown('customer')"
                        @keydown.enter="handleEnterKey('customer', filteredCustomerOptions)"
                        @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                        @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="customer"
                        class="w-full col-span-2 border py-2 pl-2 pr-8 leading-5 text-gray-900 focus:ring-0 rounded rounded-tr-none rounded-br-none outline-none" />

                    <!-- Right-aligned icon based on isDropdownOpen -->
                    <span class="absolute py-2 ml-[90%] sm:ml-[90%] lg:ml-[90%] cursor-pointer"
                        @click="isDropdownOpen = !isDropdownOpen">
                        <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                        <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                    </span>
                </div>
                <!-- Display filtered options as the user types -->
                <div v-if="isDropdownOpen">
                    <div class="absolute mt-1 w-full max-h-60 overflow-auto ml-2 rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                        style="z-index: 100;" @mousedown.prevent="preventBlur('customer')">
                        <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                            @click="selectDropdownOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                            {{ option.first_name + (option.last_name ? ' ' + option.last_name : '') }} - {{
                                option.contact_number
                            }}
                        </p>
                    </div>
                </div>
            </div>
            <!--supplier-->
            <div v-if="selected_option === 'Purchase'" class="relative justify-center items-center">
                <div class="grid grid-cols-3 gap-3 justify-center items-center">
                    <div class="items-center space-x-4 mr-2">
                        <label for="serviceCategoryDropdown" class="flex items-center space-x-2 whitespace-nowrap">
                            <i class="fas fa-user text-gray-700"></i>
                            <span>Supplier</span>
                        </label>
                    </div>
                    <!-- Input field -->
                    <input v-model="formValues.supplier" @input="handleSupplierChange(formValues.supplier)"
                        @focus="handleSupplierChange(formValues.supplier), isDropdownsupplier = true" list="productList"
                        class="py-2 px-2 col-span-2 mt-1 border w-full rounded" @blur="closeDropdown('supplier')"
                        @keydown.enter="handleEnterKey('supplier', filteredSupplierList)"
                        @keydown.down.prevent="handleDownArrow(filteredSupplierList)"
                        @keydown.up.prevent="handleUpArrow(filteredSupplierList)" />

                    <!-- Dropdown icon -->
                    <p class="absolute top-0 right-0 flex items-center h-full px-3 cursor-pointer"
                        @click="isDropdownsupplier = !isDropdownsupplier">
                        <font-awesome-icon v-if="isDropdownsupplier" icon="fa-solid fa-angle-up" />
                        <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                    </p>
                </div>
                <div class="w-full relative">
                    <div v-if="isDropdownsupplier" @mousedown.prevent="preventBlur('supplier')"
                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                        style="z-index: 100;">
                        <p v-for="(option, index) in filteredSupplierList" :key="index"
                            @click="selectedSupplierData(option)" :class="{ 'bg-gray-100': index === selectedIndex }"
                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                            {{ option.name }}
                        </p>
                    </div>
                </div>
            </div>
            <div v-if="selected_option === 'Services' || selected_option === 'Leads' || selected_option === 'AMC' || selected_option === 'Expense'"
                class="grid grid-cols-3 gap-3">
                <label for="categories" class="flex items-center space-x-2">
                    <i class="fas fa-list text-gray-700"></i>
                    <span>Categories</span>
                </label>
                <select id="categories" v-model="formValues.selected_category"
                    class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                    <option :value="'all'">All</option>
                    <option v-if="selected_option === 'Services'" v-for="(category, index) in currentServiceCategory"
                        :key="index" :value="category.id">{{ category.service_category }}</option>
                    <option v-for="category in categories" :key="category" :value="category">{{ category }}
                    </option>
                    <option v-if="selected_option === 'Leads' && currentLeadType && currentLeadType.length > 0"
                        v-for="(category, index) in currentLeadType" :key="index" :value="category.id">{{ category.name
                        }}
                    </option>
                    <option v-if="selected_option === 'AMC'" v-for="(category, index) in amc_category" :key="index"
                        :value="index">{{ category }}
                    </option>
                    <option v-if="selected_option === 'Expense'" v-for="(category, index) in currentExpenseType"
                        :key="index" :value="category.id">{{ category.name }}
                    </option>
                </select>
            </div>
            <div v-if="selected_option !== 'Expense' && selected_option !== 'Stock' && selected_option !== 'Purchase' && selected_option !== 'Customer Ledger'"
                class="grid grid-cols-3 gap-3">
                <label for="status" class="flex items-center space-x-2">
                    <i class="fas fa-info-circle text-gray-700"></i>
                    <span>Status</span>
                </label>
                <select id="status" v-model="formValues.selected_status"
                    class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                    <option :value="'all'">All</option>
                    <option :value="'pending'"
                        v-if="selected_option === 'Services' || selected_option === 'Leads' || selected_option === 'AMC'">
                        Pending</option>
                    <option v-if="selected_option === 'Services'" v-for="(status, index) in getStatusOption"
                        :key="index" :value="index">{{ status.name }}</option>
                    <option v-if="selected_option === 'Proforma' || selected_option === 'Estimation'" :value="0">
                        Waiting for convert to Invoice
                    </option>
                    <option
                        v-if="selected_option === 'Sales' || selected_option === 'Proforma' || selected_option === 'Estimation'"
                        :value="1">
                        {{ selected_option === 'Sales' ? 'Success' : 'Converted to Invoice' }}
                    </option>
                    <option v-if="selected_option === 'Estimation'" :value="2">Converted to Proforma</option>
                    <option v-if="selected_option === 'Sales'" :value="2">
                        Due List
                    </option>
                    <option v-if="selected_option === 'Leads'" v-for="(status, index) in leads_Option" :key="index"
                        :value="index">{{ status.name }}
                    </option>
                    <option v-if="selected_option === 'AMC'" v-for="(status, index) in amcs_Option" :key="index"
                        :value="index">{{ status.name }}
                    </option>
                    <option v-if="selected_option === 'RMA'" v-for="(status, index) in rma_option" :key="index"
                        :value="index">{{ status.label }}
                    </option>
                </select>
            </div>
            <!---invoice group-->
            <!-- <div v-if="selected_option === 'Sales'" class="grid grid-cols-3 gap-3">
                <label for="status" class="flex items-center space-x-2">
                    <i class="fas fa-solid fa-layer-group text-gray-700"></i>
                    <span>Invoice Group</span>
                </label>
                <select id="status" v-model="formValues.invoice_group"
                    class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                    <option :value="'all'">All</option>
                    <option :value="0">Group A</option>
                    <option :value="1">Group B</option>
                </select>
            </div> -->
            <div v-if="selected_option === 'Services' || selected_option === 'Leads' || selected_option === 'AMC'"
                class="grid grid-cols-3 gap-3">
                <label for="employees" class="flex items-center space-x-2">
                    <i class="fas fa-user-tie text-gray-700"></i>
                    <span>Employees</span>
                </label>
                <select id="employees" v-model="formValues.selected_employee"
                    class="border px-3 col-span-2 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                    <option :value="'all'">All</option>
                    <option v-for="(employee, index) in currentEmployee" :key="index" :value="employee.id">
                        {{ employee.name }}
                    </option>
                </select>
            </div>
            <!---service type--->
            <div v-if="selected_option === 'Services'" class="grid grid-cols-3 gap-3">
                <label for="status" class="flex items-center space-x-2">
                    <i class="fas fa-info-circle text-gray-700"></i>
                    <span>Service Type</span>
                </label>
                <select id="status" v-model="formValues.service_type"
                    class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                    <option :value="'all'">All</option>
                    <option :value="'Carry in'">Carry In</option>
                    <option :value="'Pick up'">Pick Up</option>
                    <option :value="'On site'">On Site</option>
                    <option :value="'Remote'">Remote</option>
                </select>
            </div>
            <div class="flex items-center space-x-4">
                <button class="px-5 py-2 bg-green-600 text-white rounded" @click="submitForm">Submit</button>
            </div>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <!-- <amountCalculation v-if="isCalculate" :selected_option="selected_option" :data="data"
            :getStatusOption="getStatusOption"></amountCalculation> -->
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import amountCalculation from './amountCalculation.vue';
import DatePicker from '../datepicker/DatePicker.vue';
import * as XLSX from 'xlsx';
export default {
    components: {
        amountCalculation,
        DatePicker
    },
    props: {
        selected_option: String,
        refresh: Boolean
    },
    data() {
        return {
            formValues: {},
            gstr1form: {},
            gstr2form: {},
            gstr3bform: {},
            categories: [],
            statuses: [],
            employees: [],
            showCustomDateRange: true,
            selected_category: 'custom',
            //---services---
            getStatusOption: [{ name: 'Service Taken', total: 0 }, { name: 'Hold', total: 0 }, { name: 'In-progress', total: 0 }, { name: 'New Estimate', total: 0 }, { name: 'Ready To Deliver', total: 0 }, { name: 'Delivered', total: 0 }, { name: 'Cancelled', total: 0 }, { name: 'Completed', total: 0 }],
            leads_Option: [{ name: 'Open', total: 0 }, { name: 'Inprogress', total: 0 }, { name: 'Completed', total: 0 }, { name: 'Cancelled', total: 0 }, { name: 'Hold', total: 0 }],
            amcs_Option: [{ name: 'Open', total: 0 }, { name: 'Inprogress', total: 0 }, { name: 'Completed', total: 0 }, { name: 'Cancelled', total: 0 }],
            rma_option: [
                { value: 1, label: 'Awaiting Customer Confirmation', total: 0 },
                { value: 2, label: 'Awaiting Parts', total: 0 },
                { value: 3, label: 'Awaiting Repair', total: 0 },
                { value: 4, label: 'Awaiting Supplier', total: 0 },
                { value: 5, label: 'Awaiting to be sent to Supplier', total: 0 },
                { value: 6, label: 'Credit', total: 0 },
                { value: 7, label: 'Ready to Deliver', total: 0 },
                { value: 8, label: 'Repair Completed', total: 0 },
                { value: 9, label: 'Repair in Process', total: 0 },
                { value: 10, label: 'Repaired/Replacement from Supplier', total: 0 },
                { value: 11, label: 'Sent to Customer', total: 0 },
                { value: 12, label: 'Sent to Supplier', total: 0 },
                { value: 13, label: 'Waiting New Battery', total: 0 },
            ],
            //---amcs category--
            amc_category: ['Paid', 'Unpaid', 'Free'],
            //----customer---
            isDropdownOpen: false,
            isInputFocused: {},
            filteredCustomerOptions: [],
            selectedIndex: 0,
            //---supplier----
            filteredSupplierList: [],
            isDropdownsupplier: false,

            open_loader: false,
            data: [],
            isCalculate: false,
            status_counts: {},
            companyId: '',
            userId: '',
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            //--date minimum and maximum---
            maxDate: '',
            minDate: '',
            //---state list--
            stateList: [
                { code: "01", state: "Jammu and Kashmir" },
                { code: "02", state: "Himachal Pradesh" },
                { code: "03", state: "Punjab" },
                { code: "04", state: "Chandigarh" },
                { code: "05", state: "Uttarakhand" },
                { code: "06", state: "Haryana" },
                { code: "07", state: "Delhi" },
                { code: "08", state: "Rajasthan" },
                { code: "09", state: "Uttar Pradesh" },
                { code: "10", state: "Bihar" },
                { code: "11", state: "Sikkim" },
                { code: "12", state: "Arunachal Pradesh" },
                { code: "13", state: "Nagaland" },
                { code: "14", state: "Manipur" },
                { code: "15", state: "Mizoram" },
                { code: "16", state: "Tripura" },
                { code: "17", state: "Meghalaya" },
                { code: "18", state: "Assam" },
                { code: "19", state: "West Bengal" },
                { code: "20", state: "Jharkhand" },
                { code: "21", state: "Odisha" },
                { code: "22", state: "Chhattisgarh" },
                { code: "23", state: "Madhya Pradesh" },
                { code: "24", state: "Gujarat" },
                { code: "26", state: "Dadra and Nagar Haveli and Daman and Diu (Newly Merged UT)" },
                { code: "27", state: "Maharashtra" },
                { code: "28", state: "Andhra Pradesh (Before Division)" },
                { code: "29", state: "Karnataka" },
                { code: "30", state: "Goa" },
                { code: "31", state: "Lakshadweep" },
                { code: "32", state: "Kerala" },
                { code: "33", state: "Tamil Nadu" },
                { code: "34", state: "Puducherry" },
                { code: "35", state: "Andaman and Nicobar Islands" },
                { code: "36", state: "Telangana" },
                { code: "37", state: "Andhra Pradesh (Newly Added)" },
                { code: "38", state: "Ladakh (Newly Added)" },
                { code: "97", state: "Other Territory" },
                { code: "99", state: "Centre Jurisdiction" }
            ]
        };
    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('employess', ['currentEmployee']),
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('supplier', ['currentSupplier']),
        ...mapGetters('leadType', ['currentLeadType']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('expensesTypeList', ['currentExpenseType']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('supplier', ['currentSupplier']),

        // Automatically adjust for financial years
        financialYears() {
            const startYear = 2023; // Starting year for the fiscal years
            const currentYear = new Date().getFullYear();
            const nextYear = currentYear + 1;

            // Generate the financial years up to the current year
            let financialYearsList = [];

            // Start with the first year (2023) and continue generating fiscal years
            for (let year = startYear; year <= currentYear; year++) {
                financialYearsList.push(`${year}-${year + 1}`);
            }

            return financialYearsList;
        },
        gstrMode() {
            return [
                { label: "Quaterly | Composition", value: "quaterly" },
                { label: "Monthly", value: "month" }
            ];
        },

        // Define options for quarter-based month selection
        quarterMonths() {
            return [
                { label: "January - March", value: "January - March" },
                { label: "April - June", value: "April - June" },
                { label: "July - September", value: "July - September" },
                { label: "October - December", value: "October - December" }
            ];
        },
        // Define all months of the year
        allMonths() {
            return [
                { label: "January", value: "January" },
                { label: "February", value: "February" },
                { label: "March", value: "March" },
                { label: "April", value: "April" },
                { label: "May", value: "May" },
                { label: "June", value: "June" },
                { label: "July", value: "July" },
                { label: "August", value: "August" },
                { label: "September", value: "September" },
                { label: "October", value: "October" },
                { label: "November", value: "November" },
                { label: "December", value: "December" }
            ];
        }

    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('employess', ['fetchEmployeeList']),
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('supplier', ['fetchISupplierList']),
        ...mapActions('leadType', ['fetchLeadTypeList']),
        ...mapActions('expensesTypeList', ['fetchExpenseTypeList']),
        ...mapActions('supplier', ['fetchISupplierList']),
        submitForm() {
            if (this.formValues.from && this.formValues.from !== '' && this.formValues.to && this.formValues.to !== '') {
                if (this.selected_option === 'Services' && this.companyId && this.companyId !== '') {
                    this.filterReportsservices();
                }
                else if (this.selected_option === 'Sales' && this.companyId && this.companyId !== '') {
                    this.getSalesReport();
                }
                else if (this.selected_option === 'Proforma' && this.companyId && this.companyId !== '') {
                    this.getProformaReport();
                }
                else if (this.selected_option === 'Estimation' && this.companyId && this.companyId !== '') {
                    this.getEstimationReport();
                }
                else if (this.selected_option === 'Purchase' && this.companyId && this.companyId !== '') {
                    this.getPurchaseReport();
                }
                else if (this.selected_option === 'Leads' && this.companyId && this.companyId !== '') {
                    this.getLeadsReport();
                }
                else if (this.selected_option === 'AMC' && this.companyId && this.companyId !== '') {
                    this.getAMCReport();
                }
                else if (this.selected_option === 'Expense' && this.companyId && this.companyId !== '') {
                    this.getExpenseReport();
                }
                else if (this.selected_option === 'Stock' && this.companyId && this.companyId !== '') {
                    this.getStockReport();
                }
                else if (this.selected_option === 'RMA' && this.companyId && this.companyId !== '') {
                    this.getRMAReport();
                }
                else if (this.selected_option === 'Customer Ledger' && this.companyId && this.companyId !== '') {
                    if (this.formValues.customer_id && this.formValues.customer_id !== '') {
                        this.getLedgerReport();
                    } else {
                        this.message = 'Please select the Customer....!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                }
            } else {
                this.message = !this.formValues.from || this.formValues.from === '' ? 'Please select the from date' : !this.formValues.to || this.formValues.to === '' ? 'Please selecte the to date' : 'Please fill required fields are FROM and TO date';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        filterReportsservices() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'services', q: this.formValues.selected_status === 'all' || this.formValues.selected_status === 'pending' ? '' : this.formValues.selected_status, category: this.formValues.selected_category == 'all' ? '' : this.formValues.selected_category,
                employer_id: this.formValues.selected_employee === 'all' ? '' : this.formValues.selected_employee,
                filter: '', per_page: 'all', page: 1, customer_id: this.formValues.customer_id ? this.formValues.customer_id : '',
                company_id: this.companyId
            };
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.formValues.service_type && this.formValues.service_type !== 'all') {
                        if (this.data.length > 0) {
                            this.data = this.data.filter(opt => {
                                if (opt.service_data && typeof opt.service_data === 'string' && JSON.parse(opt.service_data) && JSON.parse(opt.service_data).service_type && JSON.parse(opt.service_data).service_type === this.formValues.service_type) {
                                    return true;
                                } else {
                                    return false;
                                }
                            });
                        } else {
                            this.data = [];
                        }
                    }
                    let category_name = 'all';
                    if (this.formValues.selected_category !== 'all' && this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                        let find_category = this.currentServiceCategory.find(opt => opt.id === this.formValues.selected_category);
                        if (find_category) {
                            category_name = find_category.service_category;
                        }
                    }
                    let status_name = 'all';
                    if (this.formValues.selected_status !== 'all' && this.getStatusOption && this.getStatusOption.length > 0) {

                        if (this.getStatusOption[this.formValues.selected_status] && this.getStatusOption[this.formValues.selected_status].name) {
                            status_name = this.getStatusOption[this.formValues.selected_status].name;
                        }
                    }
                    let emp_name = 'all';
                    if (this.formValues.selected_employee !== 'all' && this.currentEmployee && this.currentEmployee.length > 0) {
                        let find_emp = this.currentEmployee.find(opt => opt.id == this.formValues.selected_employee);
                        if (find_emp) {
                            emp_name = find_emp.name;
                        }
                    }
                    this.formValues.selected_category
                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', customer: this.formValues.customer ? this.formValues.customer : '', selected_status: this.formValues.selected_status !== undefined ? status_name : '', selected_category: this.formValues.selected_category ? category_name : '', selected_employee: this.formValues.selected_employee ? emp_name : '', service_type: this.formValues.service_type ? this.formValues.service_type : '' });
                    if (response.data.status_counts) {
                        let keys_data = Object.keys(response.data.status_counts);
                        // if (keys_data.length > 0) {
                        keys_data.map(opt => {
                            if (this.getStatusOption[opt]) {
                                this.getStatusOption[opt].total = response.data.status_counts.opt;
                            }
                        })
                        // }
                    }
                    this.isCalculate = true;
                    if (this.data && this.data.length > 0) {
                        this.message = 'Services report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Services data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getSalesReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'sales', q: this.formValues.selected_status === 'all' ? 'search' : this.formValues.selected_status == 1 ? 'success' : 'due',
                per_page: 'all', page: 1, customer_id: this.formValues.customer_id ? this.formValues.customer_id : '',
                company_id: this.companyId,
                invoice_group: this.formValues.invoice_group >= 0 ? this.formValues.invoice_group : 0,
            };
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');

                    this.open_loader = false;
                    this.data = response.data.data;
                    let status_name = 'all';
                    if (this.formValues.selected_status !== 'all') {
                        status_name = this.formValues.selected_status == 0 ? 'Waiting for convert to Invoice' : this.formValues.selected_status == 1 ? 'Success' : 'all';
                    }
                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', customer: this.formValues.customer ? this.formValues.customer : '', selected_status: this.formValues.selected_status !== undefined ? status_name : '' });
                    if (this.data && this.data.length > 0) {
                        this.message = 'Sales report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Sales data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getProformaReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'proforma', q: this.formValues.selected_status === 'all' ? 'search' : this.formValues.selected_status == 1 ? 'success' : '',
                per_page: 'all', page: 1, customer_id: this.formValues.customer_id ? this.formValues.customer_id : '',
                company_id: this.companyId
            };
            axios.get('/proforma_invoices', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.formValues.customer_id && this.formValues.customer !== '') {
                        this.data = this.data.filter(opt => opt.customers && opt.customers.id ? opt.customers.id === this.formValues.customer_id : false);
                    }
                    if (this.formValues.selected_status !== 'all') {
                        if (this.formValues.selected_status) {
                            this.data = this.data.filter(opt => opt.sales);
                        }
                        if (this.formValues.selected_status == 0) {
                            this.data = this.data.filter(opt => !opt.sales);
                        }
                    }
                    let status_name = 'all';
                    if (this.formValues.selected_status !== 'all') {
                        status_name = this.formValues.selected_status == 0 ? 'Waiting for convert to Invoice' : this.formValues.selected_status == 1 ? 'Converted to Invoice' : 'all';
                    }
                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', customer: this.formValues.customer ? this.formValues.customer : '', selected_status: this.formValues.selected_status !== undefined ? status_name : '' });
                    if (this.data && this.data.length > 0) {
                        this.message = 'Proforma report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Proforma data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getEstimationReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'estimation', q: this.formValues.selected_status === 'all' ? 'search' : this.formValues.selected_status == 1 ? 'success' : '',
                per_page: 'all', page: 1, customer_id: this.formValues.customer_id ? this.formValues.customer_id : '',
                company_id: this.companyId
            };
            axios.get('/estimations', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.formValues.customer_id && this.formValues.customer !== '') {
                        this.data = this.data.filter(opt => opt.customers.id === this.formValues.customer_id);
                    }
                    if (this.formValues.selected_status !== 'all') {
                        if (this.formValues.selected_status === 1) {
                            this.data = this.data.filter(opt => opt.status == 1);
                        }
                        if (this.formValues.selected_status === 2) {
                            this.data = this.data.filter(opt => opt.status == 2);
                        }
                        if (this.formValues.selected_status == 0) {
                            this.data = this.data.filter(opt => opt.status == 0);
                        }
                    }
                    let status_name = 'all';
                    if (this.formValues.selected_status !== 'all') {
                        status_name = this.formValues.selected_status == 0 ? 'Waiting for convert to Invoice' : this.formValues.selected_status == 1 ? 'Converted to Invoice' : this.formValues.selected_status == 2 ? 'Converted to Proforma' : 'all';
                    }
                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', customer: this.formValues.customer ? this.formValues.customer : '', selected_status: this.formValues.selected_status !== undefined ? status_name : '' });
                    if (this.data && this.data.length > 0) {
                        this.message = 'Estimation report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Estimation data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getPurchaseReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'purchase', q: this.formValues.selected_status === 'all' ? 'search' : this.formValues.selected_status == 1 ? 'success' : '',
                per_page: 'all', page: 1, supplier_id: this.formValues.supplier_id ? this.formValues.supplier_id : '',
                company_id: this.companyId
            };
            axios.get('/purchase_orders', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.formValues.supplier_id && this.formValues.supplier !== '') {
                        this.data = this.data.filter(opt => opt.supplier_id === this.formValues.supplier_id);
                    }
                    if (this.data.length > 0) {
                        this.data = this.data.map(opt => {
                            let supplierData = this.supplierName(opt.supplier_id);
                            return {
                                ...opt,
                                supplier: supplierData
                            };
                        });
                    }

                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', supplier: this.formValues.supplier ? this.formValues.supplier : '', });
                    if (this.data && this.data.length > 0) {
                        this.message = 'Purchase report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Purchase data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getLeadsReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'leads', q: this.formValues.selected_status === 'all' || this.formValues.selected_status === 'pending' ? '' : this.formValues.selected_status, category: this.formValues.selected_category == 'all' ? '' : this.formValues.selected_category,
                employer_id: this.formValues.selected_employee === 'all' ? '' : this.formValues.selected_employee,
                filter: '', per_page: 'all', page: 1, customer_id: this.formValues.customer_id ? this.formValues.customer_id : '',
                company_id: this.companyId
            };
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.data && this.data.length > 0) {
                        this.data = this.data.map(opt => {
                            let lead_status = this.leads_Option[opt.lead_status].name;
                            return {
                                ...opt,
                                lead_status: lead_status
                            };
                        });
                    }
                    let category_name = 'all';
                    if (this.formValues.selected_category !== 'all' && this.currentLeadType && this.currentLeadType.length > 0) {
                        let find_category = this.currentLeadType.find(opt => opt.id === this.formValues.selected_category);
                        if (find_category) {
                            category_name = find_category.name;
                        }
                    }
                    let status_name = 'all';
                    if (this.formValues.selected_status !== 'all' && this.leads_Option && this.leads_Option.length > 0) {

                        if (this.leads_Option[this.formValues.selected_status] && this.leads_Option[this.formValues.selected_status].name) {
                            status_name = this.leads_Option[this.formValues.selected_status].name;
                        }
                    }
                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', customer: this.formValues.customer ? this.formValues.customer : '', selected_status: this.formValues.selected_status !== undefined ? status_name : '', selected_category: this.formValues.selected_category ? category_name : '', selected_employee: this.formValues.selected_employee ? this.formValues.selected_employee : '' });
                    if (this.data && this.data.length > 0) {
                        this.message = 'Lead report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Lead data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getAMCReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'amcs', q: this.formValues.selected_status === 'all' || this.formValues.selected_status === 'pending' ? '' : this.formValues.selected_status, category: this.formValues.selected_category == 'all' ? '' : this.formValues.selected_category,
                employer_id: this.formValues.selected_employee === 'all' ? '' : this.formValues.selected_employee,
                filter: '', per_page: 'all', page: 1, customer_id: this.formValues.customer_id ? this.formValues.customer_id : '',
                company_id: this.companyId
            };
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.data && this.data.length > 0) {
                        this.data = this.data.map(opt => {
                            let amc_status = this.amcs_Option[opt.amc_status].name;
                            let amc_payment_type = this.amc_category[opt.amc_payment_type];
                            return {
                                ...opt,
                                amc_status: amc_status,
                                amc_payment_type: amc_payment_type
                            };
                        });
                    }
                    let category_name = 'all';
                    if (this.formValues.selected_category !== 'all' && this.amc_category && this.amc_category.length > 0) {
                        let find_category = this.amc_category.find((opt, index) => index === this.formValues.selected_category);
                        if (find_category) {
                            category_name = find_category;
                        }
                    }
                    let status_name = 'all';
                    if (this.formValues.selected_status !== 'all' && this.amcs_Option && this.amcs_Option.length > 0) {

                        if (this.amcs_Option[this.formValues.selected_status] && this.amcs_Option[this.formValues.selected_status].name) {
                            status_name = this.amcs_Option[this.formValues.selected_status].name;
                        }
                    }
                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', customer: this.formValues.customer ? this.formValues.customer : '', selected_status: this.formValues.selected_status !== undefined ? status_name : '', selected_category: this.formValues.selected_category ? category_name : '', selected_employee: this.formValues.selected_employee ? this.formValues.selected_employee : '' });
                    if (this.data && this.data.length > 0) {
                        this.message = 'AMC report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'AMC data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getExpenseReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'expense', category: this.formValues.selected_category == 'all' ? '' : this.formValues.selected_category,
                filter: '', per_page: 'all', page: 1,
                company_id: this.companyId
            };
            axios.get('/expenses', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.formValues.selected_category !== 'all' && this.formValues.selected_category) {
                        this.data = this.data.filter(opt => opt.expense_type.id == this.formValues.selected_category);
                    }
                    let category_name = 'all';
                    if (this.formValues.selected_category !== 'all' && this.currentExpenseType && this.currentExpenseType.length > 0) {
                        let find_category = this.currentExpenseType.find(opt => opt.id === this.formValues.selected_category);
                        if (find_category) {
                            category_name = find_category.name;
                        }
                    }

                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', selected_category: this.formValues.selected_category ? category_name : '' });
                    if (this.data && this.data.length > 0) {
                        this.message = 'Expenses report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Expenses data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getStockReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'items',
                per_page: 2000, page: 1,
                company_id: this.companyId
            };
            axios.get('/products_details', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.data && this.data.length > 0) {
                        this.data = this.data.filter(opt => opt.products.product_type == "Product");
                    }
                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '' });
                    if (this.data && this.data.length > 0) {
                        this.message = 'Stock report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Stock data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getRMAReport() {
            this.open_loader = true;
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                type: 'rma', per_page: 'all', page: 1,
                q: this.formValues.selected_status === 'all' ? '' : this.formValues.selected_status,
                customer_id: this.formValues.customer_id ? this.formValues.customer_id : '',
                company_id: this.companyId
            };
            axios.get('/rmas', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    if (this.data && this.data.length > 0) {
                        this.data = this.data.map(opt => {
                            let rma_status = this.rma_option[opt.rma_status].label;
                            return {
                                ...opt,
                                rma_status: rma_status
                            };
                        });
                        if (this.formValues.selected_category !== 'all' && this.formValues.selected_category >= 0) {
                            this.data = this.data.filter(opt => opt.rma_status == this.formValues.selected_category);
                        }
                        if (this.formValues.customer_id && this.formValues.customer !== '') {
                            this.data = this.data.filter(opt => opt.customer.id === this.formValues.customer_id);
                        }
                    }
                    let status_name = 'all';
                    if (this.formValues.selected_status !== 'all' && this.rma_option && this.rma_option.length > 0) {

                        if (this.rma_option[this.formValues.selected_status] && this.rma_option[this.formValues.selected_status].name) {
                            status_name = this.rma_option[this.formValues.selected_status].label;
                        }
                    }
                    this.$emit('filter-data', response.data.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', customer: this.formValues.customer ? this.formValues.customer : '', selected_status: this.formValues.selected_status !== undefined ? status_name : '' });
                    if (this.data && this.data.length > 0) {
                        this.message = 'RMA report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'RMA data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getLedgerReport() {
            this.open_loader = true;
            // console.log(this.formValues, 'Waht happening data.....');
            let send_data = {
                from_date: this.formValues.from,
                to_date: this.formValues.to,
                customer_id: this.formValues.customer_id ? this.formValues.customer_id : '',
                company_id: this.companyId
            };
            axios.get('/customer-ledger', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Ledger report data...............!');
                    this.open_loader = false;
                    this.data = response.data.ledger;

                    this.$emit('filter-data', this.data, { from: this.formValues.from ? this.formValues.from : '', to: this.formValues.to ? this.formValues.to : '', customer: this.formValues.customer ? this.formValues.customer : '', opening_balance: response.data.opening_balance, closing_balance: response.data.closing_balance });
                    if (this.data && this.data.length > 0) {
                        this.message = 'Customer ledger report displayed successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Customer ledger data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })

        },
        //----Customer----
        handleDropdownInput() {
            // console.log(this.currentCustomer, 'This is customer list..@')
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.currentCustomer.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.currentCustomer.filter(
                        (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                            (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                    );
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = this.currentCustomer;
            }

            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    // this.openModal();
                    // this.openModal(product_name);
                }
            }
            else if (type === 'supplier') {
                if (optionArray && optionArray.length > 0) {
                    this.selectedSupplierData(optionArray[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.addRow.focus();               
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else if (this.selectedIndex > 0) {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----customer dropdown--
        closeDropdown(type) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                this.isInputFocused.customer = false;

            }
            else if (type && type === 'supplier' && !this.mouseDownOnDropdown) {
                this.isDropdownsupplier = false;
                this.isInputFocused.supplier = false;
            }
        },
        //---customers dropdown
        selectDropdownOption(option) {
            this.formValues.customer_id = option.id;
            this.formValues.customer = option.first_name + (option.last_name ? ' ' + option.last_name : '') + ' - ' + option.contact_number;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
        },
        preventBlur(type) {
            if (type && type === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            }
            else if (type && type === 'supplier') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });

            }
        },
        //---supplier---
        handleSupplierChange(enteredValue) {
            if (enteredValue) {
                const enteredText = enteredValue.trim().toLowerCase();
                this.filteredSupplierList = this.currentSupplier.filter(opt => {
                    const nameMatch = opt.name.toLowerCase().includes(enteredText);
                    const contactMatch = opt.contact_number.includes(enteredText);
                    return nameMatch || contactMatch || opt.name.toLowerCase() === enteredText;
                });
            } else {
                if (this.currentSupplier.length > 0) {
                    this.filteredSupplierList = this.currentSupplier;
                }
            }
        },
        selectedSupplierData(option) {
            // console.log(option, 'What happening.....Q');
            this.formValues.supplier = option.name;
            // this.supplierID = option.id;
            this.formValues.supplier_id = option.id;
            this.isDropdownsupplier = false;
        },
        //---supplier name---
        supplierName(id) {
            if (this.currentSupplier && this.currentSupplier.length > 0) {
                let findData = this.currentSupplier.find(opt => opt.id === id);
                if (findData) {
                    return findData;
                }
            }
        },
        //---initiallyy---
        getSupportingData() {
            if (this.selected_option === 'Services') {
                this.fetchServiceCategoryList();
            }
            if (this.selected_option === 'Leads' || this.selected_option === 'Services' || this.selected_option === 'AMC' || this.selected_option === 'GSTR') {
                if (this.currentEmployee) {
                    this.fetchEmployeeList();
                } else {
                    this.fetchEmployeeList();
                }
            }
            if (this.selected_option !== 'Purchase' && this.selected_option !== 'Expense' && this.selected_option !== 'Stock') {
                if (this.currentCustomer) {
                    this.fetchCustomerList();
                } else {
                    this.fetchCustomerList();
                }
            }
            if (this.selected_option === 'Purchase') {
                if (this.currentSupplier) {
                    this.fetchISupplierList();
                } else {
                    this.fetchISupplierList();
                }
            }
            if (this.selected_option === 'Leads') {
                if (this.currentLeadType) {
                    this.fetchLeadTypeList();
                } else {
                    this.fetchLeadTypeList();
                }
            }
            if (this.selected_option === 'Expense') {
                if (this.currentExpenseType) {
                    this.fetchExpenseTypeList();
                } else {
                    this.fetchExpenseTypeList();
                }
            }
        },
        //---update minimum date---
        updateMinToDate() {
            this.minDate = this.formValues.from;
        },
        //---set date range--
        setDateRange(range, isGetData) {
            const today = new Date();
            let startDate, endDate;

            if (range === 'last_month') {
                // Set the start date to the first day of last month
                startDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);

                // Set the end date to the last day of last month
                endDate = new Date(today.getFullYear(), today.getMonth(), 0);
            } else if (range === 'this_month') {
                // Set the start date to the first day of the current month
                startDate = new Date(today.getFullYear(), today.getMonth(), 1);

                // Set the end date to today's date
                endDate = today;
            } else if (range === 'today') {
                // Set the start date to today
                startDate = new Date(today.setHours(0, 0, 0, 0));

                // Set the end date to today
                endDate = today;
            } else if (range === 'yesterday') {
                // Set the start date to yesterday
                startDate = new Date(today.setDate(today.getDate() - 1));

                // Set the end date to yesterday
                endDate = new Date(today.setHours(0, 0, 0, 0));
            } else if (range === 'this_week') {
                // Set the start date to the start of the current week (Sunday)
                const firstDayOfWeek = today.getDate() - today.getDay();
                startDate = new Date(today.setDate(firstDayOfWeek));

                // Set the end date to the last day of the week (Saturday)
                endDate = new Date(today.setDate(firstDayOfWeek + 6));
            } else if (range === 'last_week') {
                // Set the start date to the start of the last week (Sunday of last week)
                const firstDayOfLastWeek = today.getDate() - today.getDay() - 7;
                startDate = new Date(today.setDate(firstDayOfLastWeek));

                // Set the end date to the last day of the last week (Saturday of last week)
                endDate = new Date(today.setDate(firstDayOfLastWeek + 6));
            }
            if (isGetData) {
                this.selected_category = range;
                this.showCustomDateRange = false;
                this.formValues = { ...this.formValues };
                // Format the dates for the date input (YYYY-MM-DD)
                this.formValues.from = this.formatDateInput(startDate);  // Date input format
                this.formValues.to = this.formatDateInput(endDate);      // Date input format
                this.submitForm();
            } else {
                //return `${range === 'this_month' ? 'This Month' : 'Last Month'} ${this.formatDateDisplay(startDate)} - ${this.formatDateDisplay(endDate)}`;
                return `${range === 'this_month' ? 'This Month' :
                    range === 'last_month' ? 'Last Month' :
                        range === 'today' ? 'Today' :
                            range === 'yesterday' ? 'Yesterday' :
                                range === 'this_week' ? 'This Week' :
                                    range === 'last_week' ? 'Last Week' :
                                        'Custom'} ${this.formatDateDisplay(startDate)} - ${this.formatDateDisplay(endDate)}`;

            }
        },

        formatDateInput(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 01-12
            const day = String(date.getDate()).padStart(2, '0'); // 01-31
            return `${year}-${month}-${day}`; // YYYY-MM-DD format
        },

        formatDateDisplay(date) {
            const year = date.getFullYear(); // Get last two digits of the year
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Get month and add leading zero
            const day = String(date.getDate()).padStart(2, '0'); // Add leading zero for day
            return `${day}/${month}/${year}`; // Format as YY/DD/YYYY
        },
        //---export report data---
        setDateRangeForMode(data, type) {
            const selectedYear = data.selectedYear.split('-'); // Split "2024-2025" into an array ["2024", "2025"]
            const startYear = parseInt(selectedYear[0]); // First year, e.g., 2024
            const endYear = parseInt(selectedYear[1]); // Second year, e.g., 2025
            const selectedMode = data.selectedMode;
            let fromDate, toDate;

            // Check if the mode is 'Quarterly' and set the appropriate dates
            if (selectedMode === 'quaterly') {
                if (data.selectedMonth === 'January - March') {
                    // Q1: January - March of the end year (e.g., January 1st, 2025 - March 31st, 2025)
                    fromDate = new Date(endYear, 0, 1); // January 1st of the second year (2025)
                    toDate = new Date(endYear, 2, 31); // March 31st of the second year (2025)
                } else if (data.selectedMonth === 'April - June') {
                    // Q2: April - June of the first year (e.g., April 1st, 2024 - June 30th, 2024)
                    fromDate = new Date(startYear, 3, 1); // April 1st of the first year (2024)
                    toDate = new Date(startYear, 5, 30); // June 30th of the first year (2024)
                } else if (data.selectedMonth === 'July - September') {
                    // Q3: July - September of the first year (e.g., July 1st, 2024 - September 30th, 2024)
                    fromDate = new Date(startYear, 6, 1); // July 1st of the first year (2024)
                    toDate = new Date(startYear, 8, 30); // September 30th of the first year (2024)
                } else if (data.selectedMonth === 'October - December') {
                    // Q4: October - December of the first year (e.g., October 1st, 2024 - December 31st, 2024)
                    fromDate = new Date(startYear, 9, 1); // October 1st of the first year (2024)
                    toDate = new Date(startYear, 11, 31); // December 31st of the first year (2024)
                }
            } else if (selectedMode === 'month') {
                const selectedMonth = data.selectedMonth;
                const monthIndex = this.allMonths.findIndex(month => month.label === selectedMonth);
                if (selectedMonth === 'January' || selectedMonth === 'February' || selectedMonth === 'March') {
                    fromDate = new Date(endYear, monthIndex, 1); // First day of selected month
                    toDate = new Date(endYear, monthIndex + 1, 0); // Last day of selected month
                } else {
                    fromDate = new Date(startYear, monthIndex, 1); // First day of selected month
                    toDate = new Date(startYear, monthIndex + 1, 0); // Last day of selected month
                }

            }
            // Set form values for from and to dates
            let fromDate_data = this.formatDateInput(fromDate);
            let toDate_data = this.formatDateInput(toDate);
            if (type === 'gstr1') {
                this.gstr1Report(fromDate_data, toDate_data, type);
            } else if (type === 'gstr2') {
                this.gstr2Report(fromDate_data, toDate_data, type);
            }
        },

        // Function to format the date in YYYY-MM-DD format
        formatDateInput(date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 01-12
            const day = String(date.getDate()).padStart(2, '0'); // 01-31
            return `${year}-${month}-${day}`; // YYYY-MM-DD format
        },
        // Function to export the current data to Excel
        exportGstr1Excel() {
            if (Object.keys(this.gstr1form).length > 0 && this.gstr1form.selectedYear && this.gstr1form.selectedMode && this.gstr1form.selectedMonth) {
                this.setDateRangeForMode(this.gstr1form, 'gstr1');
            } else {
                this.message = 'Please fill all input values for GSTR-1!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        exportGstr2Excel() {
            if (Object.keys(this.gstr2form).length > 0 && this.gstr2form.selectedYear && this.gstr2form.selectedMode && this.gstr2form.selectedMonth) {
                this.setDateRangeForMode(this.gstr2form, 'gstr2');
            } else {
                this.message = 'Please fill all input values for GSTR-2!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },

        // Utility method for exporting data to Excel
        exportToExcel(data, filename = 'report.xlsx') {
            if (!data || data.length === 0) {
                this.message = 'No data to export!';
                this.type_toaster = 'warning';
                this.show = true;
                return;
            }

            // Create a new workbook
            const wb = XLSX.utils.book_new();

            // Convert the data into a worksheet
            const ws = XLSX.utils.json_to_sheet(data);

            // Append the worksheet to the workbook
            XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

            // Write the Excel file and trigger the download
            XLSX.writeFile(wb, filename);
        },
        gstr1Report(from, to, type) {
            this.open_loader = true;
            let send_data = {
                from_date: from ? from : '',
                to_date: to ? to : '',
                type: 'sales_gst', q: 'search',
                per_page: 'all', page: 1,
                company_id: this.companyId,
                invoice_group: '',
                invoice_type: type === 'gstr1' ? this.gstr1form.selectedType : 'all'
            };
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    this.open_loader = false;
                    if (type === 'gstr1') {
                        this.getExcelExportgstr1(response.data.data, from, to);
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        gstr2Report(from, to, type) {
            this.open_loader = true;
            let send_data = {
                from_date: from ? from : '',
                to_date: to ? to : '',
                type: 'purchase', q: 'search',
                per_page: 'all', page: 1,
                company_id: this.companyId
            };
            if (this.currentSupplier && this.currentSupplier.length === 0) {
                this.fetchISupplierList();
            }
            axios.get('/purchase_orders', { params: { ...send_data } })
                .then(response => {
                    console.log(response.data, 'Followup Data GSTR-2');
                    this.open_loader = false;
                    if (type === 'gstr2') {
                        this.getExcelExportgstr2(response.data.data, from, to);
                    }

                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getExcelExportgstr1(data, from, to) {
            if (!data || data.length === 0) {
                this.message = 'No data available to export for GSTR-1!';
                this.type_toaster = 'warning';
                this.show = true;
                return;
            }

            let state_code = '';
            let placeofSupply_company = '';
            if (this.currentCompanyList && this.currentCompanyList.gst_number && this.currentCompanyList.gst_number !== '') {
                let statecode = this.currentCompanyList.gst_number.substring(0, 2);
                if (statecode) {
                    let find_data = this.stateList.find(opt => opt.code == statecode);
                    if (find_data) {
                        placeofSupply_company = find_data.code + ' - ' + find_data.state;
                        state_code = find_data.code;
                    }
                }
            }

            const dataToExport = data.map(item => {
                // Extract service_items and sales_item_data
                const salesItems = item.sales_item_data || [];
                const serviceItems = JSON.parse(item.service_items) || [];

                // Calculate totals for sales items
                let totalSalesAmount = 0;
                let totalSalesTax = 0;
                salesItems.forEach(salesItem => {
                    totalSalesAmount += salesItem.total;
                    totalSalesTax += parseFloat(salesItem.tax) || 0;  // Assuming taxvalue is the tax amount
                });

                // Calculate totals for service items
                let totalServiceAmount = 0;
                let totalServiceTax = 0;
                serviceItems.forEach(serviceItem => {
                    totalServiceAmount += parseFloat(serviceItem.total) || 0;
                    totalServiceTax += parseFloat(serviceItem.tax) || 0;  // Assuming taxvalue is the tax amount
                });

                // Combine totals
                const totalAmount = totalSalesAmount + totalServiceAmount;
                const totalTax = totalSalesTax + totalServiceTax;

                let placeofSupply = '';
                let state_code_customer = '';
                if (item.customer.gst_number && item.customer.gst_number !== '') {
                    let statecode = item.customer.gst_number.substring(0, 2);
                    if (statecode) {
                        let find_data = this.stateList.find(opt => opt.code == statecode);
                        if (find_data) {
                            placeofSupply = find_data.code + ' - ' + find_data.state;
                            state_code_customer = find_data.code;
                        }
                    }
                }

                // Calculate CGST, SGST, IGST (assuming tax values are calculated properly)
                let cgst = 0, sgst = 0, igst = 0;
                if (state_code && state_code !== '' && state_code_customer && state_code_customer !== '' && state_code_customer != state_code) {
                    igst = totalTax;
                } else {
                    cgst = totalTax / 2;
                    sgst = totalTax / 2;
                }

                // Calculate Paid from sales payment
                const paidAmount = item.sales_payment.reduce((sum, payment) => sum + payment.payment_amount, 0);
                const user = this.currentEmployee.find(opt => opt.id == item.user_id);

                return {
                    'Invoice#': item.invoice_id,
                    Date: this.getFormattedDate(item.current_date),
                    Customer: `${item.customer.first_name || ''} ${item.customer.last_name || ''}`,
                    GSTIN: item.invoice_to === 'b2b' && item.customer.gst_number ? item.customer.gst_number.toUpperCase() : '' || '',
                    Mobile: item.customer.contact_number || '',
                    Amount: totalAmount,  // Total Amount (Sales + Service)
                    'Tax Type': 'Tax Invoice',
                    'Sales Type': item.invoice_to === 'b2b' ? 'B2B' : 'B2C',
                    'Place Of Supply': placeofSupply !== '' ? placeofSupply : placeofSupply_company !== '' ? placeofSupply_company : '',
                    'Invoice Type': item.invoice_type,
                    'Base Amount': totalAmount - totalTax,  // Base Amount without tax
                    'Items Discount': item.discount,  // Assuming item discount from invoice
                    'BT Discount': item.discount,  // Assuming some business discount
                    'Taxable Value': totalAmount - totalTax,  // Amount before tax
                    'IGST': igst,
                    'SGST': sgst,
                    'CGST': cgst,
                    'CESS': 0,  // Assuming no CESS for simplicity, unless stated otherwise
                    'Total Amount': totalAmount,
                    'Grand Total': totalAmount,  // Grand Total after tax
                    'Paid': paidAmount,  // Calculated Paid Amount from sales_payment
                    'Balance': item.due_amount || 0,  // Balance Amount
                    'User': user ? user.name == 'Assign to me' ? this.currentLocalDataList ? this.currentLocalDataList.name : '' : user.name : '',
                    Status: item.status,
                };
            });

            // Prepare company details and style them
            let companyDetails = [];
            if (this.currentCompanyList) {
                companyDetails = [
                    [this.currentCompanyList.company_name || ''],
                    [this.currentCompanyList.address || ''],
                    [this.currentCompanyList.company_phone_no || ''],
                    [this.currentCompanyList.gst_number || ''],
                    [],
                    [this.gstr1form.selectedType !== 'all' ? this.gstr1form.selectedType : 'Sales & Service Report'],
                    [`${this.getFormattedDate(from)} to ${this.getFormattedDate(to)}`],
                    [],
                ];
            }

            // Add a header for the table (to be displayed after the company details)
            const tableHeader = [
                'Invoice#', 'Date', 'Customer', 'GSTIN', 'Mobile', 'Amount', 'Tax Type', 'Sales Type', 'Place Of Supply',
                'Invoice Type', 'Base Amount', 'Items Discount', 'BT Discount', 'Taxable Value',
                'IGST', 'SGST', 'CGST', 'CESS', 'Total Amount', 'Grand Total', 'Paid', 'Balance', 'User', 'Status'
            ];

            // Define styles for company details (center alignment)
            const companyDetailsStyle = {
                alignment: { horizontal: 'center', vertical: 'center' },
            };

            // Define styles for table data (center alignment, border applied to each cell)
            const tableStyle = {
                alignment: { horizontal: 'center', vertical: 'center' },
                border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                }
            };

            const boldStyle = {
                font: { bold: true },
                alignment: { horizontal: 'center', vertical: 'center' },
            };

            // Prepare the Excel sheet
            const wb = XLSX.utils.book_new();

            // Format companyDetails with styles (spanning across all columns)
            const formattedCompanyDetails = companyDetails.map((row, index) => {
                if (index === 5 || index === 6) {
                    // Make the "Sales & Service Report" and date range row span across all columns
                    return [{ v: row[0], s: { ...boldStyle, alignment: { horizontal: 'center', vertical: 'center' } } }];
                }
                return row.map(cell => ({
                    v: cell,
                    s: companyDetailsStyle
                }));
            });

            // Add the table header with bold styling
            const formattedTableHeader = tableHeader.map(header => ({
                v: header,
                s: boldStyle
            }));

            // Format table data with styles (borders only, no bold)
            const formattedDataToExport = dataToExport.map(row => Object.keys(row).map(key => ({
                v: row[key],
                s: tableStyle,
            })));

            // Combine company details, table header, and data in the correct order
            const finalData = [
                ...formattedCompanyDetails,  // Add company details first
                ...[formattedTableHeader],   // Add table header right after company details
                ...formattedDataToExport    // Add table data after header
            ];

            // Convert to sheet
            const ws = XLSX.utils.aoa_to_sheet(finalData);

            // Add merges for all company details rows to span across columns
            ws['!merges'] = [
                { s: { r: 0, c: 0 }, e: { r: 0, c: tableHeader.length - 1 } }, // Merging company name across columns
                { s: { r: 1, c: 0 }, e: { r: 1, c: tableHeader.length - 1 } }, // Merging company address across columns
                { s: { r: 2, c: 0 }, e: { r: 2, c: tableHeader.length - 1 } }, // Merging company phone across columns
                { s: { r: 3, c: 0 }, e: { r: 3, c: tableHeader.length - 1 } }, // Merging company GSTIN across columns
                { s: { r: 5, c: 0 }, e: { r: 5, c: tableHeader.length - 1 } }, // Merging "Sales & Service Report" across columns
                { s: { r: 6, c: 0 }, e: { r: 6, c: tableHeader.length - 1 } }  // Merging report date range across columns
            ];

            // Set column width based on the content length
            const colWidths = tableHeader.map(() => ({ wch: 20 })); // Default width for columns
            ws['!cols'] = colWidths;

            // Append sheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'GSTR-1 Report');

            // Write to Excel file and trigger download
            XLSX.writeFile(wb, `GSTR-1_Report_${from} - ${to}.xlsx`);
        },
        getExcelExportgstr2(data, from, to) {
            if (!data || data.length === 0) {
                this.message = 'No data available to export for GSTR-2!';
                this.type_toaster = 'warning';
                this.show = true;
                return;
            }

            // Mapping the data for the export
            const dataToExport = data.map(item => {
                // Extract the necessary fields from your data
                const purchaseItems = item.purchase_items || [];
                const purchasePayments = item.purchase_payments || [];
                const supplier = this.currentSupplier.find(opt => opt.id == item.supplier_id);
                // Assuming you want specific details in the table like the 'PurchaseOrderNo', 'Supplier', 'Amount', etc.
                return {
                    'GSTIN of Supplier': supplier ? supplier.gst_number ? supplier.gst_number : '' : '',
                    'Invoice Number': item.purchase_order,
                    'Invoice Date': this.getFormattedDate(item.purchase_order_date),
                    'Invoice Value': item.total,
                    'Place of Supply': item.sos,
                    'Supplier Name': item.supplier_name,
                    'Supplier Contact': item.supplier_contact,
                    'Paid': item.paid,
                    'Balance Amount': item.balance_amount,
                    'Payment Type': item.payment_type,
                    'Return Amount': item.return_amount,
                    'Warehouse ID': item.warehouse_id,
                };
            });

            // Prepare company details and style them
            let companyDetails = [];
            if (this.currentCompanyList) {
                companyDetails = [
                    [this.currentCompanyList.company_name || ''],
                    [this.currentCompanyList.address || ''],
                    [this.currentCompanyList.company_phone_no || ''],
                    [this.currentCompanyList.gst_number || ''],
                    [],
                    ['Purchase Report'],
                    [`${this.getFormattedDate(from)} to ${this.getFormattedDate(to)}`],
                    [],
                ];
            }

            // Define the Excel table header
            const tableHeader = [
                'GSTIN of Supplier', 'Invoice Number', 'Invoice Date', 'Invoice Value', 'Place of Supply', 'Supplier Name', 'Supplier Contact', 'Paid',
                'Balance Amount', 'Payment Type', 'Return Amount', 'Warehouse ID'
            ];

            // Set up styles for table headers and rows
            const headerStyle = {
                font: { bold: true },
                alignment: { horizontal: 'center', vertical: 'center' },
                border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                }
            };

            const cellStyle = {
                alignment: { horizontal: 'center', vertical: 'center' },
                border: {
                    top: { style: 'thin' },
                    left: { style: 'thin' },
                    bottom: { style: 'thin' },
                    right: { style: 'thin' }
                }
            };

            // Prepare the Excel sheet
            const wb = XLSX.utils.book_new();
            const sheetData = [];

            // Add company details (spanning across all columns)
            const companyDetailsStyle = {
                alignment: { horizontal: 'center', vertical: 'center' },
                font: { bold: true },
            };

            const formattedCompanyDetails = companyDetails.map((row, index) => {
                if (index === 5 || index === 6) {
                    // Make the "Purchase Report" and date range row span across all columns
                    return [{ v: row[0], s: { ...companyDetailsStyle, alignment: { horizontal: 'center', vertical: 'center' } } }];
                }
                return row.map(cell => ({
                    v: cell,
                    s: companyDetailsStyle
                }));
            });

            // Add header row with bold style
            sheetData.push(tableHeader.map(header => ({
                v: header,
                s: headerStyle
            })));

            // Add data rows
            dataToExport.forEach(row => {
                const formattedRow = Object.keys(row).map(key => ({
                    v: row[key],
                    s: cellStyle
                }));
                sheetData.push(formattedRow);
            });

            // Convert to sheet
            const ws = XLSX.utils.aoa_to_sheet([...formattedCompanyDetails, ...sheetData]);

            // Set column widths based on content length
            const colWidths = tableHeader.map(() => ({ wch: 20 })); // Default width for columns
            ws['!cols'] = colWidths;

            // Append sheet to workbook
            XLSX.utils.book_append_sheet(wb, ws, 'GSTR-2 Report');

            // Write to Excel file and trigger download
            XLSX.writeFile(wb, `GSTR-2_Report_${from} - ${to}.xlsx`);
        },
        formatDate(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();

            // Construct the formatted date string
            const formattedDate = `${day}-${month}-${year}`;

            return formattedDate;
        },

        getFormattedDate(date) {
            // Check for the specific invalid date and format if valid
            return date.substring(0, 10) !== '-000001-11' ? this.formatDate(date) : '';
        },
        getDefaultSelectionData() {
            // Get the current date
            const today = new Date();
            const currentMonth = today.getMonth(); // Get current month (0-11)
            const currentYear = today.getFullYear(); // Get current year

            // Financial year calculation based on current date
            let startYear = currentYear;
            let endYear = currentYear + 1;

            if (currentMonth < 3) {  // For months Jan-Feb-Mar, financial year starts from the previous year
                startYear = currentYear - 1;
                endYear = currentYear;
            }

            const financialYear = `${startYear}-${endYear}`;  // Format: 2023-2024 (for example)

            // Default last month calculation
            const lastMonthDate = new Date(today.setMonth(today.getMonth() - 1));  // Get the last month dynamically
            const lastMonthName = this.allMonths[lastMonthDate.getMonth()].label; // Get the name of the last month

            if (this.selected_option === 'GSTR') {
                ///---GSTR1---
                this.gstr1form.selectedYear = financialYear; // Set to current financial year
                this.gstr1form.selectedMode = 'month'; // Default mode to 'month'
                this.gstr1form.selectedMonth = lastMonthName; // Set to last month dynamically
                this.gstr1form.selectedType = 'all';
                //---GSTR2---
                this.gstr2form.selectedYear = financialYear; // Set to current financial year
                this.gstr2form.selectedMode = 'month'; // Default mode to 'month'
                this.gstr2form.selectedMonth = lastMonthName; // Set to last month dynamically
                //---GSTR3B---
                this.gstr3bform.selectedYear = financialYear;
                this.gstr3bform.selectedMonth = lastMonthName; // Set to last month dynamically
            }
        }
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        const today = new Date().toISOString().substr(0, 10);
        this.maxDate = today;
        this.formValues.from = today;
        this.formValues.to = today;
        this.formValues.selected_category = 'all';
        this.formValues.selected_employee = 'all';
        this.formValues.selected_status = 'all';
        this.formValues.service_type = 'all';
        this.formValues.invoice_group = 'all';
        if (this.selected_option === 'GSTR') {
            this.getDefaultSelectionData();
        }
        this.getSupportingData();
    },
    watch: {
        selected_option: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.formValues.selected_category = 'all';
                    this.formValues.selected_employee = 'all';
                    this.formValues.selected_status = 'all';
                    this.formValues.service_type = 'all';
                    this.data = [];
                    this.$emit('filter-data', []);
                    if (this.selected_option === 'GSTR') {
                        this.getDefaultSelectionData();
                    }
                    this.getSupportingData();
                }
            }
        },
        data: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.$emit('filter-data', newValue);
                }
            }
        },
        'formValues.customer': {
            deep: true,
            handler(newValue) {
                if (newValue === '' || !newValue) {
                    this.formValues.customer_id = '';
                }
                else if ((this.formValues.customer === '' || !this.formValues.customer) && this.formValues.customer_id !== '') {
                    this.formValues.customer_id = '';
                }
            }
        },
        'formValues.supplier': {
            deep: true,
            handler(newValue) {
                if (newValue === '' || !newValue) {
                    this.formValues.supplier_id = '';
                }
                else if ((this.formValues.supplier === '' || !this.formValues.supplier) && this.formValues.supplier_id !== '') {
                    this.formValues.supplier_id = '';
                }
            }
        },
        refresh: {
            deep: true,
            handler(newValue) {
                const today = new Date().toISOString().substr(0, 10);
                this.formValues.from = today;
                this.formValues.to = today;
                this.formValues.customer_id = '';
                this.formValues.customer = '';
                this.formValues.supplier_id = '';
                this.formValues.supplier = '';
                this.formValues.selected_category = 'all';
                this.formValues.selected_employee = 'all';
                this.formValues.selected_status = 'all';
                this.formValues.service_type = 'all';
                this.data = [];
                this.$emit('filter-data', []);
                this.getSupportingData();
            }
        }

    }
};
</script>

<style scoped>
/* Add any custom styles here */
</style>