// store/modules/lead.js

const state = {
    search_query: '',
    search_data: {},
    leads: []
  };

  const mutations = {
    SET_SEARCH_QUERY(state, newValue) {
        state.search_query = newValue;
      },
      SET_SEARCH_DATA(state, newValue){
        state.search_data = newValue;
      },
    SET_LEADS(state, leads) {
      state.leads = leads;
    },
    RESET_STATE(state) {
      state.search_query = '';
      state.search_data = {};
      state.leads = [];
    },
  };

  const actions = {
    fetchLeads({ commit }, leadsData) {
      commit('SET_LEADS', leadsData);
    },
    updateSearch({ commit }, newValue) {
        commit('SET_SEARCH_QUERY', newValue);
    },
    updateData({commit}, newValue){
        commit('SET_SEARCH_DATA', newValue);
    }
  };

  const getters = {
    allLeads(state) {
      return state.leads;
    },
    getSearch(state) {
      return state.search_query;
    },
    getData(state){
        return state.search_data;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
