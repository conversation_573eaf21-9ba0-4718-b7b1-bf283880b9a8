<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 "
        style="z-index: 200;">

        <!-- Modal -->
        <div class="model bg-white sm:w-full lg:w-1/2 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">

            <div class="bg-teal-600 justify-between items-center flex py-2 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-lg capitalize">{{ selected_page.page }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!-- Content based on selected option -->
            <!-- <iframe :src="selected_page.url" class="w-full h-full mb-10" frameborder="0"></iframe> -->
        </div>
    </div>
</template>

<script>

export default {
    name: 'pageModal',
    props: {
        showModal: Boolean,
        selected_page: Object
    },
    data() {
        return {
            isMobile: false,
            isMessageDialogVisible: false,
            message: null,
            isOpen: false,

        }
    },
    methods: {
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },

        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
    },
    mounted() {
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
        // this.handleFocus();
    },
    watch: {
        showModal(newValue) {
            console.log(newValue, 'What happpmmmm');
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
    },
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 999;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

iframe {
    width: 100%;
    height: 100%;
    /* Make iframe fill its container */
    border: none;
    /* Remove iframe border */
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;
        /* Adjust the height as needed */
    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>