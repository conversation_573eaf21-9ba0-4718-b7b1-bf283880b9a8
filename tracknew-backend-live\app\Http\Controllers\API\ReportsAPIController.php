<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Models\Products;
use App\Models\ProductsBarcode;
use App\Models\leads;
use App\Models\LeadsFollows;
use App\Http\Resources\api\LeadResource;
use App\Http\Resources\api\ServiceDataResource;
use App\Http\Resources\api\AmcResource;
use App\Http\Resources\api\SaleResource;
use App\Http\Resources\api\ProformResource;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use DateTime;


class ReportsAPIController extends AppBaseController
{
   
  
    

    public function searchResults(Request $request)
    {
        if (!Auth::check()) {
            return response()->json(['error' => 'Please login to access.'], 401);
        }
        $user = Auth::user();
        $companyId = $user->company_id;
        $isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
        $value = $request->q;
        $filter = $request->query('filter', 'all');
        $customer_id = $request->query('customer_id');
        $employer_id = $request->query('employer_id');
        $category = $request->query('category_id');
        $created_at = $request->query('from_date');
      	$created_at = $request->query('to_date');
        $type = $request->type;

      	switch ($type) {
            case 'leads':
                return $this->searchLeads($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $created_at, $perPage, $page);
            case 'services':
                return $this->searchServices($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $created_at, $perPage, $page);
            case 'amcs':
                return $this->searchAmcs($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page);            
            case 'proformas':
                return $this->searchProformas($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page);
             case 'estimations':
                return $this->searchEstimations($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page);
             case 'expenses':
                return $this->searchExpenses($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page);
             case 'rmas':
                return $this->searchRmas($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page);
            case 'sales':
                return $this->searchSales($request, $user, $companyId, $value, $customer_id, $invoice_id, $perPage, $page);
            default:
                return $this->sendResponse([], 'Invalid search type.');
        }          
    } 
  
  	protected function searchLeads($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $created_at, $perPage, $page)
    {
        $leadsQuery = \App\Models\leads::where('company_id', $companyId);
        if ($value !== '' && isset($value)) {
        
                if ($value === 'all') {
                    $leadsQuery->whereIn('leadstatus_id', ['0', '1', '2', '3', '4', '5']);
                } else {
                    $leadsQuery->where('leadstatus_id', $value);
                }
           
        } 
        else {
       
            $leadsQuery->whereIn('leadstatus_id', ['0', '1', '2', '3', '4', '5']);
        }

        if ($customer_id !== '' && isset($customer_id)) {
        
            $leadsQuery->where('customer_id', $customer_id);
        }

        if ($employer_id !== '' && isset($employer_id)) {
            $leadsQuery->where('assign_to', 'like', '%' . $employer_id . '%');
        }

        if ($category !== '' && isset($category)) {
            $leadsQuery->where('leadtype_id', $category);
        }

       

 

        if ($perPage === 'all') {
            $perPage = $leadsQuery->count();
        }

        $leads = $leadsQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => LeadResource::collection($leads),
            'pagination' => [
                'total' => $leads->total(),
                'per_page' => $leads->perPage(),
                'current_page' => $leads->currentPage(),
                'last_page' => $leads->lastPage(),
                'from' => $leads->firstItem(),
                'to' => $leads->lastItem(),
            ],
        ]);
    }

    protected function searchServices($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $created_at, $perPage, $page)
    {
        $servicesQuery = \App\Models\Services::where('company_id', $companyId);

        if (!$isAdmin) {
            $servicesQuery->whereHas('users', function ($query) use ($user) {
                $query->where('users.id', $user->id);
            });
        }

        if ($employer_id !== '') {
            $servicesQuery->whereHas('users', function ($query) use ($employer_id) {
                $query->where('users.id', $employer_id);
            });
        }

        if ($category !== '') {
            $servicesQuery->where('servicecategory_id', $category);
        }

        if ($customer_id !== '') {
            $servicesQuery->where('customer_id', $customer_id);
        }

        if ($value !== '') {
            if ($value === 'all') {
                $servicesQuery->whereIn('status', ['0', '1', '2', '3', '4', '5', '6', '7', '8']);
            } else {
                $servicesQuery->where('status', $value);
            }
        } else {
            $servicesQuery->whereIn('status', ['0', '1', '2', '3', '4', '5', '6', '7', '8']);
        }

        if ($perPage === 'all') {
            $perPage = $servicesQuery->count();
        }

        $services = $servicesQuery->orderBy('updated_at', 'desc')->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => ServiceDataResource::collection($services),
            'pagination' => [
                'total' => $services->total(),
                'per_page' => $services->perPage(),
                'current_page' => $services->currentPage(),
                'last_page' => $services->lastPage(),
                'from' => $services->firstItem(),
                'to' => $services->lastItem(),
            ],
        ]);
    }

    protected function searchAmcs($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page)
    {
        $amcQuery = \App\Models\Amc::where('company_id', $companyId);

        if ($value !== '') {
            if ($value === 'pending') {
                $amcQuery->whereIn('amc_status', ['0', '1']);
            } else {
                $amcQuery->where('amc_status', $value);
            }
        } else {
            $amcQuery->whereIn('amc_status', ['0', '1', '2']);
        }

        if ($category !== '') {
            $amcQuery->where('amc_payment_type', $category);
        }

        if (!$isAdmin) {
            $amcQuery->whereHas('users', function ($query) use ($user) {
                $query->where('users.id', $user->id);
            });
        }

        if ($employer_id !== '') {
            $amcQuery->whereHas('users', function ($query) use ($employer_id) {
                $query->where('users.id', $employer_id);
            });
        }

        if ($filter !== 'all' && $filter !== '') {
            $amcQuery->whereHas('amcDates', function ($query) use ($filter) {
                switch ($filter) {
                    case 'today':
                        $query->whereDate('date', '<=', now()->toDateString());
                        break;
                    case 'tomorrow':
                        $query->whereDate('date', now()->addDay()->toDateString());
                        break;
                    case 'this_week':
                        $query->whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('date', now()->month);
                        break;
                    case 'this_year':
                        $query->whereYear('date', now()->year);
                        break;
                }
            });
        }

        if ($perPage === 'all') {
            $perPage = $amcQuery->count();
        }

        $amcs = $amcQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
        $statusCounts = $amcQuery->selectRaw('amc_status, count(*) as count')->groupBy('amc_status')->pluck('count', 'amc_status')->toArray();

        return response()->json([
            'success' => true,
            'status_counts' => $statusCounts,
            'data' => AmcResource::collection($amcs),
            'pagination' => [
                'total' => $amcs->total(),
                'per_page' => $amcs->perPage(),
                'current_page' => $amcs->currentPage(),
                'last_page' => $amcs->lastPage(),
                'from' => $amcs->firstItem(),
                'to' => $amcs->lastItem(),
            ],
        ]);
    }
  
    protected function searchProformas($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page)
    {
        $proQuery = \App\Models\Proforma::where('company_id', $companyId);

        if ($value !== '') {
            if ($value === 'all') {
                $proQuery->whereIn('status', ['0', '1']);
            } else {
                $proQuery->where('status', $value);
            }
        } else {
            $proQuery->whereIn('status', ['0', '1']);
        }

       if ($customer_id !== '' && isset($customer_id)) {
        
            $leadsQuery->where('customer_id', $customer_id);
        }
     

     

    

        if ($perPage === 'all') {
            $perPage = $amcQuery->count();
        }

        $amcs = $amcQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
       

        return response()->json([
            'success' => true,
         
            'data' => ProformaResource::collection($amcs),
            'pagination' => [
                'total' => $amcs->total(),
                'per_page' => $amcs->perPage(),
                'current_page' => $amcs->currentPage(),
                'last_page' => $amcs->lastPage(),
                'from' => $amcs->firstItem(),
                'to' => $amcs->lastItem(),
            ],
        ]);
    }
  
    protected function searchEstimations($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page)
    {
        $amcQuery = \App\Models\Amc::where('company_id', $companyId);

        if ($value !== '') {
            if ($value === 'pending') {
                $amcQuery->whereIn('amc_status', ['0', '1']);
            } else {
                $amcQuery->where('amc_status', $value);
            }
        } else {
            $amcQuery->whereIn('amc_status', ['0', '1', '2']);
        }

        if ($category !== '') {
            $amcQuery->where('amc_payment_type', $category);
        }

        if (!$isAdmin) {
            $amcQuery->whereHas('users', function ($query) use ($user) {
                $query->where('users.id', $user->id);
            });
        }

        if ($employer_id !== '') {
            $amcQuery->whereHas('users', function ($query) use ($employer_id) {
                $query->where('users.id', $employer_id);
            });
        }

        if ($filter !== 'all' && $filter !== '') {
            $amcQuery->whereHas('amcDates', function ($query) use ($filter) {
                switch ($filter) {
                    case 'today':
                        $query->whereDate('date', '<=', now()->toDateString());
                        break;
                    case 'tomorrow':
                        $query->whereDate('date', now()->addDay()->toDateString());
                        break;
                    case 'this_week':
                        $query->whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('date', now()->month);
                        break;
                    case 'this_year':
                        $query->whereYear('date', now()->year);
                        break;
                }
            });
        }

        if ($perPage === 'all') {
            $perPage = $amcQuery->count();
        }

        $amcs = $amcQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
        $statusCounts = $amcQuery->selectRaw('amc_status, count(*) as count')->groupBy('amc_status')->pluck('count', 'amc_status')->toArray();

        return response()->json([
            'success' => true,
            'status_counts' => $statusCounts,
            'data' => AmcResource::collection($amcs),
            'pagination' => [
                'total' => $amcs->total(),
                'per_page' => $amcs->perPage(),
                'current_page' => $amcs->currentPage(),
                'last_page' => $amcs->lastPage(),
                'from' => $amcs->firstItem(),
                'to' => $amcs->lastItem(),
            ],
        ]);
    }
  
    protected function searchExpenses($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page)
    {
        $amcQuery = \App\Models\Amc::where('company_id', $companyId);

        if ($value !== '') {
            if ($value === 'pending') {
                $amcQuery->whereIn('amc_status', ['0', '1']);
            } else {
                $amcQuery->where('amc_status', $value);
            }
        } else {
            $amcQuery->whereIn('amc_status', ['0', '1', '2']);
        }

        if ($category !== '') {
            $amcQuery->where('amc_payment_type', $category);
        }

        if (!$isAdmin) {
            $amcQuery->whereHas('users', function ($query) use ($user) {
                $query->where('users.id', $user->id);
            });
        }

        if ($employer_id !== '') {
            $amcQuery->whereHas('users', function ($query) use ($employer_id) {
                $query->where('users.id', $employer_id);
            });
        }

        if ($filter !== 'all' && $filter !== '') {
            $amcQuery->whereHas('amcDates', function ($query) use ($filter) {
                switch ($filter) {
                    case 'today':
                        $query->whereDate('date', '<=', now()->toDateString());
                        break;
                    case 'tomorrow':
                        $query->whereDate('date', now()->addDay()->toDateString());
                        break;
                    case 'this_week':
                        $query->whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('date', now()->month);
                        break;
                    case 'this_year':
                        $query->whereYear('date', now()->year);
                        break;
                }
            });
        }

        if ($perPage === 'all') {
            $perPage = $amcQuery->count();
        }

        $amcs = $amcQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
        $statusCounts = $amcQuery->selectRaw('amc_status, count(*) as count')->groupBy('amc_status')->pluck('count', 'amc_status')->toArray();

        return response()->json([
            'success' => true,
            'status_counts' => $statusCounts,
            'data' => AmcResource::collection($amcs),
            'pagination' => [
                'total' => $amcs->total(),
                'per_page' => $amcs->perPage(),
                'current_page' => $amcs->currentPage(),
                'last_page' => $amcs->lastPage(),
                'from' => $amcs->firstItem(),
                'to' => $amcs->lastItem(),
            ],
        ]);
    }
  
    protected function searchRmas($request, $user, $companyId, $isAdmin, $value, $filter, $customer_id, $employer_id, $category, $perPage, $page)
    {
        $amcQuery = \App\Models\Amc::where('company_id', $companyId);

        if ($value !== '') {
            if ($value === 'pending') {
                $amcQuery->whereIn('amc_status', ['0', '1']);
            } else {
                $amcQuery->where('amc_status', $value);
            }
        } else {
            $amcQuery->whereIn('amc_status', ['0', '1', '2']);
        }

        if ($category !== '') {
            $amcQuery->where('amc_payment_type', $category);
        }

        if (!$isAdmin) {
            $amcQuery->whereHas('users', function ($query) use ($user) {
                $query->where('users.id', $user->id);
            });
        }

        if ($employer_id !== '') {
            $amcQuery->whereHas('users', function ($query) use ($employer_id) {
                $query->where('users.id', $employer_id);
            });
        }

        if ($filter !== 'all' && $filter !== '') {
            $amcQuery->whereHas('amcDates', function ($query) use ($filter) {
                switch ($filter) {
                    case 'today':
                        $query->whereDate('date', '<=', now()->toDateString());
                        break;
                    case 'tomorrow':
                        $query->whereDate('date', now()->addDay()->toDateString());
                        break;
                    case 'this_week':
                        $query->whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()]);
                        break;
                    case 'this_month':
                        $query->whereMonth('date', now()->month);
                        break;
                    case 'this_year':
                        $query->whereYear('date', now()->year);
                        break;
                }
            });
        }

        if ($perPage === 'all') {
            $perPage = $amcQuery->count();
        }

        $amcs = $amcQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
        $statusCounts = $amcQuery->selectRaw('amc_status, count(*) as count')->groupBy('amc_status')->pluck('count', 'amc_status')->toArray();

        return response()->json([
            'success' => true,
            'status_counts' => $statusCounts,
            'data' => AmcResource::collection($amcs),
            'pagination' => [
                'total' => $amcs->total(),
                'per_page' => $amcs->perPage(),
                'current_page' => $amcs->currentPage(),
                'last_page' => $amcs->lastPage(),
                'from' => $amcs->firstItem(),
                'to' => $amcs->lastItem(),
            ],
        ]);
    }

    protected function searchSales($request, $user, $companyId, $value, $customer_id, $invoice_id, $perPage, $page)
    {
        $salesQuery = \App\Models\Sales::where('company_id', $companyId);

        if ($value == 'due') {
            $salesQuery->where('due_amount', '>', 0);
        } elseif ($value == 'success') {
            $salesQuery->where('due_amount', 0);
        }

        if ($customer_id !== '') {
            $salesQuery->where('client_id', $customer_id);
        }

        if ($invoice_id !== '') {
            $salesQuery->where('invoice_id', $invoice_id);
        }

        if ($perPage === 'all') {
            $perPage = $salesQuery->count();
        }

        $sales = $salesQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => SaleResource::collection($sales),
            'pagination' => [
                'total' => $sales->total(),
                'per_page' => $sales->perPage(),
                'current_page' => $sales->currentPage(),
                'last_page' => $sales->lastPage(),
                'from' => $sales->firstItem(),
                'to' => $sales->lastItem(),
            ],
        ]);
    }

}

