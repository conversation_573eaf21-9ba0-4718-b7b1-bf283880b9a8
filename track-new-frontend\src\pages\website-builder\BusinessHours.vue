<template>
    <!-- Company Timings -->
    <div class="builder-page">
        <label class="block font-bold mb-2">Company Timings <span class="text-red-500">*</span></label>
        <div v-for="(timing, index) in websiteData.timings" :key="index"
            class="flex flex-wrap sm:flex-nowrap items-center space-y-2 sm:space-y-0 sm:space-x-2 mb-2">
            <select v-model="timing.day" class="border p-2 rounded w-full sm:w-auto">
                <option value="Mon-Sat">Mon-Sat</option>
                <option value="Sun">Sun</option>
            </select>
            <input type="time" v-model="timing.openTime" class="border p-2 rounded w-full sm:w-auto"
                placeholder="Open Time" />
            <input type="time" v-model="timing.closeTime" class="border p-2 rounded w-full sm:w-auto"
                placeholder="Close Time" />
            <div class="w-full sm:w-auto">
                <input type="checkbox" v-model="timing.isClosed" /> Closed Day
            </div>
        </div>
        <!-- Bottom Navigation Buttons -->
        <NavigationButtons :pageTitle="'Website Settings'" @goToNextPage="goToNextPage" @goToPrevPage="goToPrevPage" />
    </div>
</template>
<script>
import NavigationButtons from '@/components/website-builder/NavigationButtons.vue';

export default {
    props: {
        companyId: {
            type: String,
            required: true
        },
        domain: {
            type: [String, null], // Accepts both String and null
            required: true, // Ensures the prop must be passed
            validator: (value) => {
                return value === null || typeof value === 'string';
            },
        },
        websiteSettingsData: {
            type: Object,
            required: true,
        },
        is_updated: {
            type: Boolean,
            required: true,
        },
        isMobile: {
            type: Boolean,
            required: true,
        }
    },
    components: {
        NavigationButtons
    },
    data() {
        return {
            websiteData: {}, // Initialize with prop data
            domainName: this.domain,
            loading: false,
            //--confirmbox--
            open_confirmBox: false,
            deleteIndex: null,
            //--loader--
            circle_loader_photo: null,
        };
    },
    methods: {
        goToNextPage() {
            this.$emit('updateWebsiteSettings', this.websiteData);
            this.$emit('submitData');
            this.$emit('goToNextPage');
        },
        goToPrevPage() {
            this.$emit('updateWebsiteSettings', this.websiteData);
            this.$emit('submitData'); s
            this.$emit('goToPrevPage'); // Emit event to the parent component
        },
    },
    mounted() {
        if (this.websiteData.is_name === undefined) {
            this.websiteData.is_name = true;
        }
        if (this.websiteData.is_logo === undefined) {
            this.websiteData.is_logo = true;
        }
        if (this.websiteSettingsData && Object.keys(this.websiteSettingsData).length > 0) {
            this.websiteData = { ...this.websiteSettingsData };
        }
    },
    watch: {
        // Watch for changes in websiteSettingsData and update websiteData accordingly
        websiteSettingsData: {
            handler(newData) {
                this.websiteData = { ...newData };
            },
            deep: true,
            immediate: true
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                this.$emit('updateWebsiteSettings', this.websiteData);
            }
        }
    },
}
</script>