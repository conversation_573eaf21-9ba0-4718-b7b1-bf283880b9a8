<template>
    <div v-if="showModal" class="fixed sm:top-0 bottom-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-y-0': isOpen, 'translate-y-full bottom-12': !isOpen }">
            <!-- <div class="modal-content"> -->
            <div v-if="editData !== 'Get Exist Form'">
                <!-- Create form -->
                <!-- <p class="text-lg font-bold text-center underline pb-2">Create {{ editData }} Field</p> -->
                <div class="justify-between items-center mb-5 py-2 flex set-header-background">
                    <h2 class="text-white font-bold text-center ml-5 text-xl">Create / Edit {{ editData }} Field</h2>
                    <p class="close pr-5" @click="cancelModal">&times;</p>
                </div>
                <div class="p-4">
                    <div class="mb-4">
                        <label for="labelName" class="block text-sm font-semibold">Label Name:
                            <span class="text-red-600 px-1">*</span>
                        </label>
                        <input v-model="lableName" type="text" id="labelName" placeholder="Enter the label name"
                            ref="lableName" class="mt-1 p-2 w-full border rounded-md">
                    </div>
                    <div class="mb-4"
                        v-if="editData !== 'Radio' && editData !== 'CheckBox' && editData !== 'Upload' && editData !== 'Dropdown' && editData !== 'Date' && validateIsEdit()">
                        <label for="placeholderText" class="block text-sm font-semibold">Placeholder
                            Text:</label>
                        <input v-model="placeholderText" type="text" id="placeholderText"
                            placeholder="Enter placeholder text" class="mt-1 p-2 w-full border rounded-md">
                    </div>
                    <div class="mb-4 hidden" v-if="!fieldToEdit || (fieldToEdit && fieldToEdit.place === 'div2')">
                        <label for="fieldKey" class="block text-sm font-semibold">Field Key:
                            <span class="text-red-600 px-1">*</span></label>
                        <input v-model="fieldKey" type="text" id="fieldKey"
                            placeholder="Without spaces ex: first_name or firstName or name"
                            class="mt-1 p-2 w-full border rounded-md">
                    </div>
                    <div class="mb-4"
                        v-if="(!fieldToEdit || (fieldToEdit && fieldToEdit.place === 'div2')) && editData !== 'Notes'">
                        <label for="type" class="block text-sm font-semibold">Select type of data:
                            <span class="text-red-600 px-1">*</span>
                        </label>
                        <select v-if="editData === 'Text'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="text">Text</option>
                            <option value="url">URL Link</option>
                        </select>
                        <select v-if="editData === 'Number'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="number">Number</option>
                            <option value="tel">Phone</option>
                        </select>
                        <select v-if="editData === 'Password'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="password">Password</option>
                        </select>
                        <select v-if="editData === 'Email'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="email">Email</option>
                        </select>
                        <select v-if="editData === 'Date'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="date">Date</option>
                            <option value="datetime-local">Date with time for local</option>
                            <option value="month">Only month</option>
                            <option value="time">Only time</option>
                            <option value="week">Only week</option>
                        </select>
                        <select v-if="editData === 'Radio'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="radio">Radio</option>
                        </select>
                        <select v-if="editData === 'CheckBox'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="checkbox">CheckBox</option>
                        </select>
                        <select v-if="editData === 'Upload'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="file">File</option>
                            <option value="multiple">Multiple Files</option>
                        </select>
                        <select v-if="editData === 'Dropdown'" v-model="type" id="type"
                            class="mt-1 p-2 w-full border rounded-md">
                            <option value="single">Single</option>
                            <option value="multiple">Multiple</option>
                        </select>
                    </div>
                    <div class="mb-4"
                        v-if="(!fieldToEdit || (fieldToEdit && fieldToEdit.place === 'div2')) && editData === 'Notes'">
                        <div class="mb-4">
                            <label class="block text-sm font-semibold">Enter Number of Rows expected:
                                <span class="text-red-600 px-1">*</span></label>
                            <input v-model="rowsCount" type="number" id="fieldKey" placeholder="Rows count"
                                class="mt-1 p-2 w-full border rounded-md">
                        </div>
                        <div class="mb-4">
                            <label class="block text-sm font-semibold">Enter Number of Columns expected:
                                <span class="text-red-600 px-1">*</span>
                            </label>
                            <input v-model="colsCount" type="number" id="fieldKey" placeholder="Columns count"
                                class="mt-1 p-2 w-full border rounded-md">
                        </div>
                    </div>
                    <div class="mb-4" v-if="editData === 'Radio' || editData === 'CheckBox' || editData === 'Dropdown'">
                        <label for="option" class="block text-sm font-semibold">Enter the options: <span
                                class="text-red-600 px-1">*</span></label>
                        <textarea rows="5" :placeholder="`Example: \noption1 \noption2 \noption3 \netc`"
                            v-model="option" class="p-1 w-full mt-1 border rounded-md"></textarea>
                    </div>
                    <div class="mb-1">
                        <label class="block text-sm font-semibold">Select Is it Required?:
                            <span class="text-red-600 px-1">*</span>
                        </label>
                        <div class="flex items-center mt-1">
                            <input type="radio" id="yesOption" v-model="required" value="yes" class="mr-2">
                            <label for="yesOption">Yes</label>
                            <input type="radio" id="noOption" v-model="required" value="no" class="ml-4 mr-2">
                            <label for="noOption">No</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-5 mb-4" v-if="editData === 'Get Exist Form'">
                <label class="block text-sm font-semibold">Select Exist form:</label>
                <select id="selectform" v-model="existForm" class="mt-1 p-2 w-full border rounded-md">
                    <option v-for="(val, index) in categories_values" :value="val.id" :key="index">{{ val.name }}
                    </option>
                </select>
            </div>
            <!-- </div> -->

            <!-- Buttons -->
            <div class="flex justify-center items-center m-3">
                <button @click="cancelModal"
                    class="bg-red-600 rounded-[30px] text-white text-sm font-medium p-2 pl-5 pr-5 mr-8 hover:bg-red-500">CANCEL</button>
                <button @click="validateAndSave"
                    class="bg-green-600 rounded-[30px] text-white text-sm font-medium  p-2 pl-10 pr-10 mr-8 hover:bg-green-500">SAVE</button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'create_form_fields',
    props: {
        showModal: Boolean,
        editData: String,
        fieldToEdit: Object,
        existfields: Object,
        categories_values: Object,
    },
    data() {
        return {
            showModal_add_service: false,
            'overlay-active': this.showModal,
            isMobile: false,
            lableName: '',
            placeholderText: '',
            fieldKey: '',
            type: '',
            required: '',
            option: '',
            rowsCount: '',
            colsCount: '',
            existForm: '',
            place: 'div2',
            isOpen: false,
        }
    },
    methods: {
        cancelModal() {
            this.resetTheForm();
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        mounted() {
            this.updateIsMobile();
            window.addEventListener('resize', this.updateIsMobile);
        },
        generateFieldKey() {
            // Auto-generate fieldKey from labelName
            if (this.fieldToEdit === null) {
                this.fieldKey = this.lableName.toLowerCase().replace(/\s+/g, '_');
            }
        },

        validateDuplicateFieldKey() {
            // If no existing fields or the current field key is empty, return true
            if (!this.existfields || !this.fieldKey) {
                return true;
            }
            //--validation with update as unique field key function---
            let newFieldKey = this.fieldKey;
            let counter = 1;

            // Check if the newFieldKey is exactly "comments"
            if (newFieldKey == 'comments') {
                // If the field key is 'comments', handle it as a special case               
                newFieldKey = `comments${counter}`;  // Append counter to "comments" (e.g., "comments1", "comments2", etc.)
                counter++;
            }
            // General case for other field keys
            while (this.existfields.some(field => field.fieldKey === newFieldKey && (!this.fieldToEdit || field.id !== this.fieldToEdit.id))) {
                newFieldKey = `${this.fieldKey}${counter}`;  // Append counter to other field keys (e.g., "apple1", "apple2", etc.)
                counter++;
            }

            if (newFieldKey !== this.fieldKey) {
                // Field key was modified, handle the updated field key
                this.fieldKey = newFieldKey;  // Assign the new unique field key
                // const successMessage = `Field Key automatically updated to "${newFieldKey}" to make it unique.`;
                // this.$emit('openDialog', successMessage);
            }
            return true;
        },
        convertLabelName(data) {
            // console.log(data, 'What about.....');
            // Check if the input string is empty or contains only whitespace characters
            if (!data || /^\s*$/.test(data)) {
                return '';  // Return an empty string or handle it as needed
            }

            // Capitalize the first letter of each word
            return data.replace(/\b\w/g, firstLetter => firstLetter.toUpperCase());
        },
        validateAndSave() {
            // console.log(this.editData, 'What is the current edit data..!');
            // Validate if all required fields are filled
            if (!this.validateDuplicateFieldKey()) {
                return;
            }
            if (this.editData !== 'Radio' && this.editData !== 'CheckBox' && this.editData !== 'Dropdown' && this.editData !== 'Upload' && this.editData !== 'Notes' && this.editData !== 'Get Exist Form') {
                if (this.lableName && this.fieldKey && this.type && this.required) {
                    // All required fields are filled, proceed to save the data
                    // console.log(this.lableName, 'What about data,,,,,,,,,,,,,');
                    const formData = {
                        editData: this.editData,
                        lableName: this.convertLabelName(this.lableName),
                        placeholderText: this.placeholderText,
                        fieldKey: this.fieldKey,
                        type: this.type,
                        required: this.required,
                        enable: true,
                        edit: true,
                        place: this.fieldToEdit ? this.fieldToEdit.place : this.place,
                    };

                    // Now, you can handle the formData, for example, send it to the server or perform any other necessary actions
                    // console.log('Form Data:', formData);
                    // this.$emit('close-modal');
                    // Close the modal
                    this.$emit('close-modal', formData);
                    this.resetTheForm();
                }
                else {
                    if (!this.lableName) {
                        this.$emit('openDialog', 'Please fill the label name');
                    }
                    if (!this.fieldKey) {
                        this.$emit('openDialog', 'Please fill the field key');
                    }
                    if (!this.type) {
                        this.$emit('openDialog', 'Please fill the field type value');
                    }
                    if (!this.required) {
                        this.$emit('openDialog', 'Please fill the required field');
                    }
                    // Display an error message or take other actions when not all required fields are filled
                    // alert('Please fill in all required fields.');
                    // this.$emit('openDialog', 'Please fill in all required fields.');
                }
            } else if (this.editData === 'Radio' || this.editData === 'CheckBox') {
                if (this.lableName && this.fieldKey && this.type && this.required && this.option) {
                    // All required fields are filled, proceed to save the data
                    const formData = {
                        editData: this.editData,
                        lableName: this.convertLabelName(this.lableName),
                        fieldKey: this.fieldKey,
                        type: this.type,
                        required: this.required,
                        option: this.option.split('\n').map(option => option.trim()).filter(option => option !== ''),
                        enable: true,
                        edit: true,
                        place: this.fieldToEdit ? this.fieldToEdit.place : this.place
                    };
                    // Now, you can handle the formData, for example, send it to the server or perform any other necessary actions
                    // console.log('Form Data:', formData);
                    // this.$emit('close-modal');
                    // Close the modal
                    this.$emit('close-modal', formData);
                    this.resetTheForm();
                }
                else {
                    if (!this.lableName) {
                        this.$emit('openDialog', 'Please fill the label name');
                    }
                    if (!this.fieldKey) {
                        this.$emit('openDialog', 'Please fill the field key');
                    }
                    if (!this.type) {
                        this.$emit('openDialog', 'Please fill the field type value');
                    }
                    if (!this.required) {
                        this.$emit('openDialog', 'Please fill the required field');
                    }
                    if (!this.option) {
                        this.$emit('openDialog', 'Please fill the Options data');
                        // console.log(this.option, 'RRRRRRRRRRRRRRRRRRR');
                    }
                    // Display an error message or take other actions when not all required fields are filled
                    // alert('Please fill in all required fields.');
                    // this.$emit('openDialog', 'Please fill in all required fields.');

                }
            } else if (this.editData === 'Upload') {
                if (this.lableName && this.fieldKey && this.type && this.required) {
                    // All required fields are filled, proceed to save the data
                    const formData = {
                        editData: this.editData,
                        lableName: this.convertLabelName(this.lableName),
                        fieldKey: this.fieldKey,
                        type: this.type,
                        required: this.required,
                        enable: true,
                        edit: true,
                        place: this.fieldToEdit ? this.fieldToEdit.place : this.place
                    };
                    // Now, you can handle the formData, for example, send it to the server or perform any other necessary actions
                    // console.log('Form Data:', formData);
                    // this.$emit('close-modal');
                    // Close the modal
                    this.$emit('close-modal', formData);
                    this.resetTheForm();
                }
                else {
                    if (!this.lableName) {
                        this.$emit('openDialog', 'Please fill the label name');
                    }
                    if (!this.fieldKey) {
                        this.$emit('openDialog', 'Please fill the field key');
                    }
                    if (!this.type) {
                        this.$emit('openDialog', 'Please fill the field type value');
                    }
                    if (!this.required) {
                        this.$emit('openDialog', 'Please fill the required field');
                    }

                    // Display an error message or take other actions when not all required fields are filled
                    // alert('Please fill in all required fields.');
                    // this.$emit('openDialog', 'Please fill in all required fields.');
                }
            } else if (this.editData === 'Notes') {
                // console.log('hello......');
                if (this.lableName && this.fieldKey && this.rowsCount && this.colsCount && this.required) {
                    // All required fields are filled, proceed to save the data
                    const formData = {
                        editData: this.editData,
                        lableName: this.convertLabelName(this.lableName),
                        placeholderText: this.placeholderText,
                        fieldKey: this.fieldKey,
                        rowsCount: this.rowsCount,
                        colsCount: this.colsCount,
                        required: this.required,
                        enable: true,
                        edit: true,
                        place: this.fieldToEdit ? this.fieldToEdit.place : this.place
                    };
                    // Now, you can handle the formData, for example, send it to the server or perform any other necessary actions
                    // console.log('Form Data:', formData);
                    // this.$emit('close-modal');
                    // Close the modal
                    this.$emit('close-modal', formData);
                    this.resetTheForm();
                }
                else {
                    if (!this.lableName) {
                        this.$emit('openDialog', 'Please fill the label name');
                    }
                    if (!this.fieldKey) {
                        this.$emit('openDialog', 'Please fill the field key');
                    }
                    if (!this.rowsCount) {
                        this.$emit('openDialog', 'Please fill the rows count value');
                    }
                    if (!this.required) {
                        this.$emit('openDialog', 'Please fill the required field');
                    }
                    if (!this.colsCount) {
                        this.$emit('openDialog', 'Please fill the column count value');
                    }
                    // Display an error message or take other actions when not all required fields are filled
                    // alert('Please fill in all required fields.');
                    // this.$emit('openDialog', 'Please fill in all required fields.');
                }

            } else if (this.editData === 'Dropdown') {
                if (this.lableName && this.fieldKey && this.type && this.required && this.option) {
                    // All required fields are filled, proceed to save the data
                    // console.log(this.convertLabelName(this.lableName), 'jjjjjjjj', this.fieldKey);
                    const formData = {
                        editData: this.editData,
                        lableName: this.convertLabelName(this.lableName),
                        fieldKey: this.fieldKey,
                        type: this.type,
                        required: this.required,
                        option: this.option.split('\n').map(option => option.trim()).filter(option => option !== ''),
                        enable: true,
                        edit: true,
                        place: this.fieldToEdit ? this.fieldToEdit.place : this.place,
                        searchInput: ''
                    };

                    // Now, you can handle the formData, for example, send it to the server or perform any other necessary actions
                    // console.log('Form Data:', formData);

                    // this.$emit('close-modal');

                    // Close the modal
                    this.$emit('close-modal', formData);
                    this.resetTheForm();

                }
                else {
                    if (!this.lableName) {
                        this.$emit('openDialog', 'Please fill the label name');
                    }
                    if (!this.fieldKey) {
                        this.$emit('openDialog', 'Please fill the field key');
                    }
                    if (!this.type) {
                        this.$emit('openDialog', 'Please fill the type field');
                    }
                    if (!this.required) {
                        this.$emit('openDialog', 'Please fill the required field');
                    }
                    if (!this.option) {
                        this.$emit('openDialog', 'Please fill the required options data');
                        // console.log(this.option, 'RRRRRRRRRRRRRRRRRRR');
                    }
                    // Display an error message or take other actions when not all required fields are filled
                    // alert('Please fill in all required fields.');

                }
            }
            else if (this.editData === 'Get Exist Form') {
                // console.log('RRRRRRRRRRRRRR', this.existForm);
                if (this.existForm) {
                    let findData = this.categories_values.find((opt) => opt.id === this.existForm);
                    // console.log(findData, 'UUUUUUI');
                    if (findData) {
                        this.$emit('close-modal', findData.form);
                        this.resetTheForm();
                    }
                }
                else {
                    // alert('Please fill in all required fields.');
                    this.$emit('openDialog', 'Please please select the data.');
                }
            }
        },
        resetTheForm() {
            this.lableName = '';
            this.placeholderText = '';
            this.fieldKey = '';
            this.type = '';
            this.required = '';
            this.option = '';
            this.rowsCount = '';
            this.colsCount = '';
            this.existForm = '';
        },

        // // Add a new method to initialize data when editData is provided
        // initializeData() {
        //     if (this.editData) {

        //     } else {

        //     }
        // },
        //---in edit palceholder filed hidden--
        validateIsEdit() {
            // console.log(this.fieldToEdit && this.fieldToEdit.fieldKey, 'RWRWRWRWRWR wwwwwwwwwwwww');
            if (this.fieldToEdit && this.fieldToEdit.fieldKey) {
                if (this.fieldToEdit.fieldKey === 'additional' || this.fieldToEdit.fieldKey === 'document' || this.fieldToEdit.fieldKey === 'service_expense' || this.fieldToEdit.fieldKey === 'notification') {
                    return false;
                } else {
                    return true;
                }
            } else {
                return true;
            }
        }
    },
    watch: {
        // Watch for changes in the fieldToEdit prop and update data accordingly
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.$nextTick(() => {
                    if (this.$refs.lableName) {
                        this.$refs.lableName.focus();
                        this.$refs.lableName.click();
                    }
                })
            }, 100);
        },
        fieldToEdit: {
            immediate: true,
            handler(newVal) {
                if (newVal) {
                    // Prefill input fields with the data from the field to edit
                    // console.log(newVal.type, 'EEEEEEEE', newVal.required);
                    this.lableName = newVal.lableName;
                    this.placeholderText = newVal.placeholderText;
                    this.fieldKey = newVal.fieldKey;
                    this.type = newVal.type;
                    this.required = newVal.required ? newVal.required : 'no';
                    this.option = newVal.option ? newVal.option.join('\n') : '';
                    this.rowsCount = newVal.rowsCount;
                    this.colsCount = newVal.colsCount;
                } else {
                    this.lableName = '';
                    this.placeholderText = '';
                    this.fieldKey = '';
                    this.type = '';
                    this.required = '';
                    this.option = '';
                    this.rowsCount = '';
                    this.colsCount = '';
                }
            },
        },
        lableName: {
            handler() {
                this.generateFieldKey();
            },
        },
    },
    computed: {
        isFieldReadOnly() {
            return (
                this.fieldKey === 'pre_repair' ||
                this.fieldKey === 'brand' ||
                this.fieldKey === 'device_model' ||
                this.fieldKey === 'problem_title'
            );
        }
    }
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 80vh;
        /* Adjust the height as needed */
    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>