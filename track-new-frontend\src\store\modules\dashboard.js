// store/modules/dashboard.js

const state = {
    dashboard: []
  };

  const mutations = {
    SET_DASHBOARD(state, dashboard) {
      state.dashboard = dashboard;
    },
    RESET_STATE(state) {
      state.dashboard = [];       
    },
  };

  const actions = {
    fetchDashboard({ commit }, dashboard) {    
      commit('SET_DASHBOARD', dashboard);
    }
  };

  const getters = {
    allDashboard(state) {
      return state.dashboard;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
