<?php
namespace App\Http\Controllers\API;

use App\Models\Module;
use App\Models\User;
use App\Models\Companies;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

class UserModuleAPIController extends AppBaseController
{	
  	
  	
    // Assign modules to a user
    public function assignModules(Request $request, $companies_id)
    {
      

       // Validate the input to ensure modules are provided
        $request->validate([
            'modules' => 'required|array', // Expecting an array of module IDs
            'modules.*' => 'exists:modules,id' // Each module must exist in the modules table
        ]);

        // Find the company by the provided companies_id
        $company = Companies::find($companies_id);



        // If the company does not exist, return an error
        if (!$company) {
            return response()->json(['error' => 'Company not found.'], 404);
        }

        // Attach the modules to the company
        $company->modules()->sync($request->modules);
      
    

        return response()->json([
            'message' => 'Modules assigned successfully.',
            'data' => $company->modules
        ]);
    }

    // Revoke a specific module from a user
    public function revokeModule(Companies $company, Module $module)
    {
        $company->modules()->detach($module->id);
      

        return response()->json([
            'message' => 'Module revoked successfully.',
            'data' => $user->modules
        ]);
    }

    // Get all modules assigned to a user
    public function getCompanyModules($companies_id)
    {  
         $company = Companies::find($companies_id);

          // If the company does not exist, return an error
          if (!$company) {
              return response()->json(['error' => 'Company not found.'], 404);
          }

          // Retrieve all modules
          $modules = Module::all();

          // Add hasAccess field to each module
          $modulesWithAccess = $modules->map(function ($module) use ($company) {
              $module->hasAccess = $company->modules->contains($module->id);
              return $module;
          });

          return response()->json([
              'message' => 'Modules retrieved successfully.',
              'modules' => $modulesWithAccess
          ]);
    
    }
  
  	public function getModules(){
      
		$modules = Module::all();
        return response()->json([
          'message' => 'Modules retrieved successfully.',
          'data' => $modules
        ]);
    }

    // Check if a user has access to a specific module
    public function checkUserModule(User $user, Module $module)
    {
        $hasAccess = $user->hasModuleAccess($module->name);

        return response()->json([
            'hasAccess' => $hasAccess,
            'data' => $module->name
        ]);
    }
}
