<template>
    <div :class="{ 'manualStyle text-sm mt-[10px] sm:mt-[85px] mb-[50px]': isMobile, 'text-sm': !isMobile }">
        <div class="my-custom-margin">
            <div class="text-center py-2 mb-3">
                <p class="font-bold text-md inline-block border-b-2 border-gray-400">Stock Adjustment</p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-5">
                <!--date-->
                <div class="items-center sm:flex justify-end">
                    <label class="flex mr-2 font-bold">Date <span class="text-red-700 font-bold">*</span></label>
                    <input type="date" v-datepicker v-model="formValues.date"
                        :readonly="formValues.date !== '' || !formValues.date"
                        class="w-full sm:w-1/2 border py-2 rounded outline-none px-2"
                        :class="{ 'border-blue-700': inputFocussed.date }" @focus="inputFocussed.date = true"
                        @blur="inputFocussed.date = false" />
                </div>
                <!--reference number-->
                <div class="items-center sm:flex">
                    <label class="flex mr-2 font-bold">Reference No:</label>
                    <input type="text" v-model="formValues.reference_no"
                        class="w-full sm:w-1/2 border py-2 rounded outline-none px-2"
                        :class="{ 'border-blue-700': inputFocussed.reference }" @focus="inputFocussed.reference = true"
                        @blur="inputFocussed.date = false" />
                </div>
            </div>
            <div class="grid grid-cols-1 gap-5 mt-7">
                <!--searh product-->
                <div class="flex ml-0 mr-0 sm:ml-10 sm:mr-10 lg:ml-[200px] lg:mr-[200px]"
                    @mouseover="tooltip.item = true" @mouseleave="tooltip.item = false">
                    <div class="border border-r-0 py-2 px-2"><img :src="barcode_icon" alt="label icon"
                            class="w-[20px] h-[20px] justify-center items-center" /></div>
                    <!---search item--->
                    <input class="flex border justify-between py-2 px-2 w-full outline-none rounded-none" type="text"
                        v-model="selected_item" ref="selectItemField" @input="filterProducts()"
                        @keydown.enter="handleEnterKey()" @keydown.down.prevent="handleDownArrow(filteredProductList)"
                        @keydown.up.prevent="handleUpArrow(filteredProductList)"
                        @focus="inputFocussed.selected_item = true" @blur="blurItemDropdown"
                        :class="{ 'border-blue-600': inputFocussed.selected_item }" placeholder="Item name / barcode" />
                    <!--Add new-->
                    <div class="border border-l-0 py-2 px-2 cursor-pointer" @click="addNewItemModal"><img
                            :src="add_item_icon" alt="add item" class="w-[20px] h-[20px] justify-center items-center" />
                    </div>
                    <!--Item dropdown-->
                    <ul v-if="productDropdown && selected_item && selected_item.length > 1 && !showModalProduct"
                        class="absolute mt-10 ml-8 sm:ml-8 lg:ml-9 w-1/4 max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                        style="z-index: 150;" :style="{ width: $refs.selectItemField.offsetWidth + 'px' }"
                        @mouseover="mouseIsOnOption(true)" @mouseleave="mouseIsOnOption(false)">
                        <li class="hover:bg-gray-300 px-3 py-1 border" v-for="(product, i) in filteredProductList"
                            :key="i" :class="{ 'bg-gray-200': i === selectedIndex }"
                            @click="selectProduct(index, product)">
                            {{ product.products.product_name + ' - ' + (product.barcodes.barcode ?
                                product.barcodes.barcode
                                :
                                '') + ' - ' + (product.total_qty ? product.total_qty : '') }}
                        </li>
                        <li v-if="filteredProductList.length === 0 && selected_item && selected_item.length > 1 && findExistItem()"
                            @click="addNewItemModal"
                            class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">+
                            New Product</li>
                    </ul>
                    <!---tooltip-->
                    <div v-if="tooltip.item"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8 ml-[100px] lg:ml-[150px]">
                        <span
                            class="relative rounded-md z-10 px-2 py-2  leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Select Item</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                </div>
            </div>
            <!---table-->
            <div class="mt-5 overflow-x-auto table-container">
                <table class="table w-full text-sm">
                    <thead>
                        <tr class="bg-gray-300">
                            <th class="px-2 py-2 border border-white">Item Name</th>
                            <th class="px-2 py-2 border border-white">Quantity</th>
                            <th class="border border-white non-printable">X</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-if="items.length > 0" v-for="(item, index) in items" :key="index" class="text-center">
                            <!--item name-->
                            <td class="border relative" @mouseover="tooltip['product_name' + index] = true"
                                @mouseleave="tooltip['product_name' + index] = false">
                                <!---tooltip-->
                                <div v-if="tooltip['product_name' + index]"
                                    class="absolute flex flex-col items-center group-hover:flex -mt-8">
                                    <span
                                        class="relative rounded-md z-10 px-2 py-2  leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                        <p>Edit ?</p>
                                    </span>
                                    <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                </div>
                                <div class="flex justify-center items-center cursor-pointer w-full"
                                    @click="openDescriptionModel(item)">
                                    <p class="font-bold hover:text-gray-500">{{ item.products.product_name }}</p>
                                    <img :src="edit_item_icon" class="w-[15px] h-[15px] ml-2" />
                                </div>
                            </td>
                            <!--quantity-->
                            <td class="py-2 px-2 border">
                                <div class="flex justify-center items-center">
                                    <div @click="removeQuantity(index)"
                                        class="cursor-pointer border flex justify-center items-center text-[20px] px-3 py-1 font-bold bg-gray-200 hover:bg-gray-300 text-red-700 ">
                                        -</div>
                                    <input type="number" v-model="item.total_qty"
                                        class="px-2 text-center py-1 outline-none border w-1/4 rounded-none"
                                        @focus="inputFocussed['quantity' + index] = true"
                                        @blur="inputFocussed['quantity' + index] = false"
                                        :class="{ 'border-blue-600': inputFocussed['quantity' + index] }" />
                                    <div @click="addQuantity(index)"
                                        class="cursor-pointer border flex justify-center text-[20px] px-2 py-1 font-bold bg-gray-200 hover:bg-gray-300 text-green-700 ">
                                        +</div>
                                </div>
                            </td>

                            <!--delete row button-->
                            <td class="non-printable actions-column">
                                <button @click="deleteRow(index)"
                                    class="text-red-700 font-bold cursor-pointer hover:bg-gray-100 w-[20px]">
                                    <img :src="del_icon" alt="delete icon" class="w-6 h-6" />
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!--Total quantity-->
            <div class="m-2 sm:m-5">
                <div class="flex items-center">
                    <p class=" font-bold">Total Quantities:</p>
                    <p class="text-lg font-bold text-green-700 ml-4">{{ totalQuantity }}.00</p>
                </div>
                <div class="flex items-center mt-5">
                    <label class=" font-bold mr-5">Note</label>
                    <textarea class="border rounded lg:w-1/4 w-1/2 outline-none px-2 py-1"
                        :class="{ 'border-blue-600': inputFocussed.note }" rows="2" @focus="inputFocussed.note = true"
                        @blur="inputFocussed.note = false"></textarea>
                </div>
            </div>
            <!--buttons-->
            <div class="flex justify-center mt-5">
                <button @click="goBack"
                    class="border rounded font-normal text-white px-3 py-2 bg-red-700 hover:bg-red-600 mr-2">Cancel</button>
                <button @click="toSaveStock"
                    class="border rounded font-normal text-white px-3 py-2 bg-green-700 hover:bg-green-600 ml-2">Save</button>
            </div>
        </div>
        <!---confirm box-->
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <!--add new item-->
        <addNewItem :showModal="open_add_newItem" @close-modal="closeItemModal" :product_name="selected_item">
        </addNewItem>
        <!--product description-->
        <posProductionDes :showModal="open_description_item" @close-modal="closeDescriptionModal"
            :item_data="change_description_data"></posProductionDes>
        <!--message-->
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'stock'"></bottombar> -->
    </div>
</template>
<script>
import addNewItem from '../../dialog_box/addNewItem.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import posProductionDes from '../../dialog_box/posProductionDes.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
// import bottombar from '../../dashboard/bottombar.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
    components: {
        addNewItem,
        confirmbox,
        posProductionDes,
        dialogAlert,
        // bottombar
    },
    props: {
        isMobile: Boolean,
        store_refresh: Boolean
    },
    data() {
        return {
            formValues: {},
            inputFocussed: {},
            tooltip: {},
            barcode_icon: '/images/pos/barcode.png',
            add_item_icon: '/images/pos/plus.png',
            del_icon: '/images/service_page/del.png',
            edit_item_icon: '/images/pos/edit.png',
            //---items data
            selected_item: '',
            product: [],
            productDropdown: false,
            filteredProductList: [],
            items: [],
            isMouseInOption: false,
            selectedIndex: 0,
            //--item model--
            showModalProduct: false,
            //--modal--
            open_add_newItem: false,
            open_confirmBox: false,
            //---change description
            open_description_item: false,
            change_description_data: null,
            companyId: null,
            userId: null,
            open_message: false,
            message: '',
            get_backup: [],
            pagination: {}
        }
    },
    methods: {
        ...mapActions('items', ['fetchItemList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates']),
        filterProducts() {
            const enteredProductName = this.selected_item.toLowerCase();
            this.productDropdown = true;
            // console.log(enteredProductName, 'Product name');
            if (this.product.length !== 0 && enteredProductName.length > 0) {

                this.filteredProductList = this.product.filter(opt => {
                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.products && opt.products.product_name ? opt.products.product_name.toLowerCase().includes(enteredProductName) : false;
                    const codeMatch = opt.barcodes && opt.barcodes.barcode ? opt.barcodes.barcode.toLowerCase().includes(enteredProductName) : false;
                    return nameMatch || codeMatch;
                });
                if (this.filteredProductList.length === 0 && this.pagination && Number(this.pagination.current_page) !== this.pagination.last_page) {
                    this.getProductDetails(Number(this.pagination.current_page) + 1, 1000);
                }
            }
        },
        //---add product option controll--
        findExistItem() {
            const enteredProductName = this.selected_item.toLowerCase();
            if (this.product.length !== 0 && enteredProductName.length > 1) {
                const existingProducts = this.product.map(item => item.products.product_name.toLowerCase());
                const existingProductCodes = this.product.map(item => item.barcodes && item.barcodes.barcode.toLowerCase());

                // Use a separate array to store existing product names and codes
                const existingNamesAndCodes = [...existingProducts, ...existingProductCodes];

                // Check if the entered product name or product code matches any existing product name or product code
                const isExisting = existingNamesAndCodes.includes(enteredProductName);
                // console.log(isExisting, 'Waht happening....!');
                return !isExisting;
            }
            return true;
        },
        selectProduct(index, selectedProduct) {
            //--validate duplicate--
            // console.log('validation is done...1');
            let selectIndex = null;
            let validateDuplicate = this.items.map((item, i) => {
                if (item.product_id === selectedProduct.product_id && item.barcode_id === selectedProduct.barcode_id) {
                    selectIndex = i;
                    return true;
                }
            });
            if (selectIndex !== null && selectIndex >= 0) {
                // let total_stock = this.items[selectIndex].total_qty - this.items[selectIndex].sold_stocks;
                // if (total_stock > this.items[selectIndex].quantity) {
                this.items[selectIndex] = { ...this.items[selectIndex], total_qty: this.items[selectIndex].total_qty + 1 };
                this.selected_item = '';
                // } else {
                //     alert('low stock');
                // }
                //---reset---
                validateDuplicate = [];
                selectIndex = null;
            } else {
                let get_data = {
                    ...selectedProduct,
                    total_qty: 0,
                };
                // selectedProduct.quantity = 1;
                // selectedProduct.discount = 1;
                this.items.push(get_data);
                this.get_backup.push(selectedProduct);
                this.selected_item = '';

            }
            // this.updatePrice(this.items.length - 1);
            this.productDropdown = false;
            // this.productDropdown = false;           
            this.filteredProductList = []; // Clear the filtered list after selecting a product.
            this.focusItemFields();
        },
        //--always focus item fields--
        focusItemFields() {
            this.$nextTick(() => {
                if (this.$refs.selectItemField) {
                    this.$refs.selectItemField.focus();
                }
            });
        },

        //---blur item dropdown--
        blurItemDropdown() {
            this.inputFocussed.selected_item = false;
            if (!this.isMouseInOption) {
                this.productDropdown = false;
            }
        },
        mouseIsOnOption(value) {
            this.isMouseInOption = value;
        },
        //----calculate--
        validateQuantity(index) {
            let total_stock = this.items[index].total_qty - this.items[index].sold_stocks;
            if (!(total_stock > this.items[index].total_qty)) {
                console.log('hello');
                this.items[index].total_qty = total_stock;
                this.calculateTotal(index);
                alert('low stock');
            }
        },
        addQuantity(index) {
            this.items[index].total_qty = this.items[index].total_qty + 1;

        },
        removeQuantity(index) {
            if (this.items[index].total_qty >= 1) {
                this.items[index].total_qty = this.items[index].total_qty - 1;
            }
        },
        //----table--
        handleEnterKey(index, type, product_name) {
            if (this.filteredProductList.length > 0) {
                // Call selectedProductData with the first item in filteredProductList
                this.selectProduct(index, this.filteredProductList[this.selectedIndex]);
                this.selectedIndex = 0;
            } else {
                this.addNewItemModal();
            }
        },
        //---arrow on key press
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //---add new item--
        addNewItemModal() {
            this.open_add_newItem = true;
            this.productDropdown = false;
        },
        closeItemModal() {
            this.open_add_newItem = false;
        },
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.items.splice(this.deleteIndex, 1);
            } else if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                // Remove the row at the specified index
                this.paymentRows.splice(this.deleteIndex, 1);
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },

        deleteRow(index) {
            // this.formData.selectedTax.splice(index, 1);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        //--change date format--
        changeDateFormat(newValue) {
            const dateObject = new Date(newValue);
            if (!isNaN(dateObject.getTime())) { // Check if it's a valid date
                const year = dateObject.getFullYear();
                let month = (1 + dateObject.getMonth()).toString().padStart(2, '0');
                let day = dateObject.getDate().toString().padStart(2, '0');
                this.formValues.date = `${year}-${month}-${day}`; // Format the date as yyyy-mm-dd
            }
        },
        //---Item description modal---
        openDescriptionModel(record) {
            this.change_description_data = record;
            this.open_description_item = true;
        },
        closeDescriptionModal() {
            this.open_description_item = false;
        },
        goBack() {
            this.$router.go(-1); // Go back one step in history
        },
        //--get product list ----
        getProductDetails(page, per_page) {
            axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'RRRRRRRRR');
                    // Filter the response data to include only items where product_type is 'Product'
                    const filteredProducts = response.data.data.filter(product => product.products.product_type === 'Product');
                    // console.log(filteredProducts, 'RRRTTTT');
                    // Assign the filtered products to productList
                    this.product = filteredProducts;
                    this.pagination = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error response', error);
                })
        },
        getDateData() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0'); // January is 0!
            const day = String(today.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        toSaveStock() {
            if (this.items.length > 0) {
                this.items.map((opt, i) => {
                    if (opt.id) {
                        let sent_data = {
                            total_qty: opt.total_qty, product_name: opt.products.product_name, hsn_code: opt.products.hsn_code, product_code: opt.products.product_code, unit: opt.products.unit, category_id: opt.products.category_id,
                            barcode_id: opt.barcode_id, product_id: opt.product_id, barcode: opt.barcodes.barcode,
                        };
                        axios.put(`/stock_update/${opt.id}`, { ...sent_data, company_id: this.companyId, user_id: this.userId })
                            .then(response => {
                                // console.log('waht about response', response.data);
                                this.response_data = response.data.data;
                                // this.closeModal(this.response_data);
                                if (this.items.length - 1 === i) {
                                    this.message = response.data.message;
                                    this.open_message = true;
                                    this.items = [];
                                    this.get_backup = [];
                                    this.formValues = { date: this.getDateData() };
                                    this.getProductDetails(1, 1000);
                                    // setTimeout(() => {
                                    // this.goBack();
                                    // }, 500)
                                }
                            })
                            .catch(error => {
                                console.error('Error itm post', error);
                            })
                    }
                })
            }
        },
        closeMessage() {
            this.open_message = false;
            this.message = '';
        }
    },
    mounted() {
        this.fetchApiUpdates();
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.getProductDetails(1, 2);
        //---get items-list
        if (this.currentItems && this.currentItems.length > 0) {
            const filteredProducts = this.currentItems.filter(product => product.products.product_type === 'Product');
            this.product = filteredProducts;
            this.pagination = this.currentItemsPagination;
            this.fetchItemList(this.pagination && Object.keys(this.pagination).length > 0 ? (Number(this.pagination.total) + 50) : 1000);
        } else {
            this.fetchItemList(this.pagination && Object.keys(this.pagination).length > 0 ? (Number(this.pagination.total) + 50) : 1000);
        }
        if (this.type !== 'edit') {
            this.formValues.date = this.getDateData();
        }
        window.addEventListener('resize', this.updateIsMobile);
    },
    computed: {
        ...mapGetters('items', ['currentItems', 'currentItemsPagination']),
        //--total quantity---
        totalQuantity() {
            return this.items.reduce((sum, item) => sum + item.total_qty, 0);
        },
    },
    watch: {
        type: {
            deep: true,
            handler(newValue) {
                if (this.type !== 'edit') {
                    this.formValues.date = this.getDateData();
                }
            }
        },
        currentItems: {
            deep: true,
            handler(newValue) {
                const filteredProducts = newValue.filter(product => product.products.product_type === 'Product');
                this.product = filteredProducts;
                this.pagination = this.currentItemsPagination;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                if (this.type !== 'edit') {
                    this.formValues.date = this.getDateData();
                }
                this.fetchItemList(this.pagination && Object.keys(this.pagination).length > 0 ? (Number(this.pagination.total) + 50) : 1000);
            }
        }
    }
}
</script>