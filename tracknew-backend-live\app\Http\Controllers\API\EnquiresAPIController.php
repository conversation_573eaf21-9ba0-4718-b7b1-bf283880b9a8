<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateEnquiresAPIRequest;
use App\Http\Requests\API\UpdateEnquiresAPIRequest;
use App\Models\Enquires;
use App\Repositories\EnquiresRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;
use Auth;
/**
 * Class EnquiresController
 * @package App\Http\Controllers\API
 */

class EnquiresAPIController extends AppBaseController
{
    /** @var  EnquiresRepository */
    private $enquiresRepository;

    public function __construct(EnquiresRepository $enquiresRepo)
    {
      	
        $this->enquiresRepository = $enquiresRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/enquires",
     *      summary="getEnquiresList",
     *      tags={"Enquires"},
     *      description="Get all Enquires",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Enquires")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        // Retrieve 'company_id' from query parameters
        $companyId = $request->query('company_id');

        // Check if 'company_id' is provided
        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        // Get 'per_page' and 'page' from query parameters with default values
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        // Get the authenticated user
        $user = Auth::user();
        if (!$user) {
            return $this->sendError('Please login to access.', 400);
        }

        
            // Admin users can see all enquires within the company
        $enquiresQuery = Enquires::where('company_id', $companyId);
      	if (!$enquiresQuery->exists()) {
            return response()->json([
                'success' => true,
                'message' => 'No enquiries found for the given company ID.'
            ]);
        }
        

        // If 'per_page' is 'all', retrieve all records
        if ($perPage === 'all') {
            $perPage = $enquiresQuery->count();
        }

        // Fetch the paginated results
        $enquires = $enquiresQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
      
      	$statusLabels = [
            0 => 'Created',
            1 => 'Converted',
            2 => 'Cancel',
        ];
      	
      	 $statusCounts = Enquires::select('status', \DB::raw('COUNT(*) as count'))
                         ->groupBy('status')
                         ->pluck('count', 'status');

        
    
		$statusCountsFormatted = [];
        foreach ($statusCounts as $status => $count) {
            $statusCountsFormatted[$statusLabels[$status]] = $count;
        }
     
        // Build the response
        $response = [
            'success' => true,
            'status' => $statusCountsFormatted,
            'data' => $enquires->items(), // Use a Resource for consistent API responses
            'pagination' => [
                'total' => $enquires->total(),
                'per_page' => $enquires->perPage(),
                'current_page' => $enquires->currentPage(),
                'last_page' => $enquires->lastPage(),
                'from' => $enquires->firstItem(),
                'to' => $enquires->lastItem(),
            ],
        ];
        return response()->json($response);
      
    }


    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/enquires",
     *      summary="createEnquires",
     *      tags={"Enquires"},
     *      description="Create Enquires",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Enquires"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateEnquiresAPIRequest $request)
    {
        $input = $request->all();

        $enquires = $this->enquiresRepository->create($input);

        return $this->sendResponse($enquires->toArray(), 'Enquires saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/enquires/{id}",
     *      summary="getEnquiresItem",
     *      tags={"Enquires"},
     *      description="Get Enquires",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Enquires",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Enquires"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Enquires $enquires */
        $enquires = $this->enquiresRepository->find($id);

        if (empty($enquires)) {
            return $this->sendError('Enquires not found');
        }

        return $this->sendResponse($enquires->toArray(), 'Enquires retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/enquires/{id}",
     *      summary="updateEnquires",
     *      tags={"Enquires"},
     *      description="Update Enquires",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Enquires",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Enquires"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateEnquiresAPIRequest $request)
    {
        $input = $request->all();

        /** @var Enquires $enquires */
        $enquires = $this->enquiresRepository->find($id);

        if (empty($enquires)) {
            return $this->sendError('Enquires not found');
        }

        $enquires = $this->enquiresRepository->update($input, $id);

        return $this->sendResponse($enquires->toArray(), 'Enquires updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/enquires/{id}",
     *      summary="deleteEnquires",
     *      tags={"Enquires"},
     *      description="Delete Enquires",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Enquires",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Enquires $enquires */
        $enquires = $this->enquiresRepository->find($id);

        if (empty($enquires)) {
            return $this->sendError('Enquires not found');
        }

        $enquires->delete();

        return $this->sendSuccess('Enquires deleted successfully');
    }
}
