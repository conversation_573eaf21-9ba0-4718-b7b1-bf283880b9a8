<template>
    <div class="m-2 mx-auto text-xs w-full px-5">
        <div class="flex justify-between items-center border-l border-r border-t pl-2 pt-2 pb-2">
            <div class="w-full relative" title="Change image" @click="openFileInput" @mouseover="isHovered = true"
                @mouseleave="isHovered = false">
                <img :src="logo_img" class="w-48 h-24  object-contain" :class="{ 'filter': isHovered }" />
                <input ref="fileInput" type="file" style="display: none" @change="handleImageChange" />
                <div class="absolute inset-0 flex mt-6 ml-5" v-show="isHovered">
                    <span class="text-white text-sm font-bold">Change Image</span>
                </div>
            </div>
            <div class="justify-end">
                <div class="flex">
                    <p class="font-semibold">
                        {{ typeOfInvoice === 'estimation' ? 'Estimation No:' : 'Invoice No:' }}
                    </p>
                    <!-- <p class="ml-1 font-bold">{{ invoiceNum ? invoiceNum : '' }}</p> -->
                    <input type="text" v-model="selectedInvoice_type.prefix" class="ml-1 font-semibold w-1/2"
                        placeholder="Invoice Number" @input="handleInvoiceNum" />
                </div>
                <div class="flex">
                    <p class="font-semibold">Date:</p>
                    <p class="ml-1">{{ currentDate }}</p>
                </div>
            </div>
        </div>
        <div class="flex justify-center items-center border" :style="{ backgroundColor: formData.color }">
            <div class="flex justify-center items-center">
                <p class="text-[26px] font-bold py-2" :style="{ color: formData.text_color }">
                    {{ typeOfInvoice === 'estimation' ? 'Estimation' : 'Invoice' }}
                </p>
            </div>
            <div v-if="typeOfInvoice !== 'estimation'" class="absolute right-[25px]">
                <select v-model="invoice_type" class="border-none font-bold non-printable outline-none"
                    :style="{ backgroundColor: formData.color, color: formData.text_color }">
                    <option value="b2c">B 2 C</option>
                    <option value="b2b">B 2 B</option>
                </select>
            </div>
        </div>
        <div class="flex justify-between items-center border-l border-r pl-2 pt-2 pb-2 overflow-x-auto">
            <div class="items-center">
                <p><strong>From:</strong></p>
                <p class="font-bold">{{ formData.name }}</p>
                <p>{{ formData.address }}</p>
                <p v-if="formData.email !== ''"><strong>Email:</strong> {{ formData.email }}</p>
                <p v-if="formData.businessContact !== ''"><strong>Contact:</strong> {{ formData.business_contact }}</p>
                <p v-if="formData.gstNumber !== ''"><strong>GST:</strong> {{ formData.gst_number }}</p>
            </div>
            <div class="w-1/3 items-center">
                <!--select customer-->
                <div v-if="!client_data" class="non-printable">
                    <div class="relative">
                        <input placeholder="Select customer" v-model="selectCustomer"
                            @input="handleDropdownInput(selectCustomer)" @focus="isDropdownOpen = true"
                            @keydown.enter="handleEnterKey(null, 'customer')" ref="selectCustomerInput"
                            class="w-full border py-2 pl-3 pr-10 leading-5 text-gray-900 focus:ring-0 rounded"
                            @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" />

                        <!-- Right-aligned icon based on isDropdownOpen -->
                        <span class="absolute right-2 top-2 cursor-pointer" @click="isDropdownOpen = !isDropdownOpen">
                            <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </span>
                    </div>
                    <!-- Display filtered options as the user types -->
                    <div v-if="isDropdownOpen && selectCustomer && selectCustomer.length > 0"
                        class="absolute w-1/4 mt-1 max-h-60 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                        <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                            @click="selectDropdownOption(option)" class="cursor-pointer hover:bg-gray-100 p-2 border-b"
                            :class="{ 'bg-gray-200': index === selectedIndex }">{{
                                option.firstName }} - {{ option.lastName }} - {{ option.contactNumber }}
                        </p>
                        <!-- Add New Customer button -->
                        <button v-if="filteredCustomerOptions.length === 0" @click="openModal(selectCustomer)"
                            class="w-full text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                            + Add Customer
                        </button>
                    </div>
                </div>
                <div v-if="client_data">
                    <p><strong>To:</strong></p>
                    <p class="font-bold">{{ client_data.firstName ? client_data.firstName : '' + ' ' +
                        client_data.lastName
                        ?
                        client_data.lastName : '' }}</p>
                    <p v-if="client_data.address">{{ client_data.address }}</p>
                    <p v-if="client_data.email"><strong>Email:</strong> {{ client_data.email }}</p>
                    <p><strong>Contact:</strong> {{ client_data.contactNumber }}</p>
                    <p v-if="invoice_type === 'b2b'"><strong>GST Tin: </strong><input type="text"
                            v-model="client_data['gstNumber']" pleaceholder="Enter client GST Tin number"
                            @input="validateGST" class="border-none outline-none" :class="{ 'outline': isFocused }"
                            @focus="handleFocus" @blur="handleBlur" />
                        <span v-if="client_data.gstNumber !== '' && gstValidation !== ''"
                            class="text-red-700 text-xs absolute block">{{ gstValidation }}</span>
                    </p>
                </div>
            </div>
        </div>
        <div class="w-full overflow-x-auto">
            <table class="w-full border">
                <thead>
                    <tr class="font-bold text-16px border py-2"
                        :style="{ backgroundColor: formData.color, color: formData.text_color }">
                        <th>Sr.no</th>
                        <th class="p-1">Description</th>
                        <th>HSN Code</th>
                        <th>Qty</th>
                        <th>Price</th>
                        <th>
                            <select v-model="selectedTax" @change="calculateTax"
                                :style="{ backgroundColor: formData.color, color: formData.text_color }">
                                <option v-for="(opt, index) in formData.selectedTax" :key="index" :value="opt.value"
                                    :selected="opt.status">
                                    {{ opt.tax_name }}
                                </option>
                            </select>
                        </th>
                        <th>Tax Value</th>
                        <th>Total</th>
                        <th class="non-printable actions-column" v-if="!print_hidden"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-if="(typeOfInvoice !== 'sales' && typeOfInvoice !== 'estimation') && items.length > 0"
                        class="text-center">
                        <!--Sr.no-->
                        <td class="p-1 border">1</td>
                        <!--description-->
                        <td class="p-1 border w-1/3">
                            <textarea v-model="items[0].description" class="w-full p-1 text-center" rows="4"
                                @click="productDropdownIndex = false"></textarea>
                        </td>
                        <!--HSN code-->
                        <td class="p-1 border">
                            <input v-model.number="items[0].hsnCode" class="w-full p-1 text-center" type="text" />
                        </td>
                        <!--Quantity-->
                        <td class="p-1 border">
                            <input v-model.number="items[0].qty" @input="updateTotal" class="w-full p-1 text-center"
                                type="number" />
                        </td>
                        <!--Price-->
                        <td class="p-1 border">
                            <input v-model.number="items[0].price" class="w-full p-1 text-center" type="number"
                                @input="updateTotal" />
                        </td>
                        <!--tax-->
                        <td class="p-1 border">
                            <select v-model="items[0].tax" @change="calculateTax">
                                <option v-for="(opt, index) in formData.selectedTax" :key="index" :value="opt.value"
                                    :selected="opt.status">
                                    {{ opt.value }} %
                                </option>
                            </select>
                        </td>
                        <!--tax value-->
                        <td class="p-1 border">
                            <input v-model.number="items[0].taxvalue" class="w-full p-1 text-center" type="number"
                                readonly />
                        </td>
                        <!--total value-->
                        <td class="p-1 border">
                            <input v-model.number="items[0].total" class="w-full p-1 text-center" type="number"
                                @input="updatePrice(0)" :id="'input-total-' + 0" @keyup.enter="calculateTotal(0)" />
                        </td>
                        <!--delete row button-->
                        <!-- <td v-if="!print_hidden" class="p-1 border non-printable">
                            <button v-if="index !== 0" @click="deleteRow(index)"
                                class="text-red-700 font-bold cursor-pointer hover:bg-gray-100">
                                <img :src="del_icon" alt="delete icon" class="w-6 h-6" />
                            </button>
                        </td> -->
                    </tr>
                    <tr v-if="typeOfInvoice !== 'sales' && typeOfInvoice !== 'estimation' && items.length > 1">
                        <td colspan="8" class="font-bold py-1 px-3">Additional Materials:</td>
                    </tr>

                    <!---add extra materials-->
                    <tr v-if="items.length > 0" v-for="(item, index) in items" :key="index" class="text-center"
                        :class="{ 'hidden': index === 0 && typeOfInvoice !== 'sales' && typeOfInvoice !== 'estimation' }">
                        <!--Sr.no-->
                        <td class="p-1 border">
                            {{ index + 1 }}
                        </td>
                        <!--description-->
                        <td class="p-1 border w-1/3 relative">
                            <input v-model="item.description" class="w-full p-1 text-center"
                                @input="filterProducts(index)" @focus="productDropdownIndex = index"
                                @keydown.enter="handleEnterKey(index, 'product')"
                                @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                @keydown.up.prevent="handleUpArrow(filteredProductList)"
                                :ref="'productNameInputs' + index" />
                            <ul v-if="productDropdownIndex === index && item.description.length > 2 && !showModalProduct"
                                class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                style="z-index: 150;">
                                <li class="hover:bg-gray-300" v-for="(product, i) in filteredProductList" :key="i"
                                    :class="{ 'bg-gray-200': i === selectedIndex }"
                                    @click="selectProduct(index, product)">
                                    {{ product.product_name }}
                                </li>
                                <li v-if="filteredProductList.length === 0 && item.description.length > 2 && findExistItem(index)"
                                    @click=" openModalProduct(index)"
                                    class="new-product-btn text-green-700 hover:bg-gray-300">+
                                    New Product</li>
                            </ul>
                        </td>

                        <!--HSN code-->
                        <td class="p-1 border">
                            <input v-model.number="item.hsnCode" class="w-full p-1 text-center" type="text"
                                :refs="'hsncode' + index" @click="productDropdownIndex = false"
                                :ref="'productHsnCode' + index" />
                        </td>
                        <!--Quantity-->
                        <td class="p-1 border">
                            <input v-model.number="item.qty" @input="updateTotal" class="w-full p-1 text-center"
                                type="number" />
                        </td>
                        <!--Price-->
                        <td class="p-1 border">
                            <input v-model.number="item.price" class="w-full p-1 text-center" type="number"
                                @input="updateTotal" />
                        </td>
                        <!--tax-->
                        <td class="p-1 border">
                            <select v-model="item.tax" @change="calculateTax">
                                <option v-for="(opt, index) in formData.selectedTax" :key="index" :value="opt.value"
                                    :selected="opt.status">
                                    {{ opt.value }} %
                                </option>
                            </select>
                        </td>
                        <!--tax value-->
                        <td class="p-1 border">
                            <input v-model.number="item.taxvalue" class="w-full p-1 text-center" type="number"
                                readonly />
                        </td>
                        <!--total value-->
                        <td class="p-1 border">
                            <input v-model.number="item.total" class="w-full p-1 text-center" type="number"
                                @input="updatePrice(index)" :id="'input-total-' + index"
                                @keyup.enter="calculateTotal(1)" />
                        </td>
                        <!--delete row button-->
                        <td v-if="!print_hidden" class=" non-printable actions-column ">
                            <button @click="deleteRow(index)"
                                class="text-red-700 font-bold cursor-pointer hover:bg-gray-100">
                                <img :src="del_icon" alt="delete icon" class="w-6 h-6" />
                            </button>
                        </td>
                    </tr>

                    <!--Add row-->
                    <tr class="non-printable">
                        <td colspan="8" class="text-center p-1 border">
                            <button @click="addRow" class="text-green-700 font-bold cursor-pointer hover:bg-gray-100">+
                                Add
                                Row</button>
                        </td>
                    </tr>
                    <!--Sub total-->
                    <tr class="border text-sm">
                        <td colspan="5" class="text-end p-1 text-md border-r"><strong>Sub_Total</strong></td>
                        <td colspan="3" class="text-center text-md font-bold">{{ subTotal }}</td>
                    </tr>
                    <!--total tax-->
                    <tr :class="{ 'non-printable': totalTaxValue === 0 }">
                        <td colspan="5" class="text-end text-sm p-1 border-r">
                            <!-- <input type="number" v-model="selectedTax" class="w-[40px] pl-2" @input="calculateTax" /> %</td> -->
                            <!-- <select v-model="selectedTax" @change="calculateTax">
                            <option v-for="(opt, index) in formData.selectedTax" :key="index" :value="opt.value"
                                :selected="opt.status">
                                {{ opt.tax_name }} @ {{ opt.value }} %
                            </option>
                        </select> -->
                            CGST(₹)
                        </td>
                        <td colspan="3" class="text-center text-sm">{{ (totalTaxValue / 2).toFixed() }}</td>
                    </tr>
                    <tr :class="{ 'non-printable': totalTaxValue === 0 }">
                        <td colspan="5" class="text-end text-sm p-1 border">SGST(₹)</td>
                        <td colspan="3" class="text-center text-sm border">{{ (totalTaxValue / 2).toFixed() }}</td>
                    </tr>
                    <!--shipping charges-->
                    <tr class="border" :class="{ 'non-printable': shipping === '' || shipping === '0' }">
                        <td colspan="5" class="text-end text-sm p-1 border-r">Shipping</td>
                        <td colspan="3">
                            <div class="flex items-center justify-center">
                                <input v-model="shipping" class="p-1 text-center mr-3" type="text" placeholder="₹"
                                    @input="calculateShipping" pattern="[0-9%]+" />

                            </div>
                        </td>
                    </tr>
                    <!--discount-->
                    <tr class="border" :class="{ 'non-printable': discount === '' || discount === '0' }">
                        <td colspan="5" class="text-end text-sm p-1 border-r">Discount</td>
                        <td colspan="3">
                            <div class="flex items-center justify-center">
                                <input v-model="discount" class="p-1 text-center mr-3" type="text" placeholder="₹"
                                    @input="calculateDiscount" pattern="[0-9%]+" />
                                <input v-model="discountType" type="radio" value="%" @change="calculateDiscount"
                                    :class="{ 'non-printable': discountType === '₹' }" /> <span
                                    :class="{ 'non-printable': discountType === '₹' }">%</span>
                                <input v-model="discountType" type="radio" value="₹" @change="calculateDiscount"
                                    class="ml-3" :class="{ 'non-printable': discountType === '%' }" /> <span
                                    :class="{ 'non-printable': discountType === '%' }">₹</span>
                            </div>
                        </td>
                    </tr>

                    <!--grand total-->
                    <tr class="border">
                        <td colspan="5" class="text-end p-1 text-lg border-r"><strong>Grand Total</strong></td>
                        <td colspan="3" class="text-center text-lg font-bold">{{ formatCurrency(Math.round(grandTotal))
                            }}
                        </td>
                    </tr>
                    <!--Payament type-->
                    <tr v-if="typeOfInvoice !== 'estimation'" class="border"
                        :class="{ 'non-printable': !paymentRows[0].paid_amount }">
                        <td colspan="5" class="text-end text-sm p-1 border-r">Payment type</td>
                        <td colspan="3" class="text-center text-sm">
                            <div class="flex items-center justify-center">
                                <table>
                                    <tr v-for="(row, rowIndex) in paymentRows" :key="rowIndex">
                                        <td class="w-1/2 text-sm text-center">
                                            <div class="flex justify-around text-xs">
                                                <button v-for="(opt, index) in formData.payment_opt" :key="index"
                                                    :class="{ 'bg-green-500 text-white': opt.type === row.payment_type, 'non-printable': opt.type !== row.payment_type }"
                                                    @click="selectPaymentType(rowIndex, opt.type)"
                                                    class="border rounded px-2 hover:bg-gray-200">
                                                    {{ opt.type }}
                                                </button>
                                            </div>
                                        </td>
                                        <td class="text-center ">
                                            <input v-model="row.paid_amount" class="w-full text-sm text-center"
                                                placeholder="₹" type="number" @input="updateBalance(rowIndex)" />
                                        </td>
                                        <td v-if="rowIndex !== paymentRows.length - 1"
                                            class="text-red-700 font-bold text-xl cursor-pointer hover:bg-gray-300 rounded-full pl-2 pr-2 non-printable"
                                            @click="removeRow(rowIndex)">
                                            -
                                        </td>
                                        <td v-if="rowIndex === paymentRows.length - 1"
                                            class="text-green-700 font-bold text-xl cursor-pointer hover:bg-gray-300 rounded-full pl-2 pr-2 non-printable"
                                            @click="addNewRow(rowIndex)">
                                            +
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </td>

                    </tr>
                    <!--Return amount or balance-->
                    <tr v-if="typeOfInvoice !== 'estimation'" class="border"
                        :class="{ 'non-printable': balance_amount === 0 || !paymentRows[0].paid_amount }">
                        <td colspan="5" class="text-end text-sm p-1 border-r text-sm"><span
                                v-if="balance_amount >= 0">Return</span><span v-if="balance_amount < 0">Balance</span>
                            Amount
                        </td>
                        <td colspan="3" class="text-center font-bold">{{ balance_amount }}</td>
                    </tr>
                    <!-- Display grand total in text format -->
                    <tr class="border">
                        <td colspan="8" class="text-center p-1 text-md border-r"><strong>Total Amount (in words) : {{
                            convertToWords(Math.round(grandTotal)) }} Only</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="flex w-full">
            <div v-if="formData.disclaimer !== ''" class="p-3 border-l border-r border-b w-full">
                <p class="font-bold">Terms & Conditions:</p>
                <p class="pl-4" v-if="formData.disclaimer" v-for="(line, index) in formData.disclaimer.split('\n')"
                    :key="index">
                    <span>&#11162; </span>{{ line }}
                </p>
            </div>
            <div class="w-1/3 border-r border-b pt-3">
                <p class="text-center text-sm">For {{ formData.name }}</p>

            </div>
        </div>

        <div class="flex justify-center  items-center mt-5 non-printable">
            <button @click="backToSetting"
                class="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded pl-3 pr-3 mr-12">Back</button>
            <button @click="printInvoice" class="p-2 bg-green-700 hover:bg-green-600 text-white rounded pl-3 pr-3">
                {{ typeOfInvoice === 'estimation' ? 'Print Estimation' : 'Print Invoice' }}
            </button>
        </div>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModalCustomer" :userName="selectCustomer">
        </customerRegister>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <addName :show-modal="showModalProduct" :title="'Add Product Name'" :userName="newProduct"
            @close-modal="addNewProduct"></addName>
    </div>
</template>

<script>
import customerRegister from '../../dialog_box/customerRegister.vue';
import addName from '../../dialog_box/addName.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
export default {
    components: {
        customerRegister,
        addName,
        confirmbox
    },
    props: {
        formData: {
            type: Object,
            default: () => ({})
        },
        viewServiceData: {
            type: Object,
            default: () => ({})
        },
        categoryID: {
            type: [Number, String],
            default: null
        },
        typeOfInvoice: {
            type: String,
            default: ''
        },
        service_id: {
            type: [Number, String],
            default: null
        },
        category_name: {
            type: [Number, String],
            default: null
        },
    },
    data() {
        return {
            currentDate: '',
            currentTime: '',
            client_data: null,
            // clientName: '',
            // clientAddress: '',
            // clientEmail: '',
            // clientPhone: '',
            // clientGSTTin: '',
            isFocused: false,
            paymentRows: [
                { payment_type: '', paid_amount: '' }
            ],
            balance_amount: '',
            selectedTax: '',
            print_hidden: false,
            subTotal: 0,
            grandTotal: 0,
            items: [],
            logo_img: '/images/horizontal_bar/upload_image.png',
            del_icon: '/images/service_page/del.png',
            discount: 0,
            shipping: 0,
            totalTaxValue: 0,
            invoice_type: 'b2c',
            selectCustomer: null,
            isDropdownOpen: false,
            showModal_customer: false,
            filteredCustomerOptions: [],
            filteredProductList: [],
            productDropdownIndex: null,
            customers: [],
            product: [],
            invoiceNum: this.$route.query.type === 'edit' ? this.$route.query.invoice_no : this.modifyString(),
            editingData: null,
            isHovered: false,
            shippingType: '',
            discountType: '%',
            selectedIndex: 0,
            open_confirmBox: false,
            showModalProduct: false,
            newProduct: '',
            deleteIndex: null,
            deleteType: null,
            estimation_type: '',
            gstValidation: '',
            selectedInvoice_type: {},
        };
    },
    computed: {
        total() {
            return this.items.reduce((sum, item) => sum + item.price, 0);
        }
    },
    methods: {
        updateTotal() {
            // Iterate through each item to update totals and tax values
            this.items.forEach((item) => {
                // Calculate total based on price and tax
                item.total = ((item.qty * item.price) * (1 + item.tax / 100)).toFixed(2);

                // Calculate tax value
                item.taxvalue = (item.total - (item.qty * item.price)).toFixed(2);
            });

            // Update sub-total and grand total
            this.subTotal = this.calculateSubTotal();
            this.grandTotal = this.calculateGrandTotal();

            // Update balance if payment is entered
            if (this.paymentRows[0].paid_amount !== '') {
                this.updateBalance();
            }
        },
        updatePrice(index) {
            let findData = this.items.find((opt, i) => i === index);
            findData.price = (findData.total / (1 + findData.tax / 100)).toFixed(2);
            findData.taxvalue = (findData.total - findData.price).toFixed(2);
        },
        //---payment type
        addNewRow(rowIndex) {
            const newRow = { payment_type: '', paid_amount: '' };
            this.paymentRows.splice(rowIndex + 1, 0, newRow);
        },

        removeRow(rowIndex) {
            this.deleteType = 'payment';
            this.deleteIndex = rowIndex;
            this.open_confirmBox = true;
            // Remove the row at the specified index
            // this.paymentRows.splice(rowIndex, 1);

            // After removing the row, update the balances and totals
            // this.updateBalance(rowIndex);
        },

        updateBalance(rowIndex) {
            // Assuming you have a method to calculate total, subTotal, and grandTotal
            // this.calculateTotal();
            this.calculateSubTotal();
            this.calculateGrandTotal();
            // Sum up all paid_amount values from paymentRows
            const totalPaid = this.paymentRows.reduce((sum, row) => sum + row.paid_amount, 0);
            this.balance_amount = Math.round(totalPaid - this.grandTotal);
        },
        formatCurrency(value) {
            // Ensure value is a number before using toFixed
            const numericValue = parseFloat(value);
            if (isNaN(numericValue)) {
                // Handle the case where the value is not a valid number
                return "Invalid Amount";
            }

            return `${numericValue.toFixed(2)}`;
        },
        getCurrentDateTime() {
            const now = new Date();
            // Format the date
            const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
            this.currentDate = now.toLocaleDateString(undefined, options).replace(/\//g, '/');
            // Format the time
            this.currentTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        },
        addRow() {
            // if (this.items.length === 0 && this.typeOfInvoice === 'estimation' && this.estimation_type === 'product') {
            //     this.items.push(
            //         { description: '', hsnCode: '', qty: 1, price: 0, tax: this.selectedTax, taxvalue: 0, total: 0 });

            // }
            // Add a new empty row
            this.items.push({
                description: '', hsnCode: '', qty: 1, price: 0, tax: this.selectedTax, taxvalue: 0, total: 0
            });
            // Recalculate sub-total and grand total when a new row is added
            this.subTotal = this.calculateSubTotal();
            this.grandTotal = this.calculateGrandTotal();

            if (this.paymentRows[0].paid_amount !== '') {
                this.updateBalance();
            }
            this.$nextTick(() => {
                const lastRowIndex = this.items.length - 1;
                // console.log(this.$refs, 'What happeninf....');
                const productNameInput = this.$refs['productNameInputs' + lastRowIndex][0];
                if (productNameInput) {
                    productNameInput.focus();
                    // this.isDropdownOpenProduct = lastRowIndex;
                } else {
                    console.error("productNameInput is undefined.");
                }
            });
        },
        openFileInput() {
            // Trigger a click on the hidden file input
            this.$refs.fileInput.click();
        },
        handleImageChange(event) {
            const file = event.target.files[0];

            if (file) {
                // Assuming you're using a method to handle file uploads, update the logo_img accordingly
                this.uploadImage(file);
            }
        },
        uploadImage(file) {
            const formData = new FormData();
            formData.append("image", file);

            // Make an API request to Laravel backend
            axios.post("/api/upload-image", formData)
                .then(response => {
                    // Update the image source on successful upload
                    let data_save = { image_path: response.data.image_path };
                    // console.log(response.data, 'What happrning....Q', data_save);
                    this.logo_img = response.data.image_path;
                    localStorage.setItem('logo', JSON.stringify(data_save));
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                });
        },
        isArray(value) {
            return Array.isArray(value);
        },

        //-----delete record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null && this.deleteType !== 'payment') {
                this.items.splice(this.deleteIndex, 1);
                this.updateTotal();
            } else if (this.deleteIndex !== undefined && this.deleteIndex !== null && this.deleteType === 'payment') {
                // Remove the row at the specified index
                this.paymentRows.splice(this.deleteIndex, 1);

                // After removing the row, update the balances and totals
                this.updateBalance(this.deleteIndex);
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },

        deleteRow(index) {
            // this.formData.selectedTax.splice(index, 1);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        calculateSubTotal() {
            const subTotal = this.items.reduce((sum, item) => sum + Number(item.total), 0);
            this.calculateTotalTax();
            // console.log('Sub Total:', subTotal);
            return subTotal;
        },

        calculateGrandTotal() {
            const subTotal = this.calculateSubTotal();

            if (this.discount !== '') {
                // Check if the discount value is in percentage format (e.g., "100%")
                if (this.discountType === '%') {
                    // Extract the numeric value from the percentage
                    const discountPercentage = parseFloat(this.discount);

                    // Calculate the discounted amount based on the percentage
                    const discountAmount = (subTotal * discountPercentage) / 100;

                    // Apply the discount to the subTotal
                    return (subTotal - discountAmount).toFixed();
                } else {
                    // If the discount value is not in percentage format, apply it directly
                    return (subTotal - Number(this.discount)).toFixed();
                }
            } else {
                return subTotal;
            }
        },

        calculateTotalTax() {
            this.totalTaxValue = this.items.reduce((sum, item) => sum + Number(item.taxvalue), 0);
        },
        //--calculate total
        calculateTotal() {
            // console.log('UUUU');
            // if (event.key === 'Enter') {
            this.updateTotal();
            // }
        },
        //---calculate discount value--
        calculateDiscount() {
            this.grandTotal = this.calculateGrandTotal();
            this.calculateShipping();
            // Update balance if payment is entered
            if (this.paymentRows[0].paid_amount !== '') {
                this.updateBalance();
            }
        },
        //--customer model-
        closeModalCustomer(getData) {
            // console.log('Wahyyyyyyyyyyyyyyyyy');
            // console.log(getData, 'What happening');
            if (getData) {

                this.client_data = getData;
                // this.isDropdownOpen = false;
                this.showModal_customer = false;
            }
            this.showModal_customer = false;
        },
        openModal(data) {
            // console.log(data, 'What happening...!');
            this.isDropdownOpen = false;
            this.showModal_customer = true;
        },
        calculateShipping() {
            if (this.shipping !== '') {
                // Check if the discount value is in percentage format (e.g., "100%")
                if (this.shippingType === '%') {
                    // Extract the numeric value from the percentage
                    const shippingCharge = parseFloat(this.shipping);
                    let subTotal = this.calculateGrandTotal();
                    // Calculate the discounted amount based on the percentage
                    const shippingAmount = (subTotal * shippingCharge) / 100;

                    // Apply the discount to the subTotal
                    return this.grandTotal = (Number(subTotal) + shippingAmount).toFixed();
                } else {
                    // If the discount value is not in percentage format, apply it directly
                    let subTotal = this.calculateGrandTotal();
                    return this.grandTotal = Number(subTotal) + Number(this.shipping);
                }
            }
            // Update balance if payment is entered
            if (this.paymentRows[0].paid_amount !== '') {
                this.updateBalance();
            }
        },
        formatCurrency(value) {
            // Check if the value is a valid number
            if (isNaN(value)) {
                return "Invalid Amount";
            }
            // Format the number as currency with two decimal places
            return new Intl.NumberFormat('en-IN', {
                style: 'currency',
                currency: 'INR',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2,
            }).format(value);
        },
        calculateTax() {
            this.updateTotal();
            this.grandTotal = this.calculateGrandTotal();
            this.subTotal = this.calculateSubTotal();

            if (this.paymentRows[0].paid_amount !== '') {
                this.updateBalance();
            }
        },
        convertToWords(amount) {
            const oneToTwenty = ['Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
            const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

            const convertBelowHundred = (num) => {
                if (num < 20) {
                    return oneToTwenty[num];
                } else {
                    return tens[Math.floor(num / 10)] + ' ' + oneToTwenty[num % 10];
                }
            };

            const convertBelowThousand = (num) => {
                if (num < 100) {
                    return convertBelowHundred(num);
                } else {
                    return oneToTwenty[Math.floor(num / 100)] + ' Hundred ' + convertBelowHundred(num % 100);
                }
            };

            const convertGroup = (num, suffix) => {
                if (num === 0) {
                    return '';
                } else {
                    return convertBelowThousand(num) + ' ' + suffix;
                }
            };

            if (amount === 0) {
                return 'Zero Rupees';
            } else {
                const groups = [];
                let remaining = amount;

                const suffixes = ['', 'Thousand', 'Million', 'Billion', 'Trillion', 'Quadrillion', 'Quintillion', /* and so on... */];

                for (let i = 0; remaining > 0; i++) {
                    const groupValue = remaining % 1000;
                    remaining = Math.floor(remaining / 1000);
                    groups.push(convertGroup(groupValue, suffixes[i]));
                }

                return groups.reverse().join(' ').trim() + ' Rupees';
            }
        },
        printInvoice() {
            this.print_hidden = true; // Show the print-only section
            // console.log(this.categoryID ? { id: this.categoryID } : 'product', 'WWWWW');
            const urlParts = window.location.pathname.split('/');
            const page = urlParts[urlParts.length - 1];
            if (decodeURIComponent(page) !== 'setting' && this.client_data && this.typeOfInvoice !== 'estimation') {
                const createInvoice = { invoice_id: this.invoiceNum, current_date: this.currentDate, customer: this.client_data['id'], items: this.items, payment_mode: this.paymentRows, discount: this.discount, discountType: this.discountType, shipping: this.shipping, shippingType: this.shippingType, invoice_type: this.categoryID ? { id: this.categoryID, name: this.category_name } : 'product', invoice_to: this.invoice_type, due_amount: this.getDueAmount() };
                // console.log(this.paymentRows, 'What happening.....!');
                const getLocalData = localStorage.getItem('invoice');
                const getServiceList = localStorage.getItem('CategoriesForm');
                if (getServiceList) {
                    let parseServiceList = JSON.parse(getServiceList);
                    if (parseServiceList && this.service_id && this.categoryID) {
                        let findServiceCategory = parseServiceList.find((opt) => opt.id === this.categoryID);
                        let findServiceCategoryIndex = parseServiceList.findIndex((opt) => opt.id === this.categoryID);
                        if (findServiceCategory && findServiceCategory.data) {
                            let findSeviceIndex = findServiceCategory.data.findIndex((opt) => opt.id === Number(this.service_id));
                            // console.log(findServiceCategory, 'find category', findServiceCategoryIndex, 'find service index', findServiceCategory.data[findSeviceIndex], 'what happening...!', this.service_id, 'service', this.categoryID, 'category');
                            findServiceCategory.data[findSeviceIndex] = { ...findServiceCategory.data[findSeviceIndex], invoice_id: createInvoice['invoice_id'] };
                            parseServiceList.splice(findServiceCategoryIndex, 1, findServiceCategory);
                            localStorage.setItem('CategoriesForm', JSON.stringify(parseServiceList));
                        }
                    }
                }

                if (getLocalData) {
                    const parsedata = JSON.parse(getLocalData);

                    if (this.$route.query.type === 'edit') {
                        const findTheData = parsedata.findIndex((opt) => opt.invoice_id === this.invoiceNum);
                        // console.log();

                        if (findTheData !== -1) {
                            parsedata.splice(findTheData, 1, createInvoice);
                        } else {
                            parsedata.push(createInvoice);
                        }
                    } else {
                        const findTheData = parsedata.findIndex((opt) => opt.invoice_id === this.invoiceNum);

                        if (findTheData !== -1) {
                            parsedata.splice(findTheData, 1, createInvoice);
                        } else {
                            parsedata.push(createInvoice);
                        }
                    }
                    localStorage.setItem('invoice', JSON.stringify(parsedata));
                } else {
                    const arraydata = [createInvoice];
                    localStorage.setItem('invoice', JSON.stringify(arraydata));
                }
            } else if (this.typeOfInvoice === 'estimation' && this.client_data) {
                const createEstimation = { estimate_num: this.invoiceNum, current_date: this.currentDate, customer: this.client_data['id'], items: this.items, grand_total: this.formatCurrency(Math.round(this.grandTotal)), estimate_type: this.estimation_type };
                // console.log(this.paymentRows, 'What happening.....!');
                const getLocalEst = localStorage.getItem('estimation');

                if (getLocalEst) {
                    const parsedata = JSON.parse(getLocalEst);

                    if (this.$route.query.estimate_num_type === 'edit') {
                        const findTheData = parsedata.findIndex((opt) => opt.estimate_num === this.invoiceNum);
                        // console.log(findTheData, 'ppppp');

                        if (findTheData !== -1) {
                            parsedata.splice(findTheData, 1, createEstimation);
                        } else {
                            parsedata.push(createEstimation);
                        }
                    } else {
                        const findTheData = parsedata.findIndex((opt) => opt.id === this.invoiceNum);

                        if (findTheData !== -1) {
                            parsedata.splice(findTheData, 1, createEstimation);
                        } else {
                            parsedata.push(createEstimation);
                        }
                    }
                    localStorage.setItem('estimation', JSON.stringify(parsedata));
                } else {
                    const arraydata = [createEstimation];
                    localStorage.setItem('estimation', JSON.stringify(arraydata));
                }
            }

            // Use window.print() to trigger the browser's print dialog
            window.print();
            // After printing, reset the state
            this.print_hidden = false;
            this.backToSetting();
        },

        //---calculate due amount--
        getDueAmount() {
            // console.log(this.paymentRows, " what happening", this.grandTotal);
            const totalPaidAmount = this.paymentRows.reduce((total, payment) => total + payment.paid_amount, 0);
            return totalPaidAmount >= this.grandTotal ? 0 : this.grandTotal - totalPaidAmount;
        },

        //---go back
        backToSetting() {
            this.$emit('goSetting');
        },
        //---select customer
        selectDropdownOption(option) {
            this.client_data = option;
            this.isDropdownOpen = false; // Close the dropdown
        },
        //---filter
        handleDropdownInput(fields) {
            // console.log('What happening...WW');
            const inputValue = fields;
            if (inputValue !== undefined && inputValue !== null) {
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customers.filter(
                        (option) => option.contactNumber.toLowerCase().includes(inputNumber) || option.alternateNumber.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    // console.log(this.customers, 'WHAT HAPPENING...!', );
                    this.filteredCustomerOptions = this.customers.filter(
                        (option) => (option.firstName && option.firstName.toLowerCase().includes(inputName)) || (option.lastName && option.lastName.toLowerCase().includes(inputName)) || (option.email && option.email.toLowerCase().includes(inputName))
                    );
                }
            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = [];
            }
        },
        //---gst tin input field
        handleFocus() {
            this.isFocused = true;
        },
        handleBlur() {
            this.isFocused = false;
        },
        //-----filter product--
        filterProducts(index) {
            const enteredProductName = this.items[index].description.toLowerCase();
            let selectedItem = this.items[index];
            // console.log(selectedItem, 'waht happening');

            if (this.product.length !== 0 && enteredProductName.length > 2) {
                const existingProducts = this.items.map((item, i) => index !== i && item.description.toLowerCase());
                const existingProductCodes = this.items.map((item, i) => index !== i && item.hsnCode && typeof item.hsnCode === 'string' ? item.hsnCode.toLowerCase() : null).filter(code => code !== null);

                this.filteredProductList = this.product.filter(opt => {
                    const isExistingName = existingProducts.includes(opt.product_name.toLowerCase());
                    const isExistingCode = existingProductCodes.includes(opt.product_code.toLowerCase());

                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.product_name.toLowerCase().includes(enteredProductName);
                    const codeMatch = opt.product_code.toLowerCase().includes(enteredProductName);

                    // console.log(index, isExistingName, 'what happening,,,,!', isExistingCode, 'is existing', nameMatch, 'name match', codeMatch, 'code match');

                    return (!isExistingName || !isExistingCode) && (nameMatch || codeMatch);
                });
            }
        },
        //---add product option controll--
        findExistItem(index) {
            const enteredProductName = this.items[index].description.toLowerCase();

            if (this.product.length !== 0 && enteredProductName.length > 2) {
                const existingProducts = this.product.map(item => item.product_name.toLowerCase());
                const existingProductCodes = this.product.map(item => item.product_code.toLowerCase());

                // Use a separate array to store existing product names and codes
                const existingNamesAndCodes = [...existingProducts, ...existingProductCodes];

                // Check if the entered product name or product code matches any existing product name or product code
                const isExisting = existingNamesAndCodes.includes(enteredProductName);
                // console.log(isExisting, 'Waht happening....!');
                return !isExisting;
            }

            return true;
        },

        selectProduct(index, selectedProduct) {
            // console.log(selectedProduct, 'Selected data...@');
            this.items[index].description = selectedProduct.product_name;
            this.items[index].hsnCode = selectedProduct.product_code;
            this.items[index].price = selectedProduct.price_per_qty;
            this.items[index].tax = selectedProduct.tax;
            this.updateTotal();
            this.calculateTax();
            this.updatePrice(this.productDropdownIndex);
            this.productDropdownIndex = null;
            // this.productDropdown = false;           
            this.filteredProductList = []; // Clear the filtered list after selecting a product.
            // console.log(this.$refs['productHsnCode' + index][0], 'waht happening..!');
            this.$refs['productHsnCode' + index][0].focus();
        },
        //---add new product---
        //---product name model---
        openModalProduct(index) {
            // console.log(this.product, 'Waht happening...Q');
            if (!this.product.some((data) => data.product_name.toLowerCase().includes(this.items[index].description.toLowerCase()))) {

                // console.log(this.items, 'items', index, 'WWWWWWWWWWWWWWWWW');
                this.newProduct = this.items[index].description;
                // console.log(index, 'index', this.newProduct, 'Waht happening..!');
                this.showModalProduct = true;
            }

        },
        closeModal(index) {
            this.showModalProduct = false;
            this.newProduct = '';
            this.$nextTick(() => {
                // console.log(this.$refs, 'What happeninf....', index, 'index');
                const productHsnCode = this.$refs['productHsnCode' + index][0];
                // console.log(productHsnCode, 'Waht happening...!');
                if (productHsnCode) {
                    productHsnCode.focus();
                    // this.isDropdownOpenProduct = lastRowIndex;
                } else {
                    console.error("productHsnCode is undefined.");
                }
            });
        },

        addNewProduct(name) {
            let index = this.productDropdownIndex;
            if (name) {
                const enteredProductName = name;
                // console.log(enteredProductName, 'Entered product name..!');
                const existingProduct = this.product.find(
                    (product) => product.product_name.toLowerCase() === enteredProductName.toLowerCase()
                );

                if (!existingProduct && enteredProductName.trim() !== '') {
                    // Product doesn't exist, add a new one
                    const newProduct = {
                        name: enteredProductName,
                        hsnCode: '',
                        qty: 0,
                        price: 0,
                        tax: 0,
                        taxvalue: 0,
                        total: 0
                    };
                    const addNewProduct = { product_code: '', product_name: enteredProductName, total_qty: 0, price_per_qty: 0, tax: 0, sold_stocks: 0, available_stocks: 0, purchase_order: '', editing: false };
                    // Add the new product to the product list
                    this.items[this.productDropdownIndex].description = enteredProductName;
                    this.product.push({ ...addNewProduct });
                    let getLocalData = localStorage.getItem('inventory');
                    if (getLocalData) {
                        let parseData = JSON.parse(getLocalData);
                        if (parseData) {
                            parseData.push({ ...addNewProduct });
                            localStorage.setItem('inventory', JSON.stringify(parseData));
                        }
                    }
                    // Select the newly added product
                    //this.selectProduct(index, newProduct);
                    this.selectProduct[index] = newProduct;
                    this.filteredProductList = [];
                    this.productDropdownIndex = null;
                }
            }

            this.closeModal(index);
        },
        modifyString() {
            let getLocalData = localStorage.getItem('invoice');
            if (getLocalData && this.typeOfInvoice !== 'estimation') {
                let parseData = JSON.parse(getLocalData);
                if (parseData && parseData.length > 0) {
                    const regex = /([a-zA-Z]+)(\d+)/;
                    const match = parseData[parseData.length - 1]['invoice_id'].match(regex);
                    if (match) {
                        const letters = match[1];
                        const number = parseInt(match[2], 10);
                        const modifiedNumber = number + 1;
                        const paddedNumber = String(modifiedNumber).padStart(5, '0'); // Ensure 5 digits with leading zeros if necessary
                        return letters + paddedNumber;
                    } else {
                        // Handle the case where the input doesn't match the expected pattern
                        return 'invc00001';
                    }
                }
            } else if (this.typeOfInvoice === 'estimation') {
                let getLocalEst = localStorage.getItem('estimation');
                if (getLocalEst) {
                    let parseData = JSON.parse(getLocalEst);
                    if (parseData && parseData.length > 0) {
                        const regex = /([a-zA-Z]+)(\d+)/;
                        const match = parseData[parseData.length - 1]['estimate_num'].match(regex);
                        if (match) {
                            const letters = match[1];
                            const number = parseInt(match[2], 10);
                            const modifiedNumber = number + 1;
                            const paddedNumber = String(modifiedNumber).padStart(5, '0'); // Ensure 5 digits with leading zeros if necessary
                            return letters + paddedNumber;
                        } else {
                            // Handle the case where the input doesn't match the expected pattern
                            return 'est00001';
                        }
                    }
                }
                return 'est00001'

            }
            return 'invc00001';
        },
        //---payment option---
        selectPaymentType(rowIndex, type) {
            if (rowIndex !== null) {
                this.paymentRows[rowIndex].payment_type = type;
            }
        },
        //--on enter key based on drop down
        //----table--
        handleEnterKey(index, type, product_name) {
            // Check if filteredProductList has at least one item
            if (type === 'product') {
                if (this.filteredProductList.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectProduct(index, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    this.openModalProduct(index);
                }
            }
            if (type === 'customer') {
                if (this.filteredCustomerOptions.length > 0) {
                    this.selectDropdownOption(this.filteredCustomerOptions[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    this.openModal(this.selectCustomer);
                    this.$refs.selectCustomerInput.blur();
                }
            }
            if (type === 'warehouse') {
                if (this.filteredWarehouseList.length > 0) {
                    this.selectedWarehouseData(this.filteredWarehouseList[0]);
                } else {
                    this.addNewWarehouse();
                }
            }
        },
        //---arrow on key press
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //--validate GST--
        validateGST() {
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.client_data.gstNumber !== '') {
                // Check if the input matches the expected format
                if (regex.test(this.client_data.gstNumber)) {
                    this.gstValidation = '';
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                }
            }
        }
    },
    mounted() {
        //----sales to direct invoice--
        // console.log(this.viewServiceData, 'Waht happening...!');
        if (this.categoryID && this.category_name && this.viewServiceData === null) {
            this.items = [{ description: this.category_name, hsnCode: '', qty: 1, price: 0, tax: this.selectedTax, taxvalue: 0, total: 0 }];
        }
        //----estimation--
        this.estimation_type = this.$route.query.type;
        // console.log(this.formData, 'WWWWWWWWW00001');
        // console.log(this.typeOfInvoice, 'What about type of invoice...!')
        //---customers data--
        let userData = localStorage.getItem('userData');
        if (userData) {
            let parseData = JSON.parse(userData);
            if (parseData) {
                this.customers = parseData;
            }
        }
        //---edit invoice---
        this.updateTotal();
        if (this.$route.query.type === 'edit') {
            let invoice_Number = this.$route.query.invoice_no;
            const getLocalData = localStorage.getItem('invoice');
            // console.log(getLocalData, 'find the data.....!');
            if (getLocalData) {
                const parsedata = JSON.parse(getLocalData);
                if (parsedata && parsedata.length > 0) {
                    let findEditData = parsedata.find((opt) => opt.invoice_id === invoice_Number);
                    if (findEditData) {
                        // console.log(findEditData, 'What happening....!');
                        this.editingData = findEditData;
                        this.client_data = this.customers.find((opt) => opt.id === findEditData.customer);
                        // console.log(this.client_data, 'What about client...!', this.customers.find((opt) => opt.id === findEditData.customer));
                        //--add more client details
                        this.items = findEditData.items;
                        this.paymentRows = findEditData.payment_mode;
                        this.discount = findEditData.discount;
                        this.discountType = findEditData.discountType;
                        this.shipping = findEditData.shipping;
                        this.shippingType = findEditData.shippingType;
                        this.invoice_type = findEditData.invoice_to;
                        this.calculateTotal();
                    }
                }
            }
        }
        //---estimation edit data
        if (this.typeOfInvoice === 'estimation') {
            let findTheID = this.$route.query.estimate_num;
            if (findTheID) {
                let getLocalDataEst = localStorage.getItem('estimation');
                if (getLocalDataEst) {
                    let parseLocalEst = JSON.parse(getLocalDataEst);
                    let findexist = parseLocalEst.find((data) => data.estimate_num === findTheID);
                    if (findexist) {
                        this.items = findexist.items;
                        this.invoiceNum = findexist.estimate_num;
                        this.client_data = this.customers.find((opt) => opt.id === findexist.customer);
                        this.calculateTax();
                    }
                }
            }
        }

        //---logo--
        let logo = localStorage.getItem('logo');
        if (logo) {
            let parseLogo = JSON.parse(logo);
            if (parseLogo) {
                this.logo_img = parseLogo.image_path;
            }
        }
        //----product list--
        let getPoductList = localStorage.getItem('inventory');
        if (getPoductList) {
            let parseProduct = JSON.parse(getPoductList);
            if (parseProduct) {
                this.product = parseProduct;

            }
        }
        //----Estimation to Sale Invoice---
        if (this.$route.query.type === 'add' && this.$route.query.estimate_num) {
            let estimate_Number = this.$route.query.estimate_num;
            const getLocalData = localStorage.getItem('estimation');
            // console.log(getLocalData, 'find the data.....!');
            if (getLocalData) {
                const parsedata = JSON.parse(getLocalData);
                if (parsedata && parsedata.length > 0) {
                    let findEditData = parsedata.find((opt) => opt.estimate_num === estimate_Number);
                    if (findEditData) {
                        // console.log(findEditData, 'What happening....!');
                        this.editingData = findEditData;
                        this.client_data = this.customers.find((opt) => opt.id === findEditData.customer);
                        // console.log(this.client_data, 'What about client...!', this.customers.find((opt) => opt.id === findEditData.customer));
                        //--add more client details
                        this.items = findEditData.items;
                        // this.paymentRows = findEditData.payment_mode;
                        // this.discount = findEditData.discount;
                        // this.discountType = findEditData.discountType;
                        // this.shipping = findEditData.shipping;
                        // this.shippingType = findEditData.shippingType;
                        // this.invoice_type = findEditData.invoice_to;
                        this.calculateTotal();
                    }
                }
            }
        }
        this.getCurrentDateTime(); // Update every second if you want a live clock
        setInterval(() => {
            this.getCurrentDateTime();
        }, 60000);

    },
    watch: {
        formData: {
            handler: function (newFormData, oldFormData) {
                // console.log(newFormData && this.service_id !== 'sales' && this.service_id && this.$route.query.type !== 'edit', 'RRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRR', newFormData && this.service_id !== 'sales' )
                if (newFormData && this.service_id !== 'sales') {
                    // console.log('What happening........formdata', this.service_id);
                    // Set initial value for selectedTax based on formData
                    // console.log('hello');
                    if (newFormData.selectedTax) {
                        const selectedTaxIndex = newFormData.selectedTax.findIndex(opt => opt.status === true);
                        if (selectedTaxIndex !== -1) {
                            this.selectedTax = newFormData.selectedTax[selectedTaxIndex].value;
                        }
                    }
                    // Set initial value for payment_type based on formData
                    if (newFormData.payment_opt) {
                        const selectedPaymentIndex = newFormData.payment_opt.findIndex(opt => opt.status === true);
                        if (selectedPaymentIndex !== -1) {
                            this.paymentRows[0].payment_type = newFormData.payment_opt[selectedPaymentIndex].type;
                        }
                    }
                    if (newFormData.invoice_prefix) {
                        // console.log(newFormData.invoice_prefix, 'Waht happening...!')
                        const selectedInvoiceType = newFormData.invoice_prefix.findIndex(opt => opt.status === true);
                        if (selectedInvoiceType !== -1) {
                            this.selectedInvoice_type = newFormData.invoice_prefix[selectedInvoiceType];
                            //    console.log(this.selectedInvoice_type, 'YYYYYY');
                        }
                    }
                }
            },
            immediate: true,
        },
        viewServiceData: {
            handler: function (newFormData, oldFormData) {
                if (newFormData && this.service_id !== 'sales' && this.service_id && this.$route.query.type !== 'edit') {
                    console.log('what happening.......View service data...!', newFormData);
                    const regex = /\b(?:service)\b/i;
                    // console.log(this.category_name + (regex.test(this.category_name) ? '' : ' Service') + '\n' + (newFormData.brand ? 'Brand: ' + newFormData.brand : '') + ' ' + (newFormData.device_model ? 'Model: ' + newFormData.device_model : '' + '\n ' + newFormData.serial_number ? 'Serial No: ' + newFormData.serial_number : '') + '\n ' + (newFormData.problem_title ? newFormData.problem_title : ''));
                    let obj = { description: '', hsnCode: '', qty: 1, price: 0, tax: this.formData ? this.formData.selectedTax ? this.formData.selectedTax.find((opt) => opt.status === true).value : 0 : 0, taxvalue: 0, total: 0 }
                    obj.description = this.category_name + (regex.test(this.category_name) ? '\n' : ' Service \n') + (newFormData.brand ? 'Brand: ' + newFormData.brand + '\n ' : '') + ' ' + (newFormData.device_model ? 'Model: ' + newFormData.device_model + '\n ' : '') + (newFormData.serial_number ? 'Serial No: ' + newFormData.serial_number + '\n ' : '') + (newFormData.problem_title ? 'Problem title: ' + newFormData.problem_title : '');

                    // console.log(newFormData, 'WWWW');
                    // this.clientName = newFormData.customer;
                    this.client_data = this.customers.find((opt) => newFormData.customer);//---want to workout
                    obj.price = newFormData.serviceAmount || newFormData.estimateAmount;
                    // obj.description = newFormData.defect_details;
                    this.items.push(obj);
                    console.log(newFormData.additional, 'What happening.....!',);
                    if (newFormData.additional) {
                        newFormData.additional.map((opt) => {
                            const objData = { description: opt.productName, qty: opt.quantity, price: opt.quantity * opt.price, tax: this.selectedTax, taxvalue: 0, total: 0 };
                            this.items.push(objData);
                        })
                    }
                    // console.log(newFormData.additional, 'What about additional values...!');
                    if (newFormData.advanceAmount) {
                        this.paymentRows[0].paid_amount = newFormData.advanceAmount
                    }

                    this.updateTotal();
                }
            },
            immediate: true,
        },


    },
};
</script>

<style scoped>
.print-only {
    display: block;
}

.outline {
    border: 1px solid black;
    /* Set your desired border style and color */
    border-radius: 5px;
    padding: 2px;
}

.filter {
    filter: blur(10px);
    /* Initial blur value */
    transition: filter 0.5s ease-in-out;
    /* Smooth transition */
}

.filter:hover {
    filter: blur(0);
    /* Remove blur on hover */
}

.actions-column {
    position: absolute;
    transform: translateY(-0%);
}

@media print {

    /* Additional styles for printing */
    body {
        overflow-y: hidden;
    }

    .non-printable {
        display: none;
    }
}
</style>