<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 py-12 h-screen overflow-y-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex items-center justify-center z-50">
                <div class="bg-gray-600 bg-opacity-50" @click="closeModal"></div>
                <div class="bg-white rounded-lg shadow-lg w-full max-w-3xl mx-auto p-6 relative">
                    <div class="flex justify-between items-center border-b pb-3">
                        <h3 class="text-lg font-semibold">Upload Files</h3>
                        <button @click="closeModal" class="text-gray-600 hover:text-gray-900"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>
                    <div class="my-6">
                        <input ref="fileInput" type="file" accept="image/*" @change="handleImageChangeProfile" />
                        <!--loader circle-->
                        <div v-if="circle_loader_photo" class="flex">
                            <CircleLoader :loading="circle_loader_photo"></CircleLoader>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-2">
                        <button @click="closeModal" class="bg-gray-500 text-white px-4 py-2 rounded">Cancel</button>
                        <!-- <button @click="uploadFiles" class="bg-blue-500 text-white px-4 py-2 rounded">Upload</button> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        companyId: String
    },
    data() {
        return {
            isOpen: false,
            selectedFiles: [],
            formValues: {},
            circle_loader_photo: false,
        };
    },
    methods: {
        openModal() {
            this.isOpen = true;
        },
        closeModal() {
            this.isOpen = false;
            this.selectedFiles = [];
            setTimeout(() => {
                this.$emit('close-modal');
            }, 100);
        },
        //--upload image--
        //----Profile image--
        openFileInput() {
            // Trigger a click on the hidden file input

            this.$refs.fileInput.click();
        },
        async handleImageChangeProfile(event) {
            const file = event.target.files[0];
            // this.circle_loader_photo = true;
            // if (file) {
            //     this.uploadImageProfile(file);
            // }
            if (!file) return;

            // Check file size (in bytes)
            const maxSizeBytes = 500 * 1024; // 500kb in bytes
            if (file.size > maxSizeBytes) {
                // Image exceeds 500kb, compress it
                try {
                    this.circle_loader_photo = true; // Show loader
                    const compressedFile = await this.compressImage(file);

                    this.uploadImageProfile(compressedFile);
                } catch (error) {
                    console.error("Error compressing image:", error);
                    this.circle_loader_photo = false; // Hide loader on error
                }
            } else {
                // Image is <= 500kb, upload directly
                try {
                    this.circle_loader_photo = true;

                    this.uploadImageProfile(file);
                } catch (error) {
                    console.error("Error uploading image:", error);
                    this.circle_loader_photo = false; // Hide loader on error
                }
            }
        },
        uploadImageProfile(file) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "RMA");
            formData.append("company_id", this.companyId);
            // Make an API request to Laravel backend
            // console.log(file, 'RRRR');
            axios.post('/image', formData)
                .then(response => {
                    // console.log(response.data, 'What happrning....Q');
                    this.circle_loader_photo = false;
                    //  let data_save = { image_path: response.data.image_path };
                    if (this.formValues.attachment && this.formValues.attachment !== '') {
                        // this.removeExistAvatar(this.formValues.attachment);
                    }
                    this.formValues.attachment = response.data.media_url;
                    this.$emit('close-modal', this.formValues);
                    this.selectedFiles = [];
                    this.formValues = {};
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                });
        },

        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },

    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
    },
};
</script>

<style>
/* Add additional custom styles if needed */
</style>
