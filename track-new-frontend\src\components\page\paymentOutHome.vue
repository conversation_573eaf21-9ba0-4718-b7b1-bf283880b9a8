<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mb-[60px] mt-[57px]': isMobile, 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <headbar @toggle-sidebar="toggleSidebar" class="non-printable" @refresh_store="refresh_store"></headbar>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
                :rows="number_of_rows" :gap="gap" :type="'grid'">
            </skeleton>
            <div>
                <bannerDesign></bannerDesign>
            </div>
            <!-- print preview data -->
            <div v-if="!open_skeleton" class="relative w-full" ref="pdf">
                <home :isMobile="isMobile" :isAndroid="isAndroid" :companyId="companyId" :userId="userId"
                    :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></home>

            </div>
        </div>
        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden non-printable"
            @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>
<script>
// import sidebar from '../supporting/sidebar.vue';
import headbar from '../supporting/inventory/headbar.vue';
import home from '../supporting/inventory/paymentOut/payment_home/home.vue';
import { useMeta } from '@/composables/useMeta';
import bannerDesign from '../supporting/banner/bannerDesign.vue';
export default {
    name: 'paymentout',
    components: {
        // sidebar,
        headbar,
        home,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isAndroid: false,
            isSidebarOpen: false,
            viewServiceData: null,
            typeofService: null,
            route_item: 6,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            data: [],
            shareData: {},
            service_id: null,
            service_data: {},
            //---api integration---
            companyId: null,
            userId: null,
            exist_data: null,
            typeOfInvoice: 'Product',
            invoice_setting: null,
            customer_data: {},
            items: [],
            get_all_data: {},
            paymentData: {},
            collect_invoice_data: {},
            formValues: {},
            logo_img: '/images/head_bar/logo_01.png',
            shipping_details: {},
            company_data: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 20,
            gap: 5,
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Payment Out';
        const pageDescription = 'Manage and track outgoing payments with ease. Stay organized with detailed payment records and statuses.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },

    methods: {

        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        updateIsAndroid() {
            this.isAndroid = window.innerWidth < 768;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        this.updateIsAndroid();
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');

            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

        window.addEventListener('resize', this.updateIsMobile);
        window.addEventListener('resize', this.updateIsAndroid);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
        window.removeEventListener('resize', this.updateIsAndroid);
    },
    watch: {
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.updateIsMobile(); // Initial check
                this.updateIsAndroid();

            }
        },
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },

};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}

/* Center content on larger screens */
.center-screen {
    display: flex;
    align-items: center;
    justify-content: center;
}


@media print {

    /* Additional styles for printing */
    body {
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */

    }

    .non-printable {
        display: none;
    }

    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }

    /* Apply different styles for printing */
    .center-content {
        width: 100%;
        margin: 0;
    }
}
</style>