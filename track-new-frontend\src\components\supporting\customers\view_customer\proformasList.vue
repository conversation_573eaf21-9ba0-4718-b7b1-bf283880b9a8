<template>
    <div>
        <div v-if="!open_skeleton && data.length > 0" class="text-sm" :class="{ 'm-4': !isMobile }">
            <div
                :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="border-b border-gray-400">
                                <!--dynamic-->
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    :class="{ 'hidden': !column.visible }" class="py-2 text-left px-2">
                                    <p>{{ column.label }}</p>
                                </th>
                                <th class="leading-none text-center">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(record, index) in data" :key="index"
                                class="border-b border-gray-400 cursor-pointer hover:bg-gray-200">
                                <td v-for="(column, colIndex) in columns" :key="colIndex" @click="printRecord(record)"
                                    :class="{ 'hidden': !column.visible }" class="py-2 px-1 text-left">
                                    <span v-if="column.field === 'current_date'">
                                        {{ record[column.field].substring(0, 10) !== '-000001-11' ?
                                            formatDateTime(record[column.field]) : '' }}</span>

                                    <!-- <span v-if="column.field === 'customers'"
                                        class="text-sky-700 hover:underline cursor-pointer"
                                        @click="viewRecordCustomer(record[column.field])">{{
                                            record[column.field].first_name +
                                            '' + (record[column.field].last_name === null ? '' :
                                                record[column.field].last_name)
                                            +
                                            ' - ' + record[column.field].contact_number }}</span> -->
                                    <span
                                        v-if="column.field !== 'current_date' && column.field !== 'customers' && column.field !== 'payment' && column.field !== 'sales' && column.field !== 'estimation'">{{
                                            record[column.field]
                                        }}</span>
                                    <span v-if="column.field === 'payment'" class="items-center">
                                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }}
                                        {{ calculatetotal(record['payment_mode']) }}
                                    </span>
                                    <span class="cursor-pointer hover:text-blue-600 "
                                        v-if="column.field === 'estimation' && record['estimation'] && record['estimation']['estimate_num']"
                                        @mouseover="showModal({ estimate_id: record[column.field].estimate_num, id: record[column.field].id }, $event)"
                                        @mouseleave="hideModal">
                                        # {{ record['estimation']['estimate_num'] }}
                                    </span>
                                    <span class="cursor-pointer hover:text-blue-600 " isModalVisible
                                        @mouseover="showModal({ invoice_id: record[column.field].invoice_id, sale_id: record[column.field].id }, $event)"
                                        @mouseleave="hideModal"
                                        v-if="column.field === 'sales' && record['sales'] && record['sales']['invoice_id']">
                                        # {{ record['sales']['invoice_id'] }}
                                    </span>
                                    <!-- <span v-if="column.field === 'items'" v-for="(data, i) in record[column.field]"
                                    :key="i">
                                    {{ displayProductList(data) }}<br></span>
                                <span v-if="column.field === 'customer'">
                                    {{ findAndReturnCustomer(record[column.field]) }}</span> -->
                                    <!-- <span
                                    v-if="!Array.isArray(record[column.field]) && column.field !== 'lead_type' && column.field !== 'lead_status'">{{
                                        record[column.field] }}</span>
                                <span v-if="column.field === 'lead_type'">{{ leadType.length !== 0 &&
                                    leadType[record[column.field]]['name'] }}</span>
                                <span v-if="column.field === 'lead_status'">{{ leadStatus.length !== 0 &&
                                    leadStatus[record[column.field]]['name'] }}</span>
                                <span v-if="Array.isArray(record[column.field])">{{ record[column.field].join(',') }}</span> -->
                                </td>
                                <td class="px-2 py-2 text-center">
                                    <div class="flex justify-center">
                                        <!--convert to invoice-->
                                        <div class="flex justify-left">
                                            <button v-if="!record.editing && record.status === '0'"
                                                @click="toSale(record)"
                                                class="line-clamp-1 text-blue-500 px-1 py-1 mr-2 rounded-full bg-blue-500 text-xs text-white px-3 hover:bg-green-700 cursor-pointer">
                                                Convert To Invoice
                                            </button>
                                            <button v-if="!record.editing && record.status === '1'"
                                                class="line-clamp-1 text-gray-500 px-1 py-1 mr-2 rounded-full bg-gray-500 text-xs text-white px-3 hover:bg-gray-600 cursor-pointer">
                                                Converted to invoice
                                            </button>
                                        </div>
                                        <div class="flex justify-center">
                                            <!--More actions-->
                                            <div class="flex relative">
                                                <button v-if="!record.editing" @click="printRecord(record)"
                                                    title="Print"
                                                    class="text-violet-700 px-1 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-solid fa-eye" />
                                                </button>
                                                <button v-if="!record.editing && record.status !== '1'"
                                                    @click="startEdit(record)" title="Edit"
                                                    class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-solid fa-pencil"
                                                        style="color: #3b82f6;" />
                                                </button>
                                                <!-- <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-8 absolute bg-white right-5 divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="printRecord(record)"
                                                            class="text-violet-700 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-eye" size="lg" />
                                                            <span class="px-2">View</span>
                                                        </button>
                                                    </li>
                                                    <li :class="{ 'hidden': record.status === '1' }"
                                                        class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <!-- <li v-if="index === 0" :class="{ 'hidden': record.status === '1' }">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li> -->
                                                </ul>

                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                <td>
                                    <button class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                        @click="openProforma">
                                        + Estimation
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <!-- Hover Modal -->
                    <customerDataTable v-if="isModalVisible" :showModal="isModalVisible" :modalData="modalData"
                        :modalPosition="modalPosition" @mouseover="toggleHoverModal(true)"
                        @mouseleave="toggleHoverModal(false)" @mousemove="showModal(null, $event)"></customerDataTable>
                </div>
                <!--card view-->
                <div class="m-2 sm:m-4">
                    <div v-if="items_category === 'list'" class="grid grid-cols-1 custom-scrollbar-hidden" :class="{
                        'sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4': showContactInfo == undefined,
                        'sm:grid-cols-1  lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-2 lg:gap-4': showContactInfo == false,
                        'sm:grid-cols-1  lg:grid-cols-1 xl:grid-cols-1 2xl:grid-cols-2 gap-2 lg:gap-4': showContactInfo
                    }">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <div
                                class="max-w-md mx-auto bg-white rounded-xl border border-gray-300 overflow-hidden md:max-w-2xl shadow-lg">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center p-4 py-2">
                                    <!-- Left Side (Can be your dynamic content) -->
                                    <div class="text-xs text-red-500">
                                        <p>{{ calculateDaysAgo(formattedDate(record.current_date, true)) }}</p>
                                    </div>
                                    <!-- Right Side (Actions) -->
                                    <div class="flex space-x-4">
                                        <div class="flex justify-center relative">
                                            <!--convert to invoice-->
                                            <div class="flex justify-left">
                                                <button v-if="!record.editing && record.status === '0'"
                                                    @click="toSale(record)"
                                                    class="text-blue-500 line-clamp-1 px-1 py-1 mr-2 rounded bg-blue-500 text-white px-3 hover:bg-green-700 cursor-pointer">
                                                    Convert To Invoice
                                                </button>
                                                <button
                                                    v-if="!record.editing && record.status == '1' && record['sales']"
                                                    @click="goSales(record['sales'])"
                                                    class="text-gray-500 px-1 py-1 mr-2 rounded bg-gray-500 text-white px-3 hover:bg-gray-600 cursor-pointer">
                                                    {{ record['sales'] && record['sales']['invoice_id'] ?
                                                        '#' + record['sales']['invoice_id'] : '' }}
                                                </button>
                                            </div>
                                            <div class="flex justify-center">
                                                <!--More actions-->
                                                <div class="relative">
                                                    <button @click.stop="displayAction(index)"
                                                        class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                        <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                            size="lg" />
                                                    </button>
                                                </div>
                                                <div v-if="display_option === index" :ref="'dropdown' + index"
                                                    class="z-10 mt-8 absolute bg-slate-200 right-0 divide-y divide-gray-100 rounded-lg shadow-lg items-center"
                                                    style="display: flex; justify-content: center; align-items: center;">
                                                    <ul class="py-1 text-gray-700"
                                                        aria-labelledby="dropdownDefaultButton">
                                                        <li>
                                                            <button v-if="!record.editing" @click="printRecord(record)"
                                                                class="text-violet-700 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-eye" size="lg" />
                                                                <span class="px-2">View</span>
                                                            </button>
                                                        </li>
                                                        <li :class="{ 'hidden': record.status === '1' }">
                                                            <button v-if="!record.editing" @click="startEdit(record)"
                                                                class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-pencil"
                                                                    style="color: #3b82f6;" size="lg" />
                                                                <span class="px-2">Edit</span>
                                                            </button>
                                                        </li>
                                                        <li v-if="index === 0"
                                                            :class="{ 'hidden': record.status === '1' }">
                                                            <button v-if="!record.editing && checkRoles(['admin'])"
                                                                @click="confirmDelete(index)"
                                                                class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                    style="color: #ef4444" />
                                                                <span class="px-2">Delete</span>
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Middle Section -->
                                <div class="px-4 py-2">
                                    <!-- Customer Details (Can be your dynamic content) -->
                                    <div class="flex items-center mb-2 -mt-4">
                                        <!-- <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                            :class="{ 'bg-gray-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-blue-600': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                            {{ record.customers && record.customers.first_name
                                                ?
                                                record.customers.first_name[0].toUpperCase() :
                                                'C' }}
                                        </div> -->
                                        <!-- <div>
                                            <h4 class="text-sm leading-6 font-semibold text-gray-900 mb-1 cursor-pointer"
                                                @click="viewRecordCustomer(record['customers'])">
                                                {{ record.customers && record.customers.first_name ?
                                                    (record.customers.first_name + ' ' +
                                                        (record.customers.last_name ?
                                                            record.customers.last_name : '')) : '' }}</h4>
                                            <p class="text-sm text-gray-500 cursor-pointer"
                                                @click="dialPhoneNumber(record.customers && record.customers.contact_number)">
                                                +91-{{
                                                    record.customers.contact_number }}</p>
                                        </div> -->
                                    </div>

                                    <!-- Invoice Details (Should iterate over your data) -->
                                    <!-- <div class="grid grid-cols-2 gap-2 mb-1">
                                    <div class="bg-gray-100 rounded-md p-2 items-center">
                                        <p class="text-sm text-gray-700 font-semibold">Estimation no: </p>
                                        <p class="text-sm text-gray-500">{{ record['estimate_num'] }}</p>
                                    </div>
                                    <div class="bg-gray-100 rounded-md p-2 items-center">
                                        <p class="text-sm text-gray-700 font-semibold">Estimation Date: </p>
                                        <p class="text-sm text-gray-500">{{ formatDateTime(record['current_date']) }}
                                        </p>
                                    </div>
                                </div> -->
                                    <!-- Invoice Actions (Can be your dynamic actions) -->
                                    <div class="flex justify-between items-center">
                                        <div>
                                            <p v-if="!record.editing && record['estimation']"
                                                class="px-1 py-1 mr-2 rounded cursor-pointer hover:underline"
                                                @click="goEstimation(record['estimation'])">
                                                {{ record['estimation']['estimate_num'] ?
                                                    '#' + record['estimation']['estimate_num'] : '' }}
                                            </p>
                                        </div>
                                        <p class="text-end"><span class="font-semibold">Paid: </span><span
                                                class="text-green-700">
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9'
                                                    : currentCompanyList.currency }}
                                                {{ calculatetotal(record['payment_mode']) }}
                                            </span>
                                        </p>
                                    </div>
                                    <div class="flex justify-between bg-gray-100 rounded-md p-3 py-1">
                                        <div>
                                            <p class="sm:text-xs lg:text-sm text-sm text-gray-700 font-semibold">Grand
                                                Total:</p>
                                            <p class="text- text-gray-900 font-semibold">{{ currentCompanyList &&
                                                currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }} {{ record['grand_total'] }}
                                            </p>
                                        </div>
                                        <div class="flex items-center">
                                            <span
                                                class="px-3 py-1 rounded-full sm:text-xs lg:text-sm text-sm font-medium mr-2"
                                                :class="{ 'bg-green-200  :clasn-100 text-green-900': record['invoice_id'], 'bg-red-100 text-red-800': !record['invoice_id'] }">
                                                {{ record['invoice_id'] ? 'Success' : 'Pending' }}</span>
                                            <div class="bg-gray-100 rounded-md items-center">
                                                <p class="sm:text-xs lg:text-sm text-sm text-gray-700 font-semibold">
                                                    Proforma No: </p>
                                                <p class="text-sm text-gray-500">{{ record['proforma_no'] }}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--no data found-->
                    <div class="text-sm mb-[100px]">
                        <p class="font-bold text-center py-2 text-green-700">
                            <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                            Finished !
                        </p>
                    </div>
                </div>
            </div>
        </div>
        <!--in case empty-->
        <div v-if="!open_skeleton && data && data.length === 0">
            <div class="flex justify-center items-center">
                <img class="w-64 h-64" :src="empty_data" alt="image empty states">
            </div>
            <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
        </div>
    </div>
</template>
<script>
import customerDataTable from '../../dialog_box/customerDataTable.vue';
export default {
    props: {
        isMobile: Boolean,
        data_list: Object,
        companyId: String,
        userId: String,
        selected_category: String,
        items_category: String,
        open_skeleton: Boolean,
        now: Date,
        confirm_del: Boolean,
        customer_data: Object,
        updateModalOpen: Boolean,
        currentCompanyList: Object,
        showContactInfo: Boolean
    },
    components: {
        customerDataTable
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            //---
            showEstModal: false,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            columns: [],
            serviceList: [],
            data: [],
            originalData: [],
            isImportModalOpen: false,
            open_message: false,
            message: '',
            customerList: [],
            //---filter--
            showFilterOptions: false,
            mouseOverIsNot: false,
            filterOptions: ['by Date', 'by Customer', 'by Status', 'Custom'],
            selectedByValue: null,
            lead_filter: false,
            typeList: ['product', 'service'],
            statusList: ['Converted Invoice', 'Pending converted to invoice'],
            filteredBy: {},
            //---api integration---
            pagination: {},
            //--skeleton
            number_of_columns: 7,
            number_of_rows: 10,
            gap: 5,
            open_skeleton_isMobile: false,
            display_option: false,
            open_loader: false,
            //---modal---mouse over---
            isModalVisible: false,
            modalData: {},
            modalPosition: { x: 0, y: 0 },
            actionPosition: { x: 0, y: 0 },
            isHoveringModal: false,
            hoverTimer: null,
            lastMousePosition: { x: 0, y: 0 },
            tolerance: 50, // Tolerance for mouse movement in pixels
        };
    },
    computed: {
        paginatedData() {
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                this.length_category = filteredData.length;
                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const record = ['current_date', 'proforma_no', 'grand_total', 'sales', 'estimation', 'payment'];
            const fields = [];
            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data) {
                for (const key of record) {
                    if (key !== 'id' && key !== 'items' && key !== 'data' && key !== 'company' && key !== 'assign_to' && key !== 'status') { // Exclude the 'id' field
                        const label = formatLabel(key);
                        fields.push({ label, field: key, visible: true });
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
    },
    mounted() {
        if (this.data_list && this.data_list.length >= 0) {
            this.data = [...this.data_list];
        }
    },
    methods: {

        //---add payement value---
        calculatetotal(payment_data) {
            // Helper function to calculate total
            // console.log(payment_data, 'WWWWWWWWWWWWWW happening....');
            const calculateTotal = (data) => data.reduce((total, payment) => total + (payment.paid_amount ? payment.paid_amount : payment.payment_amount ? payment.payment_amount : 0), 0);

            // Check if payment_data is a string
            if (typeof payment_data === 'string') {
                try {
                    let payment_details = JSON.parse(payment_data);

                    if (Array.isArray(payment_details) && payment_details.length > 0) {
                        return calculateTotal(payment_details);
                    }
                } catch (error) {
                    console.error("Error parsing payment_data:", error.message);
                    return 0;
                }

            } else if (Array.isArray(payment_data) && payment_data.length > 0) {
                return calculateTotal(payment_data);
            } else {
                // console.error("Invalid payment_data: must be a non-empty array or a valid JSON string representing a non-empty array.");
                return 0;
            }
        },
        validateDateTime(dateTimeString) {
            const datePart = dateTimeString.substring(0, 10);
            const isValidDate = /^\d{4}-\d{2}-\d{2}$/.test(datePart);
            return isValidDate;
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                // console.log(this.deleteIndex, 'Whatjjjjjj');
                // const indexToDelete = this.data.find((record, i) => i === ((this.recordsPerPage * (this.currentPage - 1)) + this.deleteIndex));
                // console.log(indexToDelete, 'Waht happening the data...!');
                let find_data = this.data[this.deleteIndex];
                if (find_data && find_data.id) {
                    axios.delete(`/proforma_invoices/${find_data.id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            console.log(response.data);
                            // this.data.splice(indexToDelete, 1);                        
                            this.message = response.data.message;
                            this.open_message = true;
                            this.getEstimationList(this.recordsPerPage, this.data.length > 1 ? this.pagination.current_page : this.pagination.current_page - 1 !== 0 ? this.pagination.current_page - 1 : 1);
                        })
                        .catch(error => {
                            console.error('Error', error);
                        })
                }
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;
            if (this.isDropdownOpen) {
                // Add event listener when dropdown is opened
                document.addEventListener('click', this.handleOutsideClick);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },
        handleOutsideClick(event) {
            const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.toggleDropdown();
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        //---lead modal
        openProforma() {
            // Assuming you are within a Vue component method
            // this.$router.push({
            //     name: 'addEstimation',
            //     params: { type: 'add' }
            // });
            // this.showEstModal = true;
            this.$router.push({
                name: 'addProformaInvoice',
                params: { type: 'product' },
                query: { type: 'add' }
            });
        },

        //---close the message--
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---record---
        startEdit(record) {
            // console.log(record, 'Waht happening...!');
            // Navigate to the 'addEstimation' route with parameters for editing the record
            this.$router.push({
                name: 'addProformaInvoice', // Name of the route
                params: { type: 'product' }, // Parameter passed in the route path
                query: {
                    type: 'edit',
                    proforma_no: record.id,
                }
            });
        },
        //---closeLeadModal
        closeEstModal() {
            // console.log('helllllllo');           
            this.showEstModal = false;
        },
        //----collect customer--
        findAndReturnCustomer(id) {
            // console.log(id, 'Waht happenig....@');
            if (this.customerList.length > 0) {
                let findCustomer = this.customerList.find((data) => data.id === id);
                // console.log(findCustomer, 'WWWWWW');
                if (findCustomer) {
                    if (findCustomer.lastName) {
                        return `${findCustomer.firstName} ${findCustomer.lastName} - ${findCustomer.contactNumber}`;
                    }
                    else {
                        return `${findCustomer.firstName} - ${findCustomer.contactNumber}`;
                    }
                }
            }
        },
        //---print product--
        displayProductList(data) {
            if (data.description !== '') {
                return `${data.description} - ${data.qty}`;
            }

        },
        //---to sale--
        toSale(record) {
            this.$router.push({
                name: 'sales-invoice',
                query: { type: 'add', proforma_no: record.id }
            });
        },
        //---to service--
        toService(record) {
            console.log(record, 'to service');
        },
        //---print record---
        printRecord(record) {
            this.$router.push({ name: 'print-preview', query: { type: 'proforma', proforma_no: record.id, back: 'home' } });
        },
        //------Open filter---
        //---filter--- 
        toggleFilterSelected(option) {
            // console.log(option, 'GGGGGGGGGGGGGGG');
            this.selectedByValue = option;
            this.lead_filter = true;
            this.data = this.originalData;
            this.showFilterOptions = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //---filter dropdown
        toggleFilter() {
            this.showFilterOptions = !this.showFilterOptions;
        },
        //--close lead
        closeLeadFilter(searchData) {
            // console.log(searchData, 'What is happening.....!');

            if (searchData) {
                // this.filteredBy = searchData;
                const keysData = Object.keys(searchData);

                let filterTheData = this.data.filter(option => {
                    // Check if all search criteria match
                    return keysData.every(key => {
                        if (key === 'date') {
                            console.log(this.formatDate(searchData.date));
                            return option.current_date === this.formatDate(searchData.date);
                        }
                        if (key === 'customer') {
                            // console.log(this.findAndReturnCustomer(option.customer), 'PPPP');
                            return this.findAndReturnCustomer(option.customer) === searchData.customer;
                        }
                        if (key === 'type') {
                            return option.estimate_type === this.typeList[searchData.type];
                        }
                        if (key === 'status') {
                            return option.status === this.statusList[searchData.type];
                        }
                        return true; // For other keys, consider it as matched
                    });
                });

                if (filterTheData.length > 0) {
                    this.filteredBy = searchData;
                    this.data = filterTheData;
                } else {
                    filterTheData = this.data.filter(option => {
                        return (searchData.date && option.current_date === this.formatDate(searchData.date)) ||
                            (searchData.customer && this.findAndReturnCustomer(option.customer) === searchData.customer) ||
                            (searchData.type && option.estimate_type === this.typeList[searchData.type]) ||
                            (searchData.status && option.status === this.statusList[searchData.status]);
                    });
                    if (filterTheData.length > 0) {
                        this.filteredBy = searchData;
                        this.data = filterTheData;
                    } else {
                        this.message = 'The filter does not match any records..!';
                        this.open_message = true;
                        this.data = this.originalData;
                    }
                }
                // console.log(this.data, 'Final data after filtering:', filterTheData);
            }
            this.lead_filter = false;
        },
        //--get formayt date
        formatDate(dateString) {
            // Create a new Date object using the components in the correct order (year, month - 1, day)
            const date = new Date(dateString);
            // Get the parts of the date (year, month, day) in the correct format
            const formattedYear = date.getFullYear();
            const formattedMonth = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-indexed
            const formattedDay = String(date.getDate()).padStart(2, '0');
            // Concatenate the parts with dashes to form the desired format
            return `${formattedMonth}/${formattedDay}/${formattedYear}`;
        },
        //--hidden dropdown--
        hiddenDropdown() {
            if (!this.mouseOverIsNot) {
                this.showFilterOptions = false;
            }
        },
        mouseOverOption() {
            this.mouseOverIsNot = true;
        },
        mouseLeaveOption() {
            this.mouseOverIsNot = false;
        },
        //---reset filter--
        resetTheFilter() {
            this.data = this.originalData;
            this.filteredBy = {};
        },
        getEstimationList(per_page, page) {
            if (page == 1) {
                this.fetchProformaList({ page, per_page });
            } else {
                this.open_skeleton = true;
                axios.get('/proforma_invoices', { params: { company_id: this.companyId, per_page: per_page, page: page } })
                    .then(response => {
                        // console.log(response.data, 'estimation by list..!');
                        this.open_skeleton = false;
                        this.data = response.data.data;
                        this.pagination = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        visiblePageNumbers() {
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            // console.log('load nex data.....');
            // let send_data = {
            //     type: 'leads', q: this.status_select >= 0 && this.status_select !== 5 ? this.status_select : '', filter: this.followup_select >= 0 ? this.followup_select : '', per_page: this.recordsPerPage, page: page,
            //     customer_id: this.searchByCustomer.id ? this.searchByCustomer.id : ''
            // };
            // if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
            //     // console.log(this.filteredBy, 'helllo');
            //     if (this.filteredBy.customer_id) {
            //         send_data.customer_id = this.filteredBy.customer_id
            //     }
            //     if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
            //         send_data.employer_id = this.filteredBy.assign_to[0].id;
            //     }
            //     if (this.filteredBy.type) {
            //         send_data.category = this.filteredBy.type;
            //     }
            // }
            // // console.log(this.category_type !== null && this.category_type !== 'all', 'Waht happening...!!!');
            // if (this.category_type !== null && this.category_type !== 'all') {
            //     send_data.category_id = this.category_type;
            // }
            // axios.get('/searchs', { params: { ...send_data } })
            axios.get('/proforma_invoices', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---validate the role
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        goEstimation(data) {
            if (data && data.estimate_num && data.id) {
                this.$router.push({ name: 'print-preview', query: { type: 'estimation', est_no: data.id } });
            }
        },
        goSales(data) {
            if (data && data.invoice_id && data.id) {
                this.$router.push({ name: 'print-preview', query: { type: 'sales_home', invoice_no: data.id } });
            }
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        //----table action----
        showModal(index, event) {
            if (index) {
                this.lastMousePosition = { x: event.layerX * 1, y: event.layerY * 1 };
                // Start a timer to check after 5000ms
                this.hoverTimer = setTimeout(() => {
                    if (this.isMouseInSamePosition(event)) {
                        this.modalData = index;
                        this.modalPosition = { x: ((event.layerX * 1) + 25), y: (event.layerY * 1) + 25 };
                        this.isModalVisible = true;
                    }
                }, 200);
            }
        },
        hideModal() {
            clearTimeout(this.hoverTimer); // Clear any existing timer
            setTimeout(() => {
                if (!this.isHoveringModal) {
                    this.isModalVisible = false;
                }
            }, 200);
        },
        toggleHoverModal(status) {
            this.isHoveringModal = status;
            if (!status) {
                this.hideModal();
            }
        },
        isMouseInSamePosition(event) {
            // Allow for a 10-pixel tolerance in mouse movement
            const xDiff = Math.abs(this.lastMousePosition.x - event.layerX);
            const yDiff = Math.abs(this.lastMousePosition.y - event.layerY);
            return xDiff <= this.tolerance && yDiff <= this.tolerance;
        },
    },
    watch: {
        data_list(newValue) {
            if (newValue && newValue.length > 0) {
                this.data = [...newValue];
            }
        }
    },
}
</script>

<style scoped>
.manualStyle {
    overflow: auto;
    height: 100vh;
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
    z-index: 100;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}
</style>