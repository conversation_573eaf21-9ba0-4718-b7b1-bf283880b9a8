<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="text-sm model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="justify-between items-center py-3 px-3 flex set-header-background">
                <h2 class="text-white font-bold text-center flex justify-end text-xl">Add Customer Category</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <div class="p-4">
                <div class="p-2 pl-5 pr-5">
                    <label for="category_name" class="font-bold">Category Name:</label>
                    <input id="category_name" v-model="formValues.category_name" type="text" ref="category_name"
                        class="mt-1 p-2 border border-gray-300 w-full" />
                </div>
                <div class="p-2 pl-5 pr-5">
                    <label for="discount" class="font-bold">Discount:</label>
                    <input id="discount" v-model="formValues.discount" type="number"
                        class="mt-1 p-2 border border-gray-300 w-full" />
                </div>
                <div class="p-2 pl-5 pr-5">
                    <label for="service_statuss" class="font-bold">Status:</label>
                    <select id="service_statuss" v-model="formValues.service_status"
                        class="mt-1 p-2 border border-gray-300 w-full">
                        <option value="" disabled selected>Select status</option>
                        <option value="0">Disabled</option>
                        <option value="1">Enabled</option>
                    </select>
                </div>
                <!-- Buttons -->
                <div class="flex justify-center items-center m-2">
                    <button @click="cancelModal"
                        class="bg-pink-600 rounded text-white p-1 px-4 py-2 hover:bg-pink-500 mr-3">Cancel</button>
                    <button @click="sendModal"
                        class="bg-green-700 rounded text-white  p-1 px-4 py-2  hover:bg-green-600">Save</button>
                </div>
            </div>
        </div>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>
<script>
import axios from 'axios';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'addCustomerCategory',
    props: {
        showModal: Boolean,
        editData: Object,
        companyId: String
    },
    data() {
        return {
            'overlay-active': this.showModal,
            isMobile: false,
            formValues: {},
            isOpen: false,
            open_loader: false,
        }
    },
    methods: {
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        cancelModal() {
            // this.$emit('close-modal');
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        async sendModal() {
            this.open_loader = true;
            let parseData = {};
            if (this.editData) {
                try {
                    const response = await axios.put(`/customer-categories/${this.editData.id}`, {
                        category_name: this.formValues.category_name,
                        discount: this.formValues.discount,
                        service_status: this.formValues.service_status,
                        company_id: this.companyId
                    });
                    // console.log(response.data.data, 'put customer category');
                    this.open_loader = false;
                    parseData = response.data.data;
                    this.updateKeyWithTime('customer_category_update');
                    this.formValues = {};
                } catch (error) {
                    console.error('Error:', error);
                    this.open_loader = false;
                }
            } else {
                try {
                    const response = await axios.post('/customer-categories', {
                        category_name: this.formValues.category_name,
                        discount: this.formValues.discount,
                        service_status: this.formValues.service_status,
                        company_id: this.companyId
                    });
                    // console.log(response.data);
                    this.open_loader = false;
                    parseData = response.data.data;
                    this.formValues = {}
                } catch (error) {
                    console.error('Error:', error);
                    this.open_loader = false;
                }
            }

            this.$emit('close-modal', parseData);
        },

        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        mounted() {
            this.updateIsMobile();
            window.addEventListener('resize', this.updateIsMobile);
        },
        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                // this.category_name = this.editData ? this.editData.category_name : '';
                // this.discount = this.editData ? this.editData.discount : '';
                // this.service_status = this.editData ? this.editData.service_status : '';
                this.formValues = this.editData;
            } else {
                // Reset data when editData is not provided
                this.category_name = '';
                this.discount = '';
                this.service_status = '';
            }
        },
        //--get category---
        getCategoryData() {
            axios.get('/customer-categories', { params: { company_id: this.companyId } })
                .then(response => {
                    console.log(response.data);
                    return response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---post category---
        postCategoryData(value) {
            axios.post('/customer-categories', value)
                .then(response => {
                    console.log(response.data);
                    return response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.$nextTick(() => {
                    if (this.$refs.category_name) {
                        this.$refs.category_name.focus();
                        this.$refs.category_name.click();
                    }
                })
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
    },
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}
</style>