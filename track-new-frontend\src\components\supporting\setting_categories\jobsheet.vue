<template>
    <div class="w-full">
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>
        <div v-if="!open_skeleton" class="non-printable">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4 p-1 sm:p-4">
                <!--Delviver with OTP-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold">Service Delivery Secured with OTP:</label>
                    <label class="inline-flex items-center cursor-pointer mt-2">
                        <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                            @click="toggleSwitch('delivery_otp')"
                            :class="{ 'bg-blue-600': formData.delivery_otp, 'bg-gray-200': !formData.delivery_otp }">
                            <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-6': formData.delivery_otp, 'translate-x-0': !formData.delivery_otp }">
                            </div>
                        </div>
                        <span class="pl-3"
                            :class="{ 'text-green-600': formData.delivery_otp, 'text-red-600': !formData.delivery_otp }">{{
                                formData.delivery_otp ? 'Enabled' : 'Disabled' }}</span>
                    </label>
                </div>
                <!--Service code prfix-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label for="inv_prefix" class="block font-bold">Service Id Prefix</label>
                    <input type="text" id="inv_prefix_group_A" v-model="formData.service_code_prefix"
                        @change="is_updated = true" class="mt-1 p-2 border border-gray-300 w-full"
                        placeholder="Service Id Prefix" />
                </div>

                <!-- Disclaimer Job sheet-->
                <div class="w-full col-span-2">
                    <label for="disclaimer_jobsheet" class="block text-md font-bold">Disclaimer jobsheet for
                        SERVICES</label>
                    <textarea id="disclaimer" v-model="formData.jobsheet_disclaimer" @change="is_updated = true"
                        rows="7"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="Enter job sheet disclaimer"></textarea>
                </div>
                <div class="col-span-2"> <!-- Add informational text to guide the user -->
                    <p class="text-sm text-gray-500 mb-2 text-left">Note: Please include any specific terms or
                        conditions
                        you wish
                        to
                        communicate in the Jobsheet / Servicesheet. Separate each point with a new line.</p>
                </div>

            </div>

            <!-- Save Button - Centered -->
            <button @click="saveData"
                class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 text-lg text-white py-2 px-5 rounded-md mx-auto block hover:bg-green-500 mt-5">
                Save
            </button>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'jobsheet',
    emits: ['is-sales-save', 'updatesalesData'],
    props: {
        companyId: String,
        userId: String,
        isMobile: Boolean,
        store_refresh: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            formData: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            is_updated: false,
        };
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('companies', ['fetchCompanyList']),

        saveData() {
            this.open_loader = true;
            if (Object.keys(this.shareData).length > 0) {
                axios.put(`/invoice_settings/${this.shareData.id}`, { ...this.formData, company_id: this.companyId })
                    .then(response => {
                        console.log(response.data, 'Response update');
                        this.shareData = response.data.data;
                        this.open_loader = false;
                        this.fetchInvoiceSetting(true);
                        this.message = 'Proforma details are updated..!';
                        this.show = true;
                        this.$emit('is-sales-save', false);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            } else {
                this.$emit('updatesalesData');
            }
        },

        filteredDisclaimer(data) {
            if (this.formData.disclaimer !== '') {
                return data.split('\n').filter(line => line.trim() !== '');
            }
        },


        getInitialStoreData(store_data) {
            let parseData = JSON.parse(JSON.stringify(store_data));
            if (parseData && parseData.length > 0) {

                if (parseData.length !== 0 && parseData[0].disclaimer !== '') {
                    // let disclaimerMSG = parseData[0].disclaimer.split('\n');
                    this.formData = {
                        jobsheet_disclaimer: parseData[0].jobsheet_disclaimer, delivery_otp: parseData[0].delivery_otp, service_code_prefix: parseData[0].service_code_prefix && parseData[0].service_code_prefix !== '' ?
                            parseData[0].service_code_prefix : 'SE'
                    };
                    this.shareData = { ...parseData[0] };
                    // console.log(get_company_data, 'What happeniggg.......');                         
                }
            }
            this.open_skeleton = false;
        },
        //--is sms and whatsapp enble set as default---
        toggleSwitch(key) {
            if (key) {
                this.formData[key] = this.formData[key] == 0 ? 1 : 0;
            }
        }
    },
    mounted() {
        if (this.companyId !== null) {
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                this.open_skeleton = true;
                this.getInitialStoreData(this.currentInvoice);
                this.fetchInvoiceSetting();
                setTimeout(() => {
                    this.open_skeleton = false;
                }, 300);
            } else {
                this.open_skeleton = true;
                this.fetchInvoiceSetting();
            }
        }
    },
    watch: {

        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.getInitialStoreData(newValue);
                } else {
                    this.open_skeleton = false;
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchInvoiceSetting();
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.saveData();
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.is_updated = false;
                    this.$emit('is-sales-save', newValue);
                }
            }
        }
    }
};
</script>

<style scoped>
input[type="radio"] {
    /* remove standard background appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* create custom radiobutton appearance */
    display: inline-block;
    width: 20px;
    height: 20px;
    padding: 3px;
    /* background-color only for content */
    background-clip: content-box;
    border: 2px solid #bbbbbb;
    background-color: #e7e6e7;
    border-radius: 50%;
}

/* appearance for checked radiobutton */
input[type="radio"]:checked {
    background-color: #05810f;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
        /* Use the actual background color of your content */
        color: black;
        /* Use the actual text color of your content */
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */
    }

    .non-printable {
        display: none;
    }
}
</style>
