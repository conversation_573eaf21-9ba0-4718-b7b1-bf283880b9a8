// store/modules/customers_list.js
import axios from "axios";

const state = {
  customers_list: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  previousPerPage: null,
  selected_customer: null,
  };

  const mutations = {
      SET_CUSTOMERSLIST(state, { data, pagination}) {
          state.customers_list = {data: data, pagination: pagination};
    },
      RESET_STATE(state) {
        state.customers_list = {};
        state.lastFetchTime = null;
        state.isFetching = false;
        state.selected_customer = null;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    SET_PREVIOUS_PER_PAGE(state, perPage) {
      state.previousPerPage = perPage; // Store the previous per_page value
    },
    SELECTED_CUSTOMER(state, data) {
      state.selected_customer = data;
    }
  };

  const actions = {
    updateCustomersName({ commit }, customers_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update customers_list name
      setTimeout(() => {
        // Commit mutation to update customers_list name
        commit('SET_CUSTOMERSLIST', customers_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchCustomersList({ commit, state, rootState, dispatch }, { page, per_page }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['customer_update']; 
      const iscustomer = rootState.apiUpdates.is_update['customer']; 
      if (state.previousPerPage != per_page) {
        commit('SET_PREVIOUS_PER_PAGE', per_page); // Update the per_page tracking
        commit('SET_LAST_FETCH_TIME', null); // Force a new request if per_page changed
      }      
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (!iscustomer && (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)){
        return; // Skip request if less than 30 seconds have passed since the last request
      }
      
      try {
        const trackNew = localStorage.getItem('track_new');
        const { company_id } = trackNew ? JSON.parse(trackNew) : {};
        if (company_id && company_id !== '') {
          // Set the request status to true (indicating that the request is in progress)
      commit('SET_IS_FETCHING', true);
          axios.get('/customers', { params: { company_id: company_id, page: page, per_page: per_page } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Customers list..!');
              let { data, pagination} = response.data;
              
              commit('SET_CUSTOMERSLIST', { data, pagination });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              dispatch('apiUpdates/updateKeyIsUpdate', { key: 'customer', value: false}, { root: true });
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    //---selected customer data---
    selectedCustomerList({ commit, state }, data) {
      commit('SELECTED_CUSTOMER', data);
    },
  };

  const getters = {
    currentCustomersList(state) {
      return state.customers_list;
    },
    selectedCustomerData(state) {
      return state.selected_customer;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
