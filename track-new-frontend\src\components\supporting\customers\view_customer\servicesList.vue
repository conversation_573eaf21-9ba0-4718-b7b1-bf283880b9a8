<template>
    <div>
        <div v-if="!open_skeleton && data.length > 0" class="m-2">
            <div
                :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="border-b border-gray-400">
                                <!--dynamic-->
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    :class="{ 'hidden': !column.visible }" class="px-2 py-2 text-left">
                                    <p>{{ column.label }}</p>
                                </th>
                                <th class="px-2 py-2 leading-none">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(record, index) in data" :key="index"
                                class="border-b border-gray-400 hover:bg-gray-200 cursor-pointer">
                                <td class="py-2 text-center" v-for="(column, index) in columns" :key="index" :class="{
                                    'hidden': !column.visible, 'w-[110px]': column.field === 'status', 'px-1': column.field !== 'status'
                                }" @click="viewRecord(record)">
                                    <span v-if="column.field === 'notification'">
                                        {{ parseNotification(record[column.field]).join(', ') }}
                                    </span>
                                    <!-- <span>{{serviceTrack[Number(record[column.field])]}}</span> -->
                                    <span v-if="column.field === 'status' && record[column.field] !== null" :class="{
                                        'bg-amber-400': column.field === 'status' && record[column.field] == 0,
                                        'bg-red-300': column.field === 'status' && record[column.field] == 1,
                                        'bg-sky-500': column.field === 'status' && record[column.field] == 2,
                                        'bg-violet-500': column.field === 'status' && record[column.field] == 3,
                                        'bg-[#84cc16]': column.field === 'status' && record[column.field] == 4,
                                        'bg-[#14532d]': column.field === 'status' && record[column.field] == 5,
                                        'bg-red-500': column.field === 'status' && record[column.field] == 6,
                                        'bg-[#4d7c0f]': column.field === 'status' && record[column.field] == 7,
                                    }" class="font-normal text-xs cursor-pointer px-1 py-1 rounded text-white whitespace-nowrap"
                                        @click="viewRecord(record)">
                                        {{ record[column.field] == 0 ? 'Service Taken' : record[column.field] == 1 ?
                                            'Hold'
                                            : record[column.field] == 2 ? 'In Progrss' : record[column.field] == 3 ?
                                                'New Estimate' : record[column.field] == 4 ? 'Ready to deliver' :
                                                    record[column.field] ==
                                                        5 ? 'Delivered' : record[column.field] == 6 ? 'Cancelled' : record[column.field]
                                                            ==
                                                            7 ? 'Completed' : '' }}
                                    </span>
                                    <span
                                        v-if="!Array.isArray(record[column.field]) && typeof record[column.field] !== 'object' && column.field !== 'notification' && column.field !== 'notification' && column.field !== 'status' && column.field !== 'invoice_id' && column.field !== 'created_at'">
                                        {{column.field === 'expected_date' ?
                                            /^\d{4}-\d{2}-\d{2}$/.test(record[column.field].substring(0, 10)) &&
                                                !isNaN(Date.parse(record[column.field].substring(0, 10))) ?
                                                record[column.field].substring(0, 10) : '' : column.field === "materials" ?
                                                typeof record[column.field] === 'string' ?
                                                    JSON.parse(record[column.field]).reduce((total, item) => total + (item.qty *
                                                        item.price), 0)
                                                    : record[column.field] === 'object' && Array.isArray(record[column.field]) &&
                                                    record[column.field].reduce((total, item) => total + (item.qty * item.price), 0)
                                                :
                                                record[column.field]}}</span>
                                    <span
                                        v-if="Array.isArray(record[column.field]) && record[column.field].length > 0 && column.field !== 'notification' && column.field !== 'status' && column.field !== 'invoice_id'"
                                        v-for="(opt, i) in record[column.field]" :key="i">
                                        <template
                                            v-if="typeof opt === 'object' && opt !== null && column.field !== 'assign_to' && column.field !== 'created_by' && column.field !== 'updated_by' && column.field !== 'materials'">
                                            {{Object.keys(opt).map(key => `${key}: ${opt[key]}`).join(', ')}}
                                        </template>
                                        <template
                                            v-else-if="column.field === 'assign_to' && record[column.field].length > 0">
                                            {{ opt.name }}<br>
                                        </template>

                                    </span>
                                    <span
                                        v-if="!Array.isArray(record[column.field]) && typeof record[column.field] === 'object' && record[column.field] !== null && column.field !== 'notification' && column.field !== 'status' && column.field !== 'invoice_id' && column.field !== 'servicecategory'">
                                        {{ record[column.field].first_name + ' ' + record[column.field].last_name +
                                            ' - ' + record[column.field].contact_number }}<br>
                                    </span>
                                    <span class="hover:text-blue-700"
                                        @mouseover="showModal({ invoice_id: record['invoice_id'], due_amount: record['due_amount'], grand_total: record['grand_total'], sale_id: record['sale_id'] }, $event)"
                                        @mouseleave="hideModal" v-if="column.field === 'invoice_id'">
                                        {{ record[column.field] }}</span>
                                    <span v-if="column.field === 'category'">{{
                                        record['servicecategory'].service_category
                                    }}</span>
                                    <span v-if="column.field === 'service_type'">
                                        <!-- {{ JSON.parse(record.service_data) ?
                                        JSON.parse(record.service_data)[column.field] : '' }} -->
                                    </span>
                                    <span v-if="column.field === 'problem_title'" @click="viewRecord(record)">
                                        <!-- {{JSON.parse(record.service_data) ?
                                            Array.isArray(JSON.parse(record.service_data)[column.field]) ?
                                                JSON.parse(record.service_data)[column.field].join(', ') :
                                                JSON.parse(record.service_data)[column.field] : ''}} -->
                                        {{ parseJson(record.service_data, column.field) }}
                                    </span>
                                    <span v-if="column.field === 'created_at'"
                                        :title="calculateDaysAgo(formattedDate(record[column.field]))">
                                        {{ formattedDate(record[column.field]) }}
                                    </span>
                                </td>

                                <td class="text-center">
                                    <div class="flex justify-center">
                                        <div class="flex relative">
                                            <button v-if="!record.editing" @click="viewRecord(record)" title="View"
                                                class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-solid fa-eye" style="color: #8b5cf6;" />
                                            </button>
                                            <button v-if="!record.editing" @click="editRecord(record)" title="Edit"
                                                class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                            </button>
                                            <button v-if="!record.editing && checkRoles(['admin'])" title="Delete"
                                                @click="confirmDelete(index)"
                                                class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                    style="color: #ef4444" />
                                            </button>
                                            <!-- <button @click.stop="displayAction(index)" class="px-1 py-1">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                    class="bg-blue-300 text-blue-800 px-1 py-1 rounded" size="lg" />
                                            </button> -->
                                        </div>
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-7 right-0 absolute bg-white divide-y divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="viewRecord(record)"
                                                        class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-eye"
                                                            style="color: #8b5cf6;" />
                                                        <span class="px-2">View</span>
                                                    </button>
                                                </li>
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="editRecord(record)"
                                                        class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                        <span class="px-2">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    <!-- Hover Modal -->
                    <customerDataTable v-if="isModalVisible" :showModal="isModalVisible" :modalData="modalData"
                        :modalPosition="modalPosition" @mouseover="toggleHoverModal(true)"
                        @mouseleave="toggleHoverModal(false)" @mousemove="showModal(null, $event)"></customerDataTable>
                </div>
                <!--card view-->
                <div v-if="items_category === 'list'" class="grid grid-cols-1 custom-scrollbar-hidden" :class="{
                    'sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4': showContactInfo == undefined,
                    'sm:grid-cols-1  lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-2 lg:gap-4': showContactInfo == false,
                    'sm:grid-cols-1  lg:grid-cols-1 xl:grid-cols-1 2xl:grid-cols-2 gap-2 lg:gap-4': showContactInfo
                }">
                    <div v-for="(record, index) in data" :key="index" class="w-full">
                        <!-- Card Container -->
                        <div
                            class="max-w-md mx-auto bg-white rounded-xl border border-gray-300  overflow-hidden md:max-w-2xl relative shadow-lg">
                            <div class="flex justify-between items-center px-4 py-2">
                                <div>
                                    <p class="flex items-center cursor-pointer text-red-500 text-xs"
                                        :title="formattedDate(record['created_at'])">
                                        {{ calculateDaysAgo(formattedDate(record['created_at'])) }}
                                    </p>
                                </div>

                                <div class="flex justify-between items-center text-xs">
                                    <!--Status-->
                                    <div>
                                        <p v-if="record['status'] !== null" :class="{
                                            'bg-amber-400': record['status'] == 0,
                                            'bg-red-300': record['status'] == 1,
                                            'bg-sky-500': record['status'] == 2,
                                            'bg-violet-500': record['status'] == 3,
                                            'bg-[#84cc16]': record['status'] == 4,
                                            'bg-[#14532d]': record['status'] == 5,
                                            'bg-red-500': record['status'] == 6,
                                            'bg-[#4d7c0f]': record['status'] == 7,
                                        }" class="font-normal text-xs cursor-pointer px-1 py-1 rounded text-white whitespace-nowrap"
                                            @click="viewRecord(record)">
                                            {{ record['status'] == 0 ? 'Service Taken' : record['status'] == 1 ?
                                                'Hold'
                                                : record['status'] == 2 ? 'In Progrss' : record['status'] == 3 ?
                                                    'New Estimate' : record['status'] == 4 ? 'Ready to deliver' :
                                                        record['status'] ==
                                                            5 ? 'Delivered' : record['status'] == 6 ? 'Cancelled' : record['status']
                                                                ==
                                                                7 ? 'Completed' : '' }}
                                        </p>
                                    </div>
                                    <!--menu-->
                                    <div class="flex justify-end relative">
                                        <button @click.stop="displayAction(index)" class="pl-3">
                                            <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                class="bg-blue-200 px-1 py-1 rounded" />
                                        </button>
                                        <!--dropdown-->
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-7 absolute bg-white divide-y divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="viewRecord(record)"
                                                        class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-eye"
                                                            style="color: #8b5cf6;" />
                                                        <span class="px-2">View</span>
                                                    </button>
                                                </li>
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="editRecord(record)"
                                                        class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                        <span class="px-2">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="md:flex">
                                <div class="p-4 pt-0">
                                    <div class="mt-1 grid grid-cols-3 gap-1">
                                        <!-- Customer Details -->
                                        <!-- <div class="flex items-center col-span-2">
                                        <div class="flex-shrink-0 h-12 w-12 rounded-full flex items-center justify-center text-white font-bold"
                                            :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-yellow-600': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                            {{ record.customer && record.customer.first_name ?
                                                record.customer.first_name[0].toUpperCase() : 'C' }}
                                        </div>
                                        <div class="ml-4">
                                            <div class="text-sm leading-6 font-semibold text-gray-900 line-clamp-2">
                                                {{
                                                    record.customer && record.customer.first_name ?
                                                        record.customer.first_name
                                                        + ' ' + (record.customer.last_name ? record.customer.last_name : '') :
                                                        '' }}
                                            </div>
                                            <p class="text-sm text-gray-500 cursor-pointer"
                                                @click="dialPhoneNumber(record['customer'].contact_number)">+91 - {{
                                                    record.customer.contact_number }}</p>
                                        </div>
                                    </div> -->
                                        <!--service code-->
                                        <div
                                            class="flex justify-between items-center text-xs items-center px-2 py-1  col-span-2">
                                            <p v-if="record['service_id']"
                                                class="bg-gray-200 rounded-full text-gray-700 p-2 text-center">#
                                                {{
                                                    record['service_id'] }}
                                            </p>
                                            <!-- <h4 class="font-medium text-gray-400 mr-2">Service Code:</h4> -->
                                            <p class="bg-gray-200 rounded-full text-gray-700 p-2 text-center"># {{
                                                record['service_code'] }}
                                            </p>
                                        </div>
                                        <!--attachment-->
                                        <div>
                                            <div v-if="hasDocument(record)" @click="viewRecord(record)">
                                                <img :src="getLastDocumentUrl(record)" alt="image"
                                                    class="image-container01 items-center" />
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-1 grid grid-cols-3 gap-1 mt-2">
                                        <!-- Service Details -->
                                        <div class="col-span-2 border-l-4 px-1 items-center">
                                            <h4 class="text-md leading-6 font-bold text-sky-900 cursor-pointer line-clamp-2"
                                                @click="viewRecord(record)">
                                                <!-- {{ JSON.parse(record.service_data) ?
                                                    Array.isArray(JSON.parse(record.service_data)['problem_title']) ?
                                                        JSON.parse(record.service_data)['problem_title'].join(', ') :
                                                        JSON.parse(record.service_data)['problem_title'] ?
                                                            JSON.parse(record.service_data)['problem_title'] :
                                                            record.servicecategory.service_category + ' ' + 'Service' :
                                                    record.servicecategory.service_category + ' ' + 'Service' }} -->
                                                {{ parseJson(record.service_data, 'problem_title') }}
                                            </h4>
                                            <p class="text-sm text-sky-500">{{ record.servicecategory.service_category
                                                }}
                                            </p>
                                        </div>
                                        <div class="text-xs items-center px-1 border-l-4 ">
                                            <h4 class="font-medium text-gray-400 mr-2">Due Date:</h4>
                                            <p class="text-gray-700">{{
                                                /^\d{4}-\d{2}-\d{2}$/.test(record['expected_date'].substring(0, 10)) &&
                                                    !isNaN(Date.parse(record['expected_date'].substring(0, 10))) ?
                                                    formatDate(record['expected_date'].substring(0, 10)) : '' }}</p>
                                        </div>

                                    </div>
                                    <!-- Assigned To -->
                                    <div class="flex mt-2">
                                        <!-- <h4 class="font-medium text-gray-900">Assigned To</h4> -->
                                        <div class="relative">
                                            <!-- First Circle -->
                                            <div class="absolute  left-1 z-0">
                                                <i class="fas fa-user-circle text-blue-300 text-lg"></i>

                                            </div>
                                            <!-- Second Circle -->
                                            <div class="absolute  left-4 z-[2]">
                                                <i class="fas fa-user-circle text-red-300 text-lg"></i>
                                            </div>
                                            <!-- Third Circle -->
                                            <div class="absolute  left-7 z-[3]">
                                                <i class="fas fa-user-circle text-purple-300 text-lg"></i>
                                            </div>
                                        </div>
                                        <div class="flex flex-wrap gap-2 ml-[55px]">
                                            <p v-if="Array.isArray(record['assign_to']) && record['assign_to'].length > 0"
                                                v-for="(opt, i) in record['assign_to']" :key="i">
                                                <span v-if="record['assign_to'].length > 0"
                                                    class="inline-flex items-center px-3 py-1 rounded mr-2 mb-2" :class="{
                                                        'bg-blue-300 text-blue-800': i === 0 || i % 3 === 0,
                                                        'bg-green-300 text-green-600': i % 2 === 0 && 1 > 1,
                                                        'bg-purple-300 text-purple-800': (i === 1 || i % 1 === 0) && i % 2 !== 0 && i % 3 !== 0
                                                    }">
                                                    {{ opt.name }}
                                                </span>
                                            </p>
                                            <p v-else class="inline-flex items-center px-3 py-1">- -</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!--no data found-->
                    <div class="text-sm mb-[100px]">
                        <p class="font-bold text-center py-2 text-green-700">
                            <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                            Finished !
                        </p>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
            <Loader :showModal="open_loader"></Loader>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>
<script>
import customerDataTable from '../../dialog_box/customerDataTable.vue';
import { mapState, mapActions, mapGetters } from 'vuex';
export default {
    props: {
        isMobile: Boolean,
        data: Object,
        companyId: String,
        userId: String,
        selected_category: String,
        items_category: String,
        open_skeleton: Boolean,
        now: Date,
        confirm_del: Boolean,
        updateModalOpen: Boolean,
        showContactInfo: Boolean
    },
    components: {
        customerDataTable
    },
    data() {
        return {
            display_option: false,
            deleteIndex: null,
            open_loader: false,
            empty_data: '/images/dashboard/empty.svg',
            //--toaster---
            show: false,
            type_toaster: 'warning',
            message: '',
            //---modal---mouse over---
            isModalVisible: false,
            modalData: {},
            modalPosition: { x: 0, y: 0 },
            actionPosition: { x: 0, y: 0 },
            isHoveringModal: false,
            hoverTimer: null,
            lastMousePosition: { x: 0, y: 0 },
            tolerance: 50, // Tolerance for mouse movement in pixels            
        }
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        dynamicFields() {
            const fields = [];
            let order = [];
            if (this.selected_category === 'services') {
                order = ['created_at', 'service_id', 'problem_title', 'category', 'expected_date', 'service_code', 'assign_to', 'service_type', 'invoice_id', 'status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'leads') {
                order = ['lead_date', 'title', 'assign_to', 'assign_date', 'lead_type', 'follow_up', 'lead_status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'amcs') {
                order = ['created_at', 'title', 'assign_to', 'amc_payment_type', 'amc_details', 'number_of_interval', 'number_of_service', 'amc_status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'sales') {
                order = ['current_date', 'invoice_no', 'invoice_to', 'invoice_type', 'discount', 'due_amount', 'discount_type', 'shipping'];
            } else if (this.selected_category === 'estimations') {
                order = ['current_date', 'estimate_num', 'estimate_type', 'grand_total', 'invoice_id'];
            }

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data && this.data.length > 0) {
                order.forEach((key) => {
                    // Check if the key exists in the data object
                    if (this.data && this.data.length > 0) { //---&& this.data[0].hasOwnProperty(key)
                        const label = formatLabel(key);
                        if (key !== 'created_by' && key !== 'updated_by') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                });
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
    },
    mounted() {
        this.fetchLocalDataList();
    },
    methods: {
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        parseJson(serviceData, field) {
            try {
                const parsedData = JSON.parse(serviceData);
                if (Array.isArray(parsedData[field])) {
                    return parsedData[field].join(', ');
                } else {
                    return parsedData[field] || '';
                }
            } catch (error) {
                // If JSON parsing fails, return the raw data or handle as needed
                // console.error('Invalid JSON:', serviceData);
                return serviceData; // or return '' if you prefer to display nothing
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //--format date    
        formatDate(dateString) {
            if (!dateString || typeof dateString !== 'string') {
                return '';
            }
            // Parse the input date string as a Date object
            const dateObject = new Date(dateString);

            if (isNaN(dateObject.getTime())) {
                return ''; // Return empty string if parsing fails
            }

            // Define an array of month names
            const monthNames = ["January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"];

            // Extract day, month, and year components from the date object
            const day = dateObject.getDate();
            const monthIndex = dateObject.getMonth(); // Months are zero-based (0 = January)
            const year = dateObject.getFullYear();

            // Format the date as "Month day, year"
            const formattedDateData = `${monthNames[monthIndex]} ${day}, ${year}`;

            return formattedDateData;
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },

        //----services data--- 
        //--image
        hasDocument(record) {
            if (record.document) {
                try {
                    // Parse the document only once
                    const parsedDocument = JSON.parse(record.document);
                    return Array.isArray(parsedDocument) && parsedDocument.length > 0;
                } catch (error) {
                    // console.error('Failed to parse document:', error);
                    return false;  // Return false if parsing fails
                }
            } else {
                return false;  // Return false if there's no document
            }
        },
        getLastDocumentUrl(record) {
            if (this.hasDocument(record)) {
                const documents = JSON.parse(record.document);
                return documents[documents.length - 1].url;
            }
            return null;
        },
        //---formated display date---
        formattedDate(timestamp) {
            //---formatted display date---
            const date = new Date(timestamp);
            date.setHours(date.getHours() + 5); // Add 5 hours
            date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        editRecord(record) {
            // Handle view action
            // console.log("Edit", record);
            if (!record.invoice_id || record.invoice_id == '') {
                this.$router.push({ name: 'service-category-view', params: { viewId: record.id, type: record.servicecategory.service_category, id: record.servicecategory.id } });
            } else {
                this.message = 'This service has already been invoiced, so we cannot edit it';
                this.show = true;
            }

        },
        viewRecord(record) {
            // Handle edit action
            // console.log("view", record);
            // this.$emit('showAddServiceComponent', record);
            this.$router.push({ name: 'service-data-view', params: { serviceId: record.id, type: record.servicecategory.service_category, id: record.servicecategory.id } });
        },
        deleteRecord() {
            if (this.deleteIndex !== null) {
                this.open_loader = true;
                // console.log(this.data[this.deleteIndex], 'YYYY');
                axios.delete(`/services/${this.data[this.deleteIndex].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // console.log(response.data);
                        this.deleteIndex = null;
                        this.open_loader = false;
                        window.location.reload();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })

            }
            this.open_confirmBox = false;
        },
        confirmDelete(index) {
            if (this.data[index] && this.data[index].invoice_id && this.data[index].invoice_id !== '') {
                this.message = `This service cannot be deleted because it is linked to an existing invoice (Invoice ID: ${this.data[index].invoice_id}). Please delete the associated sales record before attempting to delete this service.`;
                this.type_toaster = 'warning';
                this.show = true;
            } else {
                this.deleteIndex = index;
                this.$emit('openconfirmbox', index);
            }
            // console.log(index, 'What happening...', this.data);

        },
        cancelDelete() {
            this.deleteIndex = null;
        },
        //----table action----
        showModal(index, event) {
            if (index) {
                this.lastMousePosition = { x: event.layerX * 1, y: event.layerY * 1 };
                // Start a timer to check after 5000ms
                this.hoverTimer = setTimeout(() => {
                    if (this.isMouseInSamePosition(event)) {
                        this.modalData = index;
                        this.modalPosition = { x: ((event.layerX * 1) + 25), y: (event.layerY * 1) + 25 };
                        this.isModalVisible = true;
                    }
                }, 200);
            }
        },
        hideModal() {
            clearTimeout(this.hoverTimer); // Clear any existing timer
            setTimeout(() => {
                if (!this.isHoveringModal) {
                    this.isModalVisible = false;
                }
            }, 200);
        },
        toggleHoverModal(status) {
            this.isHoveringModal = status;
            if (!status) {
                this.hideModal();
            }
        },
        isMouseInSamePosition(event) {
            // Allow for a 10-pixel tolerance in mouse movement
            const xDiff = Math.abs(this.lastMousePosition.x - event.layerX);
            const yDiff = Math.abs(this.lastMousePosition.y - event.layerY);
            return xDiff <= this.tolerance && yDiff <= this.tolerance;
        },
        performAction(action) {
            // Perform different actions based on the action parameter
            if (action === 'edit') {
                // Logic to edit the record
                console.log('Editing record', this.currentRecord);
            } else if (action === 'delete') {
                // Logic to delete the record
                console.log('Deleting record', this.currentRecord);
            }
            this.hideModal(); // Hide modal after action
        },
        //--role based on--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },

    },
    watch: {
        confirm_del: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.deleteRecord();
                } else {
                    this.cancelDelete();
                }
            }
        },

    }
}
</script>
<style>
.image-container01 {
    width: 150px;
    height: 70px;
    /* Set your desired fixed height */
    object-fit: cover;
    /* Maintain aspect ratio and crop as needed */
    object-position: center;
    border: 1px solid rgb(218, 218, 218);
    /* box-shadow: 1px 1px 2px 2px rgb(82, 81, 81); */
}
</style>