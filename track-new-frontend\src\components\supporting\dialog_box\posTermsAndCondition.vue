<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full set-header-background px-4 py-4 rounded rounded-b-none">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Terms and condition
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block mt-4 text-sm bg p-5">
                <!-- Disclaimer -->
                <div class="w-full mb-4">
                    <textarea id="disclaimer" v-model="formValues.disclaimer" rows="5"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="Enter Terms and conditions"></textarea>
                    <!-- Add informational text to guide the user -->
                    <p class="text-xs text-gray-500 mb-2">Note: Please include any specific terms or conditions.
                        Separate each point with a new line.</p>
                    <p v-if="message !== ''" class="text-red-500 font-bold text-sm py-2">{{ message }}</p>
                </div>

                <!--buttons-->
                <div class="flex justify-center items-center">
                    <button v-if="shouldShowAddButton"
                        class="border rounded text-white bg-green-700 font-bold px-4 py-2 hover:bg-green-600"
                        @click="saveTerms">Save</button>
                    <button @click="closeModal"
                        class="bg-red-600 text-white font-bold rounded ml-5 px-2 py-2 hover:bg-red-700">Cancel</button>
                </div>
            </div>
        </div>
        <dialogAlert :show-modal="isMessage" :message="message" @close="closeMessageDialog"></dialogAlert>
    </div>
</template>


<script>
import dialogAlert from './dialogAlert.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    components: {
        dialogAlert
    },
    props: {
        showModal: Boolean,
        termsAndCon: Object,
        companyId: String,
        userId: String,
        typeOfInvoice: String,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            isMessage: false,
            message: ''
        };
    },
    computed: {
        shouldShowAddButton() {
            // If terms and conditions are not the same, show the Add button
            if (this.typeOfInvoice === 'estimation') {
                return this.formValues.disclaimer !== this.termsAndCon[0].est_disclaimer;
            } else if (this.typeOfInvoice === 'proforma') {
                return this.formValues.disclaimer !== this.termsAndCon[0].proforma_disclaimer;
            } else if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                return this.formValues.disclaimer !== this.termsAndCon[0].disclaimer;
            }
        },
        ...mapGetters('invoice_setting', ['currentInvoice']),
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-Modal');
                this.message = '';
            }, 300);
        },
        //---close message---
        closeMessageDialog() {
            this.isMessage = false;
        },
        //---save terms and condition---
        saveTerms() {
            // console.log(this.termsAndCon, 'RRRRR');
            if (this.formValues.disclaimer && this.formValues.disclaimer !== '') {
                let sent_data = {};
                if (this.typeOfInvoice === 'estimation') {
                    sent_data.est_disclaimer = this.formValues.disclaimer;
                } else if (this.typeOfInvoice === 'proforma') {
                    sent_data.proforma_disclaimer = this.formValues.disclaimer;
                } else if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                    sent_data.disclaimer = this.formValues.disclaimer;
                }
                if (Object.keys(sent_data) && Object.keys(sent_data).length > 0) {
                    axios.put(`/invoice_settings/${this.termsAndCon[0].id}`, { ...sent_data, company_id: this.companyId })
                        .then(response => {
                            // console.log(response.data, 'invoice setting');
                            this.fetchInvoiceSetting();
                            setTimeout(() => {
                                this.$emit('close-Modal', response.data.data);
                            }, 300);
                        })
                        .catch(error => {
                            console.error('Error', error);
                        })
                }
            } else {
                // this.isMessage = true;
                this.message = 'Field is empty, Please fill terms and conditions..!';
            }
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                // console.log(this.typeOfInvoice, 'RRRRRRRRRRRR', this.termsAndCon);
            }, 100);
        },
        formValues: {
            deep: true,
            handler(newValue) {
                this.message = '';
            }
        },
        termsAndCon: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'What happening...!', this.typeOfInvoice);
                if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                    this.formValues.disclaimer = newValue[0].disclaimer;
                }
                else if (this.typeOfInvoice === 'estimation') {
                    this.formValues.disclaimer = newValue[0].est_disclaimer;
                }
                else if (this.typeOfInvoice === 'proforma') {
                    this.formValues.disclaimer = newValue[0].proforma_disclaimer;
                }
            }
        }

    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>