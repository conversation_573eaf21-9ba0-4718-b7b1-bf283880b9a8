<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateWhatsappSettingsAPIRequest;
use App\Http\Requests\API\UpdateWhatsappSettingsAPIRequest;
use App\Models\WhatsappSettings;
use App\Repositories\WhatsappSettingsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class WhatsappSettingsController
 * @package App\Http\Controllers\API
 */

class WhatsappSettingsAPIController extends AppBaseController
{
    /** @var  WhatsappSettingsRepository */
    private $whatsappSettingsRepository;

    public function __construct(WhatsappSettingsRepository $whatsappSettingsRepo)
    {
        $this->whatsappSettingsRepository = $whatsappSettingsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/whatsapp_settings",
     *      summary="getWhatsappSettingsList",
     *      tags={"WhatsappSettings"},
     *      description="Get all WhatsappSettings",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/WhatsappSettings")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
       
        $Query = WhatsappSettings::where('company_id', $companyId);


        if ($perPage === 'all') {
            $perPage = $Query->count();
        }    

        $result = $Query->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $result->items(), // Get the paginated items
            'pagination' => [
                'total' => $result->total(),
                'per_page' => $result->perPage(),
                'current_page' => $result->currentPage(),
                'last_page' => $result->lastPage(),
                'from' => $result->firstItem(),
                'to' => $result->lastItem(),
            ]
        ];

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/whatsapp_settings",
     *      summary="createWhatsappSettings",
     *      tags={"WhatsappSettings"},
     *      description="Create WhatsappSettings",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/WhatsappSettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateWhatsappSettingsAPIRequest $request)
    {
        $input = $request->all();

        $whatsappSettings = $this->whatsappSettingsRepository->create($input);

        return $this->sendResponse($whatsappSettings->toArray(), 'Whatsapp Settings saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/whatsapp_settings/{id}",
     *      summary="getWhatsappSettingsItem",
     *      tags={"WhatsappSettings"},
     *      description="Get WhatsappSettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of WhatsappSettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/WhatsappSettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var WhatsappSettings $whatsappSettings */
        $whatsappSettings = $this->whatsappSettingsRepository->find($id);

        if (empty($whatsappSettings)) {
            return $this->sendError('Whatsapp Settings not found');
        }

        return $this->sendResponse($whatsappSettings->toArray(), 'Whatsapp Settings retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/whatsapp_ettings/{id}",
     *      summary="updateWhatsappSettings",
     *      tags={"WhatsappSettings"},
     *      description="Update WhatsappSettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of WhatsappSettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/WhatsappSettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateWhatsappSettingsAPIRequest $request)
    {
        $input = $request->all();

        /** @var WhatsappSettings $whatsappSettings */
        $whatsappSettings = $this->whatsappSettingsRepository->find($id);

        if (empty($whatsappSettings)) {
            return $this->sendError('Whatsapp Settings not found');
        }

        $whatsappSettings = $this->whatsappSettingsRepository->update($input, $id);

        return $this->sendResponse($whatsappSettings->toArray(), 'WhatsappSettings updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/whatsapp_settings/{id}",
     *      summary="deleteWhatsappSettings",
     *      tags={"WhatsappSettings"},
     *      description="Delete WhatsappSettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of WhatsappSettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var WhatsappSettings $whatsappSettings */
        $whatsappSettings = $this->whatsappSettingsRepository->find($id);

        if (empty($whatsappSettings)) {
            return $this->sendError('Whatsapp Settings not found');
        }

        $whatsappSettings->delete();

        return $this->sendSuccess('Whatsapp Settings deleted successfully');
    }
}
