<template>
    <div v-if="showModal" class="text-sm fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50">
        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen sm:pb-[150px] lg:pb-[70px] pb-[150px]"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="justify-between items-center flex py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-xl">
                    {{ type == 'edit' ? 'Edit A Lead' : 'Create A Lead' }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!-- Content based on selected option -->
            <div class="mt-5 pl-4 pr-2"> <!--grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4-->
                <!---customer details-->
                <div class="flex mt-5">
                    <div class="mr-2 w-10 flex justify-center items-center" :title="'customer'">
                        <img :src="user_name" alt="customer" class="w-7 h-7">
                    </div>
                    <div class="relative flex w-full mr-2">
                        <label for="customer"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.customer || isInputFocused.customer, 'text-blue-700': isInputFocused.customer }">Customer<span
                                v-if="formValues.customer || isInputFocused.customer"
                                class="text-red-600">*</span></label>
                        <input id="customer" v-model="formValues.customer" @input="handleDropdownInput()"
                            placeholder="Select the customer"
                            @focus="handleDropdownInput(), isDropdownOpen = true, isInputFocused.customer = true"
                            @blur="closeDropdown('customer')"
                            @keydown.enter="handleEnterKey('customer', filteredCustomerOptions)"
                            @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="customer"
                            class="w-full border py-2 pl-2 pr-10 leading-5 text-gray-900 focus:ring-0 rounded rounded-tr-none rounded-br-none outline-none" />

                        <!-- Right-aligned icon based on isDropdownOpen -->
                        <span v-if="!formValues.customer || formValues.customer === ''"
                            class="absolute py-3 ml-[80%] sm:ml-[78%] lg:ml-[85%] cursor-pointer"
                            @click="isDropdownOpen = !isDropdownOpen">
                            <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </span>
                        <span v-if="formValues.customer && formValues.customer !== ''" title="Reset customer"
                            class="absolute py-3 ml-[80%] sm:ml-[78%] lg:ml-[85%] cursor-pointer"
                            @click="resetFormData('customer')">
                            <span class="material-icons text-sm text-red-600 hover:text-red-700">
                                cancel
                            </span>
                        </span>
                        <div class="w-1/10 bg-teal-600 text-center text-lg py-2 px-2 border font-bold text-white cursor-pointer rounded-tr rounded-br"
                            @click="openModal">+</div>
                    </div>
                </div>
                <!-- Display filtered options as the user types  && formValues.customer && formValues.customer.length > 1 -->
                <div v-if="isDropdownOpen"
                    class="absolute mt-1 max-h-60 w-3/4 ml-10 overflow-auto rounded-md bg-white py-1  shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                    style="z-index: 100;" @mousedown.prevent="preventBlur('customer')">
                    <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                        @click="selectDropdownOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                        class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                        {{ option.first_name + (option.last_name ? ' ' + option.last_name : '') }} - {{
                            option.contact_number }}
                    </p>
                    <!-- Add New Customer button -->
                    <button
                        v-if="filteredCustomerOptions && filteredCustomerOptions.length === 0 && formValues.customer && formValues.customer.length > 1 && !isExistOption()"
                        @click="openModal"
                        class="w-full text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                        + Add Customer
                    </button>
                </div>

                <!---lead Date-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'lead date'">
                        <img :src="date" alt="lead_date" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <!--tooltip-->
                        <div v-if="tooltip_focus && tooltip_focus === 'date'"
                            class="absolute flex flex-col items-center group-hover:flex -mt-8">
                            <span
                                class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                <p>Lead date is fixed cannot be change</p>
                            </span>
                            <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                        </div>
                        <label for="lead_date"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.lead_date || isInputFocused.date, 'text-blue-700': isInputFocused.date }">Lead
                            Date</label>
                        <input id="lead_date" v-model="formValues.lead_date" type="datetime-local" placeholder=" "
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                            @focus="tooltip_focus = 'date'" @blur="tooltip_focus = null" readonly />
                    </div>
                </div>
                <!---lead Type-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'lead type'">
                        <img :src="category" alt="leadtype_id" class="w-7 h-7">
                    </div>
                    <div class="flex w-full mr-2 relative">
                        <label for="leadtype_id"
                            class="text-sm font-bold absolute left-2 top-3 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.leadtype_id !== undefined && formValues.leadtype_id >= 0) || isInputFocused.leadtype_id, 'text-blue-700': isInputFocused.leadtype_id }">Lead
                            Type <span class="text-red-600">*</span></label>
                        <select id="leadtype_id" v-model="formValues.leadtype_id" ref="lead_type"
                            class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                            @focus="isInputFocused.leadtype_id = true">
                            <option disabled value="">Select lead type</option>
                            <option v-for="(opt, index) in leadTypeList" :key="index" :value="opt.id">{{ opt.name }}
                            </option>
                        </select>
                        <!--
                            :class="{ 'text-gray-300': formValues.leadtype_id === '' }" -->
                        <!-- Dropdown menu -->
                        <div id="dropdownHover"
                            class="z-10 hidden bg-white divide-y divide-gray-100 rounded-lg shadow w-44 dark:bg-gray-700">
                            <ul class="py-2 text-sm text-gray-700 dark:text-gray-200"
                                aria-labelledby="dropdownHoverButton">
                                <li v-for="(opt, index) in leadTypeList" :key="index"
                                    @click="formValues.leadtype_id = index, formValues.leadtype_name = option.name">
                                    {{ opt.name }}
                                </li>

                            </ul>
                        </div>

                        <span v-if="formValues.leadtype_id && formValues.leadtype_id !== ''" title="Reset lead type"
                            class="absolute py-4 ml-[85%] sm:ml-[86%] lg:ml-[88%] cursor-pointer"
                            @click="resetFormData('leadtype_id')">
                            <span class="material-icons text-sm text-red-600 hover:text-red-700">
                                cancel
                            </span>
                        </span>
                        <div class="bg-teal-600 w-1/10 text-center text-lg py-2 px-2 mt-1 border font-bold text-white cursor-pointer rounded-tr rounded-br"
                            @click="openLeadList('type')">+</div>
                    </div>
                </div>
                <!---lead Title-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'amc title'">
                        <img :src="lead_title_img" alt="title" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="title"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.title || isInputFocused.title, 'text-blue-700': isInputFocused.title }">
                            Lead Title<span v-if="formValues.title || isInputFocused.title"
                                class="text-red-600">*</span></label>
                        <input id="title" v-model="formValues.title" type="text" placeholder="Enter the lead title"
                            ref="lead_title"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                            @focus="isInputFocused.title = true" />
                    </div>
                </div>
                <!---Lead Description-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'description'">
                        <img :src="notes" alt="description" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="description"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.description || isInputFocused.description, 'text-blue-700': isInputFocused.description }">Description</label>
                        <textarea id="description" v-model="formValues.description" rows="2"
                            @focus="isInputFocused.description = true" placeholder="Add description"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"></textarea>
                    </div>
                </div>
                <!---source of lead-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'amc source'">
                        <img :src="source_img" alt="source" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="source"
                            class="text-sm font-bold absolute -top-3 sm:-top-2 text-xs bg-white px-1 text-blue-700 transition-top linear duration-300">
                            Source OF Lead</label>
                        <input id="source" v-model="formValues.source" type="text"
                            placeholder="Enter the lead source (e.g. Facebook, Google, Phone)" ref="amc_source"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                            @focus="isInputFocused.source = true" />
                    </div>
                </div>
                <!---Assign to--->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'assign To'">
                        <img :src="assign" alt="assign_to" class="w-7 h-7">
                    </div>
                    <div class="relative w-full mr-2">
                        <label for="assign_to"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.assign_to || isInputFocused.assign_to, 'text-blue-700': isInputFocused.assign_to }">Assign
                            to</label>
                        <div class="border py-2 px-2 flex flex-wrap"
                            :class="{ 'border-blue-300': isInputFocused.assign_to === true }">

                            <div v-for="(selectedOption, index) in formValues.assign_to" :key="index"
                                class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                {{ selectedOption.name }}
                                <span @click="removeOption(selectedOption)"
                                    class="text-red-500 font-semibold cursor-pointer">x</span>
                            </div>
                            <input type="text" v-model="search" @input="filterOptions" @focus="filterOptions"
                                @click="filterOptions" ref="search" @blur="hideOptions" placeholder="Select staff"
                                class="h-[35px] mt-1 outline-none border rounded-lg px-2"
                                @keydown.enter="handleEnterKey('multiple', filteredOptions())"
                                @keydown.down.prevent="handleDownArrow(filteredOptions())"
                                @keydown.up.prevent="handleUpArrow(filteredOptions())" />
                        </div>
                        <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                            v-if="showOptions" :class="{ 'h-auto': isInputFocused.assign_to === true }">
                            <div v-for="(option, index) in filteredOptions()" :key="index"
                                class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                :class="{ 'bg-green-300': index === selectedIndex }"
                                @click="selectOptionMultiple(option)">
                                {{ option.name }}
                            </div>
                            <button v-if="showAddNew !== null && search.length > 1"
                                class="bg-green-700 text-white hover:bg-green-600 px-3 py-1 rounded-lg"
                                @click="openModalEmployee">Add New</button>
                        </div>
                    </div>
                    <div v-if="(!formValues.assign_to || formValues.assign_to.length === 0) && this.userId"
                        class="absolute text-xs py-2 mt-[80px] ml-11">
                        <button @click="formValues.assign_to = [{ id: this.userId, name: 'Assign to me' }]"
                            class="border border-blue-500 text-blue-500 rounded px-2">Assign to me</button>
                    </div>
                </div>
                <!---lead Status-->
                <div class="flex items-center mt-10">
                    <div class="mr-2 w-10" :title="'lead status'">
                        <img :src="statusOf" alt="lead_status" class="w-7 h-7">
                    </div>
                    <div class="flex w-full mr-2 relative">
                        <label for="leadstatus_id"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.leadstatus_id !== undefined && formValues.leadstatus_id >= 0) || isInputFocused.leadstatus_id, 'text-blue-700': isInputFocused.leadstatus_id }">Lead
                            Status</label>

                        <select id="leadstatus_id" v-model="formValues.leadstatus_id"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none"
                            @focus="isInputFocused.leadstatus_id = true" @blur="isInputFocused.leadstatus_id = false">
                            <!-- <option v-for="(opt, index) in leadStatusList" :key="index" :value="opt.id">{{ opt.name }}
                            </option> -->
                            <option disabled value="">Select status</option>
                            <option value="0">Open</option>
                            <option value="1">Progress</option>
                            <option value="2">Completed</option>
                            <option value="3">Cancelled</option>
                            <option value="4">Hold</option>
                        </select>
                        <!-- <div class="w-1/10 text-center text-lg py-2 px-2 mt-1 border font-bold text-green-700 cursor-pointer hover:border-green-700 rounded-tr rounded-br"
                            @click="openLeadList('status')">+</div> -->
                    </div>
                </div>
                <!---next followup Date-->
                <div>
                    <p v-if="!next_follow" class="px-11 pt-3 text-blue-700 hover:underline text-xs py-2 cursor-pointer"
                        @click="toggleFollowup">Create next
                        followup</p>
                    <transition name="fade">
                        <div v-if="next_follow" class="flex items-center mt-5">
                            <div class="mr-2 w-10" :title="'assign date'">
                                <img :src="date" alt="assign_date" class="w-7 h-7">
                            </div>
                            <div class="w-full mr-2 relative">
                                <label for="assign_date"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.follow_date || isInputFocused.follow_date, 'text-blue-700': isInputFocused.follow_date }">Next
                                    follow
                                    up
                                    Date & Time</label>
                                <input id="follow_date" v-model="formValues.follow_date" type="datetime-local"
                                    v-datepicker placeholder="" ref="dateandtime"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                            </div>
                            <p>
                                <span class="material-icons text-red-600 hover:text-red-700 cursor-pointer"
                                    @click="toggleFollowup">delete</span>
                            </p>
                        </div>
                    </transition>
                    <transition name="fade">
                        <div v-if="next_follow" class="flex items-center mt-5">
                            <div class="mr-2 w-10" :title="'specification'">
                                <img :src="notes" alt="specification" class="w-7 h-7">
                            </div>
                            <div class="w-full mr-2 relative">
                                <label for="specification"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.follow_description || isInputFocused.follow_description, 'text-blue-700': isInputFocused.follow_description }">Next
                                    followp specification
                                </label>
                                <input id="follow_description" v-model="formValues.follow_description" type="text"
                                    placeholder="Enter specification"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                            </div>
                            <p>
                                <span class="material-icons text-white">close</span>
                            </p>
                        </div>
                    </transition>
                </div>
            </div>
        </div>
        <!-- Buttons -->
        <div class="fixed bottom-0 bg-white flex justify-center items-center sm:w-1/2 lg:w-1/3 w-full py-1 transform ease-in-out duration-300"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen, 'mb-[70px]': isMobile }">
            <button @click="cancelModal"
                class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white p-1 py-2 pl-10 pr-10 mr-8">Cancel</button>
            <button @click="sendModal"
                class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white p-1 py-2 pl-10 pr-10">Save</button>
        </div>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModal" :userName="customerName"
            :editData="editData" :type="typeOfRegister"></customerRegister>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <employeeRegister :show-modal="showModal_employee" @close-modal="closeModalEmployee" :type="'add'"
            :user_name="EmployeeName">
        </employeeRegister>
        <leadTypeAndStatusList :showModal="leadTypeOrStatusList" :type="typeOfList"
            :categoriesData="typeOfList === 'type' ? leadTypeList : leadStatusList" @closeModal="closeLeadList">
        </leadTypeAndStatusList>
    </div>
    <Loader :showModal="open_loader"></Loader>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>

<script>
import customerRegister from './customerRegister.vue';
import dialogAlert from './dialogAlert.vue';
import employeeRegister from './employeeRegister.vue';
import leadTypeAndStatusList from './leadTypeAndStatusList.vue';
import noAccessModel from './noAccessModel.vue';
import { mapGetters, mapActions } from 'vuex';

export default {
    name: 'addLead',
    emits: ['closeLeadModal'],
    components: {
        customerRegister,
        dialogAlert,
        employeeRegister,
        leadTypeAndStatusList,
        noAccessModel
    },
    props: {
        showModal: Boolean,
        editData: Object,
        type: String,
        userName: String,
        companyId: String,
        userId: String,
        customer_id: {type: [String, Number]},
    },
    data() {
        return {
            showModal_add_service: false,
            'overlay-active': this.showModal,
            isMobile: false,
            formValues: { leadstatus_id: 0 },
            isMessageDialogVisible: false,
            message: null,
            isOpen: false,
            updatedData: null,
            //--toaster--
            show: false,
            type_toaster: 'warning',
            //----icons
            user_name: '/images/service_page/User.png',
            date: '/images/service_page/schedule.png',
            assign: '/images/service_page/personService.png',
            statusOf: '/images/service_page/statusIcon.png',
            notes: '/images/service_page/Writing.png',
            category: '/images/service_page/Add.png',
            lead_title_img: '/images/service_page/issue_description.png',
            source_img: '/images/service_page/cooperation.png',
            //---
            isInputFocused: { assign_to: true, customer: true, leadtype_id: true, title: true, description: true, follow_date: true, follow_description: true },
            typeOfRegister: 'add',
            //---
            isDropdownOpen: false,
            customer_list: [],
            filteredCustomerOptions: [],
            customerName: null,
            showModal_customer: false,
            search: '',
            employeeList: [],
            selectedIndex: 0,
            mouseDownOnDropdown: false,
            showAddNew: null,
            showOptions: false,
            filteredEmployee: [],
            showModal_employee: false,
            EmployeeName: '',
            leadTypeOrStatusList: false,
            typeOfList: '',
            leadTypeList: [],
            leadStatusList: [],
            assign_to_backup: [],
            pagination_data: {},
            open_loader: false,
            next_follow: false,
            tooltip_focus: null,
            updated_by: null,
            //---no access---
            no_access: false,
        }
    },
    computed: {
        ...mapGetters('employess', ['currentEmployee']),
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('leadType', ['currentLeadType']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
    },
    methods: {
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('employess', ['fetchEmployeeList']),
        ...mapActions('leadType', ['fetchLeadTypeList']),
        getCurrentDate() {
            const currentDate = new Date();
            const month = String(currentDate.getMonth() + 1).padStart(2, '0');
            const day = String(currentDate.getDate()).padStart(2, '0');
            const year = currentDate.getFullYear();
            return `${year}-${month}-${day}`;
        },
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeLeadModal');
            }, 300);
        },
        //---deep copy the data---
        deepCopy(obj) {
            if (obj === null || typeof obj !== 'object') {
                return obj; // Return primitives and null as is
            }

            if (Array.isArray(obj)) {
                // Handle arrays
                return obj.map(item => this.deepCopy(item));
            }

            // Handle objects
            const newObj = {};
            for (let key in obj) {
                if (obj.hasOwnProperty(key)) {
                    newObj[key] = this.deepCopy(obj[key]);
                }
            }
            return newObj;
        },
        sendModal() {
            if (this.getplanfeatures('leads')) {
                this.no_access = true;
            } else {
                if (this.formValues.customer_id && this.formValues.customer_id !== '' && this.formValues.title && this.formValues.title !== '' && this.formValues.customer_id !== '' && this.formValues.leadtype_id >= 0 && this.formValues.leadtype_id !== '') {
                    this.open_loader = true;
                    if ((this.formValues.follow_date || this.formValues.follow_description) && this.next_follow) {
                        let obj = { description: this.formValues.follow_description, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.getDefaultDate() };
                        this.formValues.follow_up = JSON.stringify([obj]);
                        delete this.formValues.follow_date;
                        delete this.formValues.follow_description;
                    } else {
                        delete this.formValues.follow_date;
                        delete this.formValues.follow_description;
                    }
                    let send_data = this.deepCopy(this.formValues);
                    // console.log(send_data.assign_to, 'EEEEEEEEEEEEEEEEEEEEEEEEEEE');
                    if (send_data.assign_to && send_data.assign_to.length > 0) {
                        send_data.assign_to = send_data.assign_to.map(opt => opt.id).join(', ');
                    }
                    // console.log(send_data, 'FSFSFSFSF');
                    axios.post(`/leads`, { ...send_data, company_id: this.companyId })
                        .then(response => {
                            // console.log(response.data.data);
                            // this.updatedData = response.data.data;
                            this.open_loader = false;
                            // this.openMessageDialog(response.data.message);
                            // this.$emit('closeLeadModal', response.data.data);
                            setTimeout(() => {
                                this.$emit('closeLeadModal', response.data.data);
                            }, 300);
                            this.formValues = {};
                        })
                        .catch(error => {
                            console.error('Error for post', error);
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                        })

                } else {
                    if (!this.formValues.customer_id || this.formValues.customer_id === '') {
                        // this.openMessageDialog(`Please select the Customer`);
                        this.$refs.customer.focus();
                        this.message = `Please select the Customer`;
                        this.show = true;
                    }
                    else if (!this.formValues.leadtype_id || this.formValues.leadtype_id === '') {
                        // this.openMessageDialog(`Please select the lead type or ADD new by on click '+' icon`);
                        this.message = `Please select the lead type or ADD new by on click '+' icon`;
                        this.show = true;
                        this.$refs.lead_type.focus();
                    }
                    else if (!this.formValues.title || this.formValues.title === '') {
                        // this.openMessageDialog(`Please enter the Lead Title`);
                        this.message = `Please enter the Lead Title`;
                        this.show = true;
                        this.$refs.lead_title.focus();
                    }
                }
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                this.formValues = this.editData;
            } else {
                this.formValues = {};
            }
        },
        //---Add new user--
        userAddNew() {
            // console.log(this.userName);
            if (/^\d+$/.test(this.userName)) {
                this.formValues.contact_number = this.userName;
            } else {
                if (this.userName) {
                    const strSplit = this.userName.split(' ');
                    // console.log(strSplit, 'PPPPPPPP');
                    this.formValues.first_name = strSplit[0];
                    if (strSplit[1]) {
                        this.formValues.last_name = strSplit[1];
                    }
                }
            }
        },
        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.updatedData) {
                this.$emit('closeLeadModal', this.updatedData);
                this.updatedData = null;
                // this.initializeData();
            } else {
                // this.$emit('close-modal');
                // this.initializeData();
            }
        },

        //---customer--
        handleDropdownInput() {
            // console.log(this.customer_list, 'This is customer list..@')
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                    if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                        this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    }
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                            (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                    );
                    if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                        this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    }
                    // console.log(this.filteredCustomerOptions, 'What happening...!');
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                if (this.customer_list.length > 0) {
                    this.filteredCustomerOptions = this.customer_list;
                } else {
                    this.filteredCustomerOptions = [];
                }
            }

            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    this.openModal();
                    // this.openModal(product_name);
                }
            }
            else if (type === 'multiple') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectOptionMultiple(optionArray[this.selectedIndex]);
                }
                else {
                    this.openModalEmployee();
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        openModal() {
            this.customerName = this.formValues.customer;
            this.showModal_customer = true;
            this.isDropdownOpen = false;
        },
        //----cloase customer register---
        closeModal(data) {
            // console.log(data, 'TTTTTTTTTTT');
            if (data) {
                this.formValues.customer_id = data.id;
                if (data.last_name) {
                    this.formValues.customer = data.first_name + '' + data.last_name + ' - ' + data.contact_number;
                } else {
                    this.formValues.customer = data.first_name + ' - ' + data.contact_number;
                }
                this.customer_list.push(data);
            }

            this.showModal_customer = false;
        },
        //----customer dropdown--
        closeDropdown(type) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                // this.isInputFocused.customer = false;

            }
        },
        //---customers dropdown
        selectDropdownOption(option) {
            this.formValues.customer_id = option.id;
            this.formValues.customer = option.first_name + (option.last_name ? ' ' + option.last_name : '') + ' - ' + option.contact_number;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
        },
        preventBlur(type) {
            if (type && type === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            }
        },
        //---multiple dropdown---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            // this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.mobile_number + ''.includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination_data.employee.current_page !== this.pagination_data.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.mobile_number && option.mobile_number + ''.includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination_data.employee.current_page !== this.pagination_data.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    }
                } else if (enteredValue.length > 1) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination_data.employee.current_page !== this.pagination_data.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination_data.employee.current_page !== this.pagination_data.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    }
                }
            }
            // Return an empty array if no options match the filter
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.name).includes(option.name)) : this.employeeList;
        },
        //---extern data---
        extendsData() {
            this.getEmployeeList(1, 'all');
            this.filteredOptions();
        },

        selectOptionMultiple(option) {
            if (!this.formValues.assign_to) {
                this.formValues.assign_to = []; // Initialize assign_to as an array if it's not already
            }
            if (!this.formValues.assign_to.map(emp => emp.name).includes(option.name)) {
                this.assign_to_backup.push(option.id);
                this.formValues.assign_to.push(option); // Add the selected option to assign_to
            }
            this.search = ''; // Clear the search input
            this.showOptions = false; // Hide the options dropdown

            this.$nextTick(() => {
                // Focus on the search input after selecting an option
                if (this.$refs['search']) {
                    this.$refs['search'].focus();
                    this.$refs['search'].click();
                }
            });
        },
        removeOption(option) {
            // console.log(option, 'YYYYYYYYYYYYYYYY');
            this.formValues['assign_to'] = this.formValues['assign_to'].filter(selected => selected.name !== option.name);
        },

        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
                // this.isInputFocused.assign_to = false;
            }, 300); // Add delay to allow click events on options
        },
        openModalEmployee() {
            this.showModal_employee = true;
            this.EmployeeName = this.search;
            this.showOptions = false;
            this.showAddNew = null;
        },
        handleFocus() {
            // Set focus to the input field
            if (this.customer_list && this.customer_list.length > 0) {
                const inputField = this.$refs.customer;
                if (inputField) {
                    inputField.focus();
                    inputField.click();
                }
            }
        },
        closeModalEmployee(data) {
            if (data) {
                if (this.formValues.assign_to) {
                    this.formValues.assign_to.push(data);
                } else {
                    this.formValues.assign_to = [];
                    this.formValues.assign_to.push(data);
                }
                this.employeeList.push(data);
            }
            this.showModal_employee = false;
            this.showOptions = false;
            this.search = '';

        },
        //---validate is already exist customer--
        isExistOption() {
            let findDataExist = this.customer_list.find((option) => {
                if (this.formValues['customer']) {
                    let splitData = this.formValues['customer'].split('-');
                    if (splitData.length > 1 && splitData[0]) {
                        // let nameValue = data.first_name + (data.last_name ? ' ' + data.last_name : '').toLowerCase();
                        return (option.last_name ? (option.first_name + ' ' + option.last_name).toLowerCase().includes(splitData[0].toLowerCase()) :
                            option.first_name.toLowerCase().includes(splitData[0].toLowerCase()));
                    }
                }
            });
        },
        //---Lead list for type and status--
        closeLeadList(data) {
            // console.log(data, 'Waht happening..........by closing the modal..!');
            if (data !== '' && data) {
                if (this.typeOfList === 'type') {
                    // console.log('This is type of lead...!');
                    data.forEach((dataVal) => {
                        let isNotAlreadyExist = this.leadTypeList.findIndex((opt) => opt.name === dataVal)
                        if (isNotAlreadyExist !== -1) {
                            this.leadTypeList.push(dataVal);
                            console.log(this.leadStatusList, 'Status', this.leadTypeList);
                        }
                    });
                } else if (this.typeOfList === 'status') {
                    data.forEach((dataVal) => {
                        let isNotAlreadyExist = this.leadStatusList.findIndex((opt) => opt.name === dataVal)
                        if (isNotAlreadyExist !== -1) {
                            this.leadStatusList.push(data);
                        }
                    });
                }
            }
            this.leadTypeOrStatusList = false;
        },
        //---Add lead Type or Status
        openLeadList(type) {
            console.log(type, 'What happening...!');
            this.typeOfList = type;
            this.leadTypeOrStatusList = true;
        },
        getCustomerList(page, per_page) {
            // console.log(page, per_page, 'What happening..!', this.companyId)
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // Handle response
                    // console.log(response.data, 'Customer list..!');
                    this.customer_list = response.data.data;
                    this.pagination_data.customer = response.data.pagination;
                    this.handleFocus();
                    if (this.customer_list.length > 2) {
                        // Sorting the array alphabetically based on the 'name' key
                        this.customer_list.sort((a, b) => {
                            // Convert both names to lowercase to ensure case-insensitive sorting
                            const nameA = a.first_name.toLowerCase();
                            const nameB = b.first_name.toLowerCase();
                            // Compare the two names
                            if (nameA < nameB) {
                                return -1;
                            }
                            if (nameA > nameB) {
                                return 1;
                            }
                            return 0;
                        });
                    }
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        getEmployeeList(page, per_page) {
            axios.get('/employees', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // Handle response
                    // console.log(response.data.data);
                    this.employeeList = response.data.data;
                    this.pagination_data.employee = response.data.pagination;
                    this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
                    if (this.employeeList.length > 2) {
                        // Sorting the array alphabetically based on the 'name' key
                        this.employeeList.sort((a, b) => {
                            // Convert both names to lowercase to ensure case-insensitive sorting
                            const nameA = a.name.toLowerCase();
                            const nameB = b.name.toLowerCase();
                            // Compare the two names
                            if (nameA < nameB) {
                                return -1;
                            }
                            if (nameA > nameB) {
                                return 1;
                            }
                            return 0;
                        });
                    }
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        getLeadStatusList() {
            //---lead status list
            axios.get('/lead_statuses', {
                params: {
                    company_id: this.companyId
                }
            })
                .then(response => {
                    // Handle response
                    console.log(response.data.data);
                    this.leadStatusList = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        getLeadTypeList() {
            //---lead type list---
            axios.get('/lead_types', {
                params: {
                    company_id: this.companyId
                }
            })
                .then(response => {
                    // Handle response
                    console.log(response.data.data);
                    this.leadTypeList = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        getDefaultDate(status) {
            const currentDate = new Date();
            currentDate.setHours(currentDate.getHours() + 5);
            currentDate.setMinutes(currentDate.getMinutes() + 30);
            if (status) {
                this.formValues.follow_date = currentDate.toISOString().slice(0, 16);
            }
            return currentDate.toISOString().slice(0, 16);
        },
        toggleFollowup() {
            this.next_follow = !this.next_follow;
            if (this.next_follow) {
                this.getDefaultDate(true);
            }
        },
        resetFormData(key) {
            // console.log(key, 'GGGGGG', 'RRR', this.formValues[key],'TTTTTTT', (key && this.formValues[key]));
            if (key && this.formValues[key]) {
                this.formValues[key] = '';
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
    },
    mounted() {
        this.updateIsMobile();
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.updated_by = { id: dataParse.user_id, name: dataParse.name ? dataParse.name : '' };
        }
        if (this.type !== 'edit') {
            let findCurrentDate = this.getCurrentDate();
            this.formValues.lead_date = this.getDefaultDate();
            this.formValues.assign_date = findCurrentDate;
            this.formValues.leadstatus_id = 0;
            this.formValues.leadtype_id = '';
        }
        this.getDefaultDate(true);
        // Use nextTick to wait for the DOM to be updated before accessing the input field
        window.addEventListener('resize', this.updateIsMobile);
    },

    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    if (this.type !== 'edit' && !this.formValues.lead_date) {
                        let findCurrentDate = this.getCurrentDate();
                        this.formValues.lead_date = this.getDefaultDate();
                        this.formValues.assign_date = findCurrentDate;
                        this.formValues.leadstatus_id = 0;
                        this.formValues.leadtype_id = '';
                    }
                    if (this.currentLeadType && this.currentLeadType.length === 0) {
                        this.fetchLeadTypeList();
                    } else {
                        this.fetchLeadTypeList();
                    }
                    if (this.currentCustomer && this.currentCustomer.length === 0) {
                        this.fetchCustomerList();
                    } else {
                        this.fetchCustomerList();
                    }
                    if (this.currentEmployee && this.currentEmployee.length === 0) {
                        this.fetchEmployeeList();
                    } else {
                        this.fetchEmployeeList();
                    }
                    //---get lead status---
                    // this.getLeadStatusList();
                    //--get lead type--
                    // this.getLeadTypeList();
                    if (this.currentLeadType && this.currentLeadType.length > 0) {
                        this.leadTypeList = this.currentLeadType;
                    }
                    //---customers list---
                    // this.getCustomerList(1, 'all');
                    if (this.currentCustomer && this.currentCustomer.length > 0) {
                        this.customer_list = this.currentCustomer;
                    }
                    //---Employee list----
                    // this.getEmployeeList(1, 'all');
                    if (this.currentEmployee && this.currentEmployee.length > 0) {
                        this.employeeList = this.currentEmployee;
                    }
                    //---select customer----
                    if (this.customer_list && this.customer_list.length > 0 && this.customer_id) {
                        let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                        if (selected_customer) {
                            this.selectDropdownOption(selected_customer);
                        }
                    }
                    this.handleFocus();
                }
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
        userName: {
            handler() {
                this.userAddNew();
                // this.handleFocus();
            },
        },
        currentLeadType: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.leadTypeList = newValue;
                }
            }
        },
        currentCustomer: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.customer_list = newValue;
                    if (this.customer_list && this.customer_list.length > 0 && this.customer_id) {
                        let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                        if (selected_customer) {
                            this.selectDropdownOption(selected_customer);
                        }
                    }
                }
            }
        },
        currentEmployee: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.employeeList = newValue;
                }
            }
        },
        customer_id: {
            deep: true,
            handler(newValue) {
                if (newValue && this.customer_list && this.customer_list.length > 0) {
                    let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                    if (selected_customer) {
                        this.selectDropdownOption(selected_customer);
                    }
                }
            }
        }
    },
}
</script>
<style scoped>
/* Modal styling */
.modal {
    position: fixed;
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Make modal content scrollable on mobile */
.modal-content {
    overflow-y: auto;
    /* Add this line to make the content scrollable */
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;
    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>
