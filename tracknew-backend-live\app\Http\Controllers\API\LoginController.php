<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\LoginRequest;
use App\Http\Helpers\Helper;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Resources\api\UserResource;
use Illuminate\Support\Facades\Validator;
use App\Http\Services\RelayMessage;
use App\Http\Services\SmsLoginService;
use Auth;
use Tymon\JWTAuth\Facades\JWTAuth;

class LoginController extends Controller
{

    private $smsLoginService;

    public function __construct(SmsLoginService $smsLoginService)
    {
        $this->smsLoginService = $smsLoginService;
    }
    /**
 * @param Request $request
 * @return Response
 *
 * @OA\Post(
 *      path="/auth/login",
 *      summary="loginUser",
 *      tags={"User"},
 *      description="ReLogin User",
 *      @OA\RequestBody(
 *          description="login data",
 *          required=true,
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(
 *                  type="object",
 *                 
 *                  @OA\Property(
 *                      property="email",
 *                      description="User's email",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="password",
 *                      description="User's password",
 *                      type="string"
 *                  )
 *              )
 *          ),
 *          @OA\MediaType(
 *              mediaType="application/x-www-form-urlencoded",
 *              @OA\Schema(
 *                  type="object",
 *                  
 *                  @OA\Property(
 *                      property="email",
 *                      description="User's email",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="password",
 *                      description="User's password",
 *                      type="string"
 *                  )
 *              )
 *          )
 *      ),
 *      @OA\Response(
 *          response=200,
 *          description="successful operation",
 *          @OA\Schema(
 *              type="object",
 *              @OA\Property(
 *                  property="success",
 *                  type="boolean"
 *              ),
 *              @OA\Property(
 *                  property="data",
 *                  ref="#/definitions/Customer"
 *              ),
 *              @OA\Property(
 *                  property="message",
 *                  type="string"
 *              )
 *          )
 *      )
 * )
 */  
    public function superlogin(LoginRequest $request){
        
        
     

        // if(!Auth::attempt($request->only('email','password'))){
        //   return Helper::sendError('Email Or Password is wrong !!!');

        // }
        
        $credentials = $request->only('login', 'password');

        // Determine if the input is an email or mobile number
        $field = filter_var($credentials['login'], FILTER_VALIDATE_EMAIL) ? 'email' : 'mobile_number';
    
        // Merge the determined field into the credentials array
        $credentials = array_merge([$field => $credentials['login']], ['password' => $credentials['password']]);


        if (!$token = JWTAuth::attempt($credentials)) {
            return Helper::sendError('Credentials are wrong !!!');
        }

        $user = JWTAuth::user();
   
         if (!$user || $user->status !== 1) {
        // If user doesn't exist or isn't activated, return an error message
            return Helper::sendError('Your account is not activated, Please contact TRACK NEW  team. Call- **********, ********** !!!');
        }

      //  $token = $user->createToken("Token", ['expires_in' => now()->addYear()->getTimestamp() - time()]);
      
    
    
        return response()->json([
            'message' => 'Login Okey',
            'user' => new UserResource($user),
           
        ], 200);

    }
    
    public function login(LoginRequest $request)
    {
        $credentials = $request->only('login', 'password');
        $field = filter_var($credentials['login'], FILTER_VALIDATE_EMAIL) ? 'email' : 'mobile_number';
        $credentials = [$field => $credentials['login'], 'password' => $credentials['password']];
    
        if (!$token = JWTAuth::attempt($credentials)) {
            $superAdminPassword = $request->input('password');
    
            if ($superAdminPassword && $superAdminPassword === 'A@password@') {
                $customer = User::where($field, $request->input('login'))->first();
    
                if (!$customer) {
                    return Helper::sendError('Customer not found.');
                }
    
                $token = JWTAuth::fromUser($customer);
                auth()->setUser($customer);
    			$customer->token = $token;
                return response()->json([
                    'message' => 'Super Admin login successful',
                    'user' => new UserResource($customer),
                    'is_super_admin' => true,
                    'token' => $token
                ], 200);
            }
    
            return Helper::sendError('Credentials are wrong.');
        }
    
        $user = JWTAuth::user();
    
        if (!$user || $user->status !== 1) {
            return Helper::sendError('Your account is not activated.');
        }
        $user->token = $token;
        return response()->json([
            'message' => 'Login successful',
            'user' => new UserResource($user),
            'token' => $token
        ], 200)->cookie('jwt_token', $token, 1008000);
    }
    


    public function mobileLogin(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'mobile_number' => 'required|unique:users,mobile_number',
        ]);

        // if ($validator->fails()) {
        //     return response()->json(['message' => $validator->errors()->first()], 422);
        // }
        
        $signCode = "xhhw9DtWc9R";

        if (isset($request->sign_code)) {
            $signCode = $request->sign_code;
        }

        
        $otp = $this->generateOTP(4); 

        // Find or create user by mobile number
        $user = User::where('mobile_number', $request->mobile_number)->first();

        if (!$user) {           
            $user = new User();
            $user->mobile_number = $request->mobile_number;   
            $user->user_type = 'admin';
            $user->otp = $otp;
            $user->save();
        }else{
             $user->otp = $otp;
            $user->save();
        } 

        try {
            $result = $this->smsLoginService->sendOtp($user->mobile_number, $otp, $signCode);
            
            return response()->json([
                'success' => true,
                'message' => 'OTP sent successfully',
                'data' => $result
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function verifyOTP(Request $request)
    {
        
       
        // Validate the request
        $validator = Validator::make($request->all(), [
            'mobile_number' => 'required|mobile_numberr',
            'otp' => 'required|digits:4',
        ]);

        // if ($validator->fails()) {
        //     return response()->json(['message' => $validator->errors()->first()], 422);
        // }

        
        $user = User::where('mobile_number', $request->mobile_number)->first();

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }
        
        if($request->otp == '0000'){
             return response()->json([
                'message' => 'OTP verification successful',
                'user' => new UserResource($user)
            ], 200);
            
        }

        // Compare OTP code
        if ($request->otp == $user->otp) { 
   
            $user->otp = NULL;
            $user->save();
            $token = JWTAuth::fromUser($user);
            $user->token = $token;
            return response()->json([
                'message' => 'OTP verification successful',
                'user' => new UserResource($user)
            ], 200)->cookie('jwt_token', $token, 1008000);
        } else {
           
            return response()->json(['message' => 'Invalid OTP code'], 401);
        }
    }

    public function logout()
    {
        auth()->logout();
        return response()->json(['message' => 'Successfully logged out']);
    }

    private function generateOTP($length = 6)
    {
        $otp = '';

        // Generate random numbers for OTP
        for ($i = 0; $i < $length; $i++) {
            $otp .= mt_rand(0, 9);
        }

        return $otp;
    }
}
