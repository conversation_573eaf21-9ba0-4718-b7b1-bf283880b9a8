<template>
    <div>
        <!-- Button to open the camera modal -->
        <div class="mb-4">
            <button type="button" class="px-4 py-2 rounded-lg font-medium text-white bg-blue-500 hover:bg-blue-600"
                :class="{ 'bg-red-500': isCameraOpen }" @click="toggleCamera">
                <span v-if="!isCameraOpen">
                    <font-awesome-icon icon="fa-solid fa-camera" class="pr-1" />
                    <span v-if="!isMobile">Open Camera</span>
                </span>
                <span v-else>Close Camera</span>
            </button>
        </div>

        <!-- Camera Modal -->
        <div v-show="isCameraOpen" class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
            <div class="bg-white p-6 rounded-lg w-full max-w-md relative">
                <button class="absolute top-2 right-2 text-red-500 font-bold" @click="toggleCamera">
                    <font-awesome-icon icon="fa-solid fa-xmark" class="text-lg" />
                </button>

                <div class="relative w-full max-w-md mx-auto">
                    <!-- Camera Shutter Effect -->
                    <div class="absolute inset-0 bg-white transition-opacity duration-100"
                        :class="{ 'opacity-100': isShotPhoto, 'opacity-0': !isShotPhoto }"></div>

                    <!-- Camera Feed (Video) -->
                    <video v-show="!isPhotoTaken" ref="camera" class="w-full h-full object-cover rounded-lg"
                        autoplay></video>

                    <!-- Photo (Canvas) after Taking Picture -->
                    <canvas v-show="isPhotoTaken" ref="canvas" class="w-full h-full object-cover rounded-lg"></canvas>
                </div>

                <!-- Shoot Photo Button -->
                <div v-show="!isPhotoTaken" class="mt-4 flex justify-center">
                    <button type="button"
                        class="p-3 bg-blue-500 text-white rounded-full flex items-center justify-center shadow-md hover:bg-blue-600 active:bg-blue-700 transition-all duration-300"
                        @click="takePhoto">
                        <font-awesome-icon icon="fa-solid fa-camera" class="text-2xl" />
                    </button>
                </div>

                <!-- Upload Photo -->
                <div v-if="isPhotoTaken" class="mt-4 flex justify-center">
                    <button type="button" @click="uploadImage" class="px-3 py-2 rounded-full bg-green-500 text-white">
                        <font-awesome-icon icon="fa-solid fa-arrow-up-from-bracket" class="pr-1" /> Upload
                    </button>
                </div>

                <!-- Loading Spinner -->
                <div v-show="isLoading"
                    class="absolute inset-0 flex justify-center items-center bg-white bg-opacity-50">
                    <div class="flex gap-2">
                        <div class="w-3 h-3 rounded-full bg-blue-500 animate-blink"></div>
                        <div class="w-3 h-3 rounded-full bg-blue-500 animate-blink delay-200"></div>
                        <div class="w-3 h-3 rounded-full bg-blue-500 animate-blink delay-400"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        length: { type: Number, required: false },
        fields: { type: [Object, String], required: false },
    },
    data() {
        return {
            isCameraOpen: false,
            isPhotoTaken: false,
            isShotPhoto: false,
            isLoading: false,
            isMobile: false,
        };
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    methods: {
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

        toggleCamera() {
            if (this.isCameraOpen) {
                this.isCameraOpen = false;
                this.isPhotoTaken = false;
                this.isShotPhoto = false;
                this.stopCameraStream();
            } else {
                this.isCameraOpen = true;
                this.createCameraElement();
            }
        },

        async createCameraElement() {
            this.isLoading = true;
            const constraints = {
                audio: false,
                video: true,
            };

            try {
                const stream = await navigator.mediaDevices.getUserMedia(constraints);
                this.isLoading = false;
                this.$refs.camera.srcObject = stream;
            } catch (error) {
                this.isLoading = false;
                alert("Camera access error: " + error);
            }
        },

        stopCameraStream() {
            let tracks = this.$refs.camera.srcObject.getTracks();
            tracks.forEach((track) => {
                track.stop();
            });
        },

        takePhoto() {
            // Clear the canvas before taking the new photo
            const canvas = this.$refs.canvas;
            const context = canvas.getContext("2d");
            context.clearRect(0, 0, canvas.width, canvas.height); // Clear the canvas before drawing new photo

            if (!this.isPhotoTaken) {
                this.isShotPhoto = true;
                setTimeout(() => {
                    this.isShotPhoto = false;
                }, 50);
            }
            this.isPhotoTaken = !this.isPhotoTaken;

            const videoElement = this.$refs.camera;

            const videoWidth = videoElement.videoWidth;
            const videoHeight = videoElement.videoHeight;

            canvas.width = videoWidth;
            canvas.height = videoHeight;

            context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
        },

        async uploadImage() {
            const canvas = this.$refs.canvas;
            const videoElement = this.$refs.camera;
            const videoWidth = videoElement.videoWidth;
            const videoHeight = videoElement.videoHeight;

            canvas.width = videoWidth;
            canvas.height = videoHeight;

            const context = canvas.getContext("2d");
            context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

            // Convert canvas to Blob
            await canvas.toBlob((blob) => {
                if (!blob) {
                    console.error('Error creating blob from canvas');
                    return;
                }

                // Create the new file from the Blob
                const fakeFile = new File([blob], "captured-photo.jpg", { type: 'image/jpeg' });

                // Emit the event with the new file for parent component to handle
                const event = {
                    target: {
                        files: [fakeFile],
                    }
                };

                this.$emit('handleImageChange', event, this.length, this.fields);
                // Reset after upload
                this.resetAll();
            }, 'image/jpeg');


        },
        resetAll() {
            this.isCameraOpen = false;
            this.isPhotoTaken = false;
            this.isShotPhoto = false;
            this.isLoading = false;
            this.stopCameraStream();
        }
    },
};
</script>

<style scoped>
@keyframes blink {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.3;
    }

    100% {
        opacity: 1;
    }
}

.animate-blink {
    animation: blink 1s infinite;
}
</style>
