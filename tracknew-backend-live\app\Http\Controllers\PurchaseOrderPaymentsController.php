<?php

namespace App\Http\Controllers;

use App\DataTables\PurchaseOrderPaymentsDataTable;
use App\Http\Requests;
use App\Http\Requests\CreatePurchaseOrderPaymentsRequest;
use App\Http\Requests\UpdatePurchaseOrderPaymentsRequest;
use App\Repositories\PurchaseOrderPaymentsRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class PurchaseOrderPaymentsController extends AppBaseController
{
    /** @var PurchaseOrderPaymentsRepository $purchaseOrderPaymentsRepository*/
    private $purchaseOrderPaymentsRepository;

    public function __construct(PurchaseOrderPaymentsRepository $purchaseOrderPaymentsRepo)
    {
        $this->purchaseOrderPaymentsRepository = $purchaseOrderPaymentsRepo;
    }

    /**
     * Display a listing of the PurchaseOrderPayments.
     *
     * @param PurchaseOrderPaymentsDataTable $purchaseOrderPaymentsDataTable
     *
     * @return Response
     */
    public function index(PurchaseOrderPaymentsDataTable $purchaseOrderPaymentsDataTable)
    {
        return $purchaseOrderPaymentsDataTable->render('purchase_order_payments.index');
    }

    /**
     * Show the form for creating a new PurchaseOrderPayments.
     *
     * @return Response
     */
    public function create()
    {
        return view('purchase_order_payments.create');
    }

    /**
     * Store a newly created PurchaseOrderPayments in storage.
     *
     * @param CreatePurchaseOrderPaymentsRequest $request
     *
     * @return Response
     */
    public function store(CreatePurchaseOrderPaymentsRequest $request)
    {
        $input = $request->all();

        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->create($input);

        Flash::success('Purchase Order Payments saved successfully.');

        return redirect(route('purchaseOrderPayments.index'));
    }

    /**
     * Display the specified PurchaseOrderPayments.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->find($id);

        if (empty($purchaseOrderPayments)) {
            Flash::error('Purchase Order Payments not found');

            return redirect(route('purchaseOrderPayments.index'));
        }

        return view('purchase_order_payments.show')->with('purchaseOrderPayments', $purchaseOrderPayments);
    }

    /**
     * Show the form for editing the specified PurchaseOrderPayments.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->find($id);

        if (empty($purchaseOrderPayments)) {
            Flash::error('Purchase Order Payments not found');

            return redirect(route('purchaseOrderPayments.index'));
        }

        return view('purchase_order_payments.edit')->with('purchaseOrderPayments', $purchaseOrderPayments);
    }

    /**
     * Update the specified PurchaseOrderPayments in storage.
     *
     * @param int $id
     * @param UpdatePurchaseOrderPaymentsRequest $request
     *
     * @return Response
     */
    public function update($id, UpdatePurchaseOrderPaymentsRequest $request)
    {
        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->find($id);

        if (empty($purchaseOrderPayments)) {
            Flash::error('Purchase Order Payments not found');

            return redirect(route('purchaseOrderPayments.index'));
        }

        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->update($request->all(), $id);

        Flash::success('Purchase Order Payments updated successfully.');

        return redirect(route('purchaseOrderPayments.index'));
    }

    /**
     * Remove the specified PurchaseOrderPayments from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->find($id);

        if (empty($purchaseOrderPayments)) {
            Flash::error('Purchase Order Payments not found');

            return redirect(route('purchaseOrderPayments.index'));
        }

        $this->purchaseOrderPaymentsRepository->delete($id);

        Flash::success('Purchase Order Payments deleted successfully.');

        return redirect(route('purchaseOrderPayments.index'));
    }
}
