<?php

namespace App\Http\Controllers;

use App\DataTables\AmcUsersDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateAmcUsersRequest;
use App\Http\Requests\UpdateAmcUsersRequest;
use App\Repositories\AmcUsersRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class AmcUsersController extends AppBaseController
{
    /** @var AmcUsersRepository $amcUsersRepository*/
    private $amcUsersRepository;

    public function __construct(AmcUsersRepository $amcUsersRepo)
    {
        $this->amcUsersRepository = $amcUsersRepo;
    }

    /**
     * Display a listing of the AmcUsers.
     *
     * @param AmcUsersDataTable $amcUsersDataTable
     *
     * @return Response
     */
    public function index(AmcUsersDataTable $amcUsersDataTable)
    {
        return $amcUsersDataTable->render('amc_users.index');
    }

    /**
     * Show the form for creating a new AmcUsers.
     *
     * @return Response
     */
    public function create()
    {
        return view('amc_users.create');
    }

    /**
     * Store a newly created AmcUsers in storage.
     *
     * @param CreateAmcUsersRequest $request
     *
     * @return Response
     */
    public function store(CreateAmcUsersRequest $request)
    {
        $input = $request->all();

        $amcUsers = $this->amcUsersRepository->create($input);

        Flash::success('Amc Users saved successfully.');

        return redirect(route('amcUsers.index'));
    }

    /**
     * Display the specified AmcUsers.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $amcUsers = $this->amcUsersRepository->find($id);

        if (empty($amcUsers)) {
            Flash::error('Amc Users not found');

            return redirect(route('amcUsers.index'));
        }

        return view('amc_users.show')->with('amcUsers', $amcUsers);
    }

    /**
     * Show the form for editing the specified AmcUsers.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $amcUsers = $this->amcUsersRepository->find($id);

        if (empty($amcUsers)) {
            Flash::error('Amc Users not found');

            return redirect(route('amcUsers.index'));
        }

        return view('amc_users.edit')->with('amcUsers', $amcUsers);
    }

    /**
     * Update the specified AmcUsers in storage.
     *
     * @param int $id
     * @param UpdateAmcUsersRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateAmcUsersRequest $request)
    {
        $amcUsers = $this->amcUsersRepository->find($id);

        if (empty($amcUsers)) {
            Flash::error('Amc Users not found');

            return redirect(route('amcUsers.index'));
        }

        $amcUsers = $this->amcUsersRepository->update($request->all(), $id);

        Flash::success('Amc Users updated successfully.');

        return redirect(route('amcUsers.index'));
    }

    /**
     * Remove the specified AmcUsers from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $amcUsers = $this->amcUsersRepository->find($id);

        if (empty($amcUsers)) {
            Flash::error('Amc Users not found');

            return redirect(route('amcUsers.index'));
        }

        $this->amcUsersRepository->delete($id);

        Flash::success('Amc Users deleted successfully.');

        return redirect(route('amcUsers.index'));
    }
}
