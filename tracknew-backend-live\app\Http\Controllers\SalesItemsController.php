<?php

namespace App\Http\Controllers;

use App\DataTables\SalesItemsDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateSalesItemsRequest;
use App\Http\Requests\UpdateSalesItemsRequest;
use App\Repositories\SalesItemsRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class SalesItemsController extends AppBaseController
{
    /** @var SalesItemsRepository $salesItemsRepository*/
    private $salesItemsRepository;

    public function __construct(SalesItemsRepository $salesItemsRepo)
    {
        $this->salesItemsRepository = $salesItemsRepo;
    }

    /**
     * Display a listing of the SalesItems.
     *
     * @param SalesItemsDataTable $salesItemsDataTable
     *
     * @return Response
     */
    public function index(SalesItemsDataTable $salesItemsDataTable)
    {
        return $salesItemsDataTable->render('sales_items.index');
    }

    /**
     * Show the form for creating a new SalesItems.
     *
     * @return Response
     */
    public function create()
    {
        return view('sales_items.create');
    }

    /**
     * Store a newly created SalesItems in storage.
     *
     * @param CreateSalesItemsRequest $request
     *
     * @return Response
     */
    public function store(CreateSalesItemsRequest $request)
    {
        $input = $request->all();

        $salesItems = $this->salesItemsRepository->create($input);

        Flash::success('Sales Items saved successfully.');

        return redirect(route('salesItems.index'));
    }

    /**
     * Display the specified SalesItems.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $salesItems = $this->salesItemsRepository->find($id);

        if (empty($salesItems)) {
            Flash::error('Sales Items not found');

            return redirect(route('salesItems.index'));
        }

        return view('sales_items.show')->with('salesItems', $salesItems);
    }

    /**
     * Show the form for editing the specified SalesItems.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $salesItems = $this->salesItemsRepository->find($id);

        if (empty($salesItems)) {
            Flash::error('Sales Items not found');

            return redirect(route('salesItems.index'));
        }

        return view('sales_items.edit')->with('salesItems', $salesItems);
    }

    /**
     * Update the specified SalesItems in storage.
     *
     * @param int $id
     * @param UpdateSalesItemsRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateSalesItemsRequest $request)
    {
        $salesItems = $this->salesItemsRepository->find($id);

        if (empty($salesItems)) {
            Flash::error('Sales Items not found');

            return redirect(route('salesItems.index'));
        }

        $salesItems = $this->salesItemsRepository->update($request->all(), $id);

        Flash::success('Sales Items updated successfully.');

        return redirect(route('salesItems.index'));
    }

    /**
     * Remove the specified SalesItems from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $salesItems = $this->salesItemsRepository->find($id);

        if (empty($salesItems)) {
            Flash::error('Sales Items not found');

            return redirect(route('salesItems.index'));
        }

        $this->salesItemsRepository->delete($id);

        Flash::success('Sales Items deleted successfully.');

        return redirect(route('salesItems.index'));
    }
}
