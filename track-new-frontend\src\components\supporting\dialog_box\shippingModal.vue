<template>
    <div v-if="showModal" class="fixed inset-0 flex items-center justify-center bg-gray-800 bg-opacity-50 z-30">
        <div
            class="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl lg:max-w-md h-full sm:h-auto overflow-auto md:max-h-[90vh]">

            <!-- Modal Header -->
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold">Shipping Address</h3>
                <button @click="closeModal" class="text-red-600 hover:text-red-900">
                    <font-awesome-icon icon="fa-solid fa-xmark" />
                </button>
            </div>

            <!-- Checkbox: Same as Billing -->
            <div class="flex items-center mb-3">
                <input type="checkbox" id="sameAddress" v-model="is_same" class="mr-2 cursor-pointer" />
                <label for="sameAddress" class="text-gray-700 font-medium cursor-pointer">
                    Same as Billing & Shipping Address
                </label>
            </div>

            <!-- Customer Details -->
            <div v-if="is_same" class="border rounded-lg p-3 bg-gray-100 text-sm mb-3">
                <p class="font-bold">Ship To:</p>
                <p>{{ customer.first_name }} {{ customer.last_name || '' }}</p>
                <p v-if="customer.address">{{ customer.address }}</p>
                <p>Phone: {{ customer.contact_number }}</p>
                <p v-if="customer.email">Email: {{ customer.email }}</p>
                <p v-if="customer.gst_number">GSTIN: {{ customer.gst_number }}</p>
            </div>

            <!-- Shipping Form (Only When Not Same as Billing) -->
            <div v-if="!is_same" class="text-sm">
                <p class="font-bold">Ship To:</p>
                <div class="space-y-3">

                    <!-- Name -->
                    <div>
                        <label class="font-medium">Recipient Name <span class="text-red-500">*</span></label>
                        <input v-model="shipping.name" type="text" class="w-full border px-3 py-2 rounded"
                            placeholder="Enter Recipient Name" required />
                    </div>
                    <div>
                        <label class="font-medium">Phone Number <span class="text-red-500">*</span></label>
                        <input v-model="shipping.number" type="tel" @input="validatePhoneNumber(shipping.number)"
                            class="w-full border px-3 py-2 rounded" placeholder="Enter Phone Number" required />
                        <span v-if="validationMessage !== ''" class="block text-xs text-red-500 -mb-2">
                            {{ validationMessage }}</span>
                    </div>

                    <!-- Address -->
                    <div>
                        <label class="font-medium">Street Address <span class="text-red-500">*</span></label>
                        <textarea v-model="shipping.street" class="w-full border px-3 py-2 rounded"
                            placeholder="Enter Street Address" required></textarea>
                    </div>

                    <!-- State & District -->
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label class="font-medium">State</label>
                            <input v-model="shipping.state" type="text" class="w-full border px-3 py-2 rounded"
                                placeholder="Enter State" />
                        </div>
                        <div>
                            <label class="font-medium">District</label>
                            <input v-model="shipping.district" type="text" class="w-full border px-3 py-2 rounded"
                                placeholder="Enter District" />
                        </div>
                    </div>

                    <!-- City & Pincode -->
                    <div class="grid grid-cols-2 gap-2 py-2">
                        <div>
                            <label class="font-medium">City</label>
                            <input v-model="shipping.city" type="text" class="w-full border px-3 py-2 rounded"
                                placeholder="Enter City" />
                        </div>
                        <div>
                            <label class="font-medium">Pincode</label>
                            <input v-model="shipping.pincode" type="text" class="w-full border px-3 py-2 rounded"
                                placeholder="Enter Pincode" />
                        </div>
                    </div>
                </div>
            </div>

            <!-- Modal Actions -->
            <div class="mt-4 flex justify-end gap-2 py-2">
                <button @click="closeModal" class="px-4 py-2 border rounded text-gray-600">Cancel</button>
                <button v-if="!is_same" @click="saveShipping"
                    class="px-4 py-2 bg-blue-600 text-white rounded">Save</button>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        customer: Object,
        sameAsBilling: Boolean,
        shipping_detail: Object,
    },
    data() {
        return {
            shipping: {},
            is_same: true,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            validationMessage: '',
        };
    },
    methods: {
        closeModal() {
            this.$emit('close');
        },
        saveShipping() {
            if (!this.shipping.name || !this.shipping.number || !this.shipping.street || this.validationMessage !== '') {
                this.message = "Please fill in all required fields.";
                this.type_toaster = 'warning';
                this.show = true;
                return;
            }
            // Emit the data to parent component
            this.$emit('saveData', this.shipping, this.is_same);
            // this.closeModal();
        },
        //--mobile number validation--
        validatePhoneNumber(inputtxt) {
            // Regular expression for phone numbers
            var phoneno = /^(?:0|\+91)?[6-9]\d{9}$/;
            // Check if the input matches the regular expression
            if (inputtxt.match(phoneno)) {
                this.validationMessage = '';
                return true; // Phone number is valid
            } else {
                this.validationMessage = 'Enter valid mobile number';
                return false; // Phone number is invalid
            }
        },
    },
    watch: {
        showModal(newValue) {
            if (newValue) {
                this.is_same = this.sameAsBilling;
                this.shipping = this.shipping_detail;
            }
        },
        sameAsBilling(newValue) {
            this.is_same = newValue;
        },

    }
};
</script>

<style scoped>
/* Ensure Full-Screen Modal on Mobile */
@media (max-width: 640px) {
    .w-full {
        width: 100%;
        height: 100%;
        max-height: 100vh;
        border-radius: 0;
    }

    .overflow-auto {
        overflow-y: auto;
    }
}
</style>
