<template>
    <div :class="{ 'manualStyle text-sm': isMobile, 'text-sm': !isMobile }" class="custom-scrollbar-hidden"
        ref="scrollContainer">
        <div class="my-custom-margin">
            <div class="bg-gray-100 grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 gap-0">
                <!-- Left Sidebar (Chat List) -->
                <div class="h-screen overflow-y-auto w-full bg-white border-r border-gray-300 flex flex-col">
                    <div class="flex items-center justify-between p-2 bg-white border-b border-gray-200 shadow-sm">
                        <div class="flex items-center space-x-2">
                            <button class="p-2 rounded-full hover:bg-gray-200 bg-gray-100 flex items-center space-x-2"
                                title="Add Customer" @click="openModal">
                                <!-- <font-awesome-icon icon="fa-solid fa-plus" class="text-gray-600 text-lg" /> -->
                                <font-awesome-icon icon="fa-solid fa-user-plus" class="text-gray-600 text-lg" />
                            </button>
                            <button @click="openImportModal" title="Import Customer"
                                class="p-2 rounded-full hover:bg-gray-200 bg-gray-100 flex items-center space-x-2">
                                <font-awesome-icon icon="fa-solid fa-cloud-arrow-down" class="text-gray-600 text-lg" />
                            </button>
                        </div>
                        <!-- Icons (New Chat + More Options) -->
                        <div class="flex items-center space-x-3">
                            <div v-if="!open_skeleton" class="text-xs text-green-600">
                                <p v-if="pagination_data.customer">Total: <span class="font-bold">{{
                                    pagination_data.customer.total }}</span></p>
                            </div>
                            <!-- Three-dot Menu -->
                            <button class="p-2 rounded-full hover:bg-gray-100">
                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" class="text-gray-600 text-lg" />
                            </button>
                        </div>
                    </div>
                    <!-- Search Bar -->
                    <div class="p-4 py-2 border-b border-gray-200">
                        <div class="relative">
                            <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                @resetData="resetToSearch">
                            </searchCustomer>
                        </div>
                    </div>
                    <!-- Filter Buttons -->
                    <!-- <div class="sticky top-[64px] bg-white px-4 py-2 border-b border-gray-200 flex flex-wrap gap-2">
                        <button class="px-3 py-1 text-sm font-medium text-white bg-blue-500 rounded-full">All</button>
                        <button
                            v-if="currentCustomerCategoryList && currentCustomerCategoryList.data && currentCustomerCategoryList.data.length > 0"
                            v-for="category in currentCustomerCategoryList.data"
                            class="px-3 py-1 text-sm font-medium text-gray-600 bg-gray-100 rounded-full">
                            {{ category.category_name }}
                        </button>
                    </div> -->

                    <!-- Chat List -->
                    <div class="flex-1 overflow-y-auto" @scroll="handleScroll">
                        <!-- <div v-for="chat in chatList" :key="chat.id" @click="selectChat(chat)"></div> -->
                        <div v-for="(record, index) in data" :key="index" @click="selectChat(record)"
                            class="flex items-center px-2 py-4 cursor-pointer hover:bg-gray-100 border-b border-gray-100"
                            :class="{ 'bg-gray-200': selectedChat?.id === record.id }">

                            <!-- Left Side: Initial, Name, Phone -->
                            <div class="flex items-center space-x-3 flex-1">
                                <!-- User Avatar -->
                                <div class="h-10 w-10 rounded-full flex items-center justify-center 
                                            text-white font-bold bg-green-600">
                                    {{ record.first_name.charAt(0) }}
                                </div>
                                <!-- Name & Phone -->
                                <div>
                                    <span class="font-medium text-gray-900 block">{{ record.first_name }}
                                        {{ record.last_name && record.last_name !== '' ? record.last_name : '' }}</span>
                                    <span class="text-sm text-gray-600 block">{{ record.contact_number }}</span>
                                    <span v-if="record.gst_number && record.gst_number && record.gst_number !== ''"
                                        class="text-xs text-gray-500">GSTIN: {{ record.gst_number }}</span>
                                </div>
                            </div>

                            <!-- Right Side: GST & Due Amount -->
                            <div class="flex flex-col items-end">
                                <div class="flex flex-col text-end">
                                    <!-- Due Amount Label -->
                                    <span class="text-xs px-1 py-1 rounded-full"
                                        :class="record.balance_amount === 0 ? 'text-green-500' : 'text-red-500'">
                                        {{ record.balance_amount === 0 ? 'No Due' : `₹ ${record.balance_amount}` }}
                                    </span>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination_data && pagination_data.customer && this.pagination_data.customer.last_page > 0 && this.pagination_data.customer.last_page === this.pagination_data.customer.current_page && !open_skeleton_isMobile"
                            class="text-sm">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                        <!--loader-->
                        <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                            :cols="1" :rows="10" :gap="2" :type="'grid'">
                        </skeleton>

                        <!--in case empty-->
                        <div v-if="!open_skeleton && data && data.length === 0">
                            <div class="flex justify-center items-center">
                                <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                            </div>
                            <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
                        </div>
                    </div>
                </div>

                <!-- Right Chat Section -->
                <div v-if="(isMobile && open_overview) || (selectedChat && isTab) || (!isMobile && !isTab)"
                    class="flex flex-1 flex-col bg-white"
                    :class="{ 'lg:col-span-3 sm:col-span-2': !showContactInfo, 'col-span-2': showContactInfo, 'fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50': isMobile && open_overview }">
                    <transition name="modal-transition">
                        <div v-if="selectedChat" class="flex flex-col"
                            :class="{ 'translate-y-0': isOpen, 'translate-x-full right-12': !isOpen, 'model bg-white w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen sm:pb-[150px] lg:pb-[70px] pb-[150px]': isMobile && open_overview }">
                            <!--view Customer data-->
                            <div>
                                <viewcustomertheme1 :selected_customer="selectedChat" :showContactInfo="showContactInfo"
                                    @updateCustomerInfo="updateCustomerInfo" @updateoverview="updateoverview"
                                    :open_overview="open_overview" @closecustomerview="closecustomerview"
                                    @confirmDelete="confirmDelete" :currentLocalDataList="currentLocalDataList"
                                    @editcustomer="editRecord" @handleDropdownClick="handleDropdownClick"
                                    :selected_option="selected_option" :companywhatsapp="companywhatsapp" :isTab="isTab"
                                    @openWhatsApp="openWhatsApp" @navigateToWhatsApp="navigateToWhatsApp">
                                </viewcustomertheme1>
                            </div>

                            <!-- Chat Messages -->
                            <!-- <div class="flex-1 overflow-y-auto p-4">
                            <div v-for="(msg, index) in selectedChat.messages" :key="index"
                                :class="msg.sent ? 'text-right' : 'text-left'">
                                <div :class="msg.sent ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-900'"
                                    class="inline-block px-4 py-2 rounded-lg max-w-[75%] my-1">
                                    {{ msg.text }}
                                </div>
                            </div>
                        </div> -->


                        </div>
                        <div v-else class="flex items-center justify-center text-gray-400">
                            <div class="px-4 py-10 h-screen overflow-y-auto">
                                <!-- Method 1 -->
                                <section class="mb-12">
                                    <h2 class="text-2xl font-semibold mb-2 flex items-center gap-2">🧍‍♂️ Method 1: Add
                                        Single Customer</h2>
                                    <p class="text-gray-600 mb-5">Manually add individual customers and assign them
                                        specific video scripts.</p>
                                    <div class="bg-white rounded-2xl shadow-md p-6 border border-gray-100">
                                        <h3 class="font-medium text-lg mb-3 text-gray-800">🔧 How it works:</h3>
                                        <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                            <li><span class="font-semibold">Navigate to:</span>
                                                <code>Customers &gt; Add Customer</code> or click the <strong>+</strong>
                                                icon
                                            </li>
                                            <li><span class="font-semibold">Fill in details:</span> Name, Email, Phone,
                                                Category</li>
                                            <li><span class="font-semibold">Save:</span> The customer is saved with
                                                their information</li>
                                        </ul>
                                    </div>
                                </section>

                                <!-- Method 2 -->
                                <section class="mb-12">
                                    <h2 class="text-2xl font-semibold mb-2 flex items-center gap-2">📦 Method 2: Bulk
                                        Import Customers</h2>
                                    <p class="text-gray-600 mb-5">Quickly add multiple customers using a spreadsheet
                                        format.</p>
                                    <div class="bg-white rounded-2xl shadow-md p-6 border border-gray-100">
                                        <h3 class="font-medium text-lg mb-3 text-gray-800">🔧 How it works:</h3>
                                        <ul class="list-disc pl-6 space-y-2 text-gray-700">
                                            <li><span class="font-semibold">Download Template:</span> Use the sample
                                                Excel or CSV format</li>
                                            <li><span class="font-semibold">Fill in Data:</span> Add customer entries in
                                                the downloaded file</li>
                                            <li><span class="font-semibold">Upload:</span> Go to
                                                <code>Customers &gt; Bulk Import</code> and upload the file
                                            </li>
                                            <li><span class="font-semibold">Save:</span> Each customer will be saved
                                                automatically</li>
                                        </ul>
                                    </div>
                                </section>

                                <!-- Mobile App -->
                                <section class="text-center">
                                    <h2 class="text-2xl font-semibold mb-3">📱 TrackNew Mobile App</h2>
                                    <p class="text-gray-600 mb-6">Access customer scripts anytime, anywhere with our
                                        mobile app—perfect for on-the-go productivity.</p>
                                    <div class="flex flex-col sm:flex-row justify-center gap-4">
                                        <a href="#"
                                            class="inline-block bg-blue-600 text-white px-6 py-3 rounded-full font-medium shadow hover:bg-blue-700 transition duration-300">
                                            📲 Download Android App
                                        </a>
                                    </div>
                                </section>
                            </div>
                        </div>
                    </transition>
                </div>
                <!-- Right-Side Customer Info Panel -->
                <div>
                    <customerinfo :showModal="showContactInfo" :selected_customer="selectedChat"
                        @closeModal="updateCustomerInfo(false)" @editcustomer="editRecord"
                        @dialPhoneNumber="dialPhoneNumber" :customer_overview="customer_overview" :isMobile="isMobile"
                        :isTab="isTab" :currentCompanyList="currentCompanyList" @selectedOption="selectedOption"
                        :companywhatsapp="companywhatsapp" @openWhatsApp="openWhatsApp"
                        @navigateToWhatsApp="navigateToWhatsApp">
                    </customerinfo>
                </div>

            </div>
        </div>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModal" :userName="null"
            :editData="editData" :type="typeOfRegister" :more_info="true"></customerRegister>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete">
        </confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!--Filter-->
        <customerFilter :showModal="lead_filter" @closeFilter="closeLeadFilter" :customer_list_data="originalData">
        </customerFilter>
        <Loader :showModal="open_loader"></Loader>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'categories'"></bottombar> -->
        <importCustomerModal :isOpen="isImportModalOpen" @close="closeImportModal" :companyId="companyId"
            :userId="userId" :isMobile="isMobile"></importCustomerModal>
        <!--whatsapp message-->
        <whatsappMessage :showModal="openWhatsAppMessage" :type="'customer'" :data="selected_customer"
            @close="closeWhatsappMessage">
        </whatsappMessage>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <!---add new-->
        <addLead :show-modal="showLeadModal" @closeLeadModal="closeLeadModal" :companyId="companyId" :userId="userId"
            :customer_id="customer_id">
        </addLead>
        <addAmc :show-modal="showAmcModal" @closeAmcModal="closeAmcModal" :companyId="companyId" :userId="userId"
            :customer_id="customer_id">
        </addAmc>
        <openRMARegister :showModal="openModalRegister" @close-modal="closeTheModal" :companyId="companyId"
            :isMobile="isMobile" :userId="userId" :customer_id="customer_id"></openRMARegister>
        <serviceCategory :showModal="open_service_category" @close-modal="closeCategory" :customer_id="customer_id"
            @updateMode="updateStatus">
        </serviceCategory>
    </div>
</template>

<script>
import customerRegister from '@/components/supporting/dialog_box/customerRegister.vue';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import dialogAlert from '@/components/supporting/dialog_box/dialogAlert.vue';
import customerFilter from '@/components/supporting/dialog_box/filter_Modal/customerFilter.vue';
import searchCustomer from '../searchCustomer.vue';
import importCustomerModal from '@/components/supporting/dialog_box/importCustomerModal.vue';
import { mapActions, mapGetters } from 'vuex';
import whatsappMessage from '@/components/supporting/dialog_box/whatsappMessage.vue';
import viewcustomertheme1 from './viewcustomertheme1.vue';
import customerinfo from './customerinfo.vue';
import addLead from '../../dialog_box/addLead.vue';
import addAmc from '../../dialog_box/addAmc.vue';
import openRMARegister from '../../dialog_box/openRMARegister.vue';
import serviceCategory from '../../dialog_box/serviceCategory.vue';
export default {
    name: 'customers_home',
    emits: ['updateIsOpen', 'dataToParent'],
    components: {
        customerRegister,
        confirmbox,
        dialogAlert,
        customerFilter,
        // bottombar,
        searchCustomer,
        importCustomerModal,
        whatsappMessage,
        viewcustomertheme1,
        customerinfo,
        addLead,
        addAmc,
        openRMARegister,
        serviceCategory,
    },
    props: {
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            selectedChat: null,
            showContactInfo: false,
            newMessage: "",
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 50,
            currentPage: 1,
            data: [],
            originalData: [],
            showModal_customer: false,
            editData: null,
            typeOfRegister: 'add',
            open_confirmBox: false,
            deleteIndex: null,
            columns: [],
            isMobile: false,
            isTab: false,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataBy: [],
            serviceCategories: [],
            //---filter
            lead_filter: false,
            typeList: [],
            statusList: [],
            filteredBy: {},
            //--dialog
            open_message: false,
            message: '',
            //--api integration--
            companyId: null,
            userId: null,
            changeStatus: false,
            pagination_data: {},
            //--skeleton--
            open_skeleton: false,
            number_of_columns: 7,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            now: null,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            open_skeleton_isMobile: false,
            backup_data: [],
            //---table data filter asending and decending----
            is_filter: false,
            //--importted data---
            isImportModalOpen: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //--short visible ---
            short_visible: [],
            //---whats app message---
            openWhatsAppMessage: false,
            selected_customer: null,
            //--time loading--
            time_loading: false,
            //--customer overview 
            customer_overview: null,
            //--mobile responsive--
            isOpen: false,
            open_overview: false,
            //---add new--
            showLeadModal: false,
            showAmcModal: false,
            openModalRegister: false,
            open_service_category: false,
            serviceIsGo: false,
            customer_id: null,
            //---options--
            selected_option: null,
        };
    },
    computed: {
        ...mapGetters('customerList', ['currentCustomersList', 'selectedCustomerData']),
        ...mapGetters('customerCategories', ['currentCustomerCategoryList']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('dataFilter', ['filteredData']),
        ...mapGetters('dataFilter', ['pageStatus']),
        ...mapGetters('dataFilter', ['originalDataFilter']),
        ...mapGetters('dataFilter', ['typeData']),
        ...mapGetters('dataFilter', ['filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };
            // Define the custom order of fields
            const customOrder = ['created_at', 'first_name', 'contact_number', 'balance_amount'];
            // Iterate over the first record in data to get field names
            for (const key in this.data[0]) {
                if (key !== 'id' && key !== 'company_id' && key !== 'updated_at' && key !== 'deleted_at') {
                    const label = formatLabel(key);
                    fields.push({
                        label,
                        field: key,
                        visible: customOrder.includes(key) // Make visible based on the custom order
                    });
                }
            }

            // Sort fields based on the custom order, keeping the other fields at the end
            fields.sort((a, b) => {
                const aIndex = customOrder.indexOf(a.field);
                const bIndex = customOrder.indexOf(b.field);

                // If both fields are in the custom order, compare their indices
                if (aIndex !== -1 && bIndex !== -1) {
                    return aIndex - bIndex;
                }
                // If only one field is in the custom order, that one should come first
                if (aIndex !== -1) return -1;
                if (bIndex !== -1) return 1;

                // If neither field is in the custom order, maintain their original order
                return 0;
            });

            this.columns = fields;
            return fields;
        },
        paginatedData() {
            const startIndex = (this.currentPage - 1) * this.recordsPerPage;
            const endIndex = startIndex + this.recordsPerPage;
            return this.data;
        },
        totalPages() {
            return Math.ceil(this.data.length / this.recordsPerPage);
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination_data.customer.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        }
    },
    methods: {
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('customerList', ['fetchCustomersList', 'selectedCustomerList']),
        ...mapActions('customerCategories', ['fetchCustomerCategoryList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('localStorageData', ['validateRoles', 'fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),

        selectChat(chat) {
            this.selectedChat = chat;
            this.selectedCustomerList(chat);
            chat.unread = 0; // Mark messages as read
            this.isOpen = true;
            this.customer_id = chat.id;
            if (this.isMobile) {
                this.open_overview = true;
                this.isOpen = true;
            }
        },
        sendMessage() {
            if (this.newMessage.trim() && this.selectedChat) {
                this.selectedChat.messages.push({ text: this.newMessage, sent: true });
                this.newMessage = "";
            }
        },
        updateCustomerInfo(data) {
            if (data !== undefined) {
                this.showContactInfo = data;
            }
        },

        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination_data.customer.last_page) {
                this.currentPage = pageNumber;
                // this.getCustomerList(this.currentPage, this.recordsPerPage);
            }
        },
        viewRecord(record) {
            // Handle view action
            // this.$emit('showViewCustomer', record);
            this.$router.push({ name: 'customers-view', params: { id: record.id } });
        },
        editRecord(record) {
            // Handle edit action
            // this.$emit('showAddService', record, 'edit');
            this.editData = record;
            // openModal();
            this.showModal_customer = true;
            this.typeOfRegister = 'edit';
        },
        deleteRecord() {
            if (this.deleteIndex !== null) {
                this.open_loader = true;
                let backup = this.deleteIndex.id;
                axios.delete(`/customers/${this.deleteIndex.id}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.data);
                        this.open_loader = false;
                        this.data = this.data.filter((opt) => opt.id !== backup);
                        this.updateKeyWithTime('customer_update');
                    })
                    .catch(error => {
                        console.error('Error delete record', error);
                        let message = error.response.data && error.response.data.message ? error.response.data.message : '';
                        if (message !== '') {
                            this.type_toaster = 'warning';
                            this.message = message;
                            this.show = true;
                        }
                        this.open_loader = false;
                    })
                // this.originalData.splice((this.recordsPerPage * (this.currentPage - 1)) + this.deleteIndex, 1);
                // localStorage.setItem('userData', JSON.stringify(this.originalData));
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(record) {
            this.deleteIndex = record;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        addServices() {
            this.$emit('showAddService', null, 'add');
        },
        openModal() {
            this.typeOfRegister = 'add';
            this.showModal_customer = true;

        },
        closeModal(data) {
            // console.log(data);
            if (data) {
                let findDuplication = this.originalData.findIndex((opt) => opt.id === data.id)
                if (findDuplication !== -1) {
                    this.originalData[findDuplication] = data;
                    this.data[findDuplication] = data;
                } else {
                    this.originalData = [data, ...this.originalData];
                    this.data = this.originalData;
                }
                this.fetchApiUpdates();
                this.fetchCustomersList({ page: this.currentPage, per_page: this.recordsPerPage });
            }

            this.showModal_customer = false;
            this.editData = null;

        },
        updateIsMobile() {
            const width = window.innerWidth;

            // Set isMobile to true if the screen width is less than 765px (mobile)
            this.isMobile = width < 765;

            // Set isTab to true if the screen width is between 1024px and 1280px (tablet range)
            this.isTab = width > 765 && width < 1024;

            // If it's a tablet, set isMobile to false
            if (this.isTab) {
                this.isMobile = false;
            }
            // If it's neither mobile nor tablet, set both to false
            if (!this.isMobile && !this.isTab) {
                this.isMobile = false;
                this.isTab = false;
            }
            if (this.open_overview) {
                this.open_overview = false;
            }
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                    // console.log('hello');
                } else {
                    //     console.log('hello');
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            if (!this.$refs.settingOPtion) {
                // Exit early if the reference is not available            
                this.isDropdownOpen = false;
                return;
            }
            const isClickInside = this.$refs.settingOPtion.contains(event.target);
            if (!isClickInside) {
                this.isDropdownOpen = false;
                // this.toggleDropdown();
            }
        },

        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        // closeFilterModal() {
        //     this.showFilterModal = false;
        //     this.resetFilter();
        // },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredDataBy = [...this.originalData];
        },
        applyFilter() {
            // Implement logic to filter data based on filter values
            // For simplicity, this example applies a basic filter
            this.filteredDataBy = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter---
        toggleFilter() {
            // console.log('GGGGGGGGGGGGGGG');
            // this.typeList = this.leadType.map((opt) => opt.name);
            // this.statusList = this.leadStatus.map((opt) => opt.name);
            this.lead_filter = true;
            this.data = this.originalData;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //--filter
        closeLeadFilter(searchData) {
            // console.log(searchData, 'What is happening.....!');
            if (searchData) {
                const keysData = Object.keys(searchData);

                let filterTheData = this.data.filter(option => {
                    // Check if all search criteria match
                    return keysData.every(key => {
                        // if (key === 'date') {
                        //     return option.lead_date === searchData.date;
                        // }
                        if (key === 'customer') {
                            if (option.last_name) {
                                this.currentPage = 1;
                                return (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number) === searchData.customer;
                            } else {
                                this.currentPage = 1;
                                return (option.first_name + ' - ' + option.contact_number) === searchData.customer;
                            }
                        }
                        return true; // For other keys, consider it as matched
                    });
                });

                if (filterTheData.length > 0) {
                    this.filteredBy = searchData;
                    this.data = filterTheData;
                } else {
                    filterTheData = this.data.filter(option => {
                        return option.lead_date === searchData.date ||
                            option.customer === searchData.customer ||
                            option.assign_to.some(assignee => searchData.assign_to.includes(assignee)) ||
                            option.lead_type === searchData.type ||
                            option.lead_status === searchData.status;
                    });
                    if (filterTheData.length > 0) {
                        this.filteredBy = searchData;
                        this.data = filterTheData;
                    } else {
                        this.message = 'The filter does not match any records..!';
                        this.open_message = true;
                        this.data = this.originalData;
                    }
                }
            }
            this.lead_filter = false;
        },
        //---close message---
        closeMessage() {
            this.open_message = false;
        },
        //---reset filter--
        resetTheFilter() {
            this.data = this.originalData;
            this.filteredBy = {};
        },
        getCustomerList(page, per_page) {
            if (page == 1) {
                this.fetchCustomersList({ page, per_page });
                if (this.currentCustomersList && this.currentCustomersList.data) {
                    this.data = this.currentCustomersList.data;
                    this.pagination_data.customer = this.currentCustomersList.pagination;
                }
            } else {
                this.open_skeleton = true;
                axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        this.open_skeleton = false;
                        this.data = response.data.data;
                        this.originalData = response.data.data;
                        this.pagination_data.customer = response.data.pagination;
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);
                        this.open_skeleton = false;
                    });
            }
        },
        getCategoryList() {
            axios.get('/customer-categories', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data);
                    this.serviceCategories = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        formatDate(timestamp) {
            const date = new Date(timestamp);
            // Get the day, month, year, hours, and minutes
            const day = String(date.getDate()).padStart(2, '0'); // Ensures 2 digits
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
            const year = date.getFullYear();
            const hours = date.getHours() % 12 || 12; // Convert to 12-hour format
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const ampm = date.getHours() >= 12 ? 'PM' : 'AM';
            return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data);
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);
                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            // if (this.isMobile) {
            const container = this.$refs.scrollContainer;
            const scrollPosition = container.scrollTop + container.clientHeight;
            const scrollHeight = container.scrollHeight;

            // Check if user has scrolled to 50% of the scrollable container
            if (scrollPosition >= scrollHeight * 0.5 && this.pagination_data.customer.last_page > this.pagination_data.customer.current_page && !this.open_skeleton_isMobile && this.pagination_data.customer.last_page > this.pagination_data.customer.current_page) {
                this.open_skeleton_isMobile = true;
                setTimeout(() => {
                    this.loadNextPage(this.pagination_data.customer.current_page + 1);
                }, 1000)
            }
            // }
        },
        loadNextPage(page) {
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination_data.customer = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })

        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData) {
                this.backup_data = [...this.data];
                this.data = [selectedData];
            }
        },
        resetToSearch() {
            if (Array.isArray(this.backup_data) && this.backup_data.length > 0) {
                this.data = [...this.backup_data];
            }
        },
        //---initial store data---
        getInitialStoreData(store_data) {
            this.open_skeleton = false;
            this.data = store_data.data;
            this.originalData = store_data.data;
            this.pagination_data.customer = store_data.pagination;
        },
        //--send wahtsapp reminder
        openWhatsApp(record) {
            this.selected_customer = { ...record };
            this.openWhatsAppMessage = true;


            // let message_data = `Hi ${record?.first_name || 'Customer'} sales invoice your payment is due ${this.currentCompanyList && this.currentCompanyList.currency === 'INR' ? '\u20b9' : this.currentCompanyList.currency} ${record.balance_amount}. \nyou can pay to the \n${this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].bank_details ? this.currentInvoice[0].bank_details : this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].business_contact ? this.currentInvoice[0].business_contact : `Please contact support team`} \n by ${this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].name ? this.currentInvoice[0].name : 'Company'
            //     } `;
            // let encodedMessage = encodeURIComponent(message_data);
            // let phone = record?.contact_number || '';

            // // Check the user agent to determine the device type
            // const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            // let whatsappUrl = '';
            // if (/ Android | webOS | iPhone | iPad | iPod | BlackBerry | IEMobile | Opera Mini /i.test(userAgent)) {
            //     var message = encodeURIComponent(message_data);
            //     var whatsapp_url = `whatsapp://send?phone=+91${phone}&text=` + message;
            //     window.location.href = whatsapp_url;
            // }
            // else if (/ iPad | iPhone | iPod /.test(userAgent) && !window.MSStream) {
            //     // iOS devices
            //     whatsappUrl = `whatsapp://send?phone=+91${phone}&text=${encodedMessage}`;
            //     // Open WhatsApp in a new window or tab
            //     window.open(whatsappUrl, '_blank');
            // } else {
            //     // Default to WhatsApp Web for other devices
            //     whatsappUrl = `https://web.whatsapp.com/send?phone=+91${phone}&text=${encodedMessage}`;
            //     // Open WhatsApp in a new window or tab
            //     window.open(whatsappUrl, '_blank');
            // }
        },
        closeWhatsappMessage() {
            this.openWhatsAppMessage = false;
            this.selected_customer = {};
        },
        navigateToWhatsApp() {
            this.$router.push({ name: 'whatsappsetting' });
        },
        //---validate the roles
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        filterDataBy(type, key) {
            this.showSortIcons(false, type, key);
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'customermanagement' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'customermanagement', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'customermanagement' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'customermanagement', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        closeAllModals() {
            // Close all modals
            this.showModal_customer = false;
            this.open_confirmBox = false;
            this.open_message = false;
            this.lead_filter = false;
            // Close all modals
            this.showModal_customer = false;
            this.open_confirmBox = false;
            this.showLeadModal = false;
            this.showAmcModal = false;
            this.openModalRegister = false;
            // Close all modals
            if (!this.serviceIsGo) {
                this.open_service_category = false;
            }
        },
        //----import customer---
        openImportModal() {
            this.isImportModalOpen = true;
        },
        async closeImportModal() {
            this.isImportModalOpen = false;
            await this.fetchApiUpdates();
            this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
        },
        //--short icon for filter---
        showSortIcons(data, type, key) {
            if (data) {
                this.short_visible.forEach(opt => {
                    if (opt.label === data.field) {
                        opt.visible = true;
                    }
                });
            } else if (type && key) {
                this.short_visible.forEach(opt => {
                    if (opt.label === key) {
                        opt.type = type;
                    } else {
                        opt.type = '';
                    }
                });
            }
        },
        hideSortIcons(data) {
            if (data) {
                this.short_visible.forEach(opt => {
                    if (opt.label === data.field) {
                        opt.visible = false;
                    }
                });
            }
        },
        //--reload the table data----
        refreshDataTable() {
            // this.open_skeleton = true;
            this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
            this.showSortIcons(false, 'type', 'key');
        },
        //--filter--
        getSortType(column) {
            if (column && this.short_visible && this.short_visible) {
                // Find the relevant short_visible entry
                const shortVisibleEntry = this.short_visible.find(
                    (entry) => entry.label === column.field
                );

                if (!shortVisibleEntry) {
                    return ''; // Default sort type if not found
                }

                // Determine sort type based on current type
                if (shortVisibleEntry.type === '') {
                    this.filterDataBy('asc', column.field);
                } else if (shortVisibleEntry.type === 'asc') {
                    this.filterDataBy('desc', column.field);
                } else if (shortVisibleEntry.type === 'desc') {
                    this.filterDataBy('asc', column.field);
                }
            }
        },
        //---on key press to make pagination--
        handleKeyPress(event) {
            if (event.key === "ArrowRight" && this.currentPage < this.pagination_data.customer.last_page) {
                this.currentPage++;
            } else if (event.key === "ArrowLeft" && this.currentPage > 1) {
                this.currentPage--;
            }
        },
        handleClick(column, record, event) {
            if (column == 'balance_amount') {
                event.stopPropagation(); // Prevent click event for this column
                return;
            } else {
                this.viewRecord(record);
            }
        },
        handleLoadingState(index) {
            if (index === this.data.length - 1) {
                this.time_loading = false; // Stop loading once the last index is reached
            } else {
                this.time_loading = true; // Continue loading for other indices
            }
        },
        //---update customer overview--
        updateoverview(data) {
            if (data) {
                this.customer_overview = data;
            }
        },
        //--close customer view--
        closecustomerview() {
            this.open_overview = false;
        },
        handleDropdownClick(option) {
            switch (option) {
                case 'Lead':
                    this.showLeadModal = true;
                    break;
                case 'AMC':
                    this.showAmcModal = true;
                    break;
                case 'RMA':
                    this.openModalRegister = true;
                    break;
                case 'Services':
                    this.open_service_category = true;
                    break;
                case 'Sales':
                    this.$router.push({
                        name: 'sales-invoice',
                        query: { type: 'add', customer_id: this.customer_id }
                    });
                    break;
                case 'Proforma':
                    this.$router.push({
                        name: 'addProformaInvoice',
                        params: { type: 'product' },
                        query: { type: 'add', customer_id: this.customer_id }
                    });
                    break;
                case 'Estimation':
                    this.$router.push({
                        name: 'addEstimation',
                        params: { type: 'product' },
                        query: { type: 'add', customer_id: this.customer_id }
                    });
                    break;
                default:
                    console.warn('Unhandled dropdown option:', option);
            }
            this.dropdownOpen = false;
        },
        //---add new----
        closeLeadModal(newData) {
            if (newData) {
                this.message = 'Lead created successfully...!';
                this.show = true;
            }

            this.showLeadModal = false;
        },
        closeAmcModal(newData) {
            if (newData) {
                this.message = 'AMC created successfully...!';
                this.show = true;
            }
            this.showAmcModal = false;
        },
        closeTheModal(data) {
            if (data && data.id) {
                this.message = 'RMA created successfully...!';
                this.show = true;
                this.openModalRegister = false;
            } else {
                this.openModalRegister = false;
            }
        },
        //---close category--
        closeCategory() {
            this.open_service_category = false;
        },
        //---update data-----
        emitUpdateIsOpen(value) {
            this.$emit('updateIsOpen', value);
        },
        updateStatus() {
            this.serviceIsGo = true;
            this.$emit('updateIsOpen', false);
        },
        //---customer info selected options--
        selectedOption(data) {
            if (data) {
                this.selected_option = data;
            }
        }
    },
    mounted() {
        this.fetchApiUpdates();
        this.updateIsMobile();
        this.fetchCompanyList();
        this.initialize();
        this.fetchWhatsappList();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        window.addEventListener('resize', this.updateIsMobile);
        // You can also send data when the component is mounted
        this.$emit('dataToParent', this.originalData);

        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        const view = localStorage.getItem('customer_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        this.time_loading = true;
        setTimeout(() => {
            this.time_loading = false;
        }, 800);
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        if (this.isMobile) {
            this.items_category = 'list';
        }
        //---get customer list---
        // this.getCustomerList(this.currentPage, this.recordsPerPage);
        if (this.currentCustomersList && this.currentCustomersList.data && this.currentCustomersList.data.length > 0) {
            this.open_skeleton = true;
            this.getInitialStoreData(this.currentCustomersList);
            this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
        } else {
            if (this.currentCustomersList && Object.keys(this.currentCustomersList).length == 0) {
                this.open_skeleton = true;
                this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
            }
        }
        //--get customer category---
        // this.getCategoryList();
        if (this.currentCustomerCategoryList && this.currentCustomerCategoryList.data && this.currentCustomerCategoryList.data.length > 0) {
            this.serviceCategories = this.currentCustomerCategoryList.data;
        } else {
            this.fetchCustomerCategoryList();
        }
        //--invoice setting---
        if (this.currentInvoice && this.currentInvoice.length === 0) {
            this.fetchInvoiceSetting();
        } else {
            this.fetchInvoiceSetting();

        }
        //---create short list array--
        this.short_visible = ['first_name', 'balance_amount'].map((field) => ({
            label: field,
            type: '',
            visible: false,
        }));
        // Add and remove event listeners
        window.addEventListener("keydown", this.handleKeyPress);
        //--initialize tha data--
        setTimeout(() => {
            if (this.selectedCustomerData) {
                this.selectedChat = this.selectedCustomerData;
                this.customer_id = this.selectedCustomerData.id;
                this.isOpen = true;
                if (this.isMobile) {
                    this.open_overview = true;
                    this.isOpen = true;
                }
            }
        }, 1000);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        window.removeEventListener('resize', this.updateIsMobile);
        window.removeEventListener("keydown", this.handleKeyPress);
    },
    watch: {
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    this.getCustomerList(1, newValue);
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                this.getCustomerList(newValue, this.recordsPerPage);
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('customer_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentCustomersList: {
            deep: true,
            handler(newValue) {
                this.getInitialStoreData(newValue);
            }
        },
        currentCustomerCategoryList: {
            deep: true,
            handler(newValue) {
                this.serviceCategories = newValue.data;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
                this.fetchCustomerCategoryList();
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        showModal_customer: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        lead_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        }
    },
}
</script>

<style scoped>
.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
    }
}

/* Define the transition for the modal */
.modal-transition-enter-active,
.modal-transition-leave-active {
    transition: transform 0.3s ease-in-out;
}

/* Modal comes from the right when not mobile */
.modal-transition-enter,
.modal-transition-leave-to {
    transform: translateX(100%);
}

.modal-transition-enter-to {
    transform: translateX(0);
}

.modal-transition-leave-to {
    transform: translateX(100%);
}

/* For mobile, sliding effect from the bottom */
@media (min-width: 640px) {

    .modal-transition-enter,
    .modal-transition-leave-to {
        transform: translateY(100%);
    }

    .modal-transition-enter-to {
        transform: translateY(0);
    }

    .modal-transition-leave-to {
        transform: translateY(100%);
    }
}
</style>