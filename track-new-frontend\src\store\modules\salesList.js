// store/modules/sales_list.js
import axios from "axios";

const state = {
  sales_list: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  previousPerPage: null,
  };

  const mutations = {
      SET_SALESLIST(state, { data, pagination, status_counts }) {
          // console.log(data, 'data', pagination, 'pagination', status_counts, 'status');
          state.sales_list = {data: data, pagination: pagination, status_counts: status_counts};
    },
      RESET_STATE(state) {
        state.sales_list = {};
        state.lastFetchTime = null;
        state.isFetching = false;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    SET_PREVIOUS_PER_PAGE(state, perPage) {
      state.previousPerPage = perPage; // Store the previous per_page value
    },

  };

  const actions = {
    updateSalesName({ commit }, sales_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update sales_list name
      setTimeout(() => {
        // Commit mutation to update sales_list name
        commit('SET_SALESLIST', sales_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchSalesList({ commit, state, rootState }, { page, per_page, is_delete }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['sales_update'];    
      // Check if per_page has changed and reset the cooldown
    if (state.previousPerPage != per_page) {
      commit('SET_PREVIOUS_PER_PAGE', per_page); // Update the per_page tracking
      commit('SET_LAST_FETCH_TIME', null); // Force a new request if per_page changed
    }
      
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (!is_delete && (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)) {
        return; // Skip request if less than 30 seconds have passed since the last request
      } 
      
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          // Set the request status to true (indicating that the request is in progress)
      commit('SET_IS_FETCHING', true);
          commit('SET_LAST_FETCH_TIME', now); 
          axios.get('/sales', { params: { company_id: company_id, page: page, per_page: per_page } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Sales list..!', response.data.total_revenue, 'TTTTTTT',  response.data.total_sum);
              let { data, pagination} = response.data;
              let status_counts = { total_revenue: response.data.total_revenue, total_sum: response.data.total_sum, due_count: response.data.due_count, due_sum: response.data.due_sum, total_count: response.data.pagination.total }
                
                // console.log(data, 'data', pagination, 'pagination', status_counts, 'status', 'Initialllllly');
              commit('SET_SALESLIST', { data, pagination, status_counts });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    
  };

  const getters = {
    currentSalesList(state) {
      return state.sales_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
