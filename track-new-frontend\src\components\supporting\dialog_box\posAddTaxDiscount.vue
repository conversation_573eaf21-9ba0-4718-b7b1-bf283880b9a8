<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    Manage Sales Item
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block text-sm bg p-4">
                <div class="mb-3">
                    <p><span class="font-bold">Item Name: </span>{{ itemData.product_name }}</p>
                </div>
                <div class="bg-gray-300 px-3 py-5 rounded mb-5 shadow-md"
                    style="box-shadow: 3px 4px 6px 3px rgba(150, 63, 59, 0.3)">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-3">
                        <!--Tax type-->
                        <div v-if="type !== 'discount'" class="flex w-full mr-2 relative">
                            <label for="tax_type"
                                class="text-sm font-bold absolute left-2 rounded top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.tax_type !== undefined && formValues.tax_type) || isInputFocused.tax_type, 'text-blue-700': isInputFocused.tax_type }">
                                Tax Type</label>
                            <select id="tax_type" v-model="formValues.tax_type" ref="tax_type"
                                class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                                @focus="isInputFocused.tax_type = true" @blur="isInputFocused.tax_type = false">
                                <!-- <option value="">select type</option> -->
                                <option value="Exclusive">Exclusive</option>
                                <option value="Inclusive">Inclusive</option>
                            </select>
                        </div>
                        <!--Tax-->
                        <div v-if="type !== 'discount'" class="flex w-full mr-2 relative">
                            <label for="tax_value"
                                class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.tax !== undefined && formValues.tax.value >= 0) || isInputFocused.tax_value, 'text-blue-700': isInputFocused.tax_value }">
                                Tax Value</label>
                            <!-- <select id="tax_value" v-model="formValues.tax_value"
                                v-if="invoice_setting && invoice_setting.length > 0 && invoice_setting[0].selected_tax && JSON.parse(invoice_setting[0].selected_tax).length > 0"
                                class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                                @focus="isInputFocused.tax_value = true" @blur="isInputFocused.tax_value = false">
                                <option v-for="tax in JSON.parse(invoice_setting[0].selected_tax)" :value="tax.value">{{
                                    tax.tax_name }} @ {{ tax.value }}</option>
                            </select> -->
                            <select id="tax_value" v-model="formValues.tax" @change="updateTaxName"
                                v-if="invoice_setting && invoice_setting.length > 0 && invoice_setting[0].selected_tax && JSON.parse(invoice_setting[0].selected_tax).length > 0"
                                class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                                @focus="isInputFocused.tax_value = true" @blur="isInputFocused.tax_value = false">

                                <!-- Loop through selected taxes and store both value and name -->
                                <option v-for="tax in JSON.parse(invoice_setting[0].selected_tax)"
                                    :value="{ value: tax.value, name: tax.tax_name }">
                                    {{ tax.tax_name }} @ {{ tax.value }}
                                </option>
                            </select>
                        </div>
                        <!--discount type-->
                        <div v-if="type !== 'tax'" class="flex w-full mr-2 relative">
                            <label for="discount_type"
                                class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.discount_type !== undefined && formValues.discount_type) || isInputFocused.discount_type, 'text-blue-700': isInputFocused.discount_type }">
                                Discount Type</label>
                            <select id="discount_type" v-model="formValues.discount_type" ref="discount_type"
                                class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                                @focus="isInputFocused.discount_type = true"
                                @blur="isInputFocused.discount_type = false">
                                <!-- <option value="">select type</option> -->
                                <option value="Percentage">Percentage (%)</option>
                                <option value="Fixed">Fixed ({{ currentCompanyList && currentCompanyList.currency ===
                                    'INR' ? '\u20b9' : currentCompanyList.currency }})</option>
                            </select>
                        </div>
                        <!--discount value-->
                        <div v-if="type !== 'tax'" class="flex w-full mr-2 relative">
                            <label for="discount_value"
                                class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.discount_value !== undefined && formValues.discount_value >= 0) || isInputFocused.discount_value, 'text-blue-700': isInputFocused.discount_value }">
                                Discount Value</label>
                            <input v-model="formValues.discount_value"
                                class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                                @focus="isInputFocused.discount_value = true" @input="isValidateDiscount"
                                @keydown.enter="saveModel" @blur="isInputFocused.discount_value = false" />
                        </div>
                    </div>
                    <!--discription-->
                    <div class="flex w-full mr-2 relative">
                        <label for="description"
                            class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.description !== undefined && formValues.description !== '') || isInputFocused.description, 'text-blue-700': isInputFocused.description }">
                            Description</label>
                        <textarea v-model="formValues.description" rows="3" @focus="isInputFocused.description = true"
                            @blur="isInputFocused.description = false"
                            class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"></textarea>
                    </div>
                    <p v-if="validation_message !== ''" class="text-red-500 font-bold text-sm py-2">{{
                        validation_message }}</p>
                </div>
                <!--buttons-->
                <div class="flex justify-center items-center">
                    <button class="border rounded text-white bg-red-700 font-normal px-4 py-2 hover:bg-red-600 mr-4"
                        @click="closeModal">Cancel</button>
                    <button class="border rounded text-white bg-green-700 font-normal px-4 py-2 hover:bg-green-600"
                        @click="saveModel">Save</button>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        itemData: Object,
        currentCompanyList: Object,
        invoice_setting: Object,
        type: String,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: { tax: { name: '', value: 0 } },
            isInputFocused: {},
            validation_message: ''
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
                this.validation_message = '';
            }, 300);
        },
        saveModel() {
            if (
                ((this.formValues.tax_type !== '' && this.formValues.tax_value >= 0) ||
                    (this.formValues.discount_type !== '' && this.formValues.discount_value >= 0)) &&
                this.validation_message === ''
            ) {
                // console.log(this.formValues, 'What happneing...!');
                setTimeout(() => {
                    this.$emit('close-modal', this.formValues);
                    this.validation_message = '';
                }, 300);
            } else {
                this.validation_message = this.validation_message === '' ? 'Please fill any one field..!' : this.validation_message;
            }
        },
        isValidateDiscount() {
            if (this.formValues.discount_type !== '' && this.formValues.discount_value >= 0) {
                if (this.itemData && this.itemData.total >= 0) {
                    if (this.formValues.discount_type === 'Fixed') {
                        if (this.formValues.discount_value <= (1 * this.itemData.total)) {
                            return true;
                        } else {
                            this.validation_message = 'Please enter a discount value that is less than or equal to the total value.';
                        }
                    } else if (this.formValues.discount_type === 'Percentage') {
                        if (this.formValues.discount_value <= 100) {
                            return true;
                        } else {
                            this.validation_message = 'Please enter a discount value that is less than or equal to the total value.';
                        }
                    }
                }
            }
        },
        updateTaxName() {
            if (this.formValues.tax) {
                this.formValues.tax_value = this.formValues.tax.value;
                this.formValues.tax_name = this.formValues.tax.name;
            }
        },
        updateItemData(newValue) {
            if (this.type == 'tax') {
                this.formValues.tax_type = newValue.tax_type ? newValue.tax_type : '';
                this.formValues.tax.value = newValue.taxvalue >= 0 ? newValue.taxvalue * 1 : '';
                this.formValues.tax.name = newValue.tax_name ? newValue.tax_name : 'GST';
                this.formValues.tax_value = newValue.taxvalue >= 0 ? newValue.taxvalue * 1 : '';
                this.formValues.tax_name = newValue.tax_name ? newValue.tax_name : 'GST';
            } else if (this.type == 'discount') {
                this.formValues.discount_type = newValue.discount_data && newValue.discount_data.type ? newValue.discount_data.type : '';
                this.formValues.discount_value = newValue.discount_data && newValue.discount_data.value ? newValue.discount_data.value * 1 : '';
            } else if (newValue) {
                this.formValues.tax_type = newValue.tax_type ? newValue.tax_type : '';
                this.formValues.tax.value = newValue.taxvalue >= 0 ? newValue.taxvalue * 1 : '';
                this.formValues.tax.name = newValue.tax_name ? newValue.tax_name : 'GST';
                this.formValues.tax_value = newValue.taxvalue >= 0 ? newValue.taxvalue * 1 : '';
                this.formValues.tax_name = newValue.tax_name ? newValue.tax_name : 'GST';
                this.formValues.discount_type = newValue.discount_data && newValue.discount_data.type ? newValue.discount_data.type : '';
                this.formValues.discount_value = newValue.discount_data && newValue.discount_data.value ? newValue.discount_data.value * 1 : '';
            }
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue && this.type != 'discount') {
                    this.updateItemData(this.itemData);
                    this.$refs.tax_type.focus();
                } else if (newValue && this.type != 'tax') {
                    this.$refs.discount_type.focus();
                }
                if (newValue && this.itemData) {
                    this.updateItemData(this.itemData);
                }
            }, 100);
        },
        itemData: {
            deep: true,
            handler(newValue) {
                if (this.showModal) {
                    this.updateItemData(newValue);
                }
            }
        },
        formValues: {
            deep: true,
            handler(newValue) {
                this.validation_message = '';

            }
        }
    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>