<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-display relative"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex justify-between items-center relative w-full bg-teal-600 px-4 py-3 set-header-background">
                <p for="leadFilter" class="text-white font-bold text-center flex justify-end ml-3 text-xl">
                    Filter
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block mt-1 p-4">
                <div v-if="selectedByValue === 'Custom'" class="text-green-600">
                    <p>Note: At least fill in any one field..!</p>
                </div>
                <!---Date-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'" class="flex justify-end">
                    <button @click="resetTheValues(['from', 'to'])"
                        class="absolute text-xs -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                            icon="fa-solid fa-xmark" /></button>
                </div>
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'"
                    class="grid grid-cols-2 gap-4">
                    <!-- From Date -->
                    <div class="relative">
                        <label for="fromDate"
                            class="text-sm absolute left-2 -top-[12px] sm:-top-3 text-xs bg-white px-1 text-gray-700 transition-top linear duration-300"
                            :class="{ 'text-blue-700': isInputFocused.from }">
                            From Date
                        </label>

                        <input type="date" v-datepicker id="fromDate" v-model="formValues.from" :max="formValues.to"
                            @change="updateMinToDate"
                            class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full" />
                    </div>

                    <!-- To Date -->
                    <div class="relative">
                        <label for="toDate"
                            class="absolute left-2 -top-[12px] sm:-top-3 text-xs bg-white px-1 text-gray-700 transition-top linear duration-300"
                            :class="{ 'text-blue-700': isInputFocused.to }">
                            To Date
                        </label>
                        <!-- @input="validateDates" -->
                        <input type="date" v-datepicker id="toDate" v-model="formValues.to" :min="minDate"
                            :max="maxDate"
                            class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full" />
                    </div>
                </div>
                <!--Supplier-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Supplier'" class="mt-5">
                    <div v-if="formValues.supplier" class="flex justify-end ">
                        <button @click="resetTheValue('supplier')"
                            class="absolute  -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" size="sm" style="color: #ec2727;" /></button>
                    </div>
                    <div class="relative flex w-full mr-2">
                        <label for="supplier"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 bg-white px-1 text-gray-700': formValues.supplier || isInputFocused.supplier, 'text-blue-700': isInputFocused.supplier }">Supplier<span
                                v-if="formValues.supplier || isInputFocused.supplier"
                                class="text-red-600">*</span></label>
                        <input id="supplier" :ref="'supplier'" v-model="formValues.supplier"
                            @input="handleDropdownInput(fields)"
                            @focus="isDropdownOpen = true, isInputFocused.supplier = true, handleDropdownInput(fields)"
                            @blur="closeDropdown('supplier')"
                            @keydown.enter="handleEnterKey('supplier', filteredCustomerOptions)"
                            @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="supplier"
                            class="w-full border py-2 pl-2 pr-10 leading-5 text-gray-900 focus:ring-0 rounded rounded-tr-none rounded-br-none outline-none" />

                        <!-- Right-aligned icon based on isDropdownOpen -->
                        <span class="absolute py-2 ml-[80%] sm:ml-[78%] lg:ml-[85%] cursor-pointer"
                            @click="isDropdownOpen = !isDropdownOpen">
                            <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </span>
                    </div>
                </div>
                <!-- Display filtered options as the user types -->
                <div v-if="isDropdownOpen">
                    <div class="absolute mt-1 max-h-60 w-3/4 overflow-auto ml-2 rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none text-sm"
                        style="z-index: 100;" @mousedown.prevent="preventBlur('supplier')">
                        <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                            @click="selectDropdownOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                            {{ option.name }} - {{ option.contact_number }}
                        </p>
                    </div>
                </div>
                <!--filter data-->
                <!---serial number-->
                <div class="relative mt-3" v-if="selectedByValue === 'by Serial'">
                    <div v-if="formValues.searchQuery" class="flex justify-end ">
                        <button @click="resetTheValue('searchQuery')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700">
                            <font-awesome-icon icon="fa-solid fa-xmark" size="sm" style="color: red" />
                        </button>
                    </div>
                    <!-- Input Field for Filtering -->
                    <input v-model="formValues.searchQuery" placeholder="Search..." @input="onSearchInput"
                        @blur="closeDropdown('search')" @change="onSearchInput" @focus="onSearchInput"
                        @keydown.enter="handleEnterKey('search', filteredData)"
                        @keydown.down.prevent="handleDownArrow(filteredData)"
                        @keydown.up.prevent="handleUpArrow(filteredData)" ref="search"
                        class="text-sm p-2 border border-gray-300 rounded w-full" />

                    <!-- Displaying Filtered Data -->
                    <div v-if="filteredData && filteredData.length && isDropdown_search"
                        class="absolute mt-1 max-h-60 w-3/4 overflow-auto ml-2 rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                        style="z-index: 100;" @mousedown.prevent="preventBlur('serial')">
                        <p v-for="(option, index) in filteredData" :key="index" @click="selectPurchaseOption(option)"
                            :class="{ 'bg-gray-200': index === selectedIndex }"
                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                            {{ option.purchase_order }} - {{ option.supplier_name }} - {{ option.supplier_contact }}
                        </p>
                    </div>
                    <div v-else>
                        <div v-if="is_loading" class="flex justify-center items-center h-full">
                            <div
                                class="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500 border-opacity-75">
                            </div>
                            <p class="text-blue-600 font-bold space-x-4">Loading</p>
                        </div>
                        <p v-else>
                            {{ filteredData && filteredData.length > 0 ? `${filteredData.length} Purchase` :
                                'No data found.' }}</p>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex justify-center items-center m-2 mt-5 mx-auto">
                    <button @click="closeModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  px-4 mr-8 py-2">Cancel</button>
                    <button v-if="showResetButton && selectedByValue === 'Custom'" @click="resetAll"
                        class="bg-blue-700 hover:bg-blue-600 rounded-[30px] text-white px-4 mr-8 py-2">Reset
                        All</button>
                    <button @click="submitFilter"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white px-4 mr-8 py-2">Submit</button>
                </div>
            </div>
        </div>
        <dialogAlert :showModal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>


<script>
import dialogAlert from '../dialogAlert.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
    props: {
        showModal: Boolean,
        typeList: Object,
        statusList: Object,
        selectedByValue: String
    },
    components: {
        dialogAlert
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            customer_list: [],
            employeeList: [],
            supplierList: [],
            pagination: {},
            isInputFocused: { date: true, type: true, status: true },
            isDropdownOpen: false,
            selectedIndex: 0,
            showOptions: false,
            search: '',
            companyId: '',
            userId: '',
            filteredCustomerOptions: [],
            open_message: false,
            message: '',
            open_loader: false,
            maxDate: new Date().toISOString().split('T')[0], // current date
            minDate: '',
            //---search serial number---
            searchQuery: '',
            filteredData: [],
            page: 1,
            is_loading: false,
            isDropdown_search: false,
        };
    },
    methods: {
        ...mapActions('supplier', ['fetchISupplierList']),
        ...mapActions('searchSerialPurchase', ['fetchPurchaseListSearch']),

        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter');
            }, 300);
        },
        //--submit---
        submitFilter() {
            this.isOpen = false;
            if (this.selectedByValue !== 'by Serial') {
                // Add a delay to allow the transition before emitting the close event
                setTimeout(() => {
                    this.$emit('closeFilter', this.formValues);
                    this.formValues = {};
                }, 300);
            } else {
                // Add a delay to allow the transition before emitting the close event
                setTimeout(() => {
                    this.$emit('closeFilter', false, this.filteredData, this.formValues.searchQuery);
                    this.formValues.searchQuery = '';
                    this.filteredData = [];
                }, 300);
            }
        },

        //----Supplier----
        handleDropdownInput() {
            // console.log(this.customer_list, 'This is customer list..@')
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.supplierList.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.supplierList.filter(
                        (option) => (option.name).toLowerCase().includes(inputName)
                    );
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = this.supplierList;
            }

            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'supplier') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    // this.openModal();
                    // this.openModal(product_name);
                }
            } else if (type === 'search') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectPurchaseOption(optionArray[this.selectedIndex]);
                    this.selectedIndex = 0;
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else if (this.selectedIndex > 0) {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----supplier dropdown--
        closeDropdown(type) {
            if (type && type === 'supplier' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                this.isInputFocused.supplier = false;

            } else if (type && type === 'search' && !this.mouseDownOnDropdown) {
                this.isDropdown_search = false;
            }
        },
        //---suppliers dropdown
        selectDropdownOption(option) {
            // console.log(option, 'What about supplier data.......!!!!');
            this.formValues.supplier_id = option.id;
            this.formValues.supplier = option.name + ' - ' + option.contact_number;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
        },
        preventBlur(type) {
            if (type && type === 'supplier') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            } else if (type && type === 'serial') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            }
        },
        //---multiple dropdown---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            // this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                // console.log('hello');
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.mobile_number + ''.includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination.employee.current_page !== this.pagination.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.mobile_number && option.mobile_number + ''.includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination.employee.current_page !== this.pagination.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    }
                } else if (enteredValue.length > 1) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination.employee.current_page !== this.pagination.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination && this.pagination.employee.current_page !== this.pagination.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    }
                } else {
                    return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList;
                }
            }
            // console.log(this.formValues.assign_to, 'RRRRR');
            // Return an empty array if no options match the filter
            // console.log(this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList);
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList;
        },

        selectOptionMultiple(option) {
            if (!this.formValues.assign_to) {
                this.formValues.assign_to = []; // Initialize assign_to as an array if it's not already
            }
            if (this.formValues.assign_to.length === 0) {
                this.formValues.assign_to.push(option); // Add the selected option to assign_to
                this.search = ''; // Clear the search input
                // this.showOptions = false; // Hide the options dropdown
                this.$nextTick(() => {
                    // Focus on the search input after selecting an option
                    if (this.$refs['search']) {
                        this.$refs['search'].focus();
                        // this.$refs['search'].click();
                    }
                });
                this.selectedIndex = 0;
            } else {
                this.message = 'Employee filters can be selected one at a time';
                this.open_message = true;
            }
        },
        //---status and type--
        selectStatusOption(option, type, index) {
            if (option && type) {
                this.formValues[type] = index;
            }
        },
        //---reset All---
        resetAll() {
            this.formValues = {};
        },
        //---reset---
        resetTheValue(key) {
            delete this.formValues[key];
            if (key === 'searchQuery') {
                this.filteredData = [];
                // this.page = 1;
            }
        },

        closeMessage() {
            this.message = '';
            this.open_message = false;
        },
        validateDates() {
            if (this.formValues.from && this.formValues.to) {
                const fromDate = new Date(this.formValues.from);
                const toDate = new Date(this.formValues.to);

                if (toDate < fromDate) {
                    // Reset 'to' date and show error message (you can handle this according to your UI)
                    this.formValues.to = null;
                    this.message = 'To Date cannot be earlier than From Date';
                    this.open_message = true;
                }
            }
        },
        //--update minimum date
        updateMinToDate() {
            this.minDate = this.formValues.from;
        },
        resetTheValues(fields) {
            // Reset specified form fields
            fields.forEach(field => {
                this.formValues[field] = null;
            });
        },
        //---handle focus--
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.supplier;

            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---search by serial number---
        onSearchInput() {
            this.isDropdown_search = true;
            if (this.filteredData && this.filteredData.length === 0 && this.formValues.searchQuery && this.formValues.searchQuery.length > 1) {

                if (!this.currentPurchaseListSearch.pagination && !this.is_loading) {
                    this.is_loading = true;
                    this.fetchPurchaseListSearch({ page: this.page, per_page: 100 });
                } else if (this.currentPurchaseListSearch.pagination && this.currentPurchaseListSearch.pagination.current_page < this.currentPurchaseListSearch.pagination.last_page && !this.is_loading) {
                    this.is_loading = true;
                    this.fetchPurchaseListSearch({ page: this.page, per_page: 100 });
                } else {
                    this.filterDataPurchase(this.currentSalesListSearch);
                }
            } else if (this.formValues.searchQuery === '') {
                this.filteredData = [];
                // this.page = 1;
            } else if (this.currentPurchaseListSearch && this.formValues.searchQuery) {
                this.filterDataPurchase(this.currentPurchaseListSearch);
            }
        },
        selectPurchaseOption(options) {
            this.isOpen = false;
            let search_value = this.formValues.searchQuery;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                //---false, options, search_value---
                this.$emit('closeFilter', false, this.filteredData);
                this.formValues.searchQuery = '';
                // this.page = 1;
                this.filteredData = [];
            }, 300);

        },
        filterDataPurchase(newValue) {
            // console.log(newValue, 'Filtered Data:', this.searchQuery);
            if (newValue && Array.isArray(newValue.data) && newValue.data.length > 0) {
                this.filteredData = newValue.data.filter(opt => {
                    // Check if sales_item_data exists and is an array with items
                    if (opt.purchase_items && Array.isArray(opt.purchase_items) && opt.purchase_items.length > 0) {

                        // Filter based on user input in searchQuery (serial number)
                        return opt.purchase_items.some(item => {
                            if (item.serial_no && typeof item.serial_no === 'string') {
                                let serialNumbers = [];

                                // Handle both JSON arrays and comma-separated strings
                                try {
                                    serialNumbers = JSON.parse(item.serial_no);
                                } catch (error) {
                                    serialNumbers = item.serial_no.split(',').map(serial => serial.trim());
                                }

                                // Ensure serialNumbers is an array and includes the searchQuery (user input)
                                return Array.isArray(serialNumbers) && serialNumbers.some(serial => serial.includes(this.formValues.searchQuery || serial == this.formValues.searchQuery));
                            }
                            return false;
                        });
                    }
                    return false; // Skip if no valid sales_item_data found
                });

                // Handle pagination check
                // if (newValue.pagination && newValue.pagination.last_page) {
                //     if (this.page > newValue.pagination.last_page) {
                //         this.page = 1;
                //     }
                // }
            }
        },
    },
    computed: {
        ...mapGetters('supplier', ['currentSupplier']),
        ...mapGetters('searchSerialPurchase', ['currentPurchaseListSearch']),

        showResetButton() {
            // Check if formValues is not empty
            return Object.keys(this.formValues).length > 0;
        }
    },
    watch: {
        showModal(newValue) {
            if ((this.selectedByValue === 'Custom' || this.selectedByValue === 'by Supplier') && this.currentSupplier && this.currentSupplier.length === 0) {
                this.fetchISupplierList();
            }
            if (this.selectedByValue === 'Custom' || this.selectedByValue === 'by Serial') {
                this.fetchPurchaseListSearch({ page: this.page, per_page: 100 })
            }
            setTimeout(() => {
                //--get supplier--
                if (this.supplierList && this.supplierList.length === 0 && this.selectedByValue === 'Custom' || this.selectedByValue === 'by Supplier') {
                    this.open_loader = true;
                    // this.getEmployeeList(1, 'all');
                    if (this.currentSupplier && this.currentSupplier.length > 0) {
                        this.supplierList = this.currentSupplier;
                        this.open_loader = false;
                    }
                }
                if (this.selectedByValue == 'by Supplier') {
                    this.handleFocus();
                }
                if (this.selectedByValue == 'by Date') {
                    const today = new Date().toISOString().substr(0, 10);
                    this.maxDate = today;
                    this.formValues.from = today;
                    this.formValues.to = today;
                }
                this.isOpen = newValue;
            }, 100);
        },
        currentPurchaseListSearch: {
            deep: true,
            handler(newValue) {
                this.is_loading = false;
                this.page += 1;
                this.filterDataPurchase(newValue);
            }
        },
        // filteredData: {
        //     deep: true,
        //     handler(newValue) {

        //         if (newValue.length == 0 && this.formValues.searchQuery && this.formValues.searchQuery !== '' && this.page < this.currentPurchaseListSearch.pagination.last_page) {
        //             this.page += 1;
        //             this.is_loading = true;
        //             this.fetchPurchaseListSearch({ page: this.page, per_page: 100 });
        //         } else if (newValue.length > 1) {
        //             console.log(newValue, 'Waht happening the data....!');
        //             let uniqueInvoices = newValue.filter((item, index, self) =>
        //                 index === self.findIndex(t => t.invoice_id === item.invoice_id)
        //             );
        //             this.filteredData = uniqueInvoices;
        //         }
        //     }
        // },
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>