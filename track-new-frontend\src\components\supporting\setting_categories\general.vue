<template>
    <div class="w-full">
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>
        <div v-if="!open_skeleton" class="flex flex-col items-center">
            <div class="w-full mb-4">
                <!-- Company Name Label -->
                <label for="companyName" class="block text-md font-bold">
                    Company Name<span class="text-red-700">*</span>
                </label>
                <!-- Company Name Input Field -->
                <input type="text" id="companyName" v-model="formValues.company_name" @change="is_updated = true"
                    name="companyName" class="mt-1 p-2 border border-gray-300 w-full"
                    placeholder="Enter company name" />
            </div>
            <!-- GST Number <span
                    class="text-red-700">*</span> @input="validateGST"-->
            <div class="mb-4 w-full">
                <label for="gst_number" class="block text-md font-bold">GST Number (optional)</label>
                <input type="text" id="gst_number" v-model="formValues.gst_number" @change="is_updated = true"
                    class="mt-1 p-2 border border-gray-300 w-full"
                    :class="{ 'outline-red-700': formValues.gst_number !== '' && gstValidation !== '' }"
                    placeholder="Enter GST number" />
                <span v-if="formValues.gst_number !== '' && gstValidation !== ''"
                    class="text-red-700 text-xs absolute block">{{ gstValidation }}</span>
            </div>
            <!-- Business Contact Number @input="validatePhoneNumber(formValues.company_phone_no)"-->
            <div class="mb-4 w-full">
                <label for="business_contact" class="block text-md font-bold">Business Contact
                    Number<span class="text-red-700">*</span></label>
                <input type="tel" id="business_contact" v-model="formValues.company_phone_no"
                    @change="is_updated = true" class="mt-1 p-2 border border-gray-300 w-full"
                    :class="{ 'outline-red-700': validationMessage !== '' }" placeholder="Enter contact number" />
                <span v-if="validationMessage !== ''" class="block text-xs text-red-500">{{ validationMessage
                }}</span>
            </div>
            <!--Email ID-->
            <div class="mb-4 w-full">
                <label for="email" class="block text-md font-bold"
                    :class="{ 'text-red-700': formValues.email && !validateEmail(formValues.email) }">Email ID <span
                        class="text-xs">(This is
                        not
                        username)</span></label>
                <input type="email" id="email" v-model="formValues.email" class="mt-1 p-2 border border-gray-300 w-full"
                    :class="{ 'border-red-700': formValues.email && !validateEmail(formValues.email) }"
                    @change="is_updated = true" placeholder="Enter the email id" />
                <span v-if="formValues.email && !validateEmail(formValues.email)" class="text-red-700">Please enter the
                    valid email ID</span>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 w-full">
                <div class="mb-2 lg:mb-6 w-full justify-left">
                    <label class="block font-bold mb-2">Contact Numbers <span class="text-xs">(Optional)</span></label>

                    <div v-for="(contact, index) in formValues.contacts" :key="index"
                        class="flex items-center space-x-2 mb-2">
                        <input type="text" v-model="contact.type" class="border p-2 rounded w-[50%]"
                            placeholder="Enter Contact Number" required />
                        <input type="text" v-model="contact.number" class="border p-2 rounded w-full"
                            placeholder="Enter Contact Number" required />
                        <button type="button" @click="removeImage({ type: 'contact', index: index })"
                            class="text-red-500" title="Remove">
                            <font-awesome-icon icon="fa-solid fa-trash-can" />
                        </button>
                    </div>
                    <button type="button" @click="addContact" class="bg-blue-500 text-white py-1 px-3 rounded">Add
                        More
                        Contacts</button>
                </div>
                <div class="mb-2 lg:mb-6 w-full">
                    <label class="block font-bold mb-2">Email Addresses <span class="text-xs">(Optional)</span></label>
                    <div v-for="(email, index) in formValues.emails" :key="index"
                        class="flex items-center space-x-2 mb-2">
                        <input type="email" v-model="formValues.emails[index]" class="border p-2 rounded w-full"
                            placeholder="Enter email address"
                            :class="{ 'border-red-700': formValues.emails[index] && !validateEmail(formValues.emails[index]) }" />
                        <button type="button" @click="removeImage({ type: 'email', index: index })" class="text-red-500"
                            title="Remove">
                            <font-awesome-icon icon="fa-solid fa-trash-can" />
                        </button>
                    </div>
                    <button type="button" @click="addEmail" class="bg-blue-500 text-white py-1 px-3 rounded">Add
                        more
                        Mail</button>
                </div>
            </div>
            <!-- Upload Field with Image -->
            <div class="mb-5 w-full">
                <label for="fileUpload" class="block text-md mb-2 font-bold">
                    Upload logo (optional)
                </label>
                <div class="relative p-4 border border-gray-300 bg-gray-200 flex items-center justify-center">
                    <input type="file" id="fileUpload" accept="image/*" @change="handleFileChange" class="hidden" />
                    <label for="fileUpload" class="cursor-pointer">
                        <img v-if="formValues.logo" :src="formValues.logo" alt="Uploaded Image"
                            class="w-40 h-24 object-fit rounded-md" />
                        <div v-else class="w-40 h-24 bg-gray-200 flex items-center justify-center rounded-md">
                            <img :src="computing_img" alt="Click to upload" class="w-40 h-24 object-fit" />
                        </div>
                    </label>
                    <div v-if="circle_loader && !is_signature" class="flex">
                        <CircleLoader :loading="circle_loader"></CircleLoader>
                    </div>
                </div>
            </div>

            <!-- Currency Dropdown -->
            <div class="mb-4 w-full">
                <label for="currency" class="block text-md mb-2 font-bold">
                    Currency<span class="text-red-700">*</span>
                </label>
                <select id="currency" v-model="formValues.currency" @change="is_updated = true"
                    @focus="is_updated = true" @blur="is_updated = false"
                    class="p-2 border border-gray-300 rounded-md w-full"
                    :class="{ 'bg-gray-100 text-gray-500': (formValues.currency === '' && !is_updated) }">
                    <!-- Default placeholder option -->
                    <option value="">Nothing Selected</option>

                    <!-- Dynamic currency options -->
                    <option v-for="currency in currencies" class="bg-white text-black" :key="currency.code"
                        :value="currency.symbol">
                        {{ currency.name }} {{ currency.symbol }} ({{ currency.code }})
                    </option>
                </select>
            </div>
            <!-- Address -->
            <div class="mb-4 w-full">
                <label for="address" class="block text-md font-bold">Address</label>
                <textarea id="address" v-model="formValues.address" rows="3" @change="is_updated = true"
                    class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                    placeholder="Enter address"></textarea>
            </div>
            <!-- Signature Upload -->
            <div class="mb-4 w-full">
                <label for="address" class="block text-md font-bold">Upload Signature Image</label>
                <div class="relative p-4 border border-gray-300 bg-gray-200 flex items-center justify-center">
                    <input type="file" id="fileUploadSign" accept="image/*" @change="onFileChange" class="hidden" />
                    <label for="fileUploadSign" class="cursor-pointer">
                        <img v-if="formValues.signature" :src="formValues.signature" alt="Uploaded Image"
                            class="w-40 h-24 object-fit rounded-md" />
                        <div v-else class="w-40 h-24 bg-gray-200 flex items-center justify-center rounded-md">
                            <img :src="computing_img" alt="Click to upload" class="w-40 h-24 object-fit" />
                        </div>
                    </label>
                    <div v-if="circle_loader && is_signature" class="flex">
                        <CircleLoader :loading="circle_loader"></CircleLoader>
                    </div>
                </div>
            </div>

            <!-- Canvas Signature -->
            <!-- <div class="mb-6">
                <h2 class="text-lg font-bold mb-2">Draw Your Signature</h2>
                <canvas ref="signatureCanvas" width="300" height="150" class="border p-2 bg-white"
                    @mousedown="startDrawing" @mousemove="draw" @mouseup="stopDrawing" @mouseleave="stopDrawing"
                    @touchstart="startDrawing" @touchmove="drawTouch" @touchend="stopDrawing"></canvas>

                <div class="mt-2 flex items-center">
                    <label for="colorPicker" class="mr-2">Select Color:</label>
                    <input type="color" v-model="penColor" id="colorPicker" class="border p-1" />
                </div>

                <div class="mt-2 flex space-x-4">
                    <button @click="clearCanvas" class="bg-red-500 text-white px-4 py-2 rounded">Clear</button>
                    <button @click="saveCanvasSignature" class="bg-blue-500 text-white px-4 py-2 rounded">Save</button>
                </div>
            </div> -->

            <!-- Save Button -->
            <!-- <button @click="saveData"
                class="shadow-inner shadow-green-100 border border-green-500 bg-green-700 text-white py-2 px-4 rounded-md hover:bg-green-600 mt-5">
                {{ type === 'add' ? 'Save' : 'Update' }}
            </button> -->
            <div class="flex justify-between items-center mt-5">
                <button @click="saveData"
                    class="shadow-inner shadow-green-100 border border-green-500 bg-green-700 text-white py-2 px-4 rounded-md hover:bg-green-600">
                    {{ type === 'add' ? 'Save' : 'Update' }}
                </button>

                <button @click="deleteAccountConfirm"
                    class="shadow-inner shadow-red-100 border border-red-500 bg-red-700 text-white py-2 px-4 rounded-md hover:bg-red-600 ml-10">
                    Delete Account
                </button>
            </div>
        </div>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import dialogAlert from '../dialog_box/dialogAlert.vue';
import confirmbox from '../dialog_box/confirmbox.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'general',
    emits: ['is-sales-save', 'updatesalesData'],
    components: {
        dialogAlert,
        confirmbox
    },
    props: {
        companyId: String,
        userId: String,
        isMobile: Boolean,
        store_refresh: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            formValues: {},
            currencies: [
                { name: "United States Dollar", symbol: "$", code: "USD" },
                { name: "Euro", symbol: "€", code: "EUR" },
                { name: "British Pound", symbol: "£", code: "GBP" },
                { name: "Japanese Yen", symbol: "¥", code: "JPY" },
                { name: "Swiss Franc", symbol: "CHF", code: "CHF" },
                { name: "Canadian Dollar", symbol: "$", code: "CAD" },
                { name: "Australian Dollar", symbol: "$", code: "AUD" },
                { name: "Chinese Yuan", symbol: "¥", code: "CNY" },
                { name: "Indian Rupee", symbol: "₹", code: "INR" },
                { name: "Russian Ruble", symbol: "₽", code: "RUB" },
                { name: "Brazilian Real", symbol: "R$", code: "BRL" },
                { name: "South African Rand", symbol: "R", code: "ZAR" },
                { name: "South Korean Won", symbol: "₩", code: "KRW" },
                { name: "Mexican Peso", symbol: "$", code: "MXN" },
                { name: "Turkish Lira", symbol: "₺", code: "TRY" },
                { name: "Saudi Riyal", symbol: "﷼", code: "SAR" },
                { name: "UAE Dirham", symbol: "د.إ", code: "AED" },
                { name: "Hong Kong Dollar", symbol: "$", code: "HKD" },
                { name: "Singapore Dollar", symbol: "$", code: "SGD" },
                { name: "Malaysian Ringgit", symbol: "RM", code: "MYR" },
                { name: "Thai Baht", symbol: "฿", code: "THB" },
                { name: "Indonesian Rupiah", symbol: "Rp", code: "IDR" },
                { name: "Egyptian Pound", symbol: "£", code: "EGP" },
                { name: "Nigerian Naira", symbol: "₦", code: "NGN" },
                { name: "Philippine Peso", symbol: "₱", code: "PHP" },
                { name: "Pakistani Rupee", symbol: "₨", code: "PKR" },
                { name: "Bangladeshi Taka", symbol: "৳", code: "BDT" },
                { name: "Vietnamese Dong", symbol: "₫", code: "VND" },
                { name: "Polish Zloty", symbol: "zł", code: "PLN" },
                { name: "Israeli New Shekel", symbol: "₪", code: "ILS" },
                { name: "Chilean Peso", symbol: "$", code: "CLP" },
                { name: "Colombian Peso", symbol: "$", code: "COP" },
                { name: "Argentine Peso", symbol: "$", code: "ARS" },
                { name: "Peruvian Sol", symbol: "S/", code: "PEN" },
                { name: "New Zealand Dollar", symbol: "$", code: "NZD" },
                { name: "Hungarian Forint", symbol: "Ft", code: "HUF" },
                { name: "Czech Koruna", symbol: "Kč", code: "CZK" },
                { name: "Danish Krone", symbol: "kr", code: "DKK" },
                { name: "Swedish Krona", symbol: "kr", code: "SEK" },
                { name: "Norwegian Krone", symbol: "kr", code: "NOK" },
                { name: "Romanian Leu", symbol: "lei", code: "RON" },
                { name: "Bulgarian Lev", symbol: "лв", code: "BGN" },
                { name: "Croatian Kuna", symbol: "kn", code: "HRK" },
                { name: "Serbian Dinar", symbol: "дин.", code: "RSD" },
                { name: "Bahraini Dinar", symbol: ".د.ب", code: "BHD" },
                { name: "Kuwaiti Dinar", symbol: "د.ك", code: "KWD" },
                { name: "Omani Rial", symbol: "﷼", code: "OMR" },
                { name: "Qatari Riyal", symbol: "﷼", code: "QAR" },
                { name: "Jordanian Dinar", symbol: "د.ا", code: "JOD" },
                { name: "Moroccan Dirham", symbol: "د.م.", code: "MAD" },
                { name: "Tunisian Dinar", symbol: "د.ت", code: "TND" },
                { name: "Algerian Dinar", symbol: "د.ج", code: "DZD" },
                { name: "Georgian Lari", symbol: "ლ", code: "GEL" },
                { name: "Armenian Dram", symbol: "֏", code: "AMD" },
                { name: "Azerbaijani Manat", symbol: "₼", code: "AZN" },
                { name: "Kazakhstani Tenge", symbol: "₸", code: "KZT" },
                { name: "Mongolian Tugrik", symbol: "₮", code: "MNT" },
                { name: "Sri Lankan Rupee", symbol: "₨", code: "LKR" },
                { name: "Ethiopian Birr", symbol: "Br", code: "ETB" },
                { name: "Kenyan Shilling", symbol: "KSh", code: "KES" },
                { name: "Tanzanian Shilling", symbol: "TSh", code: "TZS" },
                { name: "Ugandan Shilling", symbol: "USh", code: "UGX" },
                { name: "Ghanaian Cedi", symbol: "₵", code: "GHS" },
                { name: "Jamaican Dollar", symbol: "$", code: "JMD" },
                { name: "Trinidad and Tobago Dollar", symbol: "$", code: "TTD" },
                { name: "Bahamian Dollar", symbol: "$", code: "BSD" },
                { name: "Belize Dollar", symbol: "$", code: "BZD" },
                { name: "Barbadian Dollar", symbol: "$", code: "BBD" },
                { name: "East Caribbean Dollar", symbol: "$", code: "XCD" },
                { name: "Haitian Gourde", symbol: "G", code: "HTG" },
                { name: "Cuban Peso", symbol: "$", code: "CUP" },
                { name: "Paraguayan Guarani", symbol: "₲", code: "PYG" },
                { name: "Uruguayan Peso", symbol: "$", code: "UYU" },
                { name: "Bolivian Boliviano", symbol: "Bs", code: "BOB" },
                { name: "Venezuelan Bolivar", symbol: "Bs.", code: "VES" },
                { name: "Afghan Afghani", symbol: "؋", code: "AFN" },
                { name: "Myanmar Kyat", symbol: "K", code: "MMK" },
                { name: "Cambodian Riel", symbol: "៛", code: "KHR" },
                { name: "Laotian Kip", symbol: "₭", code: "LAK" }
            ],
            circle_loader: false,
            open_message: false,
            message: '',
            type: 'add',
            validationMessage: '',
            gstValidation: '',
            //--skeleton
            open_skeleton: false,
            number_of_columns: 1,
            number_of_rows: 6,
            gap: 5,
            open_loader: false,
            computing_img: '/images/setting_page/Cloud_computing.png',
            is_updated: false,
            //---signature---
            signatureImage: null,
            penColor: '#000000',
            isDrawing: false,
            ctx: null,
            is_signature: false,
            open_confirmBox: false,
            delete_type: '',
            deleteIndex: null,
            show: false,
            message: '',
            type_toaster: 'info',
            email_validation: null,
        };
    },
    computed: {
        ...mapGetters('companies', ['currentCompanyList']),
    },
    methods: {
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),

        validatePhoneNumber(inputtxt) {
            // Regular expression for phone numbers
            var phoneno = /^(?:0|\+91)?[6-9]\d{9}$/;

            // Check if the input matches the regular expression
            if (inputtxt.match(phoneno)) {
                this.validationMessage = '';
                return true; // Phone number is valid
            } else {
                // alert("Invalid phone number format. Please enter a valid phone number.");
                // this.message = "Invalid phone number format. Please enter a valid phone number.";
                // this.open_message = true;
                this.validationMessage = 'Enter valid contact number';
                return false; // Phone number is invalid
            }
        },
        //--validate GST--
        validateGST() {
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.formValues.gst_number !== '') {
                // Check if the input matches the expected format
                if (regex.test(this.formValues.gst_number.toUpperCase())) {
                    this.gstValidation = '';
                    return true;
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                    return false;
                }
            } else {
                return true;
            }
        },
        async handleFileChange(event) {
            const file = event.target.files[0];
            if (!file) return;
            // Check file size (in bytes)
            const maxSizeBytes = 500 * 1024; // 500kb in bytes
            if (file.size > maxSizeBytes) {
                // Image exceeds 500kb, compress it
                try {
                    this.circle_loader = true; // Show loader
                    const compressedFile = await this.compressImage(file);
                    this.uploadImageProfile(compressedFile, false);
                } catch (error) {
                    console.error("Error compressing image:", error);
                    this.circle_loader = false; // Hide loader on error
                }
            } else {
                // Image is <= 500kb, upload directly
                try {
                    this.circle_loader = true; // Show loader                   

                    this.uploadImageProfile(file, false);
                } catch (error) {
                    console.error("Error uploading image:", error);
                    this.circle_loader = false; // Hide loader on error
                }
            }
        },
        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        uploadImageProfile(file, is_signature) {
            const formData = new FormData();
            formData.append("image", file);
            if (!is_signature) {
                formData.append("model", "companylogo");
                formData.append("company_id", this.companyId);
            }
            else {
                formData.append("model", "companysignature");
                formData.append("company_id", this.companyId);
            }
            // Make an API request to Laravel backend
            // console.log(file, 'RRRR');
            axios.post('/image', formData)
                .then(response => {
                    // console.log(response.data, 'What happrning....Q');
                    this.circle_loader = false;
                    this.is_signature = false;
                    //  let data_save = { image_path: response.data.image_path };
                    if (!is_signature) {
                        if (this.formValues.logo && this.formValues.logo !== '') {
                            this.removeExistImage(this.formValues.logo);
                        }
                        this.formValues.logo = response.data.media_url;
                    } else {
                        if (this.formValues.signature && this.formValues.signature !== '') {
                            this.removeExistImage(this.formValues.signature, true);
                        }
                        this.formValues.signature = response.data.media_url;
                    }
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                    this.circle_loader = false;
                    this.is_signature = false;;
                });
        },
        removeExistImage(url, is_signature) {
            axios.delete('/delete-image', { params: { model: is_signature ? "companysignature" : "companylogo", image_url: url } })
                .then(response => {
                    console.log(response.data.message);
                    // if (is_signature) {
                    //     this.formValues.signature = '';
                    // } else {
                    //     this.formValues.logo = '';
                    // }
                    // this.saveData();
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        saveData() {
            if (this.formValues.company_name && this.formValues.currency && this.formValues.currency !== '' && this.formValues.company_phone_no && (this.formValues.email && this.validateEmail(this.formValues.email) || (!this.formValues.email || this.formValues.email === '')) && (this.formValues.gst_number && this.gstValidation === '' || (!this.formValues.gst_number || this.formValues.gst_number === ''))) {
                this.open_loader = true;
                let sent_data = {
                    company_id: this.companyId,
                    gst_number: this.formValues.gst_number && this.formValues.gst_number !== '' ? this.formValues.gst_number.toUpperCase() : '',
                    ...this.formValues,
                };
                if ((this.formValues.contacts && this.formValues.contacts.length > 0) || (this.formValues.emails && this.formValues.emails.length > 0)) {
                    let contact = [];
                    let email = [];
                    if (this.formValues.contacts && this.formValues.contacts.length > 0) {
                        contact = this.formValues.contacts.filter(opt => opt.number !== '');
                    }
                    if (this.formValues.emails && this.formValues.emails.length > 0) {
                        email = this.formValues.emails.filter(opt => opt !== '');
                    }
                    sent_data.contacts = JSON.stringify({ contacts: contact, emails: email });
                } else {
                    sent_data.contacts = '';
                }
                if (this.type === 'add') {
                    axios.post('/companies', sent_data)
                        .then(response => {
                            // console.log(response.data, 'Response');
                            this.open_loader = false;
                            this.formValues = response.data.data;
                            this.type = "edit";
                            this.openMessage(response.data.message);
                            this.$emit('is-sales-save', false);
                            this.updateCompanyList();
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.openMessage(error.response.data.message);
                            this.open_loader = false;
                        })
                } else if (this.formValues.id) {
                    axios.put(`/companies/${this.formValues.id}`, sent_data)
                        .then(response => {
                            // console.log(response.data, 'Response');
                            this.open_loader = false;
                            this.formValues = response.data.data;
                            this.type = "edit";
                            this.openMessage(response.data.message);
                            this.$emit('is-sales-save', false);
                            this.updateCompanyList();
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.openMessage(error.response.data.message);
                            this.open_loader = false;
                        })
                }
            } else {
                this.openMessage(!this.formValues.company_name ? 'Please fill the company name' : !this.formValues.currency ? 'Please select the currency type' : !this.formValues.company_phone_no ? 'Please fill the business contact number' : !(this.formValues.email && this.validateEmail(this.formValues.email) || (!this.formValues.email || this.formValues.email === '')) ? 'Please enter Valid Email ID' : !(this.formValues.gst_number && this.gstValidation === '' || (!this.formValues.gst_number || this.formValues.gst_number === '')) ? 'Please enter valid GST number' : 'Please fill the * fileds');
                this.$emit('updatesalesData');
            }
        },
        openMessage(msg) {
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.open_message = false;
            this.message = '';
        },
        getExistData() {
            this.open_skeleton = true;
            axios.get(`/companies/${this.companyId}`, { params: { company_id: this.companyId } })
                .then(response => {
                    console.log(response.data, 'Response');
                    this.open_skeleton = false;
                    this.formValues = response.data.data;
                    // if (!this.formValues.currency || this.formValues.currency === '') {
                    //     this.formValues.currency = 'INR';
                    // }
                    this.type = "edit";
                    // this.openMessage(response.data.message);
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                    // this.openMessage(error.response.data.message);
                })
        },
        validateEmail(email) {
            if (email !== '' && email) {
                // Regular expression pattern for basic email validation
                const pattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                // Check if the email matches the pattern
                return pattern.test(email);
            } else {
                return true;
            }
        },
        //----get initialize data
        getInitialStoreData(store_data) {
            this.open_skeleton = false;
            if (store_data && store_data.contacts && store_data.contacts !== '') {
                let data = JSON.parse(store_data.contacts);
                this.formValues = { ...store_data, contacts: data.contacts ? data.contacts : [], emails: data.emails ? data.emails : [] };
            } else {
                this.formValues = store_data;
            }
            this.type = "edit";
        },
        //---signature---
        // Handle image file upload
        async onFileChange(event) {
            const file = event.target.files[0];
            if (!file) return;
            // Check file size (in bytes)
            const maxSizeBytes = 500 * 1024; // 500kb in bytes
            if (file.size > maxSizeBytes) {
                // Image exceeds 500kb, compress it
                try {
                    this.circle_loader = true; // Show loader
                    this.is_signature = true;
                    const compressedFile = await this.compressImage(file);
                    this.uploadImageProfile(compressedFile, true);
                } catch (error) {
                    console.error("Error compressing image:", error);
                    this.circle_loader = false; // Hide loader on error
                    this.is_signature = false;
                }
            } else {
                // Image is <= 500kb, upload directly
                try {
                    this.circle_loader = true; // Show loader         
                    this.is_signature = true;

                    this.uploadImageProfile(file, true);
                } catch (error) {
                    console.error("Error uploading image:", error);
                    this.circle_loader = false; // Hide loader on error
                    this.is_signature = false;
                }
            }
        },
        // Start drawing on canvas
        startDrawing(event) {
            this.isDrawing = true;
            this.ctx.beginPath();
            const { x, y } = this.getPosition(event);
            this.ctx.moveTo(x, y);
        },
        // Draw on canvas
        draw(event) {
            if (!this.isDrawing) return;
            const { x, y } = this.getPosition(event);
            this.ctx.lineTo(x, y);
            this.ctx.stroke();
        },
        // Stop drawing
        stopDrawing() {
            this.isDrawing = false;
            this.ctx.closePath();
        },
        // Get the correct position for mouse and touch events
        getPosition(event) {
            let x, y;

            if (event.type.includes('mouse')) {
                // Mouse event
                x = event.offsetX;
                y = event.offsetY;
            } else if (event.type.includes('touch')) {
                // Touch event
                const rect = event.target.getBoundingClientRect();
                x = event.touches[0].clientX - rect.left;
                y = event.touches[0].clientY - rect.top;
            }

            return { x, y };
        },
        // Clear the canvas
        clearCanvas() {
            const canvas = this.$refs.signatureCanvas;
            this.ctx.clearRect(0, 0, canvas.width, canvas.height);
        },
        // Save the canvas signature as a base64 string
        saveCanvasSignature() {
            const canvas = this.$refs.signatureCanvas;
            console.log(canvas, 'Canvas from direct...');
            const dataURL = canvas.toDataURL('image/png');
            console.log(dataURL, 'This is image coverted...');

            console.log('Signature Saved as Base64:', dataURL);
            // You can send this dataURL to the server or display it
        },
        // Handle touch move for mobile
        drawTouch(event) {
            event.preventDefault();
            this.draw(event);
        },
        // Add/Remove contacts
        addContact() {
            if (Array.isArray(this.formValues.contacts)) {
                this.formValues.contacts.push({ type: 'support', number: '' });
            } else {
                this.formValues.contacts = [{ type: 'support', number: '' }];
            }
        },
        removeContact(index) {
            this.formValues.contacts.splice(index, 1);
        },

        // Add/Remove emails
        addEmail() {
            if (Array.isArray(this.formValues.emails)) {
                this.formValues.emails.push('');
            } else {
                this.formValues.emails = [''];
            }
        },
        removeEmail(index) {
            this.formValues.emails.splice(index, 1);
        },
        //--remove Image
        removeImage(type) {
            if (type) {
                if (type.type === 'contact' && type.index >= 0 && this.formValues.contacts[type.index] && this.formValues.contacts[type.index].number === '') {
                    this.removeContact(type.index);
                } else if (type.type === 'email' && type.index >= 0 && this.formValues.emails[type.index] !== undefined && this.formValues.emails[type.index] === '') {
                    this.removeEmail(type.index);
                } else {
                    this.deleteIndex = type;
                    this.open_confirmBox = true;
                }
            } else {
                this.open_confirmBox = true;
            }

        },
        //--confirmbox delete confirm
        deleteRecord() {
            if (this.deleteIndex && !(this.deleteIndex.type === 'logo' || this.deleteIndex.type === 'logo_text' || this.deleteIndex.type === 'favicon')) {
                if (this.deleteIndex.type === 'contact' && this.deleteIndex.index >= 0) {
                    this.removeContact(this.deleteIndex.index);
                    this.message = 'Contact data removed successfully';
                    this.type_toaster = 'success';
                    this.show = true;
                    this.closeconfirmBoxData();
                } else if (this.deleteIndex.type === 'email' && this.deleteIndex.index >= 0) {
                    this.removeEmail(this.deleteIndex.index);
                    this.message = 'Email data removed successfully';
                    this.type_toaster = 'success';
                    this.show = true;
                    this.closeconfirmBoxData();
                } else {
                    this.closeconfirmBoxData();
                    this.message = 'Data not found';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            } else if (this.delete_type == 'account') {
                this.deleteAccount();
            } else {
                this.closeconfirmBoxData();
            }
        },
        //--confirmbox delete cancel
        cancelDelete() {
            this.closeconfirmBoxData();
        },
        closeconfirmBoxData() {
            this.deleteIndex = null;
            this.open_confirmBox = false;
            this.delete_type = '';
        },
        async deleteAccount() {
            axios.put(`/delete-account/${this.userId}`)
                .then(response => {
                    localStorage.removeItem('track_new');
                    localStorage.removeItem('features');
                    this.closeconfirmBoxData();
                    this.$store.dispatch('resetAll');
                    this.$router.push('/login');
                })
                .catch(error => {
                    console.error('Error', error);
                    this.openMessageDialog(error.response.data.message);
                    this.type_toaster = 'warning';
                    this.show = true;
                    this.closeconfirmBoxData();
                });
        },
        deleteAccountConfirm() {
            this.deleteIndex = null;
            this.delete_type = 'account';
            this.open_confirmBox = true;
        }
    },
    mounted() {
        if (this.companyId && !this.formValues.company_name) {
            // this.getExistData();
            if (this.currentCompanyList && this.currentCompanyList.length > 0) {
                this.open_skeleton = true;
                this.getInitialStoreData(this.currentCompanyList);
                this.updateCompanyList();
            } else {
                this.open_skeleton = true;
                this.updateCompanyList();
            }
        }
        // const canvas = this.$refs.signatureCanvas;
        // this.ctx = canvas.getContext('2d');
        // this.ctx.strokeStyle = this.penColor;
    },
    watch: {
        companyId: {
            deep: true,
            handler(newValue) {
                if (!this.formValues.company_name && newValue) {
                    // this.getExistData();
                    if (this.currentCompanyList && this.currentCompanyList.length > 0) {
                        this.getInitialStoreData(this.currentCompanyList);
                    } else {
                        this.updateCompanyList();
                    }
                }
            }
        },
        currentCompanyList: {
            deep: true,
            handler(newValue) {
                this.getInitialStoreData(newValue);
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.updateCompanyList();
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.saveData();
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.is_updated = false;
                    this.$emit('is-sales-save', newValue);
                }
            }
        },
        // penColor(newColor) {
        //     if (this.ctx) {
        //         this.ctx.strokeStyle = newColor;
        //     }
        // },
    }
};
</script>
<style>
#gst_number {
    text-transform: uppercase;
}

canvas {
    touch-action: none;
    /* Prevent scrolling when drawing */
}
</style>
