<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateServiceAssignsAPIRequest;
use App\Http\Requests\API\UpdateServiceAssignsAPIRequest;
use App\Models\ServiceAssigns;
use App\Repositories\ServiceAssignsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class ServiceAssignsController
 * @package App\Http\Controllers\API
 */

class ServiceAssignsAPIController extends AppBaseController
{
    /** @var  ServiceAssignsRepository */
    private $serviceAssignsRepository;

    public function __construct(ServiceAssignsRepository $serviceAssignsRepo)
    {
        $this->serviceAssignsRepository = $serviceAssignsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/service_assigns",
     *      summary="getServiceAssignsList",
     *      tags={"ServiceAssigns"},
     *      description="Get all ServiceAssigns",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/ServiceAssigns")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $serviceAssigns = $this->serviceAssignsRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($serviceAssigns->toArray(), 'Service Assigns retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/service_assigns",
     *      summary="createServiceAssigns",
     *      tags={"ServiceAssigns"},
     *      description="Create ServiceAssigns",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/ServiceAssigns")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ServiceAssigns"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateServiceAssignsAPIRequest $request)
    {
        $input = $request->all();

        $serviceAssigns = $this->serviceAssignsRepository->create($input);

        return $this->sendResponse($serviceAssigns->toArray(), 'Service Assigns saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/service_assigns/{id}",
     *      summary="getServiceAssignsItem",
     *      tags={"ServiceAssigns"},
     *      description="Get ServiceAssigns",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ServiceAssigns",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ServiceAssigns"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var ServiceAssigns $serviceAssigns */
        $serviceAssigns = $this->serviceAssignsRepository->find($id);

        if (empty($serviceAssigns)) {
            return $this->sendError('Service Assigns not found');
        }

        return $this->sendResponse($serviceAssigns->toArray(), 'Service Assigns retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/service_assigns/{id}",
     *      summary="updateServiceAssigns",
     *      tags={"ServiceAssigns"},
     *      description="Update ServiceAssigns",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ServiceAssigns",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/ServiceAssigns")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ServiceAssigns"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateServiceAssignsAPIRequest $request)
    {
        $input = $request->all();

        /** @var ServiceAssigns $serviceAssigns */
        $serviceAssigns = $this->serviceAssignsRepository->find($id);

        if (empty($serviceAssigns)) {
            return $this->sendError('Service Assigns not found');
        }

        $serviceAssigns = $this->serviceAssignsRepository->update($input, $id);

        return $this->sendResponse($serviceAssigns->toArray(), 'ServiceAssigns updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/service_assigns/{id}",
     *      summary="deleteServiceAssigns",
     *      tags={"ServiceAssigns"},
     *      description="Delete ServiceAssigns",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ServiceAssigns",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var ServiceAssigns $serviceAssigns */
        $serviceAssigns = $this->serviceAssignsRepository->find($id);

        if (empty($serviceAssigns)) {
            return $this->sendError('Service Assigns not found');
        }

        $serviceAssigns->delete();

        return $this->sendSuccess('Service Assigns deleted successfully');
    }
}
