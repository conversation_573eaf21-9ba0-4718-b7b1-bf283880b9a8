// store/modules/service_category.js
import axios from "axios";

const state = {
    service_category: [],
  pagination: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  previousPerPage: null,
  };

  const mutations = {
    SET_SERVICECATEGORY(state, service_categoryData, paginationData) {
          state.service_category = service_categoryData;
          state.pagination = paginationData;
    },
    RESET_STATE(state) {
      state.service_category = [];
      state.pagination = {};   
      state.lastFetchTime = null;
      state.isFetching = false;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    SET_PREVIOUS_PER_PAGE(state, perPage) {
      state.previousPerPage = perPage; // Store the previous per_page value
    },
  };

  const actions = {
    updateServiceCategoryName({ commit }, service_categoryData) {
      // Simulate an asynchronous operation (e.g., API call) to update service_category name
      setTimeout(() => {
        // Commit mutation to update service_category name
        commit('SET_SERVICECATEGORY', service_categoryData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchServiceCategoryList({ state, commit, rootState }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['service_category_update'];    
      const lastUpdateTimeService = rootState.apiUpdates.lastApiUpdates['service_update'];      
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || (lastUpdateTime < state.lastFetchTime && lastUpdateTimeService <= state.lastFetchTime)) {
        return; // Skip request if less than 30 seconds have passed since the last request
      }  
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          commit('SET_IS_FETCHING', true);
          axios.get('/service_categories', { params: { company_id: company_id, page: 1, per_page: 'all' } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'ServiceCategory list..!');
              let service_category_list = response.data.data;
              commit('SET_SERVICECATEGORY', service_category_list, response.data.pagination);
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return service_category_list;
            })
            .catch(error => {
              // Handle error
              commit('SET_IS_FETCHING', false);
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        commit('SET_IS_FETCHING', false);
        console.error('Error fetching item list:', error);
      }
    },
    async addServiceCategory({ state, commit }, {formData }) {
      try {
        // Convert formData to JSON
        const formJson = JSON.stringify(formData);
        // Check if the category already exists
        const exists = state.serviceCategories.some(
          (category) => category.service_category === "AMC Services"
        );

        if (exists) {
          return { success: false, message: "Service category already exists" };
        }
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          // Post new category data
          await axios.post(`/service_categories`, {
            company_id: company_id,
            service_category: "AMC Services",
            form: formJson,
          })
          .then(response => {
            // Handle response
            console.log(response.data, 'ServiceCategory list..!');
            // Update store with new category
          commit("ADD_SERVICE_CATEGORY", response.data);
          commit("SET_CURRENT_SERVICE_CATEGORY", response.data);

          return { success: true, message: "Service category added successfully" };
          })          
        }
        } catch (error) {
          console.error("Error adding service category:", error);
          return { success: false, message: "Failed to add service category" };
        }
      
    },
  };

  const getters = {
    currentServiceCategory(state) {
      return state.service_category;
      },
      currentServiceCategoryPagination(state) {
          return state.pagination;
      }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
