<template>
  <div class="builder-page">
    <div class="flex flex-col md:flex-row justify-between items-center">
      <h1 class="web-head">Gallery</h1>
      <div class="w-full md:w-auto md:self-end">
        <EnablePageName :pages="is_onSetting" @updatePagesEnabled="handleToggle"></EnablePageName>
      </div>
    </div>

    <BuilderLoader v-if="localLoading" :isLoading="localLoading" :progress="localProgress" />
    <div v-if="!localLoading">
      <!-- Storage Status -->
      <div class="my-2 flex justify-between items-center">
        <div>
          <!-- <h2 class="text-lg font-semibold">Storage Status</h2>
          <p>Allocated: 5 MB</p>
          <p>Remaining: {{ remainingSize }} MB</p>
          <div class="h-2 bg-gray-200 rounded mt-2">
            <div class="h-full bg-green-500" :style="{ width: `${usedPercentage}%` }"></div>
          </div> -->
        </div>

        <!--- View Mode Toggle and New Folder Button -->
        <div class="flex items-center space-x-2">
          <button v-if="!selectedFolder" @click="createNewFolder" class="bg-green-500 text-white px-4 py-2 rounded">+
            New Folder</button>
          <button v-if="selectedFolder"
            :class="viewMode === 'thumbnail' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'"
            class="px-4 py-2 rounded-l" @click="setViewMode('thumbnail')">
            Thumbnail View
          </button>
          <button v-if="selectedFolder"
            :class="viewMode === 'list' ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-800'"
            class="px-4 py-2 rounded-r" @click="setViewMode('list')">
            List View
          </button>
        </div>
      </div>

      <!-- Folder View -->
      <div v-if="!selectedFolder" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
        <div v-for="(folder, index) in folders" :key="folder.id"
          class="p-4 bg-white shadow rounded flex flex-col items-center cursor-pointer relative border border-gray-200"
          @click="selectFolder(folder)">
          <font-awesome-icon icon="fa-solid fa-folder" class="text-yellow-500 text-2xl py-1" />

          <!-- Folder Name / Rename Input -->
          <input v-if="folder.isEditing" v-model="folder.name" @blur="finishRenameFolder(folder)"
            class="text-center text-sm border-b focus:outline-none" />
          <p v-else @dblclick="startRenameFolder(folder)" class="text-center text-sm font-semibold">
            {{ folder.name }}
          </p>

          <!-- Three-dot Menu for Options -->
          <div class="absolute top-1 right-1 text-lg cursor-pointer text-gray-500 hover:text-blue-600">
            <i class="fas fa-ellipsis-v  cursor-pointer" @click.stop="toggleMenu(folder, index)"></i>
          </div>

          <!-- Dropdown Menu -->
          <div v-if="folder.showMenu" :ref="'dropdownOpt' + index"
            class="absolute top-8 right-2 bg-white border border-gray-200 shadow-lg rounded-md text-sm z-10">
            <p @click.stop="startRenameFolder(folder)" class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-blue-700">
              <font-awesome-icon icon="fa-solid fa-pencil" class="pr-1" /> Rename
            </p>
            <p @click.stop="deleteFolder(folder, index)"
              class="px-4 py-2 hover:bg-gray-100 cursor-pointer text-red-500">
              <font-awesome-icon icon="fa-solid fa-trash-can" class="pr-1" /> Delete
            </p>
          </div>
        </div>
      </div>

      <!-- File View for Selected Folder with Drag-and-Drop Upload -->
      <div v-else>
        <div class="mb-4 flex justify-between items-center">
          <button @click="deselectFolder" class="text-blue-500 underline">Back to Folders</button>

          <!-- Rename input when viewing the selected folder -->
          <div>
            <input v-if="selectedFolder.isEditing" v-model="selectedFolder.name"
              @blur="finishRenameFolder(selectedFolder)" class="text-lg font-semibold border-b focus:outline-none" />
            <h2 v-else @dblclick="startRenameFolder(selectedFolder)" class="text-lg font-semibold cursor-pointer">
              {{ selectedFolder.name }}
            </h2>
          </div>
        </div>
        <div class="gallery-upload w-full max-w-2xl mx-auto">
          <!-- Drag-and-Drop Section -->
          <div class="file-upload-section mb-6 border-2 border-dashed border-gray-300 p-4 rounded-md text-center"
            @dragover.prevent @dragenter.prevent="isDragging = true" @dragleave="isDragging = false"
            @drop.prevent="handleFileDrop" :class="isDragging ? 'bg-blue-100' : ''">
            <h2 class="text-lg font-bold mb-2">Drag and Drop Files Here</h2>
            <p class="text-gray-500 mb-4">or click below to select files</p>
            <input id="file-upload" type="file" multiple class="hidden" @change="handleFileSelection"
              accept="image/png, image/jpeg, image/webp" />
            <label for="file-upload" class="bg-blue-500 text-white px-4 py-2 rounded-md cursor-pointer">
              Select Files
            </label>
          </div>

          <!-- Submit Button -->
          <!-- <div class="flex justify-center">
            <button @click="handleFileUpload" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-md">
              Upload Files
            </button>
          </div> -->
        </div>

        <!-- Drag-and-Drop Upload Area -->
        <!-- <div class="mb-6 p-6 border-2 border-dashed border-gray-400 rounded-lg text-center text-gray-500"
          @dragover.prevent @drop.prevent="handleFileDrop">
          Drag and drop images here to upload to {{ selectedFolder.name }}
        </div> -->
        <div v-if="circle_loader_photo"
          class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
          <CircleLoader :loading="true"></CircleLoader>
        </div>


        <!-- File List in Selected Folder -->
        <div class="flex justify-between items-center mb-4">
          <div>
            <input type="checkbox" id="selectAll" v-model="selectAllChecked" @change="toggleSelectAll" />
            <label for="selectAll" class="ml-2 text-sm">Select All</label>
          </div>
          <button @click="openConfirmbox" class="bg-red-500 text-white px-4 py-2 rounded"
            :disabled="!hasSelectedImages">
            <font-awesome-icon icon="fa-solid fa-trash-can" class="pr-1" /> Delete Selected
          </button>
        </div>

        <!-- Images Grid -->
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          <div v-for="(file, index) in selectedFolder.files" :key="file.id"
            class="p-4 bg-white shadow rounded flex flex-col items-center relative" :draggable="true"
            @dragstart="onDragStart(index, $event)" @dragover.prevent @drop="onDrop(index, $event)"
            @dragenter="onDragEnter(index, $event)" @dragend="onDragEnd">
            <!-- Checkbox for selecting each image -->
            <input type="checkbox" v-model="file.selected" class="absolute -top-3 -left-1/2" />

            <!-- Image thumbnail and details -->
            <img v-if="viewMode == 'thumbnail'" :src="file.thumbnail" alt="File thumbnail"
              class="w-full h-32 object-cover rounded mb-2" />
            <p class="text-center text-sm">{{ file.name }}</p>
            <p class="text-xs text-gray-500">{{ file.size }} MB</p>

            <!-- Individual delete button -->
            <button @click="deleteAsSingle(index, file.thumbnail)" class="text-red-500 text-xs mt-2">
              <font-awesome-icon icon="fa-solid fa-trash-can" class="pr-1" /> Delete
            </button>
          </div>
        </div>
      </div>
    </div>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteSelectedImages" @onCancel="cancelDelete"></confirmbox>
  </div>
</template>

<script>
import BuilderLoader from '../../components/website-builder/BuilderLoader.vue';
import imageService from '../../services/imageService';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import EnablePageName from './EnablePageName.vue';
export default {
  props: {
    companyId: {
      type: String,
      required: true
    },
    folders: {
      type: Array,
      required: true
    },
    pages: {
      type: Object,
      required: true
    },
    is_updated: { type: Boolean, required: true },
  },
  components: {
    BuilderLoader,
    confirmbox,
    EnablePageName
  },
  data() {
    // console.log(this.folders)
    return {
      selectAllChecked: false,
      localLoading: false, // Local loading state for Gallery
      localProgress: 0, // Local progress state
      allocatedSize: 5, // MB
      usedSize: 0, // MB, dynamically calculated
      viewMode: 'thumbnail', // Default view
      selectedFolder: null, // Holds the current folder being viewed    
      //--loader--
      circle_loader_photo: false,
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      as_single: false,
      isDragging: false, // Tracks drag-and-drop state
      selectedFiles: [], // Stores selected files
      uploadedFiles: [],
      //----is on settings---
      is_onSetting: { is_on: true, name: 'Gallery' },
      draggedIndex: null, // Track the index of the dragged item
      currentDraggedElement: null, // Track the element being dragged for visual feedback
      selectedIndex: null,
      is_folder: false,
    };
  },
  computed: {
    hasSelectedImages() {
      return this.selectedFolder.files.some(file => file.selected);
    },
    usedPercentage() {
      return Math.min((this.usedSize / this.allocatedSize) * 100, 100);
    },
    remainingSize() {
      return (this.allocatedSize - this.usedSize).toFixed(2);
    },
  },
  methods: {
    async openConfirmbox() {
      this.deleteIndex = await this.selectedFolder.files.filter(file => file.selected);
      // console.log(this.deleteIndex, 'Delete index data....!');

      if (this.deleteIndex.length === 0) {
        this.$emit('toasterMessages', { msg: 'Please select images to delete.', type: 'warning' });
        return;
      }
      // Confirm deletion with the user
      this.open_confirmBox = true;
    },
    async deleteSelectedImages() {
      if (this.deleteIndex && this.deleteIndex.length > 0 && !this.as_single) {
        this.$emit('updateLoader', true);
        try {
          // Delete each selected image
          for (const image of this.deleteIndex) {
            await imageService.deleteImage(image.thumbnail, 'gallery'); // Call your delete function with the image URL
          }
          // Remove selected images from the folder's file array
          this.selectedFolder.files = this.selectedFolder.files.filter(file => !file.selected);
          this.selectAllChecked = false; // Reset the "Select All" checkbox
          // alert('Selected images deleted successfully.');
          this.$emit('submitGallery', this.selectedFolder);
          this.$emit('submitData');
          this.$emit('toasterMessages', { msg: 'Images deleted successfully.', type: 'success' });
          this.closeconfirmBoxData();
        } catch (error) {
          console.error('Error deleting selected images:', error);
          if (error.response.data.error === 'Image not found' && this.deleteIndex) {
            this.selectedFolder.files.splice(index, 1);
            this.$emit('submitGallery', this.selectedFolder);
            this.$emit('submitData');
          }
          this.$emit('toasterMessages', { msg: 'Failed to delete selected images.', type: 'warning' });
          this.closeconfirmBoxData();
        }
      } else if (this.as_single && this.deleteIndex) {
        this.$emit('updateLoader', true);
        await this.deleteImage(this.deleteIndex.index, this.deleteIndex.url);
        this.closeconfirmBoxData();
      } else if (this.is_folder && this.selectedIndex >= 0) {
        let store_index = this.selectedIndex;
        this.$emit('updateLoader', true);
        try {
          // Delete each selected image
          for (const image of this.folders[this.selectedIndex].files) {
            await imageService.deleteImage(image.thumbnail, 'gallery'); // Call your delete function with the image URL
          }
          this.folders.splice(store_index, 1);
          this.$emit('submitData');
          this.$emit('toasterMessages', { msg: 'Folder deleted successfully.', type: 'success' });
          this.closeconfirmBoxData();
        } catch (error) {
          console.error('Error deleting selected images:', error);
          if (error.response.data.error === 'Image not found' && this.selectedIndex >= 0) {
            this.folders.splice(store_index, 1);
            this.$emit('submitData');
          }

          this.$emit('toasterMessages', { msg: 'Failed to delete selected Folder images.', type: 'warning' });
          this.closeconfirmBoxData();
        }
      } else {
        this.closeconfirmBoxData();
      }
    },
    deleteAsSingle(index, url) {
      this.deleteIndex = { index: index, url: url };
      this.open_confirmBox = true;
      this.as_single = true;
    },
    async deleteImage(index, src) {
      if (src) {
        try {
          await imageService.deleteImage(src, 'gallery'); // Delete image from server
          this.selectedFolder.files.splice(index, 1);// Clear image file
          this.$emit('submitGallery', this.selectedFolder);
          this.$emit('submitData');
          //alert('Image removed successfully.');
        } catch (error) {
          console.error('Error deleting image:', error);
          if (error.response.data.error === 'Image not found' && this.selectedFolder.files[index]) {
            this.selectedFolder.files.splice(index, 1);
            this.$emit('submitGallery', this.selectedFolder);
            this.$emit('submitData');
          }
          // alert('Failed to delete image.');
          this.$emit('toasterMessages', { msg: 'Failed to delete selected images.', type: 'warning' });
        }
      }
    },
    toggleSelectAll() {
      this.selectedFolder.files.forEach(file => {
        file.selected = this.selectAllChecked;
      });
    },

    setViewMode(mode) {
      this.viewMode = mode;
    },
    createNewFolder() {
      const newFolder = {
        id: Date.now(),
        name: 'New Folder',
        isEditing: true,
        showMenu: false,
        files: []
      };
      this.folders.push(newFolder);
    },
    startRenameFolder(folder) {
      folder.isEditing = true;
    },
    finishRenameFolder(folder) {
      folder.isEditing = false;
      // Add API call or validation if necessary
    },
    selectFolder(folder) {
      if (!folder.isEditing) {
        this.selectedFolder = folder;
      }
    },

    deselectFolder() {
      this.selectedFolder = null;
    },

    handleFileDrop(event) {
      if (!this.selectedFolder) {
        // alert("Please select a folder to upload files.");
        this.$emit('toasterMessages', { msg: 'Please select a folder to upload files.', type: 'warning' });
        return;
      }
      this.circle_loader_photo = true;
      this.localLoading = true;
      this.localProgress = 0;
      this.isDragging = false;

      const files = Array.from(event.dataTransfer.files);
      const imageFiles = files.filter((file) => this.isImageFile(file)); // Filter image files only
      const totalFiles = imageFiles.length;
      let completedFiles = 0;
      if (imageFiles && imageFiles.length !== files.length) {
        this.$emit('toasterMessages', { msg: 'Only image file formats are allowed in the gallery.', type: 'warning' });
      }
      const uploadPromises = imageFiles.map((file) => {
        return this.uploadFile(file).then(() => {
          // Increment progress after each file upload
          completedFiles++;
          this.localProgress = Math.round((completedFiles / totalFiles) * 100);
          console.log(`Progress: ${this.localProgress}%`);
        });
      });

      // Wait for all uploads to complete
      Promise.all(uploadPromises).then(() => {
        this.localLoading = false; // End loading after all files are uploaded
        this.$emit('submitGallery', this.selectedFolder); // Emit event to parent
        this.$emit('submitData');
        this.circle_loader_photo = false;
      });
    },

    async uploadFile(file) {
      //this.isLoading = true;
      if (!this.selectedFolder) return;
      try {
        if (file) {
          this.imageFile = file; // Store the file for upload
          try {
            // Upload the image and store the returned URL
            const response = await imageService.uploadImage(file, 'gallery', this.companyId, this.selectedFolder.name);
            const uploadedFile = {

              name: file.name,
              size: (file.size / (1024 * 1024)).toFixed(2), // Convert to MB
              thumbnail: response.media_url // Use URL from the server response
            };
            this.selectedFolder.files.push(uploadedFile);
            this.$emit('submitGallery', this.selectedFolder);
            this.$emit('submitData');
          } catch (error) {
            console.error('Error uploading image:', error);
            this.$emit('toasterMessages', { msg: 'Failed to upload image.', type: 'warning' });
          }
        }       // Add uploaded file to selected folder's files array
        //this.selectedFolder.files.push(uploadedFile);
      } catch (error) {
        console.error('Upload failed:', error);
        this.$emit('toasterMessages', { msg: 'Failed to upload the file.', type: 'warning' });
      }
    },
    previewFile(file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        const previewImage = {
          id: Date.now(),
          name: file.name,
          size: (file.size / (1024 * 1024)).toFixed(2), // Convert to MB
          thumbnail: e.target.result // Use the data URL from FileReader
        };
        this.selectedFolder.files.push(previewImage);
      };
      reader.readAsDataURL(file); // Read file as data URL
    },
    updateUsedSize() {
      this.usedSize = this.folders.reduce((total, folder) => {
        return total + folder.files.reduce((folderTotal, file) => folderTotal + parseFloat(file.size), 0);
      }, 0);
    },
    toggleMenu(folder, index) {
      folder.showMenu = !folder.showMenu;
      this.selectedIndex = index;
      document.addEventListener('click', this.handleClickOutside);
    },
    handleClickOutside(event) {
      const dropdown = this.$refs['dropdownOpt' + this.selectedIndex];
      // Check if the click is outside the dropdown

      if (dropdown && !dropdown[0].contains(event.target)) {
        this.folders.map((opt, i) => {
          if (i == this.selectedIndex) {
            opt.showMenu = false;
            this.selectedIndex = null;
            document.removeEventListener('click', this.handleClickOutside);
          }
        })
        // this.folder.showMenu = false;
        // this.selectedIndex = null;

      }
    },
    deleteFolder(folder, index) {
      document.removeEventListener('click', this.handleClickOutside);
      // this.folders = this.folders.filter(f => f.id !== folder.id);
      if (this.folders && this.folders[index].files && this.folders[index].files.length > 0) {
        this.selectedIndex = index;
        this.is_folder = true;
        this.open_confirmBox = true;
      } else {
        this.selectedIndex = null;
        this.folders.splice(index, 1);
      }
    },
    initializeFolders() {
      this.folders = this.folders.map(folder => ({
        ...folder,
        showMenu: folder.showMenu || false,
        isEditing: folder.isEditing || false,
      }));
    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    async closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
      this.as_single = false;
      if (this.is_folder) {
        this.is_folder = false;
        this.selectedIndex = null;
      }
    },
    //--gallery upload options---
    handleFileSelection(event) {
      this.circle_loader_photo = true;
      this.localProgress = 0;
      this.isDragging = false;

      const files = Array.from(event.target.files);
      const imageFiles = files.filter((file) => this.isImageFile(file)); // Filter image files only
      const totalFiles = imageFiles.length;
      let completedFiles = 0;
      if (imageFiles && imageFiles.length !== files.length) {
        this.$emit('toasterMessages', { msg: 'Only image file formats are allowed in the gallery.', type: 'warning' });
      }
      const uploadPromises = imageFiles.map((file) => {
        return this.uploadFile(file).then(() => {
          // Increment progress after each file upload
          completedFiles++;
          this.localProgress = Math.round((completedFiles / totalFiles) * 100);
          console.log(`Progress: ${this.localProgress}%`);
        });
      });

      // Wait for all uploads to complete
      Promise.all(uploadPromises).then(() => {
        this.$emit('submitGallery', this.selectedFolder); // Emit event to parent
        this.circle_loader_photo = false;
      });
    },

    //--filter only images--
    isImageFile(file) {
      // Check MIME type of file
      return file.type.startsWith('image/');
    },
    //--handle switch--
    handleToggle(data) {
      // Perform any additional actions when toggled
      if (data) {
        this.is_onSetting = data;
      }
    },
    // Method to handle the drag start event
    onDragStart(index, event) {
      this.draggedIndex = index;
      this.currentDraggedElement = event.target;
      this.currentDraggedElement.classList.add('dragging'); // Optional visual feedback
    },

    // Method to handle the drag enter event (when dragged item enters another item)
    onDragEnter(index, event) {
      event.preventDefault();
      const targetElement = event.target;
      if (this.draggedIndex !== index) {
        targetElement.classList.add('drag-over'); // Highlight the target element
      }
    },

    // Method to handle the drop event to reorder the list
    onDrop(targetIndex, event) {
      event.preventDefault();
      if (this.draggedIndex !== targetIndex) {
        const movedFile = this.selectedFolder.files.splice(this.draggedIndex, 1)[0];
        this.selectedFolder.files.splice(targetIndex, 0, movedFile);
      }
      this.resetDragState(event);
    },

    // Method to handle the drag end event (reset visual feedback)
    onDragEnd(event) {
      this.resetDragState(event);
    },

    // Reset the drag state
    resetDragState(event) {
      if (this.currentDraggedElement) {
        this.currentDraggedElement.classList.remove('dragging'); // Remove the dragging class
      }
      const targetElement = event.target;
      targetElement.classList.remove('drag-over'); // Remove the drag-over class
    },

  },
  mounted() {
    this.updateUsedSize();

    if (this.pages && this.pages.gallery !== undefined) {
      this.is_onSetting = this.pages.gallery;
    }
  },
  watch: {
    is_updated: {
      deep: true,
      handler(newValue) {
        this.$emit('submitGallery', this.selectedFolder);
        this.$emit('updatePagesSetting', { gallery: this.is_onSetting });
      }
    },
    is_onSetting: {
      deep: true,
      handler(newValue) {
        this.$emit('updatePagesSetting', { gallery: newValue });
      }
    }
  }
};
</script>

<style scoped>
/* Add any additional styles if needed */
</style>
