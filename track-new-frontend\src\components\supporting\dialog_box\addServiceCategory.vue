<template>
    <div v-if="showModal" class="fixed sm:top-0 bottom-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50"
        @keyup.esc="cancelModal()">

        <!-- Modal -->
        <div ref="modal"
            class="model bg-white sm:w-3/4 lg:w-3/4 w-full top-0 overflow-auto sm:bottom-[10px] transform ease-in-out duration-100 h-screen text-sm"
            :class="{ 'translate-y-0': isOpen, 'translate-y-full bottom-12': !isOpen }">
            <!-- <div class="modal-content"> @click.self="cancelModalData()" -->

            <div class="justify-between items-center flex py-4 set-header-background">
                <p class="text-white font-bold items-center text-center flex justify-end ml-10 text-lg">
                    <span class="pr-1 text-xl">
                        {{ this.editData ? 'Edit' : 'Add' }}
                    </span> Service Category
                </p>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <div class="p-4">
                <div class="p-2 pl-5 pr-5">
                    <label for="service_category" class="font-semibold">Service Category Name</label>
                    <!--tooltip-->
                    <div v-if="formValues.service_category && formValues.service_category.length > 1"
                        class="absolute flex flex-col items-center group-hover:flex -mt-7">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Press the <span class="border border-white px-1 py-1 rounded">Enter &#9166;</span> key to
                                save the form</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                    <input id="service_category" v-model="formValues.service_category" type="text" ref="serviceList"
                        @input="validateInput" @focus="focussed.name = true" @blur="focussed.name = false"
                        @keyup.enter="sendModal" class="mt-1 p-2 border rounded w-full outline-none py-1"
                        :class="{ 'border-blue-700': focussed.name }" />
                    <p v-if="validate_msg !== '' && (formValues.service_category === '' || !formValues.service_category)"
                        class="text-xs text-red-700">Please fill the data</p>
                </div>
                <!-- <div class="p-3 pl-5 pr-5">
                    <label for="service_status">Status</label>
                    <select id="service_status" v-model="serviceStatus" class="mt-1 p-2 border border-gray-300 w-full">
                        <option value="" disabled selected>Select status</option>
                        <option value="0">Disabled</option>
                        <option value="1">Enabled</option>
                    </select>
                </div> -->
                <div class="p-3 pl-5 pr-5">
                    <label class="font-semibold">Select Status</label><br>
                    <div class="flex items-center mt-3">
                        <input type="radio" id="status_disabled" value="0" v-model="formValues.service_status">
                        <label for="status_disabled">Disabled</label><br>
                        <input type="radio" id="status_enabled" value="1" v-model="formValues.service_status"
                            class="ml-2">
                        <label for="status_enabled">Enabled</label><br>
                    </div>
                    <p v-if="validate_msg !== '' && (formValues.service_status === '' || !formValues.service_status)"
                        class="text-xs text-red-700">Please select the status</p>
                </div>
                <!-- Buttons -->
                <div class="flex justify-center items-center mt-5">
                    <button @click="cancelModal"
                        class="bg-pink-600 rounded text-white text-md p-2 px-3 py-2 mr-3 hover:bg-pink-500">Cancel</button>
                    <button @click="sendModal"
                        class="bg-green-600 rounded text-white text-md p-2 px-4 py-2 ml-2 hover:bg-green-500 ">Save</button>
                </div>
                <!---improt from cloud templates-->
                <div class="flex justify-center items-center mt-10">
                    <button @click="showModalImport"
                        class="flex items-center bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded">
                        <font-awesome-icon icon="fa-solid fa-cloud-upload-alt" class="mr-2" />
                        Import from Cloud Templates
                    </button>
                </div>
            </div>
            <!-- </div> -->
        </div>
        <importServiceForm :showModal="show_importModal" @close-modal="closeImportModal" :companyId="companyId"
            :getplanfeatures="getplanfeatures" @opennoaccess="opennoaccess">
        </importServiceForm>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type" @update:show="show = false"></Toaster>
        <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
    </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
import importServiceForm from './importServiceForm.vue';
import noAccessModel from './noAccessModel.vue';
export default {
    name: 'addServiceCategory',
    components: {
        importServiceForm,
        noAccessModel
    },
    props: {
        showModal: Boolean,
        editData: Object,
        companyId: String,
    },
    data() {
        return {
            isMobile: false,
            validate_msg: '',
            isOpen: false,
            focussed: {},
            formValues: { service_status: '1' },
            open_loader: false,
            show: false,
            message: '',
            type: 'info',
            //---import modal---
            show_importModal: false,
            //---no access---
            no_access: false,
        }
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        validateInput() {
            const validPattern = /^[a-zA-Z0-9-]*$/;
            const specialChars = /[!@#$%^&*()_+\=\[\]{};':"\\|,.<>\/?]/;

            if (!validPattern.test(this.formValues.service_category)) {
                // this.$root.$emit('show-toast', { message: 'Only letters, numbers, and hyphens are allowed.', type: 'info' });
                this.message = 'Only letters, numbers, and hyphens are allowed.';
                this.show = true;
                // Check if the input contains any special characters
                if (specialChars.test(this.formValues.service_category)) {
                    // Replace special characters with an empty string
                    this.formValues.service_category = this.formValues.service_category.replace(specialChars, '');
                }
            }
        },
        cancelModal(data) {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                if (data && data.service_category !== undefined) {
                    this.$emit('close-modal', data);
                    if (this.editData) {
                        this.formValues = { service_status: '1' };
                    }
                } else {
                    this.$emit('close-modal');
                    if (this.editData) {
                        this.formValues = { service_status: '1' };
                    }
                }
                this.validate_msg = '';
            }, 300);
        },
        cancelModalData() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
                if (this.editData) {
                    this.formValues = { service_status: '1' };
                }
            }, 300);
            this.validate_msg = '';
        },
        sendModal() {
            if (this.getplanfeatures('service_category')) {
                this.no_access = true;
            } else {
                // Handle sending logic here
                if (this.formValues.service_category && this.formValues.service_category !== '' && this.formValues.service_status) {
                    this.open_loader = true;
                    if (this.editData) {
                        axios.put(`/service_categories/${this.editData.id}`, { ...this.formValues, company_id: this.companyId })
                            .then(response => {
                                // console.log(response.data, 'Hello...!');                         
                                this.open_loader = false;
                                this.cancelModal(response.data.data);
                                this.formValues = { service_status: '1' };
                                this.updateKeyWithTime('service_category_update');
                            })
                            .catch(error => {
                                console.error('Error', error);
                                this.open_loader = false;
                                this.message = error.response.data.message ? error.response.data.message : error.response.message;
                                this.type = 'warning';
                                this.show = true;
                            })
                    } else {
                        axios.post('/service_categories', { service_categories: [{ ...this.formValues, company_id: this.companyId }], company_id: this.companyId })
                            .then(response => {
                                // console.log(response.data, 'Hello...!');
                                this.open_loader = false;
                                if (response.data.error) {
                                    this.message = `${response.data.error[0]} category name already exist, Please change the name`;
                                    this.type = 'warning';
                                    this.show = true;
                                } else {
                                    this.cancelModal(response.data.data[0]);
                                    this.formValues = { service_status: '1' };
                                    this.updateKeyWithTime('service_category_update');
                                }
                            })
                            .catch(error => {
                                console.error('Error', error);
                                this.open_loader = false;
                                this.message = error.response.data.message ? error.response.data.message : error.response.message;
                                this.type = 'warning';
                                this.show = true;
                            })
                    }
                } else {
                    this.validate_msg = 'Please fill the data..!'
                }
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                // console.log(this.editData, 'May know waht happening...!!!');
                this.formValues.service_category = this.editData ? this.editData.service_category : '';
                this.formValues.service_status = this.editData ? this.editData.service_status : '';
            } else {
                // Reset data when editData is not provided
                this.formValues = { service_status: '1' };
            }
        },
        handleOutsideClick(event) {
            if (this.$refs.modal && !this.$refs.modal.contains(event.target) && this.showModal && this.isOpen) {
                this.cancelModalData(event);
            }
        },
        showModalImport() {
            this.show_importModal = true;
        },
        closeImportModal() {
            this.show_importModal = false;
            this.cancelModalData();
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        opennoaccess() {
            this.no_access = true;
        }
    },
    mounted() {
        this.updateIsMobile();
        // console.log(this.editData, 'What happppppp');
        if (!this.editData) {
            this.formValues = { service_status: '1' };
        }
        window.addEventListener('resize', this.updateIsMobile);
        // document.addEventListener('click', this.handleOutsideClick);
    },
    beforeDestroy() {
        // document.removeEventListener('click', this.handleClickOutside);
    },
    watch: {
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            handler() {
                this.initializeData();
            },
            immediate: true,
        },
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.$nextTick(() => {
                    if (this.$refs.serviceList) {
                        this.$refs.serviceList.focus();
                        this.$refs.serviceList.click();
                    }
                })
            }, 100);
        },
    },
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
/* @media (max-width: 767px) {
    .modal {
        height: 80vh; */
/* Adjust the height as needed */
/* }

    .modal-content {
        overflow-y: auto;
    }
} */
</style>
