<template>
    <div v-if="showModal" class="text-sm fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50"
        :class="{ 'mb-[0px]': isMobile }">
        <div class="bg-white w-full sm:w-3/4  transform transition-transform ease-in-out duration-300 rounded overflow-auto sm:h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div
                class="flex justify-between items-center relative text-xl w-full px-4 py-3 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    {{ editData ? 'Edit Item' : 'Add New Item' }}
                </p>
                <p class="close" @click.stop="closeModal">&times;</p>
            </div>
            <!---form-->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 m-3 sm:m-7  lg:m-7 ">
                <!---item type-->
                <div>
                    <label for="Product"
                        :class="{ 'bg-gray-200 text-gray-600': formValues.product_type !== 'Product', 'bg-gray-600 text-white': formValues.product_type === 'Product' }"
                        class="px-4 mr-2 py-2 cursor-pointer inline-block rounded-md transition-colors duration-300 hover:bg-gray-400 hover:text-white">
                        Product
                        <input type="radio" :id="'Product'" value="Product" v-model="formValues.product_type"
                            :class="{ 'hidden': formValues.product_type !== 'Product' }" />
                    </label>
                    <label for="Services"
                        :class="{ 'bg-gray-200 text-gray-600': formValues.product_type !== 'Services', 'bg-gray-600 text-white': formValues.product_type === 'Services' }"
                        class="ml-2 px-4 py-2 cursor-pointer inline-block rounded-md transition-colors duration-300 hover:bg-gray-400 hover:text-white">
                        Services
                        <input type="radio" :id="'Services'" value="Services" v-model="formValues.product_type"
                            :class="{ 'hidden': formValues.product_type !== 'Services' }" />
                    </label>
                </div>
                <!--product name-->
                <div class="mr-2 relative">
                    <!-- @mouseover="isInputFocused.product_name = true" @mouseleave="isInputFocused.product_name = false" -->
                    <label for="product_name" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.product_name = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.product_name || isInputFocused.product_name, 'text-blue-600': isInputFocused.product_name }">
                        Product Name <span v-if="formValues.product_name || isInputFocused.product_name"
                            class="text-red-600">*</span></label>
                    <input id="product_name" v-model="formValues.product_name" type="text" ref="product_name"
                        placeholder=" "
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.product_name = true" />
                    <p v-if="validation_message !== '' && !formValues.product_name"
                        class="absolute text-[10px] text-red-700">Please enter product name</p>
                </div>
                <!--product category-->
                <div v-if="isExpanded" class="flex mr-2 relative">
                    <label for="category" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.category_id = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.category_id || isInputFocused.category_id, 'text-blue-600': isInputFocused.category_id }">
                        Category </label>
                    <select id="category_id" v-model="formValues.category_id" ref="category_id"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded rounded-r-none"
                        @focus="isInputFocused.category_id = true" title="Click &#43; icon to add new or edit">
                        <option v-for="(cat, index) in category_list" :key="index" :value="cat.id">
                            {{ cat.category_name }}</option>
                    </select>
                    <button @click="openModelCategory('category')"
                        class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>
                </div>
                <!--product Brand-->
                <div v-if="formValues.product_type !== 'Services' && isExpanded" class="flex mr-2 relative">
                    <label for="brand" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.brand = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.brand || isInputFocused.brand, 'text-blue-600': isInputFocused.brand }">
                        Brand
                        <!-- <span v-if="formValues.brand || isInputFocused.brand"class="text-red-600">*</span> -->
                    </label>
                    <select id="brand" v-model="formValues.brand" ref="brand"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded rounded rounded-r-none"
                        @focus="isInputFocused.brand = true" title="Click &#43; icon to add new or edit">
                        <option v-for="(brand, index) in brand_list" :key="index" :value="brand.id">
                            {{ brand.brand_name }}
                        </option>
                    </select>
                    <button @click="openModelCategory('brand')"
                        class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>
                </div>
                <!--product Unit-->
                <div class="flex mr-2 relative" ref="unitContainer">
                    <label for="unit" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.unit = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.unit || isInputFocused.unit, 'text-blue-600': isInputFocused.unit }">
                        Unit <span v-if="formValues.unit || isInputFocused.unit" class="text-red-600">*</span></label>
                    <!-- <select id="unit" v-model="formValues.unit" ref="unit"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded rounded rounded-r-none"
                        @focus="isInputFocused.unit = true" title="Click &#43; icon to add new or edit">
                        <option v-for="(unit, index) in unit_list" :key="index" :value="unit.unit_name">{{
                            unit.unit_name }}</option>
                    </select> -->
                    <input type="text" :placeholder="'Select the unit'" ref="unit"
                        class="border border-gray-300 rounded-r-none w-full p-1 mt-1 px-1 py-1"
                        v-model="formValues.unit" @input="filterUnits" @change="filterUnits" @focus="filterUnits"
                        @keydown.enter="handleEnterKey" @keydown.down.prevent="handleDownArrow(filteredUnits)"
                        @keydown.up.prevent="handleUpArrow(filteredUnits)" />
                    <!-- Dropdown container -->
                    <div v-if="showSuggestions"
                        class="absolute mt-11 bg-white border rounded shadow-md w-full z-50 overflow-auto px-2 cursor-pointer">
                        <!-- List of customer suggestions -->
                        <ul style="max-height: 200px;">
                            <li v-for="(leads, index) in filteredUnits" :key="index" @click="selectCustomer(leads)"
                                class="hover:bg-gray-300 py-1 px-1" :class="{ 'bg-gray-300': index === selectedIndex }">
                                {{ leads }}
                            </li>
                        </ul>
                    </div>
                    <button @click="openModelCategory('unit')"
                        class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>
                    <p v-if="validation_message !== '' && !formValues.unit"
                        class="absolute text-[10px] text-red-700 mt-10">Please select unit</p>
                </div>
                <!---HSN code-->
                <div v-if="isExpanded" class="mr-2 relative">
                    <label for="hsn_code" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.hsn_code = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.hsn_code || isInputFocused.hsn_code, 'text-blue-600': isInputFocused.hsn_code }">
                        HSN</label>
                    <input id="hsn_code" v-model="formValues.hsn_code" type="text" ref="hsn_code"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.hsn_code = true" />
                    <!-- <p v-if="validation_message !== '' && !formValues.hsn_code"
                        class="absolute text-[10px] text-red-700">Please enter HSN code</p> -->

                </div>
                <!---alert quantity-->
                <div v-if="formValues.product_type !== 'Services' && isExpanded" class="mr-2 relative">
                    <label for="alert_qty" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.alert_qty = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.alert_qty || isInputFocused.alert_qty >= 0, 'text-blue-600': isInputFocused.alert_qty }">
                        Alert Quantity </label>
                    <input id="alert_qty" v-model="formValues.alert_qty" type="number" ref="alert_qty"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.alert_qty = true" @blur="isInputFocused.alert_qty = false" />
                </div>
                <!---Barcode-->
                <div v-if="formValues.product_type !== 'Services' && isExpanded" class="mr-2 relative">
                    <label for="barcode" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.barcode = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.barcode || isInputFocused.barcode, 'text-blue-600': isInputFocused.barcode }">
                        Barcode</label>
                    <input id="barcode" v-model="formValues.barcode" type="text" ref="barcode"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.barcode = true" @blur="isInputFocused.barcode = false" />
                </div>

                <!--discount type-->
                <div v-if="isExpanded" class="flex mr-2 relative">
                    <label for="discount_type"
                        class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                        @click="isInputFocused.discount_type = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.discount_type !== undefined && formValues.discount_type) || isInputFocused.discount_type, 'text-blue-700': isInputFocused.discount_type }">
                        Discount Type</label>
                    <select id="discount_type" v-model="formValues.discount_type" @change="validateMax"
                        class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                        @focus="isInputFocused.discount_type = true" @blur="isInputFocused.discount_type = false">
                        <!-- <option value="">select type</option> -->
                        <option value="Percentage">percentage (%)</option>
                        <option value="Fixed">Fixed (₹)</option>
                    </select>
                </div>
                <!--discount value-->
                <div v-if="isExpanded" class="flex mr-2 relative">
                    <label for="discount"
                        class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                        @click="isInputFocused.discount = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.discount !== undefined && formValues.discount >= 0) || isInputFocused.discount, 'text-blue-700': isInputFocused.discount }">
                        Discount Value</label>
                    <input v-model="formValues.discount" @input="validateMax"
                        class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                        @focus="isInputFocused.discount = true" @blur="isInputFocused.discount = false" />
                </div>
                <!---Price <span v-if="formValues.price || isInputFocused.price"
                            class="text-red-600">*</span>-->
                <div v-if="formValues.product_type !== 'Services'" class="mr-2 relative">
                    <label for="price" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.price = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.price || isInputFocused.price, 'text-blue-600': isInputFocused.price }">
                        Purchase Price </label>
                    <input id="price" v-model="formValues.price" type="number" ref="price"
                        @input="handleSalesAndPurchse(formValues.price)"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.price = true" />
                    <!-- <p v-if="validation_message !== '' && !formValues.price" class="absolute text-[10px] text-red-700">
                        Please enter price value</p> -->
                </div>
                <!--Tax type-->
                <div v-if="isExpanded" class="flex mr-2 relative">
                    <label for="tax_type"
                        class="text-sm font-bold absolute left-2 rounded top-3 text-gray-300 transition-top linear duration-300"
                        @click="isInputFocused.gst_type = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.gst_type !== undefined && formValues.gst_type) || isInputFocused.gst_type, 'text-blue-700': isInputFocused.gst_type }">
                        Tax Type</label>
                    <select id="gst_type" v-model="formValues.gst_type"
                        @change="calculatePurchasePrice(formValues.gst_type)"
                        class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                        @focus="isInputFocused.gst_type = true" @blur="isInputFocused.gst_type = false">
                        <!-- <option value="">select type</option> -->
                        <option value="Exclusive">Exclusive</option>
                        <option value="Inclusive">Inclusive</option>
                    </select>
                </div>
                <!--Tax-->
                <div v-if="isExpanded" class="flex mr-2 relative">
                    <label for="tax_value"
                        class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                        @click="isInputFocused.gst_value = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.gst_value !== undefined && formValues.gst_value >= 0) || isInputFocused.gst_value >= 0, 'text-blue-700': isInputFocused.gst_value >= 0 }">
                        Tax Value</label>
                    <!--tooltip-->
                    <div v-if="tooltip_focus && tooltip_focus === 'general'"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Please update this in the general setting</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                    <!-- <select id="gst_value" v-model="formValues.gst_value"
                        @change="calculatePurchasePrice(formValues.gst_type)"
                        class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-r-none outline-none"
                        @focus="isInputFocused.gst_value = true" @blur="isInputFocused.gst_value = false"
                        title="Click &#43; icon to add new or edit">
                        <option v-for="(tax, index) in tax_value_list" :key="index" :value="tax.value">
                            {{ tax.tax_name + ' @ ' + tax.value }}</option>
                    </select> -->
                    <select id="gst_value" v-model="formValues.selected_tax"
                        @change="updateTaxName(formValues.selected_tax)"
                        class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-r-none outline-none"
                        @focus="isInputFocused.gst_value = true" @blur="isInputFocused.gst_value = false"
                        title="Click &#43; icon to add new or edit">
                        <option v-for="(tax, index) in tax_value_list" :key="index"
                            :value="{ tax_name: tax.tax_name, value: tax.value }">
                            {{ tax.tax_name + ' @ ' + tax.value }}
                        </option>
                    </select>
                    <button @click="openTaxModel"
                        class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>

                </div>
                <!---Purchase Price <span v-if="formValues.purchase_price || isInputFocused.purchase_price"
                            class="text-red-600">*</span>-->
                <div v-if="formValues.product_type !== 'Services' && isExpanded" class="mr-2 relative">
                    <label for="purchase_price" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.purchase_price || isInputFocused.purchase_price, 'text-blue-600': isInputFocused.purchase_price }">
                        Purchase Price (With Tax) </label>
                    <input id="purchase_price" v-model="formValues.purchase_price" type="number" ref="purchase_price"
                        class="text-sm bg-gray-100 p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.purchase_price = true" readonly @change="calculateSalesPrice" />
                    <!-- <p v-if="validation_message !== '' && !formValues.price" class="absolute text-[10px] text-red-700">
                        Please enter price value</p> -->
                </div>
                <!---Profit margin-->
                <div v-if="isExpanded" class="mr-2 relative">
                    <label for="profitmargin" class=" flex text-sm font-bold absolute left-2 top-3 text-gray-300"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.profitmargin || isInputFocused.profitmargin, 'text-blue-600': isInputFocused.profitmargin }">
                        Profit margin (%) </label>
                    <input id="profitmargin" v-model="formValues.profitmargin" type="number" ref="profitmargin"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.profitmargin = true" @blur="isInputFocused.profitmargin = false"
                        @input="calculateSalesPrice" />
                </div>
                <!---sales Price-->
                <div class="mr-2 relative">
                    <label for="sales_price" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.sales_price = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.sales_price || isInputFocused.sales_price, 'text-blue-600': isInputFocused.sales_price }">
                        Sales Price <span v-if="formValues.sales_price || isInputFocused.sales_price"
                            class="text-red-600">*</span></label>
                    <input id="sales_price" v-model="formValues.sales_price" type="number" ref="sales_price"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.sales_price = true" @input="calculateProfitMargin" />
                    <p v-if="validation_message !== '' && !formValues.sales_price"
                        class="absolute text-[10px] text-red-700">Please enter sales price value</p>
                </div>
                <!---dealer Price-->
                <div v-if="isExpanded" class="mr-2 relative">
                    <label for="dealer_price" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.dealer_price = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.dealer_price >= 0 || isInputFocused.dealer_price, 'text-blue-600': isInputFocused.dealer_price }">
                        Dealer Price </label>
                    <input id="dealer_price" v-model="formValues.dealer_price" type="number" ref="dealer_price"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.dealer_price = true" />
                    <p v-if="validation_message !== '' && !formValues.dealer_price"
                        class="absolute text-[10px] text-red-700">Please enter dealer price value</p>
                </div>
                <!---Opening stock-->
                <div v-if="formValues.product_type !== 'Services'" class="mr-2 relative">
                    <label for="open_stock" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.total_qty = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.total_qty || isInputFocused.total_qty, 'text-blue-600': isInputFocused.total_qty }">
                        Opening stock
                        <!-- <span v-if="formValues.total_qty || isInputFocused.total_qty" class="text-red-600">*</span> -->
                    </label>
                    <input id="total_qty" v-model="formValues.total_qty" type="number" ref="total_qty" min="0"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.total_qty = true"
                        :readonly="this.editData && Object.keys(this.editData).length > 0" />
                    <!-- <p v-if="validation_message !== '' && !formValues.total_qty"
                        class="absolute text-[10px] text-red-700">Please enter opening stock count</p> -->
                </div>
                <!---warranty in month  v-if="formValues.product_type !== 'Services'" -->
                <div class="mr-2 relative">
                    <label for="warranty" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        @click="isInputFocused.warranty = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.warranty >= 0 || isInputFocused.warranty, 'text-blue-600': isInputFocused.warranty }">
                        Warranty in Months
                    </label>
                    <input type="number" id="warranty" v-model="formValues.warranty" min="0"
                        @focus="isInputFocused.warranty = true"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                </div>
                <!---note-->
                <div v-if="isExpanded" class="mr-2 relative">
                    <label for="note" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.notes || isInputFocused.notes, 'text-blue-600': isInputFocused.notes }">
                        Note </label>
                    <textarea id="note" v-model="formValues.notes" ref="note" rows="3"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                        @focus="isInputFocused.notes = true" @blur="isInputFocused.notes = false"></textarea>
                </div>
                <!--product image-->
                <div class="flex  items-center justify-center pl-4 pr-4 cursor-pointer">
                    <div class="w-full relative items-center mt-3" title="Change profile" @click="openFileInput"
                        @mouseover="isHovered = true" @mouseleave="isHovered = false">
                        <img v-if="formValues.image" :src="formValues.image"
                            class="w-[120px] h-[120px] justify-center border border-gray-300"
                            :class="{ 'filter': isHovered }" />
                        <!-- <img v-if="!formValues.image" :src="upload_profile" class="justify-center"
                            :class="{ 'filter': isHovered }" /> -->
                        <div v-if="!formValues.image" class="text-center border-2 border-dashed border-gray-300 py-2">
                            <p><font-awesome-icon icon="fa-solid fa-arrow-up-from-bracket" /> Upload Image</p>
                        </div>
                        <input ref="fileInput" type="file" style="display: none" accept="image/*"
                            @change="handleImageChangeProfile" />
                        <!-- <div class="absolute inset-0 flex mt-6 justify-center items-center" v-show="isHovered">
                            <span class="text-gray-500 text-xs font-bold text-center">Change profile</span>
                        </div> -->
                        <!--loader circle-->
                        <div v-if="circle_loader_photo" class="flex">
                            <CircleLoader :loading="circle_loader_photo"></CircleLoader>
                        </div>
                    </div>
                </div>
                <!---add more info options-->
                <div class="relative">
                    <button @click="toggleInfo"
                        class="flex items-center justify-between w-full p-2 bg-blue-500 text-white rounded hover:bg-blue-600">
                        <span v-if="isExpanded">Less Info</span>
                        <span v-else>More info</span>
                        <font-awesome-icon :icon="isExpanded ? 'angle-up' : 'angle-down'" />
                    </button>
                </div>
            </div>
            <!---services material add-->
            <!--Extra materials divStyle p-2 sm:p-3 lg:p-5-->
            <div class="grid grid-cols-1 gap-2" :class="{ 'hidden': formValues.product_type !== 'Services' }">
                <div class="m-2 sm:m-2 lg:m-2">
                    <div>
                        <div class="flex items-center mb-2">

                            <!-- <div class="w-full sm:flex">
                            <div v-if="!formValues['additional'] || !formValues['additional'].length > 0"
                                class="px-1 sm:px-3">
                                <button @click="addRow($event)" class="text-blue-600"> + Add Material</button>
                            </div>
                        </div> -->
                            <!--view design-->
                            <div v-if="formValues['additional'] && formValues['additional'].length > 0"
                                class="flex justify-end items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                                <div class="px-2 py-1 flex-shrink-0 cursor-pointer info-msg border rounded-l-full border-r-0 border-gray-500 z-10"
                                    :class="{ 'bg-blue-200': additional_table }" @click="toggleView"
                                    :title02="`Table view`">
                                    <font-awesome-icon v-if="additional_table" icon="fa-solid fa-check"
                                        class="pr-1 text-green-600 font-bold" />
                                    <font-awesome-icon icon="fa-solid fa-bars" />
                                    <!-- <font-awesome-icon v-else icon="fa-solid fa-grip" size="lg" /> -->
                                </div>
                                <div class="px-2 py-1 rounded flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border rounded-r-full border border-gray-500 z-10"
                                    :class="{ 'bg-blue-200': !additional_table }" @click="toggleView"
                                    :title02="`Card view`">
                                    <font-awesome-icon v-if="!additional_table" icon="fa-solid fa-check"
                                        class="pr-1 text-green-600 font-bold" />
                                    <font-awesome-icon icon="fa-solid fa-grip" />
                                </div>
                            </div>
                        </div>

                        <!--grid view-->
                        <div v-if="formValues['additional'] && formValues['additional'].length > 0" class="relative">
                            <div v-if="!additional_table" class="grid gap-2 relative"
                                :class="{ 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3': (formValues['additional'] && formValues['additional'].length > 0) || enable_additional, 'grid-cols-1': !((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                                <div v-if="(formValues['additional'] && formValues['additional'].length > 0) || enable_additional"
                                    v-for="(item, index) in formValues['additional']" :key="index"
                                    class="rounded m-1 bg-yellow-100 px-2 py-1 "
                                    style="box-shadow: 2px 2px 5px 0px rgb(89, 90, 90)">
                                    <div class="flex">
                                        <div :ref="'dropdownContainer' + index" class="relative flex items-center">
                                            <p class="pr-2 w-[90px]">
                                                <font-awesome-icon icon="box" size="sm"
                                                    :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                                <span class="font-bold">Part:</span>
                                            </p>
                                            <div>
                                                <input v-model="item.product_name"
                                                    @input="handleProductChange(item, index)" list="productList"
                                                    class="px-1 py-1 border border-white rounded"
                                                    @focus="handleProductChange(item, index)"
                                                    :ref="'productNameInput' + index"
                                                    @blur="validateProductIsSelected(index)"
                                                    @keydown.enter="handleEnterKey('additional', item, filteredProductList), selectedIndex = index"
                                                    @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                                    @keydown.up.prevent="handleUpArrow(filteredProductList)" />

                                                <div v-if="isDropdownOpenProduct === index"
                                                    class="absolute mt-1 max-h-60 w-[80%] sm:w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                                    style="z-index: 30;">
                                                    <p v-for="(option, index) in filteredProductList" :key="index"
                                                        @click="selectedProductData(item, option)"
                                                        class="cursor-pointer hover:bg-gray-100 p-2 border-b"
                                                        :class="{ 'bg-gray-200': index === selectedIndex }">
                                                        {{ option.products.product_name }}
                                                    </p>
                                                    <!-- Add New product button && item.product_name.length > 1  -->
                                                    <button
                                                        v-if="filteredProductList.length === 0 && !productList.some((opt) => opt.product_name === item.product_name)"
                                                        @click="openAddItem(index)"
                                                        class="w-full text-xs text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                                                        + Add Product
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex justify-end  w-full">
                                            <button @click="removeRow(index)" class="text-red-500 px-2 py-1 rounded"><i
                                                    class="material-icons">delete</i></button>
                                        </div>
                                    </div>
                                    <div class="flex items-center py-1">
                                        <p class="pr-2 w-[90px]">
                                            <font-awesome-icon icon="fa-solid fa-boxes-stacked" size="sm"
                                                :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                            <span class="font-bold">Qty:</span>
                                        </p>
                                        <div>
                                            <button class="mr-1 text-center text-lg text-red-600"
                                                @click="item.qty = item.qty > 0 ? item.qty - 1 : item.qty, calculateTotalAndTax(item.qty, index)">
                                                <font-awesome-icon icon="fa-solid fa-circle-minus" /></button>

                                            <input v-model="item.qty" type="number"
                                                class="text-center border py-1 rounded w-[80px]"
                                                @input="validateQuantity(item.qty, index), calculateTotalAndTax(item.qty, index)"
                                                @click="isDropdownOpenProduct = false"
                                                @focus="isDropdownOpenProduct = false" />
                                            <button class="ml-1 text-center text-lg text-green-600"
                                                @click="validateQuantity(item.qty = item.qty + 1, index), calculateTotalAndTax(item.qty, index)">
                                                <font-awesome-icon icon="fa-solid fa-circle-plus" />
                                            </button>
                                        </div>
                                    </div>
                                    <!--price-->
                                    <div class="flex items-center mb-1">
                                        <p class="w-[90px]">
                                            <font-awesome-icon icon="fa-solid fa-dollar-sign" size="sm"
                                                :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                            <span class="font-bold">Price:</span>
                                        </p>
                                        <div>
                                            <input v-model="item.price" type="number"
                                                @input="calculateTotalAndTax(item.qty, index)"
                                                class="py-1 px-1 border border-white rounded"
                                                @keyup.enter="addRow($event)" />
                                        </div>
                                    </div>
                                    <!--sutotal-->
                                    <div class="flex items-center">
                                        <p class="w-[90px]">
                                            <font-awesome-icon icon="fa-solid fa-dollar-sign" size="sm"
                                                :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                            <span class="font-bold">Subtotal:</span>
                                        </p>
                                        <div>
                                            <input v-model="item.total" type="number" readonly
                                                class="py-1 px-1 border border-white rounded"
                                                @keyup.enter="addRow($event)" />
                                            <span
                                                class="text-xs px-1 py-1 flex-shrink-0 cursor-pointer info-msg font-normal"
                                                :title02="`This field is read-only`"><font-awesome-icon
                                                    icon="fa-solid fa-circle-info" class="px-2 text-blue-700 text-lg" />
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex py-2 mb-1 ">
                                        <p class="pr-2">
                                            <font-awesome-icon icon="fa-solid fa-user" size="sm"
                                                :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                            <span class="font-bold">Customer:</span>
                                        </p>
                                        <div class="flex">
                                            <label class="flex items-center ml-2">
                                                <input v-model="item.status" type="radio" value="approved" class="mr-1">
                                                <span class="text-xs text-green-700">Approved</span>
                                            </label>
                                            <label class="flex items-center mt-1 ml-2">
                                                <input v-model="item.status" type="radio" value="pending" class="mr-1">
                                                <span class="text-xs text-red-700">Pending</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="rounded m-1 flex justify-center items-center" @click="addRow($event)"
                                    :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-2 py-2 border-2 border-blue-500 border-dashed ': ((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                                    <button class="text-blue-700 text-sm text-center px-2 py-1"
                                        :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-8 py-2 border-2 border-blue-500 border-dashed ': !((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                                        + Add Materials
                                    </button>
                                </div>
                            </div>
                            <div v-if="!additional_table"
                                class="grid gap-2 relative grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 m-2">
                                <div class="flex justify-between font-bold text-center items-center bg-gray-200">
                                    <p class="border py-2 w-full">Material Total</p>
                                    <p class="border py-2 w-full">
                                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }}
                                        {{ calculateTotalMaterialCost }}
                                    </p>
                                </div>
                            </div>
                            <div v-if="additional_table" class="overflow-x-auto lg:mx-auto py-2">
                                <table class="w-full mx-auto">
                                    <thead>
                                        <tr class="set-header-background text-white">
                                            <th class="border text-xs sm:text-sm py-1">Product</th>
                                            <th class="border text-xs sm:text-sm">Qty</th>
                                            <th class="border text-xs sm:text-sm">Price</th>
                                            <th class="border text-xs sm:text-sm">Subtotal</th>
                                            <th class="border text-xs sm:text-sm">Client Approval</th>
                                            <th class="border text-xs sm:text-sm">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(item, index) in formValues['additional']" :key="index"
                                            class="text-center py-2">
                                            <td class="border py-1 px-2">
                                                <div :ref="'dropdownContainer' + index">
                                                    <input v-model="item.product_name"
                                                        @input="handleProductChange(item, index)"
                                                        @blur="validateProductIsSelected(index)" list="productList"
                                                        class="text-center py-1 border rounded w-[120px] sm:w-full"
                                                        @focus="handleProductChange(item, index)"
                                                        :ref="'productNameInput' + index"
                                                        @keydown.enter="handleEnterKey('additional', item, filteredProductList), selectedIndex = index"
                                                        @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                                        @keydown.up.prevent="handleUpArrow(filteredProductList)" />

                                                    <div v-if="isDropdownOpenProduct === index"
                                                        class="absolute mt-1 max-h-60 w-full sm:w-[30%] lg:w-[30%] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                                        style="z-index: 100;">
                                                        <p v-for="(option, index) in filteredProductList" :key="index"
                                                            @click="selectedProductData(item, option)"
                                                            class="cursor-pointer hover:bg-gray-100 p-2 border-b"
                                                            :class="{ 'bg-gray-200': index === selectedIndex }">
                                                            {{ option.products.product_name }}
                                                        </p>
                                                        <button
                                                            v-if="filteredProductList.length === 0 && !productList.some((opt) => opt.product_name === item.product_name)"
                                                            @click="openAddItem(index)"
                                                            class="w-full text-xs text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                                                            + Add Product
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>

                                            <td class="border px-1">
                                                <div class="flex justify-center items-center">
                                                    <button class="mr-1 text-center text-lg text-red-600"
                                                        @click="item.qty = item.qty > 0 ? item.qty - 1 : item.qty, calculateTotalAndTax(item.qty, index)">
                                                        <font-awesome-icon icon="fa-solid fa-circle-minus" /></button>

                                                    <input v-model="item.qty" type="number"
                                                        class="text-center border py-1 rounded w-[80px]"
                                                        @input="validateQuantity(item.qty, index), calculateTotalAndTax(item.qty, index)"
                                                        @click="isDropdownOpenProduct = false"
                                                        @focus="isDropdownOpenProduct = false" />
                                                    <button class="ml-1 text-center text-lg text-green-600"
                                                        @click="validateQuantity(item.qty = item.qty + 1, index), calculateTotalAndTax(item.qty, index)">
                                                        <font-awesome-icon icon="fa-solid fa-circle-plus" />
                                                    </button>
                                                </div>
                                            </td>
                                            <td class="border">
                                                <input v-model="item.price" type="number"
                                                    @input="calculateTotalAndTax(item.qty, index)"
                                                    class="text-center py-1 border rounded w-[100px]"
                                                    @keyup.enter="addRow($event)" />
                                            </td>
                                            <td class="border">
                                                <input v-model="item.total" type="number" readonly
                                                    class="text-center py-1 border rounded w-[100px]"
                                                    @keyup.enter="addRow($event)" />
                                                <span
                                                    class="text-xs px-1 py-1 flex-shrink-0 cursor-pointer info-msg font-normal"
                                                    :title02="`This field is read-only`"><font-awesome-icon
                                                        icon="fa-solid fa-circle-info"
                                                        class="px-2 text-blue-700 text-lg" />
                                                </span>
                                            </td>
                                            <td class="border justify-center">
                                                <div class="items-center justify-center">
                                                    <label class="flex items-center ml-2">
                                                        <input v-model="item.status" type="radio" value="approved"
                                                            class="mr-1">
                                                        <span class="text-xs text-green-700">Approved</span>
                                                    </label>
                                                    <label class="flex items-center mt-1 ml-2">
                                                        <input v-model="item.status" type="radio" value="pending"
                                                            class="mr-1">
                                                        <span class="text-xs text-red-700">Pending</span>
                                                    </label>
                                                </div>
                                            </td>
                                            <td class="border">
                                                <button @click="removeRow(index)"
                                                    class="text-red-500 px-2 py-1 rounded"><i
                                                        class="material-icons">delete</i></button>
                                            </td>
                                        </tr>
                                        <tr class="text-center border">
                                            <td class="py-2 px-5 border">
                                                <button @click="addRow($event)"
                                                    class="bg-green-700 hover:bg-green-600 text-white px-2 py-1 rounded">+
                                                    Add
                                                    Row</button>
                                            </td>
                                            <td v-for="mat in 4" :key="mat" class="border"></td>

                                        </tr>
                                        <tr class="font-bold text-center bg-gray-200">
                                            <td colspan="3" class="border py-2">
                                                <span class="flex justify-end px-3">Total</span>
                                            </td>
                                            <td class="border py-2">
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9'
                                                    : currentCompanyList.currency }}
                                                {{ calculateTotalMaterialCost }}
                                            </td>
                                            <td colspan="2" class="border py-2"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div v-else-if="formValues['additional'] && formValues['additional'].length > 0"
                            class="rounded m-1 flex justify-center items-center" @click="addRow($event)"
                            :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-2 py-2 border-2 border-blue-500 border-dashed ': ((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                            <button class="text-blue-300 text-sm text-center px-2 py-1"
                                :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-8 py-2 border-2 border-blue-500 border-dashed ': !((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                                + Add Materials
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!--buttons-->
            <div class="flex justify-center items-center mb-3 mb-[100px] lg:mb-0">
                <button class="border rounded text-white bg-red-700 px-4 py-2 hover:bg-red-600 mr-4"
                    @click="closeModal">Cancel</button>
                <button class="border rounded text-white bg-green-700 px-4 py-2 hover:bg-green-600"
                    @click="saveModel">Save</button>
            </div>

        </div>
        <!--add category brand unit model-->
        <categoryBrandUnit :showModal="open_model" :type="type_model" :categoriesData="list_data"
            @close-modal="closecategoryBrandUnit"></categoryBrandUnit>
        <!--add tax list model-->
        <addTaxList :showModal="open_tax_model" :categoriesData="tax_value_list" @close-modal="closeTaxList"
            :invoice_setting="invoice_setting">
        </addTaxList>
        <!---dialog alert-->
        <dialogAlert :showModal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import categoryBrandUnit from './categoryBrandUnit.vue';
import addTaxList from './addTaxList.vue';
import dialogAlert from './dialogAlert.vue';
import axios from 'axios';
import { mapGetters, mapActions } from 'vuex';
export default {
    components: {
        categoryBrandUnit,
        addTaxList,
        dialogAlert,
    },
    props: {
        showModal: Boolean,
        product_name: String,
        editData: Object,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            print_icon: '/images/service_page/Printer.png',
            del_icon: '/images/service_page/del.png',
            info_icon: '/images/customer_page/info.png',
            isOpen: false,
            category_list: [],
            brand_list: [],
            unit_list: [],
            tax_value_list: [],
            formValues: { product_type: 'Product' },
            units_list: [
                // Your existing units (unchanged)
                'Bags (BAG)',
                'Bale (BAL)',
                'Bundles (BDL)',
                'Buckles (BKL)',
                'Box (BOX)',
                'Bottles (BTL)',
                'Cans (CAN)',
                'Cartons (CTN)',
                'Cubic centimeters (CCM)',
                'Cubic meters (CBM)',
                'Centimeters (CMS)',
                'Drums (DRM)',
                'Dozens (DOZ)',
                'Great gross (GGK)',
                'Gross (GRS)',
                'Kilometre (KME)',
                'Kilograms (KGS)',
                'Kilo litre (KLR)',
                'Metric ton (MTS)',
                'Milli litre (MLT)',
                'Meters (MTR)',
                'Numbers (NOS)',
                'Packs (PAC)',
                'Pieces (PCS)',
                'Pairs (PRS)',
                'Quintal (QTL)',
                'Rolls (ROL)',
                'Square Yards (SQY)',
                'Sets (SET)',
                'Square feet (SQF)',
                'Square meters (SQM)',
                'Tablets (TBS)',
                'Tubes (TUB)',
                'Ten Gross (TGM)',
                'Thousands (THD)',
                'Tonnes (TON)',
                'Units (UNT)',
                'US Gallons (UGS)',
                'Yards (YDS)',

                // New additions in same format
                'Each (EA)',
                'Gram (GRM)',
                'Liter (LTR)',
                'Milligram (MGM)',
                'Ounce (OZ)',
                'Pound (LB)',
                'Gallon (GLN)',
                'Inch (IN)',
                'Foot (FT)',
                'Mile (MI)',
                'Acre (ACR)',
                'Hectare (HEC)',
                'Case (CAS)',
                'Pallet (PAL)',
                'Bundle (BUN)',
                'Container (CNT)',
                'Barrel (BRL)',
                'Sack (SAK)',
                'Crate (CRT)',
                'Jar (JAR)',
                'Vial (VIL)',
                'Canister (CAN)',
                'Packet (PKT)',
                'Envelope (ENV)',
                'Ream (REM)',
                'Spool (SPL)',
                'Bunch (BNC)',
                'Cluster (CLS)',
                'Lot (LOT)',
                'Batch (BCH)',
                'Hour (HUR)',
                'Day (DAY)',
                'Week (WEK)',
                'Month (MTH)',
                'Year (YER)',
                'Service (SRV)',
                'Session (SES)',
                'License (LIC)',
                'Subscription (SUB)'
            ],
            isInputFocused: { product_name: true, category_id: true, brand_id: true, unit: true, purchase_price: true, price: true, sales_price: true, total_qty: true },
            open_message: false,
            message: '',
            //---api integration----
            companyId: null,
            userId: null,
            //---open model data---
            open_model: false,
            type_model: null,
            list_data: null,
            //--open tax --
            open_tax_model: false,
            validation_message: '',
            //---invoice setting---
            invoice_setting: [],
            //--response data---
            response_data: null,
            open_loader: false,
            tooltip_focus: null,
            isMobile: false,
            showSuggestions: false,
            filteredUnits: [],
            selectedIndex: 0,
            isExpanded: false,
            //---profile image---
            open_loader: false,
            circle_loader_photo: false,
            isHovered: false,
            upload_profile: '/images/setting_page/profile.png',
            //--toaster---
            show: false,
            type_toaster: 'warning',
            //---additional materials--
            additional_table: false,
            isDropdownOpenProduct: false,
            //--extra product details
            items: [
                { product_name: '', qty: 0, price: 0 },
            ],
            productList: [],
            filteredProductList: [],
            //---api integration---
            pagination_data: {},

        };
    },
    computed: {
        ...mapGetters('brandUnitCategoryItem', ['currentUnitList']),
        ...mapGetters('brandUnitCategoryItem', ['currentBrandList']),
        ...mapGetters('brandUnitCategoryItem', ['currentCategoryList']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('items', ['currentItems']),

        calculateTotalMaterialCost() {
            if (this.formValues['additional'] && this.formValues['additional']) {
                let sub_total = this.formValues['additional'].reduce((sum, item) => (1 * sum) + (1 * item.total), 0);
                return sub_total || 0;
            }
        },
    },
    methods: {
        ...mapActions('brandUnitCategoryItem', ['fetchUnitList']),
        ...mapActions('brandUnitCategoryItem', ['fetchBrandList']),
        ...mapActions('brandUnitCategoryItem', ['fetchCategoryList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('items', ['fetchItemList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        toggleInfo() {
            this.isExpanded = !this.isExpanded;
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        closeModal(data) {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            this.open_message = false;
            this.message = '';
            setTimeout(() => {
                if (data.id) {
                    this.$emit('close-modal', data);
                    if (this.editData) {
                        this.formValues = { product_type: 'Product' };
                    }
                } else {
                    this.$emit('close-modal');
                    if (this.editData) {
                        this.formValues = { product_type: 'Product' };
                    }
                }
                this.validation_message = '';
            }, 300);

            if (this.type === 'edit') {
                if (this.editData) {
                    this.formValues = { product_type: 'Product' };
                }
            }
        },
        //---get barcode unique--
        currentTimeString() {
            return Date.now().toString();
        },
        saveModel() {
            if (this.formValues.product_type === 'Product' && this.formValues.product_name && this.formValues.product_name !== '' && this.formValues.unit && this.formValues.unit !== '' && this.formValues.sales_price >= 0 && this.formValues.sales_price !== '') {
                this.open_loader = true;
                if (!this.formValues.category_id) {
                    this.formValues.category_id = '';
                }
                if (!this.formValues.barcode || this.formValues.barcode === '' || this.formValues.barcode === null) {
                    this.formValues.barcode = this.currentTimeString();
                }
                if (this.formValues.brand) {
                    this.formValues.brand_id = this.formValues.brand;
                    let findData = this.brand_list.find(opt => opt.id === this.formValues.brand);
                    if (findData) {
                        this.formValues.brand_name = findData.brand_name;
                    }
                }
                if (!this.formValues.product_code) {
                    this.formValues.product_code = '1';
                }
                if (!this.formValues.user_id) {
                    this.formValues.user_id = this.userId;
                }
                if (!this.formValues.hsn_code) {
                    this.formValues.hsn_code = '';
                }
                if (!this.formValues.tax_type) {
                    this.formValues.tax_type = 'Inclusive';
                }
                if (!this.formValues.tax_value) {
                    this.formValues.tax_value = 0;
                }
                if (!this.formValues.total_qty) {
                    this.formValues.total_qty = 0;
                }
                if (!this.formValues.alert_qty) {
                    this.formValues.alert_qty = 0;
                }
                if (!this.formValues.warranty) {
                    this.formValues.warranty = 0;
                }
                if (!this.formValues.image) {
                    this.formValues.image = null;
                }
                // console.log(this.formValues, 'What happning...!');
                if (this.editData) {
                    let get_id = this.formValues.id;
                    delete this.formValues.id;

                    axios.put(`/products_details/${get_id}`, { ...this.formValues, company_id: this.companyId, user_id: this.userId })
                        .then(response => {
                            // console.log('waht about response', response.data);
                            this.updateKeyWithTime('products_update')
                            this.open_loader = false;
                            this.response_data = response.data.data;
                            // this.closeModal(this.response_data);
                            this.message = response.data.message;
                            this.open_message = true;
                            this.formValues = { product_type: 'Product' };
                            this.closeModal(this.response_data);
                            this.fetchItemList(100);
                        })
                        .catch(error => {
                            console.error('Error itm post', error);
                            this.message = error.response.data.message;
                            this.open_message = true;
                            this.open_loader = false;
                        })

                } else {
                    axios.post('/products_details', { ...this.formValues, company_id: this.companyId, user_id: this.userId })
                        .then(response => {
                            // console.log('waht about response', response.data);
                            // this.closeModal(response.data.data);
                            this.updateKeyWithTime('products_update')
                            this.open_loader = false;
                            this.response_data = response.data.data;
                            this.message = response.data.message;
                            this.open_message = true;
                            this.formValues = { product_type: 'Product' };
                            this.closeModal(this.response_data);
                            this.fetchItemList(100);

                        })
                        .catch(error => {
                            console.error('Error itm post', error);
                            this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                            this.open_message = true;
                            this.open_loader = false;
                        })
                }
            } else if (this.formValues.product_type === 'Services' && this.formValues.product_name && this.formValues.sales_price !== '') {
                //---&& this.formValues.category_id && this.formValues.gst_type && this.formValues.product_name !== '' && this.formValues.category_id !== '' && this.formValues.gst_type !== '' && this.formValues.gst_value !== '' && ---
                this.open_loader = true;
                // if (!this.formValues.product_code) {
                //     this.formValues.product_code = '1';
                // }
                if (!this.formValues.barcode || this.formValues.barcode === '' || this.formValues.barcode === null) {
                    this.formValues.barcode = this.currentTimeString();
                }
                if (!this.formValues.user_id) {
                    this.formValues.user_id = this.userId;
                }
                if (!this.editData) {
                    this.formValues.total_qty = 0;
                    this.formValues.purchase_price = 0;
                    this.formValues.alert_qty = 0;
                }
                if (!this.formValues.hsn_code) {
                    this.formValues.hsn_code = '';
                }
                if (!this.formValues.tax_type) {
                    this.formValues.tax_type = 'Inclusive';
                }
                if (!this.formValues.tax_value) {
                    this.formValues.tax_value = 0;
                }
                if (!this.formValues.warranty) {
                    this.formValues.warranty = 0;
                }
                if (!this.formValues.image) {
                    this.formValues.image = null;
                }
                // console.log(this.formValues, 'What happning...!');
                if (this.editData) {
                    const { id, ...updatedFormValues } = this.formValues;
                    axios.put(`/products_details/${id}`, { ...updatedFormValues, company_id: this.companyId, user_id: this.userId })
                        .then(response => {
                            // console.log('What about response for edit service product', response.data);
                            // this.closeModal(response.data.data);
                            this.open_loader = false;
                            this.response_data = response.data.data;
                            this.message = response.data.message;
                            this.open_message = true;
                            this.formValues = { product_type: 'Product' };
                            this.closeModal(this.response_data);
                        })
                        .catch(error => {
                            console.error('Error itm post', error);
                            this.message = error.response.data.message;
                            this.open_message = true;
                            this.open_loader = false;
                        })

                } else {
                    axios.post('/products_details', { ...this.formValues, company_id: this.companyId, user_id: this.userId })
                        .then(response => {
                            // console.log('What about add service product response', response.data);
                            // this.closeModal(response.data.data);
                            this.open_loader = false;
                            this.response_data = response.data.data;
                            this.message = response.data.message;
                            this.open_message = true;
                            this.formValues = { product_type: 'Product' };
                            this.closeModal(this.response_data);

                        })
                        .catch(error => {
                            console.error('Error itm post', error);
                            this.message = error.response.data.message;
                            this.open_message = true;
                            this.open_loader = false;
                        })
                }
            } else {
                // console.log(this.formValues, 'PPPPPPPPP');
                this.validation_message = 'Please fill the data..!'
                // this.message = 'Please fill all required fields..!';
                // this.open_message = true;
            }
        },
        //---category brand unit model---
        openModelCategory(type) {
            if (type === 'category') {
                this.open_model = true;
                this.type_model = type;
                this.list_data = this.category_list;
            } else if (type === 'brand') {
                this.open_model = true;
                this.type_model = type;
                this.list_data = this.brand_list;
            } else {
                this.open_model = true;
                this.type_model = type;
                this.list_data = this.unit_list;
            }
        },
        closecategoryBrandUnit() {
            this.open_model = false;
            this.type_model = null;
            this.list_data = null;
        },
        //---tax list model----
        openTaxModel() {
            this.open_tax_model = true;
        },
        closeTaxList(data) {
            if (data && data.length > 0) {
                // console.log(data, 'Tax list');
                this.tax_value_list = data;
            }
            this.open_tax_model = false;
        },
        //--focus field--
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.product_name;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---handle sales and purchase--
        handleSalesAndPurchse(value) {
            if (value >= 0) {
                this.formValues.purchase_price = value;
                // this.formValues.sales_price = value;
            }
        },
        calculatePurchasePrice(value) {
            if (this.formValues.product_type !== 'Services') {
                if (value === 'Exclusive' && this.formValues.gst_value >= 0) {
                    this.formValues.purchase_price = this.formValues.price + (this.formValues.price * (this.formValues.gst_value / 100));
                } else {
                    this.formValues.purchase_price = this.formValues.price;
                }
            }
        },
        //---close message model--
        closeMessage() {
            this.message = '';
            this.open_message = false;
        },
        //---get initial list data--

        getInitialListData() {
            this.formValues.warranty = 0;
            this.formValues.image = null;
            // if (this.category_list.length === 0) {
            //     axios.get('/categories', { params: { company_id: this.companyId, page: 1, per_page: 'all' } })
            //         .then(response => {
            //             this.category_list = response.data.data;
            //         })
            //         .catch(error => {
            //             console.error('Error category', error);
            //         })
            // }
            if (this.currentCategoryList && this.currentCategoryList.length > 0) {
                this.category_list = [...this.currentCategoryList];
                this.fetchCategoryList();
            } else {
                this.fetchCategoryList();
            }
            // //---brands list---
            // if (this.brand_list.length === 0) {
            //     axios.get('/brands', { params: { company_id: this.companyId, page: 1, per_page: 100 } })
            //         .then(response => {
            //             this.brand_list = response.data.data;
            //         })
            //         .catch(error => {
            //             console.error('Error brand', error);
            //         })
            // }
            if (this.currentBrandList && this.currentBrandList.length > 0) {
                this.brand_list = [...this.currentBrandList];
                this.fetchBrandList();
            } else {
                this.fetchBrandList();
            }
            //---unit list---
            // if (this.unit_list.length === 0) {
            //     axios.get('/units', { params: { company_id: this.companyId, page: 1, per_page: 'all' } })
            //         .then(response => {
            //             this.unit_list = response.data.data;
            //         })
            //         .catch(error => {
            //             console.error('Error units', error);
            //         })
            // }
            if (this.currentUnitList && this.currentUnitList.length > 0) {
                this.unit_list = [...this.currentUnitList];
                this.fetchUnitList();
            } else {
                this.fetchUnitList();
            }
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                this.invoice_setting = [...this.currentInvoice];
                this.tax_value_list = JSON.parse(this.invoice_setting[0].selected_tax);
                this.fetchInvoiceSetting();
            } else {
                // this.tooltip_focus = "general";
                this.fetchInvoiceSetting();
            }
            //---tax list---
            // if (this.tax_value_list.length === 0) {
            //     axios.get('/invoice_settings', { params: { company_id: this.companyId } })
            //         .then(response => {
            //             this.invoice_setting = response.data.data;
            //             if (response.data.data.length > 0) {
            //                 this.tax_value_list = JSON.parse(response.data.data[0].selected_tax);
            //             } else {
            //                 this.tooltip_focus = "general";
            //             }
            //         })
            //         .catch(error => {
            //             console.error('Error taxes', error);
            //         })
            // }
        },
        getExistData() {
            if (this.editData && Object.keys(this.editData).length > 0) {
                let { id, total_qty, product_id, barcode_id, purchase_price, gst_type, gst_value, tax_name, alert_qty, discount_type, discount, sales_price, products, barcodes, dealer_price: dealer_price } = this.editData;
                let price = purchase_price;
                if (gst_type === 'Exclusive' && gst_value > 0) {
                    price = purchase_price * (100 / (gst_value + 100));
                }
                let createObj = { id: id, product_type: products.product_type, total_qty: total_qty, purchase_price: purchase_price, gst_type: gst_type, gst_value: gst_value, tax_name: tax_name, selected_tax: { tax_name: tax_name ? tax_name : 'GST', value: gst_value }, alert_qty: alert_qty, discount_type: discount_type, discount: discount, sales_price: sales_price, product_name: products.product_name, brand: products.brand_id, hsn_code: products.hsn_code, unit: products.unit, category_id: products.category_id, product_code: products.product_code, barcode: barcodes.barcode, price: price, product_id: product_id, barcode_id: barcode_id, dealer_price: dealer_price, image: products.image ? products.image : null, warranty: products.warranty ? products.warranty : 0 };
                this.formValues = createObj;
                // console.log(this.formValues, 'GDGDGDGDG ');
            }
        },

        //----filter the unit list----
        //----dropdown---
        filterUnits() {
            let mergeList = [];
            if (this.unit_list.length > 0) {
                mergeList = [...this.units_list, ...(this.unit_list.map(item => item.unit_name))];
            }
            else {
                mergeList = [...this.units_list];
            }
            if (this.formValues.unit) {
                const value = this.formValues.unit.toLowerCase();
                if (Array.isArray(mergeList)) {
                    this.filteredUnits = mergeList.filter(item => {
                        if (typeof item === 'string') {
                            const strippedItem = item.replace(/\([^)]*\)/g, '').toLowerCase();
                            return strippedItem.includes(value);
                        }
                        return false;
                    });
                }
                if (this.filteredUnits && this.filteredUnits.length > 0) {
                    this.showSuggestions = true;
                }
            } else {
                this.filteredUnits = mergeList;
                if (this.filteredUnits && this.filteredUnits.length > 0) {
                    this.showSuggestions = true;
                }
            }
            document.addEventListener('click', this.handleDocumentClick);
        },

        filterCustomers() {
            if (this.formValues.unit !== '' && this.formValues.unit.length > 3) {
                // console.log(this.formValues.unit, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            }
        },
        showDropdown() {
            if (this.formValues.unit !== '') {
                this.showSuggestions = true;
                document.addEventListener('click', this.handleDocumentClick);
            }
        },
        hideDropdown() {
            this.showSuggestions = false;

            // Remove the click event listener when hiding the dropdown
            document.removeEventListener('click', this.handleDocumentClick);
        },
        selectCustomer(unit) {
            this.formValues.unit = unit
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            document.removeEventListener('click', this.handleDocumentClick); // Remove the event listener after selection
        },
        handleDocumentClick(event) {
            // Close the dropdown when clicking outside the input and dropdown
            const isClickInside = this.$refs.unit.contains(event.target) || this.$refs.unit.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.hideDropdown();
            }
        },
        handleEnterKey() {
            // Check if filteredProductList has at least one item
            if (this.filteredUnits.length > 0) {
                // Call selectedProductData with the first item in filteredProductList
                this.selectCustomer(this.filteredUnits[this.selectedIndex]);
                this.selectedIndex = 0;
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----customer dropdown--
        closeDropdown(event) {
            console.log(this.$refs.unitContainer.contains(event.relatedTarget));
            if (this.$refs.unitContainer.contains(event.relatedTarget)) {
                return;
            } else {
                this.showSuggestions = false;
            }
        },
        //--upload image--
        //----Profile image--
        openFileInput() {
            // Trigger a click on the hidden file input

            this.$refs.fileInput.click();
        },
        async handleImageChangeProfile(event) {
            const file = event.target.files[0];
            // this.circle_loader_photo = true;
            // if (file) {
            //     this.uploadImageProfile(file);
            // }
            if (!file) return;

            // Check file size (in bytes)
            const maxSizeBytes = 500 * 1024; // 500kb in bytes
            if (file.size > maxSizeBytes) {
                // Image exceeds 500kb, compress it
                try {
                    this.circle_loader_photo = true; // Show loader
                    const compressedFile = await this.compressImage(file);

                    this.uploadImageProfile(compressedFile);
                } catch (error) {
                    console.error("Error compressing image:", error);
                    this.circle_loader_photo = false; // Hide loader on error
                }
            } else {
                // Image is <= 500kb, upload directly
                try {
                    this.circle_loader_photo = true;

                    this.uploadImageProfile(file);
                } catch (error) {
                    console.error("Error uploading image:", error);
                    this.circle_loader_photo = false; // Hide loader on error
                }
            }
        },
        uploadImageProfile(file) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "Product");
            formData.append("company_id", this.companyID);
            // Make an API request to Laravel backend
            // console.log(file, 'RRRR');
            axios.post('/image', formData)
                .then(response => {
                    // console.log(response.data, 'What happrning....Q');
                    this.circle_loader_photo = false;
                    //  let data_save = { image_path: response.data.image_path };
                    if (this.formValues.image && this.formValues.image !== '') {
                        this.removeExistAvatar(this.formValues.image);
                    }
                    this.formValues.image = response.data.media_url;
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                });
        },
        removeExistAvatar(url) {
            axios.delete('/delete-image', { params: { model: "Product", image_url: url } })
                .then(response => {
                    console.log(response.data, 'delete product image..!');
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        //---validate maximum of discount
        validateMax() {
            const { discount_type, sales_price, discount } = this.formValues;

            // Check if both discount_type and sales_price exist
            if (discount_type && sales_price) {
                if (discount_type === 'Fixed' && discount > sales_price) {
                    // For 'Fixed' discount type, max is the sales price
                    this.message = 'The discount exceeds the sales price.';
                    this.show = true;
                    this.formValues.discount = sales_price;
                } else if (discount_type === 'Percentage' && discount > 100) {
                    // For 'Fixed' discount type, max is the sales price
                    this.message = 'The discount exceeds the sales price.';
                    this.show = true;
                    this.formValues.discount = 100;
                }
            } else {
                this.message = 'Please enter the sales price.';
                this.show = true;
            }
        },
        // Method to calculate sales price based on profit margin
        calculateSalesPrice() {
            if (this.formValues.purchase_price && this.formValues.profitmargin !== undefined) {
                const purchasePrice = this.formValues.purchase_price;
                const profitMargin = this.formValues.profitmargin;
                // Calculate Sales Price
                const salesPrice = purchasePrice + (purchasePrice * profitMargin / 100);
                this.formValues.sales_price = salesPrice.toFixed(2); // Set the calculated sales price
            }
        },

        // Method to calculate profit margin based on sales price
        calculateProfitMargin() {
            if (this.formValues.sales_price && this.formValues.purchase_price) {
                const salesPrice = this.formValues.sales_price;
                const purchasePrice = this.formValues.purchase_price;
                // Calculate Profit Margin as a percentage
                const profitMargin = ((salesPrice - purchasePrice) / purchasePrice) * 100;
                this.formValues.profitmargin = profitMargin.toFixed(2); // Set the calculated profit margin
            }
        },
        //---update tax selection--
        updateTaxName(selectedTax) {
            if (selectedTax) {
                this.formValues.gst_value = selectedTax.value;
                this.formValues.tax_name = selectedTax.tax_name;
                this.calculatePurchasePrice(this.formValues.gst_type);
            }
        },
        //--additional materials--
        //---toggle view---
        toggleView() {
            if (this.additional_table) {
                this.additional_table = false;
            } else {
                this.additional_table = true;
            }
        },
        //---extra items
        addRow(event) {
            event.stopPropagation();
            if (this.formValues['additional'] && this.formValues['additional'].length > 0) {
                if (this.formValues['additional'][this.formValues['additional'].length - 1]['product_id']) {
                    this.formValues['additional'].push({ product_name: '', qty: 0, price: 0, status: 'pending' });
                } else {
                    this.message = 'Please select the product from the already created row or card.';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            } else {
                this.formValues['additional'] = [{ product_name: '', qty: 0, price: 0, status: 'pending' }];
            }
            this.$nextTick(() => {
                // console.log(this.$refs['productNameInput' + (this.formValues['additional'].length - 1)], 'WWWREWRWR');
                if (this.$refs['productNameInput' + (this.formValues['additional'].length - 1)]) {
                    this.$refs['productNameInput' + (this.formValues['additional'].length - 1)][0].focus();
                    this.$refs['productNameInput' + (this.formValues['additional'].length - 1)][0].click();
                }
            });

        },
        //----selected add product---
        validateProductIsSelected(index) {
            if (index >= 0) {
                setTimeout(() => {
                    if (this.deleteIndex != index && this.isDeleteAdditional == false && this.open_confirmBox == false) {
                        if (!(this.formValues['additional'][index] && this.formValues['additional'][index]['product_id']) && !this.showModalProduct && this.formValues['additional'].length > index) {
                            // console.log(this.formValues['additional'][index], 'WWWWWWWWW happening the index data...');
                            // this.$nextTick(() => {
                            this.$refs[`productNameInput${index}`][0].focus();
                            // });
                            this.message = 'Please select the exist in Itms list or make ADD NEW +';
                            this.type_toaster = 'warning';
                            this.show = true;
                        }
                    }
                }, 100);
            }
        },
        handleProductChange(item, index) {
            const enteredProduct = item.product_name.trim();
            this.isDropdownOpenProduct = index;
            this.filteredProductList = this.productList.filter(opt => opt.products.product_name && opt.products.product_type === 'Product' &&
                opt.products.product_name.toLowerCase().includes(enteredProduct.toLowerCase()) &&
                !this.formValues['additional'].some(item => item.product_name === opt.products.product_name)
            );
            this.selectedIndex = 0;
            // console.log(this.filteredProductList.length === 0, 'TTT', this.pagination_data.product, 'tttt', this.pagination_data.product.current_page, 'RRRr', this.pagination_data.product.last_page, 'RRRRR what happening.....');
            if (this.filteredProductList.length === 0 && this.pagination_data.product && (1 * this.pagination_data.product.current_page) !== this.pagination_data.product.last_page) {
                this.getProductList((this.pagination_data.product.current_page * 1) + 1, 100);

            }
            // console.log(this.productList);
        },
        selectedProductData(item, option) {
            // console.log(option, 'what happening...1');
            item.company_id = this.companyId;
            item.product_name = option.products.product_name;
            item.product_type = option.products.product_type;
            item.price = option.sales_price;
            item.hsn_code = option.products.hsn_code;
            item.taxvalue = option.gst_value;
            item.tax_name ? item.tax_name : 'GST'
            item.tax_type = option.gst_type;
            item.qty = 1;
            item.tax = option.sales_price * (option.gst_value / 100);
            item.total = 1 * option.sales_price;
            item.product_id = option.product_id;
            item.barcode_id = option.barcode_id;
            item.discount_data = { type: option.discount_type ? option.discount_type : 'Fixed', value: option.discount ? option.discount : 0 },
                item.discount = option.discount_type === 'Percentage' ? option.sales_price * (option.discount / 100) : option.discount ? option.discount : 0,
                this.isDropdownOpenProduct = false;
            this.filteredProductList = this.productList;
            this.selectedIndex = 0;
            // console.log(item, 'What about item...!');
        },
        //--product stock validate
        validateQuantity(value, index) {
            // console.log(value, 'EEEEEEE');
            let product_id = this.formValues['additional'][index].product_id;
            const selectedProductIndex = this.productList.findIndex(product => product.product_id === product_id);
            // console.log(selectedProductIndex, 'TTETETE');
            if (selectedProductIndex !== -1) {
                const selectedProduct = this.productList[selectedProductIndex];
                console.log(this.currentInvoice[0].minus_sale, 'Sales', value);
                ;
                if (value > 0 && (value <= selectedProduct.total_qty || this.currentInvoice[0].minus_sale === '1')) {

                    // Trigger Vue reactivity
                    this.productList[selectedProductIndex] = Object.assign({}, selectedProduct);
                } else if (value > 0) {
                    this.message = 'Product stock is empty..!';
                    this.type_toaster = 'warning';
                    this.show = true;
                    this.minus_sales_open = true;
                }
            }
        },

        removeRow(index) {
            // Stop event propagation to avoid triggering any parent handlers
            event.stopPropagation();
            // this.formValues['additional'].splice(index, 1);
            if (index >= 0 && this.formValues['additional'].length > index && ((this.formValues['additional'][index].product_id && this.formValues['additional'][index].product_id !== '') || this.formValues['additional'][index].qty > 0 || this.formValues['additional'][index].price > 0)) {
                this.deleteIndex = index;
                this.isDeleteAdditional = true;
                this.open_confirmBox = true;
            } else if (index >= 0 && this.formValues['additional'].length > index) {
                this.formValues['additional'].splice(index, 1);
            }
        },
        //--get product list ----
        getProductList(page, per_page) {
            axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data.data);
                    this.productList = response.data.data;
                    this.pagination_data.product = response.data.pagination;
                    this.fetchItemList(this.pagination_data.product.total);
                })
                .catch(error => {
                    console.error('Error response', error);
                })
        },

    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        //---pict default pices--
        if (!this.editData) {
            this.formValues.unit = 'Pices';
        }
        this.updateIsMobile(); // Initial check  

        // Add a resize event listener to dynamically update isMobile
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        // Remove the resize event listener when the component is destroyed
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                setTimeout(() => {
                    if (newValue) {
                        this.getInitialListData();
                        //---Object.keys(this.formValues).length <= 1
                        // console.log(this.editData && Object.keys(this.editData).length > 0, 'Waht happpppppp rrrr');
                        if (this.editData && Object.keys(this.editData).length > 0) {
                            this.getExistData();
                        }
                        this.handleFocus();
                    }
                }, 100)
            }, 100);
        },
        product_name: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'EEEEEEEEEE');
                this.formValues.product_name = newValue;
            }
        },
        editData: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'EEEEEEEEEE');
                if (newValue) {
                    // Check if editData is populated
                    if (Object.keys(newValue).length > 0) {
                        let { id, total_qty, product_id, barcode_id, purchase_price, gst_type, gst_value, tax_name, alert_qty, discount_type, discount, sales_price, products, barcodes, dealer_price: dealer_price } = newValue;
                        let price = purchase_price;
                        if (gst_type === 'Exclusive' && gst_value > 0) {
                            price = purchase_price * (100 / (gst_value + 100));
                        }
                        let createObj = { id: id, product_type: products.product_type, total_qty: total_qty, purchase_price: purchase_price, gst_type: gst_type, gst_value: gst_value, tax_name: tax_name, selected_tax: { tax_name: tax_name ? tax_name : 'GST', value: gst_value }, alert_qty: alert_qty, discount_type: discount_type, discount: discount, sales_price: sales_price, product_name: products.product_name, brand: products.brand_id, hsn_code: products.hsn_code, unit: products.unit, category_id: products.category_id, product_code: products.product_code, barcode: barcodes.barcode, price: price, product_id: product_id, barcode_id: barcode_id, dealer_price: dealer_price, image: products.image ? products.image : null, warranty: products.warranty ? products.warranty : 0 };
                        this.formValues = createObj;
                    }
                }
            }
        },
        currentCategoryList: {
            deep: true,
            handler(newValue) {
                this.category_list = [...newValue];
            }
        },
        currentBrandList: {
            deep: true,
            handler(newValue) {
                this.brand_list = [...newValue];
            }
        },
        currentUnitList: {
            deep: true,
            handler(newValue) {
                this.unit_list = [...newValue];
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.invoice_setting = [...newValue];
                    this.tax_value_list = JSON.parse(this.invoice_setting[0].selected_tax);
                }
            }
        },
    },

};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>