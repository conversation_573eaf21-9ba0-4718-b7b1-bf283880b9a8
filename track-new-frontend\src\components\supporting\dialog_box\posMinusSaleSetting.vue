<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    Setting Minus Sales
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block text-sm bg p-4">
                <div class="bg-gray-100 px-3 py-5 rounded mb-5 shadow-md"
                    style="box-shadow: 3px 4px 6px 3px rgba(150, 63, 59, 0.3)">
                    <!--Enable Minus Sale-->
                    <div class="mb-4">
                        <label class="block text-md font-bold">Minus Sale Setting</label>
                        <div class="flex items-center mt-2">
                            <div class="flex items-center">
                                <input type="radio" id="minus_sale_false" name="minus_sale" value="0"
                                    v-model="formValues.minus_sale" @change="is_updated = true">
                                <label for="minus_sale_false">Disable</label>
                            </div>
                            <div class="ml-5 flex items-center">
                                <input type="radio" id="minus_sale_true" name="minus_sale" value="1"
                                    v-model="formValues.minus_sale" @change="is_updated = true">
                                <label for="minus_sale_true">Enable</label>
                            </div>

                        </div>
                    </div>
                </div>
                <p v-if="validation_message !== ''" class="text-red-500 font-bold text-sm py-1">{{ validation_message }}
                </p>

                <!--buttons-->
                <div class="flex justify-center items-center">
                    <button class="border rounded text-white bg-red-700  px-4 py-2 hover:bg-red-600 mr-4"
                        @click="closeModal">Cancel</button>
                    <button class="border rounded text-white bg-green-700  px-4 py-2 hover:bg-green-600"
                        @click="saveModel">Save</button>
                </div>
            </div>
        </div>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';

export default {
    props: {
        showModal: Boolean,
        invoice_setting: Object,
        companyId: String
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            isInputFocused: {},
            validation_message: '',
            is_updated: false,
            open_loader: false,
        };
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
                this.validation_message = '';
            }, 300);
        },
        saveModel() {
            if (this.formValues.minus_sale >= 0) {
                this.open_loader = true;
                if (Object.keys(this.invoice_setting).length > 0) {
                    axios.put(`/invoice_settings/${this.invoice_setting[0].id}`, { ...this.formValues, company_id: this.companyId })
                        .then(response => {
                            // console.log(response.data, 'Response update');
                            this.open_loader = false;
                            this.fetchInvoiceSetting();
                            setTimeout(() => {
                                this.$emit('close-modal', response.data.data);
                                this.validation_message = '';
                            }, 300);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                        })
                }
            } else {
                this.validation_message = this.validation_message === '' ? 'Please select any one options..!' : this.validation_message;
            }
        },

    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (this.invoice_setting && Array.isArray(this.invoice_setting) && this.invoice_setting.length > 0) {
                    this.formValues.minus_sale = this.invoice_setting[0].minus_sale;
                }
            }, 100);
        },
        formValues: {
            deep: true,
            handler(newValue) {
                this.validation_message = '';
            }
        },

    },
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>