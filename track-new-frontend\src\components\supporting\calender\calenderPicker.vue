<!-- CalendarPicker.vue -->
<template>
    <div class="absolute bg-white border shadow-lg rounded-lg p-4" v-if="isOpen" @click.away="closeCalendar">
        <!-- Header for Month and Year Navigation -->
        <div class="flex justify-between items-center mb-4 w-full">
            <button @click="prevYear" class="text-gray-500">&laquo;</button>
            <button @click="prevMonth" class="text-gray-500">&lsaquo;</button>
            <span class="font-semibold flex items-center space-x-1">
                <select v-model="currentMonth" class="outline-none">
                    <option v-for="(month, index) in months" :key="index" :value="index">{{ month }}</option>
                </select>
                <select v-model="currentYear" class="outline-none">
                    <option v-for="year in years" :key="year" :value="year">{{ year }}</option>
                </select>
            </span>
            <button @click="nextMonth" class="text-gray-500">&rsaquo;</button>
            <button @click="nextYear" class="text-gray-500">&raquo;</button>
        </div>

        <!-- Days of the Week -->
        <div class="grid grid-cols-7 gap-2 text-center mb-2">
            <span v-for="(day, index) in daysOfWeek" :key="index" class="font-semibold text-gray-600">{{ day }}</span>
        </div>

        <!-- Days of the Month -->
        <div class="grid grid-cols-7 gap-2 text-center">
            <div v-for="blank in startDay" :key="'b' + blank" class="p-2"></div>
            <button v-for="day in daysInMonth" :key="day" @click="selectDate(day)" class="p-2 rounded-md" :class="{
                'bg-blue-500 text-white': isSelected(day),
                'text-gray-600 hover:bg-blue-100': !isSelected(day)
            }">
                {{ day }}
            </button>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        modelValue: String
    },
    data() {
        return {
            isOpen: true,
            currentDate: new Date(),
            currentYear: new Date().getFullYear(),
            currentMonth: new Date().getMonth(),
            selectedDay: null,
            months: [
                "January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"
            ],
            daysOfWeek: ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"]
        };
    },
    computed: {
        daysInMonth() {
            return new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
        },
        startDay() {
            return new Date(this.currentYear, this.currentMonth, 1).getDay();
        },
        years() {
            const startYear = this.currentYear - 10;
            const endYear = this.currentYear + 10;
            return Array.from({ length: endYear - startYear + 1 }, (_, i) => startYear + i);
        }
    },
    methods: {
        prevMonth() {
            if (this.currentMonth === 0) {
                this.currentMonth = 11;
                this.currentYear--;
            } else {
                this.currentMonth--;
            }
        },
        nextMonth() {
            if (this.currentMonth === 11) {
                this.currentMonth = 0;
                this.currentYear++;
            } else {
                this.currentMonth++;
            }
        },
        prevYear() {
            this.currentYear--;
        },
        nextYear() {
            this.currentYear++;
        },
        selectDate(day) {
            const selectedDate = new Date(this.currentYear, this.currentMonth, day);
            const formattedDate = selectedDate.toISOString().split('T')[0].split('-').reverse().join('-');
            this.$emit('update:modelValue', formattedDate);
            this.selectedDay = day;
            this.closeCalendar();
        },
        isSelected(day) {
            const today = new Date(this.currentYear, this.currentMonth, day);
            const formattedToday = today.toISOString().split('T')[0].split('-').reverse().join('-');
            return formattedToday === this.modelValue;
        },
        closeCalendar() {
            this.isOpen = false;
        }
    }
};
</script>

<style scoped>
/* Customize your styles if needed */
</style>
