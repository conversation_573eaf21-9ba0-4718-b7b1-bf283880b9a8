<template>
    <div class="flex h-screen">
        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mt-[60px]': isMobile, 'mt-[57px]': !isMobile }">
            <div>
                <bannerDesign></bannerDesign>
            </div>
            <!-- services home -->
            <div class="p-1 relative m-1">
                <reportService :isMobile="isMobile" :isAndroid="isAndroid" :refresh="refresh"></reportService>

            </div>
        </div>
    </div>
</template>

<script>
import reportService from '../supporting/reports/services/reportService.vue';
import { useMeta } from '@/composables/useMeta';
import bannerDesign from '../supporting/banner/bannerDesign.vue';

export default {
    name: 'reports',
    components: {
        reportService,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isAndroid: false,
            isSidebarOpen: false,
            route_item: 8,
            refresh: false,
        };
    },
    setup() {
        const pageTitle = `Reports`;
        const pageDescription = 'View detailed reports across all business areas. Simplify analysis and track performance metrics in one place';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    methods: {
        toggleSidebar() {
            this.isSidebarOpen = !(this.isSidebarOpen);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
            this.isAndroid = window.innerWidth < 768;
        },
        refreshData() {
            this.refresh = !this.refresh;
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>