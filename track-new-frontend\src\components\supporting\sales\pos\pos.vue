<template>
    <div class="text-xs sm:text-sm sm:m-2" :class="{ 'sm:mt-[55px] mt-[50px] mb-[50px]': isMobile }">
        <div>
            <!--first row-->
            <!---sm:flex sm:justify-between flex flex-wrap-->
            <div class="flex justify-between space-x-2 items-center">
                <!--invoice type-->
                <div class="flex justify-between space-x-2 items-center">
                    <div class="flex border" :class="{ 'mb-1': isMobile }" @mouseover="tooltip.invoice_type = true"
                        @mouseleave="tooltip.invoice_type = false">
                        <label class="border py-2 px-2"
                            :class="{ 'bg-blue-700 text-white': selectedOption_invoice_type === 'b2c' }">
                            <input type="radio" v-model="selectedOption_invoice_type" value="b2c" class="hidden">
                            B2C
                        </label>
                        <label class="border py-2 px-2 border-l-0"
                            :class="{ 'bg-blue-700 text-white': selectedOption_invoice_type === 'b2b' }">
                            <input type="radio" v-model="selectedOption_invoice_type" value="b2b" class="hidden">
                            B2B
                        </label>

                        <!---tooltip-->
                        <div v-if="tooltip.invoice_type"
                            class="absolute left-[65px] flex items-center ml-6 group-hover:flex">
                            <div class="w-3 h-3 -mr-2 rotate-45 bg-black"></div>
                            <span
                                class="relative z-10 p-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">invoice
                                type</span>
                        </div>
                    </div>
                    <!--TAX Enable/Disable-->
                    <!-- <div class="flex justify-center items-center" :class="{ 'mb-3': isMobile }"
                        @mouseover="tooltip.is_tax = true" @mouseleave="tooltip.is_tax = false">
                        <label v-if="!isMobile" class="pr-1">Tax:</label>
                        <label class="inline-flex items-center cursor-pointer">
                            <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                                @click="toggleSwitch('is_tax')"
                                :class="{ 'bg-blue-600': is_tax, 'bg-gray-200': !is_tax }">
                                <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                    :class="{ 'translate-x-6': is_tax, 'translate-x-0': !is_tax }">
                                </div>
                            </div>
                            <span v-if="!isMobile" class="pl-1"
                                :class="{ 'text-green-600': is_tax, 'text-red-600': !is_tax }">{{
                                    is_tax ? 'ON' : 'OFF' }}</span>
                        </label> -->
                    <!---tooltip-->
                    <!-- <div v-if="tooltip.is_tax" class="absolute flex flex-col items-center group-hover:flex mt-14
                             ml-5">
                            <div class="w-3 h-3 -mb-2 rotate-45 bg-black"></div>
                            <span
                                class="relative z-10 p-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">Tax
                                Enable/Disable</span>
                        </div>
                    </div> -->
                </div>
                <!--Group by invoice-->
                <!-- <div v-if="invoice_setting && invoice_setting.length > 0 && typeOfInvoice !== 'estimation' && typeOfInvoice !== 'proforma'"
                    class="flex justify-center items-center" :class="{ 'mb-3 ml-5': isMobile }">
                    <label v-for="(opt, index) in JSON.parse(invoice_setting[0].invoice_prefix)" :key="index"
                        :for="opt.name"
                        :class="{ 'bg-gray-200 text-gray-600': formValues.invoice_group !== opt.value, 'bg-gray-600 text-white': formValues.invoice_group === opt.value}"
                        class="px-4 mr-2 py-2 cursor-pointer inline-block rounded-md transition-colors duration-300 hover:bg-gray-400 hover:text-white">
                        {{ opt.name }}
                        <input type="radio" :id="opt.name" :value="opt.value" v-model="formValues.invoice_group"
                            :class="{ 'hidden': formValues.invoice_group !== opt.value }" />
                    </label> -->
                <!-- <label for="Services"
                    :class="{ 'bg-gray-200 text-gray-600': formValues.product_type !== 'Services', 'bg-gray-600 text-white': formValues.product_type === 'Services' }"
                    class="ml-2 px-4 py-2 cursor-pointer inline-block rounded-md transition-colors duration-300 hover:bg-gray-400 hover:text-white">
                    Services
                    <input type="radio" :id="'Services'" value="Services" v-model="formValues.product_type"
                        :class="{ 'hidden': formValues.product_type !== 'Services' }" />
                </label> -->
                <!-- </div> -->
                <!---initial code-->
                <div class="flex">
                    <div class="flex sm:mr-2 lg:mr-5" :class="{ 'mb-1': isMobile }"
                        @mouseover="tooltip.invoice_initialcode = true"
                        @mouseleave="tooltip.invoice_initialcode = false">
                        <div class="border border-r-0 py-2 px-2">
                            <font-awesome-icon icon="fa-solid fa-bars" size="lg" />
                        </div>
                        <input type="text" placeholder="initial code" v-model="initital_code"
                            @focus="isFormFocus.initital_code = true" @blur="isFormFocus.initital_code = false"
                            class="py-2 px-2 border rounded rounded-bl-none rounded-tl-none outline-none"
                            :class="{ 'w-full': isMobile, 'border-blue-600': isFormFocus.initital_code }" readonly />
                        <!---tooltip-->
                        <div v-if="tooltip.invoice_initialcode" class="absolute flex flex-col items-center group-hover:flex mt-10
                             ml-12">
                            <div class="w-3 h-3 -mb-2 rotate-45 bg-black"></div>
                            <span
                                class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                <p>Invoice initial code</p>
                            </span>
                        </div>
                    </div>
                    <!---invoice or estimation num-->
                    <div @mouseover="tooltip.invoice_id = true" @mouseleave="tooltip.invoice_id = false">
                        <input type="number" placeholder="number" v-model="invoice_num"
                            @focus="isFormFocus.invoice_num = true" @blur="isFormFocus.invoice_num = false"
                            class="border py-2 px-2 rounded outline-none"
                            :class="{ 'w-full': isMobile, 'border-blue-600': isFormFocus.invoice_num }" readonly />
                        <!---tooltip-->
                        <div v-if="tooltip.invoice_id"
                            class="absolute flex flex-col items-center group-hover:flex ml-5">
                            <div class="w-3 h-3 -mb-2 rotate-45 bg-black"></div>
                            <span
                                class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                <p>Invoice Count ID</p>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
            <!--same as shipping address absolute top-[12%] sm:top-[10%] lg:top-[8%] 2xl:top-[7%]-->
            <div v-if="customer_data && Object.keys(customer_data).length > 0"
                class="grid grid-cols-1 sm:grid-cols-2 gap-2 text-xs">
                <!-- Checkbox to toggle same billing & shipping address -->
                <div class="flex justify-start items-center text-xs ">
                    <div class="flex  items-center">
                        <input type="checkbox" id="sameAddress" v-model="sameAsBilling" class="mr-2 cursor-pointer" />
                        <label for="sameAddress" class="text-gray-700 font-medium cursor-pointer">
                            Same as Billing & Shipping Address
                        </label>
                    </div>
                    <div class="mx-2">
                        <button @click="updateshippingDetails"
                            class="bg-blue-600 text-white rounded-full px-2 sm:py-1 hover:bg-blue-500">update</button>
                    </div>
                </div>
            </div>
            <!--second row :class="{ 'mt-7': !isMobile, 'mt-4': isMobile }"-->
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4"
                :class="{ 'sm:py-1 py-0': !(customer_data && Object.keys(customer_data).length > 0) }">
                <!---customer-->
                <div class="flex" @mouseover="tooltip.customer = true" @mouseleave="tooltip.customer = false"
                    :class="{ 'mt-0': isMobile && !isAndroid }">
                    <div class="border border-r-0 py-2 px-2">
                        <font-awesome-icon icon="fa-solid fa-user" size="lg" />
                    </div>
                    <div class="flex border justify-between py-2 px-2 w-full bg-white" @click="toggleDropdownOpen"
                        ref="dropdownTrigger">
                        <p
                            :class="{ 'text-black': Object.keys(customer_data).length > 0, 'text-gray-400': !Object.keys(customer_data).length > 0 }">
                            {{ Object.keys(customer_data).length > 0 ? customer_data.last_name ?
                                customer_data.first_name
                                + ' ' + customer_data.last_name + ' - ' + customer_data.contact_number :
                                customer_data.first_name + ' - ' + customer_data.contact_number : 'Search Name / Mobile' }}
                        </p>
                        <div class="flex">
                            <span v-if="Object.keys(customer_data).length > 0" class="mr-3 text-gray-500 cursor-pointer"
                                @click="resetCustomer">x</span>
                            <span class="cursor-pointer">
                                <!-- {{ !isDropdownOpen_customer ? '&#11206;' : '&#11205;' }} -->
                                <font-awesome-icon v-if="isDropdownOpen_customer" icon="fa-solid fa-angle-up" />
                                <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                            </span>
                        </div>
                    </div>
                    <!--add new customer-->
                    <div class="border border-l-0 py-2 px-2 cursor-pointer" @click="openModal">
                        <font-awesome-icon icon="fa-solid fa-user-plus" size="lg" style="color: blue" />
                    </div>
                    <!---customer dropdown-->
                    <div v-if="isDropdownOpen_customer" @mouseover="mouseOutFunction(true)"
                        @mouseleave="mouseOutFunction(false)"
                        :style="{ width: $refs.dropdownTrigger.offsetWidth + 'px' }"
                        class="absolute mt-10 ml-8 sm:ml-8 lg:ml-9 w-1/4 px-2 py-2 max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm z-50">
                        <div class="w-full">
                            <input placeholder="" v-model="selectCustomer" @input="handleDropdownInput(selectCustomer)"
                                @keydown.enter="handleEnterKey(null, 'customer')" @blur="customerBlur"
                                ref="selectCustomerInput" @focus="isFormFocus.customer = true"
                                class="w-full border py-2 pl-3 pr-10 leading-5 text-gray-900 focus:ring-0 rounded outline-none"
                                :class="{ 'border-blue-600': isFormFocus.customer }"
                                @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                                @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" />
                        </div>
                        <!-- Display filtered options as the user types -->
                        <p v-if="selectCustomer && selectCustomer.length > 0"
                            v-for="(option, index) in filteredCustomerOptions" :key="index"
                            @click="selectDropdownOption(option)"
                            class="cursor-pointer hover:bg-gray-100 p-2 mt-2 border-b"
                            :class="{ 'bg-gray-200': index === selectedIndex }">
                            {{ option.last_name ? option.first_name + ' ' + option.last_name + ' - ' +
                                option.contact_number
                                :
                                option.first_name + ' - ' + option.contact_number }}
                        </p>
                        <!-- Add New Customer button -->
                        <button
                            v-if="filteredCustomerOptions.length === 0 && selectCustomer && selectCustomer.length > 0"
                            @click="openModal(selectCustomer)"
                            class="w-full text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                            + Add Customer
                        </button>
                    </div>
                    <!---tooltip-->
                    <div v-if="tooltip.customer"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8 ml-[100px] lg:ml-[150px]">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Customer</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                </div>
                <!--searh product-->
                <div v-if="!isAndroid" class="flex" @mouseover="tooltip.item = true" @mouseleave="tooltip.item = false"
                    :class="{ 'mt-0': isMobile, 'mt-5': isAndroid }">
                    <div class="border border-r-0 py-2 px-2">
                        <font-awesome-icon icon="fa-solid fa-barcode" size="lg" />
                    </div>
                    <!---search item--->
                    <input class="flex border justify-between py-2 px-2 w-full outline-none rounded-none" type="text"
                        v-model="selected_item" ref="selectItemField" @input="filterProducts()"
                        @keydown.enter="handleEnterKey(0, 'product')"
                        @keydown.down.prevent="handleDownArrow(filteredProductList)"
                        @keydown.up.prevent="handleUpArrow(filteredProductList)"
                        @focus="isFormFocus.selected_item = true" @blur="blurItemDropdown"
                        :class="{ 'border-blue-600': isFormFocus.selected_item }" placeholder="Item name / barcode" />
                    <!--Add new-->
                    <div class="border border-l-0 py-2 px-2 cursor-pointer" @click="addNewItemModal">
                        <font-awesome-icon icon="fa-solid fa-circle-plus" size="lg" style="color: blue" />
                    </div>
                    <!--Item dropdown-->
                    <ul v-if="productDropdown && selected_item && selected_item.length > 1"
                        class="absolute mt-10 ml-8 sm:ml-8 lg:ml-9 w-1/4 max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm cursor-pointer"
                        style="z-index: 150;" :style="{ width: $refs.selectItemField.offsetWidth + 'px' }"
                        @mouseover="mouseIsOnOption(true)" @mouseleave="mouseIsOnOption(false)">
                        <!-- Loading state -->
                        <li v-if="isLoading" class="px-2 py-2 text-center text-gray-500">
                            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500 inline"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4">
                                </circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            Loading...
                        </li>

                        <!-- Results -->
                        <template v-else>
                            <li class="hover:bg-gray-300 px-3 py-1 border" v-for="(product, i) in filteredProductList"
                                :key="i" :class="{ 'bg-gray-200': i === selectedIndex }"
                                @click="selectProduct(index, product)">
                                {{ product.barcodes.barcode + ' - ' + product.products.product_name }}
                            </li>
                            <li v-if="filteredProductList.length === 0 && selected_item && selected_item.length > 1 && findExistItem()"
                                @click="addNewItemModal"
                                class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">+
                                New Product</li>
                        </template>
                    </ul>
                    <!---tooltip-->
                    <div v-if="tooltip.item"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8 ml-[100px] lg:ml-[150px]">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Select Item</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                </div>
            </div>
            <!--display GST Number-->
            <div v-if="customer_data && Object.keys(customer_data).length > 0" class="text-xs">
                <div class="flex">
                    <div v-if="selectedOption_invoice_type === 'b2b'" class="flex">
                        <p><span class="font-bold">{{ !isAndroid ? 'GST No:' : 'GST:' }}</span>
                            <span class="uppercase">{{ customer_data.gst_number }}</span>
                        </p>
                        <button
                            class="rounded border border-gray-600 text-[10px] px-1 ml-1 mr-1 text-green-700 hover:border-green-700"
                            @click="updateCustomerData">Update</button>
                        <p v-if="customer_data.gst_number === undefined || customer_data.gst_number === null || customer_data.gst_number === ''"
                            class="text-[10px] text-red-700">Please update GST number..!</p>
                    </div>
                    <!---due amount-->
                    <div class="ml-1">
                        <p class="text-red-700 font-bold">{{ !isAndroid ? 'Balance Amount:' : 'Balance' }}
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }}
                            {{ customer_data.balance_amount }}
                        </p>
                    </div>
                </div>
            </div>
            <!--Additional fields-->
            <div v-if="form_fields && form_fields.length > 0" class="grid sm:gap-1 gap-0 items-center mb-1 py-1 sm:py-0"
                :class="{
                    'grid-cols-2': form_fields.length >= 2,
                    'grid-cols-2 sm:grid-cols-2': form_fields.length <= 2,
                    'sm:grid-cols-4': form_fields.length > 2 && form_fields.length >= 4,
                    'xl:grid-cols-5': form_fields.length > 4 && form_fields.length >= 5,
                    [`xl:grid-cols-${form_fields.length + 1}`]: form_fields.length < 4,
                    '2xl:grid-cols-6': form_fields.length > 5 && form_fields.length >= 6,
                    [`2xl:grid-cols-${form_fields.length + 1}`]: form_fields.length < 5
                }">
                <div v-for="(field, index) in form_fields" :key="index" class="form-group"
                    :class="{ 'pl-1 sm:pl-0': index % 2, 'pr-1 sm:pr-0': !index % 2 }">
                    <!-- Only render enabled fields -->
                    <div v-if="field.enabled">
                        <!-- Label and field with custom styles -->
                        <label :for="field.label" class="block text-[10px] sm:text-xs font-bold space-x-1">
                            <input v-model="field.label" type="text" :ref="'input-' + index"
                                class="text-[10px] sm:text-xs font-bold border-none bg-transparent outline-none" />
                            <button @click="editfields(index)" title="Edit">
                                <font-awesome-icon icon="fa-solid fa-pencil"
                                    class="text-blue-700 hover:text-blue-800" />
                            </button>
                            <button @click="removefields(index)" title="Delete">
                                <font-awesome-icon icon="fa-solid fa-trash-can"
                                    class="text-red-700 hover:text-red-800" />
                            </button>
                        </label>
                        <div v-if="field.field_type === 'date'">
                            <input v-model="field.value" type="date" :placeholder="field.label" :id="field.label"
                                v-datepicker class="w-full p-1 border outline-none" />
                        </div>
                        <div v-else>
                            <input v-model="field.value" type="text" :placeholder="field.label" :id="field.label"
                                class="w-full p-1 border outline-none" />
                        </div>
                    </div>
                </div>
                <!--add custom fields-->
                <div class="flex justify-center items-center">
                    <button @click="addNewField"
                        class="text-xs text-blue-800 hover:bg-blue-300 bg-blue-200 rounded-full px-2 py-1">+ Add
                        Custom
                        Field</button>
                </div>
            </div>
            <div v-else class="flex justify-start items-center py-1">
                <button @click="addNewField"
                    class="text-xs text-blue-800 hover:bg-blue-300 bg-blue-200 rounded-full px-2 py-1">+
                    Add
                    Custom
                    Field</button>
            </div>
            <!---table-->
            <div v-if="!isAndroid"
                class="overflow-x-auto sm:h-[650px] lg:h-[280px] xl:h-[280px] 2xl:h-[350px] 3xl:h-[550px] table-container">
                <table class="table w-full text-sm">
                    <thead class="sticky top-0 z-[10]">
                        <tr class="bg-gray-300 shadow-inner">
                            <th class="px-2 py-2 border border-white">Item Name <span
                                    class="text-xs px-2 py-1 flex-shrink-0 cursor-pointer info-msg font-normal"
                                    :title02="`Note: To add non-inventory items, enter details in the first row and click the &#10004; tick icon to include them in the sales list.`"><font-awesome-icon
                                        icon="fa-solid fa-circle-info" class="px-2 text-blue-700 text-lg" />
                                </span></th>
                            <th v-if="!isMobile" class="px-2 py-2 border border-white">Stock</th>
                            <th class="px-2 py-2 border border-white">Quantity</th>
                            <th class="px-2 py-2 border border-white">Price</th>
                            <th class="px-2 py-2 border border-white">Discount({{ currentCompanyList &&
                                currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }})</th>
                            <th class="px-2 py-2 border border-white">Tax</th>
                            <th class="px-2 py-2 border border-white">Subtotal</th>
                            <th class="border border-white non-printable">X</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!--without items-->
                        <tr>
                            <!--item name-->
                            <td class="border relative">
                                <div class="flex items-center justify-center">
                                    <textarea rows="3" v-model="formData.product_name" ref="formDataProduct"
                                        @keydown.enter="focusInputFormData('qty')"
                                        class="py-1 px-2 m-1 border border-blue-500 rounded w-full"
                                        placeholder="Enter product details"></textarea>
                                    <!-- <input placeholder="HSN code" v-model="formData.hsn_code"
                                        class="py-1 px-2 w-[100px] border border-blue-500 rounded" /> -->
                                </div>
                            </td>
                            <!--stock-->
                            <td v-if="!isMobile" class="border"><span class="flex justify-center text-center">- -</span>
                            </td>
                            <!--qty-->
                            <td class="py-2 px-2 border">
                                <div class="flex justify-center items-center">
                                    <div @click="removeQuantity()"
                                        class="cursor-pointer border flex justify-center items-center text-[20px] px-3 py-1 font-bold bg-gray-200 hover:bg-gray-300 text-red-700 ">
                                        -</div>
                                    <input type="number" v-model="formData.qty" @input="updateFormDataTotal('qty')"
                                        ref="formDataQty" @keydown.enter="focusInputFormData('price')"
                                        class="px-2 py-1 outline-none border rounded-none w-[80px] text-center" min="0"
                                        pattern="[0-9]*" />
                                    <div @click="addQuantity()"
                                        class="cursor-pointer border flex justify-center text-[20px] px-2 py-1 font-bold bg-gray-200 hover:bg-gray-300 text-green-700 ">
                                        +</div>
                                </div>
                            </td>
                            <!--price-->
                            <td class="py-1 px-1 border">
                                <div class="flex justify-center items-center">
                                    <input type="number" v-model="formData.price" @input="updateFormDataTotal('price')"
                                        @keydown.enter="focusInputFormData('tax')" ref="formDataPrice"
                                        class="w-[80px] px-2 py-1 rounded outline-none border" min="0" />
                                </div>
                            </td>
                            <!--discount-->
                            <td class="border w-[100px] items-center justify-center items-center text-center px-1 relative"
                                @mouseover="tooltip['discount' + 'formData'] = true"
                                @mouseleave="tooltip['discount' + 'formData'] = false">
                                <!--tooltip-->
                                <div v-if="tooltip['discount' + 'formData']"
                                    class="absolute flex flex-col items-center group-hover:flex -mt-10">
                                    <span
                                        class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                        <p>Click to change</p>
                                    </span>
                                    <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                </div>
                                <div clss="flex jsutify-center items-center">

                                    <p class="border rounded py-1 bg-gray-100 m-1 px-2"
                                        @click="openItemTaxDiscountDialog(false, false, 'formData', 'discount')">
                                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }}
                                        {{ formData.discount }}
                                    </p>
                                </div>
                            </td>
                            <!--tax-->
                            <td class="border relative" @mouseover="tooltip['tax' + 'formData'] = true"
                                @mouseleave="tooltip['tax' + 'formData'] = false">
                                <!--tooltip-->
                                <div v-if="tooltip['tax' + 'formData']"
                                    class="absolute flex flex-col items-center group-hover:flex -mt-8">
                                    <span
                                        class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                        <p>Click to change</p>
                                    </span>
                                    <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                </div>
                                <div class="flex justify-center items-center">
                                    <p class="border w-[100px] py-1 px-2 rounded m-1 bg-gray-100"
                                        @click="openItemTaxDiscountDialog(false, false, 'formData', 'tax')">
                                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }}
                                        {{ formData.tax }}
                                    </p>
                                </div>
                            </td>
                            <!--sub total-->
                            <td class="py-1 px-1 border">
                                <div class="flex justify-center items-center">
                                    <input v-model.number="formData.total" ref="formDataTotal"
                                        @input="updatePriceFomData" @keydown.enter="focusInputFormData('submit')"
                                        class="py-1 outline-none rounded border w-[100px] px-2" type="number" min="0" />
                                </div>
                            </td>

                            <!--add row button-->
                            <td class="non-printable actions-column">
                                <div class="flex justify-between items-center px-1">
                                    <button @click="addRowData"
                                        class="text-red-700 font-bold cursor-pointer hover:bg-green-200 w-full flex justify-center rounded-full py-2">
                                        <font-awesome-icon icon="fa-solid fa-check" class="text-green-700" />
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <!-- selected items-->
                        <tr v-if="items.length > 0" v-for="(item, index) in items" :key="index" class="text-center">
                            <!--item name-->
                            <td class="border relative px-2" @mouseover="tooltip['product_name' + index] = true"
                                @mouseleave="tooltip['product_name' + index] = false"
                                :class="{ 'w-1/4': !(index === 0 && typeOfInvoice !== 'sales') }">
                                <!--<div v-if="tooltip['product_name' + index] && !(index === 0 && typeOfInvoice !== 'sales')"
                                    class="absolute flex flex-col items-center group-hover:flex -mt-8">
                                    <span
                                        class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                        <p>Click to change tax & discount</p>
                                    </span>
                                    <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                </div>-->
                                <div v-if="!(index === 0 && typeOfInvoice !== 'sales') || typeOfInvoice === 'estimation' || typeOfInvoice === 'proforma'"
                                    class="flex justify-left items-center cursor-pointer w-[150px]">
                                    <p class="font-bold">{{ item.product_name }}</p>
                                </div>
                                <div v-if="index === 0 && typeOfInvoice !== 'sales' && typeOfInvoice !== 'estimation' && typeOfInvoice !== 'proforma'"
                                    class="flex items-center justify-center">
                                    <textarea rows="5" v-model="item.product_name"
                                        class="py-1 px-2 m-1 border border-blue-500 rounded w-full"></textarea>
                                    <input placeholder="HSN code" v-model="item.hsn_code"
                                        class="py-1 px-2 w-[80px] h-[50px] border border-blue-500 rounded" />
                                </div>
                                <!---add serial number-->
                                <div v-if="item.product_type === 'Product' && typeOfInvoice !== 'estimation'"
                                    class="flex justify-start px-1 py-2 text-xs">
                                    <button
                                        v-if="(item.serial_no === undefined || item.serial_no === '' || !item.serial_no || (Array.isArray(item.serial_no) && item.serial_no.length === 0)) && (!item.notes || item.notes == '')"
                                        class="bg-blue-400 text-white px-3 rounded rounded-full p-[2px] hover:bg-blue-500"
                                        @click="openSerialNumber(item)">Add Serial No</button>
                                    <div
                                        v-if="(item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0) || (item.notes && item.notes !== '')">
                                        <p v-if="typeOfInvoice !== 'estimation' && (item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0)"
                                            class="flex flex-wrap items-center space-x-1">
                                            <span class="font-semibold">Serial No:</span>
                                            <span v-for="serial in item.serial_no" :key="serial"
                                                class="truncate inline-block max-w-[30ch]">
                                                {{ serial }},
                                            </span>
                                        </p>

                                        <p v-if="item.notes && item.notes !== ''"
                                            class="flex flex-wrap items-center space-x-1">
                                            <span class="font-semibold">Des:</span>
                                            <span class="truncate inline-block max-w-[50ch]">
                                                {{ item.notes }}
                                            </span>
                                        </p>

                                        <button
                                            class="text-[10px] text-green-700 border px-2 border-gray-700 rounded hover:border-green-700 ml-1"
                                            @click="openSerialNumber(item)">Edit</button>
                                    </div>
                                </div>
                                <!---add Note to estimation-->
                                <div v-if="item.product_type === 'Product' && typeOfInvoice === 'estimation'"
                                    class="flex justify-start px-7 py-2">
                                    <button v-if="!item.notes || item.notes == ''"
                                        class="text-xs bg-blue-400 text-white px-3 rounded rounded-full p-[2px] hover:bg-blue-500"
                                        @click="openSerialNumber(item)">Add Note</button>
                                    <div v-if="item.notes && item.notes !== ''" class="text-[12px]">
                                        <p v-if="item.notes && item.notes !== ''">{{ item.notes }}</p>
                                        <button
                                            class="text-[10px] text-green-700 border px-2 border-gray-700 rounded hover:border-green-700 ml-1"
                                            @click="openSerialNumber(item)">Edit</button>
                                    </div>
                                </div>
                            </td>
                            <!--stock-->
                            <td v-if="!isMobile" class="border">
                                {{ item.total_qty ? item.total_qty : updateTotalQty() }}
                            </td>
                            <!--qty-->
                            <td class="py-2 px-2 border">
                                <div class="flex justify-center items-center">
                                    <div @click="removeQuantity(index)"
                                        class="cursor-pointer border flex justify-center items-center text-[20px] px-3 py-1 font-bold bg-gray-200 hover:bg-gray-300 text-red-700 ">
                                        -</div>
                                    <input type="number" v-model="item.qty"
                                        class="px-2 py-1 outline-none border rounded-none w-[80px] text-center"
                                        @input="updateTotal(index), is_updated = true"
                                        @focus="isFormFocus['qty' + index] = true"
                                        @blur="isFormFocus['qty' + index] = false, validateQuantity(index)"
                                        :class="{ 'border-blue-600': isFormFocus['qty' + index] }" min="0"
                                        pattern="[0-9]*" />
                                    <div @click="addQuantity(index)"
                                        class="cursor-pointer border flex justify-center text-[20px] px-2 py-1 font-bold bg-gray-200 hover:bg-gray-300 text-green-700 ">
                                        +</div>
                                </div>
                            </td>
                            <!--price-->
                            <td class="py-1 px-1 border">
                                <input type="number" v-model="item.price" @input="updateTotal(index), is_updated = true"
                                    class="w-[80px] px-2 py-1 rounded outline-none border" min="0"
                                    @focus="isFormFocus['price' + index] = true"
                                    @blur="isFormFocus['price' + index] = false, validatePrice(index)"
                                    :class="{ 'border-blue-600': isFormFocus['price' + index] }" />
                            </td>
                            <!--discount-->
                            <td class="border w-[100px] items-center justify-center items-center text-center px-1 relative"
                                @mouseover="tooltip['discount' + index] = true"
                                @mouseleave="tooltip['discount' + index] = false">
                                <!--tooltip-->
                                <div v-if="tooltip['discount' + index]"
                                    class="absolute flex flex-col items-center group-hover:flex -mt-10">
                                    <span
                                        class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                        <p>Click to change</p>
                                    </span>
                                    <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                </div>
                                <p class="border rounded py-1 bg-gray-100 m-1 px-2"
                                    @click="openItemTaxDiscountDialog(item, index, '', 'discount')">{{
                                        currentCompanyList &&
                                            currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }} {{
                                        item.discount }}</p>
                            </td>
                            <!--tax-->
                            <td class="border relative w-[100px]" @mouseover="tooltip['tax' + index] = true"
                                @mouseleave="tooltip['tax' + index] = false">
                                <!--tooltip-->
                                <div v-if="tooltip['tax' + index]"
                                    class="absolute flex flex-col items-center group-hover:flex -mt-8">
                                    <span
                                        class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                        <p>Click to change</p>
                                    </span>
                                    <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                </div>
                                <p class="border py-1 rounded m-1 bg-gray-100 text-left px-2"
                                    @click="openItemTaxDiscountDialog(item, index, '', 'tax')">{{ currentCompanyList &&
                                        currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }} {{
                                        item.tax }}</p>
                            </td>
                            <!--sub total-->
                            <td class="py-1 px-1 border">
                                <input v-model="item.total" class="py-1 outline-none rounded border w-[100px] px-2"
                                    type="number" min="0" @input="handleSubtotalChange(index)"
                                    :id="'input-total-' + index" @keyup.enter="focusItemFields()"
                                    @focus="isFormFocus['total' + index] = true, is_updated = false"
                                    @blur="isFormFocus['total' + index] = false"
                                    :class="{ 'border-blue-600': isFormFocus['total' + index] }" />
                            </td>

                            <!--delete row button-->
                            <td class="non-printable actions-column">
                                <div class="flex justify-between items-center px-1">
                                    <button @click="moveUp(index)" :class="{ 'hidden': index === 0 }" title="Move Up"
                                        class="w-full flex justify-center text-blue-700 hover:text-blue-600">
                                        <font-awesome-icon icon="fa-solid fa-arrow-up" />
                                    </button>
                                    <button @click="moveDown(index)" :class="{ 'hidden': index === items.length - 1 }"
                                        title="Move Down"
                                        class="w-full flex justify-center text-blue-700 hover:text-blue-600">
                                        <font-awesome-icon icon="fa-solid fa-arrow-down" />
                                    </button>
                                    <button @click="deleteRow(index)"
                                        class="text-red-700 font-bold cursor-pointer hover:bg-gray-100 w-full flex justify-center">
                                        <font-awesome-icon icon="fa-solid fa-trash-can" style="color: red" />
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!--Non inventory sales items add-->
            <div v-if="isAndroid && isMobile" class="flex justify-center items-center mb-3">
                <button @click="addSalesItems"
                    class="text-blue-700 px-4 rounded border-2 border-dashed border-blue-600">
                    {{ items.length === 0 ? '+ Add Item' : '+ Add / Edit Items' }}
                    <!-- <span v-if="items.length === 0"
                        class="text-xs px-2 py-1 flex-shrink-0 cursor-pointer info-msg font-normal"
                        :title="`Note: To add non-inventory items.`">
                        <font-awesome-icon icon="fa-solid fa-circle-info" class="px-2 text-blue-700 text-lg" />
                    </span> -->
                </button>
            </div>
            <!---Mobile view sales design-->
            <div v-if="isAndroid && isMobile" class="mt-1 flex flex-wrap justify-center table-container"
                :class="{ 'h-[250px] overflow-y-auto': items.length > 7 && isAndroid && isMobile }">
                <table class="table-auto w-full border-collapse bg-white">
                    <!-- Table Header -->
                    <thead>
                        <tr class="font-bold text-center border-b">
                            <th class="text-left px-3 py-2 col-span-2">Product</th>
                            <th class="py-2">QTY</th>
                            <th class="py-2">Total</th>
                            <th class="py-2">Actions</th>
                        </tr>
                    </thead>

                    <!-- Table Body -->
                    <tbody>
                        <tr v-for="(item, index) in items" :key="index" class="border-b text-center">
                            <!-- Product Name -->
                            <td class="text-left px-3 py-2 font-semibold">{{ item.product_name }}</td>

                            <!-- Quantity and Total -->
                            <td class="py-2">{{ item.qty }}</td>
                            <td class="py-2">{{ item.total }}</td>

                            <!-- Actions -->
                            <td class="flex justify-center space-x-2 py-2 px-1">
                                <button @click="editProduct(index)" title="Edit product"
                                    class="text-blue-700 hover:text-blue-600">
                                    <font-awesome-icon icon="fa-solid fa-pencil" />
                                </button>
                                <button v-if="index > 0" @click="moveUp(index)" title="Move Up"
                                    class="text-blue-700 hover:text-blue-600">
                                    <font-awesome-icon icon="fa-solid fa-arrow-up" />
                                </button>
                                <button v-if="index < items.length - 1" @click="moveDown(index)" title="Move Down"
                                    class="text-blue-700 hover:text-blue-600">
                                    <font-awesome-icon icon="fa-solid fa-arrow-down" />
                                </button>
                                <button @click="deleteRow(index)" title="Delete" class="text-red-700 hover:bg-gray-100">
                                    <font-awesome-icon icon="fa-solid fa-trash-can" />
                                </button>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
            <!---notification send and terms and condition--->
            <div class="px-0 sm:px-3 py-1 flex justify-between">
                <!--notification-->
                <div class="flex items-center">
                    <label v-if="companywhatsapp" class="flex justify-center items-center mr-2">
                        <div class="relative w-12 h-6 rounded-full transition-colors duration-300 flex items-center"
                            @click="iswhatsapp = !iswhatsapp"
                            :class="{ 'bg-green-600': iswhatsapp, 'bg-gray-300': !iswhatsapp }">
                            <div class="absolute top-0 left-0 w-6 h-6 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-6': iswhatsapp, 'translate-x-0': !iswhatsapp }">
                            </div>
                        </div>
                        <font-awesome-icon icon="fa-brands fa-whatsapp" size="xl" class="ml-2" style="color:green" />
                    </label>
                    <button v-else @click="navigateToWhatsApp" class="text-red-600">Connect <font-awesome-icon
                            icon="fa-brands fa-whatsapp" size="xl" class="ml-2" style="color:red" /></button>
                    <label class="flex justify-center items-center ml-2">
                        <div class="relative w-8 h-4 sm:w-12 sm:h-6 rounded-full transition-colors duration-300 flex items-center"
                            @click="issms = !issms" :class="{ 'bg-green-600': issms, 'bg-gray-300': !issms }">
                            <div class="absolute top-0 left-0 w-4 h-4 sm:w-6 sm:h-6 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-4 sm:translate-x-6': issms, 'translate-x-0': !issms }">
                            </div>
                        </div>
                        <font-awesome-icon icon="fa-solid fa-comment-sms" size="xl" class="ml-2"
                            style="color:blueviolet" />
                    </label>
                    <p v-if="!isMobile" class="ml-1 text-xs">Send alert to Customer</p>
                    <div>
                        <div class=" ml-1" @mouseover="tooltip.info_notify = true"
                            @mouseleave="tooltip.info_notify = false">
                            <font-awesome-icon icon="fa-solid fa-circle-info" size="lg" class="text-blue-800" />
                        </div>
                        <!---tooltip-->
                        <div v-if="tooltip.info_notify"
                            class="absolute flex flex-col items-center group-hover:flex -mt-14 -ml-[55px]">
                            <span
                                class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                <p>sent alert to customer</p>
                            </span>
                            <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                        </div>
                    </div>
                </div>
                <!--terms and condition-->
                <div class="flex cursor-pointer" @mouseover="tooltip.terms = true" @mouseleave="tooltip.terms = false">
                    <div class="flex text-blue-500 hover:text-blue-600 font-bold px-2 items-center"
                        @click="openTermsDialog">
                        <div>
                            <font-awesome-icon icon="fa-regular fa-file-lines" size="xl" class="mr-1" />
                        </div>
                        <p>T & C</p>
                    </div>
                    <!---tooltip-->
                    <div v-if="tooltip.terms" class="absolute flex flex-col items-center group-hover:flex -mt-12 -ml-7">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Edit terms & condition</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                </div>
            </div>
            <!--bottom div-->
            <div class="py-2 mt-1 bg-gray-200 px-3 relative w-full rounded shadow-inner">
                <!--total div-->
                <!--: typeOfInvoice !== 'estimation', 'sm:grid-cols-5': typeOfInvoice === 'estimation' }-->
                <div class="grid grid-cols-2  gap-2"
                    :class="{ 'sm:grid-cols-7': typeOfInvoice !== 'estimation', 'sm:grid-cols-6': typeOfInvoice === 'estimation' }">
                    <!--total qantity-->
                    <div :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <p>Total Quantity:</p>
                        <p class="text-md items-center font-semibold text-blue-800">
                            <font-awesome-icon icon="fa-solid fa-tags" />
                            {{ totalQuantity }}
                        </p>
                    </div>
                    <!--total amount-->
                    <div :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <p>Total Amount:</p>
                        <p class="text-md font-semibold text-blue-800">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }}
                            {{ totalAmount.toFixed(2) }}
                        </p>
                    </div>
                    <!--shipping charges-->
                    <div :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <div class="flex cursor-pointer" @click="openShippingDialog" title="click to change">
                            <p>Shipping Charge:</p>
                            <font-awesome-icon icon="fa-regular fa-pen-to-square" size="lg"
                                class="ml-1 text-blue-600" />
                        </div>
                        <p class="font-semibold text-md text-sky-600">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }}
                            {{ this.shipping_details.shipping ? this.shipping_details.shipping.toFixed(2) :
                                this.shipping_details.shipping }}
                        </p>
                    </div>
                    <!--total discount-->
                    <div :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <div class="flex cursor-pointer" @click="openOverallDiscountDialog" title="click to change">
                            <p>Total Discount:</p>
                            <font-awesome-icon icon="fa-regular fa-pen-to-square" size="lg"
                                class="ml-1 text-blue-600" />
                        </div>
                        <div class="items-center">
                            <div class="text-xs items-center">Items:
                                <span class="font-semibold text-red-500">
                                    {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}
                                    {{ totalDiscount }}
                                </span>
                            </div>
                            <div v-if="over_all_discount !== null" class="text-xs font-semibold items-center">
                                OverAll:
                                <span class="font-semibold text-red-500">
                                    {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}
                                    {{ overAllDiscount }}
                                </span>
                            </div>
                        </div>
                    </div>
                    <!--grand total-->
                    <div
                        :class="{ 'bg-white rounded px-2 py-1': isMobile, 'flex justify-between items-center col-span-2': isAndroid }">
                        <p>Grand Total:</p>
                        <p class="text-lg flex items-center font-semibold text-green-800">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }}
                            {{ grandTotal.toFixed(2) }}
                        </p>

                    </div>
                    <!--save and print --- && typeOfInvoice !== 'proforma'-->
                    <div v-if="typeOfInvoice !== 'estimation'" class="col-span-2 items-center w-full relative"
                        :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <!-- <button
                            class="border px-2 w-full py-2 rounded shadow-inner shadow-green-200 border-green-500 text-white bg-green-700 "
                            @click="savePrintInvoice">Save &
                            Print</button> -->
                        <div class="flex justify-end items-center text-xs">
                            <p>Mark as fully payment</p>
                            <input type="checkbox" class="ml-2" v-model="formValues.mark_full_pay"
                                @change="updateBalanceData" />
                        </div>
                        <div v-if="paymentData && paymentData.length > 0 && paymentAddStatus"
                            class="flex justify-end pt-1">
                            <div class="relative">
                                <p class="absolute text-gray-400 ml-2 mt-[6px]">
                                    {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}
                                </p>
                            </div>
                            <input type="number" placeholder="0"
                                v-model="paymentData[paymentData.length - 1].payment_amount" ref="paymentamount"
                                @input="validateFullPay"
                                class="border py-1 px-5 bg-gray-100 rounded rounded-tr-none rounded-br-none w-full" />
                            <select class="border py-1 px-2 rounded rounded-tl-none rounded-bl-none w-full"
                                v-model="paymentData[paymentData.length - 1].payment_type" @change="is_updated = true">
                                <option
                                    v-if="invoice_setting && invoice_setting.length > 0 && invoice_setting[0].payment_opt && invoice_setting[0].payment_opt"
                                    v-for="(opt, index) in JSON.parse(invoice_setting[0].payment_opt)" :key="index"
                                    :value="opt.type">{{ opt.type }}</option>
                            </select>
                        </div>
                        <div v-if="!(paymentData && paymentData.length > 0) || !paymentAddStatus">
                            <div class="flex justify-end items-center py-1">
                                <button
                                    class="flex items-center text-sm text-green-600 border border-green-600 shadow-inner shadow-white px-2 rounded bg-green-100 sm:bg-white"
                                    @click="updateBalanceData"><font-awesome-icon icon="fa-solid fa-wallet"
                                        class="px-1 text-[#ed8e2e]" /> Add Pay</button>
                            </div>
                        </div>
                        <!--payment close-->
                        <button v-if="paymentAddStatus" @click="resetPaymentAddPay"
                            class="absolute top-1 text-red-700 flex items-center text-xs">
                            <font-awesome-icon icon="fa-solid fa-xmark" class="px-1" />Close</button>
                        <!--balance or return amount-->
                        <div v-if="paymentData && paymentData.length > 0"
                            class="flex justify-between items-center px-2 py-1">
                            <p>Balance : <span class="text-red-700 font-semibold">
                                    {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}
                                    {{ getBalanace }} </span></p>
                            <p>Return : <span class="text-green-700 font-semibold">{{ currentCompanyList &&
                                currentCompanyList.currency
                                === 'INR' ? '\u20b9' : currentCompanyList.currency}} {{ getReturn }}</span></p>
                        </div>
                    </div>
                    <!---add notes-->
                    <div v-if="typeOfInvoice === 'estimation'" class="flex justify-center items-center"
                        :class="{ 'bg-white rounded px-2 py-1': isMobile }">
                        <button class="px-2 w-full py-2 rounded text-blue-700 hover:text-blue-800"
                            @click="showAddNotes">
                            Add Note <font-awesome-icon icon="fa-regular fa-pen-to-square" />
                        </button>
                    </div>
                </div>
                <!--buttons list-->
                <div v-if="typeOfInvoice !== 'estimation' && typeOfInvoice !== 'proforma'"
                    :class="{ 'grid-cols-4': !$route.query.type || $route.query.type !== 'edit', 'grid-cols-3': $route.query.type === 'edit', 'fixed bottom-0 bg-white -ml-5 gap-2 w-full': isMobile, 'gap-16': !isMobile }"
                    class="grid mt-2 p-1">
                    <!--hold-->
                    <button @click="holdTheInvoice()"
                        :class="{ 'hidden': this.$route.query.type === 'edit', 'px-2 py-2': !isMobile, 'py-1': isMobile }"
                        class="flex border text-white rounded justify-center text-center items-center shadow-inner shadow-red-200 border-red-700  bg-red-700  text-sm flex">
                        <img :src="hold_icon" alt="hold" class="w-[25px] h-[25px] mr-1 ">
                        <span :class="{ 'text-[12px] text-center': isMobile }">Hold</span>
                    </button>

                    <!--full credit fullCreditInvoice('credit')-->

                    <!-- <button @click="openPaymentModal('credit')"
                        class="flex border  text-white rounded justify-center shadow-inner shadow-yellow-200 border-yellow-600 bg-yellow-600 text-sm font-bold text-center items-center flex-wrap"
                        :class="{ 'px-2 py-2': !isMobile, 'py-1': isMobile }"><img :src="credit_icon" alt="credit"
                            class="w-[25px] h-[25px] mr-1">
                        <span :class="{ 'text-[12px] text-center w-full': isMobile }">Full Credit</span>
                    </button> -->
                    <!--multiple-->
                    <button @click="openPaymentModal('multiple')"
                        class="flex border  text-white  rounded justify-center text-center items-center shadow-inner shadow-blue-200 border-blue-700 bg-blue-700 text-sm flex"
                        :class="{ 'px-2 py-2': !isMobile, 'py-1 px-1': isMobile }"><img :src="multiple_icon"
                            alt="multiple" class="w-[25px] h-[25px] mr-1">
                        <span :class="{ 'text-[12px] text-center line-clamp-1': isMobile }">Multiple Pay</span>
                    </button>
                    <!--cash paidAllInCash('cash')-->
                    <!-- <button @click="openPaymentModal('cash')"
                        class="flex border  text-white  rounded justify-center text-center items-center shadow-inner shadow-green-200 border-green-600 bg-green-600 text-sm font-bold  flex-wrap"
                        :class="{ 'px-2 py-2': !isMobile, 'py-1': isMobile }"><img :src="cash_icon" alt="cash"
                            class="w-[25px] h-[25px] mr-1">
                        <span :class="{ 'text-[12px] text-center w-full': isMobile }">Cash</span>
                    </button> -->
                    <!---pay all-->
                    <!-- <button @click="openPaymentModal('payall')"
                        class="flex border  text-white  rounded justify-center text-center items-center shadow-inner shadow-violet-200 border-violet-600 bg-violet-600 text-sm font-bold flex-wrap"
                        :class="{ 'px-2 py-2': !isMobile, 'py-1': isMobile }"><img :src="pay_all_icon" alt="pay_all"
                            class="w-[25px] h-[25px] mr-1">
                        <span :class="{ 'text-[12px] text-center w-full': isMobile }">Pay All</span>
                    </button> -->
                    <!-- Save-->
                    <button
                        class="flex border text-white rounded justify-center text-center items-center shadow-inner shadow-green-200 border-green-600 bg-green-600 text-sm"
                        :class="{ 'px-2 py-2': !isMobile, 'py-1': isMobile }"
                        @click="printing = false, saveData('Success')">
                        <font-awesome-icon icon="fa-regular fa-floppy-disk" class="w-[20px] h-[20px] mr-1" />
                        <span :class="{ 'text-[12px] text-center': isMobile }">Save</span>
                    </button>
                    <!--Print-->
                    <button
                        class="flex border text-white rounded justify-center text-center items-center shadow-inner shadow-gray-200 border-gray-600 bg-gray-600 text-sm flex"
                        :class="{ 'px-2 py-2': !isMobile, 'py-1': isMobile }"
                        @click="printing = true, saveData('Success')">
                        <font-awesome-icon icon="fa-solid fa-print" class="w-[20px] h-[20px] mr-1" />
                        <span :class="{ 'text-[12px] text-center': isMobile }">Print</span>
                    </button>
                </div>
                <div v-if="typeOfInvoice === 'estimation'"
                    :class="isMobile ? 'fixed bottom-0 left-1/2 transform -translate-x-1/2 flex justify-center w-full p-2 bg-white' : 'mt-6 flex justify-center'">
                    <!---save-->
                    <button @click="saveEstimation('save')"
                        class="flex border border-violet-600 px-5 py-2 mr-3 text-white  rounded justify-center text-center items-center bg-violet-600 shadow-inner shadow-violet-200 text-sm flex-wrap">
                        <img :src="pay_all_icon" alt="save" class="w-[25px] h-[25px] mr-1">
                        <span>Save</span></button>
                    <!---save & preview-->
                    <button @click="saveEstimation('print')"
                        class="flex border border-green-600 px-5 sm:px-2 py-2 text-white  rounded justify-center text-center items-center bg-green-600 shadow-inner shadow-green-200 text-sm flex-wrap"><img
                            :src="cash_icon" alt="save_preview" class="w-[25px] h-[25px] mr-1">
                        <span>
                            Save & Preview</span></button>
                </div>
                <div v-if="typeOfInvoice === 'proforma'"
                    :class="isMobile ? 'fixed bottom-0 left-1/2 transform -translate-x-1/2 flex justify-center w-full p-2 bg-white' : 'flex justify-center space-x-4 mt-2'">
                    <!---save-->
                    <button @click="saveProforma('save')"
                        class="flex border border-violet-600 px-5 py-2 mr-3 text-white  rounded justify-center text-center items-center bg-violet-600 shadow-inner shadow-violet-200 text-sm">
                        <img :src="pay_all_icon" alt="save" class="w-[25px] h-[25px] mr-1">
                        <span>Save</span></button>
                    <!---save & preview-->
                    <button @click="saveProforma('print')"
                        class="flex border border-green-600 px-5 sm:px-2 py-2 text-white  rounded justify-center text-center items-center bg-green-600 shadow-inner shadow-green-200 text-sm mr-3"><img
                            :src="cash_icon" alt="save_preview" class="w-[25px] h-[25px] mr-1">
                        <span class="line-clamp-1">
                            Print</span></button>
                    <!--multiple-->
                    <button @click="openPaymentModal('multiple')"
                        class="flex border border-blue-700 text-white  rounded justify-center text-center items-center bg-blue-700 shadow-inner shadow-blue-200 text-sm"
                        :class="{ 'px-2 py-2': !isMobile, 'py-1 px-2': isMobile }"><img :src="multiple_icon"
                            alt="multiple" class="w-[25px] h-[25px] mr-1">
                        <span :class="{ 'text-[12px] text-center line-clamp-1': isMobile }">Advance Pay</span>
                    </button>
                </div>
            </div>

        </div>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModalCustomer" :userName="selectCustomer"
            :editData="editData" :type="typeOfRegister">
        </customerRegister>
        <!--confirm box---->
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <!---add terms and condition-->
        <posTermsAndCondition :showModal="open_termsModel" @close-Modal="closeTermsDialog"
            :typeOfInvoice="typeOfInvoice" :termsAndCon="invoice_setting" :companyId="companyId" :userId="userId">
        </posTermsAndCondition>
        <!---overall discount-->
        <posAddDiscount :showModal="open_overall_discount" @close-modal="closeOverallDiscountDialog"
            :over_all_discount="JSON.parse(JSON.stringify(over_all_discount))" :grandTotal="grandTotal"
            :currentCompanyList="currentCompanyList">
        </posAddDiscount>
        <!---change each item tax and discount-->
        <posAddTaxDiscount :showModal="open_item_tax_discount" @close-modal="closeItemTaxDiscountDialog"
            :itemData="selected_item_data" :currentCompanyList="currentCompanyList" :invoice_setting="invoice_setting"
            :type="type_tax_or_discount">
        </posAddTaxDiscount>
        <!---shipping charges-->
        <shippingchargesPos :showModal="open_shippingcharges" @close-modal="closeShippingDialog"
            :itemData="shipping_details">
        </shippingchargesPos>
        <!---payment type and collect-->
        <paymentCollectPos :showModal="open_paymentModal" @close-modal="closePaymentModal" :isMobile="isMobile"
            :invoice_setting="invoice_setting" :get_all_data="get_all_data" :itemData="payment_value"
            :payment_data="JSON.parse(JSON.stringify(paymentData))" :type="payment_type" :from="typeOfInvoice"
            :currentCompanyList="currentCompanyList">
        </paymentCollectPos>
        <!--add new item-->
        <addNewItem :showModal="open_add_newItem" @close-modal="closeItemModal" :product_name="selected_item">
        </addNewItem>
        <!---print invoice-->
        <!-- <template_2 ref="printTemplate" :typeOfInvoice="typeOfInvoice" :formData="invoice_setting"
            :customer_data="customer_data" :items_data="items" :get_all_data="get_all_data" :paymentData="paymentData"
            :invoice_data="collect_invoice_data" :return_amount="formValues" :logo_img="logo_img" @getBack="getBack"
            @sucessPrint="saveData('Success')"></template_2> -->
        <!--Add Serial Number-->
        <posAddSerialNumberItem :showModal="openSerial" @close-modal="closeSerialModel" :typeOfInvoice="typeOfInvoice"
            :item_data="selected_serial_item">
        </posAddSerialNumberItem>
        <!--dialog alert-->
        <dialogAlert :show-modal="open_message" @close="closeMessage" :message="message"></dialogAlert>
        <!--hold list-->
        <holdList :showModal="enable_hold" @close-Modal="closeHold" @refresh_hold="refreshHold"></holdList>
        <Loader :showModal="open_loader"></Loader>
        <estimationAddNote :show-modal="show_add_notes" @close="closeAddNotes" :existData="est_notes">
        </estimationAddNote>
        <posMinusSaleSetting :show-modal="minus_sales_open" @close-modal="closeMinusSales"
            :invoice_setting="invoice_setting" :companyId="companyId">
        </posMinusSaleSetting>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <!-- in mobile view-->
        <addsalesData :show-modal="addSalesItem" @close-modal="closesalesItems" :formData="formData"
            :isMobile="isMobile" :isAndroid="isAndroid" @getProduct="getProductListData" :typeOfInvoice="typeOfInvoice"
            :invoice_setting="invoice_setting" :currentCompanyList="currentCompanyList" @resetForm="resetForm"
            :sales_items="items" :editData="editProductIndex >= 0 ? items[editProductIndex] : undefined"
            :editIndex="editProductIndex" :over_all_discount="isNaN(over_all_discount) ? 0 : over_all_discount"
            :shipping_details="shipping_details" :companyId01="companyId01">
        </addsalesData>
        <!--sales success msg-->
        <salesSuccessMsg :showModal="showModalSuccessMsg" @close="closeSuccessMsg" :typeOfInvoice="typeOfInvoice"
            :response_data="response_data" :currentInvoice="currentInvoice">
        </salesSuccessMsg>
        <shippingModal :showModal="isModalOpen" :customer="customer_data" :sameAsBilling="sameAsBilling"
            :shipping_detail="shipping_detail" @close="isModalOpen = false" @saveData="handleSave"></shippingModal>
    </div>
</template>
<script>
import customerRegister from '../../dialog_box/customerRegister.vue';
import addName from '../../dialog_box/addName.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import posTermsAndCondition from '../../dialog_box/posTermsAndCondition.vue';
import posAddDiscount from '../../dialog_box/posAddDiscount.vue';
import posAddTaxDiscount from '../../dialog_box/posAddTaxDiscount.vue';
import shippingchargesPos from '../../dialog_box/shippingchargesPos.vue';
import paymentCollectPos from '../../dialog_box/paymentCollectPos.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import addNewItem from '../../dialog_box/addNewItem.vue';
import template_2 from '../../setting_categories/invoiceTemplates/template_2.vue';
import posAddSerialNumberItem from '../../dialog_box/posAddSerialNumberItem.vue';
import axios from 'axios';
import holdList from '../../dialog_box/holdList.vue';
import { mapActions, mapGetters } from 'vuex';
import estimationAddNote from '../../dialog_box/estimationAddNote.vue';
import posMinusSaleSetting from '../../dialog_box/posMinusSaleSetting.vue';
import addsalesData from '../../dialog_box/addsalesData.vue';
import salesSuccessMsg from '../../dialog_box/salesSuccessMsg.vue';
import shippingModal from '../../dialog_box/shippingModal.vue';
export default {
    components: {
        customerRegister,
        addName,
        confirmbox,
        posTermsAndCondition,
        posAddDiscount,
        posAddTaxDiscount,
        shippingchargesPos,
        paymentCollectPos,
        dialogAlert,
        addNewItem,
        template_2,
        posAddSerialNumberItem,
        holdList,
        estimationAddNote,
        posMinusSaleSetting,
        addsalesData,
        salesSuccessMsg,
        shippingModal
    },
    props: {
        companyId: String,
        userId: String,
        typeOfInvoice: String,
        service_data: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            isMobile: false,
            isAndroid: false,
            selectedOption_invoice_type: '',
            initital_code: '',
            invoice_num: '',
            invoice_setting: null,
            hold_icon: '/images/pos/hold.png',
            credit_icon: '/images/pos/credit.png',
            multiple_icon: '/images/pos/multiple.png',
            cash_icon: '/images/pos/cash.png',
            pay_all_icon: '/images/pos/pay_all.png',
            logo_img: '/images/head_bar/logo_01.png',
            //---focus effect--
            isFormFocus: {},
            //---customer---
            customers: [],
            isDropdownOpen_customer: false,
            customer_data: {},
            editData: null,
            typeOfRegister: 'add',
            selectCustomer: '',
            filteredCustomerOptions: [],
            mouse_leave_customer: false,
            //--customer modal--
            showModal_customer: false,
            //---items data
            selected_item: '',
            product: [],
            productDropdown: false,
            filteredProductList: [],
            items: [],
            isMouseInOption: false,
            //---index--
            selectedIndex: 0,
            //--payment
            paymentRows: [],
            //--confirm box--
            open_confirmBox: false,
            deleteIndex: null,
            //---tooltip---
            tooltip: {},
            //---modal--
            open_termsModel: false,
            open_overall_discount: false,
            open_item_tax_discount: false,
            selected_item_data: null,
            open_item_tax_discount_index: null,
            over_all_discount: null,
            //---shipping--
            shipping: 0,
            open_shippingcharges: false,
            //---payment--
            open_paymentModal: false,
            payment_value: null,
            payment_type: null,
            //---add new item--
            open_add_newItem: false,
            get_all_data: null,
            //--api integration--
            companyId01: null,
            userId01: null,
            collect_invoice_data: null,
            printing: false,
            //--get data----
            formValues: { invoice_group: 0 },
            paymentData: [],
            exist_data: {},
            currentDate: null,
            //---Serial---
            openSerial: false,
            selected_serial_item: null,
            //---shipping data
            shipping_details: { shipping: 0 },
            //--get invoice number----
            response_data_number: null,
            //---notification---
            iswhatsapp: false,
            issms: false,
            //---dialog alert--
            open_message: false,
            message: '',
            //----hold---
            getHold_data: null,
            //---pagination
            pagination: {},
            open_loader: false,
            //---add notes---
            show_add_notes: false,
            est_notes: '',
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---is updated---
            is_updated: false,
            //----minus sales---
            minus_sales_open: false,
            //---create new items---
            formData: { product_type: "Services", product_name: '', qty: 0, price: 0, total: 0, discount: 0, tax: 0, tax_type: '', taxvalue: null, discount_data: { type: 'Fixed', value: 0 } },
            //---add sales items--
            addSalesItem: false,
            //------focus on tax || discount---
            focustaxdiscount: false,
            editProductIndex: null,
            //----make payment directly---
            paymentDetails: { value: 0, type: '' },
            paymentAddStatus: false,
            //---hold list---
            enable_hold: false,
            //---payment data initial--
            paymentData_int: false,
            //---close msg--
            showModalSuccessMsg: false,
            response_data: null,
            //--shipping details---
            isModalOpen: false,
            sameAsBilling: true,
            shipping_detail: {},
            //----clone estimation--
            is_clone: false,
            //---flag subtotal--
            flag_subtotal: false,
            //----tax or discount---
            type_tax_or_discount: '',
            //---is tax ON/OFF---
            is_tax: 1,
            //---form fields---
            form_fields: [],
            deleteType: null,
            //--loading--
            isLoading: false,
        };
    },
    methods: {
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('items', ['fetchItemList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('companies', ['fetchCompanyList']),
        ...mapActions('holdsList', ['fetchHoldsList', 'updateIsEnable', 'updateIsShowList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),
        ...mapActions('clone', ['getEstimation', 'updateEstimation']),
        //---close hold---
        closeHold(data) {
            if (data.id && this.$route.query.type !== 'edit') {
                // console.log(data, 'in hold data..!!');
                this.getHold_data = data;
                this.items = JSON.parse(data.sales_items_data);
                let get_data = JSON.parse(data.data);
                this.customer_data = get_data.customer_data;
                this.initital_code = get_data.initital_code;
                this.selectedOption_invoice_type = get_data.invoice_type;
                this.iswhatsapp = this.companywhatsapp ? get_data.iswhatsapp : false;
                this.issms = get_data.issms;
                this.formValues = get_data.form_values;
                this.paymentData = get_data.payment_mode;
                this.shipping_details = get_data.shipping_details;
            } else if (data.id && this.$route.query.type === 'edit') {
                this.openMessage('Hold invoices are used only for creating new invoices.')
            }
            // this.$emit('close_hold');
            this.enable_hold = false;
            this.updateIsShowList(false);
        },
        openMessage(msg) {
            // console.log(msg, 'what happning the data...?');
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.message = '';
            this.open_message = false;
        },
        //---update is mobile---
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        //---toggle dropdown for customer----
        toggleDropdownOpen() {
            this.isDropdownOpen_customer = !this.isDropdownOpen_customer;
            // If the dropdown is being closed
            if (this.isDropdownOpen_customer) {
                this.selectCustomer = this.customer_data ? this.customer_data.contact_number : '';
                // Wait for Vue to update the DOM
                this.$nextTick(() => {
                    // If the input element exists
                    if (this.$refs.selectCustomerInput) {
                        // Focus on the input element
                        this.$refs.selectCustomerInput.focus();
                        this.isFormFocus.customer = true;
                    }
                });
            }
        },
        //----reset the customer--
        resetCustomer() {
            this.customer_data = {};
        },
        //--customer
        //---select customer
        selectDropdownOption(option) {
            this.customer_data = option;
            this.selectedOption_invoice_type = option.gst_number ? 'b2b' : 'b2c';
            this.isDropdownOpen_customer = false; // Close the dropdown
            this.selectCustomer = '';
            if (this.pagination && this.pagination.customer && Number(this.pagination.customer.current_page) !== 1) {
                this.getCustomerListData((this.pagination.customer.current_page * 1) + 1, 'all');
            }
            this.is_updated = true;
        },
        mouseOutFunction(data) {
            // console.log(data, 'what happeninf');
            this.mouse_leave_customer = data;
        },
        customerBlur() {
            this.isFormFocus.customer = false
            if (!this.mouse_leave_customer) {
                this.isDropdownOpen_customer = false;
            }
        },
        //---filter
        handleDropdownInput(fields) {
            // console.log('What happening...WW');
            const inputValue = fields;
            // console.log(inputValue, 'Waht happening....!');
            if (inputValue !== undefined && inputValue !== null) {
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    // console.log('hello');
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customers.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber) || (option.alternateNumber && option.alternateNumber.toLowerCase().includes(inputNumber))
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();

                    this.filteredCustomerOptions = this.customers.filter(
                        (option) => (option.first_name && option.first_name.toLowerCase().includes(inputName)) || (option.last_name && option.last_name.toLowerCase().includes(inputName)) || (option.email && option.email.toLowerCase().includes(inputName))
                    );
                }
                if (this.filteredCustomerOptions.length === 0 && this.pagination.customer && Number(this.pagination.customer.current_page) !== this.pagination.customer.last_page) {
                    this.getCustomerListData((this.pagination.customer.current_page * 1) + 1, 'all');
                }
            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = [];
            }
        },
        updateCustomerData() {
            this.editData = this.customer_data;
            this.typeOfRegister = 'edit';
            this.openModal();
        },
        openModal() {
            // console.log(data, 'What happening...!');
            this.isDropdownOpen_customer = false;
            this.showModal_customer = true;
        },
        closeModalCustomer(getData) {
            // console.log('Wahyyyyyyyyyyyyyyyyy');
            // console.log(getData, 'What happening');
            if (getData) {
                this.customer_data = getData;
                // this.isDropdownOpen = false;
                this.selectedOption_invoice_type = getData.gst_number ? 'b2b' : 'b2c';
                this.showModal_customer = false;
            }
            this.showModal_customer = false;
        },
        //----table--
        handleEnterKey(index, type, product_name) {
            // Check if filteredProductList has at least one item
            if (type === 'product') {
                if (this.filteredProductList.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectProduct(index, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    this.addNewItemModal();
                }
            }
            if (type === 'customer') {
                if (this.filteredCustomerOptions.length > 0) {
                    this.selectDropdownOption(this.filteredCustomerOptions[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    this.openModal(this.selectCustomer);
                    this.$refs.selectCustomerInput.blur();
                }
            }
        },
        //---arrow on key press
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //--validate GST--
        validateGST() {
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.customer_data.gst_number !== '') {
                // Check if the input matches the expected format
                if (regex.test(this.customer_data.gst_number)) {
                    this.gstValidation = '';
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                }
            }
        },

        //---items--
        //-----filter product--
        filterProducts(is_from_watch) {
            const enteredProductName = this.selected_item.toLowerCase();
            this.productDropdown = true;
            if (this.product.length !== 0 && enteredProductName.length > 1) {
                let existingProducts = [];
                let existingProductCodes = [];
                if (this.items.length > 0) {
                    existingProducts = !this.items.map((item, i) => item.product_name.toLowerCase());
                    existingProductCodes = !this.items.map((item, i) => item.barcode && typeof item.barcode === 'string' ? item.barcode && item.barcode.toLowerCase() : null).filter(code => code !== null);
                }
                this.filteredProductList = this.product.filter(opt => {
                    let isExistingName = false;
                    let isExistingCode = false;
                    if (existingProducts.length > 0 && opt.products && opt.products.product_name) {
                        isExistingName = existingProducts.includes(opt.products.product_name.toLowerCase());
                    }
                    if (existingProductCodes.length > 0 && opt.barcodes.barcode && opt.barcodes.barcode) {
                        isExistingCode = existingProductCodes.includes(opt.barcodes.barcode.toLowerCase());
                    }
                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.products && opt.products.product_name ? opt.products.product_name.toLowerCase().includes(enteredProductName) : false;
                    const codeMatch = opt.barcodes && opt.barcodes.barcode ? opt.barcodes.barcode.toLowerCase().includes(enteredProductName) : false;
                    // return nameMatch || codeMatch;
                    return (!isExistingName || !isExistingCode) && (nameMatch || codeMatch);
                });
                if (this.filteredProductList.length === 0 && this.pagination.product && Number(this.pagination.product.current_page) < this.pagination.product.last_page && !is_from_watch) {
                    // this.getProductListData(Number(this.pagination.product.current_page) + 1, 1000);
                    this.getProductListData(1, 2);
                }
            }
        },
        //---add product option controll--
        findExistItem() {
            const enteredProductName = this.selected_item.toLowerCase();
            if (this.product.length !== 0 && enteredProductName.length > 1) {
                const existingProducts = this.product.map(item => item.products.product_name.toLowerCase());
                const existingProductCodes = this.product.map(item => item.barcodes && item.barcodes.barcode.toLowerCase());

                // Use a separate array to store existing product names and codes
                const existingNamesAndCodes = [...existingProducts, ...existingProductCodes];

                // Check if the entered product name or product code matches any existing product name or product code
                const isExisting = existingNamesAndCodes.includes(enteredProductName);
                // console.log(isExisting, 'Waht happening....!');
                return !isExisting;
            }
            return true;
        },
        selectProduct(index, selectedProduct) {
            // console.log(this.invoice_setting, 'EEEEEE');
            let selectIndex = null;
            //--validate duplicate--
            // console.log('validation is done...1', selectedProduct);
            let validateDuplicate = this.items.map((item, i) => {
                if (selectedProduct.products && item.product_name === selectedProduct.products.product_name && item.product_id === selectedProduct.product_id && item.barcode_id === selectedProduct.barcode_id) {
                    selectIndex = i;
                    return true;
                }
            });
            if (selectIndex !== null && selectIndex >= 0) {
                // console.log(validateDuplicate, 'what happening..!')
                let total_stock = this.items[selectIndex].total_qty;
                if (total_stock > this.items[selectIndex].qty || (this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].minus_sale === '1')) {
                    this.items[selectIndex] = { ...this.items[selectIndex], qty: this.items[selectIndex].qty + 1 };
                    this.selected_item = '';
                    // console.log(this.items, 'object pushed successfully...!');
                    this.updateTotal(selectIndex);
                    this.calculateTax(selectIndex);
                    this.is_updated = true;
                } else {
                    this.minus_sales_open = true;
                    // alert('Low stock alert: Please enable the minus sale option in settings to continue allowing sales that minus stock');
                }
                //---reset---
                validateDuplicate = [];
                selectIndex = null;
            } else {
                // console.log(selectedProduct, 'What happening...!', selectedProduct.sales_price * (selectedProduct.gst_value / 100));
                let get_data = {
                    company_id: this.companyId01,
                    product_type: selectedProduct.products.product_type ? selectedProduct.products.product_type : 'Product',
                    product_name: selectedProduct.products.product_name,
                    total_qty: selectedProduct.total_qty,
                    description: '',
                    price: selectedProduct.sales_price,
                    hsn_code: selectedProduct.products.hsn_code,
                    taxvalue: selectedProduct.gst_value,
                    tax_name: selectedProduct.tax_name ? selectedProduct.tax_name : 'GST',
                    tax_type: selectedProduct.gst_type,
                    qty: 1,
                    tax: selectedProduct.sales_price * (selectedProduct.gst_value / 100),
                    // total: selectedProduct.gst_type,
                    discount_data: { type: selectedProduct.discount_type, value: selectedProduct.discount },
                    discount: selectedProduct.discount_type === 'Percentage' ? selectedProduct.sales_price * (selectedProduct.discount / 100) : selectedProduct.discount,
                    product_id: selectedProduct.product_id,
                    barcode_id: selectedProduct.barcode_id,
                    product_code: selectedProduct.products.product_code,
                    serial_no: ''
                };
                // selectedProduct.qty = 1;
                // selectedProduct.discount = 1;
                this.items.push(get_data);
                this.selected_item = '';
                // console.log(this.items, 'object pushed successfully...!');
                // this.updateTotal(this.items.length - 1);
                // this.calculateTax(this.items.length - 1);
                this.is_updated = true;
            }
            // this.updatePrice(this.items.length - 1);
            this.productDropdown = false;
            // this.productDropdown = false;           
            this.filteredProductList = []; // Clear the filtered list after selecting a product.
            // console.log(this.$refs['productHsnCode' + index][0], 'waht happening..!');
            // this.$refs['productHsnCode' + index][0].focus();

            this.focusItemFields();
            this.updateTotal();
            if (this.pagination && this.pagination.product && Number(this.pagination.product.current_page) !== 1) {
                // this.getProductListData(Number(this.pagination.product.current_page) + 1, 1000);
                this.getProductListData(1, 2);

            }
        },
        //--always focus item fields--
        focusItemFields() {
            this.$nextTick(() => {
                if (this.$refs.selectItemField) {
                    this.$refs.selectItemField.focus();
                }
            });
        },

        //---blur item dropdown--
        blurItemDropdown() {
            this.isFormFocus.selected_item = false;
            if (!this.isMouseInOption) {
                this.productDropdown = false;
            }
        },
        mouseIsOnOption(value) {
            this.isMouseInOption = value;
        },
        //----calculate--
        validateQuantity(index) {
            let total_stock = this.items[index].total_qty;
            let qty = this.items[index].qty;
            if (qty < 0) {
                this.items[index].qty = 0;
            }
            // console.log(index, 'Waht happening...!!!!!!')
            if (this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].minus_sale === '1') {
                if (!(total_stock > this.items[index].qty)) {
                    // alert('low stock');
                    this.message = 'Item stock is Low..!';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            }
            else {
                if (!(total_stock > this.items[index].qty)) {
                    this.items[index].qty = total_stock;
                    this.minus_sales_open = true;
                    // alert('Low stock alert: Please enable the minus sale option in settings to continue allowing sales that minus stock');
                }
            }
            this.calculateTotal(index);

        },
        addQuantity(index) {
            // console.log(index, 'WWWWW');
            if (index >= 0) {
                let total_stock = this.items[index].total_qty;
                if (total_stock > this.items[index].qty) {
                    this.items[index].qty = this.items[index].qty + 1;
                    this.calculateTotal(index);
                    this.is_updated = true;
                } else {
                    if (this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].minus_sale === '1') {
                        this.items[index].qty = this.items[index].qty + 1;
                        this.is_updated = true;
                        if (this.items[index].qty === total_stock + 1) {
                            // alert('low stock alert');
                            this.message = 'Item stock is Low..!';
                            this.type_toaster = 'warning';
                            this.show = true;
                        }
                    } else {
                        this.minus_sales_open = true;
                        // alert('Low stock alert: Please enable the minus sale option in settings to continue allowing sales that minus stock');
                        // this.items[index].qty = this.items[index].qty;
                    }
                    this.calculateTotal(index); //-----based on minus sale do alert
                }
            } else {
                this.formData.qty = this.formData.qty + 1;
                this.updateFormDataTotal('qty');
            }
        },
        removeQuantity(index) {
            if (index >= 0 && this.items[index].qty > 0) {
                this.items[index].qty = this.items[index].qty - 1;
                this.calculateTotal(index);
                this.is_updated = true;
            } else if (index === undefined && this.formData.qty > 0) {
                this.formData.qty = this.formData.qty - 1;
                this.updateFormDataTotal('qty')
            }
        },
        updateTotal() {
            // Iterate through each item to update totals and tax values
            this.items.forEach((item) => {
                // Calculate total based on price and tax
                // let item_total_value = item.qty * item.price;
                if (item.tax_type === "Exclusive") {
                    // Calculate tax value    
                    item.discount = item.discount_data && item.discount_data.type === 'Percentage' ? ((item.qty * item.price) * ((1 * item.discount_data.value) / 100)).toFixed(2) : item.discount_data ? item.discount_data.value : 0;
                    let make_price = (item.qty * item.price) - (1 * item.discount);
                    let qty_price = 1 * make_price;
                    let tax_amount = qty_price * (1 + ((1 * item.taxvalue) / 100));
                    item.tax = (tax_amount - qty_price).toFixed(2);
                    //--get total
                    item.total = (tax_amount * 1).toFixed(2);

                } else {
                    item.discount = item.discount_data && item.discount_data.type === 'Percentage' ? ((item.qty * item.price) * ((1 * item.discount_data.value) / 100).toFixed(2)) : item.discount_data ? item.discount_data.value : 0;
                    let make_price = (item.qty * item.price) - (1 * item.discount);
                    item.total = (make_price * 1).toFixed(2);
                    // Calculate tax value
                    item.tax = ((make_price) - ((make_price) / (1 + (1 * item.taxvalue) / 100))).toFixed(2);
                }
                // this.is_updated = true;
            });

            // Update sub-total and grand total
            this.subTotal = this.calculateSubTotal();
            // this.grandTotal = this.calculateGrandTotal();
            // Update balance if payment is entered
            if (this.paymentData.length > 0 && this.paymentData[0].payment_amount !== '') {
                this.updateBalance();
            }

        },
        handleSubtotalChange(index) {
            // Trigger the price update when the subtotal changes
            this.updatePrice(index);
        },
        updatePrice(index) {
            let findData = this.items.find((opt, i) => i === index);
            let price_per_qty = findData.total / findData.qty;
            let tax_per_qty = price_per_qty / (1 + (findData.taxvalue / 100));
            // console.log(price_per_qty, 'Price', tax_per_qty, 'for tax data..!', findData, 'What happening..1');
            if (findData.tax_type === "Exclusive") {
                this.items[index].price = (price_per_qty / (1 + (findData.taxvalue / 100))).toFixed(2);
                // Calculate tax value
                this.items[index].tax = (price_per_qty - tax_per_qty).toFixed(2);
            } else {
                let tax_per_qty01 = price_per_qty * (1 + (findData.taxvalue / 100));
                this.items[index].price = (price_per_qty).toFixed(2);
                // Calculate tax value
                this.items[index].tax = (tax_per_qty01 - price_per_qty).toFixed(2);
            }
            this.items[index].discount = findData.discount_data.type === 'Percentage' ? findData.total * (findData.discount_data.value / 100).toFixed(2) : findData.discount_data.value;
            // findData.price = (findData.total / (1 + findData.tax / 100)).toFixed(2);
            // findData.taxvalue = (findData.total - findData.price).toFixed(2);
            this.is_updated = true;
        },

        calculateSubTotal() {
            const subTotal = this.items.reduce((sum, item) => sum + Number(item.total), 0);
            this.calculateTotalTax();
            // console.log('Sub Total:', subTotal);
            return subTotal;
        },

        calculateGrandTotal() {
            const subTotal = this.calculateSubTotal();

            if (this.discount !== '') {
                // Check if the discount value is in percentage format (e.g., "100%")
                if (this.discountType === '%') {
                    // Extract the numeric value from the percentage
                    const discountPercentage = parseFloat(this.discount);

                    // Calculate the discounted amount based on the percentage
                    const discountAmount = (subTotal * discountPercentage) / 100;

                    // Apply the discount to the subTotal
                    return (subTotal - discountAmount).toFixed();
                } else {
                    // If the discount value is not in percentage format, apply it directly
                    return (subTotal - Number(this.discount)).toFixed();
                }
            } else {
                return subTotal;
            }
        },

        calculateTotalTax() {
            this.totalTaxValue = this.items.reduce((sum, item) => sum + Number(item.taxvalue), 0);
        },
        //--calculate total
        calculateTotal() {
            // console.log('UUUU', this.paymentData, 'RRRRRRRRRRRR');
            // if (event.key === 'Enter') {
            this.updateTotal();
            // }
        },
        //---calculate discount value--
        calculateDiscount() {
            // this.grandTotal = this.calculateGrandTotal();
            this.calculateShipping();
            // Update balance if payment is entered
            if (this.paymentData.length > 0 && this.paymentData[0].payment_amount !== '') {
                this.updateBalance();
            }
        },
        calculateTax() {
            this.updateTotal();
            // this.grandTotal = this.calculateGrandTotal();
            this.subTotal = this.calculateSubTotal();

            if (this.paymentData.length > 0 && this.paymentData[0].payment_amount !== '') {
                this.updateBalance();
            }
        },
        updateBalance(rowIndex) {
            // console.log(this.paymentData, 'what happening....');
            let totalPaid = 0;
            if (this.paymentData.length > 0 && this.paymentData[0].payment_amount !== '') {
                totalPaid = this.paymentData.reduce((sum, row) => sum + row.payment_amount, 0);

            }
            if (totalPaid <= this.grandTotal) {
                this.formValues.balance = Math.round(this.grandTotal - totalPaid);
            } else {
                this.formValues.balance = 0;
                this.formValues.return_amount = Math.round(totalPaid - this.grandTotal);
            }
        },
        //---remove item--
        //-----delete record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null && this.deleteType !== 'payment' && this.deleteType !== 'custom') {
                this.items.splice(this.deleteIndex, 1);
                this.updateTotal();
            } else if (this.deleteIndex !== undefined && this.deleteIndex !== null && this.deleteType === 'payment') {
                // Remove the row at the specified index
                this.paymentData.splice(this.deleteIndex, 1);

                // After removing the row, update the balances and totals
                this.updateBalance(this.deleteIndex);
            } else {
                this.form_fields.splice(this.deleteIndex, 1);
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
            this.deleteType = null;
        },

        deleteRow(index) {
            // this.formData.selectedTax.splice(index, 1);
            this.deleteIndex = index;
            this.open_confirmBox = true;
            this.is_updated = true
        },
        //--move up & down functions---
        moveUp(index) {
            if (index > 0) {
                let temp = this.items[index - 1];
                this.items.splice(index - 1, 1, this.items[index]);
                this.items.splice(index, 1, temp);
            }
        },
        moveDown(index) {
            if (index < this.items.length - 1) {
                let temp = this.items[index + 1];
                this.items.splice(index + 1, 1, this.items[index]);
                this.items.splice(index, 1, temp);
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.deleteIndex = null;
            this.deleteType = null;
        },
        //---terms modal box---
        openTermsDialog() {
            this.open_termsModel = true;
        },
        closeTermsDialog(data) {
            if (data) {
                // console.log(data, 'Terms updated successfully....!');
                this.is_updated = true;
            }
            this.open_termsModel = false;
            this.focusItemFields();
        },
        //---item tax and discount modal box---
        openItemTaxDiscountDialog(data, index, type, from) {
            this.type_tax_or_discount = from;
            if (data && index >= 0 && type !== 'formData') {
                this.selected_item_data = data;
                this.open_item_tax_discount = true;
                this.open_item_tax_discount_index = index;
            } else if (type === 'formData') {
                this.selected_item_data = this.formData;
                this.open_item_tax_discount_index = null;
                this.open_item_tax_discount = true;
            }
        },
        closeItemTaxDiscountDialog(data) {
            // && data.discount_type && this.open_item_tax_discount_index !== null
            if (this.open_item_tax_discount_index >= 0 && this.open_item_tax_discount_index !== null) {
                if (data) {
                    // console.log(data, 'Item tax and discount updaed succesfully');
                    this.items[this.open_item_tax_discount_index].tax_type = data.tax_type ? data.tax_type : 'Inclusive';
                    this.items[this.open_item_tax_discount_index].taxvalue = data.tax_value >= 0 ? data.tax_value : 0;
                    this.items[this.open_item_tax_discount_index].tax_name = data.tax_name ? data.tax_name : '';
                    if (this.items[this.open_item_tax_discount_index].discount_data) {
                        this.items[this.open_item_tax_discount_index].discount_data.type = data.discount_type ? data.discount_type : 'Fixed';
                        this.items[this.open_item_tax_discount_index].discount_data.value = data.discount_value >= 0 ? data.discount_value : 0;
                    } else {
                        this.items[this.open_item_tax_discount_index].discount_data = { type: data.discount_type ? data.discount_type : 'Fixed', value: data.discount_value >= 0 ? data.discount_value : 0 };
                    }
                    this.updateTotal();
                }
                this.open_item_tax_discount = false;
                this.focusItemFields();
                this.is_updated = true;
            } else if (data) {
                this.formData.tax_type = data.tax_type ? data.tax_type : 'Inclusive';
                this.formData.taxvalue = data.tax_value >= 0 ? data.tax_value : 0;
                this.formData.tax_name = data.tax_name ? data.tax_name : '';
                if (this.formData.discount_data) {
                    this.formData.discount_data.type = data.discount_type ? data.discount_type : 'Fixed';
                    this.formData.discount_data.value = data.discount_value >= 0 ? data.discount_value : 0;
                } else {
                    this.formData.discount_data = { type: data.discount_type ? data.discount_type : 'Fixed', value: data.discount_value >= 0 ? data.discount_value : 0 };
                }
                this.updateFormDataTotal();
                this.open_item_tax_discount = false;
            }
            if (this.$refs.formDataTotal && this.focustaxdiscount) {
                this.$refs.formDataTotal.focus();
                this.open_item_tax_discount = false;
            }
            this.open_item_tax_discount = false;
        },
        //---overall discount modal box---
        openOverallDiscountDialog() {
            this.open_overall_discount = true;
        },
        closeOverallDiscountDialog(data) {
            // console.log(data, 'Waht happening...!!!!');
            if (data && data.value >= 0) {
                this.over_all_discount = data;
            }
            this.open_overall_discount = false;
            this.focusItemFields();
            this.is_updated = true;
        },
        //----shipping---
        openShippingDialog() {
            this.open_shippingcharges = true;
        },
        closeShippingDialog(data) {
            if (data && (data.shipping >= 0 || data.shipping_type || data.cod)) {
                // console.log(data, 'Shipping charges added successfully..!');
                this.shipping_details.shipping = data.shipping;
                this.shipping_details.shipping_type = data.shipping_type;
                this.shipping_details.cod = data.cod;
            }
            this.open_shippingcharges = false;
            this.focusItemFields();
            this.is_updated = true;
        },
        //---payment---
        openPaymentModal(type) {
            // console.log(type, 'WWWWWWWWWWWWWWWWWWWWWWWWWWWW');
            this.payment_type = type;
            this.open_paymentModal = true;
        },
        closePaymentModal(data) {
            // console.log(data, 'RRRRRRRRRRRRRRRRRRR', this.typeOfInvoice, 'EEEEEEEEEEEEE');
            if (data && data.type) {
                this.formValues = { ...this.formValues, ...data.form_data };
                this.paymentData = data.payment_data;
                this.getInvoiceNumAndType();
                this.is_updated = true;
                if (data.type === 'print' && this.items.length > 0 && this.customer_data !== null && this.typeOfInvoice === 'sales') {
                    // this.saveData('Success');
                    this.printing = true;
                    this.saveData('Success');
                }
                else if (this.typeOfInvoice === 'sales' || this.typeOfInvoice === 'services') {
                    this.saveData('Success');
                    // console.log('save the data..!');
                    // this.$router.go(-1);
                }
                else if (this.typeOfInvoice === 'proforma') {
                    this.saveProforma(data.type ? data.type : 'save');
                }
            }
            this.open_paymentModal = false;
            this.focusItemFields();
        },
        //---add new item--
        addNewItemModal() {
            this.open_add_newItem = true;
            this.productDropdown = false;
        },
        closeItemModal(data) {
            if (data && data.id) {
                this.product.push(data);
                this.selectProduct(this.items.length, data);
            }
            this.open_add_newItem = false;
            this.focusItemFields();
        },
        //get product list---
        getProductListData(page, per_page) {
            axios.get('/products_details', { params: { company_id: this.companyId01, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'items');
                    // this.product = response.data.data;
                    // this.pagination.product = response.data.pagination;
                    let pagination_data = response.data.pagination;
                    this.fetchItemList(pagination_data && pagination_data.total ? Number(pagination_data.total) + 50 : 2000);

                })
                .catch(error => {

                    console.error('Error', error);
                })
        },
        //get product list---
        getCustomerListData(page, per_page) {
            axios.get('/customers', { params: { company_id: this.companyId01, page: page, per_page: per_page } })
                .then(response => {
                    console.log(response.data, 'Customer');
                    this.customers = response.data.data;
                    this.pagination.customer = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //get invoice setting---
        getInvoiceData() {
            axios.get('/invoice_settings', { params: { company_id: this.companyId01 } })
                .then(response => {
                    // console.log(response.data, 'invoice setting');
                    if (response.data.data.length > 0) {
                        this.invoice_setting = response.data.data;
                    } else {
                        this.invoice_setting = [];
                        this.$router.push({ path: '/setting', query: { page: 'Sales' } });
                        localStorage.setItem('previousView', JSON.stringify({ view: 2 }));
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---get invoice number-----        
        getInvoiceNumberData() {
            if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                axios.get('/invoice-last-number', { params: { company_id: this.companyId01 } })
                    .then(response => {
                        // console.log(response.data, 'invoice number');
                        this.response_data_number = response.data.data;
                        // console.log(this.formValues.invoice_group >= 0, 'What happening...!', this.formValues.invoice_group)
                        if (this.formValues.invoice_group >= 0) {
                            this.invoice_num = this.formValues.invoice_group === 0 ? this.response_data_number.last_invoice_number_group_a + 1 : this.response_data_number.last_invoice_number_group_b + 1;
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            } else if (this.typeOfInvoice !== 'proforma') {
                axios.get('/estimations', { params: { company_id: this.companyId01, page_page: 1, page: 1 } })
                    .then(response => {
                        // console.log(response.data, 'estimation');
                        this.response_data_number = response.data.pagination;
                        // console.log(this.formValues.invoice_group >= 0, 'What happening...!')
                        this.invoice_num = this.response_data_number.total + 1;
                        // if (this.formValues.invoice_group >= 0) {
                        //     this.invoice_num = this.formValues.invoice_group === 0 ? this.response_data_number.last_invoice_number_group_a + 1 : this.response_data_number.last_invoice_number_group_b + 1;
                        // }
                        // console.log(this.invoice_num, 'What about');
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })

            } else {
                axios.get('/proforma_invoices', { params: { company_id: this.companyId01, page_page: 1, page: 1 } })
                    .then(response => {
                        // console.log(response.data, 'estimation');
                        this.response_data_number = response.data.pagination;
                        this.invoice_num = this.response_data_number.total + 1;

                    })
                    .catch(error => {
                        console.error('Error', error);
                    })

            }
        },

        holdTheInvoice() {
            // this.saveData('hold');
            // console.log('change the data');
            if (Object.keys(this.customer_data).length > 0 && this.items.length > 0 && this.formValues.invoice_group >= 0) {
                this.open_loader = true;
                let get_data = {
                    customer_data: this.customer_data,
                    initital_code: this.initital_code,
                    invoice_type: this.selectedOption_invoice_type,
                    iswhatsapp: this.companywhatsapp ? this.iswhatsapp : false,
                    issms: this.issms,
                    form_values: this.formValues,
                    payment_mode: this.paymentData,
                    shipping_details: this.shipping_details
                };
                axios.post('/hold_invoices', { company_id: this.companyId01, sales_items_data: JSON.stringify(this.items), data: JSON.stringify(get_data), type: this.typeOfInvoice === 'sales' ? 'Product' : this.typeOfInvoice === 'services' ? 'Services' : 'Direct_Services' })
                    .then(response => {
                        // console.log(response.data);
                        this.open_loader = false;
                        this.openMessage(response.data.message);
                        this.customer_data = {};
                        this.items = [];
                        this.shipping_details = {};
                        this.formValues = { invoice_group: 0 }
                        this.refreshHold();
                        this.fetchHoldsList();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                        this.openMessage(error.response.data.message);
                    })
            } else {
                this.message = "Please fill the customer and select items..!";
                this.open_message = true
            }
        },
        getInvoiceNumAndType() {
            this.collect_invoice_data = { invoice_number: this.initital_code + '' + this.invoice_num, invoice_type: this.selectedOption_invoice_type, shipping_type: this.shipping_details.shipping_type, cod: this.shipping_details.cod };
        },
        //--serial no validation--
        parseNestedJson(value) {
            try {
                let parsedValue = value;
                while (typeof parsedValue === 'string' && this.isJsonString(parsedValue)) {
                    parsedValue = JSON.parse(parsedValue);
                }
                return parsedValue;
            } catch (e) {
                return value; // Return the original value if parsing fails
            }
        },
        isJsonString(str) {
            try {
                JSON.parse(str);
                return true;
            } catch (e) {
                return false;
            }
        },
        //---get by id data---
        getDataById() {
            if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                axios.get(`/sales/${this.$route.query.invoice_no}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.data, 'get response..!', this.typeOfInvoice, 'RRRRRRRRRRRRRRRRRRRRRRR');
                        this.exist_data = response.data.data;
                        this.initital_code = this.exist_data.invoice_prefix;
                        this.invoice_num = this.exist_data.invoice_no;
                        this.formValues.invoice_group = this.exist_data.invoice_group;
                        // console.log(this.formValues.invoice_group, 'EEEEEEE WWWWW');
                        this.selectedOption_invoice_type = this.exist_data.invoice_to;
                        // this.typeOfInvoice = this.exist_data.invoice_type;
                        this.over_all_discount = { value: this.exist_data.discount >= 0 ? this.exist_data.discount : 0, type: this.exist_data.discount_type ? this.exist_data.discount_type : 'Fixed' };
                        this.formValues.balance = this.exist_data.due_amount;
                        // this.paymentData = this.exist_data.payment_mode ? JSON.parse(this.exist_data.payment_mode) : '';
                        this.paymentData = this.exist_data.sales_payment;
                        this.sameAsBilling = this.exist_data.is_billaddress == 1 ? true : false;
                        this.shipping_detail = this.exist_data.shipping_address ? JSON.parse(this.exist_data.shipping_address) : {};

                        this.items = this.exist_data.sales_item_data.map(item => ({
                            ...item,
                            product_type: 'Product',
                            // serial_no: this.parseNestedJson(item.serial_no),
                        }));
                        if (this.exist_data.customer.id) {
                            this.getCustomerDataById(this.exist_data.customer.id);
                        }
                        // console.log(this.typeOfInvoice, 'EEEEEEEEEEEEEEEEEEEEEEE');
                        if (this.typeOfInvoice === 'sales' || this.typeOfInvoice === 'services' || this.typeOfInvoice === 'direct_services') {
                            const parsedServiceItems = JSON.parse(this.exist_data.service_items);
                            if (Array.isArray(parsedServiceItems)) {
                                this.items = [...parsedServiceItems, ...this.items];
                            }
                        }
                        // Loop through each object in the array
                        this.items.forEach((item, index) => {
                            // Check if the item has a discount_data key and its value is an object
                            if (item.discount_data) {
                                // Stringify the discount_data object
                                item.discount_data = JSON.parse(item.discount_data);
                            }
                            if (item.serial_no) {
                                // Stringify the serial_no object
                                item.serial_no = JSON.parse(item.serial_no);
                            }
                        });
                        this.shipping_details.shipping = this.exist_data.shipping;

                        this.get_all_data.shipping = this.exist_data.shipping;
                        this.shipping_details.shipping_type = this.exist_data.shipping_type;
                        this.shipping_details.cod = this.exist_data.cod;
                        this.customer_data = this.exist_data.customer;
                        this.get_all_data.total_qty = this.exist_data.total_qty;
                        this.get_all_data.sub_total = this.exist_data.sub_total;
                        this.get_all_data.grand_total = this.exist_data.grand_total;
                        this.formValues.balance = this.exist_data.balance_amount;
                        this.formValues.return = this.exist_data.return_amount;
                        this.formValues.note = this.exist_data.payment_notes;
                        this.get_all_data.grand_total = this.exist_data.payment_amount;

                        if (this.exist_data && this.exist_data.header_custom && this.exist_data.header_custom !== '') {
                            let custom_data = JSON.parse(this.exist_data.header_custom);
                            this.form_fields = Array.isArray(custom_data) && custom_data.length > 0 ? custom_data : [];
                        }
                        this.updateTotal();
                        // console.log(this.items, 'list of data is...!');
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            } else if (this.typeOfInvoice === 'estimation') {
                this.getEstimationData();
            }
            else if (this.typeOfInvoice === 'proforma') {
                this.getProformaData();
            }
        },
        //---get by estimation---
        getEstimationData() {
            axios.get(`/estimations/${this.$route.query.est_no}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'estimation by edit..!');
                    this.exist_data = response.data.data;
                    let string01 = this.exist_data.estimate_num;
                    let [prefix, number] = string01.match(/([A-Za-z\/]+)(\d+)/).slice(1);
                    if (this.typeOfInvoice === 'estimation') {
                        this.initital_code = prefix;
                        this.invoice_num = number;
                    }
                    if (this.exist_data.customers && this.exist_data.customers.id) {
                        this.getCustomerDataById(this.exist_data.customers.id);
                    }
                    this.get_all_data.grand_total = this.exist_data.grand_total;
                    this.items = JSON.parse(this.exist_data.items);
                    this.items.forEach((item, index) => {
                        // Check if the item has a discount_data key and its value is an object
                        if (typeof item.discount_data === 'string') {
                            // Stringify the discount_data object
                            item.discount_data = JSON.parse(item.discount_data);
                        }
                        if (typeof item.serial_no === 'string' && item.serial_no !== '') {
                            // console.log(item.serial_no, 'What happening...!');
                            // Stringify the serial_no object
                            item.serial_no = JSON.parse(item.serial_no);
                        }
                    });
                    this.customer_data = this.exist_data.customers;
                    // this.iswhatsapp = this.exist_data.iswhatsapp;
                    // this.issms = this.exist_data.issms;
                    let parse_data = JSON.parse(this.exist_data.data);
                    // console.log(parse_data.shipping, 'RRRRR');
                    if (parse_data) {
                        this.shipping_details = parse_data.shipping ? parse_data.shipping : {};
                        this.over_all_discount = parse_data.over_all_discount;
                        this.selectedOption_invoice_type = parse_data.invoice_to;
                    }
                    this.sameAsBilling = this.exist_data.is_billaddress == 1 ? true : false;
                    this.shipping_detail = this.exist_data.shipping_address ? JSON.parse(this.exist_data.shipping_address) : {};
                    this.updateTotal();
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---get proforma data--
        getProformaData() {
            axios.get(`/proforma_invoices/${this.$route.query.proforma_no}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'estimation by edit..!');
                    this.exist_data = response.data.data;
                    let string01 = this.exist_data.proforma_no;
                    let [prefix, number] = string01.match(/([A-Za-z\/]+)(\d+)/).slice(1);
                    if (this.typeOfInvoice === 'proforma') {
                        this.initital_code = prefix;
                        this.invoice_num = number;
                    }
                    if (this.exist_data.customers && this.exist_data.customers.id) {
                        this.getCustomerDataById(this.exist_data.customers.id);
                    }
                    this.get_all_data.grand_total = this.exist_data.grand_total;
                    this.items = JSON.parse(this.exist_data.items);
                    this.items.forEach((item, index) => {
                        // Check if the item has a discount_data key and its value is an object
                        if (typeof item.discount_data === 'string') {
                            // Stringify the discount_data object
                            item.discount_data = JSON.parse(item.discount_data);
                        }
                        if (typeof item.serial_no === 'string' && item.serial_no !== '') {
                            // console.log(item.serial_no, 'What happening...!');
                            // Stringify the serial_no object
                            item.serial_no = JSON.parse(item.serial_no);
                        }
                    });
                    // console.log('this is proforma data');

                    this.paymentData = this.exist_data.payment_mode ? JSON.parse(this.exist_data.payment_mode) : [];
                    this.customer_data = this.exist_data.customers;
                    // this.iswhatsapp = this.exist_data.iswhatsapp;
                    // this.issms = this.exist_data.issms;
                    let parse_data = JSON.parse(this.exist_data.data);
                    // console.log(parse_data.shipping, 'RRRRR');
                    if (parse_data) {
                        this.shipping_details = parse_data.shipping ? parse_data.shipping : {};
                        this.over_all_discount = parse_data.over_all_discount;
                        this.selectedOption_invoice_type = parse_data.invoice_to;
                    }
                    this.sameAsBilling = this.exist_data.is_billaddress == 1 ? true : false;
                    this.shipping_detail = this.exist_data.shipping_address ? JSON.parse(this.exist_data.shipping_address) : {};
                    this.updateTotal();
                })
                .catch(error => {
                    console.error('Error', error);
                })

        },
        getCustomerDataById(id) {
            axios.get(`/customers/${id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data.data, 'Get customers...!!!');
                    this.customer_data = response.data.data;
                })
                .catch(error => {
                    console.error('Error data:', error);
                })
        },
        //---validate invoice type-----
        validateInvoiceType() {
            if (this.customer_data && Object.keys(this.customer_data).length > 0 && this.selectedOption_invoice_type === 'b2b') {
                if (this.customer_data.gst_number && this.customer_data.gst_number !== '') {
                    return true;
                } else {
                    return false;
                }
            } else {
                return true;
            }
        },
        saveData(type) {
            // Loop through each object in the array
            if (Object.keys(this.customer_data).length > 0 && this.validateInvoiceType() && this.items.length > 0 && this.formValues.invoice_group >= 0 && this.validateshipping()) {
                this.open_loader = true;
                let items_data = JSON.parse(JSON.stringify(this.items));
                items_data.forEach(item => {
                    // Check if the item has a discount_data key and its value is an object
                    if (item.discount_data && typeof item.discount_data === 'object') {
                        // Stringify the discount_data object
                        item.discount_data = JSON.stringify(item.discount_data);
                    }

                    if (item.serial_no && typeof item.serial_no === 'object') {
                        // Stringify the serial_no object
                        item.serial_no = JSON.stringify(item.serial_no);
                    }
                    if (!item.notes) {
                        item.notes = '';
                    }
                    if (!item.taxvalue && item.taxvalue !== 0) {
                        item.taxvalue = 0;
                    }
                    if (!item.discount && item.discount !== 0) {
                        item.discount = 0;
                    }
                });
                if (this.paymentData.length === 0) {
                    // this.paymentData = [{}];
                    // this.paymentData[0].payment_date = this.getCurrentDateTime();
                    // this.paymentData[0].payment_type = '';
                    // this.paymentData[0].payment_amount = 0;
                    this.formValues.balance = this.get_all_data.grand_total;
                } else {
                    if (Array.isArray(this.paymentData) && this.paymentData.length > 0) {
                        this.paymentData = this.paymentData.filter(opt => opt.id !== undefined || (opt.payment_amount > 0));
                    }
                }
                //console.log(this.customer_data, 'EEEEEEE');
                // Separate items into two arrays based on product_type
                const servicesItems = items_data.filter(item => item.product_type === 'Services' || item.product_id === undefined);
                const otherItems01 = items_data.filter(item => item.product_type !== 'Services' && item.product_id !== undefined);
                //---remove key---
                const otherItems = otherItems01.map(item => {
                    // Destructure the object to remove the specified keys
                    const { product_type, total_qty, ...rest } = item;
                    // Check if barcode_id exists, if not, add it with an empty string value
                    const newItem = { barcode_id: item.barcode_id ? item.barcode_id : "", ...rest };
                    return newItem;
                });
                let invoice_data = {
                    invoice_prefix: this.initital_code,
                    invoice_no: this.invoice_num,
                    customer_id: this.customer_data.id,
                    company_id: this.companyId,
                    discount: this.over_all_discount !== null && this.over_all_discount.value >= 0 ? this.over_all_discount.value : 0,
                    discount_type: this.over_all_discount !== null ? this.over_all_discount.type : 'Fixed',
                    due_amount: this.formValues.balance,
                    invoice_id: this.initital_code + '' + this.invoice_num,
                    invoice_to: this.selectedOption_invoice_type,
                    invoice_type: this.typeOfInvoice === 'sales' ? 'Product' : this.typeOfInvoice === 'services' ? 'Services' : 'Direct_Services',
                    // payment_mode: JSON.stringify(this.paymentData),
                    shipping: this.get_all_data.shipping,
                    invoice_group: this.formValues.invoice_group,
                    // shipping_type: this.shipping_details.shipping_type,
                    // cod: this.shipping_details.cod,
                    status: type,
                };
                let sales_data = {
                    invoice_id: this.initital_code + '' + this.invoice_num,
                    invoice_to: this.selectedOption_invoice_type,
                    invoice_type: this.typeOfInvoice === 'sales' ? 'Product' : this.typeOfInvoice === 'services' ? 'Services' : 'Direct_Services',
                    discount: this.over_all_discount !== null && this.over_all_discount.value >= 0 ? this.over_all_discount.value : 0,
                    discount_type: this.over_all_discount !== null ? this.over_all_discount.type : 'Fixed',
                    due_amount: this.formValues.balance,
                    // payment_mode: this.paymentData,
                    shipping: this.get_all_data.shipping,
                    shipping_type: this.shipping_details.shipping_type,
                    cod: this.shipping_details.cod,
                    client_id: this.customer_data.id,
                    current_date: this.$route.query.type !== 'edit' ? this.getCurrentDate() : this.get_all_data.current_date,
                    company_id: this.companyId,
                    user_id: this.userId,
                    total_qty: this.get_all_data.total_qty,
                    sub_total: this.get_all_data.sub_total,
                    grand_total: this.get_all_data.grand_total,
                    balance_amount: this.formValues.balance,
                    return_amount: this.formValues.return,
                    status: type,
                    payment_notes: this.formValues.note ? this.formValues.note : '',
                    payment_code: '',
                    payment_date: this.getCurrentDate(),
                    payment_type: JSON.stringify(this.paymentData),
                    payment_amount: this.get_all_data.grand_total,
                    customer_id: this.customer_data.id,
                    created_by: this.userId,
                    iswhatsapp: this.companywhatsapp ? this.iswhatsapp : false,
                    issms: this.issms,
                    is_billaddress: this.sameAsBilling ? 1 : 0,
                    header_custom: this.form_fields && this.form_fields.length > 0 ? JSON.stringify(this.form_fields) : '',
                };
                if (this.$route.query.est_no) {
                    sales_data.estimation_id = this.$route.query.est_no;
                }
                if (this.$route.query.proforma_no) {
                    sales_data.proforma_id = this.$route.query.proforma_no;
                }
                if (!this.sameAsBilling && this.shipping_detail) {
                    sales_data.shipping_address = JSON.stringify(this.shipping_detail);
                }
                if (this.$route.query.type !== 'edit' && this.typeOfInvoice === 'sales') {
                    axios.post('/sales', { company_id: this.companyId, user_id: this.userId, invoice_id: this.initital_code + '' + this.invoice_num, sales_data: { ...sales_data, service_items: JSON.stringify(servicesItems), sales_item_data: otherItems, invoice_data: { ...invoice_data }, sales_payment: this.paymentData } })
                        .then(response => {
                            this.fetchApiUpdates();
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (this.getHold_data !== null) {
                                this.deleteHoldRecord();
                            }
                            if (this.printing) {
                                this.$router.push({ name: 'print-preview', query: { invoice_no: response.data.data.id } })
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                            if (this.formValues.balance > 0) {
                                this.updateKeyIsUpdate({ key: 'customer', value: true });
                                this.updateKeyIsUpdate({ key: 'customer_list', value: true });
                            }
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })
                } else if (this.$route.query.type === 'edit' && this.typeOfInvoice === 'sales') {
                    // console.log(servicesItems, 'hello', otherItems);
                    axios.put(`/sales/${this.exist_data.id}`, { company_id: this.companyId, user_id: this.userId, invoice_id: this.initital_code + '' + this.invoice_num, sales_data: { ...sales_data, service_items: JSON.stringify(servicesItems), sales_item_data: otherItems, invoice_data: { ...invoice_data }, sales_payment: this.paymentData } })
                        .then(response => {
                            this.fetchApiUpdates();
                            this.open_loader = false;
                            // this.$router.go(-1);
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (this.getHold_data !== null) {
                                this.deleteHoldRecord()
                            }
                            if (this.printing) {
                                this.$router.push({ name: 'print-preview', query: { invoice_no: response.data.data.id } })
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                            if (this.formValues.balance > 0) {
                                this.updateKeyIsUpdate({ key: 'customer', value: true });
                                this.updateKeyIsUpdate({ key: 'customer_list', value: true });
                            }
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })
                } else if (this.$route.query.type !== 'edit' && this.typeOfInvoice === 'services') {
                    // let splice_data = items_data.splice(0, 1);
                    axios.post('/sales', { company_id: this.companyId, user_id: this.userId, invoice_id: this.initital_code + '' + this.invoice_num, sales_data: { ...sales_data, service_id: this.service_data.id, service_items: JSON.stringify(servicesItems), sales_item_data: otherItems, invoice_data: { ...invoice_data }, sales_payment: this.paymentData } })
                        .then(response => {
                            this.fetchApiUpdates();
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (this.getHold_data !== null) {
                                this.deleteHoldRecord()
                            }
                            // this.$router.go(-1);
                            if (this.printing) {
                                this.$router.push({ name: 'print-preview', query: { invoice_no: response.data.data.id } })
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                            if (this.formValues.balance > 0) {
                                this.updateKeyIsUpdate({ key: 'customer', value: true });
                                this.updateKeyIsUpdate({ key: 'customer_list', value: true });
                            }
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })

                } else if (this.$route.query.type === 'edit' && this.typeOfInvoice === 'services') {
                    // let splice_data = items_data.splice(0, 1);
                    axios.put(`/sales/${this.exist_data.id}`, { company_id: this.companyId, user_id: this.userId, invoice_id: this.initital_code + '' + this.invoice_num, sales_data: { ...sales_data, service_id: this.exist_data.service_id, service_items: JSON.stringify(servicesItems), sales_item_data: otherItems, invoice_data: { ...invoice_data }, sales_payment: this.paymentData } })
                        .then(response => {
                            this.fetchApiUpdates();
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (this.getHold_data !== null) {
                                this.deleteHoldRecord()
                            }
                            // this.$router.go(-1);
                            if (this.printing) {
                                this.$router.push({ name: 'print-preview', query: { invoice_no: response.data.data.id } })
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                            if (this.formValues.balance > 0) {
                                this.updateKeyIsUpdate({ key: 'customer', value: true });
                                this.updateKeyIsUpdate({ key: 'customer_list', value: true });
                            }
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })
                } else if (this.$route.query.type !== 'edit' && this.typeOfInvoice === 'direct_services') {
                    // let splice_data = items_data.splice(0, 1);
                    axios.post('/sales', { company_id: this.companyId, user_id: this.userId, invoice_id: this.initital_code + '' + this.invoice_num, sales_data: { ...sales_data, service_items: JSON.stringify(servicesItems), sales_item_data: otherItems, invoice_data: { ...invoice_data }, sales_payment: this.paymentData } })
                        .then(response => {
                            this.fetchApiUpdates();
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (this.getHold_data !== null) {
                                this.deleteHoldRecord()
                            }
                            // this.$router.go(-1);
                            if (this.printing) {
                                this.$router.push({ name: 'print-preview', query: { invoice_no: response.data.data.id } })
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                            if (this.formValues.balance > 0) {
                                this.updateKeyIsUpdate({ key: 'customer', value: true });
                                this.updateKeyIsUpdate({ key: 'customer_list', value: true });
                            }
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })

                } else if (this.$route.query.type === 'edit' && this.typeOfInvoice === 'direct_services') {
                    // let splice_data = items_data.splice(0, 1);
                    axios.put(`/sales/${this.exist_data.id}`, { company_id: this.companyId, user_id: this.userId, invoice_id: this.initital_code + '' + this.invoice_num, sales_data: { ...sales_data, service_items: JSON.stringify(servicesItems), sales_item_data: otherItems, invoice_data: { ...invoice_data }, sales_payment: this.paymentData } })
                        .then(response => {
                            this.fetchApiUpdates();
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (this.getHold_data !== null) {
                                this.deleteHoldRecord()
                            }
                            // this.$router.go(-1);
                            if (this.printing) {
                                this.$router.push({ name: 'print-preview', query: { invoice_no: response.data.data.id } })
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                            if (this.formValues.balance > 0) {
                                this.updateKeyIsUpdate({ key: 'customer', value: true });
                                this.updateKeyIsUpdate({ key: 'customer_list', value: true });
                            }
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })
                }
            } else {
                this.openMessage(!this.validateInvoiceType() ? 'Please update customer GST Number..!' : this.validateshipping() ? 'Please select the customer, invoice type, and at least one item to successfully complete the invoice.' : 'Please enable the option to use the same address as the shipping address or update the shipping details as needed.');
                this.$emit('updatesalesData');
            }
        },
        updateTotalQty() {
            if ((this.$route.query.type === 'edit' || this.typeOfInvoice === 'services') && this.items.length > 0 && this.product.length > 0) {
                // Loop through each item in items
                this.items.forEach(item => {
                    // Find the corresponding product in newValue based on product_id
                    const product = this.product.find(product => product.product_id === item.product_id);
                    // If product is found and it has total_qty, update item's total_qty
                    if (product && product.total_qty !== undefined) {
                        item.total_qty = product.total_qty;
                    }
                });
            }
        },
        getBack() {
            this.printing = false;
        },
        openSerialNumber(item) {
            this.selected_serial_item = item;
            this.openSerial = true;
        },
        closeSerialModel(data) {
            // if (Array.isArray(data)) {
            // console.log(data, 'WWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWW happening.......');
            if (data) {
                let findIndexData = this.items.findIndex(opt => opt.product_id === this.selected_serial_item.product_id);
                if (findIndexData !== -1) {
                    if (this.typeOfInvoice !== 'estimation') {
                        if (this.items[findIndexData].hasOwnProperty('serial_no')) {
                            // If 'serial_no' property exists, update it with the new data
                            this.items[findIndexData].serial_no = data.serial_no;
                            this.items[findIndexData].notes = data.notes;
                        } else {
                            // If 'serial_no' property doesn't exist, add it and set its value to 'data'
                            this.items[findIndexData].serial_no = data.serial_no;
                            this.items[findIndexData].notes = data.notes;
                            // console.log(this.items[findIndexData], 'how to updated data..!');
                        }
                        this.is_updated = true;
                    }
                    else {
                        this.items[findIndexData].notes = data.notes;
                        this.is_updated = true;
                    }
                }
            }
            this.openSerial = false; // Close the modal
        },
        refreshHold() {
            this.$emit("refresh_hold");
        },
        deleteHoldRecord() {
            if (this.getHold_data !== null) {
                axios.delete(`/hold_invoices/${this.getHold_data.id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // console.log(response.data, 'hold delete');
                        this.updateKeyWithTime('holdlist_update');
                        this.getHold_data = null;
                        this.$emit('refresh_hold');
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }

        },
        //---save estimation---

        saveEstimation(type) {
            if (Object.keys(this.customer_data).length > 0 && this.items && this.items.length > 0 && this.validateshipping()) {
                this.open_loader = true;
                let sent_data = {
                    estimate_num: this.initital_code + '' + this.invoice_num,
                    estimate_type: "Product",
                    grand_total: this.get_all_data.grand_total,
                    status: "0",
                    items: JSON.stringify(this.items),
                    company_id: this.companyId,
                    customer_id: this.customer_data.id,
                    iswhatsapp: this.companywhatsapp ? this.iswhatsapp : false,
                    issms: this.issms,
                    notes: this.formValues.notes ? this.formValues.notes : '',
                    data: JSON.stringify({ shipping: this.shipping_details, over_all_discount: this.over_all_discount, invoice_to: this.selectedOption_invoice_type }),
                    is_billaddress: this.sameAsBilling ? 1 : 0,
                };
                if (!this.sameAsBilling && this.shipping_detail) {
                    sent_data.shipping_address = JSON.stringify(this.shipping_detail);
                }
                if (this.$route.query.type !== 'edit') {
                    sent_data.current_date = this.getCurrentDate();
                    axios.post('/estimations', sent_data)
                        .then(response => {
                            this.fetchApiUpdates();
                            // console.log(response.data, 'What happening in estimation...!');
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (type === 'print') {
                                this.$router.push({ name: 'print-preview', query: { type: 'estimation', est_no: response.data.data.id } });
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })
                }
                else if (this.$route.query.type === 'edit' && this.exist_data.id) {
                    sent_data.notes = this.formValues && this.formValues.notes ? this.formValues.notes : this.exist_data && this.exist_data.notes ? this.exist_data.notes : '';
                    axios.put(`/estimations/${this.exist_data.id}`, sent_data)
                        .then(response => {
                            this.fetchApiUpdates();
                            // console.log(response.data, 'What happening in estimation...!');
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (type === 'print') {
                                this.$router.push({ name: 'print-preview', query: { type: 'estimation', est_no: response.data.data.id } });
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })
                }

            } else {
                this.openMessage(this.validateshipping() ? 'Please select customer or select the items atleast one...!' : 'Please select whether to use the same address for both billing and shipping, or provide a separate shipping address.');
                this.$emit('updatesalesData');
            }
        },
        //---save proforma---
        saveProforma(type) {
            if (Object.keys(this.customer_data).length > 0 && this.items && this.items.length > 0 && this.validateshipping()) {
                this.open_loader = true;
                this.items = this.items.map((opt) => {
                    if (!opt.serial_no) {
                        opt.serial_no = [];
                    }
                    return opt;
                });
                if (Array.isArray(this.paymentData) && this.paymentData.length > 0) {
                    this.paymentData = this.paymentData.filter(opt => opt.id !== undefined || (opt.payment_amount > 0));
                }
                let sent_data = {
                    // current_date: this.getCurrentDate(),
                    proforma_no: this.initital_code + '' + this.invoice_num,
                    grand_total: this.get_all_data.grand_total,
                    status: "0",
                    items: JSON.stringify(this.items),
                    company_id: this.companyId,
                    customer_id: this.customer_data.id,
                    iswhatsapp: this.companywhatsapp ? this.iswhatsapp : false,
                    issms: this.issms,
                    payment_mode: JSON.stringify(this.paymentData),
                    data: JSON.stringify({ shipping: this.shipping_details, over_all_discount: this.over_all_discount, invoice_to: this.selectedOption_invoice_type }),
                    is_billaddress: this.sameAsBilling ? 1 : 0,
                };
                if (this.$route.query.est_no) {
                    sent_data.estimation_id = this.$route.query.est_no;
                }
                if (!this.sameAsBilling && this.shipping_detail) {
                    sent_data.shipping_address = JSON.stringify(this.shipping_detail);
                }
                if (this.$route.query.type !== 'edit') {
                    sent_data.current_date = this.getCurrentDate();
                    axios.post('/proforma_invoices', sent_data)
                        .then(response => {
                            this.fetchApiUpdates();
                            // console.log(response.data, 'What happening in estimation...!');
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (type === 'print') {
                                this.$router.push({ name: 'print-preview', query: { type: 'proforma', proforma_no: response.data.data.id } });
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })
                }
                else if (this.$route.query.type === 'edit' && this.exist_data.id) {
                    axios.put(`/proforma_invoices/${this.exist_data.id}`, sent_data)
                        .then(response => {
                            this.fetchApiUpdates();
                            // console.log(response.data, 'What happening in estimation...!');
                            this.open_loader = false;
                            // this.openMessage(response.data.message);
                            this.message = response.data.message;
                            this.show = true;
                            if (type === 'print') {
                                this.$router.push({ name: 'print-preview', query: { type: 'proforma', proforma_no: response.data.data.id } });
                            } else {
                                // this.$router.go(-1);
                                this.showModalSuccessMsg = true;
                                this.response_data = response.data.data;
                            }
                            this.$emit('is-sales-save', false);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessage(error.response.data.message);
                        })
                }

            } else {
                this.openMessage(this.validateshipping() ? 'Please select customer or select the items atleast one...!' : 'Please select whether to use the same address for both billing and shipping, or provide a separate shipping address.');
                this.$emit('updatesalesData');
            }
        },
        getCurrentDate() {
            // Get current date
            const currentDateObj = new Date();

            const year = currentDateObj.getFullYear();
            const month = String(currentDateObj.getMonth() + 1).padStart(2, '0'); // Adding 1 because months are zero-indexed
            const day = String(currentDateObj.getDate()).padStart(2, '0');
            const hours = String(currentDateObj.getHours()).padStart(2, '0');
            const minutes = String(currentDateObj.getMinutes()).padStart(2, '0');
            const seconds = String(currentDateObj.getSeconds()).padStart(2, '0');

            // Format the date and time as YYYY-MM-DD HH:MM:SS
            let current_date_data = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            return current_date_data;
        },
        updateIsAndroid() {
            this.isAndroid = window.innerWidth < 768;
        },
        savePrintInvoice() {
            this.printing = true;
            // console.log(this.paymentData, 'TTTTTTTTTTTTTTTTTT');
            if (this.$route.query.type !== 'edit' && this.paymentData && Array.isArray(this.paymentData) && this.paymentData.length > 0 && this.get_all_data.grand_total > 0) {
                let get_payment_total = this.paymentData.reduce((total, payment) => total + payment.payment_amount, 0);

                if (get_payment_total > 0 && this.get_all_data.grand_total > get_payment_total) {
                    this.formValues.balance = this.get_all_data.grand_total - get_payment_total;
                }
            }
            this.printing = true;
            this.saveData('Success');
        },
        //----------- get current date -----
        getCurrentDateTime() {
            const now = new Date();
            const day = String(now.getDate()).padStart(2, '0');
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const year = now.getFullYear();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');

            return `${day}/${month}/${year} ${hours}:${minutes}`;
        },
        validatePrice(index) {
            let price_value = this.items[index].price;
            if (price_value === '' || price_value < 0) {
                this.items[index].price = 0;
            }
        },
        //---add notes data---
        showAddNotes() {
            this.show_add_notes = true;
            // console.log(this.exist_data, 'WWWW happning the data...');
            this.est_notes = this.formValues.notes ? this.formValues.notes : this.exist_data.notes ? this.exist_data.notes : '';
        },
        closeAddNotes(data) {
            // console.log(data, 'EEEEEEEEEEEEEEEEEE happeningggg ');
            if (data && data.notes) {
                this.formValues.notes = data.notes
            }
            this.show_add_notes = false;
        },
        closeAllModals() {
            // Close all modals
            this.showModal_customer = false;
            this.open_confirmBox = false;
            this.open_termsModel = false;
            this.open_overall_discount = false;
            this.open_item_tax_discount = false;
            this.open_shippingcharges = false;
            this.open_paymentModal = false;
            this.open_add_newItem = false;
            this.openSerial = false;
            this.open_message = false;
            // this.enable_hold = false;
            this.show_add_notes = false;
        },
        //---minus sales----
        closeMinusSales() {
            this.minus_sales_open = false;
        },
        //---user add new product details----
        addRowData() {
            if (this.formData) {
                const { product_name, price, qty, total } = this.formData;
                if (product_name && product_name !== '' && qty >= 0) {
                    this.items.push(this.formData);
                    this.updateTotal();
                    this.calculateTax();
                    this.formData = { product_name: '', qty: 0, price: 0, total: 0, discount: 0, tax: 0, tax_type: '', taxvalue: null, discount_data: { type: 'Fixed', value: 0 } };
                    this.updateFormDatadefault();
                    this.$refs.formDataProduct.focus();
                    this.focustaxdiscount = false;
                } else {
                    this.message = 'Please enter the product details and then store them in the sales list.';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            } else {
                this.message = 'There is an issue, please wait...';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        calculateTaxDiscountform() {
            if (this.formData.tax_type === "Exclusive") {
                // Calculate tax value   
                if (this.formData.discount_data && Object.keys(this.formData.discount_data).length > 0) {
                    this.formData.discount = (this.formData.discount_data && this.formData.discount_data.type === 'Percentage' ? ((this.formData.qty * this.formData.price) * ((1 * this.formData.discount_data.value) / 100)).toFixed(2) : this.formData.discount_data ? this.formData.discount_data.value : 0);
                }
                let make_price = (this.formData.qty * this.formData.price) - (1 * this.formData.discount);
                let qty_price = 1 * make_price;
                let tax_amount = qty_price * (1 + ((1 * this.formData.taxvalue) / 100));
                this.formData.tax = (tax_amount - qty_price).toFixed(2);
                //--get total
                this.formData.total = (tax_amount).toFixed(2);

            } else {
                if (this.formData.discount_data && Object.keys(this.formData.discount_data).length > 0) {
                    this.formData.discount = (this.formData.discount_data && this.formData.discount_data.type === 'Percentage' ? (((this.formData.qty * this.formData.price) * ((1 * this.formData.discount_data.value) / 100)).toFixed(2)) : this.formData.discount_data ? this.formData.discount_data.value : 0);
                }
                let make_price = (this.formData.qty * this.formData.price) - (1 * this.formData.discount);
                this.formData.total = (make_price).toFixed(2);

                if (this.formData.taxvalue >= 0) {
                    // Calculate tax value
                    this.formData.tax = (((make_price) - ((make_price) / (1 + (1 * this.formData.taxvalue) / 100))).toFixed(2));
                }
            }
        },
        updateFormDataTotal(type) {
            if (type === 'price' && this.formData.price >= 0) {
                this.formData.total = this.formData.qty * this.formData.price;
                this.calculateTaxDiscountform();
            } else if (type === 'qty' && this.formData.qty >= 0) {
                this.formData.total = this.formData.qty * this.formData.price;
                this.calculateTaxDiscountform();
            } else {
                this.calculateTaxDiscountform();
            }
        },
        //--open sales itms---
        addSalesItems() {
            this.addSalesItem = true;
        },
        //--close sales items---
        closesalesItems(data) {
            // if (data && data.product_name && data.product_name !== '') {
            //     this.items.push(this.formData);
            //     this.formData = { product_name: '', qty: 0, price: 0, total: 0, discount: 0, tax: 0, tax_type: '', taxvalue: null, discount_data: {} };
            // }
            if (data && data.length >= 0) {
                this.items = data;
            }
            this.addSalesItem = false;
            this.editProductIndex = null;
        },
        //---focus the data----
        focusInputFormData(type) {
            if (type === 'qty') {
                this.$refs.formDataQty.focus();
            } else if (type === 'price') {
                this.$refs.formDataPrice.focus();
            } else if (type === 'tax') {
                this.focustaxdiscount = true;
                this.openItemTaxDiscountDialog(false, false, 'formData', 'tax');
            } else if (type === 'submit') {
                this.addRowData();
            }
        },
        //--update price formData--
        updatePriceFomData() {
            let price_per_qty = this.formData.total / this.formData.qty;
            let tax_per_qty = price_per_qty / (1 + (this.formData.taxvalue / 100));
            // console.log(price_per_qty, 'Price', tax_per_qty, 'for tax data..!', this.formData, 'What happening..1');
            if (this.formData.tax_type === "Exclusive") {
                this.formData.price = (price_per_qty / (1 + (this.formData.taxvalue / 100))).toFixed(2);
                // Calculate tax value
                this.formData.tax = (price_per_qty - tax_per_qty).toFixed(2);
            } else {
                let tax_per_qty01 = price_per_qty * (1 + (this.formData.taxvalue / 100));
                this.formData.price = (price_per_qty).toFixed(2);
                // Calculate tax value
                this.formData.tax = (tax_per_qty01 - price_per_qty).toFixed(2);
            }
            this.formData.discount = this.formData.discount_data.type === 'Percentage' ? this.formData.total * (this.formData.discount_data.value / 100).toFixed(2) : this.formData.discount_data.value;

        },
        resetForm() {
            this.formData = { product_type: "Services", product_name: '', qty: 0, price: 0, total: 0, discount: 0, tax: 0, tax_type: '', taxvalue: null, discount_data: { type: 'Fixed', value: 0 } };
            this.updateFormDatadefault();
        },
        //---edit product--
        editProduct(index) {
            if (index >= 0) {
                this.editProductIndex = index;
                this.addSalesItem = true;
            }
        },
        //---update balance data---
        updateBalanceData(status) {
            if (this.formValues.mark_full_pay && Array.isArray(this.paymentData) && !this.paymentAddStatus) {
                this.addPaymentOption();
                this.paymentAddStatus = true;
            } else if (this.formValues.mark_full_pay && Array.isArray(this.paymentData) && this.paymentData.length > 0 && this.paymentAddStatus) {
                let backupPayment = this.paymentData.slice(0, -1);
                let totalPayment01 = 0;
                if (backupPayment && backupPayment.length > 0) {
                    totalPayment01 = backupPayment.reduce((sum, opt) => sum + (1 * opt.payment_amount), 0);
                }
                let calculate_balance01 = (this.get_all_data.grand_total - totalPayment01).toFixed(2);
                if (this.paymentData && this.paymentData.length > 1) {
                    this.paymentData[this.paymentData.length - 1].payment_amount = calculate_balance01;
                } else {
                    this.paymentData[this.paymentData.length - 1].payment_amount = (this.get_all_data.grand_total).toFixed(2);
                }
            } else if (Array.isArray(this.paymentData) && !this.paymentAddStatus) {
                const defaultPaidType = this.paymentDetails.type ? this.paymentDetails.type : this.invoice_setting && this.invoice_setting.length > 0 && JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type;
                const remainingBalance = this.paymentDetails.value ? this.paymentDetails.value : 0;
                this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_amount: remainingBalance, payment_type: defaultPaidType, payment_for: 'sales' });
                setTimeout(() => {
                    if (this.$refs.paymentamount && status === undefined) {
                        this.$nextTick(() => {
                            this.$refs.paymentamount.focus();
                        })
                    }
                }, 100)
                this.paymentAddStatus = true;
            } else {
                if (this.paymentAddStatus && Array.isArray(this.paymentData) && this.paymentData.length > 0) {
                    this.paymentData[this.paymentData.length - 1].payment_amount = 0;
                }
            }
        },
        //---add payement row--
        addPaymentOption() {
            const defaultPaidType = this.invoice_setting && this.invoice_setting.length > 0 && JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type;
            const remainingBalance = this.get_all_data ? this.get_all_data.grand_total - this.getTotalPayment : 0;
            this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_amount: remainingBalance, payment_type: defaultPaidType, payment_for: 'sales' });
        },
        //---update default tax and payement type---
        updateFormDatadefault() {
            if (this.invoice_setting) {
                const defaultTax = this.invoice_setting && JSON.parse(this.invoice_setting[0].selected_tax).find(opt => opt.status === true);
                if (defaultTax) {
                    this.formData.tax_type = 'Exclusive';
                    this.formData.taxvalue = defaultTax.value + '';
                }
            }
        },
        //--reset payment data----
        resetPaymentAddPay() {
            if (Array.isArray(this.paymentData) && this.paymentData.length > 0 && this.paymentData[this.paymentData.length - 1].id === undefined) {
                this.paymentData.pop();
                this.paymentAddStatus = false;
                this.formValues.mark_full_pay = false;
            }
        },
        //---close msg--
        closeSuccessMsg() {
            this.showModalSuccessMsg = false;
            this.$router.go(-1);
        },
        //--shipping details--
        handleSave(data, is_same) {
            if (data) {
                this.shipping_detail = data;
            }
            if (is_same !== undefined) {
                this.sameAsBilling = is_same;
            }
            this.isModalOpen = false;
        },
        updateshippingDetails() {
            this.isModalOpen = !this.isModalOpen
        },
        validateshipping() {
            if (this.sameAsBilling) {
                return true;
            } else if (!this.sameAsBilling) {
                if (this.shipping_detail && Object.keys(this.shipping_detail).length > 0) {
                    return true;
                } else {
                    this.message = 'Update ship to details';
                    this.type_toaster = 'warning';
                    this.show = true;
                    return false;
                }
            }
        },
        navigateToWhatsApp() {
            this.$router.push({ name: 'whatsappsetting' });
        },
        //--clone data initializations
        cloneinitializations(type, data) {
            if (type == 'est' && data) {
                this.exist_data = data;
                if (this.exist_data.customers && this.exist_data.customers.id) {
                    this.getCustomerDataById(this.exist_data.customers.id);
                }
                this.get_all_data.grand_total = this.exist_data.grand_total;
                this.items = JSON.parse(this.exist_data.items);
                this.items.forEach((item, index) => {
                    // Check if the item has a discount_data key and its value is an object
                    if (typeof item.discount_data === 'string') {
                        // Stringify the discount_data object
                        item.discount_data = JSON.parse(item.discount_data);
                    }
                    if (typeof item.serial_no === 'string' && item.serial_no !== '') {
                        // Stringify the serial_no object
                        item.serial_no = JSON.parse(item.serial_no);
                    }
                });
                this.customer_data = this.exist_data.customers;
                // this.iswhatsapp = this.exist_data.iswhatsapp;
                // this.issms = this.exist_data.issms;
                let parse_data = JSON.parse(this.exist_data.data);
                if (parse_data) {
                    this.shipping_details = parse_data.shipping ? parse_data.shipping : {};
                    this.over_all_discount = parse_data.over_all_discount;
                    this.selectedOption_invoice_type = parse_data.invoice_to;
                }
                this.sameAsBilling = this.exist_data.is_billaddress == 1 ? true : false;
                this.shipping_detail = this.exist_data.shipping_address ? JSON.parse(this.exist_data.shipping_address) : {};
                this.updateTotal();
            }
        },
        //--is sms and whatsapp enble set as default---
        toggleSwitch(key) {
            if (key == 'is_tax') {
                this.is_tax = this.is_tax == 0 ? 1 : 0;
            }
        },
        //--validate the code--
        validateCode(code) {
            // Only process if the code is valid and needs modification
            if (code == 'INVA0000') {
                return 'INVA';
            } else { return code; }
        },
        //----add custom fields---
        // Method to add new custom field
        addNewField() {
            // Find the highest numbered label, e.g., "Custom Field 1", "Custom Field 2"
            const customFieldLabels = this.form_fields
                .filter(field => field.label.startsWith('Custom Field'))
                .map(field => {
                    const match = field.label.match(/^Custom Field (\d+)$/);
                    return match ? parseInt(match[1], 10) : 0;
                });

            const newLabel = customFieldLabels.length > 0
                ? `Custom Field ${Math.max(...customFieldLabels) + 1}`  // Increment the highest number
                : 'Custom Field';  // Default to "Custom Field" if no such field exists

            // Create a new field object with the generated unique label
            const newField = {
                label: newLabel,
                value: '',
                type: 'header',
                enabled: true,
                field_type: 'text',  // or 'date' based on the field type you want to add
            };
            // Push new field to form_fields
            this.form_fields.push(newField);
        },
        //---remove fields--
        removefields(index) {
            if (this.form_fields && this.form_fields.length > 0) {
                if (this.form_fields[index] && this.form_fields[index].label.startsWith('Custom Field') && this.form_fields[index].value == '') {
                    this.form_fields.splice(index, 1);
                } else {
                    this.deleteType = 'custom';
                    this.deleteIndex = index;
                    this.open_confirmBox = true;
                }
            }
        },
        editfields(index) {
            // Focus on the input field at the specific index
            this.$nextTick(() => {
                const inputField = this.$refs[`input-${index}`]; // Access the ref of the input at the given index
                if (inputField && inputField.length > 0) {
                    inputField[0].focus();  // Focus on the input field
                }
            });
        },
    },
    computed: {
        ...mapGetters('customer', ['currentCustomer', 'currentPagination']),
        ...mapGetters('items', ['currentItems', 'currentItemsPagination']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('holdsList', ['currentHoldsList', 'isEnable', 'isShowList']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
        ...mapGetters('clone', ['estimationclone']),

        //--total qty---
        totalQuantity() {
            let count_total = this.items.reduce((sum, item) => sum + item.qty, 0);
            if (this.get_all_data === null) {
                this.get_all_data = { total_qty: count_total };
            } else {
                this.get_all_data.total_qty = count_total;
            }
            return count_total;
        },
        //---total amount--
        totalAmount() {
            let sub_total = this.items.reduce((sum, item) => (1 * sum) + (1 * item.total), 0);
            if (this.get_all_data === null) {
                this.get_all_data = { sub_total: sub_total };
            } else {
                this.get_all_data.sub_total = sub_total;
            }
            return sub_total;
        },
        //---total discount--
        totalDiscount() {
            return this.items.reduce((sum, item) => (1 * sum) + (1 * item.discount), 0);
        },
        //--grandTotal--
        grandTotal() {
            let totalDiscount = this.totalDiscount;
            let overallDiscount = this.over_all_discount !== null ? this.overAllDiscount : 0;
            let totalAmount = this.items.reduce((sum, item) => sum + parseFloat(item.total), 0);
            let shipping_charge = this.shipping_details.shipping > 0 ? this.shipping_details.shipping : 0;
            let total_tax = this.overAllTax.toFixed(2);
            let grandTotal = (totalAmount + shipping_charge) - (1 * overallDiscount);
            if (this.get_all_data === null) {
                this.get_all_data = { discount_total: (overallDiscount).toFixed(2), shipping: shipping_charge, grand_total: Math.round(grandTotal), total_tax: total_tax };
            } else {
                this.get_all_data.discount_total = (1 * overallDiscount).toFixed(2);
                this.get_all_data.shipping = shipping_charge;
                this.get_all_data.grand_total = Math.round(grandTotal);
                this.get_all_data.total_tax = total_tax;
            }
            return Math.round(grandTotal);
        },
        //---over all discount---
        overAllDiscount() {
            if (this.over_all_discount !== null) {
                return this.over_all_discount.type === 'Percentage' ? (this.totalAmount * (this.over_all_discount.value / 100)).toFixed(2) : this.over_all_discount.value;
            } else {
                return 0;
            }
        },
        //--total tax---
        overAllTax() {
            let calculate_tax = this.items.reduce((sum, item) => (1 * sum) + (1 * item.tax), 0);
            return calculate_tax;
        },
        getTotalPayment() {
            // console.log(this.paymentData, 'Waht is the data...!');
            let totalPayment = this.paymentData.reduce((sum, opt) => sum + (1 * opt.payment_amount), 0);
            // console.log(totalPayment, 'What happening...!');
            return totalPayment;
        },
        getBalanace() {
            let calculate_balance = (this.get_all_data.grand_total - this.getTotalPayment).toFixed(2);
            if (calculate_balance > 0) {
                this.formValues.balance = calculate_balance;
                return calculate_balance;
            } else {
                this.formValues.balance = 0;
                return 0;
            }
        },
        getReturn() {
            if (this.get_all_data.grand_total < this.getTotalPayment) {
                let calculate_return = (this.getTotalPayment - this.get_all_data.grand_total).toFixed(2);
                if (calculate_return > 0) {
                    // console.log(calculate_return, 'What happening...!')
                    this.formValues.return = calculate_return;
                    return calculate_return;
                } else {
                    this.formValues.return = 0;
                    return 0;
                }
            } else {
                this.formValues.return = 0;
                return 0;
            }
        },
        //---validate full pay---
        validateFullPay() {
            if (this.formValues.mark_full_pay && Array.isArray(this.paymentData) && this.paymentData.length > 0 && this.paymentAddStatus) {
                if (this.get_all_data.grand_total !== this.paymentData[this.paymentData.length - 1].payment_amount) {
                    this.formValues.mark_full_pay = false;
                }
            } else if (Array.isArray(this.paymentData) && this.paymentData.length > 0 && this.paymentAddStatus) {
                if (this.get_all_data.grand_total === this.paymentData[this.paymentData.length - 1].payment_amount) {
                    this.formValues.mark_full_pay = true;
                }
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        const collectForm01 = localStorage.getItem('track_new');
        if (collectForm01) {
            let dataParse = JSON.parse(collectForm01);
            this.companyId01 = dataParse.company_id;
            this.userId01 = dataParse.user_id + '';
        }
        this.currentDate = this.getCurrentDate();

        this.updateIsMobile(); // Initial check
        this.updateIsAndroid();
        this.fetchCompanyList();
        this.fetchWhatsappList();
        //--get local data customer--
        if (this.customers.length === 0) {
            // this.getCustomerListData(1, 'all');
            if (this.currentCustomer && this.currentCustomer.length > 0) {
                this.customers = this.currentCustomer;
                if (this.currentPagination && Object.keys(this.currentPagination).length > 0) {
                    this.pagination.customer = this.currentPagination;
                }
                this.fetchCustomerList();
            } else {
                this.fetchCustomerList();
            }
        }
        if (this.product.length === 0 && this.companyId01) {
            this.getProductListData(1, 2);
            if (this.currentItems && this.currentItems.length > 0) {
                this.product = this.currentItems;
                if (this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0) {
                    this.pagination.product = this.currentItemsPagination;
                }
                // this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
            }
            // this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
        }
        if (this.invoice_setting === null) {
            // this.getInvoiceData();
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                this.invoice_setting = this.currentInvoice;
                this.updateFormDatadefault();
                this.fetchInvoiceSetting();
            } else {
                this.fetchInvoiceSetting();
            }
        }
        if (this.$route.query.clone) {
            this.is_clone = this.$route.query.clone;
            if (this.estimationclone && Object.keys(this.estimationclone).length !== 0 && this.estimationclone.id == this.is_clone) {
                this.cloneinitializations('est', this.estimationclone);
            } else if (this.is_clone) {
                this.getEstimation(this.is_clone);
            }
        }
        if (this.invoice_num === '' && this.$route.query.type === 'add') {
            this.getInvoiceNumberData();
        }
        if (this.$route.query.type === 'add' && this.$route.query.est_no) {
            this.getEstimationData();
        }
        if (this.$route.query.type === 'add' && this.$route.query.proforma_no) {
            this.getProformaData();
        }
        // console.log(this.$route.query.invoice_no, 'TTTTT');
        if (this.$route.query.type === 'edit' && (this.$route.query.invoice_no || this.$route.query.est_no || this.$route.query.proforma_no)) {
            this.getDataById();
        }
        if (this.$route.query.type === 'add' && this.customers && this.customers.length > 0) {
            let selected_customer = this.$route.query.customer_id;
            if (selected_customer) {
                let find_customer = this.customers.find(opt => opt.id == selected_customer);
                if (find_customer) {
                    this.selectDropdownOption(find_customer);
                }
            }
        }
        //---initial add payment index--
        if (this.typeOfInvoice !== 'estimation' && this.$route.query.type === 'add') {
            this.updateBalanceData(true);
        }
        if (!this.isAndroid && this.$refs.formDataProduct) {
            this.$nextTick(() => {
                this.$refs.formDataProduct.focus();
            })
        }
        // console.log(this.$route.query.type, 'Waht happening...!!!');
        window.addEventListener('resize', this.updateIsMobile);
        window.addEventListener('resize', this.updateIsAndroid);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
        window.removeEventListener('resize', this.updateIsAndroid);
    },
    watch: {
        invoice_setting: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue[0] && newValue[0].invoice_prefix && this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                    // console.log(newValue[0].invoice_prefix, 'YYYYYY');
                    let findData = JSON.parse(newValue[0].invoice_prefix).find(opt => opt.status === true);
                    // console.log(findData, 'Waht happening...!');
                    if (findData) {
                        this.initital_code = this.validateCode(findData.prefix);
                        this.formValues.invoice_group = findData.value;
                    }
                    if (this.$route.query.type === 'add' && this.paymentData.length === 1 && !this.paymentData[0].payment_type) {
                        const defaultPaidType = newValue && newValue.length > 0 && JSON.parse(newValue[0].payment_opt).find(opt => opt.status === true).type;
                        this.paymentData[0].payment_type = defaultPaidType;
                    }
                } else if (this.typeOfInvoice === 'estimation') {
                    this.initital_code = 'EST/QUO';
                } else if (this.typeOfInvoice === 'proforma') {
                    this.initital_code = 'PRO/QUO';
                }
                if (newValue && Array.isArray(newValue) && newValue.length > 0) {
                    this.issms = newValue[0].is_sms >= 0 ? newValue[0].is_sms ? true : false : false;
                    this.iswhatsapp = this.companywhatsapp ? newValue[0].is_whatsapp >= 0 ? newValue[0].is_whatsapp ? true : false : false : false;
                }
                if (newValue && Array.isArray(newValue) && newValue.length > 0 && newValue[0].header_custom && this.typeOfInvoice && ['sales'].includes(this.typeOfInvoice)) {
                    try {
                        // Assuming parseData is your JSON data
                        const headerCustom = JSON.parse(newValue[0].header_custom);
                        if (headerCustom && headerCustom !== '') {
                            // Filter out duplicates by comparing label names
                            const uniqueFields = headerCustom.filter(field => {
                                // Check if the label already exists in form_fields
                                return !this.form_fields.some(existingField => existingField.label === field.label);
                            });

                            // Merge the unique fields into form_fields
                            this.form_fields = [...this.form_fields, ...uniqueFields];
                        }
                    } catch (error) {
                        console.error('Error parsing header_custom:', error);
                    }
                }
            }
        },
        'formValues.invoice_group': {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'Waht happening/.....!');
                if (this.response_data_number !== null && this.invoice_setting && this.invoice_setting.length > 0 && this.$route.query.type !== 'edit' && this.typeOfInvoice !== 'estimation') {
                    this.invoice_num = newValue === 0 ? this.response_data_number.last_invoice_number_group_a + 1 : this.response_data_number.last_invoice_number_group_b + 1;
                    let findData = JSON.parse(this.invoice_setting[0].invoice_prefix).find(opt => opt.value === newValue);
                    if (findData) {
                        this.initital_code = this.validateCode(findData.prefix);
                        this.formValues.invoice_group = findData.value;
                    }
                }
            }
        },
        service_data: {
            deep: true,
            handler(newValue) {
                if (this.$route.query.type === 'add' && newValue !== null) {
                    // this.customer_data = newValue.customer;
                    this.selectDropdownOption(newValue.customer);
                    if (newValue.customer.id) {
                        this.getCustomerDataById(newValue.customer.id)
                    }

                    let taxvalue = this.invoice_setting?.[0]?.selected_tax
                        ? (typeof this.invoice_setting[0].selected_tax === 'string'
                            ? JSON.parse(this.invoice_setting[0].selected_tax)
                            : this.invoice_setting[0].selected_tax)
                            .some(opt => opt.value === 18) ? 18 : 0
                        : 0;

                    // this.items = [{product_name: `${newValue.servicecategory.service_category}`}]
                    let service_track_data = JSON.parse(newValue.service_data);
                    let serviceAmount = service_track_data.serviceAmount > 0 ? service_track_data.serviceAmount : service_track_data.estimateAmount ? service_track_data.estimateAmount : 0;
                    let obj = {
                        company_id: this.companyId01,
                        product_name: `${newValue.servicecategory.service_category} ${service_track_data.brand ? service_track_data.brand : ''}  ${service_track_data.device_model ? service_track_data.device_model : ''}  ${service_track_data.serial_number ? service_track_data.serial_number : ''}`,
                        total_qty: 1,
                        description: '',
                        price: serviceAmount,
                        hsn_code: '9987',
                        tax_name: 'GST',
                        taxvalue: taxvalue,
                        tax_type: 'Inclusive',
                        qty: 1,
                        tax: serviceAmount * (0 / 100),
                        discount_data: { type: 'Fixed', value: 0 },
                        discount: 0,
                        product_id: '',
                        barcode_id: '',
                        product_code: '',
                        total: serviceAmount,
                        product_type: 'Services'
                    };
                    //---Dynamic fields---
                    if (newValue.servicecategory && newValue.servicecategory.form) {
                        try {
                            let parse_data = JSON.parse(newValue.servicecategory.form);
                            if (parse_data && parse_data.length > 0) {
                                const salesEnabledFields = parse_data.filter(field => field.enable_sales && !['additional', 'estimateAmount', 'discountValue', 'advanceAmount', 'serviceAmount'].includes(field.fieldKey)).map(field => {
                                    let fieldValue = service_track_data && service_track_data[field.fieldKey] ? service_track_data[field.fieldKey] : '';

                                    // If the value is an array of strings, join them with a comma
                                    if (Array.isArray(fieldValue) && fieldValue.every(item => typeof item === 'string')) {
                                        fieldValue = fieldValue.join(', '); // Join array of strings with a comma
                                    } else if (typeof fieldValue === 'object') {
                                        fieldValue = '';
                                    }

                                    return {
                                        label: field.lableName,
                                        enabled: true,
                                        type: 'header',
                                        field_type: field.type == 'date' ? field.type : 'text',
                                        value: fieldValue // Assign the processed value
                                    };
                                });

                                if (salesEnabledFields && salesEnabledFields.length > 0) {
                                    this.form_fields = [...new Set([...this.form_fields, ...salesEnabledFields])];
                                }
                            }
                        } catch (error) {
                            console.error('Parse Error', error);
                        }
                    }

                    if (newValue.materials) {
                        this.items = [obj, ...JSON.parse(newValue.materials)];
                    } else {
                        this.items = [obj];
                    }
                    //--remove unwanted keys and values---
                    this.items = this.items.map(obj => {
                        const { hsnCode, ...rest } = obj;
                        return rest;
                    });
                    this.items.forEach((item, index) => {
                        if (item.company_id === undefined) {
                            item.company_id = this.companyId;
                        }
                        if (item.discount_data === undefined) {
                            item.discount_data = { type: 'Fixed', value: 0 };
                            item.discount = 0;
                        }
                        if (item.tax_value !== undefined) {
                            item.taxvalue = item.tax_value;
                            delete item.tax_value;
                        }
                        if (item.taxvalue === undefined) {
                            item.taxvalue = 0;
                        }
                        if (item.tax_type === undefined) {
                            item.tax_type = 'Inclusive';
                        }
                        if (item.product_code === undefined) {
                            item.product_code = '';
                        }
                        if (item.hsn_code === undefined) {
                            item.hsn_code = '';
                        }
                        if (item.description === undefined) {
                            item.description = '';
                        }
                        if (item.serial_no === undefined) {
                            item.serial_no = '';
                        }
                        if (item.discount_data || item.discount) {
                            this.updateTotal(index);
                        }
                    });

                    if (service_track_data.advance_payment_type && service_track_data.advanceAmount >= 0) {
                        this.paymentData = [{ payment_date: this.getCurrentDateTime(), payment_type: service_track_data.advance_payment_type, payment_amount: service_track_data.advanceAmount, payment_for: 'sales' }];
                    }
                    //---service discount--
                    if (service_track_data && service_track_data.discountValue) {
                        let { type, value } = service_track_data.discountValue;
                        this.over_all_discount = { type: type ? type : 'Fixed', value: value ? value : 0 };
                    }
                    // this.paymentData = [{paid_type: service_track_data.advance_payment_type , payment_amount: service_track_data.advanceAmount}];
                    // console.log(this.paymentData, 'WWWWWWWWWWWWWWWWWWWWWWWWWWW', service_track_data.advance_payment_type);                    
                } else if (this.$route.query.type === 'add' && newValue === null) {
                    let obj = {
                        company_id: this.companyId01,
                        product_name: '',
                        total_qty: 1,
                        description: '',
                        price: 0,
                        hsn_code: '',
                        taxvalue: 0,
                        tax_type: 'Inclusive',
                        qty: 1,
                        tax: 0,
                        discount_data: { type: 'Fixed', value: 0 },
                        discount: 0,
                        product_id: '',
                        barcode_id: '',
                        product_code: '',
                        total: 0,
                    };
                    this.items = [obj];
                }
            },
            enable_hold: {
                deep: true,
                handler(newValue) {

                }
            }
        },
        currentCustomer: {
            deep: true,
            handler(newValue) {
                this.customers = newValue;
                if (this.currentPagination && Object.keys(this.currentPagination).length > 0) {
                    this.pagination.customer = this.currentPagination;
                }
                if (this.$route.query.type === 'add' && this.customers && this.customers.length > 0) {
                    let selected_customer = this.$route.query.customer_id;
                    if (selected_customer) {
                        let find_customer = this.customers.find(opt => opt.id == selected_customer);
                        if (find_customer) {
                            this.selectDropdownOption(find_customer);
                        }
                    }
                }
            }
        },
        currentItems: {
            deep: true,
            handler(newValue) {
                setTimeout(() => {
                    this.isLoading = false;
                }, 1000);
                this.product = newValue;
                if (this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0) {
                    this.pagination.product = this.currentItemsPagination;
                }
                this.filterProducts(true);
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                this.invoice_setting = newValue;
                this.updateFormDatadefault();
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchCustomerList();
                this.fetchInvoiceSetting();
                this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
                this.currentDate = this.getCurrentDate();
                this.updateIsMobile();
                this.updateIsAndroid();
                if (this.invoice_num === '' && this.$route.query.type === 'add') {
                    this.getInvoiceNumberData();
                }
                if (this.$route.query.type === 'add' && this.$route.query.est_no) {
                    this.getEstimationData();
                }
                if (this.$route.query.type === 'add' && this.$route.query.proforma_no) {
                    this.getProformaData();
                }
                // console.log(this.$route.query.invoice_no, 'TTTTT');
                if (this.$route.query.type === 'edit' && (this.$route.query.invoice_no || this.$route.query.est_no || this.$route.query.proforma_no)) {
                    this.getDataById();
                }
            }
        },
        // paymentData: {
        //     deep: true,
        //     handler(newValue) {
        //         console.log(newValue, 'RRRRRRRRRRRRRRRRRRRRR what happneinggggg');

        //     }
        // }
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        showModal_customer: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_termsModel: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_overall_discount: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_item_tax_discount: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_shippingcharges: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_paymentModal: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_add_newItem: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        openSerial: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        show_add_notes: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                        this.savePrintInvoice();
                    } else if (this.typeOfInvoice === 'proforma') {
                        this.saveProforma('print');
                    } else if (this.typeOfInvoice === 'estimation') {
                        this.saveEstimation('print');
                    }
                }
            }
        },
        selectedOption_invoice_type: {
            deep: true,
            handler(newValue) {
                if (this.$route.query.type !== 'edit') {
                    this.is_updated = true;
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.is_updated = false;
                    this.$emit('is-sales-save', true);
                }
            }
        },
        isShowList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.enable_hold = newValue;
                }
            }
        },
        items: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    if (this.formValues.mark_full_pay && Array.isArray(this.paymentData) && this.paymentData.length > 0 && this.typeOfInvoice !== 'estiamtion') {
                        this.updateBalanceData();
                    }
                }
            }
        },
        paymentData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length === 0 && this.$route.query.type === 'edit' && this.typeOfInvoice !== 'estimation' && !this.paymentData_int) {
                    this.updateBalanceData(true);
                    this.paymentData_int = true;
                }
            }
        },
        //--clone--
        estimationclone: {
            deep: true,
            handler(newValue) {
                if (this.is_clone && newValue && Object.keys(newValue).length > 0) {
                    this.cloneinitializations('est', newValue);
                }
            }
        }
    }
}
</script>
<style scoped>
.table-container {
    /* Set the desired fixed height */
    overflow-y: auto;
    /* Enable vertical scrolling */
    overflow-x: auto;
    /* Hide horizontal scrollbar if not needed */
}

/* Optional: Customize the scrollbar */
.table-container::-webkit-scrollbar {
    width: 3px;
    /* Width of the scrollbar */
}

.table-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    /* Track color */
}

.table-container::-webkit-scrollbar-thumb {
    background: #888;
    /* Thumb color */
    border-radius: 3px;
    /* Rounded corners */
}

.fixed-height-table {
    height: 100%;
    /* Set the height of the table to fill its container */
}
</style>