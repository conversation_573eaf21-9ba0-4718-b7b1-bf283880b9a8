<template>
    <div>
        <div @click="openDialog"
            class="w-[228px] h-[50px] bg-gradient-to-r from-teal-500 to-green-500 rounded-tl-[30px] rounded-bl-[30px] flex justify-center items-center flex-end ml-auto cursor-pointer">
            <img class="w-[35px] h-[35px] mr-5" :src="setting_img" />
            <p class="text-center text-white pl-5">Add Roles</p>
        </div>
        <!--loader-->
<skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns" :rows="number_of_rows" :gap="gap" :type=" 'grid'">
        </skeleton>
        <!-- Role Data Section -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mt-10">
            <div v-for="(data, index) in role_data" :key="index" class="border px-3 py-3 rounded rounded-xl">
                <div class="relative rounded-xl border border-white p-5 justify-center items-center mb-2"
                    :class="{ 'bg-lime-200': ((index + 1) % 3) === 0, 'bg-cyan-100': ((index + 1) % 3) !== 0 && ((index + 1) % 2) === 0, 'bg-pink-200': ((index + 1) % 3) !== 0 && ((index + 1) % 2) !== 0 }">
                    <p class="text-stone-500 text-sm text-center mb-4">Role</p>
                    <p class="text-center text-black text-lg font-medium">{{ data.name }}</p>
                </div>
                <div class="flex justify-between w-full">
                    <!-- First Button -->
                    <button class="flex items-center justify-between border border-gray-500 p-2 rounded ml-2 w-1/2"
                        @click="openModel(data)">
                        <span class="w-1/2">Permissions</span>
                        <img class="w-[20px] h-[20px] ml-2" :src="permission_key" />
                    </button>
                    <!-- Second Button -->
                    <button class="flex items-center justify-between border border-gray-500 p-2 rounded mr-2 ml-2 w-1/2"
                        @click="openDialog(data, index)">
                        <span>Edit Role</span>

                        <img class="w-[20px] h-[20px] ml-2" :src="writting_key" />
                    </button>
                    <!-- Include the DialogRole component -->
                </div>
            </div>
        </div>
    </div>
    <DialogRole :show-modal="roleModel" @close-modal="closeDialog" :type="type" :data_value="roleData" :companyId="companyId" :userId="userId"/>
    <PermissionBox :show-modal="showModal_permission" @close-modal="closeModal" :editData="data_permission" :companyId="companyId" :userId="userId"></PermissionBox>
</template>
<script>
import DialogRole from '../dialog_box/dialog_role.vue';
import PermissionBox from '../dialog_box/permissionBox.vue';
export default {
    name: 'roles',
    props:{
        companyId: String,
        userId: String
    },
    components: {
        DialogRole,
        PermissionBox,
    },
    data() {
        return {
            setting_img: '/images/setting_page/User.png',
            permission_key: '/images/setting_page/Key.png',
            writting_key: '/images/setting_page/Writing.png',
            role_data: [],
            roleData: null,
            showModal_permission: false,
            data_permission: null,
            roleModel: false,
            type: '',
            selectedIndex: null,
            open_skeleton: false,
            number_of_columns: 4,
            number_of_rows: 10,
            gap: 5,
        };
    },
    methods: {
        //--role
        openDialog(data, index) {
            // console.log(data, 'what about data....');
            if (data.name) {
                this.roleData = data;
                this.type = 'edit';
                this.selectedIndex = index;               
            }
            this.roleModel = true;
        },
        closeDialog(data) {
            // Call the openDialog method from the Dialog component
            // console.log(data, 'WWWWWW');
            if (data) {
                if (this.type === 'edit' && this.selectedIndex >=0 && this.selectedIndex !== null) {
                    this.role_data[this.selectedIndex]= data;
                    this.type = '';
                    this.selectedIndex = null;
                }
                else {
                    // console.log(data, 'WWWWWWWWW');
                    let validateduplicate = this.role_data.findIndex((opt) => opt.name.toLowerCase() === data.name.toLowerCase());
                    if (validateduplicate === -1) {
                        this.role_data.push(data);
                    }
                }
            }
            this.roleModel = false;
        },

        //----Permission---
        openModel(data) {
            // console.log(data, 'WWWW');
            this.data_permission = data;
            this.showModal_permission = true;
        },
        closeModal() {
            this.showModal_permission = false;
        },
        getRolesData(){
            this.open_skeleton = true;
            axios.get('/roles', {params:{company_id: this.companyId, page: 1, per_page: 'all'}})
            .then(response =>{
                console.log(response.data);
                this.open_skeleton = false;
                this.role_data = response.data.data;
            })
            .catch(error =>{
                console.error('Error', error);
            })
        }

    },
    mounted(){
        if(this.companyId){
            this.getRolesData();
        }
    },
    watch:{
        companyId:{
            deep: true,
            handler(newValue){
                this.getRolesData()
            }
        }
    }
};

</script>