// store/modules/items_list.js
import axios from "axios";

const state = {
  items_list: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  previousPerPage: null,
  };

  const mutations = {
      SET_ITEMSLIST(state, { data, pagination}) {
          state.items_list = {data: data, pagination: pagination};
    },
      RESET_STATE(state) {
          state.items_list = {};
          state.lastFetchTime = null;
          state.isFetching = false;
      },
      SET_LAST_FETCH_TIME(state, time) {
        state.lastFetchTime = time; // Save the timestamp when the API was last accessed
      },
      SET_IS_FETCHING(state, status) {
        state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    SET_PREVIOUS_PER_PAGE(state, perPage) {
      state.previousPerPage = perPage; // Store the previous per_page value
    },

  };

  const actions = {
    updateItemsName({ commit }, items_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update items_list name
      setTimeout(() => {
        // Commit mutation to update items_list name
        commit('SET_ITEMSLIST', items_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchItemsList({ commit, state, rootState }, { page, per_page, is_delete }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['products_update']; 
      if (state.previousPerPage != per_page) {
        commit('SET_PREVIOUS_PER_PAGE', per_page); // Update the per_page tracking
        commit('SET_LAST_FETCH_TIME', null); // Force a new request if per_page changed
      }
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (!is_delete && (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime))  { 
        return; // Skip request if less than 30 seconds have passed since the last request
      } 
      
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          // Set the request status to true (indicating that the request is in progress)
      commit('SET_IS_FETCHING', true);
          axios.get('/products_details', { params: { company_id: company_id, page: page, per_page: per_page } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Items list..!');
              let { data, pagination} = response.data;
             
              commit('SET_ITEMSLIST', { data, pagination });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    
  };

  const getters = {
    currentItemsList(state) {
      return state.items_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
