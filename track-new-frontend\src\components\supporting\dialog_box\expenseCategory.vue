<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="justify-between items-center flex py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-xl">
                    Expense Catgory</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>

            <!-- Form for CRUD operations -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-4 m-4">
                <form @submit.prevent="submitForm" class="text-sm text-center">
                    <input type="text" v-model="formValues.name" placeholder="Category Name" ref="inputName"
                        class="block mb-3 border px-3 py-2 w-full">
                    <button type="submit"
                        class=" rounded rounded-md px-3 py-2 mt-3 bg-green-700 hover:bg-green-600 text-white">
                        {{ updateIndex === null ? 'Create' : 'Update' }}</button>
                    <button class=" rounded rounded-md px-3 py-2 mt-3 bg-red-700 hover:bg-red-600 text-white ml-5"
                        @click="cancelModal">Close</button>
                </form>
            </div>

            <!-- Display categories -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-3 m-4 text-sm"
                :key="categories.length">
                <p class="font-bold underline mb-2">List:</p>
                <ul>
                    <li v-for="(category, index) in categories" :key="index" class="flex justify-between">
                        <div>{{ category.name }}</div>
                        <div class="flex justify-between">
                            <button @click="editCategory(index)">
                                <img :src="table_edit" alt="table-edit" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                            <button @click="deleteCategory(index)">
                                <img :src="table_del" alt="table-delete" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import axios from 'axios';
import confirmbox from './confirmbox.vue';
import dialogAlert from './dialogAlert.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'ExpenseCategory',
    components: {
        confirmbox,
        dialogAlert
    },
    props: {
        showModal: Boolean,
        categoriesData: Object,
        companyId: String,
        userId: String
    },
    computed: {
        ...mapGetters('expensesTypeList', ['currentExpenseType', 'currentExpenseTypePagination']),
    },

    data() {
        return {
            isMobile: false,
            formValues: {},
            isOpen: false,
            categories: [],
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            deleteIndex: null,
            open_confirmBox: false,
            updateIndex: null,
            isMessageDialogVisible: false,
            message: '',
            open_loader: false,
            //--toaster----
            show: false,
            type_toaster: 'warning'
        };
    },
    methods: {
        ...mapActions('expensesTypeList', ['fetchExpenseTypeList']),
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeModal', this.categories);
                this.categories = [];
            }, 300);

        },
        submitForm() {
            // console.log(this.updateIndex, 'What is the index valuye...!');
            if (this.formValues.name !== '' && this.formValues.name && !this.categories.some((data) => data.name.toLowerCase().includes(this.formValues.name.toLowerCase())) && this.updateIndex === null) {
                // console.log(this.categories);
                // Create category                
                this.open_loader = true;
                axios.post('/expensesTypes', { name: this.formValues.name, company_id: this.companyId })
                    .then(response => {
                        // Handle success response
                        console.log('Response:', response.data);

                        this.open_loader = false;
                        this.categories.unshift(response.data.data);
                        this.formValues = {};
                        this.updateIndex = null;
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);

                        this.open_loader = false;
                    });

                // this.saveToLocalStorage();
                this.formValues.name = ''; // Clear input
            } else if (this.updateIndex !== null && this.formValues.name !== '' && this.categories[this.updateIndex].name) {

                axios.put(`/expensesTypes/${this.categories[this.updateIndex].id}`, { company_id: this.companyId, name: this.formValues.name })
                    .then(response => {
                        // Handle success response
                        console.log('Response:', response.data, this.updateIndex, 'RRRRR');
                        this.open_loader = false;
                        this.categories[this.updateIndex] = response.data.data;
                        this.formValues = {};
                        this.updateIndex = null;
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);

                        this.open_loader = false;
                    });

            } else if (this.formValues.name) {
                this.isMessageDialogVisible = true;
                this.message = `${this.formValues.name} already exist..!`;
            }
        },
        editCategory(index) {
            // Edit category (not implemented in this example)
            // console.log('Edit category:', this.categories[index]);
            this.formValues.name = this.categories[index].name;
            this.updateIndex = index;
            // console.log(index, 'WWWWWW');
        },
        deleteCategory(index) {
            // Delete category
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },

        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.inputName;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---confirm box funxctions
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.open_loader = true;
                axios.delete(`/expensesTypes/${this.categories[this.deleteIndex].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // console.log(response.data);
                        this.open_loader = false;
                        this.categories.splice(this.deleteIndex, 1);
                    })
                    .catch(error => {
                        console.error(error);
                        this.open_loader = false;
                        this.message = error.response.data.message;
                        this.show = true;
                    });

                // this.saveToLocalStorage();
                this.open_confirmBox = false;
            }
        },

        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---close message--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
        }
    },

    mounted() {
        this.updateIsMobile();

        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                    this.categories = this.categoriesData;
                }
            }, 100);
        },
    },
};
</script>

<style>
/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.close {
    color: white;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: red;
    text-decoration: none;
    cursor: pointer;
}
</style>