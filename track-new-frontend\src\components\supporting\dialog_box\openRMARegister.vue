<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-3/4 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300  text-sm h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <!-- <div class="modal-content "> -->
            <div class="set-header-background justify-between items-center flex py-2">
                <h2 class="text-white text-center ml-12 text-xl py-1">
                    {{ type == 'edit' ? 'Edit a RMA' : 'Register a RMA' }}</h2>
                <p class="pr-5 cursor-pointer text-white font-bold" @click="cancelModal"><font-awesome-icon
                        icon="fa-solid fa-xmark" /></p>
            </div>
            <!-- Content based on selected option -->
            <div class="min-h-screen bg-gray-100 p-4" :class="{ 'mb-[60px]': isMobile }">
                <div class="bg-white rounded-lg shadow-md p-6 py-3">
                    <h2 class="text-lg font-semibold mb-3">New Repair</h2>

                    <!-- Progress Bar -->
                    <div class="flex items-center justify-center mb-6">
                        <div class="block justify-center items-center">
                            <div :class="{ 'bg-blue-500': step >= 1, 'bg-gray-300': step < 1, }"
                                class="flex justify-center items-center  w-8 h-8 text-white rounded-full mx-3 sm:mx-5">
                                <span v-if="!formValues.customer_id">1</span>
                                <span v-else="!formValues.customer_id"><font-awesome-icon
                                        icon="fa-solid fa-user" /></span>
                            </div>
                            <span :class="{ 'text-blue-500': step >= 1, 'text-gray-300': step < 1 }"
                                class="text-center text-xs">Customer</span>
                        </div>
                        <!-- <div class="h-1"
                            :class="{ 'bg-blue-500 w-full': step === 2, 'bg-gray-300 w-full': step !== 1 }"></div> -->
                        <div class="h-1" :class="{ 'bg-blue-500 w-1/4': step >= 2, 'bg-gray-300 w-1/4': step < 2 }">
                        </div>
                        <div class="block items-center mx-4">
                            <div :class="{ 'bg-blue-500': step >= 2, 'bg-gray-300': step < 2 }"
                                class="w-8 h-8 text-white rounded-full flex items-center justify-center">
                                <span v-if="!formValues.product_id">2</span>
                                <span v-else="!formValues.product_id">
                                    <font-awesome-icon icon="fa-solid fa-tag" />
                                </span>
                            </div>
                            <span :class="{ 'text-blue-500': step >= 2, 'text-gray-500': step < 2 }"
                                class="text-center text-xs">Repair</span>
                        </div>
                        <!-- <div class="w-1/4 h-1" :class="{ 'bg-blue-500': step === 2, 'bg-gray-300': step !== 2 }"></div> -->
                        <div class="h-1" :class="{ 'bg-blue-500 w-1/4': step === 3, 'bg-gray-300 w-1/4 ': step !== 3 }">
                        </div>
                        <div class="block items-center ml-4">
                            <div :class="{ 'bg-blue-500': step === 3, 'bg-gray-300': step !== 3 }"
                                class="w-8 h-8 text-white rounded-full flex items-center justify-center ml-2">
                                3
                            </div>
                            <span :class="{ 'text-blue-500': step === 3, 'text-gray-500': step !== 3 }"
                                class="text-center text-xs">Custom</span>
                        </div>
                    </div>

                    <!-- Customer Info Form -->
                    <div v-if="step === 1" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- Left Side -->
                        <div>
                            <!--search customer-->
                            <!--  <div class="relative">
                                <p class="font-semibold text-md mb-2 border-b border-gray-600 py-1">
                                    <font-awesome-icon icon="fa-solid fa-magnifying-glass" class="px-1" />
                                    Search data:
                                </p>
                                
                            <div class="flex items-center border border-gray-300 rounded my-2">
                                <input v-model="formData.searchQuery" @input="changeFilter" type="text"
                                    placeholder="Search here" class="px-4 py-2 rounded-none w-full focus:border-none"
                                    @focus="changeFilter" @blur="closeDropdown('search')"
                                    @keydown.enter="handleEnterKey('search', filteredOptions_search)"
                                    @keydown.down.prevent="handleDownArrow(filteredOptions_search)"
                                    @keydown.up.prevent="handleUpArrow(filteredOptions_search)" />
                                <select v-model="formData.selectedFilter"
                                    class="px-1 py-2 bg-white rounded-none focus:border-none border-l border-gray-400">
                                    <option value="serialNumber">Serial number</option>
                                    <option value="customer">Customer</option>
                                </select>
                                <button
                                    class="flex items-center px-4 py-3 bg-blue-500 text-white hover:bg-blue-600 outline-none">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>

                            <div v-if="filteredOptions_search && filteredOptions_search.length > 0 && formData.searchQuery && formData.searchQuery.length > 3 && this.formData.dropdownOpen"
                                class="absolute top-full left-0 right-0 z-10  mt-2 bg-white border border-gray-300 rounded shadow-lg">
                                <ul ref="dropdown" @mousedown.prevent="preventBlur('customer')"
                                    class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                                    <li v-if="formData.selectedFilter && formData.selectedFilter == 'customer'"
                                        v-for="(option, index) in filteredOptions_search" :key="index"
                                        :class="{ 'bg-gray-200': index === selectedIndex }" ref="dropdownItems"
                                        class="px-4 py-2 hover:bg-gray-100 cursor-pointer"
                                        @click="selectOption(option)">
                                        {{ option.first_name + (option.last_name ? ' ' + option.last_name : '') }} -
                                        {{
                                            option.contact_number }}
                                    </li>
                                    <li v-if="formData.selectedFilter && formData.selectedFilter == 'serialNumber'"
                                        v-for="(option, index) in filteredOptions_search" :key="index"
                                        :class="{ 'bg-gray-200': index === selectedIndex }" ref="dropdownItems"
                                        class="px-4 py-2 hover:bg-gray-100 cursor-pointer border"
                                        @click="selectOption(option)">
                                        Serial Number: {{ option.serial_number }} <br>
                                        Invoice: {{ option.sale && option.sale.id && option.sale.invoice_id ?
                                            option.sale.invoice_id : '' }}<br>
                                        Product: {{ option.product && option.product.length > 0 ?
                                            option.product[0].product_name : '' }}<br>
                                        Brand: {{ option.product && option.product.length > 0 &&
                                            option.product[0].brand ? option.product[0].brand.brand_name : ''
                                        }}<br>
                                        Customer: {{ option.client && option.client.length > 0 ?
                                            option.client[0].first_name + (option.client[0].last_name ? ' '
                                                +
                                                option.client[0].last_name : '') + ' - ' +
                                            option.client[0].contact_number : '' }}<br>
                                        Supplier:{{ option.suppliers && option.suppliers.length > 0 ?
                                            option.suppliers[0].supplier_name : '' }}
                                    </li>
                                </ul>
                            </div>
                        </div> -->
                            <!--customer info-->
                            <p class="font-semibold text-md mb-2 border-b border-gray-600 py-1"><font-awesome-icon
                                    icon="fa-regular fa-user" class="px-1" />
                                Customer Info:</p>
                            <div class="mb-4 relative">
                                <div class="flex justify-between items-center">
                                    <label for="customer" class="block text-gray-700 font-semibold">Customer *</label>
                                    <button class="text-blue-500" @click="openModal">+ Create a new Customer</button>
                                </div>
                                <input id="customer" v-model="formValues.customer" @input="handleDropdownInput()"
                                    placeholder="Select the customer"
                                    @focus="handleDropdownInput(), isDropdownOpen = true, isInputFocused.customer = true"
                                    @blur="closeDropdown('customer')"
                                    @keydown.enter="handleEnterKey('customer', filteredCustomerOptions)"
                                    @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                                    @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="customer"
                                    class="border border-gray-300 w-full rounded py-2 px-8 leading-tight focus:outline-none focus:shadow-outline"
                                    :class="{ 'bg-gray-300': reset_data.customer }" />
                                <span class="absolute inset-y-0 left-0 flex items-center pl-2 top-5 text-gray-400">
                                    <font-awesome-icon icon="fa-solid fa-magnifying-glass" />
                                </span>
                                <span v-if="reset_data && reset_data.customer" @click="resetData('customer')"
                                    @mouseover="mouseIsOnOptionReset(true, 'customer')"
                                    @mouseleave="mouseIsOnOptionReset(false, 'customer')"
                                    class="absolute inset-y-0 -right-5 -top-12 flex items-center pl-2 z-50 text-red-600 cursor-pointer">
                                    <font-awesome-icon icon="fa-solid fa-xmark" size="lg" />
                                </span>

                                <!--customer list dropdown-->
                                <div v-if="isDropdownOpen"
                                    class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                    style="z-index: 100;" @mousedown.prevent="preventBlur('customer')">
                                    <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                                        @click="selectDropdownOption(option)"
                                        :class="{ 'bg-gray-200': index === selectedIndex }"
                                        class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                                        {{ option.first_name + (option.last_name ? ' ' + option.last_name : '') }} - {{
                                            option.contact_number }}
                                    </p>
                                </div>
                            </div>
                            <p v-if="formData.customer && formData.customer.id && formData.invoices && formData.invoices.length > 0"
                                @click="openSerial" class="cursor-pointer text-blue-600 hover:text-blue-700">
                                Select the Product
                                <font-awesome-icon icon="fa-solid fa-arrow-pointer" />
                            </p>
                        </div>
                    </div>
                    <!-- Repair Info Form -->
                    <div>
                        <!-- <div v-if="step === 2" class="w-full flex justify-end items-center py-2">
                            <button class=" px-2 py-1 rounded"
                                :class="{ 'bg-[#2196f3] text-white': bulkInsert, 'bg-gray-300': !bulkInsert }"
                                @click="bulkInsertActions">
                                <font-awesome-icon icon="fa-solid fa-list-ol" class="px-1" />
                                Bulk Insert</button>
                        </div> -->
                        <div v-if="step === 2 && !bulkInsert" class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <!-- Repair Product Info -->
                            <div>
                                <h3 class="font-semibold text-md mb-2 border-b border-gray-600"> <font-awesome-icon
                                        icon="fa-solid fa-tag" class="px-1" /> Repair Product Info</h3>
                                <div class="mb-4">
                                    <div class="flex justify-between items-center">
                                        <label for="product" class="block text-gray-700 font-semibold">Product for
                                            repair
                                            *</label>
                                        <button class="text-blue-500" @click="addNewItemModal">+ Create a new
                                            Product</button>
                                    </div>
                                    <div class='relative'>
                                        <!---search item--->
                                        <input
                                            class="flex border justify-between py-2 px-8 w-full outline-none rounded-none"
                                            type="text" v-model="selected_item" ref="selectItemField"
                                            @input="filterProducts()"
                                            @keydown.enter="handleEnterKeyProduct(0, 'product')"
                                            @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                            @keydown.up.prevent="handleUpArrow(filteredProductList)"
                                            @focus="focusSearch('product'), filterProducts" @blur="blurItemDropdown"
                                            :class="{ 'border-blue-600': isFormFocus.selected_item, 'bg-gray-300': reset_data.select_item }"
                                            placeholder="Item name / barcode" />
                                        <span class="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-400">
                                            <font-awesome-icon icon="fa-solid fa-magnifying-glass" />
                                        </span>
                                        <span v-if="reset_data && reset_data.select_item" @click="resetData('product')"
                                            @mouseover="mouseIsOnOptionReset(true, 'product')"
                                            @mouseleave="mouseIsOnOptionReset(false, 'product')"
                                            class="absolute inset-y-0 -right-5 -top-12 flex items-center pl-2 z-50 text-red-500 cursor-pointer">
                                            <font-awesome-icon icon="fa-solid fa-xmark" size="lg" />
                                        </span>
                                        <!--Item dropdown-->
                                        <ul v-if="productDropdown"
                                            class="absolute w-full max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm cursor-pointer"
                                            style="z-index: 150;"
                                            :style="{ width: $refs.selectItemField.offsetWidth + 'px' }"
                                            @mouseover="mouseIsOnOption(true)" @mouseleave="mouseIsOnOption(false)">
                                            <li class="hover:bg-gray-300 px-3 py-1 border"
                                                v-for="(product, i) in filteredProductList" :key="i"
                                                :class="{ 'bg-gray-200': i === selectedIndex }"
                                                @click="selectProduct(product)">
                                                {{ product.barcodes.barcode + ' - ' + product.products.product_name }}
                                            </li>
                                            <li v-if="filteredProductList.length === 0 && selected_item && selected_item.length > 1 && findExistItem()"
                                                @click="addNewItemModal"
                                                class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">
                                                +
                                                New Product</li>
                                        </ul>
                                    </div>
                                </div>
                                <!--brand-->
                                <div class="mb-4 relative">
                                    <label for="brand" class="block text-gray-700 font-semibold">Brand *</label>
                                    <input id="brand" v-model="formValues.brand"
                                        class="w-full p-2 border border-gray-300 rounded px-8" type="text"
                                        ref="brandField" @input="filterBrand()"
                                        @keydown.enter="handleEnterKeyProduct('brand')"
                                        @keydown.down.prevent="handleDownArrow(filterBrandList)"
                                        @keydown.up.prevent="handleUpArrow(filterBrandList)"
                                        @focus="focusSearch('brand'), filterBrand()" @blur="blurItemDropdown('brand')"
                                        placeholder="Search brand" :class="{ 'bg-gray-300': reset_data.brand }" />
                                    <span class="absolute inset-y-0 left-0 flex items-center pl-2 top-5 text-gray-400">
                                        <font-awesome-icon icon="fa-solid fa-magnifying-glass" />
                                    </span>
                                    <span v-if="reset_data && reset_data.brand" @click="resetData('brand')"
                                        @mouseover="mouseIsOnOptionReset(true, 'brand')"
                                        @mouseleave="mouseIsOnOptionReset(false, 'brand')"
                                        class="absolute inset-y-0 -right-5 -top-12 flex items-center pl-2 z-50 text-red-600 cursor-pointer">
                                        <font-awesome-icon icon="fa-solid fa-xmark" size="lg" />
                                    </span>
                                    <ul v-if="currentBrandList && brandDropdown"
                                        class="absolute w-full max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm cursor-pointer"
                                        style="z-index: 150;" :style="{ width: $refs.brandField.offsetWidth + 'px' }"
                                        @mouseover="mouseIsOnOption(true, 'brand')"
                                        @mouseleave="mouseIsOnOption(false, 'brand')">
                                        <li v-if="currentBrandList.length > 0"
                                            class="hover:bg-gray-300 px-3 py-1 border"
                                            v-for="(brand, i) in filterBrandList" :key="i"
                                            :class="{ 'bg-gray-200': i === selectedIndex }" @click="selectBrand(brand)">
                                            {{ brand.brand_name }}
                                        </li>
                                        <li v-if="filterBrandList.length === 0" @click="openBrandModal"
                                            class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">
                                            + New Brand</li>
                                    </ul>
                                </div>
                                <!--supplier-->
                                <div class="mb-4 relative">
                                    <label for="supplier" class="block text-gray-700 font-semibold">Supplier *</label>
                                    <input id="supplier" v-model="formValues.supplier"
                                        class="w-full p-2 border border-gray-300 rounded px-8" type="text"
                                        ref="supplierField" @input="filterSupplier()"
                                        @keydown.enter="handleEnterKeyProduct('supplier')"
                                        @keydown.down.prevent="handleDownArrow(filterSupplierList)"
                                        @keydown.up.prevent="handleUpArrow(filterSupplierList)"
                                        @focus="focusSearch('supplier'), filterSupplier()"
                                        @blur="blurItemDropdown('supplier')" placeholder="Search supplier"
                                        :class="{ 'bg-gray-300': reset_data.supplier }" />
                                    <span class="absolute inset-y-0 left-0 flex items-center pl-2 top-5 text-gray-400">
                                        <font-awesome-icon icon="fa-solid fa-magnifying-glass" />
                                    </span>
                                    <span v-if="reset_data && reset_data.supplier" @click="resetData('supplier')"
                                        @mouseover="mouseIsOnOptionReset(true, 'supplier')"
                                        @mouseleave="mouseIsOnOptionReset(false, 'supplier')"
                                        class="absolute inset-y-0 -right-5 -top-12 flex items-center pl-2 z-50 text-red-600 cursor-pointer">
                                        <font-awesome-icon icon="fa-solid fa-xmark" size="lg" />
                                    </span>
                                    <ul v-if="currentSupplier && supplierDropdown"
                                        class="absolute w-full max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm cursor-pointer"
                                        style="z-index: 150;" :style="{ width: $refs.supplierField.offsetWidth + 'px' }"
                                        @mouseover="mouseIsOnOption(true, 'supplier')"
                                        @mouseleave="mouseIsOnOption(false, 'supplier')">
                                        <li v-if="currentSupplier.length > 0" class="hover:bg-gray-300 px-3 py-1 border"
                                            v-for="(supplier, i) in filterSupplierList" :key="i"
                                            :class="{ 'bg-gray-200': i === selectedIndex }"
                                            @click="selectSupplier(supplier)">
                                            {{ supplier.name }}
                                        </li>
                                        <li v-if="filterSupplierList.length === 0" @click="openSupplierModal"
                                            class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">
                                            + New Supplier</li>
                                    </ul>
                                </div>
                                <div class="mb-4">
                                    <label for="serialNumbers" class="block text-gray-700 font-semibold">Serial
                                        numbers *</label>
                                    <input id="serialNumbers" v-model="formValues.serial_number"
                                        class="w-full p-2 border border-gray-300 rounded" type="text" />
                                </div>
                                <div class="mb-4">
                                    <label for="warranty" class="block text-gray-700 font-semibold">Warranty in
                                        months</label>
                                    <input type="number" id="warranty" v-model="formValues.warranty"
                                        @focus="isInputFocused.warranty = true"
                                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                                    <!-- <select id="warranty" v-model="formValues.warranty"
                                        class="w-full p-2 border border-gray-300 rounded">
                                        <option value="" disabled selected>Please select a warranty ...</option>
                                        <option v-for="w in warranties" :key="w">{{ w }}</option>
                                    </select> -->
                                </div>
                                <div class="mb-4">
                                    <label for="productCondition" class="block text-gray-700 font-semibold">Product
                                        condition</label>
                                    <input id="productCondition" v-model="formValues.product_condition"
                                        class="w-full p-2 border border-gray-300 rounded" type="text" />
                                </div>
                                <div class="mb-4 relative">
                                    <label for="accessories"
                                        class="block text-gray-700 font-semibold">Accessories</label>
                                    <div
                                        class="absolute right-0 border text-xs border-green-700 text-green-700 px-2 -top-[3px] rounded">
                                        <button @click="openAccessories">Add +</button>
                                    </div>
                                    <div class="relative inline-block w-full" ref="rmaAccessories">
                                        <div
                                            class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex flex-wrap items-center px-4 py-2">

                                            <div class="flex items-center bg-gray-500 text-white rounded px-2 py-1 mr-2 mb-2"
                                                v-if="formValues.accessories && Array.isArray(formValues.accessories) && formValues.accessories.length > 0"
                                                v-for="(opt, i) in formValues.accessories">
                                                {{ opt.name }}
                                                <button @click="removeOption(opt)" class="ml-1">x</button>
                                            </div>
                                            <div>
                                                <input @click="toggleDropdown('accessories')"
                                                    placeholder="click here..." class="px-2 py-1 rounded border" />
                                            </div>
                                        </div>

                                        <div v-if="isOpen_access.accessories"
                                            class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                            <input type="text" ref="accessoriesInput" v-model="searchTerm.accessories"
                                                @keydown.enter="handleEnterKeyProduct('accessories', filteredOptions)"
                                                @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                                @keydown.up.prevent="handleUpArrow(filteredOptions)"
                                                placeholder="Search..."
                                                class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                            <ul class="max-h-60 overflow-auto">
                                                <li v-for="(option, index) in filteredOptions" :key="index"
                                                    @click="selectOptionData(option)"
                                                    class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                                    :class="{ 'bg-gray-200': index === selectedIndex }">
                                                    <span class="py-1 px-2 rounded">
                                                        {{ option.name }}
                                                    </span>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Repair Problem Description -->
                            <div>
                                <h3 class="font-semibold text-md mb-2 border-b border-gray-600"> <font-awesome-icon
                                        icon="fa-solid fa-user-secret" class="px-1" /> Repair Problem Description</h3>
                                <div class="mb-4">
                                    <label for="problemDescription" class="block text-gray-700 font-semibold">Problem
                                        description</label>
                                    <textarea id="problemDescription" v-model="formValues.problem_description"
                                        class="w-full p-2 border border-gray-300 rounded" rows="3"></textarea>
                                </div>
                                <div class="mb-4">
                                    <label for="technicianNotes" class="block text-gray-700 font-semibold">Technician
                                        notes</label>
                                    <textarea id="technicianNotes" v-model="formValues.technician_notes"
                                        class="w-full p-2 border border-gray-300 rounded" rows="3"></textarea>
                                </div>
                                <div class="mb-4">
                                    <!--Internal notes-->
                                    <label for="internalNotes" class="block text-gray-700 font-semibold">Courier Name /
                                        Number</label>
                                    <textarea id="internalNotes" v-model="formValues.internal_notes"
                                        class="w-full p-2 border border-gray-300 rounded" rows="3"></textarea>
                                </div>
                            </div>
                        </div>
                        <!---multiple product-->
                        <div v-if="step === 2 && bulkInsert">
                            <!-- Repair Product Info -->
                            <table v-if="!isMobile" class="min-w-full bg-white border border-gray-300 rounded"
                                ref="itemTable">
                                <thead>
                                    <tr>
                                        <th class="px-4 py-2 border-b">#</th>
                                        <th class="px-4 py-2 border-b">Product for repair</th>
                                        <th class="px-4 py-2 border-b">Warranty</th>
                                        <th class="px-4 py-2 border-b">Serial numbers</th>
                                        <th class="px-4 py-2 border-b">Problem description</th>
                                        <th class="px-4 py-2 border-b">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-if="bulkInsert_data.length > 0" v-for="(opt, index) in bulkInsert_data"
                                        :key="index">
                                        <!--count-->
                                        <td class="px-4 py-2 border-b">
                                            <p class="text-center">{{ index + 1 }}</p>
                                        </td>
                                        <!--product name-->
                                        <td class="px-4 py-2 border-b">
                                            <div class='relative'>
                                                <!---search item--->
                                                <input
                                                    class="flex border justify-between py-2 px-1 w-full outline-none rounded-none"
                                                    type="text" v-model="opt.selected_item"
                                                    :ref="'selectItemField' + index"
                                                    @input="filterProducts('bulk', index)"
                                                    @keydown.enter="handleEnterKeyProduct(0, 'product')"
                                                    @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                                    @keydown.up.prevent="handleUpArrow(filteredProductList)"
                                                    @focus="focusSearch('product', index)" @blur="blurItemDropdown"
                                                    :class="{ 'border-blue-600': isFormFocus.selected_item === index, 'bg-gray-300': reset_data.select_item === index }"
                                                    placeholder="Item name / barcode" />
                                                <!-- <span
                                                    class="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-400">
                                                    <font-awesome-icon icon="fa-solid fa-magnifying-glass" />
                                                </span> -->
                                                <span
                                                    v-if="reset_data && reset_data.select_item >= 0 && reset_data.select_item === index"
                                                    @mouseover="mouseIsOnOptionReset(index, 'product')"
                                                    @mouseleave="mouseIsOnOptionReset(false, 'product')"
                                                    @click="resetData('product', index)"
                                                    class="absolute inset-y-0 -right-5 -top-12 flex items-center pl-2 z-50 text-red-600 cursor-pointer">
                                                    <font-awesome-icon icon="fa-solid fa-xmark" size="lg" />
                                                </span>
                                                <!--Item dropdown-->
                                                <ul v-if="productDropdown === index"
                                                    class="absolute w-full max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm cursor-pointer"
                                                    style="z-index: 150;" @mouseover="mouseIsOnOption(index)"
                                                    @mouseleave="mouseIsOnOption(false)">
                                                    <li class="hover:bg-gray-300 px-3 py-1 border"
                                                        v-for="(product, i) in filteredProductList" :key="i"
                                                        :class="{ 'bg-gray-200': i === selectedIndex }"
                                                        @click="selectProduct(product)">
                                                        {{ product.barcodes.barcode + ' - ' +
                                                            product.products.product_name }}
                                                    </li>
                                                    <li v-if="filteredProductList.length === 0"
                                                        @click="addNewItemModal(index)"
                                                        class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">
                                                        +
                                                        New Product</li>
                                                </ul>
                                            </div>
                                        </td>
                                        <!--waranty-->
                                        <td class="px-4 py-2 border-b">
                                            <div>
                                                <select id="warranty" v-model="opt.warranty"
                                                    class="w-full p-2 border border-gray-300 rounded w-full">
                                                    <option value="" disabled selected>Please select a warranty ...
                                                    </option>
                                                    <option v-for="w in warranties" :key="w">{{ w }}</option>
                                                </select>
                                            </div>
                                        </td>
                                        <!--Serial number-->
                                        <td class="px-4 py-2 border-b">
                                            <div>
                                                <input id="serialNumbers" v-model="opt.serial_number"
                                                    placeholder="Enter serial number"
                                                    class="w-full p-2 border border-gray-300 rounded" type="text" />
                                            </div>
                                        </td>
                                        <!--problem description-->
                                        <td class="px-4 py-2 border-b">
                                            <div>
                                                <textarea id="problemDescription" v-model="opt.problem_description"
                                                    class="w-full p-2 border border-gray-300 rounded"
                                                    placeholder="Enter the problem description" rows="3"></textarea>
                                            </div>
                                        </td>
                                        <!--actions-->
                                        <td class="px-4 py-2 border-b">
                                            <div class="flex justify-center items-center">
                                                <button class="font-bold text-md text-[#e53935]"
                                                    @click="removeThisRow(index)"><font-awesome-icon
                                                        icon="fa-solid fa-xmark" /></button>
                                            </div>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div v-if="bulkInsert_data.length > 0 && isMobile" v-for="(opt, index) in bulkInsert_data"
                                :key="index">
                                <div class="border rounded bg-gray-200 shadow" :class="{ 'mt-3': index > 0 }">
                                    <!--count-->
                                    <div class="px-4 flex justify-between items-center">
                                        <p class="text-center bg-white px-1 rounded-full">{{ index + 1 }}</p>
                                        <!--actions-->
                                        <div class="px-4 py-2">
                                            <div class="flex justify-center items-center">
                                                <button class="font-bold text-md text-[#e53935]"
                                                    @click="removeThisRow(index)"><font-awesome-icon
                                                        icon="fa-solid fa-xmark" /></button>
                                            </div>
                                        </div>
                                    </div>
                                    <!--product name-->
                                    <div class="px-4 py-2">
                                        <div class='relative'>
                                            <!---search item--->
                                            <input
                                                class="flex border justify-between py-2 px-1 w-full outline-none rounded-none"
                                                type="text" v-model="opt.selected_item" :ref="'selectItemField' + index"
                                                @input="filterProducts('bulk', index)"
                                                @keydown.enter="handleEnterKeyProduct(0, 'product')"
                                                @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                                @keydown.up.prevent="handleUpArrow(filteredProductList)"
                                                @focus="focusSearch('product', index)" @blur="blurItemDropdown"
                                                :class="{ 'border-blue-600': isFormFocus.selected_item === index, 'bg-gray-300': reset_data.select_item === index }"
                                                placeholder="Item name / barcode" />
                                            <!-- <span
                                                    class="absolute inset-y-0 left-0 flex items-center pl-2 text-gray-400">
                                                    <font-awesome-icon icon="fa-solid fa-magnifying-glass" />
                                                </span> -->
                                            <span
                                                v-if="reset_data && reset_data.select_item >= 0 && reset_data.select_item === index"
                                                @mouseover="mouseIsOnOptionReset(index, 'product')"
                                                @mouseleave="mouseIsOnOptionReset(false, 'product')"
                                                @click="resetData('product', index)"
                                                class="absolute inset-y-0 -right-5 -top-12 flex items-center pl-2 z-50 text-red-600 cursor-pointer">
                                                <font-awesome-icon icon="fa-solid fa-xmark" size="lg" />
                                            </span>
                                            <!--Item dropdown-->
                                            <ul v-if="productDropdown === index"
                                                class="absolute w-full max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm cursor-pointer"
                                                style="z-index: 150;" @mouseover="mouseIsOnOption(index)"
                                                @mouseleave="mouseIsOnOption(false)">
                                                <li class="hover:bg-gray-300 px-3 py-1 border"
                                                    v-for="(product, i) in filteredProductList" :key="i"
                                                    :class="{ 'bg-gray-200': i === selectedIndex }"
                                                    @click="selectProduct(product)">
                                                    {{ product.barcodes.barcode + ' - ' +
                                                        product.products.product_name }}
                                                </li>
                                                <li v-if="filteredProductList.length === 0"
                                                    @click="addNewItemModal(index)"
                                                    class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">
                                                    +
                                                    New Product</li>
                                            </ul>
                                        </div>
                                    </div>
                                    <!--waranty-->
                                    <div class="px-4 py-2">
                                        <div>
                                            <select id="warranty" v-model="opt.warranty"
                                                class="w-full p-2 border border-gray-300 rounded w-full">
                                                <option value="" disabled selected>Please select a warranty ...
                                                </option>
                                                <option v-for="w in warranties" :key="w">{{ w }}</option>
                                            </select>
                                        </div>
                                    </div>
                                    <!--Serial number-->
                                    <div class="px-4 py-2">
                                        <div>
                                            <input id="serialNumbers" v-model="opt.serial_number"
                                                placeholder="Enter serial number"
                                                class="w-full p-2 border border-gray-300 rounded" type="text" />
                                        </div>
                                    </div>
                                    <!--problem description-->
                                    <div class="px-4 py-2">
                                        <div>
                                            <textarea id="problemDescription" v-model="opt.problem_description"
                                                class="w-full p-2 border border-gray-300 rounded"
                                                placeholder="Enter the problem description" rows="3"></textarea>
                                        </div>
                                    </div>

                                </div>
                            </div>
                            <div v-if="bulkInsert" class="py-2">
                                <button class="bg-[#00bcd4] text-white rounded px-2 py-2" @click="addOneMoreRow">Add a
                                    product</button>
                            </div>

                        </div>
                        <!-- Custom Fields -->
                        <div v-if="step === 3">
                            <h3 class="font-semibold text-md mb-2 border-b border-gray-600"><font-awesome-icon
                                    icon="fa-solid fa-align-justify" class="px-1" /> Custom Fields</h3>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="mb-4">
                                    <label for="customerInvoice" class="block text-gray-700 font-semibold">Customer
                                        Invoice</label>
                                    <input id="customerInvoice" v-model="formValues.customer_invoice"
                                        class="w-full p-2 border border-gray-300 rounded" type="text" />
                                </div>
                                <div class="mb-4">
                                    <label for="dateOfPurchase" class="block text-gray-700 font-semibold">Date of
                                        Purchase</label>
                                    <input id="dateOfPurchase" v-model="formValues.date_of_purchase"
                                        class="w-full p-2 border border-gray-300 rounded" type="date" v-datepicker />
                                </div>
                                <div class="mb-4">
                                    <label for="supplierRMA" class="block text-gray-700 font-semibold">Supplier
                                        RMA</label>
                                    <input id="supplierRMA" v-model="formValues.supplier_rma"
                                        class="w-full p-2 border border-gray-300 rounded" type="text" />
                                </div>
                                <div class="mb-4">
                                    <label for="enteredUnderIPR" class="block text-gray-700 font-semibold">Entered under
                                        IPR</label>
                                    <input id="enteredUnderIPR" v-model="formValues.under_ipr"
                                        class="w-full p-2 border border-gray-300 rounded" type="text" />
                                </div>
                                <!--notifications-->
                                <div class="flex items-center md:col-span-2">
                                    <label v-if="companywhatsapp" class="flex justify-center items-center mr-2">
                                        <div class="relative w-12 h-6 rounded-full transition-colors duration-300 flex items-center"
                                            @click="iswhatsapp = !iswhatsapp"
                                            :class="{ 'bg-green-600': iswhatsapp, 'bg-gray-300': !iswhatsapp }">
                                            <div class="absolute top-0 left-0 w-6 h-6 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                                :class="{ 'translate-x-6': iswhatsapp, 'translate-x-0': !iswhatsapp }">
                                            </div>
                                        </div>
                                        <font-awesome-icon icon="fa-brands fa-whatsapp" size="xl" class="ml-2"
                                            style="color:green" />
                                    </label>
                                    <button v-else @click="navigateToWhatsApp" class="text-red-600">Connect
                                        <font-awesome-icon icon="fa-brands fa-whatsapp" size="xl" class="ml-2"
                                            style="color:red" /></button>
                                    <label class="flex justify-center items-center ml-2">
                                        <div class="relative w-12 h-6 rounded-full transition-colors duration-300 flex items-center"
                                            @click="issms = !issms"
                                            :class="{ 'bg-green-600': issms, 'bg-gray-300': !issms }">
                                            <div class="absolute top-0 left-0 w-6 h-6 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                                :class="{ 'translate-x-6': issms, 'translate-x-0': !issms }">
                                            </div>
                                        </div>
                                        <font-awesome-icon icon="fa-solid fa-comment-sms" size="xl" class="ml-2"
                                            style="color:blueviolet" />
                                    </label>
                                    <p v-if="!isMobile" class="ml-1 text-xs">Send alert to Customer</p>
                                    <div>
                                        <div class=" ml-1" @mouseover="tooltip.info_notify = true"
                                            @mouseleave="tooltip.info_notify = false">
                                            <font-awesome-icon icon="fa-solid fa-circle-info" size="lg"
                                                class="text-blue-800" />
                                        </div>
                                        <!---tooltip-->
                                        <div v-if="tooltip.info_notify"
                                            class="absolute flex flex-col items-center group-hover:flex -mt-14 -ml-[55px]">
                                            <span
                                                class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                                <p>sent alert to customer</p>
                                            </span>
                                            <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex justify-between mt-6">
                        <button v-if="step > 1" @click="prevStep" class="bg-gray-300 text-gray-700 px-4 py-2 rounded">
                            Previous
                        </button>
                        <button v-if="step < 3" @click="nextStep" class="bg-blue-500 text-white px-4 py-2 rounded">
                            Next
                        </button>
                        <button v-if="step === 3" @click="createRepair"
                            class="bg-blue-500 text-white px-4 py-2 rounded">
                            Create Repair
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModal" :userName="customerName"
            :type="typeOfRegister"></customerRegister>
        <addNewItem :showModal="open_add_newItem" @close-modal="closeItemModal" :product_name="selected_item">
        </addNewItem>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <!--add category brand unit model-->
        <categoryBrandUnit :showModal="open_model" :type="type_model" :categoriesData="list_data"
            @close-modal="closecategoryBrandUnit"></categoryBrandUnit>
        <supplierRegister :show-modal="open_supplier" :type="'add'" @close-modal="closeSupplier" :companyId="companyId">
        </supplierRegister>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <accessories :showModal="open_accessories" @close-modal="closeAccessories" :type="'Accessories Management'"
            :categoriesData="currentAccessoriesList">
        </accessories>
        <rmaSelectserial :showModal="open_select_serial"
            :customer="formData && formData.customer ? formData.customer : null"
            :invoices="formData && formData.invoices ? formData.invoices : null" @close="closeserial"
            :companyId="companyId"></rmaSelectserial>
        <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
    </div>
</template>


<script>
import customerRegister from './customerRegister.vue';
import addNewItem from './addNewItem.vue';
import categoryBrandUnit from './categoryBrandUnit.vue';
import supplierRegister from './supplierRegister.vue';
import confirmbox from './confirmbox.vue';
import { mapGetters, mapActions } from 'vuex';
import axios from 'axios';
import accessories from './accessories.vue';
import rmaSelectserial from './rmaSelectserial.vue';
import noAccessModel from './noAccessModel.vue';
export default {
    components: {
        customerRegister,
        addNewItem,
        categoryBrandUnit,
        supplierRegister,
        confirmbox,
        accessories,
        rmaSelectserial,
        noAccessModel
    },
    props: {
        showModal: Boolean,
        type: String,
        companyId: String,
        userId: String,
        isMobile: Boolean,
        customer_id: {type: [String, Number]},
    },
    data() {
        return {
            isOpen: false,
            formValues: {},
            step: 1,
            selectedCustomer: {},
            //--loader---
            open_loader: false,
            //--customer--
            customer_list: [],
            mouseDownOnDropdown: false,
            selectedIndex: 0,
            isInputFocused: {},
            isDropdownOpen: false,
            filteredCustomerOptions: [],
            //--add new customer---
            typeOfRegister: 'add',
            customerName: '',
            showModal_customer: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            //--product list---
            selected_item: '',
            product: [],
            productDropdown: false,
            filteredProductList: [],
            pagination: {},
            isFormFocus: {},
            open_add_newItem: false,
            //---brand list--
            filterBrandList: [],
            brandDropdown: false,
            isMouseInOptionDrop: {},
            open_model: false,
            type_model: 'brand',
            list_data: [],
            //--supplier---
            supplierDropdown: false,
            filterSupplierList: [],
            open_supplier: false,
            bulkInsert: false,
            //---bluk insert Product--
            bulkInsert_data: [],
            //---reset selected data---
            reset_data: {},
            rest_mouseover: {},
            open_confirmBox: false,
            //--accessories---
            open_accessories: false,
            isOpen_access: { accessories: false },
            searchTerm: { accessories: '' },
            selectedOption: { accessories: null },
            selectedIndex: 0,
            //---search data
            formData: { selectedFilter: 'serialNumber' },
            filteredOptions_search: [],
            open_select_serial: false,
            //---no access---
            no_access: false,
            //--notifications---
            iswhatsapp: false,
            issms: false,
            //---tooltip---
            tooltip: {},
        };
    },
    methods: {
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('items', ['fetchItemList']),
        ...mapActions('brandUnitCategoryItem', ['fetchBrandList']),
        ...mapActions('supplier', ['fetchISupplierList']),
        ...mapActions('accessories', ['fetchAccessoriesList']),
        ...mapActions('searchSerial', ['fetchSerialList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),
        prevStep() {
            if (this.step > 1) {
                this.step--;
            }
        },
        nextStep() {
            if (this.step < 3) {
                if (this.step === 1 && this.formValues.customer_id) {
                    this.step++;
                } else if (this.step === 2) {
                    if (this.bulkInsert && this.bulkInsert_data && this.bulkInsert_data.length > 0 && this.bulkInsert_data[0].customer_id) {
                        this.step++;
                    } else if (this.formValues.product_id && this.formValues.brand_id && this.formValues.supplier_id && this.formValues.serial_number) {
                        this.step++;
                    } else {
                        this.message = !this.formValues.product_id || this.formValues.product_id === '' ? 'Please select the product..!' : !this.formValues.brand_id || this.formValues.brand_id === '' ? 'Please select the brand...!' : !this.formValues.supplier_id || this.formValues.supplier_id === '' ? 'Please select the supplier...!' : !this.formValues.serial_number || this.formValues.serial_number === '' ? 'Please enter the serial number...!' : 'Please fill as * fields';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                } else {
                    this.message = this.step === 1 ? 'Please select the customer..!' : 'Please select the product..!';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            } else {
                this.message = this.step === 1 ? 'Please select the customer..!' : 'Please select the product..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        cancelModal(data) {
            // this.$emit('close-modal');
            this.isOpen = false;
            if (data && data.id) {
                // Add a delay to allow the transition before emitting the close event
                setTimeout(() => {
                    this.$emit('close-modal', data);
                }, 300);
            } else {
                setTimeout(() => {
                    this.$emit('close-modal');
                }, 300);
            }
        },
        createRepair() {
            if (this.getplanfeatures('rma')) {
                this.no_access = true;
            } else {
                if (this.formValues) {
                    this.open_loader = true;
                    let sent_data = {
                        ...this.formValues, rma_status: 0, company_id: this.companyId,
                        iswhatsapp: this.companywhatsapp ? this.iswhatsapp : false,
                        issms: this.issms,
                    };

                    axios.post('/rmas', { ...sent_data })
                        .then(response => {
                            // console.log(response.data);
                            this.open_loader = false;
                            this.formValues = {};
                            this.step = 1;
                            this.cancelModal(response.data.data);
                        })
                        .catch(error => {
                            console.error('Error Post Employee', error);
                            this.open_loader = false;
                        })
                } else {
                    this.message = 'Please enter the invoice number..!';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            }
        },
        //---search the customer---
        //---customer--
        handleDropdownInput() {
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                    // if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                    //     this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    // }
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                            (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                    );
                    // if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                    //     this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    // }
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                if (this.customer_list && this.customer_list.length > 0) {
                    this.filteredCustomerOptions = this.customer_list;
                } else {
                    this.filteredCustomerOptions = [];
                }
            }
            this.focusSearch('customer');
            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
                this.scrollToSelectedItem();
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
                this.scrollToSelectedItem();
            }

        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
                this.scrollToSelectedItem();
            } else {
                this.selectedIndex = this.selectedIndex - 1;
                this.scrollToSelectedItem();
            }
        },

        //----customer dropdown--
        closeDropdown(type) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                // this.isInputFocused.customer = false;
            }
            if (this.reset_data && this.reset_data.customer && this.rest_mouseover && !this.rest_mouseover.customer) {
                this.reset_data.customer = false;
            }
            if (type && type == 'search' && this.formData && this.formData.dropdownOpen && !this.formData.mouseDownOnDropdown) {
                this.formData.dropdownOpen = false;
            }
        },
        //---customers dropdown
        selectDropdownOption(option) {
            this.formValues.customer_id = option.id;
            this.formValues.balance_amount = option.balance_amount;
            this.formValues.customer = option.first_name + (option.last_name ? ' ' + option.last_name : '') + ' - ' + option.contact_number;
            this.isDropdownOpen = false;
            this.selectedIndex = 0;
            if (!this.formValues.payment_amount && option.balance_amount) {
                this.formValues.payment_amount = option.balance_amount;
            }
            //---selecte sales invoices---
            // console.log(option, 'Waht is', option.id, 'EQEEQE');
            this.formData.customer = option;
            this.getCustomerData(option.id);
        },
        preventBlur(type) {
            if (type && type === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            }
            if (type && type === 'search') {
                this.formData.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.formData.mouseDownOnDropdown = false;
                });
            }
        },
        //---create new customer
        openModal() {
            this.customerName = this.formValues.customer;
            this.showModal_customer = true;
            this.isDropdownOpen = false;
        },
        //----cloase customer register---
        closeModal(data) {
            if (data) {
                // console.log('What happening', data);
                if (data.last_name) {
                    this.formValues.customer = data.first_name + ' ' + data.last_name + ' - ' + data.contact_number;
                    this.formValues.customer_id = data.id;
                } else {
                    this.formValues.customer = data.first_name + ' - ' + data.contact_number;
                    this.formValues.customer_id = data.id;
                }
                this.customer_list.push(data);
                // console.log(this.formValues.customer, 'RWRWRWR')
            }
            this.showModal_customer = false;
        },
        //-----filter product--
        filterProducts(type, index) {
            let enteredProductName = ''
            if (this.bulkInsert && index >= 0) {
                this.productDropdown = index;
                enteredProductName = this.bulkInsert_data[index].selected_item.toLowerCase();
            } else {
                this.productDropdown = true;
                enteredProductName = this.selected_item.toLowerCase();
            }
            // console.log(enteredProductName, 'Product name');
            if (this.product.length !== 0 && enteredProductName.length > 1) {
                // console.log('validation is done...1');
                let existingProducts = [];
                let existingProductCodes = [];
                this.filteredProductList = this.product.filter(opt => {
                    let isExistingName = false;
                    let isExistingCode = false;
                    if (existingProducts.length > 0 && opt.products && opt.products.product_name) {
                        isExistingName = existingProducts.includes(opt.products.product_name.toLowerCase());
                    }
                    if (existingProductCodes.length > 0 && opt.barcodes && opt.barcodes.barcode) {
                        isExistingCode = existingProductCodes.includes(opt.barcodes.barcode.toLowerCase());
                    }
                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.products && opt.products.product_name ? opt.products.product_name.toLowerCase().includes(enteredProductName) : false;
                    const codeMatch = opt.barcodes && opt.barcodes.barcode ? opt.barcodes.barcode.toLowerCase().includes(enteredProductName) : false;
                    // return nameMatch || codeMatch;
                    return (!isExistingName || !isExistingCode) && (nameMatch || codeMatch);
                });
                if (this.filteredProductList.length === 0 && this.pagination.product && Number(this.pagination.product.current_page) < this.pagination.product.last_page) {
                    this.getProductListData(1, 1000);
                }
            } else if (this.product && this.product.length > 0) {
                this.filteredProductList = this.product;
            }
        },
        //---add product option controll--
        findExistItem() {
            const enteredProductName = this.selected_item.toLowerCase();
            if (this.product.length !== 0 && enteredProductName.length > 1) {
                const existingProducts = this.product.map(item => item.products.product_name.toLowerCase());
                const existingProductCodes = this.product.map(item => item.barcodes && item.barcodes.barcode.toLowerCase());

                // Use a separate array to store existing product names and codes
                const existingNamesAndCodes = [...existingProducts, ...existingProductCodes];

                // Check if the entered product name or product code matches any existing product name or product code
                const isExisting = existingNamesAndCodes.includes(enteredProductName);
                // console.log(isExisting, 'Waht happening....!');
                return !isExisting;
            }
            return true;
        },
        selectProduct(selectedProduct) {
            if (selectedProduct && this.bulkInsert === false) {
                this.selected_item = selectedProduct.barcodes.barcode + ' - ' + selectedProduct.products.product_name;
                this.formValues.product_id = selectedProduct.product_id;
                this.formValues.warranty = selectedProduct.products.warranty >= 0 ? selectedProduct.products.warranty : 0;
                this.productDropdown = false;
            } else {
                this.bulkInsert_data[this.productDropdown].selected_item = selectedProduct.barcodes.barcode + ' - ' + selectedProduct.products.product_name;
                this.bulkInsert_data[this.productDropdown].product_id = selectedProduct.product_id;
                this.productDropdown = false;
            }

            this.filteredProductList = [];

        },
        //--always focus item fields--
        focusItemFields() {
            this.$nextTick(() => {
                if (this.$refs.selectItemField) {
                    this.$refs.selectItemField.focus();
                }
            });
        },

        //---blur item dropdown--
        blurItemDropdown(type) {
            if (type === 'brand') {
                this.isFormFocus.brand = false;
                if (!this.isMouseInOptionDrop.brand) {
                    this.brandDropdown = false;
                }
                if (this.reset_data && (this.reset_data.brand && this.rest_mouseover && !this.rest_mouseover.brand)) {
                    this.reset_data.brand = false;
                }
            } else if (type === 'supplier') {
                this.isFormFocus.supplier = false;
                if (!this.isMouseInOptionDrop.supplier) {
                    this.supplierDropdown = false;
                }
                if (this.reset_data && (this.reset_data.supplier && this.rest_mouseover && !this.rest_mouseover.supplier)) {
                    this.reset_data.supplier = false;
                }
            } else {
                this.isFormFocus.selected_item = false;
                if (this.isMouseInOption === false) {
                    this.productDropdown = false;
                }
            }
            if (this.reset_data && this.bulkInsert) {
                if (this.reset_data.select_item >= 0 && this.rest_mouseover && (this.rest_mouseover.product === false || this.rest_mouseover.product === undefined)) {
                    this.reset_data.select_item = false;
                }
            } else if (this.reset_data) {
                if (this.reset_data.select_item && this.rest_mouseover && !this.rest_mouseover.product) {
                    this.reset_data.select_item = false;
                }
            }
        },
        mouseIsOnOption(value, type) {
            if (type === 'brand') {
                this.isMouseInOptionDrop.brand = value;
            } else if (type === 'supplier') {
                this.isMouseInOptionDrop.supplier = value;
            }
            else {
                this.isMouseInOption = value;
            }
        },
        //----table--
        handleEnterKeyProduct(type, list_data) {
            // console.log(type, 'WWWWW happeningdgdgdg');
            if (type === 'brand') {
                if (this.filterBrandList.length > 0) {
                    // Call selectedProductData with the first item in filterBrandList
                    this.selectBrand(this.filterBrandList[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    // console.log('RRRRRRRRRRRRRRRR');
                    this.openBrandModal();
                }

            } else if (type === 'supplier') {
                if (this.filterSupplierList.length > 0) {
                    // Call selectedProductData with the first item in filterSupplierList
                    this.selectSupplier(this.filterSupplierList[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    this.openSupplierModal();
                }

            } else if (type === 'accessories') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            }
            else {
                if (this.filteredProductList.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectProduct(this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    if (this.bulkInsert && this.productDropdown >= 0) {
                        this.addNewItemModal(this.productDropdown);
                    } else {
                        this.addNewItemModal();
                    }
                }
            }
        },
        //get product list---
        getProductListData(page, per_page) {
            axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'items');
                    this.product = response.data.data;
                    this.pagination.product = response.data.pagination;
                    this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);

                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---add new item--
        addNewItemModal(index) {
            this.open_add_newItem = true;
            // this.productDropdown = false;
            if (this.bulkInsert && index >= 0 && this.bulkInsert_data.length > 0) {
                this.selected_item = this.bulkInsert_data[index].selected_item;
            }
        },
        closeItemModal(data) {
            if (data && data.id) {
                this.product.push(data);
                this.selectProduct(data);
            }
            this.open_add_newItem = false;
        },
        //---filter the brand--
        filterBrand() {
            if (this.formValues.brand) {
                const enteredProductName = this.formValues.brand.toLowerCase();
                this.brandDropdown = true;
                this.supplierDropdown = false;
                // console.log(enteredProductName, 'Product name');
                if (this.currentBrandList.length !== 0 && enteredProductName.length > 1) {
                    // console.log('validation is done...1');
                    this.filterBrandList = this.currentBrandList.filter(opt => {
                        // Check if the entered product name or product code matches any existing product name or product code
                        const nameMatch = opt.brand_name.toLowerCase().includes(enteredProductName);
                        // return nameMatch || codeMatch;
                        return nameMatch;
                    });
                }
            } else {
                if (this.currentBrandList && this.currentBrandList.length > 0) {
                    this.filterBrandList = this.currentBrandList;
                    this.brandDropdown = true;
                    this.supplierDropdown = false;
                }
            }
        },
        selectBrand(brand) {
            if (brand) {
                this.formValues.brand = brand.brand_name;
                this.formValues.brand_id = brand.id;
            }
            this.brandDropdown = false;
            this.filterBrandList = [];
        },
        openBrandModal() {
            this.open_model = true;
            this.formValues.brand = '';
            if (this.currentBrandList && this.currentBrandList.length > 0) {
                this.list_data = this.currentBrandList;
            } else {
                this.list_data = [];
            }

        },
        closecategoryBrandUnit() {
            this.open_model = false;
            this.type_model = 'brand';
            this.list_data = [];
        },
        //---filter the brand--
        filterSupplier() {
            // console.log(this.currentSupplier, 'Waht happening....', this.formValues.supplier, 'EEEEEEEE');
            if (this.formValues.supplier) {
                const enteredProductName = this.formValues.supplier.toLowerCase();
                this.supplierDropdown = true;
                this.brandDropdown = false;
                // console.log(enteredProductName, 'Product name');
                if (this.currentSupplier.length !== 0 && enteredProductName.length > 1) {
                    // console.log('validation is done...1');
                    this.filterSupplierList = this.currentSupplier.filter(opt => {
                        // Check if the entered product name or product code matches any existing product name or product code
                        const nameMatch = opt.name.toLowerCase().includes(enteredProductName);
                        // return nameMatch || codeMatch;
                        return nameMatch;
                    });
                }
            } else {
                if (this.currentSupplier && this.currentSupplier.length > 0) {
                    this.supplierDropdown = true;
                    this.brandDropdown = false;
                    this.filterSupplierList = this.currentSupplier;
                }
            }
        },
        selectSupplier(supplier) {
            if (supplier) {
                this.formValues.supplier = supplier.name + ' - ' + (supplier.contact_number && supplier.contact_number !== '' ? supplier.contact_number : '');
                this.formValues.supplier_id = supplier.id;
            }
            this.supplierDropdown = false;
            this.filterSupplierList = [];
        },
        openSupplierModal() {
            this.open_supplier = true;
        },
        closeSupplier(data) {
            if (data) {
                if (this.currentSupplier && this.currentSupplier.length > 0) {
                    this.currentSupplier.unshift(data);
                    this.selectSupplier(data);
                } else {
                    this.currentSupplier = [{ ...data }];
                }
            }
            this.open_supplier = false;
        },
        bulkInsertActions() {
            this.bulkInsert = !this.bulkInsert;
        },
        addOneMoreRow() {
            let index = this.bulkInsert_data.length;
            if (this.bulkInsert && this.bulkInsert_data.length === 0) {
                this.bulkInsert_data = [{ select_item: '', product_id: '', brand: '', brand_id: '', supplier: '', supplier_id: '', serial_number: '', warranty: '', problem_description: '' }];
                setTimeout(() => {
                    if (this.$refs['selectItemField' + index] && this.$refs['selectItemField' + index].length > 0) {
                        this.$nextTick(() => {
                            this.$refs['selectItemField' + index][0].value = '';
                            this.$refs['selectItemField' + index][0].focus();
                        });
                    }
                }, 100);
            }
            else {
                let obj_data = { select_item: '', product_id: '', brand: '', brand_id: '', supplier: '', supplier_id: '', serial_number: '', warranty: '', problem_description: '' };
                this.bulkInsert_data = [...this.bulkInsert_data, obj_data];
                setTimeout(() => {
                    if (this.$refs['selectItemField' + index] && this.$refs['selectItemField' + index].length > 0) {
                        this.$nextTick(() => {
                            this.$refs['selectItemField' + index][0].value = '';
                            this.$refs['selectItemField' + index][0].focus();
                        });
                    }
                }, 100);
            }
        },
        removeThisRow(index) {
            this.selectedIndex = index;
            this.open_confirmBox = true;
        },
        focusSearch(type, index) {
            if (type === 'product') {
                if (this.bulkInsert) {
                    if (this.bulkInsert_data[index] && this.bulkInsert_data[index].product_id && this.bulkInsert_data[index].product_id !== '') {
                        this.reset_data.select_item = index;
                        this.isFormFocus.selected_item = index
                    } else {
                        this.isFormFocus.selected_item = index
                    }
                } else {
                    if (this.formValues && this.formValues.product_id && this.formValues.product_id !== '') {
                        this.reset_data.select_item = true;
                        this.isFormFocus.selected_item = true
                    } else {
                        this.isFormFocus.selected_item = true
                    }
                }
            } else if (type === 'customer') {
                if (this.formValues && this.formValues.customer_id && this.formValues.customer_id !== '') {
                    this.reset_data.customer = true;
                }

            } else if (type === 'brand') {
                this.isFormFocus.brand = true;
                if (this.formValues && this.formValues.brand_id && this.formValues.brand_id !== '') {
                    this.reset_data.brand = true;
                }
            } else if (type === 'supplier') {
                this.isFormFocus.supplier = true;
                if (this.formValues && this.formValues.supplier_id && this.formValues.supplier_id !== '') {
                    this.reset_data.supplier = true;
                }
            }
        },
        resetData(type, index) {
            if (type === 'product') {
                if (this.bulkInsert && index >= 0 && index < this.bulkInsert_data.length) {
                    this.bulkInsert_data[index].product_id = '';
                    this.bulkInsert_data[index].selected_item = '';
                    // Clear other fields as needed

                    // Optionally, reset the value in the input field if using refs
                    if (this.$refs['selectItemField' + index] && this.$refs['selectItemField' + index].length > 0) {
                        this.$nextTick(() => {
                            this.$refs['selectItemField' + index][0].value = '';
                            this.$refs['selectItemField' + index][0].focus();
                        });
                    }

                    // Reset any other component state or flags
                    this.reset_data.select_item = false;
                    this.isFormFocus.selected_item = index;

                } else {
                    this.formValues.product_id = '';
                    this.formValues.selected_item = '';
                    // Optionally, reset the value in the input field if using refs
                    if (this.$refs['selectItemField']) {
                        this.$nextTick(() => {
                            this.$refs['selectItemField'].value = '';
                            this.$refs['selectItemField'].focus();
                        });
                    }
                    // Reset any other component state or flags
                    this.reset_data.select_item = false;
                    this.isFormFocus.selected_item = true;

                }
            } else if (type === 'customer') {
                this.formValues.customer_id = '';
                this.formValues.customer = '';
                // Optionally, reset the value in the input field if using refs
                if (this.$refs['customer']) {
                    this.$nextTick(() => {
                        this.$refs['customer'].value = '';
                        this.$refs['customer'].focus();
                    });
                }
                // Reset any other component state or flags
                this.reset_data.customer = false;
            } else if (type === 'supplier') {
                this.formValues.supplier_id = '';
                this.formValues.supplier = '';
                // Optionally, reset the value in the input field if using refs
                if (this.$refs['supplierField']) {
                    this.$nextTick(() => {
                        this.$refs['supplierField'].value = '';
                        this.$refs['supplierField'].focus();
                    });
                }
                // Reset any other component state or flags
                this.reset_data.supplier = false;
            } else if (type === 'brand') {
                this.formValues.brand_id = '';
                this.formValues.brand = '';
                // Optionally, reset the value in the input field if using refs
                if (this.$refs['brandField']) {
                    this.$nextTick(() => {
                        this.$refs['brandField'].value = '';
                        this.$refs['brandField'].focus();
                    });
                }
                // Reset any other component state or flags
                this.reset_data.supplier = false;

            }
        },
        mouseIsOnOptionReset(value, type) {
            if (type === 'customer') {
                this.rest_mouseover.customer = value;
            } else if (type === 'product') {
                this.rest_mouseover.product = value;
            } else if (type === 'brand') {
                this.rest_mouseover.brand = value;
            } else if (type === 'supplier') {
                this.rest_mouseover.supplier = value;
            }
        },
        deleteRecord() {
            let index = this.selectedIndex;
            if (this.bulkInsert_data && this.bulkInsert_data.length > 0) {
                this.bulkInsert_data.splice(index, 1);
            }
            this.open_confirmBox = false;
        },
        cancelDelete() {
            this.selectedIndex = 0;
            this.open_confirmBox = false;
        },
        //---close accessories---
        removeOption(option) {
            this.formValues.accessories = this.formValues.accessories.filter(o => o.id !== option.id);
        },
        isSelected(option) {
            if (this.formValues.accessories && Array.isArray(this.formValues.accessories) && this.formValues.accessories.length > 0) {
                return this.formValues.accessories.some(o => o.id === option.id);
            }
        },
        openAccessories() {
            this.open_accessories = true;
        },
        closeAccessories() {
            this.open_accessories = false;
        },
        handleClickOutside(event, type) {
            if (type !== 'all') {
                if (this.isOpen_access.accessories) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaAccessories;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen_access.accessories = false;
                    }
                }

            }
            else {
                this.isOpen_access = { accessories: false };
            }
        },

        toggleDropdown(type) {
            if (type === 'accessories') {
                this.isOpen_access.accessories = !this.isOpen_access.accessories;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen_access.accessories) {
                    this.$nextTick(() => {
                        this.$refs.accessoriesInput.focus();
                    });
                }
            }

        },

        selectOptionData(option) {
            if (this.isOpen_access.accessories) {
                if (this.formValues.accessories && Array.isArray(this.formValues.accessories)) {
                    this.formValues.accessories.push(option);
                } else {
                    this.formValues.accessories = [option];
                }
                // this.formValues.accessories = this.formValues.accessories && this.formValues.accessories !== '' ? [...this.formValues.accessories, option] : [];
                this.isOpen_access.accessories = false;
                this.searchTerm.accessories = '';
            }

        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
                this.scrollToSelectedItem();
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
                this.scrollToSelectedItem();
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
                this.scrollToSelectedItem();
            } else {
                this.selectedIndex = this.selectedIndex - 1;
                this.scrollToSelectedItem();
            }
        },
        //---search filter----
        changeFilter() {
            this.formData.dropdownOpen = true;
            if (this.formData && this.formData.searchQuery && this.formData.searchQuery.length > 3) {
                const inputValue = this.formData.searchQuery;
                if (this.formData.selectedFilter && this.formData.selectedFilter === 'customer') {
                    if (!isNaN(inputValue)) {
                        const inputNumber = inputValue.toLowerCase();
                        this.filteredOptions_search = this.customer_list.filter(
                            (option) => option.contact_number.toLowerCase().includes(inputNumber)
                        );
                    } else {
                        const inputName = inputValue.toLowerCase();
                        this.filteredOptions_search = this.customer_list.filter(
                            (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                                (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                        );
                    }
                } else {
                    this.$store.dispatch('searchSerial/fetchSerialList', { serialno: this.formData.searchQuery });
                }
            }
        },
        scrollToSelectedItem() {
            this.$nextTick(() => {
                const dropdown = this.$refs.dropdown;
                const selectedItem = this.$refs.dropdownItems[this.selectedIndex];
                if (selectedItem && dropdown) {
                    const itemOffsetTop = selectedItem.offsetTop;
                    const itemHeight = selectedItem.offsetHeight;
                    const dropdownHeight = dropdown.clientHeight;

                    if (itemOffsetTop + itemHeight > dropdown.scrollTop + dropdownHeight) {
                        dropdown.scrollTop = itemOffsetTop + itemHeight - dropdownHeight;
                    } else if (itemOffsetTop < dropdown.scrollTop) {
                        dropdown.scrollTop = itemOffsetTop;
                    }
                }
            });
        },
        //--selected options----
        selectOption(option) {
            if (this.formData.selectedFilter === 'serialNumber') {
                console.log(option, 'are slelected');
                this.selectedIndex = null;
                this.formData.mouseDownOnDropdown = false;
                this.formData.dropdownOpen = false;
                if (option.client && option.client.length > 0 && this.currentCustomer && this.currentCustomer.length > 0) {


                    let findData = this.currentCustomer.find(opt => opt.id == option.client[0].customer_id);
                    if (findData) {
                        this.selectDropdownOption(findData);
                    }
                }

                if (option.suppliers && option.suppliers.length > 0 && this.currentSupplier && this.currentSupplier.length > 0) {
                    let findData = this.currentSupplier.find(opt => opt.id == option.suppliers[0].supplier_id);

                    if (findData) {
                        this.selectSupplier(findData);
                    }
                }
                if (option.product && option.product.length > 0 && this.currentItems && this.currentItems.length > 0) {
                    let findData = this.currentItems.find(opt => opt.id === option.product[0].product_id);
                    if (findData) {
                        this.selectProduct(findData);
                    }
                }
            } else {
                //---search by customer-----
                this.selectDropdownOption(option);
                if (option.id) {
                    this.getCustomerData(option.id);
                }
            }
        },
        //---- get customer management data-----
        getCustomerData(id) {
            this.open_loader = true;
            axios.get(`/customer-details/${id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // Handle response
                    this.formData.invoices = response.data.data.sales;
                    this.open_loader = false;
                    this.open_select_serial = true;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                    this.open_loader = false;
                });
        },
        openSerial() {
            this.open_select_serial = true;
        },
        formatDate(timestamp) {
            const date = new Date(timestamp);
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const year = date.getFullYear();

            return `${year}-${month}-${day}`;
        },
        closeserial(data) {
            if (data) {
                if (this.currentItems && this.currentItems.length > 0 && data.item && data.item.product_id) {
                    let find_item = this.currentItems.find(opt => opt.product_id === data.item.product_id);
                    if (find_item) {
                        this.selectProduct(find_item);
                    }

                    if (find_item && find_item.brand_id && this.brandUnitCategoryItem && this.brandUnitCategoryItem.length > 0) {
                        let find_brand = this.brandUnitCategoryItem.find(opt => opt.id == find_item.brand_id);
                        if (find_brand) {
                            this.selectBrand(find_brand);
                        }
                    }
                }
                if (data.serial_no) {
                    this.formValues.serial_number = data.serial_no;
                }
                if (data.invoice && data.invoice.id && data.invoice.invoice_id) {
                    this.formValues.customer_invoice = data.invoice.invoice_id;
                    this.formValues.date_of_purchase = this.formatDate(data.invoice.current_date);
                }

                this.open_select_serial = false;
            } else {
                this.open_select_serial = false;
            }
        },
        isWarrantyExpired() {
            if (this.formValues.warranty >= 0 && this.formValues.date_of_purchase) {
                // Parse the purchase date
                const purchaseDate = new Date(this.formValues.date_of_purchase);

                // Add the warranty period to the purchase date
                purchaseDate.setMonth(purchaseDate.getMonth() + this.formValues.warranty);

                // Get the current date
                const currentDate = new Date();

                // Calculate the difference in milliseconds
                const timeDiff = purchaseDate - currentDate;

                // Convert milliseconds to days
                const daysRemaining = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
                if (daysRemaining > 0) {
                    this.message = `The warranty remaning for ${daysRemaining} days`;
                    this.type_toaster = 'info';
                    this.show = true;
                } else {
                    this.message = `This product warranty has been expired...!`;
                    this.type_toaster = 'warning';
                    this.show = true;

                }

                return daysRemaining > 0 ? daysRemaining : 0; // Return 0 if the warranty has expired
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        navigateToWhatsApp() {
            this.$emit('close-modal', null, true);
            this.$router.push({ name: 'whatsappsetting' });
        },

    },
    mounted() {
        this.fetchWhatsappList();
        if (this.currentInvoice && this.currentInvoice.length > 0) {
            if (this.currentInvoice && Array.isArray(this.currentInvoice) && this.currentInvoice.length > 0) {
                this.issms = this.currentInvoice[0].is_sms >= 0 ? this.currentInvoice[0].is_sms ? true : false : false;
                this.iswhatsapp = this.companywhatsapp ? this.currentInvoice[0].is_whatsapp >= 0 ? this.currentInvoice[0].is_whatsapp ? true : false : false : false;
            }
            this.fetchInvoiceSetting();
        } else {
            this.fetchInvoiceSetting();
        }


    },

    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    if (this.currentCustomer && this.currentCustomer.length === 0) {
                        this.fetchCustomerList();
                    } else {
                        this.customer_list = this.currentCustomer;
                        this.fetchCustomerList();
                    }
                    if (this.product.length === 0 && this.companyId) {
                        this.getProductListData(1, 2);
                        if (this.currentItems && this.currentItems.length > 0) {
                            this.product = this.currentItems;
                            if (this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0) {
                                this.pagination.product = this.currentItemsPagination;
                            }
                            this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
                        }
                        this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
                    }
                    if (this.currentBrandList && this.currentBrandList.length === 0) {
                        this.fetchBrandList();
                    } else {
                        this.fetchBrandList();
                    }
                    if (this.currentSupplier && this.currentSupplier.length === 0) {
                        this.fetchISupplierList();
                    } else {
                        this.fetchISupplierList();
                    }
                    if (this.currentAccessoriesList && this.currentAccessoriesList.length === 0) {
                        this.fetchAccessoriesList();
                    } else {
                        this.fetchAccessoriesList();
                    }
                    //---select customer----
                    if (this.customer_list && this.customer_list.length > 0 && this.customer_id) {
                        let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                        if (selected_customer) {
                            this.selectDropdownOption(selected_customer);
                        }
                    }
                }
            }, 100);
        },
        currentCustomer: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.customer_list = this.currentCustomer;
                    if (this.customer_list && this.customer_list.length > 0 && this.customer_id) {
                        let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                        if (selected_customer) {
                            this.selectDropdownOption(selected_customer);
                        }
                    }
                }
            }
        },
        currentItems: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.product = newValue;
                }
            }
        },
        customer_id: {
            deep: true,
            handler(newValue) {
                if (newValue && this.customer_list && this.customer_list.length > 0) {
                    let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                    if (selected_customer) {
                        this.selectDropdownOption(selected_customer);
                    }
                }
            }
        },
        currentSerialList: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'RWRWRSF what happpppp');
                if (newValue && newValue.length > 0 && this.formData.selectedFilter == 'serialNumber') {
                    this.filteredOptions_search = [...newValue];
                    // console.log(this.filteredOptions_search, 'RWRWRWRWR whadadad');
                } else {
                    this.filteredOptions_search = [];
                }
            }
        },
        'formValues.date_of_purchase': {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.isWarrantyExpired();
                }
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && Array.isArray(newValue) && newValue.length > 0) {
                    this.issms = newValue[0].is_sms >= 0 ? newValue[0].is_sms ? true : false : false;
                    this.iswhatsapp = this.companywhatsapp ? newValue[0].is_whatsapp >= 0 ? newValue[0].is_whatsapp ? true : false : false : false;
                }
            }
        }

    },
    computed: {
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('items', ['currentItems', 'currentItemsPagination']),
        ...mapGetters('brandUnitCategoryItem', ['currentBrandList']),
        ...mapGetters('supplier', ['currentSupplier']),
        ...mapGetters('accessories', ['currentAccessoriesList']),
        ...mapGetters('searchSerial', ['currentSerialList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        filteredOptions() {
            if (this.isOpen_access.accessories && this.currentAccessoriesList && this.currentAccessoriesList.length > 0) {
                return this.currentAccessoriesList.filter(option =>
                    option.name.toLowerCase().includes(this.searchTerm.accessories.toLowerCase()) && !this.isSelected(option)
                );
            }
        },
    }
};
</script>

<style scoped>
.material-icons {
    font-size: 24px;
}
</style>