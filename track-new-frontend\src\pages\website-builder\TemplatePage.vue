<template>
  <main-layout>
    <div class="h-screen relative">
      <!-- Category List with Horizontal Scroll -->
      <div v-if="categories && categories.length > 0"
        class="flex flex-row w-full space-x-4 py-4 overflow-x-auto mt-14 sm:mt-[75px] p-1 sm:p-3 xl:px-6 2xl:px-12 bg-white shadow-lg border border-gray-200 rounded">
        <div v-for="(category, index) in categories" :key="index" @click="selectCategory(category)"
          :class="{ 'shadow-md border border-green-600': selectedCategory === category.name }"
          class="flex-shrink-0 bg-gray-200 rounded-lg flex flex-col items-center justify-center p-2 cursor-pointer hover:bg-gray-50">
          <i class="fas fa-briefcase text-4xl mb-2"></i>
          <p class="text-center">{{ category.name }}</p>
        </div>
      </div>
      <!-- Main content area -->
      <div class="flex flex-col flex-grow overflow-y-auto overflow-x-hidden -mt-8 sm:-mt-10">
        <!-- Add your main content here -->
        <div class="builder-page margin-web">

          <div class="flex lg:flex-row flex-col justify-between">

            <!-- Left Sidebar -->
            <!-- <div class="w-1/4 bg-gray-100 border-r">
              <h2 class="text-xl font-bold p-4 border-b">Template Categories</h2>
              <ul class="space-y-2 p-4">
                <li v-for="category in categories" :key="category">
                  <button class="w-full text-left py-2 px-4 rounded hover:bg-gray-200 focus:bg-gray-300"
                    :class="{ 'bg-gray-300': selectedCategory === category }" @click="selectCategory(category)">
                    {{ category }}
                  </button>
                </li>
              </ul>
            </div> -->

            <!-- Main Content -->
            <div class="flex-1 bg-white p-6 overflow-y-auto">
              <h2 class="text-2xl font-bold mb-4">Templates</h2>
              <div v-if="templates.length" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <div v-for="template in filteredTemplates" :key="template.id"
                  class="border rounded-lg shadow hover:shadow-lg relative"
                  :class="{ 'border-green-600 bg-green-300': additionalInfo && additionalInfo.template_id && additionalInfo.template_id == template.id }">
                  <p v-if="additionalInfo && additionalInfo.template_id && additionalInfo.template_id == template.id"
                    class="absolute -top-2 -right-2 text-2xl z-[4] text-green-600"><font-awesome-icon
                      icon="fa-solid fa-circle-check" /></p>
                  <img :src="template.thumbnail || 'https://via.placeholder.com/300x200'" :alt="template.name"
                    class="w-full h-48 object-cover cursor-pointer" @click="openPreview(template)" />
                  <div class="p-4">
                    <h3 class="text-lg font-bold">{{ template.name }}</h3>
                    <button class="mt-2 w-full bg-green-600 text-white py-2 rounded hover:bg-green-700"
                      @click="openPreview(template)"
                      v-if="additionalInfo && additionalInfo.template_id && additionalInfo.template_id == template.id">
                      <font-awesome-icon icon="fa-solid fa-circle-check" class="pr-1 text-lg" /> Selected</button>
                    <button v-else class="mt-2 w-full bg-blue-600 text-white py-2 rounded hover:bg-blue-700"
                      @click="selectTemplate(template)">
                      Select
                    </button>
                    <button class="mt-2 w-full bg-gray-100 text-green-800 py-2 rounded hover:bg-gray-200"
                      @click="openModaltheme(template)"
                      v-if="additionalInfo && additionalInfo.template_id && additionalInfo.template_id == template.id && selected_template && selected_template.template && selected_template.template.color_scheme">
                      <font-awesome-icon icon="fa-solid fa-gear" class="pr-1" /> Theme Setting</button>
                  </div>
                </div>
              </div>
              <div v-else class="text-center text-gray-500">No templates available for this category.</div>
            </div>

            <!-- Preview Modal -->
            <div v-if="selectedTemplate"
              class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-30">
              <div class="h-screen bg-white w-full sm:w-3/4 lg:w-1/2 rounded-lg shadow-lg overflow-y-auto">
                <div class="p-6">
                  <div class="flex justify-between items center">
                    <h2 class="lg:text-xl sm:text-lg text-sm font-bold mb-4">{{ selectedTemplate.name }}</h2>
                    <button title="close" class="text-xl text-red-600 hover:text-red-500"
                      @click="closePreview"><font-awesome-icon icon="fa-solid fa-xmark" /></button>
                  </div>
                  <!-- Modal Top Header Options -->
                  <!-- <div class="flex justify-between mb-4">
                    <button v-for="option in options" :key="option" @click="changePage(option)"
                      class="text-gray-700 hover:text-blue-600">
                      {{ option }}
                    </button>
                  </div> -->
                  <!-- <div class="border rounded-lg overflow-hidden">
                    <img :src="selectedTemplate.thumbnail || 'https://via.placeholder.com/800x400'"
                      :alt="selectedTemplate.name" class="w-[600px] h-[400px]" />
                  </div> -->
                  <!-- Image Display Area -->
                  <div v-if="isImage" class="relative">
                    <img :src="imageUrl" :alt="selectedTemplate.name" class="max-w-full max-h-full img-model"
                      :style="{ transform: `scale(${zoomLevel})` }" ref="image" />
                    <!-- Zoom In / Out Buttons -->
                    <div class="absolute top-2 right-2 flex space-x-2">
                      <button @click="zoomIn"
                        class="bg-gray-300 text-gray-800 w-8 h-8 rounded-full text-blue-700"><font-awesome-icon
                          icon="fa-solid fa-magnifying-glass-plus" /></button>
                      <button @click="zoomOut"
                        class="bg-gray-300 text-gray-800 w-8 h-8 rounded-full text-blue-700"><font-awesome-icon
                          icon="fa-solid fa-magnifying-glass-minus" /></button>
                    </div>
                    <!-- Fullscreen Button (Only for images) -->
                    <div class="absolute bottom-2 right-2">
                      <button @click="toggleFullScreen" class="bg-blue-600 text-white p-2 rounded">Full
                        Screen</button>
                    </div>
                  </div>
                  <div class="mt-4 flex justify-end space-x-4">
                    <button class="bg-gray-300 text-gray-800 py-2 px-4 rounded hover:bg-gray-400" @click="closePreview">
                      Close
                    </button>
                    <!-- <button class="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
                      @click="previewTemplate(selectedTemplate)">
                      Preview Template
                    </button> -->
                    <button class="bg-blue-600 text-white py-2 px-4 rounded hover:bg-blue-700"
                      @click="selectTemplate(selectedTemplate)">
                      Select Template
                    </button>
                  </div>
                </div>
              </div>
            </div>
            <div v-if="!isMobile">
              <sidebar></sidebar>
            </div>
          </div>
        </div>
      </div>
      <Loader :showModal="open_loader"></Loader>
      <ThemeSettingModal :showModal="open_theme" @close-modal="closeModaltheme" :selected_template="selected_template"
        :user_id="additionalInfo && additionalInfo.id ? additionalInfo.id : null"
        :theme_settings="additionalInfo.theme_settings ? additionalInfo.theme_settings : null">
      </ThemeSettingModal>
      <Toaster :show="showModal" :message="message" :type="type_toaster" @update:show="showModal = false"></Toaster>
    </div>
  </main-layout>
</template>

<script>
import axios from "axios";
import MainLayout from "../../layouts/mainLayout.vue";
import sidebar from "@/layouts/sidebar.vue";
import { mapActions, mapGetters } from 'vuex';
import ThemeSettingModal from "./ThemeSettingModal.vue";

export default {
  components: {
    MainLayout,
    sidebar,
    ThemeSettingModal
  },
  data() {
    return {
      categories: [],
      selectedCategory: null,
      templates: [],
      templates_list: [],
      selectedTemplate: null,
      //---modal box-----
      imageUrl: '', // Image URL for preview
      zoomLevel: 1, // Zoom level for image
      isFullScreen: false, // Fullscreen toggle
      options: ['home', 'about us', 'services', 'products', 'projects', 'gallery', 'video', 'brochure'],
      isImage: false,
      //--toaster---
      showModal: false,
      type_toaster: 'success',
      message: '',
      isMobile: false,
      //---theme setting---
      open_theme: false,
      selected_template: null,
      //--loader--
      open_loader: false,
    };
  },
  computed: {
    ...mapGetters('websiteBuilder', ['additionalInfo', 'companyID']),

    filteredTemplates() {
      if (this.templates && this.templates.length > 0) {
        return this.templates.filter(
          (template) => template.category === this.selectedCategory
        );
      }
    },
  },
  methods: {
    ...mapActions('websiteBuilder', ['updateSelectdItem', 'fetchWebsiteUrl']),
    async fetchTemplates() {
      try {
        this.open_loader = true;
        const response = await axios.get(`/template_tags`);
        if (response.data.success) {

          this.categories = response.data.data.map((category) => { return { id: category.id, name: category.name } });
          this.templates_list = this.templates = response.data.data.flatMap((category) =>
            category.templates.map((template) => ({ template }))
          );
          this.templates = response.data.data.flatMap((category) =>
            category.templates.map((template) => ({
              id: template.id,
              category: category.name,
              name: template.name,
              thumbnail: template.thumbnail || "",
              // thumbnail: 'http://192.168.1.55:8001/storage/' + template.thumbnail || "",
              preview_url: template.preview_url || "",
              layout: template.layout || {},
              theme_settings: template.theme_settings
            }))
          );
          this.selectedCategory = this.categories[0].name; // Set the first category as default
          this.open_loader = false;
        }
      } catch (error) {
        console.error("Error fetching templates:", error);
        this.open_loader = false;
      }
    },
    selectCategory(category) {
      this.selectedCategory = category.name;
    },
    openPreview(template) {
      this.selectedTemplate = template;
      this.imageUrl = template.thumbnail || 'https://via.placeholder.com/800x400'; // Set image URL based on selected template
      this.isImage = true;
    },
    closePreview() {
      this.selectedTemplate = null;
    },
    selectTemplate(template) {
      this.open_loader = true;
      let find_template = this.templates_list.find(opt => opt.template.id === template.id);
      this.selected_template = find_template;
      let sentData = {
        template_id: template.id,
        theme_settings: template.theme_settings
      };

      if (sentData) {
        // Call the API to register the website with the provided data
        axios.put(`/company-sites/${this.additionalInfo && this.additionalInfo.id ? this.additionalInfo.id : ''} `, sentData)
          .then(response => {
            // console.log(response, 'Allow data');
            this.message = `You have selected the ${template.name} `;
            this.type_toaster = 'success';
            this.showModal = true;
            this.fetchWebsiteUrl();

            this.closePreview();
            this.open_loader = false;
          })
          .catch(error => {
            console.error("Error registering website:", error);
            this.open_loader = false;
          });
      } else {
        this.message = `Please select website template..!`;
        this.type_toaster = 'warning';
        this.showModal = true;
        this.open_loader = false;
      }
    },
    previewTemplate(template) {
      if (template.preview_url) {
        // const fullUrl = `http://192.168.1.55:8001/website/${template.preview_url}/vadivelan`;
        const fullUrl = `${template.preview_url}`;
        window.open(fullUrl, "_blank");
      } else {
        // alert("Preview URL is not available for this template.");
        this.message = "Preview URL is not available for this template.";
        this.type_toaster = 'warning';
        this.showModal = true;
      }
      this.closePreview();
    },
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
    //--additional functions---
    zoomIn() {
      if (this.zoomLevel < 3) {
        this.zoomLevel += 0.1;
      }
    },
    zoomOut() {
      if (this.zoomLevel > 1) {
        this.zoomLevel -= 0.1;
      }
    },
    toggleFullScreen() {
      const elem = document.querySelector('.img-model');
      if (!this.isFullscreen) {
        if (elem.requestFullscreen) {
          elem.requestFullscreen();
        }
        else if (elem.webkitRequestFullscreen) {
          elem.webkitRequestFullscreen();
        } else if (elem.msRequestFullscreen) {
          elem.msRequestFullscreen();
        }
        this.isFullscreen = true; // Update the fullscreen state
      } else {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitExitFullscreen) {
          document.webkitExitFullscreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
        this.isFullscreen = false; // Update the fullscreen state
      }
    },
    changePage(option) {
      switch (option) {
        case 'home':
          this.imageUrl = 'https://via.placeholder.com/800x400'; // Example image for home
          break;
        case 'about us':
          this.imageUrl = 'https://via.placeholder.com/800x400'; // Example image for about us
          break;
        default:
          this.imageUrl = 'https://via.placeholder.com/800x400'; // Default image
      }
    },
    //---theme setting--
    closeModaltheme(status) {
      this.open_theme = false;
      if (status) {
        this.fetchWebsiteUrl();
      }
    },
    openModaltheme(template) {
      let find_template = this.templates_list.find(opt => opt.template.id === template.id);
      this.selected_template = find_template;
      this.open_theme = true;
    }
  },
  mounted() {
    this.fetchTemplates();
    this.fetchWebsiteUrl();
    this.updateIsMobile();
    let id = this.additionalInfo && this.additionalInfo.template_id && this.additionalInfo.template_id ? this.additionalInfo.template_id : null;
    if (this.templates_list && this.templates_list.length > 0 && id) {

      let find_template = this.templates_list.find(opt => opt.template.id === id);
      this.selected_template = find_template;
    }
    window.addEventListener('resize', this.updateIsMobile);
  },
  watch: {
    additionalInfo: {
      deep: true,
      handler(newValue) {
        let id = newValue && newValue.template_id && newValue.template_id ? newValue.template_id : null;
        if (this.templates_list && this.templates_list.length > 0 && id) {
          let find_template = this.templates_list.find(opt => opt.template.id === id);
          this.selected_template = find_template;
        }
      }
    },
    templates_list: {
      deep: true,
      handler(newValue) {
        let id = this.additionalInfo && this.additionalInfo.template_id && this.additionalInfo.template_id ? this.additionalInfo.template_id : null;
        if (newValue && newValue.length > 0 && id) {
          let find_template = newValue.find(opt => opt.template.id === id);
          this.selected_template = find_template;
        }
      }
    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateIsMobile);
  },
};
</script>

<style>
/* Add additional custom styles if needed */
</style>
