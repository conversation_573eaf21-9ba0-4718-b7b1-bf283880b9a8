<?php

namespace App\Http\Controllers;

use App\DataTables\EstimationUsersDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateEstimationUsersRequest;
use App\Http\Requests\UpdateEstimationUsersRequest;
use App\Repositories\EstimationUsersRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class EstimationUsersController extends AppBaseController
{
    /** @var EstimationUsersRepository $estimationUsersRepository*/
    private $estimationUsersRepository;

    public function __construct(EstimationUsersRepository $estimationUsersRepo)
    {
        $this->estimationUsersRepository = $estimationUsersRepo;
    }

    /**
     * Display a listing of the EstimationUsers.
     *
     * @param EstimationUsersDataTable $estimationUsersDataTable
     *
     * @return Response
     */
    public function index(EstimationUsersDataTable $estimationUsersDataTable)
    {
        return $estimationUsersDataTable->render('estimation_users.index');
    }

    /**
     * Show the form for creating a new EstimationUsers.
     *
     * @return Response
     */
    public function create()
    {
        return view('estimation_users.create');
    }

    /**
     * Store a newly created EstimationUsers in storage.
     *
     * @param CreateEstimationUsersRequest $request
     *
     * @return Response
     */
    public function store(CreateEstimationUsersRequest $request)
    {
        $input = $request->all();

        $estimationUsers = $this->estimationUsersRepository->create($input);

        Flash::success('Estimation Users saved successfully.');

        return redirect(route('estimationUsers.index'));
    }

    /**
     * Display the specified EstimationUsers.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $estimationUsers = $this->estimationUsersRepository->find($id);

        if (empty($estimationUsers)) {
            Flash::error('Estimation Users not found');

            return redirect(route('estimationUsers.index'));
        }

        return view('estimation_users.show')->with('estimationUsers', $estimationUsers);
    }

    /**
     * Show the form for editing the specified EstimationUsers.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $estimationUsers = $this->estimationUsersRepository->find($id);

        if (empty($estimationUsers)) {
            Flash::error('Estimation Users not found');

            return redirect(route('estimationUsers.index'));
        }

        return view('estimation_users.edit')->with('estimationUsers', $estimationUsers);
    }

    /**
     * Update the specified EstimationUsers in storage.
     *
     * @param int $id
     * @param UpdateEstimationUsersRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateEstimationUsersRequest $request)
    {
        $estimationUsers = $this->estimationUsersRepository->find($id);

        if (empty($estimationUsers)) {
            Flash::error('Estimation Users not found');

            return redirect(route('estimationUsers.index'));
        }

        $estimationUsers = $this->estimationUsersRepository->update($request->all(), $id);

        Flash::success('Estimation Users updated successfully.');

        return redirect(route('estimationUsers.index'));
    }

    /**
     * Remove the specified EstimationUsers from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $estimationUsers = $this->estimationUsersRepository->find($id);

        if (empty($estimationUsers)) {
            Flash::error('Estimation Users not found');

            return redirect(route('estimationUsers.index'));
        }

        $this->estimationUsersRepository->delete($id);

        Flash::success('Estimation Users deleted successfully.');

        return redirect(route('estimationUsers.index'));
    }
}
