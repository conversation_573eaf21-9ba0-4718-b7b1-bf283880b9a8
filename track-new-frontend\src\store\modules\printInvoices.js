// store/modules/printInvoices.js <!--yet not used it will usefull for print invoices/ estimation/proforma--!>
import { getBaseUrl } from '@/utils/baseUrl';

export default {
    namespaced: true,
    state: {
        baseUrl: getBaseUrl(),
    },
    actions: {
        async downloadInvoice({ state }, { invoiceType, invoiceId, selectedOption }) {
            try {
                const endpoint = 
                    invoiceType === 'estimation' ? 'download-estimation' :
                    invoiceType === 'proforma' ? 'download-proforma' : 
                    'download-invoice';

                const linkData = `${state.baseUrl}/${endpoint}/${invoiceId}/${selectedOption}`;

                // Create an anchor element
                let anchor = document.createElement('a');
                anchor.href = linkData;
                anchor.target = '_blank'; // Opens the link in a new tab
                anchor.setAttribute('download', `${invoiceType}-invoice-${invoiceId}.pdf`); // Dynamic file name
                anchor.style.display = 'none';

                // Append anchor to the document and click it programmatically
                document.body.appendChild(anchor);
                anchor.click();

                // Cleanup
                document.body.removeChild(anchor);
            } catch (error) {
                console.error('Error downloading invoice:', error);
                throw new Error('Could not download the invoice.'); // Optional: Handle in the UI
            }
        },
    },
};
