<?php

namespace App\Http\Controllers;

use App\DataTables\lead_statusDataTable;
use App\Http\Requests;
use App\Http\Requests\Createlead_statusRequest;
use App\Http\Requests\Updatelead_statusRequest;
use App\Repositories\lead_statusRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class lead_statusController extends AppBaseController
{
    /** @var lead_statusRepository $leadStatusRepository*/
    private $leadStatusRepository;

    public function __construct(lead_statusRepository $leadStatusRepo)
    {
        $this->leadStatusRepository = $leadStatusRepo;
    }

    /**
     * Display a listing of the lead_status.
     *
     * @param lead_statusDataTable $leadStatusDataTable
     *
     * @return Response
     */
    public function index(lead_statusDataTable $leadStatusDataTable)
    {
        return $leadStatusDataTable->render('lead_statuses.index');
    }

    /**
     * Show the form for creating a new lead_status.
     *
     * @return Response
     */
    public function create()
    {
        return view('lead_statuses.create');
    }

    /**
     * Store a newly created lead_status in storage.
     *
     * @param Createlead_statusRequest $request
     *
     * @return Response
     */
    public function store(Createlead_statusRequest $request)
    {
        $input = $request->all();

        $leadStatus = $this->leadStatusRepository->create($input);

        Flash::success('Lead Status saved successfully.');

        return redirect(route('leadStatuses.index'));
    }

    /**
     * Display the specified lead_status.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $leadStatus = $this->leadStatusRepository->find($id);

        if (empty($leadStatus)) {
            Flash::error('Lead Status not found');

            return redirect(route('leadStatuses.index'));
        }

        return view('lead_statuses.show')->with('leadStatus', $leadStatus);
    }

    /**
     * Show the form for editing the specified lead_status.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $leadStatus = $this->leadStatusRepository->find($id);

        if (empty($leadStatus)) {
            Flash::error('Lead Status not found');

            return redirect(route('leadStatuses.index'));
        }

        return view('lead_statuses.edit')->with('leadStatus', $leadStatus);
    }

    /**
     * Update the specified lead_status in storage.
     *
     * @param int $id
     * @param Updatelead_statusRequest $request
     *
     * @return Response
     */
    public function update($id, Updatelead_statusRequest $request)
    {
        $leadStatus = $this->leadStatusRepository->find($id);

        if (empty($leadStatus)) {
            Flash::error('Lead Status not found');

            return redirect(route('leadStatuses.index'));
        }

        $leadStatus = $this->leadStatusRepository->update($request->all(), $id);

        Flash::success('Lead Status updated successfully.');

        return redirect(route('leadStatuses.index'));
    }

    /**
     * Remove the specified lead_status from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $leadStatus = $this->leadStatusRepository->find($id);

        if (empty($leadStatus)) {
            Flash::error('Lead Status not found');

            return redirect(route('leadStatuses.index'));
        }

        $this->leadStatusRepository->delete($id);

        Flash::success('Lead Status deleted successfully.');

        return redirect(route('leadStatuses.index'));
    }
}
