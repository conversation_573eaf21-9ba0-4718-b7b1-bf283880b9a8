<template>
    <div :class="{ 'manualStyle text-sm sm:mb-[70px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        class="custom-scrollbar-hidden" ref="scrollContainer" @scroll="handleScroll">
        <!---web enquiry -->
        <div v-if="!open_skeleton && leadData && leadData.length === 0 && followup_select === null && status_select === null && Object.keys(searchByCustomer) === 0 && Object.keys(filteredBy) === 0"
            class="flex justify-center items-center">

        </div>

        <!--new design header-->
        <div v-if="leadData && (leadData.length > 0 || followup_select !== null || status_select !== null || Object.keys(searchByCustomer) !== 0 || Object.keys(filteredBy) !== 0)"
            class="mb-1" :class="{ 'my-custom-margin': !isMobile }">
            <!---status view-->
            <!-- Render content for each number (1 to 5) 'rounded-tl-full rounded-bl-full': index === 0, -->
            <div class="custom-scrollbar-hidden px-1"
                :class="{ 'flex overflow-auto bg-white px-2': isMobile, 'grid sm:grid-cols-4  gap-3 mt-4': !isMobile }">

                <div class="px-2 cursor-pointer hover:text-blue-700"
                    :class="{ 'border-b-4 border-blue-800 text-blue-800': status_select === 3, 'shadow border rounded bg-white': !isMobile }"
                    @click="getStatusLabel(3, 1)">
                    <div class="p-1">
                        <div class="p-1 flex justify-between items-center">
                            <span class="pr-1"><font-awesome-icon icon="fa-solid fa-circle-check"
                                    :style="{ color: status_select === 3 ? 'rgb(30 64 175)' : 'gray' }"
                                    size="lg" /></span>
                            <p>All</p>
                            <p class="ml-2">({{ getStatusOption[3].total }})</p>
                        </div>
                    </div>
                </div>
                <div v-for="(opt, index) in getStatusOption" :key="index"
                    class="px-2 cursor-pointer hover:text-blue-700 rounded" @click="getStatusLabel(index, 1)"
                    :class="{ 'border-b-4 border-blue-800 text-blue-800': status_select === index, 'hidden': index >= 3, 'ml-1': isMobile, 'shadow border rounded bg-white hover:text-blue-700': !isMobile }">
                    <div class="p-2 flex justify-between items-center" style="white-space: nowrap;">
                        <span class="pr-1"><font-awesome-icon :icon="opt.icon"
                                :style="{ color: status_select === index ? 'rgb(30 64 175)' : 'gray' }"
                                size="lg" /></span>
                        <p class="inline" style="display: inline;">{{ index === 0 ? 'Open' : index === 1 ?
                            'Completed'
                            : index === 2 ? 'Cancelled' : 'All' }}</p>
                        <p class="ml-2">({{ opt.total }})</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="my-custom-margin">
            <!--table options-->
            <div v-if="!isMobile" class="flex justify-between m-1 mt-5 ">
                <div class="flex mr-2 space-x-4">
                    <p class="text-center items-center font-bold text-lg">Website Enquiry</p>
                    <!--view design-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="info-msg px-2 py-1 rounded-lg  border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-100"
                            @click="toggleView" :title02="'Table view'"
                            :class="{ 'bg-white': items_category === 'tile' }">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200 info-msg"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="'Card view'">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <!----Filter Model-->
                <!--Setting-->
                <div class="flex icon-color">
                    <!-- Main Filter Button -->
                    <div ref="dropdownContainerFilter" class="ml-5 relative">
                        <button @click="toggleMainDropdown" :disabled="leadData.length == 0"
                            class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                            :class="{ 'cursor-not-allowed': leadData.length == 0 }">
                            <span class="inline-flex items-center w-full pointer-events-none">
                                <font-awesome-icon icon="fa-solid fa-filter" class="px-1" /> Filter
                                <font-awesome-icon v-if="!isMainDropdownOpen" icon="fa-solid fa-angle-down"
                                    class="pl-3" />
                                <font-awesome-icon v-if="isMainDropdownOpen" icon="fa-solid fa-angle-up" class="pl-3" />
                            </span>
                        </button>
                        <!-- Main Dropdown -->
                        <div v-if="isMainDropdownOpen" ref="mainDropdown"
                            class="absolute mt-2 w-56 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border right-0">
                            <div class="py-1">
                                <!-- Other Options -->
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('by Date')">By Date</button>
                                <!-- <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('category')">By Category / Type</button> -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!---fiter information !isMobile && -->
            <div v-if="(Object.keys(filteredBy).length > 0 || (status_select !== null && status_select >= 0 && status_select !== 3) || (followup_select && followup_select !== 'all'))"
                class="text-xs flex -mb-3 flex-row overflow-auto m-1">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-if="Object.keys(filteredBy).length > 0" v-for="(value, key) in filteredBy" :key="key"
                    class="flex flex-row text-blue-600 mr-2">
                    <p v-if="key !== 'customer_id'" class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                    <p v-if="key !== 'customer_id'">{{key === 'assign_to' ? value.name : key === 'type' ?
                        leadType.find(opt => opt.id === value).name : key === 'status' ?
                            value === 0 ? 'Open' : value === 1 ? 'Completed' : value === 2 ? 'Cancelled' : '' : value}}</p>
                </div>

                <div v-if="status_select !== null && status_select >= 0 && status_select !== 3"
                    class="text-blue-600 mr-2">
                    Status = {{ status_select === 0 ? 'Open' : status_select === 1 ? 'Completed' : status_select === 2 ?
                        'Cancelled' : 'All' }}</div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton && leadData && leadData.length > 0" class="text-sm mt-5"
                :class="{ 'm-1': !isMobile }">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                    </div>
                    <!--Table view-->
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head text-nowrap">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label === 'Created At' ? 'Date' : column.label === 'Firstname'
                                                ? 'Name' : column.label === 'Mobile No' ? 'Mobile' : column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="dropdownSetting"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-24 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in leadData" :key="index"
                                    class="hover:bg-gray-200 cursor-pointer text-nowrap">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex"
                                        :class="{ 'hidden': !column.visible }" class="px-1 py-2 table-border"
                                        @click="startView(record)">

                                        <span v-if="column.field === 'created_at'"
                                            :title="calculateDaysAgo(formattedDate(record[column.field]))"
                                            class="text-blue-800 text-xs">
                                            {{ generateDate(record[column.field]) }}
                                        </span>
                                        <span v-if="column.field === 'status' && record.status >= 0"
                                            :class="{ 'text-yellow-600': record.status == 0, 'text-green-600': record.status == 1, 'text-red-600': record.status == 2 }">
                                            {{ record.status == 0 ? 'Open' : record.status == 1 ? 'Completed' :
                                                'Cancelled' }}
                                        </span>
                                        <span v-if="column.field === 'mobile_no'" class="cursor-pointer"
                                            @click="dialPhoneNumber(record.data[column.field])">
                                            {{ record.data[column.field] }}
                                        </span>
                                        <span
                                            v-if="column.field !== 'created_at' && column.field !== 'status' && column.field !== 'mobile_no' && record.data"
                                            :class="{ 'line-clamp-1': column.field === 'message' }">
                                            {{ record.data[column.field] }}
                                        </span>
                                    </td>
                                    <td class="py-2 table-border">
                                        <div class="flex justify-center">
                                            <div class="flex relative">
                                                <div class="flex">
                                                    <button v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                        @click="startView(record)"
                                                        class="px-1 text-[#3b82f6;] hover:text-blue-600" title="View">
                                                        View
                                                        <font-awesome-icon icon="fa-solid fa-eye" class="px-1" />
                                                    </button>
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)" title="Delete"
                                                        class="text-[#ef4444] hover:text-red-500  px-1  py-1 flex justify-center items-center">
                                                        Delete <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            class="px-1" />
                                                    </button>
                                                </div>
                                                <!-- <button @click.stop="displayAction(index)" class="px-1 py-1">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                        class="px-2 py-1 rounded"
                                                        :class="{ 'bg-blue-100': display_option === index }" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-7 absolute border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class=" hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startView(record)"
                                                            class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class=" hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border"
                                    v-if="!leadData || leadData.length === 0">
                                    <td>
                                        <button
                                            class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="openLeadModal">
                                            + Lead
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4 custom-scrollbar-hidden">
                        <div v-for="(record, index) in leadData" :key="index" class="w-full">
                            <div
                                class="bg-white rounded-lg border border-gray-200 overflow-hidden relative w-full shadow-lg">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center p-2">
                                    <!-- Left Side -->
                                    <div class="text-xs text-red-400 cursor-pointer"
                                        :title="generateDate(record['created_at'])">
                                        <p>{{ calculateDaysAgo(formattedDate(record['created_at'])) }}</p>
                                    </div>
                                    <div class="text-xs px-3 py-1 rounded flex items-center cursor-pointer"
                                        @click="startView(record)" v-if="record['status'] >= 0"
                                        :class="{ 'bg-yellow-200 text-yellow-800': record['status'] == '0', 'bg-green-200 text-green-900': record['status'] == '1', 'text-red-800 bg-red-100': record['status'] == '2' }">
                                        <span>
                                            {{ record['status'] == '0' ? 'Open' : record['status'] == '1' ?
                                                'Completed' : record['status'] == '2' ? 'Cancelled' : '' }}</span>
                                    </div>
                                    <!-- Right Side -->
                                    <div class="flex items-center px-2">
                                        <div class="ml-2 rounded relative">
                                            <button @click.stop="displayAction(index)">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                    class="bg-blue-100 text-blue-800 px-2 py-1 rounded font-semibold " />
                                            </button>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 text-sm absolute mt-3 right-0 border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startView(record)"
                                                            class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-eye"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">View</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2 ">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Customer Details and Lead Status -->
                                <div v-if="record.data" class="text-gray-900 px-2">
                                    <div class="flex items-center mb-1">
                                        <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                            :class="{ 'bg-gray-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-blue-600': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                            {{ record.data && record.data.firstname ?
                                                record.data.firstname[0].toUpperCase() :
                                                'C' }}
                                        </div>
                                        <div>
                                            <h4 class="text-sm leading-6 font-semibold text-gray-900 cursor-pointer"
                                                @click="startView(record)">{{
                                                    record.data && record.data.firstname ? record.data.firstname : '' }}
                                            </h4>
                                            <p class="text-sm text-gray-500 cursor-pointer"
                                                @click="dialPhoneNumber(record.data && record.data.mobile_no ? record.data.mobile_no : '')">
                                                +91
                                                - {{ record.data && record.data.mobile_no ? record.data.mobile_no : ''
                                                }}
                                            </p>
                                        </div>
                                    </div>
                                    <div v-if="record.data">
                                        <p v-if="record.data && record.data['type']" class="flex items-center">
                                            <span class="font-bold">Type: </span>
                                            <span class="px-2 py-1 rounded mx-2">
                                                {{ record.data['type'] }}
                                            </span>
                                        </p>
                                        <!--product name-->
                                        <p v-if="record.data && record.data['name']"
                                            class="flex items-center line-clamp-1">
                                            <span class="font-bold">Data: </span>
                                            <span class="px-2 py-1 mx-2 mb-2">
                                                {{ record.data['name'] }}
                                            </span>
                                        </p>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && pagination.last_page > 0 && pagination.last_page === pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>

                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>

                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="!isMobile && pagination">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>
                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none overflow-auto">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px] flex">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span></button>
                                </li>
                                <!-- Page numbers -->
                                <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">
                                        {{ pageNumber }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === this.pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== this.pagination.last_page }">
                                    <button @click="updatePage(currentPage + 1)"
                                        :disabled="currentPage === this.pagination.last_page"
                                        class="px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs flex">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && leadData && leadData.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!---in mobile view Filter Service-->
        <div v-if="isMobile" ref="dropdownContainerFilter">
            <div class="fixed bottom-36 right-5 z-50 bg-green-700 rounded-full">
                <div class="flex justify-end">
                    <button @click="toggleMainDropdown" type="button"
                        class="flex items-center justify-center px-[10px] py-2 text-white "
                        :class="{ 'cursor-not-allowed blur-sm': leadData.length == 0 }"
                        :disabled="leadData.length == 0">
                        <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                    </button>
                </div>
            </div>
            <!-- Main Dropdown -->
            <div v-if="isMainDropdownOpen" ref="mainDropdown"
                class="fixed bottom-48 sm:bottom-48 left-full transform -translate-x-full w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border">
                <div class="py-1">
                    <!-- Other Options -->
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('by Date')">By Date</button>
                    <!-- <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('category')">By Category / Type</button> -->
                </div>
            </div>
        </div>
        <!---in mobile view create new lead-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openLeadModal" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>

        <!--Filter-->
        <websiteEnquiry :showModal="lead_filter" @closeFilter="closeLeadFilter" :selectedByValue="selectedByValue"
            :page="'Website Enquiry'"></websiteEnquiry>
    </div>
    <Loader :showModal="open_loader"></Loader>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <enquiryData :showModal="open_enquiry" :itemData="viewData" @close-Modal="closeEnquiryData"
        @status-updated="updateEnquiry"></enquiryData>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>

<script>
import confirmbox from '../../dialog_box/confirmbox.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import axios from 'axios';
import { mapState, mapActions, mapGetters } from 'vuex';
import customerDataTable from '../../dialog_box/customerDataTable.vue';
import enquiryData from '../../dialog_box/enquiryData.vue';
import websiteEnquiry from '../../dialog_box/filter_Modal/websiteEnquiry.vue';
import noAccessModel from '../../dialog_box/noAccessModel.vue';
export default {
    name: 'Web Enquiry',
    emits: ['dataToParent', 'updateIsOpen'],
    components: {
        confirmbox,
        dialogAlert,
        customerDataTable,
        enquiryData,
        websiteEnquiry,
        noAccessModel
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,

            open_confirmBox: false,
            deleteIndex: null,
            changeStatus: false,
            //---
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            columns: [],
            leadData: [],
            originalData: [],
            open_message: false,
            message: '',
            //---filter--
            showFilterOptions: false,
            mouseOverIsNot: false,
            filterOptions: ['by Created Date', 'by Customer', 'by Employee', 'by category/Type', 'by Status', 'Custom'],
            selectedByValue: null,
            lead_filter: false,
            typeList: [],
            statusList: [],
            filteredBy: {},
            //---api integration
            companyId: null,
            userId: null,
            pagination: {},
            now: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 6,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            display_option: false,
            status_option: [{ type: 'open', total: 0, icon: 'fa-regular fa-folder-open' }, { type: 'completed', total: 0, icon: 'fa-solid fa-flag-checkered' }, { type: 'cancelled', total: 0, icon: 'fa-solid fa-ban' },
            { type: 'all', total: 0, icon: 'fa-solid fa-circle-check' }],
            followup_select: 'all',
            status_select: 3,
            searchByCustomer: {},
            filter_page: {},
            searchedDataList: [],
            items_category: 'tile',
            filter_option: null,
            filter_date: false,
            open_skeleton_isMobile: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---table data filter asending and decending----
            is_filter: false,
            //----filter options are-----
            isMainDropdownOpen: false,
            showSubDropdown: false,
            is_openSub: false,

            //--sort--
            resetSearchData: false,
            //---enqiry--
            open_enquiry: false,
            viewData: null,
            decodeData: false,
            //---no access---
            no_access: false,
        };
    },
    computed: {
        ...mapGetters('webEnquiry', ['currentEnquiryList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        paginatedData() {
            // console.log(this.leadData, 'WWWWWWWWWWWWWWWWWWWWW');
            if (this.leadData && this.leadData.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                return this.leadData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.leadData && this.leadData.length !== 0) {
                const totalFilteredRecords = this.leadData.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];
            let key_order;
            if (!this.isMobile) {
                key_order = ['created_at', 'firstname', 'mobile_no', 'type', 'message', 'status'];
            } else {
                key_order = ['created_at', 'firstname', 'mobile_no', 'type', 'message', 'status'];

            }

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in leadData to get field names
            if (this.leadData.length > 0 && this.leadData) {
                for (const key of key_order) {
                    if (key !== 'id' && key !== 'customer_id') {
                        const label = formatLabel(key);
                        //---key !== 'description' && key !== 'lead_type' &&
                        if (key !== 'assign_date' && key !== 'created_by' && key !== 'updated_by') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        getStatusOption() {
            return this.status_option;
        }
    },
    created() {
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        // Make GET request with parameter company_id=1
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        const view = localStorage.getItem('enquiry_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        let store_data = this.currentEnquiryList;
        if (store_data && store_data.data && store_data.data.length > 0) {
            this.open_skeleton = true;
            this.getInitialData(store_data);
            this.fetchEnquiryList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
        } else {
            if (store_data && Object.keys(store_data).length == 0) {
                this.open_skeleton = true;
                this.fetchEnquiryList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
            }
        }
        //---sortIcons---
        const initialShortVisible = ['created_at', 'firstname', 'mobile_no', 'type', 'status'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);

        setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    methods: {
        ...mapActions('webEnquiry', ['fetchEnquiryList']),
        ...mapActions('localStorageData', ['validateRoles', 'fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },

        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },

        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.open_loader = true;
                axios.delete(`/enquires/${this.leadData[this.deleteIndex].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // console.log(response.data, 'What happening...!!!!');
                        this.updateKeyWithTime('enquires_update');
                        this.message = 'enquiry deleted successfully...!';
                        this.show = true;
                        this.open_loader = false;
                        // this.open_message = true;
                        // this.message = response.data.message;
                        this.open_confirmBox = false;
                        this.deleteIndex = null;
                        this.getLeadList(this.leadData.length > 1 ? this.currentPage : this.currentPage - 1 !== 0 ? this.currentPage - 1 : 1, this.recordsPerPage);
                    })
                    .catch(error => {
                        this.open_loader = false;
                        console.error('Error', error);
                    })
            }

        },
        confirmDelete(index) {
            if (this.getplanfeatures('website_enquiry')) {
                this.no_access = true;
            } else {
                this.deleteIndex = index;
                this.open_confirmBox = true;
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                if (this.isDropdownOpen) {
                    this.isDropdownOpen = false;
                    document.removeEventListener('click', this.handleOutsideClick);
                }
                else {
                    this.isDropdownOpen = true; // Open dropdown
                    // Attach event listener to close dropdown when clicking outside
                    document.addEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick() {
            try {
                // Check if the click is outside the dropdown
                const dropdownRef = this.$refs.dropdownSetting;
                // console.log(dropdownRef, 'What happening......!!!', event.target);
                if (dropdownRef && !dropdownRef.contains(event.target)) {
                    this.isDropdownOpen = false; // Close dropdown
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        //---close the message--
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---record---
        startView(record) {
            if (this.getplanfeatures('website_enquiry')) {
                this.no_access = true;
            } else {
                this.viewData = record;
                this.open_enquiry = true;
            }
        },
        closeEnquiryData() {
            this.viewData = null;
            this.open_enquiry = false;
        },
        //---update enquiry---
        updateEnquiry(record) {
            if (record && record.id) {
                axios.put(`/enquires/${record.id}`, { status: record.status })
                    .then(response => {
                        console.log(response.data, 'Status Data');
                        this.getLeadList(this.currentPage, this.recordsPerPage);
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }
        },
        //---lead---
        toggleFilterSelected(option) {
            this.selectedByValue = option;
            this.lead_filter = true;
            this.leadData = this.originalData;
            this.showFilterOptions = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //--close lead
        closeLeadFilter(searchData, page) {
            if (searchData) {
                // this.filteredBy = searchData;
                const keysData = Object.keys(searchData);
                this.filteredBy = searchData
                this.searchByCustomer = {};
                this.open_loader = true;
                axios.get('/searchs', { params: { type: 'enquiry', q: searchData.status >= 0 ? searchData.status : this.status_select !== 3 ? this.status_select : '', filter: '', per_page: this.recordsPerPage, page: page, from_date: searchData.from ? searchData.from : '', to_date: searchData.to ? searchData.to : '' } })
                    .then(response => {
                        console.log(response.data, 'Status Data');
                        this.open_loader = false;
                        this.leadData = response.data.data;
                        this.decodeData = true;
                        this.pagination = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
            this.lead_filter = false;
        },
        //---filter dropdown
        toggleFilter() {
            this.showFilterOptions = !this.showFilterOptions;
        },
        //--hidden dropdown--
        hiddenDropdown() {
            if (!this.mouseOverIsNot) {
                this.showFilterOptions = false;
            }
        },
        mouseOverOption() {
            this.mouseOverIsNot = true;
        },
        mouseLeaveOption() {
            this.mouseOverIsNot = false;
        },
        //---reset filter--

        resetTheFilter() {
            this.filteredBy = {};
            this.status_select = 3;
            this.resetSearch('');
            this.getLeadList(1, this.recordsPerPage);
            this.currentPage = 1;
            this.resetSearchData = true;
            this.decodeData = true;
        },

        //---formated display date---
        formattedDate(timestamp) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            date.setHours(date.getHours() + 5); // Add 5 hours
            date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        //---get lead data--
        //---lead List--
        getLeadList(page, per_page) {
            if (page == 1) {
                this.fetchEnquiryList({ page: page, per_page: this.isMobile ? 20 : per_page });
                if (this.currentEnquiryList && this.currentEnquiryList.data) {
                    this.leadData = this.currentEnquiryList.data;
                    this.originalData = this.leadData;
                    this.decodeData = true;
                    this.pagination = this.currentEnquiryList.pagination;
                }
            } else {
                this.open_skeleton = true;
                axios.get('/enquires', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        this.leadData = response.data.data;
                        this.decodeData = true;
                        this.originalData = this.leadData;
                        this.pagination = response.data.pagination;
                        this.open_skeleton = false;
                        let status_counts = response.data.status;
                        if (status_counts.Created >= 0) {
                            this.status_option[0].total = status_counts.Created;
                        }
                        if (status_counts.Converted >= 0) {
                            this.status_option[1].total = status_counts.Converted;
                        }
                        if (status_counts.Cancel >= 0) {
                            this.status_option[2].total = status_counts.Cancel;
                        }
                        if (this.pagination.total >= 0) {
                            this.status_option[3].total = this.pagination.total;
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        //--filter option--
        changeFilter(opt) {
            this.filter_option = opt;
            this.toggleMainDropdown();
            if (opt === 'date') {
                this.filter_date = !this.filter_date;
                if (!this.filter_date) {
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutsideDate);
                } else {
                    document.addEventListener('click', this.handleClickOutsideDate);
                }
            }
            if (opt === 'category') {
                this.toggleFilterSelected('by category/Type');
            }
            if (opt === 'by Date') {
                this.toggleFilterSelected('by Date');
            }
        },
        handleClickOutsideDate(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs.componentContainer;
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.filter_date = !this.filter_date;
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutsideDate);
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        getFollowUpDate(fieldValue) {
            // console.log(fieldValue, 'EEEEEEEEEEEEEEEEEEEEEEE');
            if (fieldValue !== '' && typeof fieldValue === 'string') {
                const parsedValue = JSON.parse(fieldValue);
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    return this.generateDate(parsedValue[0].date_and_time);
                }
            } else if (typeof fieldValue === 'object') {
                if (Array.isArray(fieldValue) && fieldValue.length > 0 && fieldValue[0].date_and_time) {
                    return this.generateDate(fieldValue[0].date_and_time);
                }
            }
        },
        generateDate(data) {
            // console.log(data, 'What about the data......');
            const dateTimeString = data;
            const date = new Date(dateTimeString);
            const formattedDate = date.toLocaleDateString('en-GB', { year: 'numeric', month: '2-digit', day: '2-digit' });
            const formattedTime = date.toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' });
            return formattedDate + ' ' + formattedTime;
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.ceil(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },

        getStatusLabel(data, page) {
            this.status_select = data;
            if (data == 3) {
                this.resetSearch('');
                this.searchByCustomer = {};
            }
            this.open_loader = true;
            axios.get('/searchs', {
                params: {
                    type: 'enquiry', q: data !== 3 ? data : '', per_page: this.recordsPerPage, page: page
                }
            })
                .then(response => {
                    console.log(response.data, 'Status Data');
                    this.open_loader = false;
                    this.leadData = response.data.data;
                    this.decodeData = true;
                    this.pagination = response.data.pagination;
                    this.currentPage = page ? page : this.currentPage;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        ...mapActions('lead', ['updateSearch', 'updateData']),
        resetSearch(newValue) {
            try {
                const newQuery = '';
                this.updateSearch(newQuery);
                const newData = {};
                this.updateData(newData);
                this.$emit('dataToParent', newValue);
            } catch (error) {
                console.error('Error resetting search:', error);
            }
        },
        closeFilterOptionsOnClickOutside(event) {
            // console.log(event, 'EEE', this.$refs.filterOptionsDropdown);
            // Check if the click event target is outside the filter options dropdown
            const filterOptionsElement = this.$refs.filterOptionsDropdown;
            if (filterOptionsElement && !filterOptionsElement.contains(event.target)) {
                // Click occurred outside the filter options dropdown, so close it
                this.showFilterOptions = false;
            }
        },
        getDateStatusClass(fieldValue) {
            try {
                const parsedValue = typeof fieldValue === 'string' ? JSON.parse(fieldValue) : fieldValue;
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    const followUpDate = new Date(parsedValue[0].date_and_time);
                    const currentDate = new Date();

                    if (followUpDate < currentDate) {
                        return 'text-red-500';
                    } else {
                        return 'text-green-500';
                    }
                }
            } catch (error) {
                console.error('Error parsing date:', error);
            }
            return ''; // Default empty string if date cannot be parsed
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            let send_data = {
                type: 'enquiry', q: this.status_select >= 0 && this.status_select !== 3 ? this.status_select : '', per_page: this.recordsPerPage, page: page
            };


            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.leadData = [...this.leadData, ...response.data.data];
                        this.decodeData = true;
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        //---initial data
        async getInitialData(leadData) {
            // console.log(response.data.data, 'RRRRRR');
            this.leadData = leadData.data || []; // Ensure leadData.data exists, default to an empty array
            this.decodeData = true;
            this.pagination = leadData.pagination;
            this.open_skeleton = false;
            let status_counts = leadData.status;
            if (status_counts) {
                if (status_counts.Created >= 0) {
                    this.status_option[0].total = status_counts.Created;
                }
                if (status_counts.Converted >= 0) {
                    this.status_option[1].total = status_counts.Converted;
                }
                if (status_counts.Cancel >= 0) {
                    this.status_option[2].total = status_counts.Cancel;
                }
            }
            if (this.pagination.total >= 0) {
                this.status_option[3].total = this.pagination.total;
            }
        },
        //--role based on--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        //--filter dropdown---
        toggleMainDropdown() {
            this.isMainDropdownOpen = !this.isMainDropdownOpen;
            if (this.isMainDropdownOpen) {
                document.addEventListener('click', this.handleClickOutsideFilter);
            } else {
                // this.showSubDropdown = false;
                document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        toggleSubDropdown() {
            setTimeout(() => {
                if (!this.is_openSub) {
                    this.showSubDropdown = !this.showSubDropdown;
                }
            }, 200)
        },
        toggleMouserHoverSub() {
            this.is_openSub = !this.is_openSub;
            if (!this.is_openSub && this.showSubDropdown) {
                this.toggleSubDropdown();
            }
        },
        handleClickOutsideFilter(event) {
            const mainDropdown = this.$refs.dropdownContainerFilter;
            // console.log(event.target, 'Waht happning the data....', mainDropdown.contains(event.target));
            if (mainDropdown && !mainDropdown.contains(event.target)) {
                this.is_openSub = false;
                this.showSubDropdown = false;
                this.toggleMainDropdown();
                // document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'leads' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.leadData = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.leadData, type: 'enquiry', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'leads' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.leadData = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.leadData, type: 'enquiry', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },

        closeAllModals() {
            // Close all modals
            this.open_confirmBox = false;
            this.open_message = false;
            this.lead_filter = false;
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                let searchDatacustomer = {}
                searchDatacustomer.customer_id = selectedData.id;
                searchDatacustomer.customer = selectedData.first_name + (selectedData.last_name ? ' ' + selectedData.last_name : '') + ' - ' + selectedData.contact_number;
                this.closeLeadFilter(searchDatacustomer, 1);
            }
        },
        resetToSearch() {
            this.filteredBy = {};
            this.resetSearchData = false;
            this.getLeadList(1, this.recordsPerPage);
        },
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //--refresh page--
        refreshDataTable() {
            this.resetTheFilter();
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
    },
    watch: {

        currentPage: {
            deep: true,
            handler(newValue) {
                if ((this.status_select === null || this.status_select === 3)) {
                    this.getLeadList(newValue, this.recordsPerPage);
                }
                else {
                    // console.log(this.filteredBy, 'RRRRRR Waht happening....!!!');
                    if (this.status_select !== null && this.status_select >= 0 && this.status_select !== 3) {
                        this.getStatusLabel(this.status_select, newValue);
                    } else {
                        this.getLeadList(1, newValue);
                    }
                }
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if ((this.status_select === null || this.status_select === 3)) {
                    this.getLeadList(1, newValue);
                    this.currentPage == 1;
                } else {
                    if (this.status_select !== null && this.status_select >= 0 && this.status_select !== 3) {
                        this.getStatusLabel(this.status_select, 1);
                    } else {
                        this.getLeadList(1, newValue);
                        this.currentPage == 1;
                    }
                }
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('enquiry_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentEnquiryList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.getInitialData(newValue);
                }
                this.open_loader = false;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.filter_option = null;
                this.resetTheFilter();
                this.fetchLeadTypeList()
                this.fetchEnquiryList({ page: 1, per_page: this.isMobile ? 20 : per_page });
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.leadData = [...newValue];
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        lead_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        decodeData: {
            deep: true,
            async handler(newValue) {
                if (newValue) {
                    if (this.leadData.length > 0) {
                        this.leadData = await this.leadData.map(opt => {
                            try {
                                // Check if opt.data is a valid JSON string and parse it
                                if (opt.data && typeof opt.data === 'string') {
                                    const parsedData = JSON.parse(opt.data);
                                    return { ...opt, data: parsedData }; // Merge parsed data with the existing object
                                }
                            } catch (error) {
                                console.error('Error parsing JSON:', error, opt.data);
                            }
                            return opt; // Return the original object if parsing fails or opt.data is not a string
                        });
                        this.originalData = this.leadData;
                        this.decodeData = false;
                    }
                }
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        if (this.showFilterOptions) {
            document.addEventListener('click', this.closeFilterOptionsOnClickOutside);
        } else {
            document.removeEventListener('click', this.closeFilterOptionsOnClickOutside);

        }
    },
    beforeDestroy() {
        if (this.showFilterOptions) {
            document.removeEventListener('click', this.closeFilterOptionsOnClickOutside);
        }
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        document.removeEventListener('click', this.handleClickOutsideDate());
        document.removeEventListener('click', this.handleClickOutsideFilter());
    }
}
</script>

<style scoped>
.manualStyle {
    overflow: auto;
    height: 100vh;
}

/* Style the scrollbar */
::-webkit-scrollbar {
    width: 3px;
    /* Set the width of the scrollbar */
    height: 4px;
}

/* Track (the area around the scrollbar) */
::-webkit-scrollbar-track {
    background: #CFD8DC;
    /* Background color of the scrollbar track */
}

/* Handle (the draggable part of the scrollbar) */
::-webkit-scrollbar-thumb {
    background: #90A4AE;
    /* Color of the scrollbar handle */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #90A4AE;
    /* Color of the scrollbar handle on hover */
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
    z-index: 100;
}

.bg-blue-800 {
    background-color: #053BF4;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }

    .flex-row>div {
        white-space: nowrap;
        /* Prevent wrapping */
    }
}

@media (max-width: 768px) {
    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }

}
</style>
