<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="set-header-background justify-between items-center flex py-2">
                <h2 class="text-white text-center ml-12 text-xl py-1">
                    {{ type }}</h2>
                <p class="close pr-5" @click="cancelModal"><font-awesome-icon icon="fa-solid fa-xmark" /></p>
            </div>

            <!-- Form for CRUD operations -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-4 m-4">
                <form @submit.prevent="submitForm" class="text-sm text-center">
                    <input type="text" v-model="name" placeholder="Category Name" ref="inputName"
                        class="block mb-3 border px-3 py-2 w-full">
                    <button type="submit"
                        class=" rounded rounded-md px-3 py-2 mt-3 bg-green-700 hover:bg-green-600 text-white">
                        {{ updateIndex === null ? 'Create' : 'Update' }}</button>
                    <button class=" rounded rounded-md px-3 py-2 mt-3 bg-red-700 hover:bg-red-600 text-white ml-5"
                        @click="cancelModal">Close</button>
                </form>
            </div>

            <!-- Display categories -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-3 m-4 text-sm"
                :key="categories.length">
                <p class="font-bold underline mb-2">List:</p>
                <ul>
                    <li v-for="(category, index) in categories" :key="index" class="flex justify-between">
                        <div>{{ category.name }}</div>
                        <div class="flex justify-between">
                            <button @click="editCategory(index)">
                                <img :src="table_edit" alt="table-edit" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                            <button @click="deleteCategory(index)">
                                <img :src="table_del" alt="table-delete" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import axios from 'axios';
import confirmbox from './confirmbox.vue';
import dialogAlert from './dialogAlert.vue';
export default {
    name: 'accessories',
    components: {
        confirmbox,
        dialogAlert
    },
    props: {
        showModal: Boolean,
        type: String,
        categoriesData: Object,
    },
    data() {
        return {
            isMobile: false,
            formValues: {},
            isOpen: false,
            name: '',
            categories: [],
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            deleteIndex: null,
            open_confirmBox: false,
            updateIndex: null,
            isMessageDialogVisible: false,
            message: '',
            //--api integration---
            companyId: null,
            userId: null,
            //--toaster---
            show: false,
            type_toaster: 'warning',

        };
    },
    methods: {
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeModal', this.categories);
                this.categories = [];
            }, 300);

        },
        submitForm() {
            // console.log(this.updateIndex, 'What is the index valuye...!');
            if (this.name !== '' && this.name) {
                let find_data = null;
                if (this.updateIndex >= 0 && this.updateIndex !== null) {
                    find_data = this.categories.some((data) => data.id !== this.categories[this.updateIndex].id && data.name.toLowerCase().includes(this.name.toLowerCase()));
                } else {
                    find_data = this.categories.some((data) => data.name.toLowerCase().includes(this.name.toLowerCase()));
                }
                // Create category
                if (this.updateIndex === null && !find_data) {
                    axios.post('/rma_accessories', { name: this.name, company_id: this.companyId, user_id: this.userId })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data);
                            this.categories.unshift(response.data.data);
                            this.name = '';
                            this.updateIndex = null;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                } else if (this.updateIndex >= 0 && !find_data) {
                    axios.put(`/rma_accessories/${this.categories[this.updateIndex].id}`, { id: this.categories[this.updateIndex].id, name: this.name })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data);
                            this.categories[this.updateIndex] = response.data.data;
                            this.name = '';
                            this.updateIndex = null;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                } else {
                    // this.isMessageDialogVisible = true;
                    this.message = `Name already exists..!`;
                    this.show = true;
                }

            } else {
                // this.isMessageDialogVisible = true;
                this.message = `Please enter the accessories name..!`;
                this.show = true;
            }
        },
        editCategory(index) {
            // Edit category (not implemented in this example)
            // console.log('Edit category:', this.categories[index]);
            this.name = this.categories[index].name;
            this.updateIndex = index;
            // console.log(index, 'WWWWWW');
        },
        deleteCategory(index) {
            // Delete category
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },

        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.inputName;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---confirm box funxctions
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                // console.log(this.deleteIndex, 'Whatjjjjjj');
                axios.delete(`/rma_accessories/${this.categories[this.deleteIndex].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        console.log(response.data);
                        this.categories.splice(this.deleteIndex, 1);
                    })
                    .catch(error => {
                        console.error(error);
                    });

                // this.saveToLocalStorage();
                this.open_confirmBox = false;
            }
        },

        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---close message--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
        }
    },

    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                // console.log('TTTTTTTTTTTTTTT');
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                    this.categories = this.categoriesData;
                }
            }, 100);
        },
        // categoriesData(newValue) {
        //     this.categories = newValue;
        //     console.log(newValue, 'What is happening....! by the moduleis...!');
        // }
    },
};
</script>

<style>
/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.close {
    color: white;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: red;
    text-decoration: none;
    cursor: pointer;
}
</style>