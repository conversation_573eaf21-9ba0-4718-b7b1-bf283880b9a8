import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
// import { VitePWA } from 'vite-plugin-pwa';
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    // VitePWA({
    //   registerType: 'autoUpdate',
    //   injectRegister: 'auto',
    //   workbox: {
    //     cleanupOutdatedCaches: true,
    //     globPatterns:['**/*.{js,css,html,ico,png,svg,json,vue,txt,woff2}']        
    //   },
    //   manifest: {
    //     name: 'track-newnew',
    //     short_name: 'track',
    //     start_url: "https://devapp.track-new.com",
    //     categories: ["Services", "Leads", "AMC", "Sales", "Estimate", "Inventory", "customer", 'Expenses', 'Setting'],
    //     description: 'Track the services',
    //     theme_color: '#4DBA87',
    //     display_override: ["fullscreen", "minimal-ui"],
    //     display: "standalone",
    //     icons: [
    //       {
    //         src: '/images/icons/favicon.svg',
    //         sizes: 'any',
    //         type: 'image/svg+xml',
    //       },
    //       {
    //         src: '/images/icons/favicon-32x32.png',
    //         sizes: '32x32',
    //         type: 'image/png',
    //       },
    //       {
    //         src: '/images/icons/favicon-16x16.png',
    //         sizes: '16x16',
    //         type: 'image/png',
    //       },
    //       {
    //         src: '/images/icons/apple-touch-icon-152x152.png',
    //         sizes: '152x152',
    //         type: 'image/png',
    //       },
    //       {
    //         src: '/images/icons/safari-pinned-tab.svg',
    //         sizes: 'any',
    //         type: 'image/svg+xml',
    //       },
    //       {
    //         src: '/images/icons/msapplication-icon-144x144.png',
    //         sizes: '144x144',
    //         type: 'image/png',
    //       },
    //     ],
    //   },      
    // }),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)), 
      "source-map-js": "source-map",
    },
  },
  //---added new
  server: {
    proxy: {
        '/api': {
            target: 'http:// 192.168.1.58:8000',
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/api/, '')
        }
    }
  },
  
})
