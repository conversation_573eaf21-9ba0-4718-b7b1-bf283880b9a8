<template>
    <div v-if="isOpen" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpenModal, 'translate-x-full right-12': !isOpenModal, 'pb-[60px]': isMobile }">
            <!-- Close button -->
            <div class="justify-between items-center flex py-4 set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Import Customer Data
                </p>
                <p class="close pr-5" @click="closeModal">&times;</p>

            </div>
            <!-- Download Sample CSV button -->
            <div CLASS="p-4">
                <div class="flex justify-between">
                    <div>
                        <button @click="downloadSampleCSV"
                            class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 mt-4 rounded">Download Sample
                            CSV</button>
                    </div>
                    <div>
                        <button class="bg-gray-300 hover:bg-gray-400 border px-4 py-2 mt-4 rounded items-center"
                            @click="openAvailableOptions">
                            <font-awesome-icon icon="fa-solid fa-circle-info" /> Info</button>
                    </div>
                </div>
                <!-- File upload and preview section -->
                <input type="file" @change="handleFileUpload" class="mt-5 block" id="fileInput" />

                <div v-if="importedData && importedData.length > 0 && !open_loader"
                    class="table-container overflow-x-auto mt-4">
                    <table class="table w-full">
                        <!-- Table headers -->
                        <thead>
                            <tr class="font-light">
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    class="border text-sm py-1 set-header-background text-white"
                                    :class="{ 'hidden': column.label === 'Editing' }">
                                    {{ column.label }} <span class="text-red-600"
                                        :class="{ 'hidden': column.label !== 'First Name' && column.label !== 'Contact Number' }">*</span>
                                </th>
                                <th class="border px-1 py-1 set-header-background text-white text-sm leading-none">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <!-- Table body -->
                        <tbody>
                            <tr v-for="(record, index) in importedData" :key="index" class="py-2"
                                :ref="'invalidRow' + index" :class="{ 'highlight-row': invalidRows.includes(index) }">
                                <td v-for="(value, key) in record" :key="key" class="px-1 py-1 text-sm text-center"
                                    :class="{ 'hidden': key === 'editing' }">
                                    <!--&& key !== 'state_name' && key !== 'district_name' && key !== 'city_name'-->
                                    <div v-if="key !== 'customer_category'" class="w-36">
                                        <!-- Turn each value into an input field -->
                                        <input v-if="key === 'opening_balance'" type="number"
                                            v-model="importedData[index][key]" @input="preventNegativeValue"
                                            class="w-full p-1 text-sm border rounded" />
                                        <input v-else-if="key === 'birth_date' || key === 'anniversary_date'"
                                            type="date" v-datepicker v-model="importedData[index][key]"
                                            @input="preventNegativeValue" class="w-full p-1 text-sm border rounded" />

                                        <input v-else type="text" v-model="importedData[index][key]"
                                            @keypress.enter="focusNextInvalidRow(index)"
                                            @input="validateInputValue(key, importedData[index][key])"
                                            class="w-full p-1 text-sm border rounded" />
                                    </div>
                                    <div v-if="key === 'customer_category'" class="flex w-36">
                                        <select v-model="importedData[index][key]" :ref="'category_id' + index"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full outline-none focus:border-blue-500 rounded rounded-r-none"
                                            title="Click &#43; icon to add new or edit">
                                            <option v-for="(cat, j) in serviceCategories" :key="j" :value="cat.id"
                                                :class="{ 'hidden': cat.service_status !== '1' }">
                                                {{ cat.category_name }}</option>
                                        </select>
                                        <button @click="openModalService(index)"
                                            class="border border-teal-600 bg-teal-600 text-center items-center font-bold text-lg px-2 mt-1 text-white hover:border-green-700">+</button>
                                    </div>
                                    <!-- <div v-if="key === 'state_name'" class="flex w-36">
                                        <select id="state_name_com" v-model="importedData[index][key]"
                                            @change="getDistrictList(importedData[index][key], index)"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                            @focus="isInputFocusedStateCom = true"
                                            @blur="isInputFocusedStateCom = false">
                                            <option v-for="(opt, index) in this.state_list" :key="index"
                                                :value="opt.name">
                                                {{ opt.name }}</option>
                                        </select>
                                    </div>
                                    <div v-if="key === 'district_name' && importedData[index]['state_name'] && importedData[index]['state_name'] !== ''"
                                        class="flex w-36">
                                        <select id="district_name_com" v-model="importedData[index][key]"
                                            @change="getCityList(importedData[index]['district_name'], index)"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                            @focus="isInputFocusedDistrictCom = true"
                                            @blur="isInputFocusedDistrictCom = false">
                                            <option v-for="(opt, index) in district_list[index]"
                                                :class="{ 'hidden': index === 0 || !opt }" :key="index"
                                                :value="opt.name">{{
                                                    opt.name }}
                                            </option>
                                        </select>
                                    </div>
                                    <div v-if="key === 'city_name' && importedData[index]['district_name'] && importedData[index]['district_name'] !== ''"
                                        class="flex w-36">
                                        <select id="city_name" v-model="importedData[index][key]"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                            @focus="isInputFocusedCityCom = true" @blur="isInputFocusedCityCom = false">
                                            <option v-for="(opt, index) in city_list[index]" :key="index"
                                                :value="opt.name">
                                                {{ opt.name }}</option>
                                        </select>
                                    </div> -->
                                </td>
                                <td class="border px-1 py-1 text-sm text-center">
                                    <div class="flex">
                                        <button @click="saveImportedData(record, index)"
                                            class="text-green-500 px-2 py-1 ml-2 border rounded hover:bg-green-500 hover:text-white">
                                            <font-awesome-icon icon="fa-regular fa-floppy-disk" />
                                        </button>
                                        <button @click="confirmDelete(index)"
                                            class="text-red-700 px-2 py-1 ml-1 border rounded hover:bg-red-500  hover:text-white">
                                            <font-awesome-icon icon="fa-solid fa-trash-can" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td :colspan="Object.keys(importedData[importedData.length - 1]).length - 1"
                                    class="px-2 py-2 flex justify-center items-center border-2 border-green-800 hover:bg-green-100 rounded">
                                    <button
                                        @click="addNewCustomer(Object.keys(importedData[importedData.length - 1]).length - 1)">+
                                        ADD Customer</button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <div class="flex w-full space-x-2">
                    <!-- Save button (conditionally rendered) -->
                    <button v-if="importedData && importedData.length > 0" @click="validateAndClose"
                        class="bg-green-600 text-white px-4 py-2 mt-4 rounded">
                        Save All
                    </button>
                    <!-- Delete Duplicate Contacts button -->
                    <button v-if="exist_contact && exist_contact.length > 0" @click="removeExistContacts"
                        class="bg-red-600 text-white px-4 py-2 mt-4 lg:ml-5 rounded hover:bg-red-700">
                        Delete Duplicate Contacts
                    </button>

                    <!-- Save Anyway button -->
                    <button v-if="exist_contact && exist_contact.length > 0" @click="saveAnyway"
                        class="bg-yellow-500 text-white px-4 py-2 mt-4 lg:ml-5 rounded hover:bg-yellow-600">
                        Save Anyway
                    </button>

                    <button v-if="importedData && importedData.length > 0" @click="resetAllData"
                        class="bg-pink-600 text-white px-4 py-2 mt-4 lg:ml-5 rounded">
                        Reset
                    </button>
                </div>
            </div>
        </div>
        <dialogAlert :showModal="open_message" :message="message" @close="closeAlert"></dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="removeImportedData" @onCancel="cancelDelete"></confirmbox>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <Loader :showModal="open_loader"></Loader>
        <!--available options details are-->
        <showavailableOptions :show-modal="show_available" @close-Modal="closeAvailableOptions" :isMobile="isMobile"
            :type="'customer'" :customer_category="serviceCategories">
        </showavailableOptions>
        <addCustomerCategory :show-modal="showModal_add_service" @close-modal="closeModalService"
            :companyId="companyId">
        </addCustomerCategory>
    </div>
</template>

<script>
import dialogAlert from './dialogAlert.vue';
import confirmbox from './confirmbox.vue';
import showavailableOptions from './showavailableOptions.vue';
import addCustomerCategory from './addCustomerCategory.vue';
import { mapGetters, mapActions } from 'vuex';
import Papa from 'papaparse';
export default {
    components: {
        dialogAlert,
        confirmbox,
        showavailableOptions,
        addCustomerCategory
    },
    props: {
        isOpen: Boolean,
        companyId: String,
        userId: String,
        isMobile: Boolean
    },
    data() {
        return {
            showSaveButton: false,
            importedData: null,
            open_message: false,
            message: '',
            deleteIndex: null,
            open_confirmBox: false,
            category_list: [],
            exist_contact: [],
            //---open model data---
            isOpenModal: false,
            open_model: false,
            type_model: null,
            list_data: null,
            //---invoice setting---
            invoice_setting: [],
            //--toaster---
            show: false,
            type_toaster: 'warning',
            //---loader--
            open_loader: false,
            //dropdown options---
            showSuggestions: null,
            selectedIndex: 0,
            //---reset--
            reset_All: false,
            //--available options-
            show_available: false,
            //--add category---
            showModal_add_service: false,
            serviceCategories: [],
            //---address---
            state_list: [],
            district_list: [],
            city_list: [],
            invalidRows: [], // Stores invalid row indexes
            focusedInvalidIndex: null, // Tracks the focused invalid row index
            isDisplayed: false,
        }
    },
    computed: {
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the specified order to create fields
            if (this.importedData && this.importedData.length !== 0) {

                for (const key of Object.keys(this.importedData[0])) {
                    const label = formatLabel(key);
                    fields.push({ label, field: key, visible: true });
                }
                this.columns = fields;
                return fields;
            }
        },
    },
    methods: {
        closeModal() {
            if (this.importedData) {
                this.$emit('close', this.importedData);
                this.importedData = [];
            } else {
                this.$emit('close');
            }
        },
        async handleFileUpload(event) {
            this.isDisplayed = false;
            const file = event.target.files[0];
            if (file) {
                this.open_loader = true;
                const reader = new FileReader();
                reader.onload = async () => {
                    // Parse the CSV and remove empty rows
                    const parsedData = await this.parseCSV(reader.result);
                    if (parsedData) {
                        const nonEmptyRows = parsedData.filter(row => Object.values(row).some(value => value !== undefined && value !== null && value !== ''));
                        // Add editing key and set its value to false for each item
                        const importedDataWithEditing = nonEmptyRows.map(row => ({ ...row, editing: false }));

                        this.importedData = importedDataWithEditing;
                        if (this.importedData.length > 0) {
                            const uniqueContactNumber = new Set();
                            const duplicateItems = new Set();
                            // Helper function to introduce a delay
                            const delay = ms => new Promise(resolve => setTimeout(resolve, ms));

                            // Iterate over the imported data
                            for (let index = 0; index <= this.importedData.length - 1; index++) {
                                const importedItem = this.importedData[index];
                                const contact_number = importedItem.contact_number;

                                // Check for duplicate contact number
                                if (!uniqueContactNumber.has(contact_number)) {
                                    uniqueContactNumber.add(contact_number);
                                } else if (contact_number) {
                                    duplicateItems.add(contact_number);
                                    this.importedData.splice(index, 1);
                                    continue; // Skip to next iteration
                                }

                                let { birth_date, anniversary_date, state_name, district_name, city_name, customer_category, address } = importedItem;

                                // Format birth_date
                                if (birth_date) {
                                    birth_date = this.formatDateToISO(birth_date);
                                    importedItem.birth_date = birth_date;
                                }

                                // Format anniversary_date
                                if (anniversary_date) {
                                    anniversary_date = this.formatDateToISO(anniversary_date);
                                    importedItem.anniversary_date = anniversary_date;
                                }

                                // Match state_name with state_list
                                // if (state_name && this.state_list && Array.isArray(this.state_list) && this.state_list.length > 0) {
                                //     const matchedState = this.state_list.find(opt => opt.name.toLowerCase() == state_name.toLowerCase() || opt.name.toLowerCase().includes(state_name.toLowerCase()));
                                //     if (matchedState) {
                                //         importedItem.state_name = matchedState.name;
                                //     }
                                // }

                                // Get district list based on state_name
                                // Get district list based on state_name and match district_name
                                // if (district_name) {
                                //     const districtList = await this.getDistrictList(importedItem.state_name, index); // Ensure it returns district data

                                //     if (Array.isArray(this.district_list) && this.district_list[index]) {
                                //         // Find exact or partial match
                                //         const matchedDistrict = this.district_list[index].find(opt =>
                                //             opt.name.toLowerCase() == district_name.toLowerCase() ||
                                //             opt.name.toLowerCase().includes(district_name.toLowerCase())
                                //         );
                                //         if (matchedDistrict) {
                                //             importedItem.district_name = matchedDistrict.name; // Update district_name with matched value
                                //         }
                                //     }
                                // }

                                // Get city list based on district_name and match city_name
                                // if (city_name) {
                                //     const cityList = await this.getCityList(importedItem.district_name, index); // Ensure it returns city data
                                //     if (Array.isArray(this.city_list) && this.city_list[index]) {
                                //         // Find exact or partial match
                                //         const matchedCity = this.city_list[index].find(opt =>
                                //             opt.name.toLowerCase() == city_name.toLowerCase() ||
                                //             opt.name.toLowerCase().includes(city_name.toLowerCase())
                                //         );
                                //         if (matchedCity) {
                                //             importedItem.city_name = matchedCity.name; // Update city_name with matched value
                                //         }
                                //     }
                                // }
                                // Match customer_category with serviceCategories
                                if (customer_category && this.serviceCategories && Array.isArray(this.serviceCategories)) {
                                    const matchedCategory = this.serviceCategories.find(opt => opt.category_name.toLowerCase() === customer_category.toLowerCase() && opt.service_status !== '0');
                                    if (matchedCategory) {
                                        importedItem.customer_category = matchedCategory.id;
                                    }
                                }
                                // Introduce a delay of 100 milliseconds before proceeding to the next iteration
                                // await delay(1);
                                if (index === this.importedData.length - 1) {
                                    this.open_loader = false;
                                }
                            }
                            // Handle duplicates and notify user
                            if (Array.from(duplicateItems).length > 0) {
                                this.message = `Duplicate contacts found: ${Array.from(duplicateItems).join(', ')}. These are removed successfully`;
                                this.open_message = true;
                                this.open_loader = false;
                            }

                        }
                    } else {
                        this.open_loader = false;
                    }
                };
                reader.readAsText(file);
            }
        },

        async parseCSV(csvData) {
            // Split the CSV data by new lines to get each row
            // const lines = csvData.split('\n').map(line => line.trim()).filter(line => line.length > 0);
            const lines = await this.updateCSVData(csvData);
            if (lines && lines.length > 0) {

                // The first line contains headers, which are separated by commas
                // const headers = lines[0].split(',').map(header => header.trim());

                // const parsedData = [];

                // // Iterate through each line, starting from the second line (row 1) to parse the data
                // for (let i = 1; i < lines.length; i++) {
                //     const row = {};
                //     const values = lines[i].split(',').map(value => value.trim()); // Split by commas and trim extra spaces

                //     // Map the values to their respective headers
                //     for (let j = 0; j < headers.length; j++) {
                //         // Ensure we don't try to access an undefined index (in case there are missing columns in a row)
                //         row[headers[j]] = values[j] ? values[j] : ''; // If a value is empty, assign an empty string
                //     }

                //     // Add the row object to the parsedData array
                //     parsedData.push(row);
                // }

                return lines;
            } else {
                return;
            }
        },
        updateCSVData(csvData) {
            return new Promise((resolve) => {
                Papa.parse(csvData, {
                    header: true,  // Returns data as objects
                    skipEmptyLines: true,
                    complete: (result) => resolve(result.data),
                });
            });
        },

        downloadSampleCSV() {
            const sampleData = [
                { first_name: 'Muthu', last_name: 'Kumar', contact_number: '9436218812', alternative_number: '4248242', email: '<EMAIL>', opening_balance: 100, birth_date: 'DD-MM-YYYY', anniversary_date: 'DD-MM-YYYY', bussiness_name: 'XYZ company pvt.ltd', gst_number: '29ADFGE1234AD1A', address: '#23 gangammagarden', pincode: '312124', state_name: 'Tamilnadu', district_name: 'Tirupattur', city_name: 'Tirupattur', customer_category: 'Regular', notes: 'Enter the notes' },
                { first_name: 'Vel', last_name: 'Shetty', contact_number: '9436218811', alternative_number: '646353', email: '<EMAIL>', opening_balance: 200, birth_date: 'DD-MM-YYYY', anniversary_date: 'DD-MM-YYYY', bussiness_name: 'A to z company pvt.ltd', gst_number: '45TYETY1234AS1F', address: '#12 golden street, gopalan post', pincode: '312124', state_name: 'Tamilnadu', district_name: 'Tirupattur', city_name: 'Tirupattur', customer_category: 'Regular', notes: 'Enter the notes' },
            ];
            // Function to escape CSV values
            const escapeCSVValue = (value) => {
                if (typeof value === 'string' && value.includes(',')) {
                    return `"${value}"`; // Wrap in double quotes if the value contains a comma
                }
                return value;
            };
            // Convert sampleData to CSV format
            // const csvContent = "Product Name, Category Name, Brand Name, Unit, HSN Code, Alert Qty, Barcode, Discount Type, Discount Value, Purchase Price, Tax Type, Tax Value, Sales Price, Dealer Price, Total Qty, Warranty, Notes\n" +
            //     sampleData.map(row => Object.values(row).join(",")).join("\n");
            // Extract keys from the first object in the sampleData array for headers
            const headers = Object.keys(sampleData[0]);

            // Convert sampleData to CSV format
            // const csvContent = headers.join(",") + "\n" +
            //     sampleData.map(row => headers.map(header => row[header]).join(",")).join("\n");
            // Convert sampleData to CSV format
            const csvContent = headers.join(',') + '\n' +
                sampleData.map(row => headers.map(header => escapeCSVValue(row[header])).join(',')).join('\n');



            // Create a Blob from the CSV content
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });

            // Create a URL object using the Blob
            const url = URL.createObjectURL(blob);

            // Create a download link and trigger the download
            const link = document.createElement("a");
            link.setAttribute("href", url);
            link.setAttribute("download", "Customer_import.csv");
            document.body.appendChild(link);
            link.click();

            // Cleanup
            document.body.removeChild(link);
            URL.revokeObjectURL(url);
        },

        saveImportedData(record, index) {
            if (index >= 0 && record && Object.keys(record).length > 0) {
                this.open_loader = true;
                axios.post('/customers', { ...record, company_id: this.companyId, user_id: this.userId, image: '' })
                    .then(response => {
                        // console.log('waht about response', response.data);
                        // this.closeModal(response.data.data);
                        this.importedData.splice(index, 1);

                        this.open_loader = false;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })
            }
        },
        //---remove data--
        removeImportedData() {
            if (this.importedData.length !== 0 && this.deleteIndex !== null) {
                // Remove the saved record from the importedData
                this.importedData.splice(this.deleteIndex, 1);
                if (this.importedData.length === 0) {
                    this.importedData = [];
                    document.getElementById('fileInput').value = '';
                }
                // this.isDisplayed = false;
                this.validateAndHighlight();
            } else {
                this.importedData = [];
                document.getElementById('fileInput').value = '';
            }
            this.deleteIndex = null;
            this.open_confirmBox = false;
            this.reset_All = false;
        },
        //---close alert--
        closeAlert() {
            this.open_message = false;
        },
        //---save all button function---
        validateAndClose() {
            if (this.importedData.length > 0 && this.invalidRows.length === 0) {
                this.isDisplayed = false;
                this.open_loader = true;
                // Proceed with the data import if validation passes
                axios.post('/customer-imports', { customers: this.importedData, company_id: this.companyId, user_id: this.userId })
                    .then(response => {
                        this.open_loader = false;
                        this.exist_contact = response.data.data;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;

                        if (Array.isArray(this.exist_contact) && this.exist_contact.length > 0) {
                            this.importedData = this.importedData.filter(opt => this.exist_contact.includes(opt.contact_number));
                        } else {
                            this.importedData = [];
                            document.getElementById('fileInput').value = '';
                        }
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    });
            } else {
                this.isDisplayed = false;
                this.validateAndHighlight();
            }
        },

        //---delete the record

        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.deleteIndex = null;
            this.open_confirmBox = false;
            this.reset_All = false;
        },
        //---basic data load-----
        getInitialListData() {
            //---category list---
            this.getCustomerData();
            //---get state list---
            this.getStateList();

        },


        preventNegativeValue(event) {
            if (event.target.value < 0) {
                event.target.value = 0; // Prevents negative input
            }
        },
        //---filter data---
        getCategoryId(inputCategoryName) {
            if (this.category_list && Array.isArray(this.category_list) && this.category_list.length > 0) {
                // Normalize the inputCategoryName to lowercase
                const normalizedInput = inputCategoryName.toLowerCase();

                // Find the matching category in the category_list
                const category = this.category_list.find(item => item.category_name.toLowerCase() === normalizedInput);

                // If a category is found, return its ID; otherwise, return null or an appropriate response
                if (category) {
                    return category.id;
                } else {
                    return null; // or return 'Category not found'
                }
            }
        },
        //---reset---
        resetAllData() {
            if (this.importedData && Array.isArray(this.importedData) && this.importedData.length > 0) {
                this.reset_All = true;
                this.open_confirmBox = true;
                this.isDisplayed = false;
                this.invalidRows = [];
                this.focusedInvalidIndex = null;
                this.exist_contact = [];
            }
        },
        //---display available options---
        openAvailableOptions() {
            this.show_available = true;
        },
        closeAvailableOptions() {
            this.show_available = false;
        },
        //--add customer category--
        openModalService() {
            this.showModal_add_service = true;
        },
        closeModalService(data) {
            // console.log(data, 'What happening the data...!');
            if (data) {
                this.serviceCategories.push(data);
                // this.customer_category = data.id;
                // this.formValues.customer_category = data.id;
                this.showModal_add_service = false;
            } else {
                this.showModal_add_service = false;
            }
        },
        //--get customer category---
        getCustomerData() {
            axios.get('/customer-categories', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data);
                    this.serviceCategories = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        //---convert to date method---
        formatDate(field) {
            // Get the value of the input field
            let dateValue = this.formValues[field];

            // Check if the date value is not empty
            if (dateValue) {
                // Convert the date value to 'yyyy-mm-dd' format
                let formattedDate = new Date(dateValue).toISOString().slice(0, 10);

                // Update the formValues with the formatted date
                this.formValues[field] = formattedDate;
            }
        },
        isValidDate(dateString) {
            // Attempt to create a Date object from the provided string
            const date = new Date(dateString);
            return !isNaN(date.getTime()) && date.getFullYear() >= 100;
        },
        //---edit existing customer--
        formatDateInitial(date) {
            if (this.isValidDate(date)) {
                // console.log(date, 'datatatatatatat');
                const d = new Date(date);
                const month = String(d.getMonth() + 1).padStart(2, '0');
                const day = String(d.getDate()).padStart(2, '0');
                const year = d.getFullYear();
                // console.log(`${month}/${day}/${year}`, 'RRRRRRRRRRRRRRR');
                return `${year}-${month}-${day}`;
            }
        },
        //--validate GST--
        validateGST() {
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.formValues.gst_number !== '') {
                // Check if the input matches the expected format
                if (regex.test(this.formValues.gst_number)) {
                    this.gstValidation = '';
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                }
            }
        },
        //--get state list ----
        getStateList() {
            if (this.state_list.length === 0) {
                axios.post('https://www.nammav2app.in/api/state-list', { country_id: 101 })
                    .then(response => {
                        // console.log(response.data, 'state');
                        this.state_list = response.data;
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }
        },
        async getDistrictList(name, index) {
            // console.log(name, 'state name............');
            if (this.state_list && Array.isArray(this.state_list) && this.state_list.length > 0 && name) {
                const find_id = await this.selectedStateId(name);
                // console.log(find_id, 'RRRRRRRRRRRRRR', index);
                if (find_id) {
                    await axios.get('https://www.nammav2app.in/api/get-district', { params: { state_id: find_id } })
                        .then(response => {
                            // console.log(response.data, 'RRRRRRRRRRRRRRRR district');
                            this.district_list[index] = response.data;
                            // console.log(this.district_list[index]);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.district_list[index] = [];
                        })
                }
                // else {
                //     this.district_list[index] = [];
                // }
            }
            // else {
            //     this.district_list[index] = [];
            // }

        },
        async getCityList(name, index) {
            if (index >= 0 && name) {
                // console.log(this.district_list[index], 'TTTTTTTTTTTTTTTT happening....');
                if (this.district_list[index] && Array.isArray(this.district_list[index]) && this.district_list[index].length > 0 && name) {
                    const find_id = await this.selectedDistrictId(this.district_list[index], name);
                    // console.log(find_id, 'TWETETETE happpppppp');

                    if (find_id) {
                        await axios.post('https://www.nammav2app.in/api/getcity-lists', { district_id: find_id })
                            .then(response => {
                                // console.log(response.data, 'CCCCCCCCCCCC city list');
                                this.city_list[index] = response.data;
                            })
                            .catch(error => {
                                console.error('Error', error);
                            })
                    }
                    // else {
                    //     this.city_list[index] = [];
                    // }
                }
                // else {
                //     this.city_list[index] = [];
                // }
            }
            // else {
            //     this.city_list[index] = [];
            // }

        },
        //---business----
        selectedStateId(name) {
            // Find the district object with the selected name and return its ID
            const selectedState = this.state_list.find(state => state.name === name);
            return selectedState ? selectedState.id : null;
        },
        selectedDistrictId(list, name) {
            // Find the district object with the selected name and return its ID
            const selectedDistrict = list.find(district => district.name === name);
            return selectedDistrict ? selectedDistrict.id : null;
        },
        //---formated date---
        formatDateToISO(dateString) {
            // Return empty if dateString is empty or null
            if (!dateString || typeof dateString !== 'string') return '';

            // Replace "/" with "-" to standardize the format
            dateString = dateString.replace(/\//g, '-');

            // Parse the "DD-MM-YYYY" format
            const [day, month, year] = dateString.split('-').map(Number);
            if (!day || !month || !year) {
                return ''; // Return empty string if the date parts are invalid
            }

            // Create a new date object (month is 0-indexed in JS, so we subtract 1)
            const parsedDate = new Date(year, month - 1, day);

            // Check if the date is valid
            if (isNaN(parsedDate.getTime())) {
                return ''; // Return empty string if the date is invalid
            }

            // Extract the components to form "YYYY-MM-DD"
            const yyyy = parsedDate.getFullYear();
            const mm = String(parsedDate.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed, so +1
            const dd = String(parsedDate.getDate()).padStart(2, '0');

            return `${yyyy}-${mm}-${dd}`; // Return in "YYYY-MM-DD" format
        },
        // Function to remove duplicate contacts from importedData
        removeExistContacts() {
            if (this.exist_contact && this.exist_contact.length > 0) {
                // Filter out contacts that already exist in 'exist_contact'
                this.importedData = [];
                this.exist_contact = [];
                document.getElementById('fileInput').value = '';
                this.message = 'Duplicate contacts have been removed.';
                this.type_toaster = 'success';
                this.show = true;
            }
        },

        // Function to save the data anyway, even with duplicates
        saveAnyway() {
            // You can save all importedData regardless of duplicates
            if (this.importedData.length > 0 && Array.isArray(this.invalidRows) && this.invalidRows.length === 0) {
                axios.post('/customer-imports', { customers: this.importedData, company_id: this.companyId, user_id: this.userId, skip: 1 })
                    .then(response => {
                        this.importedData = [];
                        this.exist_contact = [];
                        document.getElementById('fileInput').value = '';
                        this.message = 'Data saved, including duplicates.';
                        this.type_toaster = 'success';
                        this.show = true;
                        // Optionally, handle any additional post-save logic here
                    })
                    .catch(error => {
                        this.message = error.response.data.message || error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                    });
            } else {
                this.isDisplayed = false;
                this.validateAndHighlight();
            }
        },
        //--heightlight in invalid data row--
        // Method to validate imported data
        validateAndHighlight() {
            this.invalidRows = [];

            // Regular expression to validate contact number with digits, +, -, and spaces
            const contactNumberPattern = /^[0-9+\- ]+$/;

            // Check for invalid first_name or contact_number
            this.importedData.forEach((data, index) => {
                // Validate first_name and contact_number
                if (!data.first_name || data.first_name === '') {
                    this.invalidRows.push(index); // Add invalid index to array
                }

                if (!data.contact_number || data.contact_number === '' || !contactNumberPattern.test(data.contact_number)) {
                    this.invalidRows.push(index); // Add invalid index to array
                }
            });

            // Set focus on the first invalid row if any
            if (this.invalidRows.length > 0) {
                if (!this.isDisplayed) {
                    this.isDisplayed = true;
                    this.focusedInvalidIndex = this.invalidRows[0];
                    this.focusOnInvalidRow();
                    this.message = 'Some rows have invalid First Name or Contact Number. Please review and update the highlighted fields.';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            }
        },
        // Method to handle Enter key press and move to next invalid row
        focusNextInvalidRow(currentIndex) {
            // Find the index of the next invalid row
            const nextInvalidIndex = this.invalidRows.find((index) => index > currentIndex);

            // If there's a next invalid row, focus on it, otherwise focus the next row
            if (nextInvalidIndex !== undefined) {
                this.focusedInvalidIndex = nextInvalidIndex;
                this.focusOnInvalidRow();
            } else {
                // If no next invalid row, focus the next row in the table
                const nextRow = currentIndex + 1;
                this.focusedInvalidIndex = nextRow;
                if (nextRow < this.importedData.length) {
                    this.focusOnInvalidRow();
                }
            }
        },
        focusOnInvalidRow() {
            if (this.focusedInvalidIndex !== null) {
                // Make sure that the row is fully rendered before interacting with it
                this.$nextTick(() => {
                    const row = this.$refs[`invalidRow${this.focusedInvalidIndex}`]; // Ensure the ref is correct

                    // Check if the row exists before trying to call scrollIntoView
                    if (Array.isArray(row) && row.length > 0) {
                        // Scroll the row into view smoothly
                        row[0].scrollIntoView({ behavior: 'smooth', block: 'center' });

                        // Wait a bit before focusing to ensure scroll happens first
                        setTimeout(() => {
                            const inputField = row[0].querySelector('input'); // Query for input field
                            if (inputField) {
                                inputField.focus(); // Focus on the input element
                            } else {
                                console.warn('No input field found in the row.');
                            }
                        }, 300); // Adjust delay as needed to ensure smooth scrolling happens first
                    } else {
                        console.error('Row not found with ref: ', `invalidRow${this.focusedInvalidIndex}`);
                    }
                });
            }
        },
        //--vlaidate in mobile number fields--
        validateInputValue(key, value) {
            if (key === 'contact_number' && value && value.length > 1) {
                // Regular expression to validate contact number with digits, +, -, and spaces
                const contactNumberPattern = /^[0-9+\- ]+$/;
                if (!contactNumberPattern.test(value)) {
                    this.message = 'Please enter a valid contact number (numbers, +, - and spaces allowed)';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            }
        },
        //--add new customer---
        addNewCustomer(index) {
            if (index > 0) {
                let obj_data = { first_name: '', last_name: '', contact_number: '', alternative_number: '', email: '', opening_balance: 0, birth_date: '', anniversary_date: '', bussiness_name: '', gst_number: '', address: '', pincode: '', state_name: '', district_name: '', city_name: '', customer_category: '', notes: '' };
                this.importedData.push(obj_data);
            }
        },
    },
    mounted() {

    },
    watch: {
        isOpen(newValue) {
            if (newValue) {
                this.getInitialListData();
            }
            setTimeout(() => {
                this.isOpenModal = newValue;
            }, 100);
        },
        // Watch for changes in importedData to revalidate
        importedData: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.validateAndHighlight();
                }
            }
        }
    }
};
</script>

<style scoped>
/* Add your custom styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.3s;
}

.highlight-row {
    background-color: rgba(255, 0, 0, 0.2);
    /* Light red background for invalid rows */
}
</style>