import { h, createApp } from 'vue';
import DatePicker from './DatePicker.vue'; // Import your DatePicker component
import { nextTick } from 'vue';

const DatepickerDirective = {
  mounted(el) {
    // Ensure that we access min/max only after the DOM has been updated
    nextTick(() => {
      // Check if the element is of type 'date', 'datetime-local', or 'month'
      if (['date', 'datetime-local', 'month', 'time', 'week'].includes(el.type)) {
        const wrapper = document.createElement('div');
        wrapper.classList.add('datepicker-wrapper');
        
        // Retrieve the min and max values from the input element
        const min = el.min || null;
        const max = el.max || null; 
         // Set up Flatpickr configuration based on the selected input type
      const options = {
        enableTime: false, // Default to false unless type is 'datetime-local' or 'time'
        dateFormat: "d-m-Y", // Date format for 'date' and 'month'
        time_24hr: true,  // Time format in 24hr style for datetime-local and time
        altInput: true,   // Use an input field with an alternative input for formatting
      };

      switch (el.type) {
        case 'date':
          options.enableTime = false;
          options.dateFormat = "d-m-Y";
          break;
        case 'datetime-local':
          options.enableTime = true;
          options.dateFormat = "d-m-Y H:i";
          break;
        case 'month':
          options.enableTime = false;
          options.dateFormat = "Y-m";
          break;
        case 'time':
          options.enableTime = true;
          options.dateFormat = "H:i";
          break;
        case 'week':
          options.enableTime = false;
          options.dateFormat = "Y-W";
          break;
        default:
          break;
        };
        
        // Create Vue component and attach it to the DOM
        const app = createApp({
          render() {
            return h(DatePicker, {
              modelValue: el.value,
              'onUpdate:modelValue': (newValue) => {               
                // Handle custom week format for "Y-'w'" format
                if (options.dateFormat == "Y-W") {
                  const weekYear = newValue.split('-')[0]; // Extract Year
                  const weekNumber = newValue.split('-')[1]; // Extract Week Number
                  const formattedValue = `${weekYear}-W${weekNumber}`; // Format as "Y-'w'week number"
                  el.value = formattedValue;  // Update the input value with formatted week
                } else {                  
                  if (options.dateFormat == "d-m-Y" || options.dateFormat == "d-m-Y H:i") {
                    // Manually parse newValue if it is in d-m-Y or d-m-Y H:i format
                    if (newValue) {
                      const parts = newValue.split(/[- :]/); // Split the date-time string by - and space (for d-m-Y and d-m-Y H:i)
                      let formattedValue = '';
        
                      if (parts.length === 3) { // d-m-Y
                        const day = parts[0];
                        const month = parts[1];
                        const year = parts[2];
                        formattedValue = `${year}-${month}-${day}`; // Format as YYYY-MM-DD
                      } else if (parts.length === 5) { // d-m-Y H:i
                        const day = parts[0];
                        const month = parts[1];
                        const year = parts[2];
                        const hours = parts[3];
                        const minutes = parts[4];
                        formattedValue = `${year}-${month}-${day}T${hours}:${minutes}`; // Format as YYYY-MM-DDTHH:MM
                      }
        
                      // Ensure valid date object after formatting
                      const date = new Date(formattedValue);
                      if (!isNaN(date.getTime())) {
                        el.value = formattedValue; // Only set value if valid date
                      } else {
                        console.error('Invalid date value:', formattedValue);
                      }
                    }
                  } else {
                    el.value = newValue; // For other types, just update the input value
                  }
                }
                el.dispatchEvent(new Event('input'));  // Dispatch input event to trigger v-model update
              },
              min: min,
              max: max,
              options: options,
            });
          }
        });
        
  
        // Mount Vue component to the newly created wrapper
        app.mount(wrapper);
        el.parentNode.replaceChild(wrapper, el);  // Replace the input field with the component
      }
    });
  }
};

export default DatepickerDirective;
