// store/modules/customer.js
import axios from "axios";

const state = {
  employee: [],
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  };

  const mutations = {
    SET_EMPLOYEE(state, employeeData) {
      state.employee = employeeData;
    },
    RESET_STATE(state) {
      state.employee = [];      
      state.lastFetchTime = null;
      state.isFetching = false;
  },
  SET_LAST_FETCH_TIME(state, time) {
    state.lastFetchTime = time; // Save the timestamp when the API was last accessed
  },
  SET_IS_FETCHING(state, status) {
    state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
  }
  };

  const actions = {
    updateEmployeeName({ commit }, employeeData) {
      // Simulate an asynchronous operation (e.g., API call) to update employee name
      setTimeout(() => {
        // Commit mutation to update employee name
        commit('SET_EMPLOYEE', employeeData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchEmployeeList({ commit, state, rootState }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['employee_update']; 
  
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)  { 
        return; // Skip request if less than 30 seconds have passed since the last request
      }
     
      try {
        const { company_id, user_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
           // Set the request status to true (indicating that the request is in progress)
           commit('SET_IS_FETCHING', true);
          axios.get('/employees', { params: { company_id: company_id, page: 1, per_page: 'all' } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Employee list..!');
              let employee_list = response.data.data;              
                // employee_list.unshift({ id: user_id, name: 'Assign to me' });

            if (employee_list.length > 2) {
                // Sorting the array alphabetically based on the 'name' key
                employee_list.sort((a, b) => {
                    // Convert both names to lowercase to ensure case-insensitive sorting
                    const nameA = a.name.toLowerCase();
                    const nameB = b.name.toLowerCase();
                    // Compare the two names
                    if (nameA < nameB) {
                        return -1;
                    }
                    if (nameA > nameB) {
                        return 1;
                    }
                    return 0;
                });
            }
              commit('SET_EMPLOYEE', employee_list);
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return employee_list;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },    
  };

  const getters = {
    currentEmployee(state) {
      return state.employee;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
