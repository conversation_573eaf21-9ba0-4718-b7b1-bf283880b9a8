<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-display relative"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">

            <div class="flex justify-between items-center relative w-full px-4 py-3 set-header-background">
                <p for="leadFilter" class="text-white font-bold text-center text-xl flex justify-end ml-3">
                    Filter
                </p>
                <p class="close" @click="closeModal">&times;</p>
            </div>
            <div class="block mt-1 text-sm bg pl-4 pr-4 pb-4">
                <div v-if="selectedByValue === 'Custom'" class="text-xs text-green-600">
                    <p>Note: At least fill in any one field..!</p>
                </div>
                <!---from to date-->
                <div class="block text-sm mt-7 relative">
                    <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'" class="flex justify-end">
                        <button @click="resetTheValues(['from', 'to'])"
                            class="absolute text-xs -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>
                    <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'"
                        class="grid grid-cols-2 gap-4">
                        <!-- From Date -->
                        <div class="">
                            <label for="fromDate"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.from || isInputFocused.from, 'text-blue-700': isInputFocused.from }">
                                From Date
                            </label>
                            <!---@input="validateDates"-->
                            <input id="fromDate" v-model="formValues.from" type="date" v-datepicker placeholder=" "
                                :max="formValues.to" @change="updateMinToDate"
                                class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                        </div>

                        <!-- To Date -->
                        <div class="relative">
                            <label for="toDate"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.to || isInputFocused.to, 'text-blue-700': isInputFocused.to }">
                                To Date
                            </label>
                            <!---@input="validateDates"-->
                            <input id="toDate" v-model="formValues.to" type="date" v-datepicker placeholder=" "
                                :min="formValues.from" :max="maxDate"
                                class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                        </div>
                    </div>
                </div>

                <!--type-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by category/Type'"
                    class="items-center mt-5">
                    <div v-if="formValues.type" class="flex justify-end ">
                        <button @click="resetTheValue('type')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" size="sm" style="color: #ec2727;" /></button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="type"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': isInputFocused.type, 'text-blue-700': isInputFocused.type }">
                            Type
                        </label>
                        <input id="toDate" v-model="formValues.type" type="text" placeholder="Enter enquiry type"
                            :min="formValues.from" :max="maxDate"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                    </div>
                </div>
                <!-- Buttons -->
                <div class="flex justify-center items-center m-2 mt-5">
                    <button @click="closeModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  px-4 mr-8 py-2">Cancel</button>
                    <button v-if="showResetButton && selectedByValue === 'Custom'" @click="resetAll"
                        class="bg-blue-700 hover:bg-blue-600 rounded-[30px] text-white px-4 mr-8 py-2">Reset
                        All</button>
                    <button @click="submitFilter"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white px-4 mr-8 py-2">Submit</button>
                </div>
            </div>
        </div>
        <dialogAlert :showModal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>


<script>
import dialogAlert from '../dialogAlert.vue';
export default {
    emits: ['closeFilter'],
    props: {
        showModal: Boolean,
        selectedByValue: String
    },
    components: {
        dialogAlert
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            isInputFocused: { date: true, type: true, },
            //---api integration---
            companyId: null,
            userId: null,
            open_message: false,
            message: '',
            open_loader: false,
            maxDate: new Date().toISOString().split('T')[0], // current date
            minDate: '',
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter');
            }, 300);
        },
        //--submit---
        submitFilter() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter', this.formValues);
                this.formValues = {};
            }, 300);
        },

        //---reset All---
        resetAll() {
            this.formValues = {};
        },
        //---reset---
        resetTheValue(key) {
            delete this.formValues[key];
        },
        resetTheValues(fields) {
            // Reset specified form fields
            fields.forEach(field => {
                this.formValues[field] = null;
            });
        },

        closeMessage() {
            this.message = '';
            this.open_message = false;
        },
        validateDates() {
            if (this.formValues.from && this.formValues.to) {
                const fromDate = new Date(this.formValues.from);
                const toDate = new Date(this.formValues.to);

                if (toDate < fromDate) {
                    // Reset 'to' date and show error message (you can handle this according to your UI)
                    this.formValues.to = null;
                    this.message = 'To Date cannot be earlier than From Date';
                    this.open_message = true;
                }
            }
        },
        //--update minimum date
        updateMinToDate() {
            this.minDate = this.formValues.from;
        }
    },
    watch: {
        showModal(newValue) {

            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);

            if (this.selectedByValue == 'by Date') {
                // Get current date in YYYY-MM-DD format
                const currentDate = new Date().toISOString().split('T')[0];
                // Set default values for from and to in formValues
                this.formValues.from = currentDate;
                this.formValues.to = currentDate;
            }
        },
    },
    computed: {
        showResetButton() {
            // Check if formValues is not empty
            return Object.keys(this.formValues).length > 0;
        }
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>
