<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    Set Discount
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block text-sm bg p-4">
                <div class="bg-gray-300 px-3 py-5 rounded mb-5 shadow-md"
                    style="box-shadow: 3px 4px 6px 3px rgba(150, 63, 59, 0.3)">
                    <div class="grid grid-cols-1 sm:grid-cols-2 gap-6 mb-3">
                        <!--discount type-->
                        <div class="flex w-full mr-2 relative">
                            <label for="discount_type"
                                class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.type !== undefined && formValues.type) || isInputFocused.discount_type, 'text-blue-700': isInputFocused.discount_type }">
                                Discount Type</label>
                            <select id="discount_type" v-model="formValues.type"
                                class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                                @focus="isInputFocused.discount_type = true"
                                @blur="isInputFocused.discount_type = false">
                                <!-- <option value="">select type</option> -->
                                <option value="Percentage">Percentage (%)</option>
                                <option value="Fixed">Fixed ({{ currentCompanyList && currentCompanyList.currency ===
                                    'INR' ? '\u20b9' : currentCompanyList.currency }})</option>
                            </select>
                        </div>
                        <!--discount value-->
                        <div class="flex w-full mr-2 relative">
                            <label for="discount_value"
                                class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                                @click="isInputFocused.discount_value = true"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.value !== undefined && formValues.value >= 0) || isInputFocused.discount_value, 'text-blue-700': isInputFocused.discount_value }">
                                Value</label>
                            <input v-model="formValues.value"
                                class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                                @input="isValidateDiscount()" @focus="isInputFocused.value = true"
                                @blur="isInputFocused.value = false" />
                        </div>
                    </div>
                </div>
                <p v-if="validation_message !== ''" class="text-red-500 font-bold text-sm py-1">{{ validation_message }}
                </p>

                <!--buttons-->
                <div class="flex justify-center items-center">
                    <button class="border rounded text-white bg-red-700 font-bold px-4 py-2 hover:bg-red-600 mr-4"
                        @click="closeModal">Cancel</button>
                    <button class="border rounded text-white bg-green-700 font-bold px-4 py-2 hover:bg-green-600"
                        @click="saveModel">Save</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        over_all_discount: Object,
        grandTotal: Number,
        currentCompanyList: Object,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            isInputFocused: {},
            validation_message: ''
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
                this.validation_message
            }, 300);
        },
        saveModel() {
            if (this.formValues.type !== '' && this.formValues.value >= 0 && this.validation_message === '') {
                setTimeout(() => {
                    this.$emit('close-modal', this.formValues);
                    this.validation_message = '';
                }, 300);
            } else {
                this.validation_message = this.validation_message === '' ? 'Please fill data..!' : this.validation_message;
            }
        },
        isValidateDiscount() {
            if (this.formValues.type !== '' && this.formValues.value) {
                if (this.grandTotal && this.grandTotal > 1) {
                    if (this.formValues.type === 'Fixed') {
                        if (this.formValues.value <= (1 * this.grandTotal)) {
                        } else {
                            this.validation_message = 'Please enter a discount value that is less than or equal to the total value.';
                        }
                    } else if (this.formValues.type === 'Percentage') {
                        if (this.formValues.value <= 100) {
                        } else {
                            this.validation_message = 'Please enter a discount value that is less than or equal to the total value.';
                        }
                    }
                } else if (this.formValues.value !== 0 && this.formValues.value > 0) {
                    this.validation_message = 'Please enter a discount value that is less than or equal to the total value.';
                }
            }
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;

            }, 100);
        },
        formValues: {
            deep: true,
            handler(newValue) {
                this.validation_message = '';
            }
        },
        over_all_discount: {
            deep: true,
            handler(newValue) {
                this.formValues = newValue;
            }
        }
    },
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>