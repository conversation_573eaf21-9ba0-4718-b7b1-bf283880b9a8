import axios from "axios";

const state = {
  service_list: [],
  status_counts: [],
  pagination: null,
  };
  const mutations = {
    SET_SERVICELIST(state, { data, pagination, status_counts }) {  
      const currentData = state.service_list.data || [];
          if (pagination && pagination.current_page > 1) {
              // Append new data if the current page is greater than 1
              state.service_list = {
                  data: [...currentData, ...data], // Append new data
                  pagination: pagination,
                  status_counts: status_counts,
              };
          } else {
              // Replace data if it's the first page or no pagination
              state.service_list = {
                  data: data, // Replace with new data
                  pagination: pagination,
                  status_counts: status_counts,
              };
          }
      // let findIndex = state.service_list.findIndex(opt => opt.category === id);
      // // console.log(findIndex, 'Waht is INDEX', status_counts);
      
      // if (findIndex !== -1) {
      //     //----this condition is not satishfied ????----
      //     if (pagination.current_page > state.service_list[findIndex].pagination.current_page) {
      //       state.service_list[findIndex].data = [...state.service_list[findIndex].data, ...data];
      //       state.service_list[findIndex].pagination = pagination;
      //     } else {
      //       if (pagination.current_page == 1) {
      //         state.service_list[findIndex].data = data;
      //         state.service_list[findIndex].pagination = pagination;
      //       } else {
      //         state.service_list[findIndex].data = [data.splice(((pagination.current_page) * (state.service_list[findIndex].pagination.per_page) - 1), pagination.per_page)];
      //         state.service_list[findIndex].pagination = pagination;             
      //      }            
      //     }
      //     if (status_counts && Object.keys(status_counts).length > 0) {
      //       state.status_counts = status_counts;           
      //     }else if(status_counts.length === 0){
      //       state.status_counts = status_counts;             
      //     }  
      //   } else {
      //   state.service_list.push({ category: id, data: data, pagination: pagination });        
      //     if (status_counts && Object.keys(status_counts).length > 0) {
      //       state.status_counts = status_counts;           
      //     } else if(status_counts.length === 0){
      //       state.status_counts = status_counts;             
      //     }          
      // } 
      
    },
      RESET_STATE(state) {
        state.service_list = [];
        state.status_counts = [];        
      },
  };
  const actions = {
    updateServiceName({ commit }, data) {
      // Simulate an asynchronous operation (e.g., API call) to update service_list name
      setTimeout(() => {
        // Commit mutation to update service_list name
        commit('SET_SERVICELIST', data);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchServiceList({ commit }, {id,  page, per_page}) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          let send_data = { company_id:  company_id, page: page, per_page: per_page };
          const numberPattern = /^[0-9]+$/;

          if (id !== 'all' && numberPattern.test(id)) {
              send_data.category_id = id;
          }

          axios.get(`/services`, { params: { ...send_data } })
            .then(response => {          
                // console.log(response.data, 'EWEWEWE service data.....!!!');
                
                const { data, pagination, status_counts } = response.data;               
              commit('SET_SERVICELIST',{data, pagination, status_counts, id});
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },    
  };

  const getters = {
    currentServiceList(state) {
      return { list: state.service_list, status_counts: state.status_counts };
    },   
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };