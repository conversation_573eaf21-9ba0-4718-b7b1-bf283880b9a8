<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="justify-between items-center flex py-2 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-lg py-2">
                    {{ type == 'category' ? 'Category List' : type == 'brand' ? 'Brand List' : 'Unit List' }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>

            <!-- Form for CRUD operations -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-4 m-4">
                <form @submit.prevent="submitForm" class="text-sm text-center">
                    <input type="text" v-model="categoryName"
                        :placeholder="type == 'category' ? 'Category Name' : type == 'brand' ? 'Brand Name' : 'Unit Name'"
                        ref="inputName" class="block mb-3 border px-3 py-2 w-full">
                    <button type="submit"
                        class=" rounded rounded-md px-3 py-2 mt-3 bg-green-700 hover:bg-green-600 text-white">
                        {{ updateIndex === null ? 'Create' : 'Update' }}</button>
                    <button class=" rounded rounded-md px-3 py-2 mt-3 bg-red-700 hover:bg-red-600 text-white ml-5"
                        @click="cancelModal">Close</button>
                </form>
            </div>

            <!-- Display categories -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-3 m-4 text-sm"
                :key="categories.length">
                <p class="font-bold underline mb-2">List:</p>
                <ul>
                    <li v-for="(category, index) in categories" :key="index" class="flex justify-between">
                        <div>{{ type === 'category' ? category.category_name : type === 'brand' ? category.brand_name :
                            category.unit_name }}</div>
                        <div class="flex justify-between">
                            <button @click="editCategory(index)">
                                <img :src="table_edit" alt="table-edit" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                            <button @click="deleteCategory(index)">
                                <img :src="table_del" alt="table-delete" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import confirmbox from './confirmbox.vue';
import dialogAlert from './dialogAlert.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
    name: 'categoryBrandUnit',
    components: {
        confirmbox,
        dialogAlert
    },
    props: {
        showModal: Boolean,
        type: String,
        categoriesData: Object,
    },
    data() {
        return {
            isMobile: false,
            formValues: {},
            isOpen: false,
            categoryName: '',
            categories: [],
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            deleteIndex: null,
            open_confirmBox: false,
            updateIndex: null,
            isMessageDialogVisible: false,
            message: '',
            //--api integration---
            companyId: null,
            userId: null,
            //--toaster----
            show: false,
            type_toaster: 'warning'

        };
    },
    methods: {
        ...mapActions('brandUnitCategoryItem', ['fetchBrandList', 'fetchUnitList', 'fetchCategoryList']),
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeModal', this.categories);
                this.categories = [];
            }, 300);

        },
        submitForm() {
            // console.log(!this.categories.some((data) => this.type === 'category' ? data.category_name.toLowerCase() : this.type === 'brand' ? data.brand_name.toLowerCase() : data.unit_name.toLowerCase().includes(this.categoryName.toLowerCase())), 'What is the index valuye...!');
            if (this.categoryName !== '' && this.categoryName && this.updateIndex === null && !this.validateDuplicate()) {
                // console.log(this.categories);
                // Create category
                if (this.type === 'category') {
                    axios.post('/categories', { category_name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data);
                            this.categories.push(response.data.data);
                            this.fetchCategoryList();
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                } else if (this.type === 'brand') {
                    axios.post('/brands', { brand_name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data);
                            this.categories.push(response.data.data);
                            this.fetchBrandList();
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                }
                else {
                    axios.post('/units', { unit_name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data);
                            this.categories.push(response.data.data);
                            this.fetchUnitList();
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                }
                // this.saveToLocalStorage();
                this.categoryName = ''; // Clear input
            } else if (this.updateIndex !== null && this.categoryName !== '') {
                // && this.type === 'category' ? this.categories[this.updateIndex].category_name : this.type === 'brand' ? this.categories[this.updateIndex].brand_name : this.type === 'unit' && this.categories[this.updateIndex].unit_name
                if (this.type === 'category') {
                    axios.put(`/categories/${this.categories[this.updateIndex].id}`, { category_name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data, 'hello', this.updateIndex);
                            this.categories[this.updateIndex] = response.data.data;
                            this.updateIndex = null;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });

                } else if (this.type === 'brand') {
                    axios.put(`/brands/${this.categories[this.updateIndex].id}`, { brand_name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data);
                            this.categories[this.updateIndex] = response.data.data;
                            this.updateIndex = null;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                }
                else {
                    axios.put(`/units/${this.categories[this.updateIndex].id}`, { unit_name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data);
                            this.categories[this.updateIndex] = response.data.data;
                            this.updateIndex = null;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                }
                this.categoryName = '';

            } else if (this.categoryName) {
                this.isMessageDialogVisible = true;
                this.message = `${this.categoryName} already exist..!`;
            }
        },
        editCategory(index) {
            // Edit category (not implemented in this example)
            console.log('Edit category:', index);
            this.categoryName = this.type === 'category' ? this.categories[index].category_name : this.type === 'brand' ? this.categories[index].brand_name : this.categories[index].unit_name;
            this.updateIndex = index;
            // console.log(index, 'WWWWWW');
        },
        deleteCategory(index) {
            // Delete category
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },

        loadFromLocalStorage() {
            // console.log(this.type, 'What happening...!');
            let storedCategories = [];
            if (this.categories.length === 0) {
                if (this.type === 'category') {
                    //---lead type---
                    axios.get('/categories', {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            // Handle response
                            console.log(response.data.data);
                            storedCategories = response.data.data;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });

                } else if (this.type === 'brand') {
                    // Make GET request with parameter company_id=1
                    axios.get('/brands', {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            // Handle response
                            console.log(response.data.data);
                            storedCategories = response.data.data;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                            this.message = error.response.data.message;
                            this.show = true;
                        });
                }
                else {
                    // Make GET request with parameter company_id=1
                    axios.get('/units', {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            // Handle response
                            console.log(response.data.data);
                            storedCategories = response.data.data;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                }
                if (storedCategories.length !== 0) {
                    // console.log(storedCategories, 'WERWRWRW');
                    this.categories = storedCategories;
                }
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleFocus() {
            // Set focus to the input field 
            const inputField = this.$refs.inputName;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---confirm box funxctions
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                // console.log(this.deleteIndex, 'Whatjjjjjj');
                if (this.type === 'category') {
                    axios.delete(`/categories/${this.categories[this.deleteIndex].id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            console.log(response.data);
                            this.categories.splice(this.deleteIndex, 1);
                        })
                        .catch(error => {
                            console.error(error);
                        });
                } else if (this.type === 'brand') {
                    axios.delete(`/brands/${this.categories[this.deleteIndex].id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            console.log(response.data);
                            this.categories.splice(this.deleteIndex, 1);
                        })
                        .catch(error => {
                            console.error(error);
                        });
                } else {
                    axios.delete(`/units/${this.categories[this.deleteIndex].id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            console.log(response.data);
                            this.categories.splice(this.deleteIndex, 1);
                        })
                        .catch(error => {
                            console.error(error);
                        });
                }

                // this.saveToLocalStorage();
                this.open_confirmBox = false;
            }
        },

        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---close message--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
        },
        //---validation dupli cate ---
        validateDuplicate() {
            // console.log(this.updateIndex, 'EEEEE');
            if (this.type === 'category' && this.updateIndex === null) {
                return this.categories.some((data) => data.category_name.toLowerCase() === this.categoryName.toLowerCase());
            }
            else if (this.type === 'brand' && this.updateIndex === null) {
                return this.categories.some((data) => data.brand_name.toLowerCase() === this.categoryName.toLowerCase());
            }
            else if (this.updateIndex === null) {
                return this.categories.some((data) => data.unit_name.toLowerCase() === this.categoryName.toLowerCase());
            } else {
                return false;
            }
        }
    },

    mounted() {
        this.updateIsMobile();
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                    this.categories = this.categoriesData;
                }
            }, 100);
        },
    },
};
</script>

<style>
/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.close {
    color: white;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: red;
    text-decoration: none;
    cursor: pointer;
}
</style>