<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateRmaAdditionalProductsAPIRequest;
use App\Http\Requests\API\UpdateRmaAdditionalProductsAPIRequest;
use App\Models\RmaAdditionalProducts;
use App\Repositories\RmaAdditionalProductsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class RmaAdditionalProductsController
 * @package App\Http\Controllers\API
 */

class RmaAdditionalProductsAPIController extends AppBaseController
{
    /** @var  RmaAdditionalProductsRepository */
    private $rmaAdditionalProductsRepository;

    public function __construct(RmaAdditionalProductsRepository $rmaAdditionalProductsRepo)
    {
        $this->rmaAdditionalProductsRepository = $rmaAdditionalProductsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/rmaAdditionalProducts",
     *      summary="getRmaAdditionalProductsList",
     *      tags={"RmaAdditionalProducts"},
     *      description="Get all RmaAdditionalProducts",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/RmaAdditionalProducts")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $rmaAdditionalProducts = $this->rmaAdditionalProductsRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($rmaAdditionalProducts->toArray(), 'Rma Additional Products retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/rmaAdditionalProducts",
     *      summary="createRmaAdditionalProducts",
     *      tags={"RmaAdditionalProducts"},
     *      description="Create RmaAdditionalProducts",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaAdditionalProducts"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateRmaAdditionalProductsAPIRequest $request)
    {
        $input = $request->all();

        $rmaAdditionalProducts = $this->rmaAdditionalProductsRepository->create($input);

        return $this->sendResponse($rmaAdditionalProducts->toArray(), 'Rma Additional Products saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/rmaAdditionalProducts/{id}",
     *      summary="getRmaAdditionalProductsItem",
     *      tags={"RmaAdditionalProducts"},
     *      description="Get RmaAdditionalProducts",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaAdditionalProducts",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaAdditionalProducts"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var RmaAdditionalProducts $rmaAdditionalProducts */
        $rmaAdditionalProducts = $this->rmaAdditionalProductsRepository->find($id);

        if (empty($rmaAdditionalProducts)) {
            return $this->sendError('Rma Additional Products not found');
        }

        return $this->sendResponse($rmaAdditionalProducts->toArray(), 'Rma Additional Products retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/rmaAdditionalProducts/{id}",
     *      summary="updateRmaAdditionalProducts",
     *      tags={"RmaAdditionalProducts"},
     *      description="Update RmaAdditionalProducts",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaAdditionalProducts",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaAdditionalProducts"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateRmaAdditionalProductsAPIRequest $request)
    {
        $input = $request->all();

        /** @var RmaAdditionalProducts $rmaAdditionalProducts */
        $rmaAdditionalProducts = $this->rmaAdditionalProductsRepository->find($id);

        if (empty($rmaAdditionalProducts)) {
            return $this->sendError('Rma Additional Products not found');
        }

        $rmaAdditionalProducts = $this->rmaAdditionalProductsRepository->update($input, $id);

        return $this->sendResponse($rmaAdditionalProducts->toArray(), 'RmaAdditionalProducts updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/rmaAdditionalProducts/{id}",
     *      summary="deleteRmaAdditionalProducts",
     *      tags={"RmaAdditionalProducts"},
     *      description="Delete RmaAdditionalProducts",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaAdditionalProducts",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var RmaAdditionalProducts $rmaAdditionalProducts */
        $rmaAdditionalProducts = $this->rmaAdditionalProductsRepository->find($id);

        if (empty($rmaAdditionalProducts)) {
            return $this->sendError('Rma Additional Products not found');
        }

        $rmaAdditionalProducts->delete();

        return $this->sendSuccess('Rma Additional Products deleted successfully');
    }
}
