# Track New - Setup Instructions

## Project Overview

This is the recreated Track New Service Management System using:
- **Backend**: Node.js with Express.js, Sequelize ORM, MySQL
- **Frontend**: React.js with Redux Toolkit, React Router, Tailwind CSS

## Prerequisites

Before setting up the project, ensure you have the following installed:

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **MySQL** (v8.0 or higher)
- **Git**

## Project Structure

```
Track New/
├── tracknew-nodejs-backend/     # Node.js Backend API
│   ├── src/
│   │   ├── config/             # Database and app configuration
│   │   ├── controllers/        # Route controllers
│   │   ├── middleware/         # Custom middleware
│   │   ├── models/            # Sequelize models
│   │   ├── routes/            # API routes
│   │   ├── services/          # Business logic services
│   │   ├── utils/             # Utility functions
│   │   └── validators/        # Input validation
│   ├── uploads/               # File uploads directory
│   ├── logs/                  # Application logs
│   ├── package.json
│   ├── server.js             # Main server file
│   └── .env                  # Environment variables
│
└── tracknew-react-frontend/     # React Frontend
    ├── public/
    ├── src/
    │   ├── components/        # Reusable components
    │   ├── pages/            # Page components
    │   ├── store/            # Redux store and slices
    │   ├── services/         # API services
    │   ├── hooks/            # Custom hooks
    │   ├── utils/            # Utility functions
    │   └── styles/           # CSS and styling
    ├── package.json
    └── .env                  # Environment variables
```

## Backend Setup (Node.js)

### 1. Navigate to Backend Directory
```bash
cd tracknew-nodejs-backend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create a `.env` file in the backend root directory:

```bash
cp .env.example .env
```

Update the `.env` file with your configuration:

```env
# Application Configuration
NODE_ENV=development
PORT=8000
APP_NAME=Track New API
APP_URL=http://localhost:8000

# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=tracknew_development
DB_USERNAME=root
DB_PASSWORD=your_mysql_password
DB_DIALECT=mysql

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-here-make-it-long-and-random
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your-refresh-token-secret-here-also-long-and-random
JWT_REFRESH_EXPIRES_IN=30d

# Email Configuration (Optional - for password reset)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
FROM_EMAIL=<EMAIL>
FROM_NAME=Track New

# File Upload Configuration
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,pdf,doc,docx,xls,xlsx

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
```

### 4. Database Setup

#### Create MySQL Database
```sql
CREATE DATABASE tracknew_development CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

#### Create Database User (Optional)
```sql
CREATE USER 'tracknew_user'@'localhost' IDENTIFIED BY 'secure_password';
GRANT ALL PRIVILEGES ON tracknew_development.* TO 'tracknew_user'@'localhost';
FLUSH PRIVILEGES;
```

### 5. Run Database Migrations
```bash
# Install Sequelize CLI globally (if not already installed)
npm install -g sequelize-cli

# Run migrations to create tables
npm run migrate

# Seed the database with initial data
npm run seed
```

### 6. Start the Backend Server
```bash
# Development mode (with auto-restart)
npm run dev

# Production mode
npm start
```

The backend server will start on `http://localhost:8000`

### 7. Verify Backend Setup
- Health check: `http://localhost:8000/health`
- API documentation: `http://localhost:8000/api-docs` (if enabled)

## Frontend Setup (React.js)

### 1. Navigate to Frontend Directory
```bash
cd tracknew-react-frontend
```

### 2. Install Dependencies
```bash
npm install
```

### 3. Environment Configuration
Create a `.env` file in the frontend root directory:

```env
# API Configuration
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_APP_NAME=Track New

# Firebase Configuration (Optional - for push notifications)
REACT_APP_FIREBASE_API_KEY=your-firebase-api-key
REACT_APP_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
REACT_APP_FIREBASE_PROJECT_ID=your-project-id
REACT_APP_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
REACT_APP_FIREBASE_MESSAGING_SENDER_ID=123456789
REACT_APP_FIREBASE_APP_ID=1:123456789:web:abcdef
REACT_APP_FIREBASE_VAPID_KEY=your-vapid-key

# Feature Flags
REACT_APP_ENABLE_PWA=true
REACT_APP_ENABLE_OFFLINE_MODE=true
REACT_APP_ENABLE_ANALYTICS=false

# Development
REACT_APP_DEBUG=true
```

### 4. Install Tailwind CSS
```bash
# Install Tailwind CSS and its dependencies
npm install -D tailwindcss postcss autoprefixer

# Generate Tailwind config files
npx tailwindcss init -p
```

Update `tailwind.config.js`:
```javascript
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'),
    require('@tailwindcss/typography'),
  ],
}
```

### 5. Start the Frontend Development Server
```bash
npm start
```

The frontend will start on `http://localhost:3000`

## Complete Setup Verification

### 1. Backend Verification
- Visit `http://localhost:8000/health` - should return server status
- Check logs in the terminal for any errors
- Verify database connection in the logs

### 2. Frontend Verification
- Visit `http://localhost:3000` - should show the login page
- Check browser console for any errors
- Verify API connection by attempting to login

### 3. Full System Test
1. Register a new account at `http://localhost:3000/register`
2. Login with the created account
3. Navigate through different sections (Dashboard, Services, Customers, etc.)
4. Create a test service or customer to verify CRUD operations

## Default Admin Account

After running the database seeds, you can login with:
- **Email**: <EMAIL>
- **Password**: password123

## Development Workflow

### Backend Development
```bash
cd tracknew-nodejs-backend

# Start development server with auto-reload
npm run dev

# Run tests
npm test

# Check code style
npm run lint
```

### Frontend Development
```bash
cd tracknew-react-frontend

# Start development server
npm start

# Run tests
npm test

# Build for production
npm run build

# Check code style
npm run lint
```

## Production Deployment

### Backend Production Setup
1. Set `NODE_ENV=production` in `.env`
2. Use a production database
3. Configure proper logging
4. Set up process manager (PM2)
5. Configure reverse proxy (Nginx)
6. Set up SSL certificates

### Frontend Production Build
```bash
npm run build
```

Serve the `build` folder using a web server like Nginx or Apache.

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Verify MySQL is running
   - Check database credentials in `.env`
   - Ensure database exists

2. **Port Already in Use**
   - Change PORT in backend `.env`
   - Kill existing processes: `lsof -ti:8000 | xargs kill -9`

3. **CORS Issues**
   - Verify frontend URL in backend CORS configuration
   - Check API_URL in frontend `.env`

4. **Module Not Found Errors**
   - Delete `node_modules` and `package-lock.json`
   - Run `npm install` again

5. **Permission Errors**
   - Check file permissions for uploads directory
   - Ensure proper user permissions

### Getting Help

If you encounter issues:
1. Check the logs in both backend and frontend terminals
2. Verify all environment variables are set correctly
3. Ensure all dependencies are installed
4. Check that MySQL is running and accessible

## Next Steps

After successful setup:
1. Explore the codebase structure
2. Review the API documentation
3. Customize the application for your needs
4. Add additional features as required
5. Set up production deployment

The application includes all core features from the original Track New system:
- User authentication and authorization
- Service management
- Customer management
- Lead tracking
- AMC management
- Sales and inventory
- Reporting and analytics
- File uploads and document management
