<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Companies;
use Auth;
use Illuminate\Http\JsonResponse;

class SaasMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        
        if (env('APP_PLAN') == '1') {
      	 $user = Auth::user();
          if (!is_null($user->company_id)) {
      	 	$company = Companies::find($user->company_id);
      	 	$expUser = User::find($company->user_id);
        
         // Check if the subscription is expired for POST, PUT, DELETE methods
            if ($request->isMethod('post') || $request->isMethod('put') || $request->isMethod('delete')) {
                 $expiryEndOfDay = \Carbon\Carbon::parse($expUser->will_expire)->endOfDay();// Compare with the current time
				if ($expiryEndOfDay->lessThan(now())) {
                    return response()->json([
                        'message' => __('Your subscription payment has expired, Please renew the subscription'),
                        //'redirect_url' => '/user/dashboard'
                    ], JsonResponse::HTTP_PAYMENT_REQUIRED);
                }
              
              	if ($expUser->will_expire == null) {
                    $message = __('Your subscription payment is not completed');
                   // $redirect_url = Auth::user()->plan_id == null ? '/user/subscription' : '/user/subscription/' . Auth::user()->plan_id;

                    if ($request->expectsJson()) {
                        return response()->json(['message' => $message, 
                                                 //'redirect_url' => $redirect_url
                                                ], JsonResponse::HTTP_PAYMENT_REQUIRED);
                    }

                    return response('Subscription payment not completed.', 402);
              	}
            }   
          }
      		
        }   
            

        return $next($request);
        
    }
}
