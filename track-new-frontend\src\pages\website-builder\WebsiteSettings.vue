<template>
  <div class="builder-page">
    <!-- <h1 class="web-head">Website Customization</h1> -->

    <!-- Website Intro -->
    <div class="mb-2 lg:mb-6 relative w-11/12">
      <label class="block font-bold text-sm mb-2 flex items-center">
        Website Intro
        <span class="text-gray-500 font-normal text-xs ml-2">(Before Slider)</span>
        <font-awesome-icon icon="fa-solid fa-info-circle" class="ml-2 text-gray-400" />
      </label>
      <!-- <textarea v-model="websiteData.intro" class="border p-2 rounded w-full"
        placeholder="Enter your company intro here"></textarea> -->
      <div class="grid grid-cols-1">
        <CkEditorForm label="Website Intro (Optional)" :textData="websiteData.intro || ''"
          @editorSubmit="handleEditorSubmit" :company_id="companyId" />
      </div>
    </div>
    <div class="mb-2 lg:mb-6 relative w-11/12">
      <label class="block font-bold text-sm mb-2 flex items-center">
        Website Intro
        <span class="text-gray-500 font-normal text-xs ml-2">(After Slider)</span>
        <font-awesome-icon icon="fa-solid fa-info-circle" class="ml-2 text-gray-400" />
      </label>
      <!-- <textarea v-model="websiteData.intro" class="border p-2 rounded w-full"
        placeholder="Enter your company intro here"></textarea> -->
      <CkEditorForm label="Website Intro (Optional)" :textData="websiteData.intro_after || ''"
        @editorSubmit="handleEditorSubmitData" :company_id="companyId" />
    </div>

    <!-- Bottom Navigation Buttons -->
    <NavigationButtons :pageTitle="'Website Settings'" @goToNextPage="goToNextPage" @goToPrevPage="goToPrevPage" />
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
  </div>
</template>

<script>
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import CkEditorForm from './CkEditorForm.vue';

export default {
  props: {
    companyId: {
      type: String,
      required: true
    },
    domain: {
      type: [String, null], // Accepts both String and null
      required: true, // Ensures the prop must be passed
      validator: (value) => {
        return value === null || typeof value === 'string';
      },
    },
    websiteSettingsData: {
      type: Object,
      required: true,
    },
    is_updated: {
      type: Boolean,
      required: true,
    },
    isMobile: {
      type: Boolean,
      required: true,
    }
  },
  components: {
    NavigationButtons,
    confirmbox,
    CkEditorForm
  },

  data() {
    return {
      websiteData: {}, // Initialize with prop data
      domainName: this.domain,
      loading: false,
      //--confirmbox--
      open_confirmBox: false,
      deleteIndex: null,
      //--loader--
      circle_loader_photo: null,
    };
  },
  mounted() {
    if (this.websiteData.is_name === undefined) {
      this.websiteData.is_name = true;
    }
    if (this.websiteData.is_logo === undefined) {
      this.websiteData.is_logo = true;
    }
    if (this.websiteSettingsData && Object.keys(this.websiteSettingsData).length > 0) {
      this.websiteData = { ...this.websiteSettingsData };
    }
  },
  watch: {
    // Watch for changes in websiteSettingsData and update websiteData accordingly
    websiteSettingsData: {
      handler(newData) {
        this.websiteData = { ...newData };
      },
      deep: true,
      immediate: true
    },
    is_updated: {
      deep: true,
      handler(newValue) {
        this.$emit('updateWebsiteSettings', this.websiteData);
      }
    }
  },


  methods: {
    // Handle file uploads
    async onFileChange(event, type) {
      const file = event.target.files[0];
      if (!file) return;

      const maxSizeBytes = 500 * 1024; // 500kb in bytes

      try {
        this.circle_loader_photo = type; // Show loader while processing

        // Check file size and compress if necessary
        const uploadFile = file.size > maxSizeBytes ? await this.compressImage(file) : file;

        // Upload the (possibly compressed) file
        await this.uploadImageProfile(uploadFile, type);

      } catch (error) {
        console.error("Error processing file:", error);
      } finally {
        this.circle_loader_photo = null; // Hide loader once complete
      }
    },

    uploadImageProfile(file, type) {
      const formData = new FormData();
      formData.append("image", file);
      formData.append("model", type === 'logo' || type === 'logo_text' ? "website/WebsiteLogo" : "website/Favicon");
      formData.append("company_id", this.companyId);

      return axios.post('/image', formData)
        .then(response => {
          // Set logoUrl or faviconUrl based on the uploaded file type
          if (type === 'logo') {
            this.websiteData.logo = response.data.media_url;
          } else if (type === 'logo_text') {
            this.websiteData.logo_text = response.data.media_url;
          } else if (type === 'favicon') {
            this.websiteData.favicon = response.data.media_url;
          }
          this.$emit('updateWebsiteSettings', this.websiteData);

        })
        .catch(error => {
          console.error("Error uploading image", error);
          throw error; // Re-throw to handle in calling function
        });
    },

    compressImage(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (event) => {
          const img = new Image();
          img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Set maximum width and height for compressed image
            const maxWidth = 800;
            const maxHeight = 600;

            let width = img.width;
            let height = img.height;

            // Maintain aspect ratio while resizing
            if (width > maxWidth || height > maxHeight) {
              const aspectRatio = width / height;
              if (width > height) {
                width = maxWidth;
                height = width / aspectRatio;
              } else {
                height = maxHeight;
                width = height * aspectRatio;
              }
            }

            canvas.width = width;
            canvas.height = height;
            ctx.drawImage(img, 0, 0, width, height);

            // Convert to blob with desired quality
            canvas.toBlob((blob) => {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg',
                lastModified: Date.now()
              });
              resolve(compressedFile);
            }, 'image/jpeg', 0.7); // 70% quality
          };
          img.src = event.target.result;
        };
        reader.readAsDataURL(file);
      });
    },
    //--remove Image
    removeImage(type) {
      if (type) {
        if (type.type === 'contact' && type.index >= 0 && this.websiteData.contacts[type.index] && this.websiteData.contacts[type.index].number === '') {
          this.removeContact(type.index);
        } else if (type.type === 'email' && type.index >= 0 && this.websiteData.emails[type.index] !== undefined && this.websiteData.emails[type.index] === '') {
          this.removeEmail(type.index);
        } else {
          this.deleteIndex = type;
          this.open_confirmBox = true;
        }
      } else {
        this.open_confirmBox = true;
      }

    },
    //--confirmbox delete confirm
    deleteRecord() {
      if (this.deleteIndex && (this.deleteIndex.type === 'logo' || this.deleteIndex.type === 'logo_text' || this.deleteIndex.type === 'favicon')) {
        this.$emit('updateLoader', true);
        axios.delete('/delete-image', { params: { model: this.deleteIndex.type ? this.deleteIndex.type : '', image_url: this.deleteIndex.url ? this.deleteIndex.url : '' } })
          .then(response => {
            // console.log(response.data, 'this is delete responses');
            this.$emit('updateWebsiteSettings', { ...this.websiteData, logo: this.deleteIndex.type === 'logo' ? '' : this.websiteData.logo, logo_text: this.deleteIndex.type === 'logo_text' ? '' : this.websiteData.logo_text, favicon: this.deleteIndex.type === 'favicon' ? '' : this.websiteData.favicon });
            this.$emit('toasterMessages', { msg: response.data.message, type: 'success' });
            this.$emit('submitData');
            this.closeconfirmBoxData();
          })
          .catch(error => {
            console.error('Error', error);
            this.$emit('toasterMessages', { msg: error.response.data.error, type: 'warning' });
            if (error.response.data.error === 'Image not found') {
              this.$emit('updateWebsiteSettings', { ...this.websiteData, logo: this.deleteIndex.type === 'logo' ? '' : this.websiteData.logo, logo_text: this.deleteIndex.type === 'logo_text' ? '' : this.websiteData.logo_text, favicon: this.deleteIndex.type === 'favicon' ? '' : this.websiteData.favicon });
              this.$emit('submitData');
            }
            this.closeconfirmBoxData();
          })
      } else if (this.deleteIndex && !(this.deleteIndex.type === 'logo' || this.deleteIndex.type === 'logo_text' || this.deleteIndex.type === 'favicon')) {
        if (this.deleteIndex.type === 'contact' && this.deleteIndex.index >= 0) {
          this.removeContact(this.deleteIndex.index);
          this.$emit('toasterMessages', { msg: 'Contact data removed successfully', type: 'success' });
          this.closeconfirmBoxData();
        } else if (this.deleteIndex.type === 'email' && this.deleteIndex.index >= 0) {
          this.removeEmail(this.deleteIndex.index);
          this.$emit('toasterMessages', { msg: 'Email data removed successfully', type: 'success' });
          this.closeconfirmBoxData();
        } else {
          this.closeconfirmBoxData();
          this.$emit('toasterMessages', { msg: 'Data not found', type: 'warning' });
        }
      } else {

        this.closeconfirmBoxData();
      }
    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
    },
    // Add/Remove contacts
    addContact() {
      this.websiteData.contacts.push({ type: 'support', number: '' });
    },
    removeContact(index) {
      this.websiteData.contacts.splice(index, 1);
    },

    // Add/Remove emails
    addEmail() {
      this.websiteData.emails.push('');
    },
    removeEmail(index) {
      this.websiteData.emails.splice(index, 1);
    },
    registerDomin() {

      const websiteDomainData = {
        domain_name: this.domainName,
        company_id: this.companyId

      };

      // Add companyId to the registration data
      this.loading = true;


      //console.log(registrationData);

      // Call the API to register the website with the provided data
      axios.post('/website-domain/update', websiteDomainData)
        .then(response => {
          console.log("Website Domain registered successfully");

          this.loading = false;
          //this.fetchWebsiteSettings();

        })
        .catch(error => {
          console.error("Error Domain registering website:", error);
          this.isLoading = false;
          // Handle error response, such as validation errors
          if (error.response && error.response.data) {
            alert(`Error: ${error.response.data.message || 'Registration failed.'}`);
          } else {
            alert('An unexpected error occurred. Please try again.');
          }
        });


    },
    goToNextPage() {
      this.$emit('updateWebsiteSettings', this.websiteData);
      this.$emit('submitData');
      this.$emit('goToNextPage');
    },
    goToPrevPage() {
      this.$emit('updateWebsiteSettings', this.websiteData);
      this.$emit('submitData');
      this.$emit('goToPrevPage'); // Emit event to the parent component
    },
    // Submit form data to parent
    submitForm() {
      this.$emit('updateWebsiteSettings', this.websiteData); // Emit to parent
      console.log('Website Settings Submitted:', this.websiteData);
    },
    //--editorData--
    handleEditorSubmit(data) {
      if (data) {
        this.websiteData.intro = data;
      } else {
        this.websiteData.intro = '';
      }
    },
    handleEditorSubmitData(data) {
      if (data) {
        this.websiteData.intro_after = data;
      } else {
        this.websiteData.intro_after = '';
      }
    },
    //--exract from link
    extractCoordinates() {
      // Regular expression to match coordinates in a Google Maps URL
      const regex = /@([-+]?[0-9]*\.?[0-9]+),([-+]?[0-9]*\.?[0-9]+)/;
      const match = this.websiteData.mapAddress.match(regex);
      if (match) {
        this.websiteData.latitude = match[1]; // Extract latitude
        this.websiteData.longitude = match[2]; // Extract longitude
      } else {
        this.websiteData.latitude = "";
        this.websiteData.longitude = "";
      }
    },
    //--website name / logo
    handleToggleName() {
      if (this.websiteData.is_name !== undefined) {
        this.websiteData.is_name = !this.websiteData.is_name;
      } else {
        this.websiteData.is_name = true;
      }
    },
    handleToggleLogo() {
      if (this.websiteData.is_logo !== undefined) {
        this.websiteData.is_logo = !this.websiteData.is_logo;
      } else {
        this.websiteData.is_logo = true;
      }
    }
  },
};
</script>
