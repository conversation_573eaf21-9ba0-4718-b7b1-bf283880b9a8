<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateProformaAPIRequest;
use App\Http\Requests\API\UpdateProformaAPIRequest;
use App\Models\Proforma;
use App\Models\Customer;
use App\Models\InvoiceSettings;
use App\Models\Companies;
use App\Repositories\ProformaRepository;
use App\Repositories\EstimationRepository;
use App\Http\Resources\api\ProformaResource;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Barryvdh\DomPDF\Facade\Pdf;
use Response;
use Auth;

/**
 * Class ProformaController
 * @package App\Http\Controllers\API
 */

class ProformaAPIController extends AppBaseController
{
    /** @var  ProformaRepository */
    private $proformaRepository;
  	private $estimationRepository;

    public function __construct(ProformaRepository $proformaRepo, EstimationRepository $estimationRepo)
    {
      	
        $this->proformaRepository = $proformaRepo;
        $this->estimationRepository = $estimationRepo;
    }
  
  		public function downloadPDF($id, $sign = 0)
    	{

         	$sales = Proforma::where('id', $id)->first();
         

           if (empty($sales)) {
             return $this->sendError('Proforma not found');
           }

          $company_id = $sales->company_id;

          $invoice = InvoiceSettings::where('company_id', $company_id)->first();
          $customer =  Customer::where('id', $sales->customer_id)->first();

          $saleItems =  json_decode($sales->items,true);

          $company = Companies::where('id', $company_id)->first(); 
         
    		$company = Companies::where('id', $company_id)->first();
        	$pdf = Pdf::loadView('pdf.proforma', [
             	'data' => $sales,
             	'company' => $company,
             	'invoice' => $invoice,
             	'customer' => $customer,
           		'sale_items' => $saleItems,
              	'sign' => $sign
            ]);
        	return $pdf->download($sales->proforma_no.'.pdf');     

     
    	}
    
     	public function viewPDF($id, $sign = 0)
    	{       
          
       
        
          $sales = Proforma::where('id', $id)->first(); //$this->salesRepository->findWithRelated($id);
         

           if (empty($sales)) {
             return $this->sendError('Proformas not found');
           }

          $company_id = $sales->company_id;

          $invoice = InvoiceSettings::where('company_id', $company_id)->first();
          $customer =  Customer::where('id', $sales->customer_id)->first();

          $saleItems =  json_decode($sales->items,true);

          $company = Companies::where('id', $company_id)->first();  
 
    
           return view('pdf.proforma')->with([
               'data' => $sales,
               'company' => $company,
               'invoice' => $invoice,
               'customer' => $customer,
               'sale_items' => $saleItems,
             	'sign' => $sign
           ]);     
    	}

    /**
     * Display a listing of the Proforma.
     * GET|HEAD /proformas
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');
        $customer_id = $request->query('customer_id');
        $from_date = $request->query('from_date');
        $to_date = $request->query('to_date');
        $status = $request->query('status');
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        if (!$companyId) {
            return $this->sendError('Please provide company.', 400);
        }

        if (!Auth::check()) {
            return response()->json(['error' => 'Please login to access.'], 401);
        }

        $proQuery = Proforma::where('company_id', $companyId);

       
        if ($customer_id) {
            $proQuery->where('customer_id', $customer_id);
        }

        if ($from_date) {
            $proQuery->whereDate('updated_at', '>=', $from_date);
        }

        if ($to_date) {
            $proQuery->whereDate('updated_at', '<=', $to_date);
        }

        if ($status) {
            switch ($status) {
                case '1':
                    $proQuery->where('status', '1'); // Converted
                    break;
                case '0':
                    $proQuery->where('status', '0'); // Waiting
                    break;
                case 'all':
                    $proQuery->whereIn('status', ['0', '1']); // Both Converted and Waiting
                    break;
                default:
                    break;
            }
        }

        // Handle 'all' pagination option
        if ($perPage === 'all') {
            $perPage = $proQuery->count();
        }

        // Paginate the results
        $pro = $proQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        return response()->json([
            'success' => true,
            'data' => ProformaResource::collection($pro),
            'pagination' => [
                'total' => $pro->total(),
                'per_page' => $pro->perPage(),
                'current_page' => $pro->currentPage(),
                'last_page' => $pro->lastPage(),
                'from' => $pro->firstItem(),
                'to' => $pro->lastItem()
            ],
        ]);
    }


    /**
     * Store a newly created Proforma in storage.
     * POST /proformas
     *
     * @param CreateProformaAPIRequest $request
     *
     * @return Response
     */
    public function store(CreateProformaAPIRequest $request)
    {
        $input = $request->all();
      
        if ($input['company_id'] === null) {
            return $this->sendError('Please provide company.', 400);
        }
		
       if (Auth::check()) {
            $user = Auth::user();
         	$input['created_by'] = $user->id;
            $proforma = $this->proformaRepository->create($input);
         
         	if (isset($proforma->estimation_id) && $input['estimation_id'] !== null && !empty($input['estimation_id'])) {
                $estData['status'] = 2;
                $estData['proforma_id'] = $proforma->id;
                $estData['invoice_id'] =  $proforma->proforma_no;
                $this->estimationRepository->update($estData, $proforma->estimation_id);
            }

            // $customer = Customer::find($proforma->customer_id);
            // $customer_name = $customer->first_name . ' ' . $customer->last_name;
            // $contact_number = $customer->contact_number;

            // $relayMessage = new RelayMessage();
            // $url = 'https://app.track-new.com/viewsales?type=proforma&est_no='.$proforma->id.'&companyId='.$proforma->company_id;

            // if (isset($input['issms']) && $input['issms'] == true) {
            //     $relayMessage->sendSaleEstimate($customer_name, $contact_number, $url, 'estimate');
            // }

            // if (isset($input['iswhatsapp']) && $input['iswhatsapp'] == true) {
            //     $relayMessage->saleEstimateWhatsAppMessage($customer_name, $contact_number, $url, $proforma->company_id, 'estimate');
            // }
 				return $this->sendResponse(new ProformaResource($proforma), 'Proforma saved successfully');
       		// return $this->sendResponse($proforma->toArray(), 'Proforma saved successfully');
           } 
      		else {
              return response()->json(['error' => 'Please login to access.'], 401);
          }
    }

    /**
     * Display the specified Proforma.
     * GET|HEAD /proformas/{id}
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        /** @var Proforma $proforma */
        $proforma = $this->proformaRepository->find($id);

        if (empty($proforma)) {
            return $this->sendError('Proforma not found');
        }
		 return $this->sendResponse(new ProformaResource($proforma), 'Proforma retrieved successfully');
   
    }

    /**
     * Update the specified Proforma in storage.
     * PUT/PATCH /proformas/{id}
     *
     * @param int $id
     * @param UpdateProformaAPIRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateProformaAPIRequest $request)
    {
        $input = $request->all();

        /** @var Proforma $proforma */
        $proforma = $this->proformaRepository->find($id);

        if (empty($proforma)) {
            return $this->sendError('Proforma not found');
        }
      
      	if (Auth::check()) {
            $user = Auth::user();
         	$input['updated_by'] = $user->id;

        	$proforma = $this->proformaRepository->update($input, $id);
          	
          

        // $customer = Customer::find($proforma->customer_id);
        // $customer_name = $customer->first_name . ' ' . $customer->last_name;
        // $contact_number = $customer->contact_number;
        
        // $relayMessage = new RelayMessage();
        // $url = 'https://app.track-new.com/viewsales?type=proforma&est_no='.$proforma->id.'&companyId='.$proforma->company_id;
        
        // if (isset($input['issms']) && $input['issms'] == true) {
        //     $relayMessage->sendSaleEstimate($customer_name, $contact_number, $url, 'estimate');
        // }
        
        // if (isset($input['iswhatsapp']) && $input['iswhatsapp'] == true) {
        //    $res = $relayMessage->saleEstimateWhatsAppMessage($customer_name, $contact_number, $url, $proforma->company_id, 'estimate');
           
         
        // }

        

        return $this->sendResponse($proforma->toArray(), 'Proforma updated successfully');
        } else {
            return response()->json(['error' => 'Please login to access.'], 401);
        }
    }

    /**
     * Remove the specified Proforma from storage.
     * DELETE /proformas/{id}
     *
     * @param int $id
     *
     * @throws \Exception
     *
     * @return Response
     */
    public function destroy($id)
    {
        /** @var Proforma $proforma */
        $proforma = $this->proformaRepository->find($id);

        if (empty($proforma)) {
            return $this->sendError('Proforma not found');
        }

        $proforma->delete();

        return $this->sendSuccess('Proforma deleted successfully');
    }
}
