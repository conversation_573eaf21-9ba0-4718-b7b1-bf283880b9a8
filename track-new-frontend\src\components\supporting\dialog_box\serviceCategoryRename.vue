<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto pt-[130px]">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-auto"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Service Category Rename
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block text-sm bg p-5" :class="{ 'mb-[60px]': isMobile }">
                <!-- Service Category-->
                <div class="w-full mb-4 relative">
                    <label for="service_category" class="text-sm font-bold">
                        Service Category:
                    </label>
                    <div v-for="(category, index) in formValues.service_category" :key="index"
                        class="flex flex-col justify-center items-center w-full mt-2 relative">
                        <input v-model="formValues.service_category[index]" type="text"
                            @keyup.enter="handleFocus(index + 1)" @input="validateInput(index)"
                            @keydown.arrow-down.prevent="handleFocus(formValues.service_category.length - 1 > index ? index + 1 : 0)"
                            @keydown.arrow-up.prevent="handleFocus(0 !== index ? index - 1 : formValues.service_category.length - 1)"
                            :ref="'category' + index"
                            class="text-sm p-1 py-1 border border-gray-300 w-full focus:border-blue-500 rounded outline-none"
                            placeholder="Enter the category name" />
                        <p v-if="message_err !== ''" class="py-1 text-xs text-red-500">{{ message_err }}</p>
                    </div>
                </div>
                <p v-if="message !== '' && formValues.service_category[0] === ''" class="text-red-600 text-xs">
                    {{ message }}</p>
                <div class="flex justify-center items-center">
                    <button class="border rounded text-white bg-green-700 px-6 py-2 hover:bg-green-600"
                        @click="saveCategory">Save</button>
                </div>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        item_data: Object,
        message_err: String,
        isMobile: Boolean
    },
    data() {
        return {
            isOpen: false,
            formValues: { service_category: [''] },
            message: '',
            isInputFocused: {},
            show: false,
            type: 'warning',
        };
    },
    methods: {
        validateInput(index) {
            const validPattern = /^[a-zA-Z0-9-]*$/;
            const specialChars = /[!@#$%^&*()_+\=\[\]{};':"\\|,.<>\/?]/;

            if (!validPattern.test(this.formValues.service_category[index])) {
                // this.$root.$emit('show-toast', { message: 'Only letters, numbers, and hyphens are allowed.', type: 'info' });
                this.message = 'Only letters, numbers, and hyphens are not allowed.';
                this.show = true;
                // Check if the input contains any special characters
                if (specialChars.test(this.formValues.service_category[index])) {
                    // Replace special characters with an empty string
                    this.formValues.service_category[index] = this.formValues.service_category[index].replace(specialChars, '');
                }
            }
        },
        closeModal(data) {
            this.isOpen = false;
            setTimeout(() => {
                if (Array.isArray(data)) {
                    this.$emit('close-Modal', data);
                } else {
                    this.$emit('close-Modal');
                }
                this.message = '';
            }, 300);
        },
        //---close message---
        closeMessageDialog() {
            this.isMessage = false;
        },
        //---save terms and condition---
        saveCategory() {
            const nonEmptySerials = this.formValues.service_category.filter(category => category.trim() !== '');
            if (nonEmptySerials.length > 0) {
                this.closeModal(nonEmptySerials);
            } else {
                this.message = 'Field is empty. Please enter category name.';
            }
        },
        //---add new--
        displayNextField(index, type) {
            // console.log(this.formValues.service_category[index], 'Waht happening..!');
            if (this.formValues.service_category[index].length > 0 && this.formValues.service_category[index + 1] === undefined) {
                this.formValues.service_category.push('');
            }
            else if (type === 'enter' && index === this.formValues.service_category.length - 1) {
                this.formValues.service_category.push('');
            }
        },
        //---remove index--
        removeSerial(index) {
            this.formValues.service_category.splice(index, 1); // Remove the input field at the specified index
        },
        handleFocus(index) {
            this.$nextTick(() => {
                const lastIndex = index;
                const refName = 'category' + lastIndex;
                const inputElement = this.$refs[refName];
                // console.log(inputElement, 'EEEEe');
                if (inputElement[0]) {
                    inputElement[0].focus();
                }
            });
        },

    },
    watch: {
        showModal(newValue) {
            // console.log(newValue, 'Waht happening..!');
            setTimeout(() => {
                this.isOpen = newValue;
                this.handleFocus(this.formValues.service_category.length - 1);
            }, 100);
        },
        item_data: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.formValues && this.formValues.service_category && this.formValues.service_category.length > 0) {
                    this.formValues.service_category = newValue.map(opt => opt.service_category);
                }
            }
        }
    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>
