<template>
    <div class="flex h-screen relative text-sm">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar :route_item="route_item" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mt-[60px]': isMobile, 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar"></headbar> -->


            <!-- services home -->
            <div class="relative">
                <bannerDesign></bannerDesign>
                <openRMAhome :isMobile="isMobile" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </openRMAhome>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>

        </div> -->

    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
import openRMAhome from '@/components/supporting/openRMA/openRMAhome.vue';
// import headbar from '@/components/supporting/openRMA/headbar.vue';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';
import bannerDesign from '../supporting/banner/bannerDesign.vue';
export default {
    name: 'openRMA',
    components: {
        // sidebar,
        openRMAhome,
        bannerDesign
        // headbar
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            route_item: 14,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'RMA';
        const pageDescription = 'Assign warranties to products sold or serviced, and streamline the RMA process for any faults within the warranty period. Ensure smooth RMA management by tracking product warranties and handling issues efficiently within the warranty duration.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList', 'validateRoles']),

        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        },
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        this.fetchLocalDataList();
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>