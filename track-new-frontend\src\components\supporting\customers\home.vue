<template>
    <div :class="{ 'manualStyle text-sm': isMobile, 'text-sm': !isMobile }" class="custom-scrollbar-hidden"
        ref="scrollContainer" @scroll="handleScroll">
        <div class="my-custom-margin">
            <div v-if="isMobile" class="m-4">
                <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer" @resetData="resetToSearch">
                </searchCustomer>
                <div v-if="!open_skeleton" class="text-xs mt-2">
                    <p v-if="pagination_data.customer">Total no of customers: <span class="font-bold">{{
                        pagination_data.customer.total }}</span></p>
                </div>
            </div>
            <!--page header-->
            <div v-if="!isMobile" class="m-1 my-3 flex items-center space-x-4">
                <p class="font-bold text-xl">Customers</p>
                <div v-if="!open_skeleton"
                    class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                    <p class="text-gray-700">Total Customers
                        :</p>
                    <p class="font-semibold pl-1">
                        {{ pagination_data.customer ? pagination_data.customer.total : 0 }}
                    </p>
                </div>
            </div>
            <!--new design header-->
            <div v-if="!isMobile" class="flex justify-between m-1">
                <div class="flex space-x-4">
                    <button @click="openModal"
                        class="bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center shadow-inner shadow-green-200 border border-green-500">
                        <span class="text-center"><font-awesome-icon icon="fa-solid fa-plus" /></span> <span>New
                            Customer</span>
                    </button>
                    <!--import customer-->
                    <button @click="openImportModal"
                        class="border rounded-lg px-1 sm:px-3 bg-blue-600  text-white border-blue-600 shadow-inner shadow-blue-100 flex items-center text-sm">
                        <font-awesome-icon icon="fa-solid fa-upload" class="px-1" />
                        <span v-if="!isMobile" class="text-sm lg:inline hidden">Import Customers</span>
                    </button>
                    <!--view type-->
                    <div class="flex items-center">
                        <div v-if="items_category === 'tile'"
                            class="p-1 flex-shrink-0 cursor-pointer info-msg border rounded-lg  border-gray-500"
                            :class="{ 'bg-white': items_category === 'tile' }" @click="toggleView"
                            :title02="`Table view`">
                            <font-awesome-icon v-if="items_category === 'tile'" icon="fa-solid fa-check"
                                class="pr-1 text-green-600 font-bold" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-else
                            class="p-1 rounded flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border rounded-lg border border-gray-500"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="`Card view`">
                            <font-awesome-icon v-if="items_category !== 'tile'" icon="fa-solid fa-check"
                                class="pr-1 text-green-600 font-bold" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
            </div>
            <!---fiter information-->
            <div v-if="Object.keys(filteredBy).length > 0" class="text-xs flex  flex-row overflow-auto">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2">
                    <p class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                    <p>{{ key === 'assign_to' ? value.join(', ') : key === 'type' ? typeList[value] : key ===
                        'status' ?
                        statusList[value] : value }}</p>
                </div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>

            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <!-- <div class="mt-5 ml-2 mr-2"> -->
            <div v-if="!open_skeleton && data.length > 0" class="text-sm m-1 mt-3">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <!--table header-->
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                        <!--search bar--->
                        <div>
                            <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                @resetData="resetToSearch">
                            </searchCustomer>
                        </div>
                    </div>
                    <!--table-->
                    <div v-if="items_category === 'tile'"
                        class="table-container overflow-x-auto flex justify-left items-center">
                        <table class="table w-full"><!--border border-gray-400 rounded-lg-->
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        @mouseover="showSortIcons(column)" @mouseleave="hideSortIcons(column)"
                                        @click="getSortType((column), column.field)"
                                        :class="{ 'hidden': !column.visible }" class="p-1 table-border cursor-pointer">
                                        <div class="flex justify-start items-center px-2 relative">
                                            <p>{{ column.label === 'First Name' ? 'Name' : column.label }}</p>
                                            <div v-if="(column.field === 'first_name' && (short_visible[0].visible || short_visible[0].type !== '')) || (column.field === 'balance_amount' && (short_visible[1].visible || short_visible[1].type !== ''))"
                                                class="text-xs px-2 absolute right-2 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="(column.field === 'first_name' && (short_visible[0].type === '' || short_visible[0].type === 'desc')) || (column.field === 'balance_amount' && (short_visible[1].type === '' || short_visible[1].type === 'desc'))"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': (column.field === 'first_name' && (short_visible[0].type === 'desc')) || (column.field === 'balance_amount' && (short_visible[1].type === 'desc')) }"
                                                    :title02="'Ascending order'"><font-awesome-icon
                                                        icon="fa-solid fa-chevron-up" /></span>
                                                <span
                                                    v-if="(column.field === 'first_name' && short_visible[0].type === 'asc') || (column.field === 'balance_amount' && short_visible[1].type === 'asc')"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'"><font-awesome-icon
                                                        icon="fa-solid fa-chevron-down" /></span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="px-4 py-2 leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[5px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in paginatedData" :key="record.id"
                                    class="hover:bg-gray-200 cursor-pointer px-1">
                                    <td v-for="(column, i) in columns" :key="i + 1"
                                        :class="{ 'hidden': !column.visible }" class="px-1 py-2 table-border "
                                        @click="handleClick(column.field, record, $event)">
                                        <span
                                            v-if="column.field === 'customer_category' && [record[column.field]] >= 0">{{
                                                serviceCategories[record[column.field]] &&
                                                serviceCategories[record[column.field]]['category_name'] }} </span>
                                        <span v-if="column.field === 'address'"> {{
                                            record[column.field] && record[column.field] + ' ' + record['city_name']
                                                ?
                                                record['city_name'] : '' + ' ' +
                                                    record['district_name'] ? record['district_name'] : '' + ' ' +
                                                        record['state_name']
                                                    ?
                                                    record['state_name'] : '' + ' - ' + record['pincode'] ?
                                                        record['pincode'] :
                                                        ''
                                        }} </span>
                                        <span v-if="column.field === 'first_name'"
                                            class="font-semibold text-blue-900 leading-tight hover:text-indigo-600 transition duration-200">
                                            {{ record[column.field] + ' ' + (record['last_name'] ? record['last_name'] :
                                                '') }}
                                        </span>

                                        <span v-if="column.field === 'contact_number'"
                                            @click="dialPhoneNumber(record.contact_number)" class="cursor-pointer">{{
                                                record[column.field] }}</span>

                                        <span
                                            v-if="column.field !== 'first_name' && column.field !== 'contact_number' && column.field !== 'created_at' && column.field !== 'balance_amount'">
                                            {{ record[column.field] }}
                                        </span>
                                        <span v-if="column.field === 'balance_amount'"
                                            class="flex justify-between items-center">
                                            <span
                                                :class="{ 'text-red-700': record[column.field] > 0, 'text-green-700': record[column.field] == 0 }">{{
                                                    record[column.field] >= 0 ? currentCompanyList &&
                                                        currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                        currentCompanyList.currency : '' }} {{ record[column.field] }}
                                            </span>
                                            <button v-if="record[column.field] > 0 && companywhatsapp"
                                                class="text-green-700 text-xs" @click="openWhatsApp(record)">
                                                <font-awesome-icon icon="fa-brands fa-whatsapp" />
                                                Send Reminder
                                            </button>
                                            <button v-if="record[column.field] > 0 && !companywhatsapp"
                                                class="text-red-700 text-xs" @click="navigateToWhatsApp">
                                                <font-awesome-icon icon="fa-brands fa-whatsapp" />
                                                Connect WhatsApp
                                            </button></span>
                                        <span v-if="column.field === 'created_at'" class="created-at-color"
                                            :title="calculateDaysAgo(formattedDate(record[column.field]))">
                                            <skeleton v-if="time_loading" :isLoading="time_loading" :cols="1" :rows="1"
                                                :gap="0" :type="'table'" :is_time="true">
                                            </skeleton>
                                            <span v-else> {{ formatDate(formattedDate(record[column.field])) }}</span>
                                        </span>
                                    </td>
                                    <td class="text-center py-2 table-border">
                                        <!-- <div class="flex justify-center relative"> -->
                                        <div class="flex justify-center">
                                            <!--More actions-->
                                            <div class="flex relative space-x-2 text-xs">
                                                <!-- View Button -->
                                                <button v-if="!record.editing" @click="viewRecord(record)" title="View"
                                                    class="flex items-center gap-2 px-3 py-1 text-purple-600 border border-purple-300 rounded-lg shadow-sm hover:bg-purple-100 transition">
                                                    <font-awesome-icon icon="fa-solid fa-eye" />
                                                    <span>View</span>
                                                </button>

                                                <!-- Edit Button -->
                                                <button v-if="!record.editing" @click="editRecord(record)" title="Edit"
                                                    class="flex items-center gap-2 px-3 py-1 text-blue-600 border border-blue-300 rounded-lg shadow-sm hover:bg-blue-100 transition">
                                                    <font-awesome-icon icon="fa-solid fa-pencil" />
                                                    <span>Edit</span>
                                                </button>

                                                <!-- Delete Button (Only for Admins) -->
                                                <button v-if="!record.editing && checkRoles(['admin'])"
                                                    @click="confirmDelete(record)" title="Delete"
                                                    class="flex items-center gap-2 px-3 py-1 text-red-600 border border-red-300 rounded-lg shadow-sm hover:bg-red-100 transition">
                                                    <font-awesome-icon icon="fa-regular fa-trash-can" />
                                                    <span>Delete</span>
                                                </button>
                                                <!-- <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-8 absolute bg-white divide-y divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="viewRecord(record)"
                                                            class="text-[#8b5cf6;] px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-eye"
                                                                style="color: #8b5cf6;" />
                                                            <span class="px-2">View</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="editRecord(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(record)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr v-if="!data || data.length === 0" class="items-center justify-center flex border">
                                    <button class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                        @click="openModal">+ Add Customer</button>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--card view-->
                    <div>
                        <div v-if="items_category === 'list'"
                            class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 sm:gap-4 custom-scrollbar-hidden">
                            <div v-for="(record, index) in data" :key="index" class="w-full">
                                <div class="max-w-md mx-auto bg-white rounded-xl border border-gray-300 md:max-w-2xl">
                                    <!-- Top Section -->
                                    <div class="flex justify-between items-center px-4 py-2">
                                        <!-- Customer Details (Can be your dynamic content) -->
                                        <div class="flex items-center">
                                            <div @click="viewRecord(record)"
                                                class="h-10 w-10 rounded-full flex items-center justify-center cursor-pointer text-white font-bold mr-4"
                                                :class="{ 'bg-gray-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-blue-600': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                                {{ record.first_name && record.first_name[0]
                                                    ?
                                                    record.first_name[0].toUpperCase() :
                                                    'C' }}
                                            </div>
                                            <div>
                                                <h4 class="text-sm leading-6 font-medium text-gray-900 mb-1 cursor-pointer"
                                                    @click="viewRecord(record)">
                                                    {{ record.first_name ?
                                                        (record.first_name + ' ' +
                                                            (record.last_name ?
                                                                record.last_name : '')) : '' }}</h4>
                                                <p class="text-sm text-gray-500 cursor-pointer"
                                                    @click="dialPhoneNumber(record.contact_number)">
                                                    +91-{{
                                                        record.contact_number }}</p>
                                            </div>
                                        </div>

                                        <!-- Right Side (Actions) -->
                                        <div class="flex space-x-4 relative">
                                            <div class="flex justify-center">
                                                <!--More actions-->
                                                <div class="relative mb-3">
                                                    <button @click.stop="displayAction(index)"
                                                        class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                        <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                            size="lg" />
                                                    </button>
                                                </div>
                                                <div v-if="display_option === index" :ref="'dropdown' + index"
                                                    class="z-10 mt-8 absolute bg-white divide-y divide-gray-100 right-0 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                    style="display: flex; justify-content: center; align-items: center;">
                                                    <ul class="py-1 text-gray-700"
                                                        aria-labelledby="dropdownDefaultButton">
                                                        <li class="hover:bg-gray-200">
                                                            <button v-if="!record.editing" @click="viewRecord(record)"
                                                                class="text-gray-700 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-eye"
                                                                    style="color: gray;" size="lg" />
                                                                <span class="px-2">View</span>
                                                            </button>
                                                        </li>
                                                        <li class="hover:bg-gray-200">
                                                            <button v-if="!record.editing" @click="editRecord(record)"
                                                                class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-pencil"
                                                                    style="color: #3b82f6;" size="lg" />
                                                                <span class="px-2">Edit</span>
                                                            </button>
                                                        </li>
                                                        <li class="hover:bg-gray-200">
                                                            <button v-if="!record.editing && checkRoles(['admin'])"
                                                                @click="confirmDelete(record)"
                                                                class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                    style="color: #ef4444" />
                                                                <span class="px-2">Delete</span>
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!--due amount-->
                                    <div class="px-4 pb-1 flex justify-between items-center">
                                        <!--sent reminder-->
                                        <div class="text-sm">
                                            <div v-if="record['balance_amount'] > 0">
                                                <button v-if="companywhatsapp" class="text-green-700"
                                                    @click="openWhatsApp(record)">
                                                    <font-awesome-icon icon="fa-brands fa-whatsapp" />
                                                    Send Reminder
                                                </button>
                                                <button v-else class="text-red-700" @click="navigateToWhatsApp">
                                                    <font-awesome-icon icon="fa-brands fa-whatsapp" />
                                                    Connect WhatsApp
                                                </button>
                                            </div>
                                        </div>
                                        <!--due details-->
                                        <div class="flex justify-end items-center cursor-pointer"
                                            @click="viewRecord(record)">
                                            <p class="pr-1">Due:</p>
                                            <p
                                                :class="{ 'text-red-600': record['balance_amount'] > 0, 'text-green-600': record['balance_amount'] === 0 || !record['balance_amount'] }">
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9'
                                                    : currentCompanyList.currency }} {{ record['balance_amount'] >= 0 ?
                                                    record['balance_amount'] : 0 }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--no data found-->
                    <div v-if="pagination_data && pagination_data.customer && this.pagination_data.customer.last_page > 0 && this.pagination_data.customer.last_page === this.pagination_data.customer.current_page && isMobile && !open_skeleton_isMobile"
                        class="text-sm mb-[100px]">
                        <p class="font-bold text-center py-2 text-green-700">
                            <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                            Finished !
                        </p>
                    </div>
                    <div class="pagination flex items-center justify-between mt-2 sm:text-md text-xs"
                        v-if="pagination_data.customer && !open_skeleton && !isMobile">
                        <!-- <p class="pagination-info">Page {{ currentPage }} of {{ totalPages }}</p> -->
                        <div class="mt-4">
                            <!-- <select v-model="recordsPerPage" @change="changePage"
                                class="mt-1 p-2 border border-gray-300 rounded pr-5 mr-3 mb-6">
                                <option v-for="option in options" :key="option" :value="option" class="text-xs">{{
                                    option }}
                                </option>
                            </select>
                            <label v-if="!isMobile" for="recordsPerPage">Records per page</label> -->
                            <p class="text-sm text-gray-700">
                                Showing {{ ((pagination_data.customer.current_page - 1) *
                                    pagination_data.customer.per_page) + 1 }} to {{
                                    Math.min(pagination_data.customer.current_page * pagination_data.customer.per_page,
                                        pagination_data.customer.total) }} of
                                {{ pagination_data.customer.total }} entries
                            </p>
                        </div>
                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none overflow-auto">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span>
                                    </button>
                                </li>
                                <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">
                                        {{ pageNumber }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === pagination_data.customer.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== pagination_data.customer.last_page }">
                                    <button @click="updatePage(currentPage + 1)"
                                        :disabled="currentPage === pagination_data.customer.last_page"
                                        class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!---total records per page-->
                    <!-- <div v-if="!open_skeleton && !isMobile" class="text-xs sm:mt-2 lg:mt-0">
                        <p v-if="pagination_data.customer">Total records: <span class="font-bold">{{
                            pagination_data.customer.total }}</span></p>
                    </div> -->
                </div>
            </div>
            <!--loader-->
            <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>

            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!--in mobile view import items-->
        <div v-if="isMobile" class="fixed bottom-36 right-5 z-50 bg-blue-600 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openImportModal" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-upload" size="lg" />
                </button>
            </div>
        </div>
        <!---in mobile view create new sale-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openModal" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModal" :userName="null"
            :editData="editData" :type="typeOfRegister" :more_info="true"></customerRegister>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete">
        </confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!--Filter-->
        <customerFilter :showModal="lead_filter" @closeFilter="closeLeadFilter" :customer_list_data="originalData">
        </customerFilter>
        <Loader :showModal="open_loader"></Loader>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'categories'"></bottombar> -->
        <importCustomerModal :isOpen="isImportModalOpen" @close="closeImportModal" :companyId="companyId"
            :userId="userId" :isMobile="isMobile"></importCustomerModal>
        <!--whatsapp message-->
        <whatsappMessage :showModal="openWhatsAppMessage" :type="'customer'" :data="selected_customer"
            @close="closeWhatsappMessage">
        </whatsappMessage>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import customerRegister from '../dialog_box/customerRegister.vue';
import confirmbox from '../dialog_box/confirmbox.vue';
import dialogAlert from '../dialog_box/dialogAlert.vue';
import customerFilter from '../dialog_box/filter_Modal/customerFilter.vue';
// import bottombar from '../dashboard/bottombar.vue';
import searchCustomer from './searchCustomer.vue';
import importCustomerModal from '../dialog_box/importCustomerModal.vue';
import { mapActions, mapGetters } from 'vuex';
import whatsappMessage from '../dialog_box/whatsappMessage.vue';
export default {
    name: 'customers_home',
    emits: ['updateIsOpen', 'dataToParent'],
    components: {
        customerRegister,
        confirmbox,
        dialogAlert,
        customerFilter,
        // bottombar,
        searchCustomer,
        importCustomerModal,
        whatsappMessage
    },
    props: {
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 50,
            currentPage: 1,
            data: [],
            originalData: [],
            showModal_customer: false,
            editData: null,
            typeOfRegister: 'add',
            open_confirmBox: false,
            deleteIndex: null,
            columns: [],
            isMobile: false,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataBy: [],
            serviceCategories: [],
            //---filter
            lead_filter: false,
            typeList: [],
            statusList: [],
            filteredBy: {},
            //--dialog
            open_message: false,
            message: '',
            //--api integration--
            companyId: null,
            userId: null,
            changeStatus: false,
            pagination_data: {},
            //--skeleton--
            open_skeleton: false,
            number_of_columns: 7,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            now: null,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            open_skeleton_isMobile: false,
            backup_data: [],
            //---table data filter asending and decending----
            is_filter: false,
            //--importted data---
            isImportModalOpen: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //--short visible ---
            short_visible: [],
            //---whats app message---
            openWhatsAppMessage: false,
            selected_customer: null,
            //--time loading--
            time_loading: false,
        };
    },
    computed: {
        ...mapGetters('customerList', ['currentCustomersList']),
        ...mapGetters('customerCategories', ['currentCustomerCategoryList']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('dataFilter', ['filteredData']),
        ...mapGetters('dataFilter', ['pageStatus']),
        ...mapGetters('dataFilter', ['originalDataFilter']),
        ...mapGetters('dataFilter', ['typeData']),
        ...mapGetters('dataFilter', ['filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };
            // Define the custom order of fields
            const customOrder = ['created_at', 'first_name', 'contact_number', 'balance_amount'];
            // Iterate over the first record in data to get field names
            for (const key in this.data[0]) {
                if (key !== 'id' && key !== 'company_id' && key !== 'updated_at' && key !== 'deleted_at') {
                    const label = formatLabel(key);
                    fields.push({
                        label,
                        field: key,
                        visible: customOrder.includes(key) // Make visible based on the custom order
                    });
                }
            }

            // Sort fields based on the custom order, keeping the other fields at the end
            fields.sort((a, b) => {
                const aIndex = customOrder.indexOf(a.field);
                const bIndex = customOrder.indexOf(b.field);

                // If both fields are in the custom order, compare their indices
                if (aIndex !== -1 && bIndex !== -1) {
                    return aIndex - bIndex;
                }
                // If only one field is in the custom order, that one should come first
                if (aIndex !== -1) return -1;
                if (bIndex !== -1) return 1;

                // If neither field is in the custom order, maintain their original order
                return 0;
            });

            this.columns = fields;
            return fields;
        },
        paginatedData() {
            const startIndex = (this.currentPage - 1) * this.recordsPerPage;
            const endIndex = startIndex + this.recordsPerPage;
            return this.data;
        },
        totalPages() {
            return Math.ceil(this.data.length / this.recordsPerPage);
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination_data.customer.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        }
    },
    methods: {
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('customerList', ['fetchCustomersList']),
        ...mapActions('customerCategories', ['fetchCustomerCategoryList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData']),
        ...mapActions('dataFilter', ['updateFilter']),
        ...mapActions('dataFilter', ['updateFilterParams']),
        ...mapActions('dataFilter', ['fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),

        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination_data.customer.last_page) {
                this.currentPage = pageNumber;
                // this.getCustomerList(this.currentPage, this.recordsPerPage);
            }
        },
        viewRecord(record) {
            // Handle view action
            // console.log("View", record);
            // this.$emit('showViewCustomer', record);
            this.$router.push({ name: 'customers-view', params: { id: record.id } });
        },
        editRecord(record) {
            // Handle edit action
            // console.log("Edit", record);
            // this.$emit('showAddService', record, 'edit');
            this.editData = record;
            // openModal();
            this.showModal_customer = true;
            this.typeOfRegister = 'edit';
        },
        deleteRecord() {
            if (this.deleteIndex !== null) {
                this.open_loader = true;
                // console.log(this.deleteIndex.id, 'UUUUUUUUUUUUU');
                let backup = this.deleteIndex.id;
                axios.delete(`/customers/${this.deleteIndex.id}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.data);
                        this.open_loader = false;
                        this.data = this.data.filter((opt) => opt.id !== backup);
                        this.updateKeyWithTime('customer_update');
                    })
                    .catch(error => {
                        console.error('Error delete record', error);
                        let message = error.response.data && error.response.data.message ? error.response.data.message : '';
                        if (message !== '') {
                            this.type_toaster = 'warning';
                            this.message = message;
                            this.show = true;
                        }
                        this.open_loader = false;
                    })
                // this.originalData.splice((this.recordsPerPage * (this.currentPage - 1)) + this.deleteIndex, 1);
                // localStorage.setItem('userData', JSON.stringify(this.originalData));
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(record) {

            // console.log(index, 'What happening...');
            this.deleteIndex = record;
            // console.log(record, 'EEEEEEEEEE');
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        addServices() {
            this.$emit('showAddService', null, 'add');
        },
        openModal() {
            this.typeOfRegister = 'add';
            this.showModal_customer = true;

        },
        closeModal(data) {
            // console.log(data);
            if (data) {
                let findDuplication = this.originalData.findIndex((opt) => opt.id === data.id)
                if (findDuplication !== -1) {
                    this.originalData[findDuplication] = data;
                    this.data[findDuplication] = data;
                } else {
                    // console.log('heelooo', 'what happening...!', this.originalData);
                    // console.log(this.originalData, 'What happening...1')
                    this.originalData = [data, ...this.originalData];
                    this.data = this.originalData;
                }
                this.fetchApiUpdates();
                this.fetchCustomersList({ page: this.currentPage, per_page: this.recordsPerPage });
            }

            this.showModal_customer = false;
            this.editData = null;

        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                    // console.log('hello');
                } else {
                    //     console.log('hello');
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            if (!this.$refs.settingOPtion) {
                // Exit early if the reference is not available            
                this.isDropdownOpen = false;
                return;
            }
            const isClickInside = this.$refs.settingOPtion.contains(event.target);
            if (!isClickInside) {
                this.isDropdownOpen = false;
                // this.toggleDropdown();
            }
        },

        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        // closeFilterModal() {
        //     this.showFilterModal = false;
        //     this.resetFilter();
        // },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredDataBy = [...this.originalData];
        },
        applyFilter() {
            // Implement logic to filter data based on filter values
            // For simplicity, this example applies a basic filter
            this.filteredDataBy = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter---
        toggleFilter() {
            // console.log('GGGGGGGGGGGGGGG');
            // this.typeList = this.leadType.map((opt) => opt.name);
            // this.statusList = this.leadStatus.map((opt) => opt.name);
            this.lead_filter = true;
            this.data = this.originalData;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //--filter
        closeLeadFilter(searchData) {
            // console.log(searchData, 'What is happening.....!');
            if (searchData) {
                const keysData = Object.keys(searchData);

                let filterTheData = this.data.filter(option => {
                    // Check if all search criteria match
                    return keysData.every(key => {
                        // if (key === 'date') {
                        //     return option.lead_date === searchData.date;
                        // }
                        if (key === 'customer') {
                            if (option.last_name) {
                                this.currentPage = 1;
                                return (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number) === searchData.customer;
                            } else {
                                this.currentPage = 1;
                                return (option.first_name + ' - ' + option.contact_number) === searchData.customer;
                            }
                        }
                        return true; // For other keys, consider it as matched
                    });
                });

                if (filterTheData.length > 0) {
                    this.filteredBy = searchData;
                    this.data = filterTheData;
                } else {
                    filterTheData = this.data.filter(option => {
                        return option.lead_date === searchData.date ||
                            option.customer === searchData.customer ||
                            option.assign_to.some(assignee => searchData.assign_to.includes(assignee)) ||
                            option.lead_type === searchData.type ||
                            option.lead_status === searchData.status;
                    });
                    if (filterTheData.length > 0) {
                        this.filteredBy = searchData;
                        this.data = filterTheData;
                    } else {
                        this.message = 'The filter does not match any records..!';
                        this.open_message = true;
                        this.data = this.originalData;
                    }
                }
            }
            this.lead_filter = false;
        },
        //---close message---
        closeMessage() {
            this.open_message = false;
        },
        //---reset filter--
        resetTheFilter() {
            this.data = this.originalData;
            this.filteredBy = {};
        },
        getCustomerList(page, per_page) {
            if (page == 1) {
                this.fetchCustomersList({ page, per_page });
                if (this.currentCustomersList && this.currentCustomersList.data) {
                    this.data = this.currentCustomersList.data;
                    this.pagination_data.customer = this.currentCustomersList.pagination;
                }
            } else {
                this.open_skeleton = true;
                axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        this.open_skeleton = false;
                        this.data = response.data.data;
                        this.originalData = response.data.data;
                        this.pagination_data.customer = response.data.pagination;
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);
                        this.open_skeleton = false;
                    });
            }
        },
        getCategoryList() {
            axios.get('/customer-categories', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data);
                    this.serviceCategories = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        formatDate(timestamp) {
            const date = new Date(timestamp);
            // Get the day, month, year, hours, and minutes
            const day = String(date.getDate()).padStart(2, '0'); // Ensures 2 digits
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
            const year = date.getFullYear();
            const hours = date.getHours() % 12 || 12; // Convert to 12-hour format
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const ampm = date.getHours() >= 12 ? 'PM' : 'AM';
            return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data);
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);
                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination_data.customer.last_page > this.pagination_data.customer.current_page && !this.open_skeleton_isMobile && this.pagination_data.customer.last_page > this.pagination_data.customer.current_page) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination_data.customer.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination_data.customer = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })

        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData) {
                this.backup_data = [...this.data];
                this.data = [selectedData];
            }
        },
        resetToSearch() {
            if (Array.isArray(this.backup_data) && this.backup_data.length > 0) {
                this.data = [...this.backup_data];
            }
        },
        //---initial store data---
        getInitialStoreData(store_data) {
            this.open_skeleton = false;
            this.data = store_data.data;
            this.originalData = store_data.data;
            this.pagination_data.customer = store_data.pagination;
        },
        //--send wahtsapp reminder
        openWhatsApp(record) {
            this.selected_customer = { ...record };
            this.openWhatsAppMessage = true;


            // let message_data = `Hi ${record?.first_name || 'Customer'} sales invoice your payment is due ${this.currentCompanyList && this.currentCompanyList.currency === 'INR' ? '\u20b9' : this.currentCompanyList.currency} ${record.balance_amount}. \nyou can pay to the \n${this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].bank_details ? this.currentInvoice[0].bank_details : this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].business_contact ? this.currentInvoice[0].business_contact : `Please contact support team`} \n by ${this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].name ? this.currentInvoice[0].name : 'Company'
            //     } `;
            // let encodedMessage = encodeURIComponent(message_data);
            // let phone = record?.contact_number || '';

            // // Check the user agent to determine the device type
            // const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            // let whatsappUrl = '';
            // if (/ Android | webOS | iPhone | iPad | iPod | BlackBerry | IEMobile | Opera Mini /i.test(userAgent)) {
            //     var message = encodeURIComponent(message_data);
            //     var whatsapp_url = `whatsapp://send?phone=+91${phone}&text=` + message;
            //     window.location.href = whatsapp_url;
            // }
            // else if (/ iPad | iPhone | iPod /.test(userAgent) && !window.MSStream) {
            //     // iOS devices
            //     whatsappUrl = `whatsapp://send?phone=+91${phone}&text=${encodedMessage}`;
            //     // Open WhatsApp in a new window or tab
            //     window.open(whatsappUrl, '_blank');
            // } else {
            //     // Default to WhatsApp Web for other devices
            //     whatsappUrl = `https://web.whatsapp.com/send?phone=+91${phone}&text=${encodedMessage}`;
            //     // Open WhatsApp in a new window or tab
            //     window.open(whatsappUrl, '_blank');
            // }
        },
        closeWhatsappMessage() {
            this.openWhatsAppMessage = false;
            this.selected_customer = {};
        },
        navigateToWhatsApp() {
            this.$router.push({ name: 'whatsappsetting' });
        },
        //---validate the roles
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        filterDataBy(type, key) {
            this.showSortIcons(false, type, key);
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'customermanagement' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'customermanagement', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'customermanagement' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'customermanagement', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        closeAllModals() {
            // Close all modals
            this.showModal_customer = false;
            this.open_confirmBox = false;
            this.open_message = false;
            this.lead_filter = false;
        },
        //----import customer---
        openImportModal() {
            this.isImportModalOpen = true;
        },
        async closeImportModal() {
            this.isImportModalOpen = false;
            await this.fetchApiUpdates();
            this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
        },
        //--short icon for filter---
        showSortIcons(data, type, key) {
            if (data) {
                this.short_visible.forEach(opt => {
                    if (opt.label === data.field) {
                        opt.visible = true;
                    }
                });
            } else if (type && key) {
                this.short_visible.forEach(opt => {
                    if (opt.label === key) {
                        opt.type = type;
                    } else {
                        opt.type = '';
                    }
                });
            }
        },
        hideSortIcons(data) {
            if (data) {
                this.short_visible.forEach(opt => {
                    if (opt.label === data.field) {
                        opt.visible = false;
                    }
                });
            }
        },
        //--reload the table data----
        refreshDataTable() {
            // this.open_skeleton = true;
            this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
            this.showSortIcons(false, 'type', 'key');
        },
        //--filter--
        getSortType(column) {
            if (column && this.short_visible && this.short_visible) {
                // Find the relevant short_visible entry
                const shortVisibleEntry = this.short_visible.find(
                    (entry) => entry.label === column.field
                );

                if (!shortVisibleEntry) {
                    return ''; // Default sort type if not found
                }

                // Determine sort type based on current type
                if (shortVisibleEntry.type === '') {
                    this.filterDataBy('asc', column.field);
                } else if (shortVisibleEntry.type === 'asc') {
                    this.filterDataBy('desc', column.field);
                } else if (shortVisibleEntry.type === 'desc') {
                    this.filterDataBy('asc', column.field);
                }
            }
        },
        //---on key press to make pagination--
        handleKeyPress(event) {
            if (event.key === "ArrowRight" && this.currentPage < this.pagination_data.customer.last_page) {
                this.currentPage++;
            } else if (event.key === "ArrowLeft" && this.currentPage > 1) {
                this.currentPage--;
            }
        },
        handleClick(column, record, event) {
            if (column == 'balance_amount') {
                event.stopPropagation(); // Prevent click event for this column
                return;
            } else {
                this.viewRecord(record);
            }
        },
        handleLoadingState(index) {
            if (index === this.data.length - 1) {
                this.time_loading = false; // Stop loading once the last index is reached
            } else {
                this.time_loading = true; // Continue loading for other indices
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        this.updateIsMobile(); // Initial check
        this.fetchCompanyList();
        this.initialize();
        this.fetchWhatsappList();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        window.addEventListener('resize', this.updateIsMobile);
        // You can also send data when the component is mounted
        this.$emit('dataToParent', this.originalData);

        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        const view = localStorage.getItem('customer_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        this.time_loading = true;
        setTimeout(() => {
            this.time_loading = false;
        }, 800);
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        if (this.isMobile) {
            this.items_category = 'list';
        }
        //---get customer list---
        // this.getCustomerList(this.currentPage, this.recordsPerPage);
        if (this.currentCustomersList && this.currentCustomersList.data && this.currentCustomersList.data.length > 0) {
            this.open_skeleton = true;
            this.getInitialStoreData(this.currentCustomersList);
            this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
        } else {
            if (this.currentCustomersList && Object.keys(this.currentCustomersList).length == 0) {
                this.open_skeleton = true;
                this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
            }
        }
        //--get customer category---
        // this.getCategoryList();
        if (this.currentCustomerCategoryList && this.currentCustomerCategoryList.data && this.currentCustomerCategoryList.data.length > 0) {
            this.serviceCategories = this.currentCustomerCategoryList.data;
        } else {
            this.fetchCustomerCategoryList();
        }
        //--invoice setting---
        if (this.currentInvoice && this.currentInvoice.length === 0) {
            this.fetchInvoiceSetting();
        } else {
            this.fetchInvoiceSetting();

        }
        //---create short list array--
        this.short_visible = ['first_name', 'balance_amount'].map((field) => ({
            label: field,
            type: '',
            visible: false,
        }));
        // Add and remove event listeners
        window.addEventListener("keydown", this.handleKeyPress);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        window.removeEventListener('resize', this.updateIsMobile);
        window.removeEventListener("keydown", this.handleKeyPress);
    },
    watch: {
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    this.getCustomerList(1, newValue);
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                this.getCustomerList(newValue, this.recordsPerPage);
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('customer_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentCustomersList: {
            deep: true,
            handler(newValue) {
                this.getInitialStoreData(newValue);
            }
        },
        currentCustomerCategoryList: {
            deep: true,
            handler(newValue) {
                this.serviceCategories = newValue.data;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchCustomersList({ page: 1, per_page: this.recordsPerPage });
                this.fetchCustomerCategoryList();
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        showModal_customer: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        lead_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        }
    },
}
</script>

<style scoped>
.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
    }
}
</style>