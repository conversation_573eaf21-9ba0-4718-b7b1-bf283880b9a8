// Import Firebase modules from npm package
import { initializeApp } from 'firebase/app';
import { getMessaging, getToken, isSupported, onMessage } from 'firebase/messaging';
import axios from 'axios';

// Firebase configuration options
const options = {
    firebaseConfig: {
        apiKey: "AIzaSyCjqL_bt_hQyZQz7YBhTyG-2HtT0PUfHK4", 
        authDomain: "tracknew-b2a8d.firebaseapp.com",       
        projectId: "tracknew-b2a8d",       
        storageBucket: "tracknew-b2a8d.appspot.com",       
        messagingSenderId: "419327651932",       
        appId: "1:419327651932:web:ba3b19e891deaed0647699",
    },
    vapid_public_key: "BGyYfzZqIjAxNii5TXupxFLITVcpZyKl8IycsiZj4OCecv838vjQOBEHxul8MQfh5e-TWFZQfWqpsQMIh650T4M",
};

// Initialize Firebase and Messaging
async function initFirebase() {
    const firebaseApp = initializeApp(options.firebaseConfig);

    const supported = await isSupported();
    if (!supported) {
        console.warn("This browser doesn't support Firebase Messaging.");
        return;
    }

    const messaging = getMessaging(firebaseApp);

    // Handle foreground messages
    onMessage(messaging, (payload) => {
        // console.log('Foreground message received:', payload);
        // Use the Notification API to display the notification
    if (Notification.permission === 'granted') {
        new Notification(payload.notification.title, {
            body: payload.notification.body,
            icon: payload.notification.icon || '/default-icon.png', // Optional: use a default icon if one isn't provided
        });
    }
    });

    try {
        const token = await getToken(messaging, {
            vapidKey: options.vapid_public_key,
        });

        if (token) {
            console.log('FCM token:', token);
           await sendTokenToServer(token);
        } else {
            console.warn('No registration token available.');
        }
    } catch (error) {
        console.error('Error getting FCM token:', error);
    }
}

// Register Service Worker
// Register Service Worker with an additional check to avoid undefined scriptURL
async function registerSW() {
    if ('serviceWorker' in navigator) {
        const registrations = await navigator.serviceWorker.getRegistrations();
        const isRegistered = registrations.some(reg => reg.active && reg.scriptURL && reg.scriptURL.includes('firebase-messaging-sw.js'));

        if (isRegistered) {
            console.log('Service worker already registered.');
            return;
        }

        return navigator.serviceWorker.register('/firebase-messaging-sw.js')
            .then((registration) => {
                console.log('Service Worker registered:', registration.scope);
            })
            .catch((error) => {
                console.error('Service Worker registration failed:', error);
            });
    }
}


// Send token to server
async function sendTokenToServer(token) {    
    try {
        // const response = await fetch("auth/token-register", {
        //     method: "POST",
        //     headers: { 'Content-Type': 'application/json' },
        //     body: JSON.stringify({
        //         fcm_token: token,
        //         website: options.site_name,
        //         browser: getBrowserName(),
        //     }),
        // });
    //     if (response.status === 200) {
    //         console.log('Token sent successfully.');
    //     } else {
    //         console.error('Failed to send token:', response.statusText);
    //     }
    // } catch (error) {
    //     console.error('Error sending token:', error);
    // }
        axios.post('/auth/token-register', { fcm_token: token })
        .then(response => {
            if (response.status === 200) {
                        console.log('Token sent successfully.');
                    } else {
                        console.error('Failed to send token:', response.statusText);
                    }
          })
          .catch(error => {
            // Handle error
            console.error('Error:', error);
            return error;
          }); 
    } catch (error) {
      console.error('Error fetching item list:', error);
    }       
}

// Utility to get browser name
function getBrowserName() {
    const agent = window.navigator.userAgent.toLowerCase();
    if (agent.indexOf("edge") > -1) return "Edge";
    if (agent.indexOf("chrome") > -1) return "Chrome";
    if (agent.indexOf("firefox") > -1) return "Firefox";
    if (agent.indexOf("safari") > -1) return "Safari";
    return "Unknown";
}

// Start Firebase push notifications
async function startFirebasePush() {    
    if ('Notification' in window) {
        const permission = await Notification.requestPermission();        
         // Check if permission is granted and if localStorage contains the required key and user_id
        const trackNewData = localStorage.getItem('track_new'); //--user login status
        const local_allow_user = JSON.parse(localStorage.getItem('notification')); //--user allow status       
        if (permission === 'granted' && trackNewData && local_allow_user) {
            await registerSW();
            await initFirebase();
        } else {
            console.warn('Notifications permission denied or dismissed.');
        }
    } else {
        console.warn('This browser does not support notifications.');
    }
}
async function showAgain() {
    if ("permissions" in navigator) {
        navigator.permissions.query({
            name: "notifications"
        }).then(function(notificationPerm) {
            notificationPerm.onchange = function() {
                if (notificationPerm.state === "granted") initFirebaseMessagingRegistration();
            };
        });
    }
}

export { startFirebasePush, registerSW, initFirebase };
