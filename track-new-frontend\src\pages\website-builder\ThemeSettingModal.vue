<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-y-auto">
        <div class="bg-white w-full sm:w-3/4 transform transition-transform ease-in-out duration-300 rounded overflow-y-auto h-screen"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex justify-between items-center py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-2 text-lg">Theme Setting</h2>
                <p class="close pr-5" @click="closeModal">&times;</p>
            </div>
            <div class="bg-white rounded-lg shadow-lg w-full p-3 sm:p-6 relative">
                <div v-if="formFields">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-2 lg:gap-6">
                        <div v-for="(field, index) in Object.keys(formFields)" :key="index" class="mb-4">
                            <label :for="field" class="block text-sm font-bold text-gray-700">{{
                                formFields[field].label
                            }}</label>

                            <!-- Render text input type -->
                            <div v-if="formFields[field].type === 'text'">
                                <input v-model="formData[field]" :id="field" :name="field" type="text"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    required />
                            </div>
                            <div v-if="formFields[field].type === 'dropdown'">
                                <select v-model="formData[field]" :id="field" :name="field"
                                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500"
                                    required>
                                    <option v-for="(option, optionIndex) in formFields[field].options"
                                        :key="optionIndex" :value="option">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>

                            <!-- Render dropdown with image options as radio buttons -->
                            <div v-if="formFields[field].type === 'radio'">
                                <div class="flex flex-wrap gap-4 lg:col-span-2">
                                    <div
                                        :class="`grid grid-cols-3 sm:grid-cols-${Object.keys(formFields[field].options[0]).length > 4 ? 4 : Object.keys(formFields[field].options[0]).length} gap-3`">
                                        <div v-for="(option, optionIndex) in Object.keys(formFields[field].options[0])"
                                            :key="optionIndex" class="relative flex items-center cursor-pointer">
                                            <input type="radio" :id="`${field}_${optionIndex}`" :name="field"
                                                :value="optionIndex" v-model="formData[field]" />
                                            <label :for="`${field}_${optionIndex}`" class="flex flex-col items-center">
                                                <img :src="formFields[field].options[0][optionIndex]"
                                                    :alt="'Theme ' + optionIndex"
                                                    class="w-20 h-20 lg:w-24 lg:h-24 object-cover border-2 border-transparent hover:border-indigo-500 transition duration-300" />
                                                <span class="mt-2 text-sm">{{ option.label }}</span>
                                            </label>
                                            <button
                                                @click="openTheImageModal(formFields[field].options[0][optionIndex])"
                                                class="absolute -right-6 -top-2 text-violet-700 hover:text-violet-600"
                                                title="view" :key="optionIndex">
                                                <font-awesome-icon icon="fa-solid fa-eye" class="pr-1" /></button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Render color picker -->
                            <div v-if="formFields[field].type === 'color'">
                                <input v-model="formData[field]" :id="field" :name="field" type="color"
                                    class="mt-1 block w-full px-3 py-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500" />
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end py-2">
                        <button type="button" @click="closeModal"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded mr-2">
                            Cancel
                        </button>
                        <button type="button" @click="submitForm" class="px-4 py-2 bg-blue-500 text-white rounded">
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <displayImage :showModal="showImageModal" :showImageModal="showImageModalUrl" @close-modal="closeTheImageModal">
        </displayImage>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import displayImage from '@/components/supporting/dialog_box/displayImage.vue';
export default {
    name: 'themeSetting',
    props: {
        showModal: Boolean,
        editData: Object,
        selected_template: Object,
        user_id: {
            type: [Number, String],
        },
        theme_settings: {
            type: [Object, String],
        }
    },
    components: {
        displayImage
    },
    data() {
        return {
            //---display image--
            showImageModal: false,
            showImageModalUrl: null,

            isOpen: false,
            formFields: {},
            formData: {},
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',

            //--form fields---
            // header_theme: { label: 'Header Theme', type: 'radio', options: [{ 0: 'https://buziness.me/storage/temp1.PNG', 1: 'https://buziness.me/storage/temp1.PNG', 2: 'https://buziness.me/storage/temp1.PNG' }] },
            //     slider_theme: { label: 'Slider Theme', type: 'radio', options: [{ 0: '/images/service_page/Add_user.png', 1: '/images/service_page/Add_user.png', 2: '/images/service_page/Add_user.png', 3: 'https://buziness.me/storage/temp1.PNG' }] },
            //     slider_effect: { label: 'Slider Effect', type: 'dropdown', options: ["slider", "slide", "fade", "flip", "cube", "coverflow", "cards"] },
            //     services_theme: { label: 'Services Theme', type: 'radio', options: [{ 0: '/images/service_page/Add.png', 1: '/images/service_page/Add.png', 2: '/images/service_page/Add.png', 3: '/images/service_page/Add.png', 4: '/images/service_page/Add.png' }] },
            //     product_theme: { label: 'Product Theme', type: 'radio', options: [{ 0: '/images/service_page/address.png', 1: '/images/service_page/address.png', 2: '/images/service_page/address.png', 3: '/images/service_page/address.png', 4: '/images/service_page/address.png' }] },
            //     gallery_theme: { label: 'Gallery Theme', type: 'radio', options: [{ 0: '/images/service_page/amc_payment.png', 1: '/images/service_page/amc_payment.png', 2: '/images/service_page/amc_payment.png', 3: '/images/service_page/amc_payment.png', 4: '/images/service_page/amc_payment.png' }] },
            //     testimonial_theme: { label: 'Testimonial Theme', type: 'radio', options: [{ 0: '/images/service_page/anniversary.png', 1: '/images/service_page/anniversary.png', 2: '/images/service_page/anniversary.png' }] },
            //     webpage_bg_color: { label: 'Website Background Color', type: 'color' }, webpage_header_color: { label: 'Header Text Color', type: 'color' }, button_primary_color: { label: 'Header Text Color', type: 'color' }, button_secondary_color: { label: 'Header Text Color', type: 'color' }, headbar_bg_color: { label: 'Header Text Color', type: 'color' },
            //     headbar_text_color: { label: 'Header Text Color', type: 'color' }, headbar_selected_color: { label: 'Header Text Color', type: 'color' }
            //---form data--
            // header_theme: 0, slider_theme: 0, slider_effect: "slider", services_theme: 0, product_theme: 0, gallery_theme: 0, testimonial_theme: 0,
            // webpage_bg_color: '#f3f4f6', webpage_header_color: '#1a73e8', button_primary_color: '#4caf50', button_secondary_color: '#ffffff', headbar_bg_color: '#3f51b5',
            // headbar_text_color: '#ffffff', headbar_selected_color: '#ff5722'
        }
    },
    methods: {
        closeModal(data) {
            this.isOpen = false;
            this.$emit('close-modal', data ? data : null);
        },
        submitForm() {
            // console.log('Form submitted:', this.formData);
            let sentData = {
                theme_settings: JSON.stringify(this.formData)
            };

            if (sentData) {
                // Call the API to register the website with the provided data
                axios.put(`/company-sites/${this.user_id ? this.user_id : ''} `, sentData)
                    .then(response => {
                        this.message = `Theme Setting was updated Successfully..!`;
                        this.type_toaster = 'success';
                        this.show = true;
                        this.closeModal(true);
                    })
                    .catch(error => {
                        console.error("Error registering website:", error);
                        this.message = `Please select website template..!`;
                        this.type_toaster = 'warning';
                        this.show = true;

                    });
            }
        },
        openTheImageModal(image) {
            this.showImageModal = true;
            this.showImageModalUrl = image;
        },
        closeTheImageModal() {
            this.showImageModal = false;
            this.showImageModalUrl = null;
        }
    },
    watch: {
        showModal(newVal) {
            setTimeout(() => {
                this.isOpen = newVal;
                if (newVal && this.selected_template) {
                    this.show = false;
                    if (this.selected_template.template) {
                        let { color_scheme } = this.selected_template.template;
                        if (color_scheme) {
                            let { formFields } = JSON.parse(color_scheme);
                            this.formFields = formFields;
                        }
                        if (this.theme_settings) {
                            let parse_data = JSON.parse(this.theme_settings);
                            this.formData = parse_data;
                        }
                    }
                }
            }, 100);
        },
    }
}
</script>

<style scoped>
/* Styling for the form, radio buttons, and labels */
</style>
