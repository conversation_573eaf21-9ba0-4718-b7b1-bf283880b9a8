<template>
    <div :class="{ 'manualStyle text-sm mt-[10px] sm:mb-[60px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        class="custom-scrollbar-hidden" ref="scrollContainer" @scroll="handleScroll">
        <div class="my-custom-margin">
            <!--new design header-->
            <div v-if="!isMobile" class="flex justify-between m-1 mt-5">
                <div class="flex mr-2 space-x-4">
                    <button @click="addWarehouse" :class="{ 'mr-2': isMobile }"
                        class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center">
                        <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
                        <span v-if="!isMobile" class="text-center">New Warehouse</span>
                    </button>
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="info-msg px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200"
                            @click="toggleView" :title02="'Table view'"
                            :class="{ 'bg-white': items_category === 'tile' }">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200 info-msg"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="'Card view'">

                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-1" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <!----Filter Model-->
                <!--Setting-->
                <div class="flex icon-color">
                    <div class="flex icon-color">
                        <div v-if="!open_skeleton"
                            class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                            <p class="text-gray-700">Total Warehouse
                                :</p>
                            <p class="font-semibold pl-1">
                                {{ pagination.total }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton" class="text-sm m-1 mt-5">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                    </div>
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto pb-[70px]">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ 'hidden': !column.visible || column.field === 'editing' }"
                                        class="px-2 py-2 table-border">
                                        <p>{{ column.label }}</p>
                                    </th>
                                    <th class="px-2 py-2 leading-none table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-20 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in paginatedData" :key="index"
                                    class="cursor-pointer hover:bg-gray-200">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex"
                                        :class="{ 'hidden': !column.visible || column.field === 'editing' }"
                                        class="px-1 py-2 table-border" @click="startEdit(record)">
                                        <!-- Text display for all columns -->
                                        <span>{{ record[column.field] }}</span>

                                    </td>
                                    <td class="px-2 py-2 text-center table-border">
                                        <div class="flex justify-center items-center relative">
                                            <div class="flex relative">
                                                <button v-if="!record.editing" @click="startEdit(record)" title="Edit"
                                                    class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-solid fa-pencil"
                                                        style="color: #3b82f6;" />
                                                </button>
                                                <button v-if="!record.editing && checkRoles(['admin'])"
                                                    @click="confirmDelete(index)" title="Delete"
                                                    class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-regular fa-trash-can"
                                                        style="color: #ef4444" />
                                                </button>
                                                <!-- <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-[90px] absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 py-1 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">

                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                    <td>
                                        <button class="text-green-600 text-md font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="addWarehouse">
                                            + Add Warehouse
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 sm:gap-4 custom-scrollbar-hidden">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <div
                                class="max-w-md mx-auto bg-white rounded-xl border border-gray-300 overflow-hidden md:max-w-2xl shadow-lg">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center px-4 py-2">
                                    <!-- Left Side (Can be your dynamic content) -->
                                    <!-- <div class="text-sm text-red-500">
                                <p>{{ calculateDaysAgo(formattedDate(record.created_at)) }}</p>
                            </div> -->
                                    <!-- Customer Details (Can be your dynamic content) -->
                                    <div class="flex items-center">
                                        <div class="h-12 w-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                            :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                            <font-awesome-icon icon="fa-solid fa-warehouse" size="lg" />
                                        </div>
                                        <div>
                                            <h4 class="text-lg leading-6 font-medium text-gray-900">
                                                {{ record['name'] ? record['name'] : '' }}</h4>
                                        </div>
                                    </div>

                                    <!-- Right Side (Actions) -->
                                    <div class="flex justify-center items-center relative">
                                        <div class="relative">
                                            <button @click.stop="displayAction(index)"
                                                class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                            </button>
                                        </div>
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-[90px] right-0 absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="startEdit(record)"
                                                        class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" size="lg" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                        <span class="px-2">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <!--next line-->
                                <div class="mx-4 bg-gray-100 rounded px-2 py-2 mb-2">
                                    <!--address-->
                                    <div class="block">
                                        <p class="text-sm text-gray-700 font-semibold">Address:</p>
                                        <p>{{ record['address'] ? record['address'] : ''
                                            }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--no data found-->
                        <div v-if="pagination && this.pagination.last_page > 0 && this.pagination.last_page == this.pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>

                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="totalPages && !isMobile">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>

                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none overflow-auto">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span>
                                    </button>
                                </li>
                                <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                            pageNumber
                                        }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === totalPages, 'bg-teal-600 hover:bg-teal-500': currentPage !== totalPages }">
                                    <button @click="updatePage(currentPage + 1)" :disabled="currentPage === totalPages"
                                        class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!---in mobile view create new warhouse-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="addWarehouse" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <!-- <importModal :is-open="isImportModalOpen" @close="closeImportModal" @collectData="collectImportedData">
        </importModal> -->
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <warehouse :show-modal="open_warehouse" :type="type" :editData="edit_data" @close-modal="closeWarehouse"
            :companyId="companyId">
        </warehouse>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'warehouse'"></bottombar> -->
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import confirmbox from '../../dialog_box/confirmbox.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import warehouse from '../../dialog_box/warehouse.vue';
// import bottombar from '../../dashboard/bottombar.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
    name: 'warehouse_home',
    emits: ['updateIsOpen', 'dataToParent'],
    components: {
        confirmbox,
        dialogAlert,
        warehouse,
        // bottombar
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            service_customer: '/images/service_page/Customer.png',
            service_add_group: '/images/service_page/Add_group.png',
            import_icon: '/images/service_page/import.png',
            outline_img: '/images/service_page/Ellipse.png',
            table_view: '/images/service_page/tabler_eye.png',
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            filter_icon: '/images/customer_page/filter.png',
            setting_icon: '/images/customer_page/settings.png',
            info_icon: '/images/customer_page/info.png',
            refresh_icon: '/images/customer_page/refresh.png',
            product_icon: '/images/customer_page/product.png',
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            length_category: null,
            serviceCategories: null,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataList: [],
            columns: [],
            data: [],
            originalData: [],
            isImportModalOpen: false,
            open_message: false,
            message: '',
            open_warehouse: false,
            type: null,
            edit_data: null,
            //--search
            searchQuery: '',
            showSuggestions: false,
            selectedIndex: 0,
            //---api integration--
            companyId: null,
            userId: null,
            pagination: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 10,
            gap: 5,
            now: null,
            open_loader: false,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            now: null,
            open_skeleton_isMobile: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
        };
    },
    computed: {
        ...mapGetters('warehouseList', ['currentWarehouseList']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        paginatedData() {
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                this.length_category = filteredData.length;

                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                // return Math.ceil(totalFilteredRecords / this.recordsPerPage);
                return this.pagination.last_page;
            }
        },
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data) {
                for (const key in this.data[0]) {
                    if (key !== 'id' && key !== 'company_id' && key !== 'created_at' && key !== 'updated_at' && key !== 'deleted_at') {
                        const label = formatLabel(key);
                        fields.push({ label, field: key, visible: true });
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
        filteredWarehouse() {
            // console.log('it is executed..!!');
            // Filter customers based on search query
            if (this.searchQuery !== '') {
                const query = this.searchQuery.toLowerCase();
                return this.data.filter(product => {
                    const name = product.name.toLowerCase();
                    return (
                        name.includes(query)
                    );
                });
            } else {
                return this.data;
            }
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.totalPages); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        }
    },
    created() {
        // Create a copy of the original data when the component is created
        this.originalData = JSON.parse(JSON.stringify(this.data));
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        const view = localStorage.getItem('warehouse_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        if (this.isMobile) {
            this.items_category = 'list';
        }

    },
    methods: {
        ...mapActions('warehouseList', ['fetchWarehouseList']),
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        //--add ware house--
        addWarehouse() {
            this.open_warehouse = true;
            this.type = 'add';
            this.edit_data = null;

        },
        goToProduct() {
            // this.$router.push('/purchaseOrder');
            this.$router.push('/inventory');
        },
        goToPurchaseOrder() {
            // this.$router.push('/purchaseOrder');
            this.$router.push({
                name: 'purchase_order',
            });
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },

        // Start editing a record
        startEdit(record) {
            // console.log(record, 'Waht happening...!');
            this.type = 'edit';
            this.open_warehouse = true;
            this.edit_data = record;
        },

        //---delete the record
        deleteRecord() {
            this.open_loader = true;
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                let deleteVal = this.deleteIndex;
                //--delete data--
                axios.delete(`/warehouses/${this.data[deleteVal].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        this.updateKeyWithTime('warehouse_update');
                        // console.log(response.data);
                        this.data.splice(deleteVal, 1);
                        this.open_loader = false;
                    })
                    .catch(error => {
                        console.error('Error post', error);
                        this.open_loader = false;
                    })

            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        addServices() {
            // Call the method to add an empty row
            this.addEmptyRow();

            // Navigate to the last page
            this.currentPage = this.totalPages;
        },
        goBackToHomePage() {
            this.$router.push('/services');
        },
        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            try {
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //----print prechecklist
        formattedString(listData) {
            let returnData = Object.entries(listData)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ');
            return returnData;
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //----import model box--
        checkForDuplication(recordToCheck) {
            return this.data.some(record => (
                record.product_code === recordToCheck.product_code ||
                record.product_name === recordToCheck.product_name
            ));
        },
        openImportModal() {
            this.isImportModalOpen = true;
        },
        closeImportModal(collectedData) {
            if (collectedData) {
                const seenCodes = new Set();
                const uniqueData = collectedData.filter(record => {
                    const key = `${record.product_code}_${record.product_name}`;
                    if (seenCodes.has(key)) {
                        // alert(`Duplicate entry for Product Code: ${record.product_code} and Product Name: ${record.product_name}`);
                        this.open_message = true;
                        this.message = `Duplicate entry for Product Code: ${record.product_code} and Product Name: ${record.product_name}`;
                        return false;
                    }
                    seenCodes.add(key);
                    return true;
                });
                // console.log(uniqueData, 'What happening the data...!!!!');
                // Append the unique data to the existing data array
                let getDataUnique = uniqueData.filter((opt) => {
                    // console.log(!this.checkForDuplication(opt), 'OYYIYIUURTUR');
                    if (!this.checkForDuplication(opt)) {
                        this.data.push({ ...opt, editing: false });
                    }

                });
                // console.log(getDataUnique, 'WWWWWWWWWWWWW');
                this.originalData = this.data;
                this.isImportModalOpen = false;
            } else {
                this.isImportModalOpen = false;
            }
        },
        collectImportedData(collectedData) {
            // Check for duplication based on product code and name
            const isDuplicate = this.checkForDuplication(collectedData);

            if (isDuplicate) {
                // Show alert message for duplication
                // alert('Product code or name is duplicate!');
                this.open_message = true;
                this.message = 'Product code or name is duplicate!';
                return; // Prevent saving if duplicate
            }
            this.data = [...this.data, { ...collectedData, editing: false }];
            this.originalData = this.data;
            // Continue with saving the imported data
            // console.log(collectedData, 'How to achieve..!');
        },
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        closeWarehouse(data) {
            if (data) {
                if (this.type === 'edit') {
                    let findExist = this.data.findIndex((opt) => opt.name === data.name);
                    if (findExist !== -1) {
                        this.data.splice(findExist, 1, data);
                    }
                    else {
                        this.open_message = true;
                        this.message = 'Warehouse name is not matched..!'
                    }
                } else {
                    if (this.data && this.data.length > 0) {
                        this.data.unshift(data);
                    } else {
                        this.data = [{ ...data }];
                    }
                }
            }
            this.open_warehouse = false;
            this.edit_data = {};
        },
        //---search--
        //----dropdown---
        filterWarehouse() {
            if (this.searchQuery !== '' && this.searchQuery.length > 1) {
                // console.log(this.searchQuery, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            } else {
                this.showSuggestions = false;
                this.data = this.originalData;
            }
        },
        showDropdown() {
            // console.log('What happening go there data......!!!!!!');
            if (this.searchQuery !== '') {
                // Show dropdown on input focus
                this.showSuggestions = true;
                // Add a click event listener to the document to close the dropdown when clicking outside                
                // document.addEventListener('click', this.handleDocumentClick);
            }
        },
        hideDropdown() {
            // Hide dropdown on input blur
            this.showSuggestions = false;
            // Remove the click event listener when hiding the dropdown
            // document.removeEventListener('click', this.handleDocumentClick);
        },
        selectWarehouse(warehouse) {
            // Handle the selected customer (e.g., emit an event, update data, etc.)
            // console.log('Selected warehouse:', warehouse);
            this.searchQuery = warehouse.name; // Set the input value to the selected customer name
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            this.data = [{ ...warehouse }]
            this.selectedIndex = 0;
            // document.removeEventListener('click', this.handleDocumentClick); // Remove the event listener after selection
        },
        handleDocumentClick(event) {
            // Close the dropdown when clicking outside the input and dropdown
            const isClickInside = this.$refs.searchInput.contains(event.target) || this.$refs.searchInput.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.hideDropdown();
            }
        },
        isNumeric(str) {
            // Helper method to check if a string is numeric
            return /^\d+$/.test(str);
        },
        //--on press enter key--
        handleEnterKey() {
            // Check if filteredProductList has at least one item
            if (this.data.length > 0) {
                // Call selectedWarefilteredWarehouseData with the first item in filteredWarehouseList
                this.selectWarehouse(this.data[this.selectedIndex]);
                // this.selectedIndex = 0;
                this.$refs.searchInput.blur();
                this.showSuggestions = false;
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        getWarehouseList(page, per_page) {
            if (page == 1) {
                this.fetchWarehouseList({ page, per_page });
                if (this.currentWarehouseList && this.currentWarehouseList.data) {
                    this.data = this.currentWarehouseList.data;
                    this.pagination = this.currentWarehouseList.pagination;
                }
            } else {
                this.open_skeleton = true;
                axios.get('/warehouses', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        // console.log(response.data.data);
                        this.open_skeleton = false;
                        this.data = response.data.data;
                        this.originalData = this.data;
                        this.pagination = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error get purchase', error);
                        this.open_skeleton = false;
                    })
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },

        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile && this.pagination.last_page > this.pagination.current_page) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            // console.log('load nex data.....');
            // let send_data = {
            //     type: 'leads', q: this.status_select >= 0 && this.status_select !== 5 ? this.status_select : '', filter: this.followup_select >= 0 ? this.followup_select : '', per_page: this.recordsPerPage, page: page,
            //     customer_id: this.searchByCustomer.id ? this.searchByCustomer.id : ''
            // };
            // if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
            //     // console.log(this.filteredBy, 'helllo');
            //     if (this.filteredBy.customer_id) {
            //         send_data.customer_id = this.filteredBy.customer_id
            //     }
            //     if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
            //         send_data.employer_id = this.filteredBy.assign_to[0].id;
            //     }
            //     if (this.filteredBy.type) {
            //         send_data.category = this.filteredBy.type;
            //     }
            // }
            // // console.log(this.category_type !== null && this.category_type !== 'all', 'Waht happening...!!!');
            // if (this.category_type !== null && this.category_type !== 'all') {
            //     send_data.category_id = this.category_type;
            // }
            // axios.get('/searchs', { params: { ...send_data } })
            axios.get('/warehouses', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })

        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---initialize store data
        getInitialData(store_data) {
            this.open_skeleton = false;
            this.data = store_data.data;
            this.originalData = this.data;
            this.pagination = store_data.pagination;
        },
        //--validate the role--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        closeAllModals() {
            // Close all modals
            this.open_confirmBox = false;
            this.open_message = false;
            this.open_warehouse = false;
        },
        //---refresh--
        refreshDataTable() {
            this.getWarehouseList(1, this.recordsPerPage);
            this.currentPage = 1;
        },
    },
    watch: {
        originalData: {
            deep: true,
            handler(newValue) {
                // Automatically send data to the parent when dataToSend changes
                this.$emit('dataToParent', newValue);
            }
        },
        searchedData: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'RRRRR');
                if (!this.isEmptyObject(newValue)) {
                    this.data = [{ ...newValue }];
                }
                else {
                    this.data = this.originalData;
                }
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('warehouse_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentWarehouseList: {
            deep: true,
            handler(newValue) {
                this.getInitialData(newValue);
                this.open_loader = false;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.getWarehouseList(1, this.recordsPerPage);
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    this.getWarehouseList(1, newValue)
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },

        currentPage: {
            deep: true,
            handler(newValue) {
                this.getWarehouseList(newValue, this.recordsPerPage);
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_warehouse: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        //---get list data
        // this.getWarehouseList();
        if (this.currentWarehouseList && this.currentWarehouseList.data && this.currentWarehouseList.data.length > 0) {
            this.open_skeleton = true;
            this.getInitialData(this.currentWarehouseList);
            this.getWarehouseList(1, this.recordsPerPage);

        } else {
            if (this.currentWarehouseList && Object.keys(this.currentWarehouseList).length == 0) {
                this.open_skeleton = true;
                this.getWarehouseList(1, this.recordsPerPage);
            }
        }
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
    },

}
</script>

<style scoped>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

/* Add these styles */
.w-full.text-center.border-none:focus,
.w-full.text-center.border-none:focus-within {
    border: none;
    /* Remove border on focus or focus within */
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}
</style>