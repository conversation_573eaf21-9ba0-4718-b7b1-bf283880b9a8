<template>
    <div :class="{ 'manualStyle text-sm': isMobile, 'text-sm': !isMobile }" class="custom-scrollbar-hidden">
        <div class="my-custom-margin">
            <!-- Chat Header -->
            <div class="flex items-center justify-between py-1 border-b border-gray-300">
                <button v-if="open_overview" class="px-2 rounded-full hover:bg-gray-100" @click="closecustomerview">
                    <font-awesome-icon icon="fa-solid fa-arrow-left" class="text-gray-600 text-lg" />
                </button>
                <!-- Left: Profile Picture & Name + Phone -->
                <div class="flex items-center space-x-3">
                    <!-- Profile Avatar -->
                    <div
                        class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold bg-green-600">
                        {{ selected_customer.first_name.charAt(0) }}
                    </div>
                    <!-- Name & Phone -->
                    <div>
                        <div class="font-medium text-gray-900"> {{ truncatedName }}
                            <span v-if="selected_customer.gst_number" class="ml-2 text-green-500">B2B</span>
                            <span v-else class="ml-2 text-blue-500">B2C</span>
                            <button class="text-sm p-1 pl-2 items-center hover:text-blue-600" @click="editCustomer">
                                <font-awesome-icon icon="fa-solid fa-pencil" />
                            </button>
                        </div>
                        <div class="text-sm text-gray-500">{{ selected_customer.contact_number }}</div>
                    </div>
                </div>

                <!-- Right: 3-dot Menu -->
                <div class="flex items-center space-x-4">
                    <!--Setting-->
                    <div v-if="!isMobile" class="flex">
                        <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                            <div class="p-1 px-2 flex-shrink-0 cursor-pointer info-msg border rounded-l-full border-r-0 border-gray-500"
                                :class="{ 'bg-white': items_category === 'tile' }" @click="items_category = 'tile'"
                                :title02="`Table view`">
                                <font-awesome-icon v-if="items_category === 'tile'" icon="fa-solid fa-check"
                                    class="pr-1 text-green-600 font-bold" />
                                <font-awesome-icon icon="fa-solid fa-bars" />
                            </div>
                            <div class="p-1 px-2 rounded flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border rounded-r-full border border-gray-500"
                                :class="{ 'bg-white': items_category !== 'tile' }" @click="items_category = 'list'"
                                :title02="`Card view`">
                                <font-awesome-icon v-if="items_category !== 'tile'" icon="fa-solid fa-check"
                                    class="pr-1 text-green-600 font-bold" />
                                <font-awesome-icon icon="fa-solid fa-grip" />
                            </div>
                        </div>
                    </div>
                    <!--WhatApp-->
                    <button v-if="companywhatsapp" class="text-green-700 flex items-center" @click="openWhatsApp">
                        <font-awesome-icon icon="fa-brands fa-whatsapp" class="text-lg" />
                        <!-- <span v-if="!isMobile && !isTab" class="pl-1">Remainder</span> -->
                    </button>
                    <button v-if="!companywhatsapp" class="text-red-700 flex items-center" @click="navigateToWhatsApp">
                        <font-awesome-icon icon="fa-brands fa-whatsapp" class="text-lg" />
                        <!-- <span v-if="!isMobile && !isTab" class="pl-1">WhatsApp</span> -->
                    </button>
                    <!-- Call Button -->
                    <button class="p-0 sm:p-2 rounded-full hover:bg-gray-100"
                        @click="dialPhoneNumber(selected_customer.contact_number)">
                        <font-awesome-icon icon="fa-solid fa-phone" class="text-green-600 text-lg" />
                    </button>
                    <!-- Contact Info Button -->
                    <button @click="updateshowContactInfo" class="p-0 sm:p-2 rounded-full hover:bg-gray-100">
                        <font-awesome-icon icon="fa-solid fa-circle-info" class="text-gray-600 text-lg" />
                    </button>

                    <!-- Three-Dot Menu -->
                    <div ref="dropdownContainer" class="relative">
                        <button class="p-1 sm:p-2 rounded-full hover:bg-gray-100" @click="toggleDropdown">
                            <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" class="text-gray-600 text-lg" />
                        </button>
                        <!-- Dropdown Menu -->
                        <div v-if="dropdownOpen && currentFeatureList && currentFeatureList.length > 0"
                            class="absolute right-0 mt-2 w-48 bg-white border border-gray-300 text-gray-800 shadow-lg rounded-md z-50">
                            <button v-if="currentFeatureList[0].hasAccess"
                                @click="this.$emit('handleDropdownClick', 'Services')"
                                :class="{ 'hidden': checkPlanDetails('services') }"
                                class="block px-4 py-2 hover:bg-gray-200 w-full text-left">+ Services</button>
                            <button v-if="currentFeatureList[1].hasAccess"
                                @click="this.$emit('handleDropdownClick', 'Lead')"
                                :class="{ 'hidden': checkPlanDetails('leads') }"
                                class="block px-4 py-2 hover:bg-gray-200 w-full text-left">+ Lead</button>
                            <button v-if="currentFeatureList[2].hasAccess"
                                @click="this.$emit('handleDropdownClick', 'AMC')"
                                :class="{ 'hidden': checkPlanDetails('amcs') }"
                                class="block px-4 py-2 hover:bg-gray-200 w-full text-left">+ AMC</button>
                            <button v-if="currentFeatureList[3].hasAccess"
                                @click="this.$emit('handleDropdownClick', 'RMA')"
                                :class="{ 'hidden': checkPlanDetails('rmas') }"
                                class="block px-4 py-2 hover:bg-gray-200 w-full text-left">+ RMA</button>
                            <button v-if="currentFeatureList[4].hasAccess"
                                @click="this.$emit('handleDropdownClick', 'Sales')"
                                class="block px-4 py-2 hover:bg-gray-200 w-full text-left">+ Sales</button>
                            <button v-if="currentFeatureList[5].hasAccess"
                                @click="this.$emit('handleDropdownClick', 'Proforma')"
                                class="block px-4 py-2 hover:bg-gray-200 w-full text-left">+ Proforma</button>
                            <button v-if="currentFeatureList[6].hasAccess"
                                @click="this.$emit('handleDropdownClick', 'Estimation')"
                                class="block px-4 py-2 hover:bg-gray-200 w-full text-left">+ Estimation</button>
                            <button class="block px-4 py-2 hover:bg-gray-200 w-full text-left" @click="addPaymentIn"> +
                                Payment In
                            </button>
                            <button v-if="checkRoles(['admin'])" @click="this.$emit('confirmDelete', selected_customer)"
                                class="text-red-500 px-4 py-2 flex justify-left items-center hover:bg-gray-200 w-full text-left">
                                <font-awesome-icon icon="fa-regular fa-trash-can" style="color: #ef4444" />
                                <span class="px-2">Delete</span>
                            </button>
                        </div>
                    </div>
                </div>

            </div>

            <!--customer details-->
            <!-- <div v-if="customer_data"
                class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-3 bg-white border py-3 rounded-lg shadow-lg px-3 mt-2">

                <div v-if="customer_data.first_name" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Name</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.last_name ? customer_data.first_name + ' ' +
                        customer_data.last_name : customer_data.first_name }}</div>
                </div>
                <div v-if="customer_data['created_at']" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Created At</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm ">{{ formatDateTime(customer_data['created_at']) }}</div>
                </div>
                <div v-if="customer_data.contact_number" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Phone No</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div @click="dialPhoneNumber(customer_data.contact_number)"
                        class="w-1/2 text-sm cursor-pointer text-blue-600">{{
                            customer_data.contact_number }}</div>
                </div>
                <div v-if="customer_data.balance_amount !== undefined && customer_data.balance_amount >= 0"
                    class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Due Amount</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm "
                        :class="{ 'text-green-700': customer_data.balance_amount === 0, 'text-red-500': customer_data.balance_amount > 0 }">
                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                            currentCompanyList.currency }} {{
                            customer_data.balance_amount }}</div>
                </div>
                <div v-if="customer_data.email" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Email</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.email }}</div>
                </div>
                <div v-if="customer_data.address" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Address</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.address }}</div>
                </div>
                <div v-if="customer_data.state_name" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">State</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.state_name }}</div>
                </div>
                <div v-if="customer_data.district_name" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">District</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.district_name }}</div>
                </div>
                <div v-if="customer_data.city_name" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">City</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.city_name }}</div>
                </div>
                <div v-if="customer_data.pincode" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Pin-Code</span></div> <span
                        class="mr-1 font-bold ml-1">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.pincode }} </div>
                </div>
                <div v-if="customer_data.business_name" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Business Name</span></div> <span
                        class="mr-1 font-bold">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.business_name }}</div>
                </div>
                <div v-if="customer_data.gst_number" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">GST</span></div> <span
                        class="mr-1 font-bold">:</span>
                    <div class="w-1/2 text-sm ">{{ customer_data.gst_number }}</div>
                </div>
                <div v-if="customer_data.opening_balance >= 0" class="flex ">
                    <div class="font-bold w-1/4 justify-between"><span class="text-sm">Opening Balance</span></div>
                    <span class="mr-1 font-bold">:</span>
                    <div class="w-1/2 text-sm ">{{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                        '\u20b9'
                        : currentCompanyList.currency }} {{ customer_data.opening_balance }}</div>
                </div>
            </div> -->
            <div class="overflow-y-auto">
                <!--new design header-->
                <!-- 'border-b-4 border-blue-700': selected_category === opt.type,:class="{ 'bg-yellow-100': j === 0, 'bg-blue-100': j === 1, 'bg-green-100': j === 2, 'bg-red-100': j === 3, 'bg-lime-100': j === 4, 'bg-orange-100': j === 5, 'bg-teal-100': j === 6 }"-->
                <div v-if="!open_skeleton" class="flex justify-between">
                    <div class="flex justify-between overflow-auto custom-scrollbar-hidden px-2 text-xs"
                        v-if="category_list.length > 0">
                        <div v-for="(opt, j) in category_list" :key="j" class="mr-4 py-2 cursor-pointer"
                            :class="{ 'hidden': !opt.hasAccess || checkPlanDetails(opt.type) }">
                            <p class="whitespace-nowrap px-3 rounded py-1 bg-gray-200 rounded-full"
                                :class="{ 'bg-gray-500 text-gray-200': selected_category === opt.type, 'text-gray-700': selected_category !== opt.type }"
                                @click="selectCategory(opt)">
                                <span class="pr-1 capitalize">{{ opt.type === 'amcs' ? 'AMC' : opt.type === 'rmas' ?
                                    'RMA' :
                                    opt.type }}</span>
                                <span>({{ opt.total }})</span>
                            </p>
                        </div>
                    </div>
                </div>

                <div class="mt-5 overflow-y-auto" :style="{ height: `calc(100vh - 160px)` }">
                    <serviceList v-if="selected_category === 'services'" :isMobile="isMobile" :data="data" :now="now"
                        :companyId="companyId" :userId="userId" :selected_category="selected_category"
                        :items_category="items_category" :open_skeleton="open_skeleton" @openconfirmbox="confirmDelete"
                        :confirm_del="confirm_del" :updateModalOpen="updateModalOpen"
                        @updateIsOpenData="emitUpdateIsOpen" :showContactInfo="showContactInfo">
                    </serviceList>
                    <leadsList v-if="selected_category === 'leads'" :isMobile="isMobile" :data="data" :now="now"
                        :companyId="companyId" :userId="userId" :selected_category="selected_category"
                        :items_category="items_category" :open_skeleton="open_skeleton" @openconfirmbox="confirmDelete"
                        :confirm_del="confirm_del" :updateModalOpen="updateModalOpen"
                        @updateIsOpenData="emitUpdateIsOpen" :showContactInfo="showContactInfo">
                    </leadsList>
                    <amcsList v-if="selected_category === 'amcs'" :isMobile="isMobile" :data="data" :now="now"
                        :companyId="companyId" :userId="userId" :selected_category="selected_category"
                        :items_category="items_category" :open_skeleton="open_skeleton" @openconfirmbox="confirmDelete"
                        :confirm_del="confirm_del" :customer_data="customer_data" :update-data="getCustomerData"
                        :updateModalOpen="updateModalOpen" @updateIsOpenData="emitUpdateIsOpen"
                        :showContactInfo="showContactInfo">
                    </amcsList>
                    <rmasList v-if="selected_category === 'rmas'" :isMobile="isMobile" :data="data" :now="now"
                        :companyId="companyId" :userId="userId" :selected_category="selected_category"
                        :items_category="items_category" :open_skeleton="open_skeleton" @openconfirmbox="confirmDelete"
                        :confirm_del="confirm_del" :customer_data="customer_data" :updateModalOpen="updateModalOpen"
                        @updateIsOpenData="emitUpdateIsOpen" :currentCompanyList="currentCompanyList"
                        :showContactInfo="showContactInfo"></rmasList>
                    <salesList v-if="selected_category === 'sales'" :isMobile="isMobile" :data="data" :now="now"
                        :companyId="companyId" :userId="userId" :selected_category="selected_category"
                        :items_category="items_category" :open_skeleton="open_skeleton" @openconfirmbox="confirmDelete"
                        :confirm_del="confirm_del" :customer_data="customer_data" :updateModalOpen="updateModalOpen"
                        @updateIsOpenData="emitUpdateIsOpen" :currentCompanyList="currentCompanyList"
                        :showContactInfo="showContactInfo">
                    </salesList>
                    <proformasList v-if="selected_category === 'proformas'" :isMobile="isMobile" :data_list="data"
                        :now="now" :companyId="companyId" :userId="userId" :selected_category="selected_category"
                        :items_category="items_category" :open_skeleton="open_skeleton" @openconfirmbox="confirmDelete"
                        :confirm_del="confirm_del" :customer_data="customer_data" :updateModalOpen="updateModalOpen"
                        @updateIsOpenData="emitUpdateIsOpen" :currentCompanyList="currentCompanyList"
                        :showContactInfo="showContactInfo"></proformasList>
                    <estimationList v-if="selected_category === 'estimations'" :isMobile="isMobile" :data="data"
                        :now="now" :companyId="companyId" :userId="userId" :selected_category="selected_category"
                        :items_category="items_category" :open_skeleton="open_skeleton" @openconfirmbox="confirmDelete"
                        :confirm_del="confirm_del" :customer_data="customer_data" :updateModalOpen="updateModalOpen"
                        @updateIsOpenData="emitUpdateIsOpen" :currentCompanyList="currentCompanyList"
                        :showContactInfo="showContactInfo">
                    </estimationList>
                    <!--loader-->
                    <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                        :cols="items_category === 'tile' ? number_of_columns : showContactInfo ? 1 : 2"
                        :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>
                </div>
            </div>
            <Loader :showModal="open_loader"></Loader>
            <customerRegister :show-modal="showModal_customer" @close-modal="closeModal" :editData="customer_data"
                :type="typeOfRegister"></customerRegister>
            <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete">
            </confirmbox>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>
<script>
import jsPDF from 'jspdf';
import customerRegister from '../../dialog_box/customerRegister.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import serviceList from '../view_customer/servicesList.vue';
import leadsList from '../view_customer/leadsList.vue';
import amcsList from '../view_customer/amcsList.vue';
import salesList from '../view_customer/salesList.vue';
import estimationList from '../view_customer/estimationList.vue';
import { mapActions, mapGetters } from 'vuex';
import rmasList from '../view_customer/rmasList.vue';
import proformasList from '../view_customer/proformasList.vue';

export default {
    name: 'view_customers',
    emits: ['updateCustomerInfo', 'updateoverview', 'closecustomerview', 'updateIsOpen', 'confirmDelete', 'editcustomer', 'handleDropdownClick', 'navigateToWhatsApp', 'openWhatsApp'],
    props: {
        selected_customer: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean,
        showContactInfo: Boolean,
        open_overview: Boolean,
        currentLocalDataList: Object,
        selected_option: {
            type: [String, null]
        },
        companywhatsapp: Boolean,
        isTab: Boolean
    },
    components: {
        customerRegister,
        confirmbox,
        serviceList,
        leadsList,
        amcsList,
        salesList,
        estimationList,
        rmasList,
        proformasList
    },
    data() {
        return {
            isMobile: false,
            showModal_customer: false,
            isMouseOver: false,
            typeOfRegister: 'add',
            options: [2, 4, 6, 8, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            customer_data: null,
            customer_id: null,
            data: [],
            originalData: [],
            servicecategory_data: [],
            open_confirmBox: false,
            deleteIndex: null,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredData: [],
            columns: [],
            //---API integration--
            companyId: null,
            userId: null,
            category_list: [{ id: 1, type: 'services', total: 0 }, { id: 2, type: 'leads', total: 0 }, { id: 3, type: 'amcs', total: 0 }, { id: 4, type: 'rmas', total: 0 }, { id: 5, type: 'sales', total: 0 }, { id: 6, type: 'proformas', total: 0 }, { id: 7, type: 'estimations', total: 0 }],
            selected_category: null,
            open_loader: false,
            now: null,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 7,
            number_of_rows: 10,
            gap: 5,
            confirm_del: false,
            dropdownOpen: false,
            //--toaster---
            show: false,
            message: '',
            type_toaster: 'success',
            serviceIsGo: false,
        }
    },
    computed: {
        dynamicFields() {
            const fields = [];
            let order = [];
            if (this.selected_category === 'services') {
                order = ['created_at', 'problem_title', 'category', 'expected_date', 'service_code', 'assign_to', 'service_type', 'invoice_id', 'status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'leads') {
                order = ['lead_date', 'title', 'assign_to', 'assign_date', 'lead_type', 'follow_up', 'lead_status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'amcs') {
                order = ['created_at', 'title', 'assign_to', 'amc_payment_type', 'amc_details', 'number_of_interval', 'number_of_service', 'amc_status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'sales') {
                order = ['current_date', 'invoice_no', 'invoice_to', 'invoice_type', 'discount', 'due_amount', 'discount_type', 'shipping'];
            } else if (this.selected_category === 'estimations') {
                order = ['current_date', 'estimate_num', 'estimate_type', 'grand_total', 'invoice_id'];
            }

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data && this.data.length > 0) {
                order.forEach((key) => {
                    // Check if the key exists in the data object
                    if (this.data && this.data.length > 0) { //---&& this.data[0].hasOwnProperty(key)
                        const label = formatLabel(key);
                        if (key !== 'created_by' && key !== 'updated_by') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                });
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
        ...mapGetters('features_list', ['currentFeatureList']),
        ...mapGetters('companies', ['currentCompanyList']),
        truncatedName() {
            const fullName = `${this.selected_customer.first_name} ${this.selected_customer.last_name || ''}`;
            return fullName.length > 14 && this.isMobile
                ? fullName.slice(0, 14) + '...'
                : fullName;
        },
    },
    methods: {
        ...mapActions('features_list', ['fetchFeatureList']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        //--contact info-
        updateshowContactInfo() {
            this.$emit('updateCustomerInfo', !this.showContactInfo);
        },
        closecustomerview() {
            this.$emit('closecustomerview', false);
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.totalPages) {
                this.currentPage = pageNumber;
            }
        },
        //---edit customer data--
        editCustomer() {
            if (this.selected_customer) {
                // openModal();
                // this.showModal_customer = true;
                // this.typeOfRegister = 'edit';
                this.$emit('editcustomer', this.selected_customer);
            }
        },

        deleteRecord() {
            this.confirm_del = true;
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...');            
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.confirm_del = false;
        },
        addServices() {
            this.$emit('showAddService', null, 'add');
        },
        openModal() {
            this.typeOfRegister = 'add';
            this.showModal_customer = true;
        },
        closeModal() {
            this.showModal_customer = false;
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        generatePDF(invoiceNumber) {
            // Create a new instance of jsPDF
            const pdf = new jsPDF();

            // Add content to the PDF, for example:
            pdf.text(20, 20, `Invoice Number: ${invoiceNumber}`);

            // Save the PDF file
            pdf.save(`invoice_${invoiceNumber}.pdf`);
        },
        goBackToHome() {
            this.$router.go(-1);
        },
        goBack() {
            this.$router.go(-1);
        },
        reloadPage() {
            window.location.reload();
        },
        addNewOrder() {
            this.$router.push('/services');
        },
        //----new header functions
        //--setting
        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;
            if (this.isDropdownOpen) {
                // Add event listener when dropdown is opened
                document.addEventListener('click', this.handleOutsideClick);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },
        handleOutsideClick(event) {
            const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.toggleDropdown();
            }
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        // closeFilterModal() {
        //     this.showFilterModal = false;
        //     this.resetFilter();
        // },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // Implement logic to filter data based on filter values
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //--dial number---
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //--customer---
        getCustomerData() {
            this.open_skeleton = true;
            axios.get(`/customer-details/${this.customer_id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // Handle response
                    // console.log(response.data.data);
                    this.customer_data = response.data.data;
                    if (this.category_list) {
                        if (response.data.data.services && Array.isArray(response.data.data.services) && response.data.data.services.length > 0) {
                            this.category_list[0].total = response.data.data.services.length;
                        } else {
                            this.category_list[0].total = 0;
                        }
                        if (response.data.data.leads && Array.isArray(response.data.data.leads) && response.data.data.leads.length > 0) {
                            this.category_list[1].total = response.data.data.leads.length;
                        } else {
                            this.category_list[1].total = 0;
                        }
                        if (response.data.data.amcs && Array.isArray(response.data.data.amcs) && response.data.data.amcs.length > 0) {
                            this.category_list[2].total = response.data.data.amcs.length;
                        } else {
                            this.category_list[2].total = 0;
                        }
                        if (response.data.data.rmas && Array.isArray(response.data.data.rmas) && response.data.data.rmas.length > 0) {
                            this.category_list[3].total = response.data.data.rmas.length;
                        } else {
                            this.category_list[3].total = 0;
                        }
                        if (response.data.data.sales && Array.isArray(response.data.data.sales) && response.data.data.sales.length > 0) {
                            this.category_list[4].total = response.data.data.sales.length;
                        } else {
                            this.category_list[4].total = 0;
                        }
                        if (response.data.data.proformas && Array.isArray(response.data.data.proformas) && response.data.data.proformas.length > 0) {
                            this.category_list[5].total = response.data.data.proformas.length;
                        } else {
                            this.category_list[5].total = 0;
                        }
                        if (response.data.data.estimations && Array.isArray(response.data.data.estimations) && response.data.data.estimations.length > 0) {
                            this.category_list[6].total = response.data.data.estimations.length;
                        } else {
                            this.category_list[6].total = 0;
                        }
                    }
                    this.$emit('updateoverview', this.category_list);
                    this.data = this.customer_data[this.selected_category];
                    this.open_skeleton = false;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                    this.open_skeleton = false;
                });
        },
        //--get customer category---
        getCustomerCategory() {
            axios.get('/customer-categories', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data);
                    this.servicecategory_data = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },

        //--lease data--
        getDateStatusClass(fieldValue) {
            try {
                const parsedValue = typeof fieldValue === 'string' ? JSON.parse(fieldValue) : fieldValue;
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    const followUpDate = new Date(parsedValue[0].date_and_time);
                    const currentDate = new Date();

                    if (followUpDate < currentDate) {
                        return 'text-red-500';
                    } else {
                        return 'text-green-500';
                    }
                }
            } catch (error) {
                console.error('Error parsing date:', error);
            }
            return ''; // Default empty string if date cannot be parsed
        },
        getFollowUpDate(fieldValue) {
            // console.log(fieldValue, 'EEEEEEEEEEEEEEEEEEEEEEE');
            if (fieldValue !== '' && typeof fieldValue === 'string') {
                const parsedValue = JSON.parse(fieldValue);
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    return this.generateDate(parsedValue[0].date_and_time);
                }
            } else if (typeof fieldValue === 'object') {
                if (Array.isArray(fieldValue) && fieldValue.length > 0 && fieldValue[0].date_and_time) {
                    return this.generateDate(fieldValue[0].date_and_time);
                }
            }
        },
        generateDate(data) {
            // console.log(data, 'What about the data......');
            const dateTimeString = data;
            const date = new Date(dateTimeString);
            const formattedDate = date.toLocaleDateString('en-GB', { year: 'numeric', month: '2-digit', day: '2-digit' });
            const formattedTime = date.toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' });
            return formattedDate + ' ' + formattedTime;
        },
        //---record---
        startEdit(record) {
            this.$router.push({
                name: 'leadEdit',
                query: {
                    recordId: record.id
                }
            });
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        selectCategory(opt) {
            if (opt) {
                this.selected_category = opt.type;
                this.data = this.customer_data[this.selected_category];
                // console.log(this.customer_data[this.selected_category]);
            }
        },
        //---add payment in---
        addPaymentIn() {
            if (this.customer_data) {
                this.$router.push({
                    name: 'payment_in',
                    query: { customer_id: this.customer_data.id }
                });
            }
        },
        toggleDropdown() {
            this.dropdownOpen = !this.dropdownOpen;
            if (this.dropdownOpen) {
                document.addEventListener('click', this.handleClickOutside);
            } else {
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            const dropdownContainer = this.$refs.dropdownContainer;
            if (dropdownContainer && !dropdownContainer.contains(event.target)) {
                this.dropdownOpen = false;
            }
        },
        handleDropdownClick(option) {
            switch (option) {
                case 'Lead':
                    this.showLeadModal = true;
                    break;
                case 'AMC':
                    this.showAmcModal = true;
                    break;
                case 'RMA':
                    this.openModalRegister = true;
                    break;
                case 'Services':
                    this.open_service_category = true;
                    break;
                case 'Sales':
                    this.$router.push({
                        name: 'sales-invoice',
                        query: { type: 'add', customer_id: this.customer_id }
                    });
                    break;
                case 'Proforma':
                    this.$router.push({
                        name: 'addProformaInvoice',
                        params: { type: 'product' },
                        query: { type: 'add', customer_id: this.customer_id }
                    });
                    break;
                case 'Estimation':
                    this.$router.push({
                        name: 'addEstimation',
                        params: { type: 'product' },
                        query: { type: 'add', customer_id: this.customer_id }
                    });
                    break;
                default:
                    console.warn('Unhandled dropdown option:', option);
            }
            this.dropdownOpen = false;
        },
        //---add new----
        closeLeadModal(newData) {
            // console.log('helllllllo');
            if (newData) {
                this.getCustomerData();
                this.message = 'Lead created successfully...!';
                this.show = true;
            }

            this.showLeadModal = false;
        },
        closeAmcModal(newData) {
            if (newData) {
                this.getCustomerData();
                this.message = 'AMC created successfully...!';
                this.show = true;
            }
            this.showAmcModal = false;
        },
        closeTheModal(data) {
            if (data && data.id) {
                this.getCustomerData();
                this.message = 'RMA created successfully...!';
                this.show = true;
                this.openModalRegister = false;
            } else {
                this.openModalRegister = false;
            }
        },
        //---close category--
        closeCategory() {
            this.getCustomerData();
            this.open_service_category = false;
        },
        //---update data-----
        emitUpdateIsOpen(value) {
            this.$emit('updateIsOpen', value);
        },
        closeAllModals() {
            // Close all modals
            this.showModal_customer = false;
            this.open_confirmBox = false;
            this.showLeadModal = false;
            this.showAmcModal = false;
            this.openModalRegister = false;
            // Close all modals
            if (!this.serviceIsGo) {
                this.open_service_category = false;
            }
        },
        updateStatus() {
            this.serviceIsGo = true;
            this.$emit('updateIsOpen', false);
        },
        //--validate paln details---
        checkPlanDetails(option) {
            let currentPlan = this.currentCompanyList && this.currentCompanyList.plans ? this.currentCompanyList.plans : null;

            if (['services', 'leads', 'amcs', 'rmas'].includes(option) && currentPlan && [12, 13].includes(currentPlan.id)) {
                return true;
            } else { return false; }
        },
        //---validate the roles
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //--navigate to whatsapp--
        openWhatsApp() {
            this.$emit('openWhatsApp', this.selected_customer);
        },
        navigateToWhatsApp() {
            this.$emit('navigateToWhatsApp');
        }
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile();
        this.fetchCompanyList();
        window.addEventListener('resize', this.updateIsMobile);
        this.selected_category = 'services';
        this.customer_id = this.selected_customer.id + '';
        this.getCustomerData();
        this.fetchFeatureList();
        if (this.currentFeatureList && this.category_list && this.category_list.length > 0) {
            this.currentFeatureList.map(opt => {
                let find_cate = this.category_list.findIndex(cat => cat.id == opt.id);
                if (find_cate > -1) {
                    this.category_list[find_cate].hasAccess = opt.hasAccess;
                }
            })
        }
        // this.getCustomerCategory();
        const view = localStorage.getItem('customerView_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
            if (parse_data.selected_category) {
                this.selected_category = parse_data.selected_category;
            }
        }
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        if (this.isMobile) {
            this.items_category = 'list';
        }
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside);
    },
    watch: {
        originalData(newValue) {
            // Automatically send data to the parent when dataToSend changes
            this.$emit('dataToParent', newValue);
        },
        searchedData(newValue) {
            // console.log(newValue, 'RRRRR');
            if (!this.isEmptyObject(newValue)) {
                this.data = [{ ...newValue }];
            }
            else {
                this.data = this.originalData;
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = 20;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('customerView_home', JSON.stringify({ view: newValue, selected_category: this.selected_category }));
                }
            }
        },
        selected_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('customerView_home', JSON.stringify({ view: this.items_category, selected_category: newValue }));
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                // console.log('hello what happening in the data ...!');
                this.getCustomerData();
            }
        },
        currentFeatureList: {
            deep: true,
            handler(newValue) {
                if (newValue && this.category_list && this.category_list.length > 0) {
                    newValue.map(opt => {
                        let find_cate = this.category_list.findIndex(cat => cat.id == opt.id);
                        if (find_cate > -1) {
                            this.category_list[find_cate].hasAccess = opt.hasAccess;
                        }
                    })
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        showModal_customer: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showLeadModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showAmcModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        openModalRegister: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_service_category: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        selected_customer: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.customer_id = newValue.id + '';
                    this.getCustomerData();
                }
            }
        },
        selected_option: {
            deep: true,
            handler(newvalue) {
                if (newvalue) {
                    this.selected_category = newvalue;
                    this.data = this.customer_data[this.selected_category];
                }
            }
        }
    },
}
</script>

<style>
.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.underline {
    text-decoration: underline;
    text-decoration-line: underline;
    text-decoration-thickness: 3px;
}
</style>
