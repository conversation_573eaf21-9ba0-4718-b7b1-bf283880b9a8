<template>
    <div v-if="notification_allow" class="relative">
        <!-- Bell Icon with Animation -->
        <div class="relative cursor-pointer" @click="toggleDropdown" ref="bellIcon">
            <div class="relative">
                <!--:class="unreadCount > 0 ? 'shake' : ''"-->
                <div class="cursor-pointer">
                    <font-awesome-icon icon="fa-solid fa-bell" />
                </div>
                <!-- Notification badge, shown only when unread notifications exist -->
                <!-- <div v-if="unreadCount > 0"
                    class="absolute top-0 right-0 flex items-center justify-center h-5 w-5 rounded-full bg-red-500 text-white text-xs font-bold">
                    
                </div> -->
                <!-- Bell animation for unread notifications -->
                <span v-if="!isDropdownOpen && unreadCount > 0"
                    class="animate-ping absolute -top-2 right-0 inline-flex h-4 w-4 rounded-full bg-red-400 opacity-75"></span>
                <span v-if="unreadCount > 0"
                    class="absolute -top-2 right-0 inline-flex rounded-full h-4 w-4 text-[10px] bg-red-500 flex justify-center items-center">
                    {{ unreadCount }}</span>
            </div>
        </div>
        <notificationsList :show-modal="isDropdownOpen" :isMobile="isMobile" :notifications="notifications"
            :unreadCount="unreadCount" :pagination="pagination" @close-modal="toggleNotifications"
            @movePage="navigatetoPagedata"></notificationsList>
        <!-- Background overlay when modal is open -->
        <div v-if="isDropdownOpen" class="fixed inset-0 bg-black bg-opacity-30 z-40" @click="toggleNotifications">
        </div>
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
import notificationsList from '../dialog_box/notificationsList.vue';
export default {
    components: {
        notificationsList
    },
    props: {
        isMobile: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            isDropdownOpen: false,
            notifications: [],
            unreadCount: 0,
            currentPage: 1,
            perPage: 10,
            perPageOptions: [1, 10, 20, 30, 40, 50, 100],
            notify_img: './images/service_page/comments.png',
            pagination: { current_page: 1, per_page: 10, total: 0 },
            now: null,
            needtomove: false,
            notification_allow: true,
        };
    },
    computed: {
        ...mapGetters('notificationsList', ['currentNotificationList']),
        ...mapGetters('notificationAllow', ['currentLocalNotifyList']),
        // newNotifications() {
        //     // Filter for unread notifications
        //     if (this.notifications && this.notifications.length > 0) {
        //         return this.notifications.filter(notification => !notification.read_at || notification.read_at === undefined);
        //     }
        // },
        totalPages() {
            if (this.perPage !== 'all' && this.notifications && this.notifications.length > 0) {
                return Math.ceil(this.notifications.length / this.perPage);
            } else {
                return 1;
            }
        },
        paginatedNotifications() {
            if (this.perPage !== 'all') {
                const start = (this.currentPage - 1) * this.perPage;
                const end = start + this.perPage;
                return this.notifications.slice(start, end);
            } else {
                return this.notifications;
            }
        },
    },
    mounted() {
        if (this.currentLocalNotifyList !== undefined) {
            this.notification_allow = this.currentLocalNotifyList;
        }
        this.fetchLocalNotifyList();

        if (this.currentNotificationList && this.currentNotificationList.data && this.currentNotificationList.data.length > 0 && this.currentNotificationList.pagination) {
            this.notifications = this.currentNotificationList.data;
            this.pagination = this.currentNotificationList.pagination;
            this.unreadCount = this.currentNotificationList.pagination.all_unread_count;
            this.currentPage = this.currentNotificationList.pagination.current_page;
            if (this.currentPage < this.pagination.last_page) {
                this.fetchNotificationList({ page: this.currentPage, per_page: this.perPage });
            }
        } else {
            this.fetchNotificationList({ page: this.currentPage, per_page: this.perPage });
        }
        setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    methods: {
        ...mapActions('notificationsList', ['fetchNotificationList']),
        ...mapActions('notificationAllow', ['fetchLocalNotifyList']),

        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;

            if (this.isDropdownOpen) {
                // Add event listener to detect clicks outside the dropdown
                document.addEventListener('click', this.handleOutsideClick);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },
        handleOutsideClick(event) {
            // Use refs to check if the click is outside the dropdown and bell icon
            const dropdown = this.$refs.dropdown;
            const bellIcon = this.$refs.bellIcon;

            if (
                (dropdown && !dropdown.contains(event.target)) &&
                (bellIcon && !bellIcon.contains(event.target))
            ) {
                this.isDropdownOpen = false;
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },
        async markAsRead(notification, index) {
            // Mark as read locally
            this.notifications[index].read = true;

            // Send a request to update the notification status in the database
            // try {
            //     await fetch("/api/notifications/read", {
            //         method: "POST",
            //         body: JSON.stringify({ id: notification.id }),
            //         headers: {
            //             "Content-Type": "application/json",
            //         },
            //     });
            // } catch (error) {
            //     console.error("Failed to update notification status:", error);
            // }
        },
        //-----------
        toggleNotifications() {
            this.isDropdownOpen = !this.isDropdownOpen;
        },
        setCurrentPage(page) {
            if (page >= 1 && page <= this.totalPages) {
                this.currentPage = page;
            }
        },
        navigatetoPagedata() {
            this.needtomove = true;
            this.$emit('send_req', false);
        }

    },
    watch: {
        currentNotificationList: {
            deep: true,
            handler(newValue) {
                if (newValue && Object.keys(newValue).length > 0) {
                    this.notifications = newValue.data;
                    this.pagination = newValue.pagination;
                    this.unreadCount = newValue.pagination.all_unread_count;
                    this.currentPage = newValue.pagination.current_page;
                }
            }
        },
        // perPage: {
        //     deep: true,
        //     handler(newValue) {
        //         if (newValue) {
        //             this.fetchNotificationList({ page: 1, per_page: newValue });
        //         }
        //     }
        // },
        // currentPage: {
        //     deep: true,
        //     handler(newValue) {
        //         if (newValue) {
        //             this.fetchNotificationList({ page: newValue, per_page: this.perPage });
        //         }
        //     }
        // },
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                if (newValue && !this.needtomove) {
                    this.isDropdownOpen = false;
                }
            }
        },
        isDropdownOpen: {
            deep: true,
            handler(newValue) {
                this.$emit('send_req', newValue);
            }
        },
        currentLocalNotifyList: {
            deep: true,
            handler(newValue) {
                this.notification_allow = newValue;
            }
        }
    },
    beforeDestroy() {
        // Clean up event listener when the component is destroyed
        document.removeEventListener('click', this.handleOutsideClick);
    }
};
</script>
<style>
@keyframes shake {

    0%,
    100% {
        transform: translateX(0);
    }

    20%,
    60% {
        transform: translateX(-10px);
    }

    40%,
    80% {
        transform: translateX(10px);
    }
}

.shake {
    animation: shake 1s infinite;
}

/* Add a simple fade transition for the modal */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s ease;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}
</style>