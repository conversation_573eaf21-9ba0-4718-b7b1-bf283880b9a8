<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-display relative"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex justify-between items-center w-full px-4 py-3 set-header-background">
                <p for="leadFilter" class="text-white text-center flex justify-end ml-3"> Sales Filter
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block mt-1 text-sm bg pl-4 pr-4 pb-4 mt-5">
                <div v-if="selectedByValue === 'Custom'" class="text-xs text-green-600">
                    <p>Note: At least fill in any one field..!</p>
                </div>
                <!---Date-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'" class="flex justify-end">
                    <button @click="resetTheValues(['from', 'to'])"
                        class="absolute text-xs -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                            icon="fa-solid fa-xmark" /></button>
                </div>
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'"
                    class="grid grid-cols-2 gap-4">
                    <!-- From Date -->
                    <div class="relative">
                        <label for="fromDate"
                            class="text-sm absolute left-2 -top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700 transition-top linear duration-300"
                            :class="{ 'text-blue-700': isInputFocused.from }">
                            From Date
                        </label>

                        <input type="date" v-datepicker id="fromDate" v-model="formValues.from" :max="formValues.to"
                            @change="updateMinToDate"
                            class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full" />
                    </div>

                    <!-- To Date -->
                    <div class="relative">
                        <label for="toDate"
                            class="absolute left-2 -top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700 transition-top linear duration-300"
                            :class="{ 'text-blue-700': isInputFocused.to }">
                            To Date
                        </label>
                        <!-- @input="validateDates" -->
                        <input type="date" v-datepicker id="toDate" v-model="formValues.to" :min="minDate"
                            :max="maxDate"
                            class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full" />
                    </div>
                </div>
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Customer'" class="mt-5">
                    <div v-if="formValues.customer" class="flex justify-end ">
                        <button @click="resetTheValue('customer')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="relative flex w-full mr-2">
                        <label for="customer"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.customer || isInputFocused.customer, 'text-blue-700': isInputFocused.customer }">Customer<span
                                v-if="formValues.customer || isInputFocused.customer"
                                class="text-red-600">*</span></label>
                        <input id="customer" v-model="formValues.customer" @input="handleDropdownInput(fields)"
                            @focus="isDropdownOpen = true, isInputFocused.customer = true"
                            @blur="closeDropdown('customer')"
                            @keydown.enter="handleEnterKey('customer', filteredCustomerOptions)"
                            @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="customer"
                            class="w-full border py-2 pl-2 pr-10 leading-5 text-gray-900 focus:ring-0 rounded rounded-tr-none rounded-br-none outline-none" />

                        <!-- Right-aligned icon based on isDropdownOpen -->
                        <span class="absolute py-2 ml-[80%] sm:ml-[78%] lg:ml-[85%] cursor-pointer"
                            @click="isDropdownOpen = !isDropdownOpen">
                            <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </span>
                    </div>
                </div>
                <!-- Display filtered options as the user types -->
                <div v-if="isDropdownOpen && formValues.customer && formValues.customer.length > 1"
                    class="absolute mt-1 max-h-60 w-3/4 ml-10 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                    style="z-index: 100;" @mousedown.prevent="preventBlur('customer')">
                    <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                        @click="selectDropdownOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                        class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                        {{ option.firstName + (option.lastName ? ' ' + option.lastName : '') }} - {{
                            option.contactNumber
                        }}
                    </p>
                </div>
                <!--Assign To-->
                <!-- <div class="flex items-center mt-5">
                    <div class="relative w-full mr-2">
                        <label for="assign_to"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.assign_to || isInputFocused.assign_to, 'text-blue-700': isInputFocused.assign_to }">Assign
                            to</label>
                        <div class="border py-2 px-2 flex flex-wrap"
                            :class="{ 'border-blue-300': isInputFocused.assign_to === true }">
                            <div v-for="(selectedOption, index) in formValues.assign_to" :key="index"
                                class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                {{ selectedOption }}
                                <span @click="removeOption(selectedOption)"
                                    class="text-red-500 font-semibold cursor-pointer">x</span>
                            </div>
                            <input type="text" v-model="search" @input="filterOptions" @focus="filterOptions"
                                ref="search" @blur="hideOptions"
                                class="h-[35px] mt-1 outline-none border rounded-lg px-2"
                                @keydown.enter="handleEnterKey('multiple', filteredOptions())"
                                @keydown.down.prevent="handleDownArrow(filteredOptions())"
                                @keydown.up.prevent="handleUpArrow(filteredOptions())" />
                        </div>
                        <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                            v-if="showOptions" :class="{ 'h-auto': isInputFocused.assign_to === true }">
                            <div v-for="(option, index) in filteredOptions()" :key="index"
                                class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                :class="{ 'bg-green-300': index === selectedIndex }"
                                @click="selectOptionMultiple(option)">
                                {{ option.name }}
                            </div>
                        </div>
                    </div>
                </div> -->
                <!--type-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by category/Type'"
                    class="items-center mt-5 border">
                    <div v-if="formValues.type >= 0" class="flex justify-end ">
                        <button @click="resetTheValue('type')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="type"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': isInputFocused.type, 'text-blue-700': isInputFocused.type }">
                            Type
                        </label>
                        <div class="flex py-4 px-2 items-center">
                            <button v-for="(data, index) in typeList" :key="index"
                                class="flex items-center bg-gray-200 py-2 px-3 rounded mr-3"
                                :class="{ 'bg-gray-700': formValues['type'] === index }"
                                @click="selectStatusOption(data, 'type', index)">
                                <span class="text-sm text-gray-500"
                                    :class="{ 'text-white': formValues['type'] === index }">{{ data }}</span>
                            </button>
                        </div>
                        <div v-if="formValues.type === 1" class="flex py-4 px-2 items-center border rounded mb-2 ml-2">
                            <button v-for="(category, index) in category_list" :key="index"
                                class="flex items-center bg-gray-200 py-2 px-3 rounded mr-3"
                                :class="{ 'bg-gray-700': formValues['category'] === category.id }"
                                @click="selectStatusOption(category, 'category', index)">
                                <span class="text-sm text-gray-500"
                                    :class="{ 'text-white': formValues['category'] === category.id }">{{
                                        category.serviceCategory }}</span>
                            </button>
                        </div>
                    </div>
                </div>
                <!--invoice to-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Invoice_to'"
                    class="items-center mt-5 border">
                    <div v-if="formValues.invoice_to >= 0" class="flex justify-end ">
                        <button @click="resetTheValue('invoice_to')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="type"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': isInputFocused.invoice_to, 'text-blue-700': isInputFocused.invoice_to }">
                            invoice to
                        </label>
                        <div class="flex py-4 px-2 items-center">
                            <button v-for="(data, index) in invoice_to" :key="index"
                                class="flex items-center bg-gray-200 py-2 px-3 rounded mr-3"
                                :class="{ 'bg-gray-700': formValues['invoice_to'] === index }"
                                @click="selectStatusOption(data, 'invoice_to', index)">
                                <span class="text-sm text-gray-500"
                                    :class="{ 'text-white': formValues['invoice_to'] === index }">{{ data }}</span>
                            </button>
                        </div>
                    </div>
                </div>
                <!---serial number-->
                <div class="relative mt-7" v-if="selectedByValue === 'by Serial'">
                    <div v-if="formValues.searchQuery" class="flex justify-end ">
                        <button @click="resetTheValue('searchQuery')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700">
                            <font-awesome-icon icon="fa-solid fa-xmark" size="sm" style="color: red" />
                        </button>
                    </div>
                    <!-- Input Field for Filtering -->
                    <input v-model="formValues.searchQuery" placeholder="Search..." @input="onSearchInput"
                        @blur="closeDropdown('search')" @change="onSearchInput" @focus="onSearchInput"
                        @keydown.enter="handleEnterKey('search', filteredData)"
                        @keydown.down.prevent="handleDownArrow(filteredData)"
                        @keydown.up.prevent="handleUpArrow(filteredData)" ref="search"
                        class="text-sm p-2 border border-gray-300 rounded w-full" />

                    <!-- Displaying Filtered Data -->
                    <div v-if="filteredData && filteredData.length && isDropdown_search"
                        class="absolute mt-1 max-h-60 w-3/4 overflow-auto ml-2 rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                        style="z-index: 100;" @mousedown.prevent="preventBlur('serial')">
                        <p v-for="(option, index) in filteredData" :key="index" @click="selectSalesOption(option)"
                            :class="{ 'bg-gray-200': index === selectedIndex }"
                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                            {{ option.invoice_id }} - {{ option.customer.first_name }} {{ option.customer.last_name ?
                                option.customer.last_name : '' }}
                        </p>
                    </div>
                    <div v-else>
                        <div v-if="is_loading" class="flex justify-center items-center h-full">
                            <div
                                class="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500 border-opacity-75">
                            </div>
                            <p class="text-blue-600 font-bold space-x-4">Loading</p>
                        </div>
                        <p v-else>
                            {{ filteredData && filteredData.length > 0 ? `${filteredData.length} Sales` :
                                'No data found.' }}</p>
                    </div>
                </div>
                <!--status-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Status'"
                    class="items-center mt-5 border">
                    <div v-if="formValues.status >= 0" class="flex justify-end ">
                        <button @click="resetTheValue('status')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="type"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': isInputFocused.status, 'text-blue-700': isInputFocused.status }">
                            Status
                        </label>
                        <div class="flex py-4 px-2 items-center">
                            <button v-for="(data, index) in statusList" :key="index"
                                class="flex items-center bg-gray-200 py-2 px-3 rounded mr-3"
                                :class="{ 'bg-gray-700': formValues['status'] === index }"
                                @click="selectStatusOption(data, 'status', index)">
                                <span class="text-sm text-gray-500"
                                    :class="{ 'text-white': formValues['status'] === index }">{{ data }}</span>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Buttons -->
                <div class="flex justify-center items-center m-2 mt-5 text-sm">
                    <button @click="closeModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  px-4 mr-8 py-2">Cancel</button>
                    <button v-if="showResetButton && selectedByValue === 'Custom'" @click="resetAll"
                        class="bg-blue-700 hover:bg-blue-600 rounded-[30px] text-white px-4 mr-8 py-2">Reset
                        All</button>
                    <button @click="submitFilter"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white px-4 mr-8 py-2">Submit</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    props: {
        showModal: Boolean,
        typeList: Object,
        statusList: Object,
        category_list: Object,
        invoice_to: Object,
        selectedByValue: String,
        invoice_group: {
            type: [Number, String],
            required: true,
        },
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            customer_list: [],
            isInputFocused: { date: true, type: true, status: true, invoice_to: true },
            isDropdownOpen: false,
            selectedIndex: 0,
            showOptions: false,
            search: '',
            maxDate: new Date().toISOString().split('T')[0], // current date
            minDate: '',
            //---search serial number---
            searchQuery: '',
            filteredData: [],
            page: 1,
            is_loading: false,
            isDropdown_search: false,
        };
    },
    methods: {
        ...mapActions('searchSerialSales', ['fetchSalesListSearch']),

        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeLeadFilter');
            }, 300);
        },
        //--submit---
        submitFilter(data) {
            this.isOpen = false;
            if (this.selectedByValue !== 'by Serial') {
                // Add a delay to allow the transition before emitting the close event
                setTimeout(() => {
                    this.$emit('closeLeadFilter', this.formValues);
                    this.formValues = {};
                }, 300);
            } else {
                // Add a delay to allow the transition before emitting the close event
                setTimeout(() => {
                    this.$emit('closeLeadFilter', false, this.filteredData, this.formValues.searchQuery);
                    this.formValues.searchQuery = '';
                    this.filteredData = [];
                }, 300);
            }
        },

        //----Customer----
        handleDropdownInput() {
            // console.log(this.customer_list, 'This is customer list..@')
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contactNumber.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.lastName ? (option.firstName + ' ' + option.lastName + ' - ' + option.contactNumber).toLowerCase().includes(inputName) :
                            (option.firstName + ' - ' + option.contactNumber).toLowerCase().includes(inputName)
                    );
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = [];
            }

            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    // this.openModal();
                    // this.openModal(product_name);
                }
            }
            else if (type === 'multiple') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectOptionMultiple(optionArray[this.selectedIndex]);
                    this.selectedIndex = 0;
                }
                // else {
                //     this.openModalEmployee();
                // }
            } else if (type === 'search') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectSalesOption(optionArray[this.selectedIndex]);
                    this.selectedIndex = 0;
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else if (this.selectedIndex > 0) {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----customer dropdown--
        closeDropdown(type) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                this.isInputFocused.customer = false;
            } else if (type && type === 'search' && !this.mouseDownOnDropdown) {
                this.isDropdown_search = false;
            }
        },
        //---customers dropdown
        selectDropdownOption(option) {
            this.formValues.customer = option.firstName + (option.lastName ? ' ' + option.lastName : '') + ' - ' + option.contactNumber;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
        },
        preventBlur(type) {
            if (type && type === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            } else if (type && type === 'serial') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            }
        },
        //---multiple dropdown---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            // this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.contactNumber.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.contactNumber.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                } else if (enteredValue.length > 1) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                }
            }
            // Return an empty array if no options match the filter
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.includes(option.name)) : this.employeeList;
        },

        selectOptionMultiple(option) {
            if (!this.formValues.assign_to) {
                this.formValues.assign_to = []; // Initialize assign_to as an array if it's not already
            }
            this.formValues.assign_to.push(option.name); // Add the selected option to assign_to
            this.search = ''; // Clear the search input
            this.showOptions = false; // Hide the options dropdown
            this.$nextTick(() => {
                // Focus on the search input after selecting an option
                if (this.$refs['search']) {
                    this.$refs['search'].focus();
                    this.$refs['search'].click();
                }
            });
            this.selectedIndex = 0;
        },
        removeOption(option) {
            this.formValues['assign_to'] = this.formValues['assign_to'].filter(selected => selected !== option);
        },

        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
                this.isInputFocused.assign_to = false;
            }, 300); // Add delay to allow click events on options
        },
        openModalEmployee() {
            this.showModal_employee = true;
            this.EmployeeName = this.search;
            this.showOptions = false;
            this.showAddNew = null;
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.customer;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---status and type--
        selectStatusOption(option, type, index) {
            if (option && type === 'type') {
                this.formValues[type] = index;
            }
            else if (option && type === 'category') {
                this.formValues[type] = option.id;
            }
            else if (option && type === 'invoice_to') {
                this.formValues[type] = index;
            } else if (option && type === 'status') {
                this.formValues[type] = index;
            }
        },
        //---reset All---
        resetAll() {
            this.formValues = {};
        },
        //---reset---
        resetTheValue(key) {
            delete this.formValues[key];
            if (key === 'searchQuery') {
                this.filteredData = [];
                // this.page = 1;
            }
        },
        resetTheValues(fields) {
            // Reset specified form fields
            fields.forEach(field => {
                this.formValues[field] = null;
            });
        },
        //---search by serial number---
        onSearchInput() {
            this.isDropdown_search = true;
            if (this.filteredData && this.filteredData.length === 0 && this.formValues.searchQuery && this.formValues.searchQuery.length > 1) {
                if (!this.currentSalesListSearch.pagination && !this.is_loading) {
                    this.is_loading = true;
                    this.fetchSalesListSearch({ page: this.page, per_page: 100 });
                } else if (this.currentSalesListSearch.pagination && this.currentSalesListSearch.pagination.current_page < this.currentSalesListSearch.pagination.last_page && !this.is_loading) {
                    this.is_loading = true;
                    this.fetchSalesListSearch({ page: this.page, per_page: 100 });
                } else {
                    this.filterDataSales(this.currentSalesListSearch);
                }
            } else if (this.formValues.searchQuery === '') {
                this.filteredData = [];
                // this.page = 1;
            } else if (this.currentSalesListSearch && this.formValues.searchQuery) {
                this.filterDataSales(this.currentSalesListSearch);
            }
        },
        selectSalesOption(options) {
            // this.isOpen = false;
            // let search_value = this.formValues.searchQuery;
            this.submitFilter();
            // Add a delay to allow the transition before emitting the close event
            // setTimeout(() => {
            //     this.$emit('closeLeadFilter', false, options, search_value);
            //     this.formValues = {};
            // }, 300);
            // this.formValues.searchQuery = '';
            // this.page = 1;
            // this.filteredData = [];
        },
        filterDataSales(newValue) {
            // console.log(newValue, 'Filtered Data:', this.searchQuery, this.invoice_group);

            if (newValue && Array.isArray(newValue.data) && newValue.data.length > 0) {
                this.filteredData = newValue.data.filter(opt => {
                    // Check if sales_item_data exists and is an array with items
                    if (opt.sales_item_data && Array.isArray(opt.sales_item_data) && opt.sales_item_data.length > 0) {

                        // Apply the invoice_group filtering based on the prop value
                        if (this.invoice_group === 0 && opt.invoice_group !== 0) {
                            return false; // Skip this entry if invoice_group is not 0 and prop is 0
                        }

                        if (this.invoice_group === 1 && opt.invoice_group !== 1) {
                            return false; // Skip this entry if invoice_group is not 1 and prop is 1
                        }

                        // Filter based on user input in searchQuery (serial number)
                        return opt.sales_item_data.some(item => {
                            if (item.serial_no && typeof item.serial_no === 'string') {
                                let serialNumbers = [];

                                // Handle both JSON arrays and comma-separated strings
                                try {
                                    serialNumbers = JSON.parse(item.serial_no);
                                } catch (error) {
                                    serialNumbers = item.serial_no.split(',').map(serial => serial.trim());
                                }
                                // Ensure serialNumbers is an array and includes the searchQuery (user input)
                                return Array.isArray(serialNumbers) && serialNumbers.some(serial => serial.includes(this.formValues.searchQuery || serial == this.formValues.searchQuery));
                            }
                            return false;
                        });
                    }
                    return false; // Skip if no valid sales_item_data found
                });

                // console.log(this.filteredData, 'Filtered Results based on searchQuery');

            }
        },
        //---update minimum date---        
        updateMinToDate() {
            this.minDate = this.formValues.from;
        }

    },
    computed: {
        showResetButton() {
            // Check if formValues is not empty
            return Object.keys(this.formValues).length > 0;
        },
        ...mapGetters('searchSerialSales', ['currentSalesListSearch']),
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
            if (this.selectedByValue == 'by Date') {
                const today = new Date().toISOString().substr(0, 10);
                this.maxDate = today;
                this.formValues.from = today;
                this.formValues.to = today;
            }
        },
        currentSalesListSearch: {
            deep: true,
            handler(newValue) {
                this.is_loading = false;
                this.page += 1;
                this.filterDataSales(newValue);
                // Handle pagination check
                // if (newValue.pagination && newValue.pagination.last_page) {
                //     if (this.page > newValue.pagination.last_page) {
                //         this.page = 1;
                //     }
                // }
            }
        },
        // filteredData: {
        //     deep: true,
        //     handler(newValue) {
        //         // console.log(newValue, 'EEEEEEEEEEEEEE', this.currentSalesListSearch.pagination.last_page);

        //         if (newValue.length == 0 && this.formValues.searchQuery && this.formValues.searchQuery !== '' && this.page < this.currentSalesListSearch.pagination.last_page) {
        //             this.page += 1;
        //             this.is_loading = true;
        //             this.fetchSalesListSearch({ page: this.page, per_page: 100 });
        //         } else if (newValue.length > 1) {
        //             let uniqueInvoices = newValue.filter((item, index, self) =>
        //                 index === self.findIndex(t => t.invoice_id === item.invoice_id)
        //             );
        //             this.filteredData = uniqueInvoices;
        //         }
        //     }
        // },


    },
    mounted() {

    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>