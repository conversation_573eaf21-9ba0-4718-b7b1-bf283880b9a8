<?php

namespace App\Http\Controllers\API;

use App\Models\Customer;
use App\Models\Companies;
use App\Models\Sales;
use App\Models\Amc;
use App\Models\Employee;
use App\Models\InvoiceSettings;
use App\Models\leads;
use App\Models\Services;
use App\Models\Products;
use App\Models\Estimation;
use App\Models\Rma;
use App\Models\Module;
use App\Models\Notification;
use App\Models\HoldInvoices;
use App\Models\CustomerCategory;
use App\Models\ServiceCategory;
use App\Models\Expenses;
use App\Models\Enquires;
use App\Models\Proforma;
use App\Models\Supplier;
use App\Models\PurchaseOrderPayments;
use App\Models\PurchaseOrder;
use App\Models\Warehouse;
use Carbon\Carbon;
use Response;
use Auth;
use App\Http\Controllers\AppBaseController;
use Illuminate\Http\Request;

/**
 * Class CustomerController
 * @package App\Http\Controllers\API
 */

class UpdateTrackerAPIController extends AppBaseController
{
  
  
        public function getLastUpdateTimes($id, Request $request)
    {
           $companyId = $id; // Get company_id from request

        if (!$companyId) {
            return response()->json(['error' => 'company_id is required'], 400);
        }
        
        $lastUpdates = [
            'service_update' => Services::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
            'customer_update' => Customer::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'employee_update' => Employee::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ? :'',
            'sales_update' => Sales::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
            'products_update' => Products::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
            'estimation_update' => Estimation::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
            'leads_update' => Leads::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
            'amc_update' => Amc::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
            'rma_update' => Rma::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'features_update' => Module::latest('updated_at')->value('updated_at') ?: '',
          	'notification_update' => Notification::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'holdlist_update' => HoldInvoices::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'service_category_update' => ServiceCategory::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'customer_category_update' => CustomerCategory::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'expenses_update' => Expenses::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'enquires_update' => Enquires::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'proforma_update' => Proforma::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'supplier_update' => Supplier::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'payout_update' => PurchaseOrderPayments::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'purchase_update' => PurchaseOrder::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	'warehouse_update' => Warehouse::where('company_id', $companyId)->latest('updated_at')->value('updated_at') ?: '',
          	
        ];
           return response()->json($lastUpdates);
    }
  
       public function sendWaMessage(Request $request)
    {
       $input = $request->all();
       $mobileNo = $input['number'];
       $message = $input['message'];
       $company_id = $input['company_id'];
      
       $response = sendWaMessageToServer($mobileNo, $message, $company_id);
       return $response;
       
    }   

}
