<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div ref="modalBox"
                class="relative bg-white rounded-md mx-auto m-5 p-5 transform transition-transform duration-500 ease-in-out">
                <!-- Success Message -->
                <p class="absolute right-2 -top-4 text-xl cursor-pointer text-red-700 hover:text-red-600"
                    @click="closeModal">
                    <font-awesome-icon icon="fa-solid fa-xmark" />
                </p>
                <div class="flex flex-col items-center justify-center space-y-5">
                    <div class="text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <p class="mb-6">Your {{ typeOfInvoice }} has been recorded successfully.</p>
                </div>
                <!-- Buttons -->
                <div class="flex justify-around mt-8 space-x-1 sm:space-x-4">
                    <button @click="goHomePage"
                        class="px-2 sm:px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 mr-2">
                        <font-awesome-icon icon="fa-solid fa-house" class="px-1" /> Home
                    </button>
                    <!-- Buttons for Print and Download -->
                    <button @click="viewInvoice"
                        class="px-2 sm:px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 mr-2">
                        <font-awesome-icon icon="fa-solid fa-eye" class="px-1" /> View
                    </button>
                    <button @click="downloadInvoice"
                        class="px-2 sm:px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
                        <font-awesome-icon icon="fa-solid fa-download" class="px-1" /> Download PDF
                    </button>
                </div>
            </div>
        </div>
        <!-- Confetti container -->
        <canvas id="confetti"></canvas>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        typeOfInvoice: String,
        response_data: Object,
        currentInvoice: Object
    },
    data() {
        return {
            open_loader: false,
            pdfUrl: '',
            selectedOption: 0,
            isOpen: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
        };
    },
    mounted() {
        // Ensure confetti is correctly sized when the component is mounted
        this.resizeCanvas();
        window.addEventListener('resize', this.resizeCanvas);
        if (this.showModal) {
            setInterval(() => {
                this.startConfetti();
            }, 200);
        }
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resizeCanvas);
    },
    methods: {
        viewInvoice() {
            if (this.response_data && Object.keys(this.response_data).length > 0) {
                if (this.typeOfInvoice === 'sales') {
                    this.$router.push({ name: 'print-preview', query: { invoice_no: this.response_data.id } });
                } else if (this.typeOfInvoice === 'proforma') {
                    this.$router.push({ name: 'print-preview', query: { type: 'proforma', proforma_no: this.response_data.id } })
                } else if (this.typeOfInvoice === 'estimation') {
                    this.$router.push({ name: 'print-preview', query: { type: 'estimation', est_no: this.response_data.id } });
                } else if (this.typeOfInvoice === 'services') {
                    this.$router.push({ name: 'print-preview', query: { invoice_no: this.response_data.id } });
                }
            }
        },
        async downloadInvoice() {
            this.open_loader = true;
            try {
                if (this.response_data && this.response_data.id) {
                    let link_data = `${this.pdfUrl}/${this.typeOfInvoice && this.typeOfInvoice === 'estimation' ? 'download-estimation' : this.typeOfInvoice === 'proforma' ? 'download-proforma' : 'download-invoice'}/${this.response_data && this.response_data.id ? this.response_data.id : ''}/${this.selectedOption}`;
                    // Create an anchor element
                    let anchor = document.createElement('a');
                    anchor.href = link_data;
                    anchor.setAttribute('download', 'invoices'); // Set the download attribute to trigger a download
                    anchor.style.display = 'none';
                    // Append the anchor to the document body and click it programmatically
                    document.body.appendChild(anchor);
                    anchor.click();

                    // Cleanup: remove the anchor from the document body
                    document.body.removeChild(anchor);
                    this.open_loader = false;
                    //--enable toaster--
                    this.message = `${this.typeOfInvoice} downloaded successfully...!`;
                    this.type_toaster = 'success';
                    setTimeout(() => {
                        this.show = true;
                    }, 100);
                }
            } catch (error) {
                this.open_loader = false;
                console.error('Error downloading PDF:', error);
            }
        },
        //--go home----
        goHomePage() {
            if (this.typeOfInvoice === 'sales') {
                this.$router.push('/sales');
            } else if (this.typeOfInvoice === 'proforma') {
                this.$router.push('/proforma')
            } else if (this.typeOfInvoice === 'estimation') {
                this.$router.push('/estimation');
            } else if (this.typeOfInvoice === 'services') {
                this.$router.push('/services');
            }
        },
        closeModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close');
            }, 300);
        },
        resizeCanvas() {
            const canvas = document.getElementById('confetti');
            if (canvas) {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }
        },
        startConfetti() {
            const retina = window.devicePixelRatio;
            const PI = Math.PI,
                random = Math.random,
                cos = Math.cos,
                sin = Math.sin;

            const canvas = document.getElementById('confetti');
            const ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth * retina;
            canvas.height = window.innerHeight * retina;

            const speed = 50,
                duration = (1.0 / speed),
                confettiRibbonCount = 15,
                ribbonPaperCount = 30,
                ribbonPaperDist = 8.0,
                ribbonPaperThick = 8.0,
                confettiPaperCount = 95,
                DEG_TO_RAD = PI / 180,
                colors = [
                    ["#df0049", "#660671"],
                    ["#00e857", "#005291"],
                    ["#2bebbc", "#05798a"],
                    ["#ffd200", "#b06c00"],
                ];

            // Vector2 Class
            function Vector2(_x, _y) {
                this.x = _x;
                this.y = _y;
                this.Length = function () {
                    return Math.sqrt(this.SqrLength());
                };
                this.SqrLength = function () {
                    return this.x * this.x + this.y * this.y;
                };
                this.Add = function (_vec) {
                    this.x += _vec.x;
                    this.y += _vec.y;
                };
                this.Sub = function (_vec) {
                    this.x -= _vec.x;
                    this.y -= _vec.y;
                };
                this.Div = function (_f) {
                    this.x /= _f;
                    this.y /= _f;
                };
                this.Mul = function (_f) {
                    this.x *= _f;
                    this.y *= _f;
                };
                this.Normalize = function () {
                    const sqrLen = this.SqrLength();
                    if (sqrLen !== 0) {
                        const factor = 1.0 / Math.sqrt(sqrLen);
                        this.x *= factor;
                        this.y *= factor;
                    }
                };
            }

            // EulerMass Class
            function EulerMass(_x, _y, _mass, _drag) {
                this.position = new Vector2(_x, _y);
                this.mass = _mass;
                this.drag = _drag;
                this.force = new Vector2(0, 0);
                this.velocity = new Vector2(0, 0);
            }

            EulerMass.prototype = {
                AddForce: function (_f) {
                    this.force.Add(_f);
                },
                Integrate: function (_dt) {
                    const acc = this.CurrentForce(this.position);
                    acc.Div(this.mass);
                    const posDelta = new Vector2(this.velocity.x, this.velocity.y);
                    posDelta.Mul(_dt);
                    this.position.Add(posDelta);
                    acc.Mul(_dt);
                    this.velocity.Add(acc);
                    this.force = new Vector2(0, 0);
                },
                CurrentForce: function (_pos) {
                    const totalForce = new Vector2(this.force.x, this.force.y);
                    const speed = this.velocity.Length();
                    const dragVel = new Vector2(this.velocity.x, this.velocity.y);
                    dragVel.Mul(this.drag * this.mass * speed);
                    totalForce.Sub(dragVel);
                    return totalForce;
                },
            };

            // ConfettiPaper Class
            function ConfettiPaper(_x, _y) {
                this.pos = new Vector2(_x, _y);
                this.rotationSpeed = (random() * 600 + 800);
                this.angle = DEG_TO_RAD * random() * 360;
                this.rotation = DEG_TO_RAD * random() * 360;
                this.cosA = 1.0;
                this.size = 5.0;
                this.oscillationSpeed = random() * 1.5 + 0.5;
                this.xSpeed = 40.0;
                this.ySpeed = random() * 60 + 50.0;
                this.corners = [];
                this.time = random();
                const ci = Math.round(random() * (colors.length - 1));
                this.frontColor = colors[ci][0];
                this.backColor = colors[ci][1];
                for (let i = 0; i < 4; i++) {
                    const dx = cos(this.angle + DEG_TO_RAD * (i * 90 + 45));
                    const dy = sin(this.angle + DEG_TO_RAD * (i * 90 + 45));
                    this.corners[i] = new Vector2(dx, dy);
                }
            }

            ConfettiPaper.prototype = {
                Update: function (dt) {
                    this.time += dt;
                    this.rotation += this.rotationSpeed * dt;
                    this.cosA = cos(DEG_TO_RAD * this.rotation);
                    this.pos.x += cos(this.time * this.oscillationSpeed) * this.xSpeed * dt;
                    this.pos.y += this.ySpeed * dt;
                    if (this.pos.y > ConfettiPaper.bounds.y) {
                        this.pos.x = random() * ConfettiPaper.bounds.x;
                        this.pos.y = 0;
                    }
                },
                Draw: function (g) {
                    g.fillStyle = this.cosA > 0 ? this.frontColor : this.backColor;
                    g.beginPath();
                    g.moveTo(
                        (this.pos.x + this.corners[0].x * this.size) * retina,
                        (this.pos.y + this.corners[0].y * this.size * this.cosA) * retina
                    );
                    for (let i = 1; i < 4; i++) {
                        g.lineTo(
                            (this.pos.x + this.corners[i].x * this.size) * retina,
                            (this.pos.y + this.corners[i].y * this.size * this.cosA) * retina
                        );
                    }
                    g.closePath();
                    g.fill();
                },
            };

            // ConfettiRibbon Class with wavy movement
            function ConfettiRibbon(_x, _y, _count, _dist, _thickness, _angle, _mass, _drag) {
                this.particleDist = _dist;
                this.particleCount = _count;
                this.particles = [];
                const ci = Math.round(Math.random() * (colors.length - 1));
                this.frontColor = colors[ci][0];
                this.backColor = colors[ci][1];
                this.xOff = cos(DEG_TO_RAD * _angle) * _thickness;
                this.yOff = sin(DEG_TO_RAD * _angle) * _thickness;
                this.oscillationSpeed = random() * 0.5 + 0.5;  // Slower for smooth ribbon
                this.oscillationDistance = random() * 10 + 10;  // Smaller oscillation for gentle movement
                this.ySpeed = random() * 20 + 80;  // Slow falling ribbons
                this.position = new Vector2(_x, _y);
                this.prevPosition = new Vector2(_x, _y);
                this.velocityInherit = random() * 1 + 2;
                this.time = random() * 100;

                for (let i = 0; i < this.particleCount; i++) {
                    this.particles.push(new EulerMass(_x, _y - i * this.particleDist, _mass, _drag));
                }
            }

            ConfettiRibbon.prototype = {
                Update: function (dt) {
                    this.time += dt * this.oscillationSpeed;
                    this.position.y += this.ySpeed * dt;
                    this.position.x += cos(this.time) * this.oscillationDistance * dt;
                    this.particles[0].position = this.position;

                    let delta;
                    for (let i = 1; i < this.particleCount; i++) {
                        delta = new Vector2(this.particles[i].position.x, this.particles[i].position.y);
                        delta.Sub(this.particles[i - 1].position);
                        delta.Normalize();
                        delta.Mul(this.particleDist);
                        this.particles[i].position = new Vector2(
                            this.particles[i - 1].position.x + delta.x,
                            this.particles[i - 1].position.y + delta.y
                        );
                    }
                },
                // Add the Side function to ConfettiRibbon
                Side: function (x1, y1, x2, y2, x3, y3) {
                    return (x1 - x2) * (y3 - y2) - (y1 - y2) * (x3 - x2);
                },
                Draw: function (_g) {
                    for (let i = 0; i < this.particleCount - 1; i++) {
                        // Calculate current and next particle positions with offsets
                        const p0 = new Vector2(
                            this.particles[i].position.x + this.xOff,
                            this.particles[i].position.y + this.yOff
                        );
                        const p1 = new Vector2(
                            this.particles[i + 1].position.x + this.xOff,
                            this.particles[i + 1].position.y + this.yOff
                        );

                        // Determine which side of the ribbon we are drawing
                        if (
                            this.Side(
                                this.particles[i].position.x,
                                this.particles[i].position.y,
                                this.particles[i + 1].position.x,
                                this.particles[i + 1].position.y,
                                p1.x,
                                p1.y
                            ) < 0
                        ) {
                            _g.fillStyle = this.frontColor;
                            _g.strokeStyle = this.frontColor;
                        } else {
                            _g.fillStyle = this.backColor;
                            _g.strokeStyle = this.backColor;
                        }

                        // Begin drawing the ribbon
                        if (i === 0) {
                            _g.beginPath();
                            _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
                            _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
                            _g.lineTo(
                                ((this.particles[i + 1].position.x + p1.x) * 0.5) * retina,
                                ((this.particles[i + 1].position.y + p1.y) * 0.5) * retina
                            );
                            _g.closePath();
                            _g.stroke();
                            _g.fill();
                        } else if (i === this.particleCount - 2) {
                            _g.beginPath();
                            _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
                            _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
                            _g.lineTo(
                                ((this.particles[i].position.x + p0.x) * 0.5) * retina,
                                ((this.particles[i].position.y + p0.y) * 0.5) * retina
                            );
                            _g.closePath();
                            _g.stroke();
                            _g.fill();
                        } else {
                            // For middle particles, draw continuous segments
                            _g.beginPath();
                            _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
                            _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
                            _g.lineTo(p1.x * retina, p1.y * retina);
                            _g.lineTo(p0.x * retina, p0.y * retina);
                            _g.closePath();
                            _g.stroke();
                            _g.fill();
                        }
                    }
                }
            };
            ConfettiRibbon.bounds = new Vector2(0, 0);
            const confetti = {};
            confetti.Context = function (id) {
                var i = 0;
                var canvas = document.getElementById(id);
                var canvasParent = canvas.parentNode;
                var canvasWidth = canvasParent.offsetWidth;
                var canvasHeight = canvasParent.offsetHeight;
                canvas.width = canvasWidth * retina;
                canvas.height = canvasHeight * retina;
                var context = canvas.getContext('2d');
                var interval = null;
                var confettiRibbons = new Array();
                ConfettiRibbon.bounds = new Vector2(canvasWidth, canvasHeight);
                for (i = 0; i < confettiRibbonCount; i++) {
                    confettiRibbons[i] = new ConfettiRibbon(random() * canvasWidth, -random() * canvasHeight * 2, ribbonPaperCount, ribbonPaperDist, ribbonPaperThick, 45, 1, 0.05);
                }
                var confettiPapers = new Array();
                ConfettiPaper.bounds = new Vector2(canvasWidth, canvasHeight);
                for (i = 0; i < confettiPaperCount; i++) {
                    confettiPapers[i] = new ConfettiPaper(random() * canvasWidth, random() * canvasHeight);
                }
                this.resize = function () {
                    canvasWidth = canvasParent.offsetWidth;
                    canvasHeight = canvasParent.offsetHeight;
                    canvas.width = canvasWidth * retina;
                    canvas.height = canvasHeight * retina;
                    ConfettiPaper.bounds = new Vector2(canvasWidth, canvasHeight);
                    ConfettiRibbon.bounds = new Vector2(canvasWidth, canvasHeight);
                }
                this.start = function () {
                    this.stop()
                    var context = this;
                    this.update();
                }
                this.stop = function () {
                    cAF(this.interval);
                }
                this.update = function () {
                    var i = 0;
                    context.clearRect(0, 0, canvas.width, canvas.height);
                    for (i = 0; i < confettiPaperCount; i++) {
                        confettiPapers[i].Update(duration);
                        confettiPapers[i].Draw(context);
                    }
                    for (i = 0; i < confettiRibbonCount; i++) {
                        confettiRibbons[i].Update(duration);
                        confettiRibbons[i].Draw(context);
                    }
                    this.interval = rAF(function () {
                        confetti.update();
                    });
                }
            };

            ConfettiPaper.bounds = new Vector2(canvas.width, canvas.height);
            ConfettiRibbon.bounds = new Vector2(canvas.width, canvas.height);

            const confettiPapers = [];
            const confettiRibbons = [];

            for (let i = 0; i < confettiPaperCount; i++) {
                confettiPapers.push(new ConfettiPaper(random() * canvas.width, random() * canvas.height));
            }
            for (let i = 0; i < confettiRibbonCount; i++) {
                confettiRibbons.push(new ConfettiRibbon(random() * canvas.width, -random() * canvas.height * 2, ribbonPaperCount, ribbonPaperDist, ribbonPaperThick, 45, 1, 0.05));
            }

            function updateConfetti() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                for (let i = 0; i < confettiPaperCount; i++) {
                    confettiPapers[i].Update(duration);
                    confettiPapers[i].Draw(ctx);
                }
                for (let i = 0; i < confettiRibbonCount; i++) {
                    confettiRibbons[i].Update(duration);
                    confettiRibbons[i].Draw(ctx);
                }
                requestAnimationFrame(updateConfetti);
            }

            updateConfetti();
        },
        //--update initial data
        initialData() {
            if (this.showModal) {
                /// Set base URL based on hostname
                const hostname = window.location.hostname;
                let baseURL;

                if (hostname === 'app.track-new.com') {
                    // Production URL
                    baseURL = 'https://api.track-new.com/api';
                } else if (hostname === 'devapp.track-new.com') {
                    // Development URL
                    baseURL = 'https://devapi.track-new.com/api';
                } else if (hostname === ' ************' || hostname === ' ************:8000') {
                    // Local development URL (adjust as needed)
                    baseURL = 'https://devapi.track-new.com/api';
                    // baseURL = 'https://api.track-new.com/api';

                } else {
                    // Default fallback URL
                    baseURL = 'https://devapi.track-new.com/api';
                }
                this.pdfUrl = baseURL;

                if (this.currentInvoice && this.currentInvoice.length > 0) {
                    this.selectedOption = this.currentInvoice[0].is_signature;
                }
            }
        },
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.startConfetti();
                    this.initialData();
                }
            }, 100);
        },
    }
};
</script>

<style scoped>
canvas {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    pointer-events: none;
}
</style>
