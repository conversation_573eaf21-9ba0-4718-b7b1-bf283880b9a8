<template>
    <div class="text-sm" :class="{ 'mt-[60px] mb-[60px]': isMobile }">
        <div class="my-custom-margin">
            <!--header-->
            <div class="mt-3 mb-3 flex justify-start items-center">
                <button @click="subscriptionOrders"
                    class="text-white shadow-inner shadow-blue-100 border border-blue-400 bg-blue-600 px-2 py-2 rounded">
                    <i class="fas fa-history"></i> Subscriptions History</button>
            </div>
            <!-- Alert Message -->
            <div v-if="modalMessage !== ''"
                class="bg-yellow-50 text-yellow-800 p-4 rounded-lg shadow-md w-full mx-auto mt-6">
                <h2 class="text-sm font-medium text-center">
                    {{ modalMessage }}
                </h2>
            </div>
            <!-- Toggle between Monthly and Yearly -->
            <div class="flex justify-center items-center my-6">
                <button @click="setPlanType('monthly')"
                    :class="planType === 'monthly' ? activeTabClass : inactiveTabClass" class="px-4 py-2 rounded-l-md">
                    Monthly
                </button>
                <button @click="setPlanType('yearly')"
                    :class="planType === 'yearly' ? activeTabClass : inactiveTabClass" class="px-4 py-2 rounded-r-md">
                    Yearly
                </button>
            </div>
            <!--plan list-->
            <div class="flex justify-center items-center">
                <div v-if="filteredPlans && filteredPlans.length > 0" class="grid grid-cols-1  gap-6"
                    :class="`sm:grid-cols-2 xl:grid-cols-${filteredPlans.length > 4 ? 4 : filteredPlans.length}`">
                    <div v-for="(plan, index) in filteredPlans" :key="index"
                        class="bg-white rounded-lg shadow-lg shadow-gray-500 border border-gray-300 relative">
                        <!-- Display "Current Plan" on top of the active plan -->
                        <div class="py-5 rounded-t-lg relative text-white"
                            :style="{ backgroundColor: plan.labelcolor ? plan.labelcolor : '#36454F' }">
                            <div v-if="currentCompanyList && currentCompanyList.plans && currentCompanyList.plans.id && plan.id === currentCompanyList.plans.id && timeUntil !== 'Expired'"
                                class="absolute -top-2 -right-5 items-center text-center  rounded-bl-lg py-1 px-2 text-xs text-center">
                                <font-awesome-icon icon="fa-solid fa-circle-check" size="2xl"
                                    class="px-1 items-enter text-green-500" />
                            </div>
                            <h2 class="text-lg lg:text-xl mb-2 text-center">
                                {{ plan.title }}
                            </h2>
                            <p class="text-center">(18% Tax Extra)</p>
                        </div>
                        <div class="p-6 py-2">
                            <div class="text-lg lg:text-xl  text-gray-800 py-1 text-center">
                                <font-awesome-icon icon="fa-solid fa-indian-rupee-sign" class="px-1 text-gray-500" />
                                {{ plan.price }}.00
                            </div>
                            <div class="text-gray-600 text-center mb-2">{{ calculateTime(plan.days) }}</div>
                            <div class="border-t border-gray-300 pb-3"></div>
                            <ul v-if="plan.data && Object.keys(plan.data).length > 0"
                                class="space-y-2 mb-6 text-gray-700">
                                <li v-for="(feature, i) in Object.keys(plan.data)" :key="i" class="flex items-center">
                                    <i :class="plan.data[feature] === 'off' ? 'fas fa-times-circle text-red-500' : 'fas fa-check-circle text-green-500'"
                                        class="mr-2"></ i>
                                        <span>{{ formatLabel(feature) }}</span>
                                        <span v-if="plan.data[feature] !== 'on'" class="px-2">({{ plan.data[feature] ==
                                            -1 ? 'Unlimited' :
                                            plan.data[feature] }})</span>
                                </li>
                            </ul>
                            <button
                                v-if="currentCompanyList && currentCompanyList.plans && currentCompanyList.plans.id && plan.id == currentCompanyList.plans.id && timeUntil !== 'Expired' && !is_renew"
                                class="w-full py-2 bg-blue-100 text-[#040ad1] rounded cursor-not-allowed opacity-50"
                                disabled>
                                <font-awesome-icon icon="fa-solid fa-check" class="px-1 items-center" /> Activated
                            </button>
                            <button v-else @click="openPaymentPlan(plan)"
                                class="w-full text-white shadow-inner shadow-blue-100 border border-blue-400 bg-blue-600 px-2 py-2 rounded transition duration-200">
                                <font-awesome-icon
                                    v-if="is_renew && currentCompanyList.plans && currentCompanyList.plans.id == plan.id"
                                    icon="fa-solid fa-rotate-right" class="px-1 items-center" />
                                <font-awesome-icon v-else icon="fa-solid fa-plus" class="px-1 items-center" />

                                {{ currentCompanyList && currentCompanyList.plans && currentCompanyList.plans.id ?
                                    is_renew && currentCompanyList.plans.id == plan.id ? 'Renew' : 'Upgrade'
                                    :
                                    'Subscribe' }}
                            </button>
                            <p
                                v-if="currentCompanyList && currentCompanyList.plans && currentCompanyList.plans.id && plan.id === currentCompanyList.plans.id">
                                <span class="px-6 block text-xs py-1 text-center items-center"
                                    :style="{ color: text_color }">
                                    {{ timeUntil !== 'Expired' ? `Remaning : ${timeUntil}` : `Plan: ${timeUntil}`
                                    }}</span>
                            </p>
                        </div>
                        <!-- <p v-else class="text-[12px] text-blue-600 py-1 text-center">(online payment Comming soon)</p> -->
                    </div>
                </div>
                <!--in case empty-->
                <div v-if="filteredPlans && filteredPlans.length === 0">
                    <div class="flex justify-center items-center">
                        <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                    </div>
                    <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
                </div>
            </div>
        </div>
        <bottombar v-if="isMobile" :selected_btn_btm="'categories'"></bottombar>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <Loader :showModal="open_loader"></Loader>
        <support :showModal="show_plan" @close-modal="closePlanExpire"></support>
        <paymentSubs :showModal="open_subscribe" :plan="selected_plan" :button-text="'Pay'" @close="closePaymentPlan"
            @confirm="payByPhonyPay"></paymentSubs>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import bottombar from '../dashboard/bottombar.vue';
import support from '../dialog_box/support.vue';
import axios from 'axios';
import paymentSubs from '../dialog_box/paymentSubs.vue';
export default {
    emits: ['updateIsOpen'],
    props: {
        isMobile: Boolean,
        isAndroid: Boolean,
        refresh: Boolean,
        updateModalOpen: Boolean
    },
    components: {
        bottombar,
        support,
        paymentSubs
    },
    data() {
        return {
            plans: [],
            timeUntil: '',
            text_color: 'green',
            show_plan: false,
            //---toaster----
            type_toaster: 'info',
            message: '',
            show: false,
            empty_data: '/images/dashboard/empty.svg',
            payment_type: 1,
            open_loader: false,
            //---subscribe modal---
            open_subscribe: false,
            selected_plan: {},
            planType: 'monthly',
            //---message--
            modalMessage: '',
            is_renew: false,
        };
    },
    created() {
        if (this.currentSubscriptionList && this.currentSubscriptionList.length > 0) {
            this.plans = [...this.currentSubscriptionList];
            this.fetchSubscriptionList();
        } else {
            this.open_loader = true;
            this.fetchSubscriptionList();
        }
        this.calculateTimeUntil();
        this.fetchLocalDataList();
        this.fetchCompanyList();
        this.setModalMessage();
    },
    computed: {
        ...mapGetters('subscription', ['currentSubscriptionList']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        filteredPlans() {
            return this.plans.filter(plan => {
                // console.log(plan.days);
                if (this.planType === 'monthly') return plan.days === 30;
                if (this.planType === 'yearly') return plan.days === 365;
                return false;
            });
        },
        activeTabClass() {
            return 'bg-blue-600 text-white border border-blue-600';
        },
        inactiveTabClass() {
            return 'bg-gray-200 text-gray-600 font-medium border border-gray-300';
        },
    },
    methods: {
        ...mapActions('subscription', ['fetchSubscriptionList']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('companies', ['fetchCompanyList']),
        setPlanType(type) {

            this.planType = type;
        },
        capitalizeFirstLetter(str) {
            // if (str === str.toUpperCase()) {
            //     return str.replace(/_/g, ' ');  // Return the string as is if it's fully uppercase
            // }
            return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
        },
        formatLabel(key) {
            if (key == 'employee_limit') {
                return 'User Limit';
            } else {
                const words = key === key.toUpperCase() ? [key] : key.split('_');
                // const formattedLabel = words.map(word => this.capitalizeFirstLetter(word)).join(' ');/(?=[A-Z])|_/
                const formattedLabel = words.map(word => this.capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            }
        },
        calculateTime(days) {
            if (days < 30) {
                return `${days} day${days > 1 ? 's' : ''}`;
            } else if (days < 365) {
                const months = Math.floor(days / 30);
                return `${months} month${months > 1 ? 's' : ''}`;
            } else {
                const years = Math.floor(days / 365);
                const remainingDays = days % 365;
                const months = Math.floor(remainingDays / 30);
                return `${years} year${years > 1 ? 's' : ''}` +
                    (months ? ` and ${months} month${months > 1 ? 's' : ''}` : '');
            }
        },
        subscriptionOrders() {
            this.$router.push({ name: 'subscriptionsHistory' });
        },

        calculateTimeUntil() {
            if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0 && this.currentCompanyList.expiry_date) {
                // Convert expiry date to Date object
                const expiryDate = new Date(this.currentCompanyList.expiry_date);

                // Set expiry date to end of the day in local time (23:59:59.999)
                expiryDate.setHours(23, 59, 59, 999); // End of the expiry date in local time

                // Get current date (local time)
                const now = new Date();

                // Get difference in milliseconds
                const diffInMillis = expiryDate - now;

                if (diffInMillis <= 0) {
                    this.timeUntil = `Expired`;
                    this.text_color = 'red';
                    return;
                }

                // Calculate years, months, weeks, days, hours, minutes from the difference
                const years = Math.floor(diffInMillis / (1000 * 60 * 60 * 24 * 365));
                const months = Math.floor((diffInMillis % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24 * 30));
                const weeks = Math.floor((diffInMillis % (1000 * 60 * 60 * 24 * 30)) / (1000 * 60 * 60 * 24 * 7));
                const days = Math.floor((diffInMillis % (1000 * 60 * 60 * 24 * 7)) / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diffInMillis % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diffInMillis % (1000 * 60 * 60)) / (1000 * 60));

                // Calculate remaining time based on the largest unit (years, months, etc.)
                if (years > 0) {
                    this.timeUntil = `${years} year${years > 1 ? 's' : ''}`;
                } else if (months > 0) {
                    this.timeUntil = `${months} month${months > 1 ? 's' : ''}`;
                    this.text_color = 'red'; // Color for 1 month or less
                } else if (weeks > 0) {
                    this.timeUntil = `${weeks} week${weeks > 1 ? 's' : ''}`;
                    this.text_color = 'red'; // Color for 1 week or less
                } else if (days > 0) {
                    this.timeUntil = `${days} day${days > 1 ? 's' : ''}`;
                    this.text_color = 'red'; // Color for 1 day or less
                    if (days < 1) {
                        this.is_renew = true;
                    }
                } else if (hours > 0) {
                    this.timeUntil = `${hours} hour${hours > 1 ? 's' : ''}`;
                    this.text_color = 'red'; // Color for 1 hour or less
                    this.is_renew = true;
                } else if (minutes > 0) {
                    this.timeUntil = `${minutes} minute${minutes > 1 ? 's' : ''}`;
                    this.text_color = 'red'; // Color for 1 minute or less
                    this.is_renew = true;
                }
            }
        },
        openPlanExpire() {
            this.message = 'Online payment gateway integration is currently in progress. For subscription inquiries, please contact our support team.'
            this.show = true;
            this.show_plan = true;
        },
        closePlanExpire() {
            this.show_plan = false;
        },
        //---subscribe plan----
        async subscribeThePlan(data) {
            if (data && data.price > 0) {
                try {
                    const response = await axios.post('https://api-preprod.phonepe.com/apis/pg-sandbox/pg/v1/pay',
                        {
                            "merchantId": "PGTESTPAYUAT",
                            "merchantTransactionId": "MT7850590068188104",
                            "merchantUserId": "MUID123",
                            "amount": 10000,
                            "redirectUrl": "https://webhook.site/redirect-url",
                            "redirectMode": "REDIRECT",
                            "callbackUrl": "https://webhook.site/callback-url",
                            "mobileNumber": "9999999999",
                            "paymentInstrument": {
                                "type": "PAY_PAGE"
                            }
                        });

                    console.log('Payment initiated:', response.data);
                    this.message = 'Payment process started successfully!';
                    this.show = true;
                    // Redirect to PhonePe's payment page
                    window.location.href = response.data.paymentPageUrl;
                } catch (error) {
                    console.error('Error initiating payment:', error);
                    this.message = 'Error initiating payment. Please try again.';
                    this.show = true;
                }
            } else {
                this.message = 'Invalid plan data.';
                this.show = true;
            }
        },
        //--razor pay--
        openRazorpayCheckout(price) {
            // Ensure Razorpay script is loaded
            if (window.Razorpay) {
                const options = {
                    key: 'rzp_live_cxIoh58aQLAIMm', // Replace with your Razorpay key
                    amount: price * 100, // Amount in paise (e.g., 50000 paise = 500 INR)
                    currency: 'INR',
                    name: 'Your Company Name',
                    description: 'Test Transaction',
                    handler: function (response) {
                        // Handle successful payment
                        console.log('Payment successful:', response);
                        alert('Payment Successful!');
                    },
                    prefill: {
                        name: 'John Doe',
                        email: '<EMAIL>',
                        contact: '9999999999'
                    },
                    notes: {
                        address: 'Corporate Office'
                    },
                    theme: {
                        color: '#3399cc'
                    }
                };

                const paymentObject = new window.Razorpay(options);
                paymentObject.open();
            } else {
                console.error('Razorpay script not loaded');
            }
        },
        //--phone pay
        payByPhonyPay(id, send_data) {
            if (id) {
                this.open_loader = true;
                axios.post(`/subscription/subscribe/${this.payment_type}/${id}`, { code: send_data })
                    .then(response => {
                        let data_value = response.data.data;
                        if (data_value && data_value.pay_page_url) {
                            this.closePaymentPlan();
                            window.open(data_value.pay_page_url, '_blank');
                            // window.location.href = url;
                        }
                        this.open_loader = false;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
        },
        //---open payment plan---
        openPaymentPlan(plan) {
            if (plan) {
                this.selected_plan = plan;
                this.open_subscribe = true;
            }
        },
        closePaymentPlan() {
            this.open_subscribe = false;
            this.selected_plan = {};
        },
        closeAllModals() {
            this.show_plan = false;
            this.open_subscribe = false;
        },
        // Set dynamic message based on plan and trial status
        setModalMessage() {
            const currentLocalData = this.currentCompanyList;
            let validate_expire = false;
            // Check if the expiry date exists and if the plan has expired
            // Convert the expiry date to a Date object
            const expiry_date = new Date(currentLocalData.expiry_date);

            // Normalize expiry date to midnight (00:00:00)
            expiry_date.setHours(0, 0, 0, 0); // Set time to 00:00:00.000

            // Get current date and normalize it to midnight (00:00:00)
            const current_date = new Date();
            current_date.setHours(0, 0, 0, 0); // Set time to 00:00:00.000

            // Compare only the date part
            if (current_date > expiry_date) {
                validate_expire = true;
            }
            if (!currentLocalData || validate_expire) {
                this.modalMessage = 'You don\'t have any active plans. Please select a plan and subscribe.'; // New plan
            } else if (validate_expire) {
                this.modalMessage = 'Existing plans have ended. Please renew your subscription.'; // Plans expired
            } else if (validate_expire) {
                this.modalMessage = 'Trial has ended. Please subscribe to continue.'; // Trial expired
            } else {
                this.modalMessage = ''; // Default message
            }
        },
    },
    watch: {
        refresh: {
            deep: true,
            handler(newValue) {
                this.fetchSubscriptionList();
            }
        },
        currentSubscriptionList: {
            deep: true,
            handler(newValue) {
                this.open_loader = false;
                if (newValue && newValue.length > 0) {
                    this.plans = [...newValue];
                }
            }
        },
        currentCompanyList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.calculateTimeUntil();
                    this.setModalMessage();
                }
            }
        },
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        show_plan: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_subscribe: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        }

    },
    // mounted() {
    //     const script = document.createElement('script');
    //     script.src = 'https://checkout.razorpay.com/v1/checkout.js';
    //     script.async = true;
    //     document.body.appendChild(script);
    // },
};
</script>

<style>
body {
    font-family: 'Nunito', sans-serif;
}
</style>