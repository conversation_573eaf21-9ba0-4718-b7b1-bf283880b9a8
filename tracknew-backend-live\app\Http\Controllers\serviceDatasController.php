<?php

namespace App\Http\Controllers;

use App\DataTables\serviceDatasDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateserviceDatasRequest;
use App\Http\Requests\UpdateserviceDatasRequest;
use App\Repositories\serviceDatasRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class serviceDatasController extends AppBaseController
{
    /** @var serviceDatasRepository $serviceDatasRepository*/
    private $serviceDatasRepository;

    public function __construct(serviceDatasRepository $serviceDatasRepo)
    {
        $this->serviceDatasRepository = $serviceDatasRepo;
    }

    /**
     * Display a listing of the serviceDatas.
     *
     * @param serviceDatasDataTable $serviceDatasDataTable
     *
     * @return Response
     */
    public function index(serviceDatasDataTable $serviceDatasDataTable)
    {
        return $serviceDatasDataTable->render('service_datas.index');
    }

    /**
     * Show the form for creating a new serviceDatas.
     *
     * @return Response
     */
    public function create()
    {
        return view('service_datas.create');
    }

    /**
     * Store a newly created serviceDatas in storage.
     *
     * @param CreateserviceDatasRequest $request
     *
     * @return Response
     */
    public function store(CreateserviceDatasRequest $request)
    {
        $input = $request->all();

        $serviceDatas = $this->serviceDatasRepository->create($input);

        Flash::success('Service Datas saved successfully.');

        return redirect(route('serviceDatas.index'));
    }

    /**
     * Display the specified serviceDatas.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $serviceDatas = $this->serviceDatasRepository->find($id);

        if (empty($serviceDatas)) {
            Flash::error('Service Datas not found');

            return redirect(route('serviceDatas.index'));
        }

        return view('service_datas.show')->with('serviceDatas', $serviceDatas);
    }

    /**
     * Show the form for editing the specified serviceDatas.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $serviceDatas = $this->serviceDatasRepository->find($id);

        if (empty($serviceDatas)) {
            Flash::error('Service Datas not found');

            return redirect(route('serviceDatas.index'));
        }

        return view('service_datas.edit')->with('serviceDatas', $serviceDatas);
    }

    /**
     * Update the specified serviceDatas in storage.
     *
     * @param int $id
     * @param UpdateserviceDatasRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateserviceDatasRequest $request)
    {
        $serviceDatas = $this->serviceDatasRepository->find($id);

        if (empty($serviceDatas)) {
            Flash::error('Service Datas not found');

            return redirect(route('serviceDatas.index'));
        }

        $serviceDatas = $this->serviceDatasRepository->update($request->all(), $id);

        Flash::success('Service Datas updated successfully.');

        return redirect(route('serviceDatas.index'));
    }

    /**
     * Remove the specified serviceDatas from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $serviceDatas = $this->serviceDatasRepository->find($id);

        if (empty($serviceDatas)) {
            Flash::error('Service Datas not found');

            return redirect(route('serviceDatas.index'));
        }

        $this->serviceDatasRepository->delete($id);

        Flash::success('Service Datas deleted successfully.');

        return redirect(route('serviceDatas.index'));
    }
}
