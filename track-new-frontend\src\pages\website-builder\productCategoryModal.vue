<template>
    <div v-if="show" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 text-sm">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-lg">
            <div class="flex justify-between items-center border-b pb-1 mb-2">
                <h2 class="font-bold">{{ isEditMode ? 'Edit Category' : 'Product Categories' }}</h2>
                <button @click="closeModal" class="text-red-600 hover:text-red-500"><span><font-awesome-icon
                            icon="fa-solid fa-xmark" /></span></button>
            </div>

            <!-- Categories Table -->
            <table class="min-w-full bg-white shadow-md rounded-lg mb-4">
                <thead>
                    <tr>
                        <th class="py-2 px-4 text-left border-b">Category Name</th>
                        <th class="py-2 px-4 text-left border-b">Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="(category, index) in category" :key="category.id">
                        <td class="py-2 px-4 border-b">{{ category.name }}</td>
                        <td class="py-2 px-4 border-b">
                            <!-- Edit Button -->
                            <button @click="openEditCategoryModal(category, index)" class="text-blue-500 mr-2">
                                <font-awesome-icon icon="fa-solid fa-pencil" /> Edit
                            </button>
                            <!-- Delete Button (Disabled for the first item) -->
                            <button v-if="index !== 0" @click="deleteCategory(category, index)" class="text-red-500">
                                <font-awesome-icon icon="fa-solid fa-trash-can" /> Delete
                            </button>
                        </td>
                    </tr>
                </tbody>
            </table>

            <!-- Category Name Input -->
            <div class="mb-4">
                <label class="block text-sm font-normal mb-1">Category Name *</label>
                <input v-model="categoryData.name" type="text" class="border p-2 rounded w-full"
                    @keydown.enter="addCategory"
                    :placeholder="isEditMode ? 'Edit category name' : 'Enter new category name'" required />
            </div>
            <!--buttons-->
            <!-- Action Buttons -->
            <div class="flex justify-center gap-4 mt-6">
                <button @click="resetCategory" class="px-4 py-2 bg-gray-200 rounded shadow">Reset</button>
                <button @click="addCategory"
                    class="px-4 py-2 bg-green-500 hover:bg-green-600 text-white rounded shadow">
                    {{ isEditMode ? 'Update' : 'Add' }}
                </button>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end gap-4 mt-6">
                <button @click="closeModal" class="px-4 py-2 bg-gray-200 rounded shadow">Cancel</button>
                <button @click="saveCategory" class="px-4 py-2 bg-blue-500 hover:bg-blue-600 text-white rounded shadow">
                    Save Categories
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    </div>
</template>

<script>
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import { logError } from 'ckeditor5';
export default {
    props: {
        show: {
            type: Boolean,
            required: true
        },
        categories: {
            type: Array,
            required: true
        },
        isMode: {
            type: Boolean,
            required: false
        },
        categoryInfo: {
            type: Object,
            required: false,
        },
        categoryId: {
            type: Number,
            required: false,
        }
    },
    components: {
        confirmbox
    },
    data() {
        return {
            categoryData: { id: null, name: '' }, // Local copy of category data for editing or adding
            isEditMode: false,
            category: [...this.categories],
            updateIndex: null,
            //--confirm box--
            open_confirmBox: false,
            deleteIndex: null,
        };
    },
    methods: {
        closeModal() {
            this.$emit('close'); // Emit close event to parent
        },
        saveCategory() {
            if (this.category && this.category.length > 0) {
                this.$emit('save', this.category); // Emit save event with the category data
                this.closeModal();
            }
        },
        deleteCategory(category, index) {
            this.deleteIndex = category.id;
            this.open_confirmBox = true;
        },
        openEditCategoryModal(category, index) {
            this.isEditMode = true;
            this.categoryData = { ...category }; // Set category data for editing
            this.updateIndex = category.id;

        },
        //---category--
        resetCategory() {
            this.categoryData = { id: null, name: '' };
            this.isEditMode = false,
                this.updateIndex = null;
        },
        addCategory() {
            if (!this.isEditMode && this.updateIndex === null && !this.categoryData.id && this.categoryData.name !== '') {
                let maxId = Math.max(99, ...this.category.map(opt => opt.id || 0));
                this.categoryData.id = maxId + 1;
                this.category.push(this.categoryData);
                this.resetCategory();
                this.$emit('toasterMessages', { msg: 'Category added successfully', type: 'success' });
                // } else {
                //     this.$emit('toasterMessages', { msg: 'Category already exist', type: 'warning' });
                // }
            } else if (this.isEditMode && this.updateIndex !== null && this.categoryData.id && this.categoryData.name !== '') {
                let find_exist = this.category.findIndex(opt => opt.id === this.updateIndex);
                if (this.category[find_exist] != undefined) {
                    this.category.splice(find_exist, 1, this.categoryData);
                    this.resetCategory();
                    this.$emit('toasterMessages', { msg: 'Category updated successfully', type: 'success' });
                }
            }
        },
        //--remove category options--
        deleteRecord() {
            if (this.deleteIndex !== null) {
                let find_exist = this.category.findIndex(opt => opt.id === this.deleteIndex);
                this.category.splice(find_exist, 1);
                this.$emit('toasterMessages', { msg: 'Category removed successfully', type: 'success' });
                this.open_confirmBox = false;
                this.deleteIndex = null;
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.deleteIndex = null;
        }
    },
    watch: {
        categories: {
            deep: true,
            handler(newValue) {
                this.category = [...this.categories];
            }
        },
        show: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    if (this.categories.length === 0) {
                        this.category = [{ id: 100, name: 'General' }];
                    }
                }
            }
        },
        isMode: {
            deep: true,
            handler(newValue) {
                if (newValue && this.categoryInfo && this.categoryId) {
                    this.isEditMode = newValue;
                    this.categoryData = { ...this.categoryInfo };
                    this.updateIndex = this.categoryId;
                }
            }
        }
    }
};
</script>