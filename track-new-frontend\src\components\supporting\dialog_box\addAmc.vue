<template>
    <div v-if="showModal" class="fixed text-sm sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen sm:pb-[150px] lg:pb-[70px] pb-[50px]"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="bg-teal-600 justify-between items-center flex py-3 set-header-background"
                :class="{ 'fixed top-0 left-0 z-50 w-full': isMobile }">
                <h2 class="text-white font-bold text-center ml-12 text-lg">
                    {{ type == 'edit' ? 'Edit AMC' : 'Add AMC' }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!-- Content based on selected option -->
            <div class="mt-5 pl-4 pr-4" :class="{ 'mb-[100px]': isMobile }">

                <!---customer details-->
                <div class="flex lg:mt-5 mt-[70px]">
                    <div class="mr-2 w-10 flex justify-center items-center" :title="'customer'">
                        <img :src="user_name" alt="customer" class="w-7 h-7">
                    </div>
                    <div class="relative flex w-full mr-2">
                        <label for="customer"
                            class="text-xs text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2  bg-white px-1 text-gray-700': formValues.customer || isInputFocused.customer, 'text-blue-700': isInputFocused.customer }">Customer<span
                                v-if="formValues.customer || isInputFocused.customer"
                                class="text-red-600">*</span></label>
                        <input id="customer" v-model="formValues.customer" @input="handleDropdownInput(fields)"
                            @focus="isDropdownOpen = true, isInputFocused.customer = true, handleDropdownInput(fields)"
                            @blur="closeDropdown('customer')"
                            @keydown.enter="handleEnterKey('customer', filteredCustomerOptions)"
                            @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="customer"
                            class="w-full border py-2 pl-2 pr-10 leading-5 text-gray-900 focus:ring-0 rounded rounded-tr-none rounded-br-none outline-none"
                            :readonly="type === 'edit'" />

                        <!-- Right-aligned icon based on isDropdownOpen -->
                        <span class="absolute py-3 ml-[80%] sm:ml-[78%] lg:ml-[85%] cursor-pointer"
                            @click="isDropdownOpen = !isDropdownOpen">
                            <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </span>
                        <div class="w-1/10 text-center text-lg py-2 px-2 border font-bold text-white  bg-green-700 cursor-pointer hover:border-green-700 rounded-tr rounded-br"
                            @click="openModal">+</div>
                    </div>
                </div>
                <!-- Display filtered options as the user types && formValues.customer && formValues.customer.length > 1 -->
                <div v-if="isDropdownOpen"
                    class="absolute mt-1 max-h-60 w-3/4 ml-10 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                    style="z-index: 100;" @mousedown.prevent="preventBlur('customer')">
                    <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                        @click="selectDropdownOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                        class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                        {{ option.first_name + (option.last_name ? ' ' + option.last_name : '') }} -
                        {{ option.contact_number }}
                    </p>
                    <!-- Add New Customer button -->
                    <button
                        v-if="filteredCustomerOptions.length === 0 && formValues.customer && formValues.customer.length > 1 && !isExistOption() && type !== 'edit'"
                        @click="openModal"
                        class="w-full text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                        + Add Customer
                    </button>
                </div>
                <!---AMC Date-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'amc date'">
                        <img :src="date" alt="amc_date" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="amc_date"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-4 sm:-top-2  bg-white px-1 text-gray-700': formValues.amc_date || isInputFocused.amc_date, 'text-blue-700': isInputFocused.amc_date }">
                            AMC Start Date<span v-if="formValues.amc_date || isInputFocused.amc_date"
                                class="text-red-600">*</span> <span
                                class="font-light text-gray-400 text-[12px]">(MM/DD/YYYY)</span></label>
                        <input id="amc_date" v-model="formValues.amc_date" type="date" v-datepicker placeholder=" "
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                            @focus="isInputFocused.amc_date = true" @blur="isInputFocused.amc_date = false" />
                    </div>
                </div>
                <!---AMC Title-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'amc title'">
                        <img :src="amc_title_img" alt="title" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="title"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2  bg-white px-1 text-gray-700': formValues.title || isInputFocused.title, 'text-blue-700': isInputFocused.title }">
                            AMC Title<span v-if="formValues.title || isInputFocused.title"
                                class="text-red-600">*</span></label>
                        <input id="title" v-model="formValues.title" type="text" placeholder=""
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                            @focus="isInputFocused.title = true" />
                    </div>
                </div>
                <!---AMC payment type-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'amc payment type'">
                        <img :src="amc_payment" alt="amc_payment" class="w-7 h-7">
                    </div>
                    <div class="flex w-full mr-2 relative">
                        <label for="amc_payment_type"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2  bg-white px-1 text-gray-700': (formValues.amc_payment_type !== undefined) || isInputFocused.amc_payment_type, 'text-blue-700': isInputFocused.amc_payment_type }">
                            AMC Payment type
                            <!-- <span v-if="formValues.amc_payment_type || isInputFocused.amc_payment_type"
                                class="text-red-600">*</span> -->
                        </label>
                        <select id="amc_payment_type" v-model="formValues.amc_payment_type"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                            @focus="isInputFocused.amc_payment_type = true">
                            <option value="0">Paid</option>
                            <option value="1">Unpaid</option>
                            <option value="2">Free</option>
                        </select>
                    </div>
                </div>
                <!---AMC Status-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'amc status'">
                        <img :src="statusOf" alt="amc_status" class="w-7 h-7">
                    </div>
                    <div class="flex w-full mr-2 relative">
                        <label for="amc_status"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2  bg-white px-1 text-gray-700': (formValues.amc_status !== undefined) || isInputFocused.amc_status, 'text-blue-700': isInputFocused.amc_status }">AMC
                            Status <span v-if="formValues.amc_status || isInputFocused.amc_status"
                                class="text-red-600">*</span></label>
                        <select id="amc_status" v-model="formValues.amc_status"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                            @focus="isInputFocused.amc_status = true">
                            <option value="0">Open</option>
                            <option value="1">Progress</option>
                            <option value="2">Completed</option>
                            <option value="3">Cancelled</option>
                        </select>
                    </div>
                </div>
                <!---Assign to--->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'assign To'">
                        <img :src="assign" alt="assign_to" class="w-7 h-7">
                    </div>
                    <div class="relative w-full mr-2">
                        <label for="assign_to"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2  bg-white px-1 text-gray-700': formValues.assign_to || isInputFocused.assign_to, 'text-blue-700': isInputFocused.assign_to }">Assign
                            to
                            <span v-if="formValues.assign_to || isInputFocused.assign_to" class="text-red-600">*</span>
                        </label>
                        <div class="border py-2 px-2 flex flex-wrap"
                            :class="{ 'border-blue-300': isInputFocused.assign_to === true }">

                            <div v-for="(selectedOption, index) in formValues.assign_to" :key="index"
                                class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                {{ selectedOption.name }}
                                <span @click="removeOption(selectedOption)"
                                    class="text-red-500 font-semibold cursor-pointer">x</span>
                            </div>
                            <input type="text" v-model="search" @input="filterOptions" @focus="filterOptions"
                                @click="filterOptions" ref="search" @blur="hideOptions" placeholder="Select staff"
                                class="h-[35px] mt-1 outline-none border rounded-lg px-2"
                                @keydown.enter="handleEnterKey('multiple', filteredOptions())"
                                @keydown.down.prevent="handleDownArrow(filteredOptions())"
                                @keydown.up.prevent="handleUpArrow(filteredOptions())" />
                        </div>
                        <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                            v-if="showOptions" :class="{ 'h-auto': isInputFocused.assign_to === true }">
                            <div v-for="(option, index) in filteredOptions()" :key="index"
                                class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                :class="{ 'bg-green-300': index === selectedIndex }"
                                @click="selectOptionMultiple(option)">
                                {{ option.name }}
                            </div>
                            <button v-if="showAddNew !== null && search.length > 1"
                                class="bg-green-700 text-white hover:bg-green-600 px-3 py-1 rounded-lg"
                                @click="openModalEmployee">Add New</button>
                        </div>
                    </div>
                    <div v-if="(!formValues.assign_to || formValues.assign_to.length === 0) && this.userId"
                        class="absolute  py-2 mt-[80px] ml-11">
                        <button @click="formValues.assign_to = [{ id: this.userId, name: 'Assign to me' }]"
                            class="border border-blue-500 text-blue-500 rounded px-2">Assign to me</button>
                    </div>
                </div>
                <!---AMC Description-->
                <div class="flex items-center mt-10">
                    <div class="mr-2 w-10" :title="'amc_details'">
                        <img :src="notes" alt="amc_details" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="amc_details"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2  bg-white px-1 text-gray-700': formValues.amc_details || isInputFocused.amc_details, 'text-blue-700': isInputFocused.amc_details }">Description</label>
                        <textarea id="amc_details" v-model="formValues.amc_details" rows="3"
                            @focus="isInputFocused.amc_details = true"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"></textarea>
                    </div>
                </div>
                <!---Number of Interval-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'number of interval'">
                        <img :src="interval" alt="number of interval" class="w-7 h-7">
                    </div>
                    <div class="flex w-full mr-2 relative">
                        <label for="number_of_interval"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2  bg-white px-1 text-gray-700': (formValues.number_of_interval !== undefined && formValues.number_of_interval >= 0) || isInputFocused.number_of_interval, 'text-blue-700': isInputFocused.number_of_interval }">
                            No of Intervals
                            <span v-if="formValues.number_of_interval || isInputFocused.number_of_interval"
                                class="text-red-600">*</span>
                        </label>
                        <select id="number_of_interval" v-model="formValues.number_of_interval"
                            class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                            @focus="isInputFocused.number_of_interval = true">
                            <option v-for="num in 12" :key="num" :value="num">{{ num <= 11 ? num + ' Month' : '1 Year'
                            }}</option>
                        </select>
                    </div>
                </div>
                <!---Number of Service-->
                <div class="flex items-center mt-5 ">
                    <div class="mr-2 w-10" :title="'number of service'">
                        <img :src="number_of_service" alt="number of service" class="w-7 h-7">
                    </div>
                    <div class="flex w-full mr-2 relative">
                        <label for="number_of_service"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2  bg-white px-1 text-gray-700': (formValues.number_of_service !== undefined && formValues.number_of_service >= 0) || isInputFocused.number_of_service, 'text-blue-700': isInputFocused.number_of_service }">
                            No of Service
                            <span v-if="formValues.number_of_service || isInputFocused.number_of_service"
                                class="text-red-600">*</span>
                        </label>
                        <select id="number_of_service" v-model="formValues.number_of_service"
                            class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                            @focus="isInputFocused.number_of_service = true">
                            <option v-for="num in 12" :key="num" :value="num">{{ num }}</option>
                        </select>
                    </div>
                </div>
                <!--services-->
                <p v-if="formValues.number_of_interval && formValues.number_of_service"
                    class="font-bold text-sm py-2 mt-3">
                    Up Coming Services:</p>
                <div v-if="formValues.number_of_interval && formValues.number_of_service">

                    <div v-for="(date_description, index) in formValues.date_description" :key="index"
                        class="flex w-full justify-center items-center"
                        :class="{ 'mt-5': index !== 0, 'mt-2': index === 0 }">
                        <div class="mr-2 w-10" :title="'service date'">
                            <img :src="service_date" alt="service date" class="w-7 h-7">
                        </div>
                        <div class="w-full flex justify-center items-center">
                            <div class="w-full mr-2">
                                <label for="date"
                                    class="text-xs font-bold absolute ml-2 mt-3 text-gray-300 transition-top linear duration-300"
                                    :class="{ ' bg-white -mt-[10px] sm:-mt-[8px] px-1 text-blue-700': date_description.date }">Date
                                    <span class="font-light text-gray-400 text-[12px]">(MM/DD/YYYY)</span></label>
                                <input id="date" :value="date_description.date" type="text" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    readonly />
                            </div>
                            <div class="w-full mr-2">
                                <label for="description"
                                    class="text-sm font-bold absolute ml-2 text-gray-300 mt-3 transition-top linear duration-300"
                                    :class="{ ' bg-white px-1 -mt-[10px]  sm:-mt-[8px] text-blue-700': date_description.note || isInputFocused['note' + index] }">
                                    Note
                                </label>
                                <input :id="'description_' + index" v-model="formValues.date_description[index].note"
                                    @mouseover="isInputFocused['note' + index] = true"
                                    @blur="isInputFocused['note' + index] = false"
                                    @mouseout="isInputFocused['note' + index] = false" type="text" placeholder=" "
                                    :ref="'note' + index" @keydown.enter="refTheNextNote(index)"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                            </div>
                        </div>
                    </div>
                </div>
                <p class="font-bold text-sm py-2 mt-3">AMC Products:</p>
                <!-- <p>{{  formValues}}</p> -->
                <!--Product-->
                <div v-for="(opt, index) in formValues.product_lists" :key="index" class="w-full mt-2">
                    <div class="flex justify-center items-center">
                        <div class="mr-2 w-10" :title="'service product'">
                            <img :src="service_product" alt="service product" class="w-7 h-7">
                        </div>
                        <div class="w-full flex justify-center items-center">
                            <div class="w-full mr-2">
                                <!-- Product input field -->
                                <label for="product"
                                    class="text-xs font-bold absolute ml-2 text-gray-300 mt-3 transition-top linear duration-300"
                                    :class="{ ' bg-white px-1 -mt-[15px]  sm:-mt-[8px] text-blue-700': opt.product !== '' || isInputFocused['product' + index] }">
                                    Product<span class="text-gray-300"
                                        :class="{ 'text-red-600': opt.product !== '' || isInputFocused['product' + index] }">*</span>
                                </label>
                                <input v-model="opt.product" class="border text-sm mt-1 rounded w-full p-1 py-2"
                                    :ref="'productInputs' + index" @mouseover="isInputFocused['product' + index] = true"
                                    @blur="isInputFocused['product' + index] = false" />
                                <!-- @mouseover="isInputFocused['product' + index] = true"
                                    @blur="isInputFocused['product' + index] = false"
                                    @mouseout="isInputFocused['product' + index] = false" @input="filterProducts(index)"
                                    @focus="productDropdownIndex = index"
                                    @keydown.enter="handleEnterKey('product', filteredProductList, index)"
                                    @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                    @keydown.up.prevent="handleUpArrow(filteredProductList)"
                                    :ref="'productInputs' + index" /> -->
                                <!-- Product dropdown menu -->
                                <!-- <ul v-if="productDropdownIndex === index && formValues.product_lists[index].product.length > 2 && !showModalProduct"
                                    class="absolute mt-1 max-h-60 w-1/2 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                    style="z-index: 150;">
                                    <li v-if="filteredProductList.length !== 0" class="hover:bg-gray-300 px-3 py-2"
                                        v-for="(product, i) in filteredProductList" :key="i"
                                        :class="{ 'bg-gray-200': i === selectedIndex }"
                                        @click="selectProduct(index, product)">
                                        {{ product.products.product_name }}
                                    </li>

                                    <li v-if="filteredProductList.length === 0 && opt.product !== '' && opt.product.length > 2"
                                        @click=" openModalProduct(index)"
                                        class="new-product-btn text-green-700 hover:bg-gray-300 cusrsor-pointer py-2 px-3">
                                        +
                                        New Product</li>
                                </ul> -->

                            </div>
                            <div class="w-full">
                                <!-- Note input field -->
                                <label for="note"
                                    class="text-xs font-bold absolute ml-2 text-gray-300 mt-3 transition-top linear duration-300"
                                    :class="{ ' bg-white px-1 -mt-[15px]  sm:-mt-[8px] text-blue-700': opt.description || isInputFocused['description' + index] }">Description</label>
                                <input id="description" v-model="opt.description" type="text" placeholder=" "
                                    @mouseover="isInputFocused['description' + index] = true"
                                    @blur="isInputFocused['description' + index] = false"
                                    @mouseout="isInputFocused['description' + index] = false"
                                    @keydown.enter="addProduct(index)"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                            </div>
                            <div class="ml-2 w-10" :title="'delete product'" @click="removeProduct(index)">
                                <img v-if="index !== 0" :src="table_del" alt="delete product" class="w-5 h-5">
                            </div>
                        </div>
                    </div>
                    <div v-if="Array.isArray(formValues.product_lists) && formValues.product_lists.length - 1 === index"
                        class="flex ml-12">
                        <!-- "Add" button to add more products -->
                        <button @click="addProduct(index)"
                            class="mt-1 h-6 w-6 rounded-full font-bold bg-blue-500 text-white flex items-center justify-center focus:outline-none hover:bg-blue-600 cursor-pointer">+</button>

                        <!-- "-" button to remove product -->
                        <button v-if="formValues.product_lists.length > 1" @click="removeProduct(index)"
                            class="mt-1 ml-5 h-6 w-6 rounded-full font-bold bg-blue-500 text-white flex items-center justify-center focus:outline-none hover:bg-blue-600 cursor-pointer">-</button>
                    </div>
                    <p v-if="product_Exist === index && filteredProductList.length === 0 && !findExistItem(index)"
                        class="text-red-700  ml-12">Product already exist..!</p>
                </div>
                <p class="font-bold text-sm py-2 mt-3">AMC Attachments:</p>
                <!---Attachment-->
                <div class="flex items-center">
                    <div class="mr-2 w-10" :title="'attachment image'">
                        <img :src="attachment_img" alt="attachment_img" class="w-7 h-7">
                    </div>
                    <div class="flex justify-center items-center"> <!-- Apply flexbox to this div -->
                        <div class="border rounded p-4 mt-2 relative text-sm flex mr-2">
                            <!-- Add relative positioning for the label -->
                            <label class="text-xs absolute  font-bold bg-white px-1 -mt-[25px] text-blue-700 px-2"
                                for="fileInput">Attachment</label>
                            <input v-if="formValues.amc_attachment === '' || !formValues.amc_attachment" id="fileInput"
                                type="file" @change="handleFileUpload" accept="image/*" class="display">

                            <div v-if="formValues.amc_attachment && formValues.amc_attachment !== ''"
                                class="flex ml-1 text-sm mr-2 items-center cursor-pointer"
                                :title="'On click to view the image'">
                                <img :src="formValues.amc_attachment" alt="Uploaded Image" @click="displayImageModal"
                                    class="mt-2 border rounded w-[50px] h-full">
                            </div>
                            <!-- <input id="fileInput" type="file" @change="handleFileUpload" accept="image/*"
                                class="display">
                            <div v-if="formValues.amc_attachment && formValues.amc_attachment !== ''"
                                class="flex ml-1 text-sm mr-2 items-center justify-center">
                                <img :src="formValues.amc_attachment" alt="Uploaded Image" @click="displayImageModal"
                                    class="mt-2 border rounded w-[50px] h-full">
                            </div> -->
                            <!--loader circle-->
                            <div v-if="circle_loader" class="absolute flex">
                                <CircleLoader :loading="circle_loader"></CircleLoader>
                            </div>
                        </div>
                        <div v-if="formValues.amc_attachment && !circle_loader"
                            class="absolute w-10 justify-right right-2 -mt-[70px]" :title="'delete attachment image'"
                            @click="removeAttachmentImage">
                            <img :src="table_del" alt="delete product" class="w-5 h-5">
                        </div>

                    </div>
                </div>

                <!-- Buttons -->
                <!-- <div class="flex justify-end items-center m-3 mt-5">
                    <button @click="cancelModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  py-2 p-1 pl-10 pr-10 mr-8">Cancel</button>
                    <button @click="sendModal"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white py-2 p-1 pl-10 pr-10 mr-8 ">Save</button>
                </div> -->
            </div>
        </div>
        <!-- Buttons -->
        <div class="fixed bottom-0 bg-white flex justify-center items-center sm:w-1/2 lg:w-1/3 w-full py-1 transform ease-in-out duration-300"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen, 'mb-[70px]': isMobile }">
            <button @click="cancelModal"
                class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white p-1 py-2 pl-10 pr-10 mr-8">Cancel</button>
            <button @click="sendModal"
                class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white p-1 py-2 pl-10 pr-10">Save</button>
        </div>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModal" :userName="customerName"
            :editData="editData" :type="typeOfRegister"></customerRegister>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <employeeRegister :show-modal="showModal_employee" @close-modal="closeModalEmployee" :type="'add'"
            :user_name="EmployeeName">
        </employeeRegister>
        <addNewItem :show-modal="showModalProduct" :product_name="newProduct" @close-modal="addNewProduct">
        </addNewItem>
        <displayImage :showModal="showImageModal" :showImageModal="formValues.amc_attachment"
            @close-modal="closeImageModal">
        </displayImage>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    </div>
    <Loader :showModal="open_loader"></Loader>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>

<script>
import customerRegister from './customerRegister.vue';
import dialogAlert from './dialogAlert.vue';
import employeeRegister from './employeeRegister.vue';
import addName from './addName.vue';
import displayImage from './displayImage.vue';
import axios from 'axios';
import confirmbox from './confirmbox.vue';
import addNewItem from './addNewItem.vue';
import { mapGetters, mapActions } from 'vuex';
import noAccessModel from './noAccessModel.vue';

export default {
    name: 'addLead',
    emits: ['closeAmcModal'],
    components: {
        customerRegister,
        dialogAlert,
        employeeRegister,
        addName,
        displayImage,
        confirmbox,
        addNewItem,
        noAccessModel
    },
    props: {
        showModal: Boolean,
        editData: Object,
        type: String,
        userName: String,
        companyId: String,
        userId: String,
        customer_id: {type: [String, Number]},
    },
    data() {
        return {
            showModal_add_service: false,
            'overlay-active': this.showModal,
            isMobile: false,
            formValues: {},
            isMessageDialogVisible: false,
            message: null,
            isOpen: false,
            updatedData: null,
            //----icons
            user_name: '/images/service_page/User.png',
            date: '/images/service_page/schedule.png',
            assign: '/images/service_page/personService.png',
            statusOf: '/images/service_page/statusIcon.png',
            notes: '/images/service_page/Writing.png',
            category: '/images/service_page/Add.png',
            number_of_service: '/images/service_page/number_of_service.png',
            interval: '/images/service_page/interval.png',
            service_product: '/images/service_page/service_product.png',
            service_date: '/images/service_page/service_date.png',
            attachment_img: '/images/setting_page/Cloud_computing.png',
            table_del: '/images/service_page/del.png',
            amc_payment: '/images/service_page/amc_payment.png',
            amc_title_img: '/images/service_page/issue_description.png',
            //---
            isInputFocused: { assign_to: true, customer: true, title: true, amc_payment_type: true, amc_status: true, amc_details: true, number_of_interval: true, number_of_service: true },
            typeOfRegister: 'add',
            //---
            isDropdownOpen: false,
            customer_list: [],
            filteredCustomerOptions: [],
            customerName: null,
            showModal_customer: false,
            search: '',
            employeeList: [],
            selectedIndex: 0,
            mouseDownOnDropdown: false,
            showAddNew: null,
            showOptions: false,
            filteredEmployee: [],
            showModal_employee: false,
            EmployeeName: '',
            //----------
            showModalProduct: false,
            newProduct: '',
            product: [],
            filteredProductList: [],
            productDropdownIndex: null,
            showImageModal: false,
            product_Exist: false,
            open_confirmBox: false,
            //---api integration---
            pagination_data: {},
            circle_loader: false,
            delete_type: null,
            open_loader: false,
            //--toaster--
            show: false,
            type_toaster: 'warning',
            //---no access---
            no_access: false,
        }
    },
    computed: {
        ...mapGetters('employess', ['currentEmployee']),
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
    },
    methods: {
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('employess', ['fetchEmployeeList']),
        getCurrentDate() {
            const currentDate = new Date();
            const month = String(currentDate.getMonth() + 1).padStart(2, '0');
            const day = String(currentDate.getDate()).padStart(2, '0');
            const year = currentDate.getFullYear();
            return `${year}-${month}-${day}`;
        },
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeAmcModal');
                this.formValues = {};
            }, 300);
        },
        async sendModal() {
            if (this.getplanfeatures('amc')) {
                this.no_access = true;
            } else {

                if (this.formValues.customer && this.formValues.customer !== '' && this.formValues.customer_id && this.formValues.customer_id !== '' && this.formValues.amc_date && this.formValues.amc_status >= 0 && this.formValues.assign_to && this.formValues.number_of_interval && this.formValues.number_of_service && this.formValues.date_description && this.formValues.product_lists && this.formValues.product_lists.length !== 0 && this.formValues.product_lists[0].product !== '' && this.formValues.title && this.formValues.title !== '') {
                    this.open_loader = true;

                    let send_data = JSON.parse(JSON.stringify(this.formValues));

                    if (this.type === 'edit') {
                        if (send_data.assign_to) {
                            send_data.assign_to = JSON.stringify(send_data.assign_to.map(opt => ({ user_id: opt.id })));
                        }
                        if (send_data.date_description) {
                            //---update remaining service remove--
                            const { number_of_interval, number_of_service } = send_data;

                            // Check if there are more descriptions than the number of services
                            if (this.formValues.date_description.length > number_of_service) {
                                let indicesToDelete = [];

                                // Collect IDs of the excess entries
                                for (let i = number_of_service; i < this.formValues.date_description.length; i++) {
                                    if (this.formValues.date_description[i] && this.formValues.date_description[i].id) {
                                        indicesToDelete.push(this.formValues.date_description[i].id);
                                    }
                                }

                                // If there are entries to delete
                                if (indicesToDelete.length > 0) {
                                    try {
                                        // Make the axios delete request
                                        const response = await axios.delete(`amc_dates`, {
                                            data: { ids: indicesToDelete }
                                        });

                                        // console.log(`Successfully deleted entries with IDs: ${indicesToDelete.join(', ')}`);

                                        // Slice the formValues.date_description to keep only the first `number_of_service` entries
                                        this.formValues.date_description = this.formValues.date_description.slice(0, number_of_service);

                                        // Update send_data.date_description with the truncated list
                                        send_data.date_description = JSON.stringify(this.formValues.date_description);
                                    } catch (error) {
                                        console.error(`Failed to delete entries with IDs: ${indicesToDelete.join(', ')}`, error);
                                    }
                                } else {
                                    // No entries to delete, just send the current date_description
                                    send_data.date_description = JSON.stringify(this.formValues.date_description);
                                }
                            } else {
                                // If the number of descriptions is less than or equal to number_of_service, send the current data
                                send_data.date_description = JSON.stringify(this.formValues.date_description);
                            }
                        }
                        if (send_data.product_lists) {
                            send_data.product_lists = JSON.stringify(send_data.product_lists);
                        }
                        if (typeof send_data.amc_status === 'string') {
                            send_data.amc_status = 1 * send_data.amc_status;
                        }
                        if (typeof send_data.amc_payment_type === 'string') {
                            send_data.amc_payment_type = 1 * send_data.amc_payment_type
                        }

                        // console.log(send_data, 'EEEEEE how to get all...!')

                        axios.put(`/amcs/${send_data.id}`, { company_id: this.companyId, user_id: this.userId, ...send_data })
                            .then(response => {
                                // console.log(response.data);
                                this.updatedData = response.data.data;
                                this.open_loader = false;
                                this.openMessageDialog(response.data.message);
                                this.type_toaster = 'success';
                                this.show = true;
                                this.formValues = {};
                            })
                            .catch(error => {
                                console.error('Error', error);
                                this.open_loader = false;
                                this.openMessageDialog(error.response.data.message);
                                this.type_toaster = 'warning';
                                this.show = true;
                            })

                    } else {
                        if (send_data.assign_to) {
                            send_data.assign_to = JSON.stringify(send_data.assign_to.map(opt => ({ user_id: opt.id })));
                        }
                        if (send_data.date_description) {
                            send_data.date_description = JSON.stringify(send_data.date_description);
                        }
                        if (send_data.product_lists) {
                            send_data.product_lists = JSON.stringify(send_data.product_lists);
                        }
                        axios.post('/amcs', { company_id: this.companyId, user_id: this.userId, ...send_data })
                            .then(response => {
                                // console.log(response.data);
                                this.updatedData = response.data.data;
                                this.open_loader = false;
                                this.openMessageDialog(response.data.message);
                                this.type_toaster = 'success';
                                this.show = true;
                                this.formValues = {};
                            })
                            .catch(error => {
                                console.error('Error', error);
                                this.open_loader = false;
                                this.openMessageDialog(error.response.data.message);
                                this.type_toaster = 'warning';
                                this.show = true;
                            })
                    }

                } else {
                    // console.log(this.formValues, 'WWWWWW');
                    if (!this.formValues.customer || this.formValues.customer === '' || !this.formValues.customer_id || this.formValues.customer_id === '') {
                        this.openMessageDialog(`Please select the customer Customer `);
                        this.$refs.customer.focus();
                    }
                    if (!this.formValues.amc_date) {
                        this.openMessageDialog(`Please select the AMC Date`);
                    }
                    if (!this.formValues.amc_status || this.formValues.amc_status < -1) {
                        this.openMessageDialog(`Please select the AMC Status`);
                    }
                    if (!this.formValues.assign_to || this.formValues.assign_to === '') {
                        this.openMessageDialog(`Please select the Assign to Employee`);
                    }
                    if (!this.formValues.number_of_interval) {
                        this.openMessageDialog(`Please select the Number of Intervals`);
                    }
                    if (!this.formValues.number_of_service) {
                        this.openMessageDialog(`Please select the Number of Services`);
                    }
                    if (!this.formValues.date_description) {
                        this.openMessageDialog(`Please check the Date descriptions`);
                    }
                    if (!this.formValues.product_lists || this.formValues.product_lists.length === 0 || this.formValues.product_lists[0].product === '') {
                        this.openMessageDialog(`Please check the Product lists data`);
                    }
                    if (!this.formValues.title || this.formValues.title === '') {
                        this.openMessageDialog(`Please enter the AMC Title`);
                    }

                    this.type_toaster = 'warning';
                    this.show = true;
                }
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },


        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                this.formValues = JSON.parse(JSON.stringify(this.editData));
                // console.log(this.formValues, 'WWWWWW');
                if (!this.formValues.product_lists) {
                    this.formValues.product_lists = [{ product: '', description: '' }];
                }
            } else {

                this.formValues = {};
            }
        },
        //---Add new user--
        userAddNew() {
            // console.log(this.userName);
            if (/^\d+$/.test(this.userName)) {
                this.formValues.contact_number = this.userName;
            } else {
                if (this.userName) {
                    const strSplit = this.userName.split(' ');
                    // console.log(strSplit, 'PPPPPPPP');
                    this.formValues.first_name = strSplit[0];
                    if (strSplit[1]) {
                        this.formValues.last_name = strSplit[1];
                    }
                }
            }
        },
        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.updatedData) {
                this.$emit('closeAmcModal', this.updatedData);
                // this.initializeData();
                this.updatedData = null;
            } else {
                // this.$emit('close-modal');
                // this.initializeData();
            }
        },

        //---customer--
        handleDropdownInput() {
            // console.log(this.customer_list, 'This is customer list..@')
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                    if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                        this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    }
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                            (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                    );
                    if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                        this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    }
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                if (this.customer_list.length > 0) {
                    this.filteredCustomerOptions = this.customer_list;
                } else {
                    this.filteredCustomerOptions = [];
                }
            }

            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray, index) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    this.openModal();
                    // this.openModal(product_name);
                }
            }
            else if (type === 'multiple') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectOptionMultiple(optionArray[this.selectedIndex]);
                }
                else {
                    this.openModalEmployee();
                }
            }
            else if (type === 'product') {
                if (this.filteredProductList.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectProduct(index, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    this.openModalProduct(index);
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        openModal() {
            if (this.type !== 'edit') {
                this.customerName = this.formValues.customer;
                this.showModal_customer = true;
                this.isDropdownOpen = false;
            }
        },
        //----cloase customer register---
        closeModal(data) {
            if (data) {
                // console.log('What happening', data);
                if (data.last_name) {
                    this.formValues.customer = data.first_name + ' ' + data.last_name + ' - ' + data.contact_number;
                    this.formValues.customer_id = data.id;
                } else {
                    this.formValues.customer = data.first_name + ' - ' + data.contact_number;
                    this.formValues.customer_id = data.id;
                }
                this.customer_list.push(data);
                // console.log(this.formValues.customer, 'RWRWRWR')
            }
            this.showModal_customer = false;
        },
        //----customer dropdown--
        closeDropdown(type) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                // this.isInputFocused.customer = false;

            }
        },
        //---customers dropdown
        selectDropdownOption(option) {
            this.formValues.customer = option.first_name + (option.last_name ? ' ' + option.last_name : '') + ' - ' + option.contact_number;
            this.formValues.customer_id = option.id;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
        },
        preventBlur(typeData) {
            if (typeData && typeData === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            }
        },
        //---multiple dropdown---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            // this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.mobile_number + ''.includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination_data.employee.current_page !== this.pagination_data.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.mobile_number && option.mobile_number + ''.includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination_data.employee.current_page !== this.pagination_data.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    }
                } else if (enteredValue.length > 1) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination_data.employee.current_page !== this.pagination_data.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination_data.employee.current_page !== this.pagination_data.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    }
                }
            }
            // Return an empty array if no options match the filter
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.name).includes(option.name)) : this.employeeList;
        },

        selectOptionMultiple(option) {
            // console.log(option, 'WWWWW');
            if (!this.formValues.assign_to) {
                this.formValues.assign_to = []; // Initialize assign_to as an array if it's not already
            }
            this.formValues.assign_to.push(option); // Add the selected option to assign_to
            this.search = ''; // Clear the search input
            this.showOptions = false; // Hide the options dropdown
            this.$nextTick(() => {
                // Focus on the search input after selecting an option
                if (this.$refs['search']) {
                    this.$refs['search'].focus();
                    this.$refs['search'].click();
                }
            });
        },
        removeOption(option) {
            this.formValues['assign_to'] = this.formValues['assign_to'].filter(selected => selected !== option);
        },

        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
                // this.isInputFocused.assign_to = false;
            }, 300); // Add delay to allow click events on options
        },
        openModalEmployee() {
            this.showModal_employee = true;
            // console.log(this.search, 'EEEE');
            this.EmployeeName = this.search;
            this.showOptions = false;
            this.showAddNew = null;
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.customer;
            if (inputField && this.type !== 'edit') {
                inputField.focus();
                inputField.click();
            }
        },
        closeModalEmployee(data) {
            if (data) {
                if (this.formValues.assign_to) {
                    this.formValues.assign_to.push(data);
                } else {
                    this.formValues.assign_to = [];
                    this.formValues.assign_to.push(data);
                }
                this.employeeList.push(data);
            }
            this.showModal_employee = false;
            this.showOptions = false;
            this.search = '';

        },
        //---validate is already exist customer--
        isExistOption() {
            let findDataExist = this.customer_list.find((option) => {
                if (this.formValues['customer']) {
                    let splitData = this.formValues['customer'].split('-');
                    if (splitData[0]) {
                        // let nameValue = data.first_name + (data.last_name ? ' ' + data.last_name : '').toLowerCase();
                        return (option.last_name ? (option.first_name + ' ' + option.last_name).toLowerCase().includes(splitData[0].toLowerCase()) :
                            (option.first_name).toLowerCase().includes(splitData[0].toLowerCase()));
                    }
                }
            });
        },
        // Function to generate dateDescriptionList based on number_of_interval and number_of_service
        generateDateDescriptionList() {
            let dateDescriptionList = [];
            const currentDate = new Date(this.formValues.amc_date);
            const { number_of_interval, number_of_service } = this.formValues;
            // console.log(number_of_interval && number_of_service && this.type !== 'edit', 'if condition....!');
            // console.log(this.type === 'edit' && (number_of_interval !== this.editData.number_of_interval || number_of_service !== this.editData.number_of_service), 'Els if conditions....!!', this.type, 'type', number_of_interval, 'and', this.editData.number_of_interval, 'service', number_of_service, 'and', this.editData.number_of_service);
            if (number_of_interval && number_of_service && this.type !== 'edit') {
                // Get current date              
                // Iterate through intervals and services to generate dates and description
                for (let i = 1; i <= number_of_service; i++) {
                    const date = new Date(currentDate); // Create a new date object based on current date
                    date.setMonth(date.getMonth() + (i * number_of_interval)); // Increment months

                    const formattedDate = `${(date.getDate() < 10 ? '0' : '') + date.getDate()}-${((date.getMonth() + 1) < 10 ? '0' : '') + (date.getMonth() + 1)}-${date.getFullYear()}`; // Format date as per your requirement

                    // Check if the index is within the bounds of the existing list
                    if (i <= this.formValues.date_description.length) {
                        // Update existing entry with an empty note
                        dateDescriptionList.push({
                            date: formattedDate,
                            note: this.formValues.date_description[i - 1].note,
                            updated_by: this.userId
                        });
                    } else {
                        // Add new entry with an empty note
                        dateDescriptionList.push({
                            date: formattedDate,
                            note: '',
                        });
                    }
                }
                // Set dateDescriptionList to update the UI
                this.formValues.date_description = dateDescriptionList;
            } else if (this.type === 'edit' && (number_of_interval !== this.editData.number_of_interval || number_of_service !== this.editData.number_of_service || (number_of_service > 0 && number_of_interval > 0 && this.formValues.date_description === null))) {
                dateDescriptionList = this.formValues.date_description;
                for (let i = 1; i <= number_of_service; i++) {
                    const date = new Date(currentDate); // Create a new date object based on current date
                    date.setMonth(date.getMonth() + (i * number_of_interval)); // Increment months

                    const formattedDate = `${date.getDate()}-${date.getMonth() + 1}-${date.getFullYear()}`; // Format date as per your requirement

                    // Check if the index is within the bounds of the existing list
                    if (this.formValues.date_description !== null && i <= this.formValues.date_description.length) {
                        // Update existing entry with an empty note
                        // dateDescriptionList.push({
                        //     date: formattedDate,
                        //     note: this.formValues.date_description[i - 1].note, 
                        // });
                        // Update existing entry with splice (replace at index `i - 1`)
                        dateDescriptionList.splice(i - 1, 1, {
                            ...this.formValues.date_description[i - 1],
                            date: formattedDate,
                            note: this.formValues.date_description[i - 1].note || '', // Use existing note or empty string
                        });

                    } else {
                        // Add new entry with an empty note
                        dateDescriptionList.push({
                            date: formattedDate,
                            note: '',
                        });
                    }
                }


                this.formValues.date_description = dateDescriptionList;
            }

        },

        //-----filter product--
        filterProducts(index) {
            const enteredProductName = this.formValues.product_lists[index].product.toLowerCase();
            let selectedItem = this.formValues.product_lists[index];
            // console.log(selectedItem, 'waht happening');

            if (this.product.length !== 0 && enteredProductName.length > 1) {
                const existingProducts = this.formValues.product_lists.map((item, i) => index !== i && item.product.toLowerCase());
                // const existingbarcodes = this.formValues.product_lists.map((item, i) => index !== i && item.barcode.toLowerCase());

                this.filteredProductList = this.product.filter((opt) => {

                    const isExistingName = existingProducts.includes(opt.products.product_name.toLowerCase());
                    // const isExistingBarcode = existingbarcodes.includes(opt.barcodes.barcode.toLowerCase());

                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.products.product_name.toLowerCase().includes(enteredProductName);
                    // const barcodeMatch = opt.barcodes.barcode.toLowerCase().includes(enteredProductName);
                    // console.log(index, isExistingName, 'what happening,,,,!', 'is existing', nameMatch, 'name match');
                    this.product_Exist = isExistingName ? index : isExistingName;
                    return (!isExistingName && nameMatch);
                });
            } else {
                this.product_Exist = false;
            }
        },
        //---add product option controll--
        findExistItem(index) {
            const enteredProductName = this.formValues.product_lists[index].product.toLowerCase();

            if (this.product.length !== 0 && enteredProductName.length > 1) {
                const existingProducts = this.formValues.product_lists.map(item => item.product.toLowerCase());
                // const existingBarCodes = this.formValues.product_lists.map(item => item.barcode.toLowerCase());

                // Use a separate array to store existing product names and codes
                const existingNamesAndCodes = [...existingProducts];

                // Check if the entered product name or product code matches any existing product name or product code
                const isExisting = existingNamesAndCodes.includes(enteredProductName);
                // console.log(isExisting, 'Waht happening....!');

                return !isExisting;
            }
            return true;
        },

        selectProduct(index, selectedProduct) {
            console.log(selectedProduct, 'Selected data...@');
            this.formValues.product_lists[index].product_id = selectedProduct.product_id;
            this.formValues.product_lists[index].barcode_id = selectedProduct.barcode_id;
            this.formValues.product_lists[index].product = selectedProduct.products.product_name;
            this.formValues.product_lists[index].barcode = selectedProduct.barcodes.barcode;
            this.productDropdownIndex = null;
            // this.productDropdown = false;           
            this.filteredProductList = []; // Clear the filtered list after selecting a product.
            this.product_Exist = null;
            this.selectedIndex = 0;
        },
        //---add new product---
        //---product name model---
        openModalProduct(index) {
            // console.log(this.product, 'Waht happening...Q');
            // if (!this.product.some((data) => data.product_name.toLowerCase().includes(this.formValues.product_lists[index].product.toLowerCase()))) {

            // console.log(this.items, 'items', index, 'WWWWWWWWWWWWWWWWW');
            // this.newProduct = this.formValues.product_lists[index].product;
            // console.log(index, 'index', this.newProduct, 'Waht happening..!');
            this.newProduct = this.formValues.product_lists[index].product;
            this.selectedIndex = index;
            this.showModalProduct = true;
            // }

        },

        addNewProduct(data) {
            if (data.product_id) {
                // Select the newly added product
                this.selectProduct(this.selectedIndex, data);
                // this.selectProduct[index] = newProduct;
                this.product.push(data);
                this.filteredProductList = [];
                this.productDropdownIndex = null;
            }
            this.showModalProduct = false;
            this.newProduct = '';
        },
        //---
        // Add more product fields
        addProduct(index) {
            this.formValues.product_lists.push({ product: '', description: '' });
            this.$nextTick(() => {
                if (this.$refs['productInputs' + (index + 1)]) {
                    this.$refs['productInputs' + (index + 1)][0].focus();
                    this.$refs['productInputs' + (index + 1)][0].click();
                }
            })
        },
        // Remove product field
        removeProduct(index) {
            // if (this.formValues.product_lists.length > 1) {
            //     this.formValues.product_lists.splice(index, 1);
            // }
            this.selectedIndex = index;
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        deleteRecord() {
            if (this.deleteIndex !== null && this.formValues.product_lists.length > 1) {
                this.formValues.product_lists.splice(this.deleteIndex, 1);
                this.open_confirmBox = false;
            } else if (this.delete_type === 'url') {
                axios.delete('/delete-image', { params: { model: "amc", image_url: this.formValues.amc_attachment } })
                    .then(response => {
                        // console.log(response.data, 'delete image..!');
                        this.open_confirmBox = false;
                        this.formValues.amc_attachment = null;
                        const fileInput = document.getElementById('fileInput');
                        if (fileInput) {
                            fileInput.value = '';
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            } else {
                this.open_confirmBox = false;
            }

        },
        cancelDelete() {
            this.open_confirmBox = false;
            if (this.delete_type === 'url') {
                this.delete_type = null;
            }
        },
        //---attachment--
        openFileInput() {
            // Trigger click event on the hidden file input element
            document.getElementById('fileInput').click();
        },
        async handleFileUpload(event) {
            this.circle_loader = true;
            const file = event.target.files[0];
            if (file && (!this.formValues.amc_attachment || this.formValues.amc_attachment === '')) {

                if (!file) return;

                // Check file size (in bytes)
                const maxSizeBytes = 500 * 1024; // 500kb in bytes
                if (file.size > maxSizeBytes) {
                    // Image exceeds 500kb, compress it
                    try {
                        this.circle_loader = true; // Show loader
                        const compressedFile = await this.compressImage(file);

                        this.uploadImageProfile(compressedFile);
                    } catch (error) {
                        console.error("Error compressing image:", error);
                        this.circle_loader = false; // Hide loader on error
                    }
                } else {
                    // Image is <= 500kb, upload directly
                    try {
                        this.circle_loader = true; // Show loader

                        this.uploadImageProfile(file);
                    } catch (error) {
                        console.error("Error uploading image:", error);
                        this.circle_loader = false; // Hide loader on error
                    }
                }

            } else {
                this.openMessageDialog('Please delete Exist attachment..!');
                this.circle_loader = false;
            }
        },
        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        uploadImageProfile(file) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "amc");
            formData.append("company_id", this.companyId);
            // Make an API request to Laravel backend
            // console.log(file, 'RRRR');
            axios.post('/image', formData)
                .then(response => {
                    // console.log(response.data, 'What happrning....Q');
                    this.circle_loader = false;
                    //  let data_save = { image_path: response.data.image_path };
                    this.formValues.amc_attachment = response.data.media_url;
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                    this.openMessageDialog(Array.isArray(error.response.data.error.image) ? error.response.data.error.image[0] : 'The image must be a file of type: jpeg, png, gif, pdf, txt, csv');
                    this.circle_loader = false;
                });
        },
        //---display the image---
        displayImageModal() {
            if (this.formValues.amc_attachment) {
                this.showImageModal = true;
                // window.open(this.formValues.amc_attachment, '_blank');
            }
        },
        closeImageModal() {
            this.showImageModal = false;
        },
        //---remove attachment--
        removeAttachmentImage() {
            if (this.formValues.amc_attachment) {
                this.delete_type = 'url';
                this.open_confirmBox = true;
            }
        },
        //--- make reference the next field--
        refTheNextNote(index) {
            this.$nextTick(() => {
                if (this.$refs['note' + (index + 1)] && this.formValues.date_description.length >= ((1 * index) + 1)) {
                    this.$refs['note' + (index + 1)][0].focus();
                    this.$refs['note' + (index + 1)][0].click();
                } else {
                    if (this.$refs['productInputs' + 0]) {
                        this.$refs['productInputs' + 0][0].focus();
                        this.$refs['productInputs' + 0][0].click();
                    }
                }
            })
        },
        getCustomerList(page, per_page) {
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'customer');
                    this.customer_list = response.data.data;
                    this.pagination_data.customer = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        getEmployeeList(page, per_page) {
            axios.get('/employees', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // Handle response
                    // console.log(response.data.data);
                    this.employeeList = response.data.data;
                    this.pagination_data.employee = response.data.pagination;
                    this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
                    if (this.employeeList.length > 2) {
                        // Sorting the array alphabetically based on the 'name' key
                        this.employeeList.sort((a, b) => {
                            // Convert both names to lowercase to ensure case-insensitive sorting
                            const nameA = a.name.toLowerCase();
                            const nameB = b.name.toLowerCase();
                            // Compare the two names
                            if (nameA < nameB) {
                                return -1;
                            }
                            if (nameA > nameB) {
                                return 1;
                            }
                            return 0;
                        });
                    }
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        getItemsList(page, per_page) {
            axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'items......!');
                    this.product = response.data.data;
                    this.pagination_data.item = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        getBasicData() {
            const currentDate = new Date();
            const year = currentDate.getFullYear();
            const month = String(currentDate.getMonth() + 1).padStart(2, '0');
            const day = String(currentDate.getDate()).padStart(2, '0');
            // this.formValues.amc_date = `${year}-${month}-${day}`;
            if (this.type !== 'edit') {
                // console.log('TTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT');
                this.formValues = {};
                this.formValues.product_lists = [{ product: '', description: '' }];
                this.formValues.date_description = [];
                this.formValues.amc_date = `${year}-${month}-${day}`;
            }
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        }
    },
    mounted() {

        //----get current date--
        // Set the default value of amc_date to the current date in the format 'YYYY-MM-DD'
        const currentDate = new Date();
        const year = currentDate.getFullYear();
        const month = String(currentDate.getMonth() + 1).padStart(2, '0');
        const day = String(currentDate.getDate()).padStart(2, '0');
        // this.formValues.amc_date = `${year}-${month}-${day}`;
        if (this.type !== 'edit') {
            // console.log('TTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTTT');
            this.formValues = {};
            this.formValues.product_lists = [{ product: '', description: '' }];
            this.formValues.date_description = [];
            this.formValues.amc_date = `${year}-${month}-${day}`;
        }
        this.updateIsMobile();
        // Use nextTick to wait for the DOM to be updated before accessing the input field
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            if (this.currentCustomer && this.currentCustomer.length === 0) {
                this.fetchCustomerList();
            } else {
                this.fetchCustomerList();
            }
            if (this.currentEmployee && this.currentEmployee.length === 0) {
                this.fetchEmployeeList();
            } else {
                this.fetchEmployeeList();
            }
            //---select customer----
            if (this.customer_list && this.customer_list.length > 0 && newValue && this.customer_id) {
                let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                if (selected_customer) {
                    this.selectDropdownOption(selected_customer);
                }
            }
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                    if (this.type !== 'edit' && !this.formValues.amc_date) {
                        this.getBasicData();
                    }
                    this.updateIsMobile();
                    if (this.customer_list.length === 0) {
                        // this.getCustomerList(1, 'all');
                        if (this.currentCustomer && this.currentCustomer.length > 0) {
                            this.customer_list = this.currentCustomer;
                        }
                    }
                    if (this.employeeList.length === 0) {
                        // this.getEmployeeList(1, 'all');
                        if (this.currentEmployee && this.currentEmployee.length > 0) {
                            this.employeeList = this.currentEmployee;
                        }
                    }
                    /* if (this.product.length === 0) {
                         this.getItemsList(1, 1000);
                     }*/
                }
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
        userName: {
            handler() {
                this.userAddNew();
                // this.handleFocus();
            },
        },
        // Watch for changes in number_of_interval and number_of_service
        'formValues.number_of_interval': 'generateDateDescriptionList',
        'formValues.number_of_service': 'generateDateDescriptionList',
        'formValues.amc_date': 'generateDateDescriptionList',
        currentCustomer: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.customer_list = newValue;
                    if (this.customer_list && this.customer_list.length > 0 && this.customer_id) {
                        let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                        if (selected_customer) {
                            this.selectDropdownOption(selected_customer);
                        }
                    }
                }
            }
        },
        currentEmployee: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.employeeList = newValue;
                }
            }
        },
        customer_id: {
            deep: true,
            handler(newValue) {
                if (newValue && this.customer_list && this.customer_list.length > 0) {
                    let selected_customer = this.customer_list.find(opt => opt.id == this.customer_id);
                    if (selected_customer) {
                        this.selectDropdownOption(selected_customer);
                    }
                }
            }
        }
    }
}
</script>

<style scoped>
/* Modal styling */
.modal {
    position: fixed;
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Make modal content scrollable on mobile */
.modal-content {
    overflow-y: auto;
    /* Add this line to make the content scrollable */
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;
    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>