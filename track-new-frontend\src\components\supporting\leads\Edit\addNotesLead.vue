<template>
    <!--loader-->
    <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
        :rows="number_of_rows" :gap="gap" :type="'grid'">
    </skeleton>
    <div class="my-custom-margin">
        <div v-if="!open_skeleton" :class="{ 'mb-[70px] mt-[50px]': isMobile, }">
            <!-- <div class=" -mt-2">
            <button class="font-bold hover:bg-gray-500 bg-gray-600 text-white px-2 py-2 rounded"
                @click="$router.go(-1)">&#8592; Go Back</button>
        </div> -->
            <!---Lead date-->
            <div v-if="record" class="mb-3">
                <p class="text-center text-lg font-bold text-sky-700 underline">{{ record.title }}</p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4">
                <div v-for="(column, colIndex) in dynamicFields" :key="colIndex"
                    :class="{ 'hidden': !column.visible || column.field === 'title' }"
                    class="border px-4 py-2 text-sm rounded lg:py-3 bg-zinc-100 relative"
                    style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);">
                    <div class="flex items-center">
                        <div class="mr-2 w-10 flex justify-center items-center" :title="'customer'">
                            <img :src="column.label === 'Customer' ? user_name : column.label === 'Lead Date' || column.label === 'Assign Date' ? date : column.label === 'Lead Type' ? category : column.label === 'Description' ? notes : column.label === 'Notes' ? notes : column.label === 'Assign To' ? assign : column.field === 'source' ? source_img : statusOf"
                                alt="customer" class="w-7 h-7">
                        </div>
                        <span
                            v-if="!Array.isArray(record[column.field]) && column.field !== 'lead_type' && column.field !== 'lead_status' && column.field !== 'assign_date' && column.field !== 'lead_date' && column.field !== 'customer'">
                            <span class="font-bold"> {{ column.label }} </span> : {{ record[column.field] }}</span>
                        <span v-if="column.field === 'lead_type'">
                            <span class="font-bold"> {{ column.label }} </span> :
                            {{ record[column.field].name }}</span>
                        <span v-if="column.field === 'lead_status'">
                            <span class="font-bold"> {{ column.label }} </span> :
                            {{ record[column.field] == '0' ? 'open' : record[column.field] == '1' ? 'Progress' :
                                record[column.field] == '2' ? 'Completed' : record[column.field] == '3' ? 'Cancelled' :
                                    record[column.field] == '4' ? 'Hold' : ''
                            }}</span>
                        <span v-if="column.field === 'assign_date' || column.field === 'lead_date'">
                            <span class="font-bold"> {{ column.field === 'lead_date' ? 'Created On' : column.label }}
                            </span> :
                            {{ formatDateCreated(record[column.field]) }}
                        </span>
                        <span v-if="column.field === 'customer'"><span class="font-bold"> {{ column.label }} </span> :
                            {{ record[column.field].last_name ? record[column.field].first_name +
                                '' + record[column.field].last_name + ' - ' + record[column.field].contact_number :
                                record[column.field].first_name + ' - ' + record[column.field].contact_number }}</span>
                        <span v-if="Array.isArray(record[column.field])">
                            <span class="font-bold"> {{ column.field === 'assign_to' ? 'Assigned to' : column.label }}
                            </span> : {{record[column.field].map(opt =>
                                opt.name).join(',')
                            }}</span>
                        <button
                            v-if="column.field === 'description' || column.field === 'notes' || column.field === 'assign_to'"
                            @click="openNotesModal(column)"
                            class="absolute top-1 right-1 hover:text-blue-600"><font-awesome-icon
                                icon="fa-solid fa-pencil" /></button>
                    </div>
                </div>
            </div>

            <!---Lead update-->
            <div class="mt-5">
                <div class="rounded shadow-lg p-2 bg-zinc-100 px-4 border">
                    <!-- <p class="font-bold text-center underline">Lead Update</p> -->
                    <!---lead Status-->
                    <div class="mt-1">
                        <div class="mr-2 flex items-center" :title="'lead status'">
                            <img :src="statusOf" alt="lead_status" class="w-7 h-7">
                            <label class="text-blue-700 font-bold ml-5">Lead status</label>
                        </div>
                        <div class="flex mt-2 ml-12">
                            <div class="mb-2 grid grid-cols-2 gap-2 sm:grid-cols-5">
                                <div v-for="(option, index) in option_status" :key="index"
                                    :class="{ 'hidden': formValues.leadstatus_id > 0 && index === 0 }">
                                    <label :for="'option' + index"
                                        :class="{ 'bg-stone-300 text-gray-700': formValues.leadstatus_id !== index, 'bg-gray-600 text-white': formValues.leadstatus_id === index }"
                                        class="w-full px-4 py-2 cursor-pointer inline-block rounded-md transition-colors duration-300 hover:bg-gray-400 hover:text-white">
                                        {{ option }}
                                        <input type="radio" :id="'option' + index" :value="index"
                                            v-model="formValues.leadstatus_id"
                                            :class="{ 'hidden': formValues.leadstatus_id !== index, }" />
                                    </label>
                                </div>
                            </div>
                        </div>
                        <!---add note-->
                    </div>
                    <div v-if="formValues.leadstatus_id && (formValues.leadstatus_id === 3 || formValues.leadstatus_id === 2 || formValues.leadstatus_id === 4)"
                        class="flex items-center mt-5">
                        <div class="mr-2 w-10" :title="'notes'">
                            <img :src="notes" alt="notes" class="w-7 h-7">
                        </div>
                        <div class="w-full mr-2 relative">
                            <!-- {{ formValues.leadstatus_id === 3 ? 'Cancelled Note' : formValues.leadstatus_id === 4 ?
                                    'Hold Note' : 'Completed Note' }} -->
                            <label for="notes"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.notes || isInputFocused.notes, 'text-blue-700': isInputFocused.notes }">
                                Notes</label>
                            <textarea id="notes" v-model="formValues.notes" rows="2"
                                @focus="isInputFocused.notes = true" @input="validateIsExist"
                                class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"></textarea>
                        </div>
                    </div>

                    <!-- Buttons -->
                    <div v-if="formValues.leadstatus_id && (formValues.leadstatus_id === 3 || formValues.leadstatus_id === 2 || formValues.leadstatus_id === 4) && display_button"
                        class="flex justify-center items-center m-3 mt-5 py-3">
                        <button @click="resetForm"
                            class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  p-1 py-2 pl-10 pr-10 mr-8">Reset</button>
                        <button @click="sendData"
                            class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white p-1 pl-10 pr-10 mr-8 py-2">
                            {{ editFollowUpIndex === null && formValues.leadstatus_id !== 2 && formValues.leadstatus_id
                                !==
                                3 && formValues.leadstatus_id !== 4
                                ? 'Create' : 'Update' }}</button>
                    </div>


                </div>
            </div>
            <!-- next followup-->
            <div v-if="formValues && (formValues.leadstatus_id === 0 || formValues.leadstatus_id === 1) && !isMobile"
                :class="{ 'rounded shadow-lg p-2 bg-zinc-100 px-4 border mt-5': !isMobile, 'fixed bottom-0 left-0 right-0 bg-white z-50 py-1': isMobile }">
                <div :class="{ 'flex items-center justify-center  py-3': !isMobile, 'grid grid-cols-2': isMobile }">
                    <button v-if="isMobile" @click="dialPhoneNumber(record.customer.contact_number)"
                        class="text-white bg-gradient-to-br from-purple-600 to-blue-500 hover:bg-gradient-to-bl focus:ring-1 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm px-2 py-2.5 text-center me-2 ml-2">
                        <font-awesome-icon icon="fa-solid fa-phone" size="lg" class="pr-2" /> Call </button>
                    <button @click="addOpenFollowup"
                        class="text-white bg-gradient-to-br from-purple-600 to-blue-500 hover:bg-gradient-to-bl focus:ring-1 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm px-2 py-2.5 text-center me-2">
                        <font-awesome-icon icon="fa-solid fa-headset" size="lg" class="pr-1" /> Create Next
                        Followup</button>
                </div>
            </div>
            <div v-if="formValues && isMobile"
                :class="{ 'rounded shadow-lg p-2 bg-zinc-100 px-4 border mt-5': !isMobile, 'fixed bottom-0 left-0 right-0 bg-white z-50 py-1': isMobile }">
                <div :class="{ 'flex items-center justify-center  py-3': !isMobile, 'grid grid-cols-2': isMobile }">
                    <button v-if="isMobile" @click="dialPhoneNumber(record.customer.contact_number)"
                        class="text-white bg-gradient-to-br from-purple-600 to-blue-500 hover:bg-gradient-to-bl focus:ring-1 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm px-2 py-2.5 text-center me-2 ml-2">
                        <font-awesome-icon icon="fa-solid fa-phone" size="lg" class="pr-2" /> Call </button>
                    <button @click="addOpenFollowup"
                        :class="{ 'grayscale-0': formValues.leadstatus_id === 0 || formValues.leadstatus_id === 1, 'grayscale': formValues.leadstatus_id !== 0 && formValues.leadstatus_id !== 1 }"
                        class="text-white bg-gradient-to-br from-purple-600 to-blue-500 hover:bg-gradient-to-bl focus:ring-1 focus:outline-none focus:ring-blue-300 dark:focus:ring-blue-800 font-medium rounded-lg text-sm px-2 py-2.5 text-center me-2">
                        <font-awesome-icon icon="fa-solid fa-headset" size="lg" class="pr-1" /> Create Next
                        Followup</button>
                </div>
            </div>

            <!---followup details--->
            <div v-if="record && Array.isArray(record.follow_up) && record.follow_up.length > 0"
                class="rounded shadow-lg p-2 bg-zinc-100 px-4 border mt-5">
                <p class="justify-center text-center underline text-md font-bold mb-5">Follow Up details</p>
                <div :key="refreshKey" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4">
                    <div v-for="(follow, index) in record.follow_up" :kex="index"
                        class="flex justify-between items-center border py-3 rounded rounded-lg px-3 bg-[#dbeafe]"
                        style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);">
                        <div class="w-full">
                            <p v-if="follow.updated_at" class="flex items-center py-1">
                                <font-awesome-icon icon="fa-regular fa-clock" size="lg" style="color: #2563eb;"
                                    class="pr-2" />
                                <span class="text-sky-700 font-bold">{{ getdaysByAgo(follow.updated_at) }}</span>
                            </p>
                            <div class="flex py-1 items-center">
                                <font-awesome-icon icon="fa-regular fa-message" size="lg" style="color: #2563eb;"
                                    class="pr-2" />
                                {{ follow.description }}
                            </div>
                            <div class="py-1 flex items-center">
                                <font-awesome-icon icon="fa-solid fa-repeat" size="lg" style="color: #2563eb;"
                                    class="pr-2" />
                                <span ref="follow_upDateElement"
                                    :class="{ 'text-green-700 font-bold': index === 0 && (new Date(follow.date_and_time) > new Date()), 'text-red-700': index !== 0 || !(index === 0 && (new Date(follow.date_and_time) > new Date())) }">
                                    {{ getNextFollowUpDate(follow.date_and_time) }} </span>
                            </div>
                            <p class="py-1 flex items-center" v-if="follow.updated_by && follow.updated_by.name">
                                <font-awesome-icon icon="fa-solid fa-user-tie" size="lg" style="color: #2563eb;"
                                    class="pr-2" />
                                {{ follow.updated_by.name }}
                            </p>
                        </div>
                        <!---Edit and delete-->
                        <!-- <div class="flex justify-center ml-2 mr-2 w-1/10">
                        <button v-if="!record.editing" @click="editFollowup(index)"
                            class="text-blue-700 px-1 py-1 mr-2 hover:text-blue-600">
                            <i class="material-icons">
                                edit
                            </i>
                        </button>
                        <button v-if="!record.editing" @click="deleteFollowUp(index)"
                            class="text-red-700 hover:text-red-600">
                            <i class="material-icons">
                                delete
                            </i>
                        </button>
                    </div> -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
    <!-- <employeeRegister :show-modal="showModal_employee" @close-modal="closeModalEmployee" :type="'add'"
        :user_name="EmployeeName">
    </employeeRegister> -->
    <leadTypeAndStatusList :showModal="leadTypeOrStatusList" :type="typeOfList"
        :categoriesData="typeOfList === 'type' ? leadType : leadStatus" @closeModal="closeLeadList">
    </leadTypeAndStatusList>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    <Loader :showModal="open_loader"></Loader>
    <addFollowup :showModal="open_followup" :companyId="companyId" :userId="userId" :lead_data="formValues"
        @close-modal="closeFollowup" :record="record" :updated_by="updated_by" :getplanfeatures="getplanfeatures"
        @openNoAccess="openNoAccess">
    </addFollowup>
    <addNotes :showmodal="openNotes" :lable="label_name" @close-modal="closeNotesModal" :data="record"></addNotes>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>
<script>
import dialogAlert from '../../dialog_box/dialogAlert.vue';
// import employeeRegister from '../../dialog_box/employeeRegister.vue';
import leadTypeAndStatusList from '../../dialog_box/leadTypeAndStatusList.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import addFollowup from '../../dialog_box/addFollowup.vue';
import addNotes from '../../dialog_box/addNotes.vue';
import noAccessModel from '../../dialog_box/noAccessModel.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'LeadUpdate',
    emits: ['updateIsOpen'],
    components: {
        dialogAlert,
        // employeeRegister,
        leadTypeAndStatusList,
        confirmbox,
        addFollowup,
        addNotes,
        noAccessModel
    },
    props: {
        isMobile: Boolean,
        updateModalOpen: Boolean,
        reload_data: Boolean
    },
    data() {
        return {
            data: [],
            leadStatus: [],
            leadType: [],
            columns: [],
            record: null,
            formValues: {},
            isInputFocused: { follow_date: true, notes: true, follow_des: true },
            option_status: ['Open', 'Progress', 'Completed', 'Cancelled', 'Hold'],
            search: '',
            showOptions: false,
            leadStatusList: false,
            employeeList: [],
            showAddNew: false,
            selectedIndex: 0,
            typeOfList: '',
            leadTypeOrStatusList: false,
            isMessageDialogVisible: false,
            showModal_employee: false,
            EmployeeName: '',
            message: '',
            deleteIndex: null,
            editFollowUpIndex: null,
            open_confirmBox: false,
            //---relaod--
            refreshKey: 0,
            //----icons
            user_name: '/images/service_page/User.png',
            date: '/images/service_page/schedule.png',
            assign: '/images/service_page/personService.png',
            statusOf: '/images/service_page/statusIcon.png',
            notes: '/images/service_page/Writing.png',
            category: '/images/service_page/Add.png',
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            source_img: '/images/service_page/cooperation.png',
            //--api integration--
            companyId: null,
            userId: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 20,
            gap: 5,
            open_loader: false,
            updated_by: null,
            now: null,
            open_followup: false,
            display_button: false,
            //---add notes--
            openNotes: false,
            label_name: '',
            //---no access---
            no_access: false,
        };
    },
    created() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
            this.updated_by = { id: dataParse.user_id, name: dataParse.name ? dataParse.name : '' };
        }
        //---get lead data---
        this.getLeadData();
        // this.getLeadStatus();
        this.getLeadType();
        // this.getEmployeeList();
        // Update current time every second
        setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        dynamicFields() {
            const fields = [];
            const key_order = ['customer', 'lead_date', 'title', 'assign_to', 'assign_date', 'lead_type', 'description', 'notes', 'follow_up', 'lead_status', 'source']

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            // console.log(this.data, 'WWWWWWWW happening ....@');
            if (this.data) {
                // for (const key in this.record) {
                //     if (key !== 'id' && key !== 'follow_up' && key !== 'created_at' && key !== 'updated_at' && key !== 'deleted_at' && key!== 'assign_date') { // Exclude the 'id' field
                //         const label = formatLabel(key);
                //         fields.push({ label, field: key, visible: true });
                //     }
                // }
                for (const key of key_order) {
                    if (key !== 'id' && key !== 'follow_up' && key !== 'created_at' && key !== 'updated_at' && key !== 'deleted_at' && key !== 'assign_date' && key !== 'lead_status') {
                        const label = formatLabel(key);
                        //---key !== 'description' && key !== 'lead_type' &&
                        if (key !== 'assign_date') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('companies', ['fetchCompanyList']),
        //---add next followup
        addOpenFollowup() {
            if (this.getplanfeatures('leads')) {
                this.no_access = true;
            } else {
                if (this.formValues.leadstatus_id === 0 || this.formValues.leadstatus_id === 1) {
                    this.open_followup = true;
                }
            }
        },
        closeFollowup(data) {
            // console.log(data, 'WWWWWWWWWW', this.record && this.record.follow_up && Array.isArray(this.record.follow_up) && this.record.follow_up.length ===  0 && data.follow_up);
            this.open_followup = false;
            if (this.record && this.record.follow_up && Array.isArray(this.record.follow_up) && this.record.follow_up.length >= 0 && data && data.follow_up) {
                // console.log(data.follow_up, 'Before update:', this.record.follow_up);
                this.record.follow_up = data.follow_up;
                // console.log(data.follow_up, 'After update:', this.record.follow_up);
                if (this.record.lead_status === 0) {
                    this.record.lead_status = data.lead_status;
                    this.formValues.leadstatus_id = data.lead_status;
                }
            } else {
                this.reloadDiv()
            }
            // console.log(this.record.follow_up, 'Followup....');
        },
        //---employee list
        getEmployeeList() {
            axios.get('/employees', { params: { company_id: this.companyId, page: 1, per_page: 'all' } })
                .then(response => {
                    // console.log(response.data.data, 'employee');
                    this.employeeList = response.data.data;
                    if (this.record && this.record.assign_to && Array.isArray(this.record.assign_to)) {
                        let find_duplicate = this.record.assign_to.find(opt => opt.id == this.userId);
                        if (this.userId && !find_duplicate) {
                            this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
                        }
                    } else {
                        this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
                    }
                    if (this.employeeList.length > 2) {
                        // Sorting the array alphabetically based on the 'name' key
                        this.employeeList.sort((a, b) => {
                            // Convert both names to lowercase to ensure case-insensitive sorting
                            const nameA = a.name.toLowerCase();
                            const nameB = b.name.toLowerCase();
                            // Compare the two names
                            if (nameA < nameB) {
                                return -1;
                            }
                            if (nameA > nameB) {
                                return 1;
                            }
                            return 0;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error get employee', error);
                })
        },
        //---lead type---
        getLeadType() {
            axios.get('/lead_types', {
                params: {
                    company_id: this.companyId
                }
            })
                .then(response => {
                    // Handle response
                    // console.log(response.data.data, 'type');
                    this.leadType = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        //---lead status----
        getLeadStatus() {
            axios.get('/lead_statuses', {
                params: {
                    company_id: this.companyId
                }
            })
                .then(response => {
                    // Handle response
                    console.log(response.data.data, 'status');
                    this.leadStatus = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        //----get leat data by id---
        getLeadData() {
            if (this.getplanfeatures('leads')) {
                this.no_access = true;
            } else {
                this.open_skeleton = true;
                //---lead data List--
                axios.get(`/leads/${this.$route.query.recordId}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.data.data, 'YYYYYYYYYYYYYYYYY');
                        this.record = response.data.data;
                        this.open_skeleton = false;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },

        //---Lead list for type and status--
        closeLeadList(data) {
            // console.log(data, 'Waht happening..........by closing the modal..!');
            if (data !== '' && data) {
                if (this.typeOfList === 'type') {
                    // console.log('This is type of lead...!');
                    data.forEach((dataVal) => {
                        let isNotAlreadyExist = this.leadType.findIndex((opt) => opt.name === dataVal)
                        if (isNotAlreadyExist !== -1) {
                            this.leadType.push(dataVal);
                            console.log(this.leadStatus, 'Status', this.leadType);
                        }
                    });
                } else if (this.typeOfList === 'status') {
                    data.forEach((dataVal) => {
                        let isNotAlreadyExist = this.leadStatus.findIndex((opt) => opt.name === dataVal)
                        if (isNotAlreadyExist !== -1) {
                            this.leadStatus.push(data);
                        }
                    });
                }
            }
            this.leadTypeOrStatusList = false;
        },
        //---Add lead Type or Status
        openLeadList(type) {
            // console.log(type, 'What happening...!');
            this.typeOfList = type;
            this.leadTypeOrStatusList = true;
        },
        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },
        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
        },

        sendData(type, data) {
            if (this.getplanfeatures('leads')) {
                this.no_access = true;
            } else {
                if (this.formValues && this.formValues.leadstatus_id && this.formValues.leadstatus_id !== this.record.lead_status) {
                    axios.put(`/leads/${this.record.id}`, { company_id: this.companyId, leadstatus_id: this.formValues.leadstatus_id, notes: this.formValues && this.formValues.notes ? this.formValues.notes : '' })
                        .then(response => {
                            // console.log(response.data.data, 'Response..........');
                            this.updatedData = response.data.data;
                            this.open_loader = false;
                            if (type !== 'del') {
                                this.openMessageDialog(response.data.message);
                            }
                            // this.formValues = {};
                            //---get lead data---
                            // this.getLeadData();
                            this.record = response.data.data;
                            this.display_button = false;
                            // this.getDefaultDate();
                        })
                        .catch(error => {
                            console.error('Error for post', error);
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                        })
                }
                // if ((((this.formValues.follow_des && this.formValues.follow_date) || type === 'del') && (this.formValues.assign_to && this.formValues.assign_to.length > 0)) || this.formValues.leadstatus_id === 3 || this.formValues.leadstatus_id === 2 || this.formValues.leadstatus_id === 4) {
                //     this.open_loader = true;
                //     let send_data = { ...this.formValues, id: this.record.id, follow_up: this.record.follow_up };
                //     if(send_data.assign_to && send_data.assign_to.length === 0 ){
                //         delete send_data.assign_to;
                //     }

                //     if (send_data.assign_to && send_data.assign_to.length > 0) {
                //         send_data.assign_to = send_data.assign_to.map(opt => opt.id).join(', ');
                //     }
                //     // console.log(send_data, 'Befor....');
                //     if (send_data.follow_up && type !== 'del' && send_data.leadstatus_id !== 3 &&  send_data.leadstatus_id !== 2 && this.formValues.leadstatus_id !== 4) {
                //         // console.log('It is true 11111');
                //         if (Array.isArray(send_data.follow_up) && send_data.follow_up.length > 0) {
                //             if (this.editFollowUpIndex !== null && this.editFollowUpIndex !== undefined && this.editFollowUpIndex >= 0) {
                //                 send_data.follow_up[this.editFollowUpIndex] = { description: this.formValues.follow_des, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.formattedDateTime() };
                //                 this.editFollowUpIndex = null;
                //             } else {
                //                 send_data.follow_up.unshift({ description: this.formValues.follow_des, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.formattedDateTime() });
                //             }
                //         } else {
                //             send_data.follow_up = [{ description: this.formValues.follow_des, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.formattedDateTime() }];
                //         }
                //         send_data.follow_up = send_data.follow_up;
                //     } else if (this.formValues.follow_des && this.formValues.follow_date && send_data.leadstatus_id !== 3 && send_data.leadstatus_id !== 2 && this.formValues.leadstatus_id !== 4) {
                //         console.log('It is two true....');
                //         send_data.follow_up = [{ description: this.formValues.follow_des, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.formattedDateTime() }];
                //     } else {
                //         send_data.follow_up = data;
                //         if(send_data.follow_date){
                //             delete send_data.follow_date;
                //         }
                //     }
                //     if (send_data.leadstatus_id && send_data.leadstatus_id !== 3 && send_data.leadstatus_id !== 2 && send_data.notes && this.formValues.leadstatus_id !== 4) {
                //         delete send_data.notes;
                //     }
                //     // if (!send_data.customer_id) {
                //     //     send_data.customer_id = send_data.customer.id;
                //     // }
                //     //---update the data---
                //     axios.put(`/leads/${send_data.id}`, { ...send_data, cmpany_id: this.companyId, follow_up: JSON.stringify(send_data.follow_up) })
                //         .then(response => {
                //             // console.log(response.data.data, 'Response..........');
                //             this.updatedData = response.data.data;
                //             this.open_loader = false;
                //             if (type !== 'del') {
                //                 this.openMessageDialog(response.data.message);
                //             }
                //             this.formValues = {};
                //             //---get lead data---
                //             this.getLeadData();
                //             this.getDefaultDate();
                //         })
                //         .catch(error => {
                //             console.error('Error for post', error);
                //             this.open_loader = false;
                //             this.openMessageDialog(error.response.data.message);
                //         })

                // } else {
                //     if (!this.formValues.follow_des) {
                //         this.openMessageDialog(`Please fill the Description fields`);
                //         this.$refs.description.focus();
                //     } else if (!this.formValues.follow_date) {
                //         this.openMessageDialog(`Please select the next followup date and time`);
                //         this.$refs.dateandtime.focus();
                //     } else if (!this.formValues.assign_to || this.formValues.assign_to.length === 0) {
                //         this.openMessageDialog(`Please assign the lead to employee`);
                //         this.$refs.search.focus();
                //     }
                // }
            }
        },

        //----delete functions---
        deleteFollowUp(index) {
            // Delete category

            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                if (Array.isArray(this.record.follow_up)) {
                    this.record.follow_up.splice(this.deleteIndex, 1); // Remove the element at deleteIndex
                    // Log the resulting array
                    this.sendData('del', this.record.follow_up);
                    this.open_confirmBox = false; // Close the confirmation box
                    this.deleteIndex = null; // Reset deleteIndex
                } else {
                    console.error('record.follow_up is not an array');
                }
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---edit followup
        editFollowup(index) {
            // console.log(index);
            this.formValues.follow_des = this.record.follow_up[index]['description'];
            // console.log(this.record.follow_up[index]['date_and_time'], 'Waht happening...!!!!!');
            try {
                this.formValues.follow_date = this.convertDateFormat(this.record.follow_up[index]['date_and_time']);
            } catch (error) {
                console.error('Error', error);
            }
            this.editFollowUpIndex = index;
            // console.log(index, 'WWWWWW');
        },

        getNextFollowUpDate(dateValue) {
            // Given input date string
            let inputDateString = dateValue;

            // Create a Date object from the input string
            let date = new Date(inputDateString);

            // Extract date components
            let day = String(date.getDate()).padStart(2, '0');
            let month = String(date.getMonth() + 1).padStart(2, '0'); // Add 1 because months are zero-based
            let year = date.getFullYear();

            // Extract time components
            let hours = String(date.getHours() % 12 || 12).padStart(2, '0'); // Convert to 12-hour format
            let minutes = String(date.getMinutes()).padStart(2, '0');
            let amPm = date.getHours() >= 12 ? 'PM' : 'AM';

            // Construct the formatted date and time string
            let formattedDateTime = `${day}-${month}-${year} ${hours}:${minutes} ${amPm}`;

            // console.log(formattedDateTime);
            return formattedDateTime;
        },
        // Function to reload the div
        reloadDiv() {
            this.refreshKey += 1; // Change the key to force re-render of the div
            // console.log('hello');
        },
        //---get lead typ name--
        getTypeName(id) {
            if (id !== undefined && this.leadType.length > 0) {
                let findType = this.leadType.find(opt => opt.id === id);
                if (findType) {
                    return findType.name;
                }
            }
        },
        getStatusName(id) {
            if (id !== undefined && this.leadStatus.length > 0) {
                let findStatus = this.leadStatus.find(opt => opt.id === id);
                if (findStatus) {
                    return findStatus.name;
                }
            }
        },
        //--validate string or array---
        validateJSONString(str) {
            try {
                JSON.parse(str);
                return true;
            } catch (error) {
                return false;
            }
        },
        getDefaultDate() {
            const currentDate = new Date();
            currentDate.setHours(currentDate.getHours() + 5);
            currentDate.setMinutes(currentDate.getMinutes() + 30);
            this.formValues.follow_date = currentDate.toISOString().slice(0, 16);
        },
        //--validate date string----
        convertDateFormat(dateString) {
            // Regular expression pattern for "DD-MM-YYYY"
            const pattern = /^(\d{2})-(\d{1,2})-(\d{4})$/;

            // Check if dateString matches the pattern
            if (pattern.test(dateString)) {
                // Extract day, month, and year from the dateString
                const [, day, month, year] = dateString.match(pattern);

                // Construct the new date string in "YYYY-MM-DDTHH:MM:SS" format
                const newDateString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T00:00`;

                return newDateString;
            }
            return dateString;
        },
        resetForm() {
            this.formValues.leadtype_id = this.record.lead_type.id;
            this.formValues.leadtype_name = this.record.lead_type.name;
            this.formValues.leadstatus_id = this.record.lead_status;
            this.formValues.assign_to = JSON.parse(JSON.stringify(this.record.assign_to));
            this.record.follow_up = this.validateJSONString(this.record.follow_up) ? JSON.parse(this.record.follow_up) : this.record.follow_up;
            if (this.formValues.follow_des) {
                this.formValues.follow_des = '';
            }
            if (this.formValues.notes) {
                if (this.record.notes) {
                    this.formValues.notes = this.record.notes;
                } else {
                    this.formValues.notes = '';
                }
            }
            if (this.editFollowUpIndex !== null) {
                this.editFollowUpIndex = null;
            }
        },
        formattedDateTime() {
            const currentDate = new Date();
            currentDate.setHours(currentDate.getHours() + 5);
            currentDate.setMinutes(currentDate.getMinutes() + 30);
            return currentDate.toISOString().slice(0, 16);
        },
        getdaysByAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        validateIsExist() {
            if (this.formValues.notes !== this.record.notes || this.formValues.leadstatus_id !== this.record.lead_status) {
                this.display_button = true;
            }
        },
        formatDateCreated(inputDate) {
            const date = new Date(inputDate);

            // Extract date components
            const day = date.getUTCDate().toString().padStart(2, '0');
            const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Months are zero-based
            const year = date.getUTCFullYear();

            // Extract time components
            let hours = date.getUTCHours();
            const minutes = date.getUTCMinutes().toString().padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12 || 12; // Convert to 12-hour format
            const formattedHours = hours.toString().padStart(1, '0');

            // Combine into desired format
            return `${day}-${month}-${year} ${formattedHours}:${minutes}${ampm}`;
        },
        dialPhoneNumber(phoneNumber) {
            // console.log(phoneNumber, 'RRRRRRRRRRRR');
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        closeAllModals() {
            // Close all modals
            this.open_confirmBox = false;
            this.isMessageDialogVisible = false;
            this.leadTypeOrStatusList = false;
            this.open_followup = false;
        },
        //--add notes data---
        openNotesModal(data) {
            this.openNotes = true;
            this.label_name = data;
        },
        updateData(data) {
            if (this.getplanfeatures('leads')) {
                this.no_access = true;
            } else {
                if (data) {
                    let sentdata = { company_id: this.companyId, ...data }
                    if (sentdata.assign_to && sentdata.assign_to.length > 0) {
                        sentdata.assign_to = sentdata.assign_to.map(opt => opt.id).join(', ');
                    }
                    axios.put(`/leads/${this.record.id}`, sentdata)
                        .then(response => {
                            // console.log(response.data.data, 'Response..........');
                            this.updatedData = response.data.data;
                            this.record = response.data.data;
                            this.display_button = false;
                        })
                        .catch(error => {
                            console.error('Error for post', error);
                            this.openMessageDialog(error.response.data.message);
                        })
                }
            }
        },
        closeNotesModal(data) {
            if (data) {
                try {
                    if (this.label_name && this.label_name.field) {
                        this.record[this.label_name.field] = data;
                        this.updateData({ [this.label_name.field]: data });
                        this.openNotes = false;
                        this.label_name = '';
                    }
                } catch (e) {
                    console.error('Error', e);
                    this.openNotes = false;
                    this.label_name = '';
                }
            } else {
                this.openNotes = false;
                this.label_name = '';
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        openNoAccess() {
            this.no_access = true;
        }
    },
    mounted() {
        this.fetchCompanyList();
        this.fetchLocalDataList();
        setInterval(() => {
            this.reloadDiv();
        }, 1 * 60 * 1000);
        //--get default--
        this.getDefaultDate();
    },
    watch: {
        record(newValue) {
            // console.log(newValue, 'OSFSFSFS');
            // console.log(newValue, 'WWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWW TTTTTTTTTTTTT');
            this.formValues.leadtype_id = newValue.lead_type.id;
            this.formValues.leadtype_name = newValue.lead_type.name;
            this.formValues.leadstatus_id = newValue.lead_status;
            this.formValues.assign_to = JSON.parse(JSON.stringify(this.record.assign_to));
            this.record.follow_up = this.validateJSONString(this.record.follow_up) ? JSON.parse(this.record.follow_up) : this.record.follow_up;
            if (newValue.notes) {
                this.formValues.notes = newValue.notes;
            }
        },
        'formValues.leadstatus_id': {
            deep: true,
            handler(newValue, oldValue) {
                //    console.log(this.record, 'RRRRRARA');
                if (newValue !== oldValue && newValue !== this.record.lead_status && newValue === 1) {
                    // console.log(newValue, 'Value changed');
                    this.sendData(null, newValue);
                } else {
                    if (newValue !== oldValue) {
                        this.validateIsExist();
                    }
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        isMessageDialogVisible: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        leadTypeOrStatusList: {
            deep: true,
            handler(newValue) {
                console.log('Hellow data............');

                this.$emit('updateIsOpen', newValue);
            }
        },
        open_followup: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        reload_data: {
            deep: true,
            handler(newValue) {
                //---get lead data---
                this.getLeadData();
                // this.getLeadStatus();
                this.getLeadType();
            }
        }
    }
};
</script>
