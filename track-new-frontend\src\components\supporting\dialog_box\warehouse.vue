<template>
    <div v-if="showModal" style="z-index: 150;"
        class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <!-- <div class="modal-content "> -->
            <div class="justify-between items-center flex py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-lg">{{ type == 'edit' ? 'Edit a Warehouse' :
                    'Add Warehouse' }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <div class="pl-4 pr-4">
                <!-- Content based on selected option -->
                <div class="flex-row mt-5">
                    <div class="w-full p-1 pl-4 pr-4">
                        <label for="name" class="text-sm">Warehouse Name<span class="text-red-700">*</span></label>
                        <input id="name" v-model="formValues.name" type="text" @focus="inputFocussed.name = true"
                            @blur="inputFocussed.name = false"
                            class="text-sm p-1 mt-1  border rounded py-1 w-full outline-none"
                            :class="{ 'border-blue-700': inputFocussed.name }" ref="warehousename" />
                    </div>
                    <div class="w-full p-1 pl-4 pr-4 mt-5">
                        <label for="address" class="text-sm">Warehouse address<span
                                class="text-red-700">*</span></label>
                        <textarea id="address" v-model="formValues.address" rows="3"
                            @focus="inputFocussed.address = true" @blur="inputFocussed.address = false"
                            class="text-sm p-1 mt-1 border rounded w-full outline-none"
                            :class="{ 'border-blue-700': inputFocussed.address }"></textarea>
                    </div>
                </div>
                <!-- Buttons -->
                <div class="flex justify-end items-center m-3 text-sm">
                    <button @click="cancelModal"
                        class="bg-red-700 hover:bg-red-600 mr-8 px-3 py-2 text-white rounded rounded-lg">Cancel</button>
                    <button @click="sendModal" v-if="showButton"
                        class="bg-green-700 hover:bg-green-600 mr-8 px-5 py-2 text-white rounded rounded-lg ">Save</button>
                </div>
            </div>
            <!-- </div> -->
        </div>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import axios from 'axios';
import dialogAlert from './dialogAlert.vue';
export default {
    name: 'warehouse',
    components: {
        dialogAlert,
    },
    props: {
        showModal: Boolean,
        editData: Object,
        type: String,
        userName: String,
        companyId: String
    },
    data() {
        return {
            isMobile: false,
            formValues: {},
            isMessageDialogVisible: false,
            message: null,
            isOpen: false,
            updatedData: null,
            showButton: true,
            inputFocussed: {},
            open_loader: false,
        }
    },
    methods: {
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        sendModal() {
            this.hideButton();
            if (this.formValues.name && this.formValues.address) {
                this.open_loader = true;

                if (this.type === 'edit') {
                    //---update--
                    axios.put(`/warehouses/${this.editData.id}`, { ...this.formValues, company_id: this.companyId })
                        .then(response => {
                            console.log(response.data.data);
                            this.updatedData = response.data.data;
                            this.open_loader = false;
                            this.openMessageDialog(this.type === 'edit' ? 'Existing data updated successfully' : 'Added new warehouse successfully');
                        })
                        .catch(error => {
                            console.error('Error post', error);
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                        })

                } else {
                    //--create new--
                    axios.post('/warehouses', { ...this.formValues, company_id: this.companyId })
                        .then(response => {
                            console.log(response.data.data);
                            this.updatedData = response.data.data;
                            this.open_loader = false;
                            this.openMessageDialog(this.type === 'edit' ? 'Existing data updated successfully' : 'Added new warehouse successfully');
                        })
                        .catch(error => {
                            console.error('Error post', error);
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                        })
                }

            } else {
                this.openMessageDialog(this.formValues.name ? 'Please fill the Warehouse name' : this.formValues.address ? 'Please fill the address' : 'Please fill the fields..!');
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                this.formValues = this.editData;
            } else {
                this.formValues = {};
            }
        },
        //---Add new user--
        userAddNew() {
            // console.log(this.userName);            
            if (this.userName) {
                this.formValues.name = this.userName;
            }


        },
        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.updatedData) {
                this.$emit('close-modal', this.updatedData);
                this.formValues = {};
                // this.initializeData();
            } else {
                this.$emit('close-modal');
                // this.initializeData();
            }
        },
        //--hide button
        hideButton() {
            this.showButton = false;
            setTimeout(() => {
                this.showButton = true;
            }, 10000); // 10 seconds in milliseconds
        }
    },
    mounted() {
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
        // console.log( this.userName);

    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.$nextTick(() => {
                    if (this.$refs.warehousename) {
                        this.$refs.warehousename.focus();
                        this.$refs.warehousename.click();
                    }
                });
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
        userName: {
            handler() {
                this.formValues.name = this.userName;
            },
        }
    },
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;
        /* Adjust the height as needed */
    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>