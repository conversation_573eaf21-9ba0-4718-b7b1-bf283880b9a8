import axios from 'axios';
import { store } from '../store/store';
import { clearAuth, refreshToken } from '../store/slices/authSlice';
import { toast } from 'react-hot-toast';

// Create axios instance
const api = axios.create({
  baseURL: process.env.REACT_APP_API_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
  withCredentials: true,
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const state = store.getState();
    const token = state.auth.token;

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      try {
        const state = store.getState();
        const refreshTokenValue = state.auth.refreshToken;

        if (refreshTokenValue) {
          // Try to refresh the token
          await store.dispatch(refreshToken()).unwrap();
          
          // Retry the original request with new token
          const newState = store.getState();
          const newToken = newState.auth.token;
          
          if (newToken) {
            originalRequest.headers.Authorization = `Bearer ${newToken}`;
            return api(originalRequest);
          }
        }
      } catch (refreshError) {
        // Refresh failed, logout user
        store.dispatch(clearAuth());
        window.location.href = '/login';
        return Promise.reject(refreshError);
      }
    }

    // Handle other error responses
    if (error.response?.status === 403) {
      toast.error('You do not have permission to perform this action');
    } else if (error.response?.status === 404) {
      toast.error('Resource not found');
    } else if (error.response?.status === 422) {
      const errors = error.response.data.errors;
      if (errors && typeof errors === 'object') {
        Object.values(errors).forEach(errorArray => {
          if (Array.isArray(errorArray)) {
            errorArray.forEach(msg => toast.error(msg));
          }
        });
      } else {
        toast.error(error.response.data.message || 'Validation error');
      }
    } else if (error.response?.status === 429) {
      toast.error('Too many requests. Please try again later.');
    } else if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.code === 'ECONNABORTED') {
      toast.error('Request timeout. Please check your connection.');
    } else if (!error.response) {
      toast.error('Network error. Please check your connection.');
    }

    return Promise.reject(error);
  }
);

// API methods
export const apiMethods = {
  get: (url, config = {}) => api.get(url, config),
  post: (url, data = {}, config = {}) => api.post(url, data, config),
  put: (url, data = {}, config = {}) => api.put(url, data, config),
  patch: (url, data = {}, config = {}) => api.patch(url, data, config),
  delete: (url, config = {}) => api.delete(url, config),
};

// File upload method
export const uploadFile = (url, file, onUploadProgress = null) => {
  const formData = new FormData();
  formData.append('file', file);

  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });
};

// Multiple file upload method
export const uploadFiles = (url, files, onUploadProgress = null) => {
  const formData = new FormData();
  files.forEach((file, index) => {
    formData.append(`files[${index}]`, file);
  });

  return api.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    onUploadProgress,
  });
};

// Download file method
export const downloadFile = async (url, filename) => {
  try {
    const response = await api.get(url, {
      responseType: 'blob',
    });

    const blob = new Blob([response.data]);
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);
  } catch (error) {
    toast.error('Failed to download file');
    throw error;
  }
};

// Export file method (Excel, PDF, etc.)
export const exportData = async (url, params = {}, filename = 'export') => {
  try {
    const response = await api.get(url, {
      params,
      responseType: 'blob',
    });

    const contentType = response.headers['content-type'];
    let extension = 'xlsx';
    
    if (contentType.includes('pdf')) {
      extension = 'pdf';
    } else if (contentType.includes('csv')) {
      extension = 'csv';
    }

    const blob = new Blob([response.data], { type: contentType });
    const downloadUrl = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = downloadUrl;
    link.download = `${filename}.${extension}`;
    document.body.appendChild(link);
    link.click();
    link.remove();
    window.URL.revokeObjectURL(downloadUrl);

    toast.success('File exported successfully');
  } catch (error) {
    toast.error('Failed to export file');
    throw error;
  }
};

// Batch request method
export const batchRequest = async (requests) => {
  try {
    const responses = await Promise.allSettled(
      requests.map(request => api(request))
    );

    return responses.map((response, index) => ({
      index,
      status: response.status,
      data: response.status === 'fulfilled' ? response.value.data : null,
      error: response.status === 'rejected' ? response.reason : null,
    }));
  } catch (error) {
    toast.error('Batch request failed');
    throw error;
  }
};

// Health check method
export const healthCheck = () => api.get('/health');

export default api;
