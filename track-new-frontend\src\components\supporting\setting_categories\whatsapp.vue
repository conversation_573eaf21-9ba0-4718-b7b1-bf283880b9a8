<template>
    <!--loader-->
    <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
        :rows="number_of_rows" :gap="gap" :type="'grid'">
    </skeleton>

    <div v-if="!open_skeleton">
        <!-- <div class="p-3 rounded text-white whatsapp-bg">
            <p class="text-lg py-1">WASender WhatsApp API</p>
            <a class="hover:underline py-1" href="https://cloud.wazender.in/signup"
                target="_blank">https://cloud.wazender.in/signup</a>
            <p class="py-1">Where URL, Instance ID, and Token number are necessary for message sending feature.</p>
            <p class="py-1">Note: Number should be full, example: 989xxxxxxx (Don't use +91).</p>

            <p class="py-1 underline">Steps to Get App Key, Auth Key & Template ID:</p>
            <ol class="py-1">
                <li>Step 1: Open the link <a href="https://cloud.wazender.in/signup"
                        target="_blank">https://cloud.wazender.in/signup</a></li>
                <li>Step 2: Fill in the signup form or log in if you already have an account.</li>
                <li>Step 3: Go to the API option, copy the API key, and place it in the App key.</li>
            </ol>
        </div> -->

        <div class="flex flex-col items-center mt-5">
            <div class="w-full sm:w-1/2 mb-4">
                <!-- API Key -->
                <div class="mb-4">
                    <label class="block text-md font-bold">APP Key</label>
                    <input type="text" v-model="formValues.app_key" class="mt-1 p-2 border rounded-md w-full"
                        placeholder="Enter API key" />
                </div>

                <!-- Auth Key -->
                <div class="mb-4">
                    <label class="block text-md font-bold">Auth Key</label>
                    <input type="text" v-model="formValues.auth_key" class="mt-1 p-2 border rounded-md w-full"
                        placeholder="Enter authorized key" />
                </div>

                <!-- Site Key -->
                <div class="mb-4">
                    <label class="block text-md font-bold">Template ID</label>
                    <input type="text" v-model="formValues.template_id" class="mt-1 p-2 border rounded-md w-full"
                        placeholder="Enter template id" />
                </div>
                <!-- URL -->
                <!-- <div class="mb-4">
                <label class="block text-md font-bold">Site URL</label>
                <input type="text" v-model="formValues.url" class="mt-1 p-2 border rounded-md w-full"
                    placeholder="Enter site url" />
            </div> -->
                <!-- Save Button -->
                <button @click="saveSettings"
                    class="bg-green-600 text-white py-2 px-5 rounded-md mx-auto block hover:bg-green-500">
                    {{ type === 'add' ? 'Save' : 'Update' }}
                </button>
            </div>
        </div>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import dialogAlert from '../dialog_box/dialogAlert.vue';
export default {
    name: 'whatsapp',
    props: {
        companyId: String,
        userId: String
    },
    components: {
        dialogAlert
    },
    data() {
        return {
            formValues: {},
            type: 'add',
            open_message: false,
            message: '',
            //--skeleton
            open_skeleton: false,
            number_of_columns: 1,
            number_of_rows: 4,
            gap: 5,
            open_loader: false,
            //--templates list--
            templates_list: {},
        };
    },
    methods: {
        saveSettings() {
            // Implement your save logic here
            // console.log('Settings saved:', this.formValues);
            if (this.formValues.app_key && this.formValues.auth_key && this.formValues.template_id) {
                this.open_loader = true;
                let sent_data = {
                    company_id: this.companyId,
                    ...this.formValues
                };
                if (this.type === 'add') {
                    axios.post('/whatsapp_settings', sent_data)
                        .then(response => {
                            // console.log(response.data, 'Sent');
                            this.open_loader = false;
                            this.formValues = response.data.data;
                            this.openMessage(response.data.message);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.openMessage(error.response.data.message);
                            this.open_loader = false;

                        })

                } else {
                    axios.put(`/whatsapp_settings/${this.formValues.id}`, sent_data)
                        .then(response => {
                            // console.log(response.data, 'Sent');
                            this.formValues = response.data.data;
                            this.open_loader = false;
                            this.openMessage(response.data.message);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.openMessage(error.response.data.message);
                            this.open_loader = false;

                        })
                }
            } else {
                this.openMessage(!this.formValues.app_key ? 'Please fill the Application key' : !this.formValues.auth_key ? 'Please fill Authorization key' : !this.formValues.template_id ? 'Please fill the Template ID' : 'Please fill in all input fields');
            }
        },
        openMessage(msg) {
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.open_message = false;
            this.message = '';
        },
        getExistData() {
            this.open_skeleton = true;
            axios.get('/whatsapp_settings', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'get data validate');
                    if (response.data.data.length > 0) {
                        this.formValues = response.data.data[0];
                        this.type = 'edit';
                    }
                    this.open_skeleton = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        }
    },
    mounted() {
        if (this.companyId) {
            this.getExistData();
        }
    }
};
</script>

<style scoped>
/* Add any additional styling specific to this component */
</style>
