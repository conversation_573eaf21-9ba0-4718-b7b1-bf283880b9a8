<template>
    <div class="non-printable headBar fixed top-0 left-0 z-20 w-full">
        <!---:class="{ 'fixed top-0 left-0 z-50 w-full': isMobile, 'fixed top-0 left-0 z-50 w-full': !isMobile }"-->
        <div v-if="!isMobile || isDashboardPage"
            class="fixed-header p-2 shadow-lg shadow-white flex justify-between items-center non-printable headBar px-1 relative text-white">
            <div class="flex justify-between items-center">
                <div>
                    <button @click.stop="toggleSidebar" class="block text-xl px-2 sm:px-4 py-2 text-white">
                        <font-awesome-icon icon="fa-solid fa-bars" />
                        <!-- <font-awesome-icon v-if="!isSidebarOpen" icon="fa-solid fa-angle-down" class="text-xs" />
                        <font-awesome-icon v-else icon="fa-solid fa-angle-up" class="text-xs" /> -->
                    </button>
                </div>
                <div class="px-1 cursor-pointer" @click.stop="this.$router.push('/')">
                    <img class="h-[25px] sm:h-[25px] object-contain" :src="images[2]" alt="logo" />
                </div>
            </div>
            <!--searchbar-->
            <div v-if="!isPad" class="flex justify-center items-center text-sm">
                <!-- Dropdown for Select Type -->
                <!-- <div class="relative mx-2 " ref="dropdownType">
                    <button @click="toggleDropdownType"
                        class="px-2 py-1 bg-gray-100 text-gray-700 rounded-full flex items-center space-x-2 ">
                        <span class="flex justify-between items-center">
                            <span class="px-2"><font-awesome-icon :icon="formValues.type" class="text-xs" /></span>
                            <font-awesome-icon v-if="dropdownTypeOpen" icon="fa-solid fa-angle-up" class="text-xs" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" class="text-xs" />
                        </span>
                    </button>
                    <div v-if="dropdownTypeOpen" ref="dropdownTypeMenu"
                        class="absolute mt-2 bg-white rounded-lg shadow-lg border border-gray-300 p-2 text-sm h-42 overflow-y-auto justify-center">
                        <ul>
                            <li v-for="(type, index) in options" :key="index"
                                class="text-gray-900 hover:bg-blue-300 hover:text-blue-800 rounded">
                                <button @click="selectType(type)">
                                    <span class="flex"><span class="px-2"><font-awesome-icon :icon="type.icon" /></span>
                                        {{ type.name }}</span>
                                </button>
                            </li>
                        </ul>
                    </div>
                </div> -->
                <!-- Search Bar -->
                <div class="relative mr-2 text-black lg:w-64">
                    <!-- <input type="text" placeholder="Search..." @mouseover="tooltip.search = true"
                        @mouseleave="tooltip.search = false"
                        class="w-full p-1 pl-8 rounded-full bg-gray-100 text-gray-700 focus:outline-none" />
                    <span class="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500">
                        <font-awesome-icon icon="fa-solid fa-magnifying-glass" />
                        <font-awesome-icon v-if="false" icon="fa-solid fa-xmark" />
                    </span> -->
                    <div class="" @mouseover="tooltip.search = true" @mouseleave="tooltip.search = false">
                        <searchCustomer :isMobile="isMobile" :typePage="'headerHome'" @searchData="selectedCustomer"
                            @resetData="resetToSearch" :resetSearch="resetSearch" @refreshPage="refreshPage">
                        </searchCustomer>
                    </div>
                    <!---tooltip-->
                    <div v-if="tooltip.search" class="absolute flex flex-col items-center group-hover:flex mt-0
                             ml-12">
                        <div class="w-3 h-3 -mb-2 rotate-45 bg-black"></div>
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Search Customer / Services</p>
                        </span>
                    </div>
                </div>
                <div class="relative" ref="dropdownContainer">
                    <!-- Plus Button (Full rounded text white, bold) -->
                    <button @click="toggleDropdownAddNew" @mouseover="tooltip.addnew = true" ref="plusButton"
                        @mouseleave="tooltip.addnew = false"
                        class="px-2 py-1 bg-blue-600 text-white font-bold rounded-full flex items-center space-x-2">
                        <span>
                            <font-awesome-icon icon="fa-solid fa-plus" />
                        </span>
                    </button>

                    <!-- Dropdown Options (Tooltip included) -->
                    <div v-if="dropdownOpen" ref="dropdownMenu"
                        class="absolute mt-2 bg-white rounded-lg shadow-lg border border-gray-300 createNew p-2 text-xs w-36 h-42 overflow-y-auto justify-center">
                        <p class="text-center items-center text-gray-500">Quick Create</p>
                        <ul>
                            <li v-for="(option, index) in options"
                                class="text-gray-900 hover:bg-blue-300 hover:text-blue-800 rounded" :key="index"
                                :class="{ 'hidden': !featureisEnable(option.name) }"
                                @click="selectedOption(option.name)">
                                <button class="flex jsutify-etween items-center">
                                    <span class="px-2"><font-awesome-icon :icon="option.icon" /></span>
                                    <span>{{ option.name }} <font-awesome-icon
                                            v-if="getplanfeatures('items', option.name)" icon="fa-solid fa-crown"
                                            class="text-yellow-500  px-1 rounded-lg" /></span>
                                </button>
                            </li>
                        </ul>
                    </div>
                    <!---tooltip-->
                    <div v-if="tooltip.addnew" class="absolute flex flex-col items-center group-hover:flex -ml-3">
                        <div class="w-3 h-3 -mb-2 rotate-45 bg-black"></div>
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Quick Create New</p>
                        </span>
                    </div>
                </div>
            </div>
            <!--palns make center-->
            <div v-if="!isMobile" @click="navigateToLicenseInfo" class="flex justify-center items-center relative">
                <button v-if="checkRestriction()" class="text-white  rounded text-xs px-1  text-center ml-1" :class="{
                    'bg-orange-400': (currentCompanyList && currentCompanyList.plan_name),
                    'bg-green-600': !(currentCompanyList && currentCompanyList.plan_name)
                }">
                    {{ currentCompanyList && currentCompanyList.plan_name ?
                        currentCompanyList.plan_name : 'Trial' }}
                </button>
                <span v-if="timeUntil !== 'Exp' && timeUntil !== 'Expired'" class="text-lg text-green-500 px-2">
                    <img v-if="images[3]" :src="images[3]" class="h-[20px] sm:h-[25px] object-contain" alt="active" />
                    <font-awesome-icon v-else icon="fa-solid fa-check" /></span>

                <p v-if="timeUntil !== ''" class="text-xs px-1 cursor-pointer line-clamp-1"
                    :class="{ 'text-red-500': text_color == 'red', 'text-green-400': text_color == 'green' }">
                    {{ timeUntil !== '' ? timeUntil : '' }}</p>
            </div>
            <!-- Menu icon button for mobile view -->
            <!-- <button v-if="!isMobile" @click="toggleSidebar"
                class="block sm:hidden text-xl px-2 sm:px-4 py-2 text-black">
                ☰
            </button> -->
            <!-- <div v-if="!isMobile"
                class="h-[18px] sm:h-[18px] text-left center text-md sm:text-lg items-center flex justify-center p-5">
                Dashboard
            </div> -->

            <!--search bar-->
            <!-- <div class="relative">
            <input type="text" :placeholder= placeholderText
                class="border border-gray-300 rounded p-2 focus:border-blue-500 outline-none lg:w-[500px] bg-search-icon" :class="{'w-[250px]' : isMobile}"
                v-model="searchQuery" @input="filterCustomers" @change="showDropdown" ref="searchInput" /> -->
            <!-- Dropdown container -->
            <!-- <div v-if="showSuggestions" class="absolute mt-2 bg-white border rounded shadow-md w-full"
                :style="{ 'z-index': 999 }"> -->
            <!-- List of customer suggestions -->
            <!-- <ul v-if="page === 'Customers'">
                    <li v-for="customer in filteredCustomers" :key="customer.id" @click="selectCustomer(customer)">
                        {{ customer.first_name }} {{ customer.last_name }} - {{ customer.phone }} - {{ customer.email }}
                    </li>
                </ul>
                <ul v-if="page === 'Services'">
                    <li v-for="customer in filteredCustomers" :key="customer.id" @click="selectCustomer(customer)">
                        {{ customer.first_name }} {{ customer.last_name }} - {{ customer.phone }} - {{ customer.email }}
                    </li>
                </ul>
            </div>
        </div> -->
            <div class="flex items-center text-white lg:px-2">
                <!--holdList  @click="toggleDropdown"-->
                <div v-if="isEnable" class="relative cursor-pointer px-5" ref="holdList" @click="openHoldList">
                    <div class="relative">
                        <div class="cursor-pointer info-msg" :title02="'Hold Invoice List'">
                            <font-awesome-icon icon="fa-solid fa-hand" />
                        </div>
                        <!-- Bell animation for unread notifications -->
                        <span v-if="currentHoldsList && currentHoldsList.length > 0"
                            class="animate-ping absolute -top-2 right-0 inline-flex h-4 w-4 rounded-full bg-red-400 opacity-75"></span>
                        <span v-if="currentHoldsList"
                            class="absolute -top-2 right-0 inline-flex rounded-full h-4 w-4 text-[10px] bg-red-500 flex justify-center items-center">
                            {{ currentHoldsList.length }}</span>
                    </div>
                </div>
                <!--notifications-->
                <div class="px-1 info-msg" :title02="'Notifications list'">
                    <notifications :isMobile="isMobile" :updateModalOpen="updateModalOpen" @send_req="updateIsOpen">
                    </notifications>
                </div>
                <button
                    v-if="currentCompanyList && currentCompanyList.sms_balance >= 0 && !isMobile && currentCompanyList.plans && currentCompanyList.plans.data"
                    class="flex items-center text-white py-2 px-4 text-xs rounded transition-colors duration-300 info-msg"
                    :title02="`SMS remaning balance ${currentCompanyList.sms_balance} / ${currentCompanyList.plans.data.messages_limit}`"
                    @click="openSmsRecharge()">
                    <p class="line-clamp-1">SMS Credit:</p>
                    <span class="sms-count px-1">{{ currentCompanyList.sms_balance }}</span>
                </button>
                <!-- <p v-if="!isMobile" class="text-xs px-3"><font-awesome-icon icon="fa-solid fa-headset" size="lg" /> <a
                        href="https://eagleminds.net" target="_blank"
                        class="hover:text-green-700 hover:underline">+91-8233823309</a></p> -->
                <button type="button" class="text-white text-xs px-1 py-2 text-center me-1 lg:mr-2 hover:underline"
                    @click="openSupport">
                    <font-awesome-icon icon="fa-solid fa-headset" size="lg" class="mr-1" /> <span
                        v-if="!isMobile">Support</span></button>
                <!-- <img class="h-[25px] sm:h-[30px] mr-3" :src="images[1]" alt="notification" /> -->
                <!-- <span
                class="inline-flex items-center rounded-full bg-red-500 pl-1 pr-1 text-[8px] sm:text-[10px] font-medium text-white ring-1 ring-inset ring-red-600/10 absolute ml-3 sm: mr-5 top-[15px] sm:top-[30px]">
                {{ showBadges_notify }}
            </span>
            <span
                class="inline-flex items-center rounded-full bg-violet-500 pl-1 pr-1 text-[8px] sm:text-[10px] font-medium text-white text-center ring-1 ring-inset ring-green-600/10 right-[15px] mt-5 sm:right-[15px] sm: mt-3 absolute">
                &#10003;
            </span> -->
                <button v-if="!isMobile"
                    class="items-center mr-1 lg:mr-2 bg-orange-600 rounded text-center text-[12px] px-1">
                    {{ currentLocalDataList.roles && currentLocalDataList.roles.length > 0 ?
                        currentLocalDataList.roles[0] == 'admin' ? 'Admin' : 'Technician'
                        : 'USER' }}
                </button>
                <div ref="logoutDev">
                    <img v-if="is_avatar"
                        class="inline-block h-8 w-8 rounded-full ring-2 ring-white bg-white object-contain"
                        :src="currentLocalDataList.avatar" alt="avatar" @click="clickLogoutOption($event)">
                    <img v-else class="w-[35px] h-[35px] mr-2 object-contain" :src="images[0]" alt="user"
                        @click="clickLogoutOption($event)" />
                    <div v-show="showLogoutModal"
                        class="origin-top-right absolute right-0 mt-2 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none text-gray-700 text-sm">
                        <!--class="absolute z-50 transform right-[0px] mt-2 text-black" -->
                        <div class="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                            <!-- <button @click="navigateToProfile"
                                class="w-full flex items-center px-4 py-2 text-sm text-blue-700 hover:bg-blue-100">
                                <i class="fas fa-user mr-2"></i> Profile
                            </button> -->
                            <div v-if="isMobile"
                                class="w-full flex flex-col items-start px-4 py-1 text-sm text-blue-700 hover:bg-blue-100 border-b-2 border-gray-200">
                                <div class="flex items-center w-full">
                                    <font-awesome-icon icon="fa-solid fa-circle-info" class="mr-2" /> Info
                                </div>
                                <p class="px-6 block text-xs text-center items-center">User:
                                    <span
                                        class="items-center mr-1 lg:mr-2 bg-orange-600 rounded text-center text-white text-[12px] px-1">
                                        {{ currentLocalDataList.roles && currentLocalDataList.roles.length > 0 ?
                                            currentLocalDataList.roles[0] == 'admin' ? 'Admin' : 'Technician'
                                            : 'USER' }}
                                    </span>
                                </p>
                            </div>
                            <div v-if="isMobile" @click="openSmsRecharge"
                                class="w-full flex flex-col items-start px-4 py-1 text-sm text-blue-700 hover:bg-blue-100 border-b-2 border-gray-200">
                                <div class="flex items-center w-full">
                                    <font-awesome-icon icon="fa-solid fa-comment-sms" class="mr-2" /> SMS
                                </div>
                                <p v-if="currentCompanyList && currentCompanyList.sms_balance && currentCompanyList.plans && currentCompanyList.plans.data"
                                    class="px-6 block text-xs text-center items-center">
                                    SMS Credit: <span class="sms-count px-1">{{ currentCompanyList.sms_balance }} /
                                        {{ currentCompanyList.plans.data.messages_limit }}</span>
                                </p>
                            </div>
                            <button v-if="currentLocalDataList.roles && currentLocalDataList.roles.length > 0 &&
                                currentLocalDataList.roles[0] == 'admin'" @click="navigateToLicenseInfo"
                                class="w-full flex flex-col items-start px-4 py-2 text-sm text-green-700 hover:bg-green-100 border-b-2 border-gray-200">
                                <div class="flex items-center w-full">
                                    <i class="fas fa-id-card mr-2"></i> License Info
                                </div>
                                <span class="px-6 block text-xs py-1 text-center items-center"
                                    :style="{ color: text_color }">
                                    {{ timeUntil !== 'Expired' && timeUntil !== 'Active' ? `Remaning : ${timeUntil}` :
                                        timeUntil === 'Active' ? `Status : ${timeUntil}` : timeUntil }}</span>
                            </button>
                            <!-- <button @click="navigateToSettings"
                                class="w-full flex items-center px-4 py-2 text-sm text-yellow-700 hover:bg-yellow-100">
                                <i class="fas fa-cog mr-2"></i> Settings
                            </button> -->
                            <!--notifications enable / disable-->
                            <div
                                class="w-full flex justify-between items-center px-4 py-2 text-sm text-gray-700 hover:bg-yellow-100 border-b-2 border-gray-200">
                                <p class="pr-2"><font-awesome-icon icon="fa-solid fa-message" class=" mr-1" />
                                    Notifications</p>
                                <label class="inline-flex items-center cursor-pointer">
                                    <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                                        @click="toggleSwitch"
                                        :class="{ 'bg-blue-600': notification_allow, 'bg-gray-200': !notification_allow }">
                                        <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                            :class="{ 'translate-x-6': notification_allow, 'translate-x-0': !notification_allow }">
                                        </div>
                                    </div>
                                </label>

                            </div>
                            <button @click="openLogout"
                                class="w-full flex items-center px-4 py-2 text-sm text-red-700 hover:bg-red-100">
                                <i class="fas fa-sign-out-alt mr-2"></i> Logout
                            </button>
                        </div>
                        <!-- <div v-show="showLogoutModal"
                            class="absolute bg-white border border-gray-300 p-4 rounded shadow-md">
                            <h3 class="text-lg font-semibold mb-2">Confirm Logout</h3>
                            <p class="text-sm mb-4">Are you sure you want to logout?</p>
                            <div class="flex justify-between">
                                <button @click="logout"
                                    class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none focus:bg-red-600 mr-3">Logout</button>
                                <button @click="showLogoutModal = false"
                                    class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 focus:outline-none focus:bg-gray-400">Cancel</button>
                            </div>
                        </div> -->
                    </div>
                </div>
                <!--dropdown-->
                <!-- <div class="text-white ml-2 text-sm" @click="clickLogoutOption($event)">
                    <font-awesome-icon v-if="showLogoutModal" icon="fa-solid fa-angle-up" />
                    <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                </div> -->
            </div>
        </div>
        <!--for mobile view-->
        <div v-if="isMobile && !isDashboardPage"
            class="fixed-header p-2 shadow-lg flex justify-between items-center px-1 text-white relative set-header-background">
            <div class="flex items-center">
                <!-- Back Arrow on Non-Dashboard Pages for Mobile -->
                <button @click="goBack" class="text-xl px-2 py-2 hover:bg-gray-700 rounded-full transition">
                    <font-awesome-icon icon="fa-solid fa-arrow-left" />
                </button>
            </div>

            <!-- Page Title -->
            <div class="flex-1 text-center text-lg font-normal">
                {{ pageTitle }}
            </div>

            <!-- Right Icons: Holdlist, Notifications, and Refresh -->
            <div class="flex items-center">
                <!--holdList  @click="toggleDropdown"-->
                <div v-if="isEnable" class="relative cursor-pointer px-5" ref="holdList" @click="openHoldList">
                    <div class="relative">
                        <div class="cursor-pointer info-msg" :title02="'Hold Invoice List'">
                            <font-awesome-icon icon="fa-solid fa-hand" />
                        </div>
                        <!-- Bell animation for unread notifications -->
                        <span v-if="currentHoldsList && currentHoldsList.length > 0"
                            class="animate-ping absolute -top-2 right-0 inline-flex h-4 w-4 rounded-full bg-red-400 opacity-75"></span>
                        <span v-if="currentHoldsList"
                            class="absolute -top-2 right-0 inline-flex rounded-full h-4 w-4 text-[10px] bg-red-500 flex justify-center items-center">
                            {{ currentHoldsList.length }}</span>
                    </div>
                </div>
                <!--notifications-->
                <div class="px-1 info-msg" :title02="'Notifications list'">
                    <notifications :isMobile="isMobile" :updateModalOpen="updateModalOpen" @send_req="updateIsOpen">
                    </notifications>
                </div>

                <!-- Refresh Icon -->
                <div class="px-2 cursor-pointer" @click="refreshPage" :class="{ 'blur': !refreshButtonVisible }">
                    <font-awesome-icon icon="fa-solid fa-rotate" />
                </div>
            </div>
        </div>
        <support :showModal="show_support" @close-modal="closeSupport" :companyId="companyId"></support>
        <smsRecharge :showModal="show_smsRecharge" @close-modal="closesmsRecharge"></smsRecharge>
        <logoutCofirmation :show-modal="showModal_logout" @onConfirm="logout" @onCancel="cancelLogout">
        </logoutCofirmation>
        <!--services-->
        <serviceCategory :showModal="open_service_category" @close-modal="selectedOptionClose"
            @updateMode="updateStatus">
        </serviceCategory>
        <!--leads-->
        <addLead :show-modal="showLeadModal" @closeLeadModal="selectedOptionClose" :companyId="companyId"
            :userId="userId">
        </addLead>
        <!--AMC-->
        <addAmc :show-modal="showAmcModal" @closeAmcModal="selectedOptionClose" :companyId="companyId" :userId="userId">
        </addAmc>
        <!---add new item-->
        <addNewItem :showModal="open_add_item" @close-modal="selectedOptionClose"></addNewItem>
        <!--customer register-->
        <customerRegister :show-modal="showModal_customer" @close-modal="selectedOptionClose"></customerRegister>
        <!--expense new-->
        <expenseModel :showModal="open_expense_model" :companyId="companyId" :userId="userId"
            @close-modal="selectedOptionClose"></expenseModel>
        <openRMARegister :showModal="openModalRegister" @close-modal="selectedOptionClose" :companyId="companyId"
            :isMobile="isMobile" :userId="userId"></openRMARegister>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>
<script>
import support from '../dialog_box/support.vue';
import smsRecharge from '../dialog_box/smsRecharge.vue';
import { mapActions, mapGetters } from 'vuex';
import logoutCofirmation from '../dialog_box/logoutCofirmation.vue';
import notifications from '../notifications/notifications.vue';
import searchCustomer from '../customers/searchCustomer.vue';
import serviceCategory from '../dialog_box/serviceCategory.vue';
import addLead from '../dialog_box/addLead.vue';
import addAmc from '../dialog_box/addAmc.vue';
import addNewItem from '../dialog_box/addNewItem.vue';
import customerRegister from '../dialog_box/customerRegister.vue';
import expenseModel from '../dialog_box/expenseModel.vue';
import openRMARegister from '../dialog_box/openRMARegister.vue';
import { isRestrictedDevice } from '@/utils/deviceUtils';
export default {
    name: 'headbar',
    props: {
        page: String,
        customerData: Object,
        updateModalOpen: Boolean,
        isSidebarOpen: Boolean,
    },
    components: {
        support,
        logoutCofirmation,
        notifications,
        searchCustomer,
        serviceCategory,
        addLead,
        addAmc,
        addNewItem,
        customerRegister,
        expenseModel,
        openRMARegister,
        smsRecharge
    },
    data() {
        return {
            images: [
                '/images/head_bar/User.png',
                '/images/head_bar/Notification.png',
                '/images/head_bar/logo.png',
                '/images/head_bar/check.svg',
            ],
            showBadges_notify: 2,
            showBadges_status: true,
            isMobile: false,
            isPad: false,
            searchQuery: '',
            showSuggestions: false,
            placeholderText: '',
            showLogoutModal: false,
            //---support modal--
            show_support: false,
            //---smsRecharge modal--
            show_smsRecharge: false,
            //---is avatar---
            is_avatar: false,
            showModal_logout: false,
            timeUntil: "",
            text_color: "green",
            notification_allow: true,
            //---refresh---
            refreshButtonVisible: true,
            //---search with create new--
            dropdownOpen: false,
            dropdownTypeOpen: false,
            formValues: {},
            options: [
                { name: "Customer", icon: 'fa-solid fa-user', label: "Create a new customer" },
                { name: "Sales", icon: 'fa-solid fa-file-invoice', label: "Create a new sale" },
                { name: "Proforma", icon: 'fa-solid fa-file-alt', label: "Create a new proforma" },
                { name: "Estimation", icon: 'fa-solid fa-calculator', label: "Create a new estimation" },
                { name: "Services", icon: 'fa-solid fa-tools', label: "Create a new service" },
                { name: "AMC", icon: 'fa-solid fa-wrench', label: "Create a new amc" },
                { name: "RMA", icon: 'fa-solid fa-undo', label: "Create a new rma" },
                { name: "Lead", icon: 'fa-solid fa-user-tie', label: "Create a new lead" },
                { name: "Expenses", icon: 'fa-solid fa-money-bill-wave', label: "Create a new proforma" },
                { name: "Item", icon: 'fa-solid fa-boxes', label: "Create a new item" },
            ],
            tooltip: {},
            //---create new--
            open_service_category: false,
            showLeadModal: false,
            showAmcModal: false,
            open_add_item: false,
            showModal_customer: false,
            open_expense_model: false,
            openModalRegister: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            //---service navigation----
            serviceIsGo: false,
            //--modal suport data---
            companyId: null,
            userId: null,
            //---reset search--
            resetSearch: false,
        };
    },

    methods: {
        ...mapActions('apiUpdates', ['fetchApiUpdates']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('companies', ['fetchCompanyList']),
        ...mapActions('notificationAllow', ['fetchLocalNotifyList']),
        ...mapActions('holdsList', ['fetchHoldsList', 'updateIsEnable', 'updateIsShowList']),
        ...mapActions('notificationAllow', ['updateLocalallow']),
        checkRestriction() {
            return !isRestrictedDevice();
        },
        toggleSidebar() {
            this.$emit('toggle-sidebar');
        },
        updateIsMobile() {
            // Update isMobile based on the viewport width
            // this.$forceUpdate();
            this.isMobile = window.innerWidth < 1024;
            this.isPad = window.innerWidth < 768;
        },
        //----dropdown---
        filterCustomers() {
            if (this.searchQuery !== '' && this.searchQuery.length > 3) {
                // console.log(this.searchQuery, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            } else {
                this.showSuggestions = false;
            }
        },
        showDropdown() {
            if (this.searchQuery !== '') {
                // Show dropdown on input focus
                this.showSuggestions = true;
                // Add a click event listener to the document to close the dropdown when clicking outside                
                document.addEventListener('click', this.handleDocumentClick);
            }
        },
        hideDropdown() {
            // Hide dropdown on input blur
            this.showSuggestions = false;
            // Remove the click event listener when hiding the dropdown
            document.removeEventListener('click', this.handleDocumentClick);
        },
        selectCustomer(customer) {
            // Handle the selected customer (e.g., emit an event, update data, etc.)
            // console.log('Selected Customer:', customer);
            this.searchQuery = customer.first_name + ' ' + customer.last_name + ' - ' + customer.phone + ' - ' + customer.email; // Set the input value to the selected customer name
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            this.$emit('searchData', customer); //---filter data
            document.removeEventListener('click', this.handleDocumentClick); // Remove the event listener after selection
        },
        handleDocumentClick(event) {
            // Close the dropdown when clicking outside the input and dropdown
            const isClickInside = this.$refs.searchInput.contains(event.target) || this.$refs.searchInput.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.hideDropdown();
            }
        },
        isNumeric(str) {
            // Helper method to check if a string is numeric
            return /^\d+$/.test(str);
        },
        logout() {
            // Clear the 'track_new' key from local storage
            this.showLogoutModal = false;
            localStorage.removeItem('track_new');
            localStorage.removeItem('features');
            this.$store.dispatch('resetAll');
            this.$router.push('/login');
        },
        //----support modal--
        openSupport() {
            this.show_support = true;
        },
        closeSupport() {
            this.show_support = false;
        },
        //----smsRecharge modal--
        openSmsRecharge() {
            if (this.currentLocalDataList.roles && this.currentLocalDataList.roles.length > 0 && this.currentLocalDataList.roles[0] == 'admin') {
                if (isRestrictedDevice()) {
                    this.show_smsRecharge = false;
                }
                else {
                    this.show_smsRecharge = true;
                }
            }
        },
        closesmsRecharge() {
            this.show_smsRecharge = false;
        },
        clickLogoutOption(event) {
            this.showLogoutModal = !this.showLogoutModal;
            if (this.showLogoutModal) {
                document.addEventListener('click', this.handleOutsideClick);
            } else {
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },

        handleOutsideClick(event) {
            if (this.$refs.logoutDev) {
                const isClickInside = this.$refs.logoutDev.contains(event.target);
                if (!isClickInside) {
                    this.showLogoutModal = false;
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            }
        },
        //---new methods----
        // navigateToProfile() {
        //     // Handle navigation to profile
        //     console.log('Navigating to Profile');
        // },
        navigateToLicenseInfo() {
            if (!isRestrictedDevice()) {
                this.$router.push('/subscription/history');
            }
        },
        // navigateToSettings() {
        //     // Handle navigation to settings
        //     console.log('Navigating to Settings');
        // },
        openLogout() {
            this.showModal_logout = true;
        },
        cancelLogout() {
            this.showModal_logout = false;
        },
        calculateTimeUntil() {
            if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0 && this.currentCompanyList.expiry_date) {
                const expiryDate = new Date(this.currentCompanyList.expiry_date);
                // Set expiry date to end of the day in local time (23:59:59.999)
                expiryDate.setHours(23, 59, 59, 999); // End of the expiry date in local time

                // Get current date (local time)
                const now = new Date();
                // Get difference in milliseconds
                const diffInMillis = expiryDate - now;

                if (diffInMillis <= 0) {
                    this.timeUntil = `Expired`;
                    this.text_color = 'red';
                    return;
                }

                // Calculate years, months, weeks, days, hours, minutes from the difference
                const years = Math.floor(diffInMillis / (1000 * 60 * 60 * 24 * 365)); // Years
                const months = Math.floor((diffInMillis % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24 * 30)); // Months
                const weeks = Math.floor((diffInMillis % (1000 * 60 * 60 * 24 * 30)) / (1000 * 60 * 60 * 24 * 7)); // Weeks
                // const days = Math.floor((diffInMillis % (1000 * 60 * 60 * 24 * 7)) / (1000 * 60 * 60 * 24)); // Days
                const days = Math.ceil(diffInMillis / (1000 * 60 * 60 * 24));
                const hours = Math.floor((diffInMillis % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)); // Hours
                const minutes = Math.floor((diffInMillis % (1000 * 60 * 60)) / (1000 * 60)); // Minutes

                if (years > 0) {
                    // this.timeUntil = `${years} ${this.isMobile ? 'Y' : `year${years > 1 ? 's' : ''}`}`;
                    this.timeUntil = 'Active';
                    this.text_color = 'green';
                } else if (months > 0) {
                    // this.timeUntil = `${months} ${this.isMobile ? 'M' : `month${months > 1 ? 's' : ''}`}`;
                    this.timeUntil = 'Active';
                    this.text_color = 'green';
                    // if (months == 1) {
                    //     this.text_color = 'green';
                    // }
                }
                // else if (weeks > 0) {
                //     this.timeUntil = `${weeks} ${this.isMobile ? 'W' : `week${weeks > 1 ? 's' : ''}`}`;
                //     this.text_color = 'red';
                // }
                else if (days > 0) {
                    if (days <= 15) {
                        if (days === 1) {
                            this.timeUntil = `Today left`;
                            this.text_color = 'red';
                        } else if (days === 2) {
                            this.timeUntil = `Tomorrow left`;
                            this.text_color = 'red';
                        } else {
                            this.timeUntil = `${days} ${this.isMobile ? 'D' : `day${days > 1 ? 's' : ''}`} left`;
                            this.text_color = 'red';
                        }
                    } else {
                        this.timeUntil = 'Active';
                        this.text_color = 'green';
                    }
                }
                // else if (hours > 0) {
                //     this.timeUntil = `${hours} : ${minutes} left`;
                //     this.text_color = 'red';
                // } else if (minutes > 0) {
                //     this.timeUntil = `${minutes} ${this.isMobile ? 'MIN' : `minute${minutes > 1 ? 's' : ''}`}`;
                //     this.text_color = 'red';
                // }
                else {
                    this.timeUntil = this.isMobile ? 'Exp' : `Expired`;
                    this.text_color = 'red';
                }
            }
        },
        updateIsOpen(value) {
            if (value !== undefined) {
                this.$emit('updateIsOpen', value);
            }
        },
        //--toggle switch---
        toggleSwitch() {
            this.notification_allow = !this.notification_allow;
            this.updateLocalallow(this.notification_allow);
            localStorage.setItem('notification', JSON.stringify(this.notification_allow));
            // this.fetchLocalNotifyList();
        },
        //--hold list---
        openHoldList() {
            this.updateIsShowList(true);
        },
        //---for mobile view------
        goBack() {
            this.$router.back();
        },
        refreshPage() {
            // window.location.reload();
            this.$emit('refreshRouterView');
            this.refreshButtonVisible = false;  // Hide the button      
            // Show the button again after 1 minute (60000 milliseconds)
            setTimeout(() => {
                this.refreshButtonVisible = true;
            }, 30000);
        },
        //---get headbar design---
        toggleDropdownAddNew() {
            this.dropdownOpen = !this.dropdownOpen;
            if (this.dropdownOpen) {
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click was outside the button or dropdown
            if (
                this.$refs.dropdownContainer &&
                !this.$refs.dropdownContainer.contains(event.target)
            ) {
                this.dropdownOpen = false;
            }
        },
        toggleDropdownType() {
            this.dropdownTypeOpen = !this.dropdownTypeOpen;
            if (this.dropdownTypeOpen) {
                document.addEventListener('click', this.handleClickOutsideType);
            }
        },
        handleClickOutsideType(event) {
            // Check if the click was outside the button or dropdown
            if (
                this.$refs.dropdownType &&
                !this.$refs.dropdownType.contains(event.target)
            ) {
                this.dropdownTypeOpen = false;
            }
        },
        selectType(type) {
            this.formValues.type = type.icon;
            this.dropdownTypeOpen = false;
        },
        //-----Search functions------
        async selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                this.formValues.customer_id = selectedData.id;
                await this.$router.push({ name: 'customers-view', params: { id: this.formValues.customer_id } });
                this.refreshPage();
                // setTimeout(() => {
                //     this.resetSearch = !this.resetSearch;
                // }, 100);
            }
        },
        resetToSearch() {
            this.formValues.customer_id = '';
        },
        //---create new ----
        selectedOption(options) {
            switch (options) {
                case 'Services': {
                    this.open_service_category = true;
                    break;
                }
                case 'Lead': {
                    this.showLeadModal = true;
                    break;
                }
                case 'AMC': {
                    this.showAmcModal = true;
                    break;
                }
                case 'Sales': {
                    if (window.location.pathname !== '/sales/invoice') {
                        this.$router.push({
                            name: 'sales-invoice',
                            query: { type: 'add' }
                        });
                    } else {
                        this.refreshPage();
                    }
                    break;
                }
                case 'Proforma': {
                    if (window.location.pathname !== '/proforma/product') {
                        this.$router.push({
                            name: 'addProformaInvoice',
                            params: { type: 'product' },
                            query: { type: 'add' }
                        });
                    } else {
                        this.refreshPage();
                    }
                    break;
                }
                case 'Estimation': {
                    if (window.location.pathname !== '/estimation/product') {
                        this.$router.push({
                            name: 'addEstimation',
                            params: { type: 'product' },
                            query: { type: 'add' }
                        });
                    } else {
                        this.refreshPage();
                    }
                    break;
                }
                case 'Item': {
                    this.open_add_item = true;
                    break;
                }
                case 'Customer': {
                    this.showModal_customer = true;
                    break;
                }
                case 'Expenses': {
                    this.open_expense_model = true;
                    break;
                }
                case 'RMA': {
                    this.openModalRegister = true;
                    break;
                }
                default: {
                    break;
                }
            }
            this.dropdownOpen = false;
        },
        //--colse modals--
        selectedOptionClose(data, is_whatsapp) {
            if (this.open_service_category) {
                this.open_service_category = false;
            }
            if (this.showLeadModal) {
                this.showLeadModal = false;
            }
            if (this.showAmcModal) {
                this.showAmcModal = false;
            }
            if (this.open_add_item) {
                this.open_add_item = false;
            }
            if (this.showModal_customer) {
                this.showModal_customer = false;
            }
            if (this.open_expense_model) {
                this.open_expense_model = false;
            }
            if (this.openModalRegister) {
                if (is_whatsapp) {
                    this.$router.push({ name: 'whatsappsetting' });
                }
                this.openModalRegister = false;
            }
            if (data) {
                this.refreshPage();
            }
        },
        //---close open modal box--
        closeAllModals() {
            // Close all modals
            if (!this.serviceIsGo) {
                this.open_service_category = false;
            }
            this.showLeadModal = false;
            this.showAmcModal = false;
            this.open_add_item = false;
            this.showModal_customer = false;
            this.open_expense_model = false;
            this.openModalRegister = false;
        },
        updateStatus() {
            this.serviceIsGo = true;
            this.$emit('updateIsOpen', false);
        },
        //--validate paln details---
        checkPlanDetails(option) {
            let currentPlan = this.currentCompanyList && this.currentCompanyList.plans ? this.currentCompanyList.plans : null;

            if (['Services', 'Lead', 'AMC', 'RMA'].includes(option) && currentPlan && [12, 13, 5].includes(currentPlan.id)) {
                return true;
            } else { return false; }
        },
        featureisEnable(type) {
            if (type && ['Services', 'AMC', 'RMA', 'Lead', 'Sales', 'Estimation', 'Proforma', 'Expenses'].includes(type)) {
                let type_data = type;
                if (['Lead', 'Estimation', 'Expenses'].includes(type)) {
                    type_data = type == 'Lead' ? 'Leads' : type == 'Estimation' ? 'Estimate' : 'Expense';
                }
                if (this.currentFeatureList && this.currentFeatureList.length > 0) {
                    let find_feature = this.currentFeatureList.find(opt => opt.name == type_data);
                    if (find_feature && find_feature.hasAccess) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return true;
            }
        },
        //---plan based on strict--
        getplanfeatures(type, label, key) {
            if (type == 'group') {
                if (label == 'Services') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['service']) {
                        return true;
                    } else {
                        return false;
                    }
                }
                else if (label == 'Leads') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['leads']) {
                        return true;
                    } else {
                        return false;
                    }
                }
                else if (label == 'Website') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['website']) {
                        return true;
                    } else {
                        return false;
                    }

                }
            } else if (type == 'items') {
                if (label == 'Services') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['service']) {
                        return true;
                    } else {
                        return false;
                    }
                }
                else if (label == 'Categories') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['service_category']) {
                        return true;
                    } else {
                        return false;
                    }
                }
                else if (label == 'Lead') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['leads']) {
                        return true;
                    } else {
                        return false;
                    }
                }
                else if (label == 'Website Enquiry') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['website_enquiry']) {
                        return true;
                    } else {
                        return false;
                    }
                }
                else if (label == 'AMC') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['amc']) {
                        return true;
                    } else {
                        return false;
                    }
                }
                else if (label == 'RMA') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['rma']) {
                        return true;
                    } else {
                        return false;
                    }
                }
                else if (label == 'Website') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['website']) {
                        return true;
                    } else {
                        return false;
                    }
                } else if (label == 'WhatsApp Alert') {
                    if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['whatsapp_msg']) {
                        return true;
                    } else {
                        return false;
                    }
                }
            } else {
                if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                    return true;
                } else {
                    return false;
                }
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        this.updateIsMobile(); // Initial check
        this.interval = setInterval(this.calculateTimeUntil, 60000);
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            if (this.currentLocalDataList.avatar !== '' && this.currentLocalDataList.avatar) {
                this.is_avatar = true;
            }
            this.companyId = this.currentLocalDataList.company_id;
            this.userId = this.currentLocalDataList.user_id + '';
        } else {
            this.fetchLocalDataList();
        }
        if (this.currentHoldsList && this.currentHoldsList.length > 0 && this.isEnable) {
            this.fetchHoldsList();
        } else if (this.isEnable) {
            this.fetchHoldsList();
        }
        // let notification_data = localStorage.getItem('notification');
        // if (notification_data) {
        //     this.notification_allow = JSON.parse(notification_data);
        // }
        if (this.currentLocalNotifyList !== undefined) {
            this.notification_allow = this.currentLocalNotifyList;
        }
        // this.fetchLocalNotifyList();
        this.fetchCompanyList();
        this.calculateTimeUntil();
        // Add a resize event listener to dynamically update isMobile
        window.addEventListener('resize', this.updateIsMobile);
        this.placeholderText = this.page === 'Customers' ? 'Search customer' : this.page === 'Services' ? 'Search service name' : 'Enter some text';
    },
    beforeDestroy() {
        // Remove the resize event listener when the component is destroyed
        clearInterval(this.interval);
        window.removeEventListener('resize', this.updateIsMobile);
        document.removeEventListener('click', this.handleOutsideClick);
        // Remove event listener when the component is destroyed
        document.removeEventListener('click', this.handleClickOutside);
        document.removeEventListener('click', this.handleClickOutsideType);
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('notificationAllow', ['currentLocalNotifyList']),
        ...mapGetters('holdsList', ['currentHoldsList', 'isEnable', 'isShowList']),
        ...mapGetters('features_list', ['currentFeatureList']),

        filteredCustomers() {
            // console.log('it is executed..!!');
            // Filter customers based on search query
            if (this.searchQuery !== '') {
                const query = this.searchQuery.toLowerCase();
                return this.customerData.filter(customer => {
                    const fullName = `${customer.first_name} ${customer.last_name}`.toLowerCase();
                    const email = customer.email.toLowerCase();
                    const phone = customer.phone.toLowerCase();
                    return (
                        fullName.includes(query) ||
                        email.includes(query) ||
                        (this.isNumeric(query) && phone.includes(query))
                    );
                });
            } else {
                return this.customerData;
            }
        },
        isDashboardPage() {
            return this.$route.name === 'dashboard';
        },
        pageTitle() {
            const routeName = this.$route.name;
            switch (routeName) {
                case 'dashboard':
                    return 'Dashboard';
                case 'services':
                    return 'Services';
                case 'service-data-view':
                    return 'View Service'
                case 'service-category-view':
                    return 'Edit Service'
                case 'service-category-add':
                    return 'Add New Service'
                case 'service_category_create_form':
                    return 'Create / Update Service Form'
                case 'categories':
                    return 'Categories List'
                case 'lead':
                    return 'Leads';
                case 'leadEdit':
                    return 'Lead Followup';
                case 'amc':
                    return 'AMC';
                case 'amcView':
                    return 'AMC Followup';
                case 'open_rma_home':
                    return 'RMA';
                case 'open_rma_edit':
                    return 'RMA Followup';
                case 'sales':
                    return 'Sales';
                case 'sales-invoice':
                    return 'Create / Update Invoice';
                case 'print-preview':
                    return 'Print Preview';
                case 'payment_home':
                    return 'Payment In';
                case 'payment_in':
                    return 'New Payment In'
                case 'proforma':
                    return 'Proforma';
                case 'addProformaInvoice':
                    return 'Create / Update Proforma';
                case 'addEstimation':
                    return 'Create / Update Estimation';
                case 'estimation':
                    return 'Estimation';
                case 'items':
                    return 'Inventory';
                case 'purchase_order':
                    return 'Purchase Order';
                case 'add_purchase_order':
                    return 'Create / Update Purchase';
                case 'supplier':
                    return 'Suppliers';
                case 'warehouse':
                    return 'Warehouses';
                case 'payment_home_out':
                    return 'Payment Out';
                case 'payment_out':
                    return 'Add / Update Payment Out';
                case 'stock':
                    return 'Stock Adjustment';
                case 'customers':
                    return 'Customers';
                case 'customers-view':
                    return 'Customer Overview';
                case 'expense':
                    return 'Expenses';
                case 'setting':
                    return 'Settings';
                case 'reports':
                    return 'Reports';
                case 'subscriptions':
                    return 'Subscriptions';
                case 'subscriptionsHistory':
                    return 'Subscriptions History';
                case 'pages':
                    return 'Pages';
                case 'whatsappsetting':
                    return 'WhatsApp Setting';
                case 'generate-invoice':
                    return 'Service Invoice';
                case 'users': return 'Users Management';
                default:
                    return 'My Application';
            }
        },
    },
    watch: {
        currentLocalDataList: {
            deep: true,
            handler(newValue) {
                if (newValue && Object.keys(newValue).length > 0) {
                    if (newValue.avatar !== '' && newValue.avatar) {
                        this.is_avatar = true;
                    }
                    this.companyId = newValue.company_id;
                    this.userId = newValue.user_id + '';
                }
            }
        },
        currentCompanyList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.calculateTimeUntil();
                }
            }
        },
        currentLocalNotifyList: {
            deep: true,
            handler(newValue) {
                this.notification_allow = newValue;
            }
        },
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_service_category: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showLeadModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showAmcModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_add_item: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_expense_model: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        openModalRegister: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showModal_customer: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        isEnable: {
            deep: true,
            handler(newvalue) {
                this.fetchHoldsList();
            }
        }
    }
};
</script>
<style scoped>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}

/* Style for the dropdown */
ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

li {
    padding: 8px;
    cursor: pointer;
}

li:hover {
    background-color: #f0f0f0;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
    }

    .non-printable {
        display: none;
    }
}
</style>