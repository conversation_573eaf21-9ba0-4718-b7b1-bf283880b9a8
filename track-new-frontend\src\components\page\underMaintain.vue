<template>
    <div class="bg-gradient-to-r from-purple-300 to-blue-200 min-h-screen flex items-center justify-center">
        <div class="w-9/12 bg-white shadow-lg rounded-lg overflow-hidden">
            <div class="p-8 text-center">
                <h1 class="text-2xl sm:text-4xl lg:text-6xl font-bold text-purple-400">Maintenance</h1>
                <h2 class="text-xl sm:text-2xl lg:text-4xl font-bold py-8 text-gray-800">We'll be back shortly</h2>
                <p class="text-sm sm:text-xl pb-8 px-12 font-medium text-gray-600">
                    Our software is currently undergoing scheduled maintenance. We will be back in approximately:
                </p>
                <div class=" flex items-center justify-center text-3xl font-bold text-gray-800 mb-8">
                    <div id="clock-spinner" class="relative">
                        <svg class="animate-spin h-15 w-15" viewBox="0 0 24 24">
                            <circle cx="12" cy="12" r="10" stroke="gray" stroke-width="2" fill="none"></circle>
                            <line x1="12" y1="12" x2="12" y2="4" stroke="blue" stroke-width="2" stroke-linecap="round">
                            </line>
                            <!-- <line x1="12" y1="12" x2="16" y2="12" stroke="blue" stroke-width="2" stroke-linecap="round">
                            </line> -->
                        </svg>
                    </div>
                    <p class="ml-4">
                        {{ countdown }}</p>
                </div>
                <div class="flex justify-center space-x-6">
                    <a href="/"
                        class="bg-gradient-to-r from-purple-400 to-blue-500 hover:from-pink-500 hover:to-orange-500 text-white font-semibold px-6 py-3 rounded-md">
                        <font-awesome-icon icon="fa-solid fa-house" /> Go Home
                    </a>
                    <button @click="refreshPage"
                        class="bg-gradient-to-r from-red-400 to-red-500 hover:from-red-500 hover:to-red-500 text-white font-semibold px-6 py-3 rounded-md">
                        <font-awesome-icon icon="fa-solid fa-arrow-rotate-right" /> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            endTime: new Date().getTime() + 30 * 60 * 1000, // 30 minutes from now
            countdown: '',
        };
    },
    methods: {
        updateCountdown() {
            const now = new Date().getTime();
            const distance = this.endTime - now;

            if (distance <= 0) {
                this.countdown = '00:00:00';
                return;
            }

            const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
            const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
            const seconds = Math.floor((distance % (1000 * 60)) / 1000);

            this.countdown = `${this.pad(hours)}:${this.pad(minutes)}:${this.pad(seconds)}`;
        },
        pad(number) {
            return number < 10 ? `0${number}` : number;
        },
        refreshPage() {
            this.$router.go(-1);
        },
    },
    mounted() {
        this.updateCountdown();
        this.timer = setInterval(this.updateCountdown, 1000);
    },
    beforeDestroy() {
        clearInterval(this.timer);
    },
};
</script>

<style scoped>
/* Spinner styles */
#clock-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 80px;
    height: 80px;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}
</style>