// store/modules/items.js
import axios from "axios";

const state = {
  item_list: [],
  pagination: {}, 
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  get_product_count: 0,
  };

  const mutations = {
    SET_ITEMS(state, itemData, pagination) {
      state.item_list = itemData;
      state.pagination = pagination;
    },
    RESET_STATE(state) {
      state.item_list = [];  
      state.pagination = {};
      state.lastFetchTime = null;
      state.isFetching = false;
      state.get_product_count = 0;
  },
  SET_LAST_FETCH_TIME(state, time) {
    state.lastFetchTime = time; // Save the timestamp when the API was last accessed
  },
  SET_IS_FETCHING(state, status) {
    state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
  },
  SET_IS_ITEMS(state, count) {
    state.get_product_count = count;
  }
  };

  const actions = {
    updateCustomerName({ commit }, itemData) {
      // Simulate an asynchronous operation (e.g., API call) to update customer name
      setTimeout(() => {
        // Commit mutation to update customer name
        commit('SET_ITEMS', itemData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchItemList({ commit, state, rootState }, total_count) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['products_update']; 
      
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime){
        return; // Skip request if less than 30 seconds have passed since the last request
      } 
      commit('SET_IS_ITEMS', total_count);
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          // Set the request status to true (indicating that the request is in progress)
      commit('SET_IS_FETCHING', true);
          axios.get('/products_details', { params: { company_id: company_id, page: 1, per_page: total_count } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Items list..!');
              let items_list = response.data.data;
              let pagination = response.data.pagination;
              
              commit('SET_ITEMS', items_list, pagination);
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return items_list;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    
  };

  const getters = {
    currentItems(state) {
        return state.item_list;
    },
    currentItemsPagination(state) {
      return state.pagination;
    },
  };

  export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};