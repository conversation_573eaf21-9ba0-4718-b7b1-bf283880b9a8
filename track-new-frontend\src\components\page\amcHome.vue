<template>
    <div class="flex h-screen relative">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mt-[60px]': isMobile, 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" :amcData="dataFromChild" @searchData="getFiteredDataList"
                @refresh_store="refresh_store">
            </headbar> -->


            <!-- services home -->
            <div class="relative">
                <bannerDesign></bannerDesign>
                <amcHomeTable :data="viewLeads" @dataToParent="handleDataFromChild" :searchedData="getFilteredData"
                    :isMobile="isMobile" :store_refresh="store_refresh" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></amcHomeTable>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/amc/headbar.vue';
import amcHomeTable from '../supporting/amc/amcHomeTable.vue';
import { useMeta } from '@/composables/useMeta';
import { mapActions, mapGetters } from 'vuex';
import bannerDesign from '../supporting/banner/bannerDesign.vue';
export default {
    name: 'leads',
    emits: ['updateIsOpen'],
    components: {
        // sidebar,
        // headbar,
        amcHomeTable,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            route_item: 11,
            viewLeads: [],
            getFilteredData: [],
            dataFromChild: [],
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'AMC';
        const pageDescription = 'Keep track of all AMC services with real-time status updates, ensuring smooth and efficient maintenance management.';
        useMeta(pageTitle, pageDescription);
        return { pageTitle };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList', 'validateRoles']),

        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleDataFromChild(data) {
            this.dataFromChild = data;
            // console.log(data,'WWWWWW');
        },
        getFiteredDataList(data) {
            // console.log(data, 'WWWW');
            this.getFilteredData = data;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        this.fetchLocalDataList();
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        },
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>