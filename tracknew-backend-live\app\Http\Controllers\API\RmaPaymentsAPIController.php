<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateRmaPaymentsAPIRequest;
use App\Http\Requests\API\UpdateRmaPaymentsAPIRequest;
use App\Models\RmaPayments;
use App\Repositories\RmaPaymentsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class RmaPaymentsController
 * @package App\Http\Controllers\API
 */

class RmaPaymentsAPIController extends AppBaseController
{
    /** @var  RmaPaymentsRepository */
    private $rmaPaymentsRepository;

    public function __construct(RmaPaymentsRepository $rmaPaymentsRepo)
    {
        $this->rmaPaymentsRepository = $rmaPaymentsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/rmaPayments",
     *      summary="getRmaPaymentsList",
     *      tags={"RmaPayments"},
     *      description="Get all RmaPayments",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/RmaPayments")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $rmaPayments = $this->rmaPaymentsRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($rmaPayments->toArray(), 'Rma Payments retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/rmaPayments",
     *      summary="createRmaPayments",
     *      tags={"RmaPayments"},
     *      description="Create RmaPayments",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaPayments"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateRmaPaymentsAPIRequest $request)
    {
        $input = $request->all();

        $rmaPayments = $this->rmaPaymentsRepository->create($input);

        return $this->sendResponse($rmaPayments->toArray(), 'Rma Payments saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/rmaPayments/{id}",
     *      summary="getRmaPaymentsItem",
     *      tags={"RmaPayments"},
     *      description="Get RmaPayments",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaPayments",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaPayments"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var RmaPayments $rmaPayments */
        $rmaPayments = $this->rmaPaymentsRepository->find($id);

        if (empty($rmaPayments)) {
            return $this->sendError('Rma Payments not found');
        }

        return $this->sendResponse($rmaPayments->toArray(), 'Rma Payments retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/rmaPayments/{id}",
     *      summary="updateRmaPayments",
     *      tags={"RmaPayments"},
     *      description="Update RmaPayments",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaPayments",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaPayments"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateRmaPaymentsAPIRequest $request)
    {
        $input = $request->all();

        /** @var RmaPayments $rmaPayments */
        $rmaPayments = $this->rmaPaymentsRepository->find($id);

        if (empty($rmaPayments)) {
            return $this->sendError('Rma Payments not found');
        }

        $rmaPayments = $this->rmaPaymentsRepository->update($input, $id);

        return $this->sendResponse($rmaPayments->toArray(), 'RmaPayments updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/rmaPayments/{id}",
     *      summary="deleteRmaPayments",
     *      tags={"RmaPayments"},
     *      description="Delete RmaPayments",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaPayments",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var RmaPayments $rmaPayments */
        $rmaPayments = $this->rmaPaymentsRepository->find($id);

        if (empty($rmaPayments)) {
            return $this->sendError('Rma Payments not found');
        }

        $rmaPayments->delete();

        return $this->sendSuccess('Rma Payments deleted successfully');
    }
}
