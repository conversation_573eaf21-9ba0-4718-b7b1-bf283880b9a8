<template>
    <div class="main-content" :class="{ 'mt-[60px]': isMobile, 'mb-[70px]': !isMobile }">
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>
        <div class="m-1 my-custom-margin">
            <!--New headbar-->
            <!-- <div v-if="!open_skeleton" class="flex justify-between">
            <div class="flex mr-2">
                <button @click="goBackButton" :class="{ 'mr-2': isMobile }"
                    class="py-1 text-green-500 font-bold hover:text-green-600 text-sm text-center"><span
                        class=" text-center font-bold">&#8592;</span> <span v-if="!isMobile" class="text-center">Go
                        Back</span>
                </button>

            </div>
        </div> -->
            <div v-if="!open_skeleton && !isMobile" class="text-sm py-1 px-1">
                <p v-if="!open_skeleton" class="font-bold text-sm mt-3">
                    <span @click="goBackToHomePage"
                        class="text-gray-500 hover:text-black hover:underline cursor-pointer">Services</span>
                    <span class="px-2 text-gray-500"><font-awesome-icon icon="fa-solid fa-angles-right" /></span>
                    <span @click="goBack" class="text-gray-500 hover:text-black hover:underline cursor-pointer">
                        {{ category_name }}</span>
                    <span class="px-2 text-gray-500"><font-awesome-icon icon="fa-solid fa-angles-right" /></span>
                    <span @click="reloadPage" class="text-blue-700 cursor-pointer hover:text-blue-500">View</span>
                </p>
            </div>

            <!-- Dynamic form creation -->
            <div v-if="!isFormEmpty && dynamicForm && dynamicForm.length !== 0 && !open_skeleton">
                <gridViewService :dynamicForm="dynamicForm" @collectData="collectData" :status="getDataFromForm"
                    :existData="data" :companyId="companyId" :userId="userId" :isMobile="isMobile"
                    :isCompleted="isItCompleted" @saveComments="submitData" :updateModalOpen="updateModalOpen"
                    @update-is-modal-open="isModalOpen">
                </gridViewService>

            </div>

            <!--Buttons-->
            <!-- <div v-if="!isFormEmpty && isFormModified && !open_skeleton" class="flex justify-center items-center p-2">
            <button @click="cancelData"
                class="bg-red-700 justify-center items-center inline-flex p-1 px-7 text-white mr-[5%] rounded text-lg hover:bg-red-600">
                <font-awesome-icon icon="fa-regular fa-rectangle-xmark" size="lg" :style="{ padding: '0 5px 0 0' }" />
                Cancel</button>
            <button @click="submitData"
                class="bg-green-600 justify-center items-center inline-flex p-1 px-7 text-white mr-[10%] rounded text-lg hover:bg-green-500">
                <font-awesome-icon icon="fa-solid fa-floppy-disk" size="lg" :style="{ padding: '0 5px 0 0' }" />
                Save
            </button>
        </div> -->
            <div v-if="!open_skeleton" class="fixed-buttons-container relative">
                <!--sms, Whatsapp & email--->
                <div v-if="!isMobile" class="flex flex-row w-full mt-1 items-center space-x-6"
                    :class="{ 'justify-start': sidebaropen, 'justify-center': !sidebaropen }">
                    <!-- <div class=" w-36 flex justify-center items-center rounded bg-[#8b5cf6] text-white p-2 cursor-pointer"
                        @click="goBack">
                        <font-awesome-icon icon="fa-solid fa-arrow-left" size="lg" :style="{ color: '#343332' }"
                            class="px-2" />

                        <p class="text-center px-3 sm:text-sm text-xs">back</p>
                    </div> -->
                    <!--save and cancel-->
                    <div class="w-36 flex justify-center items-center rounded bg-red-700 text-white cursor-pointer p-2 hover:bg-red-800 transition-colors duration-200"
                        @click="cancelData" title="Cancel Action">
                        <font-awesome-icon icon="fa-regular fa-rectangle-xmark" :style="{ color: 'white' }"
                            class="px-2" />
                        <p class="text-center text-sm">Cancel</p>
                    </div>

                    <!-- Save Button -->
                    <div class="w-36 flex justify-center items-center justify-center rounded space-x-2 cursor-pointer p-2"
                        :class="{ 'text-gray-500': !isFormModified, 'bg-gray-400': !isFormModified, 'bg-green-700 text-white': isFormModified }"
                        @click="submitData" title="Save Changes">
                        <font-awesome-icon icon="fa-solid fa-floppy-disk" class="px-2"
                            :style="{ color: isFormModified ? 'white' : 'gray' }" />
                        <p class="text-center text-sm">Save</p>
                    </div>
                    <!-- Jobsheet Button -->
                    <div class="w-36 flex justify-center items-center bg-gray-700 rounded text-white cursor-pointer p-2 hover:bg-gray-800 transition-colors duration-200"
                        @click="openJobSheet" title="Open Job Sheet">
                        <font-awesome-icon icon="fa-solid fa-receipt" style="color: #74C0FC;" class="px-2" />
                        <p v-if="!isMobile" class="text-center text-sm">Jobsheet</p>
                    </div>

                    <!-- Generate Invoice Button -->
                    <div v-if="validateIsFeature()"
                        class="w-42 flex justify-center items-center bg-blue-600 text-white rounded cursor-pointer p-2 hover:bg-blue-700 transition-colors duration-200"
                        @click="generateInvoice" title="Generate or Edit Invoice">
                        <font-awesome-icon icon="fa-solid fa-file-invoice-dollar" style="color: #f9ea05;"
                            class="px-2" />
                        <p v-if="!isMobile" class="text-center text-sm">
                            {{ data && data.invoice_id ? 'View Invoice' : 'Generate Invoice' }}
                        </p>
                        <font-awesome-icon v-if="getplanfeatures('service_estimations')" icon="fa-solid fa-crown"
                            class="text-yellow-500 rounded-lg pl-1" />
                    </div>
                    <!--service history-->
                    <!-- Jobsheet Button -->
                    <div class="w-40 flex justify-center items-center bg-gray-700 rounded text-white cursor-pointer p-2 hover:bg-gray-800 transition-colors duration-200"
                        @click="openServiceHistory" title="Open Job Sheet">
                        <font-awesome-icon icon="fa-solid fa-timeline" class="px-2" />
                        <p v-if="!isMobile" class="text-center text-sm">service history</p>
                    </div>
                </div>
                <!---IsMobile view-->
                <div v-if="isMobile" class="fixed bottom-0 left-0 z-50 w-full bg-white border-t border-gray-200">
                    <div class="grid h-full max-w-lg grid-cols-4 mx-auto font-medium">
                        <!---:class="{ 'grayscale': selected_btn_btm !== 'cancel', 'grayscale-0': selected_btn_btm === 'cancel' }" :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'cancel' }"-->
                        <button type="button" @click="cancelData"
                            class="inline-flex flex-col items-center justify-center px-5 py-2 hover:bg-gray-50">
                            <div v-if="selected_btn_btm === 'cancel'"
                                class="absolute top-0 transform  border-t-8 border-blue-700 rounded"
                                :style="{ width: '50px' }">
                            </div>
                            <span class="w-7 h-7 text-gray-50 pt-1">
                                <font-awesome-icon icon="fa-regular fa-rectangle-xmark" size="xl"
                                    :style="{ color: 'red' }" />
                            </span>
                            <span class="text-[12px] pt-1"
                                :class="{ 'font-bold': selected_btn_btm === 'cancel' }">Cancel</span>
                            <!---:class="{ 'text-blue-700': selected_btn_btm === 'cancel', 'text-red-500': selected_btn_btm !== 'cancel' }"-->
                        </button>
                        <!--:class="{ 'grayscale': selected_btn_btm !== 'save', 'grayscale-0': selected_btn_btm === 'save' }"    :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'save' }"-->
                        <button type="button" @click="submitData"
                            class="inline-flex flex-col items-center justify-center px-5 py-2 hover:bg-gray-50">
                            <div v-if="selected_btn_btm === 'save'"
                                class="absolute top-0 transform  border-t-8 border-blue-700 rounded"
                                :style="{ width: '50px' }">
                            </div>
                            <span class="w-7 h-7 pt-1">
                                <font-awesome-icon icon="fa-solid fa-floppy-disk" size="xl"
                                    :style="{ color: 'green' }" />
                            </span>
                            <span class="text-[12px] pt-1"
                                :class="{ 'font-bold': selected_btn_btm === 'save' }">Save</span>
                            <!---:class="{ 'text-blue-700': selected_btn_btm === 'save', 'text-green-700': selected_btn_btm !== 'save' }"-->
                        </button>
                        <!--:class="{ 'grayscale': selected_btn_btm !== 'jobsheet', 'grayscale-0': selected_btn_btm === 'jobsheet' }" :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'jobsheet' }"-->
                        <button type="button" @click="openJobSheet"
                            class="inline-flex flex-col items-center justify-center px-5 py-2 hover:bg-gray-50">
                            <div v-if="selected_btn_btm === 'jobsheet'"
                                class="absolute top-0 transform  border-t-8 border-blue-700 rounded"
                                :style="{ width: '50px' }">
                            </div>
                            <span class="w-7 h-7 pt-1">
                                <font-awesome-icon icon="fa-solid fa-receipt" :style="{ color: '#74C0FC' }" size="xl" />
                            </span>
                            <span class="text-[12px] pt-1"
                                :class="{ 'font-bold': selected_btn_btm === 'jobsheet' }">Jobsheet</span>
                            <!--:class="{ 'text-blue-700': selected_btn_btm === 'jobsheet', 'text-blue-700': selected_btn_btm !== 'jobsheet' }"-->
                        </button>
                        <!--:class="{ 'grayscale': selected_btn_btm !== 'invoice', 'grayscale-0': selected_btn_btm === 'invoice' }" :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'invoice' }"-->
                        <button v-if="validateIsFeature()" type="button" @click="generateInvoice"
                            class="inline-flex flex-col items-center justify-center px-5 py-2 hover:bg-gray-50">
                            <div v-if="selected_btn_btm === 'invoice'"
                                class="absolute top-0 transform  border-t-8 border-blue-700 rounded"
                                :style="{ width: '50px' }">
                            </div>
                            <span class="w-7 h-7 pt-1">
                                <font-awesome-icon icon="fa-solid fa-file-invoice-dollar" :style="{ color: '#f09a05' }"
                                    size="xl" />
                            </span>
                            <span class="text-[10px] pt-1"
                                :class="{ 'text-blue-700': selected_btn_btm === 'invoice', 'text-gray-500': selected_btn_btm !== 'invoice' }">
                                {{ data && data.invoice_id ? 'Edit' : 'Generate' }}</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Import the child component -->
        <smsWhatsappEmail :show-modal="showModal" :selected-option="selectedOption" @close-modal="closeModal">
        </smsWhatsappEmail>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModalCustomer" :userName="userName"
            :companyId="companyId">
        </customerRegister>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
    </div>
    <Loader :showModal="open_loader"></Loader>
    <jobsheet :showModal="show_job" :item_data="data" @close-modal="closeJobSheet" :companyId="companyId">
    </jobsheet>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <notificationAlert :showModal="show_notification" @onCancel="cancelTheNotification"
        @onConfirm="enableTheNotification"></notificationAlert>
    <completedService :showModal="open_completed" @onClose="closeOpenModalService"
        :customer_id="formValues.customer_id ? formValues.customer_id : null"></completedService>
    <serviceHistory :is-modal-open="showHistory" :service-details="data" :category_id="category_id"
        :companyId="companyId" @close="closeHistory"></serviceHistory>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>

<script>
import smsWhatsappEmail from '../../dialog_box/dialog_sms_whatsapp_email.vue';
import customerRegister from '../../dialog_box/customerRegister.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import gridViewService from './gridViewService.vue';
import axios from 'axios';
import jobsheet from '../../dialog_box/jobsheet.vue';
import notificationAlert from '../../dialog_box/notificationAlert.vue';
import { mapActions, mapGetters } from 'vuex';
import completedService from '../../dialog_box/completedService.vue';
import serviceHistory from '../../dialog_box/serviceHistory.vue';
import noAccessModel from '../../dialog_box/noAccessModel.vue';

export default {
    name: 'view_services',
    emits: ['updatedData', 'updateIsOpen'],
    components: {
        smsWhatsappEmail,
        customerRegister,
        dialogAlert,
        gridViewService,
        jobsheet,
        notificationAlert,
        completedService,
        serviceHistory,
        noAccessModel
    },
    props: {
        data: Object,
        category_name: String,
        labelsName: Object,
        fieldKey: Object,
        category_id: String,
        companyId: String,
        userId: String,
        isCompleted: Boolean,
        updateModalOpen: Boolean,
    },
    data() {
        return {
            isMobile: false,
            isFormEmpty: false,
            showModal_customer: false,
            isMessageDialogVisible: false,
            originalFormValues: {},
            isFormModified: false,
            type: 'edit',
            userName: null,
            showModal: false,
            selectedOption: '',
            serviceCategories: [],
            formValues: {},
            isDropdownOpen: false,
            filteredCustomerOptions: [],
            dynamicForm: [],
            getDataFromForm: false,
            message: null,
            backup_data: null,
            overall_backup: null,
            //--skeleton
            open_skeleton: true,
            number_of_columns: 2,
            number_of_rows: 20,
            gap: 5,
            open_loader: false,
            show_job: false,
            isItCompleted: false,
            selected_btn_btm: '',
            show: false,
            type_toaster: 'success',
            show_notification: false,
            notification_validate: false,
            //---completed services----
            open_completed: false,
            //--service history--
            showHistory: false,
            //---no access---
            no_access: false,
        };
    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('features_list', ['currentFeatureList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures', 'sidebaropen']),
    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('features_list', ['fetchFeatureList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),

        openJobSheet() {
            this.selected_btn_btm = 'jobsheet';
            this.show_job = true;
        },
        closeJobSheet() {
            this.show_job = false;
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        openModal(option) {
            // console.log(option, "What happening ...");
            this.selectedOption = option;
            this.showModal = true;
        },
        //---go back button--
        goBackButton() {
            if (!this.isFormModified) {
                this.$router.go(-1);
            } else {
                this.openMessageDialog('Please save latest updation..!');
            }
        },
        closeModal() {
            this.showModal = false;
        },
        goBackToHomePage() {
            // this.$router.push('/services');
            this.$router.go(-1);
        },
        reloadPage() {
            window.location.reload();
        },
        goBack() {
            // console.log('RRRRRRRRRRRRRR');
            // Go back to the previous page
            this.$router.go(-1);
        },
        editRecord() {
            // Handle edit action
            // console.log("Edit", this.data);
            // this.$emit('showAddServiceComponent', record);
            this.$router.push({ name: 'service-category-edit', params: { editId: this.data.id } });
        },
        findData(data) {
            return this.data.data;
        },
        //--validate in object values
        validateAdditionalMaterial(data) {
            // console.log('Validate................', data, 'What happening..!');
            if (data && data.length > 0) {
                // console.log(data.every(opt => opt.qty >= 1 && opt.price > 0 && opt.status === "approved"), 'Status');
                if (data.every(opt => opt.qty >= 1 && opt.price > 0 && opt.status === "approved")) {
                    return true;
                } else {
                    // console.log('hello');
                    this.openMessageDialog('Please get additional materials approved by the customer or obtain manual approval. Please set all quantities to non-zero values and ensure that prices are not set to zero.');
                    return false;
                }
            } else if (data && data.length === 0) {
                return true;
            }
            else {
                // this.openMessageDialog('Please validate additional material information.');
                return true;
            }
        },
        generateInvoice() {
            if (!this.getplanfeatures('service_estimations')) {
                this.selected_btn_btm = 'invoice';
                let find_status_list = this.dynamicForm.find(opt => opt.fieldKey === 'status');
                //--&& (this.formValues.estimateAmount > 0 || this.formValues.serviceAmount > 0)
                if (find_status_list && (this.formValues.status === find_status_list.option[4] || this.formValues.status === find_status_list.option[5] || this.formValues.status === find_status_list.option[6] || this.formValues.status === find_status_list.option[7] || this.formValues.status === find_status_list.option[9] || this.formValues.status === find_status_list.option[10] || this.formValues.status === find_status_list.option[11]) && !this.isFormModified) {

                    if (this.data && this.data.additional && this.validateAdditionalMaterial(this.data.additional)) {
                        // console.log(this.data.nvoice_id || this.data.sale_id, 'WWWWWWWW');
                        if (this.data.sale_id) {
                            this.$router.push({
                                name: 'print-preview',
                                query: { invoice_no: this.data.sale_id }
                            });

                        } else {
                            this.$router.push({ name: 'generate-invoice', params: { serviceId: this.data.id }, query: { type: 'add' } });
                        }
                    } else if (this.validateAdditionalMaterial(this.data && this.data.additional)) {
                        if (this.data.sale_id) {
                            this.$router.push({
                                name: 'print-preview',
                                query: { invoice_no: this.data.sale_id }
                            });

                        } else {
                            this.$router.push({ name: 'generate-invoice', params: { serviceId: this.data.id }, query: { type: 'add' } });
                        }
                    }
                } else {
                    const { status, estimateAmount, serviceAmount } = this.formValues;
                    const statusLower = status.toLowerCase();

                    const isStatusValid = find_status_list &&
                        (statusLower === find_status_list.option[4]?.toLowerCase() ||
                            statusLower === find_status_list.option[5]?.toLowerCase() ||
                            statusLower === find_status_list.option[6]?.toLowerCase() ||
                            statusLower === find_status_list.option[7]?.toLowerCase());

                    const hasAmounts = estimateAmount > 0 || serviceAmount > 0;

                    let message;

                    if (this.isFormModified) {
                        message = 'Please save latest updation and generate invoice..!';
                    } else if (!find_status_list || !isStatusValid) {
                        message = 'Please change the status to be Success or cancelled';
                    } else if (!hasAmounts) {
                        message = 'Please update the service amount or estimation amount';
                    } else {
                        message = 'Please change the status to be Success or cancelled';
                    }

                    this.openMessageDialog(message);
                }
            } else {
                //----enable not have access to current plan  
                this.no_access = true;
            }
        },
        openModalCustomer(data) {
            this.userName = data;
            // console.log(data, 'What happening...!');
            this.isDropdownOpen = false;
            this.showModal_customer = true;
        },
        closeModalCustomer() {
            this.showModal_customer = false;
        },

        cancelData() {
            this.selected_btn_btm = 'cancel';
            // console.log('cancel the data');            
            this.isFormModified = false;
            this.$router.go(-1);
            // window.location.reload();
        },

        submitData(status_cmt) {
            if (this.getplanfeatures('leads')) {
                this.no_access = true;
            } else {
                this.selected_btn_btm = 'save';
                // console.log(!this.isFormEmpty, 'UUUU', this.isFormModified, 'RRRRRRRRRRRRR how to make true');
                if (!this.isFormEmpty && this.isFormModified) {
                    // if (this.formValues.notification.length > 0 || this.notification_validate || status_cmt == true) {
                    // this.open_skeleton = true;
                    this.open_loader = true;
                    // Validate required fields
                    const requiredFields = this.dynamicForm.filter(field =>
                        field.required === 'yes' &&
                        field.enable &&
                        !['estimateAmount', 'advanceAmount', 'serviceAmount'].includes(field.fieldKey)  // Exclude specific field keys
                    );

                    const invalidFields = requiredFields.filter(field => {
                        if (!this.formValues['warranty_type'] || this.formValues['warranty_type'] !== 'Free') {
                            if ((field.fieldKey === 'document' && this.getplanfeatures('service_image')) || (field.fieldKey === 'additional' && this.getplanfeatures('additional_materials'))) {
                                // console.log('it is working......');
                            } else {
                                const value = this.formValues[field.fieldKey];
                                return value === undefined || value === null;
                            }
                        } else if (field.fieldKey !== 'estimateAmount' && field.fieldKey !== 'advanceAmount' && field.fieldKey !== 'serviceAmount') {
                            if ((field.fieldKey === 'document' && this.getplanfeatures('service_image')) || (field.fieldKey === 'additional' && this.getplanfeatures('additional_materials'))) {
                                // console.log('it is working......');
                            } else {
                                const value = this.formValues[field.fieldKey];
                                return value === undefined || value === null;
                            }
                        }
                    });
                    if (invalidFields.length > 0) {
                        // Show error message for empty required fields
                        this.openMessageDialog(`Please fill in all required fields are ${invalidFields.map(field => field.lableName).join(', ')} `);
                        // this.open_skeleton = false;
                        this.open_loader = false;
                        return;
                    }
                    // let serviceTrack = [];
                    // if (this.formValues.service_track) {
                    //     serviceTrack = this.formValues.service_track;
                    // } else {
                    //     serviceTrack = [{ name: 'order taken', date: '', status: false },
                    //     { name: 'hold', date: '', status: false },
                    //     { name: 'in-progress', date: '', status: false },
                    //     { name: 'new estimate', date: '', status: false },
                    //     { name: 'to be delivered', date: '', status: false },
                    //     { name: 'delivered', date: '', status: false },
                    //     { name: 'cancelled', date: '', status: false }
                    //     ];
                    // }
                    let serviceTrack = [];
                    if (this.data && this.data.service_track && this.data.service_track.length > 0) {
                        serviceTrack = this.data.service_track;
                    } else {
                        let find_status_list = this.dynamicForm.find(opt => opt.fieldKey === 'status');

                        if (find_status_list) {
                            serviceTrack = find_status_list.option.map((opt, i) => {
                                return { name: opt, date: '', status: false };
                            });
                        }
                    }

                    // console.log(serviceTrack, 'Waht happening...!!!!!!!');
                    const findTheObject = this.serviceCategories.find((opt) => opt.id === Number(this.category_id));
                    if (findTheObject) {
                        if (this.type === 'edit') {
                            let { customer_id, expected_date, estimateAmount, advanceAmount, serviceAmount, status, notification, document, additional, assignWork, comments, service_track } = this.formValues;

                            let findIndex = serviceTrack.findIndex(opt => opt.name.toLowerCase() === status.toLowerCase());
                            if ((status === 'delivered' || status === 'to be delivered' || status === 'completed') && serviceAmount === 0 || !serviceAmount) {
                                //----get service amount to add additional amount
                                // let get_total_service = 0;
                                // if (estimateAmount > 0) {
                                //     get_total_service = get_total_service + estimateAmount;
                                // }
                                // if (additional && additional.length > 0) {
                                //     let total = additional.reduce((acc, item) => acc + item.total, 0);
                                //     get_total_service += total;
                                // }
                                // this.formValues.serviceAmount = get_total_service;
                            }
                            if (additional && additional.length > 0) {
                                // Filter out items with qty === 0 and calculate total of remaining items
                                let filteredAdditional = additional.filter(item => item.qty !== 0);

                                // Calculate the total of remaining items
                                let total = filteredAdditional.reduce((acc, item) => acc + item.total, 0);

                                // Update the 'additional' array with filtered items (optional)
                                additional = filteredAdditional;

                                // Use 'total' for further processing
                                // console.log('Total of non-zero qty items:', total, filteredAdditional);
                            }
                            if (findIndex !== -1) {
                                if (serviceTrack[findIndex].status !== true) {
                                    serviceTrack[findIndex].status = true;
                                    serviceTrack[findIndex].date = this.getCurrentDateTime();
                                }
                                // else if (assignWork) {
                                //     serviceTrack[findIndex].assign_to = JSON.stringify(assignWork.map(obj => ({ user_name: obj.name })));
                                // }
                                serviceTrack.map((opt, i) => {
                                    if (i !== findIndex && (i > findIndex || i === 1 || i === 6)) {
                                        opt.status = false;
                                    } else if (i !== findIndex && i < findIndex && i !== 1 && i !== 6) {
                                        if (opt.status === false) {
                                            opt.status = true;
                                        }
                                        if (opt.date === '') {
                                            opt.date = this.getCurrentDateTime();
                                        }
                                    }
                                })
                            }
                            let service_send_data = {
                                customer_id: customer_id,
                                servicecategory_id: Number(this.category_id),
                                expected_date: expected_date && expected_date,
                                estimate_amount: estimateAmount ? estimateAmount : 0,
                                advance_amount: advanceAmount ? advanceAmount : 0,
                                service_amount: serviceAmount ? serviceAmount : 0,
                                status: JSON.stringify(findIndex >= 0 ? findIndex : ''),
                                notification: JSON.stringify(notification),
                                document: JSON.stringify(document),
                                materials: JSON.stringify(additional),
                                assign_to: assignWork && JSON.stringify(assignWork.map(obj => ({ user_id: obj.id }))),
                                service_track: JSON.stringify(serviceTrack),
                                comments: JSON.stringify(comments),
                                service_expense: this.formValues['service_expense'] ? this.formValues['service_expense'] : [],
                            };

                            axios.put(`/services/${this.formValues.id}`, { company_id: this.companyId, ...service_send_data, notification: status_cmt === true ? JSON.stringify(['']) : this.originalFormValues['status'] !== status ? JSON.stringify(notification) : JSON.stringify(['']), service_data: JSON.stringify({ ...this.formValues, additional: additional }) })
                                .then(response => {
                                    this.message = 'Service has been updated successfully...!';
                                    this.type_toaster = 'success';
                                    this.show = true;
                                    this.updateKeyWithTime('service_update');
                                    this.updateKeyWithTime('service_category_update');
                                    //---make congratulations---
                                    if (response.data.data.status == 5 || response.data.data.status == 7) {
                                        this.open_completed = true;
                                    }
                                    this.notification_validate = false;
                                    // console.log(response.data, 'Success response...!');
                                    this.goPermission = true;
                                    this.isFormModified = false;
                                    // this.$emit('getExistdata');
                                    let response_data = response.data.data;
                                    if (response_data.status == 5 || response_data.status == 6 || response_data.status == 7) {
                                        this.isItCompleted = true;
                                    }
                                    // this.open_skeleton = false;
                                    this.open_loader = false;
                                    let exist_data = JSON.parse(response_data.service_data);
                                    if (exist_data.additional && exist_data.additional.length > 0) {
                                        let material_list = JSON.parse(response_data.materials);
                                        if (material_list && material_list.length > 0) {
                                            exist_data.additional = material_list;
                                            this.backup_data = material_list;
                                            exist_data.assignWork = response_data.assign_to;
                                            if (exist_data.notification) {
                                                exist_data.notification = JSON.parse(response_data.notification);
                                            }
                                            // console.log(exist_data.notification, 'What happening in notification data..!');
                                            if (response_data.document) {
                                                exist_data.document = JSON.parse(response_data.document);
                                                // console.log(exist_data.document, 'What happningin document...!');
                                            }
                                        }
                                    } if (this.validateType(exist_data.customer) === 'Number') {
                                        // console.log(response_data.customer, 'TTTTTTTTTTTTTTTTT')
                                        let { first_name, last_name, contact_number } = response_data.customer;
                                        exist_data.customer = `${first_name} ${last_name} - ${contact_number}`;
                                        exist_data.customer_id = response_data.customer_id;
                                    }
                                    if (!Array.isArray(exist_data.assignWork) && this.validateType(exist_data.assignWork) === 'Number') {
                                        let getList = exist_data.assignWork.split(', ');
                                        if (getList.length !== 0) {
                                            const filteredArray = this.employee_list.filter(obj => getList.includes(String(obj.id)));
                                            if (filteredArray.length !== 0) {
                                                exist_data.assignWork = filteredArray;
                                            }
                                        }
                                    }
                                    if (typeof exist_data.problem_title === 'string') {
                                        const issuesArray = exist_data.problem_title
                                            // Remove brackets and split by comma
                                            .slice(1, -1)
                                            .split(',')
                                            // Trim each issue
                                            .map(issue => issue.trim());
                                        if (Array.isArray(issuesArray) && issuesArray.length !== 0) {
                                            exist_data.problem_title = issuesArray;
                                        }
                                    }
                                    // if(response_data.comments){
                                    //     exist_data.comments = JSON.parse(response_data.comments);
                                    // }
                                    this.formValues = { ...exist_data, id: response_data.id, invoice_id: response_data.invoice_id };
                                    this.$emit('updatedData', this.formValues);
                                    this.overall_backup = { ...exist_data, id: response_data.id, invoice_id: response_data.invoice_id };
                                    this.originalFormValues = { ...this.formValues };
                                    // this.$emit('getUpdatedData', response.data.data);

                                    //---go back
                                    // setTimeout(() => {
                                    //     this.$router.go(-1);
                                    // }, 500)
                                    // this.openMessageDialog(response.data.message);
                                    // setTimeout(() => {
                                    //     this.$emit('getExistdata', response.data.data);
                                    // }, 500);

                                })
                                .catch(error => {
                                    console.error('Error', error);
                                    this.open_loader = false;
                                    // this.open_skeleton = false;                                   
                                    this.openMessageDialog(error.response.data.message);
                                })
                        }
                    } else {
                        // Handle case where the category is not found
                        console.error('Category not found');
                    }
                    this.isFormModified = false;
                    // } else {
                    //     this.show_notification = true;
                    // }
                } else {
                    this.openMessageDialog("Form data already save!");
                }
            }
        },
        //---validate data type--
        validateType(value) {
            if (!isNaN(value) && !isNaN(parseFloat(value))) {
                return 'Number';
            } else if (typeof value === 'string') {
                return 'String';
            } else {
                return 'Invalid';
            }
        },
        //---status track function--
        // Get current date and time in the desired format (e.g., "2024-03-19 6:20PM")
        getCurrentDateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const meridiem = (hours < 12) ? 'AM' : 'PM';
            const formattedHours = (hours % 12) || 12;
            return `${year}-${month}-${day} ${formattedHours}:${minutes}${meridiem}`;
        },
        goBackToHomePage() {
            this.$router.push('/services');
        },
        reloadPage() {
            window.location.reload();
        },
        //-----
        //---customers dropdown
        selectDropdownOption(fields, option) {
            this.formValues[fields.fieldKey] = option.name;
            this.isDropdownOpen = false; // Close the dropdown
        },

        handleDropdownInput(fields) {
            const inputValue = this.formValues[fields.fieldKey];

            if (inputValue !== undefined && inputValue !== null) {
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = fields.option.filter(
                        (option) => option.phone.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = fields.option.filter(
                        (option) => option.name.toLowerCase().includes(inputName)
                    );
                }
            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = [];
            }
        },
        //---update based on category--
        updateComponentBasedOnCategoryName(newCategoryName) {
            // console.log(newCategoryName, 'TTTT', this.serviceCategories, 'II', this.category_id);
            // console.log(this.serviceCategories, 'What is the value', this.data, 'What happening..!');
            let findCategory = this.serviceCategories.find((opt) => opt.id === Number(this.category_id));
            // console.log(findCategory, 'Weeeae', 'Category', this.category_name, 'Is mobile', this.isMobile);
            // console.log(findCategory, 'What oooooo', this.category_name, this.category_id);
            if (findCategory.form && findCategory.form.length !== 0) {
                this.dynamicForm = JSON.parse(findCategory.form);
            } else {
                this.isFormEmpty = true;
            }
        },
        // Function to set originalFormValues when data changes
        setOriginalFormValues() {
            // console.log(this.formValues, 'RRRRR what happening..!') JSON.parse(JSON.stringify(;
            this.originalFormValues = { ...this.formValues };

        },

        checkFormModification() {
            this.isFormModified = !Object.keys(this.formValues).every((key, j) => {
                // console.log(this.formValues['additional'], 'eeeeeeeeeeeeeeeeeee', this.originalFormValues['additional']);
                if (key === 'additional') {
                    // console.log(this.formValues[key].length !== 0 && this.originalFormValues['additional'] && this.originalFormValues['additional'].length !== 0, 'Addiional data');
                    if (Array.isArray(this.formValues[key]) && this.formValues[key].length !== 0 && this.originalFormValues['additional'] && this.originalFormValues['additional'].length !== 0) {
                        if (this.formValues[key].length === this.backup_data.length) {
                            // console.log(this.formValues[key].map((subKey, index) => subKey.qty + ' ' + this.originalFormValues['additional'][index].qty), this.originalFormValues['additional'], 'Hello');
                            let res_data = this.formValues[key].every((subKey, index) =>
                                subKey.product_name === this.backup_data[index].product_name &&
                                subKey.qty === this.backup_data[index].qty &&
                                subKey.price === this.backup_data[index].price &&
                                subKey.status === this.backup_data[index].status
                            );
                            return res_data
                        }
                        else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                }
                else if (key === 'notification') {
                    if (this.formValues[key].length === this.overall_backup[key].length) {
                        if (this.formValues[key].length === this.overall_backup[key].length) {
                            let isEqual = true;
                            this.formValues[key].forEach((data, l) => {
                                if (data !== this.overall_backup[key][l]) {
                                    isEqual = false;
                                }
                            });
                            return isEqual;
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                }
                else if (key === 'document') {
                    // console.log(this.formValues[key], 'TTTTTTTTTT What happening......');
                    if (Array.isArray(this.formValues[key]) && this.formValues[key].length > 0 && Array.isArray(this.overall_backup[key]) && this.overall_backup[key].length > 0) {
                        if (this.formValues[key].length === this.overall_backup[key].length) {
                            for (let i = 0; i < this.formValues[key].length; i++) {
                                const formData = this.formValues[key][i];
                                const backupData = this.overall_backup[key][i];
                                if (!this.isEqualObjects(formData, backupData)) {
                                    return false;
                                }
                            }
                            return true;
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                } else if (key === 'pre_repair') {
                    // console.log(this.isEqualObjects(this.formValues[key], this.overall_backup[key]), 'Prerepair..');
                    return this.isEqualObjects(this.formValues[key], this.overall_backup[key]);
                } else if (key === 'problem_title') {
                    // console.log(this.formValues[key].length === this.originalFormValues[key].length, 'RRR');
                    if (this.formValues[key].length === this.originalFormValues[key].length) {
                        const originalValues = this.originalFormValues[key];
                        const currentValues = this.formValues[key];
                        return originalValues.every((value, index) => value === currentValues[index]);
                    } else {
                        return this.formValues[key].length === this.originalFormValues[key].length;
                    }
                } else if (key === 'assignWork') {
                    // console.log(this.overall_backup[key], 'RRRRRRRRR', this.formValues[key]);
                    if (Array.isArray(this.overall_backup[key]) && this.formValues[key].length === this.overall_backup[key].length) {
                        const originalValues = this.overall_backup[key];
                        const currentValues = this.formValues[key];
                        return originalValues.every((value, index) => value.name === currentValues[index].name);
                    } else {
                        return this.formValues[key].length === this.overall_backup[key].length;
                    }
                } else if (key === 'service_expense') {
                    if (Array.isArray(this.overall_backup[key]) && this.formValues[key].length === this.overall_backup[key].length) {
                        const originalValues = this.overall_backup[key];
                        const currentValues = this.formValues[key];

                        return originalValues.every((value, index) => value.description === currentValues[index].description && value.value === currentValues[index].value);
                    } else if (Array.isArray(this.formValues[key])) {
                        return this.formValues[key].length === this.overall_backup[key].length;
                    }
                }
                return this.formValues[key] === this.originalFormValues[key];
            });
        },
        isEqualObjects(obj1, obj2) {
            const keys1 = Object.keys(obj1);
            const keys2 = Object.keys(obj2);
            if (keys1.length !== keys2.length) {
                return false;
            }
            for (const key of keys1) {
                if (obj1[key] !== obj2[key]) {
                    return false;
                }
            }
            return true;
        },

        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.goPermission === true) {
                // this.$router.go(-1);
            }
        },

        //---collect data from form
        collectData(data) {
            // console.log(data, 'What is the data...!');
            if (this.getDataFromForm) {
                this.formValues = data;
            } else {
                this.getDataFromForm = true;
            }
        },
        //---service category---
        serviceCategoryList() {
            if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                this.open_skeleton = false;
                this.serviceCategories = this.currentServiceCategory;
                this.updateComponentBasedOnCategoryName(this.category_name);
            } else {
                this.fetchServiceCategoryList();
            }
        },
        //-- icon colors
        getIconColor(type) {
            // Determine the color based on the condition
            if (!this.isFormEmpty && this.isFormModified) {
                return type === 'cancel' ? 'red' : 'green';
            } else {
                return '#808080';
            }
        },
        //---notification--
        cancelTheNotification() {
            this.show_notification = false;
            this.notification_validate = true;
            this.submitData();
        },
        enableTheNotification() {
            this.show_notification = false;
            this.notification_validate = true;
            this.formValues.notification = ['SMS'];
            this.submitData();
        },
        //----update is modal open--
        isModalOpen(type) {
            if (type !== undefined) {
                this.$emit('updateIsOpen', type);
            }
        },
        //----completed services----        
        closeOpenModalService() {
            this.open_completed = false;
        },
        //----feature list in sales---
        validateIsFeature() {
            if (this.currentFeatureList && this.currentFeatureList.length > 0) {
                const find_data = this.currentFeatureList.find((opt) => opt.name === 'Sales');
                if (find_data && find_data.hasAccess) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        },
        //---service history--
        openServiceHistory() {
            this.showHistory = true;
        },
        closeHistory() {
            this.showHistory = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        //--cloase no-access model--
        closeNoAccess() {
            this.no_access = false;
        }
    },
    mounted() {
        this.fetchApiUpdates();
        this.fetchLocalDataList();
        // console.log('What about data..!,', this.data);
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
        if (!this.currentServiceCategory || this.currentServiceCategory.length === 0) {
            this.fetchServiceCategoryList();
        }
        this.serviceCategoryList();
        if (this.data) {
            this.setOriginalFormValues();
        }
        // console.log(this.isCompleted, 'what happening.....');
        this.isItCompleted = this.isCompleted;
    },
    watch: {
        // Watch for changes in formValues
        formValues: {
            handler(newFormValues) {
                // console.log(newFormValues, 'RWRWRWR');
                // Check for form modification whenever formValues change
                this.checkFormModification();
                // console.log(this.checkFormModification(), 'PPPPPPPPPPPPPPPPPPP');
            },
            immediate: true,
        },
        // Watch for changes in data
        data: {
            handler(newData) {
                // Update your component's data properties                
                this.formValues = newData;
                // console.log(newData.additional, 'RRRR What about data');
                // if (newData.customer)
                if (newData.additional) {
                    this.backup_data = JSON.parse(JSON.stringify(newData.additional));
                }
                this.overall_backup = JSON.parse(JSON.stringify(newData));
                // Set originalFormValues when data changes
                this.setOriginalFormValues();
                // Check for form modification
                this.checkFormModification();

                // console.log(this.checkFormModification(), 'PPPPPPPPPPPPPPPPPPP2222222222222');
            },
            immediate: true,
        },

        isCompleted: {
            deep: true,
            handler(newValue) {
                this.isItCompleted = newValue;
            }
        },
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.open_skeleton = false;
                    this.serviceCategories = newValue;
                    this.updateComponentBasedOnCategoryName(this.category_name);
                }
            }
        },

    },

};
</script>

<style scoped>
.fixed-buttons-container {
    position: fixed;
    bottom: 0;
    width: 100%;
    z-index: 10;
}

.main-content {
    margin-bottom: 50px;
}


@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
    }

    .fixed-buttons-container {
        left: 0px;
        width: 100%;
    }

    .main-content {
        margin-bottom: 50px
    }
}
</style>
