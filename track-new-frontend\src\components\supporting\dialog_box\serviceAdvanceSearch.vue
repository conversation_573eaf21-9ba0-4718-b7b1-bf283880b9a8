<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 text-sm">
        <!-- Modal -->
        <div class="model bg-white w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen, 'pb-[60px]': isMobile }">
            <!-- Close button -->
            <div class="justify-between items-center flex py-4  set-header-background">
                <p for="salesType" class="text-white font-normal text-center flex justify-end px-4">
                    Advance Search
                </p>
                <p class="close pr-5" @click="closeModal">&times;</p>
            </div>
            <div class="p-3 sm:p-5 relative">
                <!-- Category Selection with Search -->
                <div class="flex justify-start  items-center mb-2 sm:mb-4 space-y-2">
                    <select v-model="selectedCategory" @change="handleCategoryChange" class="border p-2 rounded"
                        :class="{ 'w-1/2': isMobile }">
                        <option value="all">All Categories</option>
                        <option v-for="category in categories" :key="category.id" :value="category.id">
                            {{ category.service_category }}
                        </option>
                    </select>
                    <input v-model="searchQuery" @input="handleSearch" type="text" placeholder="Search..."
                        class="ml-4 p-2 border rounded w-full sm:w-3/4 lg:w-1/2" />
                </div>

                <!-- Table Displaying Service Data -->
                <div v-if="data_list && data_list.length > 0" class="overflow-x-auto">
                    <table class="table-auto w-full border-collapse">
                        <thead>
                            <tr class="bg-gray-300">
                                <th class="border px-2 py-1">
                                    <div class="flex justify-between">
                                        <p>Actions</p>
                                        <button @click.stop="toggleDropdown" class="flex items-center p-1 rounded-lg">
                                            <font-awesome-icon icon="fa-solid fa-filter" />
                                        </button>
                                        <div v-if="isDropdownOpen" ref="settingOPtion"
                                            class="absolute mt-1 bg-white border border-gray-400 rounded top-[100px] sm:top-[130px] left-[20px] z-10 overflow-auto h-52 w-48"
                                            style="max-height: 300px;">
                                            <div v-for="(column, index) in getcolumns" :key="index"
                                                class="flex items-center p-2">
                                                <input type="checkbox" v-model="column.visible"
                                                    class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                <span class="text-xs ml-2">{{ column.label }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </th>
                                <!-- <th class="border px-2 py-1"
                                    v-for="(key, index) in Object.keys(JSON.parse(data_list[0].service_data))"
                                    :class="{ 'hidden': special_keys.includes(key) }" :key="index">{{
                                        changeHeaderName(key) }}</th> -->
                                <th class="border px-2 py-1" v-for="(column, index) in getcolumns" :key="index"
                                    :class="{ 'hidden': !column.visible }">
                                    {{ column.label }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(service, num) in data_list" :key="num" class="hover:bg-gray-200">
                                <td class="border px-2 py-1">
                                    <div class="flex space-x-2">
                                        <button v-if="!service.editing" @click="viewRecord(service)"
                                            class="p-1 flex border border-[#8b5cf6;] rounded hover:bg-violet-100 justify-cneter items-center"
                                            title="View">
                                            <font-awesome-icon icon="fa-solid fa-eye" style="color: #8b5cf6;" />
                                            <span v-if="!isMobile" class="px-1 ">View</span></button>
                                        <button v-if="!service.editing && checkRoles(['Sub_Admin', 'admin'])"
                                            @click="editRecord(service)"
                                            class="p-1 flex border border-[#3b82f6;] rounded hover:bg-blue-100 justify-cneter items-center"
                                            title="Edit">
                                            <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                            <span v-if="!isMobile" class="px-1">Edit</span>
                                        </button>
                                    </div>
                                </td>
                                <!-- <td class="border px-2 py-1"
                                    v-for="(key, index) in Object.keys(JSON.parse(data_list[0].service_data))"
                                    :class="{ 'hidden': special_keys.includes(key) }" :key="index">
                                    <span
                                        v-html="highlightText(formatData(JSON.parse(service.service_data)[key], key), searchQuery)"></span>
                                </td> -->
                                <td class="border px-2 py-1" v-for="(column, index) in getcolumns" :key="index"
                                    :class="{ 'hidden': !column.visible }">
                                    <span
                                        v-if="column.key !== 'service_id' && column.key !== 'sale_id' && column.key !== 'service_code'"
                                        v-html="highlightText(formatData(JSON.parse(service.service_data)[column.key], column.key), searchQuery)">
                                    </span>
                                    <span
                                        v-if="(column.key === 'service_id' || column.key === 'sale_id' || column.key === 'service_code') && service[column.key]"
                                        v-html="highlightText(service[column.key], searchQuery)">
                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <div v-if="load_more && isLoadMore" class="flex justify-center mt-4">
                        <button @click="loadMore" class="bg-blue-500 text-white px-4 py-2 rounded">Load More</button>
                    </div>
                </div>
                <div v-else>
                    <div class="flex justify-center items-center">
                        <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                    </div>
                    <p class="text-2xl font-bold text-gray-500 text-center">No Data</p>
                </div>

                <!-- Loader -->
                <div v-if="isLoading" class="text-center mt-4">Loading...</div>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';


export default {
    props: {
        showModal: Boolean,
        isMobile: Boolean,
        empty_data: String
    },
    data() {
        return {
            selectedCategory: 'all',  // Default to all categories
            searchQuery: '',
            currentPage: 1,
            perPage: 50,
            isLoading: false,
            load_more: true,
            isOpen: false,
            data_list: [],
            //----"service_data", ---
            special_keys: ["created_at", "id", "status", "updated_at", "servicecategory", "service_data", "service_track", "customer_id", "pre_repair", "notification", "comments"],
            sub_keys: ["id", "pivot", "user_id", "service_id"],
            //--toaster---
            show: false,
            type_toaster: 'warning',
            message: '',
            //---dropdown--
            isDropdownOpen: false,
            columns: [],
        };
    },
    computed: {
        ...mapGetters('servicesAdvanceSearch', ['currentServiceList', 'getCurrentCategory']),
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        categories() {
            return this.currentServiceCategory || [];
        },
        filteredServices() {
            // Check if the service list exists and filter based on search query
            if (this.currentServiceList && this.currentServiceList.list) {
                return this.currentServiceList.list.filter(service => {
                    const query = this.searchQuery.toLowerCase();

                    // Filter based on category first
                    const matchesCategory = service.categoryId == this.selectedCategory || this.selectedCategory === 'all';

                    // Filter based on search query for dynamic service_data
                    const matchesSearchQuery = this.searchQuery === '' || this.matchServiceData(service);

                    return matchesCategory && matchesSearchQuery;
                });
            }
        },
        getcolumns() {
            if (this.data_list && this.data_list.length > 0 && this.data_list[0].service_data) {
                const default_true = ["customer", "service_id", "service_code", "invoice_id", "problem_title", "brand", "device_model", "serial_number", 'assignWork', "service_type", "status", "comments"];
                // return Object.keys(JSON.parse(this.data_list[0].service_data))
                //     .filter(opt => !this.special_keys.includes(opt)) 
                //     .map(opt => {
                //         return {
                //             key: opt,
                //             label: this.changeHeaderName(opt),  
                //             visible: default_true.includes(opt) 
                //         };
                //     });
                const serviceData = JSON.parse(this.data_list[0].service_data);
                const serviceKeys = Object.keys(serviceData);

                // Ensure the three new keys are added if they don't exist
                const allKeys = [...new Set(["service_id", "service_code", "invoice_id", ...serviceKeys])];

                return allKeys
                    .filter(opt => !this.special_keys.includes(opt))  // Exclude special keys
                    .map(opt => {
                        return {
                            key: opt,
                            label: this.changeHeaderName(opt),  // You can format the label here
                            visible: default_true.includes(opt)  // Set visibility based on default_true
                        };
                    });
            }
            return [];  // Return empty array if no valid data
        },
        isLoadMore() {
            if (this.currentServiceList && this.currentServiceList.list) {
                const find_category = this.currentServiceList.list.find(opt => opt.categoryId == this.selectedCategory);

                if (find_category) {
                    if (find_category.pagination && (find_category.pagination.current_page < find_category.pagination.last_page) && this.currentPage !== (find_category.pagination.current_page * 1) + 1) {
                        return true;

                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            }
        },

    },
    methods: {
        ...mapActions('servicesAdvanceSearch', ['fetchServiceList']),
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),

        // Check if any field in service_data matches the search query
        matchServiceData(service) {
            try {
                const searchQuery = this.searchQuery.toLowerCase();
                if (
                    service.invoice_id && service.invoice_id.toString().toLowerCase().includes(searchQuery) ||
                    service.service_code && service.service_code.toString().toLowerCase().includes(searchQuery) ||
                    service.service_id && service.service_id.toString().toLowerCase().includes(searchQuery)
                ) {
                    return true;
                }
                // If not found in the specific fields, check in service_data (if exists)
                if (service.service_data) {
                    const serviceData = JSON.parse(service.service_data);
                    if (typeof serviceData === 'object' && serviceData !== null) {
                        return this.searchDeep(serviceData, searchQuery);
                    }
                }
                return false;
                // const serviceData = JSON.parse(service.service_data);
                // if (typeof serviceData === 'object' && serviceData !== null) {
                //     return this.searchDeep(serviceData, this.searchQuery.toLowerCase());
                // } else {
                //     return false;
                // }
            } catch (e) {
                console.error('Error parsing service data:', e);
                return false; // Return false if there's an error parsing the service data
            }
        },

        // Recursively search through object keys or array values
        searchDeep(data, query) {
            if (Array.isArray(data)) {
                // If it's an array, loop through each item
                return data.some(item => this.searchDeep(item, query));
            } else if (typeof data === 'object' && data !== null) {
                // If it's an object, loop through each key and value
                return Object.values(data).some(value => this.searchDeep(value, query));
            } else {
                // For primitive values, check if they match the query
                return data && data.toString().toLowerCase().includes(query);
            }
        },

        // Handle category selection change
        handleCategoryChange() {
            this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
        },

        // Handle search input change and filter services
        async handleSearch() {
            if (this.currentServiceList && this.currentServiceList.list) {
                const find_category = this.currentServiceList.list.find(opt => opt.categoryId == this.selectedCategory);

                if (find_category) {
                    if (find_category.pagination && (find_category.pagination.current_page < find_category.pagination.last_page) && this.currentPage !== (find_category.pagination.current_page * 1) + 1) {
                        this.load_more = true;
                        this.currentPage = (find_category.pagination.current_page * 1) + 1;
                        await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                    } else {
                        // Filter the data based on the service data and search query
                        this.data_list = find_category.data.filter(opt => this.matchServiceData(opt));
                        if (this.data_list && this.data_list.length === 0) {
                            setTimeout(async () => {
                                if (find_category.pagination && (find_category.pagination.current_page < find_category.pagination.last_page) && this.currentPage !== (find_category.pagination.current_page * 1) + 1) {
                                    this.currentPage = (find_category.pagination.current_page * 1) + 1;
                                    await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                                }
                            }, 500)
                        }
                        this.load_more = false;
                    }
                } else {
                    this.load_more = true;
                    this.currentPage = 1;
                    await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                }
            }
        },

        // Load more data (pagination)
        async loadMore() {
            this.isLoading = true;
            if (this.currentServiceList && this.currentServiceList.list) {
                const find_category = this.currentServiceList.list.find(opt => opt.categoryId == this.selectedCategory);

                if (find_category) {
                    if (find_category.pagination && (find_category.pagination.current_page < find_category.pagination.last_page) && this.currentPage !== (find_category.pagination.current_page * 1) + 1) {
                        this.currentPage = (find_category.pagination.current_page * 1) + 1;
                        await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                    } else {
                        // Filter the data based on the service data and search query
                        this.data_list = find_category.data.filter(opt => this.matchServiceData(opt));
                    }
                } else {
                    this.currentPage = 1;
                    await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                }
            }
            this.isLoading = false;
        },

        // Close modal
        closeModal() {
            this.$emit('close');
            setTimeout(() => {
                this.isOpen = false;
            }, 100);
        },
        //---higlight and format data----
        // Format the value to display it correctly in the table
        formatData(value, key) {
            // Handle Arrays
            if (Array.isArray(value)) {
                if (typeof value[0] === 'object') {
                    // If array contains objects
                    if (key === 'assignWork') {
                        return value.map(item => `Name: ${item.name || ''}`).join(', ');
                    } else if (key === 'comments') {
                        return value.map(item =>
                            `Message: ${item.comments || ''}, Date: ${item.current_date || ''}, Updated By: ${item.updated_by?.name || ''}`
                        ).join(', ');
                    } else if (key === 'document') {
                        return value.map(item => `File Name: ${item.image || ''}`).join(', ');
                    } else if (key === 'additional') {
                        return value.map(item =>
                            `Item Name: ${item.product_name || ''} -> QTY: ${item.qty || ''}`
                        ).join(', ');
                    } else {
                        return value.map(item =>
                            Object.entries(item)
                                .map(([subKey, subVal]) => !this.sub_keys.includes(subKey) ? `${subKey}: ${this.formatData(subVal)}` : null)
                                .filter(Boolean) // Filter out undefined/null values
                                .join(', ')
                        ).join(', '); // Join all object representations with commas
                    }
                } else {
                    // If it's an array of strings
                    return value.join(', ');
                }
            }

            // Handle Objects
            if (typeof value === 'object' && value !== null) {
                return Object.entries(value)
                    .map(([subKey, subVal]) => {
                        if (!this.sub_keys.includes(subKey)) {
                            return `${subKey}: ${this.formatData(subVal)}`;
                        }
                        return null;
                    })
                    .filter(Boolean) // Filter out undefined/null values
                    .join(', ');
            }

            // For other types like string or primitives
            return value || '';
        },

        // Highlight matched query in text
        highlightText(text, query) {
            // Ensure text is a string before using replace
            text = String(text);

            if (!query) return text; // Return text if no search query

            const regex = new RegExp(`(${query})`, 'gi'); // Case-insensitive match
            return text.replace(regex, '<span style="background-color: yellow;">$1</span>'); // Wrap the match in <span> with background color
        },
        //--make header clean---
        changeHeaderName(value) {
            // Replace underscores with spaces and capitalize each word's first letter
            return value.replace(/_/g, ' ')
                .replace(/\b\w/g, (match) => match.toUpperCase());
        },
        //--role based on--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        editRecord(record) {
            if (!record.invoice_id) {
                this.$router.push({ name: 'service-category-view', params: { viewId: record.id, type: record.servicecategory.service_category, id: record.servicecategory.id } });
            } else {
                this.message = 'This service has already been invoiced, so we cannot edit it';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        viewRecord(record) {
            this.$router.push({ name: 'service-data-view', params: { serviceId: record.id, type: record.servicecategory.service_category, id: record.servicecategory.id } });

        },
        //--setting
        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;
            if (this.isDropdownOpen) {
                // Add event listener when dropdown is opened
                document.addEventListener('click', this.handleOutsideClick);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },
        handleOutsideClick(event) {
            try {
                // console.log(event, 'What happening.....');
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

    },
    watch: {
        showModal(newValue) {
            if (newValue) {
                this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                this.fetchServiceCategoryList();
            }
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },

        // Watch changes in current service list and filter based on search query
        currentServiceList: {
            deep: true,
            handler(newValue) {
                if (this.showModal && newValue && newValue.list && newValue.list.length > 0) {
                    // this.getcolumns();
                    const find_data = newValue.list.find(opt => opt.categoryId == this.selectedCategory);
                    if (find_data && find_data.data) {
                        if (this.searchQuery === '') {
                            this.data_list = find_data.data;
                        } else {
                            this.data_list = find_data.data.filter(opt => this.matchServiceData(opt));
                        }
                    }
                }
            }
        },

        selectedCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && this.showModal) {
                    this.load_more = true;
                    this.currentPage = 1;
                }
            }
        }
    }
};
</script>
