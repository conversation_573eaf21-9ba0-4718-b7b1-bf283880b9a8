<?php

namespace App\Http\Controllers\API;


use App\Models\Option;

use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class ProductsController
 * @package App\Http\Controllers\API
 */

class OptionAPIController extends AppBaseController
{
  public function index(Request $request)
  {
      // Fetch the option by the key 'whatsapp_templates'
      $tax = Option::where('key', 'whatsapp_templates')->first();

      if (!$tax) {
          return response()->json([
              'success' => false,
              'message' => 'No templates found.'
          ], 404);
      }

      $response = [
          'success' => true,
          'data' => $tax, // Assuming 'items' is the data you want to retrieve
      ];

      return response()->json($response);
  }

}
