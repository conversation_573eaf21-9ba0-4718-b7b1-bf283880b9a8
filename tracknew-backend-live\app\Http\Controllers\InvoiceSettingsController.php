<?php

namespace App\Http\Controllers;

use App\DataTables\InvoiceSettingsDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateInvoiceSettingsRequest;
use App\Http\Requests\UpdateInvoiceSettingsRequest;
use App\Repositories\InvoiceSettingsRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class InvoiceSettingsController extends AppBaseController
{
    /** @var InvoiceSettingsRepository $invoiceSettingsRepository*/
    private $invoiceSettingsRepository;

    public function __construct(InvoiceSettingsRepository $invoiceSettingsRepo)
    {
        $this->invoiceSettingsRepository = $invoiceSettingsRepo;
    }

    /**
     * Display a listing of the InvoiceSettings.
     *
     * @param InvoiceSettingsDataTable $invoiceSettingsDataTable
     *
     * @return Response
     */
    public function index(InvoiceSettingsDataTable $invoiceSettingsDataTable)
    {
        return $invoiceSettingsDataTable->render('invoice_settings.index');
    }

    /**
     * Show the form for creating a new InvoiceSettings.
     *
     * @return Response
     */
    public function create()
    {
        return view('invoice_settings.create');
    }

    /**
     * Store a newly created InvoiceSettings in storage.
     *
     * @param CreateInvoiceSettingsRequest $request
     *
     * @return Response
     */
    public function store(CreateInvoiceSettingsRequest $request)
    {
        $input = $request->all();

        $invoiceSettings = $this->invoiceSettingsRepository->create($input);

        Flash::success('Invoice Settings saved successfully.');

        return redirect(route('invoiceSettings.index'));
    }

    /**
     * Display the specified InvoiceSettings.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $invoiceSettings = $this->invoiceSettingsRepository->find($id);

        if (empty($invoiceSettings)) {
            Flash::error('Invoice Settings not found');

            return redirect(route('invoiceSettings.index'));
        }

        return view('invoice_settings.show')->with('invoiceSettings', $invoiceSettings);
    }

    /**
     * Show the form for editing the specified InvoiceSettings.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $invoiceSettings = $this->invoiceSettingsRepository->find($id);

        if (empty($invoiceSettings)) {
            Flash::error('Invoice Settings not found');

            return redirect(route('invoiceSettings.index'));
        }

        return view('invoice_settings.edit')->with('invoiceSettings', $invoiceSettings);
    }

    /**
     * Update the specified InvoiceSettings in storage.
     *
     * @param int $id
     * @param UpdateInvoiceSettingsRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateInvoiceSettingsRequest $request)
    {
        $invoiceSettings = $this->invoiceSettingsRepository->find($id);

        if (empty($invoiceSettings)) {
            Flash::error('Invoice Settings not found');

            return redirect(route('invoiceSettings.index'));
        }

        $invoiceSettings = $this->invoiceSettingsRepository->update($request->all(), $id);

        Flash::success('Invoice Settings updated successfully.');

        return redirect(route('invoiceSettings.index'));
    }

    /**
     * Remove the specified InvoiceSettings from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $invoiceSettings = $this->invoiceSettingsRepository->find($id);

        if (empty($invoiceSettings)) {
            Flash::error('Invoice Settings not found');

            return redirect(route('invoiceSettings.index'));
        }

        $this->invoiceSettingsRepository->delete($id);

        Flash::success('Invoice Settings deleted successfully.');

        return redirect(route('invoiceSettings.index'));
    }
}
