<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateInvoiceSettingsAPIRequest;
use App\Http\Requests\API\UpdateInvoiceSettingsAPIRequest;
use App\Models\InvoiceSettings;
use App\Repositories\InvoiceSettingsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class InvoiceSettingsController
 * @package App\Http\Controllers\API
 */

class InvoiceSettingsAPIController extends AppBaseController
{
    /** @var  InvoiceSettingsRepository */
    private $invoiceSettingsRepository;

    public function __construct(InvoiceSettingsRepository $invoiceSettingsRepo)
    {
        $this->invoiceSettingsRepository = $invoiceSettingsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/invoice_settings",
     *      summary="getInvoiceSettingsList",
     *      tags={"InvoiceSettings"},
     *      description="Get all InvoiceSettings",
     *      @OA\Parameter(
     *          name="company_id",
     *          description="ID of the company whose services are to be fetched",
     *          @OA\Schema(
     *              type="string"
     *          ),
     *          required=true,
     *          in="query"
     *      ),
     *      @OA\Parameter(
     *          name="page",
     *          description="Page number for pagination",
     *          @OA\Schema(
     *              type="integer"
     *          ),
     *          in="query"
     *      ),
     *      @OA\Parameter(
     *          name="per_page",
     *          description="Number of items per page",
     *          @OA\Schema(
     *              type="integer"
     *          ),
     *          in="query"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/InvoiceSettings")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $invoicesQuery = InvoiceSettings::where('company_id', $companyId);

        if ($perPage === 'all') {
            $perPage =  $invoicesQuery->count();
        }

        $invoices =  $invoicesQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $invoices->items(), 
            'pagination' => [
                'total' => $invoices->total(),
                'per_page' => $invoices->perPage(),
                'current_page' => $invoices->currentPage(),
                'last_page' => $invoices->lastPage(),
                'from' => $invoices->firstItem(),
                'to' => $invoices->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/invoice_settings",
     *      summary="createInvoiceSettings",
     *      tags={"InvoiceSettings"},
     *      description="Create InvoiceSettings",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/InvoiceSettings")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/InvoiceSettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateInvoiceSettingsAPIRequest $request)
    {
        $input = $request->all();

        $invoiceSettings = $this->invoiceSettingsRepository->create($input);

        return $this->sendResponse($invoiceSettings->toArray(), 'Invoice Settings saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/invoice_settings/{id}",
     *      summary="getInvoiceSettingsItem",
     *      tags={"InvoiceSettings"},
     *      description="Get InvoiceSettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of InvoiceSettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/InvoiceSettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var InvoiceSettings $invoiceSettings */
        $invoiceSettings = $this->invoiceSettingsRepository->find($id);

        if (empty($invoiceSettings)) {
            return $this->sendError('Invoice Settings not found');
        }

        return $this->sendResponse($invoiceSettings->toArray(), 'Invoice Settings retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/invoice_settings/{id}",
     *      summary="updateInvoiceSettings",
     *      tags={"InvoiceSettings"},
     *      description="Update InvoiceSettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of InvoiceSettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/InvoiceSettings")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/InvoiceSettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateInvoiceSettingsAPIRequest $request)
    {
        $input = $request->all();

        /** @var InvoiceSettings $invoiceSettings */
        $invoiceSettings = $this->invoiceSettingsRepository->find($id);

        if (empty($invoiceSettings)) {
            return $this->sendError('Invoice Settings not found');
        }

        $invoiceSettings = $this->invoiceSettingsRepository->update($input, $id);

        return $this->sendResponse($invoiceSettings->toArray(), 'InvoiceSettings updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/invoice_settings/{id}",
     *      summary="deleteInvoiceSettings",
     *      tags={"InvoiceSettings"},
     *      description="Delete InvoiceSettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of InvoiceSettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var InvoiceSettings $invoiceSettings */
        $invoiceSettings = $this->invoiceSettingsRepository->find($id);

        if (empty($invoiceSettings)) {
            return $this->sendError('Invoice Settings not found');
        }

        $invoiceSettings->delete();

        return $this->sendSuccess('Invoice Settings deleted successfully');
    }
}
