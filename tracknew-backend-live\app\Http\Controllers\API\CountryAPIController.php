<?php

namespace App\Http\Controllers\Api;

use App\Models\Country;
use App\Http\Controllers\AppBaseController;
use Illuminate\Http\Request;

class CountryAPIController extends AppBaseController
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
       
        $Query = Country::where('status', 1)->where('is_featured', 0);


        if ($perPage === 'all') {
            $perPage = $Query->count();
        }    

        $tax = $Query->orderBy('id', 'asc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $tax->items(), // Get the paginated items
            'pagination' => [
                'total' => $tax->total(),
                'per_page' => $tax->perPage(),
                'current_page' => $tax->currentPage(),
                'last_page' => $tax->lastPage(),
                'from' => $tax->firstItem(),
                'to' => $tax->lastItem(),
            ]
        ];

        return response()->json($response);
    }
    
}
