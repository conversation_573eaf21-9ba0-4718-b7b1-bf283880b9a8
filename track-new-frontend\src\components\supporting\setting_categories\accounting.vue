<template>
  <div class="w-full flex flex-col items-center">
    <!---profil avatar-->
    <!---Profile image-->
    <div class="flex items-center pl-4 pr-4 cursor-pointer">
      <div class="relative items-center mt-3" title="Change profile" @click="openFileInput"
        @mouseover="isHovered = true" @mouseleave="isHovered = false">
        <img v-if="formValues.avatar" :src="formValues.avatar"
          class="w-[120px] h-[120px] justify-center border border-gray-300" :class="{ 'filter': isHovered }" />
        <img v-if="!formValues.avatar" :src="upload_profile" class="w-[100px] h-[100px] justify-center"
          :class="{ 'filter': isHovered }" />
        <input ref="fileInput" type="file" style="display: none" accept="image/*" @change="handleImageChangeProfile" />
        <div class="absolute inset-0 flex mt-6 justify-center items-center" v-show="isHovered">
          <span class="text-gray-500 text-xs font-bold text-center">Change profile</span>
        </div>
        <!--loader circle-->
        <div v-if="circle_loader_photo" class="flex">
          <CircleLoader :loading="circle_loader_photo"></CircleLoader>
        </div>
      </div>
    </div>
    <!-- 1. Name label and input field -->
    <div class="w-full mb-4">
      <!--tooltip-->
      <div v-if="tooltip_focus && tooltip_focus === 'name'"
        class="absolute flex flex-col items-center group-hover:flex -mt-2">
        <span
          class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
          <p>Name can't be change</p>
        </span>
        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
      </div>
      <label for="name" class="block text-md font-bold">
        Name
      </label>
      <input type="text" id="name" v-model="formValues.name" name="name" @focus="tooltip_focus = 'name'"
        @blur="tooltip_focus = null" class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter your name"
        readonly />
    </div>

    <!-- 2. Current Email label and password input field with visible eye icon -->
    <div class="w-full  mb-4">
      <!--tooltip-->
      <div v-if="tooltip_focus && tooltip_focus === 'email' && formValues.email && formValues.email !== ''"
        class="absolute flex flex-col items-center group-hover:flex -mt-3">
        <span
          class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
          <p>Email can't be change</p>
        </span>
        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
      </div>
      <label for="email_id" class="block text-md font-bold">
        Email
      </label>
      <div class="relative">
        <input type="email" id="email_id" v-model="formValues.email" @focus="tooltip_focus = 'email'"
          @blur="tooltip_focus = null" class="mt-1 p-2 border border-gray-300 w-full pr-10" placeholder="Enter email id"
          :readonly="formValues.email && formValues.email !== ''" @change="is_updated = true" />
      </div>
    </div>

    <!-- 3. New Password label and password input field with visible eye icon -->
    <div class="w-full  mb-4">
      <label for="newPassword" class="block text-md font-bold">
        New Password
      </label>
      <div class="relative">
        <input type="password" id="newPassword" v-model="formValues.new_password" @change="is_updated = true"
          class="mt-1 p-2 border border-gray-300 w-full pr-10" placeholder="Enter new password" />
        <span v-if="formValues.new_password && formValues.new_password.length < 7" class="text-xs text-red-700">Please
          enter maximum 8 characters</span>
      </div>
    </div>

    <!-- 4. Confirm Password label and password input field with visible eye icon -->
    <div class="w-full  mb-4">
      <label for="confirmPassword" class="block text-md font-bold">
        Confirm Password
      </label>
      <div class="relative">
        <input type="password" id="confirmPassword" v-model="formValues.confirm_password" @change="is_updated = true"
          class="mt-1 p-2 border border-gray-300 w-full pr-10" placeholder="Confirm new password" />
        <span v-if="formValues.new_password && formValues.new_password !== formValues.confirm_password"
          class="text-red-600 text-xs">
          {{ formValues.confirm_password ? 'Please enter valid confirm password' :
            'Please enter confirm password' }}</span>
      </div>
    </div>
    <!-- 5. Save Button -->
    <button @click="saveData"
      class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 text-white py-2 px-4 rounded-md self-center hover:bg-green-500">
      Update
    </button>
  </div>
  <dialogAlert :showModal="open_message" :message="message" @close="closeMessage"></dialogAlert>
  <Loader :showModal="open_loader"></Loader>
</template>

<script>
import axios from 'axios';
import dialogAlert from '../dialog_box/dialogAlert.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'account',
  emits: ['is-sales-save', 'updatesalesData'],
  components: {
    dialogAlert
  },
  props: {
    companyId: String,
    userId: String,
    store_refresh: Boolean,
    save_success: Boolean
  },
  data() {
    return {
      formValues: {},
      tooltip_focus: null,
      currentPasswordVisible: false,
      newPasswordVisible: false,
      confirmPasswordVisible: false,
      open_eye: '/images/setting_page/eye.png',
      close_eye: '/images/setting_page/hide.png',
      open_message: false,
      message: '',
      open_loader: false,
      circle_loader_photo: false,
      isHovered: false,
      upload_profile: '/images/setting_page/profile.png',
      is_updated: false,
    };
  },
  computed: {
    ...mapGetters('localStorageData', ['currentLocalDataList']),
  },
  methods: {
    ...mapActions('localStorageData', ['fetchLocalDataList']),

    togglePasswordVisibility(passwordType) {
      this[`${passwordType}Visible`] = !this[`${passwordType}Visible`];
    },
    saveData() {
      this.open_loader = true;
      if (this.formValues.email && this.formValues.email !== '' && this.formValues.confirm_password && this.formValues.new_password && this.formValues.new_password.length > 7 && this.formValues.new_password === this.formValues.confirm_password) {
        axios.post('/profile-update', { company_id: this.companyId, user_id: this.userId, ...this.formValues })
          .then(response => {
            // console.log(response.data);
            this.open_loader = false;
            this.formValues.new_password = '';
            this.formValues.confirm_password = '';
            this.message = response.data.message;
            this.open_message = true;
            const collectForm = localStorage.getItem('track_new');
            if (collectForm) {
              let dataParse = JSON.parse(collectForm);
              if (dataParse.avatar !== this.formValues.avatar) {
                dataParse.avatar = this.formValues.avatar;
                localStorage.setItem('track_new', JSON.stringify(dataParse));
                this.fetchLocalDataList();
              }
            }
            this.$emit('is-sales-save', false);
          })
          .catch(error => {
            console.error('Error', error);
            this.open_loader = false;
            this.message = error.response.data.message;
            this.open_message = true;
          })

      } else {
        this.open_loader = false;
        this.message = !this.formVa
        this.$emit('updatesalesData');
      }
    },
    closeMessage() {
      this.open_message = false;
      this.message = '';
    },
    //--upload image--
    //----Profile image--
    openFileInput() {
      // Trigger a click on the hidden file input

      this.$refs.fileInput.click();
      this.is_updated = true;
    },
    async handleImageChangeProfile(event) {
      const file = event.target.files[0];
      this.is_updated = true;
      // this.circle_loader_photo = true;
      // if (file) {
      //     this.uploadImageProfile(file);
      // }
      if (!file) return;

      // Check file size (in bytes)
      const maxSizeBytes = 500 * 1024; // 500kb in bytes
      if (file.size > maxSizeBytes) {
        // Image exceeds 500kb, compress it
        try {
          this.circle_loader_photo = true; // Show loader
          const compressedFile = await this.compressImage(file);

          this.uploadImageProfile(compressedFile);
        } catch (error) {
          console.error("Error compressing image:", error);
          this.circle_loader_photo = false; // Hide loader on error
        }
      } else {
        // Image is <= 500kb, upload directly
        try {
          this.circle_loader_photo = true;

          this.uploadImageProfile(file);
        } catch (error) {
          console.error("Error uploading image:", error);
          this.circle_loader_photo = false; // Hide loader on error
        }
      }
    },
    uploadImageProfile(file) {
      const formData = new FormData();
      formData.append("image", file);
      formData.append("model", "Employee");
      formData.append("company_id", this.companyID);
      // Make an API request to Laravel backend
      // console.log(file, 'RRRR');
      axios.post('/image', formData)
        .then(response => {
          // console.log(response.data, 'What happrning....Q');
          this.circle_loader_photo = false;
          //  let data_save = { image_path: response.data.image_path };
          if (this.formValues.avatar && this.formValues.avatar !== '') {
            this.removeExistAvatar(this.formValues.avatar);
          }
          this.formValues.avatar = response.data.media_url;
        })
        .catch(error => {
          console.error("Error uploading image", error);
          this.circle_loader_photo = false;
        });
    },
    removeExistAvatar(url) {
      axios.delete('/delete-image', { params: { model: "Employee", image_url: url } })
        .then(response => {
          console.log(response.data, 'delete image avatar..!');
        })
        .catch(error => {
          console.error('Error', error);
        })
    },
    compressImage(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = (event) => {
          const img = new Image();
          img.onload = () => {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');

            // Set maximum width and height for compressed image
            const maxWidth = 800;
            const maxHeight = 600;

            let width = img.width;
            let height = img.height;

            // Calculate new dimensions while maintaining aspect ratio
            if (width > maxWidth || height > maxHeight) {
              const aspectRatio = width / height;
              if (width > height) {
                width = maxWidth;
                height = width / aspectRatio;
              } else {
                height = maxHeight;
                width = height * aspectRatio;
              }
            }

            canvas.width = width;
            canvas.height = height;

            ctx.drawImage(img, 0, 0, width, height);

            canvas.toBlob((blob) => {
              const compressedFile = new File([blob], file.name, {
                type: 'image/jpeg', // Set desired output mime type
                lastModified: Date.now()
              });
              resolve(compressedFile);
            }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
          };
          img.src = event.target.result;
        };
        reader.readAsDataURL(file);
      });
    },
  },
  mounted() {
    const collectForm = localStorage.getItem('track_new');
    if (collectForm) {
      let dataParse = JSON.parse(collectForm);
      // // console.log(dataParse, 'WWWWWWWWWWWW');
      // this.servicecategory_data = dataParse;
      this.formValues.name = dataParse.name;
      this.formValues.email = dataParse.email;
      this.formValues.avatar = dataParse.avatar;
    }
  },
  watch: {
    store_refresh: {
      deep: true,
      handler(newValue) {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
          let dataParse = JSON.parse(collectForm);
          // // console.log(dataParse, 'WWWWWWWWWWWW');
          // this.servicecategory_data = dataParse;
          this.formValues.name = dataParse.name;
          this.formValues.email = dataParse.email;
          this.formValues.avatar = dataParse.avatar;

        }
      }
    },
    save_success: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.saveData();
        }
      }
    },
    is_updated: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.is_updated = false;
          this.$emit('is-sales-save', newValue);
        }
      }
    }
  }
};
</script>