<?php

namespace App\Http\Controllers\API;


use App\Models\Notification;

use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;

use Response;

/**
 * Class NotificationController
 * @package App\Http\Controllers\API
 */

class NotificationAPIController extends AppBaseController
{	
  
    public function notificationList(Request $request)
    {
        $user = auth()->user();
       
        $user->last_notification_seen = now();
        $user->save();
      
        $type = $request->get('type', null);
      	$notificationId = $request->get('notification_id', null);
      
       
      
        if ($type == "markas_read" && $notificationId) {
        
        	$notification = \App\Models\Notification::where('id', $notificationId)->first();
          
            if ($notification) {
                 $notification->update(['read_at' => now()]);
            }
         
        
        } 
      	elseif ($type == "markas_read" && $user->unreadNotifications->count() > 0) {
          
              $user->unreadNotifications->markAsRead();
         }

        
        $perPage = $request->get('per_page', 10); 
        $page = $request->get('page', 1); 

        
      if ($user->user_type == 'admin') {   

        // Get notifications for all users in the same company
        $notifications = \App\Models\Notification::where('company_id', $user->company_id)
            ->orderBy('created_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        // Count all unread notifications for users in the same company
        $allUnreadCount = \App\Models\Notification::where('company_id', $user->company_id)
            ->whereNull('read_at')
            ->count();

      } 
      elseif ($user->user_type == 'employee') {
          // Get notifications specific to this employee
          $notifications = $user->notifications()
              ->where('notifiable_id', $user->id)
              ->orderBy('created_at', 'desc')
              ->paginate($perPage, ['*'], 'page', $page);

          // Count unread notifications for this employee
          $allUnreadCount = $user->unreadNotifications->count();
      } 
      else {
          return response()->json(['error' => 'Invalid user type'], 403);
      }
      // Decode notification data for both admin and employee
      $items = $notifications->map(function ($notification) {
          $notificationData = is_string($notification->data) ? json_decode($notification->data, true) : $notification->data;

          return [
              'id' => $notification->id,
              'type' => $notification->type,
              'notifiable_type' => $notification->notifiable_type,
              'notifiable_id' => $notification->notifiable_id,
              'data' => $notificationData, // Decoded data
              'read_at' => $notification->read_at,
              'created_at' => $notification->created_at,
              'updated_at' => $notification->updated_at,
              'company_id' => $notification->company_id,
          ];
      });
    // Prepare response data with pagination details
    $response = [
        'notification_data' => $items,
        'all_unread_count' => $allUnreadCount,
        'current_page' => $notifications->currentPage(),
        'per_page' => $notifications->perPage(),
        'total' => $notifications->total(),
        'last_page' => $notifications->lastPage(),
    ];

    return response()->json($response);

    }

}
