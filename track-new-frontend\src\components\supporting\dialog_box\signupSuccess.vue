<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center  z-30 h-screen overflow-auto">

        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <!-- Modal Content -->
            <div class="relative bg-white rounded-lg shadow-lg p-6 w-full max-w-md text-center">

                <!-- Success Icon -->
                <div class="text-green-500 text-6xl mb-4">
                    <font-awesome-icon icon="fa-solid fa-circle-check" />
                </div>

                <!-- Success Message -->
                <h2 class="text-2xl font-bold text-gray-800 mb-4">
                    Success! Your account has been created.
                </h2>

                <!-- Buttons -->
                <div class="flex justify-center space-x-4">
                    <button @click="redirectToLogin"
                        class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600">
                        Login
                    </button>
                    <button @click="redirectToOtpLogin"
                        class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600">
                        Login by Mobile OTP
                    </button>
                </div>
            </div>
        </div>
        <!-- <div class="absolute">
            <confettianimation v-if="showModal"></confettianimation>
        </div> -->

    </div>
</template>

<script>
import confettianimation from './confettianimation.vue';
export default {
    components: {
        confettianimation
    },
    props: {
        showModal: {
            type: Boolean,
            required: true,
        },
        mobile: {
            type: String,
            required: false,
        },
        email: {
            type: String,
            required: false,
        }
    },
    data() {
        return {
            isOpen: false,
            confettis: [],
        };
    },
    methods: {
        redirectToLogin() {
            const queryData = {
                email: this.email || null, // Replace with the actual email data
                mobile: this.mobile || null, // Replace with the actual mobile number data
            };
            this.$router.push({ path: "/login", query: queryData });
        },
        redirectToOtpLogin() {
            const queryData = {
                mobile: this.mobile || null, // Replace with the actual mobile number data
            };
            this.$router.push({ path: "/mobilelogin", query: queryData });
        },
    },
    watch: {
        showModal: {
            immediate: true,
            handler(newValue) {
                if (newValue) {
                    this.isOpen = true;
                }
            },
        },
    },
};
</script>

<style scoped>
/* Confetti */
.confetti {
    opacity: 0.9;
    transform: rotate(45deg);
}

/* Confetti Fall Animation */
@keyframes confetti-fall {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 1;
    }

    100% {
        transform: translateY(100vh) rotate(720deg);
        opacity: 0;
    }
}
</style>
