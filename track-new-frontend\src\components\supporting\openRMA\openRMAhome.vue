<template>
    <div :class="{ 'manualStyle text-sm sm:mb-[60px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        ref="scrollContainer" @scroll="handleScroll">
        <div class="rounded-lg shadow-md">
            <div class="my-custom-margin">
                <!--page header-->
                <div v-if="!isMobile" class="m-1 my-3 flex items-center space-x-4">
                    <p class="font-bold text-xl">RMA</p>
                    <div v-if="!open_skeleton"
                        class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                        <p class="text-gray-700">Total RMA
                            :</p>
                        <p class="font-semibold pl-1">
                            {{ pagination.total }}
                        </p>
                    </div>
                </div>
                <!--new design header-->
                <div v-if="!isMobile" class="flex justify-between m-1 mt-5">
                    <div class="flex mr-2 space-x-4">
                        <button @click="addRepair" :class="{ 'mr-2': isMobile }"
                            class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center">
                            <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
                            <span v-if="!isMobile" class="text-center">New RMA</span>
                        </button>
                        <!--view design-->
                        <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                            <div v-if="items_category === 'tile'"
                                class="info-msg px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200"
                                @click="toggleView" :title02="'Table view'"
                                :class="{ 'bg-white': items_category === 'tile' }">
                                <font-awesome-icon icon="fa-solid fa-check" class="pr-2 text-green-600" />
                                <font-awesome-icon icon="fa-solid fa-bars" />
                            </div>
                            <div v-if="items_category !== 'tile'"
                                class="px-2 py-1 rounded-lg border border-gray-400flex-shrink-0 cursor-pointer info-msg  hover:bg-blue-200"
                                :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                                :title02="'Card view'">
                                <font-awesome-icon icon="fa-solid fa-check" class="pr-2 text-green-600" />
                                <font-awesome-icon icon="fa-solid fa-grip" />
                            </div>
                        </div>

                    </div>
                    <!----Filter Model-->
                    <!--Setting-->
                    <div class="flex icon-color">

                        <!----filter options----->
                        <!-- Main Filter Button -->
                        <div ref="dropdownContainerFilter" class="ml-5 relative">
                            <button @click="toggleMainDropdown" :disabled="repairs.length == 0"
                                class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                                :class="{ 'cursor-not-allowed': repairs.length == 0 }">
                                <span class="inline-flex items-center w-full pointer-events-none">
                                    <font-awesome-icon icon="fa-solid fa-filter" class="px-1" /> Filter
                                    <font-awesome-icon v-if="!isMainDropdownOpen" icon="fa-solid fa-angle-down"
                                        class="pl-3" />
                                    <font-awesome-icon v-if="isMainDropdownOpen" icon="fa-solid fa-angle-up"
                                        class="pl-3" />
                                </span>
                            </button>
                            <!-- Main Dropdown -->
                            <div v-if="isMainDropdownOpen" ref="mainDropdown"
                                class="absolute mt-2 w-56 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border right-0">
                                <div class="py-1">
                                    <!-- Other Options -->
                                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                        @click.stop="changeFilter('by Date')">By Date</button>
                                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                        @click.stop="changeFilter('customer')">By Customer</button>
                                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                        @click.stop="changeFilter('status')">By Status</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!---fiter information-->
                <div v-if="Object.keys(filteredBy).length > 0" class="text-xs flex -mb-3 flex-row overflow-auto">
                    <p class="text-blue-600 mr-2">Filtered By:</p>
                    <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2">
                        <p class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                        <p>{{ key === 'assign_to' ? value.join(', ') : key === 'type' ? typeList[value] :
                            key === 'status' ?
                                options[value] ? options[value].label : options[value] : value }}</p>
                    </div>
                    <button @click="resetTheFilter" title="reset filter"
                        class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
                </div>
                <!---load skeleton-->
                <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                    :cols="items_category === 'tile' ? number_of_columns : 3"
                    :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                    :type="items_category === 'tile' ? 'table' : 'grid'">
                </skeleton>
                <div v-if="!open_skeleton && repairs.length > 0" class="text-sm mt-5" :class="{ 'm-1': !isMobile }">
                    <div
                        :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                        <div v-if="!isMobile" class="flex justify-between"
                            :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                            <!---records per page with refresh-->
                            <div class="flex space-x-4 items-center">
                                <div>
                                    <select v-model="recordsPerPage" @change="changePage"
                                        class="border border-gray-300 rounded-lg pr-5 p-1">
                                        <option v-for="option in options_page" :key="option" :value="option"
                                            class="text-xs">
                                            {{ option }}
                                        </option>
                                    </select>
                                </div>
                                <div>
                                    <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                        :title02="'Refersh Data'" @click="refreshDataTable">
                                        <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                    </button>
                                </div>
                            </div>
                            <!--search bar--->
                            <div>
                                <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                    @resetData="resetToSearch" :resetSearch="resetSearch">
                                </searchCustomer>
                            </div>
                        </div>
                        <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                            <table class="table w-full">
                                <thead>
                                    <tr class="table-head">
                                        <!--dynamic-->
                                        <th v-for="(column, index) in dynamicFields" :key="index"
                                            :class="{ hidden: !column.visible }"
                                            class="py-2 cursor-pointer relative table-border"
                                            @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                            @click="getSortType(column)">
                                            <div class="flex justify-between items-center px-2 relative">
                                                <p>{{ column.label === 'Created At' ? 'Date' : column.label }}</p>
                                                <div v-if="isShortVisible(column.field)"
                                                    class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                    <span
                                                        v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                        class="info-msg"
                                                        :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                        :title02="'Ascending order'">
                                                        <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                    </span>
                                                    <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                        class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                        :title02="'Descending order'">
                                                        <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                    </span>
                                                </div>
                                            </div>
                                        </th>
                                        <th class="py-2 leading-none px-2 table-border">
                                            <div class="flex justify-center items-center space-x-2">
                                                <p>Actions</p>
                                                <div class="relative">
                                                    <button @click.stop="toggleDropdown"
                                                        class="flex items-center p-1 rounded-lg">
                                                        <font-awesome-icon icon="fa-solid fa-filter" />
                                                    </button>
                                                    <div v-if="isDropdownOpen" ref="settingOPtion"
                                                        class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                        style="max-height: 300px;">
                                                        <div v-for="(column, index) in columns" :key="index"
                                                            class="flex items-center p-2">
                                                            <input type="checkbox" v-model="column.visible"
                                                                class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                            <span class="text-xs ml-2">{{ column.label }}</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr v-for="(record, index) in repairs" :key="index"
                                        class="hover:bg-gray-200 cursor-pointer">
                                        <td v-for="(column, colIndex) in columns" :key="colIndex"
                                            @click="startEdit(record)" :class="{ 'hidden': !column.visible }"
                                            class="px-1 py-1 table-border">
                                            <span v-if="column.field === 'created_at'" class="text-blue-800 text-xs"
                                                :title="calculateDaysAgo(formattedDate(record[column.field]), true)">
                                                {{ validateDateTime(record[column.field]) ?
                                                    formatDateTime(formattedDate(record[column.field]))
                                                    : '' }}</span>
                                            <span v-if="column.field === 'created_date'">
                                                {{ validateDateTime(record['created_at']) ?
                                                    formatDateTime(formattedDate(record['created_at'])) : '' }}</span>
                                            <span v-if="column.field === 'cost'">
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9'
                                                    : currentCompanyList.currency }} {{ record['total_cost'] }}</span>
                                            <span v-if="column.field === 'complete_date' && record['complete_date']">
                                                {{ validateDateTime(record[column.field]) ?
                                                    formatDateTime(formattedDate(record['complete_date'])) : '' }}</span>
                                            <span v-if="column.field === 'customer'" class="hover:text-sky-700"
                                                @mouseover="showModal(record['customer'], $event)"
                                                @mouseleave="hideModal">
                                                {{ record[column.field].first_name
                                                    + ' - ' + record[column.field].contact_number }}
                                            </span>
                                            <span v-if="column.field === 'payment_amount'">
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9'
                                                    : currentCompanyList.currency }}
                                                {{ record['payment_amount'] }}
                                            </span>
                                            <span v-if="column.field === 'payment_type'">{{ record['payment_type']
                                            }}</span>
                                            <span v-if="column.field === 'product_id'">{{ record['product'] &&
                                                record['product'].product_name ? record['product'].product_name : ''
                                            }}</span>
                                            <span
                                                v-if="column.field !== 'payment_type' && column.field !== 'created_at' && column.field !== 'rma_status' && column.field !== 'customer' && column.field !== 'complete_date' && column.field !== 'cost' && column.field !== 'product_id'">{{
                                                    record[column.field] }}</span>
                                            <span v-if="column.field === 'rma_status'" class="py-1 px-2 rounded text-xs"
                                                :class="options[record['rma_status']]['class']">
                                                {{ options[record['rma_status']]['label'] }}
                                            </span>
                                        </td>
                                        <td class="py-2 text-center table-border">
                                            <div class="flex justify-center">
                                                <!--More actions-->
                                                <div class="flex relative">
                                                    <div class="flex">
                                                        <button
                                                            v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                            @click="startEdit(record)" class="px-1" title="Edit">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                        </button>
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)" title="Delete"
                                                            class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                        </button>
                                                    </div>
                                                    <!-- <button @click.stop="displayAction(index)"
                                                        class="px-2 py-1 text-blue-800 rounded"
                                                        :class="{ 'bg-blue-100': display_option === index }">
                                                        <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                            size="lg" />
                                                    </button> -->
                                                </div>
                                                <div v-if="display_option === index" :ref="'dropdown' + index"
                                                    class="z-10 mt-7 absolute border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                    style="display: flex; justify-content: center; align-items: center;">
                                                    <ul class="py-1 text-gray-700"
                                                        aria-labelledby="dropdownDefaultButton">

                                                        <li class=" hover:bg-gray-200"
                                                            :class="{ 'hidden': record.status === '1' }">
                                                            <button v-if="!record.editing" @click="startEdit(record)"
                                                                class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-pencil"
                                                                    style="color: #3b82f6;" />
                                                                <span class="px-2">Edit</span>
                                                            </button>
                                                        </li>
                                                        <li class=" hover:bg-gray-200" v-if="index === 0"
                                                            :class="{ 'hidden': record.status === '1' }">
                                                            <button v-if="checkRoles(['admin'])"
                                                                @click="confirmDelete(index)"
                                                                class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                    style="color: #ef4444" />
                                                                <span class="px-2">Delete</span>
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>

                                        </td>
                                    </tr>
                                    <tr class="items-center justify-center flex border"
                                        v-if="!repairs || repairs.length === 0">
                                        <td v-if="Object.keys(filteredBy).length === 0" colspan="5">
                                            <button
                                                class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                                @click="openPayment">
                                                + Create Repair
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <!-- Hover Modal -->
                            <customerDataTable v-if="isModalVisible" :showModal="isModalVisible" :modalData="modalData"
                                :modalPosition="modalPosition" @mouseover="toggleHoverModal(true)"
                                @mouseleave="toggleHoverModal(false)" @mousemove="showModal(null, $event)">
                            </customerDataTable>
                        </div>
                        <!--card view-->
                        <div v-if="items_category === 'list'"
                            class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 sm:gap-4 custom-scrollbar-hidden">
                            <div v-for="(record, index) in repairs" :key="index" class="w-full">
                                <div
                                    class="max-w-md mx-auto shadow-lg bg-white rounded-xl border border-gray-200 md:max-w-2xl">
                                    <!-- Top Section -->
                                    <div class="flex justify-between items-center px-4 py-2">
                                        <!-- Left Side (Can be your dynamic content) -->
                                        <div class="text-xs text-red-500 items-center cursor-pointer"
                                            :title="formatDateTime(formattedDate(record['created_at']))">
                                            <p>{{
                                                validateDateTime(record['created_at']) ?
                                                    calculateDaysAgo(formattedDate(record['created_at'])) : '' }}</p>
                                        </div>
                                        <!-- Right Side (Actions) -->
                                        <div class="flex space-x-4 relative items-center">
                                            <!--status-->
                                            <div v-if="record.rma_status >= 0" class="text-xs">
                                                <span class="py-1 px-2 rounded line-clamp-1 cursor-pointer"
                                                    @click="startEdit(record)"
                                                    :class="options[record['rma_status']]['class']">
                                                    {{ options[record['rma_status']]['label'] }}
                                                </span>
                                            </div>
                                            <div class="flex justify-center">
                                                <!--More actions-->
                                                <div class="relative">
                                                    <button @click.stop="displayAction(index)"
                                                        class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                        <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                            size="lg" />
                                                    </button>
                                                </div>
                                                <div v-if="display_option === index" :ref="'dropdown' + index"
                                                    class="z-10 mt-8 absolute bg-slate-200 divide-y divide-gray-100 right-0 rounded-lg shadow-lg items-center"
                                                    style="display: flex; justify-content: center; align-items: center;">
                                                    <ul class="py-1 text-gray-700"
                                                        aria-labelledby="dropdownDefaultButton">

                                                        <li :class="{ 'hidden': record.status === '1' }">
                                                            <button v-if="!record.editing" @click="startEdit(record)"
                                                                class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-pencil"
                                                                    style="color: #3b82f6;" />
                                                                <span class="px-2">Edit</span>
                                                            </button>
                                                        </li>
                                                        <li v-if="index === 0"
                                                            :class="{ 'hidden': record.status === '1' }">
                                                            <button v-if="!record.editing && checkRoles(['admin'])"
                                                                @click="confirmDelete(index)"
                                                                class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                    style="color: #ef4444" />
                                                                <span class="px-2">Delete</span>
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="px-4 py-2">
                                        <!-- Customer Details (Can be your dynamic content) -->
                                        <div class="flex items-center -mt-4">
                                            <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                                :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-lime-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-blue-600': index === 0 }">
                                                {{ record.customer && record.customer.first_name ?
                                                    record.customer.first_name[0].toUpperCase() :
                                                    'C' }}
                                            </div>
                                            <div>
                                                <h4 class="leading-6 font-semibold text-gray-900 cursor-pointer"
                                                    @click="startEdit(record)">
                                                    {{ record.customer && record.customer.first_name ?
                                                        record.customer.first_name + ' ' + (record.customer.last_name ?
                                                            record.customer.last_name : '') : '' }}</h4>
                                                <p class="text-gray-500 cursor-pointer text-sm"
                                                    @click="dialPhoneNumber(record.customer.contact_number)">+91-{{
                                                        record.customer.contact_number }}</p>
                                            </div>
                                            <!-- <div>Customer: {{ record['customer_id'] }}</div> -->
                                        </div>
                                        <div class="flex justify-between items-center py-1 cursor-pointer"
                                            @click="startEdit(record)">
                                            <div>
                                                <p> <span class="font-semibold">Product:</span>
                                                    {{ record.product && record.product['product_name'] ?
                                                        record.product['product_name'] : '' }}
                                                </p>
                                            </div>
                                            <div>
                                                <p><span class="font-semibold">Serial no:</span> {{
                                                    record['serial_number'] }}</p>
                                            </div>
                                        </div>
                                    </div>
                                    <!--rma data-->
                                    <div class="flex justify-between items-center px-4 py-1 cursor-pointer"
                                        @click="startEdit(record)">
                                        <div>
                                            <p>#{{ record['rma_id'] }}</p>
                                        </div>
                                        <div>
                                            <p><span class="font-semibold"> {{ currentCompanyList &&
                                                currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }} {{ record['total_cost'] }}</span></p>
                                        </div>
                                        <!--date data-->
                                        <!-- <div>
                                        <p class="pr-1 text-gray-400">Payment Type: </p>
                                        <p>{{ record['payment_type'] }} </p>
                                    </div>
                                    <div>
                                        <p class="pr-1 text-gray-400">Amount:</p>
                                        <p class="text-green-700">
                                            {{currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency}} {{ record['payment_amount'] }}</p>
                                    </div> -->
                                    </div>
                                    <!--problem description-->

                                </div>
                            </div>
                            <!--no data found-->
                            <div v-if="pagination && this.pagination.last_page > 0 && this.pagination.last_page === this.pagination.current_page && isMobile && !open_skeleton_isMobile"
                                class="text-sm mb-[100px]">
                                <p class="font-bold text-center py-2 text-green-700">
                                    <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                    Finished !
                                </p>
                            </div>
                        </div>
                        <!--loader-->
                        <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                            :cols="items_category === 'tile' ? number_of_columns : 3"
                            :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                            :type="items_category === 'tile' ? 'table' : 'grid'">
                        </skeleton>
                        <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                            v-if="pagination && totalPages && !isMobile">
                            <p class="text-sm text-gray-700">
                                Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                                {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                                {{ pagination.total }} entries
                            </p>

                            <ul class="flex list-none">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span>
                                    </button>
                                </li>
                                <li v-for="pageNumber in visiblePageNumbers()" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                            pageNumber
                                        }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== pagination.last_page }">
                                    <button @click="updatePage(currentPage + 1)"
                                        :disabled="currentPage === pagination.last_page"
                                        class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
                <!--in case empty-->
                <div v-if="!open_skeleton && repairs && repairs.length === 0">
                    <div class="flex justify-center items-center">
                        <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                    </div>
                    <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
                </div>
                <!---in mobile view Filter Service-->
                <div v-if="isMobile" ref="dropdownContainerFilter">
                    <div class="fixed bottom-36 right-5 z-50 bg-green-700 rounded-full">
                        <div class="flex justify-end">
                            <button @click="toggleMainDropdown" type="button"
                                class="flex items-center justify-center px-[10px] py-2 text-white "
                                :class="{ 'cursor-not-allowed blur-sm': repairs.length == 0 }"
                                :disabled="repairs.length == 0">
                                <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                            </button>
                        </div>
                    </div>
                    <!-- Main Dropdown -->
                    <div v-if="isMainDropdownOpen" ref="mainDropdown"
                        class="fixed bottom-48 sm:bottom-48 left-full transform -translate-x-full w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border">
                        <div class="py-1">
                            <!-- Other Options -->
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('by Date')">By Date</button>
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('customer')">By Customer</button>
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('status')">By Status</button>
                        </div>
                    </div>
                </div>
                <!---in mobile view create new sale-->
                <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
                    <div class="flex justify-end">
                        <button @click="addRepair" type="button"
                            class="flex items-center justify-center px-[10px] py-2">
                            <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                        </button>
                    </div>
                </div>
            </div>
            <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
            <openRMARegister :showModal="openModalRegister" @close-modal="closeTheModal" :companyId="companyId"
                :isMobile="isMobile" :userId="userId"></openRMARegister>
            <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
            <!-- <bottombar v-if="isMobile" :selected_btn_btm="'rma'"></bottombar> -->
            <!--Filter-->
            <rmaFilter :showModal="rma_filter" @closeFilter="closeRmaFilter" :statusList="options"
                :selectedByValue="selectedByValue"></rmaFilter>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
    </div>
</template>

<script>
import openRMARegister from '../dialog_box/openRMARegister.vue';
// import bottombar from '../dashboard/bottombar.vue';
import { mapActions, mapGetters } from 'vuex';
import confirmbox from '../dialog_box/confirmbox.vue';
import customerDataTable from '../dialog_box/customerDataTable.vue';
import rmaFilter from '../dialog_box/filter_Modal/rmaFilter.vue';
import searchCustomer from '../customers/searchCustomer.vue';
import noAccessModel from '../dialog_box/noAccessModel.vue';

export default {
    emits: ['updateIsOpen'],
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options_page: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            columns: [],
            open_confirmBox: false,
            deleteIndex: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 5,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            now: null,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: null,
            open_skeleton_isMobile: false,
            filteredBy: {},
            isDropdownOpen: false,
            //-------------
            openModalRegister: false,
            isMobile: false,
            filter: 'all',
            search: '',
            quickFilter: '',
            formValues: {},
            repairs: [],
            pagination: {},
            companyId: '',
            userId: '',
            quick_filter: false,
            filter_value: 'all',
            filter_option: '',
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            options: [
                { value: 1, label: 'Awaiting Customer Confirmation', class: 'bg-green-500', labelClass: 'ml-2' },
                { value: 2, label: 'Awaiting Parts', class: 'bg-purple-500 text-white', labelClass: 'ml-2' },
                { value: 3, label: 'Awaiting Repair', class: 'bg-orange-500', labelClass: 'ml-2' },
                { value: 4, label: 'Awaiting Supplier', class: 'bg-[#ffb6c1]', labelClass: 'ml-2' },
                { value: 5, label: 'Awaiting to be sent to Supplier', class: 'bg-indigo-500 text-white', labelClass: 'ml-2' },
                { value: 6, label: 'Credit', class: 'bg-gray-300', labelClass: 'ml-2' },
                { value: 7, label: 'Ready to Deliver', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 8, label: 'Repair Completed', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 9, label: 'Repair in Process', class: 'bg-[#87b14b]', labelClass: 'ml-2' },
                { value: 10, label: 'Repaired/Replacement from Supplier', class: 'bg-[#ff7f50]', labelClass: 'ml-2' },
                { value: 11, label: 'Sent to Customer', class: 'bg-blue-600 text-white', labelClass: 'ml-2' },
                { value: 12, label: 'Sent to Supplier', class: 'bg-[#ffc0cb]', labelClass: 'ml-2' },
                { value: 13, label: 'Waiting New Battery', class: 'bg-red-600 text-white', labelClass: 'ml-2' },
                { value: 14, label: 'Delivered', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 15, label: 'Cancel', class: 'bg-red-600 text-white', labelClass: 'ml-2' },
            ],
            //---modal---mouse over---
            isModalVisible: false,
            modalData: {},
            modalPosition: { x: 0, y: 0 },
            actionPosition: { x: 0, y: 0 },
            isHoveringModal: false,
            hoverTimer: null,
            lastMousePosition: { x: 0, y: 0 },
            tolerance: 50, // Tolerance for mouse movement in pixels
            //---table data filter asending and decending----
            is_filter: false,
            //--filter---
            isMainDropdownOpen: false,
            rma_filter: false,
            selectedByValue: null,
            //--sort--
            resetSearch: false,
            //---no access---
            no_access: false,
        };
    },
    props: {
        updateModalOpen: Boolean
    },
    components: {
        openRMARegister,
        // bottombar,
        confirmbox,
        customerDataTable,
        rmaFilter,
        searchCustomer,
        noAccessModel
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('rmaList', ['currentRmasList']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        filteredRepairs() {
            let filtered = this.repairs;

            if (this.filter !== 'all') {
                filtered = filtered.filter(repair => repair.status === this.filter);
            }

            if (this.search) {
                filtered = filtered.filter(repair =>
                    Object.values(repair).some(value =>
                        String(value).toLowerCase().includes(this.search.toLowerCase())
                    )
                );
            }

            const start = (this.pagination.page - 1) * this.pagination.perPage;
            const end = start + this.pagination.perPage;
            this.pagination.start = start + 1;
            this.pagination.end = end;

            return filtered.slice(start, end);
        },
        paginatedData() {
            if (this.repairs && this.repairs.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.repairs
                this.length_category = filteredData.length;
                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.repairs && this.repairs.length !== 0) {
                const totalFilteredRecords = this.repairs.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const record = ['created_at', 'rma_id', 'rma_status', 'customer', 'product_id', 'serial_number', 'complete_date', 'problem_description', 'cost'];
            const fields = [];
            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.repairs) {
                for (const key of record) {
                    if (key !== 'id' && key !== 'items' && key !== 'data' && key !== 'company' && key !== 'assign_to' && key !== 'status') { // Exclude the 'id' field
                        const label = formatLabel(key);
                        fields.push({ label, field: key, visible: true });
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('rmaList', ['fetchRmasList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        updateIsMobile() {
            // Update isMobile based on the viewport width
            // this.$forceUpdate();
            this.isMobile = window.innerWidth < 1024;
        },
        statusClass(status) {
            switch (status) {
                case 'Repair Completed':
                    return 'bg-green-200 text-green-800 px-2 py-1 rounded';
                case 'Awaiting Customer Confirmation':
                    return 'bg-yellow-200 text-yellow-800 px-2 py-1 rounded';
                case 'Sent to Customer':
                    return 'bg-blue-200 text-blue-800 px-2 py-1 rounded';
                case 'Awaiting to be sent to Supplier':
                    return 'bg-purple-200 text-purple-800 px-2 py-1 rounded';
                case 'Awaiting Supplier':
                    return 'bg-pink-200 text-pink-800 px-2 py-1 rounded';
                case 'Awaiting Repair':
                    return 'bg-red-200 text-red-800 px-2 py-1 rounded';
                default:
                    return 'bg-gray-200 text-gray-800 px-2 py-1 rounded';
            }
        },
        addRepair() {
            // Add your logic to add a new repair
            // console.log('Add repair');
            this.openModalRegister = true;
        },
        closeTheModal(data, is_whatsApp) {
            if (is_whatsApp) {
                this.$router.push({ name: 'whatsappsetting' });
            }
            if (data && data.id) {
                this.message = 'RMA created successfully...!';
                this.show = true;
                this.repairs.unshift(data);
                this.openModalRegister = false;
            } else {
                this.openModalRegister = false;
            }
        },
        prevPage() {
            if (this.pagination.page > 1) {
                this.pagination.page--;
            }
        },
        nextPage() {
            const totalPages = Math.ceil(this.repairs.length / this.pagination.perPage);
            if (this.pagination.page < totalPages) {
                this.pagination.page++;
            }
        },
        //---quick filter data---
        quickFilterdata() {
            this.quick_filter = !this.quick_filter;
        },
        handleClickOutside(event) {
            if (this.$refs.dropdownContainer && !this.$refs.dropdownContainer.contains(event.target)) {
                this.quick_filter = false;
            }
        },
        selectedOptions(type) {
            if (type) {
                this.filter_value = type;
                this.quick_filter = false;
            }
        },
        navigateToEdit(repair) {
            if (repair.id) {
                let type = 'edit';
                this.$router.push({ path: `/openrma/${repair.id}/${type}` });
            }
        },
        validateDateTime(dateTimeString) {
            const datePart = dateTimeString.substring(0, 10);
            const isValidDate = /^\d{4}-\d{2}-\d{2}$/.test(datePart);
            return isValidDate;
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            axios.get('/rmas', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.repairs = [...this.repairs, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        visiblePageNumbers() {
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            if (this.display_option >= 0 && this.$refs['dropdown' + this.display_option][0]) {
                const dropdownRef = this.$refs['dropdown' + this.display_option][0];
                // console.log(dropdownRef, 'What happening......!!!', event.target);
                if (dropdownRef && !dropdownRef.contains(event.target)) {
                    this.display_option = null; // Close dropdown
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutside);
                }
            }
        },
        //---validate the role
        checkRoles(roles) {
            // console.log(this.currentLocalDataList, 'current local data list...');
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        confirmDelete(index) {
            if (this.getplanfeatures('rma')) {
                this.no_access = true;
            } else {
                // console.log(index, 'What happening...', this.repairs);
                this.deleteIndex = index;
                this.open_confirmBox = true;
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                axios.delete(`/rmas/${this.repairs[this.deleteIndex].id}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.repairs);
                        this.updateKeyWithTime('rma_update');
                        this.deleteIndex = null;
                        // this.open_message = true;
                        this.message = 'AMC deleted successfully...!';
                        this.show = true;

                        this.getRmaList(this.recordsPerPage, this.currentPage, true);
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        getRmaList(per_page, page, is_delete) {
            if (Object.keys(this.filteredBy).length === 0) {
                if (page == 1) {
                    this.fetchRmasList({ page, per_page: this.isMobile ? 20 : per_page, is_delete });
                    if (this.currentRmasList && this.currentRmasList.data) {
                        this.repairs = this.currentRmasList.data;
                        this.pagination = this.currentRmasList.pagination;
                    }
                } else {
                    this.open_skeleton = true;
                    axios.get('/rmas', { params: { company_id: this.companyId, per_page: per_page, page: page } })
                        .then(response => {
                            // console.log(response.data, 'estimation by list..!');
                            this.open_skeleton = false;
                            this.repairs = response.data.data;
                            this.pagination = response.data.pagination;
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_skeleton = false;
                        })
                }
            } else {
                this.getRMAReport();
            }
        },
        //--get formayt date
        formatDate(dateString) {
            // Create a new Date object using the components in the correct order (year, month - 1, day)
            const date = new Date(dateString);
            // Get the parts of the date (year, month, day) in the correct format
            const formattedYear = date.getFullYear();
            const formattedMonth = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-indexed
            const formattedDay = String(date.getDate()).padStart(2, '0');
            // Concatenate the parts with dashes to form the desired format
            return `${formattedMonth}/${formattedDay}/${formattedYear}`;
        },
        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClickdata);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClickdata);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClickdata(event) {
            try {
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },
        refreshPage() {
            this.$router.push({ name: 'open_rma_home' });
        },
        startEdit(record_data) {
            if (this.getplanfeatures('rma')) {
                this.no_access = true;
            } else {
                this.$router.push({ name: 'open_rma_edit', params: { rmaId: record_data.id, type: 'edit' } });
            }
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        //----table action----
        showModal(index, event) {
            if (index) {
                this.lastMousePosition = { x: event.layerX * 1, y: event.layerY * 1 };
                // Start a timer to check after 5000ms
                this.hoverTimer = setTimeout(() => {
                    if (this.isMouseInSamePosition(event)) {
                        this.modalData = index;
                        this.modalPosition = { x: ((event.layerX * 1) + 25), y: (event.layerY * 1) + 25 };
                        this.isModalVisible = true;
                    }
                }, 200);
            }
        },
        hideModal() {
            clearTimeout(this.hoverTimer); // Clear any existing timer
            setTimeout(() => {
                if (!this.isHoveringModal) {
                    this.isModalVisible = false;
                }
            }, 200);
        },
        toggleHoverModal(status) {
            this.isHoveringModal = status;
            if (!status) {
                this.hideModal();
            }
        },
        isMouseInSamePosition(event) {
            // Allow for a 10-pixel tolerance in mouse movement
            const xDiff = Math.abs(this.lastMousePosition.x - event.layerX);
            const yDiff = Math.abs(this.lastMousePosition.y - event.layerY);
            return xDiff <= this.tolerance && yDiff <= this.tolerance;
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'service' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.repairs = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.repairs, type: 'service', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'service' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.repairs = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.repairs, type: 'service', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        closeAllModals() {
            // Close all modals
            this.openModalRegister = false;
            this.open_confirmBox = false;
        },
        //--added filter----
        //--filter dropdown---
        toggleMainDropdown() {
            this.isMainDropdownOpen = !this.isMainDropdownOpen;
            if (this.isMainDropdownOpen) {
                document.addEventListener('click', this.handleClickOutsideFilter);
            } else {
                // this.showSubDropdown = false;
                document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        handleClickOutsideFilter(event) {
            const mainDropdown = this.$refs.dropdownContainerFilter;
            // console.log(event.target, 'Waht happning the data....', mainDropdown.contains(event.target));
            if (mainDropdown && !mainDropdown.contains(event.target)) {
                this.is_openSub = false;
                this.showSubDropdown = false;
                this.toggleMainDropdown();
                // document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        //---reset filter--
        resetTheFilter() {
            this.filteredBy = {};
            this.resetSearch = !this.resetSearch;
            this.getRmaList(this.recordsPerPage, 1);
        },
        //--filter option--
        changeFilter(opt) {
            this.filter_option = opt;
            this.toggleMainDropdown();
            if (opt === 'customer') {
                this.toggleFilterSelected('by Customer');
            }
            if (opt === 'employee') {
                this.toggleFilterSelected('by Employee');
            }
            if (opt === 'status') {
                this.toggleFilterSelected('by Status');
            }
            if (opt === 'by Date') {
                this.toggleFilterSelected('by Date');
            }
        },
        handleClickOutsideDate(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs.componentContainer;
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.filter_date = !this.filter_date;
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutsideDate);
            }
        },
        //---rma---
        toggleFilterSelected(option) {
            this.selectedByValue = option;
            // this.data = this.originalData;
            this.rma_filter = true;
            this.showFilterOptions = false;
            // console.log(this.rma_filter, 'EEERRRASR');
        },
        //--close Filter
        closeRmaFilter(searchData, page) {
            if (searchData) {
                const keysData = Object.keys(searchData);
                this.filteredBy = searchData;
                this.getRMAReport();
            }
            this.rma_filter = false;
        },
        //---filter data RMA report----
        getRMAReport() {
            if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
                this.open_loader = true;
                let send_data = {
                    from_date: this.filteredBy.from,
                    to_date: this.filteredBy.to,
                    type: 'rma', per_page: this.recordsPerPage, page: this.currentPage,
                    q: this.filteredBy.status === 'all' ? '' : this.filteredBy.status,
                    customer_id: this.filteredBy.customer_id ? this.filteredBy.customer_id : '',
                    company_id: this.companyId
                };
                axios.get('/rmas', { params: { ...send_data } })
                    .then(response => {
                        // console.log(response.data, 'Followup Data');
                        this.open_loader = false;
                        this.repairs = response.data.data;
                        this.pagination = response.data.pagination;
                        this.message = 'Filter data is updated...!';
                        this.type_toaster = 'success';
                        this.show = true;

                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                let searchDatacustomer = {}
                searchDatacustomer.customer_id = selectedData.id;
                searchDatacustomer.customer = selectedData.first_name + (selectedData.last_name ? ' ' + selectedData.last_name : '') + ' - ' + selectedData.contact_number;
                this.closeRmaFilter(searchDatacustomer);
            }
        },
        resetToSearch() {
            this.filteredBy = {};
            this.resetSearch = !this.resetSearch;
            this.resetTheFilter();
        },
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //--refresh page---
        refreshDataTable() {
            this.resetSearch = !this.resetSearch;
            this.filteredBy = {};
            this.fetchRmasList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
        },
        getUserColor(userId) {
            const colors = ['#3e14e3', '#2a9d8f', '#264653', '#8a4f7d', '#457b9d', '#e36ae9'];
            return colors[userId % colors.length];
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        const collectForm01 = localStorage.getItem('track_new');
        if (collectForm01) {
            let dataParse = JSON.parse(collectForm01);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        const view = localStorage.getItem('rma_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        this.updateIsMobile();
        this.fetchCompanyList();
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        if (this.currentRmasList && Object.keys(this.currentRmasList).length > 0) {
            this.repairs = this.currentRmasList.data;
            this.pagination = this.currentRmasList.pagination;
            this.fetchRmasList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
        } else {
            if (this.currentRmasList && Object.keys(this.currentRmasList).length == 0) {
                this.open_skeleton = true;
                this.fetchRmasList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
            }
        }
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        if (this.isMobile) {
            this.items_category = 'list';
        }
        //---sortIcons---
        const initialShortVisible = ['customer', 'created_date', 'complete_date', 'rma_status'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
        window.addEventListener('resize', this.updateIsMobile);
        // document.addEventListener('click', this.handleClickOutside);
    },
    beforeDestroy() {
        // Remove the resize event listener when the component is destroyed
        window.removeEventListener('resize', this.updateIsMobile);
        // document.removeEventListener('click', this.handleClickOutside);
    },
    watch: {
        currentRmasList: {
            deep: true,
            handler(newValue) {
                this.open_skeleton = false;
                if (newValue) {
                    this.repairs = newValue.data;
                    this.pagination = newValue.pagination;
                    this.recordsPerPage = newValue.pagination && newValue.pagination.per_page ? newValue.pagination.per_page : 10;
                }
                this.open_loader = false;
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    this.getRmaList(newValue, 1);
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                this.getRmaList(this.recordsPerPage, newValue);
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('rma_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.repairs = [...newValue];
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        openModalRegister: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    }
};
</script>
<style scoped>
.material-icons {
    font-size: 24px;
}

.manualStyle {
    overflow: auto;
    height: 100vh;
}

info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
    z-index: 100;
}
</style>