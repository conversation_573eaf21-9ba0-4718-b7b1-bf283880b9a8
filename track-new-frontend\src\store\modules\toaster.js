// store/modules/toaster.js
const state = {
    message: '',
    type: '' // e.g., 'success', 'error'
};

const mutations = {
    SET_TOASTER(state, { message, type }) {
        state.message = message;
        state.type = type;
    },
    RESET_STATE(state) {
        state.message = '';
        state.type = '';
      },
};

const actions = {
    triggerToaster({ commit }, payload) {
        commit('SET_TOASTER', payload);
    }
};

const getters = {
    toasterMessage: state => state.message,
    toasterType: state => state.type
};

export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
};
