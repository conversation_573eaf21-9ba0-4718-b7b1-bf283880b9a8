<template>
    <div>
        <div v-if="!open_skeleton && data.length > 0" class="m-2">
            <div
                :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                <!--Table view-->
                <div v-if="items_category !== 'list'" class="table-container overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="border-b border-gray-400">
                                <!--dynamic-->
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    :class="{ 'hidden': !column.visible }" class="py-1 px-2 text-left">
                                    <p>{{ column.label }}</p>
                                </th>
                                <th class="py-1 leading-none">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(record, index) in data" :key="index"
                                class="border-b border-gray-400 hover:bg-gray-200 cursor-pointer">
                                <td v-for="(column, colIndex) in columns" :key="colIndex" @click="viewRecord(record)"
                                    :class="{ 'hidden': !column.visible }" class="px-1 py-2 text-sm text-center">
                                    <span
                                        v-if="!Array.isArray(record[column.field]) && column.field !== 'created_at' && column.field !== 'amc_payment_type' && column.field !== 'amc_status' && column.field !== 'amc_date' && column.field !== 'customer' && column.field !== 'created_by' && column.field !== 'updated_by'">{{
                                            record[column.field] }}</span>
                                    <span v-if="column.field === 'customer'">
                                        {{ record[column.field].first_name + ' ' + record[column.field].last_name +
                                            ' - ' + record[column.field].contact_number }}
                                    </span>
                                    <span v-if="column.field === 'created_at'"
                                        :title="formattedDate(record[column.field])">
                                        {{ calculateDaysAgo(formattedDate(record[column.field])) }}
                                    </span>
                                    <span v-if="column.field === 'amc_payment_type'">
                                        {{ record[column.field] === 0 ? 'Paid' : record[column.field] === 1 ? 'Unpaid' :
                                            'Free' }}
                                    </span>
                                    <span v-if="column.field === 'amc_status'"
                                        class="text-white text-xs px-1 py-1 rounded"
                                        :class="{ 'bg-yellow-500': record[column.field] == '0', 'bg-[#0D7CBF]': record[column.field] == '1', 'bg-green-500': record[column.field] == '2', 'bg-[#DA1C1C]': record[column.field] == '3' }">
                                        {{ record[column.field] === 0 ? 'Open' : record[column.field] === 1 ? 'Progress'
                                            : record[column.field] === 2 ? 'Completed' : record[column.field] === 3 ?
                                                'Cancelled' : '' }}
                                    </span>
                                    <span v-if="Array.isArray(record[column.field]) && column.field !== 'amc_date'">{{
                                        column.field === 'assign_to' ?
                                            record[column.field].map(opt => opt.name).join(', ')
                                            : record[column.field]}}</span>
                                </td>
                                <td class="py-2 text-center">
                                    <div class="flex justify-center">
                                        <div class="flex relative">
                                            <button v-if="!record.editing" @click="viewRecord(record)" title="View"
                                                class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-solid fa-eye" style="color: #8b5cf6;" />
                                            </button>
                                            <button v-if="!record.editing"
                                                @click="startEdit(JSON.parse(JSON.stringify(record)))" title="Edit"
                                                class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                            </button>
                                            <button v-if="!record.editing && checkRoles(['admin'])" title="Delete"
                                                @click="confirmDelete(index)"
                                                class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                    style="color: #ef4444" />
                                            </button>
                                            <!-- <button @click.stop="displayAction(index)"
                                                class="hover:bg-gray-200 shadow-lg px-1 py-1  rounded">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                            </button> -->
                                        </div>
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-7 right-0 absolute bg-slate-100 divide-y divide-gray-100 rounded-lg shadow-lg items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                <li>
                                                    <button v-if="!record.editing" @click="viewRecord(record)"
                                                        class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-eye"
                                                            style="color: #8b5cf6;" size="lg" />
                                                        <span class="px-2">View</span>
                                                    </button>
                                                </li>
                                                <li>
                                                    <button v-if="!record.editing"
                                                        @click="startEdit(JSON.parse(JSON.stringify(record)))"
                                                        class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" size="lg" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li>
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" size="lg" />
                                                        <span class="px-2">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                <td>
                                    <button class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                        @click="openAmcModal">
                                        + AMC
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--card view-->
                <div v-if="items_category === 'list'" class="grid grid-cols-1 custom-scrollbar-hidden" :class="{
                    'sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4': showContactInfo == undefined,
                    'sm:grid-cols-1  lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-2 lg:gap-4': showContactInfo == false,
                    'sm:grid-cols-1  lg:grid-cols-1 xl:grid-cols-1 2xl:grid-cols-2 gap-2 lg:gap-4': showContactInfo
                }">
                    <div v-for="(record, index) in data" :key="index" class="w-full">
                        <div
                            class="max-w-md mx-auto bg-white rounded-xl shadow-lg overflow-hidden md:max-w-2xl border border-gray-300 shadow-lg">

                            <!--first row bg-gradient-to-b from-gray-200 to-gray-300-->
                            <div class="relative z-10 rounded-t-lg">
                                <div class="flex justify-between items-center py-1 px-2 ">
                                    <div class="flex items-center text-xs">
                                        <p class="flex items-center cursor-pointer text-red-500"
                                            :title="formattedDate(record['created_at'])">
                                            <!-- <font-awesome-icon icon="fa-regular fa-clock" class="pr-1" style="color: #9333ea;"
                                    size="lg" /> -->
                                            {{ calculateDaysAgo(formattedDate(record['created_at'])) }}
                                        </p>
                                    </div>
                                    <div class="flex justify-between items-center text-xs">
                                        <!--Status-->
                                        <div>
                                            <p @click="viewRecord(record)" v-if="record['amc_status'] >= 0"
                                                class="font-bold px-3 py-1 rounded rounded-full  cursor-pointer text-center text-white"
                                                :class="{ 'bg-yellow-100 text-yellow-800': record['amc_status'] == '0', 'bg-blue-400': record['amc_status'] == '1', 'bg-green-500': record['amc_status'] == '2', 'bg-[#DA1C1C]': record['amc_status'] == '3', 'bg-[#8D9689]': record['amc_status'] == '4' }">
                                                {{ record['amc_status'] == '0' ? 'Open' : record['amc_status'] == '1' ?
                                                    'Progress'
                                                    :
                                                    record['amc_status'] == '2' ? 'Completed' : record['amc_status'] == '3'
                                                        ?
                                                        'Cancelled' :
                                                        record['amc_status'] == '4' ? 'Hold' : '' }}</p>
                                        </div>
                                        <!--menu-->
                                        <div class="flex justify-end relative ml-2">
                                            <button @click.stop="displayAction(index)"
                                                class="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                            </button>
                                            <!--dropdown-->
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-7 absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="viewRecord(record)"
                                                            class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-eye"
                                                                style="color: #8b5cf6;" size="lg" />
                                                            <span class="px-2">View</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" size="lg" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!---second row-->
                                <!-- <div class="flex px-4 mt-2 items-center">
                                <div class="h-12 w-12  rounded-full flex items-center justify-center text-white font-bold mr-4"
                                    :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                    {{ record.customer && record.customer.first_name ?
                                        record.customer.first_name[0].toUpperCase() : 'C' }}
                                </div>
                                <div>
                                    <h4 class="text-sm leading-6 font-semibold mb-1">{{ record.customer &&
                                        record.customer.first_name ? (record.customer.first_name
                                            + ' ' + (record.customer.last_name ? record.customer.last_name : '')) : '' }}
                                    </h4>
                                    <p class="text-sm font-medium text-gray-600 cursor-pointer"
                                        @click="dialPhoneNumber(record['customer'].contact_number)">+91-{{
                                            record.customer.contact_number }}</p>
                                </div>
                            </div> -->
                                <div class="mt-2 bg-gray-100 py-1 mx-4 px-2 rounded-lg">
                                    <!---Title-->
                                    <p @click="viewRecord(record)" class="text-sm font-semibold cursor-pointer">
                                        {{ record['title'] ? record['title'] : '' }}
                                    </p>
                                    <!--no of services-->
                                    <div class="flex justify-between items-center text-sm sm:text-xs">
                                        <p class="text-gray-800">No. of Services: <span
                                                class="bg-red-200 text-red-800 font-semibold px-2 py-1 rounded-full">{{
                                                    record['number_of_service'] }}</span>
                                        </p>
                                        <!-- Payment Type -->
                                        <div class="py-1">
                                            <h4 class="font-medium text-gray-900 inline-block">Payment Type:</h4>
                                            <span class="inline-block px-2 py-1 ml-2 rounded-md bg-gray-200">{{
                                                record['amc_payment_type'] === 0 ? 'Paid' :
                                                    record['amc_payment_type'] === 1 ? 'Unpaid' :
                                                        record['amc_payment_type'] === 2 ? 'Free' : '' }}</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Assigned To -->
                                <div class="items-center px-4 mt-2">
                                    <!-- <h4 class="font-medium text-gray-900 mr-2">Assigned To:</h4> -->
                                    <div class="relative">
                                        <!-- First Circle -->
                                        <div class="absolute  left-1 z-10">
                                            <i class="fas fa-user-circle text-blue-300 text-lg"></i>
                                        </div>
                                        <!-- Second Circle -->
                                        <div class="absolute  left-4 z-20">
                                            <i class="fas fa-user-circle text-red-300 text-lg"></i>
                                        </div>
                                        <!-- Third Circle -->
                                        <div class="absolute  left-7 z-30">
                                            <i class="fas fa-user-circle text-purple-300 text-lg"></i>
                                        </div>
                                    </div>
                                    <div class="flex flex-wrap ml-[50px]">
                                        <p v-if="Array.isArray(record['assign_to']) && record['assign_to'].length > 0"
                                            v-for="(opt, i) in record['assign_to']" :key="i">
                                            <span v-if="record['assign_to'].length > 0"
                                                class="inline-flex items-center px-3 py-1 rounded text-sm font-medium mr-2 mb-2"
                                                :class="{ 'bg-blue-100 text-blue-800': i === 0 || i % 3 === 0, 'bg-blue-100 text-blue-800': i % 2 === 0, 'bg-green-100 text-green-800': (i === 1 || i % 1 === 0) && i % 2 !== 0 && i % 3 !== 0 }">
                                                {{ opt.name }}
                                            </span>
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--no data found-->
                    <div class="text-sm">
                        <p class="font-bold text-center py-2 text-green-700">
                            <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                            Finished !
                        </p>
                    </div>
                </div>
            </div>
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
            <Loader :showModal="open_loader"></Loader>
            <!-- Use the SalesModal component -->
            <addAmc :show-modal="showAmcModal" @closeAmcModal="closeAmcModal" :editData="editData"
                :companyId="companyId" :userId="userId">
            </addAmc>
        </div>
    </div>
</template>
<script>
import addAmc from '../../dialog_box/addAmc.vue';
import { mapState, mapActions, mapGetters } from 'vuex';
export default {
    props: {
        isMobile: Boolean,
        data: Object,
        companyId: String,
        userId: String,
        selected_category: String,
        items_category: String,
        open_skeleton: Boolean,
        now: Date,
        confirm_del: Boolean,
        customer_data: Object,
        updateModalOpen: Boolean,
        showContactInfo: Boolean
    },
    components: {
        addAmc
    },

    data() {
        return {
            display_option: false,
            deleteIndex: null,
            open_loader: false,
            editData: {},
            showAmcModal: false,
            empty_data: '/images/dashboard/empty.svg',
        }
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        dynamicFields() {
            const fields = [];
            let order = [];
            if (this.selected_category === 'services') {
                order = ['created_at', 'problem_title', 'category', 'expected_date', 'service_code', 'assign_to', 'service_type', 'invoice_id', 'status'];
            } else if (this.selected_category === 'leads') {
                order = ['lead_date', 'title', 'assign_to', 'assign_date', 'lead_type', 'follow_up', 'lead_status'];
            } else if (this.selected_category === 'amcs') {
                order = ['created_at', 'title', 'assign_to', 'amc_payment_type', 'amc_details', 'number_of_interval', 'number_of_service', 'amc_status'];
            } else if (this.selected_category === 'sales') {
                order = ['current_date', 'invoice_no', 'invoice_to', 'invoice_type', 'discount', 'due_amount', 'discount_type', 'shipping'];
            } else if (this.selected_category === 'estimations') {
                order = ['current_date', 'estimate_num', 'estimate_type', 'grand_total', 'invoice_id'];
            }

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data && this.data.length > 0) {
                order.forEach((key) => {
                    // Check if the key exists in the data object
                    if (this.data && this.data.length > 0) { //---&& this.data[0].hasOwnProperty(key)
                        const label = formatLabel(key);
                        if (key !== 'created_by' && key !== 'updated_by') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                });
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
    },
    mounted() {
        this.fetchLocalDataList();
    },
    methods: {
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //--format date    
        formatDate(dateString) {
            if (!dateString || typeof dateString !== 'string') {
                return '';
            }
            // Parse the input date string as a Date object
            const dateObject = new Date(dateString);

            if (isNaN(dateObject.getTime())) {
                return ''; // Return empty string if parsing fails
            }

            // Define an array of month names
            const monthNames = ["January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"];

            // Extract day, month, and year components from the date object
            const day = dateObject.getDate();
            const monthIndex = dateObject.getMonth(); // Months are zero-based (0 = January)
            const year = dateObject.getFullYear();

            // Format the date as "Month day, year"
            const formattedDateData = `${monthNames[monthIndex]} ${day}, ${year}`;

            return formattedDateData;
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },

        //----services data--- 
        //--image
        hasDocument(record) {
            return (
                record.document &&
                Array.isArray(JSON.parse(record.document)) &&
                JSON.parse(record.document).length > 0
            );
        },
        getLastDocumentUrl(record) {
            if (this.hasDocument(record)) {
                const documents = JSON.parse(record.document);
                return documents[documents.length - 1].url;
            }
            return null;
        },
        //---formated display date---
        formattedDate(timestamp) {
            //---formatted display date---
            const date = new Date(timestamp);
            date.setHours(date.getHours() + 5); // Add 5 hours
            date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---record---
        startEdit(record) {
            this.typeOfAmc = 'edit';
            let record_data_value = JSON.parse(JSON.stringify(record));
            if (record_data_value.date_description && typeof record_data_value.date_description === 'string') {
                try {
                    record_data_value.date_description = JSON.parse(record_data_value.date_description);
                } catch (error) {
                    record_data_value.date_description = [];
                }
            }
            if (record_data_value.product_lists && typeof record_data_value.product_lists === 'string') {
                try {
                    record_data_value.product_lists = JSON.parse(record_data_value.product_lists);
                } catch (error) {
                    record_data_value.product_lists = [];
                }
            }
            if (record_data_value.amc_date) {
                record_data_value.amc_date = record_data_value.amc_date.substring(0, 10);
            }
            if (record_data_value.customer) {
                record_data_value.customer = record_data_value.customer.first_name + ' ' + record_data_value.customer.last_name + ' - ' + record_data_value.customer.contact_number;
            }
            // console.log(record, 'RRSWRWRWRW');
            this.editData = record_data_value;
            if (this.customer_data) {
                this.editData.customer = this.customer_data.first_name + ' ' + (this.customer_data.last_name ? this.customer_data.last_name : '') + ' - ' + this.customer_data.contact_number;
                // {
                //     id: this.customer_data.id,
                //     first_name: this.customer_data.first_name,
                //     last_name: this.customer_data.last_name,
                //     contact_number: this.customer_data.contact_number,
                // };
            }
            this.showAmcModal = true;
        },
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.open_loader = true;
                axios.delete(`/amcs/${this.data[this.deleteIndex].id}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.data, 'What happening...!!!!');
                        this.open_loader = false;
                        this.deleteIndex = null;
                        window.location.reload();
                    })
                    .catch(error => {
                        this.open_loader = false;
                        console.error('Error', error);
                    })
            }

        },

        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.$emit('openconfirmbox', index);
        },
        cancelDelete() {
            this.deleteIndex = null;
        },
        getDateStatusClass(fieldValue) {
            try {
                const parsedValue = typeof fieldValue === 'string' ? JSON.parse(fieldValue) : fieldValue;
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    const followUpDate = new Date(parsedValue[0].date_and_time);
                    const currentDate = new Date();

                    if (followUpDate < currentDate) {
                        return 'text-red-500';
                    } else {
                        return 'text-green-500';
                    }
                }
            } catch (error) {
                console.error('Error parsing date:', error);
            }
            return ''; // Default empty string if date cannot be parsed
        },
        getFollowUpDate(fieldValue) {
            // console.log(fieldValue, 'EEEEEEEEEEEEEEEEEEEEEEE');
            if (fieldValue !== '' && typeof fieldValue === 'string') {
                const parsedValue = JSON.parse(fieldValue);
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    return this.generateDate(parsedValue[0].date_and_time);
                }
            } else if (typeof fieldValue === 'object') {
                if (Array.isArray(fieldValue) && fieldValue.length > 0 && fieldValue[0].date_and_time) {
                    return this.generateDate(fieldValue[0].date_and_time);
                }
            }
        },
        generateDate(data) {
            // console.log(data, 'What about the data......');
            const dateTimeString = data;
            const date = new Date(dateTimeString);
            const formattedDate = date.toLocaleDateString('en-GB', { year: 'numeric', month: '2-digit', day: '2-digit' });
            const formattedTime = date.toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' });
            return formattedDate + ' ' + formattedTime;
        },
        //---view Record--
        viewRecord(record) {
            if (record) {
                this.$router.push({
                    name: 'amcView',
                    query: {
                        recordId: record.id
                    }
                });
            }
        },
        //---closeAmcModal
        closeAmcModal(newData) {
            if (newData && this.typeOfAmc !== 'edit') {
                // console.log(this.data, 'EEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE')
                this.data.unshift(newData);
                // console.log(newData, 'What happening....!');
            }
            else if (newData && this.typeOfAmc === 'edit') {
                let findIndexData = this.data.findIndex((opt) => opt.id === this.editData.id);
                this.data.splice(findIndexData, 1, newData);
                // this.paginatedData;
            }

            this.showAmcModal = false;
        },
        //--role based on--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
    },
    watch: {
        confirm_del: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.deleteRecord();
                } else {
                    this.cancelDelete();
                }
            }
        }
    }
}
</script>
<style>
.image-container01 {
    width: 150px;
    height: 70px;
    /* Set your desired fixed height */
    object-fit: cover;
    /* Maintain aspect ratio and crop as needed */
    object-position: center;
    border: 1px solid rgb(218, 218, 218);
    /* box-shadow: 1px 1px 2px 2px rgb(82, 81, 81); */
}
</style>