// store/modules/dashboardData.js
import axios from "axios";

const state = {
  dashboard_data: [],
  };

  const mutations = {
      SET_DASHBOARDDATA(state, itemData) {
          state.dashboard_data = itemData;
      },
    RESET_STATE(state) {
      state.dashboard_data = [];      
    },
  };

  const actions = {
    updateDashboardName({ commit }, itemData) {
      // Simulate an asynchronous operation (e.g., API call) to update customer name
      setTimeout(() => {
        // Commit mutation to update customer name
        commit('SET_DASHBOARDDATA', itemData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchDashboardDataList({ commit }) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get('/dashboard-values', { params: { company_id: company_id} })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Dashboard list..!');
              let dashboardData_list = response.data;              
              
              commit('SET_DASHBOARDDATA', dashboardData_list);
              return dashboardData_list;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    
  };

  const getters = {
    currentDashboard(state) {
        return state.dashboard_data;
    },
    
  };

  export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};