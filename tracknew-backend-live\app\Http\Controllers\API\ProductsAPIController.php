<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateProductsAPIRequest;
use App\Http\Requests\API\UpdateProductsAPIRequest;
use App\Models\Products;
use App\Repositories\ProductsRepository;
use App\Repositories\ProductsBarcodeRepository;
use App\Repositories\ProductsDetailsRepository;
use App\Http\Resources\api\ProductResource;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class ProductsController
 * @package App\Http\Controllers\API
 */

class ProductsAPIController extends AppBaseController
{
    /** @var  ProductsRepository */
    private $productsRepository;
    private $productsBarcodeRepository; 
    private $productsDetailsRepository;

    public function __construct(ProductsRepository $productsRepo, ProductsBarcodeRepository $productsBarcodeRepo, ProductsDetailsRepository $productsDetailsRepo)
    {
        $this->productsRepository = $productsRepo;
        $this->productsBarcodeRepository = $productsBarcodeRepo;
        $this->productsDetailsRepository = $productsDetailsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/products",
     *      summary="getProductsList",
     *      tags={"Products"},
     *      description="Get all Products",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Products")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        // $products = $this->productsRepository->all(
        //     $request->except(['skip', 'limit']),
        //     $request->get('skip'),
        //     $request->get('limit')
        // );

        $products = $this->productsRepository->all(
            $request->except(['skip', 'limit'])
        );
        return $this->sendResponse(ProductResource::collection($products), 'Products retrieved successfully');

    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/products",
     *      summary="createProducts",
     *      tags={"Products"},
     *      description="Create Products",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Products")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Products"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateProductsAPIRequest $request)
    {   
        $input = $request->all();
        // $input = $request->validated();

        // Create product
        $products = $this->productsRepository->create($input);

        // Create barcode
        $barcodeData = [
            'barcode' => $input['barcode'],
            'product_id' => $products->id
        ];
        $barcodes = $this->productsBarcodeRepository->create($barcodeData);

        // Create product details
        $productData = [
            'total_qty' => $input['total_qty'],
            'product_id' => $products->id,
            'barcode_id' => $barcodes->id,
            'tax_id' => $input['tax_id'],
            'sales_price' => $input['sales_price']
        ];
        $productDetails = $this->productsDetailsRepository->create($productData);

        return $this->sendResponse($products->toArray(), 'Product saved successfully');
    }


    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/products/{id}",
     *      summary="getProductsItem",
     *      tags={"Products"},
     *      description="Get Products",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Products",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Products"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Products $products */
        $products = $this->productsRepository->find($id);

        if (empty($products)) {
            return $this->sendError('Products not found');
        }

        return $this->sendResponse($products->toArray(), 'Products retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/products/{id}",
     *      summary="updateProducts",
     *      tags={"Products"},
     *      description="Update Products",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Products",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Products")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Products"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateProductsAPIRequest $request)
    {
        $input = $request->all();

        /** @var Products $products */
        $products = $this->productsRepository->find($id);

        if (empty($products)) {
            return $this->sendError('Products not found');
        }

        $products = $this->productsRepository->update($input, $id);

        return $this->sendResponse($products->toArray(), 'Products updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/products/{id}",
     *      summary="deleteProducts",
     *      tags={"Products"},
     *      description="Delete Products",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Products",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Products $products */
        $products = $this->productsRepository->find($id);

        if (empty($products)) {
            return $this->sendError('Products not found');
        }

        $products->delete();

        return $this->sendSuccess('Products deleted successfully');
    }
}
