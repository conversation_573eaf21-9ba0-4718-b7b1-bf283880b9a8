<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mt-[60px]': isMobile, 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" @refresh-data="refreshData"></headbar> -->
            <div>
                <bannerDesign></bannerDesign>
            </div>
            <!-- services home -->
            <div class="p-1 relative m-1">
                <!-- <reportHome :isMobile="isMobile" :isAndroid="isAndroid"></reportHome> -->
                <reportManagement :isMobile="isMobile" :isAndroid="isAndroid" :refresh="refresh"></reportManagement>

            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"></sidebar>
        </div> -->
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/reports/headbar.vue';
// import reportHome from '../supporting/reports/reportHome.vue';
import reportManagement from '@/components/supporting/reports/reportManagement.vue';
import { useMeta } from '@/composables/useMeta';
import bannerDesign from '../supporting/banner/bannerDesign.vue';

export default {
    name: 'reports',
    components: {
        // sidebar,
        // headbar,
        reportManagement,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isAndroid: false,
            isSidebarOpen: false,
            route_item: 8,
            refresh: false,
        };
    },
    setup() {
        const pageTitle = `Reports`;
        const pageDescription = 'View detailed reports across all business areas. Simplify analysis and track performance metrics in one place';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    methods: {
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
            this.isAndroid = window.innerWidth < 768;
        },
        refreshData() {
            this.refresh = !this.refresh;
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>