<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateRmaItemsAPIRequest;
use App\Http\Requests\API\UpdateRmaItemsAPIRequest;
use App\Models\RmaItems;
use App\Repositories\RmaItemsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class RmaItemsController
 * @package App\Http\Controllers\API
 */

class RmaItemsAPIController extends AppBaseController
{
    /** @var  RmaItemsRepository */
    private $rmaItemsRepository;

    public function __construct(RmaItemsRepository $rmaItemsRepo)
    {
        $this->rmaItemsRepository = $rmaItemsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/rmaItems",
     *      summary="getRmaItemsList",
     *      tags={"RmaItems"},
     *      description="Get all RmaItems",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/RmaItems")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $rmaItems = $this->rmaItemsRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($rmaItems->toArray(), 'Rma Items retrieved successfully');
    }
  
  


    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/rmaItems",
     *      summary="createRmaItems",
     *      tags={"RmaItems"},
     *      description="Create RmaItems",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaItems"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateRmaItemsAPIRequest $request)
    {
        $input = $request->all();

        $rmaItems = $this->rmaItemsRepository->create($input);

        return $this->sendResponse($rmaItems->toArray(), 'Rma Items saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/rmaItems/{id}",
     *      summary="getRmaItemsItem",
     *      tags={"RmaItems"},
     *      description="Get RmaItems",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaItems",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaItems"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var RmaItems $rmaItems */
        $rmaItems = $this->rmaItemsRepository->find($id);

        if (empty($rmaItems)) {
            return $this->sendError('Rma Items not found');
        }

        return $this->sendResponse($rmaItems->toArray(), 'Rma Items retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/rmaItems/{id}",
     *      summary="updateRmaItems",
     *      tags={"RmaItems"},
     *      description="Update RmaItems",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaItems",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/RmaItems"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateRmaItemsAPIRequest $request)
    {
        $input = $request->all();

        /** @var RmaItems $rmaItems */
        $rmaItems = $this->rmaItemsRepository->find($id);

        if (empty($rmaItems)) {
            return $this->sendError('Rma Items not found');
        }

        $rmaItems = $this->rmaItemsRepository->update($input, $id);

        return $this->sendResponse($rmaItems->toArray(), 'RmaItems updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/rmaItems/{id}",
     *      summary="deleteRmaItems",
     *      tags={"RmaItems"},
     *      description="Delete RmaItems",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of RmaItems",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var RmaItems $rmaItems */
        $rmaItems = $this->rmaItemsRepository->find($id);

        if (empty($rmaItems)) {
            return $this->sendError('Rma Items not found');
        }

        $rmaItems->delete();

        return $this->sendSuccess('Rma Items deleted successfully');
    }
}
