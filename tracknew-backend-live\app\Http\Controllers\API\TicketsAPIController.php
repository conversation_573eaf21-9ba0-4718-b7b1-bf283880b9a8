<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateTicketsAPIRequest;
use App\Http\Requests\API\UpdateTicketsAPIRequest;
use App\Models\Tickets;
use App\Repositories\TicketsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class TicketsController
 * @package App\Http\Controllers\API
 */

class TicketsAPIController extends AppBaseController
{
    /** @var  TicketsRepository */
    private $ticketsRepository;

    public function __construct(TicketsRepository $ticketsRepo)
    {
        $this->ticketsRepository = $ticketsRepo;
    }

    /**
     * Display a listing of the Tickets.
     * GET|HEAD /tickets
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        $tickets = $this->ticketsRepository->all();

        // Ensure images remain an array for each ticket
        $tickets->each(function ($ticket) {
            $ticket->images = json_decode($ticket->images, true) ?? [];
        });

        return $this->sendResponse($tickets->toArray(), 'Tickets retrieved successfully');
    }


    /**
     * Store a newly created Tickets in storage.
     * POST /tickets
     *
     * @param CreateTicketsAPIRequest $request
     *
     * @return Response
     */
    public function store(CreateTicketsAPIRequest $request)
    {
        $ticket = $this->ticketsRepository->create([
            'ticket_id' => uniqid('TICKET-'),
            'company_id' => $request->company_id,
            'description' => $request->description,
            'images' => json_encode($request->images), // Store as JSON
            'status' => 'open',
        ]);
        $ticket->images = json_decode($ticket->images, true);
        return $this->sendResponse([
            'ticket' => $ticket
        ], 'Ticket saved successfully');
    }

    /**
     * Display the specified Tickets.
     * GET|HEAD /tickets/{id}
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        /** @var Tickets $tickets */
        $tickets = $this->ticketsRepository->find($id);

        if (empty($tickets)) {
            return $this->sendError('Ticket not found');
        }

        // Ensure images remain an array
        $tickets->images = json_decode($tickets->images, true) ?? [];

        return $this->sendResponse($tickets->toArray(), 'Ticket retrieved successfully');
    }



    /**
     * Update the specified Tickets in storage.
     * PUT/PATCH /tickets/{id}
     *
     * @param int $id
     * @param UpdateTicketsAPIRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateTicketsAPIRequest $request)
    {
        $input = $request->all();

        /** @var Tickets $tickets */
        $tickets = $this->ticketsRepository->find($id);

        if (empty($tickets)) {
            return $this->sendError('Tickets not found');
        }

        $tickets = $this->ticketsRepository->update($input, $id);

        return $this->sendResponse($tickets->toArray(), 'Tickets updated successfully');
    }

    /**
     * Remove the specified Tickets from storage.
     * DELETE /tickets/{id}
     *
     * @param int $id
     *
     * @throws \Exception
     *
     * @return Response
     */
    public function destroy($id)
    {
        /** @var Tickets $tickets */
        $tickets = $this->ticketsRepository->find($id);

        if (empty($tickets)) {
            return $this->sendError('Tickets not found');
        }

        $tickets->delete();

        return $this->sendSuccess('Tickets deleted successfully');
    }
}
