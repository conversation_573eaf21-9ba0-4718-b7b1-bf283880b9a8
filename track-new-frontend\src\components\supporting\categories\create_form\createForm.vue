<template>
    <div>
        <!--Stepper for form creation-->
        <div class="flex relative">
            <div v-for="(step, index) in steps" :key="index" class="flex items-center relative w-full"
                :class="{ 'ml-[30%]': index === 0, 'mb-5': isMobile }">
                <div>
                    <div :class="{
                        'bg-blue-500': index === currentStep,
                        'bg-green-500': index < currentStep,
                        'bg-gray-300': index > currentStep,
                    }"
                        class="w-8 h-8 rounded-full flex items-center justify-center text-white font-bold relative cursor-pointer">
                        <!-- {{ index + 1 }} -->
                        <span v-if="index === 0" class="font-bold text-lg">+</span><span class="font-bold text-lg"
                            v-if="index === 1">&#9873;</span>
                    </div>
                </div>
                <div v-if="index < steps.length - 1"
                    :class="{ 'connector': true, 'bg-blue-200': index === currentStep, 'bg-green-500': index < currentStep, 'bg-gray-300': index > currentStep }">
                </div>
            </div>
        </div>
        <div class="flex relative mb-5 mt-1" v-if="!isMobile">
            <div v-for="(step, index) in steps" :key="index" class="flex items-center relative w-full"
                :class="{ 'lg:ml-[28%]': index === 0, 'lg:ml-[2%]': index === 1, 'sm:ml-[25%]': index === 0, 'sm:ml-[5%]': index === 1, }">
                <div v-if="index === 0 ? category_name : step"
                    class="flex items-center justify-center text-center relative text-bold cursor-pointer" :class="{
                        'text-blue-500': index === currentStep,
                        'text-green-500': index < currentStep,
                        'text-gray-300': index > currentStep
                    }">
                    <div v-html="index === 0 ? category_name + '<br>' + step : step"></div>
                </div>
            </div>
        </div>

        <!--form create body-->
        <div class="lg:flex sm:flex flex-row w-full">
            <!-- First child with minimum width on desktop and tablet views -->
            <!-- <div v-if="currentStep === 0" class="lg:w-1/4 sm:w-1/3 w-full p-1 bg-gray-300"> -->
            <!--tooltip-->
            <!-- <div v-if="isfocused.head" class="absolute flex flex-col items-center group-hover:flex -mt-10">
                    <span
                        class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                        <p>select the below option to<span
                                class="inline-block transform text-xl rotate-90 ">&#9754;</span>add custom fields</p>
                    </span>
                    <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                </div> -->
            <!-- Second child with remaining width on desktop and tablet views, full width on mobile view -->
            <!-- <p ref="customFieldsHeader" class="text-center underline py-2" tabindex="0"
                    @focus="isfocused.head = true">Add Custom Fields</p>
                <div
                    class="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-2 justify-center">
                    <div v-for="(field, index) in fields_option" :key="index"
                        class="p-4 bg-white rounded shadow-md cursor-pointer hover:bg-gray-100"
                        @click="openModal(field)">
                        <span v-html="fields_create[index]" class="block text-center"></span>
                        <span class="block text-center lg:text-sm sm:text-xs text-sm font-semibold pt-2">{{ field
                            }}</span>
                    </div>
                </div> -->
            <!-- </div> -->
            <!-- Second child with full width on desktop, tablet, and mobile views -->
            <div class="w-full mb-10">
                <p class="text-center underline font-bold text-lg mb-2">
                    {{ currentStep === 0 ? 'Create Form' : 'Preview Form' }}
                </p>
                <!--Style div-->
                <!-- <div class="flex justify-end bg-gray-200 p-2 mt-2 mb-2" v-if="currentStep === 1">
                    <select v-model="style_view" id="itemsCategory" class="text-green-600 mr-5 p-2">
                        <option value="1">&#9776; One field per line</option>
                        <option value="2">&#9783; Two field per line</option>
                        <option value="3">&#9476; Three field per line</option>
                    </select>
                </div> -->
                <!-- <div class="flex mt-2 mb-2 mr-5 justify-end items-center">
                    <div class="hover:bg-gray-300 rounded" title="per row 1 field">
                        <img :src="tile1_icon" alt="Tile one" class="w-8 h-8">
                    </div>
                    <div class="ml-1 mr-2 hover:bg-gray-300 rounded" title="per row 2 field">
                        <img :src="tile2_icon" alt="Tile two" class="w-8 h-8">
                    </div>
                    <div class="hover:bg-gray-300 rounded p-1" title="per row 3 field">
                        <img :src="tile3_icon" alt="Tile three" class="w-6 h-6">
                    </div>
                </div> -->
                <!-- <div class="w-full flex flex-col items-center mb-12"> -->
                <div :class="{
                    'hidden': currentStep !== 0,
                    'w-full flex flex-col items-center mb-12': currentStep === 0,
                }">
                    <div v-for="(fields, index) in dynamicForm" :key="index"
                        class="grid grid-cols-12 gap-1 pl-2 pr-2 sm:pl-2 sm:pr-2 flex justify-around items-center p-1"
                        :class="{ 'border overflow-auto': currentStep !== 0 }">
                        <!--Display image-->
                        <!-- <div v-if="currentStep !== 0 && fields.enable">
                            <img :src="fields.image" class="w-[40px] h-[40px] mr-3" :alt="index">
                        </div> -->
                        <!--Arrow position-->
                        <div v-if="currentStep === 0 && fields.place !== 'div2'"></div>
                        <div v-if="currentStep === 0 && fields.place === 'div2'" class="flex">
                            <div class="mr-1 rounded hover:bg-blue-400 cursor-pointer pl-1 pr-1"
                                :class="{ 'hidden': index === 8 }" @click="moveField(index, -1)">
                                <span class="text-blue-700">&#8593;</span>
                            </div>
                            <div class="ml-1 rounded hover:bg-blue-400 cursor-pointer pl-1 pr-1"
                                :class="{ 'hidden': index === dynamicForm.length - (filteredLength + 1) }"
                                @click="moveField(index, 1)">
                                <span class="text-blue-700">&#8595;</span>
                            </div>
                        </div>
                        <!--Form-->
                        <div v-if="(currentStep === 0) || (currentStep === 1 && fields.enable)" class="mb-1 col-span-9">
                            <!---div 1 fileds-->
                            <div v-if="fields.place === 'div1'">
                                <label class="block text-sm font-semibold pb-2"
                                    :class="{ 'filter blur-sm': !fields.enable }">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'" class="text-red-500">&#9913;</span></label>
                                <!--customer-->
                                <!-- Show input field for number or character -->
                                <div v-if="fields.editData === 'Dropdown'" class="relative">
                                    <div class="relative">
                                        <input
                                            v-if="fields.fieldKey === 'customer' && formValues[fields.fieldKey] !== null"
                                            placeholder="select customer" v-model="formValues[fields.fieldKey]"
                                            @input="handleDropdownInput(fields)" @focus="isDropdownOpen = true" readonly
                                            class="w-full border py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 rounded" />

                                        <!-- Right-aligned icon based on isDropdownOpen -->
                                        <span v-if="fields.fieldKey === 'customer'"
                                            class="absolute right-2 top-2 cursor-pointer"
                                            @click="isDropdownOpen = !isDropdownOpen">
                                            <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                                        </span>
                                    </div>
                                    <!-- Display filtered options as the user types -->
                                    <div v-if="fields.fieldKey === 'customer' && isDropdownOpen && filteredCustomerOptions.length > 0"
                                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                        style="top: 100%; left: 0; z-index: 100;">
                                        <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                                            @click="selectDropdownOption(fields, option)"
                                            :class="{ 'bg-gray-200': formValues[fields.fieldKey] === option.fistName }"
                                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                                            {{ option.first_name + ' ' + option.last_name + ' - ' +
                                                option.contact_number
                                            }}</p>
                                    </div>
                                </div>
                                <!--Service type-->
                                <!--Radio button-->
                                <div v-if="fields.type === 'radio' && fields.fieldKey == 'service_type'"
                                    :class="{ 'filter blur-sm': !fields.enable }">
                                    <p class="text-gray-500 flex text-xs items-center font-bold pb-2">
                                        Note: <span class="material-icons text-xs text-green-700 text-center">
                                            check_box
                                        </span>
                                        is Enable/Disable to form
                                    </p>
                                    <div class="mb-2 grid grid-cols-2 gap-2 sm:grid-cols-4">
                                        <div v-for="(option, index) in fields.option" :key="index"
                                            class="flex items-center">
                                            <input type="checkbox" :key="index" :id="'checkbox_' + index"
                                                v-model="fields.option_status[index]" class="mr-2 justify-end" />
                                            <input type="radio" :id="'option' + index" :value="option" class="hidden"
                                                v-model="formValues[fields.fieldKey]" readonly />
                                            <label class="pl-1 text-sm" :for="'option' + index"
                                                :class="{ 'selected': formValues[fields.fieldKey] === option }">{{
                                                    option
                                                }}</label>
                                        </div>
                                    </div>
                                    <!-- Address field -->
                                    <div v-if="(formValues.service_type === 'Pick up' || formValues.service_type === 'On site' || formValues.service_type === 'Remote')"
                                        class="mb-1 w-full">
                                        <label class="block text-sm font-semibold pb-2">Pickup / On-site
                                            address:</label>
                                        <textarea v-model="formValues[fields.fieldKey]['pickup_address']"
                                            placeholder="Pick up/On site address" name="address"
                                            class="w-full p-2 border rounded"></textarea>
                                    </div>
                                </div>
                                <!--Radio button-->
                                <div v-if="fields.type === 'radio' && fields.fieldKey !== 'service_type'"
                                    class="mb-2 grid grid-cols-2 gap-2 sm:grid-cols-3"
                                    :class="{ 'filter blur-sm': !fields.enable }">
                                    <div v-for="(option, index) in fields.option" :key="index"
                                        class="flex items-center">
                                        <input type="radio" :id="'option' + index" :value="option"
                                            v-model="formValues[fields.fieldKey]" />
                                        <label class="pl-1 text-sm" :for="'option' + index"
                                            :class="{ 'selected': formValues[fields.fieldKey] === option }">{{
                                                option
                                            }}</label>
                                    </div>
                                </div>
                            </div>
                            <!---div 01 brand, model, etc fileds-->
                            <div v-if="fields.place === 'div01'" :class="{ 'filter blur-sm': !fields.enable }">
                                <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'"
                                        class="text-red-500 font-bold">&#9913;</span></label>
                                <!--serial number-->
                                <input v-model="formValues[fields.fieldKey]" :type="fields.type"
                                    v-if="fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file'"
                                    :placeholder="fields.placeholderText" :name="fields.fieldKey" readonly
                                    @click="dropdownOpenData = null" class="w-full p-2 border rounded" />
                                <!-- Dropdown brand, device model and defect title -->
                                <div v-if="fields.editData === 'Dropdown'" class="relative">
                                    <!--tooltip-->
                                    <div v-if="isfocused[fields.fieldKey]"
                                        class="absolute flex flex-col items-center group-hover:flex -mt-12">
                                        <span
                                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                            <p>Click on the Edit <font-awesome-icon icon="fa-solid fa-pencil"
                                                    :size="isMobile ? 'sm' : 'lg'" /> icon to
                                                change and add new required options.</p>
                                        </span>
                                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                    </div>
                                    <div class="relative">
                                        <input v-if="formValues[fields.fieldKey] !== null" placeholder="select option"
                                            v-model="formValues[fields.fieldKey]" @input="handleDisplay(fields)"
                                            @focus="dropdownOpenData = fields.fieldKey, isfocused[fields.fieldKey] = true"
                                            readonly @blur="isfocused[fields.fieldKey] = false"
                                            class="w-full border py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0 rounded" />
                                    </div>
                                    <!-- Display filtered options as the user types fields.fieldKey-->
                                    <div v-if="dropdownOpenData === false"
                                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                        style="top: 100%; left: 0; z-index: 100;">
                                        <p v-if="filteredDataOptions.length > 0"
                                            v-for="(option, index) in filteredDataOptions" :key="index"
                                            @click="selectOptionData(fields, option)"
                                            :class="{ 'bg-gray-200': formValues[fields.fieldKey] === option }"
                                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">{{ option }}</p>
                                        <!-- <p class="text-center text-green-700 py-2 cursor-pointer hover:bg-gray-200"
                                            @click="AddnewOption(fields)"> + Add New</p> -->
                                    </div>
                                </div>
                                <!--Notes section-->
                                <div v-if="fields.editData === 'Notes'">
                                    <textarea v-model="formValues[fields.fieldKey]"
                                        :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                        class="w-full p-2 border rounded" :cols="fields.colsCount" readonly
                                        @click="dropdownOpenData = null" :rows="fields.rowsCount">
                                </textarea>
                                </div>
                            </div>
                            <!---div 02 custom fileds-->
                            <div v-if="fields.place === 'div2'"
                                :class="{ 'border-t border-dashed border-black py-5': index === 8 }">
                                <label class="block text-sm font-semibold pb-2"
                                    :class="{ 'filter blur-sm': !fields.enable }">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'"
                                        class="text-red-500 font-bold">&#9913;</span></label>
                                <input v-model="formValues[fields.fieldKey]" :type="fields.type"
                                    v-if="fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file'"
                                    :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                    class="w-full p-2 border rounded" :class="{ 'filter blur-sm': !fields.enable }" />
                                <!--Radio button-->
                                <div v-if="fields.type === 'radio'" class="mb-2 grid grid-cols-2 gap-2 sm:grid-cols-3"
                                    :class="{ 'filter blur-sm': !fields.enable }">
                                    <div v-for="(option, index) in fields.option" :key="index"
                                        class="flex items-center">
                                        <input type="radio" :id="'option' + index" :value="option"
                                            v-model="formValues[fields.fieldKey]" />
                                        <label class="pl-1 text-sm" :for="'option' + index"
                                            :class="{ 'selected': formValues[fields.fieldKey] === option }">{{
                                                option
                                            }}</label>
                                    </div>
                                </div>
                                <!--Check Box-->
                                <div v-if="fields.type === 'checkbox'" :class="{ 'filter blur-sm': !fields.enable }"
                                    class="mb-2 grid grid-cols-2 gap-0 sm:grid-cols-3">
                                    <div v-for="(option, index) in fields.option" :key="index"
                                        class="flex items-center">
                                        <input type="checkbox" :id="option" :value="option"
                                            :checked="formValues[fields.fieldKey] && formValues[fields.fieldKey].includes(option)"
                                            @change="updateCheckbox(fields.fieldKey, option)" />
                                        <label class="pl-2 text-sm" :for="option">{{ option }}</label>
                                    </div>
                                </div>
                                <!--Notes section-->
                                <div v-if="fields.editData === 'Notes'" :class="{ 'filter blur-sm': !fields.enable }">
                                    <textarea v-model="formValues[fields.fieldKey]"
                                        :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                        class="w-full p-2 border rounded" :cols="fields.colsCount"
                                        :rows="fields.rowsCount">
                                </textarea>
                                </div>
                                <!--Drop downs-->
                                <!--single-->
                                <select
                                    v-if="fields.editData === 'Dropdown' && fields.type === 'single' && fields.fieldKey !== 'customer'"
                                    v-model="formValues[fields.fieldKey]" id="dropdown"
                                    class="w-full p-2 border rounded" :class="{ 'filter blur-sm': !fields.enable }">
                                    <option v-if="formValues[fields.fieldKey] === undefined" value="" selected
                                        class="text-gray-400">select</option>
                                    <option v-for="(option, index) in fields.option" :key="index" :value="option">
                                        {{ option }}
                                    </option>
                                </select>
                                <!--multiple-->
                                <div class="relative" :class="{ 'filter blur-sm': !fields.enable }"
                                    v-if="fields.editData === 'Dropdown' && fields.type === 'multiple' && fields.fieldKey !== 'customer'">
                                    <div class="border py-2 px-2 flex flex-wrap"
                                        :class="{ 'border-blue-300': showOptions === true }">
                                        <div v-for="(selectedOption, index) in formValues[fields.fieldKey]" :key="index"
                                            class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                            {{ selectedOption }}
                                            <span @click="removeOption(fields, selectedOption)"
                                                class="text-red-500 font-semibold cursor-pointer">x</span>
                                        </div>
                                        <input type="text" v-model="search" @input="filterOptions"
                                            @focus="showOptions = true" @blur="hideOptions"
                                            class="h-[35px] mt-1 outline-none border rounded-lg px-2">
                                    </div>
                                    <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                                        v-show="showOptions" :class="{ 'h-auto': showOptions }">
                                        <div v-for="(option, index) in filteredOptions(fields)" :key="index"
                                            class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                            @click="selectOptionMultiple(fields, option)">
                                            {{ option }}
                                        </div>
                                        <!-- <button v-if="showAddNew"
                                            class="bg-green-700 text-white hover:bg-green-600 px-3 py-1 rounded-lg"
                                            @click="addNewOption(fields)">Add New</button> -->
                                    </div>
                                </div>
                                <!--multiple file upload-->
                                <div v-if="fields.editData === 'Upload' && fields.type === 'multiple'"
                                    class="rounded flex justify-center items-center" @click="openFileInput"
                                    :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 border-2 py-2 px-10 border-dashed cursor-pointer border-blue-500': formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0 }">
                                    <label for="fileInput" class="cursor-pointer text-blue-400"
                                        :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 border-2 py-2 px-7 border-dashed cursor-pointer border-blue-500 -ml-6 sm:-ml-10': !(formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0) }">
                                        <span class="text-center pr-2">+</span>
                                        <span class="text-center">Upload File</span>
                                    </label>
                                    <input type="file" id="fileInput" style="display: none;" accept="image/*" />
                                </div>
                            </div>

                            <!---div 03 fileds-->
                            <div v-if="fields.place === 'div3'"
                                :class="{ 'border-t border-dashed border-black py-5': index === (getDataofDiv + 8) && getDataofDiv > 0 }">
                                <!---Add Custom fields-->
                                <div v-if="display_custom_button === false && index === (getDataofDiv + 8)"
                                    class="flex justify-center items-center text-blue-700 ">
                                    <button ref="customFieldsButton" @click="handleFocusCustom()"
                                        class="border border-blue-700 shodow-lg px-2 py-2 rounded mb-2 hover:bg-blue-700 hover:text-white">Add
                                        Custom fields</button>
                                </div>

                                <label class="block text-sm font-semibold"
                                    :class="{ 'filter blur-sm': !fields.enable }">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'"
                                        class="text-red-500 font-bold">&#9913;</span></label>
                                <p v-if="index === (getDataofDiv + 8)" class="text-xs font-bold text-gray-600 pb-2"
                                    :class="{ 'filter blur-sm': !fields.enable }">
                                    Note: Please click on the edit icon <font-awesome-icon icon="fa-solid fa-pencil"
                                        :size="isMobile ? 'sm' : 'lg'" />
                                    to
                                    change the required option and add a new
                                    option.</p>
                                <div v-if="fields.editData === 'Dropdown' && fields.type === 'single' && fields.fieldKey === 'pre_repair'"
                                    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
                                    :class="{ 'filter blur-sm': !fields.enable }">
                                    <div v-for="(option, index) in fields.option" :key="index" :value="option">
                                        <p class="mb-1 text-sm font-semibold text-gray-600">{{ option }}:</p>
                                        <!--Radio button-->
                                        <fieldset>
                                            <div
                                                class="mb-2 mr-2 text-center text-sm grid grid-cols-3 switch-toggle switch-candy">
                                                <div v-for="opt in fields.preChecklist" :key="opt.value"
                                                    @click="selectOption(option, opt.value, fields.fieldKey, fields.preChecklist), isfocused[fields.fieldKey] = !isfocused[fields.fieldKey]"
                                                    :class="{ 'selected-option': formValues[fields.fieldKey] && formValues[fields.fieldKey][option] === opt.value, 'text-green-500 font-bold bg-gray-700': opt.value === 'yes', 'text-red-500 font-bold bg-gray-700': opt.value === 'no', 'text-gray-200 bg-gray-700': opt.value === 'not_applicable' }"
                                                    class="custom-radio-box">
                                                    {{ opt.label }}
                                                </div>
                                                <a class="btn btn-flat btn-success"></a>
                                            </div>
                                        </fieldset>
                                    </div>
                                </div>
                                <!---device password--->
                                <div v-if="fields.fieldKey === 'device_password'"
                                    :class="{ 'filter blur-sm': !fields.enable }">
                                    <input v-model="formValues[fields.fieldKey]" :type="fields.type"
                                        v-if="fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file'"
                                        :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                        class="w-full p-2 border rounded" />
                                </div>
                                <!--device pattern-->
                                <div v-if="fields.fieldKey === 'device_pattern'"
                                    :class="{ 'filter blur-sm': !fields.enable }">
                                    <div class="border border-gray-300 rounded cursor-pointer p-2  mx-2"
                                        @click="openMobilePattern(formValues[fields.fieldKey])">
                                        <font-awesome-icon icon="fa-solid fa-mobile-screen" size="xl" />
                                    </div>
                                </div>
                            </div>
                            <!---div 04 amount-->
                            <div v-if="fields.place === 'div4'" :class="{ 'filter blur-sm': !fields.enable }">
                                <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'" class="text-red-500 font-bold">&#9913;</span>
                                    <span v-if="fields.fieldKey === 'discountValue'"
                                        class="font-normal text-xs text-white bg-gradient-to-r from-blue-500 to-green-500 px-2 rounded-full shadow-md transform transition-transform duration-200 ease-in-out hover:scale-110 mx-2">
                                        New
                                    </span></label>
                                <input v-model="formValues[fields.fieldKey]" :type="fields.type"
                                    v-if="fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file'"
                                    :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                    class="w-full p-2 border rounded" />
                            </div>
                            <!---div 05 fileds-->
                            <div v-if="fields.place === 'div5'" :class="{ 'filter blur-sm': !fields.enable }">
                                <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'"
                                        class="text-red-500 font-bold">&#9913;</span></label>
                                <input v-model="formValues[fields.fieldKey]" :type="fields.type"
                                    v-if="fields.fieldKey !== 'document' && (fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file')"
                                    :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                    class="w-full p-2 border rounded" />
                                <!---Upload image in table-->
                                <div v-if="fields.fieldKey === 'document'" class="text-sm">
                                    <!-- <table>
                                        <thead>
                                            <tr class="text-center">
                                                <th class="border py-2 px-3">Sr.No</th>
                                                <th class="border">Name</th>
                                                <th class="border">Image</th>
                                                <th class="border">Action</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr v-for="(item, index) in formValues[fields.fieldKey]" :key="index"
                                                class="justify-center items-center py-2">
                                                <td class="text-center p-1 border">{{ index + 1 }}</td>
                                                <td class="text-center p-1 border"> <input v-model="item.name"
                                                        type="text" class="text-center py-1" />
                                                </td>
                                                <td class="justify-center p-1 border">
                                                    <img :src="item.image" alt="Uploaded Image" v-if="item.image" />
                                                   
                                                    <input type="file" @change="handleImageUpload($event, index)" />
                                                </td>
                                                <td class="text-center border px-2">
                                                    <button @click="viewImage(index)"
                                                        class="ml-2 text-[25px] text-green-700">&#128065;</button>
                                                  
                                                    <button @click="deleteRow(index)"
                                                        class="ml-2 text-[30px] text-red-700 text-center">-</button>
                                                </td>
                                            </tr>
                                            <tr class="justify-center items-center">
                                                <td colspan="4" class="border text-center py-2">
                                                    <button @click="addRow(fields)"
                                                        class="bg-green-700 px-5 py-1 text-sm rounded text-white">Add</button>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table> -->
                                    <!-- Container for adding new images  @change="handleImageChange($event, Array.isArray(formValues[fields.fieldKey]) ? formValues[fields.fieldKey].length : 0, fields)" -->
                                    <div class="rounded flex justify-center items-center" @click="openFileInput"
                                        :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 border-2 py-2 px-10 border-dashed cursor-pointer border-blue-500': formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0 }">
                                        <label for="fileInput" class="cursor-pointer text-blue-400"
                                            :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 border-2 py-2 px-7 border-dashed cursor-pointer border-blue-500 -ml-6 sm:-ml-10': !(formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0) }">
                                            <span class="text-center pr-2">+</span>
                                            <span class="text-center">Upload Image</span>
                                        </label>
                                        <input type="file" id="fileInput" style="display: none;" accept="image/*" />
                                    </div>

                                </div>

                                <!--Radio button-->
                                <div v-if="fields.type === 'radio'" class="mb-2 grid grid-cols-2 gap-2 sm:grid-cols-3">
                                    <div v-for="(option, index) in fields.option" :key="index"
                                        class="flex items-center">
                                        <input type="radio" :id="'option' + index" :value="option"
                                            v-model="formValues[fields.fieldKey]" />
                                        <label class="pl-1 text-sm" :for="'option' + index"
                                            :class="{ 'selected': formValues[fields.fieldKey] === option }">{{
                                                option
                                            }}</label>
                                    </div>
                                </div>
                                <!--Check Box-->
                                <div v-if="fields.type === 'checkbox'"
                                    class="mb-2 grid grid-cols-2 gap-0 sm:grid-cols-3">
                                    <div v-for="(option, index) in fields.option" :key="index"
                                        class="flex items-center">
                                        <input type="checkbox" :id="option" :value="option"
                                            :checked="formValues[fields.fieldKey] && formValues[fields.fieldKey].includes(option)"
                                            @change="updateCheckbox(fields.fieldKey, option)" />
                                        <label class="pl-2 text-sm" :for="option">{{ option }}</label>
                                    </div>
                                </div>
                                <!--Notes section-->
                                <div v-if="fields.editData === 'Notes'">
                                    <textarea v-model="formValues[fields.fieldKey]"
                                        :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                        class="w-full p-2 border rounded" :cols="fields.colsCount"
                                        :rows="fields.rowsCount">
                                </textarea>
                                </div>
                                <!--Drop downs-->

                                <!--single-->
                                <select
                                    v-if="fields.editData === 'Dropdown' && fields.type === 'single' && fields.fieldKey !== 'customer'"
                                    v-model="formValues[fields.fieldKey]" id="dropdown"
                                    class="w-full p-2 border rounded">
                                    <option v-if="formValues[fields.fieldKey] === undefined" value="" selected
                                        class="text-gray-400">select</option>
                                    <option v-for="(option, index) in fields.option" :key="index" :value="option">
                                        {{ option }}
                                    </option>
                                </select>
                                <!--multiple-->
                                <div class="relative"
                                    v-if="fields.editData === 'Dropdown' && fields.type === 'multiple' && fields.fieldKey !== 'customer'">
                                    <!-- Input field -->
                                    <!-- <input
                                        v-if="fields.editData === 'Dropdown' && fields.type === 'multiple' && fields.fieldKey !== 'customer'"
                                        v-model="formValues[fields.fieldKey]" @click="toggleMultipleDropdown(index)"
                                        @input="filterTheData(fields, fields.option, formValues[fields.fieldKey])"
                                        ref="dropdownContainer" class="w-full p-2 border rounded"
                                        placeholder="Select options" /> -->

                                    <!-- Dropdown options -->
                                    <!-- <div v-if="fields.multipleDropdown === true" ref="dropdownOptions"
                                        class="absolute z-50 mt-2 w-full p-2 border rounded bg-white flex flex-wrap gap-2">-->
                                    <!-- Display options with checkboxes for multiple selection -->
                                    <!-- <div v-for="(option, index) in filterOptions(fields.option, formValues[fields.fieldKey])"
                                            :key="index"
                                            class="flex justify-center overflow-y-auto border rounded text-sm bg-gray-400"
                                            :class="{ 'hidden': !getOptionRequest(fields, option) }">

                                            <input type="checkbox" :id="getOptionId(index)" :value="option"
                                                class="mr-2 hidden" />
                                            <label :for="getOptionId(index)" class="break-all p-2 text-center"
                                                @click="selectedOptionRequest(fields, option)">{{ option
                                                }}</label>
                                        </div>
                                    </div> -->
                                    <div class="border py-2 px-2 flex flex-wrap"
                                        :class="{ 'border-blue-300': showOptions === true }">
                                        <div v-for="(selectedOption, index) in formValues[fields.fieldKey]" :key="index"
                                            class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                            {{ selectedOption }}
                                            <span @click="removeOption(fields, selectedOption)"
                                                class="text-red-500 font-semibold cursor-pointer">x</span>
                                        </div>
                                        <input type="text" v-model="search" @input="filterOptions"
                                            @focus="showOptions = true" @blur="hideOptions"
                                            class="h-[35px] mt-1 outline-none border rounded-lg px-2">
                                    </div>
                                    <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                                        v-show="showOptions" :class="{ 'h-auto': showOptions }">
                                        <div v-for="(option, index) in filteredOptions(fields)" :key="index"
                                            class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                            @click="selectOptionMultiple(fields, option)">
                                            {{ option.name }}
                                        </div>
                                        <button v-if="showAddNew"
                                            class="bg-green-700 text-white hover:bg-green-600 px-3 py-1 rounded-lg"
                                            @click="addNewOption(fields)">Add New</button>
                                    </div>
                                </div>
                            </div>
                            <!---div 08 fileds-->
                            <div v-if="fields.place === 'div8'" :class="{ 'filter blur-sm': !fields.enable }">
                                <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'"
                                        class="text-red-500 font-bold">&#9913;</span></label>
                                <!---Additional materials details-->
                                <div class="rounded m-1 flex justify-center items-center" @click="addRow($event)"
                                    :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-2 py-2 border-2 border-blue-500 border-dashed ': ((formValues['additional'] && formValues['additional'].length > 0)) }">
                                    <button class="text-blue-300 text-sm text-center px-2 py-1"
                                        :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-8 py-2 border-2 border-blue-500 border-dashed ': !((formValues['additional'] && formValues['additional'].length > 0)) }">
                                        + Add Materials
                                    </button>
                                </div>
                            </div>

                            <!---div 07 fileds-->
                            <div v-if="fields.place === 'div7'" :class="{ 'filter blur-sm': !fields.enable }">
                                <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'"
                                        class="text-red-500 font-bold">&#9913;</span></label>
                                <!---expense details-->
                                <div v-if="fields.fieldKey == 'service_expense'">
                                    <!---fields-->
                                    <div v-if="formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0"
                                        class="grid grid-cols-2 sm:grid-cols-3 gap-2">
                                        <div v-for="(opt, index) in formValues[fields.fieldKey]" :key="index"
                                            class="col-span-2 text-sm flex justify-between">
                                            <div>
                                                <label class="block text-sm font-semibold pb-2">Description</label>
                                                <input type="text" placeholder="Enter purpose of expense"
                                                    class="py-2 outline-none border rounded-lg px-2"
                                                    v-model="opt.description" />
                                            </div>
                                            <div class="ml-2">
                                                <label class="block text-sm font-semibold pb-2">Amount in
                                                    value</label>
                                                <input type="number" placeholder="Enter amount of expense"
                                                    class="py-2 outline-none border rounded-lg px-2"
                                                    v-model="opt.value" />
                                            </div>
                                        </div>
                                        <div class="ml-2 relative flex items-center text-sm">
                                            <p class="font-semi-bold px-2">Total:
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9'
                                                    : currentCompanyList.currency }} {{
                                                    totalExpense(fields.fieldKey) }}
                                            </p>
                                            <button class="bg-green-600 text-white text-sm py-1 px-3 rounded"
                                                @click="addExpenseData(fields.fieldKey)">+
                                                Add
                                                Expenses</button>
                                        </div>
                                    </div>
                                    <div v-else>
                                        <button class="bg-green-600 text-white text-sm py-1 px-3 rounded"
                                            @click="addExpenseData(fields.fieldKey)">+
                                            Add
                                            Expenses</button>
                                    </div>
                                </div>
                                <!--schedule date-->
                                <div v-else>
                                    <input v-model="formValues[fields.fieldKey]" :type="fields.type"
                                        v-if="fields.fieldKey !== 'document' && (fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file')"
                                        :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                        class="w-full p-2 border rounded" />
                                    <select v-if="fields.fieldKey == 'schedule_time'"
                                        v-model="formValues[fields.fieldKey]" class="w-full p-2 mt-1 border rounded">
                                        <option value="" disabled selected>Select a time</option>
                                        <option v-for="n in Array.from({ length: 24 }, (_, i) => i + 1)" :key="n"
                                            :value="n">
                                            {{ String(n).padStart(2, '0') }}:00
                                        </option>
                                    </select>
                                </div>
                            </div>

                            <!---div 06 fileds-->
                            <div v-if="fields.place === 'div6'" :class="{ 'filter blur-sm': !fields.enable }">
                                <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                                        v-if="fields.required === 'yes'"
                                        class="text-red-500 font-bold">&#9913;</span></label>
                                <!--Drop downs-->
                                <!--single-->
                                <!-- <select
                                    v-if="fields.editData === 'Dropdown' && fields.type === 'single' && fields.fieldKey !== 'customer'"
                                    v-model="formValues[fields.fieldKey]" id="dropdown" class="w-full p-2 border rounded">
                                    <option v-if="formValues[fields.fieldKey] === undefined" value="" selected
                                        class="text-gray-400">select</option>
                                    <option v-for="(option, index) in fields.option" :key="index" :value="option">
                                        {{ option }}
                                    </option>
                                </select> -->
                                <p class="text-gray-500 flex text-xs items-center font-bold pb-2 mb-3">
                                    Note: <span class="material-icons text-xs text-green-700 text-center">
                                        check_box
                                    </span>
                                    is Enable/Disable to form
                                </p>
                                <div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 gap-5"
                                    v-if="fields.editData === 'Dropdown' && fields.type === 'single' && fields.fieldKey !== 'customer'">

                                    <div v-for="(option, index) in fields.option" :key="index"
                                        class="flex items-center">
                                        <p class="absolute text-xs text-blue-700 mb-[60px] ml-5"
                                            style="text-transform: capitalize;">{{ default_options[index] }}</p>
                                        <input type="checkbox" :key="index" :id="'checkbox_' + index"
                                            v-model="fields.option_status[index]" class="mr-2 justify-end" />
                                        <button class="flex items-center bg-gray-200 py-2 px-1 rounded w-full" :class="{
                                            'bg-gray-700': formValues[fields.fieldKey] === option && option === fields.option[0],
                                            'bg-yellow-700': formValues[fields.fieldKey] === option && option === fields.option[1],
                                            'bg-violet-300': formValues[fields.fieldKey] === option && option === fields.option[2],
                                            'bg-yellow-400': formValues[fields.fieldKey] === option && option === fields.option[3],
                                            'bg-green-400': formValues[fields.fieldKey] === option && option === fields.option[4],
                                            'bg-green-700': formValues[fields.fieldKey] === option && option === fields.option[5],
                                            'bg-red-700': formValues[fields.fieldKey] === option && option === fields.option[6],
                                            'bg-green-600': formValues[fields.fieldKey] === option && option === fields.option[7],
                                            'unselected': formValues[fields.fieldKey] !== option,
                                        }" @click="selectStatusOption(option, fields)">
                                            <img :src="statusIcons[index]"
                                                :class="{ 'grayscale': formValues[fields.fieldKey] !== option }"
                                                class="w-5 h-5 mr-1" />
                                            <span
                                                class="text-xs sm:text-sm text-gray-500 cursor-pointer hover:underline"
                                                @click="openNameModal(option, index)" :class="{
                                                    'text-white': formValues[fields.fieldKey] === option
                                                        && (option === fields.option[0] || option === fields.option[1] || option === fields.option[2] || option === fields.option[3] || option === fields.option[4] || option === fields.option[5] || option === fields.option[6] || option === fields.option[7])
                                                }">{{ option }} <font-awesome-icon icon="fa-solid fa-pencil" /></span>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <!--Existing Data-->
                            <!-- <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                                    v-if="fields.required === 'yes'" class="text-red-500 font-bold">&#9913;</span></label> -->
                            <!---Customers select field-->
                            <!-- <select v-if="fields.editData === 'Dropdown' && fields.fieldKey === 'customer'"
                                v-model="formValues[fields.fieldKey]" id="dropdown" class="w-full p-2 border rounded">
                                <option v-for="(option, index) in fields.option" :key="index" :value="option.name">
                                    {{ option.name }}
                                </option>
                            </select> -->
                        </div>
                        <div v-if="currentStep === 0"
                            class="col-span-2 grid  grid-cols-2 sm:grid-cols-4 lg:grid-cols-6 gap-1 justify-center items-center">

                            <button v-if="fields.edit" @click="editField(fields)" :title02="'Edit'"
                                class="custom-toggle w-5 h-5 text-blue-500 hover:text-blue-700 mr-1 focus:outline-none text-sm">
                                <font-awesome-icon icon="fa-solid fa-pencil" :size="isMobile ? 'sm' : 'lg'" />
                            </button>
                            <!-- Delete button -->
                            <button :title02="'Delete / Remove'"
                                v-if="fields.edit && fields.place !== 'div3' && fields.place !== 'div01' && fields.place !== 'div1' && fields.place !== 'div4' && fields.place !== 'div3' && fields.place !== 'div5' && fields.place !== 'div8' && fields.place !== 'div7'"
                                @click="confirmDelete(index)"
                                class="custom-toggle w-5 h-5 text-red-500 hover:text-red-700 mr-1 focus:outline-none text-sm">
                                <font-awesome-icon :icon="['far', 'trash-can']" :size="isMobile ? 'sm' : 'lg'"
                                    style="color: #ff2600;" />
                            </button>
                            <!-- Toggle switch for enable/disable -->
                            <div
                                :class="{ 'hidden': fields.fieldKey === 'customer' || fields.fieldKey === 'status' || fields.fieldKey === 'assignWork' || fields.fieldKey === 'expected_date' || fields.fieldKey === 'service_type' }">
                                <label class="flex items-center">
                                    <div class="custom-toggle" :class="{ 'enabled': fields.enable }"
                                        @click="toggleEnable(fields)" :title02="fields.enable ? 'Enabled' : 'Disabled'">
                                        <font-awesome-icon v-if="fields.enable" icon="fa-regular fa-eye"
                                            style="color: #755bc2;" :size="isMobile ? 'sm' : 'lg'" />
                                        <font-awesome-icon v-else icon="fa-regular fa-eye-slash" style="color: #755bc2;"
                                            :size="isMobile ? 'sm' : 'lg'" />
                                    </div>
                                </label>
                            </div>
                            <!-- Toggle switch for require/unrequire -->
                            <div
                                :class="{ 'hidden': fields.fieldKey === 'customer' || fields.fieldKey === 'status' || fields.fieldKey === 'assignWork' || fields.fieldKey === 'expected_date' || fields.fieldKey === 'service_type' }">
                                <label class="flex items-center">
                                    <div class="custom-checkbox relative"
                                        :class="{ 'checked': fields.required === 'yes' }" @click="toggleRequire(fields)"
                                        :title01="fields.required === 'yes' ? 'Required' : 'Not Required'">
                                        <!-- <span class="tick-symbol" v-if="fields.required === 'yes'">&#10003;</span> -->
                                        <font-awesome-icon v-if="fields.required === 'yes'"
                                            icon="fa-solid fa-square-check" style="color: #0784e4;"
                                            :size="isMobile ? 'sm' : 'lg'" />
                                        <font-awesome-icon v-else icon="fa-regular fa-square" style="color: #0784e4;"
                                            :size="isMobile ? 'sm' : 'lg'" />
                                    </div>
                                </label>
                            </div>
                            <!-- Toggle switch for enabling sales -->
                            <div
                                :class="{ 'hidden': ['customer', 'status', 'assignWork', 'expected_date', 'problem_description', 'pre_repair', 'device_password', 'device_pattern', 'service_priority', 'document', 'notification', 'schedule_date', 'schedule_time', 'notes', 'service_expense'].includes(fields.fieldKey) }">
                                <label class="flex items-center">
                                    <div class="custom-checkbox relative" :class="{ 'checked': fields.enable_sales }"
                                        @click="toggleRequire(fields, true)"
                                        :title01="fields.enable_sales ? 'Enabled for Sales View' : 'Not Needed for Sales View'">
                                        <font-awesome-icon v-if="fields.enable_sales" icon="fa-solid fa-check-circle"
                                            style="color: #0784e4;" :size="isMobile ? 'sm' : 'lg'" />
                                        <font-awesome-icon v-else icon="fa-regular fa-circle" style="color: #0784e4;"
                                            :size="isMobile ? 'sm' : 'lg'" />
                                    </div>
                                </label>
                            </div>
                            <!-- Select icon -->
                            <!-- <select v-model="selectedImage" class="w-[100px]">
                                <option v-for="(opt, index) in icon_list" :value="opt" :key="index">
                                    <div><img :src="opt" :alt="index" class="w-6 h-6 mr-2" /></div>
                                    {{ index }}
                                </option>
                            </select> -->
                            <div class="relative inline-block text-left" :ref="'dropdownContainer' + index">
                                <div>
                                    <button type="button" @click.stop="toggleDropdown(index)"
                                        class="inline-flex justify-center rounded-md border border-gray-300 shadow-sm bg-white text-sm font-medium text-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                                        <img v-if="fields.image" :src="fields.image" alt="selected"
                                            class="ml-1 rounded-full"
                                            :class="{ 'w-5 h-5': !isMobile, 'w-4 h-4': isMobile }" />
                                        <span v-else><font-awesome-icon icon="fa-solid fa-angle-down" /></span>
                                        <svg class="ml-1" :class="{ 'w-5 h-5': !isMobile, 'w-4 h-4': isMobile }"
                                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                            stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                                d="M19 9l-7 7-7-7" />
                                        </svg>
                                    </button>
                                </div>

                                <div v-show="fields.dropdownOpen"
                                    class="origin-top-right absolute right-0 mt-0 w-[150px] p-2 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5"
                                    style="z-index: 999;">
                                    <div class="grid grid-cols-4 gap-4" role="menu" aria-orientation="vertical"
                                        aria-labelledby="options-menu">
                                        <div v-for="(image, i) in icon_list" :key="i" @click="selectImage(image, index)"
                                            class="block text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 cursor-pointer">
                                            <img :src="image" alt="icon" class="w-6 h-6 mr-2 rounded-full inline" />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--Preview view-->
                <preview v-if="currentStep !== 0 && companyId !== null" :dynamicForm="dynamicForm"
                    class="px-2 sm:px-5 pt-1" :formValues="formValues" :companyId="companyId"
                    :updateModalOpen="updateModalOpen" @update-is-modal-open="isModalOpen"></preview>
                <!-- Next step button -->
                <!-- <div v-if="currentStep === 0" class="flex justify-end absolute bottom-0 right-0 mb-7 mr-4">
                    <button @click="nextStep"
                        class="mt-4 px-4 py-2 bg-blue-500 text-white rounded focus:outline-none hover:bg-blue-400">
                        <span v-if="!isMobile">Preview</span><span v-if="isMobile"><font-awesome-icon
                                icon="fa-solid fa-angles-right" size="lg" /></span>
                    </button>
                </div>
                <div v-if="currentStep === 1" class="flex justify-end absolute bottom-0 right-0 mb-7 mr-4">
                    <button @click="nextStep"
                        class="mt-4 px-4 py-2 bg-green-600 text-white rounded focus:outline-none hover:bg-green-500">
                        <span v-if="!isMobile">Save</span><span v-if="isMobile"><font-awesome-icon
                                icon="fa-regular fa-floppy-disk" size="lg" /></span>
                    </button>
                </div>
                <div v-if="currentStep !== 0" class="flex justify-end absolute bottom-0 left-0 mb-7 ml-4">
                    <button @click="backStep"
                        class="mt-4 px-4 py-2 bg-gray-500 text-white rounded focus:outline-none hover:bg-blue-400">
                        <span v-if="!isMobile">Back</span><span v-if="isMobile"><font-awesome-icon
                                icon="fa-solid fa-angles-left" size="lg" /></span>
                    </button>
                </div> -->
                <!-- Preview Button (Current Step 0) -->
                <div v-if="currentStep === 0" class="flex justify-center items-center fixed bottom-0 w-full">
                    <button @click="nextStep"
                        class="px-4 py-2 bg-blue-500 text-white rounded focus:outline-none hover:bg-blue-400 flex justify-center items-center"
                        :class="{ 'w-full': isMobile }">
                        <span class="pr-3">Preview</span>
                        <span><font-awesome-icon icon="fa-solid fa-angles-right" size="lg" /></span>
                    </button>
                </div>

                <!-- Back & Save Buttons (Current Step 1) -->
                <div v-if="currentStep === 1" class="fixed bottom-0 w-full"
                    :class="{ 'flex justify-center items-center': !isMobile, 'grid grid-cols-2 justify-center items-center': isMobile }">
                    <button @click="backStep"
                        class="px-4 py-2 bg-red-500 text-white focus:outline-none hover:bg-red-400 flex justify-center items-center"
                        :class="{ 'mr-10 rounded': !isMobile }">
                        <span><font-awesome-icon icon="fa-solid fa-angles-left" size="lg" /></span>
                        <span class="pl-3">Back</span>
                    </button>
                    <button @click="nextStep"
                        class="px-4 py-2 bg-green-600 text-white focus:outline-none hover:bg-green-500 flex justify-center items-center"
                        :class="{ 'rounded': !isMobile }">
                        <span class="pr-3">Save</span>
                        <span><font-awesome-icon icon="fa-regular fa-floppy-disk" size="lg" /></span>
                    </button>
                </div>
            </div>
        </div>
        <createformFields :show-modal="showModal" @close-modal="closeModal" :editData="editData"
            :fieldToEdit="editingField" :existfields="dynamicForm" :categories_values="categories_values"
            @openDialog="openMessageDialog">
        </createformFields>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog">
        </dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteField" @onCancel="cancelDelete"></confirmbox>
        <addName :showModal="open_name" :userName="button_name" :title="'Edit Button Name'"
            @close-modal="closeNameModal"></addName>
        <Loader :showModal="open_loader"></Loader>
        <createFormField :showModal="open_formField" :title="'Select the field type'" :fields_option="fields_option"
            :fields_create="fields_create" @close-modal="closeFormfield" @openModal="openModal"
            :currentStep="currentStep"></createFormField>
        <mobile_pattern :showModal="show_mobile_pattern" @close-Modal="closeMobilePattern" :pattern_data="pattern_data">
        </mobile_pattern>
    </div>
</template>

<script>
import createformFields from '../../dialog_box/createformFields.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import preview from '../create_form/preview.vue';
import addName from '../../dialog_box/addName.vue';
import createFormField from '../../dialog_box/createFormField.vue';
import mobile_pattern from '../../dialog_box/mobile_pattern.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    emits: ['updateIsOpen'],
    name: 'create_form',
    props: {
        category_name: String,
        dataForm: Object,
        service_categories: Object,
        category_id: Number,
        companyId: String,
        updateModalOpen: Boolean,
    },
    components: {
        createformFields,
        dialogAlert,
        confirmbox,
        preview,
        addName,
        createFormField,
        mobile_pattern
    },
    data() {
        return {
            isMobile: false,
            edit_icon: '/images/service_page/edit.png',
            del_icon: '/images/service_page/del.png',
            view_icon: '/images/service_page/view.png',
            hide_icon: '/images/service_page/hide.png',
            icon_list: ['/images/service_page/Status.png',
                '/images/service_page/Plumber_truck.png',
                '/images/service_page/User.png',
                '/images/service_page/Phone_call.png',
                '/images/service_page/Calendar.png',
                '/images/service_page/Add.png',
                '/images/service_page/Boxes.png',
                '/images/service_page/Help_desk.png',
                '/images/service_page/Money.png',
                '/images/service_page/Money_priority.png',
                '/images/service_page/Zakat.png',
                '/images/service_page/Setting.png',
                '/images/service_page/Writing.png',
                '/images/service_page/Sms.png',
                '/images/service_page/Whatsapp.png',
                '/images/service_page/Email.png',
                '/images/service_page/Printer.png',
                '/images/service_page/customerService.png',
                '/images/service_page/customerServiceTool.png',
                '/images/service_page/digitalServices.png',
                '/images/service_page/repair.png',
                '/images/service_page/service.png',
                '/images/service_page/serviceSetting.png',
                '/images/service_page/tool.png',
                '/images/service_page/seviceIcon.png',
                '/images/service_page/personService.png',
                '/images/service_page/statusIcon.png',
                '/images/service_page/check-list.png',
                '/images/service_page/laptopBug.png',
                '/images/service_page/printerDefect.png',
                '/images/service_page/productDefect.png',
                '/images/service_page/repairPhone.png',
                '/images/service_page/prioritize.png',
                '/images/service_page/priority.png',
                '/images/service_page/schedule.png',
                '/images/service_page/folder.png',
                '/images/service_page/notification.png',
                '/images/service_page/address.png',
                '/images/service_page/checklist.png',
                '/images/service_page/secure.png',
                '/images/service_page/issue_title.png',
                '/images/service_page/serial_number.png',
                '/images/service_page/brand.png',
                '/images/service_page/device_model.png',
                '/images/service_page/issue_description.png',
            ],
            steps: ['Create Form', 'Finish'],
            fields_option: ['Text', 'Number', 'Password', 'Email', 'Date', 'Radio', 'CheckBox', 'Upload', 'Notes', 'Dropdown'], //--- 'Get Exist Form'--
            fields_create: ['&#882;', '&#128290;', '&#128477;', '&#128231;', '&#128197;', '&#128280;', '&#128306;', '&#128206;', '&#128221;', '&#11167;', '&#127915;'],
            currentStep: 0,
            display_custom_button: false,
            showModal: false,
            editData: null,
            categories_values: null,
            isMessageDialogVisible: false,
            open_confirmBox: false,
            message: null,
            dynamicForm: [{
                id: 1,
                editData: "Dropdown", fieldKey: "customer", lableName: "Customer",
                required: "yes", type: "single", enable: true, edit: false, enable_sales: false, image: '/images/service_page/User.png', dropdownOpen: false, place: 'div1'
            },
            { id: 2, editData: 'Radio', lableName: 'Support type', option: ['Carry in', 'Pickup', 'Onsite', 'Remote', 'Installation', 'Warranty', 'Courtesy', 'AMC', 'NC AMC', 'Materials Supply', 'In Home', 'Others'], option_status: [true, true, true, true, false, false, false, false, false, false, false], fieldKey: "service_type", type: 'radio', required: 'yes', enable: true, edit: true, enable_sales: false, image: '/images/service_page/seviceIcon.png', dropdownOpen: false, place: 'div1' },
            { id: 22, editData: 'Radio', lableName: 'Service Type', option: ['Paid', 'Free'], fieldKey: "warranty_type", type: 'radio', required: 'yes', enable: true, edit: true, enable_sales: false, image: '/images/service_page/Money.png', dropdownOpen: false, place: 'div1' },
            //---div 01----
            { id: 3, editData: 'Dropdown', lableName: 'Brand', option: [], fieldKey: "brand", type: 'single', required: 'yes', enable: true, edit: true, enable_sales: false, image: '/images/service_page/brand.png', dropdownOpen: false, place: 'div01' },
            { id: 4, editData: 'Dropdown', lableName: 'Device Model', option: [], fieldKey: "device_model", type: 'single', required: 'yes', enable: true, edit: true, enable_sales: false, image: '/images/service_page/device_model.png', dropdownOpen: false, place: 'div01' },
            { id: 5, editData: 'Text', lableName: 'Serial Number', placeholderText: 'Enter device serial number', fieldKey: 'serial_number', type: 'text', required: 'yes', enable: true, edit: true, enable_sales: false, image: '/images/service_page/serial_number.png', dropdownOpen: false, place: 'div01' },
            { id: 6, editData: 'Dropdown', lableName: 'Problem Title', option: [], fieldKey: "problem_title", type: 'multiple', required: 'yes', enable: true, edit: true, image: '/images/service_page/issue_title.png', enable_sales: false, dropdownOpen: false, place: 'div01' },
            { id: 7, editData: 'Notes', lableName: 'Problem Report By Customer', placeholderText: 'Enter some data', fieldKey: 'problem_description', rowsCount: 3, colsCount: 10, required: 'no', enable: true, edit: true, enable_sales: false, image: '/images/service_page/issue_description.png', dropdownOpen: false, place: 'div01' },
            //--div2 custom fields---
            //----div3--
            {
                id: 8, editData: 'Dropdown', lableName: 'Pre Repair Checklist', fieldKey: 'pre_repair', type: 'single', required: 'yes', option: ['display'], image: '/images/service_page/checklist.png', enable: true, dropdownOpen: false, edit: true, enable_sales: false, place: 'div3', preChecklist: [
                    { value: 'yes', label: '✔', color: '#228B22' },
                    { value: 'no', label: '✗', color: '#DC143C' },
                    { value: 'not_applicable', label: 'N/A', color: '#efe3e6' },
                ]
            },
            { id: 9, editData: 'Device Password', lableName: 'Device Password', placeholderText: 'Enter the device password', fieldKey: "device_password", type: 'text', required: 'no', enable: true, edit: true, enable_sales: false, image: '/images/service_page/secure.png', dropdownOpen: false, place: 'div3' },
            { id: 20, editData: 'Device Pattern', lableName: 'Device Pattern', placeholderText: 'Enter the device password', fieldKey: "device_pattern", type: 'text', required: 'no', enable: true, edit: true, enable_sales: false, image: '/images/service_page/secure.png', dropdownOpen: false, place: 'div3' },
            //---div4--
            { id: 10, editData: 'Estimate Amount', lableName: 'Estimate Service Amount', placeholderText: 'Enter service estimate amount', fieldKey: "estimateAmount", type: 'number', required: 'no', enable: true, edit: true, enable_sales: true, image: '/images/service_page/Money.png', dropdownOpen: false, place: 'div4' },
            { id: 24, editData: 'Discount', lableName: 'Discount', placeholderText: 'Enter discount value', fieldKey: 'discountValue', type: 'number', required: 'no', enable: true, edit: true, enable_sales: true, image: '/images/service_page/discount.png', dropdownOpen: false, place: 'div4' },
            { id: 11, editData: 'Advance Amount', lableName: 'Paid Amount', placeholderText: 'Enter advance paid amount', fieldKey: 'advanceAmount', type: 'number', required: 'no', enable: true, edit: true, enable_sales: true, image: '/images/service_page/Zakat.png', dropdownOpen: false, place: 'div4' },
            // { id: 12, editData: 'Service Amount', lableName: 'Service Amount', placeholderText: 'Enter total service amount', fieldKey: 'serviceAmount', type: 'number', required: 'no', enable: true, edit: true, image: '/images/service_page/Money_priority.png', dropdownOpen: false, place: 'div4' },
            //---div5--
            { id: 13, editData: "Dropdown", fieldKey: "assignWork", lableName: "Assign Work", option: [], required: "yes", type: "multiple", enable: true, edit: false, enable_sales: false, image: '/images/service_page/personService.png', dropdownOpen: false, place: 'div5' },
            { id: 14, editData: 'Radio', lableName: 'Service Priority', option: ['High', 'Medium', 'Low'], fieldKey: "service_priority", type: 'radio', required: 'yes', enable: true, edit: true, enable_sales: false, image: '/images/service_page/prioritize.png', dropdownOpen: false, place: 'div5' },
            { id: 15, editData: 'Notes', lableName: 'Notes To Technician', placeholderText: 'Enter some data', fieldKey: 'notes', rowsCount: 3, colsCount: 10, required: 'no', enable: true, edit: true, image: '/images/service_page/Writing.png', dropdownOpen: false, place: 'div5' },
            { id: 16, editData: 'Date', lableName: 'Expected Completion Date', fieldKey: 'expected_date', type: 'date', required: 'yes', enable: true, edit: true, enable_sales: false, image: '/images/service_page/schedule.png', dropdownOpen: false, place: 'div5' },
            { id: 17, editData: 'File', lableName: 'Product Image', fieldKey: 'document', type: 'file', required: 'no', enable: true, edit: true, enable_sales: false, image: '/images/service_page/folder.png', dropdownOpen: false, place: 'div5' },
            { id: 18, editData: 'Checkbox', lableName: 'Send Notification', option: ['SMS', 'WhatsApp', 'Email'], type: 'checkbox', fieldKey: 'notification', required: 'no', enable: true, edit: true, enable_sales: false, image: '/images/service_page/notification.png', dropdownOpen: false, place: 'div5' },
            //---div8---
            { id: 23, editData: "Additional", fieldKey: "additional", lableName: "Additional material/services", required: "no", type: "text", enable: true, edit: true, enable_sales: true, image: '/images/customer_page/product.png', dropdownOpen: false, place: 'div8' },
            //---div7--
            { id: 21, editData: "Expense", fieldKey: "service_expense", lableName: "Expense Details", required: "no", type: "text", enable: true, edit: true, enable_sales: false, image: '/images/service_page/Money.png', dropdownOpen: false, place: 'div7' },

            { id: 25, editData: 'Date', lableName: 'Schedule Date', fieldKey: 'schedule_date', type: 'date', required: 'no', enable: true, edit: true, enable_sales: false, image: '/images/service_page/schedule.png', dropdownOpen: false, place: 'div7' },
            { id: 26, editData: 'Time', lableName: 'Schedule Time', fieldKey: 'schedule_time', type: 'time', required: 'no', enable: true, edit: true, enable_sales: false, image: '/images/service_page/schedule.png', dropdownOpen: false, place: 'div7' },
            //--div6---
            { id: 19, editData: "Dropdown", fieldKey: "status", lableName: "Status", option: ['Service Taken', 'Hold', 'In-progress', 'New Estimate', 'Ready To Deliver', 'Delivered', 'Cancelled', 'Completed', 'New Service', 'Awaiting For Payment', 'Service Completed', 'Return To Customer'], option_status: [true, true, true, true, true, true, true, true, false, false, false, false], required: "yes", type: "single", enable: true, edit: false, enable_sales: false, image: '/images/service_page/statusIcon.png', dropdownOpen: false, place: 'div6' },
            ],
            formValues: {},
            filteredCustomerOptions: [],
            isDropdownOpen: false,
            editingField: null,
            // tile1_icon: '/images/categories_page/tile-1.png',
            // tile2_icon: '/images/categories_page/tile-2.png',
            // tile3_icon: '/images/categories_page/tile-3.png',
            style_view: '1',
            // service_categories: [],
            selectedImage: null,
            selectedFieldIndex: null,
            showDropdown: false,
            deleteItemIndex: null,
            selectedOption: {},
            //----
            isDropdownOpen: false,
            searchInput: "",
            customer_list: [],
            filteredDataOptions: [],
            dropdownOpenData: null,
            statusIcons: ['/images/service_page/order_taken.png', '/images/service_page/hold.png', '/images/service_page/work-in-progress.png', '/images/service_page/estimate.png', '/images/service_page/delivery.png', '/images/service_page/delivered.png', '/images/service_page/cancelled.png', '/images/service_page/checked.png', '/images/service_page/new_service.png', '/images/service_page/awaiting_payment.png', '/images/service_page/service_complete.png', '/images/service_page/retrun_customer.png'],
            //----new ---
            search: '',
            showOptions: false,
            showAddNew: false,
            pagination_data: {},
            isfocused: {},
            open_name: false,
            button_name: '',
            selected_button_index: null,
            open_loader: false,
            default_options: ['service taken', 'hold', 'in-progress', 'new estimate', 'ready to deliver', 'delivered', 'cancelled', 'completed', 'new service', 'awaiting for payment', 'service completed', 'return to customer'],
            field_icon: null,
            open_formField: false,
            //--mobile pattern,
            show_mobile_pattern: false,
            //---pattern data----
            pattern_data: [],
        };
    },
    methods: {
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        //--next button
        nextStep() {
            if (this.currentStep < this.steps.length - 1) {
                this.currentStep++;
            } else {
                this.open_loader = true;
                axios.put(`/service_categories/${this.category_id}`, { company_id: this.companyId, service_category: this.category_name, form: JSON.stringify(this.dynamicForm) })
                    .then(response => {
                        // console.log(response.data, 'RRRR');
                        this.open_loader = false;
                        this.openMessageDialog('Form updated successfully!');
                        this.updateKeyWithTime('service_category_update')
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
                // console.log(this.dataForm, 'What happening...');
                // Find the corresponding service category in the data object
                //const categoryIndex = this.service_categories.findIndex(category => category.serviceCategory === this.category_name);

                //if (categoryIndex !== -1) {
                // console.log(categoryIndex, 'What happening..');
                // Update the form and style_view in the service category
                // this.service_categories[categoryIndex].form = this.dynamicForm;
                // this.service_categories[categoryIndex].style_view = this.style_view;

                // Store the updated data object in local storage
                // localStorage.setItem('CategoriesForm', JSON.stringify(this.service_categories));

                // alert('Form created successfully!');
                // this.openMessageDialog('Form created successfully!');
                //--go back--
                // this.$router.go(-1);
            }
            //  else {
            //     console.error('Service category not found in the data object.');
            // }
        },
        //---pre checklist
        selectOption(key, option, formValuesKey, preChecklist) {
            const previousOption = this.formValues[formValuesKey];
            // console.log(previousOption, 'previous options...');

            // Reset previous option color
            const previousOptionIndex = preChecklist.findIndex(opt => opt.value === previousOption);
            // console.log(previousOptionIndex, 'previous options index...!');
            if (previousOptionIndex !== -1) {
                preChecklist[previousOptionIndex].color = '';
            }

            // Delay background color change for a smooth transition
            if (this.formValues[formValuesKey]) {
                setTimeout(() => {
                    // Set the new selected option
                    this.formValues[formValuesKey][key] = option;
                    // Set the color for the selected option
                    // const selectedOptionIndex = preChecklist.findIndex(opt => opt.value === option);
                    // if (selectedOptionIndex !== -1) {
                    //     preChecklist[selectedOptionIndex].color = preChecklist[selectedOptionIndex].color || 'initial';
                    // }

                }, 100);
            } else {
                setTimeout(() => {
                    // Set the new selected option
                    this.formValues[formValuesKey] = {};
                    this.formValues[formValuesKey][key] = option;
                    // Set the color for the selected option
                    // const selectedOptionIndex = preChecklist.findIndex(opt => opt.value === option);
                    // if (selectedOptionIndex !== -1) {
                    //     preChecklist[selectedOptionIndex].color = preChecklist[selectedOptionIndex].color || 'initial';
                    // }
                }, 100);
            }
        },
        //---back button
        backStep() {
            if (this.currentStep === this.steps.length - 1) {
                this.currentStep--;
            }
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...');
            this.deleteItemIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        //--create field model
        openModal(data) {
            // if (this.isfocused && this.isfocused.head) {
            //     this.isfocused.head = false;
            // }
            if (this.open_formField) {
                this.open_formField = false;
            }
            // console.log(data, 'What about data...!');
            if (data !== 'Get Exist Form') {
                this.editData = data;
                this.editingField = null;
                this.showModal = true;
            } else {
                // console.log(this.dataForm, 'What about data', this.category_name);
                this.editData = data;
                this.categories_values = this.service_categories.filter((opt) => opt.id !== this.dataForm.id && opt.form.length !== 0);
                this.showModal = true;
            }
        },
        closeModal(data) {
            if (data) {
                // Check if it's in edit mode
                if (this.editingField) {
                    // Find the index of the editing field in the array
                    const index = this.dynamicForm.findIndex(field => field.id === this.editingField.id);

                    if (index !== -1) {
                        // If the field is found, update it with the new values
                        this.dynamicForm[index] = { ...this.dynamicForm[index], ...data };
                    } else {
                        // Handle the case where the field is not found in the array
                        console.error('Editing field not found in dynamicForm array.');
                    }
                } else if (this.editData === 'Get Exist Form') {
                    // console.log('What kkkkkkkk');
                    this.dynamicForm = data;
                }
                else {
                    const insertIndex = this.dynamicForm.length - this.filteredLength;
                    // If not in edit mode, assign a unique id to the new data and push it to the array
                    const newId = this.dynamicForm.length + 1;
                    // this.dynamicForm.push({ ...data, id: newId, image: '/images/service_page/tool.png' });
                    this.dynamicForm.splice(insertIndex, 0, { ...data, id: newId, image: '/images/service_page/tool.png' });
                }
                if (data.place === 'div2') {
                    this.focusAddedNewFields();
                }
            }
            // Reset editingField to null
            this.editingField = null;

            // Close the modal
            this.showModal = false;

            // Reset the formValues
            this.formValues = {};
        },
        //--check box
        updateCheckbox(fieldKey, option) {
            if (!this.formValues[fieldKey]) {
                // Initialize the nested array if it doesn't exist
                this.formValues[fieldKey] = [];
            }
            const index = this.formValues[fieldKey].indexOf(option);
            if (index === -1) {
                this.formValues[fieldKey].push(option);
            } else {
                this.formValues[fieldKey].splice(index, 1);
            }
        },
        //---customers dropdown
        selectDropdownOption(fields, option) {
            this.formValues[fields.fieldKey] = option.first_name + ' ' + option.last_name ? option.last_name : '' + ' - ' + option.contact_number;
            this.isDropdownOpen = false; // Close the dropdown
        },

        handleDropdownInput(fields) {
            const inputValue = this.formValues[fields.fieldKey];
            if (inputValue !== undefined && inputValue !== null) {
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    // console.log(this.customer_list, 'What happning');
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    // console.log(this.customer_list, 'What happning');
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.first_name ? option.first_name.toLowerCase() : '' + ' ' + option.last_name ? option.last_name.toLowerCase().includes(inputName) : ''
                    );
                }
            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = [];
            }
        },
        //--edit, delete and enable and disable field
        editField(field) {
            // Open the modal and pass the data to edit
            // this.editData = this.dynamicForm[index];
            if (field) {
                this.editData = field.editData;
                this.editingField = field;
                this.showModal = true;
            }
        },

        // Method to handle the "Delete" button click
        deleteField() {
            // Remove the field from dynamicForm
            if (this.deleteItemIndex !== null) {
                this.dynamicForm.splice(this.deleteItemIndex, 1);
            }
            this.open_confirmBox = false;
            this.deleteItemIndex = null;
        },

        // Method to handle toggle switch for enable/disable
        toggleEnable(field) {
            const foundField = this.dynamicForm.find((f) => f.fieldKey === field.fieldKey);
            if (foundField && field.fieldKey !== "customer" && field.fieldKey !== "status" && field.fieldKey !== "assignWork" && field.fieldKey !== "expected_date" && field.fieldKey !== 'notification' && field.fieldKey !== 'service_type') {
                foundField.enable = !foundField.enable;
                if (!foundField.enable) {
                    foundField.required = foundField.required === 'no';
                    if (foundField.enable_sales) {
                        foundField.enable_sales = false;
                    }
                }
            } else {
                // this.openMessageDialog(`${field.fieldKey === "customer" ? 'Customer' : field.fieldKey === "status" ? 'Status': field.fieldKey === "assignWork" ? 'Assign work to staff/self' : field.fieldKey === "expected_date" ? 'Expected service complete date' : 'Notification send'} form field cannot be disable`);
                this.openMessageDialog('This field cannot be disabled.');
                // alert('Customer form field cannot disable');
            }
        },
        // Method to handle toggle switch for required/not required
        toggleRequire(field, is_sales) {
            if (is_sales) {
                const foundField = this.dynamicForm.find((f) => f.fieldKey === field.fieldKey);
                let default_key = ['customer', 'status', 'assignWork', 'expected_date', 'problem_description', 'pre_repair', 'device_password', 'device_pattern', 'service_priority', 'document', 'notification', 'schedule_date', 'schedule_time', 'notes'].includes(field.fieldKey);
                if (!default_key && foundField && foundField.enable) {
                    foundField.enable_sales = foundField.enable_sales ? false : true; // Toggle between 'yes' and 'no'
                } else {
                    if (foundField.enable_sales) {
                        foundField.enable_sales = false;
                    }
                    this.openMessageDialog('Please enable the field before making changes to the sales view.');
                }
            } else {
                const foundField = this.dynamicForm.find((f) => f.fieldKey === field.fieldKey);
                if (foundField && field.fieldKey !== "customer" && field.fieldKey !== "status" && field.fieldKey !== "assignWork" && field.fieldKey !== "expected_date" && field.fieldKey !== 'service_type') {
                    foundField.required = foundField.required === 'yes' || !foundField.enable ? 'no' : 'yes'; // Toggle between 'yes' and 'no'
                    if (!foundField.enable) {
                        this.openMessageDialog('Please enable the field before making changes to the "Required" or "Not Required" setting.');
                    }
                } else if ((field.fieldKey === "assignWork" && (foundField.required === 'no' || foundField.required === false)) || (field.fieldKey === "expected_date" && (foundField.required === 'no' || foundField.required === false || foundField.required === true)) || (field.fieldKey === 'service_type' && (foundField.required === 'no' || foundField.required === false))) {
                    foundField.required = 'yes';
                } else {
                    // this.openMessageDialog(field.fieldKey === "customer" ? 'Customer form field cannot be changed' : 'Status must be chosen.');
                    // alert('Customer form field cannot be disabled');
                    this.openMessageDialog('This field cannot be change required status.');
                }
            }
        },
        //---move index of object--
        moveField(index, direction) {
            // Check if the move is within bounds
            if (index + direction >= 0 && index + direction < this.dynamicForm.length) {
                // Swap the positions of the fields in the dynamicForm array
                [this.dynamicForm[index], this.dynamicForm[index + direction]] = [this.dynamicForm[index + direction], this.dynamicForm[index]];
            }
        },
        //---update based on category--
        updateComponentBasedOnCategoryName(newCategoryName) {
            let findCategory = this.service_categories.find((opt) => opt.id === this.category_id);
            // console.log(findCategory, 'Weeeae', 'Category', this.category_name, 'Is mobile', this.isMobile, this.service_categories);
            if (findCategory && findCategory.form && findCategory.form.length !== 0) {
                this.dynamicForm = JSON.parse(findCategory.form);
                // console.log(this.dynamicForm, 'EEEEEE');
            }
            if (findCategory && findCategory.style_view && findCategory.style_view !== null) {
                this.style_view = findCategory.style_view;
                // console.log(this.style_view, 'Style view...!');
            }
        },
        //---open alert--
        openMessageDialog(message) {
            // console.log('TETETETTRRETER');
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.currentStep === 1) {
                const createFormQueryParam = this.$route.query.createform;
                if (createFormQueryParam) {
                    let selected_customer = this.$route.query.customer_id;
                    if (selected_customer) {
                        this.$router.push({ name: 'service-category-add', params: { type: this.category_name, id: this.category_id }, query: { customer_id: selected_customer } });
                    } else {
                        this.$router.push({ name: 'service-category-add', params: { type: this.category_name, id: this.category_id } });
                    }
                } else {
                    this.$router.go(-1);
                }
            }
        },
        toggleDropdown(index) {
            this.dynamicForm.forEach((item, i) => {
                if (i === index) {
                    item.dropdownOpen = !item.dropdownOpen;
                    if (item.dropdownOpen) {
                        // Attach click event listener to detect clicks outside the dropdown
                        document.addEventListener('click', this.handleClickOutside);
                    } else {
                        // Remove click event listener when dropdown is closed
                        document.removeEventListener('click', this.handleClickOutside);
                    }
                } else {
                    item.dropdownOpen = false;

                }
            });
            this.field_icon = index;
        },
        selectImage(image, index) {
            this.dynamicForm[index].image = image;
            this.dynamicForm[index].dropdownOpen = false;
        },
        togglePopover(event) {
            this.popoverPosition = {
                x: event.clientX - this.popoverWidth / 2, // Center the popover on the mouse cursor
                y: event.clientY,
            };
            this.isPopoverOpen = !this.isPopoverOpen;
        },

        //---multiple dropdowwn
        //----select---
        // toggleMultipleDropdown(index) {
        //     // Update the dynamicForm directly without using $set
        //     this.dynamicForm[index].multipleDropdown = !this.dynamicForm[index].multipleDropdown;

        //     // Ensure selectedOptions is initialized as an array
        //     if (!Array.isArray(this.dynamicForm[index].selectedOptions)) {
        //         this.dynamicForm[index].selectedOptions = [];
        //     }
        // },
        // filterTheData(fields, options, value) {
        //     if (value !== '') {
        //         let data = value.split(', ').filter(item => item !== '');
        //         fields.option = this.filterOptions(options, value);
        //     } else {
        //         fields.option = options;
        //     }
        // },

        // filterOptions(options, searchInput) {
        //     if (searchInput && searchInput !== '') {
        //         // console.log(searchInput, 'What happening....Q');
        //         let data = searchInput.split(', ').filter(item => item !== '');
        //         return options.filter(option => !data.includes(option));
        //     } else {
        //         return options;
        //     }
        // },

        // getOptionId(index) {
        //     // Generate a unique ID for each option in the dropdown
        //     return `option_${index}`;
        // },
        // getOptionRequest(fields, option) {
        //     // console.log(fields, 'in requrest');
        //     if (fields.searchInput !== '') {
        //         return true;
        //     }
        //     else {
        //         return true;
        //     }
        // },
        // // Handle selected options
        // selectedOptionRequest(fields, option) {
        //     if (this.formValues[fields.fieldKey]) {
        //         // console.log('wwffwfwfwfwfwfwfwf', option);
        //         let findData = this.dynamicForm.find(opt => opt.fieldKey === fields.fieldKey);
        //         this.formValues[fields.fieldKey] = this.formValues[fields.fieldKey] + ', ' + option;
        //         return;
        //     } else {
        //         this.formValues[fields.fieldKey] = option;
        //         return;
        //     }
        // },

        //--brand, devicemodel and defect tiltle
        handleDisplay(fields) {
            this.filteredDataOptions = [];
            if (fields.option && Array.isArray(fields.option) && event.target.value.length > 2) {
                // Filter the options based on certain criteria
                this.filteredDataOptions = fields.option.filter((opt) => this.formValues[fields.fieldKey].toLowerCase().includes(opt.toLowerCase()));
                // console.log(this.filteredDataOptions, 'Filtered Data Options');
            } else {
                this.filteredDataOptions = fields.option;
            }
        },
        selectOptionData(fields, option) {
            if (option) {
                this.formValues[fields.fieldKey] = option;
                this.dropdownOpenData = null;
                this.filteredDataOptions = [];
            }
        },
        AddnewOption(fields) {
            let value = event.target.value;
            if (value) {
                let findDuplicate = fields.option.findIndex((opt) => opt.toLowerCase() === value.toLowerCase());
                if (findDuplicate === -1) {
                    fields.option.push(value);
                    this.formValues[fields.fieldKey] = value;
                } else {
                    this.formValues[fields.fieldKey] = fields.option[findDuplicate];
                }
            }

            this.dropdownOpenData = null;
            this.filteredDataOptions = [];
        },
        handleImageUpload(event, index) {
            const file = event.target.files[0];
            // Handle image upload logic and update the corresponding item in formValues
            // For example, you can use FileReader to read the file and set it to the item's image property
        },
        deleteRow(index) {
            this.formValues.document.splice(index, 1);
            // Handle deleting the row from formValues
        },
        addRow(fields) {
            if (this.formValues[fields.fieldKey]) {
                this.formValues[fields.fieldKey].push({ name: '', image: '' }); // Add a new empty row to formValues
            }
            else {
                this.formValues[fields.fieldKey] = [{ name: '', image: '' }];
            }
        },
        //---selected option--
        selectStatusOption(option, fields) {
            if (option) {
                this.formValues[fields.fieldKey] = option;
            }
        },
        //-----multiple option---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
        },
        filteredOptions(fields) {
            // console.log(fields, 'WWWWW', this.formValues[fields.fieldKey]);
            if (this.formValues[fields.fieldKey]) {
                // console.log( fields.option, 'Wat happening..1');
                let findArray = fields.option.filter(option => option.toLowerCase().includes(this.search.toLowerCase()) && !this.formValues[fields.fieldKey].includes(option));
                if (findArray.length > 0) {
                    this.showAddNew = false;
                    return findArray
                }
                else {
                    this.showAddNew = true;
                    return findArray;
                }
            }
            else {
                return fields.option;
            }
        },
        selectOptionMultiple(fields, option) {
            // console.log(option, 'What happening...!');
            if (this.formValues[fields.fieldKey]) {
                this.formValues[fields.fieldKey].push(option);
            } else {
                this.formValues[fields.fieldKey] = [option];
            }
            this.search = '';
            this.showOptions = false;
        },
        removeOption(fields, option) {

            this.formValues[fields.fieldKey] = this.formValues[fields.fieldKey].filter(selected => selected !== option);
        },
        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
            }, 300); // Add delay to allow click events on options
        },
        addNewOption(fields) {
            fields.option.push(this.search);
            this.formValues[fields.fieldKey].push(this.search);
            this.search = '';
            this.showOptions = false;
            this.showAddNew = false;
        },
        //-----get Employee list----
        getEmployeeList(page, per_page) {
            //---employee list
            // console.log(this.companyId, 'WWWWW');
            axios.get('/employees', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data.data, 'employee');
                    let findIndexData = this.dynamicForm.findIndex((opt) => opt.fieldKey === 'assignWork')
                    this.dynamicForm[findIndexData].option = response.data.data;
                    this.pagination_data.assign_to = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error get employee', error);
                })
            // let getLocalData = localStorage.getItem('employee');
            // if (getLocalData) {
            //     let parseData = JSON.parse(getLocalData);
            //     if (parseData) {
            //         let findIndexData = this.dynamicForm.findIndex((opt) => opt.fieldKey === 'assignWork')
            //         if (findIndexData !== -1) {
            //             this.dynamicForm[findIndexData].option = parseData;
            //         }
            //     }
            // }
        },
        //---service category---
        serviceCategoryList() {
            axios.get('/service_categories', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'service catgory get..!');
                    return response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---customer list---
        getCustomerList(page, per_page) {
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'customer list....!');
                    this.customer_list = response.data.data;
                    this.pagination_data.customer = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---handle
        handleFocusCustom() {
            // console.log('Function is executing at a time');
            this.$nextTick(() => {
                if (this.$refs.customFieldsHeader) {
                    this.$refs.customFieldsHeader.focus();
                }
            })
            this.open_formField = true;
        },
        focusAddedNewFields() {
            // console.log('Button ref:', this.$refs.customFieldsButton); // Check the value of the ref
            if (this.$refs.customFieldsButton) {
                this.$refs.customFieldsButton[0].focus(); // Attempt to focus
            }
        },
        getserviceCategoryList() {
            if (this.companyId) {
                axios.get('/service_categories', { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.data, 'service gatgory get..!');
                        this.categories_values = response.data.data;
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    })
            }
        },
        openNameModal(data, index) {
            if (data) {
                this.selected_button_index = index;
                this.button_name = data;
                this.open_name = true;
            }
        },
        closeNameModal(data) {
            if (data && this.selected_button_index >= 0) {
                let filter_data = this.dynamicForm.findIndex(opt => opt.fieldKey === 'status');
                if (filter_data >= 0) {
                    this.dynamicForm[filter_data].option[this.selected_button_index] = data;
                }
                this.selected_button_index = null;
            }
            this.open_name = false;
        },
        handleClickOutside(event) {
            if (this.field_icon !== null) {
                const dropdownContainer = this.$refs[`dropdownContainer${this.field_icon}`][0];
                // console.log(dropdownContainer, 'RRRRRRR');
                // Check if the clicked element is outside the dropdown container
                if (!dropdownContainer || !dropdownContainer.contains(event.target)) {
                    // Clicked outside the dropdown, close it
                    this.dynamicForm[this.field_icon].dropdownOpen = false;
                    // Remove click event listener after closing the dropdown
                    document.removeEventListener('click', this.handleClickOutside);
                    this.field_icon = null; // Reset field_icon after closing dropdown
                }
            }
        },
        closeFormfield() {
            this.open_formField = false;
        },
        //----open mobile pattern
        openMobilePattern(data) {
            if (data && typeof data === 'string' && Array.isArray(JSON.parse(data))) {
                this.pattern_data = JSON.parse(data);
                this.show_mobile_pattern = true;
            }
            else {
                this.show_mobile_pattern = true;
            }
        },
        closeMobilePattern(data, status) {
            if (data) {
                if (this.formValues.device_pattern) {
                    this.formValues.device_pattern = JSON.stringify(data);
                    this.show_mobile_pattern = false;
                } else {
                    this.formValues.device_pattern = JSON.stringify(data);
                    this.show_mobile_pattern = false;
                }

            } else {
                if (status && this.formValues.device_pattern && typeof this.formValues.device_pattern === 'string' && Array.isArray(JSON.parse(this.formValues.device_pattern))) {
                    this.formValues.device_pattern = '';
                    this.show_mobile_pattern = false;
                } else {
                    this.show_mobile_pattern = false;
                }
            }
        },
        //----add expense ---
        addExpenseData(key) {
            if (this.formValues[key] && Array.isArray(this.formValues[key]) && this.formValues[key].length > 0) {
                this.formValues[key].push({ description: '', value: 0 });
            } else {
                this.formValues[key] = [{ description: '', value: 0 }];
            }
        },
        totalExpense(key) {
            if (this.formValues[key] && Array.isArray(this.formValues[key]) && this.formValues[key].length > 0) {
                return this.formValues[key].reduce((total, opt) => total + opt.value, 0);
            }
            return 0;
        },
        //--any modal is open--
        isModalOpen(type) {
            if (type !== undefined) {
                this.$emit('updateIsOpen', type);
            }
        },
        closeAllModals() {
            this.showModal = false;
            this.isMessageDialogVisible = false;
            this.open_confirmBox = false;
            this.open_name = false;
            this.open_formField = false;
            this.show_mobile_pattern = false;
        },

    },
    mounted() {
        this.updateIsMobile();
        this.fetchCompanyList();
        window.addEventListener('resize', this.updateIsMobile);

    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },

    watch: {
        category_name(newCategoryName) {
            this.updateComponentBasedOnCategoryName(newCategoryName);
        },
        companyId(newValue) {
            // this.getEmployeeList(1, 100);
            //--get customer list----
            // this.getCustomerList(1, 100);
        },
        dynamicForm: {
            deep: true,
            handler(newValue) {
                if (newValue && Array.isArray(newValue)) {
                    //---update status
                    let find_status = newValue.findIndex(opt => opt.fieldKey === 'status');
                    // console.log(find_status, 'EEEEE');
                    if (find_status !== -1 && !newValue[find_status]['option_status']) {
                        newValue[find_status]['option_status'] = [true, true, true, true, true, true, true, true];
                    }
                    if (find_status !== -1 && (newValue[find_status]['option'].length === 7 || newValue[find_status]['option'].length === 8)) {
                        if (newValue[find_status]['option'].length === 7) {
                            newValue[find_status]['option'] = [...newValue[find_status]['option'], 'Completed', 'New Service', 'Awaiting For Payment', 'Service Completed', 'Return To Customer'];
                            if (newValue[find_status]['option_status']) {
                                newValue[find_status]['option_status'] = [...newValue[find_status]['option_status'], false, false, false, false, false];
                            }
                        } else if (newValue[find_status]['option'].length === 8) {
                            newValue[find_status]['option'] = [...newValue[find_status]['option'], 'New Service', 'Awaiting For Payment', 'Service Completed', 'Return To Customer'];
                            if (newValue[find_status]['option_status']) {
                                newValue[find_status]['option_status'] = [...newValue[find_status]['option_status'], false, false, false, false];
                            }
                        }
                    }
                    if (find_status !== -1 && (newValue[find_status]['required'] === '' || newValue[find_status]['required'] !== 'no')) {
                        newValue[find_status]['required'] = 'yes';
                    }
                    //---update service type
                    let find_service_type = newValue.findIndex(opt => opt.fieldKey === 'service_type');
                    if (find_service_type !== -1 && !newValue[find_service_type]['option_status']) {
                        newValue[find_service_type]['option_status'] = [true, true, true, true];
                    }
                    if (find_service_type !== -1 && newValue[find_service_type]['option'].length === 3) {
                        newValue[find_service_type]['option'].push('Remote', 'Installation', 'Warranty', 'Courtesy', 'AMC', 'NC AMC', 'Materials Supply', 'Others');
                        if (newValue[find_service_type]['option_status']) {
                            newValue[find_service_type]['option_status'].push(false, false, false, false, false, false, false, false);
                        }
                    }
                    if (find_service_type !== -1 && newValue[find_service_type]['option'].length === 4) {
                        newValue[find_service_type]['option'].push('Installation', 'Warranty', 'Courtesy', 'AMC', 'NC AMC', 'Materials Supply', 'Others');
                        if (newValue[find_service_type]['option_status']) {
                            newValue[find_service_type]['option_status'].push(false, false, false, false, false, false, false);
                        }
                    }
                    // Update pattern key
                    const patternIndex = newValue.findIndex(opt => opt.fieldKey === 'device_pattern');
                    if (patternIndex === -1) {
                        let newItem = {
                            id: 20,
                            editData: 'Device Pattern',
                            lableName: 'Device Pattern',
                            placeholderText: 'Enter the device password',
                            fieldKey: "device_pattern",
                            type: 'text',
                            required: 'no',
                            enable: false,
                            edit: true,
                            image: '/images/service_page/secure.png',
                            dropdownOpen: false,
                            place: 'div3'
                        };
                        newValue.splice((newValue.length - 10), 0, newItem)
                    }
                    //---update Warranty type
                    const warrantyType = newValue.findIndex(opt => opt.fieldKey === 'warranty_type');
                    if (warrantyType === -1) {
                        let newItem = { id: 22, editData: 'Radio', lableName: 'Warranty Type', option: ['Paid', 'Free'], fieldKey: "warranty_type", type: 'radio', required: 'no', enable: false, edit: false, image: '/images/service_page/Money.png', dropdownOpen: false, place: 'div1' };
                        newValue.splice(2, 0, newItem);
                    }
                    // Update expense
                    const expenseIndex = newValue.findIndex(opt => opt.fieldKey === 'service_expense');
                    if (expenseIndex === -1) {
                        let newItem = {
                            id: 21,
                            editData: "Expense",
                            fieldKey: "service_expense",
                            lableName: "Expense Details",
                            required: "no",
                            type: "text",
                            enable: false,
                            edit: false,
                            image: '/images/service_page/Money.png',
                            dropdownOpen: false,
                            place: 'div7'
                        };
                        newValue.splice((newValue.length - 2), 0, newItem);
                    }
                    //---additional material--
                    const additionalIndex = newValue.findIndex(opt => opt.fieldKey === 'additional');
                    if (additionalIndex === -1) {
                        let newItem = {
                            id: newValue.length - 3 ? newValue.length : 23,
                            editData: "Additional",
                            fieldKey: "additional",
                            lableName: "Additional material/services",
                            required: "no", type: "text", enable: true,
                            edit: true, image: '/images/customer_page/product.png',
                            dropdownOpen: false, place: 'div8'
                        };
                        newValue.splice((newValue.length - 3), 0, newItem);
                    }
                    //----discount-----
                    const dicountIndex = newValue.findIndex(opt => opt.fieldKey === 'discountValue');
                    if (dicountIndex === -1) {
                        let newItem = {
                            id: newValue.length ? newValue.length + 1 : 24,
                            editData: 'Discount', lableName: 'Discount', placeholderText: 'Enter discount value', fieldKey: 'discountValue', type: 'number', required: 'no', enable: false, edit: true, image: '/images/service_page/discount.png', dropdownOpen: false, place: 'div4'
                        };
                        const advanceIndex = newValue.findIndex(opt => opt.fieldKey === 'advanceAmount');
                        if (advanceIndex) {
                            newValue.splice((advanceIndex - 1), 0, newItem);
                        } else {
                            newValue.splice((filteredLength.length - 4), 0, newItem);
                        }

                    }
                    //---Schedule date--
                    const scheduleIndex = newValue.findIndex(opt => opt.fieldKey === 'schedule_date');
                    if (scheduleIndex === -1) {
                        let newItem = { id: newValue.length ? newValue.length + 1 : 25, editData: 'Date', lableName: 'Schedule Date', fieldKey: 'schedule_date', type: 'date', required: 'no', enable: false, edit: true, image: '/images/service_page/schedule.png', dropdownOpen: false, place: 'div7' };
                        newValue.splice((newValue.length - 2), 0, newItem);
                    }
                    //---Schedule date--
                    const scheduleTimeIndex = newValue.findIndex(opt => opt.fieldKey === 'schedule_time');
                    if (scheduleTimeIndex === -1) {
                        let newItem = { id: newValue.length ? newValue.length + 1 : 25, editData: 'Time', lableName: 'Schedule Time', fieldKey: 'schedule_time', type: 'time', required: 'no', enable: true, edit: true, image: '/images/service_page/schedule.png', dropdownOpen: false, place: 'div7' };
                        newValue.splice((newValue.length - 2), 0, newItem);
                    }
                    //---Sales Enable / Disable--
                    newValue.map(opt => {
                        if (opt.enable) {
                            let default_key = ['customer', 'status', 'assignWork', 'expected_date', 'problem_description', 'pre_repair', 'device_password', 'device_pattern', 'service_priority', 'document', 'notification', 'schedule_date', 'schedule_time', 'notes', 'service_expense'].includes(opt.fieldKey);
                            if (!default_key) {
                                let sales_key = ['additional', 'estimateAmount', 'discountValue', 'advanceAmount'].includes(opt.fieldKey);
                                if (!opt.hasOwnProperty('enable_sales')) {
                                    opt.enable_sales = sales_key ? true : false;
                                }
                            }
                        }
                    });
                }
            }
        }
    },
    computed: {
        ...mapGetters('companies', ['currentCompanyList']),
        getDataofDiv() {
            let find_length = this.dynamicForm.filter(opt => opt.place === 'div2');
            return find_length.length;
        },
        filteredLength() {
            return this.dynamicForm.filter(item => !['div1', 'div01', 'div2'].includes(item.place)).length;
        },

    },
    //---current data update---
    //-----navigate controll---
    updateModalOpen: {
        deep: true,
        handler(newValue) {
            this.closeAllModals();
        }
    },
    showModal: {
        deep: true,
        handler(newValue) {
            this.$emit('updateIsOpen', newValue);
        }
    },
    isMessageDialogVisible: {
        deep: true,
        handler(newValue) {
            this.$emit('updateIsOpen', newValue);
        }
    },
    open_confirmBox: {
        deep: true,
        handler(newValue) {
            this.$emit('updateIsOpen', newValue);
        }
    },
    open_name: {
        deep: true,
        handler(newValue) {
            this.$emit('updateIsOpen', newValue);
        }
    },
    open_formField: {
        deep: true,
        handler(newValue) {
            this.$emit('updateIsOpen', newValue);
        }
    },
    show_mobile_pattern: {
        deep: true,
        handler(newValue) {
            this.$emit('updateIsOpen', newValue);
        }
    },
};
</script>

<style scoped>
.blur-sm {
    filter: blur(1.5px);
    /* Adjust the blur intensity as needed */
}

/* Additional styles for the connector */
.connector {
    width: 100%;
    height: 4px;
}

/* .custom-checkbox {
    position: relative;
    width: 18px;
    height: 18px;
    border: 2px solid rgb(71, 126, 245);
    border-radius: 3px;
    cursor: pointer;
}

.custom-checkbox.checked {
    background-color: rgb(71, 126, 245);
} */

.tick-symbol {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
}

/* Tooltip styling */
.custom-checkbox:hover::before {
    content: attr(title01);
    position: absolute;
    top: 150%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.custom-toggle {
    position: relative;
    cursor: pointer;
}

.custom-toggle:hover::before {
    content: attr(title02);
    position: absolute;
    top: 150%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

/*---what happening---*/
/* Add your styling here using Tailwind CSS classes or any other styles */
/* Example styling for the custom radio box */
.custom-radio-box {
    display: inline-block;
    padding: 5px;
    /* margin: 0px; */
    cursor: pointer;
    /* border: 1px solid #ccc; */
    /* border-radius: 5px; */
    transition: background-color 0.3s ease-in-out;
}

/* Additional styling for the selected option */
.selected-option {
    background-color: #228B22;
    /* Change to the color you want for the selected option */
    color: #fff;
    /* Change to the text color you want for the selected option */
}


.close-icon {
    cursor: pointer;
    position: absolute;
    top: 3px;
    right: 3px;
    font-size: 14px;
    color: #999;
}

/*-----dropdown multiple--- */
</style>