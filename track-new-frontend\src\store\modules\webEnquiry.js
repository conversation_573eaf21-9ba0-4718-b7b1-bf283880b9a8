// store/modules/webEnquiry.js
import axios from "axios";

const state = {
  enquiry_list: {},
  //---data from search---
  data: [],
  pagination: {},
  pagination_data: [],
  get_next_page: false,
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  previousPerPage: null,
  };

  const mutations = {
      SET_ENQUIRYLIST(state, { data, pagination, status}) {
        //   console.log(data, 'data', pagination, 'pagination', status_counts, 'status');
          state.enquiry_list = {data: data, pagination: pagination, status};
    },
    SET_ENQUIRYLISTDATA(state, { data, pagination, status, filter_data }) {
      if (data.length > 0 && pagination.current_page > 1) {
        state.data = [...state.data, ...data];
        state.pagination = { ...pagination };
      } else {
        state.data = [...data];
        state.pagination = { ...pagination };
      }
    
      // Filter data and update pagination_data
      if (filter_data) {
        const { from, to, type, page, per_page } = filter_data;
    
        state.pagination_data = state.data.filter((item) => {
          let isValid = true;
    
          if (from && to) {
            const itemDate = new Date(item.created_at); // Assuming the date field is named 'date'
            const fromDate = new Date(from);
            const toDate = new Date(to);
            isValid = isValid && itemDate >= fromDate && itemDate <= toDate;
          }
    
          if (type) {
            isValid = isValid && item.type === type;
          }
    
          return isValid;
        });
        // If no filter is provided, show data for the current page
        const start = (page - 1) * per_page;
        const end = start + per_page;    
        state.pagination_data = state.data.slice(start, end);
        if (state.pagination_data.length < per_page) {
          state.get_next_page = true;
        }
      } else {
        state.pagination_data = [];
      }
    },
    UPDATE_NEXTPAGE(state) {
      state.get_next_page = false;
    },    
    RESET_STATE(state) {
      state.data = [];
      state.pagination = {};
      state.pagination_data = [];
      state.enquiry_list = {};
      state.get_next_page = false;
      state.lastFetchTime = null;
      state.isFetching = false;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    SET_PREVIOUS_PER_PAGE(state, perPage) {
      state.previousPerPage = perPage; // Store the previous per_page value
    },

  };

  const actions = {
    updateLeadName({ commit }, lead_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update enquiry_list name
      setTimeout(() => {
        // Commit mutation to update enquiry_list name
        commit('SET_ENQUIRYLIST', lead_listData);
      }, 1000);
    },
    async fetchEnquiryList({ state, commit, rootState }, { page, per_page }) {
      const now = new Date().toISOString();
    const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['enquires_update']; 
      if (state.previousPerPage != per_page) {
        commit('SET_PREVIOUS_PER_PAGE', per_page); // Update the per_page tracking
        commit('SET_LAST_FETCH_TIME', null); // Force a new request if per_page changed
      }
    // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
    if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)  {
      return; // Skip request if less than 30 seconds have passed since the last request
    }
      try {        
          const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          commit('SET_IS_FETCHING', true);
            axios.get('enquires', { params: { company_id: company_id, page: page, per_page: per_page } })
              .then(response => {
                // Handle response
                // console.log(response.data, 'enquiry list..!');
                let { data, pagination, status } = response.data;
                commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
                commit('SET_IS_FETCHING', false);
                if (data && data.length > 0) {
                  commit('SET_ENQUIRYLIST', { data, pagination, status });                 
                  return data;
                } else {
                  commit('SET_ENQUIRYLIST', { data: [], pagination: { page: 1, total: 0, per_page: 10 }});
                  return;
                }
              })
              .catch(error => {
                // Handle error
                commit('SET_IS_FETCHING', false);
                console.error('Error:', error);
                return error;
              });          
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    //---get enquiry list--
    async fetchEnquiryListData({ state, commit, rootState }, { page, per_page, filter_data }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 30 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['enquires_update']; 
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime) {
        return; // Skip request if less than 30 seconds have passed since the last request
      }
      try {
        if ((state.pagination.last_page > page  && state.pagination.current_page !== page)|| Object.keys(state.pagination).length === 0) {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
          if (company_id && company_id !== '') {       
            commit('SET_IS_FETCHING', true);
          axios.get('enquires', { params: { company_id: company_id, page: page, per_page: per_page } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'enquiry list data..!');
                let {data, pagination, status} = response.data; 
                commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
                commit('SET_IS_FETCHING', false);
              if (data && data.length > 0) {
                commit('UPDATE_NEXTPAGE');
                commit('SET_ENQUIRYLISTDATA', { data, pagination, status, filter_data });              
                return data;
              } else {
                commit('SET_ENQUIRYLISTDATA', { data: [], pagination:{page: 1, total: 0, per_page: 10}, filter_data });
                return;
              }
            })
            .catch(error => {
              // Handle error
              commit('SET_IS_FETCHING', false);
              console.error('Error:', error);
              return error;
            });
          }  
        } else {
            
        }
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    
  };

  const getters = {
    currentEnquiryList(state) {
      return state.enquiry_list;
    },
    filteredEnquiryList(state) {
      return state.pagination_data;
    },
    isNextPage(state) {
      return state.get_next_page;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
