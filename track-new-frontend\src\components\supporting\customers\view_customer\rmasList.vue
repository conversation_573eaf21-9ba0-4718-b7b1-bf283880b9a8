<template>
    <div>
        <div v-if="!open_skeleton && repairs.length > 0" class="text-sm" :class="{ 'm-4': !isMobile }">
            <div
                :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="border-b border-gray-400">
                                <!--dynamic-->
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    :class="{ 'hidden': !column.visible }" class="py-2 text-left px-2">
                                    <p>{{ column.label }}</p>
                                </th>
                                <th class="leading-none text-center">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(record, index) in repairs" :key="index"
                                class="border-b border-gray-400 cursor-pointer hover:bg-gray-200">
                                <td v-for="(column, colIndex) in columns" :key="colIndex"
                                    :class="{ 'hidden': !column.visible }" class="px-1 py-1 text-left">
                                    <span v-if="column.field === 'created_at'">{{
                                        validateDateTime(record[column.field]) ?
                                            calculateDaysAgo(formattedDate(record[column.field])) : '' }}</span>
                                    <span v-if="column.field === 'created_date'">
                                        {{ validateDateTime(record['created_at']) ?
                                            formatDateTime(record['created_at']) : '' }}</span>
                                    <span v-if="column.field === 'cost'">
                                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }} {{ record['total_cost'] }}</span>
                                    <span v-if="column.field === 'complete_date' && record['complete_date']">
                                        {{ validateDateTime(record[column.field]) ?
                                            formatDateTime(record['complete_date']) : '' }}</span>
                                    <!-- <span v-if="column.field === 'customer_id'"
                                        class="text-sky-700 hover:underline cursor-pointer"
                                        @click="viewRecordCustomer(record['customer'])">{{
                                            record['customer'].first_name
                                            + ' - ' + record['customer'].contact_number }}</span> -->
                                    <span v-if="column.field === 'payment_amount'">{{ currentCompanyList &&
                                        currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency
                                    }}
                                        {{
                                            record['payment_amount'] }}</span>
                                    <span v-if="column.field === 'payment_type'">{{ record['payment_type']
                                    }}</span>
                                    <span v-if="column.field === 'product_id'">{{ record['product'] &&
                                        record['product'].product_name ? record['product'].product_name : ''
                                    }}</span>
                                    <span
                                        v-if="column.field !== 'payment_type' && column.field !== 'created_at' && column.field !== 'rma_status' && column.field !== 'customer_id' && column.field !== 'complete_date' && column.field !== 'cost' && column.field !== 'product_id'">{{
                                            record[column.field] }}</span>
                                    <span v-if="column.field === 'rma_status'" @click="startEdit(record)"
                                        class="py-1 px-2 cursor-pointer rounded text-xs"
                                        :class="options[record['rma_status']] && options[record['rma_status']]['class'] ? options[record['rma_status']]['class'] : ''">
                                        {{ options[record['rma_status']] && options[record['rma_status']]['label'] ?
                                            options[record['rma_status']]['label'] : '' }}
                                    </span>
                                </td>
                                <td class="py-1 text-center">
                                    <div class="flex justify-center">
                                        <!--More actions-->
                                        <div class="flex relative">
                                            <button v-if="!record.editing" @click="startEdit(record)" title="Edit"
                                                class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                            </button>
                                            <button v-if="!record.editing && checkRoles(['admin'])" title="Delete"
                                                @click="confirmDelete(index)"
                                                class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                    style="color: #ef4444" />
                                            </button>
                                            <!-- <button @click.stop="displayAction(index)"
                                                class="px-2 py-1 text-blue-800 rounded"
                                                :class="{ 'bg-blue-100': display_option === index }">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                            </button> -->
                                        </div>
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-8 absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">

                                                <li :class="{ 'hidden': record.status === '1' }"
                                                    class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="startEdit(record)"
                                                        class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" size="lg" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li v-if="index === 0" :class="{ 'hidden': record.status === '1' }"
                                                    class="hover:bg-gray-200">
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                        <span class="px-2">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>

                                </td>
                            </tr>
                            <tr class="items-center justify-center flex border" v-if="!repairs || repairs.length === 0">
                                <td v-if="Object.keys(filteredBy).length === 0" colspan="5">
                                    <button class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                        @click="openPayment">
                                        + Create Repair
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--card view-->
                <div v-if="items_category === 'list'" class="grid grid-cols-1 custom-scrollbar-hidden" :class="{
                    'sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4': showContactInfo == undefined,
                    'sm:grid-cols-1  lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-2 lg:gap-4': showContactInfo == false,
                    'sm:grid-cols-1  lg:grid-cols-1 xl:grid-cols-1 2xl:grid-cols-2 gap-2 lg:gap-4': showContactInfo
                }">
                    <div v-for="(record, index) in repairs" :key="index" class="w-full">
                        <div class="max-w-md mx-auto shadow-lg bg-white rounded-xl border border-gray-300 md:max-w-2xl">
                            <!-- Top Section -->
                            <div class="flex justify-between items-center px-4 py-2">
                                <!-- Left Side (Can be your dynamic content) -->
                                <div class="text-xs text-red-500 items-center">
                                    <p>{{
                                        validateDateTime(record['created_at']) ?
                                            calculateDaysAgo(formattedDate(record['created_at'])) : '' }}</p>
                                </div>
                                <!-- Right Side (Actions) -->
                                <div class="flex space-x-4 relative items-center">
                                    <!--status-->
                                    <div v-if="record.rma_status >= 0" class="text-xs">
                                        <span class="py-1 px-2 rounded line-clamp-1 cursor-pointer"
                                            @click="startEdit(record)"
                                            :class="options[record['rma_status']] && options[record['rma_status']]['class'] ? options[record['rma_status']]['class'] : ''">
                                            {{ options[record['rma_status']] && options[record['rma_status']]['label'] ?
                                                options[record['rma_status']]['label'] : '' }}
                                        </span>
                                    </div>
                                    <div class="flex justify-center">
                                        <!--More actions-->
                                        <div class="relative">
                                            <button @click.stop="displayAction(index)"
                                                class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                            </button>
                                        </div>
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-8 absolute bg-slate-200 divide-y divide-gray-100 right-0 rounded-lg shadow-lg items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">

                                                <li :class="{ 'hidden': record.status === '1' }">
                                                    <button v-if="!record.editing" @click="startEdit(record)"
                                                        class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" size="lg" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li v-if="index === 0" :class="{ 'hidden': record.status === '1' }">
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                        <span class="px-2">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="px-4 py-2">
                                <!-- Customer Details (Can be your dynamic content) -->
                                <div class="flex items-center -mt-4">
                                    <!-- <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                        :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-lime-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-blue-600': index === 0 }">
                                        {{ record.customer && record.customer.first_name ?
                                            record.customer.first_name[0].toUpperCase() :
                                            'C' }}
                                    </div> -->
                                    <!-- <div>
                                        <h4 class="leading-6 font-semibold text-gray-900 cursor-pointer"
                                            @click="viewRecordCustomer(record['customer'])">
                                            {{ record.customer && record.customer.first_name ?
                                                record.customer.first_name + ' ' + (record.customer.last_name ?
                                                    record.customer.last_name : '') : '' }}</h4>
                                        <p class="text-gray-500 cursor-pointer text-sm"
                                            @click="dialPhoneNumber(record.customer.contact_number)">+91-{{
                                                record.customer.contact_number }}</p>
                                    </div> -->
                                    <!-- <div>Customer: {{ record['customer_id'] }}</div> -->
                                </div>
                                <div class="flex justify-between items-center py-1">
                                    <div>
                                        <p> <span class="font-semibold">Product:</span>
                                            {{ record.product && record.product['product_name'] ?
                                                record.product['product_name'] : '' }}
                                        </p>
                                    </div>
                                    <div>
                                        <p><span class="font-semibold">Serial no:</span> {{
                                            record['serial_number'] }}</p>
                                    </div>
                                </div>
                            </div>
                            <!--rma data-->
                            <div class="flex justify-between items-center px-4 py-1">
                                <div>
                                    <p>#{{ record['rma_id'] }}</p>
                                </div>
                                <div>
                                    <p><span class="font-semibold"> {{ currentCompanyList && currentCompanyList.currency
                                        === 'INR' ? '\u20b9' : currentCompanyList.currency }} {{
                                                record['total_cost']
                                            }}</span></p>
                                </div>
                                <!--date data-->
                                <!-- <div>
                                        <p class="pr-1 text-gray-400">Payment Type: </p>
                                        <p>{{ record['payment_type'] }} </p>
                                    </div>
                                    <div>
                                        <p class="pr-1 text-gray-400">Amount:</p>
                                        <p class="text-green-700">
                                            {{currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency}} {{ record['payment_amount'] }}</p>
                                    </div> -->
                            </div>
                            <!--problem description-->

                        </div>
                    </div>
                    <!--no data found-->
                    <div class="text-sm mb-[100px]">
                        <p class="font-bold text-center py-2 text-green-700">
                            <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                            Finished !
                        </p>
                    </div>
                </div>

            </div>
        </div>
        <!--in case empty-->
        <div v-if="!open_skeleton && repairs && repairs.length === 0">
            <div class="flex justify-center items-center">
                <img class="w-64 h-64" :src="empty_data" alt="image empty states">
            </div>
            <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
        </div>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>
<script>
import { mapState, mapActions, mapGetters } from 'vuex';
export default {
    props: {
        isMobile: Boolean,
        data: Object,
        companyId: String,
        userId: String,
        selected_category: String,
        items_category: String,
        open_skeleton: Boolean,
        now: Date,
        confirm_del: Boolean,
        customer_data: Object,
        updateModalOpen: Boolean,
        currentCompanyList: Object,
        showContactInfo: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options_page: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            columns: [],
            open_confirmBox: false,
            deleteIndex: null,
            //--skeleton
            number_of_columns: 5,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            open_skeleton_isMobile: false,
            display_option: null,
            open_skeleton_isMobile: false,
            filteredBy: {},
            isDropdownOpen: false,
            //-------------
            openModalRegister: false,
            filter: 'all',
            search: '',
            quickFilter: '',
            formValues: {},
            repairs: [],
            pagination: {},
            quick_filter: false,
            filter_value: 'all',
            filter_option: ['Opened last 30 days', 'All unfinished', 'All my completed', 'All my unfinished', 'All mine', 'All outstanding'],
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            options: [
                { value: 1, label: 'Awaiting Customer Confirmation', class: 'bg-green-500', labelClass: 'ml-2' },
                { value: 2, label: 'Awaiting Parts', class: 'bg-purple-500 text-white', labelClass: 'ml-2' },
                { value: 3, label: 'Awaiting Repair', class: 'bg-orange-500', labelClass: 'ml-2' },
                { value: 4, label: 'Awaiting Supplier', class: 'bg-[#ffb6c1]', labelClass: 'ml-2' },
                { value: 5, label: 'Awaiting to be sent to Supplier', class: 'bg-indigo-500 text-white', labelClass: 'ml-2' },
                { value: 6, label: 'Credit', class: 'bg-gray-300', labelClass: 'ml-2' },
                { value: 7, label: 'Ready to Deliver', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 8, label: 'Repair Completed', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 9, label: 'Repair in Process', class: 'bg-[#87b14b]', labelClass: 'ml-2' },
                { value: 10, label: 'Repaired/Replacement from Supplier', class: 'bg-[#ff7f50]', labelClass: 'ml-2' },
                { value: 11, label: 'Sent to Customer', class: 'bg-blue-600 text-white', labelClass: 'ml-2' },
                { value: 12, label: 'Sent to Supplier', class: 'bg-[#ffc0cb]', labelClass: 'ml-2' },
                { value: 13, label: 'Waiting New Battery', class: 'bg-red-600 text-white', labelClass: 'ml-2' },
                { value: 14, label: 'Delivered', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
            ],
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        filteredRepairs() {
            let filtered = this.repairs;

            if (this.filter !== 'all') {
                filtered = filtered.filter(repair => repair.status === this.filter);
            }

            if (this.search) {
                filtered = filtered.filter(repair =>
                    Object.values(repair).some(value =>
                        String(value).toLowerCase().includes(this.search.toLowerCase())
                    )
                );
            }

            const start = (this.pagination.page - 1) * this.pagination.perPage;
            const end = start + this.pagination.perPage;
            this.pagination.start = start + 1;
            this.pagination.end = end;

            return filtered.slice(start, end);
        },
        paginatedData() {
            if (this.repairs && this.repairs.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.repairs
                this.length_category = filteredData.length;
                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.repairs && this.repairs.length !== 0) {
                const totalFilteredRecords = this.repairs.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const record = ['created_at', 'rma_id', 'rma_status', 'created_date', 'product_id', 'serial_number', 'complete_date', 'problem_description', 'cost'];
            const fields = [];
            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.repairs) {
                for (const key of record) {
                    if (key !== 'id' && key !== 'items' && key !== 'data' && key !== 'company' && key !== 'assign_to' && key !== 'status') { // Exclude the 'id' field
                        const label = formatLabel(key);
                        fields.push({ label, field: key, visible: true });
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
    },
    methods: {
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        statusClass(status) {
            switch (status) {
                case 'Repair Completed':
                    return 'bg-green-200 text-green-800 px-2 py-1 rounded';
                case 'Awaiting Customer Confirmation':
                    return 'bg-yellow-200 text-yellow-800 px-2 py-1 rounded';
                case 'Sent to Customer':
                    return 'bg-blue-200 text-blue-800 px-2 py-1 rounded';
                case 'Awaiting to be sent to Supplier':
                    return 'bg-purple-200 text-purple-800 px-2 py-1 rounded';
                case 'Awaiting Supplier':
                    return 'bg-pink-200 text-pink-800 px-2 py-1 rounded';
                case 'Awaiting Repair':
                    return 'bg-red-200 text-red-800 px-2 py-1 rounded';
                default:
                    return 'bg-gray-200 text-gray-800 px-2 py-1 rounded';
            }
        },
        addRepair() {
            // Add your logic to add a new repair
            // console.log('Add repair');
            this.openModalRegister = true;
        },
        prevPage() {
            if (this.pagination.page > 1) {
                this.pagination.page--;
            }
        },
        nextPage() {
            const totalPages = Math.ceil(this.repairs.length / this.pagination.perPage);
            if (this.pagination.page < totalPages) {
                this.pagination.page++;
            }
        },
        //---quick filter data---
        quickFilterdata() {
            this.quick_filter = !this.quick_filter;
        },
        handleClickOutside(event) {
            if (this.$refs.dropdownContainer && !this.$refs.dropdownContainer.contains(event.target)) {
                this.quick_filter = false;
            }
        },
        selectedOptions(type) {
            if (type) {
                this.filter_value = type;
                this.quick_filter = false;
            }
        },
        navigateToEdit(repair) {
            if (repair.id) {
                let type = 'edit';
                this.$router.push({ path: `/openrma/${repair.id}/${type}` });
            }
        },
        validateDateTime(dateTimeString) {
            const datePart = dateTimeString.substring(0, 10);
            const isValidDate = /^\d{4}-\d{2}-\d{2}$/.test(datePart);
            return isValidDate;
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            axios.get('/rmas', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.repairs = [...this.repairs, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        visiblePageNumbers() {
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            if (this.display_option >= 0 && this.$refs['dropdown' + this.display_option][0]) {
                const dropdownRef = this.$refs['dropdown' + this.display_option][0];
                // console.log(dropdownRef, 'What happening......!!!', event.target);
                if (dropdownRef && !dropdownRef.contains(event.target)) {
                    this.display_option = null; // Close dropdown
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutside);
                }
            }
        },
        //---validate the role
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.open_loader = true;
                axios.delete(`/rmas/${this.repairs[this.deleteIndex].id}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        this.deleteIndex = null;
                        this.open_loader = false;
                        window.location.reload();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
            this.$emit('openconfirmbox');
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        getRmaList(per_page, page) {
            if (page == 1) {
                this.fetchRmasList({ page, per_page });
            } else {
                this.open_skeleton = true;
                axios.get('/rmas', { params: { company_id: this.companyId, per_page: per_page, page: page } })
                    .then(response => {
                        // console.log(response.data, 'estimation by list..!');
                        this.open_skeleton = false;
                        this.repairs = response.data.data;
                        this.pagination = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        //--get formayt date
        formatDate(dateString) {
            // Create a new Date object using the components in the correct order (year, month - 1, day)
            const date = new Date(dateString);
            // Get the parts of the date (year, month, day) in the correct format
            const formattedYear = date.getFullYear();
            const formattedMonth = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-indexed
            const formattedDay = String(date.getDate()).padStart(2, '0');
            // Concatenate the parts with dashes to form the desired format
            return `${formattedMonth}/${formattedDay}/${formattedYear}`;
        },
        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;
            if (this.isDropdownOpen) {
                // Add event listener when dropdown is opened
                document.addEventListener('click', this.handleOutsideClickdata);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClickdata);
            }
        },
        handleOutsideClickdata(event) {
            const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.toggleDropdown();
            }
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },
        refreshPage() {
            this.$router.push({ name: 'open_rma_home' });
        },
        startEdit(record_data) {
            this.$router.push({ name: 'open_rma_edit', params: { rmaId: record_data.id, type: 'edit' } });
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },

    },
    mounted() {
        if (this.data && this.data.length > 0) {
            this.repairs = [...this.data];
            // console.log(this.data, 'What happnignnnnnn');
        }

    },

    watch: {
        data: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'Waht jjjjjjjjjjjjjjjjjjjjjjjjjj');
                if (newValue && newValue.length > 0) {
                    this.repairs = [...newValue];
                }
            }
        },
        confirm_del: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.deleteRecord();
                } else {
                    this.cancelDelete();
                }
            }
        }

    }
};
</script>
<style scoped>
.material-icons {
    font-size: 24px;
}

.manualStyle {
    overflow: auto;
    height: 100vh;
}

info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
    z-index: 100;
}
</style>