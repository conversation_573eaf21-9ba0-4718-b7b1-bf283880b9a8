<template>
    <div class="flex relative mt-1 overflow-hidden">
        <!-- Input field to display the selected date -->
        <Flatpickr v-model="selectedDate" placeholder="Select Date"
            class="text-sm p-1 border border-gray-300 w-full outline-none focus:border-blue-500 rounded-l rounded-r-none pr-10"
            :config="flatpickrConfig" ref="flatpickrInput" :minDate="min" :maxDate="max"
            :enableTime="options.enableTime" :dateFormat="options.dateFormat" />
        <!-- Calendar Icon Button on the right -->
        <button type="button" @click="openDatePicker($event)"
            class="p-1 border border-l-0 border-gray-300 rounded-r-md bg-white">
            <i class="fas fa-calendar-alt text-lg"></i> <!-- Calendar Icon -->
        </button>
    </div>
</template>

<script>
import { ref, watch, onMounted, nextTick } from 'vue';
import Flatpickr from 'vue-flatpickr-component';
import 'flatpickr/dist/flatpickr.css';  // Import Flatpickr styles

export default {
    name: 'GlobalDatePicker',
    components: {
        Flatpickr,
    },
    props: {
        modelValue: {
            type: [String, Date],
            default: null
        },
        config: {
            type: Object,
            default: () => ({}),
        },
        min: {
            type: [String, Date],
            default: null,
        },
        max: {
            type: [String, Date],
            default: null,
        },
        options: {
            type: Object,
            default: () => ({
                enableTime: false,  // Default setting
                dateFormat: 'd/m/Y', // Default format for date input
            }),
        },
    },

    setup(props, { emit }) {

        let selectedDate = ref(props.modelValue);
        let value = props.modelValue;
        if (value && value !== '') {
            if (props.options.dateFormat == 'Y-W') {
                if (value) {
                    const [weekYear, weekNumber] = value.split('-');
                    value = `${weekYear}-${weekNumber.replace('W', '')}`;
                    selectedDate._value = value;
                }
            } else if (props.options.dateFormat == 'd-m-Y') {
                // Handle 'd-m-Y' format
                if (value) {
                    const parts = value.split(/[-]/);
                    if (parts.length === 3) {
                        const [year, month, day] = parts;
                        value = `${day}-${month}-${year}`;
                        selectedDate._value = value;
                    }
                }
            } else if (props.options.dateFormat == 'd-m-Y H:i') {
                if (value) {
                    if (value instanceof Date) {
                        value = value.toISOString();
                    }
                    const [datePart, timePart] = value.split('T');
                    const [year, month, day] = datePart.split('-');
                    const [hours, minutes] = timePart.split(':');
                    value = `${day}-${month}-${year} ${hours}:${minutes}`;
                    selectedDate._value = value;
                }
            }
        }
        // Check if the device is mobile and force the altInput
        const isMobile = /Android|iPhone|iPad/i.test(navigator.userAgent);
        // Force input type to 'text' on mobile to prevent the native picker from showing
        const inputType = isMobile ? 'text' : props.options.enableTime ? 'datetime-local' : 'date';
        // Dynamically determine Flatpickr configuration based on options passed
        const flatpickrConfig = {
            ...props.config,
            clearButton: true,
            dateFormat: props.options.dateFormat || 'd/m/Y',
            enableTime: props.options.enableTime,
            minDate: props.min,
            maxDate: props.max,
            altInput: false,
            altFormat: 'F j, Y',
            disableMobile: true,
            noCalendar: props.options.dateFormat == 'H:i',
        };

        const flatpickrInstance = ref(null); // Reference to Flatpickr instance

        // Watch for changes in modelValue to update selectedDate
        watch(() => props.modelValue, (newVal) => {
            selectedDate.value = newVal;
        });
        watch(() => selectedDate.value, (newVal) => {
            if (newVal !== props.modelValue) {
                updateDate(newVal);  // Emit the new value to the parent
            }
        });

        // Watch for changes in min and max props to update the Flatpickr instance
        watch(
            () => props.min,
            (newMin) => {
                if (flatpickrInstance.value) {
                    flatpickrInstance.value.set('minDate', newMin);
                }
            },
            { immediate: true }
        );

        watch(
            () => props.max,
            (newMax) => {
                if (flatpickrInstance.value) {
                    flatpickrInstance.value.set('maxDate', newMax);
                }
            },
            { immediate: true }
        );

        // Emit updated value to parent
        const updateDate = (newValue) => {
            selectedDate.value = newValue;
            emit('update:modelValue', newValue);
        };

        // Open the date picker when the button is clicked
        const openDatePicker = (event) => {
            // Get the closest input field from the clicked button
            const closestInput = event.target.closest("div").querySelector(".flatpickr-input");
            if (closestInput) {
                closestInput.focus(); // Focus the corresponding input field
            }
        };

        // Once component is mounted, get reference to Flatpickr instance
        onMounted(() => {
            nextTick(() => {
                flatpickrInstance.value = document.querySelector('.flatpickr-input');
            });
        });

        return {
            selectedDate,
            flatpickrConfig,
            updateDate,
            openDatePicker,
            flatpickrInstance,
        };
    },
};
</script>

<style scoped>
/* Positioning for the button inside the input field */
button {
    cursor: pointer;
}

button:hover {
    background-color: #f0f0f0;
}

input {
    padding-right: 40px;
    /* Ensure the button does not overlap with input text */
}
</style>