<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-gray-300 w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    Update Company / Shop / Business Details
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block text-sm bg p-2">
                <div class="w-full max-w-lg  p-4 rounded-lg"
                    :class="{ 'shadow-md border-t border-white shadow-white': !isMobile }">

                    <div v-if="page === 1" class="flex">
                        <button @click="page = 0" class="justify-center"><span
                                class="text-xl font-bold px-1 mr-1">&#8592;</span>back</button>
                    </div>
                    <div v-if="page === 0" class="flex mt-5 mb-3">
                        <!-- Company Name Label -->
                        <!-- <label for="companyName" class="absolute text-md font-bold">
                        Company Name<span class="text-red-700">*</span>
                    </label> -->
                        <!-- Company Name Input Field &#127970;-->
                        <p class="absolute mt-3 ml-1">
                            <font-awesome-icon icon="fa-solid fa-building" class="text-green-700" />
                        </p>
                        <input type="text" id="companyName" v-model="formValues.company_name" name="companyName"
                            ref="company_name" class="mt-1 p-2 px-8 border border-gray-300 w-full"
                            placeholder="Company / shop / business name" />
                    </div>
                    <div v-if="page === 0" class="flex mt-5 mb-3">
                        <!-- <label for="gst_number" class="block text-md font-bold">GST Number<span
                            class="text-red-700">*</span></label> -->
                        <p class="font-bold text-green-700 absolute mt-[15px] ml-1 text-xs">GST</p>
                        <!--@input="validateGST"-->
                        <input type="text" id="gst_number" v-model="formValues.gst_number"
                            class="mt-1 p-2 px-8 border border-gray-300 w-full"
                            :class="{ 'outline-red-700': formValues.gst_number !== '' && gstValidation !== '' }"
                            placeholder="GST Number (optional)" />
                        <p v-if="formValues.gst_number !== '' && gstValidation !== ''"
                            class="text-red-500 absolute text-xs mt-10 py-2">{{ gstValidation }}</p>
                    </div>
                    <!-- Business Contact Number -->
                    <div v-if="page === 0" class="flex mt-5 mb-3">
                        <!-- <label for="business_contact" class="block text-md font-bold">Business Contact Number<span
                            class="text-red-700">*</span></label> &#128222;-->
                        <p class="absolute mt-3 ml-1 text-green-700"><font-awesome-icon icon="fa-solid fa-phone" /></p>
                        <input type="tel" id="business_contact" v-model="formValues.company_phone_no"
                            @input="validatePhoneNumber(formValues.company_phone_no)" @keyup.enter="saveData"
                            class="mt-1 p-2 px-8 border border-gray-300 w-full"
                            :class="{ 'outline-red-700': validationMessage !== '' }"
                            placeholder="Business Contact Number" />
                        <p v-if="validationMessage !== ''" class="absolute mt-10 py-2 text-xs text-red-500">
                            {{ validationMessage }}</p>
                    </div>
                    <!--Name -->
                    <div v-if="page !== 0" class="flex mt-5 mb-3">
                        <p class="absolute mt-3 ml-1">
                            &#129333;
                            <!-- <font-awesome-icon icon="fa-solid fa-user-tie" class="text-green-700" /> -->
                        </p>
                        <!-- <label for="name" class="block text-md font-bold">Name<span class="text-red-700">*</span></label> -->
                        <input type="text" id="name" v-model="formValues.name" ref="user_name"
                            class="mt-1 p-2 px-8 border border-gray-300 w-full" placeholder="Your Name" />
                    </div>
                    <!--Address -->
                    <div v-if="page !== 0" class="flex mt-5 mb-3">
                        <p class="absolute mt-3 ml-1">
                            <!-- <font-awesome-icon icon="fa-solid fa-house" class="text-green-700" /> -->
                            &#127968;
                        </p>
                        <!-- <label for="name" class="block text-md font-bold">Name<span class="text-red-700">*</span></label> -->
                        <textarea v-model="formValues.address" class="mt-1 p-2 px-8 border border-gray-300 w-full"
                            placeholder="Address" rows="2"></textarea>
                    </div>
                    <!--Email ID -->
                    <div v-if="page !== 0" class="flex mt-5 mb-3">
                        <!-- <label for="email" class="block text-md font-bold">Email ID<span
                            class="text-red-700">*</span></label> -->
                        <p class="absolute mt-3 ml-1">
                            &#128231;
                            <!-- <font-awesome-icon icon="fa-regular fa-envelope" class="text-green-700" /> -->
                        </p>
                        <input type="email" id="email" v-model="formValues.email" @input="validateEmail"
                            class="mt-1 p-2 px-8 border border-gray-300 w-full" placeholder="Login Email ID" />
                        <p v-if="emailError" class="absolute text-xs mt-10 py-2 text-red-500">{{ emailError }}</p>
                    </div>
                    <!--Password -->
                    <div v-if="page !== 0" class="flex mt-5 mb-3">
                        <!-- <label for="password" class="block text-md font-bold">Password<span
                            class="text-red-700">*</span></label> -->
                        <!-- <font-awesome-icon icon="fa-solid fa-key" class="text-green-700" /> -->
                        <p class="absolute mt-3 ml-1">&#128274;</p>
                        <input type="password" v-model="formValues.password"
                            class="mt-1 p-2 px-8 border border-gray-300 w-full" placeholder="Create new password" />
                        <p v-if="formValues.password && formValues.password !== '' && formValues.password.length < 8"
                            class="text-xs text-red-500 absolute mt-10 py-2">Password length minimum 8 characters</p>
                        <p v-if="formValues.password && formValues.password.length >= 8"
                            class="text-xs text-green-500 absolute mt-10 py-2">Password valid</p>
                    </div>
                    <!--confirm Password -->
                    <div v-if="page !== 0" class="flex mt-5 mb-3">
                        <!-- <label for="confirm_password" class="block text-md font-bold">Confirm Password<span
                            class="text-red-700">*</span></label> -->
                        <!-- <font-awesome-icon icon="fa-solid fa-check-double" class="text-green-700" /> -->
                        <p class="absolute mt-3 ml-1">
                            &#128274;
                        </p>
                        <input type="password" v-model="formValues.confirm_password" @keyup.enter="saveData"
                            class="mt-1 p-2 px-8 border border-gray-300 w-full" placeholder="Confirm the password" />
                        <p v-if="formValues.password && formValues.password.length >= 8 && formValues.confirm_password !== formValues.password"
                            class="text-xs text-red-500 absolute mt-10 py-2">Please enter valid password</p>
                    </div>
                    <div class="flex justify-center mt-5 mb-3">
                        <button @click="saveData"
                            class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 text-sm w-3/4 text-white px-7 py-2 rounded hover:bg-green-600">
                            {{ page === 0 ? 'Next' : 'Save' }}
                        </button>
                    </div>
                    <!-- <div class="flex justify-center mt-5 text-white">
                        <p>Already registered <button @click="$router.push('/login')"
                                class="text-blue-500 hover:text-blue-700">Login</button></p>
                    </div> -->
                </div>
                <!--Help-->
                <!-- <div v-if="!isMobile" class="relative">
                    <div
                        class=" text-white w-full fixed bottom-0 left-0 text-center text-xs text-gray-700 flex justify-center py-2 items-center">
                        <p>Created By: <span class="mx-1"><font-awesome-icon icon="fa-solid fa-heart" size="xl"
                                    color="red" /></span><a href="https://eagleminds.net/" target="_blank"
                                class="text-white hover:underline ml-1">Eagleminds Technologies pvt.ltd</a>
                        </p>
                        <button @click="openSupport"
                            class="px-4 py-1 rounded border border-white ml-10"><font-awesome-icon
                                icon="fa-solid fa-headset" /> Support</button>
                    </div>
                </div> -->


            </div>
        </div>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import dialogAlert from './dialogAlert.vue';
import support from './support.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    props: {
        showModal: Boolean,
    },
    components: {
        dialogAlert,
        support
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            formValues: {},
            circle_loader: false,
            open_message: false,
            message: '',
            emailError: '',
            validationMessage: '',
            gstValidation: '',
            default_upload: '/images/setting_page/Cloud_computing.png',
            logo_img: '/images/head_bar/logo.png',
            //----
            companyId: null,
            userId: null,
            page: 0,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'warning',
            show_support: false,
            isMobile: false
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        validatePhoneNumber(inputtxt) {
            // Regular expression for phone numbers
            var phoneno = /^(?:0|\+91)?[6-9]\d{9}$/;

            // Check if the input matches the regular expression
            if (inputtxt.match(phoneno)) {
                this.validationMessage = '';
                return true; // Phone number is valid
            } else {
                // alert("Invalid phone number format. Please enter a valid phone number.");
                // this.message = "Invalid phone number format. Please enter a valid phone number.";
                // this.open_message = true;
                this.validationMessage = 'Enter valid contact number';
                return false; // Phone number is invalid
            }
        },
        //--validate GST--
        validateGST() {
            // console.log(this.formValues.gst_number, 'WWWWWWW');
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.formValues.gst_number !== '') {
                // Check if the input matches the expected format
                if (regex.test(this.formValues.gst_number.toUpperCase())) {
                    this.gstValidation = '';
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                }
            }
        },
        //---validate email--
        validateEmail() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(this.formValues.email)) {
                this.emailError = 'Please enter a valid email address.';
            } else {
                this.emailError = '';
            }
        },
        handleFileChange(event) {
            this.circle_loader = true;
            const file = event.target.files[0];
            if (file) {
                // Assuming you are using URL.createObjectURL to display the image
                this.uploadImageProfile(file);
            }
        },
        uploadImageProfile(file) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "expense");
            formData.append("company_id", this.companyId);
            // Make an API request to Laravel backend
            // console.log(file, 'RRRR');
            axios.post('/image', formData)
                .then(response => {
                    // console.log(response.data, 'What happrning....Q');
                    this.circle_loader = false;
                    //  let data_save = { image_path: response.data.image_path };
                    if (this.formValues.logo && this.formValues.logo !== '') {
                        this.removeExistImage(this.formValues.logo);
                    }
                    this.formValues.logo = response.data.media_url;
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                    this.circle_loader = false;
                });
        },
        removeExistImage(url) {
            axios.delete('/delete-image', { params: { model: "expense", image_url: url } })
                .then(response => {
                    console.log(response.data.message);
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        saveData() {
            // Handle save logic here
            // console.log('Data saved:', this.uploadedImage, this.currency);
            if (this.page) {
                if (this.formValues.company_name && this.formValues.company_phone_no && this.formValues.password && this.formValues.email && this.emailError === '') {
                    this.open_loader = true;
                    let sent_data = {
                        ...this.formValues,
                        gst_number: this.formValues.gst_number ? this.formValues.gst_number.toUpperCase() : '',
                        mobile_number: this.formValues.company_phone_no,
                        user_id: this.currentLocalDataList.user_id
                    };
                    // if (this.$route.query.mobile) {
                    axios.post('/auth/user-register', sent_data)
                        .then(response => {
                            console.log(response.data, 'Response');
                            this.open_loader = false;
                            localStorage.setItem('track_new', JSON.stringify(response.data.user));
                            // this.openMessage('Welcome to TRACK-NEW');
                            this.message = 'Welcome to TRACK-NEW';
                            this.type_toaster = 'success';
                            this.show = true;
                            // this.$router.push('/');
                            this.fetchLocalDataList();
                            setTimeout(() => {
                                this.$emit('close-modal', this.formValues);
                                this.validation_message = '';
                                window.location.reload();
                            }, 300);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            if (error.response.data.data) {
                                this.openMessage(error.response.data.message + ' : ' + (Object.keys(error.response.data.data).length > 0 ? error.response.data.data.email : ''));
                            } else {
                                this.openMessage(error.response.data.message);
                            }
                        })
                    // } else {
                    //     axios.post('/auth/register', sent_data)
                    //         .then(response => {
                    //             // console.log(response.data, 'Response');
                    //             this.open_loader = false;
                    //             // this.formValues = response.data.data;
                    //             this.openMessage(response.data.message);
                    //             // this.$router.push('/login');
                    //         })
                    //         .catch(error => {
                    //             console.error('Error', error);
                    //             this.open_loader = false;
                    //             if (error.response.data.data) {
                    //                 this.openMessage(error.response.data.message + ' : ' + (Object.keys(error.response.data.data).length > 0 ? error.response.data.data.email : ''));
                    //             } else {
                    //                 this.openMessage(error.response.data.message);
                    //             }

                    //         })
                    // }
                } else {
                    // this.openMessage(!this.formValues.company_name ? 'Please fill the Company name' : !this.formValues.company_phone_no ? 'Please fill the contact number' : !this.formValues.password ? 'Please fill the valid password is minimum 8 charactes' : !this.formValues.email && this.emailError !== '' ? 'Please fill valid email id' : 'Please fill in all input fields and validate as fill informations are valid');
                    this.message = !this.formValues.company_name ? 'Please fill the Company name' : !this.formValues.company_phone_no ? 'Please fill the contact number' : !this.formValues.password ? 'Please fill the valid password is minimum 8 charactes' : !this.formValues.email && this.emailError !== '' ? 'Please fill valid email id' : 'Please fill in all input fields and validate as fill informations are valid';
                    this.show = true;
                }
            } else {
                if (this.formValues.company_name && this.formValues.company_phone_no && this.formValues.company_name !== '' && this.formValues.company_phone_no !== '' && this.validationMessage === '') {
                    this.page = 1;
                } else {
                    // this.openMessage(!this.formValues.company_name && this.formValues.company_name === '' ? 'Please fill the Company name' : !this.formValues.company_phone_no && this.formValues.company_phone_no === '' && this.validationMessage !== '' ? 'Please fill the valid contact number' : 'Please fill valid informations');
                    this.message = !this.formValues.company_name && this.formValues.company_name === '' ? 'Please fill the Company name' : !this.formValues.company_phone_no && this.formValues.company_phone_no === '' && this.validationMessage !== '' ? 'Please fill the valid contact number' : 'Please fill valid informations';
                    this.show = true;

                }
                this.handleFocus();
            }
        },
        openMessage(msg) {
            // console.log(msg, 'WWWWWW');
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.open_message = false;
            this.message = '';
        },
        handleFocus() {
            if (this.page) {
                this.$nextTick(() => {
                    let ref_data = this.$refs.user_name;
                    // console.log(ref_data, 'What happening...!!!!');
                    if (ref_data) {
                        ref_data.focus();
                    }
                })
            } else {
                this.$nextTick(() => {
                    let ref_data = this.$refs.company_name;
                    // console.log(ref_data, 'What happening...!!!!');
                    if (ref_data) {
                        ref_data.focus();
                    }
                })
            }
        },
        updateIsMobile() {
            // Update isMobile based on the viewport width
            // this.$forceUpdate();
            this.isMobile = window.innerWidth < 768;
        },
        openSupport() {
            this.show_support = true;
        },
        closeSupport() {
            this.show_support = false;
        },
        closeModal() {
            if (this.currentLocalDataList && this.currentLocalDataList.company_id) {
                this.isOpen = false;
                // Add a delay to allow the transition before emitting the close event
                setTimeout(() => {
                    this.$emit('close-modal');
                    this.validation_message = '';
                }, 300);
            } else {
                this.message = 'Please update your company / shop / business informations';
                this.type_toaster = 'info';
                this.show = true;
            }
        },
    },
    watch: {
        showModal(newValue) {
            if (newValue) {
                this.fetchLocalDataList();
            }
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        currentLocalDataList: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.mobile_number) {
                    this.formValues.mobile_number = newValue.mobile_number;
                    this.formValues.company_phone_no = newValue.mobile_number;
                }
            }
        }

    },

};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>
