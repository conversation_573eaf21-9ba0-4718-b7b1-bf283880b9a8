<template>
    <div :class="{ 'manualStyle text-sm mt-[10px] sm:mb-[60px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        class="custom-scrollbar-hidden" ref="scrollContainer" @scroll="handleScroll">
        <!--new design header-->
        <div v-if="isMobile" class="w-full px-2 sm:px-[100px]">
            <searchItems :isMobile="isMobile" :pagination="pagination" @searchData="selectedItem"
                @resetData="resetToSearch"></searchItems>
        </div>
        <div class="my-custom-margin">
            <div v-if="!isMobile" class="flex justify-between m-1 mt-5">
                <div class="flex mr-2 space-x-4">
                    <button @click="addNewItem" :class="{ 'mr-2': isMobile }"
                        class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center">
                        <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
                        <span v-if="!isMobile" class="text-center">New Product</span></button>
                    <!--import icon-->
                    <button @click="openImportModal"
                        class="border rounded-lg px-1 sm:px-3 bg-blue-600  text-white border-blue-600 shadow-inner shadow-blue-100 flex items-center text-sm ml-3"
                        :class="{ 'ml-2': isMobile }">
                        <font-awesome-icon icon="fa-solid fa-upload" class="px-1" />
                        <span v-if="!isMobile" class="text-sm lg:inline hidden">Import Product</span>
                    </button>
                    <!--view design-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="info-msg px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200"
                            @click="toggleView" :title02="'Table view or Card view'"
                            :class="{ 'bg-white': items_category === 'tile' }">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <div>
                    <div v-if="!open_skeleton"
                        class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                        <p class="text-gray-700">Total Products
                            :</p>
                        <p class="font-semibold pl-1">
                            {{ pagination.total }}
                        </p>
                    </div>
                </div>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton" class="text-sm m-1 mt-5">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                        <!--search bar--->
                        <div>
                            <searchItems :isMobile="isMobile" :pagination="pagination" @searchData="selectedItem"
                                @resetData="resetToSearch"></searchItems>
                        </div>
                    </div>
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label === 'Current Date' ? 'Date' : column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in data" :key="index"
                                    class="hover:bg-gray-200 cursor-pointer">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex"
                                        :class="{ 'hidden': !column.visible || column.field === 'editing' }"
                                        class="px-2 py-2 text-sm text-center table-border" @click="startEdit(record)">
                                        <span v-if="column.field !== 'barcodes' && column.field !== 'products'">{{
                                            record[column.field] }}</span>
                                        <span v-if="column.field === 'barcodes'">{{ record[column.field]['barcode'] ?
                                            record[column.field]['barcode'] : ''
                                        }}</span>
                                        <span v-if="column.field === 'products'">{{ record[column.field].product_name ?
                                            record[column.field].product_name : ''
                                        }}</span>

                                    </td>
                                    <td class="px-2 py-2 sm:text-[14px] text-xs sm:text-center table-border">
                                        <div class="flex justify-center">
                                            <!--More actions-->
                                            <div class="flex relative">
                                                <button v-if="!record.editing" @click="startEdit(record)"
                                                    class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-solid fa-pencil"
                                                        style="color: #3b82f6;" />
                                                    <span class="px-2">Edit</span>
                                                </button>
                                                <button v-if="!record.editing && checkRoles(['admin'])"
                                                    @click="confirmDelete(index)"
                                                    class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-regular fa-trash-can"
                                                        style="color: #ef4444" />
                                                    <span class="px-2">Delete</span>
                                                </button>
                                                <!-- <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 text-blue-800 rounded"
                                                    :class="{ 'bg-blue-100': display_option === index }">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-8 absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">

                                                    <li :class="{ 'hidden': record.status === '1' }"
                                                        class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li :class="{ 'hidden': record.status === '1' }"
                                                        class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                    <td>
                                        <button class="text-green-600 text-md font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="addNewItem">
                                            + Add Item
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 sm:gap-4 custom-scrollbar-hidden">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <!-- Repeat this card for each item in your stock list -->
                            <div class="bg-white rounded-lg overflow-hidden shadow-md border border-gray-300 p-4">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center">
                                    <div v-if="record['products'] && record['products']['image']"
                                        class="flex items-center text-sm">
                                        <div class="flex items-center justify-center px-1 py-1 rounded mr-2">
                                            <img :src="record['products']['image']" class="w-[25px] h-[25px]" />
                                        </div>
                                        <!--item name-->
                                        <h3 class="font-semibold capitalize">{{ record['products']['product_name'] ?
                                            record['products']['product_name'] : '' }}
                                        </h3>
                                    </div>
                                    <div v-else class="flex items-center text-sm">
                                        <div
                                            class="flex items-center justify-center bg-gray-200 px-3 py-1 rounded mr-2">
                                            <span class="text-lg text-gray-600">{{
                                                record['products'] && record['products']['product_name'] ?
                                                    record['products']['product_name'].charAt(0).toUpperCase() : 'PRO'
                                            }}</span>
                                        </div>
                                        <!--item name-->
                                        <h3 class="font-semibold capitalize">{{ record['products'] &&
                                            record['products']['product_name'] ? record['products']['product_name'] :
                                            '' }}
                                        </h3>
                                    </div>
                                    <!-- Right Side (Actions) -->
                                    <div class="flex space-x-4">
                                        <div class="flex justify-center relative">
                                            <button
                                                class="text-sm  font-medium bg-blue-100 text-blue-800 rounded-full px-3 py-1">
                                                {{ record['products'] && record['products']['product_type'] ?
                                                    record['products']['product_type'] : '' }}
                                            </button>
                                            <div class="flex justify-center">
                                                <!--More actions-->
                                                <div class="relative ml-2">
                                                    <button @click.stop="displayAction(index)"
                                                        class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                        <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                            size="lg" />
                                                    </button>
                                                </div>
                                                <div v-if="display_option === index" :ref="'dropdown' + index"
                                                    class="z-10 mt-8 absolute bg-white right-0 divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                    style="display: flex; justify-content: center; align-items: center;">
                                                    <ul class="py-1 text-gray-700"
                                                        aria-labelledby="dropdownDefaultButton">

                                                        <li class="hover:bg-gray-200">
                                                            <button v-if="!record.editing" @click="startEdit(record)"
                                                                class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-pencil"
                                                                    style="color: #3b82f6;" size="lg" />
                                                                <span class="px-2">Edit</span>
                                                            </button>
                                                        </li>
                                                        <li class="hover:bg-gray-200">
                                                            <button v-if="!record.editing && checkRoles(['admin'])"
                                                                @click="confirmDelete(index)"
                                                                class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                    style="color: #ef4444" />
                                                                <span class="px-2">Delete</span>
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center py-2 px-4">
                                    <!--sales price-->
                                    <div>
                                        <p class="text-gray-400">Sales Price</p>
                                        <p>{{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }} {{ record['sales_price'] }}</p>
                                    </div>
                                    <!--purchase price-->
                                    <div>
                                        <p class="text-gray-400">Purchase Price</p>
                                        <p>{{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }} {{ record['purchase_price'] ?
                                                record['purchase_price'] : 0 }}</p>
                                    </div>
                                    <!--stock-->
                                    <div>
                                        <p class="text-gray-400">Stock</p>
                                        <p>
                                            {{ record['total_qty'] }} {{ record['products'] &&
                                                record['products']['unit'] ?
                                                record['products']['unit'] : '' }}
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && this.pagination.last_page > 0 && this.pagination.last_page == this.pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>

                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="pagination && !isMobile">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>

                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none overflow-auto">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span>
                                    </button>
                                </li>
                                <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                            pageNumber
                                        }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== pagination.last_page }">
                                    <button @click="updatePage(currentPage + 1)"
                                        :disabled="currentPage === pagination.last_page"
                                        class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!--in mobile view import items-->
        <div v-if="isMobile" class="fixed bottom-36 right-5 z-50 bg-blue-600 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openImportModal" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-upload" size="lg" />
                </button>
            </div>
        </div>
        <!---in mobile view create new item-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="addNewItem" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <importModal :isOpen="isImportModalOpen" @close="closeImportModal" @collectData="collectImportedData"
            :companyId="companyId" :userId="userId" :isMobile="isMobile">
        </importModal>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!---add new item-->
        <addNewItem :showModal="open_add_item" :editData="editData" @close-modal="closeAddNewItemModal"></addNewItem>
        <Loader :showModal="open_loader"></Loader>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'items'"></bottombar> -->
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import confirmbox from '../dialog_box/confirmbox.vue';
import importModal from '../dialog_box/importModal.vue';
import dialogAlert from '../dialog_box/dialogAlert.vue';
import addNewItem from '../dialog_box/addNewItem.vue';
import axios from 'axios';
// import bottombar from '../dashboard/bottombar.vue';
import searchItems from './searchItems.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
    name: 'inventory_home',
    emits: ['updateIsOpen', 'dataToParent'],
    components: {
        confirmbox,
        importModal,
        dialogAlert,
        addNewItem,
        // bottombar,
        searchItems
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            length_category: null,
            serviceCategories: null,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataList: [],
            columns: [],
            data: [],
            originalData: [],
            isImportModalOpen: false,
            open_message: false,
            message: '',
            searchQuery: '',
            showSuggestions: false,
            placeholderText: '',
            selectedIndex: 0,
            //---product name
            isDropdownOpenProduct: false,
            filteredProductList: [],
            mouseDownOnDropdown: false,
            //---add new ite--
            open_add_item: false,
            editData: null,
            //---api integration---
            companyId: null,
            userId: null,
            pagination: {},
            open_skeleton: false,
            number_of_columns: 5,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            now: null,
            open_skeleton_isMobile: false,
            backup_data: [],
            //---table data filter asending and decending----
            is_filter: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
        };
    },
    computed: {
        ...mapGetters('itemsList', ['currentItemsList']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        paginatedData() {
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                this.length_category = filteredData.length;
                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];
            const order = ['barcodes', 'products', 'total_qty', 'sales_price', 'purchase_price', 'gst_type', 'gst_value', 'alert_qty', 'discount_type', 'discount', 'dealer_price'];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the specified order to create fields
            if (this.data && this.data.length !== 0) {
                for (const key of order) {
                    // if (this.data && this.data[0].hasOwnProperty(key) && key !== 'id' && key !== 'product_id' && key !== 'barcode_id') {
                    const label = formatLabel(key);
                    if (key !== 'barcodes' && key !== 'gst_type' && key !== 'gst_value' && key !== 'alert_qty' && key !== 'discount_type' && key !== 'discount') {
                        fields.push({ label, field: key, visible: true });
                    } else {
                        fields.push({ label, field: key, visible: false });
                    }
                    // }
                }
                this.columns = fields;
                return fields;
            }
        },
        filteredProduct() {
            // console.log('it is executed..!!');
            // Filter customers based on search query
            if (this.searchQuery !== '') {
                const query = this.searchQuery.toLowerCase();
                return this.data.filter(product => {
                    const barcode = product.barcodes && product.barcodes.barcode ? product.barcodes.barcode.toLowerCase() : '';
                    // console.log(barcode, 'barcode');
                    const name = product.products && product.products.product_name ? product.products.product_name.toLowerCase() : '';
                    // console.log(name, 'Product name');
                    // console.log(barcode.includes(query) || name.includes(query));
                    return (
                        barcode.includes(query) || name.includes(query)
                    );
                });
            } else {
                return this.data;
            }
        },
        visiblePageNumbers() {
            // console.log(this.totalPages,);
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        }
    },
    created() {
        // Create a copy of the original data when the component is created
        this.originalData = JSON.parse(JSON.stringify(this.data));

    },
    methods: {
        ...mapActions('itemsList', ['fetchItemsList']),
        ...mapActions('localStorageData', ['validateRoles', 'fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('items', ['fetchItemList']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        //--supllier--
        goToSupplier() {
            this.$router.push({ name: 'supplier' })

        },
        goToPurchaseOrder() {
            // this.$router.push('/purchaseOrder');
            this.$router.push({
                name: 'purchase_order',
            });
        },
        goToWarehouse() {
            // this.$router.push('/purchaseOrder/warehouse');
            this.$router.push({
                name: 'warehouse',
            });
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },
        //---get barcode unique--
        currentTimeString() {
            return Date.now().toString();
        },
        //--add row
        addEmptyRow() {
            const emptyRow = {
                product_name: '',
                barcode: this.currentTimeString(),
                product_code: '',
                total_qty: 0,
                price_per_qty: 0,
                tax: 0,
                sold_stocks: 0,
                available_stocks: 0,
                purchase_order: '',
                editing: true, // Set editing to true for the new row
            };
            this.data.push(emptyRow);
        },
        // Start editing a record
        startEdit(record) {
            // console.log(record, 'EEEEE');
            this.editData = record;
            this.open_add_item = true;
        },


        // Cancel editing a record
        cancelEdit(record, index) {
            // console.log(record, 'what happening 12344');
            const keysToExclude = ['id', 'editing', 'barcode'];
            // Check if product code and product name are empty, or if all fields are empty or 0
            const isRecordEmpty = Object.keys(record).filter(key => !keysToExclude.includes(key)).every(key => {
                return record[key] === '' || record[key] === 0;
            });
            // console.log(isRecordEmpty, 'What about data..!');
            if (isRecordEmpty) {
                // Remove the record from the data array
                // const indexToRemove = this.data.findIndex(item => item.product_code === record.product_code && item.product_name === record.product_name);
                if (index !== -1) {
                    this.data.splice((this.recordsPerPage * (this.currentPage - 1)) + index, 1);
                }
            } else {
                // Reset changes made during editing
                // You may want to reload the data from local storage or cancel any local changes
                const indexInData = this.data.findIndex(item => item.product_code === record.product_code && item.product_name === record.product_name);
                const indexInOriginalData = this.originalData.findIndex(item => item.product_code === record.product_code && item.product_name === record.product_name);
                // console.log(indexInOriginalData !== -1 && indexInData !== -1, 'Validation...!');
                if (indexInOriginalData !== -1 && indexInData !== -1) {

                    this.data[indexInData] = { ...this.originalData[indexInOriginalData], editing: false };
                    record.editing = false;
                } else {
                    this.data.splice((this.recordsPerPage * (this.currentPage - 1)) + index, 1);
                }
                // console.log('Edit cancelled:', record, this.data, this.originalData);
            }
        },

        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.open_loader = true;
                let deleteData = this.deleteIndex;
                axios.delete(`products_details/${this.data[deleteData].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        this.updateKeyWithTime('products_update');
                        this.open_loader = false;
                        this.data.splice(deleteData, 1);
                        this.fetchItemList(this.pagination && this.pagination.total ? this.pagination.total : 100);
                    })
                    .catch(error => {
                        console.error('Error delete', error);
                        this.open_loader = false;
                    })
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        addServices() {
            // Call the method to add an empty row
            this.addEmptyRow();

            // Navigate to the last page
            this.currentPage = this.pagination.last_page;
        },
        goBackToHomePage() {
            this.$router.push('/services');
        },
        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    try {
                        // Remove event listener when dropdown is closed
                        document.removeEventListener('click', this.handleOutsideClick);
                    } catch (error) {
                        console.error("Error removing event listener:", error);
                    }
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            try {
                const isClickInside = this.$refs.settingOPtion && (this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target));
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //----print prechecklist
        formattedString(listData) {
            let returnData = Object.entries(listData)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ');
            return returnData;
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //----import model box--
        checkForDuplication(recordToCheck) {
            return this.data.some(record => (
                record.product_code === recordToCheck.product_code ||
                record.product_name === recordToCheck.product_name
            ));
        },
        openImportModal() {
            this.isImportModalOpen = true;
        },
        closeImportModal(collectedData) {
            if (collectedData) {
                const seenCodes = new Set();
                const uniqueData = collectedData.filter(record => {
                    const key = `${record.barcode}_${record.product_name}`;
                    // console.log(seenCodes.has(key), 'EEWEWEWEW');
                    if (seenCodes.has(key)) {
                        // alert(`Duplicate entry for Product Code: ${record.product_code} and Product Name: ${record.product_name}`);
                        this.open_message = true;
                        this.message = `Duplicate entry for Product Code: ${record.barcode} and Product Name: ${record.product_name}`;
                        return false;
                    }
                    seenCodes.add(key);
                    return true;
                });
                // console.log(uniqueData, 'What happening the data...!!!!');
                // Append the unique data to the existing data array
                let getDataUnique = uniqueData.filter((opt) => {
                    // console.log(!this.checkForDuplication(opt), 'OYYIYIUURTUR');
                    if (!this.checkForDuplication(opt)) {
                        this.data.push({ ...opt, editing: false });
                        localStorage.setItem('inventory', JSON.stringify(this.data));
                    }

                });
                // console.log(getDataUnique, 'WWWWWWWWWWWWW');
                this.originalData = this.data;
                this.isImportModalOpen = false;
            } else {
                this.isImportModalOpen = false;
            }
        },
        collectImportedData(collectedData) {
            // Check for duplication based on product code and name
            const isDuplicate = this.checkForDuplication(collectedData);

            if (isDuplicate) {
                // Show alert message for duplication
                // alert('Product code or name is duplicate!');
                this.open_message = true;
                this.message = 'Product name or barcode is duplicate!';
                return; // Prevent saving if duplicate
            }
            this.data = [...this.data, { ...collectedData, editing: false }];
            this.originalData = this.data;
            // Continue with saving the imported data
            // console.log(collectedData, 'How to achieve..!');
        },
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //------search bar
        //----dropdown---
        filterProduct() {
            if (this.searchQuery !== '' && this.searchQuery.length > 1) {
                // console.log(this.searchQuery, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            } else {
                this.showSuggestions = false;
                this.data = this.originalData;
            }
        },
        showDropdown() {
            // console.log('What happening go there data......!!!!!!');
            if (this.searchQuery !== '') {
                // Show dropdown on input focus
                this.showSuggestions = true;
                // Add a click event listener to the document to close the dropdown when clicking outside
                document.addEventListener('click', this.handleDocumentClick);
            }
        },
        hideDropdown() {
            // Hide dropdown on input blur
            this.showSuggestions = false;
            // Remove the click event listener when hiding the dropdown
            document.removeEventListener('click', this.handleDocumentClick);
        },
        selectProduct(product) {
            // Handle the selected customer (e.g., emit an event, update data, etc.)
            // console.log('Selected product:', product);
            this.searchQuery = product.barcodes.barcode + ' ' + product.products.product_name; // Set the input value to the selected customer name
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            this.data = [{ ...product }];
            // this.$emit('searchData', product); //---filter data
            document.removeEventListener('click', this.handleDocumentClick); // Remove the event listener after selection
        },
        handleDocumentClick(event) {
            // Close the dropdown when clicking outside the input and dropdown
            const isClickInside = this.$refs.searchInput.contains(event.target) || this.$refs.searchInput.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.hideDropdown();
            }
        },
        isNumeric(str) {
            // Helper method to check if a string is numeric
            return /^\d+$/.test(str);
        },
        //--on press enter key--
        handleEnterKey() {
            // Check if filteredProductList has at least one item
            if (this.filteredProduct.length > 0) {
                // Call selectedProductData with the first item in filteredProductList
                this.selectProduct(this.filteredProduct[this.selectedIndex]);
                this.selectedIndex = 0;
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //---find any one record is editing is true--
        findEditingValue() {
            let findData = this.data.filter((opt) => opt.editing === true);
            if (findData.length !== 0) {
                return true;
            } else {
                return false;
            }
        },
        //---product list
        //---item product name--
        handleProductChange(product_name, index) {
            this.isDropdownOpenProduct = index;
            if (product_name) {
                const enteredProduct = product_name.trim().toLowerCase();

                this.filteredProductList = this.data.filter(opt => {
                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.product_name.toLowerCase().includes(enteredProduct);

                    return nameMatch;
                });
                // console.log(this.filteredProductList, 'Waht happening.......@@@');
            }
        },

        //---selected product
        selectedProductData(item, index, option) {
            // console.log(option, 'option', index, 'index', 'item', item);
            console.log('selected product...!');
            this.isDropdownOpenProduct = false;
        },
        //----control dropdown--
        //----customer dropdown--
        closeDropdown() {
            // console.log('hello input');
            if (!this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpenProduct = false;
            }
        },
        preventBlur() {
            // console.log('hello');
            this.mouseDownOnDropdown = true;
            setTimeout(() => {
                this.mouseDownOnDropdown = false;
            }, 100);
        },
        //---add new item----
        addNewItem() {
            this.editData = null;
            this.open_add_item = true;
        },
        closeAddNewItemModal(data) {
            this.open_add_item = false;
            // console.log(data, 'RRRRRRRRRRRR');
            if (data && data.id) {
                if (this.editData) {
                    // console.log(this.editData, 'EWEWEWE what happppp');
                    const findIndex = this.data.findIndex(opt => opt.id === this.editData.id);
                    if (findIndex !== -1) {
                        // Replace the item at the found index with the new data
                        this.data.splice(findIndex, 1, data);
                    }
                    // Clear editData after the update
                    this.editData = {};
                    // this.getItemsList(this.current_page, this.recordsPerPage);
                    // console.log(findIndex, 'Waht happening...@');
                    // if (findIndex !== -1) {
                    //     // this.data[findIndex] = data;
                    //     this.data.splice(findIndex, 1, data);
                    //     this.editData = null;
                    // }
                } else {
                    this.data.unshift(data);
                }
            }
        },
        getItemsList(page, per_page) {
            if (page == 1) {
                this.fetchItemsList({ page, per_page });
                if (this.currentItemsList && this.currentItemsList.data) {
                    this.data = this.currentItemsList.data;
                    this.pagination = this.currentItemsList.pagination;
                }
            } else {
                this.open_skeleton = true;
                if (window.innerWidth < 1024 && page === 1 && per_page < 20) {
                    per_page = 20;
                }
                //--get product list ----
                axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        // console.log(response.data.data);
                        this.open_skeleton = false;
                        this.data = response.data.data;
                        this.originalData = this.data;
                        this.pagination = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error response', error);
                        this.open_skeleton = false;
                    })
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(Number(this.pagination.current_page) + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            // console.log('load nex data.....');
            // let send_data = {
            //     type: 'leads', q: this.status_select >= 0 && this.status_select !== 5 ? this.status_select : '', filter: this.followup_select >= 0 ? this.followup_select : '', per_page: this.recordsPerPage, page: page,
            //     customer_id: this.searchByCustomer.id ? this.searchByCustomer.id : ''
            // };
            // if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
            //     // console.log(this.filteredBy, 'helllo');
            //     if (this.filteredBy.customer_id) {
            //         send_data.customer_id = this.filteredBy.customer_id
            //     }
            //     if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
            //         send_data.employer_id = this.filteredBy.assign_to[0].id;
            //     }
            //     if (this.filteredBy.type) {
            //         send_data.category = this.filteredBy.type;
            //     }
            // }
            // // console.log(this.category_type !== null && this.category_type !== 'all', 'Waht happening...!!!');
            // if (this.category_type !== null && this.category_type !== 'all') {
            //     send_data.category_id = this.category_type;
            // }
            // axios.get('/searchs', { params: { ...send_data } })
            axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        //-----Search functions------
        selectedItem(selectedData) {
            if (selectedData) {
                this.backup_data = [...this.data];
                this.data = [selectedData];
            }
        },
        resetToSearch() {
            if (Array.isArray(this.backup_data) && this.backup_data.length > 0) {
                this.data = [...this.backup_data];
            }
        },
        //---store data initialize--
        getInitialData(storeData) {
            this.open_skeleton = false;
            this.data = storeData.data;
            this.originalData = this.data;
            this.pagination = storeData.pagination;
        },
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },//---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'items' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'items', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'items' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'items', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        //----close all modal box--
        closeAllModals() {
            this.open_confirmBox = false;
            this.isImportModalOpen = false;
            this.open_message = false;
            this.open_add_item = false;
        },
        //--sort icons--
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //---refresh--
        refreshDataTable() {
            this.fetchItemsList({ page: 1, per_page: this.recordsPerPage });
            this.currentPage = 1;
        },

    },
    watch: {
        originalData: {
            deep: true,
            handler(newValue) {
                // Automatically send data to the parent when dataToSend changes
                this.$emit('dataToParent', newValue);
            }
        },
        searchedData: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'RRRRR');
                if (!this.isEmptyObject(newValue)) {
                    this.data = [{ ...newValue }];
                }
                else {
                    this.data = this.originalData;
                }
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    this.getItemsList(1, newValue);
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                this.getItemsList(newValue, this.recordsPerPage);
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('items_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentItemsList: {
            deep: true,
            handler(newValue) {
                this.getInitialData(newValue);
                this.open_loader = false;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchItemsList({ page: 1, per_page: this.recordsPerPage });
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        isImportModalOpen: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_add_item: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.fetchCompanyList();
        //----get items list---
        if (this.data.length === 0) {
            // this.getItemsList(this.currentPage, this.recordsPerPage);
            if (this.currentItemsList && this.currentItemsList.data && this.currentItemsList.data.length > 0) {
                this.open_skeleton = true;
                this.getInitialData(this.currentItemsList);
                this.fetchItemsList({ page: 1, per_page: this.recordsPerPage });
            } else {
                if (this.currentItemsList && Object.keys(this.currentItemsList).length == 0) {
                    this.open_skeleton = true;
                    this.fetchItemsList({ page: 1, per_page: this.recordsPerPage });
                }
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        const view = localStorage.getItem('items_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        //---sortIcons---
        const initialShortVisible = ['products', 'total_qty', 'sales_price', 'purchase_price', 'dealer_price'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
    },

}
</script>

<style scoped>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

/* Add these styles */
.w-full.text-center.border-none:focus,
.w-full.text-center.border-none:focus-within {
    border: none;
    /* Remove border on focus or focus within */
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}
</style>
