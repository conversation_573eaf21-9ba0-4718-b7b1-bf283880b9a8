<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded-lg overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <!-- <PERSON><PERSON>er -->
            <h3 class="text-xl font-bold mb-4 text-center text-gray-900 rounded-t-lg py-4 bg-gray-300">
                {{ type !== 'jobsheet' ? 'Send WhatsApp Reminder' : 'JobSheet' }} </h3>
            <button class="absolute top-0 right-1 text-xl text-red-700 hover:text-red-600" @click="closeModal">
                <font-awesome-icon icon="fa-regular fa-circle-xmark" />
            </button>
            <div class="p-6 pt-1">
                <h2> {{ type !== 'jobsheet' ? 'Send WhatsApp Reminder' : 'JobSheet link' }}</h2>
                <textarea rows="5" v-model="editableMessage" class="w-full p-2 border"></textarea>

                <div class="flex justify-end space-x-2 mt-4">
                    <button @click="sendMessage" class="bg-green-500 text-white px-4 py-2 rounded">Send</button>
                    <button @click="closeModal" class="bg-gray-500 text-white px-4 py-2 rounded">Cancel</button>
                </div>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    props: {
        showModal: Boolean,
        type: String,
        data: Object
    },
    data() {
        return {
            editableMessage: '',
            isOpen: false,
            //---toaster----
            type_toaster: 'info',
            message: '',
            show: false,
            //--loader--
            open_loader: false,
        };
    },
    computed: {
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
    },
    methods: {
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),
        closeModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close');
            }, 300);
        },
        async sendMessage() {
            const number = this.type === 'customer'
                ? this.data.contact_number
                : (this.type === 'sales' && this.data?.customer?.contact_number ? this.data.customer.contact_number :
                    this.type === 'jobsheet' && this.data.customer ?
                        (typeof this.data.customer === 'string' && this.data.customer.split('-').length > 1
                            ? this.data.customer.split('-')[1]
                            : (typeof this.data.customer === 'object' && this.data.customer.contact_number ? this.data.customer.contact_number : '')
                        )
                        : '');
            let company_id = this.currentCompanyList?.id || '';
            try {
                this.open_loader = true;
                const response = await this.sendWhatsAppMessage({
                    number,
                    type: "text",
                    message: this.editableMessage,
                    company_id: company_id
                });
                if (response.status == "success") {
                    this.open_loader = false;
                    this.type_toaster = 'success';
                    this.message = 'WhatsApp message sent successfully..!';
                    this.show = true;
                    this.closeModal();
                }
            } catch (e) {
                console.error('WhatsAPP Error', e);
                this.open_loader = false;
            }
        },
        createMessage() {
            let company_name = this.currentCompanyList?.company_name || '';
            let symbol = this.currentCompanyList?.currency === 'INR' ? '₹' : this.currentCompanyList?.currency || '';
            if (this.type === 'customer' && this.data) {
                let customer_name = `${this.data.first_name || ''} ${this.data.last_name || ''}`.trim();
                let due_amount = this.data.balance_amount || 0;
                this.editableMessage = `*Hello ${customer_name}*, your pending amount is *${symbol}${due_amount}*. Kindly make the payment.\n_By ${company_name}_`;
            }// Sales Reminder Template
            else if (this.type === 'sales' && this.data) {
                // Get customer details from the sales data object
                let customer_name = `${this.data.customer.first_name || ''} ${this.data.customer.last_name || ''}`.trim();
                // Combine invoice prefix and number (if both exist)
                let invoice_no = `${this.data.invoice_prefix || ''}${this.data.invoice_no || ''}`;
                let grand_total = this.data.grand_total || 0;
                let balance_amount = this.data.balance_amount || 0;
                let bank_info = this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].bank_details ? JSON.parse(this.currentInvoice[0].bank_details) : '';

                let upiId = bank_info?.upi_id || 'your-upi-id-here';
                let bankName = bank_info?.bank || 'Your Bank Name';
                let accountNumber = bank_info?.account_no || 'XXXXXXXXXX';
                let ifsc = bank_info?.ifsc || 'IFSCXXXX';
                this.editableMessage = `*Hello ${customer_name}*, this is a payment request for your recent purchase. Your invoice number is *${invoice_no}* with a total amount of *${symbol}${grand_total}*. A pending balance of *${symbol}${balance_amount}* remains. Kindly arrange payment at your earliest convenience.\n\n*Payment Details:*\nUPI: *${upiId}*\nBank: *${bankName}*\nAccount No: *${accountNumber}*\nIFSC: *${ifsc}*\n\n_By ${company_name}_`;
                // this.editableMessage = `*Hello ${customer_name}*, thank you for your purchase. Your invoice number is *${invoice_no}* with a grand total of *${symbol}${grand_total}*. Your pending due amount is *${symbol}${balance_amount}*.\n_By ${company_name}_`;
            } else if (this.type == 'jobsheet') {
                // Get customer details from the sales data object
                let customer_name = `${this.data.customer && typeof this.data.customer == 'string' && this.data.customer.split('-').length > 1 ? this.data.customer.split('-')[0] : typeof this.data.customer == 'object' ? `${this.data.customer.first_name || ''} ${this.data.customer.last_name || ''}`.trim() : ''}`;

                // Generate job sheet link
                let job_sheet = axios.defaults.baseURL + `/job-sheet/${this.data.service_code}?copy_type=customer`;
                // Compose the message with the clickable job sheet link and formatted text
                this.editableMessage = `*Hello ${customer_name}*, you can find detailed information regarding your service in the job sheet by clicking the link below: ${job_sheet} Best regards,  *${company_name}*`;
            }
        },
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.createMessage();
                }
            }, 100);
        },
    }
};
</script>

<style scoped>
/* Optional: Additional styling for modal */
</style>