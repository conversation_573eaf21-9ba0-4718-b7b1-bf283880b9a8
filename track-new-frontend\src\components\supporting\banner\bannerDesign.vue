<template>
    <div v-if="!bannerClosed && bannerData && bannerData.status && isExpiryValid"
        :class="{ 'lg:mt-2': move_top, 'lg:mb-2': move_bottom }"
        class="relative text-sm px-7 w-full overflow-hidden bg-gradient-to-r from-pink-500 via-purple-500 to-indigo-500 p-1 shadow-lg">
        <div class="flex items-center justify-between text-white">
            <span>{{ bannerData.message ? bannerData.message : '' }}</span>
            <button @click="closeBanner" class="absolute right-2 top-0 text-lg">
                <font-awesome-icon icon="fa-solid fa-xmark" />
            </button>
        </div>
    </div>
</template>

<script>
import { mapState, mapActions, mapGetters } from 'vuex';

export default {
    props: {
        move_top: Boolean,
        move_bottom: Boolean
    },
    data() {
        return {
            isMobile: false,
            isExpiryValid: true,
        };
    },
    computed: {
        // Use Vuex state to check if the banner is closed
        ...mapState('banner', ['bannerClosed']),
        ...mapGetters('banner', ['lastApiCallTime', 'bannerData'])
    },
    methods: {
        // Use Vuex action to close the banner
        ...mapActions('banner', ['closeBanner', 'updateLastApiCallTime', 'getFeatureData']),
        // Function to validate if the expiry date is in the future
        validateExpiryDate(expiryDate) {
            const currentDate = new Date();
            const expiryDateObj = new Date(expiryDate);

            // Compare dates and check if expiryDate is in the future
            return expiryDateObj > currentDate;
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

    },
    mounted() {
        this.updateIsMobile();
        if (!this.bannerData) {
            this.getFeatureData();
        } else {
            if (this.bannerData && this.bannerData.expiry_date) {
                this.isExpiryValid = this.validateExpiryDate(this.bannerData.expiry_date);
            }
        }
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        bannerData: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    if (this.newValue && this.newValue.expiry_date) {
                        this.isExpiryValid = this.validateExpiryDate(this.newValue.expiry_date);
                    }
                }

            }
        }
    }
};
</script>

<style scoped>
@keyframes marquee {
    0% {
        transform: translateX(0);
    }

    100% {
        transform: translateX(-22em);
    }
}

.animate-marquee {
    animation: marquee 10s linear infinite;
}

@media (max-width: 767px) {
    .-webkit-text-stroke-2 {
        -webkit-text-stroke: 1px white;
    }
}
</style>
