<!-- DeleteConfirmationModal.vue -->

<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="bg-white overflow-hidden max-w-md mx-auto py-2 shadow-lg">
                <div class="flex justify-center items-center my-2 text-5xl font-medium">
                    <font-awesome-icon icon="fa-regular fa-circle-xmark" color="red" @click="cancel"
                        class="cursor-pointer" />
                </div>
                <div>
                    <p class="text-2xl font-medium py-1 text-center text-neutral-500">Are you sure?</p>
                    <p class="text-center text-md text-gray-500">Do you really want to delete this record.</p>
                    <p class="text-center text-md text-gray-500">This record cannot be restore.</p>
                </div>
                <div class="flex justify-center items-center py-4">
                    <button @click="confirm"
                        class="bg-red-600 text-white font-normal px-4 py-2 hover:bg-red-500 rounded mr-2">Confirm</button>
                    <button @click="cancel"
                        class="bg-green-600 text-white font-normal px-4 py-2 hover:bg-green-500 rounded ml-2">Cancel</button>
                </div>
            </div>
        </div>
        <!-- </div> -->
        <Loader :showModal="circle_loader"></Loader>
    </div>
</template>

<script>
export default {
    name: 'ConfirmBox',
    props: {
        showModal: Boolean,
    },
    emits: ['onConfirm', 'onCancel'],
    data() {
        return {
            'overlay-active': this.showModal,
            isOpen: false,
            circle_loader: false,
        }
    },
    methods: {
        confirm() {
            this.circle_loader = true;
            // console.log(this.showModal, 'what happening....');
            this.$emit('onConfirm');
        },
        cancel() {
            // this.$emit('onCancel');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('onCancel');
            }, 300);
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.circle_loader = false;
            }, 100);
        },

    }
};
</script>

<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        width: 90%;
        /* Adjust the width as needed */
        max-width: 320px;
        /* Set a maximum width for smaller screens */
    }

    .modal-content {
        padding: 1rem;
        /* Add padding to the content for better spacing */
    }


}
</style>