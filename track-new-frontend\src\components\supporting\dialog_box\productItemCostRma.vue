<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-y-auto py-5">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen_modal, 'scale-0': !isOpen_modal }">
            <div class="flex items-center justify-center z-50">
                <div class="bg-white rounded-lg shadow-lg w-full max-w-lg mx-auto p-6 relative">
                    <div class="flex justify-between">
                        <h3 class="font-semibold mb-4">Included Product</h3>
                        <div class="bg-opacity-50 cursor-pointer" @click="cancelModal"><font-awesome-icon
                                icon="fa-solid fa-xmark" />
                        </div>
                    </div>

                    <form @submit.prevent="submitForm">
                        <div class="mb-4">
                            <label class="block text-gray-700">Product</label>
                            <div class="relative inline-block w-full" ref="rmaProduct">
                                <div @click="toggleDropdown('product')"
                                    class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                    <span class="px-2 rounded"
                                        :class="{ 'text-gray-400': !formValues.product_name || formValues.product_name === '' }">{{
                                            formValues.product_name && formValues.product_name !== '' ?
                                                formValues.product_name :
                                                'Select an Product' }}</span>
                                    <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                </div>

                                <div v-if="isOpen.product"
                                    class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                    <input type="text" ref="productInput" v-model="searchTerm.product"
                                        @keydown.enter="handleEnterKeyProduct('product', filteredOptions)"
                                        @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                        @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                        class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                    <ul class="max-h-60 overflow-auto">
                                        <li v-for="(option, index) in filteredOptions" :key="index"
                                            @click="selectOptionData(option)"
                                            class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                            :class="{ 'bg-gray-200': index === selectedIndex }">
                                            <span class="py-1 px-2 rounded">
                                                {{ option.products.product_name }}
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Description</label>
                            <input type="text" v-model="formValues.description"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Quantity *</label>
                            <input type="number" v-model="formValues.quantity"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Unit price *</label>
                            <input type="number" v-model="formValues.unit_price"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700">Tax Type</label>
                            <select v-model="formValues.tax_type"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3">
                                <option value="" disabled>Please select a tax type ...</option>
                                <option value="inclusive">Inclusive</option>
                                <option value="exclusive">Exclusive</option>
                            </select>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Tax *</label>
                            <div class="relative inline-block w-full" ref="rmatax">
                                <div @click="toggleDropdown('tax')"
                                    class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                    <span class="px-2 rounded"
                                        :class="{ 'text-gray-400': !formValues.tax_value || formValues.tax_value === '' }">
                                        {{ formValues.tax_value && formValues.tax_value !== '' ?
                                            formValues.tax_value :
                                            'Select an Tax' }}</span>
                                    <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                </div>

                                <div v-if="isOpen.tax"
                                    class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                    <input type="text" ref="taxInput" v-model="searchTerm.tax"
                                        @keydown.enter="handleEnterKeyProduct('tax', filteredOptions)"
                                        @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                        @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                        class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                    <ul class="max-h-60 overflow-auto">
                                        <li v-for="(option, index) in filteredOptions" :key="index"
                                            @click="selectOptionData(option)"
                                            class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                            :class="{ 'bg-gray-200': index === selectedIndex }">
                                            <span class="py-1 px-2 rounded">
                                                {{ option.tax_name + ' ' + option.value + ' % ' }}
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Total</label>
                            <input type="number" v-model="formValues.total"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" readonly />
                        </div>

                        <div class="flex justify-end space-x-2">
                            <button type="button" @click="cancelModal"
                                class="bg-gray-500 text-white px-4 py-2 rounded">Cancel</button>
                            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'product_cost',

    props: {
        showModal: Boolean,
        currentItems: Object
    },
    data() {
        return {
            formValues: {},
            isOpen_modal: false,
            isOpen: { product: false, tax: false },
            searchTerm: { product: '', tax: '' },
            selectedOption: { product: null, tax: null },
            selectedIndex: 0,
            //--toaster---
            show: false,
            type_toaster: 'warning',
            message: ''
        };
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
        filteredOptions() {
            if (this.isOpen.product) {
                return this.currentItems.filter(option =>
                    option.products.product_name.toLowerCase().includes(this.searchTerm.product.toLowerCase())
                );
            } else if (this.isOpen.tax && this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].selected_tax
            ) {
                return JSON.parse(this.currentInvoice[0].selected_tax).filter(option =>
                    option.tax_name.toLowerCase().includes(this.searchTerm.tax.toLowerCase())
                );
            }
        },
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        handleClickOutside(event, type) {
            if (type !== 'all') {
                if (this.isOpen.product) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaProduct;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.product = false;
                    }
                }
                else if (this.isOpen.tax) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmatax;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.tax = false;
                    }
                }
            } else {
                this.isOpen = { product: false, tax: false };
            }
        },
        calculateTotal() {
            if (this.formValues.tax_type === 'exclusive') {
                const taxRate = parseFloat(this.formValues.tax) / 100;
                const total = (this.formValues.quantity * this.formValues.unit_price) * (1 + taxRate);
                this.formValues.total = total.toFixed(2);
            } else {
                const total = (this.formValues.quantity * this.formValues.unit_price);
                this.formValues.total = total.toFixed(2);
            }
        },
        toggleDropdown(type) {
            if (type === 'product') {
                this.isOpen.product = !this.isOpen.product;
                this.isOpen.tax = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.product) {
                    this.$nextTick(() => {
                        this.$refs.productInput.focus();
                    });
                }
            }
            else if (type === 'tax') {
                this.isOpen.tax = !this.isOpen.tax;
                this.isOpen.product = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.tax) {
                    this.$nextTick(() => {
                        this.$refs.taxInput.focus();
                    });
                }
            }
        },
        selectOptionData(option) {
            if (this.isOpen.product) {
                this.formValues.product_name = option.products.product_name;
                this.formValues.description = option.products.product_name;
                this.formValues.product_id = option.products.id;
                this.formValues.barcode_id = option.barcodes.id;
                this.formValues.unit_price = option.sales_price;
                this.calculateTotal();
                this.isOpen.product = false;
                this.searchTerm.product = '';
            } else if (this.isOpen.tax) {
                this.formValues.tax_value = option.tax_name + ' ' + option.value + ' % ';
                this.formValues.tax = option.value;
                this.calculateTotal();
                this.isOpen.tax = false;
                this.searchTerm.tax = '';
            }
        },
        cancelModal(data) {
            // this.$emit('close-modal');
            this.isOpen_modal = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                if (data && data.product_id) {
                    this.$emit('closeModal', data);
                } else {
                    this.$emit('closeModal');
                }
            }, 300);

        },
        submitForm() {
            if (this.formValues.product_id && this.formValues.product_name !== '' && this.formValues.quantity && this.formValues.unit_price >= 0 && this.formValues.tax >= 0) {
                // Handle form submission
                this.cancelModal(this.formValues);
            } else {
                this.message = !this.formValues.product_id || !this.formValues.product_name === '' ? 'Please select the item / product name' : !this.formValues.quantity ? 'Please enter the quantity' : this.formValues.unit_price < 0 ? 'please enter the unit price' : !this.formValues.tax >= 0 ? 'Please slect the tax' : 'Please fill as * fileds data..!';
                this.show = true;
            }
        },
        handleEnterKeyProduct(type, list_data) {
            if (type === 'product') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            } else if (type === 'tax') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },

    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside($event, 'all'));
    },
    watch: {
        showModal(newValue) {
            if (newValue) {
                this.fetchInvoiceSetting();
                this.formValues = { quantity: 1, unit_price: 0, total: 0, tax_type: 'inclusive' };
            }
            setTimeout(() => {
                this.isOpen_modal = newValue;
            }, 100);
        },
        'this.formValues.product_name': {
            deep: true,
            handler(newValue) {
                this.calculateTotal();
            }
        },
        'formValues.quantity': {
            handler(newValue) {
                this.calculateTotal();
            }
        },
        'formValues.unit_price': {
            handler(newValue) {
                this.calculateTotal();
            }
        },
        'formValues.tax': {
            handler(newValue) {
                this.calculateTotal();
            }
        },
        'formValues.tax_type': {
            handler(newValue) {
                this.calculateTotal();
            }
        }

    },
};
</script>

<style>
/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.close {
    color: white;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: red;
    text-decoration: none;
    cursor: pointer;
}
</style>