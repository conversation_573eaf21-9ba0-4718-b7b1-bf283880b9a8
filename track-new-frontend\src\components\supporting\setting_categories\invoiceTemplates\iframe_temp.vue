<template>
    <div :class="{ 'mt-[50px] mb-[50px]': isMobile, 'manualStyle px-1': !isMobile }">
        <!-- <p>{{ exist_data.id }}</p> -->
        <iframe v-if="!open_skeleton" ref="pdfViewer" :src="pdfViewerSrc"
            class="w-full h-screen custom-scrollbar-hidden p-2"
            style="border: none; overflow: auto; scrollbar-width: none;"></iframe>
        <div class="py-1 fixed bottom-0 sm:left-1/2 transform sm:-translate-x-1/2 flex justify-center space-x-3 sm:space-x-10 px-6 bg-white"
            :class="{ 'w-full text-sm': isMobile, 'text-md': !isMobile }">
            <button v-if="!isMobile" @click="backToSetting"
                class="flex bg-gray-700 hover:bg-gray-600 text-white rounded py-2 px-3 lg:px-6 items-center">
                <font-awesome-icon icon="fa-solid fa-arrow-left" class="mr-1" />
                Back
            </button>
            <div class="text-sm">
                <!--signature options-->
                <select id="signature" v-model="selectedOption"
                    class="px-1 py-2 border-2 border-blue-300 rounded shadow-inner shadow-blue-200">
                    <option v-for="option in signatureOptions" :key="option.id" :value="option.value">
                        {{ option.label }}
                    </option>
                </select>
            </div>

            <button @click="downloadPDF($event)"
                class="flex bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 lg:px-6 items-center rounded">
                <font-awesome-icon icon="fa-solid fa-download" class="mr-1" />
                PDF
            </button>
            <!-- <button @click="shareViaWhatsApp" class="bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded">
                <font-awesome-icon icon="fa-brands fa-whatsapp" class="mr-1" />
                Share
            </button>
            <button @click="shareViaEmail" class="bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-4 rounded">
                <font-awesome-icon icon="fa-regular fa-envelope" class="mr-1" />
                Share
            </button> -->
            <button v-if="!isMobile" @click="printPDF"
                class="bg-gray-500 hover:bg-gray-600 text-white py-2 flex px-3 lg:px-6 items-center rounded">
                <font-awesome-icon icon="fa-solid fa-print" class="mr-1" />
                Print
            </button>
            <button @click="editTheESTInvoice"
                class="flex bg-blue-700 hover:bg-blue-600 text-white rounded py-2 px-3 lg:px-6 items-center">
                <font-awesome-icon icon="fa-solid fa-pencil" class="mr-1" />
                Edit
                <!-- {{ typeOfInvoice === 'estimation' ? 'Estimation' : 'Invoice' }} -->
            </button>
        </div>
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>
    </div>
</template>

<script>
import axios from 'axios';

export default {
    props: {
        pdfUrl: String,
        isMobile: Boolean,
        exist_data: Object,
        typeOfInvoice: String,
        currentLocalDataList: Object,
        currentInvoice: Object
    },
    data() {
        return {
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 20,
            gap: 5,
            selectedOption: 0,
            signatureOptions: [
                { id: 'noSignature', value: 0, label: 'No Signature' },
                { id: 'withSignature', value: 1, label: 'With Signature' },
                { id: 'computerizeGenerator', value: 2, label: 'Computerize Generator' }
            ],
        };
    },
    computed: {
        pdfViewerSrc() {
            // console.log(this.typeOfInvoice, 'RRRRRRRRR');
            return `${this.pdfUrl}/${this.typeOfInvoice === 'estimation' ? 'view-estimation' : this.typeOfInvoice === 'proforma' ? 'view-proforma' : 'view-invoice'}/${this.exist_data && this.exist_data.id ? this.exist_data.id : ''}/${this.selectedOption}`;
        },
    },
    mounted() {
        if (this.currentInvoice && this.currentInvoice.length > 0) {
            this.selectedOption = this.currentInvoice[0].is_signature;
        }
    },
    methods: {
        async shortenUrl(url) {
            try {
                const targetUrl = 'https://t.track-new.com/api/url/add';
                const response = await axios.post(targetUrl,
                    { url },
                    {
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': 'Bearer rOuXNgCMFSZpvepMQjTmSVVdJcTdoWUA'
                        }
                    }
                );

                if (response.status !== 200) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = response.data;
                if (data.shorturl) {
                    return data.shorturl;
                } else {
                    throw new Error('Shortening URL failed');
                }
            } catch (error) {
                console.error('Error shortening URL:', error);
                return null;
            }
        },
        async downloadPDF() {
            this.open_skeleton = true;
            try {
                if (this.exist_data) {
                    let link_data = `${this.pdfUrl}/${this.typeOfInvoice && this.typeOfInvoice === 'estimation' ? 'download-estimation' : this.typeOfInvoice === 'proforma' ? 'download-proforma' : 'download-invoice'}/${this.exist_data && this.exist_data.id ? this.exist_data.id : ''}/${this.selectedOption}`;

                    // Create an anchor element
                    let anchor = document.createElement('a');
                    anchor.href = link_data;
                    anchor.setAttribute('download', 'invoices'); // Set the download attribute to trigger a download
                    anchor.style.display = 'none';

                    // Append the anchor to the document body and click it programmatically
                    document.body.appendChild(anchor);
                    anchor.click();

                    // Cleanup: remove the anchor from the document body
                    document.body.removeChild(anchor);
                    this.open_skeleton = false;

                }
            } catch (error) {
                this.open_skeleton = false;
                console.error('Error downloading PDF:', error);
            }
        },
        async shareViaWhatsApp() {
            this.open_skeleton = true;
            try {
                if (this.exist_data) {
                    let link_data = `${this.pdfUrl}/${this.typeOfInvoice && this.typeOfInvoice === 'estimation' ? 'download-estimation' : this.typeOfInvoice === 'proforma' ? 'download-proforma' : 'download-invoice'}/${this.exist_data && this.exist_data.id ? this.exist_data.id : ''}/${this.selectedOption}`;
                    // const shortUrl = await this.shortenUrl(link_data);

                    // if (shortUrl) {
                    if (this.isMobile) {
                        const message = encodeURIComponent('Check out this PDF');
                        const whatsappUrl = `whatsapp://send?text=${message}%20${link_data}`;
                        window.open(whatsappUrl, '_blank');
                    } else {
                        const message = encodeURIComponent(`Check out this PDF: ${link_data}`);
                        const whatsappUrl = `https://web.whatsapp.com/send?text=${message}`;
                        window.open(whatsappUrl, '_blank');
                    }
                    // } else {
                    //     alert('Failed to shorten the URL for WhatsApp sharing.');
                    // }
                }
                this.open_skeleton = false;

            } catch (error) {
                console.error('Error sharing via WhatsApp:', error);
                this.open_skeleton = false;
            }
        },
        async shareViaEmail() {
            this.open_skeleton = true;
            try {
                if (this.exist_data) {
                    let link_data = `${this.pdfUrl}/${this.typeOfInvoice && this.typeOfInvoice === 'estimation' ? 'download-estimation' : this.typeOfInvoice === 'proforma' ? 'download-proforma' : 'download-invoice'}/${this.exist_data && this.exist_data.id ? this.exist_data.id : ''}/${this.selectedOption}`;
                    // const shortUrl = await this.shortenUrl(link_data);

                    // if (shortUrl) {
                    const subject = encodeURIComponent('PDF Attachment');
                    const body = encodeURIComponent(`Check out this PDF: ${link_data}`);
                    const mailtoUrl = `mailto:?subject=${subject}&body=${body}`;
                    window.open(mailtoUrl, '_blank');
                    // } else {
                    //     alert('Failed to shorten the URL for email sharing.');
                    // }
                }
                this.open_skeleton = false;
            } catch (error) {
                console.error('Error sharing via email:', error);
                this.open_skeleton = false;
            }
        },
        async printPDF() {
            this.open_skeleton = true;
            try {
                if (this.exist_data) {
                    let link_data = `${this.pdfUrl}/${this.typeOfInvoice && this.typeOfInvoice === 'estimation' ? 'download-estimation' : this.typeOfInvoice === 'proforma' ? 'download-proforma' : 'download-invoice'}/${this.exist_data && this.exist_data.id ? this.exist_data.id : ''}/${this.selectedOption}`;
                    const response = await axios.get(link_data, { responseType: 'blob' });

                    const blob = new Blob([response.data], { type: 'application/pdf' });
                    const url = window.URL.createObjectURL(blob);
                    const printWindow = window.open(url);

                    printWindow.addEventListener('load', () => {
                        printWindow.print();
                    });
                }
                this.open_skeleton = false;
            } catch (error) {
                console.error('Error printing PDF:', error);
                this.open_skeleton = false;
            }
        },
        backToSetting() {
            // this.printing = false;
            if (this.page_name === 'setting') {
                this.$emit('goSetting');
            } else if (this.$route.query.type === 'sales_home') {
                this.$router.go(-1);
            } else if (this.checkRoles(['Sub_Admin', 'admin', 'Account Manager', 'Service Manager', 'Sales man'])) {
                if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                    this.$router.push('/sales');
                } else if (this.typeOfInvoice === 'estimation') {
                    this.$router.push('/estimation');
                } else {
                    this.$router.push('/proforma');
                }
            } else {
                this.$router.push('/');
            }
        },
        editTheESTInvoice() {
            if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                this.$router.push({
                    name: 'sales-invoice',
                    query: { type: 'edit', invoice_no: this.exist_data.id }
                });
            } else if (this.typeOfInvoice === 'estimation') {
                this.$router.push({
                    name: 'addEstimation', // Name of the route
                    params: { type: 'product' }, // Parameter passed in the route path
                    query: { // Query parameters passed in the URL
                        type: 'edit',
                        est_no: this.exist_data.id
                    }
                });
            } else {
                this.$router.push({
                    name: 'addProformaInvoice', // Name of the route
                    params: { type: 'product' }, // Parameter passed in the route path
                    query: {
                        type: 'edit',
                        proforma_no: this.exist_data.id,
                    }
                });
            }
        },
        //---validate the role
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        }

    },
    watch: {
        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.selectedOption = newValue[0].is_signature;
                }
            }
        }
    }
};
</script>
<style>
.iframe-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.iframe-content {
    width: 100vw;
    height: 100vh;
}

iframe::-webkit-scrollbar {
    display: none;
    /* Hide scrollbar for webkit browsers */
}

iframe {
    scrollbar-width: none;
    /* Hide scrollbar for other browsers */
    overflow: auto;
    /* Allow overflow content to be visible */
}
</style>
