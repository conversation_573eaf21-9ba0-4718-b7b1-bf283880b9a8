<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <!-- <div class="modal-content"> -->
            <div class="bg-teal-600 justify-between items-center py-2 flex px-5">
                <h2 class="text-white font-bold text-center ml-2 text-lg">{{ selectedOption === 'sms' ? 'SMS' :
                    selectedOption === 'whatsapp' ? 'WhatsApp' : 'Email' }}</h2>
                <p class="close" @click="cancelModal">&times;</p>
            </div>
            <!-- Content based on selected option -->
            <div class="m-5 pl-5 pr-5">
                <!-- SMS Content -->
                <textarea v-if="selectedOption === 'sms'" v-model="message" rows="10"
                    class="border border-gray-400 mx-auto w-full m-5 p-5" ref="messagetext"></textarea>

                <!-- WhatsApp Content -->
                <textarea v-else-if="selectedOption === 'whatsapp'" v-model="message" rows="10"
                    class="border border-gray-400 mx-auto w-full m-5" ref="messagetext"></textarea>
                <!-- Email Content -->
                <textarea v-else-if="selectedOption === 'email'" v-model="message" rows="10"
                    class="border border-gray-400 mx-auto w-full m-5" ref="messagetext"></textarea>

            </div>

            <!-- Buttons -->
            <div class="flex justify-center items-center m-2">
                <button @click="cancelModal"
                    class="bg-pink-700 bg-text-center text-white text-sm px-5 font-md p-2 mr-12 rounded-full">Cancel</button>
                <button @click="sendModal"
                    class="bg-lime-600 bg-text-center text-white text-sm px-5 font-md p-2 rounded-full">Send</button>
            </div>
            <!-- </div> -->
        </div>
    </div>
</template>
<script>
export default {
    name: 'smsWhatsappEmail',
    props: {
        showModal: Boolean,
        selectedOption: String,
    },
    data() {
        return {
            message: '',
            'overlay-active': this.showModal,
            isMobile: false,
            isOpen: false,

        }
    },
    methods: {
        cancelModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        sendModal() {
            // Handle sending logic here
            console.log('Sending message:', this.message);
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

    },
    mounted() {
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.$nextTick(() => {
                    if (this.$refs.messagetext) {
                        this.$refs.messagetext.focus();
                        this.$refs.messagetext.click();
                    }
                })
            }, 100);
        },
    }
}
</script>
<style scoped>
/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}
</style>