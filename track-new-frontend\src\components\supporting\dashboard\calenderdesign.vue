<!-- components/CustomCalendar.vue -->
<template>
    <div class="relative inline-block">
        <div class="bg-white border border-gray-300 rounded-lg shadow-lg p-4">
            <div class="flex justify-between items-center mb-4">
                <button @click="prevMonth" class="text-gray-500 hover:text-gray-700">
                    <font-awesome-icon icon="fa-solid fa-angles-left" />
                </button>
                <span class="text-lg font-semibold">
                    {{ currentMonthName }} {{ currentYear }}
                </span>
                <button @click="nextMonth" class="text-gray-500 hover:text-gray-700">
                    <font-awesome-icon icon="fa-solid fa-angles-right" />
                </button>
            </div>
            <div class="grid grid-cols-7 gap-2">
                <div v-for="day in daysOfWeek" :key="day" class="text-center font-bold text-gray-700">
                    {{ day }}
                </div>
                <div v-for="(day, index) in daysInMonth" :key="index"
                    class="text-center items-c py-2 px-[10px] rounded-full cursor-pointer hover:bg-blue-500 hover:text-white transition"
                    :class="{
                        'bg-red-500 text-white': isToday(day),
                        'bg-gray-200 text-gray-800': !isToday(day) && day,
                        'opacity-50 cursor-not-allowed': !day
                    }" @click="selectDate(day)">
                    {{ day || '' }}
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        value: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            currentMonth: new Date().getMonth(),
            currentYear: new Date().getFullYear(),
            selectedDate: null
        };
    },
    computed: {
        daysOfWeek() {
            return ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        },
        daysInMonth() {
            const firstDayOfMonth = new Date(this.currentYear, this.currentMonth, 1).getDay();
            const totalDays = new Date(this.currentYear, this.currentMonth + 1, 0).getDate();
            const daysArray = Array.from({ length: firstDayOfMonth }, () => null);
            for (let day = 1; day <= totalDays; day++) {
                daysArray.push(day);
            }
            return daysArray;
        },
        currentMonthName() {
            return new Date(this.currentYear, this.currentMonth).toLocaleString('default', { month: 'long' });
        }
    },
    methods: {
        prevMonth() {
            if (this.currentMonth === 0) {
                this.currentMonth = 11;
                this.currentYear -= 1;
            } else {
                this.currentMonth -= 1;
            }
        },
        nextMonth() {
            if (this.currentMonth === 11) {
                this.currentMonth = 0;
                this.currentYear += 1;
            } else {
                this.currentMonth += 1;
            }
        },
        isToday(day) {
            const today = new Date();
            return (
                day === today.getDate() &&
                this.currentMonth === today.getMonth() &&
                this.currentYear === today.getFullYear()
            );
        },
        selectDate(day) {
            if (day) {
                const selected = new Date(this.currentYear, this.currentMonth, day + 1).toISOString().split('T')[0];
                this.$emit('input', selected);
                console.log(selected, 'What about date data.......');

            }
        }
    },
    watch: {
        value(newValue) {
            if (newValue) {
                const date = new Date(newValue);
                this.currentMonth = date.getMonth();
                this.currentYear = date.getFullYear();
                this.selectedDate = date.getDate();
            }
        }
    }
};
</script>