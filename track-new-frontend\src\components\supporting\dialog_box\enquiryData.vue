<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full sm:w-1/2 transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Website Enquiry
                </p>
                <p class="close text-sm" @click="closeModal"><font-awesome-icon icon="fa-solid fa-xmark" /></p>

            </div>
            <div class="block text-sm bg p-5">
                <!-- Dynamically display itemData -->
                <div class="text-gray-700 grid grid-cols-2 gap-2">
                    <template v-for="(value, key) in filteredData" :key="key">
                        <div class="font-bold px-1 capitalize">{{ formatKey(key) }}:</div>
                        <div v-if="key !== 'status'">{{ key === 'created_at' ? formattedDate(value) : value }}</div>
                        <div v-else>
                            <select v-model="editableStatus" @change="updateStatus" class="border p-1 rounded w-full">
                                <option v-for="(label, key) in statusOptions" :key="key" :value="key">
                                    {{ label }}
                                </option>
                            </select>
                        </div>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        itemData: Object,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            formValues: {},
            message: '',
            statusOptions: {
                0: "Open",
                1: "Completed",
                2: "Cancelled",
            },
            editableStatus: null, // Stores the selected status
        };
    },
    computed: {
        filteredData() {
            // Filter out unwanted keys
            const excludedKeys = ["id", "updated_at", "deleted_at", "company_id"];
            const flatData = {};

            if (this.itemData) {
                Object.keys(this.itemData).forEach((key) => {
                    if (!excludedKeys.includes(key)) {
                        if (typeof this.itemData[key] === "object" && this.itemData[key] !== null) {
                            // Handle nested objects
                            Object.keys(this.itemData[key]).forEach((nestedKey) => {
                                flatData[nestedKey] = this.itemData[key][nestedKey];
                            });
                        } else {
                            flatData[key] = this.itemData[key];
                        }
                    }
                });
            }

            // Set the current status value for editing
            if (flatData.status !== undefined && this.editableStatus === null) {
                this.editableStatus = flatData.status;
            }

            return flatData;
        },
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-Modal');
                this.message = '';
            }, 300);
        },
        //---close message---
        closeMessageDialog() {
            this.isMessage = false;
        },
        //---save terms and condition---
        saveTerms() {
            if (this.formValues.shipping >= 0 && this.formValues.shipping !== '') {
                this.isOpen = false;
                setTimeout(() => {
                    this.$emit('close-Modal', this.formValues);
                }, 300);

            } else {
                // this.isMessage = true;
                this.message = 'Field is empty, Please fill..!';
            }
        },
        updateStatus() {
            // Emit the updated status to the parent component
            this.$emit("status-updated", { ...this.itemData, status: this.editableStatus });
        },
        formatKey(key) {
            if (key) {
                // Capitalize the first letter of the key
                return key.charAt(0).toUpperCase() + key.slice(1).replace(/_/g, " ");
            }
        },
        //---formated display date---
        formattedDate(timestamp) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            date.setHours(date.getHours() + 5); // Add 5 hours
            date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        itemData: {
            deep: true,
            handler(newValue) {
                this.formValues = newValue;
                this.editableStatus = newValue && newValue.status ? newValue.status : 0;
            }
        }

    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>