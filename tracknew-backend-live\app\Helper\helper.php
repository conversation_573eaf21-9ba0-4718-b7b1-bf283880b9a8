<?php

// app/helpers.php

use App\Models\Option;
use App\Models\CompanySettings;
use Illuminate\Support\Facades\DB;

function displaywords($number)
{
    $no = floor($number);
    $point = round($number - $no, 2) * 100;
    $hundred = null;
    $digits_1 = strlen($no);
    $i = 0;
    $str = array();
    $words = array(
        '0' => '',
        '1' => 'one',
        '2' => 'two',
        '3' => 'three',
        '4' => 'four',
        '5' => 'five',
        '6' => 'six',
        '7' => 'seven',
        '8' => 'eight',
        '9' => 'nine',
        '10' => 'ten',
        '11' => 'eleven',
        '12' => 'twelve',
        '13' => 'thirteen',
        '14' => 'fourteen',
        '15' => 'fifteen',
        '16' => 'sixteen',
        '17' => 'seventeen',
        '18' => 'eighteen',
        '19' => 'nineteen',
        '20' => 'twenty',
        '30' => 'thirty',
        '40' => 'forty',
        '50' => 'fifty',
        '60' => 'sixty',
        '70' => 'seventy',
        '80' => 'eighty',
        '90' => 'ninety'
    );
    $digits = array('', 'hundred', 'thousand', 'lakh', 'crore');
    while ($i < $digits_1) {
        $divider = ($i == 2) ? 10 : 100;
        $number = floor($no % $divider);
        $no = floor($no / $divider);
        $i += ($divider == 10) ? 1 : 2;
        if ($number) {
            $plural = (($counter = count($str)) && $number > 9) ? 's' : null;
            $hundred = ($counter == 1 && $str[0]) ? ' and ' : null;
            $str[] = ($number < 21) ? $words[$number] .
                " " . $digits[$counter] . $plural . " " . $hundred
                :
                $words[floor($number / 10) * 10]
                . " " . $words[$number % 10] . " " . $digits[$counter] . $plural . " " . $hundred;
        } else $str[] = null;
    }
    $str = array_reverse($str);
    $result = implode('', $str);
    $points = ($point) ? " and " . $words[$point / 10] . " " .
        $words[$point = $point % 10] : '';
    return ucwords($result . "Rupees Only");
}


if (!function_exists('get_option')) {
    /**
     * Get Settings From Database
     * @param $key
     * @param bool $decode
     * @param $locale
     * @return mixed
     */
    function get_option($key, bool $decode = false, $locale = false, $associative = false): mixed
    {
        if ($locale == true) {
            $cacheKey = $key . $locale;
        } else {
            $cacheKey = $key;
        }

        $cacheKey =
            $option = cache_remember($cacheKey, function () use ($key, $locale) {
                $row = Option::query();
                if ($locale != false) {
                    $row = $row->where('lang', current_locale());
                }
                return  $row = $row->where('key', $key)->first();
            });

        return $decode ? json_decode($option->value ?? '') : $option->value ?? null;
    }
}


if (!function_exists('cache_remember')) {
    /**
     * This function will remember the cache
     * @param string $key
     * @param callable $callback
     * @param integer $ttl
     * @return mixed
     */
    function cache_remember(string $key, callable $callback, int $ttl = 1800): mixed
    {
        return cache()->remember($key, env('CACHE_LIFETIME', $ttl), $callback);
    }
}

if (!function_exists('amount_format')) {

    function amount_format($amount = 0, $icon_type = 'name')
    {
        $currency = get_option('base_currency', true);
        if ($icon_type == 'name') {
            $currency = $currency->position == 'right' ? $currency->name . ' ' . number_format($amount, 2)  :  number_format($amount, 2) . ' ' . $currency->name;
        } elseif ($icon_type == 'both') {
            $currency = $currency->icon . number_format($amount, 2) . ' ' . $currency->name;
        } else {
            $currency = $currency->position == 'right' ? number_format($amount, 2) . $currency->icon : $currency->icon . number_format($amount, 2);
        }

        return $currency;
    }
}

if (!function_exists('checkSmsBalance')) {

    function checkSmsBalance(string $key): mixed
    {
        $company = \App\Models\Companies::with('user.planData')->find($key);
        if (!$company || !$company->user) {
            return 0; // or handle the error as needed
        }
        $user = $company->user;
        $messageLimit = null;
        if ($user->plan) {
            $messageLimit = json_decode($user->plan, true);
        } else {
            // If plan column is null, fetch plan data from plans table based on plan_id
            $plan = \App\Models\Plans::find($user->plan_id);

            if (!$plan) {
                return 0; // or handle the error as needed
            }

            $messageLimit = json_decode($plan->plan_data, true); // Assuming plan_data is the column storing the plan details
        }
        if (!isset($messageLimit['messages_limit'])) {
            return 0; // or handle the error as needed
        }

        $messageLimit = json_decode($company->user->plan, true);

        $totalSmsTransactions = \DB::table('sms_transactions')
            ->where('company_id', $company->id)
            ->count();


        $remainingSmsBalance = $messageLimit['messages_limit'] - $totalSmsTransactions;
        return max(0, $remainingSmsBalance);
    }
}

function saveServiceActivity($data)
{


    // $admin = \App\Models\AppSetting::first();
    //date_default_timezone_set($admin->time_zone ?? 'UTC');
    $data['datetime'] = date('Y-m-d H:i:s');
    $role = auth()->user()->user_type;
    $username = auth()->user()->name;
    $data['ref_id'] = $data['service_id'];

    $services = $data['services'];
    $employeeNames = $services->users->pluck('name')->implode(', ');
    $employeeIds = $services->users->pluck('id')->toArray();

    switch ($data['activity_type']) {
        case "add_service":

            $data['activity_message'] = __('messages.service_added', ['name' => $username]);
            $data['activity_type'] = __('messages.add_service');
            $data['type'] = "App\Models\Services";

            $sendTo = ['admin', 'employee'];
            $activity_data = [
                'service_id' => $data['service_id'],
                'service_code' => isset($data['services']->service_code) ? $data['services']->service_code : '',
                'customer_id' => $data['services']->customer_id,
                'customer_name' => isset($data['services']->customers) ? $data['services']->customers->first_name . ' ' . $data['services']->customers->last_name : '',
                'category_id' => isset($data['services']->category) ? $data['services']->category->id : '',
                'category_name' => isset($data['services']->category) ? $data['services']->category->service_category : '',
            ];
            break;
        case "assigned_service":

            $data['activity_message'] = __('messages.service_assigned', ['name' => $employeeNames, 'admin' => $username]);
            $data['activity_type'] = __('messages.assigned_service');
            $data['type'] = "App\Models\Services";


            $activity_data = [
                'service_id' => $data['service_id'],
                'service_name' => isset($data['services']->service_code) ? $data['services']->service_code : '',
                'customer_id' => $data['services']->customer_id,
                'customer_name' => isset($data['services']->customers) ? $data['services']->customers->first_name . ' ' . $data['services']->customers->last_name : '',
                'category_id' => isset($data['services']->category) ? $data['services']->category->id : '',
                'category_name' => isset($data['services']->category) ? $data['services']->category->service_category : '',

            ];
            $sendTo = ['employees'];
            break;
        case "add_leads":

            $data['activity_message'] = __('messages.lead_added', ['name' => $username]);
            $data['activity_type'] = __('messages.add_lead');
            $data['type'] = "App\Models\Leads";


            $activity_data = [
                'lead_id' => $data['service_id'],
                'title' => isset($data['services']->title) ? $data['services']->title : '',
                'customer_id' => $data['services']->customer_id,
                'customer_name' => isset($data['services']->customers) ? $data['services']->customers->first_name . ' ' . $data['services']->customers->last_name : '',
                'category_id' => '',
                'category_name' => ''
            ];
            $sendTo = ['admin', 'employee'];
            break;
        case "assigned_leads":

            $data['activity_message'] = __('messages.lead_assigned', ['name' => $employeeNames, 'admin' => $username]);
            $data['activity_type'] = __('messages.assigned_lead');
            $data['type'] = "App\Models\Leads";


            $activity_data = [
                'lead_id' => $data['service_id'],
                'title' => isset($data['services']->title) ? $data['services']->title : '',
                'customer_id' => $data['services']->customer_id,
                'customer_name' => isset($data['services']->customers) ? $data['services']->customers->first_name . ' ' . $data['services']->customers->last_name : '',
                'category_id' => '',
                'category_name' => ''
            ];
            $sendTo = ['employees'];
            break;

        default:
            $activity_data = [];
            break;
    }


    $user = auth()->user();

    $notification_data = [

        'id'   => $data['service_id'],
        'type' => $data['type'],
        'subject' => $data['activity_type'],
        'message' => $data['activity_message'],
        "ios_badgeType" => "Increase",
        "ios_badgeCount" => 1
    ];

    $noty = [
        'id'   => $data['service_id'],
        'type' => $data['type'],
        'subject' => $data['activity_type'],
        'message' => $data['activity_message'],
        'category_id' => $activity_data['category_id'],
        'category_name' => $activity_data['category_name'],

    ];



    foreach ($sendTo as $to) {

        switch ($to) {
            case 'admin':

                if ($user->user_type == 'employee') {

                    $admin = \App\Models\User::where('user_type', 'admin')->where('company_id', $user->company_id)->first();
                    if ($admin) {
                        sendNotification('admin', $admin, $notification_data);
                    }
                }


                break;
            case 'employee':
                $user_data = \App\Models\User::where('id', $user->id)->first();


                $notification = \App\Models\Notification::create(
                    array(
                        'id' => Illuminate\Support\Str::random(32),
                        'type' => $data['type'],
                        'notifiable_type' => 'App\Models\User',
                        'notifiable_id' => (int)$user_data->id ?? 0,
                        'data' => json_encode($noty),
                        'company_id' => $user_data->company_id

                    )
                );
                sendNotification($to, $user_data, $notification_data);

                break;
            case 'employees':


                foreach ($employeeIds as $id) {
                    $employee = \App\Models\User::getUserByKeyValue('id', $id);



                    $notification = \App\Models\Notification::create(
                        array(
                            'id' => Illuminate\Support\Str::random(32),
                            'type' => $data['type'],
                            'notifiable_type' => 'App\Models\User',
                            'notifiable_id' => (int)$employee->id ?? 0,
                            'data' => json_encode($noty),
                            'company_id' => $employee->company_id

                        )
                    );

                    sendNotification('employee', $employee, $notification_data);
                }
                break;
            case 'superuser':
                $user = \App\Models\User::getUserByKeyValue('id', $data['booking']->customer_id);
                break;
        }
    }
}

function sendNotification($type, $user_data, $data)
{
    if (!$user_data) {
        \Log::error('No user data found for sending notification.');
        return false; // Return a generic response to the client
    }

    try {
        if (empty($user_data->fcm_token)) {
            // \Log::error("FCM Token is missing for user: " . $user_data->id);
            return false;
        }

        // Fetch Firebase access token
        $accessToken = getFirebaseAccessToken();

        if (!$accessToken) {
            \Log::error('Failed to fetch Firebase access token.');
            return false; // Return a generic response to the client
        }

        // Prepare the notification payload
        $notification = [
            'id' => (string)($data['id'] ?? ''),
            'type' => (string)($data['type'] ?? ''),
            'subject' => (string)($data['subject'] ?? ''),
            'message' => (string)($data['message'] ?? ''),
            "ios_badgeType" => "Increase",
            "ios_badgeCount" => (string)1,
        ];

        // Prepare the FCM message
        $fields = [
            'message' => [
                'token' => $user_data->fcm_token, // Send to specific device token
                'notification' => [
                    'title' => $data['subject'] ?? '',
                    'body' => $data['message'] ?? '',
                ],
                'data' => $notification, // Custom data payload
            ],
        ];

        $fields = json_encode($fields);

        // Initialize cURL
        $ch = curl_init();

        // Set cURL options
        curl_setopt_array($ch, [
            CURLOPT_URL => "https://fcm.googleapis.com/v1/projects/tracknew-b2a8d/messages:send",
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json; charset=utf-8',
                "Authorization: Bearer $accessToken",
            ],
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_HEADER => false,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $fields,
            CURLOPT_SSL_VERIFYPEER => false,
        ]);

        // Execute the request
        $response = curl_exec($ch);

        // Check for cURL errors
        if (curl_errno($ch)) {
            \Log::error('cURL Error in sendNotification: ' . curl_error($ch));
            return false; // Return a generic response to the client
        }

        // Check HTTP response code for errors
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        if ($httpCode < 200 || $httpCode >= 300) {
            \Log::error("HTTP Error in sendNotification: $httpCode. Response: $response");
            return false; // Return a generic response to the client
        }

        // Decode the response to check for FCM-specific errors
        $responseData = json_decode($response, true);
        if (isset($responseData['error'])) {
            \Log::error('FCM Error in sendNotification: ' . ($responseData['error']['message'] ?? 'Unknown error'));
            return false; // Return a generic response to the client
        }

        // Log the successful response for debugging
        //\Log::info('Notification sent successfully. Response: ' . $response);

        return true; // Return success to the client
    } catch (\Exception $e) {
        // Log the exception internally
        \Log::error('Exception in sendNotification: ' . $e->getMessage());
        return false; // Return a generic response to the client
    } finally {
        // Close the cURL session
        if (isset($ch)) {
            curl_close($ch);
        }
    }
}

function getFirebaseAccessToken()
{
    // Check if the access token is cached and still valid
    // $cachedAccessToken = \Cache::get('firebase_access_token');
    //  $cachedExpiryTime = \Cache::get('firebase_access_token_expiry');

    // \Log::error('cache tken : ' . $cachedAccessToken);
    //\Log::error('cache tken exp : ' . $cachedExpiryTime);

    //if ($cachedAccessToken && $cachedExpiryTime && $cachedExpiryTime > now()) {
    //  \Log::error('cache : ' . $cachedAccessToken);
    // Return the cached token if it's not expired
    //    return $cachedAccessToken;
    //}

    // Set the path to your service account key
    $serviceAccountPath = storage_path('app/public/tracknew.json');

    // Use Google API Client to authenticate using service account credentials
    $client = new \Google_Client();
    $client->setAuthConfig($serviceAccountPath);
    $client->addScope('https://www.googleapis.com/auth/firebase.messaging');
    $client->useApplicationDefaultCredentials();

    // Fetch a new access token and its expiry time
    $tokenResponse = $client->fetchAccessTokenWithAssertion();
    $accessToken = $tokenResponse['access_token'];
    //$expiresIn = $tokenResponse['expires_in']; // This gives the expiration time in seconds

    // Calculate the expiration time
    //$expiryTime = now()->addSeconds($expiresIn);

    // Cache the new access token and its expiration time
    //  \Cache::put('firebase_access_token', $accessToken, $expiryTime);
    //  \Cache::put('firebase_access_token_expiry', $expiryTime, $expiryTime);

    // \Log::error('create tken : ' . $accessToken);
    // \Log::error('create tken exp : ' . $expiryTime);

    return $accessToken;
}

function sendWaMessageToServer($mobile_number, $message_data, $company_id)
{
    // Fetch company-specific WhatsApp settings
    $result = CompanySettings::where('company_id', $company_id)->first();

    if ($result && $result->whatsapp_status == 1) {
        // Fetch the instance ID and access token from CompanySettings
        $instanceId = $result->whatsapp_id ?? '609ACF283XXXX'; // Default instance ID
        $accessToken = env('WHATSAPP_KEY');

        // Define the base URL
        $url = "https://cloud.wazender.in/api/send";

        // Prepare query parameters
        $queryParams = [
            'number' => '91' . $mobile_number,
            'message' => is_array($message_data) ? ($message_data['message'] ?? '') : $message_data, // Extract message
            'instance_id' => $instanceId,
            'access_token' => $accessToken,
        ];

        if (is_array($message_data) && !empty($message_data['image'])) {
            $queryParams['media_url'] = 'https://api.track-new.com/images/whatsapp_templates/images/' . $message_data['image'];
            $queryParams['type'] = 'media'; // Set type as media if image exists
        } else {
            $queryParams['type'] = 'text'; // Default type is text
        }

        try {
            // Initialize cURL
            $curl = curl_init();

            // Set cURL options
            curl_setopt_array($curl, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30, // Timeout to avoid hanging indefinitely
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => http_build_query($queryParams),
                CURLOPT_SSL_VERIFYPEER => false, // Disable SSL verification (consider enabling in production)
            ]);

            // Execute the request
            $response = curl_exec($curl);

            // Check for cURL errors
            if (curl_errno($curl)) {
                \Log::error('cURL Error in sendWaMessageToServer: ' . curl_error($curl));
                //throw new \Exception('cURL Error: ' . curl_error($curl));
                return false;
            }

            // Check HTTP response code for errors
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            if ($httpCode < 200 || $httpCode >= 300) {
                \Log::error("HTTP Error in sendWaMessageToServer: $httpCode. Response: $response");
                // throw new \Exception("HTTP Error: $httpCode. Response: $response");
                return false;
            }
        } catch (\Exception $e) {
            // Log or handle the error
            \Log::error('Exception in sendWaMessageToServer: ' . $e->getMessage());
            return false;
            //throw new \Exception("Error in sendWhatsappMessage function: " . $e->getMessage());
        } finally {
            // Close the cURL session
            if (isset($curl)) {
                curl_close($curl);
            }
        }

        return $response; // Return the API response
    }

    // Return an error if WhatsApp is disabled or company settings not found
    return true;
}

function storeMediaFileData($file, $collectionName)
{


    if ($file) {

        // Upload the file(s) to the specified collection
        if (is_array($file)) {
            foreach ($file as $value) {
                $filename = $value->getClientOriginalName();
                \Storage::disk('s3')->putFileAs($collectionName, $value, $filename);
                // Get the URL for the uploaded file
                $url = \Storage::disk('s3')->url("$collectionName/$filename");
                // Optionally, you can perform additional actions with the URL
            }
        } else {
            $filename = $file->getClientOriginalName();

            \Storage::disk('s3')->putFileAs($collectionName, $file, $filename);
            // Get the URL for the uploaded file
            $url = \Storage::disk('s3')->url("$collectionName/$filename");
            // Optionally, you can perform additional actions with the URL
        }

        return $url ?? null; // Return the URL of the uploaded file
    }

    return null;
}

function imageDelete($path)
{


    // Extract the path from the image URL


    // Log the extracted path
    //Log::info('Extracted Path: ' . $path);

    // Decode the path (in case of URL-encoded characters)
    $decoded_path = urldecode($path);

    // Delete the image from storage
    if (Storage::disk('s3')->exists($decoded_path)) {
        Storage::disk('s3')->delete($decoded_path);
        return true;
    } else {
        return false;
    }
}

function timeAgoFormate($date)
{
    if ($date == null) {
        return '-';
    }
    // date_default_timezone_set('UTC');

    $diff_time = \Carbon\Carbon::createFromTimeStamp(strtotime($date))->diffForHumans();

    return $diff_time;
}


function shortenUrl($url, $custom = "", $format = "json")
{
    $data = array(
        "url" => $url
    );


    $curl = curl_init();

    curl_setopt_array($curl, array(
        CURLOPT_URL => "https://t.track-new.com/api/url/add",
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_MAXREDIRS => 2,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_CUSTOMREQUEST => "POST",
        CURLOPT_HTTPHEADER => [
            "Authorization: Bearer rOuXNgCMFSZpvepMQjTmSVVdJcTdoWUA",
            "Content-Type: application/json",
        ],
        CURLOPT_POSTFIELDS => json_encode($data)
    ));

    $response = curl_exec($curl);
    $err = curl_error($curl);
    curl_close($curl);


    if ($err) {
        return $err;
    } else {
        $responseData = json_decode($response, true);

        if (isset($responseData['shorturl'])) {
            $shortUrl = "https://t.track-new.com/TRKNEW/" . basename($responseData['shorturl']);
            return $shortUrl;
        } else {
            return false;
        }
    }
}

function getFinancialYearLabel()
{
    $now = now();
    $year = (int) $now->format('Y');
    $month = (int) $now->format('m');

    if ($month < 4) {
        $start = $year - 1;
        $end = $year;
    } else {
        $start = $year;
        $end = $year + 1;
    }

    return substr($start, -2) . '-' . substr($end, -2); // e.g., 25-26
}

function saveService($data)
{
    $data['datetime'] = date('Y-m-d H:i:s');
    $companyId = null;
    if ($data['services'] == 'Amcs') {
        $amc = DB::table('amc')->where('id', $data['service_id'])->first();
        $companyId = $amc->company_id ?? null;
    } else if ($data['services'] == 'Services') {
        $services = DB::table('services')->where('id', $data['service_id'])->first();
        $companyId = $services->company_id ?? null;
    }
    $user = DB::table('users')->where('company_id', $companyId)->first();
    $role = $user->user_type;
    $username = $user->name;
    $data['ref_id'] = $data['service_id'];

    $services = $data['services'];
    //$employeeNames = $services->user->pluck('name')->implode(', ');
    //$employeeIds = $services->user->pluck('id')->toArray();

    switch ($data['activity_type']) {
        case "reminder":

            $data['activity_message'] = __('messages.reminder', ['service' => $data['services']]);
            $data['activity_type'] = __('messages.reminder', ['service' => $data['services']]);
            $data['type'] = "App\\Models\\" . $data['services'];

            $sendTo = ['admin', 'employee'];
            $activity_data = [
                'service_id' => $data['service_id'],
                'service_code' => '',
                'customer_id' => '',
                'customer_name' => '',
                'category_id' => '',
                'category_name' => '',
            ];
            break;
        default:
            $activity_data = [];
            break;
    }

    $notification_data = [

        'id'   => $data['service_id'],
        'type' => $data['type'],
        'subject' => $data['activity_type'],
        'message' => $data['activity_message'],
        "ios_badgeType" => "Increase",
        "ios_badgeCount" => 1
    ];

    $noty = [
        'id'   => $data['service_id'],
        'type' => $data['type'],
        'subject' => $data['activity_type'],
        'message' => $data['activity_message'],
        'category_id' => $activity_data['category_id'],
        'category_name' => $activity_data['category_name'],

    ];



    foreach ($sendTo as $to) {

        switch ($to) {
            case 'admin':

                if ($user->user_type == 'employee') {

                    $admin = \App\Models\User::where('user_type', 'admin')->where('company_id', $user->company_id)->first();
                    if ($admin) {
                        sendNotification('admin', $admin, $notification_data);
                    }
                }


                break;
            case 'employee':
                $user_data = \App\Models\User::where('id', $user->id)->first();


                $notification = \App\Models\Notification::create(
                    array(
                        'id' => Illuminate\Support\Str::random(32),
                        'type' => $data['type'],
                        'notifiable_type' => 'App\Models\User',
                        'notifiable_id' => (int)$user_data->id ?? 0,
                        'data' => json_encode($noty),
                        'company_id' => $user_data->company_id

                    )
                );
                sendNotification($to, $user_data, $notification_data);

                break;
        }
    }
}
