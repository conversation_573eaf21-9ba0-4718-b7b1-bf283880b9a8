<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\SmsPlans As Plan;
use App\Models\Gateways AS Gateway;
use App\Models\Orders;
use App\Models\User;
//use App\Traits\Notifications;
use Session;
use Auth;
use DB;
use Storage;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class SmsSubscriptionApiController extends Controller
{
   // use Notifications;

   



    public function subscribe(Request $request, $gatewayid, $planid)
    {    
   
        $plan = Plan::where('status', 1)->where('price', '>', 0)->findOrFail($planid);
        $gateway = Gateway::where('status', 1)->findOrFail($gatewayid);
       
        $tax = get_option('tax');

        $tax = $tax > 0 ? ($plan->price / 100) * $tax : 0;
        $total = (double)$tax + $plan->price;
        $payable = $total * $gateway->multiply + $gateway->charge;
      
       
        if ($gateway->min_amount > $payable) {
            return response()->json([
                'status' => 'error',
                'message' => __('The minimum transaction amount is :amount', ['amount' => $gateway->min_amount])
            ], 400);
        }
  
        if ($gateway->max_amount != -1 && $gateway->max_amount < $payable) {
            return response()->json([
                'status' => 'error',
                'message' => __('The maximum transaction amount is :amount', ['amount' => $gateway->max_amount])
            ], 400);
        }

        if ($gateway->is_auto == 0) {
            $request->validate([
                'comment' => ['required', 'string', 'max:500'],
                'image' => ['required', 'image', 'max:2048'], // 2MB
            ]);

            $payment_data['comment'] = $request->input('comment');
            if ($request->hasFile('image')) {
                $path = 'uploads' . '/payments' . date('/y/m/');
                $name = uniqid() . "." . $request->file('image')->extension();
                Storage::put($path . $name, file_get_contents($request->file('image')));
                $payment_data['screenshot'] = Storage::url($path . $name);
            }
        }

      
      
    $txd = 'trd' . date('YmdHis') . rand(1000, 9999);
    DB::table('session_payments')->insert([
        'transaction_id' => $txd,
        'user_id' => Auth::id(),
        'plan_id' => $plan->id,
        'gateway_id' => $gateway->id,
      	'namespace' => $gateway->namespace,
        'amount' => $payable,
        'created_at' => now(),
    ]);
      
       $payment_data = [
         	
            'currency' => $gateway->currency ?? 'USD',
            'email' => Auth::user()->email ?? '<EMAIL>',
            'name' => Auth::user()->name ?? 'Vadivelan',
            'phone' => $request->mobile_number ?? '9629090020',
            'billName' => 'Plan Name: ' . $plan->title,
            'amount' => $total,
            'test_mode' => $gateway->test_mode,
            'charge' => $gateway->charge ?? 0,
            'pay_amount' => str_replace(',', '', number_format($payable)),
            'getway_id' => $gateway->id,
          	'callback_url' => url('api/subscription_sms/success?transaction_id=' . $txd)
        ];

        
        Session::put('plan_id', $plan->id);
        Session::put('user_id', Auth::id());
      	Session::put('gateway_namespace', $gateway->namespace);

        if (!empty($gateway->data)) {
            foreach (json_decode($gateway->data ?? '') ?? [] as $key => $info) {
                $payment_data[$key] = $info;
            }
        }
       
 
       return $gateway->namespace::make_payment($payment_data, $txd);
    }

    public function status(Request $request, $status)
    {
      
      
          $transactionId = $request->query('transaction_id');

          // Retrieve stored payment data
          $paymentSession = DB::table('session_payments')->where('transaction_id', $transactionId)->first();

          if (!$paymentSession) {
              Log::error('Transaction not found for callback', ['transaction_id' => $transactionId]);
              return response()->json(['status' => 'error', 'message' => 'Transaction not found'], 404);
          }
    
      
        

        // Decode the JSON request
        $callbackData = json_decode($request->getContent(), true);
      
        //Log::info('PhonePe Callback received23', ['raw_request' => $callbackData]);

        // Check if the decoding was successful
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('Failed to decode PhonePe callback JSON', ['error' => json_last_error_msg()]);
            return response()->json(['status' => 'error', 'message' => 'Invalid JSON received'], 400);
        }

        // Extract the necessary data from the decoded callback
        if (isset($callbackData['response'])) {
            $decodedResponse = json_decode(base64_decode($callbackData['response']), true);

            if ($decodedResponse['success'] === true && $decodedResponse['code'] === 'PAYMENT_SUCCESS') {
             
			$response = $paymentSession->namespace::verify_payment($paymentSession->m_txd);  
                
			return $status == 'success' ? $this->success($paymentSession) : $this->faild();
               
            }
        }

    
   
      	    
      
     
        
    }

    public function success($paymentSession)
    {
      
        //abort_if(!Session::has('payment_info'), 404);

        //$paymentInfo = Session::get('payment_info');      
      //  Session::forget('payment_info');
    
       //	Session::forget('call_back');

        $plan = Plan::findOrFail($paymentSession->plan_id);
    	$user = User::findOrFail($paymentSession->user_id);   
        $user->message_limit = ($user->message_limit ?? 0) + $plan->count;
        
        $user->save();

        $tax = get_option('tax');
        $tax = $tax > 0 ? ($plan->price / 100) * $tax : 0;

        $order = new Orders;
        $order->plan_id = $plan->id;
        $order->payment_id = $paymentSession->payment_id;
        $order->user_id = $user->id;
        $order->gateway_id = $paymentSession->gateway_id;
        $order->amount = $paymentSession->amount;
        $order->tax = $tax;
        $order->type = 1;
        $order->plan_name = $plan->title;   
        $order->price = $plan->price;
        $order->invoice_no = $paymentSession->transaction_id;
        $order->status = $paymentSession->status ?? 1;
        $order->will_expire = Carbon::now()->addDays($plan->days);
        if (isset($paymentSession->meta)) {
            $order->meta = json_decode($paymentSession->meta, true);
        }
        $order->save();
		 DB::table('session_payments')->where('transaction_id', $paymentSession->transaction_id)->delete();
       // $this->sentOrderMail($order);

        return response()->json([
            'status' => 'success',
            'message' => __('Your subscription payment is complete')
        ]);
    }

    public function faild()
    {
        $plan_id = Session::get('plan_id');

        Session::forget('payment_info');
        Session::forget('call_back');
        Session::forget('plan_id');

        return response()->json([
            'status' => 'error',
            'message' => __('Payment failed, please try again')
        ], 400);
    }

    public function log()
    {
        $orders = Orders::where('user_id', Session::put('user_id'))->with('plan', 'gateway')->latest()->paginate(20);

        return response()->json([
            'status' => 'success',
            'data' => $orders
        ]);
    }
}
