<!-- MainComponent.vue -->
<template>
  <div>
    <main-layout :username="domainName" class="overflow-y-hidden">
      <BuilderLoader v-if="isLoading" :progress="progress" />
      <guest-layout v-if="!isRegistered">
        <WebsiteRegisterForm @registrationComplete="registrationComplete" :checkUsername="checkUsernameAvailability" />
      </guest-layout>
      <guest-layout v-else-if="!isTemplated">
        <TemplateHeadlessPage @templateRegistration="handleTemplateSelection" :username="userName" />
      </guest-layout>
      <div v-else>

        <!-- Main Content -->
        <div class="flex h-screen relative">
          <!-- Main content area -->
          <div class="flex flex-col flex-grow overflow-y-auto">


            <!-- Add your main content here -->
            <div class="builder-page margin-web">
              <div class="flex lg:flex-row flex-col justify-between">
                <div class="w-full lg:p-5 lg:border lg:border-gray-300">

                  <h2 class="text-md font-semibold text-gray-700 mb-3">Preview Domain Details</h2>

                  <header class="flex sm:flex-row flex-col justify-between items-center mb-4">

                    <div class="flex items-center p-4 border">

                      <!-- Icon -->
                      <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mr-4">
                        <font-awesome-icon icon="fa-solid fa-globe" class="text-xl" />
                      </div>

                      <!-- Website Info -->
                      <div>
                        <a :href="reviewDomainUrl" target="_blank" class="text-blue-600 font-semibold hover:underline">
                          {{ reviewDomainUrl }}
                        </a>
                        <p class="text-sm text-gray-500">Created: {{ formatDate(createdAt) }}</p>
                      </div>
                    </div>
                    <div v-if="domainStatus === 0">
                      <!-- <div> -->
                      <button
                        class="bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 transition duration-200"
                        @click="showDomainModal = true">
                        Add Domain Name
                      </button>
                    </div>

                  </header>
                  <h2 class="text-md font-semibold text-gray-700 mb-3">Custom Domain Details</h2>
                  <header v-if="domainStatus === 1 || domainStatus === 2"
                    class="flex justify-between items-center mb-4">
                    <div class="flex sm:flex-row flex-col items-center p-4 border">
                      <!-- Icon -->
                      <div class="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mr-4">
                        <font-awesome-icon icon="fa-solid fa-globe" class="text-xl" />
                      </div>

                      <!-- Website Info -->
                      <div>
                        <a v-if="domainStatus === 1" :href="domainUrl"
                          class="text-blue-600 font-semibold hover:underline" target="_blank">
                          {{ domainUrl }}
                        </a>
                        <span v-else class="text-gray-400 cursor-not-allowed">
                          {{ domainUrl }}
                        </span>
                        <p class="text-sm text-gray-500">Created: {{ formatDate(createdAt) }}</p>
                      </div>
                    </div>
                  </header>
                  <div v-if="domainStatus !== 1"
                    class="bg-red-100 text-red-700 p-4 rounded-md flex items-center justify-between">
                    <span>Connect a domain to your website. To make sure your website is accessible, connect your domain
                      first.</span>
                    <span class="bg-red-500 text-white py-1 px-3 rounded-md">{{ statusText }}</span>
                  </div>

                  <div class="grid grid-cols-2 sm:grid-cols-4 gap-4 mt-6 mb-5">
                    <div class="bg-gray-100 p-4 rounded-md text-center">
                      <p class="text-sm text-gray-500">Domain</p>
                      <p class="text-lg font-semibold">{{ domainStatus === 1 ? 'Permanent' : 'Temporary' }}</p>
                    </div>
                    <div :class="{
                      'bg-green-100 text-green-600': domainStatus === 1,
                      'bg-yellow-100 text-yellow-600': domainStatus === 2,
                      'bg-red-100 text-red-600': domainStatus === 0,
                    }" class="p-4 rounded-md text-center">
                      <p class="text-sm text-gray-500">Domain</p>
                      <p class="text-lg font-semibold">
                        {{ statusText }}
                      </p>
                    </div>


                  </div>


                  <header class="flex flex-col space-y-6 mb-6">


                    <!-- Instructions to Add Custom Domain -->
                    <section v-if="domainStatus !== 1" class="bg-gray-50 p-3 sm:p-6 rounded-lg shadow-md">
                      <h2 class="text-lg font-semibold mb-2">Instructions to Add a Custom Domain</h2>
                      <p class="text-gray-600 mb-4">
                        To connect your custom domain, follow the steps below:
                      </p>
                      <ol class="list-decimal list-inside text-gray-700 space-y-2">
                        <li>Click the "Add Custom Domain" button and enter your domain name in the text field.</li>
                        <li>Ensure the domain name does not include `www` or `https://` prefixes.</li>
                        <li>Point your domain to our server using one of the following methods:</li>
                      </ol>

                      <!-- Methods to Connect Domain -->
                      <div class="mt-6 space-y-6">
                        <!-- Method 1: A Record -->
                        <div>
                          <h3 class="font-bold text-lg mb-2">Method 1: A Record</h3>
                          <p class="text-sm text-gray-600 mb-2">
                            Update your domain's A Record to point to our IP address below:
                          </p>
                          <div class="flex items-center">
                            <input type="text" readonly class="border p-2 rounded-l w-full sm:w-64"
                              :value="aRecordIp" />
                            <button @click="handleCopy(aRecordIp, 'aRecordCopied')"
                              class="bg-gray-200 p-2 rounded-r hover:bg-gray-300">
                              {{ aRecordCopied ? "Copied" : "Copy" }}
                            </button>
                          </div>
                        </div>

                        <!-- Method 2: Nameservers -->
                        <div>
                          <h3 class="font-bold text-lg mb-2">Method 2: Nameservers</h3>
                          <p class="text-sm text-gray-600 mb-2">
                            Update your domain's nameservers to the following:
                          </p>
                          <div class="space-y-2">
                            <div class="flex items-center">
                              <input type="text" readonly class="border p-2 rounded-l w-full sm:w-64" :value="ns1" />
                              <button @click="handleCopy(ns1, 'ns1Copied')"
                                class="bg-gray-200 p-2 rounded-r hover:bg-gray-300">
                                {{ ns1Copied ? "Copied" : "Copy" }}
                              </button>
                            </div>
                            <div class="flex items-center">
                              <input type="text" readonly class="border p-2 rounded-l w-full sm:w-64" :value="ns2" />
                              <button @click="handleCopy(ns2, 'ns2Copied')"
                                class="bg-gray-200 p-2 rounded-r hover:bg-gray-300">
                                {{ ns2Copied ? "Copied" : "Copy" }}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </section>
                  </header>
                </div>
                <div v-if="!isMobile">
                  <sidebar></sidebar>
                </div>
              </div>
            </div>

          </div>

        </div>
      </div>
    </main-layout>
    <!-- Modal -->
    <!-- <div v-if="showDomainModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center">
        <div class="bg-white w-1/2 rounded-lg shadow-lg p-6">
          <h2 class="text-2xl font-bold mb-4">Enter Domain Name</h2>
          
          <div>
            <label for="domainName" class="block font-bold mb-2">Domain Name <span class="text-red-500">*</span></label>
            <input
              type="text"
              id="domainName"
              v-model="domainName"
              class="border p-2 rounded w-full"
              placeholder="Ex. buziness.me"
            />
          </div>

          <div class="flex justify-end mt-6 space-x-4">
            <button
              @click="showDomainModal = false"
              class="bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400"
            >
              Cancel
            </button>
            <button
              @click="handleDomainSubmit"         
            
              class="py-2 px-4 rounded bg-blue-500 text-white"
            >
              Save Domain
            </button>
          </div>
        </div>
      </div> -->
    <div v-if="showDomainModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center">
      <div class="bg-white w-1/2 rounded-lg shadow-lg p-6">
        <h2 class="text-lg font-bold mb-4">Domain Setup</h2>

        <!-- Step 1: Do you have a domain -->
        <div v-if="step === 1">
          <p class="text-lg mb-4">Do you have a domain?</p>
          <div class="flex justify-center space-x-4">
            <button class="bg-green-500 text-white py-2 px-4 rounded-lg hover:bg-green-600" @click="step = 2">
              Yes
            </button>
            <button class="bg-red-500 text-white py-2 px-4 rounded-lg hover:bg-red-600" @click="step = -1">
              No
            </button>
          </div>
        </div>

        <!-- Step 2: Enter Domain Name -->
        <div v-if="step === 2">
          <label for="domainName" class="block font-bold mb-2">Enter Domain Name</label>
          <input type="text" id="domainName" v-model="domainName" class="border p-2 rounded w-full mb-4"
            placeholder="Ex. buziness.me" />
          <ol class="list-decimal list-inside text-gray-700 space-y-2 mb-3">
            <li>Ensure the domain name does not include `www` or `https://` prefixes.</li>

          </ol>
          <div class="flex justify-between">
            <button @click="step = 1" class="bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
              Back
            </button>
            <button @click="step = 3" class="bg-blue-500 text-white py-2 px-4 rounded hover:bg-blue-600">
              Next
            </button>
          </div>
        </div>

        <!-- Step 3: Show Instructions -->
        <div v-if="step === 3">
          <h3 class="text-sm font-bold mb-2">Instructions to Add a Custom Domain</h3>
          <p class="text-gray-600 text-sm mb-2">
            To connect your custom domain, select one of the methods below:
          </p>

          <!-- Radio Buttons to Choose Method -->
          <div class="mb-2">
            <label class="flex items-center mb-2">
              <input type="radio" value="aRecord" v-model="selectedMethod" class="mr-2" />
              <span class="text-gray-700 text-sm font-medium">Method 1: DNS A Record</span>
            </label>
            <label class="flex items-center">
              <input type="radio" value="nameserver" v-model="selectedMethod" class="mr-2" />
              <span class="text-gray-700 text-sm font-medium">Method 2: Nameservers</span>
            </label>
          </div>

          <!-- DNS A Record Section -->
          <div v-if="selectedMethod === 'aRecord'">
            <h3 class="font-bold text-sm mb-2">Method 1: DNS A Record</h3>
            <p class="text-sm text-gray-600 mb-2">
              To connect your custom domain using an A Record, follow the steps below:
            </p>
            <div class="flex items-center mb-2">
              <input type="text" readonly class="border text-sm p-2 rounded-l w-64" :value="aRecordIp" />
              <button @click="handleCopy(aRecordIp, 'aRecordCopied')"
                class="bg-gray-200 p-2 rounded-r hover:bg-gray-300 text-sm">
                {{ aRecordCopied ? "Copied" : "Copy" }}
              </button>
            </div>
            <ol class="list-decimal list-inside text-gray-700 text-sm space-y-2">
              <li>Log in to your domain provider's control panel (e.g., GoDaddy, Namecheap, etc.).</li>
              <li>Navigate to the <strong>DNS Management</strong> or <strong>DNS Settings</strong> section.</li>
              <li>Locate the option to add a new DNS record, and click <strong>Add Record</strong>.</li>
              <li>Set the record type to <strong>A Record</strong>. In the <strong>Name</strong> field, enter your
                domain name or "@" to represent the root domain (e.g., <em>yourdomain.com</em>).</li>

              <li>In the <strong>Value</strong> or <strong>IP Address</strong> field, paste the IP address provided
                above.</li>
              <li>Set the TTL (Time to Live) to the default value (usually 3600 seconds).</li>
              <li>Save the record and wait for DNS propagation, which can take up to 24-48 hours.</li>
            </ol>
          </div>


          <!-- Nameserver Section -->
          <div v-if="selectedMethod === 'nameserver'">
            <h3 class="font-bold text-sm mb-2">Method 2: Nameservers</h3>
            <p class="text-sm text-gray-600 mb-2">
              Update your domain's nameservers to the following:
            </p>
            <div class="space-y-2">
              <div class="flex items-center">
                <input type="text" readonly class="border p-2 text-sm rounded-l w-64" :value="ns1" />
                <button @click="handleCopy(ns1, 'ns1Copied')"
                  class="bg-gray-200 p-2 rounded-r text-sm hover:bg-gray-300">
                  {{ ns1Copied ? "Copied" : "Copy" }}
                </button>
              </div>
              <div class="flex items-center">
                <input type="text" readonly class="border p-2 text-sm rounded-l w-64" :value="ns2" />
                <button @click="handleCopy(ns2, 'ns2Copied')"
                  class="bg-gray-200 p-2 rounded-r text-sm hover:bg-gray-300">
                  {{ ns2Copied ? "Copied" : "Copy" }}
                </button>
              </div>
            </div>
            <!-- Optional Image -->
            <!-- <img src="/images/website_builder/ns.PNG" alt="Nameserver" class="h-10" /> -->
          </div>

          <!-- Navigation Buttons -->
          <div class="flex justify-between mt-6">
            <button @click="step = 2" class="bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
              Back
            </button>
            <button @click="handleDomainSubmit" class="bg-green-500 text-white py-2 px-4 rounded hover:bg-green-600">
              Finish
            </button>
          </div>
        </div>

        <!-- Step -1: No Domain -->
        <div v-if="step === -1">
          <p class="text-lg text-red-500 mb-4">
            Please contact the technical team for assistance with setting up a domain.
          </p>
          <div class="flex justify-center">
            <button @click="showDomainModal = false"
              class="bg-gray-300 text-gray-700 py-2 px-4 rounded hover:bg-gray-400">
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
    <Toaster :show="showModal" :message="message" :type="type_toaster" @update:show="showModal = false"></Toaster>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
  </div>

</template>

<script>
import { markRaw } from 'vue';
import guestLayout from '../../layouts/guestLayout.vue';
import MainLayout from '../../layouts/mainLayout.vue';
// import BreadCrumb from '../../components/website-builder/Breadcrumb.vue';
import WebsiteRegisterForm from './WebsiteRegisterPage.vue';
import TemplateHeadlessPage from './TemplateHeadlessPage.vue';
import BuilderLoader from "../../components/website-builder/BuilderLoader.vue";
import { mapActions, mapGetters } from 'vuex';
import sidebar from '@/layouts/sidebar.vue';
import noAccessModel from '@/components/supporting/dialog_box/noAccessModel.vue';
export default {

  components: {
    MainLayout,
    guestLayout,
    // BreadCrumb,
    WebsiteRegisterForm,
    TemplateHeadlessPage,
    BuilderLoader,
    sidebar,
    noAccessModel
  },

  data() {
    return {
      isLoading: true,
      step: 1,
      progress: 0,
      selectedMethod: null,
      aRecordIp: "**************",
      ns1: "faye.ns.cloudflare.com",
      ns2: "vin.ns.cloudflare.com",
      aRecordCopied: false,
      ns1Copied: false,
      ns2Copied: false,
      showDomainModal: false,
      loading: false,
      domainName: null,
      domainStatus: null,
      isRegistered: false,
      isTemplated: false,

      companyId: null,
      domainUrl: null,
      reviewDomainUrl: null,
      createdAt: null,
      templateId: null,
      webSite: '',
      userName: '',
      //--toaster---
      showModal: false,
      type_toaster: 'success',
      message: '',
      isMobile: false,
      //---no access---
      no_access: false,
    };
  },

  computed: {
    statusText() {
      if (this.domainStatus === 1) {
        return 'Active';
      } else if (this.domainStatus === 2) {
        return 'Please Wait';
      } else {
        return 'Not Connected';
      }
    },
    ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
  },

  mounted() {
    this.fetchWebsiteSettings();
    this.fetchCompanyList();
    this.fetchLocalDataList();

    this.updateIsMobile();
    window.addEventListener('resize', this.updateIsMobile);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateIsMobile);
  },

  methods: {
    ...mapActions('websiteBuilder', ['updateSelectdItem', 'updatePreviewUrl']),
    ...mapActions('localStorageData', ['fetchLocalDataList']),
    ...mapActions('companies', ['fetchCompanyList']),
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
    handleCopy(text, stateVariable) {
      navigator.clipboard
        .writeText(text)
        .then(() => {
          this[stateVariable] = true; // Set "Copied" state
          setTimeout(() => {
            this[stateVariable] = false; // Reset back to "Copy" after 10 seconds
          }, 10000); // 10 seconds
        })
        .catch((err) => {
          console.error("Failed to copy:", err);
        });
    },
    async fetchWebsiteSettings() {
      this.progress = 75;

      const collectForm = localStorage.getItem('track_new');

      if (collectForm) {
        const dataParse = JSON.parse(collectForm);
        this.companyId = dataParse.company_id;
        this.userId = dataParse.user_id + '';

        if (this.companyId) {
          try {
            const response = await axios.get(`/website-list/${this.companyId}`);

            if (response.data.success) {
              const data_one = response.data.data;
              this.progress += 25;
              this.isLoading = false;
              if (Array.isArray(data_one) && data_one.length === 0) {
                this.isRegistered = false;
              } else {
                // Registration section
                this.isRegistered = true;
                //Template Section 
                if (data_one.template_id == 0 && !data_one.template_id) {
                  this.isTemplated = false;
                } else {
                  this.isTemplated = true;
                }
                // Domian Status 
                this.updateDomainStatus(data_one);
                this.createdAt = data_one.created_at;
                this.domainName = data_one.domain_name || '';
                this.userName = data_one.username;
                this.webSite = data_one.website_name

              }
            } else {
              console.error('Error:', response.data.message);
              this.isLoading = true;
            }
          } catch (error) {
            console.error("Failed to fetch initial data:", error);
            this.isRegistered = false;
            this.isLoading = true;
            this.currentPage = markRaw(WebsiteRegisterForm);
          } finally {
            this.progress += 25;
            this.isLoading = false; // End loading
          }
        }
      }
    },
    updateDomainStatus(data_one) {
      this.domainStatus = data_one.domain_status;

      if (data_one.domain_name) {
        this.domainUrl = 'https://' + data_one.domain_name;
      }

      this.reviewDomainUrl = 'https://buziness.me/website/' + data_one.username;
      //---update into store---
      this.updatePreviewUrl(this.reviewDomainUrl);
    },
    formatDate(dateString) {
      const options = { year: "numeric", month: "long", day: "numeric" };
      return new Date(dateString).toLocaleDateString(undefined, options);
    },
    checkUsernameAvailability(username) {
      return axios.post('/website/check-username', { username: username })
        .then(response => {
          return response.data.available; // Returns true if available, false otherwise
        })
        .catch(error => {
          console.error('Error checking username availability:', error);
          return false; // Return false if there's an error
        });
    },

    handleTemplateSelection(template) {
      if (this.getplanfeatures('website')) {
        this.no_access = true;
      } else {

        this.templateId = template.id;

        const registrationData = {
          username: this.userName,
          websiteName: this.webSite,
          company_id: this.companyId,
          template_id: this.templateId
        };

        axios.post('/website/register', registrationData)
          .then(response => {
            //console.log("Website registered successfully:", response.data.data.website_name);

            // this.websiteSettingsData.name = response.data.data.website_name;
            this.isTemplated = true;
            this.fetchWebsiteSettings();
            // Optional: Display a success message to the user
            // alert('Website registered successfully!');
            this.message = `Website registered successfully!`;
            this.type_toaster = 'success';
            this.showModal = true;
          })
          .catch(error => {
            console.error("Error registering website:", error);

            // Handle error response, such as validation errors
            if (error.response && error.response.data) {
              // alert(`Error: ${error.response.data.message || 'Registration failed.'}`);
              this.message = `Error: ${error.response.data.message || 'Registration failed.'}`;
              this.type_toaster = 'warning';
              this.showModal = true;
            } else {
              // alert('An unexpected error occurred. Please try again.');
              this.message = 'An unexpected error occurred. Please try again.';
              this.type_toaster = 'warning';
              this.showModal = true;
            }
          });
      }
    },
    registrationComplete(data) {
      if (this.getplanfeatures('website')) {
        this.no_access = true;
      } else {
        this.userName = data.username;
        this.webSite = data.websiteName
        this.isRegistered = true;
        //console.log(this.userName);
        /// console.log(this.webSite);


        // Call the API to register the website with the provided data
        //   axios.post('/website/register', registrationData)
        //   .then(response => {
        //     console.log("Website registered successfully:", response.data.data.website_name);

        //     this.websiteSettingsData.name = response.data.data.website_name;

        //  // this.fetchWebsiteSettings();
        //     // Optional: Display a success message to the user
        //     alert('Website registered successfully!');
        //   })
        //   .catch(error => {
        //     console.error("Error registering website:", error);

        //     // Handle error response, such as validation errors
        //     if (error.response && error.response.data) {
        //       alert(`Error: ${error.response.data.message || 'Registration failed.'}`);
        //     } else {
        //       alert('An unexpected error occurred. Please try again.');
        //     }
        //   });
      }
    },
    handleDomainSubmit() {
      if (this.getplanfeatures('website')) {
        this.no_access = true;
      } else {
        // Validate and submit the domain
        if (!this.domainName) {
          // alert("Domain name cannot be empty.");
          this.message = 'Domain name cannot be empty.';
          this.type_toaster = 'info';
          this.showModal = true;
          return;
        }

        this.connectDomain();
        this.showDomainModal = false; // Close the modal after submission
      }
    },
    connectDomain() {
      const websiteDomainData = {
        domain_name: this.domainName,
        company_id: this.companyId,
        domain_method: this.selectedMethod
      };

      // Add companyId to the registration data
      this.loading = true;
      axios.post('/website-domain/update', websiteDomainData)
        .then(response => {
          const res = response.data.data;
          this.updateDomainStatus(res);
          this.domainName = res.domain_name
          console.log("Website Domain registered successfully");

          this.loading = false;
          //this.fetchWebsiteSettings();

        })
        .catch(error => {
          console.error("Error Domain registering website:", error);
          this.isLoading = false;
          // Handle error response, such as validation errors
          if (error.response && error.response.data) {
            // alert(`Error: ${error.response.data.message || 'Registration failed.'}`);
            this.message = `Error: ${error.response.data.message || 'Registration failed.'}`;
            this.type_toaster = 'info';
            this.showModal = true;
          } else {
            // alert('An unexpected error occurred. Please try again.');
            this.message = 'An unexpected error occurred. Please try again.';
            this.type_toaster = 'info';
            this.showModal = true;
          }
        });
    },
    //--close no-access model--
    closeNoAccess() {
      this.no_access = false;
    },
    //---plan based on strict--
    getplanfeatures(key) {
      if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
        return true;
      } else {
        return false;
      }
    },


  }
};
</script>

<style>
/* Add any additional styling here if needed */
</style>