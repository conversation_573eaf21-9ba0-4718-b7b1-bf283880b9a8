<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen_poduct, 'scale-0': !isOpen_poduct }">
            <div class="flex items-center justify-center z-50">
                <div class="bg-gray-600 bg-opacity-50" @click="closeModal"></div>

                <div class="bg-white rounded-lg shadow-lg w-full max-w-3xl mx-auto p-6 relative">
                    <div class="flex justify-between items-center border-b mb-3 py-1">
                        <h3 class="font-semibold">Additional RMA Product for Repair</h3>
                        <button @click="closeModal" class="text-gray-600 hover:text-red-600"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>

                    <form @submit.prevent="submitForm">
                        <div class="mb-4">
                            <label class="block text-gray-700">Reference</label>
                            <input type="text" v-model="formValues.part_code"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Product for repair *</label>
                            <div class="relative inline-block w-full" ref="rmaProduct">
                                <div @click="toggleDropdown('product')"
                                    class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                    <span class="px-2 rounded"
                                        :class="{ 'text-gray-400': !formValues.description || formValues.description === '' }">{{
                                            formValues.description && formValues.description !== '' ?
                                                formValues.description :
                                                'Select an Product' }}</span>
                                    <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                </div>

                                <div v-if="isOpen.product"
                                    class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                    <input type="text" ref="productInput" v-model="searchTerm.product"
                                        @keydown.enter="handleEnterKeyProduct('product', filteredOptions)"
                                        @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                        @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                        class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                    <ul class="max-h-60 overflow-auto">
                                        <li v-for="(option, index) in filteredOptions" :key="index"
                                            @click="selectOptionData(option)"
                                            class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                            :class="{ 'bg-gray-200': index === selectedIndex }">
                                            <span class="py-1 px-2 rounded">
                                                {{ option.products.product_name }}
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Serial numbers</label>
                            <input type="text" v-model="formValues.serials"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Quantity *</label>
                            <input type="number" v-model="formValues.quantity"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Part problem</label>
                            <textarea v-model="formValues.part_problem"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3"></textarea>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Part tech notes</label>
                            <textarea v-model="formValues.notes"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3"></textarea>
                        </div>

                        <div class="flex justify-end space-x-2">
                            <button type="button" @click="closeModal"
                                class="bg-gray-500 text-white px-4 py-2 rounded">Cancel</button>
                            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>

export default {
    props: {
        showModal: Boolean,
        currentItems: Object,
    },
    data() {
        return {
            isModalOpen: false,
            formValues: {},
            isOpen_poduct: false,
            isOpen: { product: false },
            searchTerm: { product: '' },
            selectedOption: { product: null },
            selectedIndex: 0,
            //--toaster---
            show: false,
            type_toaster: 'warning',
            message: '',
        };
    },
    computed: {
        filteredOptions() {
            if (this.isOpen.product) {
                return this.currentItems.filter(option =>
                    option.products.product_name.toLowerCase().includes(this.searchTerm.product.toLowerCase())
                );
            }
        }
    },
    methods: {
        openModal() {
            this.isModalOpen = true;
        },
        closeModal(data) {
            this.isModalOpen = false;
            if (data && data.description) {
                this.$emit('close-modal', data);
                this.formValues = {};
            } else {
                this.$emit('close-modal');
            }

        },
        submitForm() {
            if (this.formValues && this.formValues.description && this.formValues.description !== '' && this.formValues.quantity && this.formValues.quantity !== '') {
                // Handle form submission logic here
                this.closeModal(this.formValues);
            } else {
                this.message = this.formValues && (!this.formValues.description || this.formValues.description === '') ? 'Please selecte the product..' : this.formValues && (!this.formValues.quantity || this.formValues.quantity < 0) ? 'Please enter the quantity' : 'Plase fill as * fields...';
                this.show = true;
            }
        },
        handleClickOutside(event, type) {
            if (type !== 'all') {
                if (this.isOpen.product) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaProduct;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.product = false;
                    }
                }
            } else {
                this.isOpen = { product: false };
            }
        },
        toggleDropdown(type) {
            if (type === 'product') {
                this.isOpen.product = !this.isOpen.product;
                this.isOpen.tax = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.product) {
                    this.$nextTick(() => {
                        this.$refs.productInput.focus();
                    });
                }
            }
        },
        selectOptionData(option) {
            if (this.isOpen.product) {
                this.formValues.description = option.products.product_name;
                // this.formValues.description = option.products.product_name;
                // this.formValues.product_id = option.products.id;
                // this.formValues.barcode_id = option.barcodes.id;
                // this.formValues.unit_price = option.sales_price;
                this.isOpen.product = false;
                this.searchTerm.product = '';
            }
        },
        handleEnterKeyProduct(type, list_data) {
            if (type === 'product') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },

    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside($event, 'all'));
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen_poduct = newValue;
            }, 100);
        },

    },
};
</script>

<style>
/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.close {
    color: white;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: red;
    text-decoration: none;
    cursor: pointer;
}
</style>