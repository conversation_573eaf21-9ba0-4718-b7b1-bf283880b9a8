import axios from "axios";

const state = {
  whatsapp: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  updated: false,
  };

  const mutations = {
      SET_WHATSAPP(state, { data}) {
          state.whatsapp = {...data};
    },
      RESET_STATE(state) {
          state.whatsapp = {};
          state.lastFetchTime = null;
        state.isFetching = false;
        state.updated = false;
      },
      SET_LAST_FETCH_TIME(state, time) {
        state.lastFetchTime = time; // Save the timestamp when the API was last accessed
      },
      SET_IS_FETCHING(state, status) {
        state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    WHATSAPP_UPDATED(state, value) {
      state.updated = value;
      
    }  

};
  // Utility function to check if a string is valid JSON
function isValidJson(str) {
    try {
        JSON.parse(str);
        return true; // Valid JSON string
    } catch (e) {
        return false; // Invalid JSON string
    }
}

const actions = {
  updateWhatsappName({ commit }, whatsappData) {
    // Simulate an asynchronous operation (e.g., API call) to update whatsapp name
    setTimeout(() => {
      // Commit mutation to update whatsapp name
      commit('SET_WHATSAPP', whatsappData);
    }, 1000); // Simulated delay of 1 second
  },
  async fetchWhatsappList({ state, commit, rootState }) {
    const now = new Date().toISOString();
    const thirtySecondsInMilliseconds = 5 * 1000;
    // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
    if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || state.updated) {
      return; // Skip request if less than 30 seconds have passed since the last request
    }
    try {
      const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
      if (company_id && company_id !== '') {
        commit('SET_IS_FETCHING', true);
        axios.get(`/company_settings/${company_id}`, {
          params: { company_id: company_id }
        })
          .then(response => {
            // Handle response
            // console.log(response.data, 'Whatsapp list..!');
            let { data } = response.data;
            if (data && data.whatsapp_auth) {
              data.whatsapp_auth = isValidJson(data.whatsapp_auth) ? JSON.parse(data.whatsapp_auth) : null;
            }
            if (data && data.whatsapp) {
              data.whatsapp = isValidJson(data.whatsapp) ? JSON.parse(data.whatsapp) : null;
            }
            commit('WHATSAPP_UPDATED', true);
            commit('SET_WHATSAPP', { data });
            commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
            commit('SET_IS_FETCHING', false);
            return data;
          })
          .catch(error => {
            commit('WHATSAPP_UPDATED', true);
            commit('SET_IS_FETCHING', false);
            return error;
          });
      }
    } catch (error) {
      commit('SET_IS_FETCHING', false);
    }
  },
  async formattedMessage({ commit, state }, {page, status }) {
    let messageTemplate = "Default message template";
    let formattedMessage = '';
    let original_data = {
      customer_name: 'Jhon',
      service_code: 'IGHR123',
      device_brand: 'Apple',
      device_model: 'APP12',
      company_name: 'Eagle',
      long_url: 'www.eagleminds.com',
      final_amount: 1000,
      problem_title: 'Monitor issue',
      service_category: 'Mobile',
      estimate_amount: 2000,
      advance_amount: 1500,
      discount: 10
    }
    
    // Check if the page is "services" and if the status exists in the template
    if (page === "services" && state.whatsapp.whatsapp?.[page]?.[status]?.status) {
      messageTemplate = state.whatsapp.whatsapp[page][status].message;
      // Replace placeholders with actual data
      formattedMessage = messageTemplate.replace(/{(\w+)}/g, (_, key) => data[key] || '');
      return formattedMessage;
    }
  },
  async sendWhatsAppMessage({ commit, state }, { number, type, message, media_url, filename, company_id }) {
    if (state.whatsapp && Object.keys(state.whatsapp).length > 0) {
      const instance_id = state.whatsapp.whatsapp_id ? state.whatsapp.whatsapp_id : '';
      const access_token = '678a1c549f882';
      let payload = {
        number,
        type,
        message: message, // Send the dynamically generated message
        instance_id,
        access_token,
        company_id
      };
    
      // If it's a media message, add media_url and filename
      if (type === 'media') {
        payload.media_url = media_url;
        payload.filename = filename || 'file';
      }
    
      try {
        const response = await axios.post('/whatsapp-message', payload);
        // commit('ADD_MESSAGE', {
        //     number,
        //     type,
        //     message: formattedMessage, // Store the actual sent message
        //     media_url,
        //     status: 'sent',
        // });
        // console.log('send message successfully');
              
        return response.data;
      } catch (error) {
        console.error('Error sending WhatsApp message:', error);
        throw error;
      }
    }
  },
  updateData({ commit }){
    commit('WHATSAPP_UPDATED', false);
  }
  };

  const getters = {
    currentWhatsappData(state) {
      return state.whatsapp;
    },
    companywhatsapp(state) {
      return state.whatsapp && state.whatsapp.whatsapp_id && state.whatsapp.whatsapp_status === 1 ? true : false;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
