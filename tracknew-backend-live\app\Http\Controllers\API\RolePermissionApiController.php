<?php
namespace App\Http\Controllers\API;

use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Models\RolePermission;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;

class RolePermissionApiController extends AppBaseController
{
            /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/permissions",
     *      summary="getPermissionsList",
     *      tags={"Role Permissions"},
     *      description="Get all Permissions",   
    
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Roles")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');
        $roles = Role::with('permissions')->where('company_id', $companyId)->get();

        $permissions = Permission::all();
       
        return response()->json(['role' => $roles, 'permissions' => $permissions]);
    }
    /**
     * @OA\Post(
     *     path="/roles/permissions/assign",
     *     summary="Assign permission to role",
     *     description="Assign a permission to a role",
     *     operationId="assignPermissionToRole",
     *     tags={"Role Permissions"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"role_id","permission_id", "user_id"},
     *             @OA\Property(property="role_id", type="integer", example="1"),
     *             @OA\Property(property="permission_id", type="integer", example="1"),
     *             @OA\Property(property="user_id", type="integer", example="1"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Permission assigned to role successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Permission assigned to role successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Role or Permission not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="Role or Permission not found")
     *         )
     *     ),
     * )
     */
    /**
     * @OA\Post(
     *     path="/roles/permissions/assign",
     *     summary="Assign permission to role",
     *     description="Assign a permission to a role",
     *     operationId="assignPermissionToRole",
     *     tags={"Role Permissions"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"role_id","permission_id"},
     *             @OA\Property(property="role_id", type="integer", example="1"),
     *             @OA\Property(property="permission_id", type="integer", example="1"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Permission assigned to role successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Permission assigned to role successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Role or Permission not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="Role or Permission not found")
     *         )
     *     ),
     * )
     */
//   public function assignPermissionToRole(Request $request)
//     {
//         $request->validate([
//             'role_id' => 'required|exists:roles,id'
//         ]);
//         $permissionIds = $request->permission_id;
//         $permissionIds = array_map('intval', $permissionIds);      
//         $user_role = Role::find($request->role_id);
//         $permission = Permission::find($permissionIds); // Replace 10 with the actual permission ID

//             if ($user_role) {
            
//                 $user_role->syncPermissions([$permission]); // Sync the permission to the role
//                 return response()->json(['message' => 'Permissions assigned to the Role successfully'], 200);
//             } else {
//                 return response()->json(['message' => 'Failure'], 404);
//             }        
//     }

    /**
     * @OA\Post(
     *     path="/roles/permissions/remove",
     *     summary="Remove permission from role",
     *     description="Remove a permission from a role",
     *     operationId="removePermissionFromRole",
     *     tags={"Role Permissions"},
     *     @OA\RequestBody(
     *         required=true,
     *         @OA\JsonContent(
     *             required={"role_id","permission_id"},
     *             @OA\Property(property="role_id", type="integer", example="1"),
     *             @OA\Property(property="permission_id", type="integer", example="1"),
     *         ),
     *     ),
     *     @OA\Response(
     *         response=200,
     *         description="Permission removed from role successfully",
     *         @OA\JsonContent(
     *             @OA\Property(property="message", type="string", example="Permission removed from role successfully")
     *         )
     *     ),
     *     @OA\Response(
     *         response=404,
     *         description="Role or Permission not found",
     *         @OA\JsonContent(
     *             @OA\Property(property="error", type="string", example="Role or Permission not found")
     *         )
     *     ),
     * )
     */
    // public function removePermissionFromRole(Request $request)
    // {
    //     $request->validate([
    //         'role_id' => 'required|exists:roles,id',
    //         'permission_id' => 'required|exists:permissions,id',
    //     ]);

    //     $role = Role::findOrFail($request->role_id);
    //     $permission = Permission::findOrFail($request->permission_id);

    //     $role->permissions()->detach($permission);

    //     return response()->json(['message' => 'Permission removed from role successfully'], 200);
    // }
}
