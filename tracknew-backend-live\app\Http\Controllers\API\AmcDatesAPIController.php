<?php
namespace App\Http\Controllers\API;

use App\Repositories\AmcDatesRepository;
use App\Models\AmcDates;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class AmcDatesAPIController
 * @package App\Http\Controllers\API
 */

class AmcDatesAPIController extends AppBaseController
{
    /** @var  AmcRepository */
   
    private $amcDatesRepository;

    public function __construct(AmcDatesRepository $amcDatesRepo)
    {      
        $this->amcDatesRepository = $amcDatesRepo;
    }


    public function deleteDates(Request $request)
    {
        $request->validate([
            'ids' => 'required|array',
            'ids.*' => 'integer',
        ]);    
      
        $ids = $request->input('ids');        
        $amcs = AmcDates::whereIn('id', $ids)->get();
      
        if ($amcs->isEmpty()) {
            return $this->sendError('No records found to delete');
        }
        
        foreach ($amcs as $amc) {
            $amc->forceDelete();
        }
        return $this->sendSuccess('Records deleted successfully');
    }

}
