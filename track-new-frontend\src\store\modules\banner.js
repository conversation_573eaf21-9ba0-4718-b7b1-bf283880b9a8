import axios from "axios";

const state = {
  bannerClosed: false,
  lastApiCallTime: null, // Store the last time the API was called
  banner_data: null,
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
};

const mutations = {
  SET_BANNER_CLOSED(state, status) {
    state.bannerClosed = status;
  },
  SET_LAST_API_CALL_TIME(state, time) {
    state.lastApiCallTime = time;
  },
  SET_FEATRUEDATA(state, data) {
    state.banner_data = data;
  },
  SET_LAST_FETCH_TIME(state, time) {
    state.lastFetchTime = time; // Save the timestamp when the API was last accessed
  },
  SET_IS_FETCHING(state, status) {
    state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
  },
  RESET_STATE(state) {
    state.bannerClosed = false;
    state.lastApiCallTime = null;
    state.banner_data = null;
    state.lastFetchTime = null;
    state.isFetching = false;
  },
};

const actions = {
  closeBanner({ commit }) {
    commit("SET_BANNER_CLOSED", true);
  },
  openBanner({ commit }) {
    commit("SET_BANNER_CLOSED", false);
  },
  updateLastApiCallTime({ commit }) {
    const currentTime = new Date().getTime();
    commit("SET_LAST_API_CALL_TIME", currentTime);
  },
  getFeatureData({ commit }) {
    const now = new Date().toISOString();
    const thirtySecondsInMilliseconds = 5 * 1000;

    // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
    if ((state.isFetching || (state.lastFetchTime && (new Date(now) - new Date(state.lastFetchTime)) < thirtySecondsInMilliseconds))) { 
      return; // Skip request if less than 30 seconds have passed since the last request
    }
    try {
      const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
      if (company_id && company_id !== '') {
        commit('SET_IS_FETCHING', true);
        axios.get('/feature-update')
          .then(response => {
            // console.log(response.data.data, 'Feature response');
            commit('SET_FEATRUEDATA', response.data.data);
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return;
           
          })
          .catch(error => {
            commit('SET_IS_FETCHING', false);
            console.error('Error', error);
          });
      }
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
}
};

const getters = {
  bannerClosed: (state) => state.bannerClosed,
  lastApiCallTime: (state) => state.lastApiCallTime,
  bannerData: (state) => state.banner_data,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
