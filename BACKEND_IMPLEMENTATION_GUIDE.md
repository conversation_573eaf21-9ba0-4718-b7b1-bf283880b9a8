# Track New - Backend Implementation Guide (Laravel API)

## Project Structure

```
tracknew-backend/
├── app/
│   ├── Http/
│   │   ├── Controllers/
│   │   │   └── API/
│   │   │       ├── AuthController.php
│   │   │       ├── DashboardController.php
│   │   │       ├── ServicesAPIController.php
│   │   │       ├── AmcAPIController.php
│   │   │       ├── LeadsAPIController.php
│   │   │       ├── CustomerAPIController.php
│   │   │       ├── SalesAPIController.php
│   │   │       ├── EstimationAPIController.php
│   │   │       ├── RmaAPIController.php
│   │   │       └── ...
│   │   ├── Middleware/
│   │   ├── Requests/
│   │   └── Resources/
│   ├── Models/
│   │   ├── User.php
│   │   ├── Companies.php
│   │   ├── Services.php
│   │   ├── Customer.php
│   │   ├── Amc.php
│   │   ├── Sales.php
│   │   ├── Leads.php
│   │   └── ...
│   ├── Repositories/
│   ├── Helper/
│   └── Jobs/
├── database/
│   ├── migrations/
│   ├── seeders/
│   └── factories/
├── routes/
│   └── api.php
└── config/
```

## Step 1: Laravel Project Setup

### 1.1 Create New Laravel Project
```bash
composer create-project laravel/laravel tracknew-backend "8.*"
cd tracknew-backend
```

### 1.2 Install Required Packages
```bash
# JWT Authentication
composer require tymon/jwt-auth:dev-develop

# Permissions
composer require spatie/laravel-permission

# Media Library
composer require spatie/laravel-medialibrary

# PDF Generation
composer require barryvdh/laravel-dompdf

# Excel Import/Export
composer require maatwebsite/excel

# API Documentation
composer require darkaonline/l5-swagger

# AWS S3 Storage
composer require league/flysystem-aws-s3-v3

# CORS
composer require fruitcake/laravel-cors

# Database Tools
composer require doctrine/dbal

# Payment Gateway (PhonePe)
# Add to composer.json repositories section and require
```

### 1.3 Configure Environment
```env
APP_NAME="Track New API"
APP_ENV=local
APP_KEY=base64:...
APP_DEBUG=true
APP_URL=http://localhost:8000

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=tracknew
DB_USERNAME=root
DB_PASSWORD=

JWT_SECRET=...

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=
AWS_BUCKET=

MAIL_MAILER=smtp
MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=

FIREBASE_SERVER_KEY=
WHATSAPP_API_KEY=
SMS_API_KEY=
```

## Step 2: Database Design & Migrations

### 2.1 Core Migrations

#### Users Table
```php
Schema::create('users', function (Blueprint $table) {
    $table->id();
    $table->string('name');
    $table->string('email')->unique();
    $table->string('mobile_number')->nullable();
    $table->string('user_name')->nullable();
    $table->string('user_type')->default('employee');
    $table->timestamp('email_verified_at')->nullable();
    $table->string('password');
    $table->unsignedBigInteger('company_id')->nullable();
    $table->unsignedBigInteger('plan_id')->nullable();
    $table->date('will_expire')->nullable();
    $table->boolean('status')->default(1);
    $table->text('fcm_token')->nullable();
    $table->integer('message_limit')->default(0);
    $table->timestamps();
    $table->softDeletes();
});
```

#### Companies Table
```php
Schema::create('companies', function (Blueprint $table) {
    $table->id();
    $table->string('company_name');
    $table->string('company_email')->nullable();
    $table->string('company_phone')->nullable();
    $table->text('company_address')->nullable();
    $table->string('company_logo')->nullable();
    $table->string('gst_number')->nullable();
    $table->string('pan_number')->nullable();
    $table->boolean('status')->default(1);
    $table->timestamps();
});
```

#### Services Table
```php
Schema::create('services', function (Blueprint $table) {
    $table->id();
    $table->string('service_code')->unique();
    $table->unsignedBigInteger('company_id');
    $table->unsignedBigInteger('customer_id');
    $table->unsignedBigInteger('service_category_id')->nullable();
    $table->string('service_type');
    $table->text('problem_description');
    $table->text('solution_description')->nullable();
    $table->enum('priority', ['low', 'medium', 'high'])->default('medium');
    $table->enum('status', ['0', '1', '2', '3', '4', '5'])->default('0');
    $table->decimal('estimated_cost', 10, 2)->nullable();
    $table->decimal('actual_cost', 10, 2)->nullable();
    $table->date('scheduled_date')->nullable();
    $table->time('scheduled_time')->nullable();
    $table->text('notes')->nullable();
    $table->timestamps();
});
```

### 2.2 Additional Core Tables
- **customers** - Customer information
- **leads** - Lead management
- **amc** - Annual maintenance contracts
- **sales** - Sales orders
- **products** - Product catalog
- **estimations** - Service estimates
- **expenses** - Expense tracking
- **invoices** - Invoice management
- **rma** - Return merchandise authorization

## Step 3: Models & Relationships

### 3.1 User Model
```php
class User extends Authenticatable implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable, HasRoles, SoftDeletes;

    protected $fillable = [
        'name', 'email', 'password', 'company_id', 'user_name',
        'user_type', 'mobile_number', 'plan_id', 'will_expire',
        'status', 'fcm_token', 'message_limit'
    ];

    protected $hidden = ['password', 'remember_token'];

    public function company()
    {
        return $this->belongsTo(Companies::class, 'company_id');
    }

    public function services()
    {
        return $this->belongsToMany(Services::class);
    }

    public function planData()
    {
        return $this->belongsTo(Plans::class, 'plan_id');
    }

    // JWT Methods
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }
}
```

### 3.2 Services Model
```php
class Services extends Model
{
    protected $fillable = [
        'service_code', 'company_id', 'customer_id', 'service_category_id',
        'service_type', 'problem_description', 'solution_description',
        'priority', 'status', 'estimated_cost', 'actual_cost',
        'scheduled_date', 'scheduled_time', 'notes'
    ];

    public function company()
    {
        return $this->belongsTo(Companies::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

    public function category()
    {
        return $this->belongsTo(ServiceCategory::class, 'service_category_id');
    }

    public function assigns()
    {
        return $this->hasMany(ServiceAssigns::class, 'service_id');
    }

    public function users()
    {
        return $this->belongsToMany(User::class);
    }
}
```

## Step 4: Authentication & Authorization

### 4.1 JWT Configuration
```php
// config/jwt.php
return [
    'secret' => env('JWT_SECRET'),
    'keys' => [
        'public' => env('JWT_PUBLIC_KEY'),
        'private' => env('JWT_PRIVATE_KEY'),
        'passphrase' => env('JWT_PASSPHRASE', ''),
    ],
    'ttl' => env('JWT_TTL', 60),
    'refresh_ttl' => env('JWT_REFRESH_TTL', 20160),
    'algo' => env('JWT_ALGO', 'HS256'),
];
```

### 4.2 Auth Controller
```php
class AuthController extends Controller
{
    public function login(Request $request)
    {
        $credentials = $request->only(['login', 'password']);
        
        // Determine if login is email or mobile
        $field = filter_var($credentials['login'], FILTER_VALIDATE_EMAIL) 
            ? 'email' : 'mobile_number';
        
        $credentials = [
            $field => $credentials['login'],
            'password' => $credentials['password']
        ];

        if (!$token = JWTAuth::attempt($credentials)) {
            return response()->json(['error' => 'Invalid credentials'], 401);
        }

        $user = JWTAuth::user();
        
        if (!$user || $user->status !== 1) {
            return response()->json(['error' => 'Account not activated'], 401);
        }

        return response()->json([
            'user' => new UserResource($user),
            'token' => $token,
            'expires_in' => auth()->factory()->getTTL() * 60
        ]);
    }

    public function register(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:6',
            'company_name' => 'required|string|max:255',
            'mobile_number' => 'required|string|max:15',
        ]);

        if ($validator->fails()) {
            return response()->json($validator->errors(), 422);
        }

        // Create company first
        $company = Companies::create([
            'company_name' => $request->company_name,
            'company_email' => $request->email,
            'company_phone' => $request->mobile_number,
        ]);

        // Create user
        $user = User::create([
            'name' => $request->name,
            'email' => $request->email,
            'password' => Hash::make($request->password),
            'company_id' => $company->id,
            'mobile_number' => $request->mobile_number,
            'user_type' => 'admin',
        ]);

        // Assign admin role
        $user->assignRole('admin');

        $token = JWTAuth::fromUser($user);

        return response()->json([
            'user' => new UserResource($user),
            'token' => $token,
            'company' => $company
        ], 201);
    }

    public function logout()
    {
        JWTAuth::logout();
        return response()->json(['message' => 'Successfully logged out']);
    }

    public function refresh()
    {
        return response()->json([
            'token' => JWTAuth::refresh()
        ]);
    }
}
```

## Step 5: API Controllers

### 5.1 Services API Controller
```php
class ServicesAPIController extends AppBaseController
{
    private $servicesRepository;

    public function __construct(ServicesRepository $servicesRepo)
    {
        $this->servicesRepository = $servicesRepo;
    }

    public function index(Request $request)
    {
        $user = Auth::user();
        $company_id = $user->company_id;
        
        $services = Services::where('company_id', $company_id)
            ->with(['customer', 'category', 'assigns.user'])
            ->orderBy('created_at', 'desc')
            ->paginate(15);

        return $this->sendResponse($services, 'Services retrieved successfully');
    }

    public function store(CreateServicesAPIRequest $request)
    {
        $input = $request->all();
        $input['company_id'] = Auth::user()->company_id;
        $input['service_code'] = $this->generateServiceCode();

        $service = $this->servicesRepository->create($input);

        return $this->sendResponse($service, 'Service saved successfully');
    }

    public function show($id)
    {
        $service = Services::with(['customer', 'category', 'assigns.user'])
            ->findOrFail($id);

        return $this->sendResponse($service, 'Service retrieved successfully');
    }

    public function update($id, UpdateServicesAPIRequest $request)
    {
        $service = Services::findOrFail($id);
        $service = $this->servicesRepository->update($request->all(), $id);

        return $this->sendResponse($service, 'Service updated successfully');
    }

    public function destroy($id)
    {
        $service = Services::findOrFail($id);
        $service->delete();

        return $this->sendSuccess('Service deleted successfully');
    }

    private function generateServiceCode()
    {
        $company_id = Auth::user()->company_id;
        $year = date('Y');
        $month = date('m');
        
        $lastService = Services::where('company_id', $company_id)
            ->whereYear('created_at', $year)
            ->whereMonth('created_at', $month)
            ->orderBy('id', 'desc')
            ->first();

        $sequence = $lastService ? (int)substr($lastService->service_code, -4) + 1 : 1;
        
        return "SRV{$year}{$month}" . str_pad($sequence, 4, '0', STR_PAD_LEFT);
    }
}
```

### 5.2 Dashboard Controller
```php
class DashboardController extends Controller
{
    public function index(Request $request)
    {
        $user = Auth::user();
        $company_id = $user->company_id;
        $dates = $request->get('dates', []);

        $dashboard = Dashboard::breadboardValues($company_id, $dates);

        return response()->json([
            'success' => true,
            'data' => $dashboard
        ]);
    }

    public function getChartData(Request $request)
    {
        $user = Auth::user();
        $company_id = $user->company_id;
        $type = $request->get('type', 'services');
        $period = $request->get('period', 'month');

        $chartData = $this->generateChartData($company_id, $type, $period);

        return response()->json([
            'success' => true,
            'data' => $chartData
        ]);
    }

    private function generateChartData($company_id, $type, $period)
    {
        // Implementation for chart data generation
        // Based on type (services, sales, leads, etc.) and period
    }
}
```

## Step 6: API Routes

### 6.1 Authentication Routes
```php
// routes/api.php

// Public routes
Route::post('auth/login', [AuthController::class, 'login']);
Route::post('auth/register', [AuthController::class, 'register']);
Route::post('auth/forgot-password', [AuthController::class, 'forgotPassword']);
Route::post('auth/reset-password', [AuthController::class, 'resetPassword']);

// Protected routes
Route::group(['middleware' => ['auth:api']], function () {
    Route::post('auth/logout', [AuthController::class, 'logout']);
    Route::post('auth/refresh', [AuthController::class, 'refresh']);
    Route::get('auth/user', [AuthController::class, 'user']);
    
    // Dashboard
    Route::get('dashboard', [DashboardController::class, 'index']);
    Route::get('dashboard/chart-data', [DashboardController::class, 'getChartData']);
    
    // Services
    Route::resource('services', ServicesAPIController::class);
    Route::post('services/{id}/assign', [ServicesAPIController::class, 'assignService']);
    Route::put('services/{id}/status', [ServicesAPIController::class, 'updateStatus']);
    
    // Customers
    Route::resource('customers', CustomerAPIController::class);
    Route::get('customer-details/{id}', [CustomerAPIController::class, 'fetchCustomerDetails']);
    
    // Leads
    Route::resource('leads', LeadsAPIController::class);
    Route::post('leads/{id}/convert', [LeadsAPIController::class, 'convertToService']);
    
    // AMC
    Route::resource('amcs', AmcAPIController::class);
    Route::get('amc/seven-days-report', [AmcAPIController::class, 'sevenDaysReport']);
    
    // Sales
    Route::resource('sales', SalesAPIController::class);
    Route::post('sales/{id}/payment', [SalesPaymentAPIController::class, 'addPayment']);
    
    // Estimations
    Route::resource('estimations', EstimationAPIController::class);
    Route::get('download-estimation/{id}', [EstimationAPIController::class, 'downloadPDF']);
    
    // RMA
    Route::resource('rmas', RmaAPIController::class);
    Route::get('view-inward/{id}', [RmaAPIController::class, 'viewPDF']);
    
    // Reports
    Route::get('reports/services', [ReportsController::class, 'servicesReport']);
    Route::get('reports/sales', [ReportsController::class, 'salesReport']);
    Route::get('reports/amc', [ReportsController::class, 'amcReport']);
});

// Public tracking
Route::get('service-track/{service_code}', [ServicesAPIController::class, 'getTrack']);
```

## Step 7: Middleware & Security

### 7.1 JWT Middleware
```php
class JWTMiddleware
{
    public function handle($request, Closure $next)
    {
        try {
            $user = JWTAuth::parseToken()->authenticate();
        } catch (Exception $e) {
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                return response()->json(['status' => 'Token is Invalid']);
            } else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                return response()->json(['status' => 'Token is Expired']);
            } else {
                return response()->json(['status' => 'Authorization Token not found']);
            }
        }
        return $next($request);
    }
}
```

### 7.2 Permission Middleware
```php
class PermissionMiddleware
{
    public function handle($request, Closure $next, $permission)
    {
        if (!auth()->user()->can($permission)) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }
        
        return $next($request);
    }
}
```

This backend implementation guide provides the foundation for building the Track New API. Continue with additional controllers, models, and features as needed.
