<template>
    <div>
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <!-- Employees Service Table -->
            <div>
                <h3 class="text-sm font-bold mb-2">Employees Service List</h3>
                <div class="overflow-x-auto">
                    <table class="w-full min-w-max bg-white border border-gray-200">
                        <thead>
                            <tr class="bg-gray-200">
                                <th class="sm:py-2 sm:px-4 py-1 px-2">Employees</th>
                                <th class="sm:py-2 sm:px-4 py-1 px-2">Pending</th>
                                <th class="sm:py-2 sm:px-4 py-1 px-2">Completed</th>
                                <th class="sm:py-2 sm:px-4 py-1 px-2">Canceled</th>
                                <th class="sm:py-2 sm:px-4 py-1 px-2">Total</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(employer, index) in employerList" :key="employer.employer_id"
                                class="cursor-pointer" :class="index % 2 === 0 ? 'bg-gray-100' : 'bg-white'">
                                <td class="sm:py-2 sm:px-4 py-1 px-2 font-semibold hover:text-blue-600"
                                    @click="updateStatusQuery(employer.employer_id, 'total')">{{
                                        employer.employer_name }}</td>
                                <td class="sm:py-2 sm:px-4 py-1 px-2 text-yellow-600 hover:text-yellow-700"
                                    @click="updateStatusQuery(employer.employer_id, 'pending')">{{
                                        employer.pending_services }}
                                </td>
                                <td class="sm:py-2 sm:px-4 py-1 px-2 text-green-600 hover:text-green-700"
                                    @click="updateStatusQuery(employer.employer_id, 'completed')">{{
                                        employer.completed_services }}
                                </td>
                                <td class="sm:py-2 sm:px-4 py-1 px-2 text-red-600 hover:text-green-600"
                                    @click="updateStatusQuery(employer.employer_id, 'canceled')">{{
                                        employer.canceled_services }}</td>
                                <td class="sm:py-2 sm:px-4 py-1 px-2 font-bold hover:text-blue-600"
                                    @click="updateStatusQuery(employer.employer_id, 'total')">{{
                                        employer.total_services }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Overall Service Status Pie Chart -->
            <div class="relative">
                <h3 class="text-sm font-bold mb-2">Overall Service Status</h3>
                <div class="h-40 sm:h-48 lg:h-64">
                    <!-- Check if all values are 0 in serviceStatus array, then show "No Data" -->
                    <div v-if="isEmptyServiceStatus"
                        class="absolute top-1/2 left-1/4 transform -translate-x-1/2 -translate-y-1/2 flex items-center justify-center text-gray-500">
                        <div
                            class="w-24 sm:w-32 lg:w-40 h-24 sm:h-32 lg:h-40 border-4 border-dashed border-gray-400 rounded-full flex items-center justify-center">
                            <span class="text-xs sm:text-sm lg:text-base font-semibold">No Data</span>
                        </div>
                    </div>
                    <Pie :data="chartData" :options="chartOptions" />
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, computed } from 'vue';
import { Pie } from 'vue-chartjs';
import { useRouter } from 'vue-router';
import {
    Chart as ChartJS,
    Title, Tooltip, Legend, ArcElement, CategoryScale
} from 'chart.js';

// Register required Chart.js components
ChartJS.register(Title, Tooltip, Legend, ArcElement, CategoryScale);

export default defineComponent({
    components: { Pie },
    props: {
        employerList: { type: Array, required: true },
        serviceStatus: { type: Array, required: true }
    },
    setup(props) {
        const router = useRouter(); // Get the router instance
        // Check if all values in serviceStatus are 0
        const isEmptyServiceStatus = computed(() => {
            return props.serviceStatus.every(status => status.value === 0);
        });

        const chartData = computed(() => ({
            labels: props.serviceStatus.map(status => status.name),
            datasets: [
                {
                    label: 'Service Count',
                    data: props.serviceStatus.map(status => status.value),
                    backgroundColor: [
                        'rgba(59, 130, 246, 0.7)',   // Blue (Order Taken)
                        'rgba(239, 68, 68, 0.7)',    // Red (Hold)
                        'rgba(234, 179, 8, 0.7)',    // Yellow (In Progress)
                        'rgba(139, 92, 246, 0.7)',   // Violet (New Estimate)
                        'rgba(132, 204, 22, 0.7)',   // Lime (To be Delivered)
                        'rgba(20, 83, 45, 0.7)',     // Dark Green (Delivered)
                        'rgba(239, 68, 68, 0.7)',    // Red (Cancelled)
                        'rgba(22, 163, 74, 0.7)'     // Green (Completed)
                    ],
                    borderColor: [
                        'rgba(59, 130, 246, 1)',
                        'rgba(239, 68, 68, 1)',
                        'rgba(234, 179, 8, 1)',
                        'rgba(139, 92, 246, 1)',
                        'rgba(132, 204, 22, 1)',
                        'rgba(20, 83, 45, 1)',
                        'rgba(239, 68, 68, 1)',
                        'rgba(22, 163, 74, 1)'
                    ],
                    borderWidth: 2,
                    hoverOffset: 10 // Slight pop-out effect on hover
                }
            ]
        }));

        const chartOptions = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: { position: 'right' },
                tooltip: { enabled: true }
            }
        };
        // Method to handle the update of the status query when a service status is clicked
        const updateStatusQuery = (employerId, status) => {
            // For example, you can use the Vue Router to navigate with query parameters:
            router.push({ name: 'services', query: { employerId, status } });
        };


        return { chartData, chartOptions, isEmptyServiceStatus, updateStatusQuery };
    }
});
</script>

<style scoped>
table {
    width: 100%;
    border-collapse: collapse;
}

th,
td {
    border: 1px solid #ddd;
    text-align: left;
}

.chart-container {
    height: 300px;
}
</style>