<template>
    <div v-if="formData && formData.length > 0" class="m-2 mx-auto text-xs w-full px-5">
        <div class="grid grid-cols-2 items-center border-l border-r border-t px-2 py-2">
            <div class="w-full">

                <img v-if="logo_img" :src="logo_img" class="w-48 h-24 object-contain" />
                <div v-else>
                    <p class="font-bold text-green-600 text-xl">{{ formData[0].name }}</p>
                </div>
            </div>
            <div class="grid grid-cols-1">
                <div class="flex py-1 justify-end text-sm">
                    <p class="font-semibold">
                        {{ typeOfInvoice === 'estimation' ? 'Estimation No:' : 'Invoice No:' }}
                    </p>

                    <p class="ml-1 font-semibold">{{ invoice_data.invoice_number }}</p>
                </div>
                <div class="flex py-1 justify-end text-sm">
                    <p class="font-semibold">Date:</p>
                    <p class="ml-1">{{ invoice_data.current_date ? new
                        Date(invoice_data.current_date).toLocaleDateString('en-GB') : currentDate }}</p>
                </div>
            </div>
        </div>
        <div class="flex justify-center items-center border" :style="{ backgroundColor: formData[0].color }">
            <div class="flex justify-center items-center">
                <p class="text-[26px] font-bold py-2" :style="{ color: formData[0].text_color }">
                    {{ typeOfInvoice === 'estimation' ? 'Estimation' : 'Invoice' }}
                </p>
            </div>
            <div v-if="typeOfInvoice !== 'estimation'" class="absolute right-[25px]">
                <select v-model="invoice_data.invoice_type" class="border-none font-bold non-printable outline-none"
                    :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }" disabled>
                    <option value="b2c">B 2 C</option>
                    <option value="b2b">B 2 B</option>
                </select>
            </div>
        </div>
        <div class="flex justify-between items-center border-l border-r pl-2 pt-2 pb-2 overflow-x-auto">
            <div class="items-center">
                <p><strong>From:</strong></p>
                <p class="font-bold">{{ formData[0].name }}</p>
                <p>{{ formData[0].address }}</p>
                <p v-if="formData[0].email !== ''"><strong>Email:</strong> {{ formData[0].email }}</p>
                <p v-if="formData[0].business_contact !== ''"><strong>Contact:</strong> {{ formData[0].business_contact
                    }}</p>
                <p v-if="formData[0].gst_number && formData[0].gst_number !== ''"><strong>GST:</strong> {{
                    formData[0].gst_number }}</p>
            </div>
            <div class="w-1/3 items-center">

                <div v-if="customer_data">
                    <p><strong>To:</strong></p>
                    <p class="font-bold">{{ customer_data.last_name ? customer_data.first_name : '' + ' ' +
                        customer_data.last_name
                        ?
                        customer_data.first_name : '' }}</p>
                    <p v-if="customer_data.address">{{ customer_data.address }}</p>
                    <p v-if="customer_data.email"><strong>Email:</strong> {{ customer_data.email }}</p>
                    <p><strong>Contact:</strong> {{ customer_data.contact_number }}</p>
                    <p v-if="invoice_data.invoice_type === 'b2b'"><strong>GST Tin: </strong>
                        {{ customer_data.gst_number }}
                    </p>
                    <p v-if="invoice_data.shipping_type"><strong>Shipping via:</strong>
                        {{ invoice_data.shipping_type }}
                    </p>
                    <p v-if="invoice_data.cod"><strong>Shipping ID:</strong>
                        {{ invoice_data.cod }}
                    </p>
                </div>
            </div>
        </div>
        <div class="w-full overflow-x-auto">
            <table class="w-full border">
                <thead>
                    <tr class="font-bold text-16px border py-2"
                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">
                        <!-- <th>Sr.no</th> -->
                        <th class="p-1">Item Name</th>
                        <th>HSN Code</th>
                        <th>Qty</th>
                        <th>Price</th>
                        <th>Discount</th>
                        <th>Tax</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>

                    <!---add extra materials-->
                    <tr v-for="(item, index) in items_data" :key="index" class="text-center">
                        <!--Sr.no-->
                        <!-- <td class="p-1 border">
                            {{ index}}
                        </td> -->
                        <!--Product name / description-->
                        <td class="p-1 border w-1/3 relative text-start px-3 py-1">
                            <p><span class="mr-2">{{ index + 1 }}.</span><span class="font-bold">{{ item.product_name }}
                                </span></p>
                            <p v-if="item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0"
                                class="text-[10px] py-1">Serial no: {{ item.serial_no.join(', ') }}</p>
                        </td>
                        <!--HSN code-->
                        <td class="p-1 border">
                            {{ item.hsn_code }}
                        </td>
                        <!--Quantity-->
                        <td class="p-1 border">
                            {{ item.qty }}
                        </td>
                        <!--Price-->
                        <td class="p-1 border">
                            {{ item.price }}
                        </td>
                        <!--discount value-->
                        <td class="p-1 border">
                            {{ item.discount }}
                        </td>
                        <!--tax-->
                        <td class="p-1 border">
                            {{ item.tax }}
                        </td>
                        <!--total value-->
                        <td class="p-1 border">
                            {{ item.total }}
                        </td>
                    </tr>
                    <!-- Add empty rows to fill remaining space -->
                    <tr>
                    <tr>
                        <td colspan="8" :style="{ height: isPrinting ? calculateRemainingSpace() + 'px' : '5px' }"></td>
                    </tr>
                    </tr>
                    <!--Sub total-->
                    <tr v-if="items_data && items_data.length > 0" class="border">
                        <td colspan="5" class="text-end p-1 border-r"><strong>Sub_Total</strong></td>
                        <td colspan="3" class="text-center font-bold">{{ get_all_data.sub_total.toFixed(2) }}
                        </td>
                    </tr>
                    <!--total tax-->
                    <tr v-if="items_data && items_data.length > 0 && formData[0].gstNumber !== ''"
                        :class="{ 'non-printable': get_all_data.total_tax === 0 }">
                        <td colspan="5" class="text-end p-1 border-r">
                            CGST(₹)
                        </td>
                        <td colspan="3" class="text-center">{{ (get_all_data.total_tax / 2).toFixed(2) }}</td>
                    </tr>
                    <tr v-if="items_data && items_data.length > 0 && formData[0].gstNumber !== ''"
                        :class="{ 'non-printable': get_all_data.total_tax === 0 }">
                        <td colspan="5" class="text-end p-1 border">SGST(₹)</td>
                        <td colspan="3" class="text-center border">{{ (get_all_data.total_tax / 2).toFixed(2) }}
                        </td>
                    </tr>
                    <!--shipping charges-->
                    <tr v-if="items_data && items_data.length > 0" class="border"
                        :class="{ 'non-printable': get_all_data.shipping === '' || get_all_data.shipping === 0 }">
                        <td colspan="5" class="text-end p-1 border-r">Shipping(₹)</td>
                        <td colspan="3">
                            <div class="flex items-center justify-center">
                                <p class="p-1 text-center mr-3">{{ get_all_data.shipping }}</p>
                            </div>
                        </td>
                    </tr>
                    <!--discount-->
                    <tr v-if="items_data && items_data.length > 0" class="border"
                        :class="{ 'non-printable': get_all_data.discount_total === '' || get_all_data.discount_total <= 0 }">
                        <td colspan="5" class="text-end p-1 border-r">Discount(₹)</td>
                        <td colspan="3">
                            <div class="flex items-center justify-center">
                                <p class="p-1 text-center mr-3">{{ get_all_data.discount_total }}</p>
                            </div>
                        </td>
                    </tr>
                    <!--grand total-->
                    <tr v-if="items_data && items_data.length > 0" class="border">
                        <td colspan="5" class="text-end p-1 text-md border-r"><strong>Grand Total(₹)</strong></td>
                        <td colspan="3" class="text-center text-md font-bold">
                            {{ formatCurrency(Math.round(get_all_data.grand_total)) }}
                        </td>
                    </tr>
                    <!--Payament type-->
                    <tr v-if="typeOfInvoice !== 'estimation' && paymentData && paymentData.length > 0" class="border"
                        :class="{ 'non-printable': !paymentData[0].paid_amount }">
                        <td colspan="5" class="text-end text-xs p-1 border-r">Payment type</td>
                        <td colspan="3" class="text-center text-sm">
                            <div class="flex items-center justify-center">
                                <table>
                                    <tr v-for="(row, rowIndex) in paymentData" :key="rowIndex">
                                        <td class="w-1/2 text-sm text-center">
                                            <div class="flex justify-around text-xs">
                                                <button class="border rounded px-2 hover:bg-gray-200 mr-1">
                                                    {{ row.paid_type }}
                                                </button>
                                            </div>
                                        </td>
                                        <td class="text-center">
                                            <p class="w-full text-xs text-center">{{ row.paid_amount }}</p>
                                        </td>
                                    </tr>
                                </table>
                            </div>
                        </td>

                    </tr>
                    <!--Return amount or balance-->
                    <tr v-if="typeOfInvoice !== 'estimation' && (return_amount.balance > 0 || return_amount.return > 0)"
                        class="border"
                        :class="{ 'non-printable': return_amount.balance === 0 && return_amount.return === 0 }">
                        <td colspan="5" class="text-end text-sm p-1 border-r text-sm">
                            <!-- <span>{{return_amount}}</span>                         -->
                            <span v-if="return_amount.return > 0">Return(₹)</span>
                            <span v-if="return_amount.balance > 0">Balance(₹)</span>
                        </td>
                        <td colspan="3" class="text-center font-bold">{{ return_amount.balance > 0 ?
                            return_amount.balance : return_amount.return }}</td>
                    </tr>
                    <!-- Display grand total in text format -->
                    <tr class="border">
                        <td colspan="8" class="text-center p-1 text-md border-r"><strong>Total Amount (in words) : {{
                            convertToWords(Math.round(get_all_data.grand_total)) }} Only</strong>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class="flex w-full">
            <div v-if="formData[0].disclaimer !== ''" class="p-3 border-l border-r border-b w-full">
                <p class="font-bold">Terms & Conditions:</p>
                <p class="px-2" v-if="typeOfInvoice !== 'estimation' && formData[0].disclaimer"
                    v-for="(line, index) in formData[0].disclaimer.split('\n')" :key="index">
                    <span>&#11162; </span>{{ line }}
                </p>
                <p class="px-2" v-if="typeOfInvoice === 'estimation' && formData[0].est_disclaimer"
                    v-for="(line, index) in formData[0].est_disclaimer.split('\n')" :key="index">
                    <span>&#11162; </span>{{ line }}
                </p>
            </div>
            <div class="w-1/3 border-r border-b pt-3">
                <p class="text-center px-3 text-xs font-bold">For {{ formData[0].name }}</p>

            </div>
        </div>
        <div class="flex justify-center  items-center mt-5 non-printable">
            <button @click="backToSetting"
                class="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded pl-3 pr-3 mr-12">Back</button>
            <button @click="printInvoice" class="p-2 bg-green-700 hover:bg-green-600 text-white rounded pl-3 pr-3">
                {{ typeOfInvoice === 'estimation' ? 'Print Estimation' : 'Print Invoice' }}
            </button>
        </div>

    </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {

    props: {
        formData: {
            type: Object,
            default: () => ({})
        },
        viewServiceData: {
            type: Object,
            default: () => ({})
        },
        categoryID: {
            type: [Number, String],
            default: null
        },
        typeOfInvoice: {
            type: String,
            default: ''
        },
        service_id: {
            type: [Number, String],
            default: null
        },
        category_name: {
            type: [Number, String],
            default: null
        },
        customer_data: {
            type: Object,
            default: () => ({})
        },
        items_data: {
            type: Object,
            default: () => ({})
        },
        get_all_data: {
            type: Object,
            default: () => ({})
        },
        paymentData: {
            type: Object,
            default: () => ({})
        },
        payment_display: {
            type: Object,
            default: () => ({})
        },
        invoice_data: {
            type: Object,
            default: () => ({})
        },
        return_amount: {
            type: Object,
            default: () => ({})
        },
        logo_img: {
            type: String,
            default: null
        },
        page_name: {
            type: String,
            default: null
        },
        exist_data: {
            type: Object,
            default: () => ({})
        },
        companyId: {
            type: String,
            default: null,
        }
    },
    data() {
        return {
            currentDate: '',
            currentTime: '',
            isPrinting: false
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
    },
    methods: {
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        formatCurrency(value) {
            // Ensure value is a number before using toFixed
            const numericValue = parseFloat(value);
            if (isNaN(numericValue)) {
                // Handle the case where the value is not a valid number
                return "Invalid Amount";
            }

            return `${numericValue.toFixed(2)}`;
        },
        getCurrentDateTime() {
            const now = new Date();
            // Format the date
            const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
            this.currentDate = now.toLocaleDateString(undefined, options).replace(/\//g, '/');
            // Format the time
            this.currentTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        },
        convertToWords(amount) {
            const oneToTwenty = ['Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
            const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

            const convertBelowHundred = (num) => {
                if (num < 20) {
                    return oneToTwenty[num];
                } else {
                    return tens[Math.floor(num / 10)] + ' ' + oneToTwenty[num % 10];
                }
            };

            const convertBelowThousand = (num) => {
                if (num < 100) {
                    return convertBelowHundred(num);
                } else {
                    return oneToTwenty[Math.floor(num / 100)] + ' Hundred ' + convertBelowHundred(num % 100);
                }
            };

            const convertGroup = (num, suffix) => {
                if (num === 0) {
                    return '';
                } else {
                    return convertBelowThousand(num) + ' ' + suffix;
                }
            };

            if (amount === 0) {
                return 'Zero Rupees';
            } else {
                const groups = [];
                let remaining = amount;

                const suffixes = ['', 'Thousand', 'Million', 'Billion', 'Trillion', 'Quadrillion', 'Quintillion', /* and so on... */];

                for (let i = 0; remaining > 0; i++) {
                    const groupValue = remaining % 1000;
                    remaining = Math.floor(remaining / 1000);
                    groups.push(convertGroup(groupValue, suffixes[i]));
                }
                return groups.reverse().join(' ').trim() + ' Rupees';
            }
        },
        //---go back
        backToSetting() {
            // this.printing = false;
            if (this.page_name === 'setting') {
                this.$emit('goSetting');
            } else if (this.$route.query.type === 'sales_home') {
                this.$router.go(-1);
            } else {
                if (this.typeOfInvoice === 'Product' && this.exist_data) {
                    this.$router.push({
                        name: 'sales-invoice',
                        query: { type: 'edit', invoice_no: this.exist_data.id }
                    });

                } else if (this.typeOfInvoice === 'Services' && this.exist_data) {
                    this.getServiceData(this.exist_data);
                } else if (this.typeOfInvoice === 'estimation' && this.exist_data && this.$route.query.back !== 'home') {
                    this.$router.push({
                        name: 'addEstimation', // Name of the route
                        params: { type: 'product' }, // Parameter passed in the route path
                        query: { // Query parameters passed in the URL
                            type: 'edit',
                            est_no: this.exist_data.id
                        }
                    });
                }
                else {
                    this.$router.go(-1);
                }
            }
        },
        printInvoice() {

            this.isPrinting = true;
            // Use window.print() to trigger the browser's print dialog
            this.$emit('sucessPrint');
            setTimeout(() => {
                window.print();
                if (this.page_name !== 'setting' && this.typeOfInvoice !== 'estimation') {
                    this.$router.push('/sales')
                }
                if (this.page_name !== 'setting' && this.typeOfInvoice === 'estimation') {
                    this.$router.push('/estimation')
                }
            }, 300);

            // After printing, reset the state
            // this.printing = false;
            // this.backToSetting();
            // this.$router.go(-1);
        },
        getServiceData(record) {
            axios.get(`/services/${record.service_id}`, { company_id: this.companyId })
                .then(response => {
                    console.log(response.data, 'What happening the get service..!');
                    this.service_data = response.data.data;
                    // let service_track_data = JSON.parse(newValue.service_data);
                    this.$router.push({
                        name: 'generate-invoice',
                        params: { type: this.service_data.servicecategory.service_category, id: this.service_data.servicecategory.id, serviceId: record.service_id },
                        query: { type: 'edit', invoice_no: record.id }
                    });
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        calculateRemainingSpace() {
            // Calculate the height of the printed content
            const printedContentHeight = document.querySelector('.m-2').offsetHeight;
            // Height of A4 paper in pixels (approximately 842px)
            const a4PaperHeight = 842;
            // Calculate the remaining height of the A4 paper
            const remainingHeight = a4PaperHeight - printedContentHeight;
            // Log the calculated remaining space
            console.log('Remaining Space:', remainingHeight);
            console.log(remainingHeight, 'RRRRR');
            // Return the remaining height
            return remainingHeight;
        },
        //---validate the role
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        }
    },

    mounted() {
        this.getCurrentDateTime(); // Update every second if you want a live clock
        setInterval(() => {
            this.getCurrentDateTime();
        }, 60000);
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
    },


};
</script>

<style scoped>
.print-only {
    display: block;
}

.outline {
    border: 1px solid black;
    /* Set your desired border style and color */
    border-radius: 5px;
    padding: 2px;
}

.filter {
    filter: blur(10px);
    /* Initial blur value */
    transition: filter 0.5s ease-in-out;
    /* Smooth transition */
}

.filter:hover {
    filter: blur(0);
    /* Remove blur on hover */
}

.actions-column {
    position: absolute;
    transform: translateY(-0%);
}

@media print {

    /* Additional styles for printing */
    body {
        margin: 0;
        height: 100%;
    }

    /* Define different page sizes based on @page rule */
    @page {
        size: A4;
        /* Default paper size */
    }

    /* Ensure the container fills the entire page */
    .m-2 {
        margin: 5px 5px;
        height: 100%;
    }

    /* Ensure all elements fit within the page */
    * {
        overflow: hidden !important;
    }

    .non-printable {
        display: none;
    }
}
</style>
