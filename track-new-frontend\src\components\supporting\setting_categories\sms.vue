<template>
  <div class="w-full flex items-center">
    <!-- First Child -->
    <div class="w-full lg:w-1/2 sm:w-3/4">
      <div class="mb-4">
        <label class="block mb-1 font-bold" for="apiKey">API Key</label>
        <input class="p-2 border w-full rounded" type="text" id="apiKey" v-model="formValues.apiKey"
          placeholder="Enter Key" />
      </div>

      <div class="mb-4">
        <label class="block mb-1 font-bold" for="hsnNumber">Sender ID</label>
        <input class="p-2 border w-full rounded" type="text" id="hsnNumber" v-model="formValues.sender_id"
          placeholder="Enter HSN number" />
      </div>
      <div>
        <button @click="saveData"
          class="bg-green-600 text-white py-2 px-5 rounded-md mx-auto block mt-4 font-semibold hover:bg-green-500">
          Save
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'sms',
  data() {
    return {
      formValues: {}
    };
  },
};
</script>