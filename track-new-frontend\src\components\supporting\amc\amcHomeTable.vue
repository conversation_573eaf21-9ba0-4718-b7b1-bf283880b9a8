<template>
    <div :class="{ 'manualStyle text-sm mb-[70px]': isMobile, 'text-sm': !isMobile }" ref="scrollContainer"
        @scroll="handleScroll">
        <!---Create new Lead -->
        <div v-if="!open_skeleton && data && data.length === 0 && followup_select === null && status_select === null && Object.keys(searchByCustomer) === 0 && Object.keys(filteredBy) === 0"
            class="flex justify-center items-center">
            <button @click="openLeadModal"
                class="text-green-700 border rounded-full px-3 py-1 hover:border-green-700 shadow-lg">Create your first
                AMC</button>
        </div>
        <!--new design header-->
        <div :class="{ 'my-custom-margin': !isMobile }">
            <!---status view-->
            <div class="custom-scrollbar-hidden px-1"
                :class="{ 'flex overflow-auto bg-white px-2': isMobile, 'grid sm:grid-cols-4 lg:grid-cols-6 gap-2 mt-4': !isMobile }">
                <div class="px-2 cursor-pointer"
                    :class="{ 'border-b-4 border-blue-800 text-blue-800 bg-blue-200': status_select === 'pending', 'bg-yellow-200': status_select !== 'pending', 'shadow border rounded': !isMobile }"
                    @click="getStatusLabel('pending', 1)">
                    <div class="p-1">
                        <div class="p-1 px-2 flex justify-between items-center rounded rounded-full">
                            <span class="pr-1"><font-awesome-icon icon="fa-solid fa-hourglass-half"
                                    :style="{ color: status_select === 'pending' ? 'rgb(30 64 175)' : 'gray' }"
                                    size="lg" /></span>
                            <p>Pending</p>
                            <p class="ml-2">({{ getStatusOption[5].total }})</p>
                        </div>
                    </div>
                </div>
                <div class="px-2 cursor-pointer"
                    :class="{ 'border-b-4 border-blue-800 text-blue-800 bg-blue-200': status_select === 4, 'bg-[#E1B9C9]': status_select !== 4, 'shadow border rounded ': !isMobile }"
                    @click="getStatusLabel(4, 1)">
                    <div class="p-1">
                        <div class="p-1 flex justify-between items-center rounded rounded-full">
                            <span class="pr-1"><font-awesome-icon icon="fa-solid fa-circle-check"
                                    :style="{ color: status_select === 4 ? 'rgb(30 64 175)' : 'gray' }"
                                    size="lg" /></span>
                            <p>All</p>
                            <p class="ml-2">({{ getStatusOption[4].total }})</p>
                        </div>
                    </div>
                </div>

                <div v-for="(opt, index) in getStatusOption" :key="index"
                    class="px-2 cursor-pointer hover:text-blue-700 rounded" @click="getStatusLabel(index, 1)"
                    :class="{ 'border-b-4 border-blue-800 text-blue-800 bg-blue-200': status_select === index, 'bg-white': status_select !== index, 'hidden': index >= 4, 'ml-1': isMobile, 'shadow border rounded hover:text-blue-700': !isMobile }">
                    <div class="p-2 flex justify-between items-center" style="white-space: nowrap;">
                        <span class="pr-1"><font-awesome-icon :icon="opt.icon"
                                :style="{ color: status_select === index ? 'rgb(30 64 175)' : 'gray' }"
                                size="lg" /></span>
                        <p class="inline" style="display: inline;">{{ index === 0 ? 'Open' : index === 1 ?
                            'Inprogress'
                            : index === 2 ? 'Completed' : index ===
                                3 ? 'Cancelled' : 'All' }}</p>
                        <p class="ml-2">({{ opt.total }})</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="my-custom-margin">
            <!--table design-->
            <div v-if="!isMobile" class="flex  justify-between m-1 mt-4">
                <div class="flex mr-2 space-x-4">
                    <button @click="openAmcModal" :class="{ 'mr-2': isMobile }"
                        class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center">
                        <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
                        <span v-if="!isMobile" class="text-center">New AMC</span></button>
                    <!--view design-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="info-msg px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer"
                            @click="toggleView" :title02="'Table view'"
                            :class="{ 'bg-white': items_category === 'tile' }">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200 info-msg"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="'Card view'">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <!----Filter Model-->
                <!-- Main Filter Button -->
                <div ref="dropdownContainerFilter" class="ml-5 relative">
                    <button @click="toggleMainDropdown" :disabled="data.length == 0"
                        class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                        :class="{ 'cursor-not-allowed': data.length == 0 }">
                        <span class="inline-flex items-center w-full pointer-events-none">
                            <font-awesome-icon icon="fa-solid fa-filter" class="px-1" /> Filter
                            <font-awesome-icon v-if="!isMainDropdownOpen" icon="fa-solid fa-angle-down" class="pl-3" />
                            <font-awesome-icon v-if="isMainDropdownOpen" icon="fa-solid fa-angle-up" class="pl-3" />
                        </span>
                    </button>
                    <!-- Main Dropdown -->
                    <div v-if="isMainDropdownOpen" ref="mainDropdown"
                        class="absolute mt-2 w-56 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border right-0">
                        <div class="py-1">
                            <!-- By Due Option with Sub-Dropdown -->
                            <div class="relative">
                                <button @click="toggleSubDropdown" @mouseenter="toggleSubDropdown"
                                    @mouseleave="toggleSubDropdown"
                                    class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200">By
                                    Due</button>

                                <!-- Sub-Dropdown for By Due -->
                                <div v-if="showSubDropdown" @mouseenter="toggleMouserHoverSub"
                                    @mouseleave="toggleMouserHoverSub" ref="subDropdown"
                                    class="absolute right-full top-0 mr-2 w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none border">
                                    <ul class="py-1 text-left">
                                        <li v-for="(option, index) in followupOptions" :key="index"
                                            class="cursor-pointer py-2 px-5 hover:bg-blue-200"
                                            :class="{ 'bg-blue-200 text-blue-700': followup_select === option.value }"
                                            @click="handleFollowup(option.value, 1)">
                                            {{ option.label }}
                                        </li>
                                    </ul>
                                </div>
                            </div>

                            <!-- Other Options -->
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('by Date From')">By Date</button>
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('customer')">By Customer</button>
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('employee')">By Assigned to</button>
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('category')">By Category / Type</button>
                        </div>
                    </div>
                </div>
            </div>
            <!---fiter information && !isMobile-->
            <div v-if="Object.keys(filteredBy).length > 0 || (status_select !== null && status_select !== 4)"
                class="text-xs flex -mb-3 flex-row overflow-auto m-1 text-blue-600">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2"
                    :class="{ 'hidden': key == 'customer_id' || Object.keys(filteredBy).length === 0 }">
                    <p class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                    <p>{{ key === 'assign_to' ? value.join(', ') : key === 'type' ? typeList[value] : key === 'status' ?
                        statusList[value] : value }}</p>
                </div>
                <div v-if="status_select !== null && Object.keys(filteredBy).length === 0">
                    <p>{{ status_select === 'pending' ? 'Pending' : status_select === 0 ? 'Open' : status_select === 1 ?
                        'Inprogress' : status_select === 2 ? 'Completed' : status_select === 3 ? 'Cancelled' : 'All' }}
                    </p>
                </div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1 mx-1">X</button>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton && Array.isArray(data) && data.length > 0" class="text-sm mt-5"
                :class="{ 'm-1': !isMobile }">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                        <!--search bar--->
                        <div>
                            <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                @resetData="resetToSearch" :resetSearch="resetSearch">
                            </searchCustomer>
                        </div>
                    </div>
                    <div v-if="items_category !== 'list'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label === 'Created At' ? 'Date' : column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2"
                                                        :class="{ 'hidden': column.field === 'items' || column.field === 'payment_mode' || column.field === 'discount' || column.field === 'shipping' }">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in paginatedData" :key="index"
                                    class="hover:bg-gray-200 cursor-pointer">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex"
                                        :class="{ 'hidden': !column.visible }" class="px-1 py-2 table-border"
                                        @click="viewRecord(record)">
                                        <span
                                            v-if="!Array.isArray(record[column.field]) && column.field !== 'created_at' && column.field !== 'amc_payment_type' && column.field !== 'amc_status' && column.field !== 'amc_date' && column.field !== 'customer' && column.field !== 'created_by' && column.field !== 'updated_by'"
                                            :class="{ 'text-violet-700': column.field === 'title' }">
                                            {{ record[column.field] }}
                                        </span>
                                        <span v-if="column.field === 'customer'" class="hover:text-sky-700"
                                            @mouseover="showModal(record[column.field], $event)"
                                            @mouseleave="hideModal">
                                            {{ record[column.field].first_name + ' ' + record[column.field].last_name +
                                                ' - ' + record[column.field].contact_number }}
                                        </span>
                                        <span
                                            v-if="column.field === 'created_by' && Object.keys(record[column.field]).length > 0">
                                            {{ record[column.field].name }}
                                        </span>
                                        <span
                                            v-if="column.field === 'updated_by' && Object.keys(record[column.field]).length > 0">
                                            {{ record[column.field].name }}
                                        </span>
                                        <span v-if="column.field === 'created_at'" class="text-blue-800"
                                            :title="calculateDaysAgo(formattedDate(record[column.field]))">
                                            {{ formatDateTime(formattedDate(record[column.field])) }}
                                        </span>
                                        <span v-if="column.field === 'amc_payment_type'"
                                            :class="{ 'text-green-700': record[column.field] == 0, 'text-red-700': record[column.field] == 1, 'text-blue-700': record[column.field] > 1 }">
                                            {{ record[column.field] === 0 ? 'Paid' : record[column.field] === 1 ?
                                                'Unpaid'
                                                : 'Free' }} </span>
                                        <span v-if="column.field === 'amc_status'"
                                            class="text-white px-1 py-1 rounded text-xs"
                                            :class="{ 'bg-yellow-500': record[column.field] == '0', 'bg-[#0D7CBF]': record[column.field] == '1', 'bg-green-500': record[column.field] == '2', 'bg-[#DA1C1C]': record[column.field] == '3' }">
                                            {{ record[column.field] === 0 ? 'Open' : record[column.field] === 1 ?
                                                'Progress'
                                                : record[column.field] === 2 ? 'Completed' : record[column.field] === 3
                                                    ?
                                                    'Cancelled' : '' }}
                                        </span>
                                        <!-- <span
                                            v-if="Array.isArray(record[column.field]) && column.field !== 'amc_date'">{{
                                                column.field === 'assign_to' ?
                                                    record[column.field].map(opt => opt.name).join(',')
                                                    : record[column.field]
                                            }}</span> -->
                                        <span
                                            v-if="column.field === 'assign_to' && Array.isArray(record[column.field]) && record[column.field].length > 0"
                                            v-for="(opt, i) in record[column.field]" :key="i">
                                            <span class="flex flex-wrap gap-1" :class="{ 'mt-1': i > 0 }">
                                                <span
                                                    class="flex items-center px-1 py-0.5 rounded-full text-white text-xs"
                                                    :style="{ color: getUserColor(i), border: `1px solid ${getUserColor(i)}` }">
                                                    <font-awesome-icon icon="fa-solid fa-circle-user" class="pr-1" />
                                                    <span class="text-nowrap">{{ opt.name }}</span>
                                                </span>
                                            </span>
                                        </span>
                                    </td>
                                    <td class="text-center table-border">
                                        <div class="flex justify-center">
                                            <div class="flex relative">
                                                <div class="flex">
                                                    <button v-if="!record.editing" @click="viewRecord(record)"
                                                        class="px-1" title="View">
                                                        <font-awesome-icon icon="fa-solid fa-eye"
                                                            style="color: #8b5cf6;" /></button>
                                                    <button v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                        @click="startEdit(JSON.parse(JSON.stringify(record)))"
                                                        class="px-1" title="Edit">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" />
                                                    </button>
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)" title="Delete"
                                                        class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                    </button>
                                                </div>
                                                <!-- <button @click.stop="displayAction(index)"
                                                    class="text-blue-800 px-2 py-1 rounded"
                                                    :class="{ 'bg-blue-100': display_option === index }">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-7 absolute border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class=" hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="viewRecord(record)"
                                                            class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-eye"
                                                                style="color: #8b5cf6;" />
                                                            <span class="px-2">View</span>
                                                        </button>
                                                    </li>
                                                    <li class=" hover:bg-gray-200">
                                                        <button
                                                            v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                            @click="startEdit(JSON.parse(JSON.stringify(record)))"
                                                            class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                    <td>
                                        <button
                                            class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="openAmcModal">
                                            + AMC
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- Hover Modal -->
                        <customerDataTable v-if="isModalVisible" :showModal="isModalVisible" :modalData="modalData"
                            :modalPosition="modalPosition" @mouseover="toggleHoverModal(true)"
                            @mouseleave="toggleHoverModal(false)" @mousemove="showModal(null, $event)">
                        </customerDataTable>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4 custom-scrollbar-hidden">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <div
                                class="max-w-md mx-auto bg-white rounded-xl shadow-lg overflow-hidden md:max-w-2xl border border-gray-300">
                                <!--first row bg-gradient-to-b from-gray-200 to-gray-300-->
                                <div class="relative z-10 rounded-t-lg">
                                    <div class="flex justify-between items-center py-1 px-2 ">
                                        <div class="flex items-center text-xs">
                                            <p class="flex items-center cursor-pointer text-red-500"
                                                :title="formatDateTime(formattedDate(record['created_at']))">
                                                {{ calculateDaysAgo(formattedDate(record['created_at'])) }}
                                            </p>
                                        </div>
                                        <div class="flex justify-between items-center text-xs">
                                            <!--Status-->
                                            <div>
                                                <p @click="viewRecord(record)" v-if="record['amc_status'] >= 0"
                                                    class="px-3 py-1 rounded rounded-full  cursor-pointer text-center text-white"
                                                    :class="{ 'bg-yellow-100 text-yellow-800': record['amc_status'] == '0', 'bg-blue-400': record['amc_status'] == '1', 'bg-green-500': record['amc_status'] == '2', 'bg-[#DA1C1C]': record['amc_status'] == '3', 'bg-[#8D9689]': record['amc_status'] == '4' }">
                                                    {{ record['amc_status'] == '0' ? 'Open' : record['amc_status'] ==
                                                        '1' ?
                                                        'Progress'
                                                        :
                                                        record['amc_status'] == '2' ? 'Completed' : record['amc_status'] ==
                                                            '3'
                                                            ?
                                                            'Cancelled' :
                                                            record['amc_status'] == '4' ? 'Hold' : '' }}</p>
                                            </div>
                                            <!--menu-->
                                            <div class="flex justify-end relative ml-2">
                                                <button @click.stop="displayAction(index)"
                                                    class="bg-blue-100 text-blue-800 px-2 py-1 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button>
                                                <!--dropdown-->
                                                <div v-if="display_option === index" :ref="'dropdown' + index"
                                                    class="z-10 mt-7 absolute border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                    style="display: flex; justify-content: center; align-items: center;">
                                                    <ul class="py-1 text-gray-700"
                                                        aria-labelledby="dropdownDefaultButton">
                                                        <li class="hover:bg-gray-200">
                                                            <button v-if="!record.editing" @click="viewRecord(record)"
                                                                class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-eye"
                                                                    style="color: #8b5cf6;" size="lg" />
                                                                <span class="px-2">View</span>
                                                            </button>
                                                        </li>
                                                        <li class="hover:bg-gray-200">
                                                            <button
                                                                v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                                @click="startEdit(record)"
                                                                class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-pencil"
                                                                    style="color: #3b82f6;" size="lg" />
                                                                <span class="px-2">Edit</span>
                                                            </button>
                                                        </li>
                                                        <li class="hover:bg-gray-200">
                                                            <button v-if="!record.editing && checkRoles(['admin'])"
                                                                @click="confirmDelete(index)"
                                                                class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                    style="color: #ef4444" size="lg" />
                                                                <span class="px-2">Delete</span>
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!---second row-->
                                    <div class="flex px-4 items-center">
                                        <div class="h-10 w-10  rounded-full flex items-center justify-center text-white font-bold mr-4"
                                            :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                            {{ record.customer && record.customer.first_name ?
                                                record.customer.first_name[0].toUpperCase() : 'C' }}
                                        </div>
                                        <div>
                                            <h4 class="text-sm leading-6 font-semibold cursor-pointer"
                                                @click="viewRecord(record)">{{ record.customer &&
                                                    record.customer.first_name ? (record.customer.first_name
                                                        + ' ' + (record.customer.last_name ? record.customer.last_name : '')) :
                                                    ''
                                                }}
                                            </h4>
                                            <p class="text-sm font-medium text-gray-600 cursor-pointer"
                                                @click="dialPhoneNumber(record['customer'].contact_number)">+91-{{
                                                    record.customer.contact_number }}</p>
                                        </div>
                                    </div>
                                    <div class="mt-2 bg-gray-100 py-1 mx-4 px-2 rounded-lg">
                                        <!---Title-->
                                        <p @click="viewRecord(record)" class="text-sm font-semibold cursor-pointer">
                                            {{ record['title'] ? record['title'] : '' }}
                                        </p>
                                        <!--no of services-->
                                        <div class="flex justify-between items-center text-sm sm:text-xs">
                                            <p class="text-gray-800">No. of Services: <span
                                                    class="bg-red-200 text-red-800 font-semibold px-2 py-1 rounded-full">{{
                                                        record['number_of_service'] }}</span>
                                            </p>
                                            <!-- Payment Type -->
                                            <div class="py-1">
                                                <h4 class="font-medium text-gray-900 inline-block">Payment Type:</h4>
                                                <span class="inline-block px-2 py-1 ml-2 rounded-md bg-gray-200">{{
                                                    record['amc_payment_type'] === 0 ? 'Paid' :
                                                        record['amc_payment_type'] === 1 ? 'Unpaid' :
                                                            record['amc_payment_type'] === 2 ? 'Free' : '' }}</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Assigned To -->
                                    <div class="items-center px-4 mt-2 cursor-pointer" @click="viewRecord(record)">
                                        <!-- <h4 class="font-medium text-gray-900 mr-2">Assigned To:</h4> -->
                                        <div class="relative">
                                            <!-- First Circle -->
                                            <div class="absolute  left-1 z-10">
                                                <i class="fas fa-user-circle text-blue-300 text-lg"></i>
                                            </div>
                                            <!-- Second Circle -->
                                            <div class="absolute  left-4 z-20">
                                                <i class="fas fa-user-circle text-red-300 text-lg"></i>
                                            </div>
                                            <!-- Third Circle -->
                                            <div class="absolute  left-7 z-30">
                                                <i class="fas fa-user-circle text-purple-300 text-lg"></i>
                                            </div>
                                        </div>
                                        <div class="flex flex-wrap ml-[50px]">
                                            <p v-if="Array.isArray(record['assign_to']) && record['assign_to'].length > 0"
                                                v-for="(opt, i) in record['assign_to']" :key="i">
                                                <span v-if="record['assign_to'].length > 0"
                                                    class="inline-flex items-center px-3 py-1 rounded text-sm font-medium mr-2 mb-2"
                                                    :class="{ 'bg-blue-100 text-blue-800': i === 0 || i % 3 === 0, 'bg-blue-100 text-blue-800': i % 2 === 0, 'bg-green-100 text-green-800': (i === 1 || i % 1 === 0) && i % 2 !== 0 && i % 3 !== 0 }">
                                                    {{ opt.name }}
                                                </span>
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && pagination.last_page > 0 && pagination.last_page === pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>


                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="pagination.last_page && !isMobile">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>
                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span>
                                    </button>
                                </li>
                                <!-- <li v-for="pageNumber in pagination.last_page" :key="pageNumber">
                        <button @click="updatePage(pageNumber)"
                            :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }" class="px-3 py-2 rounded text-black ho
                            ver:bg-teal-500 sm:text-md text-xs">{{ pageNumber}}</button>
                    </li> -->
                                <!-- Page numbers -->
                                <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                            pageNumber }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== pagination.last_page }">
                                    <button @click="updatePage(currentPage + 1)"
                                        :disabled="currentPage === pagination.last_page"
                                        class="px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!---in mobile view Filter Service-->
        <div v-if="isMobile" ref="dropdownContainerFilter">
            <div class="fixed bottom-36 right-5 z-50 bg-green-700 rounded-full">
                <div class="flex justify-end">
                    <button @click="toggleMainDropdown" type="button"
                        class="flex items-center justify-center px-[10px] py-2 text-white "
                        :class="{ 'cursor-not-allowed blur-sm': data.length == 0 }" :disabled="data.length == 0">
                        <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                    </button>
                </div>
            </div>
            <!-- Main Dropdown -->
            <div v-if="isMainDropdownOpen" ref="mainDropdown"
                class="fixed bottom-48 sm:bottom-48 left-full transform -translate-x-full w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border">
                <div class="py-1">
                    <!-- By Due Option with Sub-Dropdown -->
                    <div class="relative">
                        <button @click="toggleSubDropdown"
                            class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200">By
                            Due</button>

                        <!-- Sub-Dropdown for By Due -->
                        <div v-if="showSubDropdown" @mouseenter="toggleMouserHoverSub"
                            @mouseleave="toggleMouserHoverSub" ref="subDropdown"
                            class="absolute right-full top-0 ml-2 w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none border">
                            <ul class="py-1 text-left">
                                <li v-for="(option, index) in followupOptions" :key="index"
                                    class="cursor-pointer py-2 px-5 hover:bg-blue-200"
                                    :class="{ 'bg-blue-200 text-blue-700': followup_select === option.value }"
                                    @click="handleFollowup(option.value, 1)">
                                    {{ option.label }}
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Other Options -->
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('by Date From')">By Date</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('customer')">By Customer</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('employee')">By Assigned to</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('category')">By Category / Type</button>
                </div>
            </div>
        </div>
        <!---in mobile view create new lead-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openAmcModal" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!-- Use the SalesModal component -->
        <addAmc :show-modal="showAmcModal" @closeAmcModal="closeAmcModal" :editData="editData" :type="typeOfAmc"
            :companyId="companyId" :userId="userId">
        </addAmc>
        <!--Filter-->
        <amcFilter :showModal="lead_filter" @closeFilter="closeLeadFilter" :typeList="typeList" :statusList="statusList"
            :selectedByValue="selectedByValue"></amcFilter>
        <Loader :showModal="open_loader"></Loader>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'amc'"></bottombar> -->
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
    </div>
</template>

<script>
import confirmbox from '../dialog_box/confirmbox.vue';
import dialogAlert from '../dialog_box/dialogAlert.vue';
import addAmc from '../dialog_box/addAmc.vue';
import amcFilter from '../dialog_box/filter_Modal/amcFilter.vue';
// import bottombar from '../dashboard/bottombar.vue';
import { mapActions, mapGetters } from 'vuex';
import customerDataTable from '../dialog_box/customerDataTable.vue';
import searchCustomer from '../customers/searchCustomer.vue';
import noAccessModel from '../dialog_box/noAccessModel.vue';

export default {
    name: 'leads_home',
    emits: ['updateIsOpen', 'dataToParent'],
    components: {
        confirmbox,
        dialogAlert,
        addAmc,
        amcFilter,
        // bottombar,
        customerDataTable,
        searchCustomer,
        noAccessModel
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            //---
            showAmcModal: false,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            columns: [],
            serviceList: [],
            data: [],
            originalData: [],
            isImportModalOpen: false,
            open_message: false,
            message: '',
            //---sales model--
            showSalesModal: false,
            selectedSalesType: '',
            selectedServiceType: '',
            serviceCategoriesList: [],
            editData: null,
            typeOfAmc: 'add',
            leadType: [],
            leadStatus: [],
            //---filter--
            showFilterOptions: false,
            mouseOverIsNot: false,
            filterOptions: ['by Date', 'by Customer', 'by Employee', 'by category/Type', 'by Status', 'Custom'],
            selectedByValue: null,
            lead_filter: false,
            typeList: ['Paid', 'Unpaid', 'Free'],
            statusList: ['Open', 'Progress', 'Closed'],
            filteredBy: {},
            //--api integration
            companyId: null,
            pagination: {},
            userId: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 8,
            number_of_rows: 10,
            gap: 4,
            followupOptions: [
                { label: 'All', value: 'all' },
                { label: 'Today', value: 'today' },
                { label: 'Tomorrow', value: 'tomorrow' },
                { label: 'Week', value: 'this_week' },
                { label: 'Month', value: 'this_month' },
                { label: 'Year', value: 'this_year' },
            ],
            status_option:
                [
                    { type: 'open', total: 0, icon: 'fa-regular fa-folder-open' },
                    { type: 'progress', total: 0, icon: 'fa-solid fa-bars-progress' },
                    { type: 'completed', total: 0, icon: 'fa-solid fa-flag-checkered' },
                    { type: 'cancelled', total: 0, icon: 'fa-solid fa-ban' },
                    { type: 'all', total: 0, icon: 'fa-solid fa-circle-check' },
                    { type: 'pending', total: 0, icon: 'fa-solid fa-hourglass-half' }
                ],
            display_option: false,
            followup_select: 'all',
            status_select: 'pending',
            items_category: 'tile',
            filter_option: null,
            filter_date: false,
            open_skeleton_isMobile: false,
            now: null,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---table data filter asending and decending----
            is_filter: false,
            //----filter options are-----
            isMainDropdownOpen: false,
            showSubDropdown: false,
            is_openSub: false,
            //---modal---mouse over---
            isModalVisible: false,
            modalData: {},
            modalPosition: { x: 0, y: 0 },
            actionPosition: { x: 0, y: 0 },
            isHoveringModal: false,
            hoverTimer: null,
            lastMousePosition: { x: 0, y: 0 },
            tolerance: 50, // Tolerance for mouse movement in pixels
            //--sort--
            resetSearch: false,
            starter: false,
            //---no access---
            no_access: false,
        };
    },
    computed: {
        ...mapGetters('amcsList', ['currentAmcsList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),

        paginatedData() {
            if (this.data && this.data.length !== 0) {
                // const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                // const endIndex = startIndex + this.recordsPerPage;
                // const filteredData = this.data
                // this.length_category = filteredData.length;

                // return filteredData.slice(startIndex, endIndex);
                return this.data
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];
            const key_order = ['created_at', 'customer', 'title', 'assign_to', 'amc_payment_type', 'amc_details', 'number_of_interval', 'number_of_service', 'amc_status', 'created_by', 'updated_by'];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // // Iterate over the first record in data to get field names
            if (this.data && this.data.length > 0) {
                //     for (const key in this.data[0]) {
                //         if (key !== 'id' && key !== 'amc_attachment' && key !== 'product_lists' && key !== 'date_description' && key !== 'updated_at' && key !== 'deleted_at' && key !== 'created_at' && key !== 'user_id' && key !=='company_id' && key !== 'amc_details' && key !== 'customer_id') { // Exclude the 'id' field
                //        let key_order =  ['customer', 'amc_date', 'amc_title', 'amc_payment_type', 'amc_status', 'assign_to', 'amc_details', 'number_of_interval', 'number_of_service',]
                //             const label = formatLabel(key);
                //             fields.push({ label, field: key, visible: true });
                //         }
                //     }
                // Iterate over the keys in the key_order array
                for (const key of key_order) {
                    if (this.data[0].hasOwnProperty(key)) {
                        const label = formatLabel(key);
                        if (key !== 'amy_payment_type' && key !== 'amc_details' && key !== 'number_of_interval' && key !== 'created_by' && key !== 'updated_by') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                }

                this.columns = fields;
                return fields;
                //     this.columns = fields;
                //     return fields;
            } else {
                return
            }
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        getStatusOption() {
            return this.status_option;
        }
    },
    created() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

        const view = localStorage.getItem('amc_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        // this.getAmcListData(1);
        // this.getStatusLabel('pending', 1);
        let store_data = this.currentAmcsList;
        if (store_data && store_data.data && store_data.data.length > 0) {
            this.open_skeleton = true;
            this.starter = true;
            this.getInitialData(store_data, this.starter);
            this.getStatusLabel(this.status_select, 1);
            this.fetchAmcsList({ page: 1, per_page: this.recordsPerPage });
        } else {
            if (store_data && Object.keys(store_data).length == 0) {
                this.open_skeleton = true;
                this.starter = true;
                this.fetchAmcsList({ page: 1, per_page: this.recordsPerPage });
            }
            this.getStatusLabel(this.status_select, 1);
        }
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        //---sortIcons---
        const initialShortVisible = ['customer', 'amc_payment_type', 'amc_status'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        document.removeEventListener('click', this.handleClickOutsideDate());
        document.removeEventListener('click', this.handleClickOutsideFilter());
    },
    methods: {
        ...mapActions('amcsList', ['fetchAmcsList']),
        ...mapActions('localStorageData', ['validateRoles', 'fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        getAmcListData(page, is_delete) {
            if (page == 1) {
                this.fetchAmcsList({ page, per_page: this.isMobile ? 20 : this.recordsPerPage, is_delete });
                if (this.currentAmcsList && this.currentAmcsList.data) {
                    this.data = this.currentAmcsList.data;
                    this.pagination = this.currentAmcsList.pagination;
                }
            } else {
                this.open_skeleton = true;
                axios.get('/amcs', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                    .then(response => {
                        // console.log(response.data, 'What happening the get overall data', response.data.status_counts[0]);
                        this.data = response.data.data;
                        this.originalData = this.data;
                        this.pagination = response.data.pagination;
                        if (response.data.status_counts[0] >= 0) {
                            this.status_option[0].total = response.data.status_counts[0];
                        }
                        if (response.data.status_counts[1] >= 0) {
                            this.status_option[1].total = response.data.status_counts[1];
                        }
                        if (response.data.status_counts[2] >= 0) {
                            this.status_option[2].total = response.data.status_counts[2];
                        }
                        if (response.data.status_counts[3] >= 0) {
                            this.status_option[3].total = response.data.status_counts[3];
                        }
                        if (response.data.pagination.total >= 0) {

                            this.status_option[4].total = response.data.pagination.total;
                        }
                        if (response.data.status_counts[0] >= 0 || response.data.status_counts[1] >= 0) {
                            this.status_option[5].total = (response.data.status_counts[0] * 1) + response.data.status_counts[1] ? (response.data.status_counts[1] * 1) : 0;

                        }
                        this.open_skeleton = false;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                axios.delete(`/amcs/${this.data[this.deleteIndex].id}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.data);
                        this.updateKeyWithTime('amc_update');
                        this.deleteIndex = null;
                        // this.open_message = true;
                        this.message = 'AMC deleted successfully...!';
                        this.show = true;
                        this.getAmcListData(this.currentPage, this.recordsPerPage, true);
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            if (this.getplanfeatures('amc')) {
                this.no_access = true;
            } else {
                // console.log(index, 'What happening...', this.data);
                this.deleteIndex = index;
                this.open_confirmBox = true;
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsidseClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            try {
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        //---lead modal
        openAmcModal() {
            this.showAmcModal = true;
            this.typeOfAmc = 'add';
        },

        //---close the message--
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---record---
        startEdit(record) {
            if (this.getplanfeatures('amc')) {
                this.no_access = true;
            } else {
                this.typeOfAmc = 'edit';
                let record_data_value = JSON.parse(JSON.stringify(record));
                if (record_data_value.date_description && typeof record_data_value.date_description === 'string') {
                    try {
                        record_data_value.date_description = JSON.parse(record_data_value.date_description);
                    } catch (error) {
                        record_data_value.date_description = [];
                    }
                }
                if (record_data_value.product_lists && typeof record_data_value.product_lists === 'string') {
                    try {
                        record_data_value.product_lists = JSON.parse(record_data_value.product_lists);
                    } catch (error) {
                        record_data_value.product_lists = [];
                    }
                }
                if (record_data_value.amc_date) {
                    record_data_value.amc_date = record_data_value.amc_date.substring(0, 10);
                }
                if (record_data_value.customer) {
                    record_data_value.customer = record_data_value.customer.first_name + ' ' + record_data_value.customer.last_name + ' - ' + record_data_value.customer.contact_number;
                }
                // console.log(record, 'RRSWRWRWRW');
                this.editData = record_data_value;
                this.showAmcModal = true;
            }
        },
        //---closeAmcModal
        closeAmcModal(newData) {
            if (newData && this.typeOfAmc !== 'edit') {
                this.fetchApiUpdates();
                // console.log(this.data, 'EEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE')
                this.data.unshift(newData);
                this.message = 'AMC created successfully...!';
                this.show = true;
                // console.log(newData, 'What happening....!');
            }
            else if (newData && this.typeOfAmc === 'edit') {
                this.fetchApiUpdates();
                let findIndexData = this.data.findIndex((opt) => opt.id === this.editData.id);
                this.data.splice(findIndexData, 1, newData);
                this.message = 'AMC updated successfully...!';
                this.show = true;
                // this.paginatedData;
            }

            this.showAmcModal = false;
        },
        //---view Record--
        viewRecord(record) {
            if (this.getplanfeatures('amc')) {
                this.no_access = true;
            } else {
                if (record) {
                    this.$router.push({
                        name: 'amcView',
                        query: {
                            recordId: record.id
                        }
                    });
                }
            }
        },
        //------Open filter---
        //---lead---
        // toggleFilter() {
        // console.log('GGGGGGGGGGGGGGG');
        // this.typeList = this.leadType.map((opt) => opt.name);
        // this.statusList = this.leadStatus.map((opt) => opt.name);
        // this.lead_filter = true;
        // this.data = this.originalData;
        // console.log(this.lead_filter, 'EEERRRASR');
        // },
        toggleFilterSelected(option) {
            // console.log(option, 'GGGGGGGGGGGGGGG');
            // this.typeList = this.leadType.map((opt) => opt.name);
            // this.statusList = this.leadStatus.map((opt) => opt.name);
            this.selectedByValue = option;
            this.lead_filter = true;
            this.data = this.originalData;
            this.showFilterOptions = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //--close lead
        closeLeadFilter(searchData) {
            // console.log(searchData, 'What is happening.....!');
            if (searchData) {
                const keysData = Object.keys(searchData);
                this.filteredBy = searchData;
                this.getStatusLabel(this.status_select, 1, this.filteredBy);
            }
            this.lead_filter = false;
        },
        //---filter dropdown
        toggleFilter() {
            this.showFilterOptions = !this.showFilterOptions;
        },
        //--hidden dropdown--
        hiddenDropdown() {
            if (!this.mouseOverIsNot) {
                this.showFilterOptions = false;
            }
        },
        mouseOverOption() {
            this.mouseOverIsNot = true;
        },
        mouseLeaveOption() {
            this.mouseOverIsNot = false;
        },
        //---reset filter--
        resetTheFilter() {
            this.resetSearch = !this.resetSearch;
            this.data = this.originalData;
            this.status_select = 4;
            this.filteredBy = {};
        },
        //--filter option--
        changeFilter(opt) {
            this.filter_option = opt;
            this.toggleMainDropdown();
            this.filteredBy = { due: opt };
            if (opt === 'date') {
                this.filter_date = !this.filter_date;
                if (!this.filter_date) {
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutsideDate);
                } else {
                    document.addEventListener('click', this.handleClickOutsideDate);
                }
            }
            if (opt === 'customer') {
                this.toggleFilterSelected('by Customer');
            }
            if (opt === 'employee') {
                this.toggleFilterSelected('by Employee');
            }
            if (opt === 'category') {
                this.toggleFilterSelected('by category/Type');
            }
            if (opt === 'by Date From') {
                this.toggleFilterSelected('by Date From');
            }
        },
        handleClickOutsideDate(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs.componentContainer;
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.filter_date = !this.filter_date;
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutsideDate);
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        handleFollowup(optionValue, page) {
            this.followup_select = optionValue;
            this.filteredBy = { due: optionValue };
            this.toggleMainDropdown();
            if (this.status_select !== null && this.status_select !== 4) {
                this.status_select = 4;
            }
            this.open_loader = true;
            let sendData = { type: 'amcs', q: this.status_select && this.status_select !== 4 ? this.status_select : '', filter: optionValue !== 'all' ? optionValue : '', per_page: this.recordsPerPage, page: page };
            if (Object.keys(this.filteredBy).length > 0) {
                if (this.filteredBy.customer_id) {
                    sendData.customer_id = this.filteredBy.customer_id;
                }
                if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
                    sendData.employer_id = this.filteredBy.assign_to[0].id;
                }
                if (Object.keys(this.filteredBy).length > 0 || this.filteredBy.type >= 0) {
                    sendData.category = this.filteredBy.type;
                }
            }
            // Handle followup option selection (e.g., perform an action based on the selected value)
            // console.log('Selected followup option:', optionValue);
            axios.get('/searchs', { params: { ...sendData } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.data = response.data.data;
                    this.pagination = response.data.pagination;
                    this.currentPage = page ? page : this.currentPage;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getStatusLabel(reqData, page) {
            if (this.followup_select !== null) {
                this.followup_select = 'all';
            }
            this.status_select = reqData;
            // console.log(reqData, 'What happening....!!!');
            this.open_skeleton = true;
            // this.followup_select = null;
            // console.log(reqData, 'Waht happening...');
            let sendData = { type: 'amcs', q: reqData !== 4 ? reqData : '', filter: this.followup_select >= 0 && data !== 4 ? this.followup_select : '', per_page: this.recordsPerPage, page: page };
            if (Object.keys(this.filteredBy).length > 0) {
                if (this.filteredBy.customer_id) {
                    sendData.customer_id = this.filteredBy.customer_id;
                }
                if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
                    sendData.employer_id = this.filteredBy.assign_to[0].id;
                }
                if (Object.keys(this.filteredBy).length > 0 || this.filteredBy.type >= 0) {
                    sendData.category = this.filteredBy.type;
                }
                if (this.filteredBy.from && this.filteredBy.to) {
                    sendData.from_date = this.filteredBy.from ? this.filteredBy.from : '';
                    sendData.to_date = this.filteredBy.to ? this.filteredBy.to : ''
                }
            }
            axios.get('/searchs', { params: { ...sendData } })
                .then(response => {
                    // console.log(response.data, 'Status Data');                    
                    this.data = response.data.data;
                    if (response.data.pagination) {
                        this.pagination = response.data.pagination;
                    }
                    this.currentPage = page ? page : this.currentPage;
                    this.open_skeleton = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---formated display date---
        formattedDate(timestamp) {
            const date = new Date(timestamp);
            date.setUTCHours(date.getUTCHours() + 5); // Add 5 hours for the time zone offset
            date.setUTCMinutes(date.getUTCMinutes() + 30); // Add 30 minutes for the time zone offset
            const formattedDate = date.toISOString().slice(0, 16).replace('T', ' ');
            return formattedDate;
        },

        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            let send_data = {
                type: 'amcs', q: this.status_select >= 0 && this.status_select !== 4 ? this.status_select : '', filter: this.followup_select >= 0 ? this.followup_select : '', per_page: this.recordsPerPage, page: page
            };
            if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
                // console.log(this.filteredBy, 'helllo');
                if (this.filteredBy.customer_id) {
                    send_data.customer_id = this.filteredBy.customer_id
                }
                if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
                    send_data.employer_id = this.filteredBy.assign_to[0].id;
                }
                if (this.filteredBy.type) {
                    send_data.category = this.filteredBy.type;
                }
            }

            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                        this.open_skeleton_isMobile = false;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        //---initial data---
        getInitialData(amcData, is_starter) {
            if (!is_starter) {
                this.data = amcData.data;
                this.originalData = this.data;
                this.pagination = amcData.pagination;
            } else {
                this.starter = false;
            }
            let status_data = amcData.status_counts;
            if (status_data[0] >= 0) {
                this.status_option[0].total = status_data[0];
            }
            if (status_data[1] >= 0) {
                this.status_option[1].total = status_data[1];
            }
            if (status_data[2] >= 0) {
                this.status_option[2].total = status_data[2];
            }
            if (status_data[3] >= 0) {
                this.status_option[3].total = status_data[3];
            }
            if (this.pagination.total >= 0 && !is_starter) {
                this.status_option[4].total = this.pagination.total;
            } else if (is_starter) {
                this.status_option[4].total = amcData.pagination.total;
            }
            if (status_data[0] >= 0 || status_data[1] >= 0) {
                this.status_option[5].total = status_data[0] ? (status_data[0] * 1) + (status_data[1] ? (status_data[1] * 1) : 0) : (status_data[1] ? status_data[1] * 1 : 0);
            }
            this.open_skeleton = false;
        },
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        //--filter dropdown---
        toggleMainDropdown() {
            this.isMainDropdownOpen = !this.isMainDropdownOpen;
            if (this.isMainDropdownOpen) {
                document.addEventListener('click', this.handleClickOutsideFilter);
            } else {
                // this.showSubDropdown = false;
                document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        toggleSubDropdown() {
            setTimeout(() => {
                if (!this.is_openSub) {
                    this.showSubDropdown = !this.showSubDropdown;
                }
            }, 200)
        },
        toggleMouserHoverSub() {
            this.is_openSub = !this.is_openSub;
            if (!this.is_openSub && this.showSubDropdown) {
                this.toggleSubDropdown();
            }
        },
        handleClickOutsideFilter(event) {
            const mainDropdown = this.$refs.dropdownContainerFilter;
            // console.log(event.target, 'Waht happning the data....', mainDropdown.contains(event.target));
            if (mainDropdown && !mainDropdown.contains(event.target)) {
                this.is_openSub = false;
                this.showSubDropdown = false;
                this.toggleMainDropdown();
                // document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'amcs' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'amcs', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'amcs' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'amcs', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        //----table action----
        showModal(index, event) {
            if (index) {
                this.lastMousePosition = { x: event.layerX * 1, y: event.layerY * 1 };
                // Start a timer to check after 5000ms
                this.hoverTimer = setTimeout(() => {
                    if (this.isMouseInSamePosition(event)) {
                        this.modalData = index;
                        this.modalPosition = { x: ((event.layerX * 1) + 25), y: (event.layerY * 1) + 25 };
                        this.isModalVisible = true;
                    }
                }, 200);
            }
        },
        hideModal() {
            clearTimeout(this.hoverTimer); // Clear any existing timer
            setTimeout(() => {
                if (!this.isHoveringModal) {
                    this.isModalVisible = false;
                }
            }, 200);
        },
        toggleHoverModal(status) {
            this.isHoveringModal = status;
            if (!status) {
                this.hideModal();
            }
        },
        isMouseInSamePosition(event) {
            // Allow for a 10-pixel tolerance in mouse movement
            const xDiff = Math.abs(this.lastMousePosition.x - event.layerX);
            const yDiff = Math.abs(this.lastMousePosition.y - event.layerY);
            return xDiff <= this.tolerance && yDiff <= this.tolerance;
        },
        closeAllModals() {
            // Close all modals
            this.open_confirmBox = false;
            this.open_message = false;
            this.showAmcModal = false;
            this.lead_filter = false;
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                let searchDatacustomer = {}
                searchDatacustomer.customer_id = selectedData.id;
                searchDatacustomer.customer = selectedData.first_name + (selectedData.last_name ? ' ' + selectedData.last_name : '') + ' - ' + selectedData.contact_number;
                this.closeLeadFilter(searchDatacustomer);
            }
        },
        resetToSearch() {
            this.filteredBy = {};
            this.resetSearch = !this.resetSearch;
            this.resetTheFilter();
        },
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //--refresh page---
        refreshDataTable() {
            this.resetSearch = false;
            this.filteredBy = {};
            this.fetchAmcsList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
        },
        getUserColor(userId) {
            const colors = ['#3e14e3', '#2a9d8f', '#264653', '#8a4f7d', '#457b9d', '#e36ae9'];
            return colors[userId % colors.length];
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
    },
    watch: {
        originalData(newValue) {
            // console.log(newValue, 'EEEAEA');
            // Automatically send data to the parent when dataToSend changes
            this.$emit('dataToParent', newValue);
        },
        searchedData(newValue) {
            // console.log(newValue, 'RRRRR');
            if (!this.isEmptyObject(newValue)) {
                let filterData = this.data.filter((opt) => opt.customer.toLowerCase() === newValue.customer.toLowerCase());
                if (filterData) {
                    // console.log(filterData, 'Filter datatatat');
                    this.data = filterData;
                } else {
                    this.data = [{ ...newValue }];
                }
            }
            else {
                this.data = this.originalData;
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                if (Object.keys(this.filteredBy).length === 0) {
                    this.getAmcListData(newValue, this.recordsPerPage);
                } else {
                    this.getStatusLabel(null, newValue);
                }
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    if (Object.keys(this.filteredBy).length === 0) {
                        this.getAmcListData(1, newValue);
                    } else {
                        this.getStatusLabel(null, 1);
                    }

                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('amc_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentAmcsList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.getInitialData(newValue, this.starter);
                    this.open_loader = false;
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.filter_option = null;
                this.resetTheFilter();
                this.fetchAmcsList({ page: 1, per_page: this.recordsPerPage });
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    // console.log(newValue, 'New Value data...............');
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showAmcModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        lead_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    },
}
</script>

<style scoped>
.manualStyle {
    overflow: auto;
    height: 100vh;
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

/* Style the scrollbar */
::-webkit-scrollbar {
    width: 3px;
    /* Set the width of the scrollbar */
    height: 4px;
}

/* Track (the area around the scrollbar) */
::-webkit-scrollbar-track {
    background: #CFD8DC;
    /* Background color of the scrollbar track */
}

/* Handle (the draggable part of the scrollbar) */
::-webkit-scrollbar-thumb {
    background: #90A4AE;
    /* Color of the scrollbar handle */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #90A4AE;
    /* Color of the scrollbar handle on hover */
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
    z-index: 100;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}

@media (max-width: 768px) {
    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }

}
</style>
