<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateEstimationAPIRequest;
use App\Http\Requests\API\UpdateEstimationAPIRequest;
use App\Models\Estimation;
use App\Models\Customer;
use App\Models\InvoiceSettings;
use App\Models\Companies;
use App\Repositories\EstimationRepository;
use App\Repositories\EstimationUsersRepository;
use App\Http\Resources\api\EstimationResource;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Http\Services\RelayMessage;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Support\Facades\Log;
use Response;
use Auth;

/**
 * Class EstimationController
 * @package App\Http\Controllers\API
 */

class EstimationAPIController extends AppBaseController
{
    /** @var  EstimationRepository */
    private $estimationRepository;
    private $estimationUsersRepository;

    public function __construct(EstimationRepository $estimationRepo, EstimationUsersRepository $estimationUsersRepo)
    {

        $this->estimationRepository = $estimationRepo;
        $this->estimationUsersRepository = $estimationUsersRepo;
    }
  
  
  		public function downloadPDF($id, $sign = 0)
    	{

         $sales = Estimation::where('id', $id)->first(); //$this->salesRepository->findWithRelated($id);
         

           if (empty($sales)) {
             return $this->sendError('Estimation not found');
           }

          	$company_id = $sales->company_id;

          	$invoice = InvoiceSettings::where('company_id', $company_id)->first();
          $customer =  Customer::where('id', $sales->customer_id)->first();

          $saleItems =  json_decode($sales->items,true);

          $company = Companies::where('id', $company_id)->first(); 
         
    		$company = Companies::where('id', $company_id)->first();
        	$pdf = Pdf::loadView('pdf.estimation', [
             	'data' => $sales,
             	'company' => $company,
             	'invoice' => $invoice,
             	'customer' => $customer,
           		'sale_items' => $saleItems,
              	'sign' => $sign
            ]);
        	return $pdf->download($sales->estimate_num.'.pdf');     

     
    	}
    
     	public function viewPDF($id, $sign = 0 )
    	{       
          
       
        
          $sales = Estimation::where('id', $id)->first(); //$this->salesRepository->findWithRelated($id);
         

           if (empty($sales)) {
             return $this->sendError('Estimation not found');
           }

          $company_id = $sales->company_id;

          $invoice = InvoiceSettings::where('company_id', $company_id)->first();
          $customer =  Customer::where('id', $sales->customer_id)->first();

          $saleItems =  json_decode($sales->items,true);

          $company = Companies::where('id', $company_id)->first();  
 
    
           return view('pdf.estimation')->with([
               	'data' => $sales,
               	'company' => $company,
               	'invoice' => $invoice,
               	'customer' => $customer,
             	'sale_items' => $saleItems,
             	'sign' => $sign
           ]);     
    	}

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/estimations",
     *      summary="getEstimationList",
     *      tags={"Estimation"},
     *      description="Get all Estimations",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Estimation")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
      	if (!Auth::check()) {
            return response()->json(['error' => 'Please login to access.'], 401);
        }
        $companyId = $request->query('company_id');
      
      	$customer_id = $request->query('customer_id');
        $from_date = $request->query('from_date');
        $to_date = $request->query('to_date');
        $status = $request->query('status');      
      	
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
      	

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 
      
        
          $user = Auth::user();
          $isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();
          $estimationsQuery = Estimation::where('company_id', $companyId);
           
           
            if ($customer_id) {
                $estimationsQuery->where('customer_id', $customer_id);
            }

            if ($from_date) {
                $estimationsQuery->whereDate('updated_at', '>=', $from_date);
            }

            if ($to_date) {
                $estimationsQuery->whereDate('updated_at', '<=', $to_date);
            }

            if ($status) {
                switch ($status) {
                    case '1':
                        $proQuery->where('status', '1'); // Converted
                        break;
                    case '0':
                        $proQuery->where('status', '0'); // Waiting
                        break;
                     case '2':
                        $proQuery->where('status', '2'); // Waiting
                        break;
                    case 'all':
                        $proQuery->whereIn('status', ['0', '1', '2']); // Both Converted and Waiting
                        break;
                    default:
                        break;
                }
            }
        
          
        
            // If perPage is set to 'all', get all leads without pagination
            if ($perPage === 'all') {
                
                 $perPage = $estimationsQuery->count();
            } 
                 $estimations = $estimationsQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
            
        
            return response()->json([
                'success' => true,
                'data' => EstimationResource::collection($estimations),            
                'pagination' => [
                   'total' => $estimations->total(),
                    'per_page' => $estimations->perPage(),
                    'current_page' => $estimations->currentPage(),
                    'last_page' => $estimations->lastPage(),
                    'from' => $estimations->firstItem(),
                    'to' => $estimations->lastItem()
                ],
            ]);
       

       
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/estimations",
     *      summary="createEstimation",
     *      tags={"Estimation"},
     *      description="Create Estimation",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Estimation")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Estimation"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateEstimationAPIRequest $request)
    {
        $input = $request->all();
        
        if ($input['company_id'] === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $estimation = $this->estimationRepository->create($input);
        
        
        $customer = Customer::find($estimation->customer_id);
        $relayMessage = new RelayMessage();
      	$url = "https://api.track-new.com/api/download-estimation/".$estimation->id;
      	//$url = 'https://devapp.track-new.com/viewsales?type=estimation&est_no='.$estimation->id.'&companyId='.$estimation->company_id;
       
        
        if (isset($input['issms']) && $input['issms'] == true) {
          	if(checkSmsBalance($estimation->company_id) !== 0){ 
            	$relayMessage->sendSaleEstimate($customer, $url, 'estimate');
            }
        }
        $customer_name = $customer->first_name ?? ''. ' ' . $customer->last_name ?? '';
        $contact_number = $customer->contact_number;
        if (isset($input['iswhatsapp']) && $input['iswhatsapp'] == true) {
            $relayMessage->saleEstimateWhatsAppMessage($customer_name, $contact_number, $url, $estimation->company_id, 'estimate');
        }
        
        // if($estimation->customer_id){
            
        //      $customer = Customer::find($estimation->customer_id);
       
        
        //  $relayMessage = new RelayMessage();
        //  $relayMessage->sendEstimate($customer->contact_number, $url);
            
        // }
        
       

        if(isset($input['assign_to'])){

        $user_data = json_decode($input['assign_to'], true);

            foreach ($user_data as $user_id) {
    
                $data['estimation_id'] = $estimation->id; 
                $data['user_id'] = $user_id;
              
                $delete_amc_user = $this->estimationUsersRepository->deleteByEstimateId($estimation->id, $user_id);
    
                    $this->estimationUsersRepository->create($data);
            }
        }

       

        return $this->sendResponse(new EstimationResource($estimation), 'Estimation saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/estimations/{id}",
     *      summary="getEstimationItem",
     *      tags={"Estimation"},
     *      description="Get Estimation",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Estimation",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Estimation"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Estimation $estimation */
        $estimation = $this->estimationRepository->find($id);

        if (empty($estimation)) {
            return $this->sendError('Estimation not found');
        }

        return $this->sendResponse(new EstimationResource($estimation), 'Estimation retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/estimations/{id}",
     *      summary="updateEstimation",
     *      tags={"Estimation"},
     *      description="Update Estimation",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Estimation",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Estimation")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Estimation"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateEstimationAPIRequest $request)
    {
        $input = $request->all();

        /** @var Estimation $estimation */
        $estimation = $this->estimationRepository->find($id);

        if (empty($estimation)) {
            return $this->sendError('Estimation not found');
        }

        $estimation = $this->estimationRepository->update($input, $id);
        
        
        $customer = Customer::find($estimation->customer_id);
        
        
        $relayMessage = new RelayMessage();
        $url = 'https://api.track-new.com/api/download-estimation/'.$estimation->id;
        
        if (isset($input['issms']) && $input['issms'] == true) {
      //  Log::info('This is an info log message.'.checkSmsBalance($estimation->company_id));
          	if(checkSmsBalance($estimation->company_id) !== 0){ 
            	$relayMessage->sendSaleEstimate($customer, $url, 'estimate');
            }
        }
         $customer_name = $customer->first_name . ' ' . $customer->last_name;
        $contact_number = $customer->contact_number;
        if (isset($input['iswhatsapp']) && $input['iswhatsapp'] == true) {
           $res = $relayMessage->saleEstimateWhatsAppMessage($customer_name, $contact_number, $url, $estimation->company_id, 'estimate');
           
         
        }

        if(isset($input['assign_to'])){

            $user_data = json_decode($input['assign_to'], true);       

            foreach ($user_data as $user_id) {

                $data['estimation_id'] = $estimation->id; 
                $data['user_id'] = $user_id;            
                $delete_amc_user = $this->estimationUsersRepository->deleteByEstimateId($estimation->id, $user_id); 
                $this->estimationUsersRepository->create($data);                

            }
        }

        if($input['status'] == '1'){
            
        }
        return $this->sendResponse(new EstimationResource($estimation), 'Estimation updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/estimations/{id}",
     *      summary="deleteEstimation",
     *      tags={"Estimation"},
     *      description="Delete Estimation",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Estimation",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Estimation $estimation */
        $estimation = $this->estimationRepository->find($id);

        if (empty($estimation)) {
            return $this->sendError('Estimation not found');
        }

        $estimation->delete();

        return $this->sendSuccess('Estimation deleted successfully');
    }
}
