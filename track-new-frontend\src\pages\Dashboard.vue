<template>
    <auth-layout>
      <div class="p-4 bg-white shadow rounded-md">
        <h1 class="text-2xl font-bold mb-4">Dashboard</h1>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="bg-blue-500 text-white p-6 rounded-md shadow">
            <h2 class="text-lg font-semibold">Item 1</h2>
            <p>This is a dashboard widget description.</p>
          </div>
          <div class="bg-green-500 text-white p-6 rounded-md shadow">
            <h2 class="text-lg font-semibold">Item 2</h2>
            <p>This is another dashboard widget.</p>
          </div>
          <div class="bg-red-500 text-white p-6 rounded-md shadow">
            <h2 class="text-lg font-semibold">Item 3</h2>
            <p>This is yet another widget.</p>
          </div>
        </div>
      </div>
    </auth-layout>
  </template>
  
  <script>
  import AuthLayout from '../layouts/authLayout.vue';
  
  export default {
    components: {
      AuthLayout,
    },
  };
  </script>
  