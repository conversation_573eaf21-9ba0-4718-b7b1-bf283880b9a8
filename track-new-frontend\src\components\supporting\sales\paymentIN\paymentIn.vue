<template>
    <div class="container mx-auto p-2 mb-[60px] text-sm" :class="{ 'mt-[45px]': isMobile }">
        <div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-0 sm:gap-4">
                <div
                    class="mb-4 w-full justify-center items-center bg-white px-4 py-4 rounded rounded-xl border border-gray-200 relative">
                    <div class="relative flex flex-col w-full">
                        <label for="customer" class="block text-sm font-semibold text-gray-500">Customer Name</label>
                        <input id="customer" v-model="formValues.customer" @input="handleDropdownInput()"
                            placeholder="Select the customer"
                            @focus="handleDropdownInput(), isDropdownOpen = true, isInputFocused.customer = true"
                            @blur="closeDropdown('customer')"
                            @keydown.enter="handleEnterKey('customer', filteredCustomerOptions)"
                            @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="customer"
                            class="border border-gray-300 rounded py-2 px-12 leading-tight focus:outline-none focus:shadow-outline bg-search-icon" />
                    </div>
                    <div v-if="isDropdownOpen"
                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                        style="z-index: 100;" @mousedown.prevent="preventBlur('customer')">
                        <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                            @click="selectDropdownOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                            {{ option.first_name + (option.last_name ? ' ' + option.last_name : '') }} - {{
                                option.contact_number }}
                        </p>
                    </div>
                    <div class="flex w-full items-center mt-2 mb-2 relative" title="Balance amount can't edit">
                        <label for="currentBalance" class="text-sm font-semibold mr-2 text-gray-500">Current Balance (
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }} ):</label>
                        <input id="currentBalance" v-model="formValues.balance_amount" type="number" disabled />
                    </div>
                    <div class="w-full justify-center items-center relative">
                        <label for="paymentAmount" class="text-sm block font-semibold text-gray-500">Enter Payment
                            Amount:</label>
                        <span class="absolute inset-y-0 left-0 flex items-center pl-2 top-5 text-gray-400">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }}
                        </span>
                        <input id="paymentAmount" v-model="formValues.payment_amount" type="number"
                            class="border border-gray-300 rounded w-full py-2 px-6 leading-tight focus:outline-none focus:shadow-outline"
                            @input="updatePaymentAmountInvoice" />
                    </div>
                    <!--remaining and return balance-->
                    <div class="flex justify-between items-center py-1">
                        <p class="text-red-600 font-medium">Balance
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }} {{
                                formValues.remaining_amount ? formValues.remaining_amount : 0 }}
                        </p>

                        <p class="text-green-600 font-medium">Return {{ currentCompanyList &&
                            currentCompanyList.currency
                            === 'INR' ? '\u20b9' : currentCompanyList.currency }} {{
                                formValues.return_amount ? formValues.return_amount : 0 }}
                        </p>
                    </div>
                </div>
                <div class="mb-4 bg-white px-4 py-4 rounded rounded-xl border border-gray-200">
                    <div class="grid grid-cols-2 gap-2">
                        <div>
                            <label for="paymentDate" class="text-sm block font-semibold text-gray-500">Payment
                                Date:</label>
                            <input id="paymentDate" v-model="formValues.payment_date" type="date" v-datepicker
                                class="border border-gray-300 rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline" />
                        </div>
                        <div>
                            <label for="paymentType" class="text-sm block font-semibold text-gray-500">Payment
                                Type:</label>
                            <select id="paymentType" v-model="formValues.payment_type"
                                class="border border-gray-300 rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline">
                                <option
                                    v-if="currentInvoice && currentInvoice.length > 0 && currentInvoice[0].payment_opt && Array.isArray(JSON.parse(currentInvoice[0].payment_opt))"
                                    v-for="(opt, index) in JSON.parse(currentInvoice[0].payment_opt)" :value="opt.type"
                                    :key="index">
                                    {{ opt.type }}</option>
                            </select>
                        </div>
                    </div>
                    <div class="mt-1">
                        <label for="notes" class="text-sm block font-semibold text-gray-500">Notes:</label>
                        <textarea id="notes" v-model="formValues.notes"
                            class="border border-gray-300 rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline"
                            rows="3"></textarea>
                    </div>
                    <!---bank informations-->
                    <!-- <div>
                        <label for="bank_info" class="text-sm block font-semibold text-gray-500">Bank Details:</label>
                        <textarea id="bank_info" v-model="formValues.bank_info"
                            class="border border-gray-300 rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline"
                            rows="3"></textarea>
                    </div> -->
                </div>
            </div>
        </div>
        <div class="bg-white px-2 py-2 rounded rounded-lg border border-gray-200">
            <div class="py-2"
                :class="{ 'flex flex-col justify-start': isAndroid, 'flex justify-between items-center': !isAndroid }">
                <p class="font-semibold">Settle invoices with this payment</p>
                <div class="relative">
                    <div class="relative flex flex-col w-full">
                        <input id="invoice" v-model="formValues.invoice" @input="handleDropdownInputInvoices()"
                            placeholder="Select the invoice"
                            @focus="handleDropdownInputInvoices(), isDropdownOpenInvoice = true, isInputFocused.invoice = true"
                            @blur="closeDropdown('invoice')"
                            @keydown.enter="handleEnterKey('invoice', filteredInvoiceOptions)"
                            @keydown.down.prevent="handleDownArrow(filteredInvoiceOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredInvoiceOptions)" ref="invoice"
                            class="border border-gray-300 rounded py-2 px-12 leading-tight focus:outline-none focus:shadow-outline bg-search-icon" />
                    </div>
                    <div v-if="isDropdownOpenInvoice"
                        class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                        style="z-index: 100;" @mousedown.prevent="preventBlur('invoice')">
                        <p v-for="(option, index) in filteredInvoiceOptions" :key="index"
                            @click="selectDropdownOptionInvoices(option)"
                            :class="{ 'bg-gray-200': index === selectedIndex }"
                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                            {{ option.invoice_id }}
                        </p>
                    </div>
                </div>
            </div>
            <div class="overflow-auto">
                <!--loader-->
                <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                    :cols="items_category === 'tile' ? number_of_columns : 3"
                    :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                    :type="items_category === 'tile' ? 'table' : 'grid'">
                </skeleton>
                <table v-if="!open_skeleton && !isAndroid" class="w-full overflow-auto">
                    <thead>
                        <tr class="bg-gray-300">
                            <th class="py-2 text-center border px-2">
                                <input type="checkbox" @change="selectAllRows" class="bg-white" />
                            </th>
                            <th class="py-2 border">Date</th>
                            <th class="py-2 border">Due Date</th>
                            <th class="py-2 border">Invoice Number</th>
                            <th class="py-2 border">Invoice Amount</th>
                            <th class="py-2 border">Amount Settled</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(invoice, index) in filteredInvoiceOptions" :key="invoice.id">
                            <td class="py-2 border">
                                <div class="flex justify-center items-center">
                                    <input type="checkbox" v-model="selectedRows" :value="invoice.id"
                                        @change="updatePaymentAmount" />
                                </div>
                            </td>
                            <td class="py-2 border">
                                <div class="flex justify-center items-center">{{ formatDateTime(invoice.current_date) }}
                                </div>
                            </td>
                            <td class="py-2 border">
                                <div class="flex justify-left items-center">
                                    <p class="px-1">{{ calculateUntilDueDate(invoice.current_date) }}</p>
                                    <p v-if="calculateDaysUntilDue(invoice.current_date) >= 0 && invoice['due_amount'] > 0"
                                        class="flex flex-col">
                                        <!-- <span>Due:</span>  -->
                                        <span>({{
                                            calculateDaysUntilDue(invoice.current_date) }} days to
                                            due)</span>
                                    </p>
                                    <p v-else-if="invoice['due_amount'] > 0" class="flex flex-col text-red-500">
                                        <!-- <span>Overdue:</span> -->
                                        <span>({{
                                            Math.abs(calculateDaysUntilDue(invoice.current_date)) }}
                                            days to overdue)</span>
                                    </p>
                                </div>
                            </td>
                            <td class="py-2 border">
                                <div class="flex justify-center items-center">{{ invoice.invoice_id }}
                                </div>
                            </td>
                            <td class="py-2 border">
                                <div class="flex justify-left items-center">
                                    <span class="px-1"> {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                        '\u20b9' : currentCompanyList.currency }}
                                        {{ invoice.grand_total }} </span>
                                    <span class="text-red-600 font-semibold flex items-center">
                                        ( {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }} {{
                                            invoice.due_amount }} Pending)</span>
                                </div>
                            </td>
                            <td class="py-2 border">
                                <div class="flex justify-center items-center">
                                    {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}
                                    {{ totalPaidAmount(invoice.sales_payment) }}
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
                <!--is Mobile view-->
                <div v-if="!open_skeleton && isAndroid">
                    <div class="flex py-2 text-left px-2 items-center">
                        <input type="checkbox" @change="selectAllRows" class="bg-white" /> <span class="px-2">Select
                            All</span>
                    </div>
                    <div v-for="(invoice, index) in filteredInvoiceOptions" :key="invoice.id"
                        :class="{ 'mt-3': index !== 0 }">
                        <div
                            class="flex justify-self-start items-center shadow-lg border border-gray-300 py-2 rounded rounded-lg">
                            <div class="flex justify-center items-center px-2">
                                <input type="checkbox" v-model="selectedRows" :value="invoice.id"
                                    @change="updatePaymentAmount" />
                            </div>
                            <div class="px-2">

                                <div class="flex justify-start items-center">Date: {{
                                    formatDateTime(invoice.current_date) }}
                                </div>

                                <div class="flex justify-left items-center"> Due Date:
                                    <p class="px-1">{{ calculateUntilDueDate(invoice.current_date) }}</p>
                                    <p v-if="calculateDaysUntilDue(invoice.current_date) >= 0 && invoice['due_amount'] > 0"
                                        class="flex flex-col">
                                        <!-- <span>Due:</span>  -->
                                        <span>({{
                                            calculateDaysUntilDue(invoice.current_date) }} days to
                                            due)</span>
                                    </p>
                                    <p v-else-if="invoice['due_amount'] > 0" class="flex flex-col text-red-500">
                                        <!-- <span>Overdue:</span> -->
                                        <span>({{
                                            Math.abs(calculateDaysUntilDue(invoice.current_date)) }}
                                            days to overdue)</span>
                                    </p>
                                </div>

                                <div class="flex justify-leftitems-center">Invoice no: {{ invoice.invoice_id }}
                                </div>

                                <div class="flex justify-left items-center">Invoice Amount:
                                    <span class="px-1"> {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                        '\u20b9' : currentCompanyList.currency }}
                                        {{ invoice.grand_total }} </span>
                                    <span class="text-red-600 font-semibold flex items-center">
                                        ( {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }} {{
                                            invoice.due_amount }} Pending)</span>
                                </div>

                                <div class="flex justify-left items-center">Amount Settled:
                                    {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}
                                    {{ totalPaidAmount(invoice.sales_payment) }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="fixed bottom-0 bg-white border-t border-gray-200 px-4 py-3 flex justify-center items-center space-x-4"
            :class="{ 'left-0 w-full': isMobile, 'w-3/4': !isMobile }">
            <button @click="cancelPayment" class="bg-gray-300 text-gray-700 px-8 py-2 rounded">Cancel</button>
            <button @click="savePayment" class="bg-green-600 text-white px-10 py-2 rounded">Save</button>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
    emits: ['updateIsOpen'],
    props: {
        isMobile: Boolean,
        isAndroid: Boolean
    },
    data() {
        return {
            partyName: 'Shivam Infotech',
            currentBalance: '63,404.54',
            paymentAmount: 0,
            selectedRows: [],
            formValues: {},
            isInputFocused: {},
            mouseDownOnDropdown: false,
            isDropdownOpen: false,
            isDropdownOpenInvoice: false,
            customer_list: [],
            filteredCustomerOptions: [],
            filteredInvoiceOptions: [],
            selectedIndex: 0,
            invoices: [],
            customer_data: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 6,
            number_of_rows: 10,
            gap: 5,
            items_category: 'tile',
            now: null,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: ''
        };
    },
    created() {
        setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    mounted() {
        this.fetchApiUpdates();
        if (this.currentCustomer && this.currentCustomer.length === 0) {
            this.fetchCustomerList();
        } else {
            this.customer_list = this.currentCustomer;
            this.fetchCustomerList();
        }
        if (this.currentInvoice && this.currentInvoice.length > 0) {
            if (!this.formValues.payment_type && this.currentInvoice[0].payment_opt && Array.isArray(JSON.parse(this.currentInvoice[0].payment_opt))) {
                let find_payment_type = JSON.parse(this.currentInvoice[0].payment_opt).find(opt => opt.status === true);
                if (find_payment_type) {
                    this.formValues.payment_type = find_payment_type.type;
                }
            }
            this.fetchInvoiceSetting();
        } else {
            this.fetchInvoiceSetting();
        }
        if (this.invoices && this.invoices.length > 0) {
            this.filteredInvoiceOptions = this.invoices;
        }
        if (!this.formValues.payment_date) {
            this.formValues.payment_date = this.getCurrentDate();
        }
        if (this.formValues.customer_id) {
            this.getSalesSearchList();
        }
        let customer_id = this.$route.query.customer_id;
        if (customer_id) {
            this.formValues.customer_id = customer_id;
            this.getCustomerData(customer_id);
        }
        this.fetchCompanyList();
    },
    computed: {
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),

        totalBalanceAmount() {
            if (this.invoices.length > 0) {
                return this.invoices.reduce((total, invoice) => total + invoice.balance_amount, 0);
            }
        },
    },
    methods: {
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('companies', ['fetchCompanyList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        totalPaidAmount(payment_data) {
            if (this.invoices.length > 0 && Array.isArray(payment_data) && payment_data.length > 0) {
                // let find_data = this.invoices.find(opt => opt.id == id);
                // if (find_data && find_data.payment_mode && Array.isArray(JSON.parse(find_data.payment_mode))) {
                //     return JSON.parse(find_data.payment_mode).reduce((total, invoice) => total + invoice.payment_amount, 0);
                // }
                return payment_data.reduce((total, invoice) => total + invoice.payment_amount, 0);
            }
        },
        updatePaymentAmountInvoice() {
            if (this.formValues.payment_amount && this.invoices.length > 0) {
                if (this.selectedRows.length === 0) {
                    if (this.formValues.balance_amount && this.formValues.balance_amount <= this.formValues.payment_amount) {
                        this.selectedRows = this.invoices.map((invoice) => invoice.id);
                    } else {
                        let remainingAmount = this.formValues.payment_amount;
                        // Iterate through invoices to distribute the payment amount
                        for (let invoice of this.invoices) {
                            if (remainingAmount <= 0) {
                                break;
                            }
                            const dueAmount = invoice.balance_amount;
                            if (remainingAmount >= dueAmount) {
                                // Fully pay off this invoice
                                // invoice.payment_amount = dueAmount;
                                remainingAmount -= dueAmount;
                            } else {
                                // Partially pay off this invoice
                                // invoice.payment_amount = remainingAmount;
                                remainingAmount = 0;
                            }
                            // Mark this invoice as selected
                            this.selectedRows.push(invoice.id);
                        }
                    }

                } else {
                    // console.log('hello');
                    if (this.formValues.balance_amount && this.formValues.balance_amount <= this.formValues.payment_amount) {
                        this.selectedRows = this.invoices.map((invoice) => invoice.id);
                    } else if (this.formValues.payment_amount) {
                        let remainingAmount = this.formValues.payment_amount;
                        this.selectedRows = [];
                        // Iterate through invoices to distribute the payment amount
                        for (let invoice of this.invoices) {
                            if (remainingAmount <= 0) {
                                break;
                            }
                            const dueAmount = invoice.balance_amount;
                            if (remainingAmount >= dueAmount) {
                                // Fully pay off this invoice
                                // invoice.payment_amount = dueAmount;
                                remainingAmount -= dueAmount;

                            } else {
                                // Partially pay off this invoice
                                // invoice.payment_amount = remainingAmount;
                                remainingAmount = 0;
                            }

                            // Mark this invoice as selected
                            this.selectedRows.push(invoice.id);
                        }
                    } else {
                        this.selectedRows = [];
                    }
                }
            }
        },
        updatePaymentAmount() {
            // if (this.selectedRows.length === 0) {
            //     this.formValues.payment_amount = this.invoices.reduce((total, invoice) => total + invoice.balance_amount, 0);
            // this.updatePaymentAmountInvoice();
            // } else {
            if (this.selectedRows.length > 0) {
                this.formValues.payment_amount = this.selectedRows.reduce((total, id) => {
                    const invoice = this.invoices.find((invoice) => invoice.id === id);
                    return total + (invoice ? invoice.balance_amount : 0);
                }, 0);
                // this.updatePaymentAmountInvoice();
            }
        },
        selectAllRows(event) {
            if (event.target.checked) {
                this.selectedRows = this.invoices.map((invoice) => invoice.id);
                this.updatePaymentAmount();
            } else {
                this.selectedRows = [];
                this.formValues.payment_amount = 0;
            }
        },
        savePayment() {
            if (this.filteredInvoiceOptions && this.filteredInvoiceOptions.length > 0 && this.formValues.payment_amount && this.formValues.payment_amount > 0 && this.selectedRows.length > 0) {
                let send_data = [];

                // Case when payment amount is greater than due amount
                if (this.formValues.payment_amount > this.formValues.due_amount) {
                    this.filteredInvoiceOptions.map(opt => {
                        if (opt.due_amount) {
                            let obj_pay_data = {
                                payment_date: this.getCurrentDateTime(),
                                payment_type: this.formValues.payment_type,
                                payment_amount: opt.due_amount,
                                payment_for: 'payment_in',
                                payment_notes: this.formValues.notes ? this.formValues.notes : ''
                            };

                            if (opt.sales_payment && opt.sales_payment.length > 0) {
                                send_data.push({
                                    id: opt.id,
                                    balance_amount: 0,
                                    due_amount: 0,
                                    sales_payment: [...opt.sales_payment, obj_pay_data], // Clone the array before pushing new payment
                                    notes: this.formValues.notes ? this.formValues.notes : ''
                                });
                            } else {
                                send_data.push({
                                    id: opt.id,
                                    balance_amount: 0,
                                    due_amount: 0,
                                    sales_payment: [obj_pay_data], // Initialize the sales_payment array with the first payment
                                    notes: this.formValues.notes ? this.formValues.notes : ''
                                });
                            }
                        }
                    });

                }
                // Case when payment amount is less than due amount
                else if (this.formValues.payment_amount < this.formValues.due_amount) {
                    let remainingAmount = this.formValues.payment_amount;
                    this.filteredInvoiceOptions.forEach(opt => {
                        if (remainingAmount > 0 && opt.due_amount > 0 && this.selectedRows.includes(opt.id)) {
                            let payment = Math.min(opt.due_amount, remainingAmount);
                            let obj_pay_data = {
                                payment_date: this.getCurrentDateTime(),
                                payment_type: this.formValues.payment_type,
                                payment_amount: payment,
                                payment_for: 'payment_in',
                                payment_notes: this.formValues.notes ? this.formValues.notes : ''
                            };

                            send_data.push({
                                id: opt.id,
                                balance_amount: opt.due_amount - payment,
                                due_amount: opt.due_amount - payment,
                                sales_payment: [...opt.sales_payment, obj_pay_data], // Clone the array and add payment
                                notes: this.formValues.notes ? this.formValues.notes : ''
                            });

                            remainingAmount -= payment;
                        }
                    });
                }
                // Case when payment amount is equal to due amount
                else if (this.formValues.payment_amount === this.formValues.due_amount) {
                    this.filteredInvoiceOptions.forEach(opt => {
                        if (opt.due_amount) {
                            let obj_pay_data = {
                                payment_date: this.getCurrentDateTime(),
                                payment_type: this.formValues.payment_type,
                                payment_amount: opt.due_amount,
                                payment_for: 'payment_in',
                                payment_notes: this.formValues.notes || ''
                            };

                            send_data.push({
                                id: opt.id,
                                balance_amount: 0,
                                due_amount: 0,
                                sales_payment: [...opt.sales_payment, obj_pay_data], // Clone and push payment to the array
                                notes: this.formValues.notes || ''
                            });
                        }
                    });
                }

                // If there is data to send, send it to the database
                if (send_data.length > 0) {
                    this.sendDataToDatabase(send_data);
                    this.open_loader = true;

                } else {
                    this.message = 'Please enter the payment amount and update the payment.';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            } else {
                this.message = 'Please select the customer or fill the payment amount and update the payment.';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        cancelPayment() {
            this.$router.go(-1);
        },
        sendDataToDatabase(save_data) {
            if (save_data) {
                // console.log(save_data, 'hello Datata tatttt');
                axios.post(`/customer/payments`, { sales_data: save_data })
                    .then(response => {
                        // console.log(response.data, 'update response...!');
                        this.open_loader = false;
                        this.message = 'Payment in updated successfully..!';
                        this.show = true;
                        this.formValues.balance_amount = 0;
                        this.updateKeyWithTime('sales_update');
                        this.updateKeyIsUpdate({ key: 'customer', value: true });
                        this.updateKeyIsUpdate({ key: 'customer_list', value: true });
                        //--update the customer---
                        this.fetchCustomerList();
                        this.getSalesSearchList(true);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            } else {
                this.open_loader = false;
            }
        },
        //---search the customer---
        //---customer--
        handleDropdownInput() {
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                    // if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                    //     this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    // }
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                            (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                    );
                    // if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                    //     this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    // }
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                if (this.customer_list && this.customer_list.length > 0) {
                    this.filteredCustomerOptions = this.customer_list;
                } else {
                    this.filteredCustomerOptions = [];
                }
            }

            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                }
            } else if (type === 'invoice') {
                if (optionArray && optionArray.length > 0) {
                    this.selectDropdownOptionInvoices(optionArray[this.selectedIndex]);
                    this.selectedIndex = 0;
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },

        //----customer dropdown--
        closeDropdown(type) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                // this.isInputFocused.customer = false;

            } else if (type && type === 'invoice' && !this.mouseDownOnDropdown) {
                this.isDropdownOpenInvoice = false;
            }
        },
        //---customers dropdown
        selectDropdownOption(option) {
            this.formValues.customer_id = option.id;
            this.formValues.balance_amount = option.balance_amount;
            this.formValues.customer = option.first_name + (option.last_name ? ' ' + option.last_name : '') + ' - ' + option.contact_number;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
            this.formValues.opening_balance = { customer: option, balance_amount: option.opening_balance, date: option.created_at };
            if (!this.formValues.payment_amount && option.balance_amount) {
                this.formValues.payment_amount = option.balance_amount;
            }
        },
        preventBlur(type) {
            if (type && type === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            } else if (type && type === 'invoice' && !this.isDropdownOpenInvoice) {
                this.isInputFocused.invoice = false;
            }
        },
        //---search invoice---
        handleDropdownInputInvoices() {
            const inputValue = this.formValues.invoice;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredInvoiceOptions = this.invoices.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredInvoiceOptions = this.invoices.filter(
                        (option) => (option.invoice_id).toLowerCase().includes(inputName)
                    );
                }
            } else {
                if (this.invoices && this.invoices.length > 0) {
                    this.filteredInvoiceOptions = this.invoices;
                } else {
                    this.filteredInvoiceOptions = [];
                }
            }
            this.isDropdownOpenInvoice = !this.isDropdownOpenInvoice ? true : this.isDropdownOpenInvoice;
        },
        selectDropdownOptionInvoices(option) {
            this.filteredInvoiceOptions = [option];
            this.isDropdownOpenInvoice = false;
            this.selectedIndex = 0;
            if (this.formValues.payment_amount && option.balance_amount) {
                this.formValues.payment_amount = option.balance_amount;
            }
            else if (option.balance_amount) {
                this.formValues.payment_amount = option.balance_amount;
            }
        },
        //---get current data--
        getCurrentDate() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        //--get sales list---
        getSalesSearchList(status) {
            if (this.formValues.customer_id) {
                this.open_skeleton = true;
                // console.log(this.companyId, 'EEEEEEEEEEEEEEEE');
                axios.get('/searchs', { params: { company_id: this.companyId, type: 'sales', q: 'due', customer_id: this.formValues.customer_id, page: 1, per_page: 'all' } })
                    .then(response => {
                        // console.log(response.data, 'What happening...!');
                        this.open_skeleton = false;
                        this.invoices = response.data.data;
                        this.filteredInvoiceOptions = this.invoices;
                        if (status) {
                            this.formValues.payment_amount = this.invoices.reduce((total, opt) => total + opt.due_amount, 0);
                            this.formValues.balance_amount = this.formValues.payment_amount;
                            this.formValues.due_amount = this.formValues.payment_amount;
                        } else {
                            this.formValues.payment_amount = this.invoices.reduce((total, opt) => total + opt.due_amount, 0);
                            this.formValues.due_amount = this.formValues.payment_amount;
                        }
                        this.formValues.notes = '';
                        this.selectedRows = this.invoices.map((invoice) => invoice.id);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //----Overdue calculation--
        calculateDaysUntilDue(invoiceDateData) {
            if (this.now) {
                const invoiceDate = new Date(invoiceDateData);
                let dueDays = this.currentInvoice && this.currentInvoice.length > 0 ? this.currentInvoice[0].due_duration : 30;
                // console.log(dueDays, 'TT waht happeninggggg going the data....!');
                if (isNaN(invoiceDate.getTime())) {
                    console.error('Invalid invoice date');
                    return 0; // Return 0 as a fallback value in case of error
                }
                if (isNaN(this.now.getTime())) {
                    console.error('Invalid invoice date');
                    return 0; // Return 0 as a fallback value in case of error               
                }

                // Create a new date object for the due date
                const dueDate = new Date(invoiceDate);
                dueDate.setDate(invoiceDate.getDate() + dueDays);

                const currentTime = this.now.getTime();
                const dueTime = dueDate.getTime();

                // Calculate the difference in milliseconds
                const difference = dueTime - currentTime;

                // Convert milliseconds to days
                const differenceInDays = Math.ceil(difference / (1000 * 60 * 60 * 24));

                // If today is counted as the first day, increment the due days
                if (differenceInDays >= 0 && dueDate > this.now && differenceInDays <= dueDays) {
                    return differenceInDays;
                } else if (differenceInDays > dueDays) {
                    return dueDays;
                } else {
                    // Calculate the days until due (positive if due date is in the future, negative if overdue)
                    return differenceInDays;
                }
            }
        },
        calculateUntilDueDate(invoiceDateData) {
            if (this.now) {
                const invoiceDate = new Date(invoiceDateData);
                let dueDays = this.currentInvoice && this.currentInvoice.length > 0 ? this.currentInvoice[0].due_duration : 30;

                if (isNaN(invoiceDate.getTime())) {
                    console.error('Invalid invoice date');
                    return 'Invalid date'; // Return 'Invalid date' as a fallback value in case of error
                }
                if (isNaN(this.now.getTime())) {
                    console.error('Invalid current date');
                    return 'Invalid date'; // Return 'Invalid date' as a fallback value in case of error               
                }

                // Create a new date object for the due date
                const dueDate = new Date(invoiceDate);
                dueDate.setDate(invoiceDate.getDate() + dueDays);

                // Format the due date as "DD/MM/YYYY"
                const day = String(dueDate.getDate()).padStart(2, '0');
                const month = String(dueDate.getMonth() + 1).padStart(2, '0');
                const year = dueDate.getFullYear();

                return `${day}-${month}-${year}`;
            }
        },
        getCurrentDateTime() {
            const now = new Date();
            const day = String(now.getDate()).padStart(2, '0');
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const year = now.getFullYear();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');

            return `${day}/${month}/${year} ${hours}:${minutes}`;
        },
        //---get customer data
        getCustomerData(customer_id) {
            this.open_skeleton = true;
            axios.get(`/customer-details/${customer_id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // Handle response
                    // console.log(response.data.data);
                    this.customer_data = response.data.data;
                    this.formValues.customer_id = this.customer_data.id;
                    this.formValues.balance_amount = this.customer_data.balance_amount;
                    this.formValues.customer = this.customer_data.first_name + (this.customer_data.last_name ? ' ' + this.customer_data.last_name : '') + ' - ' + this.customer_data.contact_number;
                    this.isDropdownOpen = false; // Close the dropdown
                    this.selectedIndex = 0;
                    if (!this.formValues.payment_amount && this.customer_data.balance_amount) {
                        this.formValues.payment_amount = this.customer_data.balance_amount;
                    }

                    this.open_skeleton = false;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                    this.open_skeleton = false;
                });
        },
    },
    watch: {
        currentCustomer: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.customer_list = newValue;
                }
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    if (!this.formValues.payment_type && newValue[0].payment_opt && Array.isArray(JSON.parse(newValue[0].payment_opt))) {
                        let find_payment_type = JSON.parse(newValue[0].payment_opt).find(opt => opt.status === true);
                        if (find_payment_type) {
                            this.formValues.payment_type = find_payment_type.type;
                        }
                    }
                    // if (!this.formValues.bank_info && newValue[0].bank_details && newValue[0].bank_details !== '') {
                    //     this.formValues.bank_info = newValue[0].bank_details
                    // }
                }
            }
        },
        'formValues.customer_id': {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.getSalesSearchList();
                }
            }
        },
        'formValues.payment_amount': {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    if (this.formValues.balance_amount && this.formValues.balance_amount < newValue) {
                        this.formValues.return_amount = newValue - this.formValues.balance_amount;
                        this.formValues.remaining_amount = 0;
                    } else if (this.formValues.balance_amount && this.formValues.balance_amount > newValue) {
                        this.formValues.return_amount = 0;
                        this.formValues.remaining_amount = this.formValues.balance_amount - newValue;
                    } else if (this.formValues.balance_amount && this.formValues.balance_amount === newValue) {
                        this.formValues.return_amount = 0;
                        this.formValues.remaining_amount = 0;
                    }
                    // this.updatePaymentAmountInvoice();
                } else {
                    this.formValues.return_amount = 0;
                    this.formValues.remaining_amount = this.formValues.balance_amount;
                }
            }
        },
        selectedRows: {
            deep: true,
            handler(newValue) {
                if (newValue.length === 0) {
                    this.formValues.payment_amount = 0;
                }
            }
        }
    }
};
</script>

<style scoped>
/* Add your custom styles here if needed */
</style>