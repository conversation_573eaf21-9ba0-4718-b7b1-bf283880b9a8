<template>
    <div>
        <div id="printable-content" v-if="formData && formData.length > 0" class="m-2 mx-auto w-full px-5 prindContent"
            ref="templateContent"
            :class="{ 'text-sm': isAndroid && isPrinting, 'text-[5px]': isAndroid && !isPrinting, 'mt-[50px]': isMobile && !isPrinting }">
            <div class="w-full flex justify-between px-2"
                :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                <div class="flex items-center mb-2">
                    <div class="mr-2">{{ typeOfInvoice !== 'estimation' ? 'TAX INVOICE' : 'ESTIMATE INVOICE' }}
                    </div>
                    <div class="items-center">
                        <p class="border border-[#868597] rounded text-[#868597] uppercase px-1 text-center">Original
                            for Recipient</p>
                    </div>
                </div>
                <!--invoice type-->
                <div v-if="invoice_data.invoice_type">
                    <p class="border border-[#868597] rounded text-[#868597] uppercase px-1 items-center">{{
                        invoice_data.invoice_type === 'b2c' ? 'B2C' : 'BB' }}</p>
                </div>
            </div>
            <div class="grid grid-cols-3 items-center border border-b-0 bg-white">
                <div class="col-span-2 items-center px-2 py-1 ">
                    <div class="flex items-center">
                        <!---company name-->
                        <div>
                            <p class="text-[#0B6A9F] font-bold mb-1"
                                :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                                {{
                                    formData[0].name }}</p>
                        </div>
                    </div>
                    <!---company details-->
                    <div class="items-center"
                        :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                        <p>{{ formData[0].address }}</p>
                        <p v-if="formData[0].email !== ''"><span>Email:</span> {{ formData[0].email }}
                        </p>
                        <p v-if="formData[0].business_contact !== ''"><span>Contact:</span> {{
                            formData[0].business_contact
                            }}</p>
                        <p v-if="formData[0].gst_number && formData[0].gst_number !== ''">
                            <span>GST:</span> {{ formData[0].gst_number }}
                        </p>
                    </div>
                </div>
                <!--logo-->
                <div>
                    <div class="px-1 py-1  flex justify-center items-center">
                        <img v-if="logo_img" :src="logo_img" class="flex justify-center items-center object-fit"
                            :class="{ 'w-full h-28 ': !isAndroid || isPrinting, 'w-12 h-12 ': isAndroid && !isPrinting }" />
                        <div v-else>
                            <p class="font-bold text-green-600 text-xl">{{ formData[0].name }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <!--invoice to shipping to-->
            <div class="grid grid-cols-3 border-l border-r border-t bg-white">
                <div class="flex border-r px-2 py-2 "
                    :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                    <div v-if="customer_data">
                        <p>BILL TO</p>
                        <p class="font-bold">{{ customer_data.last_name ? customer_data.first_name : '' + ' ' +
                            customer_data.last_name
                            ?
                            customer_data.first_name : '' }}</p>
                        <p v-if="customer_data.address"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Address:</span>{{
                                customer_data.address
                            }}
                        </p>
                        <p v-if="customer_data.email"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Email:</span> {{
                                customer_data.email }}
                        </p>
                        <p :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Contact:</span> {{ customer_data.contact_number }}
                        </p>
                        <p v-if="invoice_data.invoice_type === 'b2b'"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>GST Tin -
                            </span>
                            {{ customer_data.gst_number }}
                        </p>
                    </div>
                </div>
                <div class="flex px-2 py-2 "
                    :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                    <div v-if="customer_data">
                        <p>SHIP TO</p>
                        <p class="font-bold">{{ customer_data.last_name ? customer_data.first_name : '' + ' ' +
                            customer_data.last_name
                            ?
                            customer_data.first_name : '' }}</p>
                        <p v-if="customer_data.address"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Address:</span> {{
                                customer_data.address
                            }}
                        </p>
                        <p v-if="customer_data.email"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Email:</span> {{
                                customer_data.email }}
                        </p>
                        <p :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Contact:</span> {{ customer_data.contact_number }}
                        </p>
                        <p v-if="invoice_data.invoice_type === 'b2b'"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>GST Tin -
                            </span>
                            {{ customer_data.gst_number }}
                        </p>
                        <p v-if="invoice_data.shipping_type"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Shipping
                                via:</span>
                            {{ invoice_data.shipping_type }}
                        </p>
                        <p v-if="invoice_data.cod"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Shipping ID:</span>
                            {{ invoice_data.cod }}
                        </p>
                    </div>
                </div>
                <!--invoice details-->
                <div class="grid grid-cols-2 gap-0 items-center border-l"
                    :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                    <div class="flex flex-col text-center px-1">
                        <p class="font-semibold">
                            {{ typeOfInvoice === 'estimation' ? 'Estimation No:' : 'Invoice No:' }}
                        </p>

                        <p>{{ invoice_data.invoice_number }}</p>
                    </div>
                    <div class="flex flex-col text-center px-1">
                        <p class="font-semibold">Date:</p>
                        <p :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">{{
                            getFormattedDateTime(invoice_data.current_date) }}</p>
                        <!---
                        // invoice_data.current_date ? new
                        // Date(invoice_data.current_date).toLocaleDateString('en-GB') : currentDate-->
                    </div>
                </div>
            </div>

            <div class="w-full overflow-x-auto">
                <table class="w-full">
                    <thead :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                        <tr class="border uppercase"
                            :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">
                            <th class="font-medium border-r">S.NO.</th>
                            <th class="p-1 font-medium border-r">Items</th>
                            <th class="font-medium border-r">HSN Code</th>
                            <th class="font-medium border-r">Qty</th>
                            <th class="font-medium border-r">Price</th>
                            <th class="font-medium border-r">Discount</th>
                            <th class="font-medium border-r">Tax</th>
                            <th class="font-medium">Total</th>
                        </tr>
                    </thead>
                    <tbody>

                        <!---add extra materials-->
                        <tr v-for="(item, index) in items_data" :key="index" class="text-center"
                            :class="{ 'page-break': (index + 1) === 20 || ((index + 1) % 30 === 0 && index + 1 !== 30), 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                            <!--Sr.no-->
                            <td class="p-1 border text-center">
                                <span class="mr-2">{{ index + 1 }}</span>
                            </td>
                            <!--Product name / description-->
                            <td class="p-1 border relative text-start px-3 py-1">
                                <p><span>{{ item.product_name }}</span></p>
                                <p v-if="item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0"
                                    :class="{ 'text-[10px]': !isAndroid || isPrinting, 'text-[3px]': isAndroid && !isPrinting }">
                                    Serial no: {{ item.serial_no.join(', ') }}</p>
                            </td>
                            <!--HSN code-->
                            <td class="p-1 border">
                                {{ item.hsn_code }}
                            </td>
                            <!--Quantity-->
                            <td class="p-1 border">
                                {{ item.qty }}
                            </td>
                            <!--Price-->
                            <td class="p-1 border">
                                <span>₹</span>{{ item.price }}
                            </td>
                            <!--discount value-->
                            <td class="p-1 border">
                                <span>₹</span>{{ item.discount }}
                            </td>
                            <!--tax-->
                            <td class="p-1 border">
                                <span>₹</span>{{ item.tax }}
                            </td>
                            <!--total value-->
                            <td class="p-1 border text-center">
                                <span>₹</span>{{ item.total }}
                            </td>
                        </tr>
                        <!-- Add empty rows to fill remaining space -->
                        <tr :style="{ height: isPrinting ? calculateRemainingSpace() + 'px' : '5px' }">
                            <td v-for="index in 8" :key="index" class="p-1 border">
                                <!-- Your content here -->
                            </td>
                        </tr>
                        <!---make gap 1-->
                        <tr>
                            <td colspan="8" class="py-1"></td>
                        </tr>
                        <!---new design-->
                        <tr :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                            <!---amount in words && payment details-->
                            <td colspan="4" class="h-full border">
                                <div class="grid grid-cols-1 px-1">
                                    <!---amount in words -->
                                    <div>
                                        <p class="font-semibold ">Total Amount (in words):</p>
                                        <p class="px-1">{{
                                            convertToWords(Math.round(get_all_data.grand_total)) }} Only</p>
                                    </div>
                                    <!--bank details-->
                                    <div v-if="formData[0].bank_details" class="mt-1">
                                        <p class="font-semibold ">Payment Information:</p>
                                        <p v-for="(line, l) in formData[0].bank_details.split('\n')" :key="l"
                                            class="px-1">
                                            {{ line }}
                                        </p>
                                    </div>
                                </div>
                            </td>
                            <!--amount details-->
                            <td colspan="4" class="border">
                                <div class="grid grid-cols-1 gap-1">
                                    <!--discount-->
                                    <div v-if="items_data && items_data.length > 0 && (Number(totalDiscount) + Number(get_all_data.discount_total)) > 0"
                                        class="grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }"
                                        :class="{ 'non-printable': totalDiscount <= 0 && get_all_data.discount_total <= 0 }">
                                        <div class="text-left p-1 uppercase">Discount (₹)</div>
                                        <div class="flex items-center">
                                            <p class="px-1 text-end"><span>₹</span>
                                                {{ (Number(totalDiscount) +
                                                    Number(get_all_data.discount_total)) }}</p>
                                        </div>
                                    </div>
                                    <!--Sub total-->
                                    <div v-if="items_data && items_data.length > 0"
                                        class="font-semibold grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">
                                        <div class="text-left p-1 uppercase">Sub Total</div>
                                        <!-- <div class="text-center p-1">{{ get_all_data.total_qty }}</div> -->
                                        <div class="text-end p-1"><span>₹</span>
                                            {{ Number(get_all_data.sub_total -
                                                get_all_data.total_tax).toFixed(2) }}
                                        </div>
                                    </div>
                                    <!--total tax-->
                                    <div v-if="items_data && items_data.length > 0 && formData[0].gstNumber !== ''"
                                        class="grid grid-cols-2"
                                        :class="{ 'non-printable': get_all_data.total_tax === 0 }"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">

                                        <div class="text-left p-1">
                                            CGST (₹)
                                        </div>
                                        <div class="text-end p-1"><span>₹</span>
                                            {{ (get_all_data.total_tax / 2).toFixed(2) }}
                                        </div>
                                    </div>
                                    <div v-if="items_data && items_data.length > 0 && formData[0].gstNumber !== ''"
                                        :class="{ 'non-printable': get_all_data.total_tax === 0 }"
                                        class="grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">

                                        <div class="text-left p-1">SGST (₹)</div>
                                        <div class="text-end p-1"><span>₹</span> {{ (get_all_data.total_tax /
                                            2).toFixed(2) }}
                                        </div>
                                    </div>
                                    <!--shipping charges-->
                                    <div v-if="items_data && items_data.length > 0 && get_all_data.shipping > 0"
                                        class="grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }"
                                        :class="{ 'non-printable': get_all_data.shipping === '' || get_all_data.shipping === 0 }">
                                        <div class="text-left p-1">Shipping (₹)</div>
                                        <div class="flex items-center justify-end">
                                            <p class="p-1 text-center"><span>₹</span> {{ get_all_data.shipping
                                                }}</p>
                                        </div>
                                    </div>
                                    <!--grand total-->
                                    <div v-if="items_data && items_data.length > 0" class="grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">
                                        <div class="text-left p-1 text-md font-semibold"><span class="uppercase">Grand
                                                Total
                                                (₹)</span></div>
                                        <div class="text-end text-md p-1 font-semibold"><span>₹</span>
                                            {{ formatCurrency(Math.round(get_all_data.grand_total)) }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <!---make gap 2-->
                        <tr>
                            <td colspan="8" class="py-1"></td>
                        </tr>
                        <!--Payment Details-->
                        <tr v-if="typeOfInvoice !== 'estimation'" class="border"
                            :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }"
                            :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                            <td colspan="8" class="text-center font-semibold py-1">Payment Details</td>
                        </tr>
                        <!--Payament type-->
                        <tr v-if="typeOfInvoice !== 'estimation' && paymentData && paymentData.length > 0"
                            class="border"
                            :class="{ 'non-printable': !paymentData[0].paid_amount, 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }"
                            v-for="(row, rowIndex) in paymentData" :key="rowIndex">
                            <td colspan="4" class="text-center px-2 border-r font-semibold">
                                {{ row.paid_type }}
                            </td>
                            <td colspan="4" class="px-1">
                                <p class="text-center">{{ row.paid_amount ? row.paid_amount.toFixed(2) : 0.00 }}
                                </p>
                            </td>
                        </tr>
                        <!--Return amount or balance-->
                        <tr v-if="typeOfInvoice !== 'estimation' && (return_amount.balance > 0 || return_amount.return > 0)"
                            class="border"
                            :class="{ 'non-printable': return_amount.balance === 0 && return_amount.return === 0, 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                            <td colspan="4" class="text-center p-1 border-r font-semibold">
                                <!-- <span>{{return_amount}}</span>                         -->
                                <span v-if="return_amount.return > 0">Return(₹)</span>
                                <span v-if="return_amount.balance > 0">Balance(₹)</span>
                            </td>
                            <td colspan="4" class="px-1 text-center font-bold">₹ {{ return_amount.balance > 0 ?
                                return_amount.balance.toFixed(2) : return_amount.return.toFixed(2) }}</td>
                        </tr>
                        <!-- Display grand total in text format -->
                        <!-- <tr class="border">
                        <td colspan="8" class="text-center p-1 text-md border-r"><strong>Total Amount (in words) : {{
                            convertToWords(Math.round(get_all_data.grand_total)) }} Only</strong>
                        </td>
                    </tr> -->
                    </tbody>
                </table>
            </div>
            <!---Amoint in words-->
            <!-- <div class="bg-white mt-2 p-3 py-2 border">
            <p>Total Amount (in words):</p>
            <p class="font-semibold text-gray-500">{{ convertToWords(Math.round(get_all_data.grand_total)) }} Only</p>

        </div> -->
            <!--footer-->
            <div class="flex w-full bg-white py-2">
                <div v-if="formData[0].disclaimer !== ''" class="p-3 py-2 border w-full">
                    <p class="font-bold"
                        :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">Terms &
                        Conditions:</p>
                    <p class="px-2"
                        :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }"
                        v-if="typeOfInvoice !== 'estimation' && formData[0].disclaimer"
                        v-for="(line, index) in formData[0].disclaimer.split('\n')" :key="index">
                        <span>{{ index + 1 }}. </span>{{ line }}
                    </p>
                    <p class="px-2"
                        :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }"
                        v-if="typeOfInvoice === 'estimation' && formData[0].est_disclaimer"
                        v-for="(line, index) in formData[0].est_disclaimer.split('\n')" :key="index">
                        <span>{{ index + 1 }}. </span>{{ line }}
                    </p>
                </div>
                <div class="w-1/3 border-r border-b border-t pt-3">
                    <p class="px-2 font-semibold text-center w-full"
                        :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                        For {{
                            formData[0].name }}</p>
                </div>
            </div>
        </div>
        <div class="flex justify-center text-sm  items-center mt-5 non-printable" :class="{ 'hidden': isPrinting }">
            <button v-if="!isViewSalesPage" @click="backToSetting"
                class="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded pl-3 pr-3 mr-2 sm:mr-4 lg:mr-12">Back</button>
            <button v-if="!isViewSalesPage" @click="editTheESTInvoice"
                class="p-2 bg-blue-700 hover:bg-blue-600 text-white rounded pl-3 pr-3 mr-2 sm:mr-4 lg:mr-12">Edit
                {{ typeOfInvoice === 'estimation' ? 'Estimation' : 'Invoice' }}</button>

            <button @click="printInvoice"
                class="text-sm p-2 bg-green-700 hover:bg-green-600 text-white rounded pl-3 pr-3">
                {{ typeOfInvoice === 'estimation' ? 'Print Estimation' : 'Print Invoice' }}
            </button>
            <!-- <button @click="downloadPDF"
                class="text-sm p-2 bg-green-700 hover:bg-green-600 text-white rounded pl-3 pr-3 ml-3">
                Download
            </button> -->
        </div>
        <div class="non-printable">
            <Loader :showModal="open_loader"></Loader>
        </div>
    </div>

</template>

<script>
import jsPDF from 'jspdf';
export default {
    props: {
        formData: {
            type: Object,
            default: () => ({})
        },
        viewServiceData: {
            type: Object,
            default: () => ({})
        },
        categoryID: {
            type: [Number, String],
            default: null
        },
        typeOfInvoice: {
            type: String,
            default: ''
        },
        service_id: {
            type: [Number, String],
            default: null
        },
        category_name: {
            type: [Number, String],
            default: null
        },
        customer_data: {
            type: Object,
            default: () => ({})
        },
        items_data: {
            type: Object,
            default: () => ({})
        },
        get_all_data: {
            type: Object,
            default: () => ({})
        },
        paymentData: {
            type: Object,
            default: () => ({})
        },
        payment_display: {
            type: Object,
            default: () => ({})
        },
        invoice_data: {
            type: Object,
            default: () => ({})
        },
        return_amount: {
            type: Object,
            default: () => ({})
        },
        logo_img: {
            type: String,
            default: null
        },
        page_name: {
            type: String,
            default: null
        },
        exist_data: {
            type: Object,
            default: () => ({})
        },
        companyId: {
            type: String,
            default: null,
        },
        isMobile: {
            type: Boolean,
            default: false,
        },
        isAndroid: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            currentDate: '',
            currentTime: '',
            isPrinting: false,
            open_loader: false,
        };
    },
    computed: {
        //---total discount--
        totalDiscount() {
            if (this.items_data && this.items_data.length > 0) {
                return this.items_data.reduce((sum, item) => (1 * sum) + (1 * item.discount), 0);
            }
        },
        isViewSalesPage() {
            return window.location.href.includes('viewsales');
        }
    },
    methods: {
        formatCurrency(value) {
            // Ensure value is a number before using toFixed
            const numericValue = parseFloat(value);
            if (isNaN(numericValue)) {
                // Handle the case where the value is not a valid number
                return "Invalid Amount";
            }

            return `${numericValue.toFixed(2)}`;
        },
        getCurrentDateTime() {
            const now = new Date();
            // Format the date
            const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
            this.currentDate = now.toLocaleDateString(undefined, options).replace(/\//g, '/');
            // Format the time
            this.currentTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        },
        convertToWords(amount) {
            const oneToTwenty = ['Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
            const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

            const convertBelowHundred = (num) => {
                if (num < 20) {
                    return oneToTwenty[num];
                } else {
                    return tens[Math.floor(num / 10)] + ' ' + oneToTwenty[num % 10];
                }
            };

            const convertBelowThousand = (num) => {
                if (num < 100) {
                    return convertBelowHundred(num);
                } else {
                    return oneToTwenty[Math.floor(num / 100)] + ' Hundred ' + convertBelowHundred(num % 100);
                }
            };

            const convertGroup = (num, suffix) => {
                if (num === 0) {
                    return '';
                } else {
                    return convertBelowThousand(num) + ' ' + suffix;
                }
            };

            if (amount === 0) {
                return 'Zero Rupees';
            } else {
                const groups = [];
                let remaining = amount;

                const suffixes = ['', 'Thousand', 'Million', 'Billion', 'Trillion', 'Quadrillion', 'Quintillion', /* and so on... */];

                for (let i = 0; remaining > 0; i++) {
                    const groupValue = remaining % 1000;
                    remaining = Math.floor(remaining / 1000);
                    groups.push(convertGroup(groupValue, suffixes[i]));
                }
                return groups.reverse().join(' ').trim() + ' Rupees';
            }
        },
        //---go back
        backToSetting() {
            // this.printing = false;
            if (this.page_name === 'setting') {
                this.$emit('goSetting');
            } else if (this.$route.query.type === 'sales_home') {
                this.$router.go(-1);
            } else {
                if (this.typeOfInvoice !== 'estimation') {
                    this.$router.push('/sales');
                } else {
                    this.$router.push('/estimation');
                }
                /*if (this.typeOfInvoice === 'Product' && this.exist_data) {
                    this.$router.push({
                        name: 'sales-invoice',
                        query: { type: 'edit', invoice_no: this.exist_data.id }
                    });

                } else if (this.typeOfInvoice === 'Services' && this.exist_data) {
                    this.getServiceData(this.exist_data);
                } else if (this.typeOfInvoice === 'estimation' && this.exist_data && this.$route.query.back !== 'home') {
                    this.$router.push({
                        name: 'addEstimation', // Name of the route
                        params: { type: 'product' }, // Parameter passed in the route path
                        query: { // Query parameters passed in the URL
                            type: 'edit',
                            est_no: this.exist_data.id
                        }
                    });
                }
                else {
                    this.$router.go(-1);
                }*/
            }
        },
        editTheESTInvoice() {
            if (this.typeOfInvoice !== 'estimation') {
                this.$router.push({
                    name: 'sales-invoice',
                    query: { type: 'edit', invoice_no: this.exist_data.id }
                });
            } else {
                this.$router.push({
                    name: 'addEstimation', // Name of the route
                    params: { type: 'product' }, // Parameter passed in the route path
                    query: { // Query parameters passed in the URL
                        type: 'edit',
                        est_no: this.exist_data.id
                    }
                });

            }
        },

        printInvoice() {
            this.isPrinting = true;
            this.open_loader = true;
            // Use window.print() to trigger the browser's print dialog
            this.$emit('sucessPrint');

            setTimeout(() => {
                // if (!this.isAndroid) {
                const options = {
                    name: "_blank",
                    specs: [
                        "fullscreen=yes",
                        "titlebar=yes",
                        "scrollbars=yes"
                    ],
                    styles: [
                        "https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css",
                        "https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css",
                        "https://unpkg.com/kidlat-css/css/kidlat.css",
                        "../../../../assets/main.css",
                    ],
                    timeout: 3000,
                    autoClose: true,
                    windowTitle: `${this.invoice_data.invoice_number ? this.invoice_data.invoice_number : 'invoice'}`,
                    paperSize: {
                        format: 'A4',
                        orientation: 'portrait', // or 'landscape' if you want
                        margin: '1cm' // Default margin is 1cm
                    }
                };
                this.$htmlToPaper('printable-content', options);
                // } else 
                // this.print();
                // }
                setTimeout(() => {
                    this.open_loader = false;
                    this.isPrinting = false;
                }, 500);
                // if (this.page_name !== 'setting' && this.typeOfInvoice !== 'estimation') {
                //     this.$router.push('/sales')
                // }
                // if (this.page_name !== 'setting' && this.typeOfInvoice === 'estimation') {
                //     this.$router.push('/estimation')
                // }
            }, 500);

            // After printing, reset the state
            // this.printing = false;
            // this.backToSetting();
            // this.$router.go(-1);
        },

        // printInvoice() {
        //     this.isPrinting = true;
        //     setTimeout(() => {
        //         html2pdf(document.getElementById("printable-content"), {
        //             margin: [10, 10, 10, 10],
        //             padding: 0,
        //             filename: "invoice_data.pdf",
        //             image: { type: 'jpeg', quality: 1.0 },
        //             html2canvas: { dpi: 75, scale: 1, scrollY: 0, dpi: 192, letterRendering: true },
        //             pagebreak: { mode: ['avoid-all', 'css', 'legacy'] },
        //             jsPDF: { orientation: 'portrait', unit: 'mm', format: 'a4', compressPDF: true },
        //         });
        //         this.isPrinting = false;
        //     }, 500)

        // },
        getServiceData(record) {
            axios.get(`/services/${record.service_id}`, { company_id: this.companyId })
                .then(response => {
                    console.log(response.data, 'What happening the get service..!');
                    this.service_data = response.data.data;
                    // let service_track_data = JSON.parse(newValue.service_data);
                    this.$router.push({
                        name: 'generate-invoice',
                        params: { type: this.service_data.servicecategory.service_category, id: this.service_data.servicecategory.id, serviceId: record.service_id },
                        query: { type: 'edit', invoice_no: record.id }
                    });
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        calculateRemainingSpace() {
            // Calculate the height of the printed content
            const printedContentHeight = document.querySelector('.prindContent').offsetHeight;
            // Height of A4 paper in pixels (approximately 842px)
            const a4PaperHeight = !this.isAndroid ? 1024 : 600;
            // Calculate the remaining height of the A4 paper
            const remainingHeight = a4PaperHeight - printedContentHeight;
            // Log the calculated remaining space
            // console.log('Remaining Space:', remainingHeight);
            // console.log(remainingHeight, 'RRRRR');
            // Return the remaining height
            return remainingHeight;
        },
        getFormattedDateTime(dateData) {
            const currentDate = new Date(dateData);
            const day = String(currentDate.getUTCDate()).padStart(2, '0');
            const month = String(currentDate.getUTCMonth() + 1).padStart(2, '0');
            const year = currentDate.getUTCFullYear();
            let hours = currentDate.getUTCHours();
            const minutes = String(currentDate.getUTCMinutes()).padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12;
            if (hours === 12 && ampm == 'AM') {
                const formattedDate = `${day}-${month}-${year}`;
                return formattedDate;
            } else {
                const formattedDate = `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
                return formattedDate;
            }
        },
        //---download pdf---
        async downloadPDF() {
            /*const element = this.$refs.templateContent;
            this.isPrinting = true;
            this.$emit('sucessPrint');
            setTimeout(() => {
                setTimeout(() => {
                    this.isPrinting = false;
                }, 500);
    
                if (!element) {
                    console.error('Invalid element provided as first argument');
                    return;
                }
                window.html2canvas = html2canvas;
                html2canvas(element).then(canvas => {
                    const imgData = canvas.toDataURL('image/png');
                    const pdf = new jsPDF({
                        orientation: 'p',
                        unit: 'px',
                        format: 'a4',
                        hotfixes: ['px_scaling'],
                    });
                    const imgProps = pdf.getImageProperties(imgData);
                    const pdfWidth = pdf.internal.pageSize.getWidth();
                    const pdfHeight = (imgProps.height * pdfWidth) / imgProps.width;
    
                    pdf.addImage(imgData, 'PNG', 0, 10, pdfWidth, pdfHeight);
                    pdf.save('template-3.pdf');
                }).catch(error => {
                    console.error('Error generating PDF:', error);
                });
            }, 500);*/

            // const element = this.$refs.templateContent;
            // if (!element) {
            //     console.error('Invalid element provided');
            //     return;
            // }

            // // Function to generate PDF
            // const generatePDF = async () => {
            //     try {
            //         const canvas = await html2canvas(element, { scale: 2 }); // Adjust scale as needed
            //         const imgData = canvas.toDataURL('image/png'); // Ensure the format is PNG or JPEG

            //         // Calculate PDF dimensions
            //         const pdf = new jsPDF('p', 'pt', 'a4');
            //         const pdfWidth = pdf.internal.pageSize.getWidth();
            //         const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

            //         // Add image to PDF
            //         pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
            //         pdf.save('template-3.pdf');
            //     } catch (error) {
            //         console.error('Error generating PDF:', error);
            //     }
            // };

            // generatePDF();

            // generatePDF().catch(error => {
            //     console.error('Error generating PDF:', error);
            // });
            // this.$refs.DownloadComp.generatePdf();


        },

        //---print methods----
        print() {
            //---second type---
            // Get HTML to print from element
            const prtHtml = document.getElementById('printable-content').innerHTML;

            // Get all stylesheets HTML
            let stylesHtml = '';
            for (const node of [...document.querySelectorAll('link[rel="stylesheet"], style')]) {
                stylesHtml += node.outerHTML;
            }
            // Include Tailwind CSS directly in stylesHtml
            stylesHtml += `
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://unpkg.com/kidlat-css/css/kidlat.css">`;
            //---<link rel="stylesheet" href="../../../../assets/main.css">

            // Open the print window
            const WinPrint = window.open('', '', 'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0');
            WinPrint.document.write(`<!DOCTYPE html><html><head>${stylesHtml}</head><body>${prtHtml}</body></html>`);
            WinPrint.document.close();
            WinPrint.focus();
            WinPrint.print();
            WinPrint.close();
        }

    },
    mounted() {
        this.getCurrentDateTime(); // Update every second if you want a live clock
        setInterval(() => {
            this.getCurrentDateTime();
        }, 60000);
        console.log(this.formData);
    },

};
</script>

<style scoped>
.m-2 {
    margin: 0.5rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.w-full {
    width: 100%;
}

.px-5 {
    padding-left: 1.25rem;
    padding-right: 1.25rem;
}

.text-sm {
    font-size: 0.875rem;
}

.mt-50px {
    margin-top: 50px;
}

.flex {
    display: flex;
}

.justify-between {
    justify-content: space-between;
}

.items-center {
    align-items: center;
}

.mb-2 {
    margin-bottom: 0.5rem;
}

.mr-2 {
    margin-right: 0.5rem;
}

.border {
    border: 1px solid #868597;
}

.rounded {
    border-radius: 0.25rem;
}

.uppercase {
    text-transform: uppercase;
}

.px-1 {
    padding-left: 0.25rem;
    padding-right: 0.25rem;
}

.text-center {
    text-align: center;
}

.font-bold {
    font-weight: 700;
}

.mb-1 {
    margin-bottom: 0.25rem;
}

.grid {
    display: grid;
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.py-1 {
    padding-top: 0.25rem;
    padding-bottom: 0.25rem;
}

.border-b-0 {
    border-bottom: 0;
}

.bg-white {
    background-color: white;
}

.col-span-2 {
    grid-column: span 2 / span 2;
}

.text-start {
    text-align: start;
}

.p-1 {
    padding: 0.25rem;
}

.relative {
    position: relative;
}

.text-xs {
    font-size: 0.75rem;
}

.text-md {
    font-size: 1.125rem;
}

.page-break {
    page-break-before: always;
}

.non-printable {
    display: none;
}

.font-semibold {
    font-weight: 600;
}

.p-3 {
    padding: 0.75rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.text-end {
    text-align: end;
}

.text-green-600 {
    color: #059669;
}

.text-xl {
    font-size: 1.25rem;
}

/*--manual style--*/
.filter {
    filter: blur(10px);
    /* Initial blur value */
    transition: filter 0.5s ease-in-out;
    /* Smooth transition */
}

.filter:hover {
    filter: blur(0);
    /* Remove blur on hover */
}

.actions-column {
    position: absolute;
    transform: translateY(-0%);
}

.border {
    border: 1px solid rgb(214, 211, 211);
}

.border-r {
    border-right: 1px solid rgb(214, 211, 211);
}

.border-l {
    border-left: 1px solid rgb(214, 211, 211);
}

.border-t {
    border-top: 1px solid rgb(214, 211, 211);
}

.border-b {
    border-bottom: 1px solid rgb(214, 211, 211);
}
</style>
