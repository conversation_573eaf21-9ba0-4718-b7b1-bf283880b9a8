<?php

namespace App\Http\Controllers;

use App\DataTables\PurchaseOrderItemDataTable;
use App\Http\Requests;
use App\Http\Requests\CreatePurchaseOrderItemRequest;
use App\Http\Requests\UpdatePurchaseOrderItemRequest;
use App\Repositories\PurchaseOrderItemRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class PurchaseOrderItemController extends AppBaseController
{
    /** @var PurchaseOrderItemRepository $purchaseOrderItemRepository*/
    private $purchaseOrderItemRepository;

    public function __construct(PurchaseOrderItemRepository $purchaseOrderItemRepo)
    {
        $this->purchaseOrderItemRepository = $purchaseOrderItemRepo;
    }

    /**
     * Display a listing of the PurchaseOrderItem.
     *
     * @param PurchaseOrderItemDataTable $purchaseOrderItemDataTable
     *
     * @return Response
     */
    public function index(PurchaseOrderItemDataTable $purchaseOrderItemDataTable)
    {
        return $purchaseOrderItemDataTable->render('purchase_order_items.index');
    }

    /**
     * Show the form for creating a new PurchaseOrderItem.
     *
     * @return Response
     */
    public function create()
    {
        return view('purchase_order_items.create');
    }

    /**
     * Store a newly created PurchaseOrderItem in storage.
     *
     * @param CreatePurchaseOrderItemRequest $request
     *
     * @return Response
     */
    public function store(CreatePurchaseOrderItemRequest $request)
    {
        $input = $request->all();

        $purchaseOrderItem = $this->purchaseOrderItemRepository->create($input);

        Flash::success('Purchase Order Item saved successfully.');

        return redirect(route('purchaseOrderItems.index'));
    }

    /**
     * Display the specified PurchaseOrderItem.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $purchaseOrderItem = $this->purchaseOrderItemRepository->find($id);

        if (empty($purchaseOrderItem)) {
            Flash::error('Purchase Order Item not found');

            return redirect(route('purchaseOrderItems.index'));
        }

        return view('purchase_order_items.show')->with('purchaseOrderItem', $purchaseOrderItem);
    }

    /**
     * Show the form for editing the specified PurchaseOrderItem.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $purchaseOrderItem = $this->purchaseOrderItemRepository->find($id);

        if (empty($purchaseOrderItem)) {
            Flash::error('Purchase Order Item not found');

            return redirect(route('purchaseOrderItems.index'));
        }

        return view('purchase_order_items.edit')->with('purchaseOrderItem', $purchaseOrderItem);
    }

    /**
     * Update the specified PurchaseOrderItem in storage.
     *
     * @param int $id
     * @param UpdatePurchaseOrderItemRequest $request
     *
     * @return Response
     */
    public function update($id, UpdatePurchaseOrderItemRequest $request)
    {
        $purchaseOrderItem = $this->purchaseOrderItemRepository->find($id);

        if (empty($purchaseOrderItem)) {
            Flash::error('Purchase Order Item not found');

            return redirect(route('purchaseOrderItems.index'));
        }

        $purchaseOrderItem = $this->purchaseOrderItemRepository->update($request->all(), $id);

        Flash::success('Purchase Order Item updated successfully.');

        return redirect(route('purchaseOrderItems.index'));
    }

    /**
     * Remove the specified PurchaseOrderItem from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $purchaseOrderItem = $this->purchaseOrderItemRepository->find($id);

        if (empty($purchaseOrderItem)) {
            Flash::error('Purchase Order Item not found');

            return redirect(route('purchaseOrderItems.index'));
        }

        $this->purchaseOrderItemRepository->delete($id);

        Flash::success('Purchase Order Item deleted successfully.');

        return redirect(route('purchaseOrderItems.index'));
    }
}
