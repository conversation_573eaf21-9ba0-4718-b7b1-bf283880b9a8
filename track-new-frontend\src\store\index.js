// store/index.js
import { createStore } from "vuex";
import lead from "./modules/lead";
import customer from "./modules/customer";
import auth from "./modules/auth";
import dashboard from "./modules/dashboard";
import service from "./modules/service";
import items from "./modules/items"; //---search
import purchase from "./modules/purchase"; //--search
import supplier from "./modules/supplier";
import invoice_setting from "./modules/invoice_setting";
import employess from "./modules/employess";
import serviceCategories from "./modules/serviceCategories";
import leadType from "./modules/leadType";
import serviceList from "./modules/serviceList";
import leadsList from "./modules/leadsList";
import amcsList from "./modules/amcsList";
import salesList from "./modules/salesList";
import holdsList from "./modules/holdsList";
import companies from "./modules/companies";
import estimations from "./modules/estimations";
import itemsList from "./modules/itemsList"; //---list
import purchaseList from "./modules/purchaseList"; //--list
import warehouse from "./modules/warehouse"; //---search
import supplierList from "./modules/supplierList"; //---list
import warehouseList from "./modules/warehouseList"; //---list
import customerList from "./modules/customerList"; //---list
import customerCategories from "./modules/customerCategories"; //----categories
import expensesList from "./modules/expensesList";
import expensesTypeList from "./modules/expensesTypeList";
import employeesList from "./modules/employeesList"; //---list
import dashboardData from "./modules/dashboardData"; //---dashboard data
import localStorageData from "./modules/localStorageData";
import brandUnitCategoryItem from "./modules/brandUnitCategoryItem"; //---brand, unit, category
import proforma from "./modules/proforma";
import paymentInList from "./modules/paymentInList";
import paymentOutList from "./modules/paymentOutList";
import rmaList from "./modules/rmaList";
import searchSerial from "./modules/searchSerial";
import accessories from "./modules/accessories";
import importCloud from "./modules/importCloud"; //---cloud templates
import subscription from "./modules/subscription";
import subscriptionHistory from "./modules/subscriptionHistory";
import features_list from "./modules/features_list";
import dataFilter from "./modules/dataFilter";
import servicesSearchSerial from "./modules/servicesSearchSerial";
import servicesAdvanceSearch from "./modules/servicesAdvanceSearch"; //---service anvance search---
import searchSerialSales from "./modules/searchSerialSales";
import searchSerialPurchase from "./modules/searchSerialPurchase";
import notificationsList from "./modules/notificationsList";
import notificationAllow from "./modules/notificationAllow";
import modalIsOpen from "./modules/modalIsOpen";
import sidebarandBottombarList from "./modules/sidebarandBottombarList";
import sortIcons from "./modules/sortIcons";
import websiteBuilder from "./modules/websiteBuilder";
import webEnquiry from "./modules/webEnquiry";
import recordsPerPage from "./modules/recordsPerPage";
import banner from "./modules/banner";
import apiUpdates from "./modules/apiUpdates";
import whatsappSetting from "./modules/whatsappSetting";
import createamcservice from "./modules/createamcservice";
import statecode from "./modules/statecode";
import device from "./modules/device";
import clone from "./modules/clone";

const store = createStore({
  modules: {
    lead: lead,
    customer: customer,
    auth: auth,
    dashboard: dashboard,
    service: service,
    items: items,
    purchase: purchase,
    supplier: supplier,
    invoice_setting: invoice_setting,
    employess: employess,
    serviceCategories: serviceCategories,
    leadType: leadType,
    serviceList: serviceList,
    leadsList: leadsList,
    amcsList: amcsList,
    salesList: salesList,
    holdsList: holdsList,
    companies: companies,
    estimations: estimations,
    itemsList: itemsList,
    purchaseList: purchaseList,
    warehouse: warehouse,
    supplierList: supplierList,
    warehouseList: warehouseList,
    customerList: customerList,
    customerCategories: customerCategories,
    expensesList: expensesList,
    expensesTypeList: expensesTypeList,
    employeesList: employeesList,
    dashboardData: dashboardData,
    localStorageData: localStorageData,
    brandUnitCategoryItem: brandUnitCategoryItem,
    proforma: proforma,
    paymentInList: paymentInList,
    rmaList: rmaList,
    searchSerial: searchSerial,
    accessories: accessories,
    importCloud: importCloud,
    paymentOutList: paymentOutList,
    subscription: subscription,
    subscriptionHistory: subscriptionHistory,
    features_list: features_list,
    dataFilter: dataFilter,
    servicesSearchSerial: servicesSearchSerial,
    servicesAdvanceSearch: servicesAdvanceSearch,
    searchSerialSales: searchSerialSales,
    searchSerialPurchase: searchSerialPurchase,
    notificationsList: notificationsList,
    notificationAllow: notificationAllow,
    modalIsOpen: modalIsOpen,
    sidebarandBottombarList: sidebarandBottombarList,
    sortIcons: sortIcons,
    websiteBuilder: websiteBuilder,
    webEnquiry: webEnquiry,
    recordsPerPage: recordsPerPage,
    banner: banner,
    apiUpdates: apiUpdates,
    whatsappSetting: whatsappSetting,
    createamcservice: createamcservice,
    statecode: statecode,
    device: device,
    clone: clone,
  },
  actions: {
    resetAll({ commit }) {
      commit("lead/RESET_STATE", null, { root: true });
      commit("customer/RESET_STATE", null, { root: true });
      commit("auth/RESET_STATE", null, { root: true });
      commit("dashboard/RESET_STATE", null, { root: true });
      commit("items/RESET_STATE", null, { root: true });
      commit("purchase/RESET_STATE", null, { root: true });
      commit("supplier/RESET_STATE", null, { root: true });
      commit("invoice_setting/RESET_STATE", null, { root: true });
      commit("employess/RESET_STATE", null, { root: true });
      commit("serviceCategories/RESET_STATE", null, { root: true });
      commit("leadType/RESET_STATE", null, { root: true });
      commit("serviceList/RESET_STATE", null, { root: true });
      commit("leadsList/RESET_STATE", null, { root: true });
      commit("amcsList/RESET_STATE", null, { root: true });
      commit("salesList/RESET_STATE", null, { root: true });
      commit("holdsList/RESET_STATE", null, { root: true });
      commit("companies/RESET_STATE", null, { root: true });
      commit("estimations/RESET_STATE", null, { root: true });
      commit("itemsList/RESET_STATE", null, { root: true });
      commit("purchaseList/RESET_STATE", null, { root: true });
      commit("warehouse/RESET_STATE", null, { root: true });
      commit("supplierList/RESET_STATE", null, { root: true });
      commit("warehouseList/RESET_STATE", null, { root: true });
      commit("customerList/RESET_STATE", null, { root: true });
      commit("expensesList/RESET_STATE", null, { root: true });
      commit("expensesTypeList/RESET_STATE", null, { root: true });
      commit("employeesList/RESET_STATE", null, { root: true });
      commit("dashboardData/RESET_STATE", null, { root: true });
      commit("localStorageData/RESET_STATE", null, { root: true });
      commit("brandUnitCategoryItem/RESET_STATE", null, { root: true });
      commit("proforma/RESET_STATE", null, { root: true });
      commit("paymentInList/RESET_STATE", null, { root: true });
      commit("rmaList/RESET_STATE", null, { root: true });
      commit("searchSerial/RESET_STATE", null, { root: true });
      commit("accessories/RESET_STATE", null, { root: true });
      commit("importCloud/RESET_STATE", null, { root: true });
      commit("paymentOutList/RESET_STATE", null, { root: true });
      commit("subscription/RESET_STATE", null, { root: true });
      commit("subscriptionHistory/RESET_STATE", null, { root: true });
      commit("features_list/RESET_STATE", null, { root: true });
      commit("dataFilter/RESET_STATE", null, { root: true });
      commit("servicesSearchSerial/RESET_STATE", null, { root: true });
      commit("servicesAdvanceSearch/RESET_STATE", null, { root: true });
      commit("searchSerialSales/RESET_STATE", null, { root: true });
      commit("searchSerialPurchase/RESET_STATE", null, { root: true });
      commit("notificationsList/RESET_STATE", null, { root: true });
      commit("notificationAllow/RESET_STATE", null, { root: true });
      commit("modalIsOpen/RESET_STATE", null, { root: true });
      commit("sortIcons/RESET_STATE", null, { root: true });
      commit("websiteBuilder/RESET_STATE", null, { root: true });
      commit("webEnquiry/RESET_STATE", null, { root: true });
      commit("recordsPerPage/RESET_STATE", null, { root: true });
      commit("banner/RESET_STATE", null, { root: true });
      commit("apiUpdates/RESET_STATE", null, { root: true });      
      commit("whatsappSetting/RESET_STATE", null, { root: true });  
      commit("createamcservice/RESET_STATE", null, { root: true });
      commit("statecode/RESET_STATE", null, { root: true });     
      commit("device/RESET_STATE", null, { root: true });    
      commit("clone/RESET_STATE", null, { root: true });   
    },
  },
});

export default store;
