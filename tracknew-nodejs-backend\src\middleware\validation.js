const { validationResult } = require('express-validator');
const { AppError } = require('./errorHandler');

// Middleware to handle validation errors
const validateRequest = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    const errorMessages = errors.array().map(error => ({
      field: error.path,
      message: error.msg,
      value: error.value
    }));

    return next(new AppError('Validation failed', 422, errorMessages));
  }
  
  next();
};

// Custom validation functions
const validateObjectId = (value) => {
  const objectIdRegex = /^[0-9a-fA-F]{24}$/;
  return objectIdRegex.test(value);
};

const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const validateMobile = (mobile) => {
  const mobileRegex = /^[0-9]{10,15}$/;
  return mobileRegex.test(mobile.replace(/\D/g, ''));
};

const validatePassword = (password) => {
  // At least 6 characters, one uppercase, one lowercase, one number
  const passwordRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).{6,}$/;
  return passwordRegex.test(password);
};

const validateGST = (gst) => {
  // Indian GST number format: 15 characters
  const gstRegex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/;
  return gstRegex.test(gst);
};

const validatePAN = (pan) => {
  // Indian PAN number format: 10 characters
  const panRegex = /^[A-Z]{5}[0-9]{4}[A-Z]{1}$/;
  return panRegex.test(pan);
};

const validateURL = (url) => {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
};

const validateDateRange = (startDate, endDate) => {
  const start = new Date(startDate);
  const end = new Date(endDate);
  return start <= end;
};

const validateFileType = (filename, allowedTypes) => {
  const extension = filename.split('.').pop().toLowerCase();
  return allowedTypes.includes(extension);
};

const validateFileSize = (size, maxSize) => {
  return size <= maxSize;
};

// Sanitization functions
const sanitizeString = (str) => {
  if (typeof str !== 'string') return str;
  return str.trim().replace(/[<>]/g, '');
};

const sanitizeEmail = (email) => {
  if (typeof email !== 'string') return email;
  return email.toLowerCase().trim();
};

const sanitizePhone = (phone) => {
  if (typeof phone !== 'string') return phone;
  return phone.replace(/\D/g, '');
};

// Validation middleware factory
const createValidationMiddleware = (validations) => {
  return [
    ...validations,
    validateRequest
  ];
};

module.exports = {
  validateRequest,
  validateObjectId,
  validateEmail,
  validateMobile,
  validatePassword,
  validateGST,
  validatePAN,
  validateURL,
  validateDateRange,
  validateFileType,
  validateFileSize,
  sanitizeString,
  sanitizeEmail,
  sanitizePhone,
  createValidationMiddleware
};
