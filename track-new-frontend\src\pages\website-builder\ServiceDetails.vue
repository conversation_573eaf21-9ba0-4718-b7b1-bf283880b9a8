<template>
  <div class="builder-page">
    <div class="flex flex-col md:flex-row justify-between items-center">
      <h1 class="web-head">Service Details</h1>
      <div class="w-full md:w-auto md:self-end">
        <EnablePageName :pages="is_onSetting" @updatePagesEnabled="handleToggle"></EnablePageName>
      </div>
    </div>
    <!-- Add Service Button -->
    <div class="flex justify-end mb-6">
      <button @click="openModal" class="bg-blue-500 text-white py-2 px-4 rounded shadow">
        Add Service <font-awesome-icon icon="fa-solid fa-plus-circle" />
      </button>
    </div>
    <!-- Service Cards -->
    <div v-if="localServices && localServices.services" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="(service, index) in localServices.services" :key="index"
        class="bg-white shadow-lg rounded-lg p-4 relative" :draggable="true" @dragstart="onDragStart(index, $event)"
        @dragover.prevent @drop="onDrop(index, $event)" @dragenter="onDragEnter(index, $event)" @dragend="onDragEnd">
        <div @click="editService(index)" class="cursor-pointer">
          <img v-if="service && service.imageUrl && !Array.isArray(service.imageUrl)" :src="service.imageUrl"
            alt="Product Image" class="w-full h-48 object-cover rounded-lg mb-4" />
          <div v-if="service && service.imageUrl && Array.isArray(service.imageUrl)" class="grid grid-cols-2 gap-1">
            <div v-for="(img, index) in service.imageUrl" :key="index">
              <img :src="img" alt="Product Image" class="h-16 w-16 object-cover rounded-lg" />
            </div>
          </div>
          <h3 class="text-lg font-semibold">{{ service.title }}</h3>
          <p class="text-gray-700">{{ extractPlainText(service.shortDescription) }}</p>
        </div>
        <div class="mt-4 flex justify-between">
          <!-- Edit Button -->
          <button @click="editService(index)" class="text-blue-500">
            <font-awesome-icon icon="fa-solid fa-edit" />
          </button>
          <!-- Delete Button -->
          <button @click="deleteService(index)" class="text-red-500">
            <font-awesome-icon icon="fa-solid fa-trash" />
          </button>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="mt-6">
      <NavigationButtons :pageTitle="'Service Details'" @goToNextPage="goToNextPage" @goToPrevPage="goToPrevPage" />
    </div>

    <!-- Add/Edit Service Modal -->
    <service-modal :showModal="showModal" :isEditMode="isEditMode" :service="currentService"
      :categories="localServices.categories" :company_id="companyId" @save="handleSaveService" @close="closeModal"
      @updatecategory="updatecategory" />
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
  </div>
</template>

<script>
import ServiceModal from './ServiceModal.vue'; // Ensure correct import
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import imageService from '../../services/imageService';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import EnablePageName from './EnablePageName.vue';

export default {
  props: {
    companyId: {
      type: String,
      required: true
    },
    servicesData: {
      type: Object,
      required: true,
    },
    is_updated: { type: Boolean, required: true },
    pages: {
      type: Object,
      required: true
    },
  },
  components: {
    ServiceModal,
    NavigationButtons,
    FontAwesomeIcon,
    confirmbox,
    EnablePageName
  },

  data() {
    return {
      localServices: {}, // List of services
      showModal: false, // Control modal visibility
      isEditMode: false, // To toggle between add/edit mode
      currentService: null, // Service to edit
      currentIndex: null, // Index of service being edited
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      //----is on settings---
      is_onSetting: { is_on: true, name: 'Services' },
      draggingIndex: null,
    };
  },
  watch: {
    // Watch for localServices changes to emit updates to the parent
    localServices: {
      handler(newServices) {
        this.$emit('updateServices', newServices); // Emit updated services to parent
      },
      deep: true, // Ensure nested changes are observed
    },
    is_updated: {
      deep: true,
      handler(newValue) {
        this.$emit('updateServices', this.localServices);
        this.$emit('updatePagesSetting', { service: this.is_onSetting });
      }
    },
    is_onSetting: {
      deep: true,
      handler(newValue) {
        this.$emit('updatePagesSetting', { service: newValue });
      }
    },
    servicesData: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.localServices.services = newValue.services;
          // console.log(this.localServices, 'GGGGGGGGGGGGGGG');
        }
      }
    }
  },
  mounted() {
    if (this.servicesData && Array.isArray(this.servicesData) && this.servicesData.length > 0) {
      this.localServices = { services: this.servicesData, categories: [{ id: 100, name: 'General' }] };
    } else {
      this.localServices = this.servicesData;
    }
    if (this.pages && this.pages.service !== undefined) {
      this.is_onSetting = this.pages.service;
    }
  },
  methods: {
    openModal() {
      this.isEditMode = false;
      this.currentService = null;
      this.showModal = true;
    },
    editService(index) {
      this.isEditMode = true;
      this.currentIndex = index;
      this.currentService = { ...this.localServices.services[index] };// Load service data
      this.showModal = true;
    },
    handleSaveService(serviceData) {
      if (this.isEditMode) {
        this.localServices.services.splice(this.currentIndex, 1, serviceData); // Update existing service
        this.$emit('updateServices', this.localServices);
        this.$emit('updatePagesSetting', { service: this.is_onSetting });
        this.$emit('submitData');
      } else {
        if (this.localServices.services) {
          this.localServices.services.push(serviceData); // Add new service
        } else {
          this.localServices.services = [{ ...serviceData }];
        }
        this.$emit('updateServices', this.localServices);
        this.$emit('updatePagesSetting', { service: this.is_onSetting });
        this.$emit('submitData');
      }
      // this.closeModal();
    },
    async deleteService(index) {
      this.deleteIndex = index;
      this.open_confirmBox = true;
    },
    closeModal() {
      this.showModal = false;
    },
    goToNextPage() {
      this.$emit('updateServices', this.localServices);
      this.$emit('updatePagesSetting', { service: this.is_onSetting });
      this.$emit('submitData');
      this.$emit('goToNextPage'); // Emit event to the parent component
    },
    goToPrevPage() {
      this.$emit('updateServices', this.localServices);
      this.$emit('updatePagesSetting', { service: this.is_onSetting });
      this.$emit('submitData');
      this.$emit('goToPrevPage'); // Emit event to the parent component
    },
    async deleteRecord() {
      this.$emit('updateLoader', true);
      // Remove the service from the local array
      const serviceToDelete = this.localServices.services[this.deleteIndex];
      // Check if the service has an image URL to delete      
      try {
        // Handle the deletion of image(s)
        if (serviceToDelete && serviceToDelete.imageUrl) {
          if (Array.isArray(serviceToDelete.imageUrl)) {
            // Delete each image in the array
            await Promise.all(serviceToDelete.imageUrl.map(async (file) => {
              if (file) {
                try {
                  await imageService.deleteImage(file, 'services');
                } catch (error) {
                  console.error('Error deleting image:', error);
                }
              }
            }));
          } else {
            // Delete a single image
            await imageService.deleteImage(serviceToDelete.imageUrl, 'services');
          }
        }

        // Handle the deletion of the service brochure
        if (serviceToDelete.service_brochure && serviceToDelete.service_brochure !== '') {
          await imageService.deleteImage(serviceToDelete.service_brochure, 'services');
        }

        // Remove the service from localServices
        this.localServices.services.splice(this.deleteIndex, 1);
        this.closeconfirmBoxData();

        // Emit success message
        this.$emit('toasterMessages', { msg: 'Services deleted successfully', type: 'success' });
      } catch (error) {
        console.error('Error deleting service:', error);

        // Handle error response for image not found
        if (error.response && error.response.data.error === 'Image not found') {
          this.localServices.services.splice(this.deleteIndex, 1);
        }

        // Emit failure message
        this.$emit('toasterMessages', { msg: 'Services cannot be deleted', type: 'warning' });
        this.closeconfirmBoxData();
      }

    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
    },
    //--categories--
    async updatecategory(data) {
      if (data && data.length > 0) {
        this.$emit('updateServiceCategories', data);
        this.$emit('submitData');
        this.localServices.categories = data;
      }
    },
    //--handle switch--
    handleToggle(data) {
      // Perform any additional actions when toggled
      if (data) {
        this.is_onSetting = data;
      }
    },
    //--editor--
    isValidBase64(str) {
      try {
        // Check if the string matches Base64 format
        return !!str.match(/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/);
      } catch (error) {
        console.error("Error validating Base64 string:", error);
        return false;
      }
    },
    base64Decode(encodedData) {
      try {
        return atob(encodedData); // Decode Base64 data
      } catch (error) {
        console.error("Error decoding Base64 data:", error);
        return ""; // Return empty string on error
      }
    },
    extractPlainText(htmlString) {
      if (htmlString && this.isValidBase64(htmlString)) {
        const decoded = this.base64Decode(htmlString); // Decode Base64       

        // Create a temporary DOM element to parse HTML and extract plain text
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = decoded;
        return tempDiv.textContent || tempDiv.innerText || ""; // Extract text content
      } else {
        return htmlString;
      }
    },
    //---drag and drop reorder the list--
    onDragStart(index, event) {
      this.draggingIndex = index;
      event.dataTransfer.effectAllowed = 'move'; // Set drag effect
      event.dataTransfer.setData('text/plain', index); // Store the index of the dragged item
    },
    onDragEnter(index, event) {
      if (this.draggingIndex !== index) {
        // Visually highlight the target element
        const target = event.target;
        target.classList.add('drag-over');
      }
    },
    onDragEnd() {
      this.draggingIndex = null;
      // Clean up any visual highlight after dragging ends
      const draggedItems = document.querySelectorAll('.drag-over');
      draggedItems.forEach(item => item.classList.remove('drag-over'));
    },
    onDrop(targetIndex, event) {
      const sourceIndex = event.dataTransfer.getData('text/plain');
      const draggedItem = this.localServices.services[sourceIndex];

      // Move the dragged item to the target position
      this.localServices.services.splice(sourceIndex, 1);
      this.localServices.services.splice(targetIndex, 0, draggedItem);

      this.$emit('updateServices', this.localServices); // Emit updated services list if necessary
    }
  }
};
</script>
<style scoped>
.drag-over {
  background-color: #f0f0f0;
  /* Light gray background when hovering over an item */
}
</style>
