<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="set-header-background justify-between items-center flex py-2">
                <h2 class="text-white font-bold text-center ml-12 text-lg py-2">
                    Tax List</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>

            <!-- Form for CRUD operations -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-4 m-4">
                <!--loader-->
                <skeleton v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
                    :rows="number_of_rows" :gap="gap" :type="'grid'">
                </skeleton>

                <input v-if="!open_skeleton" type="text" v-model="formValues.tax_name" placeholder="tax Name"
                    ref="inputName" class="block mb-3 border px-3 py-2 w-full">
                <input v-if="!open_skeleton" type="number" v-model="formValues.value" @keyup.enter="submitForm"
                    placeholder="tax value" class="block mb-3 border px-3 py-2 w-full">
                <button v-if="!open_skeleton" @click="submitForm"
                    class=" rounded rounded-md px-3 py-2 mt-3 bg-green-700 hover:bg-green-600 text-white">
                    {{ updateIndex === null ? 'Create' : 'Update' }}</button>
                <button v-if="!open_skeleton"
                    class=" rounded rounded-md px-3 py-2 mt-3 bg-red-700 hover:bg-red-600 text-white ml-5"
                    @click="cancelModal">Close</button>
            </div>

            <!-- Display categories -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-3 m-4 text-sm"
                :key="categories.length">
                <p class="font-bold underline mb-2">List:</p>
                <ul>
                    <li v-for="(category, index) in categories" :key="index" class="flex justify-between">
                        <div>{{ category.tax_name + ' @ ' + category.value }}</div>
                        <div class="flex justify-between">
                            <button @click="editCategory(index)">
                                <img :src="table_edit" alt="table-edit" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                            <button @click="deleteCategory(index)">
                                <img :src="table_del" alt="table-delete" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    </div>
</template>

<script>
import confirmbox from './confirmbox.vue';
import dialogAlert from './dialogAlert.vue';
export default {
    name: 'taxList',
    components: {
        confirmbox,
        dialogAlert
    },
    props: {
        showModal: Boolean,
        categoriesData: Object,
        invoice_setting: Object
    },
    data() {
        return {
            isMobile: false,
            formValues: {},
            isOpen: false,
            categories: [],
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            deleteIndex: null,
            open_confirmBox: false,
            updateIndex: null,
            isMessageDialogVisible: false,
            message: '',
            //--api integration---
            companyId: null,
            userId: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 1,
            number_of_rows: 3,
            gap: 5,

        };
    },
    methods: {
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeModal', this.categories);
                this.categories = [];
            }, 300);

        },
        submitForm() {
            this.open_skeleton = true;
            // console.log(this.updateIndex, 'What is the index valuye...!', this.formValues);
            if (this.formValues.tax_name && this.formValues.value >= 0 && this.updateIndex === null) {
                // console.log(this.categories);
                axios.put(`/invoice_settings/${this.invoice_setting[0].id}`, { selected_tax: JSON.stringify([...this.categories, this.formValues]), company_id: this.companyId })
                    .then(response => {
                        // Handle success response
                        // console.log('Response:updated', response.data);
                        this.open_skeleton = false;
                        this.categories = JSON.parse(response.data.data.selected_tax);
                        this.formValues = {};
                        this.handleFocus();
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);
                        this.open_skeleton = false;
                    });
            } else if (this.updateIndex !== null && this.formValues.tax_name !== '' && this.formValues.value !== '') {
                this.categories.splice(this.updateIndex, 1, this.formValues);
                axios.put(`/invoice_settings/${this.invoice_setting[0].id}`, { ...this.invoice_setting, selected_tax: JSON.stringify(this.categories), company_id: this.companyId })
                    .then(response => {
                        // Handle success response
                        console.log('Response: created', response.data);
                        this.open_skeleton = false;
                        this.categories = JSON.parse(response.data.data.selected_tax);
                        this.formValues = {};
                        this.handleFocus();
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);
                        this.open_skeleton = false;
                    });
                this.updateIndex = null;
            } else {
                this.open_skeleton = false;
                this.isMessageDialogVisible = true;
                this.message = 'Please fill all required fields..!';
            }
        },
        editCategory(index) {
            // Edit category (not implemented in this example)
            // console.log('Edit category:', this.categories[index]);
            this.formValues = this.categories[index];
            this.updateIndex = index;
            // console.log(index, 'WWWWWW');
        },
        deleteCategory(index) {
            // Delete category
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },

        loadFromLocalStorage() {
            // console.log(this.type, 'What happening...!');
            let storedCategories = [];
            //---lead type---
            axios.get('/taxes', {
                params: {
                    company_id: this.companyId
                }
            })
                .then(response => {
                    // Handle response
                    console.log(response.data.data);
                    storedCategories = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });

            if (storedCategories.length !== 0) {
                // console.log(storedCategories, 'WERWRWRW');
                this.categories = storedCategories;
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.inputName;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---confirm box funxctions
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                // console.log(this.deleteIndex, 'Whatjjjjjj');
                this.categories.splice(this.deleteIndex, 1);
                axios.put(`/invoice_settings/${this.invoice_setting[0].id}`, { selected_tax: JSON.stringify(this.categories), company_id: this.companyId })
                    .then(response => {
                        console.log(response.data);
                        this.categories = (JSON.parse(response.data.data.selected_tax));
                        this.deleteIndex = null;
                    })
                    .catch(error => {
                        console.error(error);
                    });

                // this.saveToLocalStorage();
                this.open_confirmBox = false;
            }
        },

        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---close message--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
        }
    },

    mounted() {
        this.updateIsMobile();
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                    this.categories = this.categoriesData;
                }
            }, 100);
        },
        // categoriesData(newValue) {
        //     this.categories = newValue;
        //     console.log(newValue, 'What is happening....! by the moduleis...!');
        // }
    },
};
</script>

<style>
/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.close {
    color: white;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: red;
    text-decoration: none;
    cursor: pointer;
}
</style>