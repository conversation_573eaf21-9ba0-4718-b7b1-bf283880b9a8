// store/modules/apiUpdates.js
const state = {
    lastApiUpdates: {},
    lastFetchTime: null, // To track the last fetched time
    isFetching: false, // To track if the request is in progress   
    is_update: {
      customer: false,
      customer_list: false,
      }
    };
  
  const mutations = {
    SET_API_UPDATE_TIME(state, { apiName, time }) {
      state.lastApiUpdates[apiName] = time;
      },
      SET_API_UPDATE(state, data) {
          state.lastApiUpdates = data;
      },
      SET_LAST_FETCH_TIME(state, time) {
        state.lastFetchTime = time; // Save the timestamp when the API was last accessed
      },
      SET_IS_FETCHING(state, status) {
        state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
      },
      RESET_STATE(state) {
        state.lastApiUpdates = {};
        state.lastFetchTime = null;
        state.isFetching = false;
        state.is_update = {
          customer: false,
          customer_list: false,
        };
    },
    SET_UPDATE_KEY(state, { key }) {      
      if (state.lastApiUpdates && Object.keys(state.lastApiUpdates).length > 0 && key) {
        // Get the current time in ISO 8601 format with milliseconds
        const currentTime = new Date().toISOString(); // Format: 2025-01-20T08:48:03.000Z
        // console.log(state.lastApiUpdates[key], 'Before');    
        // Update the state with the key and current time
        state.lastApiUpdates[key] = currentTime;
        // console.log(state.lastApiUpdates[key], 'After');        
      }      
    },
    SET_ISUPDATE_KEY(state, { key, value }) {      
      if (state.is_update && Object.keys(state.is_update).length > 0 && key) {        
        state.is_update[key] = value;       
      }      
    },
    
  };
  
const actions = {  
    updateApiTimestamp({ commit }, { apiName, time }) {
      commit('SET_API_UPDATE_TIME', { apiName, time });
      },
      //--update api routes--
    async fetchApiUpdates({ commit }) {        
        const now = new Date().getTime();
        const thirtySecondsInMilliseconds = 5 * 1000;       
        // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
        if (state.isFetching || (state.lastFetchTime && (now - state.lastFetchTime) < thirtySecondsInMilliseconds)) {
          return; // Skip request if less than 30 seconds have passed since the last request
        } 
       
            try {
              const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
              if (company_id && company_id !== '') {
                 // Set the request status to true (indicating that the request is in progress)
                commit('SET_IS_FETCHING', true);
                axios.get(`/last-updates/${company_id}`)
                  .then(response => {
                    // Handle response
                    // console.log(response.data, 'api updateds..!');
                    let update_list = response.data;              
                    
                      commit('SET_API_UPDATE', update_list);
                      commit('SET_LAST_FETCH_TIME', now);
                      commit('SET_IS_FETCHING', false);
                    return update_list;
                  })
                  .catch(error => {
                    // Handle error
                      console.error('Error:', error);
                      commit('SET_IS_FETCHING', false);
                    return error;
                  });
              }  
            } catch (error) {
                console.error('Error fetching item list:', error);
                commit('SET_IS_FETCHING', false);
            } 
    },
    updateKeyWithTime({ commit }, key) {      
      commit('SET_UPDATE_KEY', { key });
  },
  updateKeyIsUpdate({ commit },{ key, value}) {      
    commit('SET_ISUPDATE_KEY', { key, value });
  },
  };
  
  const getters = {
    getLastApiUpdateTime: (state) => (apiName) => {
      return state.lastApiUpdates[apiName];
    },
  };
  
  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
  };
  