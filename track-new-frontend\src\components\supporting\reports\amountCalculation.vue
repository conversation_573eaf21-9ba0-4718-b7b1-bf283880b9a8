<template>
    <div class="p-6 bg-white shadow-md rounded-md my-3 border border-gray-300">
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">

        </div>
    </div>
</template>

<script>
export default {
    props: {
        selected_option: String,
        data: Object,
        getStatusOption: Object,
    },
    data() {
        return {

        };
    },
    computed: {

    },
    methods: {


    },
    mounted() {

    },
    watch: {

    }
};
</script>

<style scoped>
/* Add any custom styles here */
</style>