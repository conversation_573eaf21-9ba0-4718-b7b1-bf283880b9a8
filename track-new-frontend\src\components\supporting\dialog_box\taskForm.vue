<template>
    <div class="max-w-md mx-auto mt-10">
        <form class="bg-white shadow-md rounded px-8 pt-6 pb-8 mb-4">
            <!-- Task Title -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="taskTitle">
                    Task Title
                </label>
                <input v-model="formData.taskTitle"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    id="taskTitle" type="text" placeholder="Enter task title" />
            </div>

            <!-- Assign Date & Time -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="assignDate">
                    Assign Date & Time
                </label>
                <input v-model="formData.assignDate"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    id="assignDate" type="datetime-local" />
            </div>

            <!-- Assign To -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="assignTo">
                    Assign To
                </label>
                <input v-model="formData.assignTo"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    id="assignTo" type="text" placeholder="Enter person name" />
            </div>

            <!-- Description -->
            <div class="mb-4">
                <label class="block text-gray-700 text-sm font-bold mb-2" for="description">
                    Description
                </label>
                <textarea v-model="formData.description"
                    class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                    id="description" placeholder="Enter task description" rows="4"></textarea>
            </div>

            <!-- Buttons -->
            <div class="flex items-center justify-between">
                <button type="button" @click="handleSubmit"
                    class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Submit
                </button>
                <button @click="handleCancel" type="button"
                    class="bg-gray-500 hover:bg-gray-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</template>

<script>
export default {
    data() {
        return {
            formData: {
                taskTitle: '',
                assignDate: '',
                assignTo: '',
                description: '',
            },
        };
    },
    methods: {
        // Handle form submission
        handleSubmit() {
            console.log('Form Data:', this.formData);
            // Add your form submission logic here
        },

        // Handle form cancellation
        handleCancel() {
            // Reset the form data
            this.formData = {
                taskTitle: '',
                assignDate: '',
                assignTo: '',
                description: '',
            };
            console.log('Form reset');
        },
    },
};
</script>

<style scoped>
/* Add any custom styles if needed */
</style>