<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateCompanySettingsAPIRequest;
use App\Http\Requests\API\UpdateCompanySettingsAPIRequest;
use App\Models\CompanySettings;
use App\Models\Option;
use App\Repositories\CompanySettingsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class CompanySettingsController
 * @package App\Http\Controllers\API
 */

class CompanySettingsAPIController extends AppBaseController
{
    /** @var  CompanySettingsRepository */
    private $companySettingsRepository;

    public function __construct(CompanySettingsRepository $companySettingsRepo)
    {

        $this->companySettingsRepository = $companySettingsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/companySettings",
     *      summary="getCompanySettingsList",
     *      tags={"CompanySettings"},
     *      description="Get all CompanySettings",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/CompanySettings")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companySettings = $this->companySettingsRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($companySettings->toArray(), 'Company Settings retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/companySettings",
     *      summary="createCompanySettings",
     *      tags={"CompanySettings"},
     *      description="Create CompanySettings",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CompanySettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateCompanySettingsAPIRequest $request)
    {
        $input = $request->all();

        $companySettings = $this->companySettingsRepository->create($input);

        return $this->sendResponse($companySettings->toArray(), 'Company Settings saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/companySettings/{id}",
     *      summary="getCompanySettingsItem",
     *      tags={"CompanySettings"},
     *      description="Get CompanySettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CompanySettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CompanySettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */

  
  	public function show($id)
	{
        /** @var CompanySettings $companySettings */
        $companySettings = CompanySettings::where('company_id', $id)->first();

        if (empty($companySettings)) {
            return $this->sendError('Company Settings not found');
        }

        // Reboot the instance and fetch QR code
        //$this->rebootInstance($companySettings->whatsapp_id);
        $response = $this->getQrCode($companySettings->whatsapp_id);
        $responseArray = json_decode($response, true);

        // Handle response and update company settings
        if (isset($responseArray['status']) && $responseArray['status'] === 'error') {
            $message = $responseArray['message'];

            if ($message === 'Instance ID Invalidated' || $message === 'Instance ID is required') {
                if ($companySettings->whatsapp_status === 1) {
                    $companySettings->whatsapp_status = 0;
                    $companySettings->save();
                }
            }
        }
        if (isset($responseArray['status']) && $responseArray['message'] === 'Instance ID has been used') {
            $companySettings->whatsapp_auth = json_encode($responseArray['data']);
            if ($companySettings->whatsapp_status === 0) {
                $companySettings->whatsapp_status = 1;
                $this->setWebhook($companySettings->whatsapp_id);
              
            }
          
          
           

    
            // Stale PID file, remove it
            
          
          
          
          	
            $companySettings->save();
        }

        return $this->sendResponse($companySettings->toArray(), 'Company Settings Retrieved successfully');
	}


    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/companySettings/{id}",
     *      summary="updateCompanySettings",
     *      tags={"CompanySettings"},
     *      description="Update CompanySettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CompanySettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CompanySettings"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateCompanySettingsAPIRequest $request)
    {
        $input = $request->all();

        // Find the CompanySettings by company_id
        $companySettings = CompanySettings::where('company_id', $id)->first();

        if ($companySettings) {
            // Update existing CompanySettings
            $companySettings->fill($input);
            $companySettings->save();
            return $this->sendResponse($companySettings, 'WhatsApp template updated successfully');
        } else {
            // Create new CompanySettings
            $companySettings = new CompanySettings($input);
            $companySettings->company_id = $id;
            $companySettings->save();
            return $this->sendResponse($companySettings, 'WhatsApp template created successfully');
        }
    }
  
   public function getDisconnect($id, UpdateCompanySettingsAPIRequest $request)
    {
        $input = $request->all();
		
        // Find the CompanySettings by company_id
        $companySettings = CompanySettings::where('company_id', $id)->first();
     	if(!$companySettings){
          return $this->sendError('Company not found', 200);
        }

        if ($companySettings) {
            // Update existing CompanySettings
            $companySettings->whatsapp_status = 0;            
            $companySettings->save();
          	$response = $this->resetInstance($companySettings->whatsapp_id);
          	 if (!isset($response['status']) || $response['status'] !== 'success') {
                return response()->json(['success' => false, 'message' => $response['message']], 500);        			
    		}
            return response()->json(['success' => true, 'message' => 'Disconnected success'], 200);
        }else{
          return response()->json(['success' => false, 'message' => 'Failed to disconnect '], 200);
        }
    }


public function getResponse(Request $request)
{   	
  
     //   \Log::info('Incoming request data:', [   
      //      'request' => $request->all(), // Logs all input data    
      //   ]);

    $data = $request->all();
  	

    // Validate the required fields
    if (isset($data['instance_id'])) {
        $instanceId = $data['instance_id'];
      	//Cache::put('sse_data'.$instanceId, $data);
      //  $event = $data['data']['event'];

        // Check if the event is "new subscriber"
        //if ($event === 'new subscriber') {
            // Find the record by instance_id
         //   $companySetting = CompanySettings::where('whatsapp_id', $instanceId)->first();

          //  if ($companySetting) {
                // Update whatsapp_status to 1
              //  $companySetting->update(['whatsapp_status' => 1]);

              //  return $this->sendResponse(['status' => 'success', 'message' => 'WhatsApp status updated'], 200);
           // } else {
                // If no record is found for the instance_id
              //  return $this->sendResponse(['status' => 'error', 'message' => 'Instance ID not found'], 404);
           // }
       // }

        // Return a response if the event is not "new subscriber"
        //return $this->sendResponse(['status' => 'ignored', 'message' => 'Event not processed'], 200);
    }

    // Handle missing or invalid data
   // return $this->sendResponse(['status' => 'error', 'message' => 'Invalid payload'], 400);
   
}


  
public function getQr($id, Request $request)
{
     $input = $request->all();
     
	 try {
        $result = CompanySettings::where('company_id', $id)->first();

        if (!$result) {
            $maxRetries = 2;
            $retryCount = 0;

            do {
                $newInstance = $this->createInstance();
                $existingInstance = CompanySettings::where('whatsapp_id', $newInstance['instance_id'])->first();
                $retryCount++;
            } while ($existingInstance && $retryCount < $maxRetries);

            if ($retryCount >= $maxRetries) {
                throw new \Exception('Failed to create a unique instance after multiple attempts');
            }

            $result = CompanySettings::create([
                'company_id' => $id,
                'whatsapp_id' => $newInstance['instance_id'],
                'whatsapp' => Option::where('key', 'whatsapp_templates')->first()->value ?? '',
            ]);
        }
   

    $instanceId = $result->whatsapp_id;
   //    \Log::info('ids:', [   
         //             'ins' => $instanceId
       //             ]);
       
					
    try {
        $response = $this->getQrCode($instanceId);
        $responseArray = json_decode($response, true);
       			
		
        if (isset($responseArray['status']) && $responseArray['status'] === 'error') {
            if ($responseArray['message'] === 'Instance ID Invalidated' || $responseArray['message'] === 'Instance ID is required') {
                $maxRetries = 2; 
                $retryCount = 0;
              	//$res = $this->resetInstance($instanceId);
              

                do {                    
                    $newInstance = $this->createInstance();
                    $existingInstance = CompanySettings::where('whatsapp_id', $newInstance['instance_id'])->first();
                    $retryCount++;
                //   	\Log::info('whatsapp:', [   
                  //    'request' =>$newInstance
                //    ]);
                } while ($existingInstance && $retryCount < $maxRetries);

                if ($retryCount >= $maxRetries) {
                    return response()->json(['error' => 'Failed to create a unique instance after multiple attempts'], 500);
                }

                $result->update([
                    'whatsapp_id' => $newInstance['instance_id'],
                ]);

                $response = $this->getQrCode($newInstance['instance_id']);
            }
        }

        if (isset($responseArray['status']) && $responseArray['message'] === 'Instance ID has been used') {
            if ($result->whatsapp_status === 0) {
                $result->whatsapp_status = 1;
                $result->save();
                // $this->setWebhook($instanceId, $authToken);
            }
        }

        return $response;
    } catch (\Exception $e) {
        return response()->json(['error' => $e->getMessage()], 500);
    }
       } finally {
        // Remove the PID file after QR scanning is complete
        //@unlink($pidFile);
    }
}



  
protected function createInstance()
{
    $url = "https://cloud.wazender.in/api/create_instance";  
    $queryParams = [
        'access_token' => env('WHATSAPP_KEY'),
    ];

    $response = $this->makeApiRequest($url, $queryParams);

    if (!isset($response['status']) || $response['status'] !== 'success') {
        throw new \Exception('Failed to generate instance ID: ' . ($response['message'] ?? 'Unknown error'));
    }

    if (!isset($response['instance_id'])) {
        throw new \Exception('Instance ID not found in response');
    }

    return [
        'instance_id' => $response['instance_id'],
        'access_token' => env('WHATSAPP_KEY'),
    ];
}

  
protected function getQrCode($instanceId)
{
    $url = "https://cloud.wazender.in/api/get_qrcode";
    $queryParams = [
        'instance_id' => $instanceId,
        'access_token' => env('WHATSAPP_KEY')
    ];

    return $this->makeApiRequest($url, $queryParams, false); // Return raw response
}
  
protected function reconnectInstance($instanceId)
{
    $url = "https://cloud.wazender.in/api/reconnect";
    $queryParams = [
        'instance_id' => $instanceId,
        'access_token' => env('WHATSAPP_KEY')
    ];
    $response = $this->makeApiRequest($url, $queryParams);
   	// if (!isset($response['status']) || $response['status'] !== 'success') {
    //  throw new \Exception('Failed to reboot instance: ' . ($response['message'] ?? 'Unknown error'));
    //}
    return $response;
}

  
protected function rebootInstance($instanceId)
{
    $url = "https://cloud.wazender.in/api/reboot";
    $queryParams = [
        'instance_id' => $instanceId,
        'access_token' => env('WHATSAPP_KEY')
    ];

    $response = $this->makeApiRequest($url, $queryParams);

    //if (!isset($response['status']) || $response['status'] !== 'success') {
       // throw new \Exception('Failed to reboot instance: ' . ($response['message'] ?? 'Unknown error'));
    //}

    return $response;
}
  
  protected function resetInstance($instanceId)
{
    $url = "https://cloud.wazender.in/api/reset_instance";
    $queryParams = [
        'instance_id' => $instanceId,
        'access_token' => env('WHATSAPP_KEY')
    ];

    $response = $this->makeApiRequest($url, $queryParams);

   // if (!isset($response['status']) || $response['status'] !== 'success') {
      //  throw new \Exception('Failed to reboot instance: ' . ($response['message'] ?? 'Unknown error'));
    //}

    return $response;
}

 protected function setWebhook($instanceId)
{
    // Define the API URL for setting the webhook
    $url = "https://cloud.wazender.in/api/set_webhook";

    // Prepare query parameters
    $queryParams = [
        'webhook_url' => 'https://devapi.track-new.com/api/whatsapp-response',
        'enable' => 'true',
        'instance_id' => $instanceId,
        'access_token' => env('WHATSAPP_KEY'),
    ];

    // Make the API request
    $response = $this->makeApiRequest($url, $queryParams);

    // Validate response
   // if (!isset($response['status']) || $response['status'] !== 'success') {
     //   throw new \Exception('Failed to set webhook: ' . ($response['message'] ?? 'Unknown error'));
  //  }

    return $response;
}




protected function makeApiRequest($url, $queryParams, $decode = true)
{
 
    $curl = curl_init();
    try {
        curl_setopt_array($curl, [
            CURLOPT_URL => $url . '?' . http_build_query($queryParams),
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'GET',
            CURLOPT_SSL_VERIFYPEER => false,
        ]);

        $response = curl_exec($curl);

        if (curl_errno($curl)) {
            throw new \Exception(curl_error($curl));
        }
     // \Log::info('what:', [   
       //     'request' => $response, // Logs all input data    
       //  ]);

        return $decode ? json_decode($response, true) : $response;
    } finally {
        curl_close($curl);
    }
}



  
  


    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/companySettings/{id}",
     *      summary="deleteCompanySettings",
     *      tags={"CompanySettings"},
     *      description="Delete CompanySettings",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CompanySettings",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var CompanySettings $companySettings */
        $companySettings = $this->companySettingsRepository->find($id);

        if (empty($companySettings)) {
            return $this->sendError('Company Settings not found');
        }

        $companySettings->delete();

        return $this->sendSuccess('Company Settings deleted successfully');
    }
}
