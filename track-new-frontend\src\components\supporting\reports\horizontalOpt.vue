<template>
    <div class="bg-white shadow-lg  rounded border">
        <div class="flex justify-around p-2 overflow-auto">
            <div v-for="option in options" :key="option.name"
                class="icon-text-container cursor-pointer hover:text-blue-600 " :class="selectedOption(option)"
                :style="{ display: option.hasAccess === false ? 'none' : '' }" @click="clickOption(option)">
                <font-awesome-icon :icon="option.icon" />
                <!-- <i :class="option.icon" class="text-gray-700"></i> -->
                <span class="pt-2 line-clamp-1">{{ option.name }}</span>
            </div>
        </div>
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    props: {
        change_option: String,
    },
    data() {
        return {
            options: [
                { id: 1, name: 'Services', icon: 'fa-solid fa-wrench' },
                { id: 5, name: 'Sales', icon: 'fas fa-chart-line' },
                { id: 11, name: 'Customer Ledger', icon: 'fa-solid fa-user' },
                { id: 6, name: '<PERSON>orma', icon: 'fas fa-file-invoice' },
                { id: 7, name: 'Estimation', icon: 'fas fa-calculator' },
                { id: 9, name: 'Purchase', icon: 'fa-solid fa-cart-shopping' },
                { id: 2, name: 'Leads', icon: 'fas fa-user-friends' },
                { id: 3, name: 'AMC', icon: 'fas fa-file-contract' },
                { id: 8, name: 'Expense', icon: 'fas fa-money-bill-wave' },
                { id: 10, name: 'Stock', icon: 'fa-solid fa-arrow-trend-up' },
                { id: 4, name: 'RMA', icon: 'fas fa-exchange-alt' },
                { id: 12, name: 'Outstanding', icon: 'fas fa-wallet' },
                { id: 13, name: 'GSTR', icon: 'fas fa-table-list' },
                // { name: 'Customer', icon: 'fas fa-users' },
                // { name: 'Employees', icon: 'fas fa-user-tie' },
            ],
            selected_opt: 'Services',
        };
    },
    computed: {
        ...mapGetters('features_list', ['currentFeatureList']),
    },
    methods: {
        ...mapActions('features_list', ['fetchFeatureList']),
        selectedOption(opt) {
            if (opt && this.selected_opt && this.selected_opt === opt.name) {
                return 'text-blue-600 border-b-4 border-blue-600 rounded'
            }
        },
        clickOption(opt) {
            if (opt) {
                this.selected_opt = opt.name;
                localStorage.setItem('report_option', JSON.stringify({ option: opt.name }));
            }
        },
        filterListOptions() {
            if (this.currentFeatureList && this.currentFeatureList.length > 0) {
                this.options.forEach(opt => {
                    // Find the corresponding item in array1 based on id
                    const selected_opt = this.currentFeatureList.find(feature => feature.id === opt.id);
                    // If found, add the hasaccess property to item2
                    if (selected_opt) {
                        opt.hasAccess = selected_opt.hasAccess;
                    }
                })
            }
        }
    },
    mounted() {
        const view = localStorage.getItem('report_option');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.option) {
                this.selected_opt = parse_data.option;
            }
        }
        if (this.currentFeatureList && this.currentFeatureList.length > 0) {
            this.filterListOptions();
        }
        this.fetchFeatureList();
        if (this.selected_opt && this.selected_opt !== '') {
            this.$emit('change-opt', this.selected_opt)
        }
    },
    watch: {
        selected_opt: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.$emit('change-opt', newValue);
                }
            }
        },
        currentFeatureList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.filterListOptions();
                }
            }
        },
        change_option: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue !== '') {
                    this.selected_opt = newValue;
                    localStorage.setItem('report_option', JSON.stringify({ option: newValue }));
                }
            }
        }

    }
};
</script>

<style scoped>
.icon-text-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    text-align: center;
}

.icon-text-container i {
    font-size: 24px;
    margin-bottom: 4px;
}
</style>