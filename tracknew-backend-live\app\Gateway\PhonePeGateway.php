<?php

namespace App\Gateway;

use Illuminate\Http\Request;
use App\Models\Gateways;
use App\Models\UserGatewaySubscribers;
use PhonePe\payments\v1\PhonePePaymentClient; // Correct namespace based on SDK
//use PhonePe\Payment\v1\PgPayRequestBuilder; // Correct namespace based on SDK
use PhonePe\payments\v1\models\request\builders\PgPayRequestBuilder;
use PhonePe\payments\v1\models\request\builders\InstrumentBuilder; // Correct namespace based on SDK
use PhonePe\common\config\Env; // Correct namespace based on SDK
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Session;
use DB;
use Auth;

class PhonePeGateway
{
    protected static $phonePeClient;
    protected static $salt;
    protected static $saltIndex;
  	protected static $merchant_id;

    public static function init()
    {
        // Fetch gateway configuration from the database
        $gatewayConfig = Gateways::where('name', 'phonepe')->first();

        if (!$gatewayConfig) {
            abort(500, 'PhonePe gateway configuration not found.');
        }
     
      $payment_data = [];
      if (!empty($gatewayConfig->data)) {
            foreach (json_decode($gatewayConfig->data ?? '') ?? [] as $key => $info) {
                $payment_data[$key] = $info;
            }
        }

        // Initialize the PhonePe client with configuration
       
		self::$merchant_id = $payment_data['merchant_id'];
        self::$salt = $payment_data['salt'];
        self::$saltIndex = $payment_data['salt_index'];
        self::$phonePeClient = new PhonePePaymentClient(
              $payment_data['merchant_id'],
              self::$salt,
              self::$saltIndex,
              $payment_data['env'] ?? "UAT",
              $payment_data['should_publish_events'] ?? true
          );
    }

    public static  function make_payment(array $data, $txd)
    {
      
      if (!self::$phonePeClient) {
            self::init();
        }      
       
        $paymentData = [
          	'merchant_id' => $data['merchant_id'],
            'amount' => $data['pay_amount'] * 100, 
            'currency' => $data['currency'],
            'callback_url' => $data['callback_url'],
            'description' => $data['billName'],
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'charge' => $data['charge'],
            'salt' => $data['salt'],
          	'order_id' => 'ORD' . date('YmdHis') . rand(1000, 9999),
            'salt_index' => $data['salt_index'],
            'is_fallback' => $data['is_fallback'] ?? 0
        ];
      
      
     

     try {
 
            $request = self::buildPayPageRequest($paymentData);        
    		$retryCount = 0;
            $maxRetries = 5;
            $delay = 1; // start with a 1 second delay

            while ($retryCount < $maxRetries) {
            try {
                $response = self::$phonePeClient->pay($request);
                // If successful, exit the loop
                break;
            } catch (\Exception $e) {
                if (strpos($e->getMessage(), 'TOO_MANY_REQUESTS') !== false && $retryCount < $maxRetries) {
                    // Apply exponential backoff strategy
                    sleep($delay);
                    $retryCount++;
                    $delay *= 2;
                } else {
                    throw $e;
                }
            }
        }
       
       if (!isset($response)) {
            throw new \Exception('Failed to process the payment after multiple attempts.');
        }         
      
            $payPageUrl = $response->getInstrumentResponse()->getRedirectInfo()->getUrl();
       		$m_id = $response->getMerchantTransactionId(); 
       
       	 	//$paymentSession = DB::table('payment_sessions')->where('transaction_id', $txd)->first();
           $datas = [
              'payment_id' => $paymentData['order_id'],
              'payment_method' => 'PhonePe',           
              'status' => 1,
              'meta' => json_encode($response),
              'm_txd' => $m_id,
              'payment_status' => 1
          ];

          // Perform the update
          $affectedRows = DB::table('session_payments')
              ->where('transaction_id', $txd)
              ->update($datas);
                
           // Session::put('payment_info',$datas);
			//Session::put('payment_response', $response);
            return self::success([
                'pay_page_url' =>  $payPageUrl
            ]);
        } catch (\Exception $e) {
          // Log::error('PhonePe API request failed: ' . $e->getMessage());
           return self::error('Payment request failed' . $e->getMessage());
        }
    }
  
  	protected static function buildPayPageRequest(array $data)
    {
      
     
        // Adjust based on actual SDK methods
        $builder = PgPayRequestBuilder::builder(); // Example class name
        $request = $builder->mobileNumber($data['phone'])
                           ->callbackUrl($data['callback_url'])
                           ->merchantId($data['merchant_id'])
                           ->merchantUserId('TAMILANONLINE')
                           ->amount($data['amount'])
                           ->merchantTransactionId($data['order_id'])          				   
                           ->redirectUrl('https://app.track-new.com/subscription')
                           ->redirectMode("REDIRECT")
                           ->paymentInstrument(InstrumentBuilder::buildPayPageInstrument())
                           ->build(); 
      
     // var_dump($request);
      //exit();

        return $request;
    }


    public static function verify_payment($m_id)
    {
      
      if (!self::$phonePeClient) {
            self::init();
        }

 		

        try {
                         // Assuming $m_id is your merchant ID and self::$phonePeClient->statusCheck($m_id) returns a PgCheckStatusResponse object
              $response = self::$phonePeClient->statusCheck($m_id);

             

              // Assuming the PgCheckStatusResponse class has a method to get the state
              $status = $response->getState(); // Assuming there's a method getState() to get the state

              // Check the payment status
              if ($status === 'COMPLETED') {
                  return [
                      'status' => 'success',
                      'data' => [
                          'payment_id' => $response->getTransactionId(), // Assuming getTransactionId() method exists
                          'status' => 'success',
                          'message' => 'Payment successful'
                      ]
                  ];
              } else {
                  return [
                      'status' => 'error',
                      'message' => 'Payment verification failed'
                  ];
              }
        } catch (\Exception $e) {
            Log::error('PhonePe payment verification failed: ' . $e->getMessage());
            return [
                'status' => 'error',
                'message' => 'Payment verification failed'
            ];
        }
    }

    private static function success($data)
    {
        return response()->json([
            'status' => 'success',
            'data' => $data
        ]);
    }

    private static function error($message)
    {
        return response()->json([
            'status' => 'error',
            'message' => $message
        ]);
    }
  
   public static function init_subscription(array $data, $txd, $vpa, $is_qr)
{
     
    
    if (!self::$phonePeClient) {
        self::init(); // Ensure the client is initialized
    }

    try {
        // Prepare the subscription data payload
        $subscriptionData = [
            'merchantId' => self::$merchant_id, // Merchant ID
            'merchantSubscriptionId' => 'MSUB' . date('YmdHis') . rand(1000, 9999), // Unique subscription ID
          	'merchantTransactionId' => 'MTRANS' . date('YmdHis') . rand(1000, 9999), // Unique subscription ID
            'merchantUserId' => $data['merchantUserId'] ?? 'default_user', // User identifier
            'authWorkflowType' => $data['authWorkflowType'] ?? 'TRANSACTION', // Workflow type (PENNY_DROP or TRANSACTION)
            'amountType' => 'VARIABLE', // Type of amount (FIXED or VARIABLE)
            'amount' => 15000*100,
            'frequency' => $data['frequency'] ?? 'DAILY', // Recurrence frequency
            'recurringCount' => $data['recurringCount'] ?? 12, // Number of recurrences
            'mobileNumber' => $data['mobileNumber'] ?? '9629090020', // User's mobile number
            
        ];


        // Build the Base64 payload and headers
        $response = self::sendSubscriptionRequest($subscriptionData);
 
	
     
      	if ($response['status'] === 'success') {
            // Request user authorization
            $authorizationResponse = self::requestUserAuthorization($response['data']['subscriptionId'], round($data['pay_amount'] * 100), $data['callback_url'], $vpa, $is_qr);
  
            if ($authorizationResponse['status'] === 'success') {
               $datas = [
                      'payment_id' => $response['data']['subscriptionId'],
                      'payment_method' => 'PhonePe',           
                      'status' => 1,
                      'meta' => json_encode($response),
                      'm_txd' => $txd,
                      'payment_status' => 1
                  ];

              // Perform the update
              $affectedRows = DB::table('session_payments')
                  ->where('transaction_id', $txd)
                  ->update($datas);
              $userSubscription = UserGatewaySubscribers::where('user_id', Auth::id())
                                ->where('txd', $txd)
                                ->first();
              $userSubscription->update([
                                  'subscription_id' => $response['data']['subscriptionId'],
                                  'subscribe_state' => $response['data']['state']                                  
                              	]);
                return self::success([
                    'subscription_id' => $response['data']['subscriptionId'],
                    'redirect_url' => $authorizationResponse['redirect_url'], // URL for user to authorize
                ]);
            } else {
                return self::error('Failed to authorize subscription: ' . $authorizationResponse['message']);
            }
        } else {
            return self::error('Failed to create subscription: ' . $response['message']);
        }
    } catch (\Exception $e) {
        Log::error('PhonePe subscription creation failed: ' . $e->getMessage());
        return self::error('Subscription request failed: ' . $e->getMessage());
    }
}
  
  protected static function sendSubscriptionRequest(array $subscriptionData)
{
    try {
        $apiKey = self::$salt; // Replace with your salt key
        $saltIndex = 1; // Replace with your salt index
        $baseUrl = 'https://mercury-t2.phonepe.com';// Production or sandbox URL

        // Encode payload to Base64
        $base64Payload = base64_encode(json_encode($subscriptionData));

        // Generate the X-VERIFY header
        $payloadHash = $base64Payload . '/v3/recurring/subscription/create' . $apiKey;

        $checksum = hash('sha256', $payloadHash);
        $xVerify = $checksum . '###' . $saltIndex;

        // Prepare the request
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $baseUrl . '/v3/recurring/subscription/create',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => json_encode(['request' => $base64Payload]),
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'X-VERIFY: ' . $xVerify,
                'accept: application/json',
            ],
        ]);

        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            throw new \Exception('cURL Error: ' . $err);
        }

        // Decode the response and handle errors
        $responseData = json_decode($response, true);
      
     	

        if ($httpCode === 200 && isset($responseData['success']) && $responseData['success'] === true) {
            return [
                'status' => 'success',
                'data' => $responseData['data'] ?? [],
            ];
        } else {
            return [
                'status' => 'error',
                'message' => $responseData['message'] ?? 'Unknown error',
            ];
        }
    } catch (\Exception $e) {
        Log::error('PhonePe API request failed: ' . $e->getMessage());
        return [
            'status' => 'error',
            'message' => $e->getMessage(),
        ];
    }
}
  
  
  	protected static function requestUserAuthorization(string $subscriptionId, int $amount, string $durl, string $vpa, bool $qr)
	{   
   
      try {
          $apiKey = self::$salt; // Salt key
          $saltIndex = 1; // Salt index
          $baseUrl = 'https://mercury-t2.phonepe.com'; // Production or sandbox URL

          // Prepare authorization payload
          $authPayload = [

              'merchantId' => self::$merchant_id,
              'merchantUserId' => self::$merchant_id,
              'subscriptionId' => $subscriptionId, // Subscription ID
              'authRequestId' => 'AUTH' . date('YmdHis') . rand(1000, 9999), // Unique authorization request ID
              'amount' => $amount, // Amount in paise
              "paymentInstrument" => [
                "type" => $qr ? "UPI_QR" : "UPI_COLLECT",
                $qr? '':"vpa" => $vpa
                

              ]
          ];
        	if (!$qr) {
                $authPayload["paymentInstrument"]["vpa"] = $vpa;
            }

          // Encode payload to Base64
          $base64Payload = base64_encode(json_encode($authPayload));

          // Generate the X-VERIFY header
          $payloadHash = $base64Payload . '/v3/recurring/auth/init' . $apiKey;
          $checksum = hash('sha256', $payloadHash);
          $xVerify = $checksum . '###' . $saltIndex;

          // Prepare the cURL request
          $curl = curl_init();
          curl_setopt_array($curl, [
              CURLOPT_URL => $baseUrl . '/v3/recurring/auth/init',
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_POST => true,
              CURLOPT_POSTFIELDS => json_encode(['request' => $base64Payload]),
              CURLOPT_HTTPHEADER => [
                  'Content-Type: application/json',
                  'X-VERIFY: ' . $xVerify,
                  'accept: application/json',
                  'X-CALLBACK-URL:'.$durl
              ],
          ]);

          $response = curl_exec($curl);
          $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
          $err = curl_error($curl);

          curl_close($curl);

          if ($err) {
              throw new \Exception('cURL Error: ' . $err);
          }

          // Decode the response
          $responseData = json_decode($response, true);

            if ($httpCode === 200 && isset($responseData['success']) && $responseData['success'] === true) {
                return [
                    'status' => 'success',
                    'redirect_url' => $responseData['data']['redirectUrl'] ?? null,
                ];
            } else {
                return [
                    'status' => 'error',
                    'message' => $responseData['message'] ?? 'Unknown error',
                ];
            }
          } catch (\Exception $e) {
              //Log::error('Authorization request failed: ' . $e->getMessage());
              return [
                  'status' => 'error',
                  'message' => $e->getMessage(),
              ];
          }
	}
  
  	public static function validateVPA(string $vpa)
    {
        if (!self::$phonePeClient) {
            self::init(); 
        }
        try {
            $apiKey = self::$salt; 
            $saltIndex = 1; 
            $baseUrl = 'https://mercury-t2.phonepe.com';

            // Construct API path (without domain)
            $apiPath = "/v3/vpa/" . self::$merchant_id . "/" . $vpa . "/validate";

            // Generate X-VERIFY hash
            $checksum = hash('sha256', $apiPath . $apiKey);
            $xVerify = $checksum . '###' . $saltIndex;

            // Prepare the cURL request
            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $baseUrl . $apiPath,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'X-VERIFY: ' . $xVerify,
                    'accept: application/json'
                ],
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                throw new \Exception('cURL Error: ' . $err);
            }

            // Decode the response
            $responseData = json_decode($response, true);

            if ($httpCode === 200 && isset($responseData['success']) && $responseData['success'] === true) {
              
                return $responseData;
              
            } else {
                return $responseData;
            }
        } catch (\Exception $e) {
            return [
                'status' => 'error',
                'message' => $e->getMessage(),
            ];
        }
    }
  
  	public static function initDebit($subscriptionId, $amount, $callurl, $txd)
	{
      	if (!self::$phonePeClient) {
            self::init(); 
        }
       	try {
          $apiKey = self::$salt;          
          $baseUrl = 'https://mercury-t2.phonepe.com';         
          $authPayload_1 = [
              'merchantId' => self::$merchant_id,
              'merchantUserId' => self::$merchant_id,
              'subscriptionId' => $subscriptionId, 
              'transactionId' => 'txn'.date('YmdHis').rand(1000, 9999),
              'autoDebit' => true,
              'amount' => 100                
          ];           
          $base64Payload = base64_encode(json_encode($authPayload_1, JSON_UNESCAPED_SLASHES));           
          $payloadHash_1 = $base64Payload.'/v3/recurring/debit/init'.$apiKey;
         	 log:info('payload='. json_encode($authPayload_1, JSON_UNESCAPED_SLASHES));
          $checksum_a = hash('sha256', $payloadHash_1);
         	//  log:info('check='.$checksum_a);
          $xVerifyy = $checksum_a.'###'.self::$saltIndex;  
         	//	log:info('xverify='.$xVerifyy);      
          $curl = curl_init();
          curl_setopt_array($curl, [
              CURLOPT_URL => $baseUrl . '/v3/recurring/debit/init',
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_POST => true,
              CURLOPT_POSTFIELDS => json_encode(['request' => $base64Payload]),
              CURLOPT_HTTPHEADER => [
                  'Content-Type: application/json',
                  'X-VERIFY: '.$xVerifyy,               
                  'X-CALLBACK-URL: '.$callurl,
                  'Accept: application/json'
              ],
          ]);
          $response = curl_exec($curl);
          $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
          $err = curl_error($curl);
          curl_close($curl);
          if ($err) {
              throw new \Exception('cURL Error: ' . $err);
          }
          // Decode the response
          $responseData = json_decode($response, true);
          // Log::error('Res', [ $responseData ]);
            if ($httpCode === 200 && isset($responseData['success']) && $responseData['success'] === true) {
                UserGatewaySubscribers::updateOrCreate(
                    [             
                        'txd' => $txd
                    ],
                    [        

                        'notifiaction_id' => $responseData['data']['notificationId'] ?? null,                       
                        'notification_state' => $responseData['data']['state'] ?? 'FAILED',
                       
                    ]
                );
                return $responseData;
            } else {
                return [
                    'status' => 'error',
                    'message' => $responseData['message'] ?? 'Unknown error',
                ];
            }
          } catch (\Exception $e) {
              //Log::error('Authorization request failed: ' . $e->getMessage());
              return [
                  'status' => 'error',
                  'message' => $e->getMessage(),
              ];
         }
  	}
  
  	public static function debitExecute($subscriptionId, $amount, $callurl, $txd)
	{
      	if (!self::$phonePeClient) {
            self::init(); 
        }
       	try {
          $apiKey = self::$salt;          
          $baseUrl = 'https://mercury-t2.phonepe.com';         
          $authPayload_1 = [
              'merchantId' => self::$merchant_id,
              'merchantUserId' => self::$merchant_id,
              'subscriptionId' => $subscriptionId, 
              'notificationId' => $subscriptionId,
              'transactionId' => 'TX' . strtoupper(bin2hex(random_bytes(8))), 
              'amount' =>100
          ];  
          //log:info(json_encode($authPayload_1, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE));
          $base64Payload = base64_encode(json_encode($authPayload_1));            
          $payloadHash_1 = $base64Payload.'/v3/recurring/debit/execute'.$apiKey;
          $checksum_a = hash('sha256', $payloadHash_1);
          $xVerifyy = $checksum_a.'###'.self::$saltIndex;  
          $curl = curl_init();
          curl_setopt_array($curl, [
              CURLOPT_URL => $baseUrl . '/v3/recurring/debit/execute',
              CURLOPT_RETURNTRANSFER => true,
              CURLOPT_POST => true,
              CURLOPT_POSTFIELDS => json_encode(['request' => $base64Payload]),
              CURLOPT_HTTPHEADER => [
                  'Content-Type: application/json',
                  'X-VERIFY: '.$xVerifyy              
                  
              ],
          ]);
          $response = curl_exec($curl);
          $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
          $err = curl_error($curl);
          curl_close($curl);
          if ($err) {
              throw new \Exception('cURL Error: ' . $err);
          }
          // Decode the response
          $responseData = json_decode($response, true);
          // Log::error('Res', [ $responseData ]);
            if ($httpCode === 200 && isset($responseData['success']) && $responseData['success'] === true) {
               // UserGatewaySubscribers::updateOrCreate(
                 //   [             
                   //     'txd' => $txd
                  //  ],
                 //   [
                 //     'notifiaction_id' => $responseData['data']['notificationId'] ?? null,                       
                 //     'notification_state' => $responseData['data']['state'] ?? 'FAILED',                       
                  //  ]
              //  );
                return $responseData;
            } else {
                return [
                    'status' => 'error',
                    'message' => $responseData['message'] ?? 'Unknown error',
                ];
            }
          } catch (\Exception $e) {
              //Log::error('Authorization request failed: ' . $e->getMessage());
              return [
                  'status' => 'error',
                  'message' => $e->getMessage(),
              ];
         }
  	}
}
