<template>
    <div class="p-2 bg-gray-100">
        <!-- Billing Information -->
        <div class="mb-2">
            <div class="flex justify-between items-center px-1">
                <h1 class="text-lg font-bold mb-3">Subscription Info</h1>
                <button @click="navigateToSubscription" class="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded">{{
                    !isExpired ? 'Buy' : timeUntil === 'Active' ?
                        'Upgrade' : 'Renew' }}</button>
            </div>

            <!-- Payment Information Section -->
            <div class="bg-white rounded-lg shadow mt-6 p-6">
                <h2 class="text-sm sm:text-lg font-semibold mb-4">Plan info</h2>
                <div v-if="currentCompanyList && currentCompanyList.plans"
                    class="flex justify-between items-center mb-4 border-b-2 border-gray-200 py-2">
                    <div>
                        <p class="text-sm text-gray-600">Plan Name</p>
                        <p v-if="currentCompanyList.plans" class="text-sm sm:text-lg font-bold">
                            {{ currentCompanyList &&
                                currentCompanyList.plans.title ? currentCompanyList.plans.title : '' }}
                        </p>
                    </div>
                    <div>
                        <p class="text-sm text-gray-600">Payment plan</p>
                        <p class="text-sm sm:text-lg font-bold">{{ currentCompanyList && currentCompanyList.currency ===
                            'INR' ? '\u20b9' : currentCompanyList.currency }} {{
                                currentCompanyList.plans && currentCompanyList.plans.price }}
                            / {{ currentCompanyList.plans.days == 365 ? '1 Year' : currentCompanyList.plans.days +
                                ' Days' }}</p>
                    </div>
                    <div class="flex justify-center items-center text-sm sm:text-lg">
                        <button v-if="isExpired" class="bg-green-700 text-white p-1 px-2 rounded">Active</button>
                        <button v-else class="bg-red-700 text-white p-1 px-2 rounded">Expired</button>
                        <span class="px:1 sm:px-3 text-xs sm:text-sm" v-if="timeUntil !== 'Active'"
                            :style="{ color: text_color }">{{ timeUntil !== 'Active' ?
                                timeUntil : ''
                            }}</span>

                    </div>
                </div>
                <!--plan expire-->
                <div>
                    <p class="space-x-2" :class="{
                        'text-green-600': days_count > 15,
                        'text-red-600': days_count <= 15
                    }">
                        Expire:
                        <span>{{ formattedExpiryDate(currentCompanyList.expiry_date) }}</span>
                        <span class="rounded-full px-2 py-1 text-xs text-white" :class="{
                            'bg-green-500': days_count > 15,
                            'bg-red-500': days_count <= 15
                        }">
                            {{ days_count }} {{ days_count > 1 ? 'Days' : 'Day' }}
                        </span>
                    </p>
                </div>
            </div>
        </div>

        <!-- Invoices Section -->
        <div v-if="table_data && table_data.length > 0" class="bg-white rounded-lg shadow p-6 mt-6">
            <h2 class="text-sm sm:text-lg font-semibold mb-4">Invoices</h2>
            <div class="grid grid-cols-4 gap-2 sm:gap-4 py-3 border-b-2 border-gray-200"
                v-for="(invoice, index) in table_data" :key="index">
                <div>
                    <p>{{ invoice.plan_name }}</p>
                </div>
                <div class="flex justify-center items-center">
                    <p>{{ this.formattedDate(invoice.created_at) }}</p>
                </div>
                <div class="flex justify-center items-center">
                    <p>{{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                        currentCompanyList.currency }}
                        {{ invoice.amount }}</p>
                </div>
                <div class="flex justify-center items-center">
                    <button class="bg-blue-600 hover:bg-blue-500 text-white px-2 py-1 rounded"
                        @click="downloadPDF(invoice)"><font-awesome-icon icon="fa-solid fa-download" /> <span
                            v-if="!isMobile">Download</span></button>
                </div>
            </div>
        </div>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    props: {

        isMobile: Boolean,
        isAndroid: Boolean,
        table_data: {
            type: Array,
            required: true
        },
        currentLocalDataList: Object,
        currentCompanyList: Object,
    },
    data() {
        return {
            timeUntil: null,
            text_color: 'green',
            open_loader: false,
            pdfUrl: '',
            days_count: 0
        };
    },
    computed: {
        ...mapGetters('subscription', ['currentSubscriptionList']),
        isExpired() {
            if (this.currentCompanyList && this.currentCompanyList.expiry_date) {
                const expiryDate = new Date(this.currentCompanyList.expiry_date + "T23:59:59Z"); // End of the day on expiry date
                const now = new Date();
                const diffInMillis = expiryDate - now;

                const years = Math.floor(diffInMillis / (1000 * 60 * 60 * 24 * 365));
                const months = Math.floor((diffInMillis % (1000 * 60 * 60 * 24 * 365)) / (1000 * 60 * 60 * 24 * 30));
                // const weeks = Math.floor((diffInMillis % (1000 * 60 * 60 * 24 * 30)) / (1000 * 60 * 60 * 24 * 7));
                // Calculate difference in days, hours, and minutes
                const days = Math.ceil(diffInMillis / (1000 * 60 * 60 * 24)); // Round up to ensure we show full days
                const hours = Math.floor((diffInMillis % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((diffInMillis % (1000 * 60 * 60)) / (1000 * 60));


                if (years > 0) {
                    this.timeUntil = 'Active';
                    this.text_color = 'green';
                } else if (months > 0) {
                    this.timeUntil = 'Active';
                    this.text_color = 'green';
                }
                else if (days > 0) {
                    if (days <= 15) {
                        if (days === 1) {
                            this.timeUntil = `Today left`;
                            this.text_color = 'red';
                        } else {
                            this.timeUntil = `${days} ${this.isMobile ? 'D' : `day${days > 1 ? 's' : ''}`} left`;
                            this.text_color = 'red';
                        }
                    } else {
                        this.timeUntil = 'Active';
                        this.text_color = 'green';
                    }
                }

                const today = new Date(); // Get the current date
                // const expiryDate = new Date(this.currentCompanyList.expiry_date); // Convert expiry_date to a Date object
                this.days_count = days;
                // Check if the expiry date is before today's date
                return expiryDate > today;
            }
        },
    },
    created() {
        if (this.currentSubscriptionList && this.currentSubscriptionList.length > 0) {
            this.fetchSubscriptionList();
        } else {
            this.fetchSubscriptionList();
        }
        // console.log(this.table_data, 'What happening  table data');
        /// Set base URL based on hostname
        const hostname = window.location.hostname;
        let baseURL;

        if (hostname === 'app.track-new.com') {
            // Production URL
            baseURL = 'https://api.track-new.com/api';
        } else if (hostname === 'devapp.track-new.com') {
            // Development URL
            baseURL = 'https://devapi.track-new.com/api';
        } else if (hostname === ' ************' || hostname === ' ************:8000') {
            // Local development URL (adjust as needed)
            baseURL = 'https://devapi.track-new.com/api';
            // baseURL = 'https://api.track-new.com/api';

        } else {
            // Default fallback URL
            baseURL = 'https://devapi.track-new.com/api';
        }
        this.pdfUrl = baseURL;
    },
    methods: {
        ...mapActions('subscription', ['fetchSubscriptionList']),
        formattedDate(date_data) {
            // Convert the string to a Date object (in UTC)
            const date = new Date(date_data);

            // Get the UTC day, month, and year
            const day = date.getUTCDate(); // Get the UTC day (no local time conversion)
            const month = (date.getUTCMonth() + 1).toString().padStart(2, '0'); // Get the UTC month (1-based, ensure 2 digits)
            const year = date.getUTCFullYear(); // Get the UTC year

            // Return the formatted date as "DD-MM-YYYY"
            return `${day.toString().padStart(2, '0')}-${month}-${year}`;
        },
        navigateToSubscription() {
            this.$router.push('/subscription');
        },
        async downloadPDF(invoice) {
            // console.log(invoice, 'What happening in the data...!');
            this.open_loader = true;
            try {
                if (invoice) {
                    let link_data = `${this.pdfUrl}/download-order-invoice/${invoice.id}}`;

                    // Create an anchor element
                    let anchor = document.createElement('a');
                    anchor.href = link_data;
                    anchor.setAttribute('download', 'invoices'); // Set the download attribute to trigger a download
                    anchor.style.display = 'none';

                    // Append the anchor to the document body and click it programmatically
                    document.body.appendChild(anchor);
                    anchor.click();

                    // Cleanup: remove the anchor from the document body
                    document.body.removeChild(anchor);
                    this.open_loader = false;

                }
            } catch (error) {
                this.open_loader = false;
                console.error('Error downloading PDF:', error);
            }
        },
        // Format expiry date to a readable format
        formattedExpiryDate() {
            const expiryDate = new Date(this.currentCompanyList.expiry_date);
            return expiryDate.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
            });
        },
    },
    watch: {
        currentSubscriptionList: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'what happning in the data.....');
            }
        },
        currentCompanyList: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'What happnin in the data');

            }
        },
    }
};
</script>

<style scoped>
/* You can add any custom styles here */
</style>