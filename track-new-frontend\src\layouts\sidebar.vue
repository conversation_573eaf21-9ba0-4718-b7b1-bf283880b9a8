<template>
  <!--md:w-64 md:static fixed-->
  <aside
    :class="{ 'sideBarWebsite lg:w-52 2xl:w-64 lg:p-5 h-full': !isMobile, 'p-3 sm:p-6 h-screen overflow-hidden sideBar my-3': isMobile }">
    <!-- Website Selector -->
    <div class="space-y-2" :class="{ 'mt-14': isMobile }">
      <!-- <div>
        <label class="block text-gray-500 text-sm">Website name</label>
        <select class="mt-1 block w-full p-2 border border-gray-300 rounded-md focus:ring focus:ring-blue-300">
          <option>mediumorchid-skunk-35</option>
        </select>
      </div> -->
    </div>
    <div v-if="!isMobile" class="space-y-2">
      <p class="flex items-center p-2 transition text-[#0d8bf1]"><font-awesome-icon icon="fa-solid fa-paper-plane"
          class="mr-2" /> Actions</p>
    </div>
    <!--preview web page-->
    <!-- <div v-if="$route.path === '/websites/settings' && previewUrl && previewUrl !== ''">
      <div
        class="flex items-center p-2  rounded-md text-white hover:bg-gray-100 hover:text-blue-600 transition text-sm">
        <span class="mr-2 text-xl"><font-awesome-icon icon="fa-solid fa-eye" /></span>
        <a :href="previewUrl" target="_blank">Preview</a>
      </div>
    </div> -->
    <!-- Navigation Menu -->
    <nav>
      <ul class="space-y-2">
        <li v-for="item in menuItems" :key="item.id" :class="{ 'hidden': item.id === 2 }">
          <router-link :to="item.route" class="flex items-center p-2 transition"
            :class="{ 'hover:bg-gray-100 hover:text-gray-600': isMobile, 'text-white  rounded-md': !($route.path === item.route) && isMobile, ' bg-white text-gray-600  rounded-md': ($route.path === item.route) && isMobile, 'text-[#0d8bf1]': !($route.path === item.route) && !isMobile, 'text-white bg-[#0d8bf1]': ($route.path === item.route) && !isMobile, 'hover:bg-[#0d8bf1] hover:text-white': !isMobile, }">
            <font-awesome-icon :icon="item.icon" class="mr-2" />
            <span class="text-sm">{{ item.label }}</span>
          </router-link>
        </li>
      </ul>
    </nav>
    <!--verticlal bar v-if="shouldDisplayVerticalBar" -->
    <div class="relative">
      <VerticalBar @item-selected="handleItemSelected" :isMobile="isMobile" :selectedItem="selectedItem"></VerticalBar>
    </div>
  </aside>
</template>


<script>
import VerticalBar from '@/components/website-builder/VerticalBar.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: "Sidebar",
  components: {
    VerticalBar
  },
  data() {
    return {
      menuItems: [
        {
          id: 1,
          label: "Dashboard",
          icon: "fa-solid fa-gauge",
          route: "/websites/dashboard",
        },
        {
          id: 2,
          label: "Settings",
          icon: "fa-solid fa-globe",
          route: "/websites/settings",
        },
        {
          id: 3,
          label: "Website Template",
          icon: "fa-solid fa-layer-group",
          route: "/websites/template",
        },
      ],
      selectedItem: 2,
      isMobile: false,
    };
  },
  computed: {
    ...mapGetters('websiteBuilder', ['selectedId', 'previewUrl']),
    // Determine if VerticalBar should be displayed
    shouldDisplayVerticalBar() {
      return this.$route.path === '/websites/settings'; // Show VerticalBar only on '/websites/settings' route
    },
  },
  methods: {
    ...mapActions('websiteBuilder', ['updateSelectdItem']),

    // Handle selection in VerticalBar
    handleItemSelected(item) {
      if (item) {
        this.selectedItem = item;
      }
    },

    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
  },
  mounted() {
    this.updateIsMobile();
    this.selectedItem = this.selectedId ? this.selectedId : 2;

    window.addEventListener('resize', this.updateIsMobile);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateIsMobile);
  },
  watch: {
    selectedId: {
      deep: true,
      handler(newValue) {
        if (newValue !== undefined) {
          this.selectedItem = this.selectedId;
        }
      }
    },
  },
};
</script>
<style></style>
