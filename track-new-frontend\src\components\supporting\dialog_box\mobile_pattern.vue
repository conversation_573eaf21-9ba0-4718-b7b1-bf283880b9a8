<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p class="text-white font-bold text-center flex justify-end ml-12">Mobile Pattern</p>
                <p class="close" @click="closeModal">&times;</p>
            </div>
            <div>
                <mobilePattern :success="success" :freeze="freeze" @finish="savePatternData" :pattern="pattern">
                </mobilePattern>
            </div>
            <div v-if="!view_page" class="flex justify-center mt-2 items-center py-2">
                <button class="btn-reset rounded py-2 px-5 bg-red-500 text-white" @click="resetPattern">Reset</button>
                <button class="btn-save rounded py-2 px-7 bg-green-500 text-white ml-4"
                    @click="savePattern">Save</button>
            </div>

        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import mobilePattern from './mobilePattern.vue';
export default {
    props: {
        showModal: Boolean,
        pattern_data: Object,
        view_page: Boolean
    },
    components: {
        mobilePattern
    },
    data() {
        return {
            success: null,
            freeze: false,
            isOpen: false,
            pattern: [],
            isReset: false,
            //--toaster---
            show: false,
            message: '',
            type_toaster: 'success',
        };
    },

    methods: {
        closeModal(data) {
            this.isOpen = false;
            if (data && data.length > 0) {
                setTimeout(() => {
                    this.$emit('close-Modal', data, this.isReset);
                    this.isReset = false;
                }, 300);
            } else {
                setTimeout(() => {
                    this.$emit('close-Modal', this.isReset);
                    this.isReset = false;
                }, 300);
            }
        },
        savePatternData(data) {
            if (data && Array.isArray(data) && data.length > 0) {
                this.pattern = data;
                this.isReset = false;
            }
        },
        resetPattern() {
            this.pattern = [];
            this.isReset = true;
        },
        savePattern() {
            if (this.pattern.length > 0) {
                this.closeModal(this.pattern);
            } else {
                // this.type_toaster = 'warning';
                // this.message = 'Pattern was removed, Please save you latest changes...!';
                // this.show = true;
                this.closeModal();
            }
        },
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        pattern_data: {
            deep: true,
            handler(newValue) {
                this.pattern = newValue;
            }
        }
    },
};
</script>

<style scoped>
.grid {
    width: 300px;
    height: 300px;
}

/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>
