<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateAmcAPIRequest;
use App\Http\Requests\API\UpdateAmcAPIRequest;
use App\Models\Amc;
use App\Repositories\AmcRepository;
use App\Repositories\AmcDatesRepository;
use App\Repositories\AmcUsersRepository;
use App\Repositories\AmcProductRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Http\Resources\api\AmcResource;
use App\Repositories\ReminderRepository;
use Response;
use Auth;


/**
 * Class AmcController
 * @package App\Http\Controllers\API
 */

class AmcAPIController extends AppBaseController
{
    /** @var  AmcRepository */
    private $amcRepository;
    private $amcUsersRepository;
    private $amcDatesRepository;
    private $amcProductRepository;
    private $reminderRepository;

    public function __construct(AmcRepository $amcRepo, AmcUsersRepository $amcUsersRepo, AmcDatesRepository $amcDatesRepo, ReminderRepository $reminderRepo, AmcProductRepository  $amcProductRepo)
    {
        $this->amcRepository = $amcRepo;
        $this->amcUsersRepository = $amcUsersRepo;
        $this->amcDatesRepository = $amcDatesRepo;
        $this->reminderRepository = $reminderRepo;
        $this->amcProductRepository = $amcProductRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/amcs",
     *      summary="getAmcList",
     *      tags={"Amc"},
     *      description="Get all Amcs",
     *      @OA\Parameter(
     *          name="company_id",
     *          description="ID of the company whose services are to be fetched",
     *          @OA\Schema(
     *              type="string"
     *          ),
     *          required=true,
     *          in="query"
     *      ),
     *      @OA\Parameter(
     *          name="page",
     *          description="Page number for pagination",
     *          @OA\Schema(
     *              type="integer"
     *          ),
     *          in="query"
     *      ),
     *      @OA\Parameter(
     *          name="per_page",
     *          description="Number of items per page",
     *          @OA\Schema(
     *              type="integer"
     *          ),
     *          in="query"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Amc")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $user = Auth::user();
        if (!$user) {
            return $this->sendError('Please login to access.', 400);
        }

        $isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();

        if (!$isAdmin) {
            $userId = $user->id;
            if ($userId === null) {
                return $this->sendError('Unauthorized Access.', 400);
            }

            $amcQuery = Amc::where('company_id', $companyId)
                ->whereHas('users', function ($query) use ($userId) {
                    $query->where('users.id', $userId);
                });
        } else {
            $amcQuery = Amc::where('company_id', $companyId);
        }

        if ($perPage === 'all') {
            $perPage = $amcQuery->count();
        }

        $amcs = $amcQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $statusCounts = $amcQuery->selectRaw('amc_status, count(*) as count')->groupBy('amc_status')->pluck('count', 'amc_status')->toArray();

        $response = [
            'success' => true,
            'status_counts' => $statusCounts,
            'data' => AmcResource::collection($amcs), // Get the paginated items
            'pagination' => [
                'total' => $amcs->total(),
                'per_page' => $amcs->perPage(),
                'current_page' => $amcs->currentPage(),
                'last_page' => $amcs->lastPage(),
                'from' => $amcs->firstItem(),
                'to' => $amcs->lastItem(),
            ],
        ];

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/amcs",
     *      summary="createAmc",
     *      tags={"Amc"},
     *      description="Create Amc",
     * @OA\RequestBody(
     *   required=true,
     *   description="Item object that needs to be added",
     *   @OA\MediaType(
     *     mediaType="application/x-www-form-urlencoded",
     *     @OA\Schema(
     *       type="object",
     *       @OA\Property(
     *         property="amc_date",
     *         type="string",
     *         description="AMC date"
     *       ),
     *       @OA\Property(
     *         property="title",
     *         type="string",
     *         description="AMC title"
     *       ),
     *       @OA\Property(
     *         property="amc_details",
     *         type="string",
     *         description="AMC details"
     *       ),
     *       @OA\Property(
     *         property="amc_payment_type",
     *         type="string",
     *         description="AMC payment type"
     *       ),
     *       @OA\Property(
     *         property="amc_status",
     *         type="string",
     *         description="AMC status"
     *       ),
     *       @OA\Property(
     *         property="number_of_interval",
     *         type="integer",
     *         description="Number of intervals"
     *       ),
     *       @OA\Property(
     *         property="number_of_service",
     *         type="integer",
     *         description="Number of services"
     *       ),
     *       @OA\Property(
     *         property="date_description",
     *         type="string",
     *         description="Date description"
     *       ),
     *       @OA\Property(
     *         property="product_lists",
     *         type="string",
     *         description="Product lists"
     *       ),
     *       @OA\Property(
     *         property="customer_id",
     *         type="integer",
     *         description="Customer ID"
     *       ),
     *      @OA\Property(
     *         property="company_id",
     *         type="integer",
     *         description="Company ID"
     *      ),
     *      @OA\Property(
     *         property="user_id",
     *         type="integer",
     *         description="User ID"
     *      )
     *     )
     *   )
     * ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Amc"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateAmcAPIRequest $request)
    {
        $input = $request->all();

        $input['user_id'] = auth()->user()->id;
        $input['updated_by'] = auth()->user()->id;

        $amc = $this->amcRepository->create($input);

        //$amcUsers = $this->amcUsersRepository->create($data);


        if (isset($input['assign_to'])) {

            $assignTo = json_decode($input['assign_to'], true);

            // Check if JSON decoding was successful
            if ($assignTo !== null) {
                foreach ($assignTo as $userData) {
                    $user_id = $userData['user_id'];
                    $data['amc_id'] =  $amc->id;;
                    $data['user_id'] =  $user_id;

                    $this->amcUsersRepository->create($data);
                }
            } else {
                // Handle JSON decoding error
                return $this->sendError('Invalid JSON format in assign_to field', 400);
            }
        }

        if (isset($input['date_description'])) {

            $amcDates = json_decode($request->input('date_description'), true);

            if (is_array($amcDates)) {

                foreach ($amcDates as $amcDate) {

                    $amcDatesData = [
                        'amc_id' => $amc->id,
                        'date' => $amcDate['date'],
                        'note' => $amcDate['note'],
                        'attachment' => $amcDate['attachment'] ?? '',
                        'status' => $amcDate['status'] ?? '',
                        'created_by' => auth()->user()->id,
                        'updated_by' => auth()->user()->id
                    ];

                    $this->amcDatesRepository->create($amcDatesData);
                }
                foreach ($amcDates as $reminderDate) {

                    $reminderDateData = [
                        'user_id' => $request->input('user_id'),
                        'reminder_date' => $reminderDate['date'],
                        'reminder_type' => 'App\Models\Amc',
                        'reminder_type_id' =>  $amc->id,
                        'reminder_status' => '1',
                    ];

                    $this->reminderRepository->create($reminderDateData);
                }
            }
        }

        // Insert AMC Products
        if (isset($input['product_lists'])) {

            $products = json_decode($input['product_lists'], true);
            if (is_array($products)) {
                foreach ($products as $product) {
                    $productData = [
                        'amc_id' => $amc->id,
                        'product_name' => $product['product'] ?? '',
                        'description' => $product['description'] ?? ''
                    ];
                    $this->amcProductRepository->create($productData);
                }
            } else {
                return $this->sendError('Invalid JSON format in products field', 400);
            }
            
        }
        return $this->sendResponse(new AmcResource($amc), 'Amc saved successfully');
    }


    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/amcs/{id}",
     *      summary="getAmcItem",
     *      tags={"Amc"},
     *      description="Get Amc",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Amc",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Amc"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {

        $user = Auth::user();
        $company_id = $user->company_id;

        $amc = Amc::where('id', $id)->where('company_id', $company_id)->first();
        /** @var Amc $amc */
        //$amc = $this->amcRepository->find($id);

        if (empty($amc)) {
            return $this->sendError('Amc not found');
        }

        return $this->sendResponse(new AmcResource($amc), 'Amc retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/amcs/{id}",
     *      summary="updateAmc",
     *      tags={"Amc"},
     *      description="Update Amc",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Amc",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     * @OA\RequestBody(
     *   required=true,
     *   description="Item object that needs to be added",
     *   @OA\MediaType(
     *     mediaType="application/x-www-form-urlencoded",
     *     @OA\Schema(
     *       type="object",
     *       @OA\Property(
     *         property="amc_date",
     *         type="string",
     *         description="AMC date"
     *       ),
     *       @OA\Property(
     *         property="title",
     *         type="string",
     *         description="AMC title"
     *       ),
     *       @OA\Property(
     *         property="amc_details",
     *         type="string",
     *         description="AMC details"
     *       ),
     *       @OA\Property(
     *         property="amc_payment_type",
     *         type="string",
     *         description="AMC payment type"
     *       ),
     *       @OA\Property(
     *         property="amc_status",
     *         type="string",
     *         description="AMC status"
     *       ),
     *       @OA\Property(
     *         property="number_of_interval",
     *         type="integer",
     *         description="Number of intervals"
     *       ),
     *       @OA\Property(
     *         property="number_of_service",
     *         type="integer",
     *         description="Number of services"
     *       ),
     *       @OA\Property(
     *         property="date_description",
     *         type="string",
     *         description="Date description"
     *       ),
     *       @OA\Property(
     *         property="product_list",
     *         type="string",
     *         description="Product list"
     *       ),
     *       @OA\Property(
     *         property="customer_id",
     *         type="integer",
     *         description="Customer ID"
     *       ),
     *      @OA\Property(
     *         property="user_id",
     *         type="integer",
     *         description="User ID"
     *       )
     *     )
     *   )
     * ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Amc"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */

    public function update($id, UpdateAmcAPIRequest $request)
    {
        $input = $request->all();

        /** @var Amc $amc */
        $amc = $this->amcRepository->find($id);

        if (empty($amc)) {
            return $this->sendError('Amc not found');
        }

        $input['updated_by'] = auth()->user()->id;

        $amc = $this->amcRepository->update($input, $id);


        if (isset($input['assign_to'])) {

            $assignTo = json_decode($input['assign_to'], true);

            //    // Debugging: Log user ID before creating the record
            // \Log::info("Skipping creation.". $assignTo);
            // Check if JSON decoding was successful
            if ($assignTo !== null) {
                $this->amcUsersRepository->deleteByAmcId($id);
                foreach ($assignTo as $userData) {
                    $user_id = $userData['user_id'];

                    $dataUser['amc_id'] =  $id;
                    $dataUser['user_id'] =  $user_id;

                    try {

                        // Check if the record already exists
                        // $existingRecord = $this->amcUsersRepository->findByAmcIdAndUserId($id, $user_id);

                        // // If the record exists, skip creating it
                        // if ($existingRecord) {

                        //     //$this->amcUsersRepository->deleteByAmcId($id, $user_id);
                        //     //\Log::info("Record for user ID $user_id already exists. Skipping creation.");
                        // } else {
                        // If the record doesn't exist, create it
                        $this->amcUsersRepository->create($dataUser);
                        //  \Log::info("Record for user ID $user_id created successfully.");
                        // }
                    } catch (\Exception $e) {
                        // \Log::error("Error processing user ID $user_id: " . $e->getMessage());
                    }
                }
            } else {
                // Handle JSON decoding error
                return $this->sendError('Invalid JSON format in assign_to field', 400);
            }
        }

        if (isset($input['date_description'])) {
            $amcDates = json_decode($input['date_description'], true);

            // Update or create follow-up entries
            $existingFollowUp = [];
            if (is_array($amcDates)) {
                foreach ($amcDates as $followUp) {

                    $amcDatesData = [
                        'amc_id' => $id,
                        'date' => $followUp['date'] ?? '',
                        'note' => $followUp['note'] ?? '',
                        'status' => $followUp['status'] ?? '',
                        'attachment' => $followUp['attachment'] ?? '',
                        'updated_by' => auth()->user()->id,
                        // Optionally, you can also update 'created_by' if needed
                    ];

                    // Check if this follow-up entry already exists and update it, otherwise create a new one
                    if (isset($followUp['id'])) {
                        // Update existing follow-up entry
                        $existingFollowUp = $this->amcDatesRepository->update($amcDatesData, $followUp['id']);
                    } else {
                        // Create new follow-up entry
                        $newFollowUp = $this->amcDatesRepository->create($amcDatesData);
                    }
                }

                $existingReminder = [];

                foreach ($amcDates as $reminderDate) {
                    $reminderData = [
                        'user_id' => $request->input('user_id'),
                        'reminder_date' => $reminderDate['date'],
                        'reminder_type' => '1',
                        'reminder_type_id' =>  $id,
                        'reminder_status' => '1',
                    ];

                    // Create new reminder
                    $newReminder = $this->reminderRepository->create($reminderData);
                    $existingReminder[] = $newReminder->id;

                    // **Delete reminder** (Reminder not include in the requests)
                    $this->reminderRepository->deleteWhereNotIn('id', $existingReminder, $id);
                }
            }
        }

        // **Update AMC Products**
        if (isset($input['product_lists'])) {
            $products = json_decode($input['product_lists'], true);
            if (is_array($products)) {
                $existingProductIds = [];

                foreach ($products as $product) {
                    $productData = [
                        'amc_id' => $id,
                        'product_name' => $product['product'] ?? '',
                        'description' => $product['description'] ?? '',
                    ];

                    if (isset($product['id'])) {
                        // Update existing product
                        $this->amcProductRepository->update($productData, $product['id']);
                        $existingProductIds[] = $product['id'];
                    } else {
                        // Create new product
                        $newProduct = $this->amcProductRepository->create($productData);
                        $existingProductIds[] = $newProduct->id;
                    }
                }

                // **Delete removed products** (Products not included in the request)
                $this->amcProductRepository->deleteWhereNotIn('id', $existingProductIds, $id);
            } else {
                return $this->sendError('Invalid JSON format in product_lists field', 400);
            }
        }

        return $this->sendResponse(new AmcResource($amc), 'Amc updated successfully');
    }


    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/amcs/{id}",
     *      summary="deleteAmc",
     *      tags={"Amc"},
     *      description="Delete Amc",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Amc",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Amc $amc */
        $amc = $this->amcRepository->find($id);

        if (empty($amc)) {
            return $this->sendError('Amc not found');
        }

        $amc->delete();

        return $this->sendSuccess('Amc deleted successfully');
    }

    public function sevenDaysReport(Request $request)
    {
        return response()->json($this->getDashboardData($request));
    }

    public function getDashboardData(Request $request)
    {
        $user = Auth::user();
        $filter = $request->day;
        $companyId = $request->company_id; 
        $now = now();
        
        if (!$this->isValidFilter($filter)) {
            return collect();
        }

        $query = Amc::query()
            ->when(!$this->isAdmin($user), fn($q) => $q->forUser($user->id))
            ->when($companyId, fn($q) => $q->where('company_id', $companyId))
            ->with([
                'amcDates' => fn($q) => $this->applyDateFilterToRelation($q, $filter, $now),
                'customerData:id,first_name,last_name,business_name',
                'company:id,company_name'
            ])
            ->hasDateInRange($filter, $now);

        return $this->formatDashboardData($query->get());
    }

    protected function isAdmin($user): bool
    {
        return $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();
    }

    protected function isValidFilter($filter): bool
    {
        return in_array($filter, [
            'today', 'tomorrow', 'thisWeek', 'nextWeek', 
            'thisMonth', 'nextMonth', 'thisYear', 'nextYear'
        ]);
    }

    protected function applyDateFilterToRelation($query, $filter, $now)
    {
        $query->orderBy('date');
        
        match ($filter) {
            'today' => $query->whereDate('date', $now->toDateString()),
            'tomorrow' => $query->whereDate('date', $now->copy()->addDay()->toDateString()),
            'thisWeek' => $query->whereBetween('date', [
                $now->copy()->startOfWeek()->toDateString(),
                $now->copy()->endOfWeek()->toDateString()
            ]),
            'nextWeek' => $query->whereBetween('date', [
                $now->copy()->addWeek()->startOfWeek()->toDateString(),
                $now->copy()->endOfWeek()->toDateString()
            ]),
            'thisMonth' => $query->whereBetween('date', [
                $now->copy()->startOfMonth()->toDateString(),
                $now->copy()->endOfMonth()->toDateString()
            ]),
            'nextMonth' => $query->whereBetween('date', [
                $now->copy()->addMonth()->startOfMonth()->toDateString(),
                $now->copy()->endOfMonth()->toDateString()
            ]),
            'thisYear' => $query->whereBetween('date', [
                $now->copy()->startOfYear()->toDateString(),
                $now->copy()->endOfYear()->toDateString()
            ]),
            'nextYear' => $query->whereBetween('date', [
                $now->copy()->addYear()->startOfYear()->toDateString(),
                $now->copy()->endOfYear()->toDateString()
            ]),
            default => null
        };
    }

    protected function formatDashboardData($amcs)
    {
        return $amcs->map(fn($amc) => [
            'id' => $amc->id,
            'title' => $amc->title,
            'status' => $amc->amc_status,
            'payment_type' => $amc->amc_payment_type,
            'dates' => $amc->amcDates->map(fn($date) => [
                'id' => $date->id,
                'date' => $date->date->format('Y-m-d'),
                'attachment' => $date->attachment
            ]),
            'customer' => $amc->customerData?->only(['id', 'first_name']),
            'company' => $amc->company?->only(['id', 'company_name'])
        ]);
    }
}
