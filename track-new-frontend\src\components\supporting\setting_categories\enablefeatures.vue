<template>
    <div class="p-2 sm:p-6 w-full mx-auto mt-2">
        <div class="flex justify-between items-center mb-4 ">
            <h2 class="text-lg sm:text-xl font-bold">Feature Settings</h2>
            <div class="flex items-center">
                <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center cursor-pointer"
                    @click="selectAllfeature" :class="{ 'bg-blue-600': selected_all, 'bg-gray-200': !selected_all }">
                    <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                        :class="{ 'translate-x-6': selected_all, 'translate-x-0': !selected_all }"></div>
                </div>
                <span class="px-1 text-blue-700">select all</span>
            </div>
        </div>
        <form @submit.prevent="submitSettings" class="space-y-4">
            <div v-for="(feature, index) in features" :key="index"
                class="flex items-center justify-between border-b py-1">
                <label :for="index" class="text-sm font-medium">{{ feature.name }}</label>
                <label class="inline-flex items-center cursor-pointer">
                    <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                        @click="toggleSwitch(index)"
                        :class="{ 'bg-blue-600': feature.hasAccess, 'bg-gray-200': !feature.hasAccess }">
                        <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                            :class="{ 'translate-x-6': feature.hasAccess, 'translate-x-0': !feature.hasAccess }"></div>
                    </div>
                </label>
            </div>

            <div class="flex justify-end mt-6">
                <button type="submit"
                    class="px-6 py-2 shadow-inner shadow-blue-100 border border-blue-600 bg-blue-600 text-white rounded-lg hover:bg-blue-600 transition duration-150 ease-in-out">
                    Submit
                </button>
            </div>
        </form>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import axios from 'axios';
import { mapGetters, mapActions } from 'vuex';
export default {
    emits: ['is-sales-save', 'updatesalesData', 'updateBar'],
    props: {
        companyId: String,
        userId: String,
        store_refresh: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            // Define the features with their names and initial enabled state
            features: [],
            settings: {},
            selected_all: false,
            open_loader: false,
            show: false,
            message: '',
            type_toaster: 'warning',
            open_loader: false,
            is_updated: false,
        };
    },
    created() {
        if (this.currentFeatureList && this.currentFeatureList.length > 0) {
            this.features = JSON.parse(JSON.stringify(this.currentFeatureList));
            this.fetchFeatureList();
        } else {
            this.fetchFeatureList();
        }
        this.fetchLocalDataList();
        // Initialize settings with the current state of features
        this.settings = Object.keys(this.features).reduce((acc, key) => {
            acc[key] = this.features[key].enabled;
            return acc;
        }, {});
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('features_list', ['currentFeatureList']),
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('features_list', ['fetchFeatureList', 'updateFeatureList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        submitSettings() {
            this.open_loader = true;
            if (this.features && this.features.length > 0) {
                const result = this.features.filter(item => item.hasAccess).map(item => item.id);
                if (result.length > 0) {
                    axios.post(`/assign_modules/companies/${this.companyId}`, { modules: result })
                        .then(response => {
                            this.open_loader = false;
                            this.message = 'Options are updated successfully...!';
                            this.type_toaster = 'success';
                            this.show = true;
                            this.updateKeyWithTime('features_update');
                            this.fetchFeatureList();
                            // Handle response
                            this.$emit('updateBar');
                            this.$emit('is-sales-save', false);
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                            this.open_loader = false;
                        });
                } else {
                    // console.log(this.currentFeatureList, 'RRRRRRRRRRRRRRRRRRR');
                    // this.fetchFeatureList();
                    this.features = JSON.parse(JSON.stringify(this.currentFeatureList));
                    this.open_loader = false;
                    this.message = 'Please enable at least one option to submit.';
                    this.show = true;
                    this.$emit('updatesalesData');
                }
            }
        },
        toggleSwitch(index) {
            this.is_updated = true;

            this.features[index]['hasAccess'] = !this.features[index]['hasAccess'];
            let isItAll = this.features.every(opt => opt.hasAccess);
            // let isItAllFalse = this.features.every(opt => !opt.hasAccess);
            if (isItAll) {
                // this.selectAllfeature();
                this.selected_all = true;
            } else {
                this.selected_all = false;
            }
        },
        selectAllfeature() {
            this.is_updated = true;
            this.selected_all = !this.selected_all;
            if (this.selected_all && this.features && this.features.length > 0) {
                this.features.map(opt => opt.hasAccess = this.selected_all);
            } else if (!this.selected_all && this.features && this.features.length > 0) {
                this.features.map(opt => opt.hasAccess = this.selected_all);
            }
        }
    },
    watch: {
        currentFeatureList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.features = JSON.parse(JSON.stringify(newValue));
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchFeatureList();
            }
        },
        features: {
            deep: true,
            handler(newValue) {
                let is_select_all = newValue.every((opt) => opt.hasAccess == true);
                if (is_select_all) {
                    this.selected_all = true;
                }
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.submitForm();
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.$emit('is-sales-save', newValue);
                    this.is_updated = false;
                }
            }
        }
    }
};
</script>

<style scoped>
/* Optional: Additional styles for the component */
</style>