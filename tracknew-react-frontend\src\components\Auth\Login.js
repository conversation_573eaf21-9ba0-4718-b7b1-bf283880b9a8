import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useForm } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';

import { loginUser, clearError } from '../../store/slices/authSlice';
import LoadingSpinner from '../UI/LoadingSpinner';
import Button from '../UI/Button';
import Input from '../UI/Input';

// Validation schema
const schema = yup.object({
  login: yup
    .string()
    .required('Email or mobile number is required')
    .test('email-or-mobile', 'Please enter a valid email or mobile number', function(value) {
      if (!value) return false;
      
      // Check if it's an email
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (emailRegex.test(value)) return true;
      
      // Check if it's a mobile number (10-15 digits)
      const mobileRegex = /^[0-9]{10,15}$/;
      if (mobileRegex.test(value.replace(/\D/g, ''))) return true;
      
      return false;
    }),
  password: yup
    .string()
    .required('Password is required')
    .min(6, 'Password must be at least 6 characters'),
});

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [rememberMe, setRememberMe] = useState(false);
  
  const dispatch = useDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  
  const { isLoading, error, isAccountLocked, loginAttempts } = useSelector(
    (state) => state.auth
  );

  const from = location.state?.from?.pathname || '/dashboard';

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch
  } = useForm({
    resolver: yupResolver(schema),
    defaultValues: {
      login: '',
      password: '',
    },
  });

  // Load saved credentials if remember me was checked
  useEffect(() => {
    const savedCredentials = localStorage.getItem('tracknew_remember_me');
    if (savedCredentials) {
      const { login } = JSON.parse(savedCredentials);
      setValue('login', login);
      setRememberMe(true);
    }
  }, [setValue]);

  // Clear error when component unmounts
  useEffect(() => {
    return () => {
      dispatch(clearError());
    };
  }, [dispatch]);

  const onSubmit = async (data) => {
    try {
      await dispatch(loginUser(data)).unwrap();
      
      // Save credentials if remember me is checked
      if (rememberMe) {
        localStorage.setItem('tracknew_remember_me', JSON.stringify({
          login: data.login
        }));
      } else {
        localStorage.removeItem('tracknew_remember_me');
      }
      
      navigate(from, { replace: true });
    } catch (error) {
      // Error is handled in the slice
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleForgotPassword = () => {
    const loginValue = watch('login');
    if (loginValue && loginValue.includes('@')) {
      navigate('/forgot-password', { state: { email: loginValue } });
    } else {
      navigate('/forgot-password');
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        <div>
          <div className="mx-auto h-12 w-auto flex justify-center">
            <img
              className="h-12 w-auto"
              src="/logo.png"
              alt="Track New"
              onError={(e) => {
                e.target.style.display = 'none';
                e.target.nextSibling.style.display = 'block';
              }}
            />
            <div 
              className="h-12 w-12 bg-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xl hidden"
            >
              TN
            </div>
          </div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign in to your account
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Or{' '}
            <Link
              to="/register"
              className="font-medium text-blue-600 hover:text-blue-500"
            >
              create a new account
            </Link>
          </p>
        </div>

        <form className="mt-8 space-y-6" onSubmit={handleSubmit(onSubmit)}>
          <div className="space-y-4">
            <div>
              <Input
                label="Email or Mobile Number"
                type="text"
                autoComplete="username"
                placeholder="Enter your email or mobile number"
                error={errors.login?.message}
                {...register('login')}
              />
            </div>

            <div>
              <div className="relative">
                <Input
                  label="Password"
                  type={showPassword ? 'text' : 'password'}
                  autoComplete="current-password"
                  placeholder="Enter your password"
                  error={errors.password?.message}
                  {...register('password')}
                />
                <button
                  type="button"
                  className="absolute inset-y-0 right-0 top-6 pr-3 flex items-center"
                  onClick={togglePasswordVisibility}
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-gray-400" />
                  )}
                </button>
              </div>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <input
                id="remember-me"
                name="remember-me"
                type="checkbox"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="remember-me" className="ml-2 block text-sm text-gray-900">
                Remember me
              </label>
            </div>

            <div className="text-sm">
              <button
                type="button"
                onClick={handleForgotPassword}
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                Forgot your password?
              </button>
            </div>
          </div>

          {/* Account locked warning */}
          {isAccountLocked && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">
                Your account has been temporarily locked due to multiple failed login attempts.
                Please try again later or reset your password.
              </div>
            </div>
          )}

          {/* Login attempts warning */}
          {loginAttempts > 0 && loginAttempts < 5 && (
            <div className="rounded-md bg-yellow-50 p-4">
              <div className="text-sm text-yellow-700">
                {5 - loginAttempts} login attempts remaining before account lockout.
              </div>
            </div>
          )}

          {/* Error message */}
          {error && (
            <div className="rounded-md bg-red-50 p-4">
              <div className="text-sm text-red-700">{error}</div>
            </div>
          )}

          <div>
            <Button
              type="submit"
              variant="primary"
              size="lg"
              className="w-full"
              disabled={isLoading || isAccountLocked}
            >
              {isLoading ? (
                <>
                  <LoadingSpinner size="sm" className="mr-2" />
                  Signing in...
                </>
              ) : (
                'Sign in'
              )}
            </Button>
          </div>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Don't have an account?{' '}
              <Link
                to="/register"
                className="font-medium text-blue-600 hover:text-blue-500"
              >
                Sign up here
              </Link>
            </p>
          </div>
        </form>

        {/* Demo credentials for development */}
        {process.env.NODE_ENV === 'development' && (
          <div className="mt-6 p-4 bg-gray-100 rounded-md">
            <h3 className="text-sm font-medium text-gray-900 mb-2">Demo Credentials:</h3>
            <div className="text-xs text-gray-600 space-y-1">
              <div>Admin: <EMAIL> / password123</div>
              <div>Manager: <EMAIL> / password123</div>
              <div>Engineer: <EMAIL> / password123</div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Login;
