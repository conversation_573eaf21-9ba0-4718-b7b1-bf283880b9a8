<template>
    <div class="w-full">
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>
        <div v-if="!open_skeleton" class="non-printable">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4 p-1 sm:p-4">
                <!--TAX Enable/Disable-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold">Tax enable/disable:</label>
                    <label class="inline-flex items-center cursor-pointer mt-2">
                        <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                            @click="toggleSwitch('proforma_gst')"
                            :class="{ 'bg-blue-600': formData.proforma_gst, 'bg-gray-200': !formData.proforma_gst }">
                            <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-6': formData.proforma_gst, 'translate-x-0': !formData.proforma_gst }">
                            </div>
                        </div>
                        <span class="pl-3"
                            :class="{ 'text-green-600': formData.proforma_gst, 'text-red-600': !formData.proforma_gst }">{{
                                formData.proforma_gst ? 'Enabled' : 'Disabled' }}</span>
                    </label>
                </div>
                <!-- Disclaimer Proforma-->
                <div class="w-full col-span-2">
                    <label for="disclaimer_proforma" class="block text-md font-bold">Disclaimer for Proforma</label>
                    <textarea id="disclaimer_proforma" v-model="formData.proforma_disclaimer"
                        @change="is_updated = true" rows="5"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="Enter the proforma disclaimer"></textarea>
                </div>
                <div class="col-span-2"> <!-- Add informational text to guide the user -->
                    <p class="text-sm text-gray-500 mb-2 text-left">Note: Please include any specific terms or
                        conditions
                        you wish
                        to
                        communicate in the invoice / estimation. Separate each point with a new line.</p>
                </div>

            </div>

            <!-- Save Button - Centered -->
            <button @click="saveData"
                class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 text-lg text-white py-2 px-5 rounded-md mx-auto block hover:bg-green-500 mt-5">
                Save
            </button>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'proforma',
    emits: ['is-sales-save', 'updatesalesData'],
    props: {
        companyId: String,
        userId: String,
        isMobile: Boolean,
        store_refresh: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            formData: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            is_updated: false,
        };
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('companies', ['fetchCompanyList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        saveData() {
            this.open_loader = true;
            if (Object.keys(this.shareData).length > 0) {
                axios.put(`/invoice_settings/${this.shareData.id}`, { ...this.formData, company_id: this.companyId })
                    .then(response => {
                        // console.log(response.data, 'Response update');
                        this.shareData = response.data.data;
                        this.open_loader = false;
                        this.fetchInvoiceSetting(true);
                        this.message = 'Proforma details are updated..!';
                        this.show = true;
                        this.$emit('is-sales-save', false);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            } else {
                this.$emit('updatesalesData');
            }
        },

        filteredDisclaimer(data) {
            if (this.formData.disclaimer !== '') {
                return data.split('\n').filter(line => line.trim() !== '');
            }
        },


        getInitialStoreData(store_data) {
            let parseData = JSON.parse(JSON.stringify(store_data));
            if (parseData && parseData.length > 0) {

                if (parseData.length !== 0 && parseData[0].disclaimer !== '') {
                    // let disclaimerMSG = parseData[0].disclaimer.split('\n');
                    this.formData = { proforma_disclaimer: parseData[0].proforma_disclaimer, proforma_gst: parseData[0].proforma_gst };
                    this.shareData = { ...parseData[0] };
                    // console.log(get_company_data, 'What happeniggg.......');                         
                }
            }
            this.open_skeleton = false;
        },
        //--is sms and whatsapp enble set as default---
        toggleSwitch(key) {
            if (key) {
                this.formData[key] = this.formData[key] == 0 ? 1 : 0;
            }
        }
    },
    mounted() {

        if (this.companyId !== null) {
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                this.getInitialStoreData(this.currentInvoice);
                this.open_skeleton = true;
                this.fetchInvoiceSetting();
                setTimeout(() => {
                    this.open_skeleton = false;
                }, 300);
            } else {
                this.open_skeleton = true;
                this.fetchInvoiceSetting();
            }
        }
    },
    watch: {

        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.getInitialStoreData(newValue);
                } else {
                    this.open_skeleton = false;
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchInvoiceSetting();
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.saveData();
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.is_updated = false;
                    this.$emit('is-sales-save', newValue);
                }
            }
        }
    }
};
</script>

<style scoped>
input[type="radio"] {
    /* remove standard background appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* create custom radiobutton appearance */
    display: inline-block;
    width: 20px;
    height: 20px;
    padding: 3px;
    /* background-color only for content */
    background-clip: content-box;
    border: 2px solid #bbbbbb;
    background-color: #e7e6e7;
    border-radius: 50%;
}

/* appearance for checked radiobutton */
input[type="radio"]:checked {
    background-color: #05810f;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
        /* Use the actual background color of your content */
        color: black;
        /* Use the actual text color of your content */
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */
    }

    .non-printable {
        display: none;
    }
}
</style>
