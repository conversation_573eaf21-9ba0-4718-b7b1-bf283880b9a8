import React, { useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { Provider } from 'react-redux';
import { QueryClient, QueryClientProvider } from 'react-query';
import { ReactQueryDevtools } from 'react-query/devtools';
import { Toaster } from 'react-hot-toast';
import { HelmetProvider } from 'react-helmet-async';

// Redux store
import { store } from './store/store';

// Components
import Layout from './components/Layout/Layout';
import ProtectedRoute from './components/Auth/ProtectedRoute';
import PublicRoute from './components/Auth/PublicRoute';
import LoadingSpinner from './components/UI/LoadingSpinner';
import ErrorBoundary from './components/ErrorBoundary/ErrorBoundary';

// Pages
import Login from './pages/Auth/Login';
import Register from './pages/Auth/Register';
import ForgotPassword from './pages/Auth/ForgotPassword';
import ResetPassword from './pages/Auth/ResetPassword';
import Dashboard from './pages/Dashboard/Dashboard';
import Services from './pages/Services/Services';
import ServiceDetails from './pages/Services/ServiceDetails';
import CreateService from './pages/Services/CreateService';
import Customers from './pages/Customers/Customers';
import CustomerDetails from './pages/Customers/CustomerDetails';
import CreateCustomer from './pages/Customers/CreateCustomer';
import Leads from './pages/Leads/Leads';
import LeadDetails from './pages/Leads/LeadDetails';
import CreateLead from './pages/Leads/CreateLead';
import AMC from './pages/AMC/AMC';
import AMCDetails from './pages/AMC/AMCDetails';
import CreateAMC from './pages/AMC/CreateAMC';
import Sales from './pages/Sales/Sales';
import SalesDetails from './pages/Sales/SalesDetails';
import CreateSales from './pages/Sales/CreateSales';
import Products from './pages/Products/Products';
import ProductDetails from './pages/Products/ProductDetails';
import CreateProduct from './pages/Products/CreateProduct';
import Estimations from './pages/Estimations/Estimations';
import EstimationDetails from './pages/Estimations/EstimationDetails';
import CreateEstimation from './pages/Estimations/CreateEstimation';
import Reports from './pages/Reports/Reports';
import Settings from './pages/Settings/Settings';
import Profile from './pages/Profile/Profile';
import NotFound from './pages/NotFound/NotFound';

// Hooks
import { useAuth } from './hooks/useAuth';

// Utils
import { initializeFirebase } from './utils/firebase';

// Styles
import './App.css';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
      staleTime: 5 * 60 * 1000, // 5 minutes
    },
  },
});

function AppContent() {
  const { isLoading, isAuthenticated, initializeAuth } = useAuth();

  useEffect(() => {
    // Initialize authentication
    initializeAuth();

    // Initialize Firebase
    initializeFirebase();
  }, [initializeAuth]);

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <Router>
      <Routes>
        {/* Public Routes */}
        <Route
          path="/login"
          element={
            <PublicRoute>
              <Login />
            </PublicRoute>
          }
        />
        <Route
          path="/register"
          element={
            <PublicRoute>
              <Register />
            </PublicRoute>
          }
        />
        <Route
          path="/forgot-password"
          element={
            <PublicRoute>
              <ForgotPassword />
            </PublicRoute>
          }
        />
        <Route
          path="/reset-password/:token"
          element={
            <PublicRoute>
              <ResetPassword />
            </PublicRoute>
          }
        />

        {/* Protected Routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<Dashboard />} />
          
          {/* Services */}
          <Route path="services" element={<Services />} />
          <Route path="services/create" element={<CreateService />} />
          <Route path="services/:id" element={<ServiceDetails />} />
          
          {/* Customers */}
          <Route path="customers" element={<Customers />} />
          <Route path="customers/create" element={<CreateCustomer />} />
          <Route path="customers/:id" element={<CustomerDetails />} />
          
          {/* Leads */}
          <Route path="leads" element={<Leads />} />
          <Route path="leads/create" element={<CreateLead />} />
          <Route path="leads/:id" element={<LeadDetails />} />
          
          {/* AMC */}
          <Route path="amc" element={<AMC />} />
          <Route path="amc/create" element={<CreateAMC />} />
          <Route path="amc/:id" element={<AMCDetails />} />
          
          {/* Sales */}
          <Route path="sales" element={<Sales />} />
          <Route path="sales/create" element={<CreateSales />} />
          <Route path="sales/:id" element={<SalesDetails />} />
          
          {/* Products */}
          <Route path="products" element={<Products />} />
          <Route path="products/create" element={<CreateProduct />} />
          <Route path="products/:id" element={<ProductDetails />} />
          
          {/* Estimations */}
          <Route path="estimations" element={<Estimations />} />
          <Route path="estimations/create" element={<CreateEstimation />} />
          <Route path="estimations/:id" element={<EstimationDetails />} />
          
          {/* Reports */}
          <Route path="reports" element={<Reports />} />
          
          {/* Settings */}
          <Route path="settings" element={<Settings />} />
          
          {/* Profile */}
          <Route path="profile" element={<Profile />} />
        </Route>

        {/* 404 Route */}
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

function App() {
  return (
    <ErrorBoundary>
      <HelmetProvider>
        <Provider store={store}>
          <QueryClientProvider client={queryClient}>
            <AppContent />
            <Toaster
              position="top-right"
              toastOptions={{
                duration: 4000,
                style: {
                  background: '#363636',
                  color: '#fff',
                },
                success: {
                  duration: 3000,
                  theme: {
                    primary: 'green',
                    secondary: 'black',
                  },
                },
                error: {
                  duration: 5000,
                  theme: {
                    primary: 'red',
                    secondary: 'black',
                  },
                },
              }}
            />
            {process.env.NODE_ENV === 'development' && (
              <ReactQueryDevtools initialIsOpen={false} />
            )}
          </QueryClientProvider>
        </Provider>
      </HelmetProvider>
    </ErrorBoundary>
  );
}

export default App;
