<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50"
        @keyup.esc="toggleImageVisibilityClose()">

        <!-- Modal -->
        <div ref="modal"
            class="file-model bg-white w-full sm:w-4/5 lg:w-2/3 h-4/5 flex justify-center items-center overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }" @click.self="toggleImageVisibilityClose()">
            <div class="relative w-full h-full flex justify-center items-center">
                <!-- Content will vary based on file type -->
                <template v-if="isImage">
                    <img :src="showImageModal" alt="Uploaded Image"
                        :style="{ transform: `scale(${zoomLevel}) translate(${translateX}px, ${translateY}px)`, cursor: isDragging ? 'grabbing' : 'grab' }"
                        class="max-w-full max-h-full img-model" />
                </template>
                <template v-else-if="isDocument">
                    <iframe :src="showImageModal" class="w-full h-full" frameborder="0"></iframe>
                </template>

                <button @click="toggleFileVisibilityClose"
                    class="absolute top-2 right-2 bg-red-700 text-white px-2 py-1 rounded hover:bg-red-600">Close</button>

                <button v-if="isImage" @click="toggleFullScreen"
                    class="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-blue-700 text-white px-4 py-2 rounded hover:bg-blue-600 text-xs">
                    Full Screen
                </button>
            </div>
        </div>

    </div>
</template>


<script>
export default {
    name: 'display file',
    props: {
        showModal: Boolean,
        showImageModal: String,
    },
    data() {
        return {
            isMobile: false,
            isOpen: false,
            isImage: false,
            isDocument: false,
            zoomLevel: 1,
            zoomin: '/images/service_page/zoomIn.png',
            zoomout: '/images/service_page/zoomOut.png',
            active_icon: '/images/setting_page/active.png',
            deactive_icon: '/images/setting_page/deactive.png',
            isDragging: false,
            startX: 0,
            startY: 0,
            translateX: 0,
            translateY: 0,
            isFullscreen: false,
        };
    },
    methods: {
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        toggleImageVisibilityClose() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        zoomIn() {
            this.zoomLevel += 0.1;
        },
        zoomOut() {
            this.zoomLevel -= 0.1;
        },
        checkFileType() {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif'];
            const documentExtensions = ['pdf', 'txt', 'csv'];

            const fileExtension = this.showImageModal.split('.').pop().toLowerCase();

            if (imageExtensions.includes(fileExtension)) {
                this.isImage = true;
                this.isDocument = false;
            } else if (documentExtensions.includes(fileExtension)) {
                this.isImage = false;
                this.isDocument = true;
            } else {
                // Handle unknown file types (optional)
                console.warn('Unsupported file type');
            }
        },
        toggleFileVisibilityClose() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        toggleFullScreen() {
            const elem = document.querySelector('.img-model');
            if (!this.isFullscreen) {
                if (elem.requestFullscreen) {
                    elem.requestFullscreen();
                }
                else if (elem.webkitRequestFullscreen) {
                    elem.webkitRequestFullscreen();
                } else if (elem.msRequestFullscreen) {
                    elem.msRequestFullscreen();
                }
                this.isFullscreen = true; // Update the fullscreen state
            } else {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                this.isFullscreen = false; // Update the fullscreen state
            }
            // Add event listener to detect fullscreen change
            // document.addEventListener('fullscreenchange', () => {
            //     if (!document.fullscreenElement) { // If fullscreen is exited
            //         // Reset element dimensions to fit screen size
            //         const elem = document.querySelector('.img-model');
            //         const screenWidth = screen.width;
            //         const screenHeight = screen.height;
            //         elem.style.width = screenWidth + 'px'; // Set width to screen width
            //         elem.style.height = screenHeight + 'px'; // Set height to screen height
            //     }
            // });
        },
        startDragging(event) {
            console.log('drag');
            this.isDragging = true;
            this.startX = event.clientX - this.translateX;
            this.startY = event.clientY - this.translateY;
        },
        handleDragging(event) {
            if (!this.isDragging) return;
            this.translateX = event.clientX - this.startX;
            this.translateY = event.clientY - this.startY;
        },
        stopDragging() {
            this.isDragging = false;
        },
        handleOutsideClick(event) {
            if (this.$refs.modal && !this.$refs.modal.contains(event.target) && this.showModal && this.isOpen) {
                this.toggleImageVisibilityClose(event);
            }
        },
    },
    mounted() {
        this.updateIsMobile();
        // Use nextTick to wait for the DOM to be updated before accessing the input field
        window.addEventListener('resize', this.updateIsMobile);
        document.addEventListener('click', this.handleOutsideClick);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.checkFileType();
                }
            }, 100);
        },
    }
};
</script>
<style scoped>
.img-model {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.img-model img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}
</style>
