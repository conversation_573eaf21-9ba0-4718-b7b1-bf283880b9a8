<template>
    <div :class="{ 'manualStyle text-sm sm:mb-[60px]': isMobile, 'text-sm': !isMobile }" ref="scrollContainer"
        @scroll="handleScroll">
        <div class="my-custom-margin">
            <!--search-->
            <div v-if="isMobile" class="mt-2 sm:m-4" :class="{ 'mt-3': isMobile }">
                <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer" @resetData="resetToSearch"
                    :resetSearch="resetSearch">
                </searchCustomer>
            </div>
            <!--page header-->
            <div v-if="!isMobile" class="m-1 my-3 flex items-center space-x-4">
                <p class="font-bold text-xl">Estimation</p>
                <div v-if="!open_skeleton"
                    class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                    <p class="text-gray-700">Total Estimation
                        :</p>
                    <p class="font-semibold pl-1">
                        {{ pagination.total }}
                    </p>
                </div>
            </div>
            <!--new design header-->
            <div v-if="!isMobile" class="flex justify-between m-1 mt-4">
                <div class="flex mr-2 space-x-4">
                    <button @click="openEstimation" :class="{ 'mr-2': isMobile }"
                        class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center">
                        <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
                        <span v-if="!isMobile" class="text-center">New Estimate</span>
                    </button>
                    <!--view design-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="info-msg px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200"
                            @click="toggleView" :title02="'Table view'"
                            :class="{ 'bg-white': items_category === 'tile' }">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200 info-msg"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="'Card view'">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <!----Filter Model-->
                <div ref="dropdownContainerFilter" class="items-center relative">
                    <button @click="toggleMainDropdown" :disabled="data.length == 0"
                        class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                        :class="{ 'cursor-not-allowed': data.length == 0 }">
                        <span class="inline-flex items-center w-full pointer-events-none">
                            <font-awesome-icon icon="fa-solid fa-filter" class="px-1" /> Filter
                            <font-awesome-icon v-if="!isMainDropdownOpen" icon="fa-solid fa-angle-down" class="pl-3" />
                            <font-awesome-icon v-if="isMainDropdownOpen" icon="fa-solid fa-angle-up" class="pl-3" />
                        </span>
                    </button>
                    <!-- Main Dropdown -->
                    <div v-if="isMainDropdownOpen" ref="mainDropdown"
                        class="absolute mt-2 w-48 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 right-0 border">
                        <div class="py-1">
                            <!-- Other Options -->
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('by Date')">By Date</button>
                        </div>
                    </div>
                </div>
            </div>
            <!---fiter information-->
            <div v-if="Object.keys(filteredBy).length > 0" class="text-xs flex -mb-3 flex-row overflow-auto">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2">
                    <p class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                    <p>{{ key === 'assign_to' ? value.join(', ') : key === 'type' ? typeList[value] : key === 'status' ?
                        statusList[value] : value }}</p>
                </div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton && data.length > 0" class="text-sm m-1 mt-5">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                        <!--search bar--->
                        <div>
                            <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                @resetData="resetToSearch" :resetSearch="resetSearch">
                            </searchCustomer>
                        </div>
                    </div>
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label === 'Current Date' ? 'Date' : column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in data" :key="index"
                                    class="hover:bg-gray-200 cursor-pointer">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex"
                                        :class="{ 'hidden': !column.visible }" class="px-1 py-1 table-border"
                                        @click="printRecord(record)">
                                        <span v-if="column.field === 'current_date'"
                                            class="cursor-pointer created-at-color"
                                            :title="calculateDaysAgo(formattedDate(record.current_date, true))">
                                            {{ formatDateTime(record[column.field]) }}
                                        </span>

                                        <span v-if="column.field === 'customers'" class="hover:text-sky-700"
                                            @mouseover="showModal(record[column.field], $event)"
                                            @mouseleave="hideModal">{{
                                                record[column.field].first_name +
                                                ' ' + (record[column.field].last_name === null ? '' :
                                                    record[column.field].last_name)
                                                +
                                                ' - ' + record[column.field].contact_number }}</span>
                                        <span
                                            v-if="column.field !== 'current_date' && column.field !== 'customers' && column.field !== 'invoice_id'">
                                            {{ column.field === 'grand_total' && record[column.field] ?
                                                currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                    currentCompanyList.currency : '' }}
                                            {{ record[column.field] }}
                                        </span>
                                        <span @mouseover="handleMouseOver(record, column.field, $event)"
                                            @mouseleave="hideModal"
                                            class="cursor-pointer hover:text-blue-600 hover:underline"
                                            v-if="column.field === 'invoice_id'">
                                            {{ record[column.field] }}</span>

                                        <!-- <span v-if="column.field === 'items'" v-for="(data, i) in record[column.field]"
                                    :key="i">
                                    {{ displayProductList(data) }}<br></span>
                                <span v-if="column.field === 'customer'">
                                    {{ findAndReturnCustomer(record[column.field]) }}</span> -->
                                        <!-- <span
                                    v-if="!Array.isArray(record[column.field]) && column.field !== 'lead_type' && column.field !== 'lead_status'">{{
                                        record[column.field] }}</span>
                                <span v-if="column.field === 'lead_type'">{{ leadType.length !== 0 &&
                                    leadType[record[column.field]]['name'] }}</span>
                                <span v-if="column.field === 'lead_status'">{{ leadStatus.length !== 0 &&
                                    leadStatus[record[column.field]]['name'] }}</span>
                                <span v-if="Array.isArray(record[column.field])">{{ record[column.field].join(',') }}</span> -->
                                    </td>
                                    <td class="px-2 py-2 text-center table-border">
                                        <div class="flex justify-center">
                                            <div class="flex justify-left">
                                                <a v-if="!record.editing" @click.prevent="printRecord(record)"
                                                    :href="`/estimation/preview?type=estimation&est_no=${record.id}&back=home`"
                                                    class="text-violet-700 px-1 py-1 flex justify-center items-center">
                                                    <!-- <font-awesome-icon icon="fa-solid fa-eye" /> -->
                                                    <font-awesome-icon icon="fa-solid fa-print" />
                                                </a>
                                                <button v-if="!record.editing && record.status == '0'"
                                                    @click="startEdit(record)"
                                                    class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-solid fa-pencil"
                                                        style="color: #3b82f6;" />
                                                </button>
                                                <div v-if="!record.editing && record.status === '0' && record.status !== '3'"
                                                    class="flex">
                                                    <button @click="toSale(record)"
                                                        class="text-blue-500 px-1 py-1 mr-2 rounded-full bg-blue-500 text-xs text-white px-3 hover:bg-green-700 cursor-pointer">
                                                        to Invoice
                                                    </button>
                                                    <button @click="toProforma(record)"
                                                        class="text-blue-500 px-1 py-1 mr-2 rounded-full bg-blue-500 text-xs text-white px-3 hover:bg-green-700 cursor-pointer">to
                                                        Proforma
                                                    </button>
                                                </div>

                                                <!-- Status 1: Converted to Invoice -->
                                                <button v-if="!record.editing && record.status === '1'"
                                                    class="text-gray-500 px-1 py-1 mr-2 rounded-full bg-gray-500 text-xs text-white px-3 hover:bg-gray-600 cursor-pointer">
                                                    Converted to Invoice
                                                </button>

                                                <!-- Status 2: Converted to Proforma -->
                                                <button v-if="!record.editing && record.status === '2'"
                                                    class="text-gray-500 px-1 py-1 mr-2 rounded-full bg-gray-500 text-xs text-white px-3 hover:bg-gray-600 cursor-pointer">
                                                    Converted to Proforma
                                                </button>
                                                <button v-if="!record.editing && record.status === '3'"
                                                    class="text-red-700 px-1 py-1 mx-2 rounded-full bg-red-100 text-xs px-3 hover:bg-red-200 cursor-pointer">
                                                    Cancelled
                                                </button>
                                            </div>
                                            <div class="flex justify-center">
                                                <!--More actions-->
                                                <div class="relative">
                                                    <button @click.stop="displayAction(index)"
                                                        class="px-2 py-1 text-blue-800 rounded"
                                                        :class="{ 'bg-blue-100': display_option === index }">
                                                        <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                            size="lg" />
                                                    </button>
                                                </div>
                                                <div v-if="display_option === index" :ref="'dropdown' + index"
                                                    class="z-10 mt-8 absolute bg-white shadow-inner shadow-gray-400 border border-gray-400 right-0 divide-y divide-gray-100 rounded-lg items-center"
                                                    style="display: flex; justify-content: center; align-items: center;">
                                                    <ul class="py-1 text-gray-700 text-xs"
                                                        aria-labelledby="dropdownDefaultButton">
                                                        <li class="hover:bg-gray-200">
                                                            <a v-if="!record.editing"
                                                                @click.prevent="printRecord(record)"
                                                                :href="`/estimation/preview?type=estimation&est_no=${record.id}&back=home`"
                                                                class="text-violet-700 px-2 py-1 flex justify-center items-center">
                                                                <!-- <font-awesome-icon icon="fa-solid fa-eye" size="lg" /> -->
                                                                <font-awesome-icon icon="fa-solid fa-print" />
                                                                <span class="px-2">Print / View</span>
                                                            </a>
                                                        </li>
                                                        <li :class="{ 'hidden': record.status !== '0' }"
                                                            class="hover:bg-gray-200">
                                                            <button v-if="!record.editing" @click="startEdit(record)"
                                                                class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-pencil"
                                                                    style="color: #3b82f6;" />
                                                                <span class="px-2">Edit</span>
                                                            </button>
                                                        </li>
                                                        <li v-if="index === 0"
                                                            :class="{ 'hidden': record.status !== '0' && record.status !== '3' }"
                                                            class="hover:bg-gray-200">
                                                            <button v-if="!record.editing && checkRoles(['admin'])"
                                                                @click="confirmDelete(index)"
                                                                class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                    style="color: #ef4444" />
                                                                <span class="px-2">Delete</span>
                                                            </button>
                                                        </li>
                                                        <li v-if="record.status === '0'" class="hover:bg-gray-200">
                                                            <button v-if="!record.editing"
                                                                @click="openConfirmSalesCancel(record)"
                                                                class="text-orange-500 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-xmark" />
                                                                <span class="px-2">Cancel</span>
                                                            </button>
                                                        </li>
                                                        <li class="hover:bg-gray-200">
                                                            <button v-if="!record.editing"
                                                                @click="cloneEstimation(record)"
                                                                class="text-green-700 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-clone" />
                                                                <span class="px-2">Clone EST</span>
                                                            </button>
                                                        </li>
                                                    </ul>

                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                    <td>
                                        <button
                                            class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="openEstimation">
                                            + Estimation
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- Hover Modal -->
                        <customerDataTable v-if="isModalVisible" :showModal="isModalVisible" :modalData="modalData"
                            :modalPosition="modalPosition" @mouseover="toggleHoverModal(true)"
                            @mouseleave="toggleHoverModal(false)" @mousemove="showModal(null, $event)">
                        </customerDataTable>
                    </div>
                    <!--card view-->
                    <div>
                        <div v-if="items_category === 'list'"
                            class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4 custom-scrollbar-hidden">
                            <div v-for="(record, index) in data" :key="index" class="w-full relative">
                                <div
                                    class="max-w-md mx-auto bg-white rounded-xl border border-gray-300  overflow-hidden md:max-w-2xl shadow-lg">
                                    <!-- Top Section -->
                                    <div class="flex justify-between items-center p-4 py-2">
                                        <!-- Left Side (Can be your dynamic content) -->
                                        <div class="text-xs text-red-500 cursor-pointer"
                                            :title="formatDateTime(record.current_date)">
                                            <p>{{ calculateDaysAgo(formattedDate(record.current_date, true)) }}</p>
                                        </div>
                                        <!-- Right Side (Actions) -->
                                        <div class="flex space-x-4">
                                            <div class="flex justify-center">
                                                <!--convert to invoice-->
                                                <div class="flex justify-left">
                                                    <div v-if="!record.editing && record.status === '0'">
                                                        <button @click="toSale(record)"
                                                            class="text-blue-500 px-1 py-1 mr-2 rounded bg-blue-500 text-white px-3 hover:bg-green-700 cursor-pointer">
                                                            To Invoice
                                                        </button>
                                                        <button @click="toProforma(record)"
                                                            class="text-blue-500 px-1 py-1 mr-2 rounded bg-blue-500 text-white px-3 hover:bg-green-700 cursor-pointer">
                                                            To Proforma
                                                        </button>
                                                    </div>
                                                    <button @click="navigatToInvoice(index)"
                                                        v-if="!record.editing && record.status === '2'"
                                                        class="text-gray-500 px-1 py-1 mr-2 rounded bg-gray-500 text-white px-3 hover:bg-gray-600 cursor-pointer">
                                                        {{ record['invoice_id'] }}
                                                    </button>
                                                    <button @click="navigatToInvoice(index)"
                                                        v-if="!record.editing && record.status === '1'"
                                                        class="text-gray-500 px-1 py-1 mr-2 rounded bg-gray-500 text-white px-3 hover:bg-gray-600 cursor-pointer">
                                                        {{ record['invoice_id'] }}
                                                    </button>
                                                    <button v-if="!record.editing && record.status === '3'"
                                                        class="text-red-700 px-1 py-1 mx-2 rounded bg-red-100 text-xs px-3 hover:bg-red-200 cursor-pointer">
                                                        Cancelled
                                                    </button>
                                                </div>
                                                <div class="flex justify-center">
                                                    <!--More actions-->
                                                    <div>
                                                        <button @click.stop="displayAction(index)"
                                                            class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                            <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                                size="lg" />
                                                        </button>
                                                    </div>
                                                    <div v-if="display_option === index" :ref="'dropdown' + index"
                                                        class="z-10 mt-8 absolute bg-white right-0 divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                        style="display: flex; justify-content: center; align-items: center;">
                                                        <ul class="py-1 text-gray-700 text-xs"
                                                            aria-labelledby="dropdownDefaultButton">
                                                            <li class="hover:bg-gray-200">
                                                                <a v-if="!record.editing"
                                                                    :href="`/estimation/preview?type=estimation&est_no=${record.id}&back=home`"
                                                                    @click.prevent="printRecord(record)"
                                                                    class="text-violet-700 px-2 py-1 flex justify-center items-center">
                                                                    <font-awesome-icon icon="fa-solid fa-print" />
                                                                    <span class="px-2">Print / View</span>
                                                                </a>
                                                            </li>
                                                            <li class="hover:bg-gray-200"
                                                                :class="{ 'hidden': record.status !== '0' }">
                                                                <button v-if="!record.editing"
                                                                    @click="startEdit(record)"
                                                                    class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                                    <font-awesome-icon icon="fa-solid fa-pencil"
                                                                        style="color: #3b82f6;" />
                                                                    <span class="px-2">Edit</span>
                                                                </button>
                                                            </li>
                                                            <li v-if="index === 0" class="hover:bg-gray-200"
                                                                :class="{ 'hidden': record.status !== '0' && record.status !== '3' }">
                                                                <button v-if="!record.editing && checkRoles(['admin'])"
                                                                    @click="confirmDelete(index)"
                                                                    class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                                    <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                        style="color: #ef4444" />
                                                                    <span class="px-2">Delete</span>
                                                                </button>
                                                            </li>
                                                            <li v-if="record.status === '0'" class="hover:bg-gray-200">
                                                                <button v-if="!record.editing"
                                                                    @click="openConfirmSalesCancel(record)"
                                                                    class="text-orange-500 px-2 py-1 flex justify-center items-center">
                                                                    <font-awesome-icon icon="fa-solid fa-xmark" />
                                                                    <span class="px-2">Cancel</span>
                                                                </button>
                                                            </li>
                                                            <li class="hover:bg-gray-200">
                                                                <button v-if="!record.editing"
                                                                    @click="cloneEstimation(record)"
                                                                    class="text-green-700 px-2 py-1 flex justify-center items-center">
                                                                    <font-awesome-icon icon="fa-regular fa-clone" />
                                                                    <span class="px-2">Clone EST</span>
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Middle Section -->
                                    <div class="px-4 py-2">
                                        <!-- Customer Details (Can be your dynamic content) -->
                                        <div class="flex items-center mb-2 -mt-4">
                                            <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                                :class="{ 'bg-gray-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-blue-600': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                                {{ record.customers && record.customers.first_name
                                                    ?
                                                    record.customers.first_name[0].toUpperCase() :
                                                    'C' }}
                                            </div>
                                            <div>
                                                <h4 class="text-sm leading-6 font-semibold text-gray-900 mb-1 cursor-pointer"
                                                    @click="printRecord(record)">
                                                    {{ record.customers && record.customers.first_name ?
                                                        (record.customers.first_name + ' ' +
                                                            (record.customers.last_name ?
                                                                record.customers.last_name : '')) : '' }}</h4>
                                                <p class="text-sm text-gray-500 cursor-pointer"
                                                    @click="dialPhoneNumber(record.customers && record.customers.contact_number)">
                                                    +91-{{
                                                        record.customers.contact_number }}</p>
                                            </div>
                                        </div>

                                        <!-- Invoice Details (Should iterate over your data) -->
                                        <!-- <div class="grid grid-cols-2 gap-2 mb-1">
                                    <div class="bg-gray-100 rounded-md p-2 items-center">
                                        <p class="text-sm text-gray-700 font-semibold">Estimation no: </p>
                                        <p class="text-sm text-gray-500">{{ record['estimate_num'] }}</p>
                                    </div>
                                    <div class="bg-gray-100 rounded-md p-2 items-center">
                                        <p class="text-sm text-gray-700 font-semibold">Estimation Date: </p>
                                        <p class="text-sm text-gray-500">{{ formatDateTime(record['current_date']) }}
                                        </p>
                                    </div>
                                </div> -->
                                        <!-- Invoice Actions (Can be your dynamic actions) -->
                                        <div class="flex justify-between bg-gray-100 rounded-md p-3 py-1 cursor-pointer"
                                            @click="printRecord(record)">
                                            <div>
                                                <p class="sm:text-xs lg:text-sm text-sm text-gray-700 font-semibold">
                                                    Grand
                                                    Total:</p>
                                                <p class="text- text-gray-900 font-semibold">{{ currentCompanyList &&
                                                    currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                    currentCompanyList.currency }} {{ record['grand_total']
                                                    }}
                                                </p>
                                            </div>
                                            <div class="flex items-center">
                                                <span v-if="record['status'] !== '3'"
                                                    class="px-3 py-1 rounded-full sm:text-xs lg:text-sm text-sm font-medium mr-2"
                                                    :class="{ 'bg-green-200  :clasn-100 text-green-900': record['invoice_id'], 'bg-red-100 text-red-800': !record['invoice_id'] }">
                                                    {{ record['invoice_id'] ? 'Success' : 'Pending' }}</span>
                                                <div class="bg-gray-100 rounded-md items-center">
                                                    <p
                                                        class="sm:text-xs lg:text-sm text-sm text-gray-700 font-semibold">
                                                        Estimation no: </p>
                                                    <p class="text-sm text-gray-500">{{ record['estimate_num'] }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && this.pagination.last_page > 0 && this.pagination.last_page === this.pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>

                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="pagination && totalPages && !isMobile">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>
                        <ul class="flex list-none">
                            <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                    class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                    <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                    <span class="pl-1" v-if="!isMobile">Prev</span>
                                </button>
                            </li>
                            <li v-for="pageNumber in visiblePageNumbers()" :key="pageNumber">
                                <button @click="updatePage(pageNumber)"
                                    :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                    class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                        pageNumber
                                    }}</button>
                            </li>
                            <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                :class="{ 'bg-gray-500': currentPage === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== pagination.last_page }">
                                <button @click="updatePage(currentPage + 1)"
                                    :disabled="currentPage === pagination.last_page"
                                    class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                                    <span class="pr-1" v-if="!isMobile">Next</span>
                                    <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!---in mobile view Filter Service-->
        <div v-if="isMobile" ref="dropdownContainerFilter">
            <div class="fixed bottom-36 right-5 z-50 bg-green-700 rounded-full">
                <div class="flex justify-end">
                    <button @click="toggleMainDropdown" type="button"
                        class="flex items-center justify-center px-[10px] py-2 text-white "
                        :class="{ 'cursor-not-allowed blur-sm': data.length == 0 }" :disabled="data.length == 0">
                        <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                    </button>
                </div>
            </div>
            <!-- Main Dropdown -->
            <div v-if="isMainDropdownOpen" ref="mainDropdown"
                class="fixed bottom-48 sm:bottom-48  left-full transform -translate-x-full w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border">
                <div class="py-1">

                    <!-- Other Options -->
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('by Date')">By Date</button>
                </div>
            </div>
        </div>
        <!---in mobile view create new sale-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openEstimation" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!-- Use the SalesModal component -->
        <estimationTypeModalVue :showModal="showEstModal" @closeEstModal="closeEstModal">
        </estimationTypeModalVue>
        <!--Lead Filter-->
        <estimationFilter :showModal="lead_filter" :page="'Estimation'" @closeFilter="closeLeadFilter"
            :typeList="typeList" :statusList="statusList" :selectedByValue="selectedByValue"></estimationFilter>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'estimate'"></bottombar> -->
        <salesCancelConfirm :showModal="estimation_cancel_confirmation" :type="'estimation'"
            @onCancel="closeSalesConfirm" @onConfirm="confirmSalesCancel"></salesCancelConfirm>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import axios from 'axios';
import confirmbox from '../dialog_box/confirmbox.vue';
import dialogAlert from '../dialog_box/dialogAlert.vue';
import estimationTypeModalVue from '../dialog_box/estimationTypeModal.vue';
import estimationFilter from '../dialog_box/filter_Modal/estimationFilter.vue';
// import bottombar from '../dashboard/bottombar.vue';
import { mapActions, mapGetters } from 'vuex';
import customerDataTable from '../dialog_box/customerDataTable.vue';
import searchCustomer from '../customers/searchCustomer.vue';
import salesCancelConfirm from '../dialog_box/salesCancelConfirm.vue';
export default {
    name: 'Estimation_home',
    emits: ['updateIsOpen'],
    components: {
        confirmbox,
        dialogAlert,
        estimationTypeModalVue,
        estimationFilter,
        // bottombar,
        customerDataTable,
        searchCustomer,
        salesCancelConfirm
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            //---
            showEstModal: false,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            columns: [],
            serviceList: [],
            data: [],
            originalData: [],
            isImportModalOpen: false,
            open_message: false,
            message: '',
            customerList: [],
            //---filter--
            showFilterOptions: false,
            mouseOverIsNot: false,
            filterOptions: ['by Date', 'by Customer', 'by Status', 'Custom'],
            selectedByValue: null,
            lead_filter: false,
            typeList: ['product', 'service'],
            statusList: ['Converted Invoice', 'Pending converted to invoice'],
            filteredBy: {},
            //---api integration---
            companyId: null,
            userId: null,
            pagination: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 7,
            number_of_rows: 10,
            gap: 5,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            now: null,
            open_loader: false,
            //---modal---mouse over---
            isModalVisible: false,
            modalData: {},
            modalPosition: { x: 0, y: 0 },
            actionPosition: { x: 0, y: 0 },
            isHoveringModal: false,
            hoverTimer: null,
            lastMousePosition: { x: 0, y: 0 },
            tolerance: 50, // Tolerance for mouse movement in pixels
            //---table data filter asending and decending----
            is_filter: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //--filter--
            isMainDropdownOpen: false,
            customer_id: null,
            resetSearch: false,
            //--cancel est--
            estimation_cancel_confirmation: false,
            cancelrecords_data: null,
        };
    },
    computed: {
        ...mapGetters('estimations', ['currentEstimationsList']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        ...mapGetters('clone', ['estimationclone']),
        paginatedData() {
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                this.length_category = filteredData.length;
                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const record = ['current_date', 'customers', 'estimate_num', 'estimate_type', 'grand_total', 'invoice_id'];
            const fields = [];
            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data) {
                for (const key of record) {
                    if (key !== 'id' && key !== 'items' && key !== 'data' && key !== 'company' && key !== 'assign_to' && key !== 'status') { // Exclude the 'id' field
                        const label = formatLabel(key);
                        fields.push({ label, field: key, visible: true });
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.fetchCompanyList();
        if (this.companyId) {
            // this.getEstimationList(this.recordsPerPage, this.currentPage);
            if (this.currentEstimationsList && this.currentEstimationsList.data && this.currentEstimationsList.data.length > 0) {
                this.data = this.currentEstimationsList.data;
                this.pagination = this.currentEstimationsList.pagination;
                this.fetchEstimationsList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
            } else {
                if (this.currentEstimationsList && Object.keys(this.currentEstimationsList).length == 0) {
                    this.open_skeleton = true;
                    this.fetchEstimationsList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
                }
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        const view = localStorage.getItem('estimation_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        if (this.isMobile) {
            this.items_category = 'list';
        }
        //---sortIcons---
        const initialShortVisible = ['current_date', 'customers', 'grand_total', 'invoice_id'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
        // Add and remove event listeners
        window.addEventListener("keydown", this.handleKeyPress);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        window.removeEventListener("keydown", this.handleKeyPress);
    },
    methods: {
        ...mapActions('estimations', ['fetchEstimationsList']),
        ...mapActions('localStorageData', ['validateRoles', 'fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('clone', ['getEstimation', 'updateEstimation']),

        validateDateTime(dateTimeString) {
            const datePart = dateTimeString.substring(0, 10);
            const isValidDate = /^\d{4}-\d{2}-\d{2}$/.test(datePart);
            return isValidDate;
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                let find_data = this.data[this.deleteIndex];
                if (find_data && find_data.id) {
                    axios.delete(`/estimations/${find_data.id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            // console.log(response.data);
                            this.updateKeyWithTime('estimation_update');
                            // this.data.splice(indexToDelete, 1);                        
                            this.message = response.data.message;
                            this.open_message = true;
                            this.getEstimationList(this.recordsPerPage, this.data.length > 1 ? this.pagination.current_page : this.pagination.current_page - 1 !== 0 ? this.pagination.current_page - 1 : 1, true);
                        })
                        .catch(error => {
                            console.error('Error', error);
                        })
                }
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            try {
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        //---lead modal
        openEstimation() {
            this.$router.push({
                name: 'addEstimation',
                params: { type: 'product' },
                query: { type: 'add' }
            });
        },

        //---close the message--
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---record---
        startEdit(record) {
            this.$router.push({
                name: 'addEstimation', // Name of the route
                params: { type: 'product' }, // Parameter passed in the route path
                query: {
                    type: 'edit',
                    est_no: record.id,
                }
            });
        },
        //---closeLeadModal
        closeEstModal() {
            // console.log('helllllllo');           
            this.showEstModal = false;
        },
        //----collect customer--
        findAndReturnCustomer(id) {
            // console.log(id, 'Waht happenig....@');
            if (this.customerList.length > 0) {
                let findCustomer = this.customerList.find((data) => data.id === id);
                // console.log(findCustomer, 'WWWWWW');
                if (findCustomer) {
                    if (findCustomer.lastName) {
                        return `${findCustomer.firstName} ${findCustomer.lastName} - ${findCustomer.contactNumber}`;
                    }
                    else {
                        return `${findCustomer.firstName} - ${findCustomer.contactNumber}`;
                    }
                }
            }
        },
        //---print product--
        displayProductList(data) {
            if (data.description !== '') {
                return `${data.description} - ${data.qty}`;
            }

        },
        //---to sale--
        toSale(record) {
            this.$router.push({
                name: 'sales-invoice',
                query: { type: 'add', est_no: record.id }
            });
        },
        //---to proforma 
        toProforma(record) {
            this.$router.push({
                name: 'addProformaInvoice',
                params: { type: 'product' },
                query: { type: 'add', est_no: record.id }
            });
        },
        //---to service--
        toService(record) {
            console.log(record, 'to service');
        },
        //---print record---
        printRecord(record) {
            this.$router.push({ name: 'estimate-preview', query: { type: 'estimation', est_no: record.id, back: 'home' } });
        },
        //------Open filter---
        //---filter--- 
        toggleFilterSelected(option) {
            // console.log(option, 'GGGGGGGGGGGGGGG');
            this.selectedByValue = option;
            this.lead_filter = true;
            this.data = this.originalData;
            this.showFilterOptions = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //---filter dropdown
        toggleFilter() {
            this.showFilterOptions = !this.showFilterOptions;
        },
        //--close lead
        //--close lead
        closeLeadFilter(searchData, bySerial, search) {
            if (searchData) {
                // const keysData = Object.keys(searchData);
                this.filteredBy = searchData;
                this.open_loader = true;
                this.getFilterData(1, this.recordsPerPage);

            }
            this.lead_filter = false;
        },
        //--get formayt date
        formatDate(dateString) {
            // Create a new Date object using the components in the correct order (year, month - 1, day)
            const date = new Date(dateString);
            // Get the parts of the date (year, month, day) in the correct format
            const formattedYear = date.getFullYear();
            const formattedMonth = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-indexed
            const formattedDay = String(date.getDate()).padStart(2, '0');
            // Concatenate the parts with dashes to form the desired format
            return `${formattedMonth}/${formattedDay}/${formattedYear}`;
        },
        //--hidden dropdown--
        hiddenDropdown() {
            if (!this.mouseOverIsNot) {
                this.showFilterOptions = false;
            }
        },
        mouseOverOption() {
            this.mouseOverIsNot = true;
        },
        mouseLeaveOption() {
            this.mouseOverIsNot = false;
        },
        //---refresh--
        refreshDataTable() {
            this.resetTheFilter();
        },
        //---reset filter--
        resetTheFilter() {
            this.filteredBy = {};
            this.customer_id = null;
            this.resetSearch = true;
            this.currentPage = 1;
            this.getEstimationList(this.recordsPerPage, 1);
        },
        getEstimationList(per_page, page, is_delete) {
            if (page == 1) {
                this.fetchEstimationsList({ page, per_page: this.isMobile ? 20 : per_page, is_delete });
                if (this.currentEstimationsList && this.currentEstimationsList.data && !is_delete) {
                    this.data = this.currentEstimationsList.data;
                    this.pagination = this.currentEstimationsList.pagination;
                }
            } else {
                this.open_skeleton = true;
                axios.get('/estimations', { params: { company_id: this.companyId, per_page: per_page, page: page } })
                    .then(response => {
                        // console.log(response.data, 'estimation by list..!');
                        this.open_skeleton = false;
                        this.data = response.data.data;
                        this.pagination = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        visiblePageNumbers() {
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            if (Object.keys(this.filteredBy).length === 0) {
                axios.get('/estimations', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                    .then(response => {
                        // console.log(response.data, 'Status Data');
                        if (response.data) {
                            this.pagination = response.data.pagination;
                            this.data = [...this.data, ...response.data.data];
                        }
                        this.open_skeleton_isMobile = false;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            } else {
                this.getFilterData(page, this.recordsPerPage);
            }
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---validate the role
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        //---navigate--
        navigatToInvoice(index) {
            if (index >= 0 && this.data && this.data[index] && this.data[index].status) {
                if (this.data[index].status == 1 && this.data[index]['sale'] && this.data[index]['sale'].id) {
                    this.$router.push({ name: 'print-preview', query: { type: 'sales_home', invoice_no: this.data[index]['sale'].id } });
                } else if (this.data[index].status == 2 && this.data[index]['Proforma'] && this.data[index]['Proforma'].id) {
                    this.$router.push({ name: 'proforma-preview', query: { type: 'proforma', proforma_no: this.data[index]['Proforma'].id, back: 'home' } });
                }
            }
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'estimate' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'estimate', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'estimate' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'estimate', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        //----table action----
        showModal(index, event) {
            if (index) {
                this.lastMousePosition = { x: event.layerX * 1, y: event.layerY * 1 };
                // Start a timer to check after 5000ms
                this.hoverTimer = setTimeout(() => {
                    if (this.isMouseInSamePosition(event)) {
                        this.modalData = index;
                        this.modalPosition = { x: ((event.layerX * 1) + 25), y: (event.layerY * 1) + 25 };
                        this.isModalVisible = true;
                    }
                }, 200);
            }
        },
        hideModal() {
            clearTimeout(this.hoverTimer); // Clear any existing timer
            setTimeout(() => {
                if (!this.isHoveringModal) {
                    this.isModalVisible = false;
                }
            }, 200);
        },
        handleMouseOver(record, field, event) {
            let dataToShow;
            if (record['status'] == "1") {
                dataToShow = {
                    invoice_id: record['invoice_id'],
                    sale_id: record['inv_id']
                };
            } else if (record['status'] == "2") {
                dataToShow = {
                    proforma_num: record['invoice_id'],
                    id: record['proforma_id']
                };
            } else {
                dataToShow = record[field];
            }

            this.showModal(dataToShow, event);
        },
        toggleHoverModal(status) {
            this.isHoveringModal = status;
            if (!status) {
                this.hideModal();
            }
        },
        isMouseInSamePosition(event) {
            // Allow for a 10-pixel tolerance in mouse movement
            const xDiff = Math.abs(this.lastMousePosition.x - event.layerX);
            const yDiff = Math.abs(this.lastMousePosition.y - event.layerY);
            return xDiff <= this.tolerance && yDiff <= this.tolerance;
        },
        //---modal is updated---
        closeAllModals() {
            this.open_confirmBox = false;
            this.open_message = false;
            this.showEstModal = false;
            this.lead_filter = false;
        },
        //--filter dropdown---
        toggleMainDropdown() {
            this.isMainDropdownOpen = !this.isMainDropdownOpen;
            if (this.isMainDropdownOpen) {
                document.addEventListener('click', this.handleClickOutsideFilter);
            } else {
                // this.showSubDropdown = false;
                document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },

        handleClickOutsideFilter(event) {
            const mainDropdown = this.$refs.dropdownContainerFilter;
            // console.log(event.target, 'Waht happning the data....', mainDropdown.contains(event.target));
            if (mainDropdown && !mainDropdown.contains(event.target)) {
                this.toggleMainDropdown();
                // document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        //--filter option--
        changeFilter(opt) {
            this.filter_option = opt;
            this.lead_filter = true;
            this.selectedByValue = opt;
            this.toggleMainDropdown();
            if (opt === 'date') {
                this.filter_date = !this.filter_date;
                if (!this.filter_date) {
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutsideDate);
                } else {
                    document.addEventListener('click', this.handleClickOutsideDate);
                }
            }
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                this.customer_id = selectedData.id;
                this.filteredBy = { first_name: selectedData.first_name, contact_number: selectedData.contact_number };
                this.getFilterData(1, this.isMobile ? 20 : this.recordsPerPage);
            }
        },
        resetToSearch() {
            this.customer_id = '';
            this.filteredBy = {};
            if (!this.resetSearch) {
                this.getEstimationList(this.recordsPerPage, 1);
            }
            this.resetSearch = false;
        },
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //--filter data---
        getFilterData(page, per_page) {
            this.open_loader = true;
            let send_data = {
                from_date: this.filteredBy.from,
                to_date: this.filteredBy.to,
                type: 'estimation', q: 'search',
                per_page: per_page, page: page, customer_id: this.customer_id ? this.customer_id : '',
                company_id: this.companyId
            };
            axios.get('/estimations', { params: { ...send_data } })
                .then(response => {
                    this.open_loader = false;
                    if (this.isMobile) {
                        if (page === 1) {
                            this.data = response.data.data;
                        } else {
                            this.data = [...this.data, ...response.data.data];
                        }
                        this.open_skeleton_isMobile = false;
                    } else {
                        this.data = response.data.data;
                    }
                    this.pagination = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                    this.open_skeleton_isMobile = false;
                })
        },
        //---on key press to make pagination--
        handleKeyPress(event) {
            if (event.key === "ArrowRight" && this.currentPage < this.pagination.last_page) {
                this.currentPage++;
            } else if (event.key === "ArrowLeft" && this.currentPage > 1) {
                this.currentPage--;
            }
        },
        //---clone estimation---
        cloneEstimation(record) {
            if (record) {
                this.updateEstimation(record);
                this.$router.push({
                    name: 'addEstimation',
                    params: { type: 'product' },
                    query: { type: 'add', clone: record.id }
                });

            }
        },
        //--cancell===
        //---sales cancel confirmation---
        openConfirmSalesCancel(record_data) {
            this.cancelrecords_data = record_data;
            this.estimation_cancel_confirmation = true;
        },
        closeSalesConfirm() {
            this.estimation_cancel_confirmation = false;
        },
        confirmSalesCancel(reason_data) {
            if (this.cancelrecords_data) {
                if (this.cancelrecords_data && this.cancelrecords_data.id) {
                    axios.put(`/estimations/${this.cancelrecords_data.id}`, { company_id: this.companyId, reason: reason_data.reason, status: '3' })
                        .then(response => {
                            this.updateKeyWithTime('estimation_update');
                            // console.log(response.data, 'estimation cancel request..!');
                            this.getEstimationList(this.recordsPerPage, this.currentPage, true);
                            this.message = response.data.message;
                            this.open_message = true;
                            this.cancelrecords_data = null;
                        })
                        .catch(error => {
                            console.error('Error', error);
                        })
                }
            }
            this.estimation_cancel_confirmation = false;
        },
    },
    watch: {
        originalData(newValue) {
            // console.log(newValue, 'EEEAEA');
            // Automatically send data to the parent when dataToSend changes
            this.$emit('dataToParent', newValue);
        },
        searchedData(newValue) {
            // console.log(newValue, 'RRRRR');
            if (!this.isEmptyObject(newValue)) {
                this.data = [{ ...newValue }];
            }
            else {
                this.data = this.originalData;
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    if (Object.keys(this.filteredBy).length === 0) {
                        this.getEstimationList(newValue, 1);
                    } else {
                        this.getFilterData(1, newValue);
                    }
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                if (Object.keys(this.filteredBy).length === 0) {
                    this.getEstimationList(this.recordsPerPage, newValue);
                } else {
                    this.getFilterData(newValue, this.recordsPerPage);
                }
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('estimation_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentEstimationsList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.open_skeleton = false;
                    if (newValue.data) {
                        this.data = newValue.data;
                    }
                    if (newValue.pagination) {
                        this.pagination = newValue.pagination;
                    }
                    this.open_loader = false;
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchEstimationsList({ page: 1, per_page: this.isMobile ? 20 : this.recordsPerPage });
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    this.is_filter = false;
                    // console.log(newValue, 'New Value data...............');
                }
            }
        },
        //---modals data---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.closeAllModals();
                }
            }
        },
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        }, open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        }, showEstModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        }, lead_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    },
}
</script>

<style scoped>
.manualStyle {
    overflow: auto;
    height: 100vh;
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
    z-index: 100;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}
</style>
