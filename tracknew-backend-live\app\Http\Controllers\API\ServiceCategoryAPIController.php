<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateServiceCategoryAPIRequest;
use App\Http\Requests\API\UpdateServiceCategoryAPIRequest;
use App\Models\ServiceCategory;
use App\Models\Services;
use App\Repositories\ServiceCategoryRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class ServiceCategoryController
 * @package App\Http\Controllers\API
 */

class ServiceCategoryAPIController extends AppBaseController
{
    /** @var  ServiceCategoryRepository */
    private $serviceCategoryRepository;

    public function __construct(ServiceCategoryRepository $serviceCategoryRepo)
    {
        $this->serviceCategoryRepository = $serviceCategoryRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/service_categories",
     *      summary="getServiceCategoryList",
     *      tags={"ServiceCategory"},
     *      description="Get all ServiceCategories",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/ServiceCategory")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
 public function index(Request $request)
    {

        $companyId = $request->query('company_id');
        
        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $servicesQuery = ServiceCategory::withCount('services')->where('company_id', $companyId)
        ;

        if ($perPage === 'all') {
            $perPage = $servicesQuery->count();
        }
       
        
        $services = $servicesQuery->orderBy('service_category', 'asc')->paginate($perPage, ['*'], 'page', $page);       

        $response = [
            'success' => true,
            'data' => $services->items(),//$sales->items(),LeadResource::collection($sales), // Get the paginated items
            'pagination' => [
                'total' => $services->total(),
                'per_page' => $services->perPage(),
                'current_page' => $services->currentPage(),
                'last_page' => $services->lastPage(),
                'from' => $services->firstItem(),
                'to' => $services->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }


    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/service_categories",
     *      summary="createServiceCategory",
     *      tags={"ServiceCategory"},
     *      description="Create ServiceCategory",
*      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/ServiceCategory")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ServiceCategory"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
      public function store(CreateServiceCategoryAPIRequest $request)
      {
          $input = $request->all();

          // Ensure 'service_categories' is an array
          if (!isset($input['service_categories']) || !is_array($input['service_categories'])) {
              return $this->sendError('Invalid input format. Expected an array of service categories.');
          }

          $errors = [];
          $savedCategories = [];

          foreach ($input['service_categories'] as $categoryInput) {
              // Check if the service category already exists for the company
              $existingServiceCategory = ServiceCategory::where([
                  ['service_category', '=', $categoryInput['service_category']],
                  ['company_id', '=', $categoryInput['company_id']]
              ])->first();

              if ($existingServiceCategory) {
                  $errors[] = $categoryInput['service_category'];
              } else {
                  // If the service category does not exist, proceed with creation
                  $serviceCategory = $this->serviceCategoryRepository->create($categoryInput);
                  $savedCategories[] = $serviceCategory->toArray();
              }
          }

          if (!empty($errors)) {
            $responseData = [
                'error' => $errors,
                'message' => 'Some service categories could not be saved'
            ];
             // return $this->sendError('Some service categories could not be saved',  $errors);
            return response()->json($responseData);
          }
        
        	
        $response = [
                'data' => $savedCategories,
                'message' => 'Service Categories saved successfully'
            ];

          return response()->json($response);
      }



    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/service_categories/{id}",
     *      summary="getServiceCategoryItem",
     *      tags={"ServiceCategory"},
     *      description="Get ServiceCategory",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ServiceCategory",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ServiceCategory"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var ServiceCategory $serviceCategory */
        $serviceCategory = $this->serviceCategoryRepository->find($id);

        if (empty($serviceCategory)) {
            return $this->sendError('Service Category not found');
        }

        return $this->sendResponse($serviceCategory->toArray(), 'Service Category retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/service_categories/{id}",
     *      summary="updateServiceCategory",
     *      tags={"ServiceCategory"},
     *      description="Update ServiceCategory",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ServiceCategory",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
*      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/ServiceCategory")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ServiceCategory"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
      public function update($id, UpdateServiceCategoryAPIRequest $request)
      {
          $input = $request->all();

          /** @var ServiceCategory $serviceCategory */
          $serviceCategory = $this->serviceCategoryRepository->find($id);

          if (empty($serviceCategory)) {
              return $this->sendError('Service Category not found');
          }          
        
          $existingServiceCategory = ServiceCategory::where([
              ['service_category', '=', $input['service_category']],
              ['company_id', '=', $input['company_id']],
               ['id', '!=', $id]
          ])->first();

          if ($existingServiceCategory) {
              return $this->sendError($input['service_category'].' Service Category already exists');
          }

          $serviceCategory = $this->serviceCategoryRepository->update($input, $id);

          return $this->sendResponse($serviceCategory->toArray(), 'Service Category updated successfully');
      }


    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/service_categories/{id}",
     *      summary="deleteServiceCategory",
     *      tags={"ServiceCategory"},
     *      description="Delete ServiceCategory",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ServiceCategory",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var ServiceCategory $serviceCategory */
        $serviceCategory = $this->serviceCategoryRepository->find($id);

        if (empty($serviceCategory)) {
            return $this->sendError('Service Category not found');
        }

        // Check if the service category is used in the Services table
        $serviceCount = Services::where('servicecategory_id', $serviceCategory->id)->count();

        if ($serviceCount > 0) {
            return $this->sendError('Please delete all associated services before deleting this category.');
        }

        $serviceCategory->delete();

        return $this->sendSuccess('Service Category deleted successfully');
    }

}
