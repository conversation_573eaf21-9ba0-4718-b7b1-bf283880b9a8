<template>
    <div v-if="service" class="label-print-area overflow-auto">
        <!-- Preview Section -->
        <div class="mb-6">
            <h3 class="font-medium mb-2">Print Preview</h3>
            <div class="border border-gray-300 p-4 rounded-lg bg-gray-50">
                <!-- Preview Container -->
                <div class="preview-container" :style="previewContainerStyles">
                    <div v-for="(row, rowIndex) in previewRows" :key="rowIndex" class="preview-row"
                        :style="previewRowStyles">
                        <div v-for="(label, labelIndex) in row" :key="labelIndex"
                            :class="{ 'grid grid-cols-2 gap-1': selectedCode !== 'barcode', 'preview-label': selectedCode == 'barcode' }"
                            :style="previewLabelStyles">
                            <div
                                :style="selectedCode !== 'barcode' ? { display: 'flex' } : { display: 'flex', flexDirection: 'column' }">
                                <div>
                                    <!-- QR Code Option -->
                                    <div v-if="selectedCode === 'qr' && qrCodeUrl"
                                        class="flex justify-center items-center" :style="qrCodeStyles">
                                        <img :src="qrCodeUrl" :alt="service.code" class="mb-0" />
                                    </div>

                                    <!-- Barcode Option -->
                                    <div v-if="selectedCode === 'barcode'"
                                        class="barcode-container mb-0 flex justify-center items-center"
                                        :style="barcodeStyles">
                                        <svg :style="{ height: '400px' }" :id="barcodeId" class="barcodeId"
                                            :alt="service.code"></svg>
                                    </div>
                                    <div class="text-left" :class="{ 'mt-2': selectedCode === 'barcode' }"
                                        :style="fontStyles">
                                        <p v-if="selectedCode !== 'barcode'" class="font-mono">{{ service.code }}</p>
                                    </div>
                                </div>
                                <div class="text-left" :style="fontStyles">
                                    <!-- <p v-if="selectedCode !== 'barcode'" class="font-mono">{{ service.code }}</p> -->
                                    <div>
                                        <div v-for="(part, index) in customerParts" :key="index">{{ part }}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Label Configuration -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Label Dimensions -->
            <div class="space-y-4">
                <h3 class="font-medium">Label Dimensions (mm)</h3>
                <div class="flex items-center">
                    <label class="w-32">Width:</label>
                    <input type="number" v-model="labelWidth" class="border px-2 py-1 w-20" min="10"
                        @input="updatePreview" />
                </div>
                <div class="flex items-center">
                    <label class="w-32">Height:</label>
                    <input type="number" v-model="labelHeight" class="border px-2 py-1 w-20" min="10"
                        @input="updatePreview" />
                </div>
                <div class="pt-4"> <!-- Added padding top to separate from dimensions -->
                    <h3 class="font-medium mb-2">Print Mode</h3>
                    <div class="flex flex-col space-y-2"> <!-- Changed to column layout -->
                        <label class="flex items-center">
                            <input type="radio" v-model="printMode" value="individual" class="mr-2">
                            Individual Labels (one per page)
                        </label>
                        <label class="flex items-center">
                            <input type="radio" v-model="printMode" value="continuous" class="mr-2">
                            Continuous Sheet
                        </label>
                    </div>
                </div>
            </div>


            <!-- Print Layout -->
            <div class="space-y-4">
                <h3 class="font-medium">Print Layout</h3>
                <div class="flex items-center">
                    <label class="w-32">Total Labels:</label>
                    <input type="number" v-model="labelCount" class="border px-2 py-1 w-20" min="1"
                        @input="updatePreview" />
                </div>
                <div class="flex items-center">
                    <label class="w-32">Labels per Row:</label>
                    <input type="number" v-model="labelsPerRow" class="border px-2 py-1 w-20" min="1"
                        @input="updatePreview" />
                </div>
                <div class="flex items-center">
                    <label class="w-32">Gap Between (mm):</label>
                    <input type="number" v-model="labelGap" class="border px-2 py-1 w-20" min="0"
                        @input="updatePreview" />
                </div>
            </div>
        </div>

        <!-- Print Button -->
        <div class="mt-6 text-center">
            <button @click="printLabel" class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                Print {{ labelCount }} Label{{ labelCount > 1 ? 's' : '' }}
                ({{ rowsNeeded }} Row{{ rowsNeeded > 1 ? 's' : '' }})
            </button>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watch } from 'vue';
import QRCode from 'qrcode';
import JsBarcode from 'jsbarcode';

const props = defineProps({
    service: Object,
    labelSize: {
        type: String,
        default: '4x6'
    },
    selectedCode: {
        type: String,
        default: 'barcode'
    },
    label_count: {
        type: Number,
        default: 2,
    }
});

// Label configuration
let labelWidth = ref(50);
let labelHeight = ref(24);
let labelCount = ref(2); // Default to 4 for better preview
let labelsPerRow = ref(2);
let labelGap = ref(2); // Default gap between labels in mm
let printMode = ref('continuous'); // 'continuous' or 'individual'
let pageMargin = ref(2);

// Calculate how many rows will be needed
const rowsNeeded = computed(() => {
    return Math.ceil(labelCount.value / labelsPerRow.value);
});

// Generate preview rows data
const previewRows = computed(() => {
    const rows = [];
    for (let i = 0; i < rowsNeeded.value; i++) {
        const start = i * labelsPerRow.value;
        const end = start + labelsPerRow.value;
        rows.push(Array.from({ length: Math.min(labelsPerRow.value, labelCount.value - start) }, (_, index) => start + index + 1));
    }
    return rows;
});

// Preview container styles
const previewContainerStyles = computed(() => {
    return {
        width: '100%',
        maxWidth: `${(labelWidth.value * labelsPerRow.value) + (labelGap.value * (labelsPerRow.value - 1))}mm`,
        margin: '0 auto',
        backgroundColor: '#f9fafb', // Light gray background for preview area
        padding: '10px',
        borderRadius: '4px',
        border: '1px dashed #d1d5db' // Dashed border to indicate preview area
    };
});

// Preview row styles
const previewRowStyles = computed(() => {
    return {
        display: 'flex',
        justifyContent: 'center',
        gap: `${labelGap.value}mm`,
        marginBottom: `${labelGap.value}mm`,
        ':last-child': {
            marginBottom: '0'
        }
    };
});

// Preview label styles
const previewLabelStyles = computed(() => {
    return {
        width: `${labelWidth.value}mm`,
        height: `${labelHeight.value}mm`,
        backgroundColor: 'white',
        border: '1px solid #e5e7eb',
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'start',
        alignItems: 'start',
        padding: '2mm',
        boxSizing: 'border-box'
    };
});

const qrSize = computed(() => {
    const sizes = {
        '2x4': 100,
        '4x6': 150,
        '3x3': 120
    };
    return sizes[props.labelSize];
});

const qrCodeUrl = ref(null);
const barcodeId = "barcode-svg-" + Math.random().toString(36).substring(2);
const labelToPrint = ref(null);

// Update preview when any config changes
const updatePreview = () => {
    // Regenerate barcode if needed
    if (props.selectedCode === 'barcode' && props.service?.code) {
        nextTick(() => {
            generateBarcode(props.service.code);
        });
    }
};

// Watch for changes in selectedCode
watch(() => props.selectedCode, (newValue) => {
    if (newValue === 'qr') {
        generateQRCode(props.service.code);
    } else if (newValue === 'barcode') {
        nextTick(() => {
            generateBarcode(props.service.code);
        });
    }
}, { immediate: true });
// Watch for changes in label_count
watch(() => props.label_count, (newValue) => {
    // Adjust layout for 2 labels per row
    if (newValue == 2) {
        labelWidth.value = 50;  // Set label width for 2 labels per row
        labelHeight.value = 24; // Adjust height for 2 labels per row
        labelCount.value = 2;   // Number of labels to print
        labelsPerRow.value = 2; // Number of labels per row
        labelGap.value = 2;     // Gap between labels
    }
    // Adjust layout for 3 labels per row
    else if (newValue == 3) {
        labelWidth.value = 35;  // Set label width for 3 labels per row
        labelHeight.value = 21; // Adjust height for 3 labels per row
        labelCount.value = 3;   // Number of labels to print
        labelsPerRow.value = 3; // Number of labels per row
        labelGap.value = 2;     // Gap between labels
    }
}, { immediate: true });

onMounted(() => {
    if (props.service && props.service.code) {
        generateQRCode(props.service.code);
        nextTick(() => {
            generateBarcode(props.service.code);
        });
    }
});

const generateQRCode = (code) => {
    QRCode.toDataURL(code, { width: qrSize.value, margin: 1 })
        .then(url => {
            qrCodeUrl.value = url;
        })
        .catch(err => {
            console.error("Error generating QR code:", err);
        });
};

const generateBarcode = (code) => {
    const barcodeElement = document.getElementById(barcodeId);
    barcodeId
    if (barcodeElement) {
        const scaleFactor = labelWidth.value / 50;
        const barcodeHeight = scaleFactor * 30;

        JsBarcode(barcodeElement, code, {
            format: "CODE128",
            displayValue: false,
            //     fontSize: 14,
            //     width: 2,
            //     height: barcodeHeight,
            width: 1.2, // Optimal for 35mm labels
            height: labelHeight.value * 1, // 60% of label height
            margin: 5, // Padding around barcode
            fontSize: 0 // No text under barcode
        });
    }
    // Get all elements with the barcode container class
    const barcodeElements = document.getElementsByClassName('barcode-container');

    // Loop through all found elements
    Array.from(barcodeElements).forEach((element) => {
        // Create SVG element if it doesn't exist
        let svg = element.querySelector('svg');
        if (!svg) {
            svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
            element.appendChild(svg);
        }

        // Calculate dimensions based on label width
        const scaleFactor = labelWidth.value / 50;
        const barcodeHeight = scaleFactor * 30;

        // Generate barcode
        JsBarcode(svg, code, {
            format: "CODE128",
            displayValue: true,
            fontSize: 14,
            width: 2,
            height: barcodeHeight,
        });
    });
};

const qrCodeStyles = computed(() => {
    const scale = labelWidth.value / 50;
    return {
        width: `${scale * 50}px`,
        height: `${scale * 50}px`,
    };
});

const barcodeStyles = computed(() => {
    const scale = labelWidth.value / 50;
    return {
        width: `${scale * 150}px`,
        height: `${scale * 30}px`,
    };
});
const fontStyles = computed(() => {
    const printWidth = labelWidth.value - 2;
    const printHeight = labelHeight.value;
    const fontSize = Math.min(printWidth, printHeight) * 0.3;
    return {
        fontSize: `${fontSize}px`,
        marginTop: `${Math.min(printWidth, printHeight) * 0.3}px`,
        height: '50%',
    };
});
const customerParts = computed(() => {
    return props.service.customer.split(' - ').filter(part => part.trim());
});

// const printLabel = () => {
//     // Validate inputs
//     if (labelCount.value < 1) {
//         alert('Please enter a valid number of labels (at least 1)');
//         return;
//     }
//     if (labelsPerRow.value < 1) {
//         alert('Please enter a valid number of labels per row (at least 1)');
//         return;
//     }
//     if (labelGap.value < 0) {
//         alert('Gap between labels cannot be negative');
//         return;
//     }

//     const printWidth = labelWidth.value - 2;
//     const printHeight = labelHeight.value;
//     const codeHeightPercentage = 65;
//     const textHeightPercentage = 35;
//     const fontSize = Math.min(printWidth, printHeight) * 0.3;

//     // Get barcode HTML if needed
//     let barcodeHTML = '';
//     if (props.selectedCode === 'barcode') {
//         const previewBarcode = document.querySelector('.preview-label .barcode-container svg');
//         if (previewBarcode) {
//             barcodeHTML = previewBarcode.outerHTML;
//             previewBarcode.setAttribute('height', `${labelHeight.value}mm`);
//         }
//     }

//     // Create a single label template
//     const createLabel = () => {
//         let codeElement;
//         if (props.selectedCode === 'qr' && qrCodeUrl.value) {
//             codeElement = `
//                 <div style="
//                     width: 100%;
//                     height: ${codeHeightPercentage}%;
//                     display: flex;
//                     justify-content: center;
//                     align-items: center;
//                     padding: 2mm;
//                 ">
//                     <img src="${qrCodeUrl.value}" 
//                          alt="${props.service.code}" 
//                          style="
//                             max-width: 100%;
//                             max-height: 100%;
//                             object-fit: contain;
//                          ">
//                 </div>
//             `;
//         } else {
//             codeElement = `
//                 <div style="
//                     width: 100%;
//                     height: ${codeHeightPercentage}%;
//                     display: flex;
//                     justify-content: center;
//                     align-items: center;
//                     padding: 1mm;
//                 ">
//                    ${barcodeHTML}
//                 </div>
//             `;
//         }

//         const customerInfoElement = `
//             <div style="
//                 width: 100%;
//                 height: ${textHeightPercentage}%;
//                 display: flex;
//                 flex-direction: column;
//                 justify-content: center;
//                 align-items: flex-start; 
//         text-align: left;
//         padding: 0 2mm;
//         box-sizing: border-box;
//             ">
//             ${props.selectedCode === 'qr' ?
//                 `<div style="
//                     font-size: ${fontSize}pt;
//                     font-family: Arial, sans-serif;
//                     font-weight: bold;
//                     margin-bottom: 1mm;
//             width: 100%;
//                 ">${props.service.code}</div>` : ''}
//                 <div style="
//                     font-size: ${fontSize}pt;
//                     font-family: Arial, sans-serif;  width: 100%;
//                 "> ${props.service.customer.split(' - ').map(part => 
//             `<div style="width: 100%;">${part}</div>`
//         ).join('')}
//         </div>
//             </div>
//         `;

//         return `
//             <div style="
//                 width: ${printWidth}mm;
//                 height: ${printHeight}mm;
//                 display: flex;
//                 flex-direction: column;
//                 box-sizing: border-box;
//                 ${printMode.value === 'individual' ? 'page-break-after: always;' : ''}
//                 ${printMode.value === 'continuous' ? 'margin-right: ' + labelGap.value + 'mm;' : ''}
//             ">
//                 ${codeElement}
//                 ${customerInfoElement}
//             </div>
//         `;
//     };
//     // Create all labels
//     let labelsHTML = '';
//     if (printMode.value === 'individual') {
//         // One label per page
//         for (let i = 0; i < labelCount.value; i++) {
//             labelsHTML += createLabel();
//         }
//     } else {
//         // Continuous printing with grid layout
//         const rows = Math.ceil(labelCount.value / labelsPerRow.value);

//         // Calculate total width needed for the grid
//         const gridWidth = (labelWidth.value * labelsPerRow.value) +
//             (labelGap.value * (labelsPerRow.value - 1));

//         labelsHTML += `<div style="
//         display: grid;
//         grid-template-columns: repeat(${labelsPerRow.value}, ${labelWidth.value}mm);
//         gap: ${labelGap.value}mm;
//         width: ${gridWidth}mm;
//         margin: 0 auto;
//     ">`;

//         for (let i = 0; i < labelCount.value; i++) {
//             labelsHTML += `<div style="
//             width: ${labelWidth.value}mm;
//             height: ${labelHeight.value}mm;
//             page-break-inside: avoid;
//         ">${createLabel(i)}</div>`;
//         }

//         // Fill empty grid cells if needed
//         const emptyCells = (labelsPerRow.value - (labelCount.value % labelsPerRow.value)) % labelsPerRow.value;
//         for (let i = 0; i < emptyCells; i++) {
//             labelsHTML += `<div style="width: ${labelWidth.value}mm;"></div>`;
//         }

//         labelsHTML += `</div>`;
//     }


//     // Calculate page width needed for the labels
//     const pageWidthNeeded = printMode.value === 'individual'
//         ? printWidth
//         : (printWidth * labelsPerRow.value) + (labelGap.value * (labelsPerRow.value - 1));

//     // Open print window
//     const printWindow = window.open('', '_blank');
//     printWindow.document.write('<html><head><title>Print Labels</title>');
//     printWindow.document.write('<style>');

//     // Print-specific styles
//     printWindow.document.write(`
//         @page {
//             size: ${printWidth}mm ${printHeight}mm;
//             margin: 0;
//         }

//         body {
//             margin: 0;
//             padding: 0;
//             width: ${pageWidthNeeded}mm;
//             font-family: Arial, sans-serif;
//         }

//         .labels-container {
//             width: 100%;
//             display: flex;
//             flex-direction: column;
//             align-items: center;
//         }

//         .label-row {
//             display: flex;
//             justify-content: center;
//             width: 100%;
//         }

//         @media print {
//             body {
//                 padding: 0;
//                 width: auto;
//             }

//             .label-row {
//                 page-break-inside: avoid;
//             }
//         }
//     `);

//     printWindow.document.write('</style>');
//     printWindow.document.write('</head><body>');
//     printWindow.document.write(`<div class="labels-container">${labelsHTML}</div>`);
//     printWindow.document.write('</body></html>');
//     printWindow.document.close();

//     // Wait for content to load before printing
//     printWindow.onload = function () {
//         setTimeout(() => {
//             printWindow.print();
//             printWindow.close();
//         }, 500);
//     };
// };
const printLabel = () => {
    // Validate inputs (same as before)
    if (labelCount.value < 1) {
        alert('Please enter a valid number of labels (at least 1)');
        return;
    }
    if (labelsPerRow.value < 1) {
        alert('Please enter a valid number of labels per row (at least 1)');
        return;
    }
    if (labelGap.value < 0) {
        alert('Gap between labels cannot be negative');
        return;
    }

    const printWidth = labelWidth.value - 2;
    const printHeight = labelHeight.value - 2;
    const fontSize = Math.min(printWidth, printHeight) * 0.3;

    // Get barcode HTML if needed (same as before)
    let barcodeHTML = '';
    if (props.selectedCode === 'barcode') {
        const previewBarcode = document.querySelector('.preview-label .barcode-container svg');
        if (previewBarcode) {
            barcodeHTML = previewBarcode.outerHTML;
            previewBarcode.setAttribute('height', `${labelHeight.value}mm`);
        }
    }

    // Create a single label template
    const createLabel = () => {
        if (props.selectedCode === 'qr' && qrCodeUrl.value) {
            // Split customer info into parts (name, phone, etc.)
            const customerParts = props.service.customer.split(' - ');
            const customerName = customerParts[0] || '';
            const customerContact = customerParts.slice(1).join(' - ') || '';

            return `
                <div style="
                    width: ${printWidth}mm;
                    height: ${printHeight}mm;
                    display: grid;
                    grid-template-columns: 40% 60%;
                    grid-template-rows: 100%;
                    gap: 1mm;
                    box-sizing: border-box;
                    ${printMode.value === 'individual' ? 'page-break-after: always;' : ''}
                    ${printMode.value === 'continuous' ? 'margin-right: ' + labelGap.value + 'mm;' : ''}
                    padding: 1mm;
                ">
                    <!-- QR Code Section -->
                    <div style="
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: center;
                        height: 100%;
                    ">
                        <img src="${qrCodeUrl.value}" 
                             alt="${props.service.code}" 
                             style="
                                max-width: 100%;
                                max-height: 70%;
                                object-fit: contain;
                             ">
                        <div style="
                            font-size: ${fontSize}pt;
                            font-family: Arial, sans-serif;
                            font-weight: bold;
                            margin-top: 1mm;
                            text-align: center;
                            word-break: break-all;
                        ">${props.service.code}</div>
                    </div>
                    
                    <!-- Customer Info Section -->
                    <div style="
                        display: flex;
                        flex-direction: column;
                        justify-content: center;
                        align-items: flex-start;
                        height: 100%;
                        overflow: hidden;
                    ">
                        <div style="
                            font-size: ${fontSize}pt;
                            font-family: Arial, sans-serif;
                            font-weight: normal;
                            margin-bottom: 2mm;
                            word-break: break-word;
                            width: 100%;
                        ">${customerName}</div>
                        
                        <div style="
                            font-size: ${fontSize * 0.9}pt;
                            font-family: Arial, sans-serif;
                            word-break: break-word;
                            width: 100%;
                        ">${customerContact}</div>
                    </div>
                </div>
            `;
        } else {
            // Original barcode layout
            const codeHeightPercentage = 65;
            const textHeightPercentage = 35;

            let codeElement = `
                <div style="
                    width: 100%;
                    height: ${codeHeightPercentage}%;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    padding: 1mm;
                ">
                   ${barcodeHTML}
                </div>
            `;

            const customerInfoElement = `
                <div style="
                    width: 100%;
                    height: ${textHeightPercentage}%;
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    align-items: flex-start; 
                    text-align: left;
                    padding: 0 2mm;
                    box-sizing: border-box;
                ">
                    <div style="
                        font-size: ${fontSize}pt;
                        font-family: Arial, sans-serif;
                        width: 100%;
                    "> ${props.service.customer.split(' - ').map(part =>
                `<div style="width: 100%;">${part}</div>`
            ).join('')}
                    </div>
                </div>
            `;

            return `
                <div style="
                    width: ${printWidth}mm;
                    height: ${printHeight}mm;
                    display: flex;
                    flex-direction: column;
                    box-sizing: border-box;
                    ${printMode.value === 'individual' ? 'page-break-after: always;' : ''}
                    ${printMode.value === 'continuous' ? 'margin-right: ' + labelGap.value + 'mm;' : ''}
                ">
                    ${codeElement}
                    ${customerInfoElement}
                </div>
            `;
        }
    };

    // Rest of the function remains the same (creating all labels and printing)
    let labelsHTML = '';
    if (printMode.value === 'individual') {
        // One label per page
        for (let i = 0; i < labelCount.value; i++) {
            labelsHTML += createLabel();
        }
    } else {
        // Continuous printing with grid layout
        const rows = Math.ceil(labelCount.value / labelsPerRow.value);
        const gridWidth = (labelWidth.value * labelsPerRow.value) +
            (labelGap.value * (labelsPerRow.value - 1));

        labelsHTML += `<div style="
            display: grid;
            grid-template-columns: repeat(${labelsPerRow.value}, ${labelWidth.value}mm);
            gap: ${labelGap.value}mm;
            width: ${gridWidth}mm;
            margin: 0 auto;
        ">`;

        for (let i = 0; i < labelCount.value; i++) {
            labelsHTML += `<div style="
                width: ${labelWidth.value}mm;
                height: ${labelHeight.value}mm;
                page-break-inside: avoid;
            ">${createLabel(i)}</div>`;
        }

        // Fill empty grid cells if needed
        const emptyCells = (labelsPerRow.value - (labelCount.value % labelsPerRow.value)) % labelsPerRow.value;
        for (let i = 0; i < emptyCells; i++) {
            labelsHTML += `<div style="width: ${labelWidth.value}mm;"></div>`;
        }

        labelsHTML += `</div>`;
    }

    // Calculate page width needed for the labels
    const pageWidthNeeded = printMode.value === 'individual'
        ? printWidth
        : (printWidth * labelsPerRow.value) + (labelGap.value * (labelsPerRow.value - 1));

    // Open print window
    const printWindow = window.open('', '_blank');
    printWindow.document.write('<html><head><title>Print Labels</title>');
    printWindow.document.write('<style>');

    // Print-specific styles
    printWindow.document.write(`
        @page {
            size: ${printWidth}mm ${printHeight}mm;
            margin: 0 !important;
            padding: 0 !important;
        }
        
        body {
            margin: 0 !important;
            padding: 0 !important;
            width: ${pageWidthNeeded}mm;
            font-family: Arial, sans-serif;
        }
        
        .labels-container {
            width: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        
        .label-row {
            display: flex;
            justify-content: center;
            width: 100%;
        }
        
        @media print {
            body {
                padding: 0;
                width: auto;
            }
            
            .label-row {
                page-break-inside: avoid;
            }
        }
    `);

    printWindow.document.write('</style>');
    printWindow.document.write('</head><body>');
    printWindow.document.write(`<div class="labels-container">${labelsHTML}</div>`);
    printWindow.document.write('</body></html>');
    printWindow.document.close();

    // Wait for content to load before printing
    printWindow.onload = function () {
        setTimeout(() => {
            printWindow.print();
            printWindow.close();
        }, 500);
    };
};
</script>

<style>
@media print {
    body * {
        visibility: hidden;
    }

    .labels-container,
    .labels-container * {
        visibility: visible;
    }

    .labels-container {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }

    @page {
        size: auto;
        margin: 10mm;
    }
}

.preview-container {
    overflow: auto;
    max-height: 400px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.preview-row {
    display: flex;
    justify-content: center;
}

.preview-label {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.text-left {
    text-align: left;
    width: 100%;
    padding: 0 4px;
}

.font-mono {
    font-family: monospace;
    width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .preview-container {
        transform: scale(0.8);
        transform-origin: top center;
    }
}
</style>