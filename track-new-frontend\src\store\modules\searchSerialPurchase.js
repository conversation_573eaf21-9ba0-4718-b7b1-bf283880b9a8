// store/modules/searchSerialPurchase.js
import axios from "axios";

const state = {
  purchase_list: {data: [],
    pagination: null
    },
  };

  const mutations = {
      SET_PURCHASELIST(state, { data, pagination}) {
        const currentData = state.purchase_list.data || [];
          if (pagination && pagination.current_page > 1) {
              // Append new data if the current page is greater than 1
              state.purchase_list = {
                  data: [...currentData, ...data], // Append new data
                  pagination: pagination
              };
          } else {
              // Replace data if it's the first page or no pagination
              state.purchase_list = {
                  data: data, // Replace with new data
                  pagination: pagination
              };
          }
    },
      RESET_STATE(state) {
        state.purchase_list = {
            data: [],
            pagination: null
          };
        },
  };

  const actions = {
    updatePurchaseName({ commit }, purchase_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update purchase_list name
      setTimeout(() => {
        // Commit mutation to update purchase_list name
        commit('SET_PURCHASELIST', purchase_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchPurchaseListSearch({ commit }, {page, per_page}) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get('/purchase_orders', { params: { company_id: company_id, page: page, per_page: per_page } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Purchase list..!', response.data.total_revenue, 'TTTTTTT',  response.data.total_sum);
              let { data, pagination} = response.data;                
                // console.log(data, 'data', pagination, 'pagination', status_counts, 'status', 'Initialllllly');
              commit('SET_PURCHASELIST', {data, pagination});
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    
  };

  const getters = {
    currentPurchaseListSearch(state) {
      return state.purchase_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
