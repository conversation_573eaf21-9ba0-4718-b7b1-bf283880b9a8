<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg shadow-lg border border-gray-300 relative w-full max-w-lg transform transition-transform ease-in-out duration-300 overflow-hidden flex flex-col items-center"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">

            <!-- Modal Header (Fixed at Top) -->
            <div class="py-5 rounded-t-lg relative text-white text-lg font-semibold w-full text-center"
                style="background-color: rgb(3, 171, 143);">
                SMS Credit Recharge
                <button @click="cancelModal" class="absolute top-3 right-4 text-white text-2xl">&times;</button>
            </div>

            <!-- Scrollable Modal Content -->
            <div class="p-6 py-4 w-full max-h-[80vh] overflow-y-auto">
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 w-full">
                    <div v-for="plan in smsPlans" :key="plan.id"
                        class="bg-white border border-gray-300 rounded-lg shadow-md p-4 flex flex-col items-center text-center transition duration-300 hover:shadow-xl hover:border-blue-400">

                        <!-- Price & Tax Section (Same Color as Header) -->
                        <div class="w-full text-white text-lg font-bold p-3 rounded-t-md flex flex-col items-center"
                            style="background-color: rgb(3, 171, 143);">
                            <div class="flex items-center">
                                <font-awesome-icon icon="fa-solid fa-indian-rupee-sign"
                                    class="px-1 text-white text-sm" />
                                {{ plan.price }}
                            </div>
                            <div class="text-sm text-white font-medium mt-1">(18% Tax Extra)</div>
                        </div>

                        <!-- Divider 
                        <div class="border-t border-gray-300 my-2 w-full"></div>-->

                        <!-- Plan Details -->
                        <ul class="space-y-1 text-gray-700 text-xs text-left w-full px-3 py-2">
                            <li class="flex items-center">
                                <font-awesome-icon icon="fa-solid fa-check-circle"
                                    class="text-green-500 mr-2 text-sm" />
                                <span>{{ plan.days }}</span>
                            </li>
                            <li v-for="feature in plan.features" :key="feature" class="flex items-center">
                                <font-awesome-icon icon="fa-solid fa-check-circle"
                                    class="text-green-500 mr-2 text-sm" />
                                <span>{{ feature }}</span>
                            </li>
                        </ul>

                        <!-- Recharge Button -->
                        <button @click="subscribePlan(plan.id)"
                            class="w-full text-white shadow-inner shadow-blue-100 border border-blue-400 bg-blue-600 px-3 py-2 rounded-b-md text-sm mt-3 hover:bg-blue-700 transition">
                            <font-awesome-icon icon="fa-solid fa-plus" class="px-1 text-xs" /> Recharge
                        </button>
                    </div>
                </div>

                <!-- Loading Indicator -->
                <div v-if="loading" class="mt-2 text-center font-medium animate-pulse text-sm text-gray-600">
                    Processing...</div>
            </div>
        </div>
    </div>
</template>

<script>
import axios from "axios";
export default {
    emits: ['close-modal'],
    name: "SmsRechargeModal",
    props: {
        showModal: Boolean,
    },
    data() {
        return {
            isOpen: false,
            smsPlans: [],
            loading: false,
            gatewayId: 1,
        };
    },
    methods: {
        cancelModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit("close-modal");
            }, 300);
        },
        async fetchSmsPlans() {
            try {
                const response = await axios.get('/sms-plans');
                this.smsPlans = response.data.data.map(plan => ({
                    id: plan.id,
                    price: plan.price,
                    days: plan.days === -1 ? 'Unlimited' : `${plan.days} Days`,
                    features: [
                        `Credits: ${plan.count}`,
                        plan.user_limit ? `User Limit: ${plan.user_limit}` : '',
                        plan.messages_limit ? `Messages Limit: ${plan.messages_limit}` : '',
                        plan.whatsapp_limit ? `WhatsApp Limit: ${plan.whatsapp_limit}` : ''
                    ].filter(feature => feature)
                }));
            } catch (error) {
                console.error('Error fetching SMS plans:', error);
            }
        },
        async subscribePlan(planId) {
            this.loading = true;
            try {
                const response = await axios.post(`/sms-subscribe/${this.gatewayId}/${planId}`);
                if (response.data.status === "success" && response.data.data.pay_page_url) {
                    window.location.href = response.data.data.pay_page_url;
                }
            } catch (error) {
                console.error("Subscription failed:", error);
            } finally {
                this.loading = false;
            }
        },
    },
    watch: {
        showModal(newValue) {
            if (newValue) {
                this.isOpen = true;
                this.fetchSmsPlans();
            }
        },
    },
};
</script>

<style scoped>
/* Smooth scrolling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-thumb {
    background: #03ab8f;
    border-radius: 10px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}
</style>