<template>
    <div v-if="showModal"
        class="fixed sm:top-0 inset-0 bg-black bg-opacity-50 flex sm:items-center justify-center z-50 overflow-auto">
        <div class="bg-gray-300 w-full lg:w-3/4 transform transition-transform ease-in-out duration-300 rounded overflow-auto h-screen"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-3 text-xl rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    Payments
                </p>
                <p class="close" @click="closeModal">&times;</p>
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 text-sm bg p-4"
                :class="{ 'mb-[60px]': validation_message === '' }">
                <div class="px-3 py-6 rounded mb-5 shadow-md bg-white"
                    style="box-shadow: 3px 4px 6px 3px rgba(150, 63, 59, 0.3)">
                    <div v-for="(opt, index) in paymentData" :key="index" class="flex mb-3"
                        :class="{ 'mt-5': index !== 0 }">
                        <div class="w-full">
                            <!--amount-->
                            <div class="flex w-full mr-2 relative">
                                <label for="amount"
                                    class="text-sm font-bold absolute left-2 rounded top-3 text-gray-300 transition-top linear duration-300"
                                    @click="isInputFocused['amount' + index] = true"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (opt && opt.payment_amount !== undefined && opt.payment_amount >= 0) || isInputFocused['amount' + index], 'text-blue-700': isInputFocused['amount' + index] }">
                                    Amount</label>
                                <input id="amount" type="number" v-model="opt.payment_amount"
                                    class="text-sm p-1 py-2 mt-1 border rounded border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                                    @focus="isInputFocused['amount' + index] = true"
                                    @blur="isInputFocused['amount' + index] = false"
                                    :readonly="type === 'credit' || index !== paymentData.length - 1" />
                            </div>
                            <!--payment type-->
                            <div v-if="type !== 'credit' || index !== paymentData.length - 1"
                                class="w-full mr-2 mt-3 relative bg-white px-1 py-2 rounded"
                                :class="{ 'bg-gray-200': (type === 'credit' || index !== paymentData.length - 1) }">
                                <p class="text-xs font-bold rounded block mb-2">Payment Type:</p>
                                <div class="grid grid-cols-4 gap-2 lg:gap-5 px-1 lg:px-2">
                                    <div v-for="(opt01, i) in JSON.parse(invoice_setting[0].payment_opt)" :key="i">
                                        <label :for="opt.payment_type"
                                            class="flex justify-center items-center px-1 lg:px-2 py-2 cursor-pointer inline-block rounded-md transition-colors duration-300 hover:bg-gray-400 hover:text-white"
                                            :class="{ 'bg-gray-200 text-gray-600': paymentData[index].payment_type !== opt01.type, 'bg-gray-700 text-white': paymentData[index].payment_type === opt01.type }"
                                            @click="changeOptionType(index, opt01.type)">
                                            <input type="radio" :id="opt.payment_type + index" :value="opt01.type"
                                                v-model="opt.payment_type" class="ml-0 lg:ml-2"
                                                :class="{ 'hidden': paymentData[index].payment_type !== opt01.type }" />
                                            {{ opt01.type }}
                                        </label>
                                    </div>
                                </div>

                                <!-- <select id="payment_type" v-model="opt.payment_type"
                                    class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                                    @focus="isInputFocused['payment_type' + index] = true"
                                    @blur="isInputFocused['payment_type' + index] = false">
                                    <option v-for="(opt, index) in JSON.parse(invoice_setting[0].payment_opt)"
                                        :value="opt.type" :selected="opt.status">{{ opt.type }}</option>
                                </select> -->

                            </div>
                        </div>
                        <!---Delete payment-->
                        <div class="flex justify-center items-center ml-2">
                            <button :class="{ 'hidden': index === 0 }" @click="removePaymentRow(index)">
                                <img :src="del_icon" class="w-[20px] h-[20px]" />
                            </button>
                        </div>

                    </div>
                    <!--add payment row-->
                    <div v-if="type === 'multiple' || (paymentData && paymentData.length === 0)"
                        class="flex justify-center py-2">
                        <button @click="addPaymentOption"
                            class="w-1/2 bg-green-600 text-white font-normal py-2 rounded text-sm shadow-md hover:bg-green-700">Add
                            Payment</button>
                    </div>
                    <!--note-->
                    <div class="flex w-full mr-2 relative mt-3 mb-5">
                        <label for="note"
                            class="text-sm font-normal absolute left-2 top-3 rounded transition-top linear duration-300"
                            @click="isInputFocused.note = true"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.note !== undefined && formValues.note !== '') || isInputFocused.note, 'text-blue-700': isInputFocused.note }">
                            Note <span v-if="!isInputFocused.note">(click here)</span></label>
                        <textarea v-if="isInputFocused.note" v-model="formValues.note" rows="3"
                            @focus="isInputFocused.note = true" @blur="isInputFocused.note = false"
                            class="rounded mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none outline-none"
                            :class="{ '': isInputFocused.note }"></textarea>
                    </div>
                </div>
                <!--bill payments-->
                <div class="flex justify-center mt-3 sm:mt-0  h-[370px]">
                    <div class="rounded w-3/4 justify-center rounded bg-white"
                        style="box-shadow: 3px 4px 6px 3px rgba(150, 63, 59, 0.3)"> <template
                            v-for="(item, index) in payment_display_value">
                            <div class="grid grid-cols-2 gap-4 text-end px-5 py-2 text-sm font-normal  border">
                                <div class="flex justify-between items-center">
                                    <p class="text-left">
                                        <font-awesome-icon v-if="index === 0" icon="fa-solid fa-tags" class="px-1" />
                                        <font-awesome-icon v-if="index === 1 || index === 4"
                                            icon="fa-solid fa-file-invoice" class="px-1" />
                                        <font-awesome-icon v-if="index === 2" icon="fa-solid fa-truck-fast"
                                            class="px-1" />
                                        <font-awesome-icon v-if="index === 3" icon="fa-solid fa-percent" class="px-1" />
                                        <font-awesome-icon v-if="index === 5" icon="fa-solid fa-wallet" class="px-1" />
                                        <!-- <font-awesome-icon v-if="index === 6" icon="fa-solid fa-hand-holding-dollar"
                                        class="px-1" />
                                    <font-awesome-icon v-if="index === 7" icon="fa-solid fa-hand-holding-dollar"
                                        flip="horizontal" class="px-1" /> -->
                                        {{ item.label }}
                                    </p>
                                    <p class="font-bold">:</p>
                                </div>
                                <p v-if="index !== 5 && index !== 6 && index !== 7" class="text-lg">
                                    {{ index ? currentCompanyList
                                        && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency : '' }} {{ item.value }}
                                </p>
                                <p v-if="index === 5" class="text-lg text-blue-700">{{ currentCompanyList &&
                                    currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }} {{
                                        getTotalPayment }}</p>
                                <p v-if="index === 6" class="text-lg"
                                    :class="{ 'text-green-700': getBalnace === 0, 'text-red-700': getBalnace > 0 }">{{
                                        currentCompanyList &&
                                            currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }}
                                    {{ getBalnace }}</p>
                                <p v-if="index === 7" class="text-lg text-green-700">{{ currentCompanyList &&
                                    currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }} {{
                                        getReturn }}</p>
                            </div>
                        </template>
                    </div>
                </div>
            </div>
            <p v-if="validation_message !== ''" class="text-red-500 font-normal text-sm py-2 px-5"
                :class="{ 'mb-[60px]': validation_message !== '' }">
                {{ validation_message }}</p>
        </div>
        <!-- Payment Buttons -->
        <div class="w-full lg:w-3/4 fixed bottom-0 bg-white py-3 px-6 border-t border-gray-200">
            <div class="flex justify-center items-center">
                <button class="border rounded text-white bg-red-700 font-normal px-4 py-2 hover:bg-red-600 mr-4"
                    @click="closeModal">
                    <span class="truncate">
                        <font-awesome-icon icon="fa-solid fa-xmark" size="lg" class="mr-1" />
                        Cancel
                    </span>
                </button>
                <button class="border rounded text-white bg-violet-500 font-normal px-4 py-2 hover:bg-violet-600 mr-4"
                    @click="saveModel('save')">
                    <span class="truncate">
                        <font-awesome-icon icon="fa-regular fa-floppy-disk" size="lg" class="mr-1" />
                        Save
                    </span>
                </button>
                <button class="flex border rounded text-white bg-green-700 font-normal px-2 py-2 hover:bg-green-600"
                    @click="saveModel('print')">
                    <span class="truncate">
                        <font-awesome-icon icon="fa-solid fa-print" size="lg" class="mr-1" />
                        Save & Preview
                    </span>
                </button>
            </div>
        </div>
        <!--confirm box---->
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>

    </div>
</template>

<script>
import invoice_setting from '@/store/modules/invoice_setting';
import confirmbox from './confirmbox.vue';
// import template_2 from '../setting_categories/invoiceTemplates/template_2.vue';
export default {
    components: {
        confirmbox,
        // template_2
    },
    props: {
        showModal: Boolean,
        itemData: Object,
        type: String,
        get_all_data: Object,
        invoice_setting: Object,
        payment_data: Object,
        isMobile: Boolean,
        from: String,
        balance_amount: Number,
        currentCompanyList: Object,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            print_icon: '/images/service_page/Printer.png',
            del_icon: '/images/service_page/del.png',
            isOpen: false,
            payment_display_value: [
                { label: 'Total Items', value: 0.00 },
                { label: 'Total', value: 0.00 },
                { label: 'Shipping', value: 0.00 },
                { label: 'Discount(-)', value: 0.00 },
                { label: 'Total Payable', value: 0.00 },
                { label: 'Total Paying', value: 0.00 },
                { label: 'Balance', value: 0.00 },
                { label: 'Change Return', value: 0.00 }
            ],
            paymentData: [],
            formValues: {},
            isInputFocused: {},
            validation_message: '',
            //--confirm box---
            open_confirmBox: false,
            deleteIndex: null,
            printing: false,

        };
    },
    computed: {
        getTotalPayment() {
            // console.log(this.paymentData, 'Waht is the data...!');
            let totalPayment = this.paymentData.reduce((sum, opt) => sum + (1 * opt.payment_amount), 0);
            // console.log(totalPayment, 'What happening...!');
            return totalPayment;
        },
        getBalnace() {
            let calculate_balance = (this.get_all_data.grand_total - this.getTotalPayment).toFixed(2);
            if (calculate_balance > 0) {
                this.formValues.balance = calculate_balance;
                return calculate_balance;
            } else {
                this.formValues.balance = 0;
                return 0;
            }
        },
        getReturn() {
            if (this.get_all_data.grand_total < this.getTotalPayment) {
                let calculate_return = (this.getTotalPayment - this.get_all_data.grand_total).toFixed(2);
                if (calculate_return > 0) {
                    // console.log(calculate_return, 'What happening...!')
                    this.formValues.return = calculate_return;
                    return calculate_return;
                } else {
                    this.formValues.return = 0;
                    return 0;
                }
            } else {
                this.formValues.return = 0;
                return 0;
            }
        }
    },
    methods: {
        closeModal() {
            // console.log('hello sir waht happening...@');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
                this.validation_message = '';
            }, 300);
        },
        saveModel(type) {
            if (this.paymentData && this.paymentData.length > 0 && (this.paymentData[0].payment_amount >= 0 && this.paymentData[0].payment_type) || this.type === 'credit') {
                this.isOpen = false;
                setTimeout(() => {
                    this.$emit('close-modal', { form_data: this.formValues, payment_data: this.paymentData, type: type });
                    this.validation_message = '';
                }, 300);
            } else {
                this.validation_message = 'Please fill payment type and amount..!'
            }
        },

        //---add payement row--
        addPaymentOption() {
            const defaultPaidType = this.invoice_setting && JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type;
            const remainingBalance = this.get_all_data ? this.get_all_data.grand_total - this.getTotalPayment : 0;
            // if (this.paymentData.length === 1) {
            //     // If there's only one payment option, update its payment_amount with the remaining balance
            //     this.paymentData[0].payment_amount = remainingBalance;
            // } else {
            // If there are multiple payment options, add a new one with defaultPaidType and remaining balance
            this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_amount: remainingBalance, payment_type: defaultPaidType, payment_for: 'sales' });
            // }
            // console.log(this.paymentData, 'RRRRRRRR');
        },
        //---remove payment row---
        removePaymentRow(index) {
            // this.paymentData.splice(index, 1);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        //---remove record alert--
        deleteRecord() {
            if (this.deleteIndex !== null) {
                this.paymentData.splice(this.deleteIndex, 1);
                this.deleteIndex = null;
            }
            this.open_confirmBox = false;
        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        changeOptionType(index, opt) {
            // console.log(index, 'OOOOOO', opt, 'What happening...!');
            this.paymentData[index].payment_type = opt;
        },
        //----------- get current date -----
        getCurrentDateTime() {
            const now = new Date();
            const day = String(now.getDate()).padStart(2, '0');
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const year = now.getFullYear();
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');

            return `${day}/${month}/${year} ${hours}:${minutes}`;
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        itemData: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'What happening itemDatattt.......!!!!');
                this.paymentData = newValue;
            }
        },
        payment_data: {
            deep: true,
            handler(newValue) {
                this.validation_message = '';
                if (this.from === 'sales' && this.type !== 'credit') {
                    this.paymentData = newValue;

                    if (this.paymentData.length > 0 && this.paymentData[this.paymentData.length - 1]['payment_type'] === '') {
                        this.paymentData[this.paymentData.length - 1]['payment_type'] = this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].payment_opt ? JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type : '';
                    }

                } else {
                    this.paymentData = JSON.parse(JSON.stringify(newValue));
                    if (this.paymentData.length > 0 && this.paymentData[this.paymentData.length - 1]['payment_type'] === '') {
                        this.paymentData[this.paymentData.length - 1]['payment_type'] = this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].payment_opt ? JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type : '';
                    }
                    if (this.paymentData.length > 0 && this.type !== 'credit') {
                        this.paymentData.map(opt => {
                            if (!opt.payment_date) {
                                opt.payment_date = this.getCurrentDateTime();
                            }
                            if (!opt.payment_for) {
                                opt.payment_for = 'sales';
                            }
                        })
                        let find_balance = this.paymentData.reduce((sum, opt) => sum + opt.payment_amount, 0);
                        this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_type: this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].payment_opt ? JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type : '', payment_amount: this.get_all_data ? this.get_all_data.grand_total - find_balance : 0, payment_for: this.from && this.from === 'sales' ? 'sales' : 'sales' });
                    }
                }
            }
        },

        get_all_data: {
            deep: true,
            handler(newValue) {
                this.payment_display_value.map(opt => {
                    if (opt.label === 'Total Items') {
                        opt.value = newValue.total_qty;
                    } else if (opt.label === 'Total') {
                        opt.value = (1 * newValue.sub_total).toFixed(2);
                    } else if (opt.label === 'Shipping') {
                        opt.value = newValue.shipping;
                    } else if (opt.label === 'Discount(-)') {
                        opt.value = newValue.discount_total;
                    } else if (opt.label === 'Total Payable') {
                        opt.value = newValue.grand_total;
                    }
                })
                if (this.paymentData.length === 0) {
                    this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_type: '', payment_amount: newValue.grand_total, payment_for: this.from && this.from === 'sales' ? 'sales' : 'sales' });
                }
            }
        },
        type: {
            deep: true,
            handler(newValue) {
                // if (newValue === 'payall') {
                //     this.paymentData = [this.paymentData[0]];
                //     this.paymentData[0].payment_amount = this.get_all_data.grand_total;
                //     this.paymentData[0].payment_type = JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type;
                // }
                // else if (newValue === 'cash') {
                //     this.paymentData = [this.paymentData[0]];
                //     this.paymentData[0].payment_type = 'Cash';
                //     this.paymentData[0].payment_amount = this.get_all_data.grand_total;
                // }
                // console.log(this.payment_data, 'RRRWRWREGRGG', this.paymentData, 'TTTTT', this.invoice_setting);
                if (newValue === 'credit') {
                    // console.log(this.payment_data.length === 0, 'EEEEEEEEEEEEEEEEEEEEEEEEEE');
                    if (this.paymentData.length === 0) {
                        this.paymentData = [{}];
                        this.paymentData[0].payment_date = this.getCurrentDateTime();
                        this.paymentData[0].payment_type = '';
                        this.paymentData[0].payment_amount = 0;
                    } else if (this.payment_data.length === this.paymentData.length) {
                        this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_type: newValue, payment_amount: 0, payment_for: this.from && this.from === 'sales' ? 'sales' : 'sales' });
                    } else if (this.payment_data.length < this.paymentData.length && this.paymentData[this.paymentData.length - 1].payment_amount > 0) {
                        this.paymentData[this.paymentData.length - 1].payment_amount = 0;
                    }
                }
                else if (this.payment_data.length === this.paymentData.length) {
                    if (this.paymentData.length > 0) {
                        let find_balance = this.paymentData.reduce((sum, opt) => sum + opt.payment_amount, 0);
                        if (this.get_all_data.grand_total - find_balance > 0) {
                            this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_type: this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].payment_opt ? JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type : '', payment_amount: this.get_all_data ? this.get_all_data.grand_total - find_balance : 0, payment_for: this.from && this.from === 'sales' ? 'sales' : 'sales' });
                        }
                    }
                } else if (this.payment_data.length < this.paymentData.length && this.paymentData[this.paymentData.length - 1].payment_amount === 0) {
                    let find_balance = this.paymentData.reduce((sum, opt) => sum + opt.payment_amount, 0);
                    if (this.paymentData.length > 0 && this.get_all_data.grand_total - find_balance > 0) {
                        this.paymentData[this.paymentData.length - 1]['payment_date'] = this.getCurrentDateTime();
                        this.paymentData[this.paymentData.length - 1]['payment_amount'] = this.get_all_data ? this.get_all_data.grand_total - find_balance : 0;
                        if (this.paymentData[this.paymentData.length - 1]['payment_type'] === '') {
                            this.paymentData[this.paymentData.length - 1]['payment_type'] = this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].payment_opt ? JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type : '';
                        }
                    }
                }
            }
        },
        invoice_setting: {
            deep: true,
            handler(newValue) {
                // console.log(newValue && newValue[0] && newValue[0].payment_opt, 'RRRRRRRRRRRRRRRRRRRRRRRRRRR', this.paymentData.length, 'RRRRRREEEEEEEEEEEEEEEEEEEE', this.paymentData.length > 0, 'OOOOO', this.type, 'RRRRRR', this.paymentData[this.paymentData.length-1]['payment_type'] === '');
                if (newValue && newValue[0] && newValue[0].payment_opt && this.paymentData) {
                    if (this.paymentData.length === 0) {
                        this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_type: JSON.parse(newValue[0].payment_opt).find(opt => opt.status === true).type, payment_amount: this.get_all_data ? this.get_all_data.grand_total : 0, payment_for: this.from && this.from === 'sales' ? 'sales' : 'sales' });
                    }
                    if (this.paymentData.length > 0 && this.paymentData[this.paymentData.length - 1]['payment_type'] === '') {
                        // let find_balance = this.paymentData.reduce((sum, opt) => sum + opt.payment_amount, 0);
                        // this.paymentData.push({ payment_type: JSON.parse(newValue[0].payment_opt).find(opt => opt.status === true).type, payment_amount: this.get_all_data ? this.get_all_data.grand_total - find_balance : 0 });
                        this.paymentData[this.paymentData.length - 1]['payment_date'] = this.getCurrentDateTime();
                        this.paymentData[this.paymentData.length - 1]['payment_type'] = JSON.parse(newValue[0].payment_opt).find(opt => opt.status === true).type;
                    }
                }
            }
        },
        balance_amount: {
            deep: true,
            handler(newValue) {
                if (this.get_all_data && newValue) {
                    let find_balance = this.paymentData.reduce((sum, opt) => sum + opt.payment_amount, 0);
                    if (this.get_all_data.grand_total - find_balance > 0) {
                        this.paymentData.push({ payment_date: this.getCurrentDateTime(), payment_type: this.invoice_setting && JSON.parse(this.invoice_setting[0].payment_opt).find(opt => opt.status === true).type, payment_amount: newValue, payment_for: this.from && this.from === 'sales' ? 'sales' : 'sales' });
                    }
                }
            }
        }
    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>