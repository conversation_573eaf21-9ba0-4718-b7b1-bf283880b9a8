<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateAmcUsersAPIRequest;
use App\Http\Requests\API\UpdateAmcUsersAPIRequest;
use App\Models\AmcUsers;
use App\Repositories\AmcUsersRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class AmcUsersController
 * @package App\Http\Controllers\API
 */

class AmcUsersAPIController extends AppBaseController
{
    /** @var  AmcUsersRepository */
    private $amcUsersRepository;

    public function __construct(AmcUsersRepository $amcUsersRepo)
    {
        $this->amcUsersRepository = $amcUsersRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/amcUsers",
     *      summary="getAmcUsersList",
     *      tags={"AmcUsers"},
     *      description="Get all AmcUsers",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/AmcUsers")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $amcUsers = $this->amcUsersRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($amcUsers->toArray(), 'Amc Users retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/amcUsers",
     *      summary="createAmcUsers",
     *      tags={"AmcUsers"},
     *      description="Create AmcUsers",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/AmcUsers"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateAmcUsersAPIRequest $request)
    {
        $input = $request->all();

        $amcUsers = $this->amcUsersRepository->create($input);

        return $this->sendResponse($amcUsers->toArray(), 'Amc Users saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/amcUsers/{id}",
     *      summary="getAmcUsersItem",
     *      tags={"AmcUsers"},
     *      description="Get AmcUsers",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of AmcUsers",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/AmcUsers"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var AmcUsers $amcUsers */
        $amcUsers = $this->amcUsersRepository->find($id);

        if (empty($amcUsers)) {
            return $this->sendError('Amc Users not found');
        }

        return $this->sendResponse($amcUsers->toArray(), 'Amc Users retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/amcUsers/{id}",
     *      summary="updateAmcUsers",
     *      tags={"AmcUsers"},
     *      description="Update AmcUsers",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of AmcUsers",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/AmcUsers"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateAmcUsersAPIRequest $request)
    {
        $input = $request->all();

        /** @var AmcUsers $amcUsers */
        $amcUsers = $this->amcUsersRepository->find($id);

        if (empty($amcUsers)) {
            return $this->sendError('Amc Users not found');
        }

        $amcUsers = $this->amcUsersRepository->update($input, $id);

        return $this->sendResponse($amcUsers->toArray(), 'AmcUsers updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/amcUsers/{id}",
     *      summary="deleteAmcUsers",
     *      tags={"AmcUsers"},
     *      description="Delete AmcUsers",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of AmcUsers",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var AmcUsers $amcUsers */
        $amcUsers = $this->amcUsersRepository->find($id);

        if (empty($amcUsers)) {
            return $this->sendError('Amc Users not found');
        }

        $amcUsers->delete();

        return $this->sendSuccess('Amc Users deleted successfully');
    }
}
