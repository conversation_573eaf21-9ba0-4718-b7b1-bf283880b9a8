#!/bin/bash

# Track New - Quick Start Script
# This script helps set up the Track New project quickly

set -e

echo "🚀 Track New - Quick Start Setup"
echo "================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Check if Node.js is installed
check_nodejs() {
    if ! command -v node &> /dev/null; then
        print_error "Node.js is not installed. Please install Node.js 16+ and try again."
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 16 ]; then
        print_error "Node.js version 16+ is required. Current version: $(node -v)"
        exit 1
    fi
    
    print_status "Node.js version: $(node -v) ✓"
}

# Check if MySQL is installed and running
check_mysql() {
    if ! command -v mysql &> /dev/null; then
        print_warning "MySQL client not found. Please ensure MySQL is installed and running."
        return 1
    fi
    
    print_status "MySQL client found ✓"
    return 0
}

# Setup backend
setup_backend() {
    print_step "Setting up Node.js Backend..."
    
    cd tracknew-nodejs-backend
    
    # Install dependencies
    print_status "Installing backend dependencies..."
    npm install
    
    # Copy environment file
    if [ ! -f .env ]; then
        print_status "Creating .env file..."
        cp .env.example .env
        print_warning "Please update the .env file with your database credentials and other settings."
    else
        print_status ".env file already exists"
    fi
    
    # Create uploads directory
    mkdir -p uploads
    mkdir -p logs
    
    print_status "Backend setup completed ✓"
    cd ..
}

# Setup frontend
setup_frontend() {
    print_step "Setting up React Frontend..."
    
    cd tracknew-react-frontend
    
    # Install dependencies
    print_status "Installing frontend dependencies..."
    npm install
    
    # Create environment file
    if [ ! -f .env ]; then
        print_status "Creating frontend .env file..."
        cat > .env << EOL
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_APP_NAME=Track New
REACT_APP_DEBUG=true
REACT_APP_ENABLE_PWA=true
EOL
    else
        print_status "Frontend .env file already exists"
    fi
    
    print_status "Frontend setup completed ✓"
    cd ..
}

# Create database
setup_database() {
    print_step "Setting up Database..."
    
    read -p "Enter MySQL root password (press Enter if no password): " -s MYSQL_ROOT_PASSWORD
    echo
    
    MYSQL_CMD="mysql -u root"
    if [ ! -z "$MYSQL_ROOT_PASSWORD" ]; then
        MYSQL_CMD="mysql -u root -p$MYSQL_ROOT_PASSWORD"
    fi
    
    # Test MySQL connection
    if ! $MYSQL_CMD -e "SELECT 1;" &> /dev/null; then
        print_error "Cannot connect to MySQL. Please check your MySQL installation and credentials."
        return 1
    fi
    
    # Create database
    print_status "Creating database..."
    $MYSQL_CMD -e "CREATE DATABASE IF NOT EXISTS tracknew_development CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    # Create user (optional)
    read -p "Do you want to create a dedicated database user? (y/n): " CREATE_USER
    if [ "$CREATE_USER" = "y" ] || [ "$CREATE_USER" = "Y" ]; then
        read -p "Enter username for database user: " DB_USER
        read -p "Enter password for database user: " -s DB_PASSWORD
        echo
        
        $MYSQL_CMD -e "CREATE USER IF NOT EXISTS '$DB_USER'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
        $MYSQL_CMD -e "GRANT ALL PRIVILEGES ON tracknew_development.* TO '$DB_USER'@'localhost';"
        $MYSQL_CMD -e "FLUSH PRIVILEGES;"
        
        print_status "Database user '$DB_USER' created ✓"
        print_warning "Please update the DB_USERNAME and DB_PASSWORD in tracknew-nodejs-backend/.env"
    fi
    
    print_status "Database setup completed ✓"
}

# Run database migrations
run_migrations() {
    print_step "Running Database Migrations..."
    
    cd tracknew-nodejs-backend
    
    # Check if .env exists and has database config
    if [ ! -f .env ]; then
        print_error ".env file not found. Please run setup first."
        return 1
    fi
    
    # Install sequelize-cli globally if not installed
    if ! command -v sequelize &> /dev/null; then
        print_status "Installing Sequelize CLI..."
        npm install -g sequelize-cli
    fi
    
    # Run migrations
    print_status "Running database migrations..."
    npm run migrate
    
    # Run seeders
    print_status "Seeding database with initial data..."
    npm run seed
    
    print_status "Database migrations completed ✓"
    cd ..
}

# Start services
start_services() {
    print_step "Starting Services..."
    
    # Start backend in background
    print_status "Starting backend server..."
    cd tracknew-nodejs-backend
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    # Wait a moment for backend to start
    sleep 3
    
    # Start frontend
    print_status "Starting frontend server..."
    cd tracknew-react-frontend
    npm start &
    FRONTEND_PID=$!
    cd ..
    
    print_status "Services started!"
    print_status "Backend: http://localhost:8000"
    print_status "Frontend: http://localhost:3000"
    print_status "Health Check: http://localhost:8000/health"
    
    echo
    print_status "Press Ctrl+C to stop all services"
    
    # Wait for user to stop services
    trap "kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT
    wait
}

# Main menu
show_menu() {
    echo
    echo "Select an option:"
    echo "1. Full setup (recommended for first time)"
    echo "2. Setup backend only"
    echo "3. Setup frontend only"
    echo "4. Setup database only"
    echo "5. Run migrations only"
    echo "6. Start services"
    echo "7. Exit"
    echo
}

# Main execution
main() {
    print_status "Checking prerequisites..."
    check_nodejs
    
    while true; do
        show_menu
        read -p "Enter your choice (1-7): " choice
        
        case $choice in
            1)
                check_mysql
                setup_backend
                setup_frontend
                setup_database
                run_migrations
                echo
                print_status "Full setup completed! ✅"
                print_status "You can now start the services with option 6."
                ;;
            2)
                setup_backend
                ;;
            3)
                setup_frontend
                ;;
            4)
                check_mysql
                setup_database
                ;;
            5)
                run_migrations
                ;;
            6)
                start_services
                ;;
            7)
                print_status "Goodbye! 👋"
                exit 0
                ;;
            *)
                print_error "Invalid option. Please try again."
                ;;
        esac
    done
}

# Run main function
main
