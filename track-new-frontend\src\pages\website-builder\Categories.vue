<template>
    <div class="builder-page">
        <!-- Add New Button aligned to the right -->
        <div class="flex justify-end mb-4">
            <button @click="openModal" class="bg-blue-500 text-white py-2 px-4 rounded">
                Add New Category
            </button>
        </div>

        <!-- Categories Table -->
        <table class="min-w-full bg-white shadow-md rounded-lg mb-4">
            <thead>
                <tr>
                    <th class="py-2 px-4 text-left border-b">Category Name</th>
                    <th class="py-2 px-4 text-left border-b">Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="(category, index) in categories" :key="category.id">
                    <td class="py-2 px-4 border-b">{{ category.name }}</td>
                    <td class="py-2 px-4 border-b">
                        <!-- Edit Button -->
                        <button @click="openEditCategoryModal(category, index)" class="text-blue-500 mr-2">
                            <font-awesome-icon icon="fa-solid fa-pencil" /> Edit
                        </button>
                        <!-- Delete But<PERSON> (Disabled for the first item) -->
                        <button v-if="index !== 0" @click="deleteCategory(category, index)" class="text-red-500">
                            <font-awesome-icon icon="fa-solid fa-trash-can" /> Delete
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
        <!-- Bottom Navigation -->
        <div class="mt-6">
            <NavigationButtons :pageTitle="'Product Details'" @goToNextPage="goToNextPage"
                @goToPrevPage="goToPrevPage" />
        </div>

        <!-- Add/Edit Product Modal -->
        <productCategoryModal :show="showModal" :categories="categories" @close="closeModal" @save="saveCategory"
            @toasterMessages="toasterMessages" :isMode="isEditMode" :categoryInfo="categoryData"
            :categoryId="updateIndex" />
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    </div>
</template>
<script>
import productCategoryModal from './productCategoryModal.vue';
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import EnablePageName from './EnablePageName.vue';

export default {
    props: {
        companyId: {
            type: String,
            required: true
        },
        productsData: { // Receive initial products data from parent
            type: Array,
            default: () => []
        },
        productCategoriessData: {
            type: Array,
            default: () => []
        },
        pages: {
            type: Object,
            required: true
        },
        is_updated: { type: Boolean, required: true },
    },
    components: {
        productCategoryModal,
        NavigationButtons,
        FontAwesomeIcon,
        confirmbox,
        EnablePageName
    },
    data() {
        return {
            categories: [],
            showModal: false, // Control modal visibility
            isEditMode: false, // To toggle between add/edit mode
            categoryData: null, // Product to edit
            updateIndex: null,
            //--confirm box--
            open_confirmBox: false,
            deleteIndex: null,
            //----is on settings---
            is_onSetting: { is_on: true, name: 'Products' },
        };
    },
    watch: {
        is_updated: {
            deep: true,
            handler(newValue) {
                this.$emit('updateProductCategories', this.categories);
            }
        },
        productCategoriessData: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.categories = [...newValue];
                }
            }
        },
    },
    mounted() {
        // console.log(this.productCategoriessData, 'Waht happening in the data....!!');

        if (this.productCategoriessData) {
            this.categories = [...this.productCategoriessData];
        }
    },
    methods: {
        toasterMessages(data) {
            this.message = data.msg;
            this.type_toaster = data.type;
            this.$emit('toasterMessages', { msg: data.msg, type: data.type });
        },
        openModal() {
            this.isEditMode = false;
            this.categoryData = null;
            this.updateIndex = null;
            this.showModal = true;
        },

        closeModal() {
            this.showModal = false;
        },
        saveCategory(data) {
            if (data && data.length > 0) {
                this.$emit('updateProductCategories', data);
                this.categories = [...data];
                this.toasterMessages({ msg: 'Categories save successfully', type: 'success' });
                this.showModal = false;
            }
        },
        deleteCategory(category, index) {
            this.deleteIndex = category.id;
            this.open_confirmBox = true;
        },
        openEditCategoryModal(category, index) {
            this.isEditMode = true;
            this.categoryData = { ...category }; // Set category data for editing
            this.updateIndex = category.id;
            this.showModal = true;
        },
        goToNextPage() {
            this.$emit('updateProductCategories', this.categories);
            this.$emit('submitData');
            this.$emit('goToNextPage'); // Emit event to the parent component
        },
        goToPrevPage() {
            this.$emit('updateProductCategories', this.categories);
            this.$emit('submitData');
            this.$emit('goToPrevPage'); // Emit event to the parent component
        },
        //--remove category options--
        deleteRecord() {
            if (this.deleteIndex !== null) {
                let find_exist = this.categories.findIndex(opt => opt.id === this.deleteIndex);
                this.categories.splice(find_exist, 1);
                this.$emit('toasterMessages', { msg: 'Category removed successfully', type: 'success' });
                this.open_confirmBox = false;
                this.deleteIndex = null;
                this.$emit('updateProductCategories', this.categories);
                this.$emit('submitData');
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
    }
};
</script>