<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-x-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" :servicesList="dataFromChild" @searchData="getFiteredDataList"
                @refresh_store="refresh_store">
            </headbar> -->

            <!-- services home -->
            <div class="relative">
                <purchaseVue @dataToParent="handleDataFromChild" :type="type" :editData="edit_data"
                    :searchedData="getFilteredData" :companyId="currentLocalDataList.company_id" :createdBy="userId"
                    :store_refresh="store_refresh" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"
                    @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate">
                </purchaseVue>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
        <dialogConfirmBox :visible="show_confirm_box" :message="message" :type="'sales'" @save="saveData"
            @ok="withoutSave" @cancel="cancelData"></dialogConfirmBox>
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/inventory/addPurchase/headbar.vue';
import purchaseVue from '../supporting/inventory/addPurchase/purchase.vue';
import dialogConfirmBox from '../supporting/dialog_box/dialogConfirmBox.vue';
import { useMeta } from '@/composables/useMeta';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'categories',
    components: {
        // sidebar,
        // headbar,
        purchaseVue,
        dialogConfirmBox
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            showViewCustomer: false,
            viewCustomerData: null,
            route_item: 6,
            dataFromChild: [],
            getFilteredData: [],
            queryParams: null,
            edit_data: null,
            type: null,
            //--api integration--
            companyId: null,
            userId: null,
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
            //---store data----
            isStoreData: false,
            save_success: false,
            //---confirmbox data---
            show_confirm_box: false,
            message: '',
        };
    },
    setup() {
        const pageTitle = 'Add / Update Purchase';
        const pageDescription = 'Create new or update existing purchase orders with ease. Streamlined tools for accurate and efficient order management.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList'])
    },
    methods: {
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        showViewCustomerComponent(data) {
            // console.log(data, 'what about data..!');
            this.showViewCustomer = true;
            this.viewCustomerData = data;
        },
        // Method to go back to home
        goBackToHome() {
            this.showViewCustomer = false;
        },

        handleDataFromChild(data) {
            this.dataFromChild = data;
        },
        getFiteredDataList(data) {
            // console.log(data, 'WWWW');
            this.getFilteredData = data;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        },
        //---in case user not store data ---
        salessaveData(value) {
            // console.log(value, 'what about the data.....');
            this.isStoreData = value;
        },
        //---save data---
        saveData() {
            // console.log('save data....');
            this.show_confirm_box = false;
            this.save_success = true;
        },
        cancelData() {
            // console.log('cancel data....');
            this.show_confirm_box = false;
        },
        withoutSave() {
            // console.log('without save data...');
            this.show_confirm_box = false;
            this.isStoreData = false;
            this.$router.go(-1);
        },
        //--update sles save--
        saveDataUpdate() {
            this.save_success = false;
        }
    },

    mounted() {
        this.fetchLocalDataList();
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile(); // Initial check
        this.updateIsEnableBottom(false);
        const query = this.$route.query; // Get query parameters
        this.queryParams = query;
        // console.log(this.queryParams, 'query...@@');

        if (this.queryParams.type === 'edit' && this.queryParams.po) {
            this.type = this.queryParams.type;
            //---get list data
            axios.get(`/purchase_orders/${this.queryParams.po}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data.data, 'oooo', this.queryParams.po);
                    let findMatchPO = response.data.data;
                    // let findMatchPO = response.data.data.find((opt) => opt.id === (1*this.queryParams.po));
                    // console.log(findMatchPO, 'TTTTT');
                    if (findMatchPO) {
                        findMatchPO.purchase_order_date = findMatchPO.purchase_order_date;
                        this.edit_data = JSON.parse(JSON.stringify(findMatchPO));
                    }
                })
                .catch(error => {
                    console.error('Error get purchase', error);
                })

        } else {
            this.type = this.queryParams.type;
        }

        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        this.updateIsEnableBottom(true);
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen || this.isStoreData) {
            if (this.isStoreData) {
                // alert('are you sure get back?');
                this.message = 'Are you sure you want to go back? Unsaved data may be lost...!';
                this.show_confirm_box = true;
            }
            if (this.anyModalOpen) {
                // If any modal is open, close all modals
                this.updateModalOpen = !this.updateModalOpen;
            }
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnableBottom(true);
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>