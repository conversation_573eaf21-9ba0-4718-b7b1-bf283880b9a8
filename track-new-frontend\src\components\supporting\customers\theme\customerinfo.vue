<template>
    <div v-if="showModal" class="text-sm"
        :class="{ 'fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50': isTab || isMobile }">
        <!-- Modal -->
        <transition name="modal-transition">
            <div class="model bg-white w-full sm:w-1/2 lg:w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen sm:pb-[150px] lg:pb-[70px] pb-[150px]"
                :class="{ 'translate-y-0': isOpen, 'translate-x-full right-12': !isOpen }">
                <!-- Fixed Header Section -->
                <div class="relative flex items-center border-b bg-white sticky top-0 z-10"
                    :class="{ 'p-4': isMobile, 'py-2': !isMobile }">
                    <!-- Close <PERSON><PERSON> (Left) -->
                    <button @click="cancelModal" class="p-2 rounded-full hover:bg-gray-100">
                        <font-awesome-icon icon="fa-solid fa-arrow-left" class="text-gray-600 text-lg" />
                    </button>

                    <!-- Centered Customer Info Label -->
                    <div class="absolute left-1/2 transform -translate-x-1/2 text-lg font-semibold text-gray-900">
                        Customer Info
                    </div>
                </div>

                <!-- Scrollable Content -->
                <div class="flex-1 overflow-y-auto space-y-4 m-1" :class="{ 'p-4': isMobile, 'py-2': !isMobile }">

                    <!-- Customer Info Card -->
                    <div class="rounded-lg p-2 border border-gray-200">
                        <!-- Centered Profile Section -->
                        <div class="flex flex-col items-center text-center space-y-2">
                            <!-- Initial (Avatar) -->
                            <div
                                class="h-16 w-16 rounded-full flex items-center justify-center text-white font-bold bg-green-600 text-xl">
                                {{ selected_customer.first_name.charAt(0) }}
                            </div>
                            <!-- Name & Phone -->
                            <div>
                                <div class="font-medium text-lg text-gray-900">{{ selected_customer.first_name }}
                                    {{ selected_customer.last_name && selected_customer.last_name !== '' ?
                                        selected_customer.last_name : '' }}
                                    <span v-if="selected_customer.gst_number"
                                        class="ml-2 text-green-500 text-sm">B2B</span>
                                    <span v-else class="ml-2 text-blue-500 text-sm">B2C</span>
                                </div>
                                <div class="text-sm text-gray-500">{{ selected_customer.contact_number }}</div>
                                <div class="text-sm text-gray-700"> <!-- Business Icon -->
                                    <font-awesome-icon icon="fa-solid fa-briefcase" class="text-gray-600 text-lg" /> {{
                                        selected_customer.business_name }}
                                </div>
                            </div>
                        </div>

                        <!-- Divider -->
                        <hr class="my-2 border-gray-300">

                        <!-- Action Buttons: Call, Share, Edit -->
                        <div class="flex justify-around">
                            <button @click="dialPhoneNumber(selected_customer.contact_number)"
                                class="flex items-center text-green-700 hover:text-green-600">
                                <font-awesome-icon icon="fa-solid fa-phone" class="text-md pr-1" /> Call
                            </button>
                            <!--WhatApp-->
                            <button v-if="companywhatsapp" class="text-green-700 flex items-center"
                                @click="openWhatsApp">
                                <font-awesome-icon icon="fa-brands fa-whatsapp" class="text-lg" />
                                <!-- <span v-if="!isMobile && !isTab" class="pl-1">Remainder</span> -->
                            </button>
                            <button v-if="!companywhatsapp" class="text-red-700 flex items-center"
                                @click="navigateToWhatsApp">
                                <font-awesome-icon icon="fa-brands fa-whatsapp" class="text-lg" />
                                <!-- <span v-if="!isMobile && !isTab" class="pl-1">WhatsApp</span> -->
                            </button>
                            <!-- <button class="flex flex-col items-center text-blue-600 hover:text-blue-600">
                                <font-awesome-icon icon="fa-solid fa-share-alt" class="text-md" />
                            </button> -->
                            <button @click="editCustomer" class="flex items-center text-blue-600 hover:text-yellow-600">
                                <font-awesome-icon icon="fa-solid fa-pencil" class="text-md pr-1" /> Edit
                            </button>
                        </div>
                    </div>

                    <!-- Customer Details Card -->
                    <div class="rounded-lg p-4 border border-gray-200 m-1">
                        <div class="text-sm  text-blue-500 mb-2">Customer Details: </div>
                        <div class="text-gray-700 space-y-2">
                            <p><span class="font-semibold">Created At:</span> {{
                                formatDate(formattedDate(selected_customer.created_at))
                            }}
                            </p>
                            <p><span class="font-semibold">Phone No:</span> {{ selected_customer.contact_number }}</p>
                            <p><span class="font-semibold">Due Amount:</span> <span class="text-red-500">
                                    {{ currentCompanyList && currentCompanyList.currency ===
                                        'INR' ?
                                        '\u20b9'
                                        : currentCompanyList.currency }} {{
                                        selected_customer.balance_amount
                                    }}</span></p>
                            <p v-if="selected_customer.email && selected_customer.email !== ''"><span
                                    class="font-semibold">Email:</span> <a :href="'mailto:' + selected_customer.email"
                                    class="text-blue-600 underline">{{
                                        selected_customer.email }}</a></p>
                            <p v-if="selected_customer.address && selected_customer.address !== ''"><span
                                    class="font-semibold">Address:</span> {{ selected_customer.address }}</p>
                            <p v-if="selected_customer.state_name && selected_customer.state_name !== ''"><span
                                    class="font-semibold">State:</span> {{ selected_customer.state_name }}</p>
                            <p v-if="selected_customer.district_name && selected_customer.district_name !== ''"><span
                                    class="font-semibold">District:</span> {{ selected_customer.district_name }}</p>
                            <p v-if="selected_customer.city_name && selected_customer.city_name !== ''"><span
                                    class="font-semibold">City:</span> {{ selected_customer.city_name }}</p>
                            <p v-if="selected_customer.pinCode && selected_customer.pinCode !== ''"><span
                                    class="font-semibold">Pin-Code:</span> {{ selected_customer.pinCode }}</p>
                            <p v-if="selected_customer.gst_number && selected_customer.gst_number !== ''"><span
                                    class="font-semibold">GST:</span> {{ selected_customer.gst_number }}</p>
                            <p v-if="selected_customer.opening_balance >= 0"><span class="font-semibold">Opening
                                    Balance:</span> <span class="text-red-600">
                                    {{ currentCompanyList && currentCompanyList.currency ===
                                        'INR' ?
                                        '\u20b9'
                                        : currentCompanyList.currency }} {{ selected_customer.opening_balance }}</span>
                            </p>
                        </div>
                    </div>

                    <!-- Customer Overview Card -->
                    <div class="rounded-lg p-2 border border-gray-200">
                        <div class="text-sm text-green-500 mb-2">Customer Overview</div>
                        <div v-for="service in customer_overview"
                            class="flex items-center p-4 cursor-pointer hover:bg-gray-100 border-b border-gray-100"
                            @click="selectedOptions(service.type)">
                            <div class="flex-1">
                                <div class="flex justify-between">
                                    <span class="font-medium text-gray-900">{{ service.type }}</span>
                                    <span class="text-xs text-gray-500">{{ service.total }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </transition>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        selected_customer: Object,
        isMobile: Boolean,
        isTab: Boolean,
        customer_overview: {
            type: [Object, null],
            default: null,
        },
        currentCompanyList: Object,
        companywhatsapp: Boolean
    },
    data() {
        return {
            isOpen: false,
        }
    },
    methods: {
        cancelModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('closeModal');
            }, 300);
        },
        editCustomer() {
            this.$emit('editcustomer', this.selected_customer);
        },
        dialPhoneNumber(data) {
            this.$emit('dialPhoneNumber', data);
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        formatDate(timestamp) {
            const date = new Date(timestamp);
            // Get the day, month, year, hours, and minutes
            const day = String(date.getDate()).padStart(2, '0'); // Ensures 2 digits
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
            const year = date.getFullYear();
            const hours = date.getHours() % 12 || 12; // Convert to 12-hour format
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const ampm = date.getHours() >= 12 ? 'PM' : 'AM';
            return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
        },
        //---options---
        selectedOptions(option) {
            if (option) {
                this.$emit('selectedOption', option);
                if (this.isTab || this.isMobile) {
                    this.cancelModal();
                }
            }
        },
        //--navigate to whatsapp--
        openWhatsApp() {
            this.$emit('openWhatsApp', this.selected_customer);
        },
        navigateToWhatsApp() {
            this.$emit('navigateToWhatsApp');
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
    }
}
</script>
<style scoped>
/* Modal styling */
.modal {
    position: fixed;
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Make modal content scrollable on mobile */
.modal-content {
    overflow-y: auto;
    /* Add this line to make the content scrollable */
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.3s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;
    }

    .modal-content {
        overflow-y: auto;
    }
}

/* Define the transition for the modal */
.modal-transition-enter-active,
.modal-transition-leave-active {
    transition: transform 0.3s ease-in-out;
}

/* Modal comes from the right when not mobile */
.modal-transition-enter,
.modal-transition-leave-to {
    transform: translateX(100%);
}

.modal-transition-enter-to {
    transform: translateX(0);
}

.modal-transition-leave-to {
    transform: translateX(100%);
}

/* For mobile, sliding effect from the bottom */
@media (min-width: 640px) {

    .modal-transition-enter,
    .modal-transition-leave-to {
        transform: translateY(100%);
    }

    .modal-transition-enter-to {
        transform: translateY(0);
    }

    .modal-transition-leave-to {
        transform: translateY(100%);
    }
}
</style>
