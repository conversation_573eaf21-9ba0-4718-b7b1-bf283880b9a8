<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateExpensesAPIRequest;
use App\Http\Requests\API\UpdateExpensesAPIRequest;
use App\Models\Expenses;
use App\Repositories\ExpensesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Http\Resources\api\ExpenseResource;
use Response;

/**
 * Class ExpensesController
 * @package App\Http\Controllers\API
 */

class ExpensesAPIController extends AppBaseController
{
    /** @var  ExpensesRepository */
    private $expensesRepository;

    public function __construct(ExpensesRepository $expensesRepo)
    {
        $this->expensesRepository = $expensesRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/expenses",
     *      summary="getExpensesList",
     *      tags={"Expenses"},
     *      description="Get all Expenses",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Expenses")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }       	
      	$from_date = $request->query('from_date');    			
      	$to_date = $request->query('to_date'); 
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $expensesQuery = Expenses::where('company_id', $companyId);
      
      	if ($from_date) {
        	$expensesQuery->whereDate('updated_at', '>=', $from_date);
      	}

      	if ($to_date) {
        	$expensesQuery->whereDate('updated_at', '<=', $to_date);
      	}

        if ($perPage === 'all') {
            $perPage =  $expensesQuery->count();
        }

        $expenses =  $expensesQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => ExpenseResource::collection($expenses), // Get the paginated items
            'pagination' => [
                'total' => $expenses->total(),
                'per_page' => $expenses->perPage(),
                'current_page' => $expenses->currentPage(),
                'last_page' => $expenses->lastPage(),
                'from' => $expenses->firstItem(),
                'to' => $expenses->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/expenses",
     *      summary="createExpenses",
     *      tags={"Expenses"},
     *      description="Create Expenses",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Expenses")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Expenses"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateExpensesAPIRequest $request)
    {
        $input = $request->all();
        $input['created_by'] = auth()->user()->id;
        $input['updated_by'] = auth()->user()->id;

        $expenses = $this->expensesRepository->create($input);

        return $this->sendResponse(new ExpenseResource($expenses), 'Expenses saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/expenses/{id}",
     *      summary="getExpensesItem",
     *      tags={"Expenses"},
     *      description="Get Expenses",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Expenses",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Expenses"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Expenses $expenses */
        $expenses = $this->expensesRepository->find($id);

        if (empty($expenses)) {
            return $this->sendError('Expenses not found');
        }

        return $this->sendResponse(new ExpenseResource($expenses), 'Expenses retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/expenses/{id}",
     *      summary="updateExpenses",
     *      tags={"Expenses"},
     *      description="Update Expenses",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Expenses",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Expenses")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Expenses"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateExpensesAPIRequest $request)
    {
        $input = $request->all();
      
        $input['updated_by'] = auth()->user()->id;

        /** @var Expenses $expenses */
        $expenses = $this->expensesRepository->find($id);

        if (empty($expenses)) {
            return $this->sendError('Expenses not found');
        }

        $expenses = $this->expensesRepository->update($input, $id);

        return $this->sendResponse(new ExpenseResource($expenses), 'Expenses updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/expenses/{id}",
     *      summary="deleteExpenses",
     *      tags={"Expenses"},
     *      description="Delete Expenses",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Expenses",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Expenses $expenses */
        $expenses = $this->expensesRepository->find($id);

        if (empty($expenses)) {
            return $this->sendError('Expenses not found');
        }

        $expenses->delete();

        return $this->sendSuccess('Expenses deleted successfully');
    }
}
