<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mb-[60px] mt-[57px]': isMobile, 'mt-[60px]': !isMobile }">
            <!-- Headbar with its content -->
            <headbar @toggle-sidebar="toggleSidebar" dataFromChild :productData="dataFromChild"
                @searchData="getFiteredDataList" @refresh_store="refresh_store"></headbar>
            <div>
                <bannerDesign></bannerDesign>
            </div>
            <!-- services home -->
            <div class="p-1 relative">
                <stockAdjust :isMobile="isMobile" :store_refresh="store_refresh"></stockAdjust>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"></sidebar>
        </div> -->

    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/inventory/stockAdjustment/headbar.vue';
import headbar from '../supporting/inventory/headbar.vue';
import stockAdjust from '../supporting/inventory/stockAdjustment/stockAdjust.vue';
import { useMeta } from '@/composables/useMeta';
import bannerDesign from '../supporting/banner/bannerDesign.vue';

export default {
    name: 'stock',
    components: {
        // sidebar,
        headbar,
        stockAdjust,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            route_item: 6,
            dataFromChild: null,
            getFilteredData: null,
            store_refresh: false,
        };
    },
    setup() {
        const pageTitle = 'Stock Adjust';
        const pageDescription = 'Adjust stock levels accurately to reflect real inventory counts. Efficient tools for managing stock discrepancies.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    methods: {
        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleDataFromChild(data) {
            this.dataFromChild = data;
        },
        getFiteredDataList(data) {
            // console.log(data, 'WWWW');
            this.getFilteredData = data;
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>