<template>
    <div>
        <div v-if="showModal" class="fixed sm:top-0 bottom-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

            <!-- Modal -->
            <div class="model bg-slate-100 sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
                :class="{ 'translate-y-0': isOpen, 'translate-y-full bottom-12': !isOpen }">
                <!-- <div class="modal-content "> -->
                <div class="justify-between items-center flex py-2 set-header-background">
                    <h2 class="text-white font-bold text-xl ml-3">
                        {{ title }}</h2>
                    <p class="close pr-5" @click="cancelModal">&times;</p>
                </div>
                <!-- Modal content -->
                <div class="p-4">
                    <!-- First child with minimum width on desktop and tablet views -->
                    <div v-if="currentStep === 0" class="w-full p-1">
                        <!--tooltip-->
                        <div v-if="isfocused.head" class="absolute flex flex-col items-center group-hover:flex -mt-6">
                            <span
                                class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                <p>select the below option to<font-awesome-icon icon="fa-regular fa-hand-pointer"
                                        class="px-1" size="lg" />add custom
                                    fields</p>
                            </span>
                            <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                        </div>
                        <!-- Second child with remaining width on desktop and tablet views, full width on mobile view -->
                        <p ref="customFieldsHeader" class="text-center underline py-2" tabindex="0"
                            @focus="isfocused.head = true">Add Custom Fields</p>
                        <div class="grid grid-cols-2 gap-2 justify-center">
                            <div v-for="(field, index) in fields_option" :key="index"
                                class="p-4 bg-white rounded shadow-sm shadow-blue-400 cursor-pointer hover:bg-green-100"
                                @click="openModal(field)">
                                <span v-if="index !== fields_option.length - 1" v-html="fields_create[index]"
                                    class="block text-center"></span>
                                <span v-else class="block text-center"><font-awesome-icon icon="fa-solid fa-angle-down"
                                        size="lg" /></span>
                                <span class="block text-center lg:text-sm sm:text-xs text-sm font-semibold pt-2">{{
                                    field
                                    }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'CreateFields',
    props: {
        showModal: Boolean,
        title: String,
        fields_option: Object,
        fields_create: Object,
        currentStep: Number
    },
    data() {
        return {
            isOpen: false,
            isfocused: {},
        };
    },
    methods: {
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        handleFocus() {
            this.$nextTick(() => {
                if (this.$refs.customFieldsHeader) {
                    this.$refs.customFieldsHeader.focus();
                }
            })
        },
        openModal(field) {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            this.$emit('openModal', field);
        }

    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                }
            }, 100);
        },
    }
};
</script>

<style scoped>
/* Modal styling */
.modal {
    position: fixed;
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Make modal content scrollable on mobile */
.modal-content {
    overflow-y: auto;
    /* Add this line to make the content scrollable */
}

#gst_number,
#business_name {
    text-transform: uppercase;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;


    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>
