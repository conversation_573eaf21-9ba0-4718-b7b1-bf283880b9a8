# Track New - Service Management System

A comprehensive service management system built with Node.js and React.js, recreated from the original Laravel/Vue.js version with enhanced features and modern architecture.

## 🚀 Features

### Core Modules
- **🔐 Authentication & Authorization** - JWT-based auth with role-based access control
- **📊 Dashboard** - Real-time analytics and key metrics
- **🔧 Service Management** - Complete service lifecycle tracking
- **👥 Customer Management** - Customer profiles and relationship management
- **📈 Lead Management** - Lead tracking and conversion pipeline
- **📋 AMC Management** - Annual maintenance contract handling
- **💰 Sales Management** - Sales orders and payment tracking
- **📦 Inventory Management** - Product catalog and stock management
- **📄 Estimation & Quotation** - Quote generation and management
- **📊 Reporting & Analytics** - Comprehensive business reports
- **📱 Mobile Responsive** - Works seamlessly on all devices

### Advanced Features
- **🔔 Real-time Notifications** - Firebase push notifications
- **📧 Communication** - Email, SMS, and WhatsApp integration
- **💳 Payment Integration** - Multiple payment gateway support
- **📁 File Management** - Document upload and management
- **🔍 Advanced Search** - Global search across all modules
- **📱 PWA Support** - Progressive Web App capabilities
- **🌐 Multi-language** - Internationalization support
- **🎨 Customizable UI** - Tailwind CSS with theme support

## 🛠️ Technology Stack

### Backend (Node.js)
- **Framework**: Express.js
- **Database**: MySQL with Sequelize ORM
- **Authentication**: JWT with refresh tokens
- **Validation**: Express Validator
- **File Upload**: Multer with AWS S3 support
- **Email**: Nodemailer
- **SMS**: Twilio integration
- **Push Notifications**: Firebase Admin SDK
- **Logging**: Winston
- **Security**: Helmet, Rate limiting, CORS

### Frontend (React.js)
- **Framework**: React 18 with Hooks
- **State Management**: Redux Toolkit
- **Routing**: React Router v6
- **UI Framework**: Tailwind CSS
- **Forms**: React Hook Form with Yup validation
- **HTTP Client**: Axios with interceptors
- **Charts**: Chart.js and Recharts
- **Notifications**: React Hot Toast
- **Date Handling**: Moment.js
- **File Handling**: React Dropzone

## 📋 Prerequisites

- **Node.js** 16+ 
- **npm** or **yarn**
- **MySQL** 8.0+
- **Git**

## ⚡ Quick Start

### Option 1: Using Quick Start Script (Recommended)

```bash
# Make the script executable and run it
chmod +x quick-start.sh
./quick-start.sh
```

The script will guide you through the complete setup process.

### Option 2: Manual Setup

#### 1. Clone the Repository
```bash
git clone <repository-url>
cd track-new
```

#### 2. Backend Setup
```bash
cd tracknew-nodejs-backend
npm install
cp .env.example .env
# Update .env with your database credentials
npm run migrate
npm run seed
npm run dev
```

#### 3. Frontend Setup
```bash
cd tracknew-react-frontend
npm install
# Create .env file with API URL
echo "REACT_APP_API_URL=http://localhost:8000/api" > .env
npm start
```

#### 4. Access the Application
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8000
- **Health Check**: http://localhost:8000/health

## 🔑 Default Login Credentials

After running the database seeds:

```
Email: <EMAIL>
Password: password123
```

## 📁 Project Structure

```
track-new/
├── tracknew-nodejs-backend/     # Node.js Backend
│   ├── src/
│   │   ├── config/             # Configuration files
│   │   ├── controllers/        # Route controllers
│   │   ├── middleware/         # Custom middleware
│   │   ├── models/            # Database models
│   │   ├── routes/            # API routes
│   │   ├── services/          # Business logic
│   │   ├── utils/             # Utility functions
│   │   └── validators/        # Input validation
│   ├── uploads/               # File uploads
│   ├── logs/                  # Application logs
│   └── server.js             # Main server file
│
├── tracknew-react-frontend/     # React Frontend
│   ├── public/                # Static files
│   ├── src/
│   │   ├── components/        # Reusable components
│   │   ├── pages/            # Page components
│   │   ├── store/            # Redux store
│   │   ├── services/         # API services
│   │   ├── hooks/            # Custom hooks
│   │   ├── utils/            # Utility functions
│   │   └── styles/           # CSS files
│   └── package.json
│
├── SETUP_INSTRUCTIONS.md       # Detailed setup guide
├── quick-start.sh             # Quick setup script
└── README.md                  # This file
```

## 🔧 Configuration

### Backend Environment Variables

```env
# Application
NODE_ENV=development
PORT=8000
APP_NAME=Track New API

# Database
DB_HOST=localhost
DB_PORT=3306
DB_NAME=tracknew_development
DB_USERNAME=root
DB_PASSWORD=your_password

# JWT
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=7d

# Email (Optional)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### Frontend Environment Variables

```env
REACT_APP_API_URL=http://localhost:8000/api
REACT_APP_APP_NAME=Track New
REACT_APP_DEBUG=true
```

## 📚 API Documentation

The API documentation is available at:
- **Development**: http://localhost:8000/api-docs
- **Swagger JSON**: http://localhost:8000/api-docs.json

### Key API Endpoints

```
Authentication:
POST /api/auth/register     # Register new user
POST /api/auth/login        # User login
POST /api/auth/logout       # User logout
GET  /api/auth/me          # Get current user

Services:
GET    /api/services       # Get all services
POST   /api/services       # Create new service
GET    /api/services/:id   # Get service details
PUT    /api/services/:id   # Update service
DELETE /api/services/:id   # Delete service

Customers:
GET    /api/customers      # Get all customers
POST   /api/customers      # Create new customer
GET    /api/customers/:id  # Get customer details
PUT    /api/customers/:id  # Update customer
DELETE /api/customers/:id  # Delete customer
```

## 🧪 Testing

### Backend Testing
```bash
cd tracknew-nodejs-backend
npm test
```

### Frontend Testing
```bash
cd tracknew-react-frontend
npm test
```

## 🚀 Deployment

### Production Build

#### Backend
```bash
cd tracknew-nodejs-backend
npm install --production
NODE_ENV=production npm start
```

#### Frontend
```bash
cd tracknew-react-frontend
npm run build
# Serve the build folder with a web server
```

### Docker Deployment (Coming Soon)
```bash
docker-compose up -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues:

1. Check the [SETUP_INSTRUCTIONS.md](SETUP_INSTRUCTIONS.md) for detailed setup guide
2. Review the logs in both backend and frontend terminals
3. Ensure all environment variables are configured correctly
4. Verify that MySQL is running and accessible

### Common Issues

- **Port already in use**: Change the PORT in `.env` files
- **Database connection error**: Check MySQL credentials and ensure database exists
- **CORS issues**: Verify API URL in frontend configuration
- **Module not found**: Delete `node_modules` and run `npm install` again

## 🔄 Migration from Original System

This system maintains compatibility with the original Track New data structure while providing:
- Enhanced performance with Node.js backend
- Modern React.js frontend with better UX
- Improved security and authentication
- Better scalability and maintainability
- Enhanced mobile responsiveness

## 🎯 Roadmap

- [ ] Mobile app (React Native)
- [ ] Advanced analytics dashboard
- [ ] AI-powered insights
- [ ] Multi-tenant architecture
- [ ] Advanced workflow automation
- [ ] Integration marketplace
- [ ] Advanced reporting engine

## 👥 Team

- **Backend Development**: Node.js/Express.js
- **Frontend Development**: React.js/Redux
- **Database Design**: MySQL/Sequelize
- **UI/UX Design**: Tailwind CSS

---

**Track New** - Streamlining service management for modern businesses 🚀
