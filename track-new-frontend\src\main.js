import "./bootstrap";
import { createApp, ref, provide, inject } from "vue";
import { startFirebasePush } from "./firebaseConfig";

import App from "./App.vue";

import toaster from "./components/supporting/toaster.vue";

import router from "./router/index.js";

import store from "./store/index.js";

//---loaders--
import skeleton from "./components/supporting/skeleton.vue";
import loader from "./components/supporting/dialog_box/loader.vue";
import pointsLoader from "./components/supporting/pointsLoader.vue";
import circleLoader from "./components/supporting/dialog_box/circleLoader.vue";
import expirePlan from "./components/supporting/dialog_box/expirePlan.vue";

import VueHtmlToPaper from "vue-html-to-paper";

import "./assets/main.css";

import axios from "axios";

/* import the fontawesome core */
import { library } from "@fortawesome/fontawesome-svg-core";

/* import font awesome icon component */
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";

/* import specific icons */
import { fas } from "@fortawesome/free-solid-svg-icons";
import { far } from "@fortawesome/free-regular-svg-icons";
import { fab } from "@fortawesome/free-brands-svg-icons";

//---import date picker---
import DatepickerDirective from "./components/supporting/datepicker/v-datepicker";

/* add icons to the library */
library.add(fas, fab, far);

/// Set base URL based on hostname
const hostname = window.location.hostname;
let baseURL;

if (hostname === "app.track-new.com") {
  // Production URL
  baseURL = "https://api.track-new.com/api";
} else if (hostname === "devapp.track-new.com") {
  // Development URL
  baseURL = "https://devapi.track-new.com/api";
} else if (hostname === " ************" || hostname === " ************:8000") {
  // Local development URL (adjust as needed)
  baseURL = "https://devapi.track-new.com/api";
  // baseURL = 'https://api.track-new.com/api';
} else {
  // Default fallback URL
  baseURL = "https://devapi.track-new.com/api";
}

// Set base URL globally for Axios
axios.defaults.baseURL = baseURL;
// Set base URL globally for Axios
// axios.defaults.baseURL = 'https://api.track-new.com/api';

// Define the global modal state
const modalState = ref({ showExpirePlan: false });

// Attach base URL to the Vue instance prototype
const vueApp = createApp(App);
// Provide modal state to the whole app
vueApp.provide("modalState", modalState);

// Add an interceptor to include the token from local storage in the request headers
axios.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("track_new");
    if (token) {
      let token_parse = JSON.parse(token);
      if (token_parse) {
        config.headers.Authorization = `Bearer ${token_parse.token}`;
      }
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);
axios.interceptors.response.use(
  (response) => response,
  (error) => {
    if (
      error.response &&
      error.response.data &&
      error.response.data.message ===
        "Your subscription payment has expired, Please renew the subscription"
    ) {
      modalState.value.showExpirePlan = true; // Show the modal
    } // Case 2: Unauthenticated
    else if (error.response.data.message === "Unauthenticated.") {
      // Remove 'track_new' from localStorage if it exists
      if (localStorage.getItem('track_new')) {
        localStorage.removeItem('track_new');
      }
      // Redirect to login page
      router.push('/login'); // Adjust the path if your login route is different
    }
    return Promise.reject(error);
  }
);
//---cookies globally Define the cookie methods---
// vueApp.config.globalProperties.$setCookie = (name, value, days) => {
//     let expires = "";
//     if (days) {
//         const date = new Date();
//         date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
//         expires = "; expires=" + date.toUTCString();
//     }
//     document.cookie = name + "=" + (value || "") + expires + "; path=/";
// };

// vueApp.config.globalProperties.$getCookie = (name) => {
//     const nameEQ = name + "=";
//     const cookiesArray = document.cookie.split(';');
//     for (let i = 0; i < cookiesArray.length; i++) {
//         let cookie = cookiesArray[i];
//         while (cookie.charAt(0) === ' ') cookie = cookie.substring(1, cookie.length);
//         if (cookie.indexOf(nameEQ) === 0) return cookie.substring(nameEQ.length, cookie.length);
//     }
//     return null;
// };

// vueApp.config.globalProperties.$eraseCookie = (name) => {
//     document.cookie = name + "=; Max-Age=-99999999;";
// };
//---App.vue router before validation--
router.beforeEach(async (to, from, next) => {
  const currentCompanyList = store.getters["companies/currentCompanyList"];
  let validate_expire = false;
  const non_plan = [
    "/subscription",
    "/login",
    "/mobilelogin",
    "/signup",
    "/pages",
  ];
  const { company_id, roles } = JSON.parse(localStorage.getItem("track_new")) || {};
  
  if (company_id && company_id !== "" && ['admin','Sub Admin'].includes( roles[0]) && currentCompanyList && Object.keys(currentCompanyList).length > 0) {
    const companyInfo = currentCompanyList;    
    
    if (companyInfo.expiry_date) {
     // Set expiry date to the end of the day (11:59 PM)
     const expiry_date = new Date(companyInfo.expiry_date);

     // Normalize expiry date to midnight (00:00:00)
     expiry_date.setHours(0, 0, 0, 0); // Set time to 00:00:00.000

     const current_date = new Date();
     current_date.setHours(0, 0, 0, 0); // Set time to 00:00:00.000

     // If the plan is still valid
     if (current_date > expiry_date) {
        validate_expire = true;
      }
    }
    if (
      (!companyInfo.expiry_date || validate_expire) &&
      !non_plan.includes(to.path)
    ) {
      return next("/subscription"); // Redirect to subscription page
    }
  }
  const anyModalOpen = store.state.modalIsOpen.anyModalOpen;
  if (anyModalOpen) {
    //   if (!confirm("A modal is open. Do you really want to leave?")) {
    store.commit("modalIsOpen/setModalState", false);
    return next(false);
    //   }
  } else {
    const path = to.path;
    // Dispatch the action to update sidebar and bottom selection based on the path
    store.dispatch("sidebarandBottombarList/updateSelectionBasedOnPath", path);
    // If no modals are open, allow the navigation
    next();
  }
});


//---spell check, autocomplete & autocorrect---
// Apply the attributes globally on input elements
vueApp.mixin({
  mounted() {
    // Get all input elements on the page
    const inputs = document.querySelectorAll("input");

    // Set autocomplete, autocorrect, and spellcheck attributes globally
    inputs.forEach((input) => {
      input.setAttribute("autocomplete", "off");
      input.setAttribute("autocorrect", "off");
      input.setAttribute("spellcheck", "false");
    });
  },
});
// Register the custom directive globally
vueApp.directive("datepicker", DatepickerDirective);
// Register globally imported components
vueApp.component("Skeleton", skeleton);
vueApp.component("Loader", loader);
vueApp.component("PointsLoader", pointsLoader);

vueApp.component("CircleLoader", circleLoader);
vueApp.component("ExpirePlan", expirePlan);
vueApp.component("font-awesome-icon", FontAwesomeIcon);

// Register the toast component globally
vueApp.component("Toaster", toaster);

vueApp.use(store);
// vueApp.use(router);
vueApp.use(VueHtmlToPaper);
// Start Firebase Push Notifications
startFirebasePush(); // <--- Call startFirebasePush to initialize push notifications

// Use the GlobalComponents plugin
vueApp.use(router).mount("#app");
