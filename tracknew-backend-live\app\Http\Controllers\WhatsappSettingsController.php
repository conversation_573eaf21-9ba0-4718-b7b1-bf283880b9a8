<?php

namespace App\Http\Controllers;

use App\DataTables\WhatsappSettingsDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateWhatsappSettingsRequest;
use App\Http\Requests\UpdateWhatsappSettingsRequest;
use App\Repositories\WhatsappSettingsRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class WhatsappSettingsController extends AppBaseController
{
    /** @var WhatsappSettingsRepository $whatsappSettingsRepository*/
    private $whatsappSettingsRepository;

    public function __construct(WhatsappSettingsRepository $whatsappSettingsRepo)
    {
        $this->whatsappSettingsRepository = $whatsappSettingsRepo;
    }

    /**
     * Display a listing of the WhatsappSettings.
     *
     * @param WhatsappSettingsDataTable $whatsappSettingsDataTable
     *
     * @return Response
     */
    public function index(WhatsappSettingsDataTable $whatsappSettingsDataTable)
    {
        return $whatsappSettingsDataTable->render('whatsapp_settings.index');
    }

    /**
     * Show the form for creating a new WhatsappSettings.
     *
     * @return Response
     */
    public function create()
    {
        return view('whatsapp_settings.create');
    }

    /**
     * Store a newly created WhatsappSettings in storage.
     *
     * @param CreateWhatsappSettingsRequest $request
     *
     * @return Response
     */
    public function store(CreateWhatsappSettingsRequest $request)
    {
        $input = $request->all();

        $whatsappSettings = $this->whatsappSettingsRepository->create($input);

        Flash::success('Whatsapp Settings saved successfully.');

        return redirect(route('whatsappSettings.index'));
    }

    /**
     * Display the specified WhatsappSettings.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $whatsappSettings = $this->whatsappSettingsRepository->find($id);

        if (empty($whatsappSettings)) {
            Flash::error('Whatsapp Settings not found');

            return redirect(route('whatsappSettings.index'));
        }

        return view('whatsapp_settings.show')->with('whatsappSettings', $whatsappSettings);
    }

    /**
     * Show the form for editing the specified WhatsappSettings.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $whatsappSettings = $this->whatsappSettingsRepository->find($id);

        if (empty($whatsappSettings)) {
            Flash::error('Whatsapp Settings not found');

            return redirect(route('whatsappSettings.index'));
        }

        return view('whatsapp_settings.edit')->with('whatsappSettings', $whatsappSettings);
    }

    /**
     * Update the specified WhatsappSettings in storage.
     *
     * @param int $id
     * @param UpdateWhatsappSettingsRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateWhatsappSettingsRequest $request)
    {
        $whatsappSettings = $this->whatsappSettingsRepository->find($id);

        if (empty($whatsappSettings)) {
            Flash::error('Whatsapp Settings not found');

            return redirect(route('whatsappSettings.index'));
        }

        $whatsappSettings = $this->whatsappSettingsRepository->update($request->all(), $id);

        Flash::success('Whatsapp Settings updated successfully.');

        return redirect(route('whatsappSettings.index'));
    }

    /**
     * Remove the specified WhatsappSettings from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $whatsappSettings = $this->whatsappSettingsRepository->find($id);

        if (empty($whatsappSettings)) {
            Flash::error('Whatsapp Settings not found');

            return redirect(route('whatsappSettings.index'));
        }

        $this->whatsappSettingsRepository->delete($id);

        Flash::success('Whatsapp Settings deleted successfully.');

        return redirect(route('whatsappSettings.index'));
    }
}
