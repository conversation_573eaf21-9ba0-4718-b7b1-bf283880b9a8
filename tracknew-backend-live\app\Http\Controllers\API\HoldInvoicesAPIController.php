<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateHoldInvoicesAPIRequest;
use App\Http\Requests\API\UpdateHoldInvoicesAPIRequest;
use App\Models\HoldInvoices;
use App\Repositories\HoldInvoicesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class HoldInvoicesController
 * @package App\Http\Controllers\API
 */

class HoldInvoicesAPIController extends AppBaseController
{
    /** @var  HoldInvoicesRepository */
    private $holdInvoicesRepository;

    public function __construct(HoldInvoicesRepository $holdInvoicesRepo)
    {
        $this->holdInvoicesRepository = $holdInvoicesRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/hold_invoices",
     *      summary="getHoldInvoicesList",
     *      tags={"HoldInvoices"},
     *      description="Get all HoldInvoices",
     *      @OA\Parameter(
     *          name="company_id",
     *          description="ID of the company whose services are to be fetched",
     *          @OA\Schema(
     *              type="string"
     *          ),
     *          required=true,
     *          in="query"
     *      ),
     *      @OA\Parameter(
     *          name="page",
     *          description="Page number for pagination",
     *          @OA\Schema(
     *              type="integer"
     *          ),
     *          in="query"
     *      ),
     *      @OA\Parameter(
     *          name="per_page",
     *          description="Number of items per page",
     *          @OA\Schema(
     *              type="integer"
     *          ),
     *          in="query"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/HoldInvoices")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $holdInvoicesQuery = HoldInvoices::where('company_id', $companyId);

        if ($perPage === 'all') {
            $perPage =  $holdInvoicesQuery->count();
        }

        $holdInvoices =  $holdInvoicesQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $holdInvoices->items(), 
            'pagination' => [
                'total' => $holdInvoices->total(),
                'per_page' => $holdInvoices->perPage(),
                'current_page' => $holdInvoices->currentPage(),
                'last_page' => $holdInvoices->lastPage(),
                'from' => $holdInvoices->firstItem(),
                'to' => $holdInvoices->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/hold_invoices",
     *      summary="createHoldInvoices",
     *      tags={"HoldInvoices"},
     *      description="Create HoldInvoices",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/HoldInvoices")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/HoldInvoices"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateHoldInvoicesAPIRequest $request)
    {
        $input = $request->all();

        $holdInvoices = $this->holdInvoicesRepository->create($input);

        return $this->sendResponse($holdInvoices->toArray(), 'Hold Invoices saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/hold_invoices/{id}",
     *      summary="getHoldInvoicesItem",
     *      tags={"HoldInvoices"},
     *      description="Get HoldInvoices",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of HoldInvoices",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/HoldInvoices"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var HoldInvoices $holdInvoices */
        $holdInvoices = $this->holdInvoicesRepository->find($id);

        if (empty($holdInvoices)) {
            return $this->sendError('Hold Invoices not found');
        }

        return $this->sendResponse($holdInvoices->toArray(), 'Hold Invoices retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/hold_invoices/{id}",
     *      summary="updateHoldInvoices",
     *      tags={"HoldInvoices"},
     *      description="Update HoldInvoices",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of HoldInvoices",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/HoldInvoices")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/HoldInvoices"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateHoldInvoicesAPIRequest $request)
    {
        $input = $request->all();

        /** @var HoldInvoices $holdInvoices */
        $holdInvoices = $this->holdInvoicesRepository->find($id);

        if (empty($holdInvoices)) {
            return $this->sendError('Hold Invoices not found');
        }

        $holdInvoices = $this->holdInvoicesRepository->update($input, $id);

        return $this->sendResponse($holdInvoices->toArray(), 'HoldInvoices updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/hold_invoices/{id}",
     *      summary="deleteHoldInvoices",
     *      tags={"HoldInvoices"},
     *      description="Delete HoldInvoices",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of HoldInvoices",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var HoldInvoices $holdInvoices */
        $holdInvoices = $this->holdInvoicesRepository->find($id);

        if (empty($holdInvoices)) {
            return $this->sendError('Hold Invoices not found');
        }

        $holdInvoices->delete();

        return $this->sendSuccess('Hold Invoices deleted successfully');
    }
}
