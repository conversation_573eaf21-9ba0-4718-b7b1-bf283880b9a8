<template>
    <div v-if="showSalesModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="flex justify-between items-center relative w-full bg-teal-600 px-3 py-2">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-2 text-lg">Select Sales
                    Type</p>
                <!-- Close button -->
                <!-- <button @click="closeSalesModal" class="rounded rounded-full "> -->
                <p class="close" @click="closeSalesModal">&times;</p>
                <!-- </button> -->
                <!-- <p class="text-lg font-bold">Select Sales Type</p> -->
            </div>
            <!-- <div class="block mt-4 text-sm">
                <label for="salesType" class="font-bold">Select Sales Type:</label><br>
                <select v-model="selectedSalesType" @change="handleSalesTypeSelection"
                    class="border border-gray-500 rounded px-2 py-1 mt-2">
                    <option v-if="selectedSalesType === ''" value="">select</option>
                    <option value="product">Product Sales</option>
                    <option value="service">Service Sales</option>
                </select>
            </div> -->
            <div class="block mt-4 text-sm bg p-4">
                <div class="grid grid-cols-1 gap-4">
                    <div>
                        <label for="product"
                            class="flex justify-center items-center px-5 py-5 border m-3 rounded rounded-lg hover:bg-blue-500 hover:text-white cursor-pointer">
                            <input type="radio" id="product" name="salesType" v-model="selectedSalesTypeData"
                                value="product" style="display: none;" @change="handleSalesTypeSelection">
                            Product / Services Sales
                        </label>
                    </div>
                    <!-- <div>
                        <label for="service"
                            class="flex justify-center items-center px-5 py-5 border m-3 rounded rounded-lg hover:bg-blue-500 hover:text-white cursor-pointer">
                            <input type="radio" id="service" name="salesType" v-model="selectedSalesType" value="service"
                                style="display: none;" @change="handleSalesTypeSelection">
                            Service Sales</label>
                    </div> -->
                </div>
            </div>
            <!-- Service Type Dropdown -->
            <!-- <div v-if="selectedSalesType === 'service'" class="mt-4 text-sm">
                <label for="serviceType" class="font-bold">Select Service Type:</label><br>
                <select v-if="serviceCategoriesList !== null && serviceCategoriesList" v-model="selectedServiceType"
                    @change="handleServiceTypeSelection" class="border border-gray-500 rounded px-2 py-1 mt-2">
                    <option v-for="(opt, index) in serviceCategoriesList" :key="index" :value="opt.service_category">{{
                        opt.service_category }}
                    </option>
                </select>
            </div> -->
            <!-- Service Type Options -->
            <div v-if="selectedSalesType === 'service'" class="mt-4 text-sm">
                <label class="font-bold ml-3">Select Service Type:</label><br>
                <div v-if="serviceCategoriesList !== null && serviceCategoriesList" class="grid grid-cols-2">
                    <div v-for="(opt, index) in serviceCategoriesList" :key="index">
                        <label :for="'serviceType' + index"
                            class="mt-2 flex justify-center items-center px-5 py-5 border m-3 rounded rounded-lg hover:bg-blue-500 hover:text-white">
                            <input type="radio" :id="'serviceType' + index" v-model="selectedServiceTypeData"
                                :value="opt.id" @change="handleServiceTypeSelection" style="display: none;">
                            {{ opt.service_category }}</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showSalesModal: Boolean,
        selectedSalesType: String,
        selectedServiceType: String,
        serviceCategoriesList: Object
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedServiceTypeData: null,

        };
    },
    methods: {
        closeSalesModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeSalesModal');
            }, 300);
        },
        handleSalesTypeSelection() {
            this.$emit('handleSalesTypeSelection', this.selectedSalesType);
        },
        handleServiceTypeSelection() {
            this.$emit('handleServiceTypeSelection', this.selectedServiceType);
        },
    },
    watch: {
        showSalesModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },

    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}
</style>