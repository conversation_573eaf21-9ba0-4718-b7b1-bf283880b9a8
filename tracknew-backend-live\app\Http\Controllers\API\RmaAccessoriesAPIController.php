<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateRmaAccessoriesAPIRequest;
use App\Http\Requests\API\UpdateRmaAccessoriesAPIRequest;
use App\Models\RmaAccessories;
use App\Repositories\RmaAccessoriesRepository;
use App\Http\Resources\api\RmaAccessoriesResource;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class RmaAccessoriesController
 * @package App\Http\Controllers\API
 */

class RmaAccessoriesAPIController extends AppBaseController
{
    /** @var  RmaAccessoriesRepository */
    private $rmaAccessoriesRepository;

    public function __construct(RmaAccessoriesRepository $rmaAccessoriesRepo)
    {
        $this->rmaAccessoriesRepository = $rmaAccessoriesRepo;
    }

    /**
     * Display a listing of the RmaAccessories.
     * GET|HEAD /rmaAccessories
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
      
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

         // Base query to filter by company_id
        $rmaQuery = RmaAccessories::where('company_id', $companyId);
    
    
      
    
        // Adjust perPage for all records request
        if ($perPage === 'all') {
            $perPage = $rmaQuery->count();
        }
    
        $rma = $rmaQuery->orderBy('updated_at', 'desc')->paginate($perPage, ['*'], 'page', $page);
    

        $response = [
            'success' => true,
            'data' => RmaAccessoriesResource::collection($rma),//$sales->items(),LeadResource::collection($sales), // Get the paginated items
            'pagination' => [
                'total' => $rma->total(),
                'per_page' => $rma->perPage(),
                'current_page' => $rma->currentPage(),
                'last_page' => $rma->lastPage(),
                'from' => $rma->firstItem(),
                'to' => $rma->lastItem()
            ],
        
        ];
        
        return response()->json($response);
      
    }

    /**
     * Store a newly created RmaAccessories in storage.
     * POST /rmaAccessories
     *
     * @param CreateRmaAccessoriesAPIRequest $request
     *
     * @return Response
     */
    public function store(CreateRmaAccessoriesAPIRequest $request)
    {
        $input = $request->all();

        $rmaAccessories = $this->rmaAccessoriesRepository->create($input);

        return $this->sendResponse($rmaAccessories->toArray(), 'Rma Accessories saved successfully');
    }

    /**
     * Display the specified RmaAccessories.
     * GET|HEAD /rmaAccessories/{id}
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        /** @var RmaAccessories $rmaAccessories */
        $rmaAccessories = $this->rmaAccessoriesRepository->find($id);

        if (empty($rmaAccessories)) {
            return $this->sendError('Rma Accessories not found');
        }

        return $this->sendResponse($rmaAccessories->toArray(), 'Rma Accessories retrieved successfully');
    }

    /**
     * Update the specified RmaAccessories in storage.
     * PUT/PATCH /rmaAccessories/{id}
     *
     * @param int $id
     * @param UpdateRmaAccessoriesAPIRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateRmaAccessoriesAPIRequest $request)
    {
        $input = $request->all();

        /** @var RmaAccessories $rmaAccessories */
        $rmaAccessories = $this->rmaAccessoriesRepository->find($id);

        if (empty($rmaAccessories)) {
            return $this->sendError('Rma Accessories not found');
        }

        $rmaAccessories = $this->rmaAccessoriesRepository->update($input, $id);

        return $this->sendResponse($rmaAccessories->toArray(), 'RmaAccessories updated successfully');
    }

    /**
     * Remove the specified RmaAccessories from storage.
     * DELETE /rmaAccessories/{id}
     *
     * @param int $id
     *
     * @throws \Exception
     *
     * @return Response
     */
    public function destroy($id)
    {
        /** @var RmaAccessories $rmaAccessories */
        $rmaAccessories = $this->rmaAccessoriesRepository->find($id);

        if (empty($rmaAccessories)) {
            return $this->sendError('Rma Accessories not found');
        }

        $rmaAccessories->delete();

        return $this->sendSuccess('Rma Accessories deleted successfully');
    }
}
