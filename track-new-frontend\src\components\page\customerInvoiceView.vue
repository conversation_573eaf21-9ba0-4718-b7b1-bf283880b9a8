<template>
    <div class="flex h-screen">
        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full">
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
                :rows="number_of_rows" :gap="gap" :type="'grid'">
            </skeleton>
            <!-- print preview data -->
            <div v-if="!open_skeleton" class="relative w-full px-3 py-3">
                <template_2 :typeOfInvoice="typeOfInvoice" :formData="invoice_setting" :customer_data="customer_data"
                    :items_data="items" :get_all_data="get_all_data" :paymentData="paymentData"
                    :invoice_data="collect_invoice_data" :return_amount="formValues"
                    :logo_img="company_data ? company_data.logo : logo_img" :exist_data="exist_data"
                    :companyId="companyId"></template_2>
            </div>
        </div>
    </div>
</template>
<script>
import template_2 from '../supporting/setting_categories/invoiceTemplates/template_2.vue';

export default {
    name: 'invoice_view',
    components: {
        template_2
    },
    data() {
        return {
            isMobile: false,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            data: [],
            shareData: {},
            service_id: null,
            service_data: {},
            //---api integration---
            companyId: null,
            userId: null,
            exist_data: null,
            typeOfInvoice: 'Product',
            invoice_setting: null,
            customer_data: {},
            items: [],
            get_all_data: {},
            paymentData: {},
            collect_invoice_data: {},
            formValues: {},
            logo_img: '/images/head_bar/logo_01.png',
            shipping_details: {},
            company_data: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 20,
            gap: 5,
        };
    },
    methods: {
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        // Method to parse the URL
        parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const category = urlParts[urlParts.length - 4];
            const categoryID = urlParts[urlParts.length - 3]
            const id = urlParts[urlParts.length - 1];
            this.category_id = Number(categoryID);
            this.service_id = decodeURIComponent(id);
            this.category_name = decodeURIComponent(category);
        },
        goBackPage() {
            this.$router.go(1);
        },
        getDataById() {
            this.open_skeleton = true;
            // console.log(this.$route.query.invoice_no, 'EEEEE');
            if (this.$route.query.type !== 'estimation') {
                axios.get(`/sales/${this.$route.query.invoice_no}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        console.log(response.data, 'get response..!', this.typeOfInvoice);
                        this.open_skeleton = false;
                        this.exist_data = response.data.data;
                        this.initital_code = this.exist_data.invoice_prefix;
                        this.invoice_num = this.exist_data.invoice_no;
                        this.selectedOption_invoice_type = this.exist_data.invoice_to;
                        this.typeOfInvoice = this.exist_data.invoice_type;
                        // this.typeOfInvoice = this.exist_data.invoice_type;
                        this.over_all_discount = { value: this.exist_data.discount >= 0 ? this.exist_data.discount : 0, type: this.exist_data.discount_type ? this.exist_data.discount_type : 'Fixed' };
                        this.formValues.balance = this.exist_data.due_amount;
                        this.paymentData = JSON.parse(this.exist_data.payment_mode);
                        this.items = this.exist_data.sales_item_data.map(item => ({
                            ...item,
                            product_type: 'Product'
                        }));
                        // console.log(this.typeOfInvoice, 'EEEEEEEEEEEEEEEEEEEEEEE');
                        // if (this.typeOfInvoice === 'sales' || this.typeOfInvoice === 'services' || this.typeOfInvoice === 'direct_services') {
                        const parsedServiceItems = JSON.parse(this.exist_data.service_items);
                        if (Array.isArray(parsedServiceItems)) {
                            this.items = [...parsedServiceItems, ...this.items];
                        }
                        // }
                        // Loop through each object in the array
                        this.items.forEach(item => {
                            // Check if the item has a discount_data key and its value is an object
                            if (item.discount_data) {
                                // Stringify the discount_data object
                                item.discount_data = JSON.parse(item.discount_data);
                            }
                            if (item.serial_no) {
                                // Stringify the discount_data object
                                item.serial_no = JSON.parse(item.serial_no);
                            }
                        });

                        this.shipping_details.shipping = this.exist_data.shipping;
                        this.get_all_data.shipping = this.exist_data.shipping;
                        this.shipping_details.shipping_type = this.exist_data.shipping_type;
                        this.shipping_details.cod = this.exist_data.cod;
                        this.customer_data = this.exist_data.customer;
                        this.get_all_data.total_qty = this.exist_data.total_qty;
                        this.get_all_data.sub_total = this.exist_data.sub_total;
                        this.get_all_data.grand_total = this.exist_data.grand_total;
                        this.formValues.balance = this.exist_data.balance_amount;
                        this.formValues.return = this.exist_data.return_amount;
                        this.formValues.note = this.exist_data.payment_notes;
                        this.get_all_data.grand_total = this.exist_data.payment_amount;

                        console.log(this.items, 'list of data is...!');
                        // this.totalQuantity();
                        // this.totalAmount();
                        // this.totalDiscount();
                        this.updateTotal();
                        this.grandTotal();
                        // this.overAllDiscount();
                        // this.overAllTax();                    
                        this.getInvoiceNumAndType();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            } else if (this.$route.query.type === 'estimation') {
                this.typeOfInvoice = 'estimation';
                axios.get(`/estimations/${this.$route.query.est_no}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        console.log(response.data, 'estimation by edit..!');
                        this.open_skeleton = false;
                        this.exist_data = response.data.data;
                        let string01 = this.exist_data.estimate_num;
                        let [prefix, number] = string01.match(/([A-Za-z]+)(\d+)/).slice(1);
                        this.initital_code = prefix;
                        this.invoice_num = number;
                        this.get_all_data.grand_total = this.exist_data.grand_total;
                        this.items = JSON.parse(this.exist_data.items);
                        this.customer_data = this.exist_data.customers;
                        this.iswhatsapp = this.exist_data.iswhatsapp;
                        this.issms = this.exist_data.issms;
                        let parse_data = JSON.parse(this.exist_data.data);
                        this.shipping_details = parse_data.shipping ? parse_data.shipping : {};
                        this.over_all_discount = parse_data.over_all_discount;
                        this.selectedOption_invoice_type = parse_data.invoice_to;
                        this.updateTotal();
                        this.totalAmount();
                        this.grandTotal();
                        this.getInvoiceNumAndType();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        //get invoice setting---
        getInvoiceData() {
            axios.get('/invoice_settings', { params: { company_id: this.companyId } })
                .then(response => {
                    console.log(response.data, 'invoice setting');
                    this.invoice_setting = response.data.data;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        // totalTax() {
        //     let total_tax = this.items.reduce((sum, item) => 1*sum + 1*item.tax, 0);
        //     console.log(total_tax, 'Total tax calculated successfully');
        //     this.get_all_data.total_tax = total_tax;
        // },
        getInvoiceNumAndType() {
            this.collect_invoice_data = { current_date: this.exist_data.current_date.substring(0, 10), invoice_number: this.$route.query.type !== 'estimation' ? this.initital_code + '' + this.invoice_num : this.exist_data.estimate_num, invoice_type: this.selectedOption_invoice_type, shipping_type: this.shipping_details.shipping_type, cod: this.shipping_details.cod };
        },
        //--total qty---
        totalQuantity() {
            let count_total = this.items.reduce((sum, item) => sum + item.qty, 0);
            if (this.get_all_data === null) {
                this.get_all_data = { total_qty: count_total };
            } else {
                this.get_all_data.total_qty = count_total;
            }
            return count_total;
        },
        //---total amount--
        totalAmount() {
            let sub_total = this.items.reduce((sum, item) => (1 * sum) + (1 * item.total), 0);
            if (this.get_all_data === null) {
                this.get_all_data = { sub_total: sub_total };
            } else {
                this.get_all_data.sub_total = sub_total;
            }
            return sub_total;
        },
        //---total discount--
        totalDiscount() {
            return this.items.reduce((sum, item) => (1 * sum) + (1 * item.discount), 0);
        },
        //--grandTotal--
        grandTotal() {
            let totalDiscount = this.totalDiscount();
            let overallDiscount = this.over_all_discount !== null ? this.overAllDiscount() : 0;
            let totalAmount = this.items.reduce((sum, item) => sum + parseFloat(item.total), 0);
            let shipping_charge = this.shipping_details.shipping > 0 ? this.shipping_details.shipping : 0;
            let total_tax = this.overAllTax().toFixed(2);
            let grandTotal = (totalAmount + shipping_charge) - (1 * overallDiscount);
            if (this.get_all_data === null) {
                this.get_all_data = { discount_total: (overallDiscount).toFixed(2), shipping: shipping_charge, grand_total: Math.round(grandTotal), total_tax: total_tax };
            } else {
                this.get_all_data.discount_total = (1 * overallDiscount).toFixed(2);
                this.get_all_data.shipping = shipping_charge;
                this.get_all_data.grand_total = Math.round(grandTotal);
                this.get_all_data.total_tax = total_tax;
            }
            return Math.round(grandTotal);
        },
        //---over all discount---
        overAllDiscount() {
            if (this.over_all_discount !== null) {
                return this.over_all_discount.type === 'Percentage' ? (this.totalAmount() * (this.over_all_discount.value / 100)).toFixed(2) : this.over_all_discount.value;
            } else {
                return 0;
            }
        },
        //--total tax---
        overAllTax() {
            let calculate_tax = this.items.reduce((sum, item) => (1 * sum) + (1 * item.tax), 0);
            // console.log(calculate_tax, 'TTTTT');
            return calculate_tax;
        },
        getExistData() {
            axios.get(`/companies/${this.companyId}`, { params: { company_id: this.companyId } })
                .then(response => {
                    console.log(response.data, 'Response');
                    this.company_data = response.data.data;
                })
                .catch(error => {
                    console.error('Error', error);
                    // this.openMessage(error.response.data.message);
                })
        },
        updateTotal() {
            // Iterate through each item to update totals and tax values
            this.items.forEach((item) => {
                // Calculate total based on price and tax
                // let item_total_value = item.qty * item.price;
                console.log(item, 'What happeni......@@@@@');
                if (item.tax_type === "Exclusive") {
                    // Calculate tax value                   
                    item.discount = item.discount_data && item.discount_data.type === 'Percentage' ? ((item.qty * item.price) * ((1 * item.discount_data.value) / 100)).toFixed(2) : item.discount_data ? item.discount_data.value : 0;
                    let make_price = (item.qty * item.price) - (1 * item.discount);
                    // console.log(make_price, 'YYYYYYYYYYYYYYYYYYYYYYYYYYY');
                    let qty_price = 1 * make_price;
                    let tax_amount = qty_price * (1 + ((1 * item.taxvalue) / 100));
                    // console.log(tax_amount, 'What happening..!', qty_price);
                    item.tax = (tax_amount - qty_price).toFixed(2);
                    // //--get total
                    item.total = (tax_amount).toFixed(2);

                } else {
                    item.discount = item.discount_data && item.discount_data.type === 'Percentage' ? ((item.qty * item.price) * ((1 * item.discount_data.value) / 100)).toFixed(2) : item.discount_data ? item.discount_data.value : 0;
                    let make_price = (item.qty * item.price) - (1 * item.discount);
                    // console.log(make_price, 'YYYYYYYYYYYYYYYYYYYYYYYYYYY');
                    item.total = (1 * make_price).toFixed(2);
                    // Calculate tax value
                    item.tax = (item.total - (item.total / (1 + (1 * item.taxvalue) / 100))).toFixed(2);
                }

            });

            // Update sub-total and grand total
            this.subTotal = this.calculateSubTotal();
        },
        calculateSubTotal() {
            const subTotal = this.items.reduce((sum, item) => sum + Number(item.total), 0);
            // console.log('Sub Total:', subTotal);
            return subTotal;
        },

    },
    mounted() {
        this.updateIsMobile(); // Initial check
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');

            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        if (this.invoice_setting === null && this.companyId) {
            this.getInvoiceData();
        }
        if (this.$route.query.invoice_no || this.$route.query.est_no) {
            this.getDataById();
        }
        if (this.company_data) {
            this.getExistData();
        }
        this.parseUrl(); // Call the function to parse the URL
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },

};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}

/* Center content on larger screens */
.center-screen {
    display: flex;
    align-items: center;
    justify-content: center;
}

@media print {

    /* Additional styles for printing */
    body {
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */
    }

    .non-printable {
        display: none;
    }

    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }

    /* Apply different styles for printing */
    .center-content {
        width: 100%;
        margin: 0;
    }
}
</style>