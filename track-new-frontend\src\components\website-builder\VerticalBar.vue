<template>
  <div class="text-sm">
    <!-- Sidebar for Desktop and Tablet -->
    <div class="relative w-full">
      <div class="flex flex-col space-y-1 pt-1 custom-scrollbar-hidden overflow-y-auto">
        <div v-for="item in items" :key="item.id" class="cursor-pointer py-2  transition-all duration-300"
          :class="{ 'rounded-lg hover:bg-blue-100 hover:text-black px-4': isMobile, 'hover:bg-[#0d8bf1] hover:text-white px-2': !isMobile, 'bg-white font-semibold text-gray-600 p-4': (selectedItem === item.id && shouldDisplayVerticalBar) && isMobile, 'text-white': (selectedItem !== item.id || !shouldDisplayVerticalBar) && isMobile, 'text-[#0d8bf1]': (selectedItem !== item.id || !shouldDisplayVerticalBar) && !isMobile, 'bg-[#0d8bf1] text-white p-4': (selectedItem === item.id && shouldDisplayVerticalBar) && !isMobile }"
          @click="selectItem(item.id)">
          <font-awesome-icon :icon="item.icon.split(' ')" class="mr-2" />
          <span>{{ item.label }}</span>
        </div>
      </div>
    </div>
    <!-- Horizontal Bar for Mobile -->
    <!-- <div v-if="isMobile" class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t p-2 z-50">
      <div class="flex justify-between">
        <div v-for="item in items" :key="item.id"
          class="cursor-pointer flex-1 text-center py-2 hover:bg-blue-100 transition-all duration-300 rounded-lg"
          :class="{ 'bg-blue-500 text-white': selectedItem === item.id, 'text-gray-700': selectedItem !== item.id }"
          @click="selectItem(item.id)">
          <font-awesome-icon :icon="item.icon.split(' ')" class="text-xl" />
          <div class="text-xs mt-1">{{ item.label }}</div>
        </div>
      </div>
    </div> -->
  </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  props: {
    isMobile: {
      type: Boolean,
      required: true
    },
    selectedItem: {
      type: Number, // Ensure selectedItem is passed as a number from the parent
      default: 1    // Default selected item
    }
  },
  data() {
    return {
      items: [
        { id: 2, label: 'Site Setting', icon: 'fa-solid fa-globe' },
        { id: 3, label: 'Home page Setting', icon: 'fa-solid fa-gear' },
        { id: 4, label: 'Home Page Slider', icon: 'fa-solid fa-sliders' },
        { id: 5, label: 'About Page', icon: 'fa-solid fa-info-circle' },
        { id: 6, label: 'Services', icon: 'fa-solid fa-tools' },
        { id: 7, label: 'Brochure Page', icon: 'fa-solid fa-file-pdf' },
        { id: 8, label: 'Product Page', icon: 'fa-solid fa-box' },
        { id: 9, label: 'Product Categories', icon: 'fa-solid fa-table-columns' },
        { id: 10, label: 'Testimonial Page', icon: 'fa-solid fa-comment-dots' },
        { id: 11, label: 'Gallery', icon: 'fa-solid fa-photo-video' },
        { id: 12, label: 'Company Video', icon: 'fa-solid fa-video' },
        { id: 13, label: 'Business Hours', icon: 'fa-solid fa-calendar-check' }
      ],
      completedSteps: [],
    };
  },
  computed: {
    ...mapGetters('websiteBuilder', ['selectedId']),
    shouldDisplayVerticalBar() {
      return this.$route.path === '/websites/settings'; // Show VerticalBar only on '/websites/settings' route
    },
  },

  methods: {
    ...mapActions('websiteBuilder', ['updateSelectdItem']),
    selectItem(itemId) {
      if (this.$route.path === '/websites/settings') {
        // Emit the selected item to the parent component
        this.$emit('item-selected', itemId);
        if (!this.completedSteps.includes(itemId)) {
          this.completedSteps.push(itemId);
        }
      } else {
        this.$router.push('/websites/settings');
      }

      this.updateSelectdItem(itemId);
    }
  },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
  display: none;
}
</style>
