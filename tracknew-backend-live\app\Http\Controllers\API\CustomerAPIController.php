<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateCustomerAPIRequest;
use App\Http\Requests\API\UpdateCustomerAPIRequest;
use App\Models\Customer;
use App\Models\Companies;
use App\Models\Sales;
use App\Models\MessageTransactions;
use App\Repositories\CustomerRepository;
use App\Http\Resources\api\CustomerResource;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Http\Services\RelayMessage;
use App\Http\Services\SmsDeliveryService;
use Carbon\Carbon;
use Response;
use Auth;

/**
 * Class CustomerController
 * @package App\Http\Controllers\API
 */

class CustomerAPIController extends AppBaseController
{
    /** @var  CustomerRepository */
    private $customerRepository;
    private $smsDeliveryService;

    public function __construct(CustomerRepository $customerRepo, SmsDeliveryService $smsDeliveryService)
    {
        $this->smsDeliveryService = $smsDeliveryService;
        $this->customerRepository = $customerRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/customers",
     *      summary="getCustomerList",
     *      tags={"Customer"},
     *      description="Get all Customers",
     *      @OA\Parameter(
     *         name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Customer")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
//         $customers = $this->customerRepository->all(
//             $request->except(['skip', 'limit']),
//             $request->get('skip'),
//             $request->get('limit')
//         );

//         return $this->sendResponse($customers->toArray(), 'Services updated successfully');
// exit();
        $companyId = $request->query('company_id');
        
        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 

        $perPage = $request->query('per_page', 1000);
        $page = $request->query('page', 1);

        $customersQuery = Customer::where('company_id', $companyId);
        
        if ($perPage === 'all') {
            $perPage = $customersQuery->count();
        }

        $customers = $customersQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
        
        $customers->each(function ($customer) {
            $customer->balance_amount = (float) Sales::where('client_id', $customer->id)->where('status', 'Success')->sum('balance_amount');
            $customer->return_amount  = (float) Sales::where('client_id', $customer->id)->where('status', 'Success')->sum('return_amount');
        });
        
        $response = [
            'success' => true,
            'data' => $customers->items(), // Get the paginated items
            'pagination' => [
                'total' => $customers->total(),
                'per_page' => $customers->perPage(),
                'current_page' => $customers->currentPage(),
                'last_page' => $customers->lastPage(),
                'from' => $customers->firstItem(),
                'to' => $customers->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/customers",
     *      summary="createCustomer",
     *      tags={"Customer"},
     *      description="Create Customer",
      *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Customer")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Customer"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateCustomerAPIRequest $request)
    {
        $input = $request->all();

        //$customer = Customer::where('contact_number',$input['contact_number'])->where('company_id',$input['company_id'])->first();       

       // if ($customer) {
        //    return $this->sendError('Contact number is already Exist');
       // }

        $customer = $this->customerRepository->create($input);

        return $this->sendResponse($customer->toArray(), 'Customer saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/customers/{id}",
     *      summary="getCustomerItem",
     *      tags={"Customer"},
     *      description="Get Customer",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Customer",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Customer"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Customer $customer */
        $user = Auth::user();
        
        $company_id = $user->company_id;
        
        $customer = Customer::where('id', $id)->where('company_id', $company_id)->first();
        
        
            $customer->balance_amount = (float) Sales::where('client_id', $id)->sum('balance_amount');
            $customer->return_amount  = (float) Sales::where('client_id', $id)->sum('return_amount');
      
        
        // $customer = $this->customerRepository->find($id);
       
        if (empty($customer)) {
            return $this->sendError('Customer not found');
        }

        return $this->sendResponse($customer->toArray(), 'Customer retrieved successfully123');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/customers/{id}",
     *      summary="updateCustomer",
     *      tags={"Customer"},
     *      description="Update Customer",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Customer",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Customer")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Customer"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateCustomerAPIRequest $request)
    {
        $input = $request->all();

        /** @var Customer $customer */
        $customer = $this->customerRepository->find($id);

        if (empty($customer)) {
            return $this->sendError('Customer not found');
        }

        $customer = $this->customerRepository->update($input, $id);

        return $this->sendResponse($customer->toArray(), 'Customer updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/customers/{id}",
     *      summary="deleteCustomer",
     *      tags={"Customer"},
     *      description="Delete Customer",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Customer",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy(Request $request, $id)
    {
        
        $user = Auth::user();
        $companyId = $request->query('company_id');
        if($user->company_id == $companyId){
        /** @var Customer $customer */
            $customer = $this->customerRepository->find($id);
    
            if (empty($customer)) {
                return $this->sendError('Customer not found');
            }
          
          	// Check for associated sales, RMA, leads, estimation, and proforma records
          	$hasAmcs = \App\Models\Amc::where('customer_id', $id)->exists();
          	$hasServices = \App\Models\Services::where('customer_id', $id)->exists();
            $hasSales = \App\Models\Sales::where('client_id', $id)->exists();
            $hasRma = \App\Models\Rma::where('customer_id', $id)->exists();
            $hasLeads = \App\Models\leads::where('customer_id', $id)->exists();
            $hasEstimation = \App\Models\Estimation::where('customer_id', $id)->exists();
            $hasProforma = \App\Models\Proforma::where('customer_id', $id)->exists();

            if ($hasSales || $hasRma || $hasLeads || $hasEstimation || $hasProforma || $hasAmcs) {
                return $this->sendError('Customer delete failed: Customer has associated records.');
            }


    
            $customer->delete();
    
            return $this->sendSuccess('Customer deleted successfully');
            
        }
        else{
            
            return $this->sendError('Customer delete Failed');
            
        }
    }
    
    public function fetchCustomerDetails($id){
        
        /** @var Customer $customer */
        $user = Auth::user();
        
        $company_id = $user->company_id;
        
        $customer = Customer::where('id', $id)->where('company_id', $company_id)->first();
        
        $sumSales = Sales::where('client_id', $id)
          ->where('status', 'Success')
          ->selectRaw('COALESCE(SUM(balance_amount), 0) as balance_total, COALESCE(SUM(return_amount), 0) as return_total')->first();

        $customer->balance_amount = $sumSales ? (float) $sumSales->balance_total : 0.0;
    	$customer->return_amount = $sumSales ? (float) $sumSales->return_total : 0.0;
        
        // $customer = $this->customerRepository->find($id);
       
        if (empty($customer)) {
            return $this->sendError('Customer not found');
        }

        return $this->sendResponse(new CustomerResource($customer), 'Customer retrieved successfully');
    
    }
  
  	public function sendReviewSms($id, Request $request){
      
      try {
        //$user = Auth::user();        
        //$company_id = $user->company_id;        
        $customer = Customer::where('id', $id)->first();
      	if (empty($customer)) {
            return $this->sendError('Customer not found');
        }
      	$contact_number = $customer->contact_number;
      	if (empty($contact_number)) {
            return response()->json(['error' => 'Contact number is missing or invalid'], 400);
        }
       	$company = Companies::find($customer->company_id);
        if (!$company) {
            return response()->json(['error' => 'Company not found'], 404);
        }
        if (is_array($company->social_links)) {
            $social_links = $company->social_links;  
        } elseif (is_string($company->social_links)) {
            $social_links = json_decode($company->social_links, true);  
        } else {
            $social_links = [];  
        }
        
         if (empty($social_links['website'])) {
            return response()->json(['error' => 'Review Website link is missing'], 400);
        }
        
                

         if ($request->has('is_whatsapp') && $request->is_whatsapp == true) {
             $messageContent = __('messages.review_whatsapp_message', ['name' => $customer->first_name, 'business' => $company->company_name, 'url' => $social_links['website'] ?? $social_links->website ?? '']);
             $messageContent = str_replace('\n', PHP_EOL, $messageContent);
             sendWaMessageToServer($contact_number, $messageContent, $company->id);
           
        }

        if ($request->has('is_sms') && $request->is_sms == true) {

            $response = $this->smsDeliveryService->sendReview($customer->first_name, $company, $contact_number, $social_links['website'] ?? $social_links->website ?? '');
          //	$relayMessage = new RelayMessage();   
       		//$response = $relayMessage->sendReview($customer->first_name, $company, $contact_number, $social_links['website'] ?? $social_links->website ?? '');  
       		$smsStatus = 0; 
      	
        if ($response) {
            $smsStatus = 1; // success
        }

        $messageTransaction = MessageTransactions::firstOrCreate(
            ['company_id' => $company->id],
            [
                 'sms_count' => 0,
                 'email_count' => 0,
                 'whatsapp_count' => 0,
             ]
        );

        if($smsStatus){
            
            $messageTransaction->increment('sms_count');
        }
           // Store SMS transaction
        $smsTransactionData = [
            'template' => "Google review",
          	'temp_id' => 1707172502821021325,
            'message' => "Hello!,{$customer->first_name} . Review link {$social_links['website']}",
            'status' => $smsStatus,
            'user_id' => auth()->user()->id, 
           	'company_id' => $customer->company_id, 
            'sent_date' => Carbon::now()->toDateString(),
            'to' => $customer->contact_number,
            'customer_id' => $customer->id,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];

        \DB::table('sms_transactions')->insert($smsTransactionData);
           
        
        }
       
      
        return response()->json(['message' => 'Review sent successfully'], 200);
     } 
      catch (\Exception $e) {
          // Log the exception for debugging purposes
          //Log::error('Error sending review SMS: ' . $e->getMessage());

          // Return error response
         return response()->json(['error' => $e->getMessage()], 500);
    	}
    }
  	
public function importCustomers(Request $request)
{
    // Validate that the request contains an array of customer objects
    $request->validate([
        'customers' => 'required|array',
        'customers.*.first_name' => 'nullable|string',
        'customers.*.last_name' => 'nullable|string',
        'customers.*.contact_number' => 'required|string',
        'customers.*.email' => 'nullable|email',
        'customers.*.business_name' => 'nullable|string',
        'customers.*.gst_number' => 'nullable|string',
        'customers.*.opening_balance' => 'nullable|numeric',
    ]);

    try {
        // Process the customer data within a transaction
       $skip = $request->input('skip', 0);
        $result = \DB::transaction(function () use ($request, $skip) {
            return $this->processCustomers($request->customers, $skip);
        });

        return response()->json(['message' => $result['message'], 'data' => $result['data']]);
    } catch (\Exception $e) {
        // Return an error response with the detailed error message
        return response()->json(['error' => 'An error occurred during the import process: ' . $e->getMessage()], 500);
    }
}



protected function processCustomers(array $customers, $skip)
{
    // Set execution and memory limits to handle large datasets
    ini_set('max_execution_time', 0); // No time limit
    ini_set('memory_limit', '2048M'); // Increase memory limit

    $user = Auth::user();
    $company_id = $user->company_id;

    // Get existing contact numbers for the current company
    $existingCustomers = \App\Models\Customer::where('company_id', $company_id)
        ->pluck('contact_number')
        ->toArray();

    $chunkSize = 1000;
    $batch = [];
    $skippedContacts = [];

    // Process each customer object
    foreach ($customers as $customer) {
        $contact_number = $customer['contact_number'] ?? '';

        // Skip if the contact number already exists
        if ($skip || !in_array($contact_number, $existingCustomers)) {
            $batch[] = [
                'first_name' => $customer['first_name'] ?? '',
                'last_name' => $customer['last_name'] ?? '',
                'contact_number' => $contact_number,
                'email' => $customer['email'] ?? '',
                'business_name' => $customer['business_name'] ?? '',
                'gst_number' => $customer['gst_number'] ?? '',
                'opening_balance' => $customer['opening_balance'] ?? 0,
                'company_id' => $company_id,
                'created_at' => now(),
                'updated_at' => now(),
                'address' => $customer['address'] ?? '',
                'alternative_number' => $customer['alternative_number'] ?? '',
                'birth_date' => $customer['birth_date'] ?? '',
                'anniversary_date' => $customer['anniversary_date'] ?? '',
                'city_name' => $customer['city_name'] ?? '',
                'district_name' => $customer['district_name'] ?? '',
                'customer_category' => $customer['customer_category'] ?? '',
                'notes' => $customer['notes'] ?? '',
                'pincode' => $customer['pincode'] ?? '',
                'state_name' => $customer['state_name'] ?? '',
            ];

            // Insert batch when chunk size is reached
            if (count($batch) == $chunkSize) {
                $result = $this->insertBatch($batch);
                if ($result['status'] === 'error') {
                    throw new \Exception($result['message']);
                }
                $batch = []; // Reset the batch after insertion
            }
        } else {
            // Track skipped contacts
            $skippedContacts[] = $contact_number;
        }
    }

    // Insert any remaining records in the last batch
    if (!empty($batch)) {
        $result = $this->insertBatch($batch);
        if ($result['status'] === 'error') {
            throw new \Exception($result['message']);
        }
    }

    // Return success message with skipped contacts
    $message = 'Customer import completed successfully.';
    if (!empty($skippedContacts)) {
        $message .= "\nThe following contact numbers already exist and were skipped:\n" . implode("\n", $skippedContacts);

    }

    return ['status' => 'success', 'message' => $message, 'data' => $skippedContacts];
}



protected function insertBatch($batch)
{
    try {
        \App\Models\Customer::insert($batch);
        return ['status' => 'success', 'message' => 'Batch inserted successfully.'];
    } catch (\Exception $e) {
        return ['status' => 'error', 'message' => 'Batch insert failed: ' . $e->getMessage()];
    }
}
}
