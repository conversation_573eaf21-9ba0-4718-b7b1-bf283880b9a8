<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateEstimationUsersAPIRequest;
use App\Http\Requests\API\UpdateEstimationUsersAPIRequest;
use App\Models\EstimationUsers;
use App\Repositories\EstimationUsersRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class EstimationUsersController
 * @package App\Http\Controllers\API
 */

class EstimationUsersAPIController extends AppBaseController
{
    /** @var  EstimationUsersRepository */
    private $estimationUsersRepository;

    public function __construct(EstimationUsersRepository $estimationUsersRepo)
    {
        $this->estimationUsersRepository = $estimationUsersRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/estimationUsers",
     *      summary="getEstimationUsersList",
     *      tags={"EstimationUsers"},
     *      description="Get all EstimationUsers",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/EstimationUsers")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $estimationUsers = $this->estimationUsersRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($estimationUsers->toArray(), 'Estimation Users retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/estimationUsers",
     *      summary="createEstimationUsers",
     *      tags={"EstimationUsers"},
     *      description="Create EstimationUsers",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/EstimationUsers"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateEstimationUsersAPIRequest $request)
    {
        $input = $request->all();

        $estimationUsers = $this->estimationUsersRepository->create($input);

        return $this->sendResponse($estimationUsers->toArray(), 'Estimation Users saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/estimationUsers/{id}",
     *      summary="getEstimationUsersItem",
     *      tags={"EstimationUsers"},
     *      description="Get EstimationUsers",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of EstimationUsers",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/EstimationUsers"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var EstimationUsers $estimationUsers */
        $estimationUsers = $this->estimationUsersRepository->find($id);

        if (empty($estimationUsers)) {
            return $this->sendError('Estimation Users not found');
        }

        return $this->sendResponse($estimationUsers->toArray(), 'Estimation Users retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/estimationUsers/{id}",
     *      summary="updateEstimationUsers",
     *      tags={"EstimationUsers"},
     *      description="Update EstimationUsers",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of EstimationUsers",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/EstimationUsers"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateEstimationUsersAPIRequest $request)
    {
        $input = $request->all();

        /** @var EstimationUsers $estimationUsers */
        $estimationUsers = $this->estimationUsersRepository->find($id);

        if (empty($estimationUsers)) {
            return $this->sendError('Estimation Users not found');
        }

        $estimationUsers = $this->estimationUsersRepository->update($input, $id);

        return $this->sendResponse($estimationUsers->toArray(), 'EstimationUsers updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/estimationUsers/{id}",
     *      summary="deleteEstimationUsers",
     *      tags={"EstimationUsers"},
     *      description="Delete EstimationUsers",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of EstimationUsers",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var EstimationUsers $estimationUsers */
        $estimationUsers = $this->estimationUsersRepository->find($id);

        if (empty($estimationUsers)) {
            return $this->sendError('Estimation Users not found');
        }

        $estimationUsers->delete();

        return $this->sendSuccess('Estimation Users deleted successfully');
    }
}
