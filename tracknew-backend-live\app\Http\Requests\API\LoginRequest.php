<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Http\Helpers\Helper;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        // return [
        //     'email' => 'required|email|exists:users,email',
        //     'password' => 'required'
        // ];
        return [
            'email' => 'email|exists:users,email',
            'password' => 'required'
        ];
    }

    public function failedValidation(Validator $validator){

        Helper::sendError('validation error', $validator->errors());

    }
}
