// store/modules/dataFilter.js
const state = {
    data: [],              // Original data
    type: '',              // Page name or type of view
    filter_data: [],       // Filtered data
    filter: {
      key: '',             // Filter key (e.g., 'customer.first_name')
      type: ''             // Sort type ('asc' or 'desc')
    },
    status: false          // Status flag
  };
  
  const mutations = {
    setData(state, payload) {
      state.data = payload;
      state.filter_data = payload; // Initialize filtered data
    },
    setType(state, type) {
      state.type = type;
      state.status = true; // Set status to true when page name/type changes
    },
    setFilterParams(state, { key, type }) {
      state.filter.key = key;
      state.filter.type = type;
    },
      applyFilter(state) {        
      if (state.filter.key) {
        state.filter_data = [...state.data].sort((a, b) => {
          const getValue = (obj, keyPath) => {
            if (keyPath == 'follow_up') {
              return obj['follow_up'].length > 0 && obj['follow_up'][0]['date_and_time'];
            } else if (keyPath == 'customer' || keyPath == 'customers') {
              return keyPath.split('.').reduce((acc, part) => acc && acc[part], obj)['first_name'];
            } else if (keyPath == 'product' || keyPath == 'products') {
              return keyPath.split('.').reduce((acc, part) => acc && acc[part], obj)['product_name'];
            } else if ((keyPath == 'firstname' || keyPath == 'mobile_no' || keyPath == 'type') && state.type == 'enquiry') {                
              return obj.data[keyPath];              
            } else {              
              return keyPath.split('.').reduce((acc, part) => acc && acc[part], obj);
            }
          };         

          let propA = getValue(a, state.filter.key);
          let propB = getValue(b, state.filter.key);
  
          if (state.filter.key.includes('date') || state.filter.key == 'follow_up') {
            propA = new Date(propA);
            propB = new Date(propB);
          }else if (typeof propA === 'string' && typeof propB === 'string') {
          propA = propA.toLowerCase();
          propB = propB.toLowerCase();
        } else {
          propA = propA || ''; // Default to empty string if null
          propB = propB || ''; // Default to empty string if null
        }
  
          if (propA < propB) return state.filter.type === 'asc' ? -1 : 1;
          if (propA > propB) return state.filter.type === 'asc' ? 1 : -1;
          return 0;
        });
        //   console.log(state.filter_data, 'wWWWWWWWWW');          
      }
      },
      RESET_STATE(state) {
          state.dashboard_data = [];
          state.data = [];
          state.type = '';
          state.filter_data = [];
          state.filter = {
              key: '',           
              type: ''
          };
          state.status = false;  
      },
  };
  
  const actions = {
    async fetchData({ commit }, { data, type, filterParams }) {
     commit('setData', data);
     commit('setType', type);
     commit('setFilterParams', filterParams);
     commit('applyFilter');
    },
    updateFilter({ commit }, filterParams) {
      commit('setFilterParams', filterParams);
      commit('applyFilter');
      },
      updateFilterParams({ commit }, filterParams) {
        //   console.log(filterParams, 'EEEEEEEEEEEEEEEEEE');          
        commit('setFilterParams', filterParams);
      },
      async fetchDataParams({ commit }, { filterParams }) {
        //   console.log(filterParams, 'EEEEEEEEEEEEEEEEEE');
          
        commit('setFilterParams', filterParams);
        commit('applyFilter');         
     }
  };
  
  const getters = {
    filteredData: (state) => state.filter_data,
    pageStatus: (state) => state.status,
    originalDataFilter: (state) => state.data,
    typeData: (state) => state.type,
    filterTypeKey: (state) => state.filter,
  };
  
  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
  