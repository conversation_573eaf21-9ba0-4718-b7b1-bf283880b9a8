<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateProductsDetailsAPIRequest;
use App\Http\Requests\API\UpdateProductsDetailsAPIRequest;
use App\Models\ProductsDetails;
use App\Repositories\ProductsDetailsRepository;
use App\Repositories\ProductsRepository;
use App\Repositories\ProductsBarcodeRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Http\Resources\api\ProductResource;
use Illuminate\Support\Facades\DB;
use Ramsey\Uuid\Uuid;
use Response;
use Auth;

/**
 * Class ProductsDetailsController
 * @package App\Http\Controllers\API
 */

class ProductsDetailsAPIController extends AppBaseController
{
    /** @var  ProductsDetailsRepository */
    private $productsDetailsRepository;
    private $productsBarcodeRepository;
    private $productsRepository;

    public function __construct(ProductsDetailsRepository $productsDetailsRepo, ProductsBarcodeRepository $productsBarcodeRepo, ProductsRepository $productsRepo )
    {
        $this->productsDetailsRepository = $productsDetailsRepo;
        $this->productsBarcodeRepository = $productsBarcodeRepo;
        $this->productsRepository = $productsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/products_details",
     *      summary="getProductsDetailsList",
     *      tags={"ProductsDetails"},
     *      description="Get all ProductsDetails",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/ProductsDetails")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {


    //     $companyId = $request->query('company_id');

    //         $results = DB::select("SELECT
    //         products_details.id,
    //         products_details.product_id,
    //         products_details.barcode_id,
    //         products_details.purchase_price,
    //         products_details.gst_type,
    //         products_details.gst_value,
    //         products_details.alert_qty,
    //         products_details.discount_type,
    //         products_details.discount,
    //         products_details.total_qty - COALESCE(salesitems.total_qty, 0) AS total_qty,
    //         products_details.sales_price
    //     FROM
    //         (SELECT 
    //             id, 
    //             product_id, 
    //             barcode_id, 
    //             purchase_price, 
    //             gst_type, 
    //             gst_value, 
    //             alert_qty, 
    //             discount_type, 
    //             discount, 
    //             SUM(total_qty) AS total_qty, 
    //             sales_price 
    //         FROM products_details 
    //         WHERE company_id = '".$companyId."' 
    //         GROUP BY product_id, barcode_id) AS products_details
    //     LEFT JOIN
    //         (SELECT 
    //             product_id, 
    //             barcode_id, 
    //             SUM(qty) AS total_qty
               
    //         FROM salesitems 
    //         WHERE company_id = '".$companyId."'  
    //         GROUP BY product_id, barcode_id) AS salesitems
    //     ON
    //         products_details.product_id = salesitems.product_id
    //         AND products_details.barcode_id = salesitems.barcode_id
    //     ");



    //         // var_dump($results);
    //         // exit();
        
    //     // $productsDetails = $this->productsDetailsRepository->all(
    //     //     $request->except(['skip', 'limit', 'group_by']), // Exclude group_by from the query parameters
    //     //     $request->get('skip'),
    //     //     $request->get('limit'),
    //     //     'barcode_id', // Group by barcode_id
    //     //     ['*'] // Select columns including product_id
    //     // );


    // //     $productsDetails = ProductDetail::with('barcode')
    // // ->groupBy('barcode_id')
    // // ->get();
        
    // $transformedResults = ProductResource::collection(ProductsDetails::hydrate($results));
    // return $this->sendResponse($transformedResults, 'Products Details retrieved successfully');
    
   //     $companyId = $request->query('company_id');

   //     if ($companyId === null) {
   //         return $this->sendError('Please provide company ID.', 400);
    //    }
    
   //     $perPage = $request->query('per_page', 10);
    //    $page = $request->query('page', 1);
    //  $query = "SELECT
        //    products_details.id,
      //      products_details.product_id,
    //        products_details.barcode_id,
      //      products_details.purchase_price,
   //         products_details.dealer_price,
   //         products_details.gst_type,
   //         products_details.gst_value,
    //        products_details.alert_qty,
    //        products_details.discount_type,
   //         products_details.discount,
           
    //        products_details.created_at,
     //       products_details.total_qty - COALESCE(salesitems.total_qty, 0) AS total_qty,
    //        products_details.sales_price
    //    FROM
     //       (SELECT 
    //            id, 
     //           product_id, 
    //            barcode_id, 
     //           purchase_price, 
      //          dealer_price,
      //          gst_type, 
      //          gst_value, 
      //          alert_qty, 
    //            discount_type, 
  //              discount, 
               
       //         created_at,
      //          SUM(total_qty) AS total_qty, 
       //         sales_price
      //      FROM products_details 
   //         WHERE company_id = '".$companyId."' AND deleted_at IS NULL GROUP BY product_id, barcode_id) AS products_details
    //    LEFT JOIN
      //      (SELECT 
      //          product_id, 
      //          barcode_id, 
     //           SUM(qty) AS total_qty
      //          FROM salesitems 
      //      WHERE company_id = '".$companyId."' AND deleted_at IS NULL AND status = 0
    //        GROUP BY product_id, barcode_id) AS salesitems
     //   ON
      //      products_details.product_id = salesitems.product_id
       //     AND products_details.barcode_id = salesitems.barcode_id ORDER BY products_details.created_at DESC";
    
     //   $results = DB::select($query);
    
     //   $total = count($results);
    
    //    $start = ($page - 1) * $perPage;
     //   $slicedResults = array_slice($results, $start, $perPage);
    
   //     $transformedResults = ProductResource::collection(ProductsDetails::hydrate($slicedResults));
    
    //    $lastPage = ceil($total / $perPage); 
        
     //   $response = [
      //      'success' => true,
      //      'data' =>  $transformedResults,
       //     // Get the paginated items
        //    'pagination' => [
      //          'total' => $total,
     //           'per_page' => $perPage,
     //           'current_page' => $page,
     //           'last_page' => $lastPage,
     //           'from' => $start + 1,
    //            'to' => min($start + $perPage, $total),
      //      ]
      //  ];
        
       // return response()->json($response);
      
        $companyId = $request->query('company_id');
      	$fromDate = $request->query('from_date');
		$toDate = $request->query('to_date');

        if ($companyId === null) {
            return $this->sendError('Please provide company ID.', 400);
        }
    
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
    
        $query = "SELECT
    products_details.id,
    products_details.product_id,
    products_details.barcode_id,
    products_details.purchase_price,
    products_details.dealer_price,
    products_details.gst_type,
    products_details.tax_name,
    products_details.gst_value,
    products_details.alert_qty,
    products_details.discount_type,
    products_details.discount,
    products_details.updated_at,
    products_details.total_qty - COALESCE(salesitems.total_qty, 0) AS total_qty,
    products_details.sales_price
FROM
    (SELECT 
        id, 
        product_id, 
        barcode_id, 
        purchase_price, 
        dealer_price,
        gst_type, 
        gst_value, 
        tax_name,
        alert_qty, 
        discount_type, 
        discount, 
        updated_at,
        SUM(total_qty) AS total_qty, 
        sales_price
    FROM products_details 
    WHERE company_id = ? AND deleted_at IS NULL"
    . ($fromDate ? " AND updated_at >= ?" : "")
    . ($toDate ? " AND updated_at <= ?" : "")
    . " GROUP BY product_id, barcode_id) AS products_details
LEFT JOIN
    (SELECT 
        product_id, 
        barcode_id, 
        SUM(qty) AS total_qty
    FROM salesitems 
    WHERE company_id = ? AND deleted_at IS NULL AND status = 0
    GROUP BY product_id, barcode_id) AS salesitems
ON
    products_details.product_id = salesitems.product_id
    AND products_details.barcode_id = salesitems.barcode_id
ORDER BY products_details.updated_at DESC";
    
        $params = [$companyId];
        if ($fromDate) $params[] = $fromDate;
        if ($toDate) $params[] = $toDate;
        $params[] = $companyId;

        $results = DB::select($query, $params);
    
        $total = count($results);
    
        $start = ($page - 1) * $perPage;
        $slicedResults = array_slice($results, $start, $perPage);
    
        $transformedResults = ProductResource::collection(ProductsDetails::hydrate($slicedResults));
    	
      	if ($perPage > 0) {
            $lastPage = ceil($total / $perPage);
        } else {
            // Handle the error: Set $lastPage to 1 or any other fallback value
            $lastPage = 1; // or some default behavior if $perPage is 0
            // Optionally, you could log or show a message to indicate that $perPage was zero
        }
        
        
        $response = [
            'success' => true,
            'data' =>  $transformedResults,
            // Get the paginated items
            'pagination' => [
                'total' => $total,
                'per_page' => $perPage,
                'current_page' => $page,
                'last_page' => $lastPage,
                'from' => $start + 1,
                'to' => min($start + $perPage, $total),
            ]
        ];
        
        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/products_details",
     *      summary="createProductsDetails",
     *      tags={"ProductsDetails"},
     *      description="Create ProductsDetails",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/ProductsDetails")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ProductsDetails"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    
    public function store(CreateProductsDetailsAPIRequest $request)
    {
        $input = $request->all();
        $uuid = Uuid::uuid4();
        // Common product data for both branches
        $productData = [
            "product_name" => $input['product_name'],
            "hsn_code" => $input['hsn_code'],
            "unit" => $input['unit'],
            "warranty" => $input['warranty'] ?? 0,
			"image"   => $input['image'],
            "category_id" => $input['category_id'] ?? '',
            "company_id" => $input['company_id'],
            "user_id" => $input['user_id']
        ];

        // Set default values for brand_id and barcode_id
        $productData['brand_id'] = $input['brand_id'] ?? 0;
        $input['barcode_id'] = 0;

       // if ($input['product_type'] == 'Product') {
            // Check for existing barcode
            $existingBarcode = $this->productsBarcodeRepository->findBy($input['barcode']);

            if ($existingBarcode) {
                return response()->json(['error' => 'Barcode already exists'], 400);
            }

            // Create new product entry with barcode
            $products = $this->productsRepository->create($input);
            $barcodeData = [
                'barcode' => $input['barcode'],
                'product_id' => $products->id,
              	'company_id' => $input['company_id'],
            ];
            $barcode = $this->productsBarcodeRepository->create($barcodeData);

            $input['barcode_id'] = $barcode->id;
        // } else {
        //     // Create new product entry without barcode
        //     $products = $this->productsRepository->create($productData);
        // }

        // Set additional values and save product details
        $input['product_id'] = $products->id;
        $input['id'] =  (string)$uuid;
        //  \Log::info('Input array:', $input);

         $productsDetails = $this->productsDetailsRepository->create($input);
 
      
        // // Return response
         return $this->sendResponse(new ProductResource($productsDetails), 'Products Details saved successfully');


        
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/products_details/{id}",
     *      summary="getProductsDetailsItem",
     *      tags={"ProductsDetails"},
     *      description="Get ProductsDetails",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ProductsDetails",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ProductsDetails"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var ProductsDetails $productsDetails */
        
        
        $productsDetails = $this->productsDetailsRepository->find($id);

        if (empty($productsDetails)) {
            return $this->sendError('Products Details not found');
        }

        return $this->sendResponse(new ProductResource($productsDetails), 'Products Details retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/products_details/{id}",
     *      summary="updateProductsDetails",
     *      tags={"ProductsDetails"},
     *      description="Update ProductsDetails",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ProductsDetails",
     *           @OA\Schema(
     *             type="string"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/ProductsDetails")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ProductsDetails"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateProductsDetailsAPIRequest $request)
    {
        // $input = $request->all();

        // /** @var ProductsDetails $productsDetails */
        // $productsDetails = $this->productsDetailsRepository->find($id);

        // if (empty($productsDetails)) {
        //     return $this->sendError('Products Details not found');
        // }

        // $productsDetails = $this->productsDetailsRepository->update($input, $id);

        // return $this->sendResponse($productsDetails->toArray(), 'ProductsDetails updated successfully');

    $input = $request->all();

    // Find the ProductsDetails record by ID
    $productsDetails = $this->productsDetailsRepository->find($id);

    // Check if the record exists
    if (empty($productsDetails)) {
        return $this->sendError('Products Details not found');
    }

    // Update the related Product record
    $productData = [
        "product_name" => $input['product_name'],    
        "hsn_code" => $input['hsn_code'],
        "product_code" => $input['product_code'],
        "unit" => $input['unit'],
        "warranty" => $input['warranty'],
      	"image"   => $input['image'],
        "category_id" => $input['category_id'] ?? '',
        "company_id" => $input['company_id'],
        "user_id" => $input['user_id']
    ];

    $products = $this->productsRepository->update($input, $productsDetails->product_id);

       $existingBarcode = $this->productsBarcodeRepository->findBy('barcode', $input['barcode']);

        if ($existingBarcode) {
            // Barcode already exists, return an error response or handle the situation accordingly
            return response()->json(['error' => 'Barcode already exists'], 400);
        } else {
            // Barcode is unique, proceed with creating a new entry
            $barcodeData = [
                'barcode' => $input['barcode'],
                'product_id' => $products->id
            ];
            $barcode = $this->productsBarcodeRepository->update($barcodeData, $productsDetails->barcode_id);
            
            
        }

        $input['barcode_id'] = $barcode->id;
        $input['product_id'] = $products->id;
        $updatedProductsDetails = $this->productsDetailsRepository->update($input, $id);

    return $this->sendResponse(new ProductResource($updatedProductsDetails), 'Products Details updated successfully');
    }
    
    
    public function stockUpdate($id, UpdateProductsDetailsAPIRequest $request)
    {
        // $input = $request->all();

        // /** @var ProductsDetails $productsDetails */
        // $productsDetails = $this->productsDetailsRepository->find($id);

        // if (empty($productsDetails)) {
        //     return $this->sendError('Products Details not found');
        // }

        // $productsDetails = $this->productsDetailsRepository->update($input, $id);

        // return $this->sendResponse($productsDetails->toArray(), 'ProductsDetails updated successfully');

        $input = $request->all();

    // Find the ProductsDetails record by ID
    $productsDetails = $this->productsDetailsRepository->find($id);
    

    // Check if the record exists
    if (empty($productsDetails)) {
        return $this->sendError('Products Details not found');
    }
    
    $input['total_qty'] = $productsDetails['total_qty'] + $input['total_qty'];

 
        $updatedProductsDetails = $this->productsDetailsRepository->update($input, $id);

    return $this->sendResponse(new ProductResource($updatedProductsDetails), 'Products Details updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/products_details/{id}",
     *      summary="deleteProductsDetails",
     *      tags={"ProductsDetails"},
     *      description="Delete ProductsDetails",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ProductsDetails",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        // Use Eloquent relationships to load the ProductsDetails with related models
        $productsDetails = ProductsDetails::with('products', 'barcodes')->findOrFail($id);
    
        // If ProductsDetails is not found, it will throw ModelNotFoundException automatically
    
        // Begin a database transaction for atomic operations
        DB::beginTransaction();
    
        try {
            // Delete the ProductsDetails record
            $productsDetails->delete();
    
            // Delete related Barcode record
            if ($productsDetails->barcode) {
                $productsDetails->barcode->delete();
            }
    
            // Delete related Product record
            if ($productsDetails->product) {
                $productsDetails->product->delete();
            }
    
            // Commit the transaction if all deletions are successful
            DB::commit();
    
            // Return success response
            return $this->sendSuccess('Products Details deleted successfully');
        } catch (\Exception $e) {
            // Rollback the transaction if any error occurs
            DB::rollback();
    
            // Return error response
            return $this->sendError('Failed to delete Products Details');
        }
    }
  		public function importProducts(CreateProductsDetailsAPIRequest $request)
    {
        $input = $request->all();  // Expecting an array of products

        $user = Auth::user();
        $company_id = $user->company_id;
      	$user_id = $user->id;
        if (!is_array($input['products'])) {
            return response()->json(['error' => 'Invalid data format. Products should be an array.'], 400);
        }

        $products = $input['products'];
        $createdProducts = [];
      

        foreach ($products as $product) {
            $uuid = Uuid::uuid4();
          

            // Common product data for each item in the array
            $productData = [
                "product_name" => $product['product_name'],
                "product_type" => $product['product_type'],              
                "hsn_code" => $product['hsn_code'],
                "unit" => $product['unit'],
                "warranty" => $product['warranty'] ?? 0,                
                "category_id" => $product['category_id'] ?? 0,
                "company_id" => $company_id,
                "user_id" => $user_id,
                "brand_id" =>  $product['brand_id'] ?? 0,  // Default if not provided
               
            ];

            // Check if the barcode already exists
            $existingBarcode = $this->productsBarcodeRepository->findBy($product['barcode'], $company_id);
            if ($existingBarcode) {
                return response()->json(['error' => 'Barcode already exists for one or more products'], 400);
            }

            // Create the product
            $newProduct = $this->productsRepository->create($productData);

            // Create the barcode for the product
            $barcodeData = [
                'barcode' => $product['barcode'],
                'product_id' => $newProduct->id,
              	'company_id' => $company_id,
            ];
            $barcode = $this->productsBarcodeRepository->create($barcodeData);

            // Set the barcode ID in the product details
            $product['barcode_id'] = $barcode->id;
            $product['product_id'] = $newProduct->id;
            $product['id'] = (string) $uuid;
			$product['company_id'] = $company_id;
            // Save product details
            $createdProductDetails = $this->productsDetailsRepository->create($product);
            $createdProducts[] = new ProductResource($createdProductDetails);
        }

        // Return all created product details
        return $this->sendResponse($createdProducts, 'Products imported successfully');
    }

}
