<!-- Header.vue -->
<template>
  <header class="px-4 py-2 flex justify-between items-center set-header-background text-white ">
    <div class="container flex justify-between items-center">
      <!-- Logo and Text -->
      <div class="flex justify-left items-center">
        <div v-if="isMobile" class="items-center hover:text-gray-600 pr-3" @click.stop="updatesidebar">
          <font-awesome-icon icon="fa-solid fa-bars" />
        </div>
        <!-- Logo -->
        <img src="/images/head_bar/logo.png" alt="TrackNew Logo" class="h-5 sm:h-10" />
        <!-- Text -->
        <div v-if="!isMobile" class="text-sm sm:text-lg text-white pl-2">
          Website Builder
        </div>
      </div>
    </div>
    <div class="flex justify-end w-full text-sm ">
      <!-- Preview Web Page -->
      <div v-if="$route.path === '/websites/settings' && previewUrl && previewUrl !== ''" class="mx-5">
        <div
          class="flex items-center space-x-2 rounded-md bg-blue-500 hover:bg-blue-700 text-white px-2 py-1 transition duration-300 ease-in-out">
          <span><font-awesome-icon icon="fa-solid fa-eye" /></span>
          <a :href="previewUrl" target="_blank" class="text-white">Preview</a>
        </div>
      </div>
      <!-- Exit Link -->
      <router-link to="/"
        class="flex items-center bg-red-500 hover:bg-red-700 text-white rounded-md px-2 py-1 transition duration-300 ease-in-out">
        <font-awesome-icon icon="fa-solid fa-arrow-right-from-bracket" flip="horizontal" class="pl-2" />
        <span>Exit</span>
      </router-link>
    </div>

  </header>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
  name: "Header",
  props: {
    isMobile: {
      type: Boolean,
      required: true,
    }
  },
  computed: {
    ...mapGetters('websiteBuilder', ['selectedId', 'previewUrl']),
  },
  methods: {
    updatesidebar() {
      this.$emit('updatesidebar');
    }
  }
};
</script>

<style scoped>
/* Add additional styles if needed */
</style>
