<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreatePurchaseOrderPaymentsAPIRequest;
use App\Http\Requests\API\UpdatePurchaseOrderPaymentsAPIRequest;
use App\Models\PurchaseOrderPayments;
use App\Repositories\PurchaseOrderPaymentsRepository;
use App\Repositories\PurchaseOrderRepository;
use App\Http\Resources\api\PurchasePaymentResource;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Illuminate\Support\Facades\DB;
use Response;

/**
 * Class PurchaseOrderPaymentsController
 * @package App\Http\Controllers\API
 */

class PurchaseOrderPaymentsAPIController extends AppBaseController
{
    /** @var  PurchaseOrderPaymentsRepository */
    private $purchaseOrderPaymentsRepository;
  	private $purchaseOrderRepository;

    public function __construct(PurchaseOrderPaymentsRepository $purchaseOrderPaymentsRepo, PurchaseOrderRepository $purchaseOrderRepo)
    {
        $this->purchaseOrderPaymentsRepository = $purchaseOrderPaymentsRepo;
      	$this->purchaseOrderRepository = $purchaseOrderRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/purchase_order_payments",
     *      summary="getPurchaseOrderPaymentsList",
     *      tags={"PurchaseOrderPayments"},
     *      description="Get all PurchaseOrderPayments",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/PurchaseOrderPayments")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {      
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        // Group by payment_code and sum payment_amount
        $subQuery = PurchaseOrderPayments::select('payment_code', DB::raw('SUM(payment_amount) as total_payment_amount'))
            ->where('company_id', $companyId)
            ->groupBy('payment_code');

        // Join the subquery to select all columns
        $salesQuery = PurchaseOrderPayments::joinSub($subQuery, 'grouped_sales', function ($join) {
                $join->on('puchaseorder_payments.payment_code', '=', 'grouped_sales.payment_code');
            })
            ->select('puchaseorder_payments.*', 'grouped_sales.total_payment_amount')
            ->where('puchaseorder_payments.company_id', $companyId)
            ->groupBy('puchaseorder_payments.payment_code'); 

        if ($perPage === 'all') {
            $perPage = $salesQuery->count();
        }

        // Paginate the grouped results
        $sales = $salesQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => PurchasePaymentResource::collection($sales),
            'pagination' => [
                'total' => $sales->total(),
                'per_page' => $sales->perPage(),
                'current_page' => $sales->currentPage(),
                'last_page' => $sales->lastPage(),
                'from' => $sales->firstItem(),
                'to' => $sales->lastItem()
            ],
        ];

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/purchase_order_payments",
     *      summary="createPurchaseOrderPayments",
     *      tags={"PurchaseOrderPayments"},
     *      description="Create PurchaseOrderPayments",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/PurchaseOrderPayments")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrderPayments"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreatePurchaseOrderPaymentsAPIRequest $request)
    {
        $input = $request->all();

        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->create($input);

        return $this->sendResponse($purchaseOrderPayments->toArray(), 'Purchase Order Payments saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/purchase_order_payments/{id}",
     *      summary="getPurchaseOrderPaymentsItem",
     *      tags={"PurchaseOrderPayments"},
     *      description="Get PurchaseOrderPayments",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrderPayments",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrderPayments"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var PurchaseOrderPayments $purchaseOrderPayments */
        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->find($id);

        if (empty($purchaseOrderPayments)) {
            return $this->sendError('Purchase Order Payments not found');
        }

        return $this->sendResponse(new PurchasePaymentResource($purchaseOrderPayments), 'Purchase Order Payments retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/purchase_order_payments/{id}",
     *      summary="updatePurchaseOrderPayments",
     *      tags={"PurchaseOrderPayments"},
     *      description="Update PurchaseOrderPayments",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrderPayments",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
    *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/PurchaseOrderPayments")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrderPayments"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdatePurchaseOrderPaymentsAPIRequest $request)
    {
        $input = $request->all();
      
      	$purchase = $this->purchaseOrderRepository->find($input['purchaseorder_id']);
      
      	if (!$purchase) {
            return $this->sendError('Purchase not found for ID: ' . $purchaseOrderPayments->purchaseorder_id);
        }
      
      	$salesData['due_amount'] = $input['due_amount'];
      	$salesData['balance_amount'] = $input['balance_amount'];
		$this->purchaseOrderRepository->update($salesData, $purchase->id);

        /** @var PurchaseOrderPayments $purchaseOrderPayments */
        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->find($id);

        if (empty($purchaseOrderPayments)) {
            return $this->sendError('Purchase Order Payments not found');
        }

        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->update($input, $id);

        return $this->sendResponse(new PurchasePaymentResource($purchaseOrderPayments), 'PurchaseOrderPayments updated successfully');
    }
  

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/purchase_order_payments/{id}",
     *      summary="deletePurchaseOrderPayments",
     *      tags={"PurchaseOrderPayments"},
     *      description="Delete PurchaseOrderPayments",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrderPayments",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var PurchaseOrderPayments $purchaseOrderPayments */
        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->find($id);

        if (empty($purchaseOrderPayments)) {
            return $this->sendError('Purchase Order Payments not found');
        }
      
      	$purchase = $this->purchaseOrderRepository->find($purchaseOrderPayments->purchaseorder_id);
      
      	if (!$purchase) {
            return $this->sendError('Purchase not found for ID: ' . $purchaseOrderPayments->purchaseorder_id);
        }
      
      	$data['balance_amount'] = (int)$purchase->balance_amount + (int)$purchaseOrderPayments->payment_amount;
        //$data['due_amount'] = (int)$purchase->due_amount + (int)$purchaseOrderPayments->payment_amount;
		$this->purchaseOrderRepository->update($data, $purchase->id);
        $purchaseOrderPayments->delete();

        return $this->sendSuccess('Purchase Order Payments deleted successfully');     
    }
  
  	public function destroyByPaymentCode($payment_code)
    {
        /** @var Collection $salesPayments */
        $purchaseOrderPayments = $this->purchaseOrderPaymentsRepository->findBy('payment_code', $payment_code);

        if ($purchaseOrderPayments->isEmpty()) {
            return $this->sendError('Sales Payments not found for payment code: ' . $payment_code);
        }

        foreach ($purchaseOrderPayments as $paymentData) {
            $purchase = $this->purchaseOrderRepository->find($paymentData->purchaseorder_id);

            if (!$purchase) {
                return $this->sendError('Purchase not found for ID: ' . $paymentData->purchaseorder_id);
            }

            $datas['balance_amount'] = (int)$purchase->balance_amount + (int)$paymentData->payment_amount;
            // $data['due_amount'] = (int)$sale->due_amount + (int)$salesPayment->payment_amount;

            $this->purchaseOrderRepository->update($datas, $purchase->id);

            $paymentData->delete();
        }

        return $this->sendSuccess('Purchase Payments deleted successfully for payment code: ' . $payment_code);
    }
}
