// store/modules/accressories.js
import axios from "axios";

const state = {
  accressories: [],
};
  
  const mutations = {
      SET_ACCESSORIESLIST(state, { data}) {       
          state.accressories = data;
    },
      RESET_STATE(state) {
          state.accressories = [];
      }
  };

  const actions = {
    updateAccessoriesName({ commit }, accressoriesData) {
      // Simulate an asynchronous operation (e.g., API call) to update accressories name
      setTimeout(() => {
        // Commit mutation to update accressories name
        commit('SET_ACCESSORIESLIST', accressoriesData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchAccessoriesList({ commit }) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get('/rma_accessories', { params: { company_id: company_id, page: 1, per_page: 'all' } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Accessories list..!');
              let { data} = response.data;              
              commit('SET_ACCESSORIESLIST', {data});
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    
  };

  const getters = {
    currentAccessoriesList(state) {
      return state.accressories;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
