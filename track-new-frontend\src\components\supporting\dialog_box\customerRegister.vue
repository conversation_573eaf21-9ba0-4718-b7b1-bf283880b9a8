<template>
    <div v-if="showModal"
        class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-auto">
        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 rounded"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen, 'pb-[70px]': isMobile, 'h-screen': !is_quick_save }">
            <!-- <div class="modal-content "> -->
            <div class="set-header-background justify-between items-center flex py-2">
                <h2 class="text-white text-center ml-12 text-xl py-1">
                    {{ type == 'edit' ? 'Edit a Customer' : 'Register a Customer' }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!-- Content based on selected option -->
            <div class="mt-1 p-4">
                <!--grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4-->
                <!---Personal details-->
                <div class="rounded">
                    <div class="flex justify-between text-center items-center cursor-pointer px-2 py-2 rounded sm:w-1/2 w-full text-sm font-bold"
                        :class="{ 'bg-blue-300': personalfields, ' bg-gray-300': !personalfields }"
                        @click="personalfields = !personalfields">
                        <p>Personal Details</p>
                        <div v-if="personalfields" class="triangle-up"></div>
                        <div v-else class="triangle-down"></div>
                    </div>
                    <div v-if="personalfields" class="px-2 py-5"
                        :class="{ 'border border-blue-400 rounded': personalfields }">
                        <!---Name-->
                        <div class="flex items-center">
                            <div class="mr-2" :title="'Name'">
                                <img :src="user_name" alt="user_name" class="w-7 h-7">
                            </div>
                            <div class="mr-2 relative" :class="{ 'w-3/4': is_quick_save, 'w-1/2': !is_quick_save }">
                                <label for="first_name" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.first_name || isInputFocused, 'text-blue-600': isInputFocused }">First
                                    Name <span v-if="formValues.first_name || isInputFocused"
                                        class="text-red-600">*</span></label>
                                <input id="first_name" v-model="formValues.first_name" type="text" ref="first_name"
                                    placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    @focus="isInputFocused = true"
                                    @keydown.down.prevent="handleFocusMobile('contact_number')"
                                    title="on press  &#8595; to move contact" />
                                <span v-if="formValues.first_name === ''"
                                    class="absolute block text-[10px] text-red-500">Enter
                                    name</span>
                            </div>
                            <!-- @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" -->
                            <div v-if="!is_quick_save" class="w-1/2 relative">
                                <label for="last_name" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.last_name || isInputFocusedLastName, 'text-blue-600': isInputFocusedLastName }">Last
                                    Name</label>
                                <input id="last_name" v-model="formValues.last_name" type="text" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedLastName = true" @blur="isInputFocusedLastName = false" />
                            </div>
                        </div>

                        <!--Mobile-->
                        <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'Mobile number'">
                                <img :src="phone" alt="mobile_number" class="w-7 h-7">
                            </div>
                            <div class="mr-2 relative" :class="{ 'w-3/4': is_quick_save, 'w-1/2': !is_quick_save }">
                                <!--<span v-if="formValues.contact_number || isInputFocusedMobilePrimary" class="text-red-600">*</span>-->
                                <label for="contact_number"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.contact_number || isInputFocusedMobilePrimary, 'text-blue-600': isInputFocusedMobilePrimary }">Primary
                                    Contact
                                    <span v-if="formValues.contact_number || isInputFocusedMobilePrimary"
                                        class="text-red-600">*</span></label>
                                <input id="contact_number" v-model="formValues.contact_number" type="tel"
                                    ref="contact_number" placeholder=" "
                                    @input="validatePhoneNumber(formValues.contact_number), handleDropdownInput(formValues.contact_number)"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    :class="{ 'focus:border-red-700': validationMessage !== '' }"
                                    @focus="isInputFocusedMobilePrimary = true"
                                    @keydown.down.prevent="handleFocusMobile('save_button')"
                                    @keydown.up.prevent="handleFocusMobile('first_name')"
                                    title="on press  &#8595; to move save button" />
                                <span v-if="validationMessage !== ''" class="absolute block text-xs text-red-500">
                                    {{ validationMessage }}</span>
                            </div>
                            <div v-if="!is_quick_save" class="w-1/2 relative">
                                <label for="alternate_number"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.alternative_number || isInputFocusedMobileSecondary, 'text-blue-600': isInputFocusedMobileSecondary }">Secondary
                                    Contact</label>
                                <!-- @input="validatePhoneNumber(formValues.alternative_number, 'alter')" -->
                                <input id="alternate_number" v-model="formValues.alternative_number" type="tel"
                                    placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    :class="{ 'focus:border-red-700': validationMessageAlter !== '' }"
                                    @focus="isInputFocusedMobileSecondary = true"
                                    @blur="isInputFocusedMobileSecondary = false" />
                                <span v-if="validationMessageAlter !== ''" class="absolute block text-xs text-red-500">
                                    {{ validationMessageAlter }}
                                </span>
                            </div>
                        </div>
                        <!--Email-->
                        <div v-if="!is_quick_save" class="flex items-center mt-5">
                            <div class="mr-2" :title="'email'">
                                <img :src="email" alt="email" class="w-7 h-7">
                            </div>
                            <div class="w-1/2 mr-2 relative">
                                <label for="email" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.email || isInputFocusedEmail, 'text-blue-600': isInputFocusedEmail }">Email</label>
                                <input id="email" v-model="formValues.email" type="email" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedEmail = true" @blur="isInputFocusedEmail = false" />
                            </div>
                        </div>
                        <!--Opening Balance-->
                        <div v-if="!is_quick_save" class="flex items-center mt-5">
                            <div class="mr-2" :title="'balance'">
                                <img :src="balance_img" alt="balance" class="w-7 h-7">
                            </div>
                            <div class="w-1/2 mr-2 relative">
                                <label for="balance" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.opening_balance >= 0 || isInputFocusedData.balance, 'text-blue-600': isInputFocusedData.balance }">Opening
                                    Balance</label>
                                <!--:readonly="type === 'edit'"-->
                                <input id="balance" v-model="formValues.opening_balance" type="number" min='0'
                                    max="100000000" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedData.balance = true"
                                    @blur="isInputFocusedData.balance = false" />
                            </div>
                        </div>
                        <!-- Customer Exists Warning -->
                        <div v-if="filteredCustomerOptions && filteredCustomerOptions.length > 0 && formValues.contact_number && formValues.contact_number.length > 5"
                            class="bg-red-50 border border-red-400 text-red-600 p-3 rounded-md flex justify-between items-center mt-4">
                            <div>
                                <span class="font-semibold pr-1">Already Exists:</span>
                                <span v-for="(customer, index) in filteredCustomerOptions" :key="customer.id"
                                    :class="{ 'hidden': index > 0 }">
                                    {{ customer.first_name }} <span v-if="customer.last_name">{{ customer.last_name
                                        }}</span>
                                    - <span class="text-gray-500">{{ customer.contact_number }}</span>
                                </span>
                            </div>
                            <button v-if="!more_info" @click="selectThisCustomer"
                                class="bg-blue-600 hover:bg-blue-500 text-white px-3 py-1 rounded-md transition duration-200">
                                Select
                            </button>
                        </div>

                        <!-- Action Buttons -->
                        <div class="flex space-x-20 mt-2 text-sm -mb-2">
                            <!-- More Info Button (Left-Aligned) -->
                            <button @click="is_quick_save = !is_quick_save"
                                class="text-blue-600 hover:text-blue-500 flex items-center transition duration-200">
                                <font-awesome-icon icon="fa-solid fa-circle-info" class="mr-1" />
                                <span v-if="is_quick_save">More Info</span>
                                <span v-else>Less Info</span>
                            </button>

                            <!-- Quick Save Button (Centered) -->
                            <button @click="sendModal" ref="save_button"
                                class="bg-green-600 hover:bg-green-500 text-white px-5 py-2 rounded-full transition duration-200 shadow-md focus:outline-none focus:ring-2 focus:ring-green-400">
                                <font-awesome-icon icon="fa-solid fa-forward-fast" class="mr-1" /> Save
                            </button>
                        </div>

                    </div>
                </div>
                <!---Birth and anniversary-->
                <div v-if="!is_quick_save" class="rounded mt-5">
                    <div class="flex justify-between items-center cursor-pointer px-2 py-2 rounded text-sm font-bold sm:w-1/2 w-full"
                        :class="{ 'bg-blue-300': dateFields, ' bg-gray-300': !dateFields }"
                        @click="dateFields = !dateFields">
                        <p>Birth & Anniversary</p>
                        <div v-if="dateFields" class="triangle-up"></div>
                        <div v-else class="triangle-down"></div>
                    </div>
                    <div v-if="dateFields" class="px-2 py-5 rounded" :class="{ 'border border-blue-400': dateFields }">
                        <div class="flex items-center">
                            <div class="mr-2" :title="'Birth Date'">
                                <img :src="birthDate" alt="birth_date" class="w-7 h-7">
                            </div>
                            <!---Birth Date-->
                            <div class="w-full mr-2 relative">
                                <label for="birth_date" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.birth_date || isInputFocusedData.birth_date, 'text-blue-600': isInputFocusedData.birth_date }">
                                    Birth Date
                                </label>
                                <input id="birth_date" v-model="formValues.birth_date" type="date" v-datepicker
                                    ref="birth_date" @input="formatDate('birth_date')" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                            </div>
                        </div>
                        <!--Anniversary Date-->
                        <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'Anniversary Date'">
                                <img :src="anniversary" alt="anniversary date" class="w-7 h-7">
                            </div>
                            <div class="w-full mr-2 relative">
                                <label for="anniversary_date"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.anniversary_date || isInputFocusedData.anniversary_date, 'text-blue-600': isInputFocusedData.anniversary_date }">Anniversary
                                    Date</label>
                                <input id="anniversary_date" v-model="formValues.anniversary_date" type="date"
                                    v-datepicker @input="formatDate('anniversary_date')" ref="anniversary_date"
                                    placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                            </div>
                        </div>
                    </div>
                </div>
                <!---Business details-->
                <div v-if="!is_quick_save" class="rounded mt-5">
                    <div class="flex justify-between items-center cursor-pointer px-2 py-2 rounded text-sm font-bold sm:w-1/2 w-full"
                        :class="{ 'bg-blue-300': businessfields, ' bg-gray-300': !businessfields }"
                        @click="businessfields = !businessfields">
                        <p>Business Details</p>
                        <div v-if="businessfields" class="triangle-up"></div>
                        <div v-else class="triangle-down"></div>
                    </div>
                    <div v-if="businessfields" class="px-2 py-5 rounded"
                        :class="{ 'border border-blue-400': businessfields }">
                        <div class="flex items-center">
                            <div class="mr-2" :title="'PAN Number'">
                                <img :src="company" alt="business_name" class="w-7 h-7">
                            </div>
                            <!---Company Name-->
                            <div class="w-full mr-2 relative">
                                <label for="business_name" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.business_name || isInputFocusedBusiness, 'text-blue-600': isInputFocusedBusiness }">
                                    Company Name</label>
                                <input id="business_name" v-model="formValues.business_name" type="text"
                                    ref="business_name" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    @focus="isInputFocusedBusiness = true" @blur="isInputFocusedBusiness = false" />
                            </div>
                        </div>
                        <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'PAN Number'">
                                <!-- <img :src="company" alt="business_name" class="w-7 h-7"> -->
                                <p class="font-bold text-sm text-red-700">GST</p>
                            </div>
                            <!--GST number-->
                            <div class="w-full relative mr-2">
                                <label for="gst_number" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.gst_number || isInputFocusedGst, 'text-blue-600': isInputFocusedGst }">GST
                                    Number</label>
                                <!---@input="validateGST"-->
                                <input id="gst_number" v-model="formValues.gst_number" type="text" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedGst = true" @blur="isInputFocusedGst = false" />
                                <span v-if="formValues.gst_number !== '' && gstValidation !== ''"
                                    class="text-red-700 text-xs absolute block">{{ gstValidation }}</span>
                            </div>
                        </div>
                        <!--address-->
                        <!-- <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'Address'">
                                <img :src="communicate_address" alt="address" class="w-7 h-7">
                            </div>
                            <div class="w-1/2 mr-2 relative">
                                <label for="address" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.address || isInputFocusedAddress, 'text-blue-600': isInputFocusedAddress }">Address</label>
                                <input id="contact_number" v-model="formValues.address" type="text" ref="contact_number"
                                    placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    @focus="isInputFocusedAddress = true" @blur="isInputFocusedAddress = false" />
                            </div>
                            <div class="w-1/2 relative">
                                <label for="pincode" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.pincode || isInputFocusedPincode, 'text-blue-600': isInputFocusedPincode }">Pincode</label>
                                <input id="alternate_number" v-model="formValues.pincode" type="number" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedPincode = true" @blur="isInputFocusedPincode = false" />
                            </div>
                        </div> -->
                        <!--state, district country-->
                        <!-- <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'Address'">
                                <img :src="map" alt="map_address" class="w-7 h-7">
                            </div>
                            <div class="w-1/3 mr-2 relative">
                                <label for="state_name" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.state_name || isInputFocusedState, 'text-blue-600': isInputFocusedState }">State</label>
                                <select id="state_name" v-model="formValues.state_name"
                                    @change="getDistrictList(selectedStateId)"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedState = true" @blur="isInputFocusedState = false">
                                    <option v-for="(opt, index) in this.state_list" :key="index" :value="opt.name">
                                        {{ opt.name }}</option>
                                </select>
                            </div>
                            <div v-if="formValues.state_name" class="w-1/3 mr-2 relative">
                                <label for="district_name" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.district_name || isInputFocusedDistrict, 'text-blue-600': isInputFocusedDistrict }">Districts</label>
                                <select id="district_name" v-model="formValues.district_name"
                                    @change="getCityList(selectedDistrictId)"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedDistrict = true" @blur="isInputFocusedDistrict = false">
                                    <option v-for="(opt, index) in this.district_list"
                                        :class="{ 'hidden': index === 0 }" :key="index" :value="opt.name">{{ opt.name }}
                                    </option>

                                </select>
                            </div>
                            <div v-if="formValues.district_name" class="w-1/3 mr-2 relative">
                                <label for="city_name" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.city_name || isInputFocusedCity, 'text-blue-600': isInputFocusedCity }">City</label>
                                <select id="city_name" v-model="formValues.city_name"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedCity = true" @blur="isInputFocusedCity = false">
                                    <option v-for="(opt, index) in this.city_list" :key="index" :value="opt.name">
                                        {{ opt.name }}</option>
                                </select>
                            </div>
                        </div> -->
                    </div>
                </div>
                <div v-if="!is_quick_save" class="rounded mt-5">
                    <div class="flex justify-between items-center cursor-pointer px-2 py-2 rounded text-sm font-bold sm:w-1/2 w-full"
                        :class="{ 'bg-blue-300': communicateFields, ' bg-gray-300': !communicateFields }"
                        @click="communicateFields = !communicateFields">
                        <p>Address</p>
                        <div v-if="communicateFields" class="triangle-up"></div>
                        <div v-else class="triangle-down"></div>
                    </div>
                    <div v-if="communicateFields" class="px-2 py-5 rounded"
                        :class="{ 'border border-blue-400': communicateFields }">
                        <!--address-->
                        <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'Communicate Address'">
                                <img :src="communicate_address" alt="address" class="w-7 h-7">
                            </div>
                            <div class="w-1/2 mr-2 relative">
                                <label for="address" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.address || isInputFocusedAddressCom, 'text-blue-600': isInputFocusedAddressCom }">Address</label>
                                <input id="contact_number" v-model="formValues.address" type="text" ref="contact_number"
                                    placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    @focus="isInputFocusedAddressCom = true" @blur="isInputFocusedAddressCom = false" />
                            </div>
                            <div class="w-1/2 relative">
                                <label for="pincode" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500': formValues.pincode || isInputFocusedPincodeCom, 'text-blue-600': isInputFocusedPincodeCom }">Pincode</label>
                                <input id="pincode" v-model="formValues.pincode" type="number" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedPincodeCom = true" @blur="isInputFocusedPincodeCom = false" />
                            </div>
                        </div>
                        <!--Communicate Address-->
                        <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'Address'">
                                <img :src="map" alt="map" class="w-7 h-7">
                            </div>
                            <div class="w-1/3 mr-2 relative">
                                <label for="state_name_com"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.state_name || isInputFocusedStateCom, 'text-blue-600': isInputFocusedStateCom }">State</label>
                                <!-- <select id="state_name_com" v-model="formValues.state_name"
                                    @change="getDistrictList(selectedStateId)"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedStateCom = true" @blur="isInputFocusedStateCom = false">
                                    <option v-for="(opt, index) in this.state_list" :key="index" :value="opt.name">
                                        {{ opt.name }}</option>
                                </select> -->
                                <input id="state_name_com" v-model="formValues.state_name" type="text"
                                    ref="state_name_com" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    @focus="isInputFocusedStateCom = true" @blur="isInputFocusedStateCom = false" />
                            </div>

                            <div class="w-1/3 mr-2 relative">
                                <label for="district_name_com"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.district_name || isInputFocusedDistrictCom, 'text-blue-600': isInputFocusedDistrictCom }">Districts</label>
                                <!-- <select id="district_name_com" v-model="formValues.district_name"
                                    @change="getCityList(selectedDistrictId)"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedDistrictCom = true" @blur="isInputFocusedDistrictCom = false">
                                    <option v-for="(opt, index) in this.district_list"
                                        :class="{ 'hidden': index === 0 }" :key="index" :value="opt.name">{{ opt.name }}
                                    </option>
                                </select> -->
                                <input id="district_name_com" v-model="formValues.district_name" type="text"
                                    ref="district_name_com" placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    @focus="isInputFocusedDistrictCom = true"
                                    @blur="isInputFocusedDistrictCom = false" />
                            </div>
                            <div class="w-1/3 mr-2 relative">
                                <label for="city_name" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.city_name || isInputFocusedCityCom, 'text-blue-600': isInputFocusedCityCom }">City</label>
                                <!-- <select id="city_name" v-model="formValues.city_name"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"
                                    @focus="isInputFocusedCityCom = true" @blur="isInputFocusedCityCom = false">
                                    <option v-for="(opt, index) in this.city_list" :key="index" :value="opt.name">
                                        {{ opt.name }}</option>
                                </select> -->
                                <input id="city_name" v-model="formValues.city_name" type="text" ref="city_name"
                                    placeholder=" "
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                                    @focus="isInputFocusedCityCom = true" @blur="isInputFocusedCityCom = false" />
                            </div>
                        </div>
                    </div>
                </div>
                <!--customer category-->
                <div v-if="!is_quick_save" class="rounded mt-5">
                    <div class="flex justify-between items-center cursor-pointer px-2 py-2 rounded text-sm font-bold sm:w-1/2 w-full"
                        :class="{ 'bg-blue-300': categoryFields, ' bg-gray-300': !categoryFields }"
                        @click="categoryFields = !categoryFields">
                        <p>Category</p>
                        <div v-if="categoryFields" class="triangle-up"></div>
                        <div v-else class="triangle-down"></div>
                    </div>
                    <div v-if="categoryFields" class="px-2 py-5 rounded"
                        :class="{ 'border border-blue-400': categoryFields }">
                        <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'Address'">
                                <img :src="category" alt="category" class="w-7 h-7">
                            </div>
                            <div class="flex sm:w-3/4 w-full relative">
                                <label for="categoryDropdown"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.customer_category || isInputFocusedCategory, 'text-blue-600': isInputFocusedCategory }">Category</label>
                                <select id="categoryDropdown" v-model="formValues.customer_category"
                                    class="text-sm p-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded-r-none rounded-l"
                                    @change="handleServiceCategorySelection" @focus="isInputFocusedCategory = true"
                                    @blur="isInputFocusedCategory = false">
                                    <option value="" disabled selected>Select a customer category</option>
                                    <option v-for="category in serviceCategories" :key="category.id"
                                        :value="category.id">{{
                                            category.category_name }}</option>
                                    <!-- <option class="text-center bg-zinc-300" value="status">
                                        + New Customer Category</option> -->
                                </select>
                                <div class="bg-teal-600 text-white p-1 px-2 flex justify-center items-center"
                                    @click="openModalService">
                                    <font-awesome-icon icon="fa-solid fa-plus" />
                                </div>
                            </div>

                        </div>
                    </div>

                </div>
                <!--Notes-->
                <div v-if="!is_quick_save" class="rounded mt-5">
                    <div class="flex justify-between items-center cursor-pointer px-2 py-2 rounded text-sm font-bold sm:w-1/2 w-full"
                        :class="{ 'bg-blue-300': notesFields, ' bg-gray-300': !notesFields }"
                        @click="notesFields = !notesFields">
                        <p>Notes</p>
                        <div v-if="notesFields" class="triangle-up"></div>
                        <div v-else class="triangle-down"></div>
                    </div>
                    <div v-if="notesFields" class="px-2 py-5 rounded"
                        :class="{ 'border border-blue-400': notesFields }">
                        <div class="flex items-center mt-5">
                            <div class="mr-2" :title="'Address'">
                                <img :src="notes" alt="notes" class="w-7 h-7">
                            </div>
                            <div class="sm:w-3/4 w-full mr-2 relative">
                                <label for="notes" class="text-sm font-bold absolute left-2 top-3 text-gray-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-500 text-gray-500': formValues.notes || isInputFocusedNotes, 'text-blue-600': isInputFocusedNotes }">Notes</label>
                                <textarea id="notes" v-model="formValues.notes" rows="3"
                                    @focus="isInputFocusedNotes = true" @blur="isInputFocusedNotes = false"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Buttons -->
            <div v-if="!is_quick_save" class="flex justify-end items-center m-3 mt-5">
                <button @click="cancelModal"
                    class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  p-1 px-10 py-2 mr-8">Cancel</button>
                <button @click="sendModal" ref="save_button"
                    class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white p-1 px-10 py-2 mr-8 ">Save</button>
            </div>
            <!-- </div> -->
        </div>
        <addCustomerCategory :show-modal="showModal_add_service" @close-modal="closeModalService"
            :companyId="companyId">
        </addCustomerCategory>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <confirmDialog :showModal="show_confirm" :message="message" @onConfirm="userAcepted" @onCancel="userCancelled">
        </confirmDialog>
    </div>
</template>

<script>
import axios from 'axios';
import addCustomerCategory from './addCustomerCategory.vue';
import dialogAlert from './dialogAlert.vue';
import { mapState, mapGetters, mapActions } from 'vuex';
import confirmDialog from './confirmDialog.vue';
export default {
    name: 'customerRegister',
    components: {
        addCustomerCategory,
        dialogAlert,
        confirmDialog
    },
    props: {
        showModal: Boolean,
        editData: Object,
        type: String,
        userName: String,
        more_info: Boolean
    },
    data() {
        return {
            showModal_add_service: false,
            'overlay-active': this.showModal,
            isMobile: false,
            customerCategory: "",
            customerCategoryName: "",
            serviceCategories: [],
            formValues: {},
            isMessageDialogVisible: false,
            message: null,
            isOpen: false,
            updatedData: null,
            validationMessage: '',
            validationMessageAlter: '',
            userAlreadyExist: false,
            existUser: null,
            //----icons
            user_name: '/images/service_page/User.png',
            phone: '/images/service_page/Phone_call.png',
            company: '/images/service_page/cooperation.png',
            communicate_address: '/images/service_page/address.png',
            notes: '/images/service_page/Writing.png',
            email: '/images/service_page/Email.png',
            category: '/images/service_page/Add.png',
            map: '/images/service_page/map.png',
            anniversary: '/images/service_page/anniversary.png',
            birthDate: '/images/service_page/happy-birthday.png',
            balance_img: '/images/service_page/Money.png',

            //---header--
            personalfields: true,
            businessfields: false,
            communicateFields: false,
            categoryFields: false,
            notesFields: false,
            dateFields: false,
            //--fields
            isInputFocused: false,
            isInputFocusedLastName: false,
            isInputFocusedMobilePrimary: true,
            isInputFocusedMobileSecondary: false,
            isInputFocusedEmail: false,
            isInputFocusedBusiness: false,
            isInputFocusedGst: false,
            isInputFocusedAddress: false,
            isInputFocusedPincode: false,
            isInputFocusedState: false,
            isInputFocusedDistrict: false,
            isInputFocusedCity: false,
            isInputFocusedStateCom: false,
            isInputFocusedDistrictCom: false,
            isInputFocusedCityCom: false,
            isInputFocusedCategory: false,
            isInputFocusedNotes: false,
            isInputFocusedAddressCom: false,
            isInputFocusedPincodeCom: false,
            isInputFocusedData: { birth_date: true, anniversary_date: true, balance: false },
            //---gst number---
            gstValidation: '',
            //--api integration--
            companyId: null,
            userId: null,
            getStateId: null,
            getDistrictId: null,
            state_list: [],
            district_list: [],
            city_list: [],
            district_list_com: [],
            city_list_com: [],
            //---api integration---
            companyId: null,
            userId: null,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //--is quick save---
            is_quick_save: true,
            filteredCustomerOptions: [],
            show_confirm: false,
            is_confirmed: false,
        }
    },
    methods: {
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        //--mobile number validation--
        validatePhoneNumber(inputtxt, type) {
            // Regular expression for phone numbers
            var phoneno = /^(?:0|\+91)?[6-9]\d{9}$/;

            // Check if the input matches the regular expression
            if (inputtxt.match(phoneno)) {
                if (type === 'alter') {
                    this.validationMessageAlter = '';
                } else {
                    this.validationMessage = '';
                }
                return true; // Phone number is valid
            } else {
                // alert("Invalid phone number format. Please enter a valid phone number.");
                // this.message = "Invalid phone number format. Please enter a valid phone number.";
                // this.open_message = true;
                if (type === 'alter') {
                    this.validationMessageAlter = 'Enter valid mobile number';
                }
                else {
                    this.validationMessage = 'Enter valid mobile number';
                }
                return false; // Phone number is invalid
            }
        },
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        userAcepted() {
            this.show_confirm = false;
            this.is_confirmed = true;
            this.sendModal();
        },
        userCancelled() {
            this.show_confirm = false;
        },
        sendModal() {
            if (this.formValues.first_name && this.formValues.first_name.length >= 3 && /[a-zA-Z]/.test(this.formValues.first_name) && this.formValues.contact_number && this.validatePhoneNumber(this.formValues.contact_number) && this.gstValidation === '') {
                if ((this.filteredCustomerOptions && this.filteredCustomerOptions.length === 0) || this.is_confirmed) {
                    this.open_loader = true;
                    this.is_confirmed = false;
                    if (this.formValues.customer_category === '' || this.formValues.customer_category === null) {
                        delete this.formValues['customer_category'];
                    }
                    if (!this.formValues.contact_number) {
                        this.formValues.contact_number = '';
                    }
                    if (this.type === 'edit') {
                        axios.put(`/customers/${this.formValues.id}`, this.formValues)
                            .then(response => {
                                // console.log(response.data.data);
                                // this.updatedData = response.data.data;
                                this.open_loader = false;
                                this.formValues = {};
                                this.updateKeyWithTime('customer_update');
                                // this.openMessageDialog('Existing data updated successfully');
                                this.$emit('close-modal', response.data.data);

                            })
                            .catch(error => {
                                console.error('Error update request', error);
                                this.open_loader = false;
                            })
                    } else {
                        axios.post('/customers', { ...this.formValues, company_id: this.companyId })
                            .then(response => {
                                // console.log(response.data.data);
                                // this.updatedData = response.data.data;
                                this.open_loader = false;
                                this.formValues = {};
                                this.updateKeyWithTime('customer_update');
                                // this.openMessageDialog('Add new customer successfully');
                                this.$emit('close-modal', response.data.data);

                            })
                            .catch(error => {
                                console.error('Error', error);
                                this.open_loader = false;
                                this.openMessageDialog(error.response.data.message);
                            })
                    }
                    // console.log(parseTheData, 'EEEEEEEE');
                    // this.$emit('close-modal', updatedObj);
                } else {
                    this.show_confirm = true;
                    this.message = 'In this contact number, a customer already exists. Can we save multiple customers on the same contact number?';
                }
            } else if (!this.userAlreadyExist) {
                this.openMessageDialog(!this.formValues.first_name ? 'Please enter the name' : this.formValues.first_name.length <= 2 ? 'Please fill first name atleast 3 characters' : !/[a-zA-Z]/.test(this.formValues.first_name) ? 'Please enter valid first name' : !this.formValues.contact_number || !this.validatePhoneNumber(this.formValues.contact_number) ? 'Please enter the contact number and validate' : this.gstValidation !== '' ? 'Please Enter valid GST Number' : 'Please fill the required informations');
            } else {
                this.openMessageDialog(`User alrady exist for ${this.existUser.first_name + ' - ' + this.existUser.contact_number}`);
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleServiceCategorySelection() {
            // console.log(value, 'WWWWWWW');
            const selectedCategory = this.serviceCategories.find(category => category.id === this.formValues.customer_category);
            // console.log(selectedCategory, 'May know what happening....!');
            if (selectedCategory && selectedCategory !== 'status') {
                this.customerCategoryName = selectedCategory ? selectedCategory.category_name : "";
            } else {
                this.openModalService();
                this.formValues.customer_category = '';
            }
        },
        openModalService() {
            this.showModal_add_service = true;
        },
        closeModalService(data) {
            // console.log(data, 'What happening the data...!');
            if (data) {
                this.serviceCategories.push(data);
                // this.customer_category = data.id;
                this.formValues.customer_category = data.id;
                this.showModal_add_service = false;
            } else {
                this.showModal_add_service = false;
            }
        },
        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                // console.log(this.editData, 'What about...!');
                this.formValues = { ...this.editData, birth_date: this.formatDateInitial(this.editData.birth_date), anniversary_date: this.formatDateInitial(this.editData.anniversary_date) };
            } else {

                this.formValues = {};
            }
        },
        //---Add new user--
        userAddNew() {
            // console.log(this.userName);
            if (/^\d+$/.test(this.userName)) {
                this.formValues.contact_number = this.userName;
            } else {
                if (this.userName) {
                    const strSplit = this.userName.split(' ');
                    // console.log(strSplit, 'PPPPPPPP');
                    this.formValues.first_name = strSplit[0];
                    if (strSplit[1]) {
                        this.formValues.last_name = strSplit[1];
                    }
                }

            }
        },
        //---open alert--
        openMessageDialog(message) {
            // this.isMessageDialogVisible = true;
            this.message = message;
            this.type_toaster = 'info';
            this.show = true;
        },
        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.updatedData) {
                this.$emit('close-modal', this.updatedData);
                // this.initializeData();
                if (this.updatedData) {
                    this.updatedData = null;
                }
            } else {
                // this.$emit('close-modal');
                // this.initializeData();
            }
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.first_name;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---handle focus first name to contact number---
        handleFocusMobile(type) {
            this.$nextTick(() => {
                // console.log(type);
                if (this.$refs[type]) {
                    this.$refs[type].focus();
                    // this.$refs[type].click();
                }
            })
        },
        //--validate GST--
        validateGST() {
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.formValues.gst_number !== '') {
                // Check if the input matches the expected format
                if (regex.test(this.formValues.gst_number)) {
                    this.gstValidation = '';
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                }
            }
        },
        //--get state list ----
        getStateList() {
            if (this.state_list.length === 0 && (this.communicateFields || this.businessfields)) {
                axios.post('https://www.nammav2app.in/api/state-list', { country_id: 101 })
                    .then(response => {
                        // console.log(response.data, 'state');
                        this.state_list = response.data;
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }
        },
        getDistrictList(id, editType) {
            // console.log(id, 'state id............');
            axios.get('https://www.nammav2app.in/api/get-district', { params: { state_id: id } })
                .then(response => {
                    // if (type === 'com') {
                    //     this.district_list_com = response.data;
                    //     if (this.formValues.district_name_com && editType === 'edit') {
                    //         this.getCityList(this.comSelectedDistrictId, 'com')
                    //     }
                    // } else {
                    this.district_list = response.data;
                    if (this.formValues.district_name && editType === 'edit') {
                        this.getCityList(this.selectedDistrictId)
                    }
                    // }
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        getCityList(id) {
            axios.post('https://www.nammav2app.in/api/getcity-lists', { district_id: id })
                .then(response => {
                    // console.log(response.data, 'city');
                    // if (type === 'com') {
                    //     this.city_list_com = response.data;
                    // } else {
                    this.city_list = response.data;
                    // }
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---convert to date method---
        formatDate(field) {
            // Get the value of the input field
            let dateValue = this.formValues[field];

            // Check if the date value is not empty
            if (dateValue) {
                // Convert the date value to 'yyyy-mm-dd' format
                let formattedDate = new Date(dateValue).toISOString().slice(0, 10);

                // Update the formValues with the formatted date
                this.formValues[field] = formattedDate;
            }
        },
        isValidDate(dateString) {
            // Attempt to create a Date object from the provided string
            const date = new Date(dateString);
            return !isNaN(date.getTime()) && date.getFullYear() >= 100;
        },
        //---edit existing customer--
        formatDateInitial(date) {
            if (this.isValidDate(date)) {
                // console.log(date, 'datatatatatatat');
                const d = new Date(date);
                const month = String(d.getMonth() + 1).padStart(2, '0');
                const day = String(d.getDate()).padStart(2, '0');
                const year = d.getFullYear();
                // console.log(`${month}/${day}/${year}`, 'RRRRRRRRRRRRRRR');
                return `${year}-${month}-${day}`;
            }
        },
        //--get customer category---
        getCustomerData() {
            axios.get('/customer-categories', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data);
                    this.serviceCategories = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        //--search customer--
        handleDropdownInput(data) {
            if (data && data.length > 3) {
                if (this.currentCustomer && this.currentCustomer.length === 0) {
                    this.fetchCustomerList();
                }
                const inputValue = data;
                if (inputValue && inputValue.length > 3) {
                    if (!isNaN(inputValue)) {
                        const inputNumber = inputValue.toLowerCase();
                        this.filteredCustomerOptions = this.currentCustomer.filter(option =>
                            option.contact_number.toLowerCase().includes(inputNumber)
                        );
                    } else {
                        const inputName = inputValue.toLowerCase();
                        this.filteredCustomerOptions = this.currentCustomer.filter(option =>
                            option.last_name
                                ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                                : (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                        );
                    }
                }
            } else {
                this.filteredCustomerOptions = [];
            }
        },
        selectThisCustomer() {
            if (this.filteredCustomerOptions && this.filteredCustomerOptions.length > 0 && this.formValues.contact_number && this.formValues.contact_number.length > 5) {
                // console.log(this.filteredCustomerOptions[0], 'Selected customer');
                this.$emit('close-modal', this.filteredCustomerOptions[0])
            }
        }
    },
    computed: {
        ...mapGetters('customer', ['currentCustomer']),
        //---business----
        selectedStateId() {
            // Find the district object with the selected name and return its ID
            const selectedState = this.state_list.find(state => state.name === this.formValues.state_name);
            console.log(selectedState, 'Seletcted State Id');
            return selectedState ? selectedState.id : null;
        },
        selectedDistrictId() {
            // Find the district object with the selected name and return its ID
            const selectedDistrict = this.district_list.find(district => district.name === this.formValues.district_name);
            return selectedDistrict ? selectedDistrict.id : null;
        },
        //----communication
        //---business----
        comSelectedStateId() {
            // Find the district object with the selected name and return its ID
            const selectedState = this.state_list.find(state => state.name === this.formValues.state_name_com);
            console.log(selectedState, 'Seletcted State Id');
            return selectedState ? selectedState.id : null;
        },
        comSelectedDistrictId() {
            // Find the district object with the selected name and return its ID
            const selectedDistrict = this.district_list_com.find(district => district.name === this.formValues.district_name_com);
            return selectedDistrict ? selectedDistrict.id : null;
        }
    },
    mounted() {
        this.updateIsMobile();
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        // let getCategory = localStorage.getItem('customerCategory');
        // if (getCategory) {
        //     let parseCategory = JSON.parse(getCategory);
        //     if (parseCategory) {
        //         this.serviceCategories = parseCategory;
        //     }
        // }
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            if (this.serviceCategories && this.serviceCategories.length === 0) {
                this.getCustomerData();
            }
            if (this.editData && this.type == 'edit') {
                this.initializeData();
            }
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                    if (this.more_info) {
                        this.is_quick_save = !this.more_info;
                    }
                }
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
        userName: {
            handler() {
                this.userAddNew();
                // this.handleFocus();
            },
        },
        state_list: {
            deep: true,
            handler(newValue) {
                if (this.formValues.state_name) {
                    this.getDistrictList(this.selectedStateId, 'edit')
                }
            }
        },

    },
}
</script>
<style scoped>
/* Modal styling */
.modal {
    position: fixed;
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Make modal content scrollable on mobile */
.modal-content {
    overflow-y: auto;
    /* Add this line to make the content scrollable */
}

#gst_number,
#business_name {
    text-transform: uppercase;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;


    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>