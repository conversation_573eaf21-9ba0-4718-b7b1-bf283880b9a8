{"name": "track-newnew", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite --port 4000 --host ************", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@ckeditor/ckeditor5-vue": "^7.3.0", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-brands-svg-icons": "^6.5.2", "@fortawesome/free-regular-svg-icons": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@zxing/browser": "^0.1.5", "axios": "^1.7.2", "chart.js": "^4.4.3", "ckeditor5": "^43.3.1", "file-saver": "^2.0.5", "firebase": "^11.3.1", "flatpickr": "^4.6.13", "jsbarcode": "^3.11.6", "jspdf": "^1.5.3", "jspdf-autotable": "^3.5.0", "papaparse": "^5.5.2", "qrcode": "^1.5.4", "vue": "^3.4.21", "vue-chartjs": "^5.3.1", "vue-flatpickr-component": "^11.0.5", "vue-html-to-paper": "^2.0.3", "vue-pdf": "^1.0.0", "vue-router": "^4.3.2", "vue3-table-lite": "^1.4.0", "vuex": "^4.1.0", "xlsx": "^0.18.5"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "autoprefixer": "^10.4.19", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "vite": "^6.2.1", "vite-plugin-pwa": "^0.21.1"}}