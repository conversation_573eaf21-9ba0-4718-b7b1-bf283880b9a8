<template>
    <div class="text-sm mb-12" :class="{ 'mt-[60px]': isMobile }">
        <!-- <div class="mb-5 -mt-2">
            <button class="font-bold hover:bg-gray-500 bg-gray-600 text-white px-2 py-2 rounded"
                @click="$router.go(-1)">
                &#8592; Go Back
            </button>
        </div> -->
        <div class="my-custom-margin m-1 mt-5 relative">

            <!--Title-->
            <div v-if="!open_skeleton && record && record.title" class="py-2">
                <p class="text-lg font-bold text-center underline text-blue-800">{{ record.title }}</p>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
                :rows="number_of_rows" :gap="gap" :type="'grid'">
            </skeleton>
            <!---Amc data-->
            <div v-if="!open_skeleton" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-2"
                :class="{ 'border px-2 py-2 rounded bg-white': isMobile }"
                :style="{ 'box-shadow': isMobile ? '0 4px 6px rgba(0, 0, 0, 0.3)' : '' }">
                <div v-for="(column, colIndex) in dynamicFields" :key="colIndex"
                    :class="{ 'hidden': !column.visible, 'border px-4 py-2 rounded lg:py-3 bg-white': !isMobile }"
                    :style="{ 'box-shadow': !isMobile ? '0 4px 6px rgba(0, 0, 0, 0.3)' : '' }">
                    <div class="flex items-center">
                        <div class="mr-2 w-10 flex justify-center items-center" :title="'customer'">
                            <img :src="column.label === 'Customer' ? user_name : column.label === 'Amc Date' ? date : column.label === 'Amc Details' ? notes : column.label === 'Number Of Interval' ? interval : column.label === 'Assign To' ? assign : column.label === 'Amc Payment Type' ? payment_type : column.label === 'Number Of Service' ? number_of_service : column.label === 'Title' ? title_img : column.label === 'Created At' ? created_amc : statusOf"
                                alt="customer" class="sm:w-7 sm:h-7 w-6 h-6">
                        </div>
                        <span
                            v-if="!Array.isArray(record[column.field]) && column.field !== 'amc_payment_type' && column.field !== 'amc_status' && column.field !== 'customer' && column.field !== 'assign_to' && column.field !== 'created_at' && column.field !== 'number_of_interval'">
                            <span class="font-bold"> {{ column.label }} </span> : {{ column.field === 'amc_date' ?
                                record[column.field].substring(0, 10) : record[column.field] }}</span>
                        <span v-if="column.field === 'amc_payment_type'">
                            <span class="font-bold"> {{ column.label }} </span> : {{ record[column.field] === '0' ?
                                'Paid' :
                                'Unpaid' }}</span>
                        <span v-if="column.field === 'amc_status'">
                            <span class="font-bold"> {{ column.label }} </span> : {{ record[column.field] === 0 ? 'Open'
                                :
                                record[column.field] === 1 ? 'Progress' : 'Completed' }}</span>
                        <span v-if="column.field === 'customer'"><span class="font-bold"> {{ column.label }} </span> :
                            {{ record[column.field].first_name + ' ' + (record[column.field].last_name ?
                                record[column.field].last_name : '') }}
                            <span v-if="record[column.field].contact_number" class="text-blue-800 font-bold pl-2"
                                @click="dialPhoneNumber(record[column.field].contact_number)"><font-awesome-icon
                                    class="pr-1" icon="fa-solid fa-phone" size="lg" style="color: #122c40;" /> {{
                                        record[column.field].contact_number }}</span>
                        </span>
                        <span v-if="column.field === 'number_of_interval'">
                            <span>{{ column.label }} </span> : {{ record[column.field] <= 11 ? `${record[column.field]}
                                Month` : `Year` }} </span>

                                <!-- <span v-if="column.field === 'assign_to'">{{record[column.field].map((opt) => opt.name).join(', ')}}</span> -->
                                <span v-if="Array.isArray(record[column.field])">
                                    <span class="font-bold"> {{ column.label }} </span> : {{
                                        record[column.field].map((opt) => opt.name).join(', ')
                                    }}</span>
                                <span v-if="column.field === 'created_at'">
                                    <span class="font-bold"> {{ column.label }} </span> : {{
                                        formatedData(record[column.field]) }}
                                </span>
                    </div>
                </div>
            </div>
            <!---Product and date description-->
            <div v-if="!open_skeleton" class="grid grid-cols-1 sm:grid-cols-3 gap-2 mt-4 text-sm">

                <!--Product List-->
                <div v-if="record && Array.isArray(record.product_lists)"
                    class="border px-2 py-2 text-sm rounded lg:py-3 mt-2 bg-white"
                    style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);">
                    <div class="flex py-2">
                        <div class="mr-2 w-10 flex justify-center items-center" :title="'product'">
                            <img :src="service_product" al t="product" class="sm:w-7 sm:h-7 w-6 h-6">
                        </div>
                        <p class="px-2 py-1 font-bold">Product List:</p>
                    </div>
                    <div>
                        <table class="w-full border border-gray-300">
                            <thead class="set-header-background text-white font-normal">
                                <tr>
                                    <th class="border border-gray-300 py-1 font-normal">Product
                                    </th>
                                    <th class="border border-gray-300 py-1 font-normal">
                                        Description
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(product, index) in record.product_lists" :key="index">
                                    <td class="text-center border border-gray-300 py-1 px-1">{{ product.product }}</td>
                                    <td class="text-center border border-gray-300 py-1 px-1">{{ product.description }}
                                    </td>

                                </tr>
                            </tbody>
                        </table>
                        <!-- <div>
                        <p><span class="font-bold py-1">Product :</span> {{ product.product }}</p>
                        <p><span class="font-bold py-1">Description :</span> {{ product.description }}</p>
                    </div> -->
                    </div>
                </div>
                <!---date description-->
                <div v-if="record && Array.isArray(record.product_lists)"
                    class="col-span-2 border px-2 py-2 text-sm rounded lg:py-3 mt-2 bg-white"
                    style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);">
                    <div class="flex py-2">
                        <div class="mr-2 w-10 flex justify-center items-center" :title="'date service'">
                            <img :src="service_date" alt="date service" class="sm:w-7 sm:h-7 w-6 h-6">
                        </div>
                        <p class="px-2 py-1 font-bold">Date Description:</p>
                    </div>
                    <div>
                        <table class="w-full border border-gray-300">
                            <thead class="set-header-background text-white">
                                <tr>
                                    <th class="border border-gray-300 py-1 font-normal">
                                        Date</th>
                                    <!-- <th class="border border-gray-300 py-1 font-normal">Note</th> -->
                                    <th class="border border-gray-300 py-1 font-normal">status
                                    </th>
                                    <th class="border border-gray-300 py-1 font-normal">Updated By
                                    </th>
                                    <th class="border border-gray-300 py-1 font-normal">Service At
                                    </th>
                                    <th class="border border-gray-300 py-1 font-normal">Service Id
                                    </th>
                                    <th class="border border-gray-300 py-1 font-normal">Service Status
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(service, index) in record.date_description" :key="index">
                                    <td class="text-center border border-gray-300 py-1 px-1 text-blue-700">{{
                                        generateDate(service.date) }}</td>
                                    <!-- <td class="text-center border border-gray-300 py-1 px-1">{{ service.note }}</td> -->
                                    <td class="text-center border border-gray-300 py-1 px-1"
                                        :class="{ 'text-green-600 font-bold': service.status === '2', 'text-blue-700 font-bold': service.status === '1', 'text-yellow-700 font-bold': service.status === '0', 'text-red-600': service.status !== '2' && service.status !== '1' && service.status !== '0' }">
                                        {{ service.status ? service.status === '2' ? 'Completed' :
                                            service.status === '1' ? 'Progress' : service.status === '0' ? 'Open' :
                                                service.status === '3' ?
                                                    'Cancelled' : '' : 'Not opened yet' }}
                                    </td>
                                    <td class="text-center border border-gray-300 py-1 px-1">{{ service.updated_by ?
                                        service.updated_by.name : '' }}</td>
                                    <td class="text-center border border-gray-300 py-1 px-1 text-blue-700 hover:underline cursor-pointer"
                                        @click="viewServices(service)">
                                        {{ service &&
                                            service.service_created_at ? generateDate(service.service_created_at, true) :
                                            ' -- ' }}</td>
                                    <td class="text-center border border-gray-300 py-1 px-1 text-green-700 hover:underline cursor-pointer"
                                        @click="viewServices(service)">
                                        {{ service
                                            &&
                                            service.service_code ? service.service_code : ' -- ' }}</td>
                                    <td @click="viewServices(service)"
                                        class="text-center border border-gray-300 py-1 px-1 hover:underline cursor-pointer"
                                        :class="{
                                            'text-blue-700': service.service_status == 0 && service.service_id,
                                            'text-rose-700': service.service_status == 1 && service.service_id,
                                            'text-yellow-700': service.service_status == 2 && service.service_id,
                                            'text-violet-700': service.service_status == 3 && service.service_id,
                                            'text-lime-700': service.service_status == 4 && service.service_id,
                                            'text-[#14532d]': service.service_status == 5 && service.service_id,
                                            'text-red-700': service.service_status == 6 && service.service_id,
                                            'text-green-700': service.service_status == 7 && service.service_id
                                        }">{{ service &&
                                            service.service_status >= 0 && service.service_id ?
                                            getServicestatus(service.service_status) : ' -- '
                                        }}</td>

                                </tr>
                            </tbody>
                        </table>
                        <!-- <div>
                        <p><span class="font-bold py-1">Date :</span> {{ service.date }}</p>
                        <p><span class="font-bold py-1">Note :</span> {{ service.note }}</p>
                        <p><span class="font-bold py-1">status :</span> <span class="font bold"
                                :class="{ 'text-green-600 font-bold': service.status === '2', 'text-orange-700 font-bold': service.status === '1', 'text-yellow-700 font-bold': service.status === '0', 'text-red-600': service.status !== '2' && service.status !== '1' && service.status !== '0' }">
                                {{ service.status ? service.status === '2' ? 'Closed' : service.status === '1' ?
                                    'Progress' : 'Open' : 'Not opened yet' }}</span>
                        </p>
                    </div> -->
                    </div>
                </div>
            </div>

            <!---AMC update-->
            <div v-if="!open_skeleton" class="mt-5">
                <p
                    class="font-bold text-center underline text-lg m-3 mt-                                                                                                                                                                  5">
                    AMC Update</p>
                <div class="grid gap-4">
                    <!---date Description-->
                    <div class="relative">
                        <div v-for="(date_description, index) in formValues.date_description" :key="index"
                            class="w-full mt-5 justify-center items-center border bg-white px-2 py-4 text-sm rounded"
                            style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);">
                            <!-- <div class="mr-2 w-10" :title="'service date'">
                                <img :src="service_date" alt="service date" class="w-7 h-7">
                            </div> -->
                            <div class="flex justify-between items-center mb-3">
                                <p class="font-bold text-blue-700">UpComming Services {{ index + 1 }}</p>
                                <div class="flex justify-between items-center space-x-2">
                                    <button v-if="date_description && !date_description.service_id"
                                        @click="createServices(date_description)"
                                        class="flex items-center gap-2 bg-gradient-to-r from-blue-500 to-blue-700 text-white px-3 sm:px-5 py-1 sm:py-2 rounded-lg shadow-md hover:shadow-lg hover:from-blue-600 hover:to-blue-800 transition duration-300">
                                        <font-awesome-icon icon="fa-solid fa-plus" />
                                        Create Service
                                    </button>
                                    <button v-if="date_description && date_description.service_id"
                                        @click="editServices(date_description)"
                                        class="flex items-center gap-2 bg-gradient-to-r from-green-500 to-green-700 text-white px-3 sm:px-5 py-1 sm:py-2 rounded-lg shadow-md hover:shadow-lg hover:from-green-600 hover:to-green-800 transition duration-300">
                                        <font-awesome-icon icon="fa-solid fa-pencil" />
                                        Edit <span class="hidden sm:flex">Service</span>
                                    </button>
                                    <button v-if="date_description && date_description.service_id"
                                        @click="viewServices(date_description)"
                                        class="flex items-center gap-2 bg-gradient-to-r from-amber-500 to-amber-700 text-white px-3 sm:px-5 py-1 sm:py-2 rounded-lg shadow-md hover:shadow-lg hover:from-amber-600 hover:to-amber-800 transition duration-300">
                                        <font-awesome-icon icon="fa-solid fa-eye" />
                                        View <span class="hidden sm:flex">Service</span>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <div
                                    class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 justify-center items-center">
                                    <div class="w-full mr-2" :class="{ 'mt-3': isMobile }">
                                        <label for="date"
                                            class="text-sm font-bold absolute ml-2 mt-3 text-gray-300 transition-top linear duration-300"
                                            :class="{ 'text-xs bg-white -mt-3 sm:-mt-[8px] px-1 text-blue-700': date_description.date }">Date</label>
                                        <!--:value="formatDateService(date_description.date)"-->
                                        <input id="date" type="date" v-datepicker placeholder=" "
                                            v-model="formValues.date_description[index].date"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                                    </div>
                                    <div class="w-full mr-2">
                                        <label for="description"
                                            class="text-sm font-bold absolute ml-2 text-gray-300 mt-3 transition-top linear duration-300"
                                            :class="{ 'text-xs bg-white px-1 -mt-3  sm:-mt-[8px] text-blue-700': date_description.note || isInputFocused['note' + index] }">
                                            Note
                                        </label>
                                        <input :id="'description_' + index"
                                            v-model="formValues.date_description[index].note"
                                            @mouseover="isInputFocused['note' + index] = true"
                                            @blur="isInputFocused['note' + index] = false"
                                            @mouseout="isInputFocused['note' + index] = false" type="text"
                                            placeholder=" " :ref="'note' + index" @keydown.enter="refTheNextNote(index)"
                                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                                    </div>
                                    <!-- <div class="w-full mr-2">
                                    <label for="status"
                                        class="text-sm font-bold absolute ml-2 text-gray-300 mt-3 transition-top linear duration-300"
                                        :class="{ 'text-xs bg-white px-1 -mt-3  sm:-mt-[8px] text-blue-700': date_description.status || isInputFocused['status' + index] }">
                                        Status
                                    </label>
                                    <select id="date_status" v-model="formValues.date_description[index].status"
                                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                                        @focus="isInputFocused.status = true" @blur="isInputFocused.status = false"
                                        :disabled="isReadOnly(index)">
                                        <option value="0">Open</option>
                                        <option value="1">Progress</option>
                                        <option value="2">Completed</option>
                                        <option value="3">Cancelled</option>
                                    </select>
                                </div> -->
                                    <div class="w-full mr-2 mt-1">
                                        <label for="status"
                                            class="text-xs bg-white px-1 ml-2 -mt-[10px] font-bold text-blue-700 sm:-mt-[15px] absolute transition-top linear duration-300">
                                            Status
                                        </label>
                                        <div class="flex flex-wrap mt-1">
                                            <button
                                                v-for="(option, i) in ['Open', 'Progress', 'Completed', 'Cancelled']"
                                                :key="i"
                                                class="text-xs px-2 py-2 mr-2 mb-2 border border-gray-300 rounded focus:outline-none focus:border-blue-500 transition-colors duration-300"
                                                :class="{ 'bg-blue-500 text-white': Number(formValues.date_description[index].status) === i && formValues.date_description[index].status !== '' }"
                                                @click="selectStatus(i, index)">
                                                {{ option }}
                                            </button>
                                        </div>
                                    </div>
                                    <div class="w-full mr-2">
                                        <div class="border rounded p-4 mt-2 relative text-sm flex mr-2">
                                            <label
                                                class="absolute text-xs font-bold bg-white px-1 -mt-[25px] text-blue-700 px-2">
                                                Attachemnt
                                            </label>
                                            <input
                                                v-if="formValues.date_description[index].attachment === '' || !formValues.date_description[index].attachment"
                                                :id="'fileInput' + index" type="file"
                                                @change="handleFileUpload($event, index)" accept="image/*"
                                                class="display">
                                            <!--loader circle-->
                                            <div v-if="circle_loader === index" class="flex">
                                                <CircleLoader :loading="true"></CircleLoader>
                                            </div>
                                            <div v-if="formValues.date_description[index].attachment"
                                                class="flex ml-1 text-sm mr-2 items-center justify-center cursor-pointer"
                                                :title="'On click to view the image'">
                                                <img :src="formValues.date_description[index].attachment"
                                                    alt="Uploaded Image" @click="displayImageModal(index)"
                                                    class="mt-2 border rounded w-32 h-full">
                                            </div>
                                        </div>
                                        <div v-if="formValues.date_description[index].attachment"
                                            class="absolute w-10 justify-right right-2 -mt-[60px] sm:-mt-[80px] cursor-pointer"
                                            :title="'delete attachment image'" @click="removeAttachmentImage(index)">
                                            <img :src="table_del" alt="delete product" class="w-5 h-5">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-5 px-2 py-2 bg-white rounded" style="box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);">
                    <p class="font-bold text-blue-700">AMC Common Update</p>
                    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 py-1">
                        <!---Assign to--->
                        <div class="items-center mt-5">
                            <div class="flex items-center mr-2" :title="'assign To'">
                                <img :src="assign" alt="assign_to" class="sm:w-7 sm:h-7 w-6 h-6">
                                <label for="assign_to" class="font-bold text-xs bg-white px-1 text-gray-700">
                                    Assign to
                                </label>
                            </div>
                            <div class="relative w-full mr-2 mt-2 ">

                                <div class="border py-2 px-2 flex flex-wrap"
                                    :class="{ 'border-blue-300': isInputFocused.assign_to === true }">

                                    <div v-for="(selectedOption, index) in formValues.assign_to" :key="index"
                                        class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                        {{ selectedOption.name }}
                                        <span @click="removeOption(selectedOption)"
                                            class="text-red-500 font-semibold cursor-pointer">x</span>
                                    </div>
                                    <input type="text" v-model="search" @input="filterOptions" @focus="filterOptions"
                                        ref="search" @blur="hideOptions"
                                        class="h-[35px] mt-1 outline-none border rounded-lg px-2"
                                        @keydown.enter="handleEnterKey('multiple', filteredOptions())"
                                        @keydown.down.prevent="handleDownArrow(filteredOptions())"
                                        @keydown.up.prevent="handleUpArrow(filteredOptions())" />
                                </div>
                                <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                                    v-if="showOptions" :class="{ 'h-auto': isInputFocused.assign_to === true }">
                                    <div v-for="(option, index) in filteredOptions()" :key="index"
                                        class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                        :class="{ 'bg-green-300': index === selectedIndex }"
                                        @click="selectOptionMultiple(option)">
                                        {{ option.name }}
                                    </div>
                                    <button v-if="showAddNew !== null && search.length > 1"
                                        class="bg-green-700 text-white hover:bg-green-600 px-3 py-1 rounded-lg"
                                        @click="openModalEmployee">Add New</button>
                                </div>
                            </div>
                        </div>
                        <!---AMC Status-->
                        <div class="items-center mt-5">
                            <div class="flex items-center mr-2" :title="'amc status'">
                                <img :src="statusOf" alt="amc_status" class="sm:w-7 sm:h-7 w-6 h-6">
                                <label for="amc_status" class="text-xs font-bold bg-white px-1 text-gray-700 ">AMC
                                    Status</label>
                            </div>
                            <div class="flex w-full mr-2 mt-2 relative flex-wrap">

                                <div class="mt-1 flex flex-wrap">
                                    <template v-for="(option, index) in ['Open', 'Progress', 'Completed', 'Cancelled']">
                                        <input type="radio" :id="'amc_option_' + index" :value="index"
                                            v-model="formValues.amc_status" class="hidden" />
                                        <label :for="'amc_option_' + index"
                                            class="text-sm px-3 py-2 mr-2 mb-2 border border-gray-300 rounded cursor-pointer select-none transition-colors duration-300"
                                            :class="{ 'bg-gray-700 text-white': formValues.amc_status == index, 'bg-gray-200': formValues.amc_status !== index }">{{
                                                option }}</label>
                                    </template>
                                </div>
                            </div>
                            <!-- <div class="flex w-full mr-2 relative">
                                <label for="amc_status"
                                    class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                    :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.amc_status !== undefined && formValues.amc_status >= 0) || isInputFocused.amc_status, 'text-blue-700': isInputFocused.amc_status }">AMC
                                    Status</label>
                                <select id="lead_status" v-model="formValues.amc_status"
                                    class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                                    @focus="isInputFocused.amc_status = true" @blur="isInputFocused.amc_status = false">
                                    <option value="0">Open</option>
                                    <option value="1">Progress</option>
                                    <option value="2">Completed</option>
                                    <option value="3">Cancelled</option>
                                </select>
                            </div> -->
                        </div>
                        <!---attachment-->
                        <div class="items-center mt-5">
                            <div class="flex items-center mr-2" :title="'amc status'">
                                <img :src="attachment_img" alt="attachment_img" class="w-7 h-7">
                                <label for="amc_status"
                                    class="text-xs font-bold bg-white px-1 text-gray-700 ">Attachment</label>
                            </div>
                            <div class="relative w-full mr-2 mt-2 ">
                                <!-- Apply flexbox to this div -->
                                <div class="border rounded p-4 mt-2 relative text-sm flex mr-2">

                                    <input v-if="formValues.amc_attachment === '' || !formValues.amc_attachment"
                                        id="fileInput" type="file" @change="handleFileUpload($event, null, true)"
                                        accept="image/*" class="display">

                                    <div v-if="formValues.amc_attachment && formValues.amc_attachment !== ''"
                                        class="flex ml-1 text-sm mr-2 items-center justify-center cursor-pointer"
                                        :title="'On click to view the image'">
                                        <img :src="formValues.amc_attachment" alt="Uploaded Image"
                                            @click="displayImageModal(null, formValues.amc_attachment)"
                                            class="mt-2 border rounded w-[50px] h-full">
                                    </div>
                                    <!--loader circle-->
                                    <div v-if="circle_loader" class="absolute flex">
                                        <CircleLoader :loading="circle_loader"></CircleLoader>
                                    </div>
                                </div>
                                <div v-if="formValues.amc_attachment && !circle_loader"
                                    class="absolute w-10 justify-right right-2 -mt-[100px]"
                                    :title="'delete attachment image'"
                                    @click="removeAttachmentImage(null, formValues.amc_attachment)">
                                    <img :src="table_del" alt="delete product" class="w-5 h-5">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Buttons -->
                <div class="fixed bottom-0 w-full flex justify-center items-center mt-5">
                    <button @click="$router.go(-1)"
                        class="bg-red-700 hover:bg-red-600 sm:rounded-full text-white px-10 py-2 sm:mr-8 w-1/2 sm:w-auto"><font-awesome-icon
                            icon="fa-solid fa-xmark" size="lg" class="pr-2" />Cancel</button>
                    <button @click="sendData"
                        class="bg-green-700 hover:bg-green-600 sm:rounded-full text-white px-10 sm:mr-8 py-2  w-1/2 sm:w-auto">
                        <font-awesome-icon icon="fa-regular fa-floppy-disk" size="lg" class="pr-2" />Save</button>
                </div>
            </div>
            <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog">
            </dialogAlert>
            <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete">
            </confirmbox>
            <displayImage :showModal="showImageModal" :showImageModal="imageURL" @close-modal="closeImageModal">
            </displayImage>
            <employeeRegister :show-modal="showModal_employee" @close-modal="closeModalEmployee" :type="'add'"
                :user_name="EmployeeName"> </employeeRegister>
        </div>
    </div>
    <Loader :showModal="open_loader"></Loader>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>

<script>
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import displayImage from '../../dialog_box/displayImage.vue';
import employeeRegister from '../../dialog_box/employeeRegister.vue';
import { mapActions, mapGetters } from 'vuex';
import noAccessModel from '../../dialog_box/noAccessModel.vue';
export default {
    name: 'displayAMC',
    components: {
        dialogAlert,
        confirmbox,
        displayImage,
        employeeRegister,
        noAccessModel
    },
    props: {
        isMobile: Boolean,
    },
    data() {
        return {
            data: [],
            leadStatus: [],
            leadType: [],
            columns: [],
            record: null,
            formValues: {},
            isInputFocused: {},
            search: '',
            showOptions: false,
            leadStatusList: false,
            employeeList: [],
            showAddNew: false,
            selectedIndex: 0,
            typeOfList: '',
            leadTypeOrStatusList: false,
            isMessageDialogVisible: false,
            showModal_employee: false,
            EmployeeName: '',
            message: '',
            deleteIndex: null,
            editFollowUpIndex: null,
            open_confirmBox: false,
            showImageModal: false,
            imageURL: null,

            //----icons
            user_name: '/images/service_page/User.png',
            date: '/images/service_page/schedule.png',
            assign: '/images/service_page/personService.png',
            statusOf: '/images/service_page/statusIcon.png',
            notes: '/images/service_page/Writing.png',
            category: '/images/service_page/Add.png',
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            payment_type: '/images/service_page/amc_payment.png',
            interval: '/images/service_page/interval.png',
            service_date: '/images/service_page/service_date.png',
            number_of_service: '/images/service_page/number_of_service.png',
            service_product: '/images/service_page/service_product.png',
            title_img: '/images/service_page/issue_description.png',
            created_amc: '/images/service_page/Calendar.png',
            attachment_img: '/images/setting_page/Cloud_computing.png',
            //---api integration----
            companyId: null,
            userId: null,
            pagination: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 2,
            number_of_rows: 20,
            gap: 5,
            circle_loader: false,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            delete_type: null,
            //----create AMC Services--
            selected_data: null,
            //---no access---
            no_access: false,
        };
    },
    created() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.fetchAMCCategory();
        //--get record---
        this.getAMCData(this.$route.query.recordId);
        //--get employee---
        if (this.currentEmployee && this.currentEmployee.length === 0) {
            this.fetchEmployeeList();
        } else {
            this.employeeList = this.currentEmployee;
            this.fetchEmployeeList();
        }

    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('createamcservice', ['currentAMCCategory', 'currenAMCData']),
        ...mapGetters('employess', ['currentEmployee']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),

        dynamicFields() {
            let fields = [];
            const key_order = ['created_at', 'customer', 'assign_to', 'amc_payment_type', 'amc_details', 'number_of_interval', 'number_of_service', 'amc_status'];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            // console.log(this.data, 'WWWWWWWW happening ....@');
            if (this.data) {
                // for (const key in this.record) {
                for (const key of key_order) {
                    if (this.record.hasOwnProperty(key)) {
                        if (key !== 'id' && key !== 'amc_attachment' && key !== 'date_description' && key !== 'product_lists' && key !== 'company_id' && key !== 'customer_id') { // Exclude the 'id' field
                            const label = formatLabel(key);
                            fields.push({ label, field: key, visible: true });
                        }
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },

    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('employess', ['fetchEmployeeList']),
        ...mapActions('createamcservice', ['fetchAMCCategory', 'createAMCCategory', 'updateAMCData', 'resetAMCData', 'getAMCData']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime']),

        getAMCData(id) {
            this.open_skeleton = true;
            axios.get(`/amcs/${id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'GET Record...~');
                    this.record = response.data.data;
                    this.open_skeleton = false;
                    // console.log(typeof this.record.product_lists, 'KKHKHKHKHKH');
                    if (typeof this.record.product_lists === 'string') {
                        this.record.product_lists = JSON.parse(this.record.product_lists);

                    }
                    if (typeof this.record.date_description === 'string') {
                        this.record.date_description = JSON.parse(this.record.date_description);
                        if (typeof this.record.date_description === 'object' && Array.isArray(this.record.date_description) === false) {
                            this.record.date_description = [this.record.date_description];
                        }
                        this.record.date_description = this.record.date_description.map(opt => {
                            opt.date = this.formatDateService(opt.date);
                            return opt;
                        });
                    } else {
                        this.record.date_description = this.record.date_description.map(opt => {
                            opt.date = this.formatDateService(opt.date);
                            return opt;
                        });
                    }

                    // console.log(this.record, 'Finally updated');
                    this.formValues = this.record;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        getEmployeeList(page, per_page) {
            axios.get(`/employees`, { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'GET Employee...~');
                    this.employeeList = response.data.data;
                    this.pagination.employee = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })

        },
        //---multiple dropdown---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            // this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.contactNumber.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.contactNumber.toLowerCase().includes(enteredValue.toLowerCase() && !this.formValues.assign_to.map(opt => opt.name).includes(option.name)));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                } else if (enteredValue.length >= 0) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.includes(option.name) && !this.formValues.assign_to.map(opt => opt.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(opt => opt.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                }
            }
            // Return an empty array if no options match the filter
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(opt => opt.name).includes(option.name)) : this.employeeList;
        },

        selectOptionMultiple(option) {
            if (!this.formValues.assign_to) {
            }
            this.formValues.assign_to.push(option); // Add the selected option to assign_to
            this.search = ''; // Clear the search input
            this.showOptions = false; // Hide the options dropdown
            this.$nextTick(() => {
                // Focus on the search input after selecting an option
                if (this.$refs['search']) {
                    this.$refs['search'].focus();
                    this.$refs['search'].click();
                }
            });
        },
        removeOption(option) {
            this.formValues['assign_to'] = this.formValues['assign_to'].filter(selected => selected !== option);
        },

        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
                this.isInputFocused.assign_to = false;
            }, 300); // Add delay to allow click events on options
        },
        openModalEmployee() {
            this.showModal_employee = true;
            this.EmployeeName = this.search;
            this.showOptions = false;
            this.showAddNew = null;
        },

        closeModalEmployee(data) {
            if (data) {
                if (this.formValues.assign_to) {
                    this.formValues.assign_to.push(data);
                } else {
                    this.formValues.assign_to = [];
                    this.formValues.assign_to.push(data);
                }
                this.employeeList.push(data);
            }
            this.showModal_employee = false;
            this.showOptions = false;
            this.search = '';

        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    this.openModal();
                    // this.openModal(product_name);
                }
            }
            else if (type === 'multiple') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectOptionMultiple(optionArray[this.selectedIndex]);
                }
                else {
                    this.openModalEmployee();
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },


        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },
        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
        },
        sendData() {
            if (this.getplanfeatures('amc')) {
                this.no_access = true;
            } else {
                if (this.formValues.amc_status >= 0 && this.formValues.assign_to) {
                    this.open_loader = true;
                    let send_data = JSON.parse(JSON.stringify(this.formValues))
                    if (send_data.assign_to) {
                        send_data.assign_to = JSON.stringify(send_data.assign_to.map(opt => ({ user_id: opt.id })));
                    }
                    if (send_data.date_description) {
                        send_data.date_description = JSON.stringify(send_data.date_description);
                    }
                    if (send_data.product_lists) {
                        send_data.product_lists = JSON.stringify(send_data.product_lists);
                    }
                    if (typeof send_data.amc_status === 'string') {
                        send_data.amc_status = 1 * send_data.amc_status;
                    }
                    if (typeof send_data.amc_payment_type === 'string') {
                        send_data.amc_payment_type = 1 * send_data.amc_payment_type;
                    }
                    // console.log(send_data, 'EEEEEE how to get all...!')
                    axios.put(`/amcs/${send_data.id}`, { company_id: this.companyId, user_id: this.userId, ...send_data })
                        .then(response => {
                            this.updateKeyWithTime('amc_update');
                            this.record = response.data.data;
                            this.open_loader = false;
                            if (typeof this.record.product_lists === 'string') {
                                this.record.product_lists = JSON.parse(this.record.product_lists);
                            }
                            if (typeof this.record.date_description === 'string') {
                                this.record.date_description = JSON.parse(this.record.date_description);
                            }
                            this.record.date_description = this.record.date_description.map(opt => {
                                opt.date = this.formatDateService(opt.date);
                                return opt;
                            });
                            this.formValues = this.record;
                            this.message = 'AMC has been updated successfully..!';
                            this.type_toaster = 'success';
                            this.show = true;
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                        })
                    // this.saveToLocalStorage();
                    // this.openMessageDialog(this.editFollowUpIndex !== null ? 'Existing AMC updated successfully' : 'Added AMC successfully');
                    // this.openMessageDialog(this.editFollowUpIndex !== null ? 'Existing data updated successfully' : 'Add new lead successfully');

                } else {
                    this.openMessageDialog(`Please fill all requied input fields...!`);
                }
            }
        },

        //---attachment--
        async handleFileUpload(event, index, data) {
            this.circle_loader = index >= 0 ? index : true;
            const file = event.target.files[0];

            if (file && index >= 0 && index !== null && (!this.formValues.date_description[index].attachment || this.formValues.date_description[index].attachment === '')) {

                if (!file) return;

                // Check file size (in bytes)
                const maxSizeBytes = 500 * 1024; // 500kb in bytes
                if (file.size > maxSizeBytes) {
                    // Image exceeds 500kb, compress it
                    try {
                        this.circle_loader = true; // Show loader
                        const compressedFile = await this.compressImage(file);

                        this.uploadImageProfile(compressedFile, index);
                    } catch (error) {
                        console.error("Error compressing image:", error);
                        this.circle_loader = false; // Hide loader on error
                    }
                } else {
                    // Image is <= 500kb, upload directly
                    try {
                        this.circle_loader = true; // Show loader

                        this.uploadImageProfile(file, index);
                    } catch (error) {
                        console.error("Error uploading image:", error);
                        this.circle_loader = false; // Hide loader on error
                    }
                }
            } else if (data) {
                if (file) {
                    if (!file) return;

                    // Check file size (in bytes)
                    const maxSizeBytes = 500 * 1024; // 500kb in bytes
                    if (file.size > maxSizeBytes) {
                        // Image exceeds 500kb, compress it
                        try {
                            this.circle_loader = true; // Show loader
                            const compressedFile = await this.compressImage(file);

                            this.uploadImageProfile(compressedFile, null, true);
                        } catch (error) {
                            console.error("Error compressing image:", error);
                            this.circle_loader = false; // Hide loader on error
                        }
                    } else {
                        // Image is <= 500kb, upload directly
                        try {
                            this.circle_loader = true; // Show loader

                            this.uploadImageProfile(file, null, true);
                        } catch (error) {
                            console.error("Error uploading image:", error);
                            this.circle_loader = false; // Hide loader on error
                        }
                    }

                }
            } else {
                this.openMessageDialog('Attachment already exist..!');
                this.circle_loader = false;
            }
        },
        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        uploadImageProfile(file, index, data) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "amc");
            formData.append("company_id", this.companyId);
            // Make an API request to Laravel backend
            // console.log(file, 'RRRR');
            axios.post('/image', formData)
                .then(response => {
                    // console.log(response.data, 'What happrning....Q');
                    this.circle_loader = false;
                    //  let data_save = { image_path: response.data.image_path };
                    if (index >= 0 && index !== null) {
                        this.formValues.date_description[index].attachment = response.data.media_url;
                    } else if (data) {
                        this.formValues.amc_attachment = response.data.media_url;
                    }
                    this.message = 'Image has been updated successfully..!';
                    this.show = true;
                    this.sendData();
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                });
        },
        //---display the image---
        displayImageModal(index, url) {

            if (index !== null && index >= 0 && this.formValues.date_description[index].attachment) {
                this.showImageModal = true;
                this.imageURL = this.formValues.date_description[index].attachment;
                // window.open(this.formValues.date_description[index].attachment, '_blank');
            } else if (url) {
                this.showImageModal = true;
                this.imageURL = url;
            }
        },
        closeImageModal() {
            this.showImageModal = false;
        },
        //---remove attachment--
        removeAttachmentImage(index, url) {
            // console.log(index, 'EEEEE');
            if (index !== null && index >= 0 && this.formValues.date_description[index].attachment) {
                this.deleteIndex = index;
                this.open_confirmBox = true;
            } else if (url) {
                this.delete_type = url;
                this.open_confirmBox = true;
            }
        },
        deleteRecord() {
            if (this.deleteIndex !== null) {
                axios.delete('/delete-image', { params: { model: "amc", image_url: this.formValues.date_description[this.deleteIndex].attachment } })
                    .then(response => {
                        // console.log(response.data, 'delete image..!');
                        this.formValues.date_description[this.deleteIndex].attachment = null;
                        const fileInput = document.getElementById('fileInput' + this.deleteIndex);
                        if (fileInput) {
                            fileInput.value = '';
                        }
                        this.deleteIndex = null;
                        this.open_confirmBox = false;
                        this.message = 'Image has been deleted successfully..!';
                        this.type_toaster = 'success';
                        this.show = true;
                        this.sendData();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.deleteIndex = null;
                        this.open_confirmBox = false;
                        this.message = 'Image can not delete..!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    })
            } else if (this.delete_type && this.delete_type !== '') {
                axios.delete('/delete-image', { params: { model: "amc", image_url: this.formValues.amc_attachment } })
                    .then(response => {
                        // console.log(response.data, 'delete image..!');
                        this.open_confirmBox = false;
                        this.formValues.amc_attachment = null;
                        const fileInput = document.getElementById('fileInput');
                        if (fileInput) {
                            fileInput.value = '';
                        }
                        this.delete_type = null;
                        this.message = 'Image has been deleted successfully..!';
                        this.type_toaster = 'success';
                        this.show = true;
                        this.sendData();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.message = 'Image can not delete..!';
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.delete_type = null;
                        this.open_confirmBox = false;
                    })
            }
        },
        cancelDelete() {
            this.deleteIndex = null;
            this.open_confirmBox = false;
            this.delete_type = null;
        },
        //---enable the next next index from 0th index 
        isReadOnly(index) {
            // console.log(this.formValues.date_description, 'Waht happening...!!!!',);
            if (this.formValues.date_description !== null && this.formValues.date_description.length > 0) {
                if (index === 0) {
                    return false; // First index is always editable
                } else if (this.formValues.date_description[index - 1] && this.formValues.date_description[index - 1].status) {
                    // console.log(this.formValues.date_description[index - 1].status, 'Waht happenig......!!!!', index-1);
                    // Check if the status of the previous index is '2'
                    // return typeof this.formValues.date_description[index - 1].status === 'string' ? this.formValues.date_description[index - 1].status !== '2' || this.formValues.date_description[index - 1].status === '3' : this.formValues.date_description[index - 1].status !== 2 || this.formValues.date_description[index - 1].status === 3;

                    return typeof this.formValues.date_description[index - 1].status === 'string' ? this.formValues.date_description[index - 1].status !== '2' && this.formValues.date_description[index - 1].status !== '3' : this.formValues.date_description[index - 1].status !== 2 && this.formValues.date_description[index - 1].status !== 3;
                }
                else {
                    return true;
                }
            }
        },
        formatedData(dateData) {
            // Parse the input date as UTC
            const inputDate = new Date(dateData);

            // Adjust the date to UTC+5:30
            const offsetMinutes = 5 * 60 + 30;
            const adjustedTime = inputDate.getTime() + offsetMinutes * 60000;
            const adjustedDate = new Date(adjustedTime);

            // Formatting the date to DD-MM-YYYY HH:MM AM/PM
            const day = adjustedDate.getUTCDate().toString().padStart(2, '0');
            const month = (adjustedDate.getUTCMonth() + 1).toString().padStart(2, '0');
            const year = adjustedDate.getUTCFullYear();
            let hours = adjustedDate.getUTCHours();
            const minutes = adjustedDate.getUTCMinutes();
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12; // Convert 0 to 12 for 12-hour format
            const formattedHours = hours.toString().padStart(2, '0');
            const formattedMinutes = minutes.toString().padStart(2, '0');

            const formattedDate = `${day}-${month}-${year} ${formattedHours}:${formattedMinutes} ${ampm}`;
            return formattedDate;
        },
        dialPhoneNumber(phoneNumber) {
            // console.log(phoneNumber, 'RRRRRRRRRRRR');
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        formatDateService(dateString) {
            const inputDate = new Date(dateString);
            const day = inputDate.getUTCDate().toString().padStart(2, '0');
            const month = (inputDate.getUTCMonth() + 1).toString().padStart(2, '0');
            const year = inputDate.getUTCFullYear();

            return `${year}-${month}-${day}`;
        },
        selectStatus(option, index) {
            if (this.isReadOnly(index)) {
                if (typeof this.formValues.date_description[index - 1].status === 'string' ? this.formValues.date_description[index - 1].status !== '2' && this.formValues.date_description[index - 1].status !== '3' : this.formValues.date_description[index - 1].status !== 2 && this.formValues.date_description[index - 1].status !== 3) {
                    this.message = 'Please update previous services to complete or cancel, as this will affect the service status';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            } else {
                this.formValues.date_description[index].status = option;
            }
        },
        //---create AMC Services---
        createServices(date_description) {
            if (this.getplanfeatures('amc')) {
                this.no_access = true;
            } else {
                if (this.record) {
                    this.updateAMCData(this.record);
                }
                if (date_description && date_description.id && Object.keys(this.currentAMCCategory).length == 0) {
                    this.selected_data = date_description;
                    this.open_loader = true;
                    this.createAMCCategory();
                } else if (date_description && date_description.id && Object.keys(this.currentAMCCategory).length > 0) {
                    this.selected_data = date_description;
                    this.addServices(this.currentAMCCategory);
                }
            }
        },
        addServices(record) {
            if (record) {
                // Replace other special characters with spaces in service category
                const sanitizedServiceCategory = record.service_category.replace(/[^\w\s]/g, ' ');
                let newUrl;
                if (record.form) {
                    newUrl = this.$router.push({ name: 'service-category-add', params: { type: sanitizedServiceCategory, id: record.id }, query: { customer_id: this.customer_id, amc_id: this.record.id, amc_date_id: this.selected_data.id } }).href;
                } else {
                    newUrl = this.$router.push({ name: 'service_category_create_form', params: { type: sanitizedServiceCategory, serviceId: record.id }, query: { createform: 'true', customer_id: this.customer_id } }).href;
                }
                // Change the URL
                window.history.pushState({}, '', newUrl);
            }
        },
        viewServices(date_description) {
            if (this.getplanfeatures('amc')) {
                this.no_access = true;
            } else {
                if (date_description && date_description.service_id && this.currentAMCCategory && Object.keys(this.currentAMCCategory).length > 0) {
                    this.$router.push({ name: 'service-data-view', params: { serviceId: date_description.service_id, type: this.currentAMCCategory.service_category, id: this.currentAMCCategory.id } });
                }
            }
        },
        editServices(date_description) {
            if (this.getplanfeatures('amc')) {
                this.no_access = true;
            } else {
                if (date_description && date_description.service_id && this.currentAMCCategory && Object.keys(this.currentAMCCategory).length > 0) {
                    this.$router.push({ name: 'service-category-view', params: { viewId: date_description.service_id, type: this.currentAMCCategory.service_category, id: this.currentAMCCategory.id } });
                }
            }
        },
        generateDate(data, is_time) {
            // console.log(data, 'What about the data......');
            const dateTimeString = data;
            const date = new Date(dateTimeString);
            const formattedDate = date.toLocaleDateString('en-GB', { year: 'numeric', month: '2-digit', day: '2-digit' });
            const formattedTime = date.toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' });
            if (is_time) {
                return formattedDate + ' ' + formattedTime;
            } else {
                return formattedDate;
            }
        },
        getServicestatus(status) {
            return status == 0 ? 'Service Taken' : status == 1 ? 'Hold' : status == 2 ? 'In-Progress' : status == 3 ? 'New Estimate' : status == 4 ? 'Ready to Deliver' : status == 5 ? 'Delivered' : status == 6 ? 'Cancelled' : 'Completed';

        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
    },
    watch: {
        'formValues.amc_status': {
            deep: true,
            handler(newValue) {
                if (newValue == 2 && this.formValues.date_description && Array.isArray(this.formValues.date_description) && this.formValues.date_description.length > 0) {
                    let validate_all_com = this.formValues.date_description.every(opt => opt.status == 2);
                    if (!validate_all_com) {
                        this.message = 'Please complete all the services and finalize the AMC...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.formValues.amc_status = 1;
                    }
                }
            }
        },
        currentEmployee: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.employeeList = newValue;
                }
            }
        },
        currentAMCCategory: {
            deep: true,
            handler(newValue) {
                if (this.selected_data && this.open_loader) {
                    this.addServices(newValue);
                    this.open_loader = false;
                } else {
                    this.open_loader = false;
                }
            }
        }
    }

};
</script>
