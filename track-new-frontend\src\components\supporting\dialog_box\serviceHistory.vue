<template>
    <!-- Modal -->
    <div v-if="isModalOpen"
        class="text-sm fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-center z-50 ">
        <div class="bg-white w-full sm:w-3/4  transform transition-transform ease-in-out duration-300 rounded overflow-auto sm:h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div
                class="flex justify-between items-center relative text-xl w-full px-4 py-3 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    Service History
                </p>
                <p class="close" @click.stop="closeModal">&times;</p>
            </div>
            <div class="m-5 model-data" v-if="viewServiceData && Object.keys(viewServiceData).length > 0">
                <!-- Service Info -->
                <div class="grid grid-cols-2 sm:grid-cols-2 gap-4">
                    <div>
                        <div>
                            <p> <span class="font-bold">Customer Details:</span></p>
                        </div>
                        <div>
                            <span>{{ viewServiceData.customer.first_name }} {{ viewServiceData.customer.last_name
                                }}</span>
                        </div>
                        <div>
                            <span>{{ viewServiceData.customer.contact_number }}</span>
                        </div>
                        <div>
                            <span>{{ viewServiceData.customer.address ? viewServiceData.customer.address : '' }}</span>
                        </div>
                    </div>
                    <div class="items-center">
                        <div>
                            <p> <span class="font-bold">Service:</span></p>
                        </div>
                        <div>
                            <span>Id:</span>
                            <span class="px-1">{{ viewServiceData.service_id }}</span>
                        </div>
                        <div>
                            <span>Code:</span>
                            <span class="px-1">{{ viewServiceData.service_code }}</span>
                        </div>
                        <div>
                            <span>Date:</span>
                            <span class="px-1">{{ formattedDate(viewServiceData.created_at) }}</span>
                        </div>
                    </div>
                </div>
                <!-- Device details -->
                <div v-if="viewServiceData.service_data">
                    <div>
                        <p> <span class="font-bold">Service Details:</span></p>
                    </div>
                    <p v-if="viewServiceData.service_data.service_type">Service Type: {{
                        viewServiceData.service_data.service_type }}</p>
                    <p v-if="viewServiceData.service_data.brand">Brand: {{ viewServiceData.service_data.brand }}</p>
                    <p v-if="viewServiceData.service_data.device_model">Model: {{
                        viewServiceData.service_data.device_model }}</p>
                    <p v-if="viewServiceData.service_data.problem_title">Problem Title: {{
                        viewServiceData.service_data.problem_title.join(', ') }}</p>

                    <div> {{ viewServiceData.service_data.additional ? 'Additional Material:' : 'Servcie Pay details:'
                        }}
                        <table class="w-full">
                            <thead v-if="viewServiceData.service_data.additional">
                                <th>Product Name </th>
                                <th> Qty </th>
                                <th> Price </th>
                                <th> Total </th>
                                <th> Status </th>
                            </thead>
                            <tbody>
                                <tr :class="{ 'hidden': !viewServiceData.service_data.additional }"
                                    v-for="(data, index) in viewServiceData.service_data.additional" :key="index">
                                    <td class="px-1"> {{ data.product_name }}</td>
                                    <td>
                                        <p class="text-center">{{ data.qty }}</p>
                                    </td>
                                    <td>
                                        <p class="text-center">{{ data.price }}</p>
                                    </td>
                                    <td>
                                        <p class="text-center">{{ (data.qty * data.price).toFixed(2) }}</p>
                                    </td>
                                    <td>
                                        <p class="text-center">{{ data.status }}</p>
                                    </td>
                                </tr>
                                <tr v-if="viewServiceData.service_data.additional">
                                    <td colspan="3">
                                        <p class="text-end px-2 font-bold">Total</p>
                                    </td>
                                    <td>
                                        <p class="text-center font-bold">{{ calculateTotal.toFixed(2) }}</p>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr v-if="viewServiceData.service_data.estimateAmount">
                                    <td colspan="3">
                                        <p class="text-end px-2 font-bold">Estimated Service Amount</p>
                                    </td>
                                    <td>
                                        <p class="text-center font-bold">{{ viewServiceData.service_data.estimateAmount
                                            }}</p>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr v-if="viewServiceData.service_data.discountValue">
                                    <td colspan="3">
                                        <p class="text-end px-2 font-bold">Discount ({{
                                            viewServiceData.service_data.discountValue.type }})</p>
                                    </td>
                                    <td>
                                        <p class="text-center font-bold">
                                            {{ calculateDiscount }}
                                        </p>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr class="bg-gray-300">
                                    <td colspan="3">
                                        <p class="text-end px-2 font-bold">Grand Total</p>
                                    </td>
                                    <td>
                                        <p class="text-center font-bold">
                                            {{ finalAmount.toFixed(2) }}
                                        </p>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr v-if="viewServiceData.service_data.advanceAmount" class="bg-blue-100 text-blue-600">
                                    <td colspan="3">
                                        <p class="text-end px-2 font-bold">Advance Pay</p>
                                    </td>
                                    <td>
                                        <p class="text-center font-bold">{{ viewServiceData.service_data.advanceAmount
                                            }}</p>
                                    </td>
                                    <td></td>
                                </tr>
                                <tr v-if="viewServiceData.service_data.advanceAmount"
                                    :class="{ 'bg-red-100 text-red-600': finalPaymentStatus === 'Balance Pay', 'bg-green-100 text-green-600': finalPaymentStatus === 'Return Pay' }">
                                    <td colspan="3">
                                        <p class="text-end px-2 font-bold"> {{ finalPaymentStatus }}</p>
                                    </td>
                                    <td>
                                        <p class="text-center font-bold">
                                            {{ finalPaymentStatus === 'Return Pay' ? -1 * finalPaymentAmount.toFixed(2)
                                                : finalPaymentAmount.toFixed(2) }}
                                        </p>
                                    </td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <!-- Service Track History -->
                <div class="space-y-2 mt-3">
                    <h3 class="text-lg font-semibold">Service History </h3>
                    <ul class="list-disc pl-5 space-y-2">
                        <li v-for="(status, index) in viewServiceData.service_data.service_track" :key="index"
                            class="space-x-4">
                            <span class="font-semibold">{{ status.name }}:</span>
                            <span>{{ status.date ? status.date : 'N/A' }}</span>
                            <span class="text-green-600 font-bold">
                                {{ viewServiceData.status == index ? 'Active' : '' }}
                            </span>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="m-5">
                <!-- Buttons to Print or Download PDF -->
                <!-- <div class="mt-6 flex space-x-4">
                    <button @click="printServiceDetails"
                        class="px-4 py-2 bg-gray-500 text-white rounded-md">Print</button>
                    <button @click="downloadPDF" class="px-4 py-2 bg-blue-500 text-white rounded-md">Download
                        PDF</button>
                </div> -->
            </div>
        </div>
    </div>
</template>

<script>
import { jsPDF } from "jspdf";
export default {
    props: {
        isModalOpen: Boolean,
        serviceDetails: Object,
        category_id: String,
        companyId: String,
    },
    data() {
        return {
            isOpen: false,
            viewServiceData: {},
            is_searched: false,
        };
    },
    computed: {
        // Additional Material Total Calculation
        calculateTotal() {
            if (this.viewServiceData.service_data.additional && this.viewServiceData.service_data.additional.length > 0) {
                return this.viewServiceData.service_data.additional.reduce((total, item) => {
                    return total + (item.qty * item.price);
                }, 0);
            }
            return 0;
        },

        // Calculate Discount Amount
        calculateDiscount() {
            if (this.viewServiceData.service_data.discountValue) {
                if (this.viewServiceData.service_data.discountValue.type === 'Fixed') {
                    return (this.viewServiceData.service_data.discountValue.value).toFixed(2);
                } else if (this.viewServiceData.service_data.discountValue.type === 'Percentage') {
                    const discount = (this.calculateTotal || 0 + (this.viewServiceData.service_data.estimateAmount || 0)) * (this.viewServiceData.service_data.discountValue.value / 100);
                    return discount.toFixed(2);
                }
            }
            return 0;
        },

        // Calculate Final Amount After Discount
        finalAmount() {
            const total = this.calculateTotal || 0 + (this.viewServiceData.service_data.estimateAmount || 0);
            const discount = this.calculateDiscount || 0;
            return total - discount;
        },

        // Calculate Payment Status
        finalPaymentStatus() {
            const balance = this.finalAmount - (this.viewServiceData.service_data.advanceAmount || 0);
            return balance > 0 ? 'Balance Pay' : 'Return Pay';
        },

        // Final Payment Amount
        finalPaymentAmount() {
            return this.finalAmount - (this.viewServiceData.service_data.advanceAmount || 0);
        }
    },
    watch: {
        isModalOpen(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (this.serviceDetails && this.serviceDetails.id && !this.is_searched) {
                    this.serviceDataList(this.serviceDetails.id);
                }
            }, 100);
        },
    },
    methods: {
        // Function to close modal
        closeModal() {
            this.$emit('close');
        },
        // Print the service details
        printServiceDetails() {
            const printContents = document.querySelector(".model-data").innerHTML;
            const originalContents = document.body.innerHTML;
            document.body.innerHTML = printContents;
            window.print();
            document.body.innerHTML = originalContents;
        },
        // Download service details as a PDF
        // Download service details as a PDF
        downloadPDF() {
            const doc = new jsPDF();

            // Set font styles for title
            doc.setFont("helvetica", "normal");
            doc.setFontSize(12);
            doc.text("Service Details", 10, 10);

            // Customer Info Section
            doc.setFontSize(12);
            doc.setFont("helvetica", "bold");
            doc.text("Customer Details:", 10, 20);
            doc.setFont("helvetica", "normal");
            doc.text(`Name: ${this.viewServiceData.customer.first_name} ${this.viewServiceData.customer.last_name}`, 10, 30);
            doc.text(`Contact: ${this.viewServiceData.customer.contact_number}`, 10, 40);
            doc.text(`Address: ${this.viewServiceData.customer.address || 'N/A'}`, 10, 50);

            // Service Info Section
            doc.setFont("helvetica", "bold");
            doc.text("Service Details:", 10, 60);
            doc.setFont("helvetica", "normal");
            doc.text(`Service ID: ${this.viewServiceData.service_id}`, 10, 70);
            doc.text(`Service Code: ${this.viewServiceData.service_code}`, 10, 80);
            doc.text(`Created At: ${this.formattedDate(this.viewServiceData.created_at)}`, 10, 90);

            // Device Info Section
            doc.setFont("helvetica", "bold");
            doc.text("Device Details:", 10, 100);
            doc.setFont("helvetica", "normal");
            doc.text(`Service Type: ${this.viewServiceData.service_data.service_type || 'N/A'}`, 10, 110);
            doc.text(`Brand: ${this.viewServiceData.service_data.brand || 'N/A'}`, 10, 120);
            doc.text(`Model: ${this.viewServiceData.service_data.device_model || 'N/A'}`, 10, 130);
            if (this.viewServiceData.service_data.problem_title) {
                doc.text(`Problem Title: ${this.viewServiceData.service_data.problem_title.join(', ') || 'N/A'}`, 10, 140);
            }

            // Additional Material Table
            doc.setFont("helvetica", "bold");
            doc.text("Additional Material:", 10, 150);

            // Set up table structure
            const additionalData = this.viewServiceData.service_data.additional.map(item => [
                item.product_name, item.qty, item.price, (item.qty * item.price).toFixed(2), item.status
            ]);

            // Add table with headers
            doc.autoTable({
                startY: 155,
                head: [['Product Name', 'Qty', 'Price', 'Total', 'Status']],
                body: additionalData,
                theme: 'striped', // Adds zebra-striping effect to rows
                styles: { fontSize: 10 },
                columnStyles: {
                    0: { cellWidth: 40 }, // Product name column width
                    1: { cellWidth: 20 }, // Qty column width
                    2: { cellWidth: 20 }, // Price column width
                    3: { cellWidth: 20 }, // Total column width
                    4: { cellWidth: 30 }  // Status column width
                },
            });

            // Financial Summary Table
            const totalAmount = this.calculateTotal;
            const estimateAmount = this.viewServiceData.service_data.estimateAmount || 0;
            const discountAmount = this.calculateDiscount || 0;
            const grandTotal = this.finalAmount.toFixed(2);
            const advanceAmount = this.viewServiceData.service_data.advanceAmount || 0;
            const balancePay = this.finalPaymentAmount.toFixed(2);

            const financialData = [
                ['Total', totalAmount.toFixed(2)],
                ['Estimated Amount', estimateAmount.toFixed(2)],
                ['Discount', discountAmount.toFixed(2)],
                ['Grand Total', grandTotal],
                ['Advance Pay', advanceAmount.toFixed(2)],
                ['Balance Pay', balancePay]
            ];

            // Add financial summary table
            doc.setFont("helvetica", "bold");
            doc.text("Financial Summary:", 10, doc.lastAutoTable.finalY + 10);

            doc.autoTable({
                startY: doc.lastAutoTable.finalY + 15,
                head: [['Description', 'Amount']],
                body: financialData,
                theme: 'striped',
                styles: { fontSize: 10 },
                columnStyles: {
                    0: { cellWidth: 100 }, // Description column width
                    1: { cellWidth: 50 }   // Amount column width
                },
            });

            // Save the generated PDF
            doc.save('service-details.pdf');
        },
        //---formated display date---
        formattedDate(timestamp) {
            const date = new Date(timestamp);
            date.setHours(date.getHours() + 5); // Add 5 hours
            date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        serviceDataList(id) {
            axios.get(`/services/${id}`, { params: { company_id: this.companyId, category_id: this.category_id } })
                .then(response => {
                    let response_data = response.data.data;
                    let exist_data = JSON.parse(response_data.service_data);
                    if (exist_data.additional && exist_data.additional.length > 0) {
                        let material_list = JSON.parse(response_data.materials);
                        if (material_list && material_list.length > 0) {
                            exist_data.additional = material_list;
                            exist_data.assignWork = response_data.assign_to;
                            if (exist_data.notification) {
                                exist_data.notification = JSON.parse(response_data.notification);
                            }
                            if (response_data.document) {
                                exist_data.document = JSON.parse(response_data.document);
                            }
                        }
                    }
                    if (this.validateType(exist_data.customer) === 'Number' || response_data.customer) {
                        let { first_name, last_name, contact_number } = response_data.customer;
                        exist_data.customer = `${first_name} ${last_name} - ${contact_number}`;
                        exist_data.customer_id = response_data.customer.id;
                    }
                    this.viewServiceData = { ...response_data, service_data: exist_data };
                    this.is_searched = true;
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        },
        validateType(value) {
            if (!isNaN(value) && !isNaN(parseFloat(value))) {
                return 'Number';
            } else if (typeof value === 'string') {
                return 'String';
            } else {
                return 'Invalid';
            }
        }
    }
};
</script>

<style scoped>
/* TailwindCSS Styling for Modal */
th {
    @apply bg-gray-300;
}

th,
td {
    @apply border border-gray-300 p-1;
}
</style>
