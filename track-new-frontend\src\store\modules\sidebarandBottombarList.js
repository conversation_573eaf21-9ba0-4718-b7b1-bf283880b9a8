import { getBaseUrl } from "@/utils/baseUrl";
const state = {
  sidebarList: [
    { id: 1, label: 'Dashboard', imageSrc: "/images/side_bar/Layout.png", alt: 'dashboard', isHovered: false, isClicked: false, route: '/', premium: false },
    { id: 3, label: 'Services', imageSrc: '/images/side_bar/Clock.png', alt: 'services', isHovered: false, isClicked: false, route: '/services', premium: true },
    { id: 7, label: 'Categories', imageSrc: '/images/side_bar/Add.png', alt: 'categories', isHovered: false, isClicked: false, route: '/categories', premium: true },
    { id: 2, label: 'Leads', imageSrc: "/images/side_bar/Leads.png", alt: 'leads', isHovered: false, isClicked: false, route: '/leads', premium: true },
    { id: 11, label: 'AMC', imageSrc: "/images/side_bar/amc.png", alt: 'amc', isHovered: false, isClicked: false, route: '/amc', premium: true },
    { id: 14, label: 'RMA', imageSrc: "/images/side_bar/RMA.png", alt: 'openrma', isHovered: false, isClicked: false, route: '/openrma', premium: true },
    { id: 5, label: 'Sales / Invoice', imageSrc: '/images/side_bar/Sales.png', alt: 'sales', isHovered: false, isClicked: false, route: '/sales', premium: false },
    { id: 23, label: 'Payment In', imageSrc: '/images/side_bar/pages.png', alt: 'payment in', isHovered: false, isClicked: false, route: '/sales/invoice/paymentin', premium: false },
    { id: 15, label: 'Proforma', imageSrc: "/images/side_bar/proforma.png", alt: 'proforma', isHovered: false, isClicked: false, route: '/proforma', premium: false },
    { id: 10, label: 'Quote / Estimate', imageSrc: "/images/side_bar/quote.png", alt: 'quote', isHovered: false, isClicked: false, route: '/estimation', premium: false },
    { id: 6, label: 'Items', imageSrc: '/images/side_bar/Inventory.png', alt: 'inventory', isHovered: false, isClicked: false, route: '/items', premium: false },
    { id: 4, label: 'Customers', imageSrc: '/images/side_bar/User.png', alt: 'customers', isHovered: false, isClicked: false, route: '/customers', premium: false },
    { id: 12, label: 'Expense', imageSrc: '/images/side_bar/expense.png', alt: 'expense', isHovered: false, isClicked: false, route: '/expense', premium: false },
    { id: 9, label: 'Setting', imageSrc: '/images/side_bar/Settings.png', alt: 'setting', isHovered: false, isClicked: false, route: '/setting', premium: false },
    { id: 28, label: 'List', imageSrc: '/images/side_bar/Settings.png', alt: 'list', isHovered: false, isClicked: false, route: '/users', premium: false },
    // { id: 29, label: 'Team', imageSrc: '/images/side_bar/Settings.png', alt: 'team', isHovered: false, isClicked: false, route: '/users', premium: false },
    { id: 8, label: 'Reports', imageSrc: '/images/side_bar/Statistics.png', alt: 'reports', isHovered: false, isClicked: false, route: '/reports', premium: false },
    { id: 16, label: 'Pricing Plan', imageSrc: '/images/side_bar/subscription.png', alt: 'subscription', isHovered: false, isClicked: false, route: '/subscription', premium: false },
    { id: 27, label: 'Subscription', imageSrc: '/images/side_bar/subscription.png', alt: 'subscription', isHovered: false, isClicked: false, route: '/subscription/history', premium: false },
    { id: 17, label: 'Website', imageSrc: '/images/side_bar/pages.png', alt: 'website', isHovered: false, isClicked: false, route: '/websites/dashboard', premium: true },
    { id: 18, label: 'Purchase', imageSrc: '/images/side_bar/pages.png', alt: 'purchase', isHovered: false, isClicked: false, route: '/items/purchaseOrder', premium: false },
    { id: 19, label: 'Supplier', imageSrc: '/images/side_bar/pages.png', alt: 'supplier', isHovered: false, isClicked: false, route: '/items/purchaseOrder/supplier', premium: false },
    { id: 20, label: 'Warehouse', imageSrc: '/images/side_bar/pages.png', alt: 'warehouse', isHovered: false, isClicked: false, route: '/items/purchaseOrder/warehouse', premium: false },
    { id: 21, label: 'Payment Out', imageSrc: '/images/side_bar/pages.png', alt: 'payment out', isHovered: false, isClicked: false, route: '/items/purchaseOrder/paymentout', premium: false },
    { id: 22, label: 'Stock Adjust', imageSrc: '/images/side_bar/pages.png', alt: 'stock adjust', isHovered: false, isClicked: false, route: '/items/purchaseOrder/supplier/stock', premium: false },
    { id: 24, label: 'Website Enquiry', imageSrc: '/images/side_bar/pages.png', alt: 'website enquiry', isHovered: false, isClicked: false, route: '/websiteenquiry', premium: true },
    { id: 25, label: 'Ecommerce', imageSrc: '/images/side_bar/pages.png', alt: 'ecommerce', isHovered: false, isClicked: false, route: '/ecommerce', premium: false },
    { id: 26, label: 'WhatsApp Alert', imageSrc: '/images/side_bar/pages.png', alt: 'whatsapp', isHovered: false, isClicked: false, route: '/whatsapp', premium: true },
    { id: 13, label: 'Pages', imageSrc: '/images/side_bar/pages.png', alt: 'pages', isHovered: false, isClicked: false, route: '/pages', premium: false },
  ],
  bottombarList: [
    { id: 0, name: "home", image: "/images/side_bar/home.png", url: "/" },
    {
      id: 1,
      name: "services",
      image: "/images/side_bar/Clock.png",
      url: "/services",
    },
    { id: 2, name: "lead", image: "/images/side_bar/Leads.png", url: "/leads" },
    { id: 3, name: "amc", image: "/images/side_bar/amc.png", url: "/amc" },
    { id: 4, name: "rma", image: "/images/side_bar/RMA.png", url: "/openrma" },
    { id: 5, name: "sale", image: "/images/side_bar/Sales.png", url: "/sales" },
    {
      id: 6,
      name: "proforma",
      image: "/images/side_bar/proforma.png",
      url: "/proforma",
    },
    {
      id: 7,
      name: "estimate",
      image: "/images/side_bar/quote.png",
      url: "/estimation",
    },
    {
      id: 7,
      name: "expense",
      image: "/images/side_bar/expense.png",
      url: "/expense",
    },
    { id: 8, name: "menu", image: "/images/side_bar/Sales.png", url: "" },
  ],
  selected_bottom: "home",
  selected_sidebar: 1,
  bottombar_enable: true,
  is_login: true,
};

const mutations = {
  setSidebarAndBottomSelection(state, { sidebarId, bottomName }) {
    state.selected_sidebar = sidebarId;
    state.selected_bottom = bottomName;
  },
  setBootmbarupdate(state, { isEnable }) {
    state.bottombar_enable = isEnable;
  },
  setIsLogin(state, { isLogin }) {
    state.is_login = isLogin;
  },
};

const actions = {
  updateSelectionBasedOnPath({ commit, state }, path) {
    const split_data = path.split("/");
    const firstSegment = split_data[1];
    const lastSegment = split_data[split_data.length - 1];

    // Find matching sidebar and bottombar items
    const sidebarItem = state.sidebarList.find(
      (item) => item.route.split("/")[1] === firstSegment
    );
    const bottombarItem = state.bottombarList.find(
      (item) => item.url.split("/")[1] === firstSegment
    );
    const sidebarItemLast = state.sidebarList.find(
      (item) => item.route.split("/").pop() === lastSegment
    );

    // Determine sidebarId based on conditions
    let sidebarId;
    if (sidebarItem) {
      if (firstSegment === "items") {
        sidebarId =
          lastSegment === "form"
            ? 18
            : sidebarItemLast
            ? sidebarItemLast.id
            : sidebarItem.id;
      } else if (firstSegment === "sales") {
        sidebarId = sidebarItemLast ? sidebarItemLast.id : sidebarItem.id;
      }  else if (firstSegment === "subscription") {
        sidebarId = sidebarItemLast ? sidebarItemLast.id : sidebarItem.id;
      }else {
        sidebarId = sidebarItem.id;
      }
    } else {
      sidebarId = state.selected_sidebar; // Fallback to the current selection
    }

    // Determine bottomName based on conditions
    let bottomName;
    if (bottombarItem) {
      bottomName = bottombarItem.name;
    } else {
      bottomName =
        firstSegment === "categories"
          ? "categories"
          : firstSegment === "items"
          ? "items"
          : "menu";
    }

    // Commit the mutation with determined sidebarId and bottomName
    commit("setSidebarAndBottomSelection", {
      sidebarId,
      bottomName,
    });

    // Determine login state
    if (["login", "mobilelogin", "signup", "websites"].includes(firstSegment)) {
      commit("setIsLogin", { isLogin: false });
    } else {
      const { company_id } =
        JSON.parse(localStorage.getItem("track_new")) || {};
      commit("setIsLogin", { isLogin: !!company_id });
    }
  },
  updateIsEnableBottom({ commit, state }, isEnable) {
    commit("setBootmbarupdate", { isEnable: isEnable });
  },
};

const getters = {
  bottomSelected: (state) => state.selected_bottom,
  sidebarselected: (state) => state.selected_sidebar,
  isEnableBottom: (state) => state.bottombar_enable,
  isLoginEnable: (state) => state.is_login,
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
