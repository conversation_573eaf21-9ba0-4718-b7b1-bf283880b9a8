// store/modules/paymentInList.js
import axios from "axios";

const state = {
  payment_list: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  previousPerPage: null,
  customerId: null,
  };

  const mutations = {
      SET_PAYMENTINLIST(state, { data, pagination}) {       
          state.payment_list = {data: data, pagination: pagination};
    },
      RESET_STATE(state) {
          state.payment_list = {};
          state.lastFetchTime = null;
        state.isFetching = false;
        state.previousPerPage = null;
        state.customerId = null;
      },
      SET_LAST_FETCH_TIME(state, time) {
        state.lastFetchTime = time; // Save the timestamp when the API was last accessed
      },
      SET_IS_FETCHING(state, status) {
        state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    SET_PREVIOUS_PER_PAGE(state, {per_page, customer_id}) { 
      state.previousPerPage = per_page; // Store the previous per_page value      
      state.customerId = customer_id;
    },
  };

  const actions = {
    updatePaymentName({ commit }, payment_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update payment_list name
      setTimeout(() => {
        // Commit mutation to update payment_list name
        commit('SET_PAYMENTINLIST', payment_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchPaymentInList({ commit, state, rootState }, { page, per_page, customer_id, is_delete }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['sales_update']; 
      
      if (state.previousPerPage != per_page || ((state.customerId || customer_id) && state.customerId !== customer_id)) {
        commit('SET_PREVIOUS_PER_PAGE', {per_page, customer_id});  
          commit('SET_LAST_FETCH_TIME', null);     
 
      }          
     
      //If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (!is_delete && (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)){
        return; // Skip request if less than 30 seconds have passed since the last request
      }
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          commit('SET_IS_FETCHING', true);
          let send_data = { company_id: company_id, page: customer_id ? 1 : page, per_page: customer_id ? 2000 : per_page };
          axios.get('/sales_payments', { params:{...send_data}})
            .then(response => {
              // Handle response
              // console.log(response.data, 'Payment list..!');
              commit('SET_LAST_FETCH_TIME', now);
              commit('SET_IS_FETCHING', false);
              if (customer_id) {
                let { data, pagination } = response.data;
                const data_filter = data.filter(opt => opt.customer.id === customer_id);
                const data_page = { ...pagination, total: data_filter.length, current_page: 1, last_page: 1 };
                commit('SET_PAYMENTINLIST', {data:data_filter , pagination: data_page});
              } else {
                let { data, pagination } = response.data;
                commit('SET_PAYMENTINLIST', {data, pagination});
              return data;
              }          
              
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },    
  };

  const getters = {
    currentPaymentInList(state) {
      return state.payment_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
