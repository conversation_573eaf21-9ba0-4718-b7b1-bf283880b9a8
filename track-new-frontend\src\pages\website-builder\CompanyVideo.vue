<template>
    <div class="builder-page">
        <div class="flex flex-col md:flex-row justify-between items-center">
            <h1 class="web-head">Company Video Links</h1>
            <div class="w-full md:w-auto md:self-end">
                <EnablePageName :pages="is_onSetting" @updatePagesEnabled="handleToggle"></EnablePageName>
            </div>
        </div>

        <!-- Form for adding new video link -->
        <div class="flex items-center justify-center my-3 sm:my-5">
            <form @submit.prevent="handleSubmit"
                class="w-full max-w-sm sm:max-w-md md:max-w-lg lg:max-w-xl xl:max-w-2xl p-4 bg-white shadow-md rounded-md">
                <!-- Title Input -->
                <div class="mb-4">
                    <label class="block font-bold mb-1">Video Title
                        <span class="text-red-500">*</span>
                    </label>
                    <input v-model="newVideoTitle" type="text" placeholder="Enter video title"
                        class="border p-2 rounded-md w-full" />
                </div>
                <!-- Video Link Input -->
                <div class="mb-4">
                    <label class="block font-bold mb-1">Video Link
                        <span class="text-red-500">*</span>
                    </label>
                    <input v-model="newVideoLink" type="text" placeholder="Enter video link"
                        class="border p-2 rounded-md w-full" />
                </div>
                <!-- Submit Button -->
                <div class="flex justify-center">
                    <button type="submit" class="bg-blue-500 hover:bg-blue-600 text-white p-2 rounded-md sm:w-32">
                        {{ editIndex >= 0 && editIndex !== null ? 'Update' : 'Add' }} Link
                    </button>
                </div>
            </form>
        </div>

        <!-- Video Links List -->
        <div v-if="videoLinks.length > 0">
            <ul class="bg-white shadow-md rounded-md  p-4 border border-gray-200">
                <li v-for="(video, index) in videoLinks" :key="video.id" class="flex items-center justify-between mb-2"
                    :draggable="true" @dragstart="onDragStart(index, $event)" @dragover.prevent
                    @drop="onDrop(index, $event)" @dragenter="onDragEnter(index, $event)" @dragend="onDragEnd($event)">
                    <div class="flex">
                        <span class="pr-1">{{ index + 1 }}. </span>
                        <a :href="video.link" target="_blank" class="cursor-pointer hover:text-blue-700 block">
                            {{ video.title ? video.title : video.link }}
                        </a>
                    </div>

                    <div class="flex items-center justify-end space-x-2 text-xs mt-1">
                        <!-- Edit Button -->
                        <button @click="viewVideoLink(video)"
                            class="bg-black hover:bg-gray-600 text-white p-1 px-2 rounded-md">
                            <font-awesome-icon icon="fa-solid fa-eye" /> View
                        </button>
                        <!-- Edit Button -->
                        <button @click="editVideoLink(video.id, index)"
                            class="bg-blue-500 hover:bg-blue-600 text-white p-1 px-2 rounded-md">
                            <font-awesome-icon icon="fa-solid fa-pencil" /> Edit
                        </button>

                        <!-- Delete Button -->
                        <button @click="deleteVideoLink(index)"
                            class="bg-red-500 hover:bg-red-600 text-white p-1 px-2 rounded-md">
                            <font-awesome-icon icon="fa-solid fa-trash-can" /> Delete
                        </button>
                    </div>
                </li>
            </ul>
        </div>
        <div v-else>
            <p>No video links available.</p>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    </div>
</template>

<script>
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import EnablePageName from './EnablePageName.vue';
export default {
    name: "CompanyVideo",
    components: {
        confirmbox,
        EnablePageName
    },
    props: {
        companyVideoData: {
            type: Object,
            required: true,
        },
        is_updated: {
            type: Boolean,
            required: true,
        },
        pages: {
            type: Object,
            required: true
        },
    },
    data() {
        return {
            newVideoLink: "", // Store the user input for the new video link
            newVideoTitle: "", // Store the user input for the new video title
            videoLinks: [], // Array of video link objects
            editIndex: null,
            open_confirmBox: false,
            deleteIndex: null,
            //----is on settings---
            is_onSetting: { is_on: true, name: 'Corporate Video' },
            draggedIndex: null, // Index of the dragged video
            currentDraggedElement: null, // The element being dragged
        };
    },
    mounted() {
        if (this.companyVideoData && this.companyVideoData.length > 0) {
            this.videoLinks = [...this.companyVideoData];
        }
        if (this.pages && this.pages.company_video !== undefined) {
            this.is_onSetting = this.pages.company_video;
        }
    },
    methods: {
        // Handle form submission to add a new video link
        handleSubmit() {
            if (this.newVideoLink.trim() === "" || this.newVideoTitle.trim() === "") {
                this.toasterMsg('Please fill all required fields', 'warning');
                return;
            }
            // Check if it's an update or a new entry
            if (this.editIndex >= 0 && this.videoLinks[this.editIndex]) {
                this.videoLinks[this.editIndex].link = this.newVideoLink.trim();
                this.videoLinks[this.editIndex].title = this.newVideoTitle.trim();
                this.newVideoLink = "";
                this.newVideoTitle = "";
                this.editIndex = null;
                this.toasterMsg('Video link updated successfully', 'success');
            } else {
                const newLink = {
                    id: Date.now(), // Use current timestamp as a unique ID
                    link: this.newVideoLink.trim(),
                    title: this.newVideoTitle.trim() || '', // Add empty string if title is not provided
                };
                this.videoLinks.push(newLink); // Add the new link to the list
                this.newVideoLink = ""; // Clear the input fields
                this.newVideoTitle = "";
                this.toasterMsg('Video link added successfully', 'success');
            }
            this.$emit('updatecompanyVideo', this.videoLinks);
            this.$emit('submitData');
        },
        viewVideoLink(data) {
            if (data && data.link && data.link !== '') {
                // Example: Open the video link in a new tab
                window.open(data.link, '_blank');
            }
        },
        // Handle editing a video link
        editVideoLink(id, index) {
            this.newVideoLink = this.videoLinks[index].link;
            this.newVideoTitle = this.videoLinks[index].title || ''; // Pre-fill title field if it exists
            this.editIndex = index;
        },
        // Handle deleting a video link
        deleteVideoLink(index) {
            this.open_confirmBox = true;
            this.deleteIndex = index;
        },
        deleteRecord() {
            this.$emit('updateLoader', true);
            if (this.deleteIndex >= 0) {
                this.videoLinks.splice(this.deleteIndex, 1); // Remove the link at the specified index
                this.$emit('updatecompanyVideo', this.videoLinks);
                this.closeconfirmBoxData();
                this.toasterMsg('Video link deleted successfully', 'success');
            } else {
                this.closeconfirmBoxData();
                this.toasterMsg('Video link cannot be deleted', 'warning');
            }
        },
        cancelDelete() {
            this.closeconfirmBoxData();
        },
        closeconfirmBoxData() {
            this.deleteIndex = null;
            this.$emit('updateLoader', false);
            this.open_confirmBox = false;
        },
        toasterMsg(msg, type) {
            this.$emit('toasterMessages', { msg: msg, type: type });
        },
        //--handle switch--
        handleToggle(data) {
            // Perform any additional actions when toggled
            if (data) {
                this.is_onSetting = data;
            }
        },
        // Method triggered when dragging starts
        onDragStart(index, event) {
            this.draggedIndex = index;
            this.currentDraggedElement = event.target;
            this.currentDraggedElement.classList.add('dragging'); // Add visual feedback (optional)
        },

        // Method triggered when the dragged element enters another item
        onDragEnter(index, event) {
            event.preventDefault();
            const targetElement = event.target;
            if (this.draggedIndex !== index) {
                targetElement.classList.add('drag-over'); // Highlight the target item
            }
        },

        // Method triggered when the dragged element is dropped
        onDrop(targetIndex, event) {
            event.preventDefault();
            if (this.draggedIndex !== targetIndex) {
                const movedItem = this.videoLinks.splice(this.draggedIndex, 1)[0];
                this.videoLinks.splice(targetIndex, 0, movedItem); // Move the dragged item to the target position
            }
            this.resetDragState(event);
        },

        // Method triggered when dragging ends
        onDragEnd(event) {
            this.resetDragState(event);
        },

        // Reset the drag state and clean up visual feedback
        resetDragState(event) {
            if (this.currentDraggedElement) {
                this.currentDraggedElement.classList.remove('dragging');
            }
            const targetElement = event.target;
            targetElement.classList.remove('drag-over'); // Remove the drag-over class
        },

    },
    watch: {
        is_updated: {
            deep: true,
            handler(newValue) {
                this.$emit('updatecompanyVideo', this.videoLinks);
                this.$emit('updatePagesSetting', { company_video: this.is_onSetting });
            }
        },
        videoLinks: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.$emit('updatecompanyVideo', this.videoLinks);
                }
            }
        },
        is_onSetting: {
            deep: true,
            handler(newValue) {
                this.$emit('updatePagesSetting', { company_video: newValue });
            }
        }
    }
};
</script>

<style scoped></style>
