<template>
    <div :class="{ 'manualStyle text-sm sm:mb-[70px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        class="custom-scrollbar-hidden" ref="scrollContainer" @scroll="handleScroll">
        <!---Create new Lead -->
        <div v-if="!open_skeleton && leadData && leadData.length === 0 && followup_select === null && status_select === null && Object.keys(searchByCustomer) === 0 && Object.keys(filteredBy) === 0"
            class="flex justify-center items-center">
            <button @click="openLeadModal"
                class="text-green-700 border rounded-full px-3 py-1 hover:border-green-700 shadow-lg">Create your first
                Lead</button>
        </div>

        <!--new design header-->
        <div v-if="leadData && (leadData.length > 0 || followup_select !== null || status_select !== null || Object.keys(searchByCustomer) !== 0 || Object.keys(filteredBy) !== 0)"
            class="mb-1" :class="{ 'my-custom-margin': !isMobile }">
            <!---status view-->
            <!-- Render content for each number (1 to 5) 'rounded-tl-full rounded-bl-full': index === 0, -->
            <div class="custom-scrollbar-hidden px-1"
                :class="{ 'flex overflow-auto bg-white px-2': isMobile, 'grid sm:grid-cols-4 lg:grid-cols-7 gap-2 mt-4': !isMobile }">
                <div class="px-2 cursor-pointer hover:text-blue-700"
                    :class="{ 'border-b-4 border-blue-800 text-blue-800 bg-blue-200': status_select === 'pending', 'bg-yellow-200': status_select !== 'pending', 'shadow border rounded': !isMobile }"
                    @click="getStatusLabel('pending', 1)">
                    <div class="p-1">
                        <div class="p-1 flex justify-between items-center rounded rounded-full px-2">
                            <span class="pr-1"><font-awesome-icon icon="fa-solid fa-hourglass-half"
                                    :style="{ color: status_select === 'pending' ? 'rgb(30 64 175)' : 'gray' }"
                                    size="lg" /></span>
                            <p>Pending</p>
                            <p class="ml-2">({{ getStatusOption[6].total }})</p>
                        </div>
                    </div>
                </div>
                <div class="px-2 cursor-pointer hover:text-blue-700"
                    :class="{ 'border-b-4 border-blue-800 text-blue-800 bg-blue-200': status_select === 5, 'bg-[#E1B9C9]': status_select !== 5, 'shadow border rounded': !isMobile }"
                    @click="getStatusLabel(5, 1)">
                    <div class="p-1">
                        <div class="p-1 flex justify-between items-center rounded rounded-full">
                            <span class="pr-1"><font-awesome-icon icon="fa-solid fa-circle-check"
                                    :style="{ color: status_select === 5 ? 'rgb(30 64 175)' : 'gray' }"
                                    size="lg" /></span>
                            <p>All</p>
                            <p class="ml-2">({{ getStatusOption[5].total }})</p>
                        </div>
                    </div>
                </div>
                <div v-for="(opt, index) in getStatusOption" :key="index"
                    class="px-2 cursor-pointer hover:text-blue-700 rounded" @click="getStatusLabel(index, 1)"
                    :class="{ 'border-b-4 border-blue-800 text-blue-800 bg-blue-200': status_select === index, 'bg-white': status_select !== index, 'hidden': index >= 5, 'ml-1': isMobile, 'shadow border rounded hover:text-blue-700': !isMobile }">
                    <div class="p-2 flex justify-between items-center" style="white-space: nowrap;">
                        <span class="pr-1"><font-awesome-icon :icon="opt.icon"
                                :style="{ color: status_select === index ? 'rgb(30 64 175)' : 'gray' }"
                                size="lg" /></span>
                        <p class="inline" style="display: inline;">{{ index === 0 ? 'Open' : index === 1 ?
                            'Inprogress'
                            : index === 2 ? 'Completed' : index ===
                                3 ? 'Cancelled' : index === 4 ? 'Hold' : 'All' }}</p>
                        <p class="ml-2">({{ opt.total }})</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="my-custom-margin">
            <!--table options-->
            <div v-if="!isMobile" class="flex  justify-between m-1 mt-5 ">
                <div class="flex mr-2 space-x-4">
                    <button @click="openLeadModal" :class="{ 'mr-2': isMobile }"
                        class="bg-green-600 border border-green-600 shadow-inner shadow-green-100  px-3 rounded p-1 text-white hover:bg-green-700 text-sm text-center">
                        <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
                        <span v-if="!isMobile" class="text-center">New Lead</span>
                    </button>
                    <!--view design-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="info-msg px-2 py-1 rounded-lg  border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-100"
                            @click="toggleView" :title02="'Table view'"
                            :class="{ 'bg-white': items_category === 'tile' }">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200 info-msg"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="'Card view'">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <!----Filter Model-->
                <!--Setting-->
                <div class="flex icon-color">
                    <!-- Main Filter Button -->
                    <div ref="dropdownContainerFilter" class="ml-5 relative">
                        <button @click="toggleMainDropdown" :disabled="leadData.length == 0"
                            class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                            :class="{ 'cursor-not-allowed': leadData.length == 0 }">
                            <span class="inline-flex items-center w-full pointer-events-none">
                                <font-awesome-icon icon="fa-solid fa-filter" class="px-1" /> Filter
                                <font-awesome-icon v-if="!isMainDropdownOpen" icon="fa-solid fa-angle-down"
                                    class="pl-3" />
                                <font-awesome-icon v-if="isMainDropdownOpen" icon="fa-solid fa-angle-up" class="pl-3" />
                            </span>
                        </button>
                        <!-- Main Dropdown -->
                        <div v-if="isMainDropdownOpen" ref="mainDropdown"
                            class="absolute mt-2 w-56 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border right-0">
                            <div class="py-1">
                                <!-- By Due Option with Sub-Dropdown -->
                                <div class="relative">
                                    <button @click="toggleSubDropdown" @mouseenter="toggleSubDropdown"
                                        @mouseleave="toggleSubDropdown"
                                        class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200">By
                                        Due</button>

                                    <!-- Sub-Dropdown for By Due -->
                                    <div v-if="showSubDropdown" @mouseenter="toggleMouserHoverSub"
                                        @mouseleave="toggleMouserHoverSub" ref="subDropdown"
                                        class="absolute right-full top-0 mr-2 w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none border">
                                        <ul class="py-1 text-left">
                                            <li v-for="(option, index) in followupOptions" :key="index"
                                                class="cursor-pointer py-2 px-5 hover:bg-blue-200"
                                                :class="{ 'bg-blue-200 text-blue-700': followup_select === option.value }"
                                                @click="handleFollowup(option.value, 1)">
                                                {{ option.label }}
                                            </li>
                                        </ul>
                                    </div>
                                </div>

                                <!-- Other Options -->
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('by Date')">By Date</button>
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('customer')">By Customer</button>
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('employee')">By Assigned to</button>
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('category')">By Category / Type</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!---fiter information !isMobile && -->
            <div v-if="(Object.keys(filteredBy).length > 0 || Object.keys(searchByCustomer).length > 0 || (status_select !== null && status_select !== 5) || (followup_select && followup_select !== 'all'))"
                class="text-xs flex -mb-3 flex-row overflow-auto m-1">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-if="Object.keys(filteredBy).length > 0" v-for="(value, key) in filteredBy" :key="key"
                    class="flex flex-row text-blue-600 mr-2">
                    <p v-if="key !== 'customer_id'" class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                    <p v-if="key !== 'customer_id'">{{key === 'assign_to' ? value.name : key === 'type' ?
                        leadType.find(opt => opt.id === value).name : key === 'status' ?
                            value === 0 ? 'Open' : value === 1 ? 'Progress' : value === 2 ? 'Completed' : value === 3 ?
                                'Cancelled' : value === 4 ? 'Hold' : '' : value}}</p>
                </div>
                <div v-if="Object.keys(searchByCustomer).length > 0" class="text-blue-600 mr-2">Search By Customer = {{
                    searchByCustomer.first_name + '' + (searchByCustomer.last_name ? searchByCustomer.last_name : '') +
                    ' - ' +
                    searchByCustomer.contact_number }}</div>
                <div v-if="status_select !== null && status_select !== 5" class="text-blue-600 mr-2">
                    Status = {{ status_select === 0 ? 'Open' : status_select === 1 ? 'Progress' : status_select === 2 ?
                        'Completed' : status_select === 3 ? 'Cancelled' : status_select === 4 ? 'Hold' :
                            status_select === 'pending' ? 'Pending' : 'All' }}</div>
                <div v-if="followup_select && followup_select !== 'all'" class="text-blue-600 mr-2"> FollowUp =
                    {{ followup_select }}</div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton && leadData && leadData.length > 0" class="text-sm mt-5"
                :class="{ 'm-1': !isMobile }">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                        <!--search bar--->
                        <div>
                            <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                @resetData="resetToSearch" :resetSearch="resetSearchData">
                            </searchCustomer>
                        </div>
                    </div>
                    <!--Table view-->
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label === 'Lead Date' ? 'Date' : column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="dropdownSetting"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in leadData" :key="index"
                                    class="hover:bg-gray-200 cursor-pointer">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex"
                                        :class="{ 'hidden': !column.visible }" class="px-1 py-2 table-border"
                                        @click="startEdit(record)">
                                        <span
                                            v-if="!Array.isArray(record[column.field]) && column.field !== 'title' && column.field !== 'lead_type' && column.field !== 'lead_status' && column.field !== 'assign_date' && column.field !== 'lead_date' && column.field !== 'follow_up' && column.field !== 'customer' && column.field !== 'assign_to'">
                                            <template
                                                v-if="column.field !== 'created_by' && column.field !== 'updated_by'">
                                                {{ record[column.field] }}
                                            </template>
                                            <template
                                                v-if="column.field === 'created_by' || column.field === 'updated_by'">
                                                {{ record[column.field].name ? record[column.field].name : '' }}
                                            </template>
                                        </span>
                                        <span v-if="column.field === 'title'"
                                            class="hover:text-blue-500 text-violet-700">{{
                                                record[column.field] }}</span>
                                        <span v-if="column.field === 'lead_type'">{{ record[column.field].name }}</span>
                                        <span v-if="column.field === 'lead_status'"
                                            class="px-1 text-white py-1 rounded text-xs"
                                            :class="{ 'bg-yellow-500': record['lead_status'] == '0', 'bg-[#0D7CBF]': record['lead_status'] == '1', 'bg-green-500': record['lead_status'] == '2', 'bg-[#DA1C1C]': record['lead_status'] == '3', 'bg-[#8D9689]': record['lead_status'] == '4' }">
                                            {{ record[column.field] == '0' ? 'Open' : record[column.field] == '1' ?
                                                'Progress' :
                                                record[column.field] == '2' ? 'Completed' : record[column.field] == '3' ?
                                                    'Cancelled' : record[column.field] == '4' ? 'Hold' : '' }}</span>
                                        <span v-if="column.field === 'assign_date' || column.field === 'lead_date'"
                                            :title="calculateDaysAgo(formattedDate(record[column.field]))"
                                            class="text-blue-800 text-xs">
                                            {{ generateDate(formattedDate(record[column.field])) }}</span>
                                        <!-- <span
                                            v-if="Array.isArray(record[column.field]) && column.field !== 'follow_up'">
                                            {{record[column.field].map((opt) => opt.name).join(', ')}}
                                        </span> -->
                                        <span v-if="column.field === 'customer'" class="hover:text-sky-700"
                                            @mouseover="showModal(record[column.field], $event)"
                                            @mouseleave="hideModal">
                                            {{ record[column.field].first_name + ' ' + record[column.field].last_name +
                                                ' - ' + record[column.field].contact_number }}</span>
                                        <span @click="startEdit(record)" class="hover:text-blue-500 text-xs"
                                            v-if="column.field === 'follow_up' && (record['lead_status'] === 0 || record['lead_status'] === 1)"
                                            :class="getDateStatusClass(record[column.field])">
                                            {{ record['lead_status'] === 0 || record['lead_status'] === 1 ?
                                                getFollowUpDate(record[column.field]) : record['lead_status'] === 2 ?
                                                    'Lead Completed' : record['lead_status'] === 3 ? 'Lead Cancelled' :
                                                        'Lead Holded' }}
                                        </span>
                                        <span
                                            v-if="column.field === 'follow_up' && record['lead_status'] !== 0 && record['lead_status'] !== 1"
                                            :class="{ 'text-green-700': record['lead_status'] === 2, 'text-red-700': record['lead_status'] === 3, 'text-gray-500': record['lead_status'] === 4 }">
                                            {{ record['lead_status'] === 0 || record['lead_status'] === 1 ?
                                                getFollowUpDate(record[column.field]) : record['lead_status'] === 2 ?
                                                    'Lead Completed' : record['lead_status'] === 3 ? 'Lead Cancelled' :
                                                        'Lead Holded' }}
                                        </span>
                                        <span
                                            v-if="column.field === 'assign_to' && Array.isArray(record[column.field]) && record[column.field].length > 0"
                                            v-for="(opt, i) in record[column.field]" :key="i">
                                            <span class="flex flex-wrap gap-1" :class="{ 'mt-1': i > 0 }">
                                                <span
                                                    class="flex items-center px-1 py-0.5 rounded-full text-white text-xs"
                                                    :style="{ color: getUserColor(i), border: `1px solid ${getUserColor(i)}` }">
                                                    <font-awesome-icon icon="fa-solid fa-circle-user" class="pr-1" />
                                                    <span class="text-nowrap">{{ opt.name }}</span>
                                                </span>
                                            </span>
                                        </span>
                                    </td>
                                    <td class="py-2 table-border">
                                        <div class="flex justify-center">
                                            <div class="flex relative">
                                                <div class="flex">
                                                    <button v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                        @click="startEdit(record)" class="px-1" title="Edit">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" />
                                                    </button>
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)" title="Delete"
                                                        class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                    </button>
                                                </div>
                                                <!-- <button @click.stop="displayAction(index)" class="px-1 py-1">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                        class="px-2 py-1 rounded"
                                                        :class="{ 'bg-blue-100': display_option === index }" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-7 absolute border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class=" hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class=" hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border"
                                    v-if="!leadData || leadData.length === 0">
                                    <td>
                                        <button
                                            class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="openLeadModal">
                                            + Lead
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- Hover Modal -->
                        <customerDataTable v-if="isModalVisible" :showModal="isModalVisible" :modalData="modalData"
                            :modalPosition="modalPosition" @mouseover="toggleHoverModal(true)"
                            @mouseleave="toggleHoverModal(false)" @mousemove="showModal(null, $event)">
                        </customerDataTable>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4 custom-scrollbar-hidden">
                        <div v-for="(record, index) in leadData" :key="index" class="w-full">
                            <div
                                class="bg-white rounded-lg border border-gray-200 overflow-hidden relative w-full shadow-lg">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center p-2">
                                    <!-- Left Side -->

                                    <div class="text-xs text-red-400 cursor-pointer"
                                        :title="generateDate(formattedDate(record['lead_date']))">
                                        <p>{{ calculateDaysAgo(formattedDate(record['lead_date'])) }}</p>
                                    </div>
                                    <div class="text-xs px-3 py-1 rounded flex items-center cursor-pointer"
                                        @click="startEdit(record)" v-if="record['lead_status'] >= 0"
                                        :class="{ 'bg-yellow-100 text-yellow-800': record['lead_status'] == '0', 'bg-blue-200 text-blue-900': record['lead_status'] == '1', 'bg-green-200 text-green-900': record['lead_status'] == '2', 'text-red-800 bg-red-100': record['lead_status'] == '3', 'text-gray-800 bg-gray-100': record['lead_status'] == '4' }">
                                        <span>
                                            {{ record['lead_status'] == '0' ? 'Open' : record['lead_status'] == '1' ?
                                                'Progress'
                                                :
                                                record['lead_status'] == '2' ? 'Completed' : record['lead_status'] == '3' ?
                                                    'Cancelled' :
                                                    record['lead_status'] == '4' ? 'Hold' : '' }}</span>
                                    </div>
                                    <!-- Right Side -->
                                    <div class="flex items-center px-2">
                                        <span
                                            class="bg-gray-100 text-gray-600 text-xs font-semibold px-2 py-1 rounded">{{
                                                record['lead_type']
                                                    ? record['lead_type'].name : '' }}</span>
                                        <!--menu-->
                                        <div class="ml-2 rounded relative">
                                            <button @click.stop="displayAction(index)">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                    class="bg-blue-100 text-blue-800 px-2 py-1 rounded font-semibold " />
                                            </button>
                                            <!--dropdown-->
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 text-sm absolute mt-3 right-0 border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2 ">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Customer Details and Lead Status -->
                                <div class="text-gray-900 px-2">
                                    <div class="flex items-center mb-1">
                                        <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                            :class="{ 'bg-gray-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-blue-600': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                            {{ record.customer && record.customer.first_name ?
                                                record.customer.first_name[0].toUpperCase() :
                                                'C' }}
                                        </div>
                                        <div>
                                            <h4 class="text-sm leading-6 font-semibold text-gray-900 cursor-pointer"
                                                @click="startEdit(record)">{{
                                                    record.customer && record.customer.first_name ?
                                                        record.customer.first_name
                                                        + ' ' + (record.customer.last_name ? record.customer.last_name : '') :
                                                        '' }}
                                            </h4>
                                            <p class="text-sm text-gray-500 cursor-pointer"
                                                @click="dialPhoneNumber(record['customer'].contact_number)">+91
                                                - {{
                                                    record.customer.contact_number }}</p>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-3 gap-1">
                                        <div class="col-span-2 border-l-4 border-r-4 px-1">
                                            <h3 class="text-sm font-semibold cursor-pointer line-clamp-1"
                                                @click="startEdit(record)">{{
                                                    record['title'] }}</h3>
                                            <p class="text-sm line-clamp-1"><span>{{ record['description'] ?
                                                record['description']

                                                : '-' + ' ' + '-' + ' ' + '-'
                                                    }}</span></p>
                                        </div>
                                        <div class="block text-xs">
                                            <h4 class="font-bold text-gray-800">Next Follow Date:</h4>
                                            <p class="flex items-center"
                                                v-if="Array.isArray(record['follow_up']) && record['follow_up'].length > 0 && (record['lead_status'] === 0 || record['lead_status'] === 1)">
                                                <span @click="startEdit(record)"
                                                    class="hover:text-blue-500 cursor-pointer"
                                                    v-if="record['lead_status'] === 0 || record['lead_status'] === 1"
                                                    :class="getDateStatusClass(record['follow_up'])">
                                                    {{ record['lead_status'] === 0 || record['lead_status'] === 1 ?
                                                        getFollowUpDate(record['follow_up']) : record['lead_status'] === 2 ?
                                                            'Lead Completed' : record['lead_status'] === 3 ? 'Lead Cancelled' :
                                                                'Lead Holded' }}
                                                </span>
                                            </p>
                                            <p v-else class="flex items-center">{{ '-' + ' ' + '-' + ' ' + '-' }}</p>
                                        </div>
                                    </div>
                                </div>

                                <!-- Assign To -->
                                <div class="px-2 py-2 cursor-pointer" @click="startEdit(record)">
                                    <div class="relative">
                                        <!-- First Circle -->
                                        <div class="absolute  left-1 z-[1]">
                                            <i class="fas fa-user-circle text-blue-300 text-lg"></i>

                                        </div>
                                        <!-- Second Circle -->
                                        <div class="absolute  left-4 z-[2]">
                                            <i class="fas fa-user-circle text-red-300 text-lg"></i>
                                        </div>
                                        <!-- Third Circle -->
                                        <div class="absolute  left-7 z-[3]">
                                            <i class="fas fa-user-circle text-purple-300 text-lg"></i>
                                        </div>
                                    </div>

                                    <div class="flex flex-wrap gap-2 ml-[50px]">
                                        <p v-if="Array.isArray(record['assign_to']) && record['assign_to'].length > 0"
                                            v-for="(opt, i) in record['assign_to']" :key="i">
                                            <span v-if="record['assign_to'].length > 0"
                                                class="inline-flex items-center px-3 py-1 rounded mr-2"
                                                :class="{ 'bg-blue-100 text-blue-800': i === 0 || i % 3 === 0, 'bg-blue-100 text-blue-300': i % 2 === 0, 'bg-green-100 text-green-800': (i === 1 || i % 1 === 0) && i % 2 !== 0 && i % 3 !== 0 }">
                                                {{ opt.name }}
                                            </span>
                                        </p>
                                        <p v-else class="inline-flex items-center px-1 py-2">- -</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && pagination.last_page > 0 && pagination.last_page === pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>

                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="!isMobile && pagination">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>
                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none overflow-auto">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px] flex">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span></button>
                                </li>
                                <!-- Page numbers -->
                                <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">
                                        {{ pageNumber }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === this.pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== this.pagination.last_page }">
                                    <button @click="updatePage(currentPage + 1)"
                                        :disabled="currentPage === this.pagination.last_page"
                                        class="px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs flex">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && leadData && leadData.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!---in mobile view Filter Service-->
        <div v-if="isMobile" ref="dropdownContainerFilter">
            <div class="fixed bottom-36 right-5 z-50 bg-green-700 rounded-full">
                <div class="flex justify-end">
                    <button @click="toggleMainDropdown" type="button"
                        class="flex items-center justify-center px-[10px] py-2 text-white "
                        :class="{ 'cursor-not-allowed blur-sm': leadData.length == 0 }"
                        :disabled="leadData.length == 0">
                        <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                    </button>
                </div>
            </div>
            <!-- Main Dropdown -->
            <div v-if="isMainDropdownOpen" ref="mainDropdown"
                class="fixed bottom-48 sm:bottom-48 left-full transform -translate-x-full w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border">
                <div class="py-1">
                    <!-- By Due Option with Sub-Dropdown -->
                    <div class="relative">
                        <button @click="toggleSubDropdown"
                            class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200">By
                            Due</button>

                        <!-- Sub-Dropdown for By Due -->
                        <div v-if="showSubDropdown" @mouseenter="toggleMouserHoverSub"
                            @mouseleave="toggleMouserHoverSub" ref="subDropdown"
                            class="absolute right-full top-0 ml-2 w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none border">
                            <ul class="py-1 text-left">
                                <li v-for="(option, index) in followupOptions" :key="index"
                                    class="cursor-pointer py-2 px-5 hover:bg-blue-200"
                                    :class="{ 'bg-blue-200 text-blue-700': followup_select === option.value }"
                                    @click="handleFollowup(option.value, 1)">
                                    {{ option.label }}
                                </li>
                            </ul>
                        </div>
                    </div>

                    <!-- Other Options -->
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('by Date')">By Date</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('customer')">By Customer</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('employee')">By Assigned to</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('category')">By Category / Type</button>
                </div>
            </div>
        </div>
        <!---in mobile view create new lead-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openLeadModal" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!-- Use the SalesModal component -->
        <addLead :show-modal="showLeadModal" @closeLeadModal="closeLeadModal" :editData="editData" :type="typeOfLead"
            :companyId="companyId" :userId="userId">
        </addLead>
        <!--Filter-->
        <leadFilter :showModal="lead_filter" @closeFilter="closeLeadFilter" :typeList="typeList"
            :selectedByValue="selectedByValue" :page="'lead'"></leadFilter>
    </div>
    <Loader :showModal="open_loader"></Loader>
    <!-- <bottombar v-if="isMobile" :selected_btn_btm="'lead'"></bottombar> -->
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>

<script>
import confirmbox from '../dialog_box/confirmbox.vue';
import dialogAlert from '../dialog_box/dialogAlert.vue';
import addLead from '../dialog_box/addLead.vue';
import leadFilter from '../dialog_box/filter_Modal/leadFilter.vue';
import axios from 'axios';
import { mapState, mapActions, mapGetters } from 'vuex';
// import bottombar from '../dashboard/bottombar.vue';
import customerDataTable from '../dialog_box/customerDataTable.vue';
import searchCustomer from '../customers/searchCustomer.vue';
import noAccessModel from '../dialog_box/noAccessModel.vue';
export default {
    name: 'leads_home',
    emits: ['dataToParent', 'updateIsOpen'],
    components: {
        confirmbox,
        dialogAlert,
        addLead,
        leadFilter,
        // bottombar,
        customerDataTable,
        searchCustomer,
        noAccessModel
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            changeStatus: false,
            //---
            showLeadModal: false,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            columns: [],
            serviceList: [],
            leadData: [],
            originalData: [],
            isImportModalOpen: false,
            open_message: false,
            message: '',
            //---sales model--
            showSalesModal: false,
            selectedSalesType: '',
            selectedServiceType: '',
            serviceCategoriesList: [],
            editData: null,
            typeOfLead: 'add',
            leadType: [],
            leadStatus: [],
            //---filter--
            showFilterOptions: false,
            mouseOverIsNot: false,
            filterOptions: ['by Created Date', 'by Customer', 'by Employee', 'by category/Type', 'by Status', 'Custom'],
            selectedByValue: null,
            lead_filter: false,
            typeList: [],
            statusList: [],
            filteredBy: {},
            //---api integration
            companyId: null,
            userId: null,
            pagination: {},
            now: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 6,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            display_option: false,
            followupOptions: [
                { label: 'All', value: 'all' },
                { label: 'Today', value: 'today' },
                { label: 'Tomorrow', value: 'tomorrow' },
                { label: 'Week', value: 'this_week' },
                { label: 'Month', value: 'this_month' },
                { label: 'Year', value: 'this_year' },
            ],
            status_option: [{ type: 'open', total: 0, icon: 'fa-regular fa-folder-open' }, { type: 'progress', total: 0, icon: 'fa-solid fa-bars-progress' }, { type: 'completed', total: 0, icon: 'fa-solid fa-flag-checkered' }, { type: 'cancelled', total: 0, icon: 'fa-solid fa-ban' },
            { type: 'hold', total: 0, icon: 'fa-solid fa-hand' }, { type: 'all', total: 0, icon: 'fa-solid fa-circle-check' }, { type: 'pending', total: 0, icon: 'fa-solid fa-hourglass-half' }],
            followup_select: 'all',
            status_select: 'pending',
            searchByCustomer: {},
            filter_page: {},
            searchedDataList: [],
            items_category: 'tile',
            filter_option: null,
            filter_date: false,
            open_skeleton_isMobile: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---table data filter asending and decending----
            is_filter: false,
            //----filter options are-----
            isMainDropdownOpen: false,
            showSubDropdown: false,
            is_openSub: false,
            //---modal---mouse over---
            isModalVisible: false,
            modalData: {},
            modalPosition: { x: 0, y: 0 },
            actionPosition: { x: 0, y: 0 },
            isHoveringModal: false,
            hoverTimer: null,
            lastMousePosition: { x: 0, y: 0 },
            tolerance: 50, // Tolerance for mouse movement in pixels
            //--sort--
            resetSearchData: false,
            started: false,
            //---no access---
            no_access: false,
        };
    },
    computed: {
        ...mapState('lead', ['search_query', 'search_data']),
        ...mapGetters('leadType', ['currentLeadType']),
        ...mapGetters('leadsList', ['currentLeadList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        paginatedData() {
            if (this.leadData && this.leadData.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                return this.leadData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.leadData && this.leadData.length !== 0) {
                const totalFilteredRecords = this.leadData.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];
            let key_order;
            if (!this.isMobile) {
                key_order = ['lead_date', 'customer', 'title', 'assign_to', 'assign_date', 'lead_type', 'description', 'follow_up', 'lead_status', 'created_by', 'updated_by'];
            } else {
                key_order = ['title', 'lead_date', 'customer', 'assign_to', 'assign_date', 'lead_type', 'description', 'follow_up', 'lead_status',];

            }

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in leadData to get field names
            if (this.leadData.length > 0 && this.leadData) {
                // for (const key in this.leadData[0]) {
                //     if (key !== 'id' && key !== 'customer_id' && key !== 'follow_up') { // Exclude the 'id' field
                //         const label = formatLabel(key);
                //         if (key !== 'description' && key !== 'lead_type' && key !== 'lead_status') {
                //             fields.push({ label, field: key, visible: true });
                //         } else {
                //             fields.push({ label, field: key, visible: false });
                //         }
                //     }
                // }
                for (const key of key_order) {
                    if (key !== 'id' && key !== 'customer_id') {
                        const label = formatLabel(key);
                        //---key !== 'description' && key !== 'lead_type' &&
                        if (key !== 'assign_date' && key !== 'created_by' && key !== 'updated_by') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        // visiblePageNumbers() {
        //     // Calculate the range of page numbers to display
        //     const startPage = Math.floor((this.currentPage - 1) / this.recordsPerPage) * this.recordsPerPage + 1; // Start from multiples of 10
        //     const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available

        //     // Generate an array of page numbers to display
        //     return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        // },
        getStatusOption() {
            return this.status_option;
        }
    },
    created() {
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : this.recordsPerPage;
        // Make GET request with parameter company_id=1
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        const view = localStorage.getItem('leads_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        // this.getLeadStatus();
        if (!this.currentLeadType || this.currentLeadType.length === 0) {
            this.fetchLeadTypeList();
        } else {
            this.fetchLeadTypeList();
        }
        // this.getLeadType();
        //---get lead list---
        // this.getLeadList(this.currentPage, this.recordsPerPage);
        // Update current time every second
        let store_data = this.currentLeadList;
        if (store_data && store_data.data && store_data.data.length > 0) {
            this.open_skeleton = true;
            this.started = true;
            this.getInitialData(store_data, this.started);
            this.getStatusLabel(this.status_select, 1);
            this.fetchLeadList({ page: 1, per_page: 1 });
        } else {
            if (store_data && Object.keys(store_data).length == 0) {
                this.open_skeleton = true;
                this.started = true;
                this.fetchLeadList({ page: 1, per_page: 1 });
            }
            this.getStatusLabel(this.status_select, 1);

        }
        //---sortIcons---
        const initialShortVisible = ['customer', 'lead_type', 'follow_up', 'lead_status'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);

        setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    methods: {
        ...mapActions('leadType', ['fetchLeadTypeList']),
        ...mapActions('leadsList', ['fetchLeadList']),
        ...mapActions('localStorageData', ['validateRoles', 'fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        getLeadStatus() {
            //---lead status----
            axios.get('/lead_statuses', {
                params: {
                    company_id: this.companyId
                }
            })
                .then(response => {
                    // Handle response
                    console.log(response.data.data);
                    this.leadStatus = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        getLeadType() {
            //---lead type---
            /*axios.get('/lead_types', {
                params: {
                    company_id: this.companyId
                }
            })
                .then(response => {
                    // Handle response
                    console.log(response.data.data);
                    this.leadType = response.data.data;
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });*/
            if (this.currentLeadType && this.currentLeadType.length > 0) {
                this.leadType = this.currentLeadType;
            }
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },

        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.open_loader = true;
                axios.delete(`/leads/${this.leadData[this.deleteIndex].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        this.updateKeyWithTime('leads_update');
                        // console.log(response.data, 'What happening...!!!!');
                        this.message = 'Lead deleted successfully...!';
                        this.show = true;
                        this.open_loader = false;
                        // this.open_message = true;
                        // this.message = response.data.message;
                        this.open_confirmBox = false;
                        this.deleteIndex = null;
                        this.getLeadList(this.leadData.length > 1 ? this.currentPage : this.currentPage - 1 !== 0 ? this.currentPage - 1 : 1, this.recordsPerPage, true);
                    })
                    .catch(error => {
                        this.open_loader = false;
                        console.error('Error', error);
                    })
            }

        },
        confirmDelete(index) {
            if (this.getplanfeatures('leads')) {
                this.no_access = true;
            } else {
                // console.log(index, 'What happening...', this.leadData);
                this.deleteIndex = index;
                this.open_confirmBox = true;
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                if (this.isDropdownOpen) {
                    this.isDropdownOpen = false;
                    document.removeEventListener('click', this.handleOutsideClick);
                }
                else {
                    this.isDropdownOpen = true; // Open dropdown
                    // Attach event listener to close dropdown when clicking outside
                    document.addEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick() {
            try {
                // Check if the click is outside the dropdown
                const dropdownRef = this.$refs.dropdownSetting;
                // console.log(dropdownRef, 'What happening......!!!', event.target);
                if (dropdownRef && !dropdownRef.contains(event.target)) {
                    this.isDropdownOpen = false; // Close dropdown
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        //---lead modal
        openLeadModal() {
            this.showLeadModal = true;
        },

        //---close the message--
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---record---
        startEdit(record) {
            if (this.getplanfeatures('leads')) {
                this.no_access = true;
            } else {
                this.$router.push({
                    name: 'leadEdit',
                    query: {
                        recordId: record.id
                    }
                });
            }
        },
        //---closeLeadModal
        closeLeadModal(newData) {
            // console.log('helllllllo');
            if (newData && this.typeOfLead !== 'edit') {
                this.leadData.unshift(newData);
                this.message = 'Lead created successfully...!';
                this.show = true;
                // console.log(newData, 'What happening....!');
            } else if (newData && this.typeOfLead === 'edit') {
                let findIndexData = this.leadData.findIndex((opt) => opt.id === this.editData.id);
                this.leadData.splice(findIndexData, 1, newData);
                this.paginatedData;
                this.message = 'Lead updated successfully...!';
                this.show = true;
            }
            this.showLeadModal = false;
        },
        //---lead---
        toggleFilterSelected(option) {
            // console.log(option, 'GGGGGGGGGGGGGGG');
            this.typeList = this.leadType;
            this.selectedByValue = option;
            this.lead_filter = true;
            this.leadData = this.originalData;
            this.showFilterOptions = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //--close lead
        closeLeadFilter(searchData, page) {
            // console.log(searchData, 'What is happening.....!');
            if (searchData) {
                // this.filteredBy = searchData;
                const keysData = Object.keys(searchData);
                this.filteredBy = searchData
                this.searchByCustomer = {};
                this.followup_select = null;
                this.open_loader = true;
                axios.get('/searchs', { params: { type: 'leads', q: searchData.status >= 0 ? searchData.status : this.status_select !== 5 ? this.status_select : '', filter: '', per_page: this.recordsPerPage, page: page, customer_id: searchData.customer_id ? searchData.customer_id : '', employer_id: searchData.assign_to && searchData.assign_to.length > 0 ? searchData.assign_to[0].id : '', category: searchData.type ? searchData.type : '', created_at: searchData.date ? searchData.date : '', from_date: searchData.from ? searchData.from : '', to_date: searchData.to ? searchData.to : '' } })
                    .then(response => {
                        console.log(response.data, 'Status Data');
                        this.open_loader = false;
                        this.leadData = response.data.data;
                        this.pagination = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
            this.lead_filter = false;
        },
        //---filter dropdown
        toggleFilter() {
            this.showFilterOptions = !this.showFilterOptions;
        },
        //--hidden dropdown--
        hiddenDropdown() {
            if (!this.mouseOverIsNot) {
                this.showFilterOptions = false;
            }
        },
        mouseOverOption() {
            this.mouseOverIsNot = true;
        },
        mouseLeaveOption() {
            this.mouseOverIsNot = false;
        },
        //---reset filter--

        resetTheFilter() {
            this.filteredBy = {};
            this.status_select = 5;
            this.searchByCustomer = {};
            this.followup_select = 'all';
            this.resetSearch('');
            this.getLeadList(1, this.recordsPerPage);
            this.currentPage = 1;
            this.resetSearchData = true;
        },
        //----find status name---        
        findStatusData(id) {
            if (this.leadStatus.length > 0) {
                let findObj = this.leadStatus.find((opt) => opt.id === id);
                if (findObj) {
                    return findObj.name
                }
            }
        },
        //---find type name---
        findTypeData(id) {
            if (this.leadType.length > 0) {
                let findObj = this.leadType.find((opt) => opt.id === id);
                if (findObj) {
                    return findObj.name
                }
            }
        },
        //---formated display date---
        formattedDate(timestamp) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            date.setHours(date.getHours()); // Add 5 hours
            date.setMinutes(date.getMinutes()); // Add 30 minutes
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        //---get lead data--
        //---lead List--
        getLeadList(page, per_page, is_delete) {
            if (page == 1) {
                // console.log(this.isMobile, 'EEEEEEEEEEEE');
                this.fetchLeadList({ page: page, per_page: this.isMobile ? 20 : per_page, is_delete });
                if (this.currentLeadList && this.currentLeadList.data && !is_delete) {
                    this.leadData = this.currentLeadList.data;
                    this.originalData = this.leadData;
                    this.pagination = this.currentLeadList.pagination;
                }
            } else {
                this.open_skeleton = true;
                axios.get('/leads', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        // console.log(response.data.data, 'RRRRRR');
                        this.leadData = response.data.data;
                        this.originalData = this.leadData;
                        this.pagination = response.data.pagination;
                        this.open_skeleton = false;
                        if (response.data.total_open >= 0) {
                            this.status_option[0].total = response.data.total_open;
                        }
                        if (response.data.total_progress >= 0) {
                            this.status_option[1].total = response.data.total_progress;
                        }
                        if (response.data.total_complete >= 0) {
                            this.status_option[2].total = response.data.total_complete;
                        }
                        if (response.data.total_cancel >= 0) {
                            this.status_option[3].total = response.data.total_cancel;
                        }
                        if (response.data.total_hold >= 0) {
                            this.status_option[4].total = response.data.total_hold;
                        }
                        if (this.pagination.total) {
                            this.status_option[5].total = this.pagination.total;
                        }
                        if (response.data.total_open >= 0 || response.data.total_progress >= 0 || response.data.total_hold >= 0) {
                            this.status_option[6].total = (1 * response.data.total_open >= 0 ? response.data.total_open : 0) + (1 * response.data.total_progress >= 0 ? response.data.total_progress : 0) + (1 * response.data.total_hold >= 0 ? response.data.total_hold : 0);
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        //--filter option--
        changeFilter(opt) {
            this.filter_option = opt;
            this.toggleMainDropdown();
            if (opt === 'date') {
                this.filter_date = !this.filter_date;
                if (!this.filter_date) {
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutsideDate);
                } else {
                    document.addEventListener('click', this.handleClickOutsideDate);
                }
            }
            if (opt === 'customer') {
                this.toggleFilterSelected('by Customer');
            }
            if (opt === 'employee') {
                this.toggleFilterSelected('by Employee');
            }
            if (opt === 'category') {
                this.toggleFilterSelected('by category/Type');
            }
            if (opt === 'by Date') {
                this.toggleFilterSelected('by Date');
            }
        },
        handleClickOutsideDate(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs.componentContainer;
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.filter_date = !this.filter_date;
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutsideDate);
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        getFollowUpDate(fieldValue) {
            // console.log(fieldValue, 'EEEEEEEEEEEEEEEEEEEEEEE');
            if (fieldValue !== '' && typeof fieldValue === 'string') {
                const parsedValue = JSON.parse(fieldValue);
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    return this.generateDate(parsedValue[0].date_and_time);
                }
            } else if (typeof fieldValue === 'object') {
                if (Array.isArray(fieldValue) && fieldValue.length > 0 && fieldValue[0].date_and_time) {
                    return this.generateDate(fieldValue[0].date_and_time);
                }
            }
        },
        generateDate(data) {
            // console.log(data, 'What about the data......');
            const dateTimeString = data;
            const date = new Date(dateTimeString);
            const formattedDate = date.toLocaleDateString('en-GB', { year: 'numeric', month: '2-digit', day: '2-digit' });
            const formattedTime = date.toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' });
            return formattedDate + ' ' + formattedTime;
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        handleFollowup(optionValue, page) {
            this.toggleMainDropdown();
            this.followup_select = optionValue;
            if (optionValue === 'all') {
                this.resetSearch('');
                this.searchByCustomer = {};
            }
            if (this.status_select !== null && this.status_select !== 5) {
                this.status_select = 5;
            }
            this.open_loader = true;
            let sendData = { type: 'leads', q: this.status_select && optionValue !== 'all' && this.status_select !== 5 ? this.status_select : '', filter: optionValue !== 'all' ? optionValue : '', per_page: this.per_page, page: page, customer_id: Object.keys(this.searchByCustomer).length > 0 && this.searchByCustomer.id ? this.searchByCustomer.id : '' };
            if (Object.keys(this.filteredBy).length > 0) {
                if (this.filteredBy.customer_id) {
                    sendData.customer_id = this.filteredBy.customer_id;
                }
                if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
                    sendData.employer_id = this.filteredBy.assign_to[0].id;
                }
            }
            // Handle followup option selection (e.g., perform an action based on the selected value)
            // console.log('Selected followup option:', optionValue);
            axios.get('/searchs', { params: { ...sendData } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.leadData = response.data.data;
                    this.pagination = response.data.pagination;
                    this.currentPage = page ? page : this.currentPage;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        getStatusLabel(data, page) {
            if (this.followup_select !== null) {
                this.followup_select = 'all';
            }
            this.status_select = data;
            // console.log(data, 'What happening....!!!');
            if (data == 5) {
                // console.log('Heloooooo1');
                this.resetSearch('');
                this.searchByCustomer = {};
            }
            this.open_skeleton = true;
            // this.followup_select = null;
            // console.log(data, 'Waht happening...');
            axios.get('/searchs', { params: { type: 'leads', q: data !== 5 ? data : '', filter: this.followup_select >= 0 && data !== 5 ? this.followup_select : '', per_page: this.recordsPerPage, page: page, customer_id: Object.keys(this.searchByCustomer).length > 0 && this.searchByCustomer.id ? this.searchByCustomer.id : '' } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    this.open_skeleton = false;
                    this.leadData = response.data.data;
                    this.pagination = response.data.pagination;
                    this.currentPage = page ? page : this.currentPage;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        getSearchedData(page) {
            if (this.followup_select == 'all' || this.status_select === 5) {
                this.status_select = 5;
                this.followup_select = 'all';
            }
            if (Object.keys(this.searchByCustomer).length > 0 && this.searchByCustomer.id) {
                this.open_loader = true;
                axios.get('/searchs', { params: { type: 'leads', q: this.status_select >= 0 && this.followup_select !== 'all' ? this.status_select : '', filter: this.followup_select >= 0 && this.status_select !== 5 ? this.followup_select : '', per_page: this.recordsPerPage, page: page, customer_id: this.searchByCustomer.id } })
                    .then(response => {
                        console.log(response.data, 'Status Data');
                        this.open_loader = false;
                        this.leadData = response.data.data;
                        this.pagination = response.data.pagination;
                        this.currentPage = page ? page : this.currentPage;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
        },
        ...mapActions('lead', ['updateSearch', 'updateData']),
        resetSearch(newValue) {
            try {
                const newQuery = '';
                this.updateSearch(newQuery);
                const newData = {};
                this.updateData(newData);
                this.$emit('dataToParent', newValue);
            } catch (error) {
                console.error('Error resetting search:', error);
            }
        },
        closeFilterOptionsOnClickOutside(event) {
            // console.log(event, 'EEE', this.$refs.filterOptionsDropdown);
            // Check if the click event target is outside the filter options dropdown
            const filterOptionsElement = this.$refs.filterOptionsDropdown;
            if (filterOptionsElement && !filterOptionsElement.contains(event.target)) {
                // Click occurred outside the filter options dropdown, so close it
                this.showFilterOptions = false;
            }
        },
        getDateStatusClass(fieldValue) {
            try {
                const parsedValue = typeof fieldValue === 'string' ? JSON.parse(fieldValue) : fieldValue;
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    const followUpDate = new Date(parsedValue[0].date_and_time);
                    const currentDate = new Date();

                    if (followUpDate < currentDate) {
                        return 'text-red-500';
                    } else {
                        return 'text-green-500';
                    }
                }
            } catch (error) {
                console.error('Error parsing date:', error);
            }
            return ''; // Default empty string if date cannot be parsed
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            let send_data = {
                type: 'leads', q: this.status_select >= 0 && this.status_select !== 5 ? this.status_select : '', filter: this.followup_select >= 0 ? this.followup_select : '', per_page: this.recordsPerPage, page: page,
                customer_id: this.searchByCustomer.id ? this.searchByCustomer.id : ''
            };
            if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
                // console.log(this.filteredBy, 'helllo');
                if (this.filteredBy.customer_id) {
                    send_data.customer_id = this.filteredBy.customer_id
                }
                if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
                    send_data.employer_id = this.filteredBy.assign_to[0].id;
                }
                if (this.filteredBy.type) {
                    send_data.category = this.filteredBy.type;
                }
            }
            // console.log(this.category_type !== null && this.category_type !== 'all', 'Waht happening...!!!');
            if (this.category_type !== null && this.category_type !== 'all') {
                send_data.category_id = this.category_type;
            }
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.leadData = [...this.leadData, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        //---initial data
        getInitialData(leadData, started) {
            if (!started) {
                this.leadData = leadData.data;
                this.originalData = this.leadData;
                this.pagination = leadData.pagination;
            } else {
                this.started = false;
            }
            this.open_skeleton = false;
            let status_counts = leadData.status_counts;
            if (status_counts.total_open >= 0) {
                this.status_option[0].total = status_counts.total_open;
            }
            if (status_counts.total_progress >= 0) {
                this.status_option[1].total = status_counts.total_progress;
            }
            if (status_counts.total_complete >= 0) {
                this.status_option[2].total = status_counts.total_complete;
            }
            if (status_counts.total_cancel >= 0) {
                this.status_option[3].total = status_counts.total_cancel;
            }
            if (status_counts.total_hold >= 0) {
                this.status_option[4].total = status_counts.total_hold;
            }
            if (this.pagination.total && !started) {
                this.status_option[5].total = this.pagination.total;
            } else if (started) {
                this.status_option[5].total = leadData.pagination.total;
            }
            if (status_counts.total_open >= 0 || status_counts.total_progress >= 0 || status_counts.total_hold >= 0) {
                this.status_option[6].total = (1 * status_counts.total_open >= 0 ? status_counts.total_open : 0) + (1 * status_counts.total_progress >= 0 ? status_counts.total_progress : 0) + (1 * status_counts.total_hold >= 0 ? status_counts.total_hold : 0);
            }
        },
        //--role based on--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        //--filter dropdown---
        toggleMainDropdown() {
            this.isMainDropdownOpen = !this.isMainDropdownOpen;
            if (this.isMainDropdownOpen) {
                document.addEventListener('click', this.handleClickOutsideFilter);
            } else {
                // this.showSubDropdown = false;
                document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        toggleSubDropdown() {
            setTimeout(() => {
                if (!this.is_openSub) {
                    this.showSubDropdown = !this.showSubDropdown;
                }
            }, 200)
        },
        toggleMouserHoverSub() {
            this.is_openSub = !this.is_openSub;
            if (!this.is_openSub && this.showSubDropdown) {
                this.toggleSubDropdown();
            }
        },
        handleClickOutsideFilter(event) {
            const mainDropdown = this.$refs.dropdownContainerFilter;
            // console.log(event.target, 'Waht happning the data....', mainDropdown.contains(event.target));
            if (mainDropdown && !mainDropdown.contains(event.target)) {
                this.is_openSub = false;
                this.showSubDropdown = false;
                this.toggleMainDropdown();
                // document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'leads' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.leadData = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.leadData, type: 'leads', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'leads' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.leadData = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.leadData, type: 'leads', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        //----table action----
        showModal(index, event) {
            if (index) {
                this.lastMousePosition = { x: event.layerX * 1, y: event.layerY * 1 };
                // Start a timer to check after 5000ms
                this.hoverTimer = setTimeout(() => {
                    if (this.isMouseInSamePosition(event)) {
                        this.modalData = index;
                        this.modalPosition = { x: ((event.layerX * 1) + 25), y: (event.layerY * 1) + 25 };
                        this.isModalVisible = true;
                    }
                }, 200);
            }
        },
        hideModal() {
            clearTimeout(this.hoverTimer); // Clear any existing timer
            setTimeout(() => {
                if (!this.isHoveringModal) {
                    this.isModalVisible = false;
                }
            }, 200);
        },
        toggleHoverModal(status) {
            this.isHoveringModal = status;
            if (!status) {
                this.hideModal();
            }
        },
        isMouseInSamePosition(event) {
            // Allow for a 10-pixel tolerance in mouse movement
            const xDiff = Math.abs(this.lastMousePosition.x - event.layerX);
            const yDiff = Math.abs(this.lastMousePosition.y - event.layerY);
            return xDiff <= this.tolerance && yDiff <= this.tolerance;
        },
        closeAllModals() {
            // Close all modals
            this.open_confirmBox = false;
            this.open_message = false;
            this.showLeadModal = false;
            this.lead_filter = false;
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                let searchDatacustomer = {}
                searchDatacustomer.customer_id = selectedData.id;
                searchDatacustomer.customer = selectedData.first_name + (selectedData.last_name ? ' ' + selectedData.last_name : '') + ' - ' + selectedData.contact_number;
                this.closeLeadFilter(searchDatacustomer, 1);
            }
        },
        resetToSearch() {
            this.filteredBy = {};
            this.resetSearchData = false;
            this.getLeadList(1, this.recordsPerPage);
        },
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //--refresh page--
        refreshDataTable() {
            this.resetTheFilter();
        },
        getUserColor(userId) {
            const colors = ['#3e14e3', '#2a9d8f', '#264653', '#8a4f7d', '#457b9d', '#e36ae9'];
            return colors[userId % colors.length];
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
    },
    watch: {
        search_query(newValue) {
            // console.log(newValue, 'WWWWWWWWWW happening the data');
            // Watch for changes in Vuex state and update local searchQuery
            if (!this.isEmptyObject(newValue) && !this.isEmptyObject(this.search_data)) {
                // console.log(newValue, 'What happening...!!!!');
                this.filteredBy = {};
                this.searchByCustomer = this.search_data;
                this.searchedDataList = [];
                this.getSearchedData(1);
                // this.leadData = this.leadData.filter(opt => opt.customer.id === newValue.customer.id);
            }
        },
        searchedData(newValue) {
            // console.log(newValue, 'RRRRR');
            if (!this.isEmptyObject(newValue)) {
                // console.log(newValue, 'What happening...!!!!');
                this.filteredBy = {};
                this.searchByCustomer = newValue;
                this.searchedDataList = [];
                this.getSearchedData(1);
                // this.leadData = this.leadData.filter(opt => opt.customer.id === newValue.customer.id);
            }
            else {
                this.searchByCustomer = {};
                this.getLeadList(1, this.recordsPerPage);
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                if ((this.status_select === null || this.status_select === 5) && (this.followup_select === null || this.followup_select === 'all') && Object.keys(this.searchByCustomer).length === 0 && Object.keys(this.filteredBy).length === 0) {
                    this.getLeadList(newValue, this.recordsPerPage);
                }
                else {
                    // console.log(this.filteredBy, 'RRRRRR Waht happening....!!!');
                    if (this.status_select !== null && this.status_select >= 0 && this.status_select !== 5) {
                        this.getStatusLabel(this.status_select, newValue);
                    } else if (this.followup_select && this.followup_select !== 'all') {
                        this.handleFollowup(this.followup_select, newValue);
                    } else if (Object.keys(this.searchByCustomer).length !== 0) {
                        this.getSearchedData(newValue);
                    } else if (Object.keys(this.filteredBy).length !== 0) {
                        this.closeLeadFilter(this.filteredBy, newValue);
                    } else if (this.status_select !== null && this.status_select === 'pending') {
                        this.getStatusLabel('pending', newValue);
                    } else {
                        this.getLeadList(1, newValue);
                    }
                }
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if ((this.status_select === null || this.status_select === 5) && (this.followup_select === null || this.followup_select === 'all') && Object.keys(this.searchByCustomer).length === 0 && Object.keys(this.filteredBy).length === 0) {
                    this.getLeadList(1, newValue);
                    this.currentPage == 1;
                } else {
                    if (this.status_select !== null && this.status_select >= 0 && this.status_select !== 5) {
                        this.getStatusLabel(this.status_select, 1);
                    } else if (this.followup_select && this.followup_select !== 'all') {
                        this.handleFollowup(this.followup_select, 1);
                    } else if (Object.keys(this.searchByCustomer).length !== 0) {
                        this.getSearchedData(1);
                    } else if (Object.keys(this.filteredBy).length !== 0) {
                        this.closeLeadFilter(this.filteredBy, 1);
                    } else if (this.status_select !== null && this.status_select === 'pending') {
                        this.getStatusLabel('pending', 1);
                    }
                    else {
                        this.getLeadList(1, newValue);
                        this.currentPage == 1;
                    }
                }

            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('leads_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentLeadType: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.getLeadType();
                }
            }
        },
        currentLeadList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.getInitialData(newValue, this.started);
                }
                this.open_loader = false;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.filter_option = null;
                this.resetTheFilter();
                this.fetchLeadTypeList()
                this.fetchLeadList({ page: 1, per_page: this.isMobile ? 20 : per_page });
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.leadData = [...newValue];
                    // console.log(newValue, 'New Value data...............');
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showLeadModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        lead_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        if (this.showFilterOptions) {
            document.addEventListener('click', this.closeFilterOptionsOnClickOutside);
        } else {
            document.removeEventListener('click', this.closeFilterOptionsOnClickOutside);

        }
    },
    beforeDestroy() {
        if (this.showFilterOptions) {
            document.removeEventListener('click', this.closeFilterOptionsOnClickOutside);
        }
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        document.removeEventListener('click', this.handleClickOutsideDate());
        document.removeEventListener('click', this.handleClickOutsideFilter());
    }
}
</script>

<style scoped>
.manualStyle {
    overflow: auto;
    height: 100vh;
}

/* Style the scrollbar */
::-webkit-scrollbar {
    width: 3px;
    /* Set the width of the scrollbar */
    height: 4px;
}

/* Track (the area around the scrollbar) */
::-webkit-scrollbar-track {
    background: #CFD8DC;
    /* Background color of the scrollbar track */
}

/* Handle (the draggable part of the scrollbar) */
::-webkit-scrollbar-thumb {
    background: #90A4AE;
    /* Color of the scrollbar handle */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #90A4AE;
    /* Color of the scrollbar handle on hover */
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
    z-index: 100;
}

.bg-blue-800 {
    background-color: #053BF4;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }

    .flex-row>div {
        white-space: nowrap;
        /* Prevent wrapping */
    }
}

@media (max-width: 768px) {
    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }

}
</style>
