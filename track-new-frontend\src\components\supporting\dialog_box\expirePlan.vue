<template>
    <div v-if="showModal" class="text-sm fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white p-6 w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <!--close icon-->
            <button @click="closeModal"
                class="cursor-pointer absolute top-1 right-1 text-red-600 cursor-pointer hover:text-red-700"><font-awesome-icon
                    icon="fa-regular fa-circle-xmark" size="lg" /></button>
            <!-- Sad Animated Icons -->
            <div class="text-center mb-4">
                <div class="animate-pulse text-red-500 text-6xl">
                    <font-awesome-icon icon="fa-solid fa-exclamation-triangle" />
                    <!-- <font-awesome-icon icon="fa-solid fa-exclamation-circle" />
                    <font-awesome-icon icon="fa-solid fa-exclamation-circle" /> -->
                </div>
            </div>

            <!-- Message and Contact Details -->
            <h2 class="text-lg  mb-4 text-center text-red-600">{{ validateLocalData ?
                `${currentLocalDataList.plans.title}
                plan subscription
                expired.` :
                `Free Trail is Over.` }}
            </h2>
            <p class="mt-4">To continue using the services, buy a subscription today.</p>
            <div class="mt-4">
                <a href="/subscription"
                    class="bg-green-600 text-white hover:bg-green-700 cursor-pointer px-8 py-2 rounded  shadow-inner shadow-green-200 border border-green-600">Buy
                    Now</a>
            </div>
            <div class="mt-4">
                <a class="cursor-pointer hover:underline text-gray-500 text-xs" href="/pages">More informations</a>
            </div>
            <!-- <div class="contact-details text-left mb-4">
                <h3 class="text-xl font-medium mb-2">Support Team Contact Details</h3>
                <div class="mt-4 space-y-3">
                    <div class="block">
                        <div class="py-1">
                            <button @click="dialPhoneNumber(7200110301)"
                                class="flex items-center  text-blue-700"><font-awesome-icon icon="fa-solid fa-phone"
                                    size="lg" /> <span class="ml-2">+91-7200110301</span></button>
                        </div>
                        <div class="py-1">
                            <button @click="dialPhoneNumber(7200068990)"
                                class="flex items-center  text-blue-700"><font-awesome-icon icon="fa-solid fa-phone"
                                    size="lg" /> <span class="ml-2">+91-7200068990</span></button>
                        </div>
                    </div>

                    <div>
                        <button @click="openWhatsApp(7200110301)"
                            class="flex items-center text-green-700"><font-awesome-icon icon="fa-brands fa-whatsapp"
                                size="lg" />
                            <span class="ml-2">+91-7200110301</span></button>
                    </div>
                    <div>
                        <button @click="openemail('<EMAIL>')"
                            class="flex items-center text-pink-700"><font-awesome-icon icon="fa-solid fa-envelope"
                                size="lg" />
                            <span class="ml-2"><EMAIL></span>
                        </button>
                    </div>
                    <div class="text-orange-700">
                        <font-awesome-icon icon="fa-brands fa-edge" size="lg" /> <a href="https://track-new.com/"
                            target="_blank" class="hover:text-green-700 hover:underline ml-2">https://track-new.com/</a>
                    </div>
                </div>
            </div> -->
            <!-- Close Button -->
            <!-- <div class="flex justify-end items-center">
                
                <button @click="closeModal" class="btn-close">Close</button>
            </div> -->
        </div>
    </div>
</template>

<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { mapActions, mapGetters } from 'vuex';

export default {
    components: {
        FontAwesomeIcon
    },
    props: {
        showModal: Boolean
    },
    data() {
        return {
            isMobile: false,
            isOpen: false,
            local_data: {},
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        validateLocalData() {
            if (this.currentLocalDataList && this.currentLocalDataList.plans && this.currentLocalDataList.plans.title) {
                return true;
            } else {
                return false;
            }
        }
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        closeModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('update:show', false);
            }, 300);
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        openWhatsApp(phone) {
            const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            if (/android/i.test(userAgent)) {
                window.location.href = `intent://send/+91${phone}#Intent;scheme=smsto;package=com.whatsapp;action=android.intent.action.SENDTO;end`;
            } else if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
                window.location.href = `whatsapp://send?phone=+91${phone}`;
            } else {
                window.location.href = `https://web.whatsapp.com/send?phone=+91${phone}`;
            }
        },
        openemail(mail_id) {
            window.open(`mailto:${mail_id}?subject=Support track new software`);
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        //--go pages---
        async goPages() {
            try {
                this.$emit('navigate', '/pages');
                // this.closeModal();
            } catch (error) {
                console.error('Navigation error:', error);
            }
        },

        async goSubscriptions() {
            try {
                // this.$emit('navigate', '/subscription');
                // this.closeModal();
                this.$root.$onNavigate('/subscription')
            } catch (error) {
                console.error('Navigation error:', error);
            }
        }

    },
    mounted() {
        this.updateIsMobile();
        this.fetchLocalDataList();
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
    }
};
</script>

<style scoped>
/* Modal overlay */
.modal-overlay {
    @apply fixed inset-0 bg-gray-800 bg-opacity-75 flex items-center justify-center;
}

/* Modal content */
.modal-content {
    @apply bg-white p-6 rounded-lg shadow-lg w-full max-w-lg text-center;
}

/* Button styles */
.btn-close {
    @apply bg-gray-500 text-white py-2 px-4 rounded-lg hover:bg-gray-600;
}

/* Contact details */
.contact-details {
    @apply space-y-2;
}

/* Animate pulse */
@keyframes pulse {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }

    100% {
        opacity: 1;
    }
}

.animate-pulse {
    animation: pulse 1s infinite;
}
</style>