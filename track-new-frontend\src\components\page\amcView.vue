<template>
    <div class="flex h-screen relative">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!--Headbar with its content -- >
            <!-- < headbar @toggle-sidebar=" toggleSidebar">
            </headbar> -->


            <!-- services home -->
            <div class="relative">
                <displayAMC :isMobile="isMobile"></displayAMC>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"></sidebar>

        </div> -->

    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/amc/amcView/headbar.vue';
import displayAMC from '../supporting/amc/amcView/displayAMC.vue';
import { useMeta } from '@/composables/useMeta';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'leads',
    components: {
        // sidebar,
        // headbar,
        displayAMC,
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            route_item: 11,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Followup AMC';
        const pageDescription = 'Manage AMC follow-ups effectively with status updates aligned to each customer interaction, keeping your service on track.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList', 'validateRoles']),
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },


    },
    mounted() {
        this.updateIsMobile(); // Initial check
        this.fetchLocalDataList();
        this.updateIsEnableBottom(false);
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        this.updateIsEnableBottom(true);

        window.removeEventListener('resize', this.updateIsMobile);
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnableBottom(true);
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>