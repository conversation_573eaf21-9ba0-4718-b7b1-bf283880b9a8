<template>
    <div class="text-sm p-2">
        <div class="py-2 flex justify-between items-center">
            <div class="flex justify-center items-center">
                <label>SearchBy:</label>
                <input v-model="searchTerm" class="px-2 border border-gray-400 rounded mx-3 py-2 w-full" />
            </div>
            <div class="flex justify-between items-center space-x-4">
                <button @click="exportToPDF" class="btn btn-red">
                    <font-awesome-icon icon="fa-solid fa-file-pdf" size="lg" />
                    <!-- <span class="text-[10px] block text-center">PDF</span> -->
                </button>
            </div>
        </div>
        <div class="w-full">
            <vue3-table-lite :is-static-mode="true" :columns="table.columns" :rows="table.rows"
                :total="table.totalRecordCount" :sortable="table.sortable"></vue3-table-lite>
        </div>
        <div class="text-right py-2">
            <div v-for="(total, key) in totals" :key="key">
                <span class="font-bold">{{ key }}: </span> {{ currentCompanyList && currentCompanyList.currency ===
                    'INR'
                    ? '\u20b9' : currentCompanyList.currency }} {{ total.toFixed(2) }}
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, reactive, ref, computed, watch } from "vue";
import Vue3TableLite from "vue3-table-lite";
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import jsPDF from 'jspdf';
import 'jspdf-autotable';

export default defineComponent({
    name: "Datatable",
    components: { Vue3TableLite },
    props: {
        isMobile: Boolean,
        isAndroid: Boolean,
        table_data: {
            type: Array,
            required: true
        },
        columns_data: {
            type: Array,
            required: true
        },
        selected_option: {
            type: String,
            required: true
        },
        currentCompanyList: Object,
    },
    setup(props) {
        const searchTerm = ref("");
        const recordsPerPage = ref(20);
        // Search text
        const data = reactive({ rows: [...props.table_data] });
        const columns = reactive([...props.columns_data]);
        // Watch for changes in table_data and columns_data props
        watch(
            () => props.table_data,
            (newData) => {
                data.rows = [...newData];
            }
        );

        watch(
            () => props.columns_data,
            (newColumns) => {
                columns.length = 0;
                columns.push(...newColumns);
            }
        );

        const filteredRows = computed(() => {
            if (props.selected_option === 'orders' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.plan_name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.plan_duration.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.invoice_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.invoice_date.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.will_expire.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.service_type.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.gateway_name.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.status.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else {
                return [];
            }
        });
        // Table config
        const table = reactive({
            columns: columns,
            rows: filteredRows,
            totalRecordCount: computed(() => filteredRows.value.length),
            sortable: {
                order: "id",
                sort: "asc",
            },
        });

        const totals = computed(() => {
            if (props.selected_option === 'orders' && data.rows.length > 0) {
                const totalObj = {
                    amount: 0,
                };
                filteredRows.value.forEach(row => {
                    totalObj.amount += parseFloat(row.amount) || 0;
                });
                return totalObj;
            }
        });


        const exportToPDF = () => {
            const doc = new jsPDF();
            // Prepare the table data
            const tableData = [
                ...filteredRows.value.map(row => table.columns.map(col => row[col.field])),
                props.selected_option === 'orders' ? ['', 'Total Paid', ': ', '', '', '', '', totals.value.amount] :
                    []
            ];

            // Add the table headers
            const tableHeaders = [table.columns.map(col => col.label)];
            // Calculate the total number of pages based on recordsPerPage
            const totalPages = Math.ceil(tableData.length / recordsPerPage.value);
            // Function to format page information
            const getPageInfo = (pageNumber) => {
                const startIndex = (pageNumber - 1) * recordsPerPage.value + 1;
                const endIndex = Math.min(pageNumber * recordsPerPage.value, tableData.length);
                return `Page ${pageNumber} of ${totalPages}, Showing records ${startIndex} to ${endIndex} of ${tableData.length}`;
            };
            // Add page information to the document
            doc.text(getPageInfo(1), 14, 10); // Add page information at position (14, 10)
            // AutoTable configuration
            const autoTableConfig = {
                head: tableHeaders,
                body: tableData,
                startY: 20, // Start table at Y-position 20 after the page information
                showHead: 'everyPage', // Ensure the table headers are shown on every page
                didDrawPage: function (data) {
                    // Add page information on every page except the first
                    if (data.pageNumber > 1) {
                        doc.text(getPageInfo(data.pageNumber), 14, 10); // Add page information at position (14, 10)
                    }
                }
            };
            // Add autoTable to document
            doc.autoTable(autoTableConfig);

            // Add record count and total records count at the end of the document
            const lastPageInfo = `Total Records: ${tableData.length}`;
            doc.text(lastPageInfo, 14, doc.internal.pageSize.height - 10); // Add last page information at the bottom

            // Save the PDF file
            doc.save(`${props.selected_option}.pdf`);
        };

        return {
            searchTerm,
            table,
            totals,
            exportToPDF,
        };
    },

});
</script>
<style scoped>
.btn {
    @apply bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded;
}

.btn-red {
    @apply bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded;
}
</style>