<template>
    <div class="p-1 mt-5" :class="{ 'mb-[60px]': isMobile, 'my-custom-margin': !isMobile }">
        <!--page header-->
        <div v-if="!isMobile" class="m-1 mx-4 mb-3 flex items-center space-x-4">
            <p class="font-bold text-xl">{{ view_customer_categories ? 'Customer Categories' : 'Service Categories' }}
            </p>
            <div v-if="!open_skeleton" class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                <p class="text-gray-700">Total
                    {{ view_customer_categories ? 'Customer Categories' : 'Service Categories' }}
                    :</p>
                <p class="font-semibold pl-1">
                    {{ view_customer_categories ? data_customer.length : currentAMCCategory && currentAMCCategory.id ?
                        data.length - 1 : data.length }}
                </p>
            </div>
        </div>
        <!--new design header-->
        <div class="flex mx-4 justify-between relative">
            <div class="flex mr-2 space-x-4">
                <button @click="openModal" :class="{ 'mr-2': isMobile }"
                    class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center">
                    <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /></span>
                    <span v-if="!isMobile" class="text-center sm:hidden lg:inline-block">{{
                        view_customer_categories ? 'New Customer Category' :
                            'New Service Category' }}</span></button>
                <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                    <div v-if="items_category === 'tile'"
                        class="info-msg px-2 py-1 border rounded-lg border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200"
                        :class="{ 'bg-white': items_category === 'tile' }" @click="toggleView" :title02="'Table view'">
                        <font-awesome-icon icon="fa-solid fa-check" class="pr-2 text-green-600" />
                        <font-awesome-icon icon="fa-solid fa-bars" />
                        <!-- <font-awesome-icon v-else icon="fa-solid fa-grip" size="lg" /> -->
                    </div>
                    <div v-if="items_category !== 'tile'"
                        class="px-2 py-1 border rounded-lg border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200 info-msg"
                        :title02="'Card view'" :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView">
                        <font-awesome-icon icon="fa-solid fa-check" class="pr-2 text-green-600" />
                        <font-awesome-icon icon="fa-solid fa-grip" />
                    </div>
                </div>
            </div>
            <!--Type of categories-->
            <div class="flex items-center justify-around">
                <button @click="serviceCategoryView"
                    :class="{ 'bg-green-600 text-white shadow-inner shadow-green-100 border border-green-600': !view_customer_categories, 'bg-neutral-100 shadow-inner shadow-neutral-100 border border-neutral-600': view_customer_categories, 'hover:bg-green-500': !view_customer_categories, 'hover:bg-neutral-100': view_customer_categories }"
                    class="rounded-lg text-sm lg:mr-5 sm:mr-2 mr-1 lg:px-3 sm:px-2 px-1 py-2">
                    <span class="mr-1">&#128736;</span> <span v-if="!isMobile"
                        class="sm:hidden lg:inline-block">Service</span>
                </button>
                <button @click="customerCategoryView"
                    :class="{ 'bg-neutral-100 shadow-inner shadow-neutral-100 border border-neutral-600': !view_customer_categories, 'bg-green-600 text-white shadow-inner shadow-green-100 border border-green-600': view_customer_categories, 'hover:bg-neutral-100': !view_customer_categories, 'hover:bg-green-500': view_customer_categories }"
                    class="rounded-lg text-sm lg:px-3 sm:px-2 px-1 py-2 mr-1">
                    <span class="mr-1">&#129333;</span> <span v-if="!isMobile"
                        class="sm:hidden lg:inline-block">Customer</span>
                </button>
            </div>
        </div>
        <!---load skeleton-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="items_category === 'list' ? 'grid' : 'table'">
        </skeleton>
        <!--View body-->
        <div v-if="!open_skeleton && data.length > 0" class="text-sm m-4" :class="{ 'm-4': !isMobile }">
            <div
                :class="{ 'sm:p-4 p-2 sm:py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                <!--list View-->
                <div class="py-2" v-if="items_category === 'list' && !open_skeleton">
                    <!--Service category-->
                    <div v-if="view_customer_categories === false"
                        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Render tile content for service categories -->
                        <div v-for="(record, index) in paginatedData" :key="record.id"
                            class="bg-white p-2 border border-gray-300 rounded shadow-lg">
                            <div class="mb-2">
                                <p class="text-md font-semibold cursor-pointer">
                                    {{ record.service_category }}
                                </p>
                                <p class="text-sm">Status: <span
                                        :class="{ 'text-green-600': record.service_status, 'text-red-600': !record.service_status }">
                                        {{ record.service_status === 1 ? 'Active' : 'De-Active' }}
                                    </span>
                                </p>
                            </div>
                            <div class="mt-2 flex justify-center">
                                <button @click="editRecord(record)" class="mr-3" title="Edit"
                                    :class="{ 'hidden': currentAMCCategory && currentAMCCategory.id && record.id == currentAMCCategory.id }">
                                    <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                </button>
                                <button @click="confirmDelete(index)" class="mr-3" title="Delete"
                                    :class="{ 'hidden': currentAMCCategory && currentAMCCategory.id && record.id == currentAMCCategory.id }">
                                    <font-awesome-icon icon="fa-regular fa-trash-can" style="color: #ef4444" />
                                </button>
                                <button
                                    class="flex items-center justify-center border border-green-600 hover:bg-blue-100 rounded pl-2 pr-2 py-1"
                                    @click="createFormFun(record)">
                                    <span class="text-green-600 font-semibold pr-2">
                                        {{ record.form && record.form.length !== 0 ? 'Edit Form' : 'Create Form' }}
                                    </span>
                                    <img :src="form_icon" alt="Form Icon" class="w-5 h-5" />
                                </button>
                            </div>
                        </div>
                        <div class="bg-white p-2 border rounded shadow-lg text-center flex flex-col justify-center items-center hover:bg-blue-50 cursor-pointer"
                            @click="openModal">
                            <img :src="service_add_group" alt="services" class="w-[30px] h-[30px] mb-1 mt-2"
                                style="z-index: 1;" />
                            <!-- <span class="text-md font-semibold cursor-pointer hover:text-green-600 text-black"> Add Category</span> -->
                        </div>
                    </div>
                    <!--customer category-->
                    <div v-if="view_customer_categories === true"
                        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                        <!-- Render tile content for customer categories -->
                        <div v-for="(record, index) in paginatedDataCustomer" :key="record.id"
                            class="bg-white p-2 border border-gray-300 rounded shadow-lg">
                            <div class="mb-2">
                                <p class="text-md font-semibold cursor-pointer">{{ record.category_name }}</p>
                                <p class="text-sm">Discount: {{ record.discount }} %</p>
                                <p class="text-sm">Status: <span
                                        :class="{ 'text-green-600': record.service_status, 'text-red-600': !record.service_status }">
                                        {{ record.service_status === 1 ? 'Active' : 'De-Active' }}
                                    </span>
                                </p>
                            </div>
                            <div class="mt-2">
                                <button @click="editRecord(record)" class="mr-3 mb-2" title="Edit">
                                    <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                </button>
                                <button @click="confirmDelete(index)" title="Delete">
                                    <font-awesome-icon icon="fa-regular fa-trash-can" style="color: #ef4444" />
                                </button>
                            </div>
                        </div>
                        <div class="bg-white p-2 border rounded shadow-lg text-center flex flex-col justify-center items-center hover:bg-blue-50 cursor-pointer"
                            @click="openModal">
                            <img :src="service_add_group" alt="services" class="w-[30px] h-[30px] mb-1 mt-2"
                                style="z-index: 1;" />
                            <!-- <span class="text-md font-semibold cursor-pointer hover:text-green-600 text-black"> Add Category</span> -->
                        </div>
                    </div>
                </div>

                <!--Table view-->
                <div class="mt-5" v-if="items_category === 'tile' && !open_skeleton">
                    <!--Service category-->
                    <div v-if="view_customer_categories === false"
                        class="table-container overflow-x-auto items-center justify-center flex">
                        <table class="table w-full bg-white">
                            <thead>
                                <tr class="table-head">
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center px-2 table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-36 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2"
                                                        :class="{ 'hidden': column.field === 'items' || column.field === 'payment_mode' || column.field === 'discount' || column.field === 'shipping' }">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in data" :key="record.id"
                                    class="hover:bg-gray-200 cursor-pointer">
                                    <td class="px-4 py-2 table-border" v-for="(column, index) in columns" :key="index"
                                        :class="{ 'hidden': !column.visible }" @click="createFormFun(record)">
                                        <span v-if="column.field !== 'service_status'">{{ record[column.field] }}</span>
                                        <span v-if="column.field === 'service_status'"
                                            :class="{ 'text-red-600': !record[column.field], 'text-green-600': record[column.field] }">{{
                                                record[column.field] ?
                                                    'Active' : 'De-Active' }}</span>
                                    </td>
                                    <!-- <td class="border px-4 py-2 sm:text-[14px] text-xs ">
                                <p class='font-semibold cursor-pointer'>
                                    {{ record.name }}
                                </p>
                            </td>
                            <td class="border px-4 py-2 sm:text-[14px] text-xs text-center">{{ record.status }}</td> -->
                                    <td
                                        class="flex px-4 py-2 sm:text-[14px] text-xs text-center justify-center items-center table-border">
                                        <button @click="editRecord(record)" class="mr-3" title="Edit"
                                            :class="{ 'hidden': currentAMCCategory && currentAMCCategory.id && record.id == currentAMCCategory.id }">
                                            <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                        </button>
                                        <button @click="confirmDelete(index)" class="mr-3" title="Delete"
                                            :class="{ 'hidden': currentAMCCategory && currentAMCCategory.id && record.id == currentAMCCategory.id }">
                                            <font-awesome-icon icon="fa-regular fa-trash-can" style="color: #ef4444" />
                                        </button>
                                        <button
                                            class="flex justify-center border border-green-600 rounded pl-1 pr-1 items-center"
                                            @click="createFormFun(record)">
                                            <span class="text-green-600 font-semibold pr-2">{{ record.form &&
                                                record.form.length
                                                !==
                                                0 ? 'Edit Form' : 'Create form' }}</span><img :src="form_icon"
                                                class="w-5 h-6" />
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4"
                                        class="bg-white p-2 table-border shadow-lg text-center justify-center items-center hover:bg-blue-50 cursor-pointer"
                                        @click="openModal">
                                        <div class=" flex justify-center items-center">
                                            <img :src="service_add_group" alt="services"
                                                class="w-[30px] h-[30px] mb-1 mt-2"
                                                style="z-index: 1; align-self: center;" />
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--customer category-->
                    <div v-if="view_customer_categories === true"
                        class="table-container overflow-x-auto items-center justify-center flex">
                        <table class="table w-full bg-white">
                            <thead>
                                <tr class="table-head">
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-36 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in data_customer" :key="record.id"
                                    class="hover:bg-gray-200 cursor-pointer">
                                    <td class="px-4 py-2 table-border" v-for="(column, index) in columns" :key="index"
                                        :class="{ 'hidden': !column.visible }" @click="editRecord(record)">
                                        <span v-if="column.field !== 'service_status'">{{ record[column.field] }}</span>
                                        <span v-if="column.field === 'service_status'"
                                            :class="{ 'text-red-600': record[column.field] == 0 || !record[column.field], 'text-green-600': record[column.field] == 1 }">{{
                                                record[column.field] == 1 ?
                                                    'Active' : 'De-Active' }}</span>
                                    </td>
                                    <!-- <td class="border px-4 py-2 sm:text-[14px] text-xs ">
                                <p class='font-semibold cursor-pointer'>
                                    {{ record.name }}
                                </p>
                            </td>
                            <td class="border px-4 py-2 sm:text-[14px] text-xs text-center ">{{ record.discount }} %</td>
                            <td class="border px-4 py-2 sm:text-[14px] text-xs text-center">{{ record.status }}</td> -->
                                    <td class="px-4 py-2 flex justify-center items-center table-border">
                                        <button @click="editRecord(record)" class="mr-2 px-1" title="Edit">
                                            <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                        </button>
                                        <button @click="confirmDelete(index)" title="Delete">
                                            <font-awesome-icon icon="fa-regular fa-trash-can" style="color: #ef4444" />
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4"
                                        class="bg-white p-2 table-border shadow-lg text-center justify-center items-center hover:bg-blue-50 cursor-pointer"
                                        @click="openModal">
                                        <div class=" flex justify-center items-center">
                                            <img :src="service_add_group" alt="services"
                                                class="w-[30px] h-[30px] mb-1 mt-2"
                                                style="z-index: 1; align-self: center;" />
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs" v-if="totalPages">
                        <p class="pagination-info">Page {{ currentPage }} of {{ totalPages }}</p>
                        <div class="mt-4">
                           <select v-model="recordsPerPage" @change="changePage"
                                class="mt-1 p-2 border border-gray-300 rounded pr-5 mr-3 mb-6">
                                <option v-for="option in options" :key="option" :value="option" class="text-xs">{{
                                    option }}
                                </option>
                            </select>
                            <label v-if="!isMobile" for="recordsPerPage">Records per page</label>
                        </div>

                        <ul class="flex list-none">
                            <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                    class="px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                    <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                    <span class="pl-1" v-if="!isMobile">Prev</span>
                                </button>
                            </li>
                            <li v-for="pageNumber in totalPages" :key="pageNumber">
                                <button @click="updatePage(pageNumber)"
                                    :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                    class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                        pageNumber
                                    }}</button>
                            </li>
                            <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                :class="{ 'bg-gray-500': currentPage === totalPages, 'bg-teal-600 hover:bg-teal-500': currentPage !== totalPages }">
                                <button @click="updatePage(currentPage + 1)" :disabled="currentPage === totalPages"
                                    class="px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                                    <span class="pr-1" v-if="!isMobile">Next</span>
                                    <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                </button>
                            </li>
                        </ul>
                    </div> -->
                </div>
            </div>
        </div>
        <!--in case empty-->
        <div v-if="data && data.length === 0">
            <div class="flex justify-center items-center">
                <img class="w-64 h-64" :src="empty_data" alt="image empty states">
            </div>
            <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
        </div>
        <addServiceCategory :showModal="showModal_category" @close-modal="closeModal" :editData="editData"
            :companyId="companyId">
        </addServiceCategory>
        <addCustomerCategory :show-modal="showModal_customer" @close-modal="closeModalCustomer" :companyId="companyId"
            :editData="customer_data">
        </addCustomerCategory>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'categories'"></bottombar> -->
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
    </div>
</template>

<script>
import addServiceCategory from '../../dialog_box/addServiceCategory.vue';
import addCustomerCategory from '../../dialog_box/addCustomerCategory.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import noAccessModel from '../../dialog_box/noAccessModel.vue';
// import bottombar from '../../dashboard/bottombar.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
    emits: ['updateIsOpen', 'dataToParent'],
    name: 'service_categories',
    components: {
        addServiceCategory,
        addCustomerCategory,
        confirmbox,
        dialogAlert,
        // bottombar
        noAccessModel
    },
    props: {
        searchedData: Object,
        updateModalOpen: Boolean,
    },
    data() {
        return {
            service_customer: '/images/service_page/Customer.png',
            service_add_group: '/images/service_page/Add_group.png',
            outline_img: '/images/service_page/Ellipse.png',
            table_view: '/images/service_page/tabler_eye.png',
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            customer_category_img: '/images/categories_page/customer.png',
            service_category_img: '/images/categories_page/vehicle.png',
            list_view: '/images/categories_page/list_view.png',
            tile_view: '/images/categories_page/tile_view.png',
            form_icon: '/images/categories_page/form_icon.png',
            filter_icon: '/images/customer_page/filter.png',
            setting_icon: '/images/customer_page/settings.png',
            info_icon: '/images/customer_page/info.png',
            refresh_icon: '/images/customer_page/refresh.png',
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            data: [],
            data_customer: [],
            showModal_category: false,
            editData: null,
            isMobile: false,
            view_customer_categories: false,
            showModal_customer: false,
            customer_data: null,
            open_confirmBox: false,
            deleteIndex: null,
            items_category: 'tile',
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataList: [],
            columns: [],
            message: '',
            open_message: false,
            originalData: [],
            //--api integration--
            companyId: null,
            userId: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 4,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---table data filter asending and decending----
            is_filter: false,
            pagination: {},
            //---no access---
            no_access: false,
        };
    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('createamcservice', ['currentAMCCategory', 'currenAMCData']),
        paginatedData() {
            if (this.items_category !== 'list') {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                return this.data.slice(startIndex, endIndex);
            } else {
                return this.data;
            }
        },
        paginatedDataCustomer() {
            const startIndex = (this.currentPage - 1) * this.recordsPerPage;
            const endIndex = startIndex + this.recordsPerPage;
            // console.log(this.data_customer.slice(startIndex, endIndex), 'Waht happening...!');
            return this.data_customer.slice(startIndex, endIndex);
        },
        totalPages() {
            console.log(this.data.length / this.recordsPerPage, 'RWRWRW', this.data.length, 'DdaDAADA', this.recordsPerPage);

            return Math.ceil(this.data.length / this.recordsPerPage);
        },
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (!this.view_customer_categories && this.data) {

                for (const key in this.data[0]) {
                    if (key !== 'id' && key !== 'form' && key !== 'data' && key !== 'style_view' && key !== 'company_id' && key !== 'created_at' && key !== 'updated_at' && key !== 'deleted_at') { // Exclude the 'id' field
                        const label = formatLabel(key);
                        fields.push({ label, field: key, visible: true });
                    }
                }
            } else if (this.view_customer_categories && this.data_customer) {
                for (const key in this.data_customer[0]) {
                    if (key !== 'id' && key !== 'company_id' && key !== 'created_at' && key !== 'updated_at' && key !== 'deleted_at') { // Exclude the 'id' field
                        const label = formatLabel(key);
                        fields.push({ label, field: key, visible: true });
                    }
                }
            }
            this.columns = fields;
            return fields;
        },
    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('createamcservice', ['resetFetchTime', 'fetchAMCCategory', 'createAMCCategory', 'updateAMCData', 'resetAMCData', 'getAMCData']),

        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.totalPages) {
                this.currentPage = pageNumber;
            }
        },
        editRecord(record) {
            if (this.view_customer_categories) {
                this.customer_data = record;
                this.showModal_customer = true;
            } else {
                if (this.getplanfeatures('service_category')) {
                    this.no_access = true;
                } else {
                    // console.log('hello');
                    this.editData = record;
                    this.showModal_category = true;
                }
            }
        },
        deleteRecord() {
            //----customer category--
            if (this.deleteIndex >= 0 && this.deleteIndex !== null) {
                this.open_loader = true;
                if (this.view_customer_categories) {
                    axios.delete(`/customer-categories/${this.data_customer[this.deleteIndex].id}`, { params: { company_id: this.companyId } })
                        .then(response => {
                            // console.log(response.data, 'deleted data');
                            this.updateKeyWithTime('service_category_update');
                            this.resetFetchTime();
                            this.message = 'Customer category deleted successfully...!';
                            this.show = true;
                            this.open_loader = false;
                            this.data_customer.splice(this.deleteIndex, 1);
                            this.deleteIndex = null;
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                        })
                    // this.data_customer.splice((this.recordsPerPage * (this.currentPage - 1)) + this.deleteIndex, 1);
                    // localStorage.setItem('customerCategory', JSON.stringify(this.data_customer));
                }
                else {
                    //--service category--
                    axios.delete(`/service_categories/${this.data[this.deleteIndex].id}`, { params: { company_id: this.companyId } })
                        .then(response => {
                            // console.log(response.data, 'deleted data');
                            this.message = 'Service category deleted successfully...!';
                            this.show = true;
                            this.open_loader = false;
                            this.data.splice(this.deleteIndex, 1);
                            this.deleteIndex = null;
                        })
                        .catch(error => {
                            console.error('Error', error);
                            if (error && error.response && error.response.data && error.response.data.message) {
                                this.message = error.response.data.message;
                                this.type_toaster = 'warning';
                                this.show = true;
                            }
                            this.open_loader = false;
                        })
                    // this.data.splice((this.recordsPerPage * (this.currentPage - 1)) + this.deleteIndex, 1);
                    // localStorage.setItem('CategoriesForm', JSON.stringify(this.data));
                }
            }
            this.open_confirmBox = false;
        },
        confirmDelete(index) {
            if (this.getplanfeatures('service_category')) {
                this.no_access = true;
            } else {
                // console.log(index, 'What happening...');
                this.deleteIndex = index;
                this.open_confirmBox = true;
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        openModal() {
            if (this.view_customer_categories) {
                this.customer_data = null;
                this.showModal_customer = true;
            }
            else {
                this.editData = null;
                this.showModal_category = true;
            }
        },
        closeModal(data) {
            if (data) {
                this.fetchApiUpdates();
                this.fetchServiceCategoryList();
                let findExist = this.data.findIndex((opt) => opt.id === data.id);
                if (findExist !== -1) {
                    this.message = 'Service category updated successfully...!'
                    this.show = true;
                    this.data[findExist].service_category = data.service_category;
                    this.data[findExist].service_status = data.service_status;
                } else {
                    this.message = 'Service category created successfully...!'
                    this.show = true;
                    this.data.push(data);
                }
                this.showModal_category = false;
            } else {
                this.fetchServiceCategoryList();
                this.showModal_category = false;
            }
        },
        closeModalCustomer(data) {
            // console.log(data, 'In customer list');
            if (data) {
                let findExist = this.data_customer.findIndex((opt) => opt.id === data.id);
                if (findExist !== -1) {
                    this.message = 'Customer category updated successfully...!'
                    this.show = true;
                    this.data_customer[findExist] = data;
                } else {
                    this.message = 'Customer category created successfully...!'
                    this.show = true;
                    this.data_customer.push(data);
                }
            }
            this.showModal_customer = false;
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        customerCategoryView() {
            this.view_customer_categories = true;
        },
        serviceCategoryView() {
            this.view_customer_categories = false;
        },
        createFormFun(record) {
            if (this.getplanfeatures('service_category')) {
                this.no_access = true;
            } else {
                this.$router.push({ name: 'service_category_create_form', params: { type: record.service_category, serviceId: record.id } });
            }
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            try {
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        // closeFilterModal() {
        //     this.showFilterModal = false;
        //     this.resetFilter();
        // },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // Implement logic to filter data based on filter values
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //--get category---
        getCategoryData() {
            axios.get('/customer-categories', { company_id: this.companyId, page: 1, per_page: 'all' })
                .then(response => {
                    console.log(response.data, 'Customer gatgory get..!');
                    return response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---post category---
        postCategoryData(value) {
            axios.post('/customer-categories', value)
                .then(response => {
                    console.log(response.data.data, 'Post category data..!');
                    return response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---service category---
        serviceCategoryList() {
            this.open_skeleton = true;
            // axios.get('/service_categories', { params: { company_id: this.companyId } })
            //     .then(response => {
            //         console.log(response.data, 'service gatgory get..!');
            //         this.open_skeleton = false;
            //         this.data = response.data.data;
            //         this.$emit('dataToParent', this.data);
            //     })
            //     .catch(error => {
            //         console.error('Error:', error);
            //     })
            if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                this.open_skeleton = false;
                this.data = this.currentServiceCategory;
                this.$emit('dataToParent', this.data);
            }
        },
        //---customer category---
        customerCategoryList() {
            this.open_skeleton = true;
            axios.get('/customer-categories', { params: { company_id: this.companyId } })
                .then(response => {
                    this.open_skeleton = false;
                    this.data_customer = response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        filterDataBy(type, key) {
            const type_page = this.view_customer_categories ? 'customer_category' : 'service_category';
            // console.log(type_page, 'what about the page data....!', type, 'TYpe', this.is_filter);
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === type_page && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        if (this.view_customer_categories) {
                            this.data_customer = [...this.originalDataFilter]
                        } else {
                            this.data = [...this.originalDataFilter];
                        }
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.view_customer_categories ? this.data_customer : this.data, type: type_page, filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === type_page && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        if (this.view_customer_categories) {
                            this.data_customer = [...this.originalDataFilter]
                        } else {
                            this.data = [...this.originalDataFilter];
                        }
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.view_customer_categories ? this.data_customer : this.data, type: type_page, filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        closeAllModal() {
            this.showModal_category = false;
            this.showModal_customer = false;
            this.open_confirmBox = false;
            this.open_message = false;
            this.open_loader = false;
        },
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        }
    },
    watch: {
        searchedData(newValue) {
            if (!this.isEmptyObject(newValue)) {
                if (this.view_customer_categories) {
                    this.data_customer = [{ ...newValue }];
                } else {
                    this.data = [{ ...newValue }];
                }
            }
            else {
                if (this.view_customer_categories) {
                    // let customer_data = localStorage.getItem('customerCategory');
                    // if (customer_data) {
                    //     let parseData = JSON.parse(customer_data);
                    //     if (parseData) {
                    //         this.data_customer = parseData;
                    //     }
                    // }
                    this.customerCategoryList();
                } else {
                    // const storedData = localStorage.getItem('CategoriesForm');
                    // if (storedData) {
                    //     let dataParse = JSON.parse(storedData);
                    //     // console.log(dataParse, 'WWWWWWWWWWWW');
                    //     this.data = dataParse;
                    // }
                    this.serviceCategoryList();
                }
            }
        },
        view_customer_categories(newValue) {
            // console.log(newValue, 'UUUUUU', this.data_customer);
            if (newValue) {
                this.$emit('dataToParent', this.data_customer);
                //---sortIcons---
                const initialShortVisible = ['category_name', 'service_status', 'discount'].map((field) => ({
                    label: field,
                    visible: false,
                    type: ''
                }));
                this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
            } else {
                this.$emit('dataToParent', this.data);
                const initialShortVisible = ['service_category', 'service_status', 'services_count'].map((field) => ({
                    label: field,
                    visible: false,
                    type: ''
                }));
                this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = 20;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('category_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.open_skeleton = false;
                    this.data = newValue;
                    this.$emit('dataToParent', this.data);
                }
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    if (this.view_customer_categories) {
                        this.data_customer = [...newValue]
                    } else {
                        this.data = [...newValue];
                    }
                    this.is_filter = false;
                }
            }
        },
        //---modal is open--
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModal();
            }
        },
        showModal_category: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showModal_customer: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_loader: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        const collectForm01 = localStorage.getItem('track_new');
        if (collectForm01) {
            let dataParse = JSON.parse(collectForm01);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        const view = localStorage.getItem('category_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        this.updateIsMobile(); // Initial check
        if (this.isMobile) {
            this.items_category = 'list';
        }
        window.addEventListener('resize', this.updateIsMobile);
        if (this.currentServiceCategory && this.currentServiceCategory.length === 0) {
            this.fetchServiceCategoryList();
        } else {
            this.fetchServiceCategoryList()
        }
        if (!this.currentAMCCategory || Object.keys(this.currentAMCCategory).length == 0) {
            this.fetchAMCCategory();
        }
        //---service category--
        this.serviceCategoryList();
        //---customer category--
        this.customerCategoryList();
        const initialShortVisible = ['service_category', 'service_status', 'services_count'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
}
</script>

<style scoped>
@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
    }
}
</style>
