import axios from "axios";

const state = {
    estimation_rec: {},
};

const mutations = {
  
  EST_RECORD(state, record) {
        state.estimation_rec = record;
  },
  RESET_STATE(state) {
      state.estimation_rec = {};
  },
};

const actions = { 
  getEstimation({ commit }, id) {    
    try {
      const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
      if (company_id && company_id !== '' && id) {        
        axios.get(`/estimations/${id}`, { params: { company_id:  company_id } })
                .then(response => {
                    // console.log(response.data, 'estimation by edit..!');
                    commit('EST_RECORD', response.data.data);
                    return;
                })
                .catch(error => {
                    console.error('Error', error);
                })
      }
      } catch (error) {
        console.error('Error fetching item list:', error);
      }        
  },
  updateEstimation({ commit }, data) {
    if (data) {
      commit('EST_RECORD', data);
    }
  }
};

const getters = {
    estimationclone(state) {
        return state.estimation_rec
    }
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
