<template>
    <div v-if="!open_skeleton" class="m-1 sm:m-3 md:m-4 lg:m-5 sm:text-sm text-xs"
        :class="{ 'blurred-content': isBlur }">
        <!---welcome message-->
        <div class="mb-2 lg:-mt-3">
            <p class="font-bold text-lg pl-5">
                Hi,
                <span style='font-size:24px;'>&#128522; </span>
                <span v-if="getData && getData.company_name && getData.user_type && getData.user_type !== 'employee'"
                    class="py-2 text-center font-bold">
                    {{ getData.company_name }}
                </span>
                <span v-else>{{ getData && getData.name ? getData.name : getData && getData.company_id ? 'Employee' :
                    'User'
                    }}</span>
            </p>
        </div>
        <div class="p-2">
            <!-- bg-white rounded-[20px] shadow border <div class="mb-5">
                <p class="font-bold text-xl">Overview</p>
                <p class="text-gray-400">Summary</p>
            </div> -->
            <div>
                <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-4">
                    <div v-for="(stat, index) in sortedData" :key="stat.id" @click="navigattoHome(stat)"
                        :class="{ 'hidden': !fetchstatusData(stat.id) || checkRolesValidate(stat.id) }">
                        <!--class="shadow-xl px-2 py-2 rounded rounded-xl border cursor-pointer" :class="{ 'bg-yellow-100': index === 0, 'bg-blue-100': index === 1, 'bg-green-100': index === 2, 'bg-red-100': index === 3 }"-->
                        <card :title="stat.id" :total="stat.total" :complete="stat.complete" :pending="stat.progress"
                            :index_num="index"
                            :color="stat.id === 'Services' ? 'bg-blue-500' : stat.id === 'Leads' ? 'bg-green-500' : stat.id === 'Amc' ? 'bg-orange-500' : stat.id === `Rma's` ? 'bg-pink-500' : stat.id === 'Sales' ? 'bg-lime-500' : stat.id === `Proforma's` ? 'bg-yellow-600' : stat.id === 'Estimations' ? 'bg-sky-500' : 'bg-violet-600'"
                            :view_color="stat.id === 'Services' ? 'bg-blue-700' : stat.id === 'Leads' ? 'bg-green-700' : stat.id === 'Amc' ? 'bg-orange-700' : stat.id === `Rma's` ? 'bg-pink-700' : stat.id === 'Sales' ? 'bg-lime-700' : stat.id === `Proforma's` ? 'bg-yellow-800' : stat.id === 'Estimations' ? 'bg-sky-700' : 'bg-violet-800'"
                            :img_data="index === 0 ? dashboard_img : index === 1 ? leads_img : index === 2 ? amc_img : estimation_img"
                            :currentFeatureList="currentFeatureList" :currentCompanyList="currentCompanyList" />
                        <!--  <div class=" grid grid-cols-2 gap-4 text-xl font-bold tracking-tight text-gray-900 sm:text-2xl">
                            <p>{{ stat.id }}</p>
                            <p class="text-center">{{ stat.total }}</p>
                        </div>
                       <div class="grid grid-cols-2 gap-4  order-first text-lg font-semibold tracking-tight text-gray-900 sm:text-xl">
                            <p>Total:</p>
                            <p></p>
                        </div> 
                        <div class="grid grid-cols-2 gap-4 text-base leading-7 text-gray-600 text-center">
                            <p>Completed:</p>
                            <p>{{ stat.complete }}</p>
                        </div>
                        <div class="grid grid-cols-2 gap-4 text-base leading-7 text-gray-600 text-center">
                            <p>Pending:</p>
                            <p>{{ stat.progress }}</p>
                        </div>-->
                    </div>
                </div>
            </div>
        </div>
        <!---chart data-->
        <div class="p-2 items-center relative">
            <div>
                <h2 class="text-sm font-bold">Incoming (last
                    <span>
                        <select v-model="days_counts" class="border border-gray-600 px-1"
                            @change="getChartData(days_counts)">
                            <option v-for="n in 45" :key="n" :value="n">{{ n }}</option>
                        </select>
                    </span>
                    days)
                </h2>
            </div>
            <!-- Loader -->
            <div v-if="circle_loader" class="flex justify-center items-center">
                <CircleLoader :loading="true"></CircleLoader>
            </div>
            <chartView :datasets="datasets" :labels="labels_chart" />
        </div>
        <!---Services table and chart view-->
        <div class="p-2 mb-5" v-if="dashboard_data && !open_skeleton">
            <servicesDesign
                :employerList="dashboard_data.assign_service_list && dashboard_data.assign_service_list.length > 0 ? dashboard_data.assign_service_list : []"
                :serviceStatus="dashboard_data.service_statics ? dashboard_data.service_statics : []"></servicesDesign>
        </div>
        <!--amc list data-->
        <!-- <div class="p-2 mb-5" v-if="!open_skeleton">
            <amcData></amcData>
        </div> -->
        <!--second data-->
        <div v-if="!isPad" class="px-2 bg-gray-200">
            <ul class="flex flex-wrap text-sm font-medium text-center text-gray-500">
                <li v-if="currentFeatureList && currentFeatureList.length > 0"
                    v-for="(feature, index) in table_view_list" class="cursor-pointer"
                    :class="{ 'hidden': !feature.hasAccess || checkRolesValidate(feature.name) || checkPlanDetails(feature.name) }"
                    @click="view_type = feature.name">
                    <span class="inline-block py-3 px-4" :class="{
                        'hover:text-blue-600 hover:bg-blue-100': feature.hasAccess,
                        'bg-white': view_type == feature.name
                    }" :style="{
                        borderTop: view_type == feature.name ? `4px solid ${getColor(feature.color, '600')}` : '',
                        color: feature.hasAccess ? getColor(feature.color, '600') : ''
                    }">
                        <font-awesome-icon :icon="feature.icon" class="px-1 items-center" />
                        {{ feature.name }}
                    </span>
                </li>
            </ul>
        </div>
        <div v-if="!isPad" class="grid grid-cols-1 gap-4">
            <tableViewData :view_type="view_type" :stats="stats" :isPad="isPad" :table_view_list="table_view_list"
                :service_status="service_status" :rma_status="rma_status" :currentCompanyList="currentCompanyList">
            </tableViewData>
        </div>
        <!----new design-->
        <!-- <div class="container mx-auto p-4">
            <div class="flex flex-wrap -mx-4 mb-6">
                <card title="48" description="Unfinished" color="bg-blue-500" />
                <card title="2" description="My completed Last 30 days" color="bg-green-500" />
                <card title="11" description="My unfinished" color="bg-orange-500" />
                <card title="36" description="Outstanding" color="bg-pink-500" />
            </div>
            <div>
                <h2 class="text-xl font-bold mb-4">Incoming repairs (last 30 days)</h2>
                <chartView />
            </div>
        </div> -->
        <!-- <calenderdesign></calenderdesign> -->
    </div>
    <!--loader-->
    <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
        :rows="number_of_rows" :gap="gap" :type="'grid'">
    </skeleton>
    <!-- <MyComponent :chart="myDynamicValue" /> -->
    <!-- <bottombar v-if="isMobile" :selected_btn_btm="'home'"></bottombar> -->
</template>
<script>
import axios from 'axios';
import skeleton from '../skeleton.vue';
import chart from '../dialog_box/chart.vue';
// import bottombar from './bottombar.vue';
import card from './designs/card.vue';
import chartView from './designs/chartView.vue';
import { mapActions, mapGetters } from 'vuex';
// import calenderdesign from './calenderdesign.vue';
import tableViewData from '../dashboard/designs/tableViewData.vue';
import servicesDesign from './designs/servicesDesign.vue';
import Skeleton from '../skeleton.vue';
//---amc list--
import amcData from './designs/amcData.vue';
export default {
    components: {
        skeleton,
        chart,
        // bottombar,
        card,
        chartView,
        // calenderdesign,
        tableViewData,
        servicesDesign,
        amcData
    },
    props: {
        isMobile: Boolean,
        isBlur: Boolean
    },
    data() {
        return {
            //---new images--
            dashboard_img: '/images/side_bar/Clock.png',
            leads_img: '/images/side_bar/Leads.png',
            amc_img: '/images/side_bar/amc.png',
            estimation_img: '/images/side_bar/quote.png',
            //--api integration---
            companyId: null,
            userId: null,
            dashboard_data: [],
            getData: null,
            stats: [],
            services_data: [],
            leads_data: [],
            amc_data: [],
            service_overview: {},
            statics: [],
            service_status: ['service taken', 'hold', 'in-progress', 'new estimate', 'ready to delivered', 'delivered', 'cancelled', 'completed'],
            slices: [
                { color: 'blue', label: 'Blue', value: 30 },
                { color: 'green', label: 'Green', value: 20 },
                { color: 'yellow', label: 'Yellow', value: 25 },
                { color: 'red', label: 'Red', value: 25 }
            ],
            value: 10,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 4,
            number_of_rows: 20,
            gap: 5,
            isPad: false,
            //---bar chart data--
            days_counts: 10,
            datasets: [
                {
                    label: 'Services',
                    backgroundColor: 'rgba(66, 133, 244, 0.2)',
                    borderColor: '#4285F4',
                    pointBackgroundColor: '#4285F4',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#4285F4',
                    fill: false,
                    tension: 0.4, // Adding curvature to the line
                    pointRadius: 5, // Increase point size
                    pointHoverRadius: 7, // Increase point size on hover
                    borderWidth: 2, // Reduce line width
                    data: [],
                    id: 1
                },
                {
                    label: 'Leads',
                    backgroundColor: 'rgba(16, 184, 5, 0.2)',
                    borderColor: '#10B805',
                    pointBackgroundColor: '#10B805',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#10B805',
                    fill: false,
                    tension: 0.4, // Adding curvature to the line
                    pointRadius: 5, // Increase point size
                    pointHoverRadius: 7, // Increase point size on hover
                    borderWidth: 2, // Reduce line width
                    data: [],
                    id: 2
                },
                {
                    label: 'AMC',
                    backgroundColor: 'rgba(255, 165, 0, 0.2)',
                    borderColor: '#FFA500',
                    pointBackgroundColor: '#FFA500',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#FFA500',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    borderWidth: 2,
                    data: [],
                    id: 3
                },
                {
                    label: 'RMA',
                    backgroundColor: 'rgba(255, 165, 0, 0.2)',
                    borderColor: '#DE3163',
                    pointBackgroundColor: '#DE3163',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#DE3163',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    borderWidth: 2,
                    data: [],
                    id: 4
                },
                {
                    label: 'Sales',
                    backgroundColor: 'rgba(54, 83, 20, 0.2)',
                    borderColor: ' #365314',
                    pointBackgroundColor: ' #365314',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: ' #365314',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    borderWidth: 2,
                    data: [],
                    id: 5
                },
                {
                    label: 'Proforma',
                    backgroundColor: 'rgba(133, 77, 14, 0.2)',
                    borderColor: '#854d0e',
                    pointBackgroundColor: '#854d0e',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#854d0e',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    borderWidth: 2,
                    data: [],
                    id: 6
                },
                {
                    label: 'Estimation',
                    backgroundColor: 'rgba(7, 89, 133, 0.2)',
                    borderColor: '#075985',
                    pointBackgroundColor: '#075985',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#075985',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    borderWidth: 2,
                    data: [],
                    id: 7
                },
                {
                    label: 'Expense',
                    backgroundColor: 'rgba(91, 33, 182, 0.2)',
                    borderColor: '#5b21b6',
                    pointBackgroundColor: '#5b21b6',
                    pointBorderColor: '#fff',
                    pointHoverBackgroundColor: '#fff',
                    pointHoverBorderColor: '#5b21b6',
                    fill: false,
                    tension: 0.4,
                    pointRadius: 5,
                    pointHoverRadius: 7,
                    borderWidth: 2,
                    data: [],
                    id: 8
                },
            ],
            labels_chart: [],
            circle_loader: false,
            view_type: '',
            table_view_list: [{ id: 1, icon: 'fa-solid fa-wrench', color: 'blue' }, { id: 2, icon: 'fas fa-address-book', color: 'green' },
            { id: 3, icon: 'fas fa-calendar-check', color: 'orange' }, { id: 4, icon: 'fas fa-exchange-alt', color: 'rose' }, { id: 5, icon: 'fas fa-shopping-cart', color: 'lime' },
            { id: 6, icon: 'fas fa-file-alt', color: 'yellow' }, { id: 7, icon: 'fas fa-calculator', color: 'sky' }, { id: 8, icon: 'fas fa-money-bill-wave', color: 'violet' }],
            desiredOrder: ['Services', 'Leads', 'Amc', 'Rma\'s', 'Sales', 'Proforma\'s', 'Estimations', 'Expenses'],
            rma_status: [
                { value: 1, label: 'Awaiting Customer Confirmation', class: 'bg-green-500', labelClass: 'ml-2' },
                { value: 2, label: 'Awaiting Parts', class: 'bg-purple-500 text-white', labelClass: 'ml-2' },
                { value: 3, label: 'Awaiting Repair', class: 'bg-orange-500', labelClass: 'ml-2' },
                { value: 4, label: 'Awaiting Supplier', class: 'bg-[#ffb6c1]', labelClass: 'ml-2' },
                { value: 5, label: 'Awaiting to be sent to Supplier', class: 'bg-indigo-500 text-white', labelClass: 'ml-2' },
                { value: 6, label: 'Credit', class: 'bg-gray-300', labelClass: 'ml-2' },
                { value: 7, label: 'Ready to Deliver', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 8, label: 'Repair Completed', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 9, label: 'Repair in Process', class: 'bg-[#87b14b]', labelClass: 'ml-2' },
                { value: 10, label: 'Repaired/Replacement from Supplier', class: 'bg-[#ff7f50]', labelClass: 'ml-2' },
                { value: 11, label: 'Sent to Customer', class: 'bg-blue-600 text-white', labelClass: 'ml-2' },
                { value: 12, label: 'Sent to Supplier', class: 'bg-[#ffc0cb]', labelClass: 'ml-2' },
                { value: 13, label: 'Waiting New Battery', class: 'bg-red-600 text-white', labelClass: 'ml-2' },
                { value: 14, label: 'Delivered', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 15, label: 'Cancel', class: 'bg-red-600 text-white', labelClass: 'ml-2' },
            ],
        };
    },
    computed: {
        ...mapGetters('dashboardData', ['currentDashboard']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('features_list', ['currentFeatureList']),
        ...mapGetters('companies', ['currentCompanyList']),
        yLabels() {
            if (this.service_overview && Object.keys(this.service_overview).length > 0) {
                const maxServices = Math.max(...Object.values(this.service_overview).map(monthData => monthData.total_services));
                const labels = [];
                for (let i = 0; i <= 100; i += 25) {
                    labels.push(i);
                }
                return labels;
            }
        },
        sortedData() {
            if (this.stats && this.stats.length > 0) {
                // Create a map for quick lookup of indices in the desired order
                const orderMap = this.desiredOrder.reduce((map, id, index) => {
                    map[id.toLowerCase()] = index; // Convert to lowercase for case-insensitive comparison
                    return map;
                }, {});

                // Sort the data according to the desired order
                return this.stats.slice().sort((a, b) => {
                    const aIndex = orderMap[a.id.toLowerCase()];
                    const bIndex = orderMap[b.id.toLowerCase()];

                    if (aIndex === undefined) return 1; // Place undefined IDs at the end
                    if (bIndex === undefined) return -1;

                    return aIndex - bIndex;
                });
            }
            return this.stats; // Return original stats if no sorting is needed
        }
    },

    methods: {
        ...mapActions('dashboardData', ['fetchDashboardDataList']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('features_list', ['fetchFeatureList']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),

        getDashboard() {
            this.open_skeleton = true;
            axios.get('/dashboard-values')
                .then(response => {
                    // console.log(response.data, 'Dashboard ....');
                    this.open_skeleton = false;
                    this.dashboard_data = response.data;
                    this.stats = this.dashboard_data.overview;
                    // this.services_data = this.dashboard_data.recent_services;
                    // console.log(this.services_data, 'RRRR');
                    // this.leads_data = this.dashboard_data.recent_leads;
                    // this.amc_data = this.dashboard_data.recent_amcs;
                    this.statics = this.dashboard_data.service_statics;
                    this.service_overview = this.dashboard_data.sales_barchart;
                    //---update table data---
                    if (this.table_view_list && this.table_view_list.length > 0) {
                        this.table_view_list[0].data = this.dashboard_data.recent_services;
                        this.table_view_list[1].data = this.dashboard_data.recent_leads;
                        this.table_view_list[2].data = this.dashboard_data.recent_amcs;
                        this.table_view_list[3].data = this.dashboard_data.recent_rmas;
                        this.table_view_list[4].data = this.dashboard_data.recent_sales;
                        this.table_view_list[5].data = this.dashboard_data.recent_proforma;
                        this.table_view_list[6].data = this.dashboard_data.recent_estimations;
                        this.table_view_list[7].data = this.dashboard_data.recent_expenses;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        calculatePercentage(totalServices) {
            return totalServices;
        },
        updateIsPad() {
            this.isPad = window.innerWidth < 768;
        },
        //---initial store data
        getInitialStoreData(store_data) {
            this.open_skeleton = false;
            this.dashboard_data = store_data;
            this.stats = this.dashboard_data.overview;
            // this.services_data = this.dashboard_data.recent_services;
            // this.leads_data = this.dashboard_data.recent_leads;
            // this.amc_data = this.dashboard_data.recent_amcs;
            this.statics = this.dashboard_data.service_statics;
            this.service_overview = this.dashboard_data.sales_barchart;
            //---update table data---
            if (this.table_view_list && this.table_view_list.length > 0) {
                this.table_view_list[0].data = this.dashboard_data.recent_services;
                this.table_view_list[1].data = this.dashboard_data.recent_leads;
                this.table_view_list[2].data = this.dashboard_data.recent_amcs;
                this.table_view_list[3].data = this.dashboard_data.recent_rmas;
                this.table_view_list[4].data = this.dashboard_data.recent_sales;
                this.table_view_list[5].data = this.dashboard_data.recent_proforma;
                this.table_view_list[6].data = this.dashboard_data.recent_estimations;
                this.table_view_list[7].data = this.dashboard_data.recent_expenses;
            }
        },


        //---get chart_data
        getChartData(days_count) {
            this.circle_loader = true;
            axios.get('dashboard/chart-data', { params: { day_count: days_count } })
                .then(response => {
                    if (response.data.labels) {
                        this.labels_chart = response.data.labels;
                    }
                    if (response.data.service_counts && this.datasets[0] && this.datasets[0].data) {
                        this.datasets[0].data = response.data.service_counts;
                    }
                    if (response.data.leads_counts && this.datasets[1] && this.datasets[1].data) {
                        this.datasets[1].data = response.data.leads_counts;
                    }
                    if (response.data.amcs_counts && this.datasets[2] && this.datasets[2].data) {
                        this.datasets[2].data = response.data.amcs_counts;
                    }
                    if (response.data.rmas_counts && this.datasets[3] && this.datasets[3].data) {
                        this.datasets[3].data = response.data.rmas_counts;
                    }
                    if (response.data.sales_counts && this.datasets[4] && this.datasets[4].data) {
                        this.datasets[4].data = response.data.sales_counts;
                    }
                    if (response.data.proformas_counts && this.datasets[5] && this.datasets[5].data) {
                        this.datasets[5].data = response.data.proformas_counts;
                    }
                    if (response.data.estimations_counts && this.datasets[6] && this.datasets[6].data) {
                        this.datasets[6].data = response.data.estimations_counts;
                    }
                    if (response.data.expenses_counts && this.datasets[7] && this.datasets[7].data) {
                        this.datasets[7].data = response.data.expenses_counts;
                    }
                    this.circle_loader = false;
                })
                .catch(error => {
                    this.circle_loader = false;
                    console.error('Error', error);
                })
        },
        //---get filter data---
        fetchIcons(id) {
            let find_data = this.currentFeatureList.find(opt => opt.id === id);
            if (find_data) {
                return find_data;
            }
        },
        //---status data--
        fetchstatusData(match) {
            if (this.currentFeatureList && this.currentFeatureList.length > 0 && match) {
                let currentPlan = this.currentCompanyList && this.currentCompanyList.plans ? this.currentCompanyList.plans : null;

                switch (match) {
                    case 'Services': {
                        return this.currentFeatureList[0].hasAccess && !(currentPlan && [12, 13].includes(currentPlan.id));
                    }
                    case 'Leads': {
                        return this.currentFeatureList[1].hasAccess && !(currentPlan && [12, 13].includes(currentPlan.id));
                    }
                    case `Amc`: {
                        return this.currentFeatureList[2].hasAccess && !(currentPlan && [12, 13].includes(currentPlan.id));
                    }
                    case `Rma's`: {
                        return this.currentFeatureList[3].hasAccess && !(currentPlan && [12, 13].includes(currentPlan.id));
                    }
                    case 'Sales': {
                        return this.currentFeatureList[4].hasAccess;
                    }
                    case `Proforma's`: {
                        return this.currentFeatureList[5].hasAccess;
                    }
                    case 'Estimations': {
                        return this.currentFeatureList[6].hasAccess;
                    }
                    case 'Expenses': {
                        return this.currentFeatureList[7].hasAccess;
                    }
                    default: {
                        break;
                    }
                }
            }
        },
        mergeIconColor() {
            let status_data = ['Services', 'Leads', `Amcs`, `Rma's`, 'Sales', `Proforma's`, 'Estimations', 'Expenses'];
            if (this.currentFeatureList && this.currentFeatureList.length > 0 && this.table_view_list && this.table_view_list.length > 0) {
                this.table_view_list.forEach((opt, index) => {
                    let options = this.fetchIcons(opt.id);
                    let status_return = this.fetchstatusData(status_data[index]);
                    this.table_view_list.splice(index, 1, { ...opt, ...options });
                });
            }
        },
        //---get color---
        getColor(color, shade) {
            // Define your color mapping here
            const colors = {
                red: {
                    '600': '#dc2626',
                    '200': '#fee2e2'
                },
                green: {
                    '600': '#16a34a',
                    '200': '#d9f99d'
                },
                blue: {
                    '600': '#2563eb',
                    '200': '#bfdbfe'
                },
                orange: {
                    '600': '#ea580c',
                    '200': '#fed7aa'
                },
                yellow: {
                    '600': '#ca8a04',
                    '200': '#fef08a'
                },
                lime: {
                    '600': '#65a30d',
                    '200': '#d9f99d'
                },
                rose: {
                    '600': '#e11d48',
                    '200': '#fecdd3'
                },
                violet: {
                    '600': '#7c3aed',
                    '200': '#ddd6fe'
                },
                sky: {
                    '600': '#0284c7',
                    '200': '#bae6fd'
                },
            };
            return colors[color] && colors[color][shade] ? colors[color][shade] : '#000000'; // Fallback color
        },
        navigattoHome(rec) {
            switch (rec.id) {
                case 'Services': {
                    this.$router.push('/services');
                    break;
                }
                case 'Leads': {
                    this.$router.push('/leads');
                    break;
                }
                case `Amc`: {
                    this.$router.push('/amc');
                    break;
                }
                case `Rma's`: {
                    this.$router.push('/openrma');
                    break;
                }
                case 'Sales': {
                    this.$router.push('/sales');
                    break;
                }
                case `Proforma's`: {
                    this.$router.push('/proforma');
                    break;
                }
                case 'Estimations': {
                    this.$router.push('/estimation');
                    break;
                }
                case 'Expenses': {
                    this.$router.push('/expense');
                    break;
                }
                default: {
                    break;
                }
            }
        },
        //--validate roles data-----
        checkRolesValidate(title) {
            // console.log(this.currentLocalDataList.roles[0], this.currentLocalDataList.roles[0].toLowerCase() == 'Service Engineer'.toLowerCase());
            // console.log(title, 'What about data...!');
            if (this.currentLocalDataList.roles && this.currentLocalDataList.roles.length > 0 && this.currentLocalDataList.roles[0]) {
                let role = this.currentLocalDataList.roles[0];
                switch (role) {
                    case 'admin':
                        return false;
                    case 'Sub_Admin':
                        return false;
                    case 'Account Manager':
                        return false;
                    case 'Sales man':
                        return false;
                    case 'Service Manager':
                        return false;
                    case 'Service Engineer':
                        if (title == `Rma's` || title === 'Sales' || title === `Proforma's` || title === `Estimations` || title === `Expenses` || title == `RMA` || title === `Proforma` || title === `Estimate` || title === `Expense`) {
                            return true;
                        } else {
                            return false;
                        }
                    default:
                        break;
                }
            } else {
                return false
            }
        },
        filterdataSets() {
            if (this.currentLocalDataList.roles && this.currentLocalDataList.roles.length > 0 && this.currentLocalDataList.roles[0]) {
                let currentPlan = this.currentCompanyList && this.currentCompanyList.plans ? this.currentCompanyList.plans : null;
                if (currentPlan && [12, 13].includes(currentPlan.id)) {
                    const excludedLabels = ['Services', 'Leads', 'AMC', 'RMA'];
                    this.datasets = this.datasets.filter(opt => !excludedLabels.includes(opt.label));
                } else {
                    if (this.currentLocalDataList.roles[0] === 'Service Engineer') {
                        const excludedLabels = ['RMA', 'Sales', 'Proforma', 'Estimation', 'Expense'];
                        this.datasets = this.datasets.filter(opt => !excludedLabels.includes(opt.label));
                    }
                }
            }
        },
        checkPlanDetails(option) {
            let currentPlan = this.currentCompanyList && this.currentCompanyList.plans ? this.currentCompanyList.plans : null;
            if (['Services', 'Leads', 'AMC', 'RMA'].includes(option) && currentPlan && [12, 13].includes(currentPlan.id)) {
                return true;
            } else { return false; }
        }
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.getData = dataParse;
            // console.log(this.getData, 'EEEEE');
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsPad();
        this.fetchFeatureList();
        this.fetchCompanyList();
        if (this.currentFeatureList && Object.keys(this.currentFeatureList).length > 0) {
            this.mergeIconColor();
            //---label chart
            this.datasets = this.datasets.filter((opt, index) => this.fetchIcons(opt.id) && this.fetchIcons(opt.id).hasAccess === true);
        }
        //---get today dashboard---
        if (this.dashboard_data.length === 0) {
            // this.getDashboard();
            if (this.currentDashboard && Object.keys(this.currentDashboard).length > 0) {
                this.open_skeleton = true;
                this.getInitialStoreData(this.currentDashboard);
                this.fetchDashboardDataList();
                this.getChartData(this.days_counts);
            } else {
                this.open_skeleton = true;
                this.fetchDashboardDataList();
                this.getChartData(this.days_counts);
            }
        }
        window.addEventListener('resize', this.updateIsPad);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsPad);
    },
    watch: {
        currentDashboard: {
            deep: true,
            handler(newValue) {
                if (newValue && Object.keys(newValue).length > 0) {
                    this.getInitialStoreData(newValue);
                }
            }
        },
        currentFeatureList: {
            deep: true,
            handler(newValue) {
                this.mergeIconColor();
                //---label chart
                this.datasets = this.datasets.filter((opt, index) => this.fetchIcons(opt.id) && this.fetchIcons(opt.id).hasAccess === true);
            }
        },
        table_view_list: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    let find_hasaccess = newValue.find(opt => opt.hasAccess === true);
                    // console.log(find_hasaccess, 'EEEEEEEEEEEEEEEEEEE');
                    if (find_hasaccess) {
                        this.view_type = find_hasaccess.name;
                    }
                }
            }
        },
        currentLocalDataList: {
            deep: true,
            handler(newValue) {
                this.filterdataSets();
            }
        }
    }
};
</script>
<style>
/* Define styles for the chart container */
.chart {
    display: flex;
    justify-content: space-between;
    align-items: flex-end;
}

/* Define styles for each bar */
.bar {
    width: 20px;
    background-color: #4299e1;
}

.bar-value {
    top: calc(100% + 0.5rem);
    /* Adjust as needed */
}

.pie-chart {
    border-radius: 50%;
    overflow: hidden;
}

.pie-slice {
    clip-path: polygon(50% 50%, 100% 0, 100% 100%);
}

.clip-half {
    clip: rect(auto, auto, auto, 50%);
}

.legend {
    display: flex;
    flex-direction: column;
}

.legend-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.legend-color {
    border: 1px solid #ccc;
}

.legend-label {
    font-size: 14px;
}

.blurred-content {
    filter: blur(3px);
    pointer-events: none;
    opacity: 0.4;
}
</style>