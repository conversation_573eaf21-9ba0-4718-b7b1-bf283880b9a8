<template>
    <div v-if="showModal" class="fixed sm:top-0 bottom-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50">

        <!-- Modal -->
        <div class="model bg-white sm:w-3/4 lg:w-3/4 w-full top-0 overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen text-sm"
            :class="{ 'translate-y-0': isOpen, 'translate-y-full bottom-12': !isOpen }">
            <!-- <div class="modal-content"> -->
            <div class="justify-between items-center flex py-4 set-header-background">
                <h2 class="text-white font-bold text-center ml-2 text-xl">
                    Service List</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!-- new design header -->
            <div v-if="!isMobile" class="flex  mb-3 px-2 justify-between m-2">

                <!--Setting-->
                <!-- <div class="flex">
                    <div class="flex items-center" :class="{ 'mr-2': isMobile }">
                        <div class="mr-2 pl-2 pr-2 rounded border bg-gray-100 flex-shrink-0 cursor-pointer py-1"
                            :class="{ 'border-blue-500': items_category === 'tile' }" @click="saveSelection('tile')">
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                        <div class="ml-2 pl-2 pr-2 rounded border bg-gray-100 flex-shrink-0 cursor-pointer py-1"
                            :class="{ 'border-blue-500': items_category !== 'tile' }" @click="saveSelection('list')">
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                    </div>
                </div> -->
                <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                    <div v-if="items_category === 'tile'"
                        class="px-2 py-1 flex-shrink-0 cursor-pointer info-msg border rounded-lg border-gray-500"
                        :class="{ 'bg-white': items_category === 'tile' }" @click="toggleView" :title02="`Table view`">
                        <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
                        <font-awesome-icon icon="fa-solid fa-bars" />
                        <!-- <font-awesome-icon v-else icon="fa-solid fa-grip" size="lg" /> -->
                    </div>
                    <div v-if="items_category !== 'tile'"
                        class="px-2 py-1 rounded flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border rounded-lg border border-gray-500"
                        :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView" :title02="`Card view`">
                        <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
                        <font-awesome-icon icon="fa-solid fa-grip" />
                    </div>
                </div>
            </div>
            <!---load skeleton-->
            <skeleton class="mt-5" :isLoading="open_skeleton" :cols="number_of_columns" :rows="number_of_rows"
                :gap="gap" :type="items_category === 'tile' ? 'grid' : 'table'">
            </skeleton>

            <!--View body-->
            <!--Tile View-->
            <div class="mt-3 pb-5 pl-2 pr-2 m-2" v-if="items_category === 'tile' && !open_skeleton">
                <!-- Category List -->
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
                    <!-- Render category items -->
                    <div v-if="dataList.length > 0" v-for="(category, index) in dataList" :key="index"
                        class="p-4 border rounded-lg bg-gradient-to-r from-blue-100 to-blue-300 text-blue-900 hover:text-green-800 text-lg font-semibold cursor-pointer hover:bg-blue-600 shadow-md transition-transform transform hover:scale-105"
                        :class="{ 'hidden': category.service_status === 0, 'hidden': currentAMCCategory && currentAMCCategory.id && category.id == currentAMCCategory.id }"
                        @click="addServices(category)">
                        <div class="flex flex-col items-center justify-center mb-2 mt-2">
                            <p class="text-lg font-semibold cursor-pointer">{{ category.service_category }}</p>
                        </div>
                    </div>

                    <!-- Create New Category Button -->
                    <div v-if="dataList.length === 0"
                        class="bg-green-50 p-2 py-4 border rounded shadow-sm text-center flex justify-center items-center text-green-700 text-lg font-semibold cursor-pointer hover:bg-green-300"
                        @click="openModal">
                        <font-awesome-icon icon="fa-solid fa-plus" class="mb-1 mt-2 pr-1" size="lg"
                            style="color: green" />
                        <span>Create New Category</span>
                    </div>
                </div>
            </div>

            <!--Table view-->
            <div class="mt-5" v-if="items_category !== 'tile' && !open_skeleton">
                <!-- <select v-model="recordsPerPage" @change="changePage"
                class="mt-1 p-2 border border-gray-300 rounded pr-5 mr-3 mb-6">
                <option v-for="option in options" :key="option" :value="option" class="text-xs">{{ option }}</option>
            </select>
            <label for="recordsPerPage">Records per page</label> -->
                <!--Service category-->
                <div class="table-container items-center justify-center flex m-4">
                    <table class="table w-full bg-white">
                        <thead>
                            <tr>
                                <!-- <th
                                class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold  leading-none">
                                Name</th>
                            <th
                                class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold  leading-none">
                                Status</th>
                            <th
                                class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold  leading-none">
                                Actions</th> -->
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    :class="{ 'hidden': !column.visible }"
                                    class="border text-xs py-2 bg-teal-600 text-white  text-xs font-bold">
                                    <p>{{ column.label }}</p>
                                </th>
                                <th
                                    class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold  leading-none">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-if="dataList.length > 0" v-for="(record, index) in dataList" :key="record.id"
                                :class="{ 'hidden': currentAMCCategory && currentAMCCategory.id && record.id == currentAMCCategory.id }">
                                <td class="border px-4 py-2 text-sm text-center" v-for="(column, index) in columns"
                                    :key="index" :class="{ 'hidden': !column.visible }">
                                    <span v-if="column.field !== 'service_status'">{{ record[column.field] }}</span>
                                    <span v-if="column.field === 'service_status'">Enabled</span>

                                    <!-- <p class="text-sm font-semibold cursor-pointer" v-if="record.status === 'Disabled'">{{
                                    record.name }}</p>
                                <router-link v-if="record.status !== 'Disabled'"
                                    :to="{ name: 'service-category', params: { type: record.name, id: record.id } }"
                                    :class="'font-semibold hover:text-green-600 hover:underline'">
                                    {{ record.name }}
                                </router-link>-->
                                </td>
                                <!-- <td class="border px-4 py-2 sm:text-[14px] text-xs text-center">{{ record.status }}</td> -->
                                <td class="border px-4 py-2 sm:text-[14px] text-xs text-center">
                                    <button class="mr-2 mb-2" v-if="record.status !== 'Disabled'"
                                        @click="addServices(record)">
                                        <img :src="add_symbol" alt="table-view" class="bg-white w-6 h-6" />
                                    </button>
                                    <!-- <button @click="editRecord(record)" class="mr-2 mb-2">
                                    <img :src="table_edit" alt="table-view" class="bg-cyan-500 hover:bg-cyan-700 w-6 h-6" />
                                </button>
                                <button @click="confirmDelete(index)">
                                    <img :src="table_del" alt="table-view" class="bg-red-500 hover:bg-red-700 w-6 h-6" />
                                </button> -->
                                </td>
                            </tr>
                            <tr v-if="dataList.length === 0">
                                <td colspan="3"
                                    class="bg-white p-2 border shadow-lg text-center justify-center items-center hover:bg-blue-50 cursor-pointer">
                                    <!-- <router-link :to="'/categories'"> -->
                                    <div class=" flex justify-center items-center" @click="openModal">
                                        <img :src="service_add_group" alt="services" class="w-[30px] h-[30px] mb-1 mt-2"
                                            style="z-index: 1; align-self: center;" />
                                    </div>
                                    <!-- <span class="text-lg font-semibold cursor-pointer hover:text-green-600 text-black"> Add
                                        Categories</span> -->
                                    <!-- </router-link> -->
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs m-4" v-if="pagination">
                    <p>Total Record: {{ pagination.total }}</p>




                </div>
            </div>
            <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
            <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        </div>
        <addServiceCategory :show-modal="showModal_category" @close-modal="closeModal" :companyId="companyId">
        </addServiceCategory>
    </div>
</template>

<script>
import addServiceCategory from './addServiceCategory.vue';
import confirmbox from './confirmbox.vue';
import dialogAlert from './dialogAlert.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'serviceCategory',
    components: {
        addServiceCategory,
        confirmbox,
        dialogAlert,
    },
    props: {
        showModal: Boolean,
        customer_id: {type: [String, Number]},
        page: String
    },
    data() {
        return {
            service_customer: '/images/service_page/Customer.png',
            service_add_group: '/images/service_page/Add_group.png',
            outline_img: '/images/service_page/Ellipse.png',
            table_view: '/images/service_page/tabler_eye.png',
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            customer_category_img: '/images/categories_page/customer.png',
            service_category_img: '/images/categories_page/vehicle.png',
            list_view: '/images/categories_page/list_view.png',
            tile_view: '/images/categories_page/tile_view.png',
            add_symbol: '/images/service_page/plus.png',
            filter_icon: '/images/customer_page/filter.png',
            setting_icon: '/images/customer_page/settings.png',
            info_icon: '/images/customer_page/info.png',
            refresh_icon: '/images/customer_page/refresh.png',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            dataList: [],
            isOpen: false,
            originalData: [],
            showModal_category: false,
            editData: null,
            isMobile: false,
            showModal_customer: false,
            customer_data: null,
            open_confirmBox: false,
            deleteIndex: null,
            items_category: 'tile',
            colors_list: ['red', 'green', 'blue', 'yellow', 'purple', 'pink', 'gray', 'stone', 'black'],
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataList: [],
            columns: [],
            message: '',
            open_message: false,
            companyId: null,
            userId: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 4,
            number_of_rows: 10,
            gap: 5,
            pagination: {},
        };
    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('serviceCategories', ['currentServiceCategoryPagination']),
        ...mapGetters('createamcservice', ['currentAMCCategory', 'currenAMCData']),
        filteredData() {
            return this.dataList.filter(record => record.service_status === 1);
        },
        paginatedData() {
            if (this.dataList && this.dataList.length > 0) {
                this.dataList = this.dataList.filter(record => record.service_status === 1);
                if (this.items_category !== 'tile') {
                    const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                    const endIndex = startIndex + this.recordsPerPage;
                    return this.dataList.slice(startIndex, endIndex);
                } else {
                    return this.dataList;
                }
            }
        },
        totalPages() {
            return Math.ceil(this.dataList.length / this.recordsPerPage);
        },
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            // console.log(this.data[0], 'EEEEE');
            for (const key in this.dataList[0]) {
                if (key !== 'id' && key !== 'form' && key !== 'style_view' && key !== 'data' && key !== 'created_at' && key !== 'deleted_at' && key !== 'updated_at' && key !== 'company_id') { // Exclude the 'id' field
                    const label = formatLabel(key);
                    fields.push({ label, field: key, visible: true });
                }
            }
            this.columns = fields;
            return fields;
        },
    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('createamcservice', ['resetFetchTime', 'fetchAMCCategory', 'createAMCCategory', 'updateAMCData', 'resetAMCData', 'getAMCData']),
        addServices(record) {
            this.$emit('updateMode');
            // Replace other special characters with spaces in service category
            const sanitizedServiceCategory = record.service_category.replace(/[^\w\s]/g, ' ');
            let newUrl;
            if (record.form) {
                newUrl = this.$router.push({ name: 'service-category-add', params: { type: sanitizedServiceCategory, id: record.id }, query: { customer_id: this.customer_id } }).href;
            } else {
                newUrl = this.$router.push({ name: 'service_category_create_form', params: { type: sanitizedServiceCategory, serviceId: record.id }, query: { createform: 'true', customer_id: this.customer_id } }).href;
            }
            if (this.page && this.page == 'dashboard') {
                this.$emit('updateModalclose');
                setTimeout(() => {
                    // Change the URL
                    window.history.pushState({}, '', newUrl);

                    // Refresh the page
                    this.$emit('close-modal', true);
                }, 100)

            } else {
                // Change the URL
                window.history.pushState({}, '', newUrl);
                // Refresh the page
                // window.location.reload();
                this.$emit('close-modal', true);
            }
        },
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
                this.proofIs = false;
            }, 300);
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.totalPages) {
                this.currentPage = pageNumber;
            }
        },
        editRecord(record) {
            this.editData = record;
            this.showModal_category = true;
        },
        deleteRecord() {
            if (this.deleteIndex !== null) {
                if (this.items_category !== 'tile') {
                    this.dataList.splice(this.deleteIndex, 1);
                } else {
                    this.dataList.splice(((this.recordsPerPage * (this.currentPage - 1)) + this.deleteIndex), 1);
                }
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...');
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        openModal() {
            this.editData = null;
            this.showModal_category = true;
        },
        closeModal(data) {
            if (data) {
                let findDuplicate = this.originalData.findIndex((opt) => opt.id === data.id);
                if (findDuplicate !== -1) {
                    this.dataList[findDuplicate] = data;
                    this.originalData = this.dataList;
                } else {
                    this.dataList.push(data);
                    this.originalData = this.dataList;
                }
            }
            // window.location.reload();
            this.showModal_category = false;
        },
        closeModalCustomer() {
            this.showModal_customer = false;
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        saveSelection(data) {
            // console.log(data, 'EQEQEQE');
            this.items_category = data;
            localStorage.setItem('selectedCategory', this.items_category);
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        // closeFilterModal() {
        //     this.showFilterModal = false;
        //     this.resetFilter();
        // },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // Implement logic to filter data based on filter values
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---service category---
        serviceCategoryList(page, per_page) {
            this.open_skeleton = true;
            // axios.get('/service_categories', { params: { company_id: this.companyId, page: page, per_page: per_page } })
            //     .then(response => {
            //         this.open_skeleton = false;
            //         // console.log(response.data.data, 'RRRRR');
            //         this.dataList = response.data.data;
            //         this.pagination = response.data.pagination;
            //     })
            //     .catch(error => {
            //         console.error('Error', error);
            //         this.open_skeleton = false;
            //     });
            if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                this.dataList = this.currentServiceCategory;
                if (this.currentServiceCategoryPagination && Object.keys(this.currentServiceCategoryPagination).length > 0) {
                    this.pagination = this.currentServiceCategoryPagination;
                }
                this.open_skeleton = false;
            } else {
                this.fetchServiceCategoryList();
                this.open_skeleton = false;
            }
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
    },

    created() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
        // Retrieve the stored value from localStorage
        const storedValue = localStorage.getItem('selectedCategory');
        if (storedValue) {
            this.items_category = storedValue;
        }
        if (!this.currentAMCCategory || Object.keys(this.currentAMCCategory).length == 0) {
            this.fetchAMCCategory();
        }
    },

    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            if (newValue) {
                // console.log(newValue, 'WWWRWRWR');  
                if (newValue && this.dataList.length === 0) {
                    this.serviceCategoryList(1, this.items_category === 'tile' ? 'all' : this.recordsPerPage);
                }
                setTimeout(() => {
                    this.isOpen = newValue;
                }, 100);
            }
        },
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.dataList = newValue;
                    if (this.currentServiceCategoryPagination && Object.keys(this.currentServiceCategoryPagination).length > 0) {
                        this.pagination = this.currentServiceCategoryPagination;
                    }
                }
            }
        },
        // current_page: {
        //     deep: true,
        //     handler(newValue) {
        //         if (this.pagination && newValue < this.pagination.last_page) {
        //             this.serviceCategoryList(newValue, this.recordsPerPage);
        //         }
        //     }
        // },
        // recordsPerPage: {
        //     deep: true,
        //     handler(newValue) {
        //         if (newValue) {
        //             this.serviceCategoryList(1, newValue);
        //         }
        //     }
        // },
        // isMobile: {
        //     deep: true,
        //     handler(newValue) {
        //         if (newValue) {
        //             this.items_category = 'tile';
        //         }
        //     }
        // },
        // items_category: {
        //     deep: true,
        //     handler(newValue) {
        //         if (this.dataList.length === 0) {
        //             this.serviceCategoryList(1, this.items_category === 'tile' ? 'all' : this.recordsPerPage);
        //         }
        //     }

        // }
    }
}
</script>

<style scoped>
.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
    }
}
</style>
