<?php

namespace App\Http\Controllers\API;


use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Models\Orders;
use App\Models\Option;
use Barryvdh\DomPDF\Facade\Pdf;
use Response;
use Auth;

/**
 * Class ProductsController
 * @package App\Http\Controllers\API
 */

class OrderAPIController extends AppBaseController
{
   

    public function __construct()
    {
 
    }
  
  	
  	 	public function downloadInvoice($orderId)
    {
        // Fetch the order and related data
        $order = Orders::with(['user', 'plan', 'gateway'])->findOrFail($orderId);
      
      
        $invoice = Option::where('key', 'invoice_data')->first();
        $currency = Option::where('key', 'base_currency')->first();
        $tax = $currency = Option::where('key', 'tax')->first();
      
 
		$invoice_data = json_decode($invoice->value);      
   
      
        $pdf = Pdf::loadView('pdf.order', [
         	'order' => $order,
         	'invoice' => $invoice_data
        ]);
        $filename = $order->invoice_no ? $order->invoice_no : 'inv';      
        return $pdf->download($filename.'.pdf');            
    }
   

    public function generateInvoice($orderId)
    {
        // Fetch the order and related data
        $order = Orders::with(['user', 'plan', 'gateway'])->findOrFail($orderId);      
      
        $invoice = Option::where('key', 'invoice_data')->first();
        $currency = Option::where('key', 'base_currency')->first();
        $tax = $currency = Option::where('key', 'tax')->first();      
 
		$invoice_data = json_decode($invoice->value);      
   
        return view('pdf.order')->with([
           	'order' => $order,
         	'invoice' => $invoice_data
        ]);
       
    }

   
     
    public function index(Request $request)
    {
        

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
        $userId = Auth::user()->id;
        $Query = Orders::with(['user', 'plan', 'gateway'])->where('status', 1)->where('user_id', $userId);


        if ($perPage === 'all') {
            $perPage = $Query->count();
        }    

        $tax = $Query->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $tax->items(), // Get the paginated items
            'pagination' => [
                'total' => $tax->total(),
                'per_page' => $tax->perPage(),
                'current_page' => $tax->currentPage(),
                'last_page' => $tax->lastPage(),
                'from' => $tax->firstItem(),
                'to' => $tax->lastItem(),
            ]
        ];

        return response()->json($response);
    }


   
}
