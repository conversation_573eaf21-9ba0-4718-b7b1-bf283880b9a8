<!-- MainComponent.vue -->
<template>
  <div>
    <!--header-->
    <div class="fixed w-full z-10">
      <Header :isMobile="isMobile" @updatesidebar="updatesidebar"></Header>
    </div>
    <div class="flex">
      <!-- Sidebar Component -->
      <!-- <div v-if="!isMobile">
        <Sidebar :isMobile="isMobile" @updatesidebar="updatesidebar"></Sidebar>
      </div> -->
      <!-- Sidebar for mobile view -->
      <div>
        <!-- Background overlay for blur effect -->
        <div v-if="isMobile && isEnablesidebar" class="absolute inset-0 bg-black bg-opacity-50 z-10"></div>

        <!-- Sidebar -->
        <div v-if="isMobile && isEnablesidebar" ref="sidebar" class="absolute z-20 w-60 -mt-14 h-screen">
          <Sidebar @updatesidebar="updatesidebar" :isMobile="isMobile" />
        </div>
      </div>

      <main class="flex-1">
        <slot></slot>
      </main>

      <!-- Main Content -->
      <!-- <main class="flex-1 p-6">
       
        <div class="bg-white p-6 rounded-lg shadow-md">
          <header class="flex justify-between items-center mb-4">
            <div class="text-lg font-semibold">Dashboard</div>
            <div>
              <button class="bg-blue-500 text-white py-2 px-4 rounded-md">Upgrade</button>
              <button class="ml-2 bg-blue-500 text-white py-2 px-4 rounded-md">WordPress Admin</button>
            </div>
          </header>
          <div class="bg-red-100 text-red-700 p-4 rounded-md flex items-center justify-between">
            <span>Connect a domain to your website. To make sure your website is accessible, connect your domain first.</span>
            <button class="bg-red-500 text-white py-1 px-3 rounded-md">Connect</button>
          </div>
          <div class="grid grid-cols-4 gap-4 mt-6">
            <div class="bg-gray-100 p-4 rounded-md text-center">
              <p class="text-sm text-gray-500">Domain</p>
              <p class="text-lg font-semibold">Temporary</p>
            </div>
            <div class="bg-gray-100 p-4 rounded-md text-center">
              <p class="text-sm text-gray-500">Hosting</p>
              <p class="text-lg font-semibold">Active</p>
            </div>
            <div class="bg-gray-100 p-4 rounded-md text-center">
              <p class="text-sm text-gray-500">Email</p>
              <p class="text-lg font-semibold">Inactive</p>
            </div>
            <div class="bg-gray-100 p-4 rounded-md text-center">
              <p class="text-sm text-gray-500">Backups</p>
              <p class="text-lg font-semibold">Daily</p>
            </div>
          </div>
        </div>
      </main> -->
    </div>
  </div>
</template>

<script>

import Sidebar from './sidebar.vue';
import Header from './header.vue';

export default {
  components: {
    Sidebar,
    Header
  },
  data() {
    return {
      isMobile: false,
      isEnablesidebar: false,
    };
  },
  methods: {
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
    updatesidebar() {
      this.isEnablesidebar = !this.isEnablesidebar;
      //---sidebar icons--
      if (this.isMobile && this.isEnablesidebar) {
        document.body.addEventListener('click', this.handleOutsideClick);
      }
    },
    //---is Mbile true only---
    handleOutsideClick(event) {
      // Check if the click target is outside the sidebar
      const sidebarElement = this.$refs.sidebar;
      if (!sidebarElement || !sidebarElement.contains(event.target)) {
        // this.updatesidebar();
        this.isEnablesidebar = false;
        // Emit action to close the sidebar
      }
    },
  },
  mounted() {
    this.updateIsMobile();
    window.addEventListener('resize', this.updateIsMobile);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateIsMobile);
    //---sidebar icons--
    if (this.isMobile) {
      document.body.removeEventListener('click', this.handleOutsideClick);
    }

  }
};
</script>

<style></style>