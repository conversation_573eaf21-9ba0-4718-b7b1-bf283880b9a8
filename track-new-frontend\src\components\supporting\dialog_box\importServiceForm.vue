<template>
    <div v-if="showModal" class="fixed sm:top-0 bottom-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50"
        @keyup.esc="cancelModal()">

        <!-- Modal -->
        <div ref="modal"
            class="model bg-slate-100 w-full top-0 overflow-auto sm:bottom-[10px] transform ease-in-out duration-100 h-screen text-sm"
            :class="{ 'translate-y-0': isOpen, 'translate-y-full bottom-12': !isOpen }">
            <!-- <div class="modal-content"> sm:w-3/4 lg:w-3/4 -->

            <div class="justify-between items-center flex py-4 set-header-background">
                <p class="text-white font-bold items-center text-center flex justify-end ml-10 text-lg">
                    <font-awesome-icon icon="fa-solid fa-cloud-arrow-up" size="lg" class="px-2" /> Import from cloud
                    Templates
                </p>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <div class="p-4">
                <div v-if="!currentStep">
                    <div v-if="service_form && Array.isArray(service_form) && service_form.length > 0"
                        class="grid grid-cols-1 sm:grid-cols-3 lg:grid-cols-6 2xl:grid-cols-8 gap-6">
                        <div v-for="(category, index) in service_form"
                            class="border border-gray-300 rounded rounded-md p-2 bg-white shadow-sm relative">
                            <!-- Radio button in top right corner -->
                            <div class="absolute top-1 left-3 z-10">
                                <input type="checkbox" v-if="selected_category && Array.isArray(selected_category)"
                                    id="toggleRadio" v-model="selected_category[index]" />
                            </div>
                            <p class="text-center text-lg cursor-pointer hover:underline"
                                @click="selectCategory(index)">
                                {{ category.name }}</p>
                            <div class="flex justify-center items-center mt-2">
                                <button @click="previewCategory(index)"
                                    class="rounded-full shadow-md shadow-blue-300 px-4 py-1 bg-blue-500 text-white">Preview
                                    <font-awesome-icon icon="fa-solid fa-eye" class="px-2 items-center" />
                                </button>
                            </div>
                        </div>
                    </div>
                    <!--in case empty-->
                    <div v-else>
                        <div class="flex justify-center items-center">
                            <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                        </div>
                        <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
                    </div>
                </div>
                <div v-if="currentStep" :class="{ 'pb-[80px]': isMobile }">
                    <preview :dynamicForm="dynamicForm" :formValues="formValues" :companyId="companyId"></preview>
                </div>
            </div>
            <!-- </div> -->
        </div>
        <!-- Back & Save Buttons (Current Step 1) -->
        <div v-if="currentStep" class="fixed bottom-0 right-0 w-full" :class="{
            'flex justify-center items-center': !isMobile, 'grid grid-cols-2 justify-center items-center pb-[70px]': isMobile
        }">
            <button @click="backStep"
                class="px-2 sm:px-4 py-1 sm:py-2 bg-red-500 text-white focus:outline-none hover:bg-red-400 flex justify-center items-center"
                :class="{ 'mr-10 rounded': !isMobile }">
                <font-awesome-icon icon="fa-solid fa-angles-left" class="items-center" />
                <span class="font-bold pl-1 sm:pl-3">Back</span>
            </button>
            <button @click="nextStep"
                class="px-2 sm:px-4 py-1 sm:py-2 bg-green-600 text-white focus:outline-none hover:bg-green-500 flex justify-center items-center"
                :class="{ 'rounded': !isMobile }">
                <font-awesome-icon icon="fa-solid fa-cloud-arrow-up" class="px-2 items-center" />
                <span class="font-bold pr-3">Import</span>
            </button>
        </div>
        <!-- Buttons -->
        <div v-if="!currentStep" class="fixed bottom-1 right-0 w-full"
            :class="{ 'flex justify-center items-center': !isMobile, 'grid grid-cols-3 justify-center items-center  pb-[70px]': isMobile }">
            <button @click="cancelModal"
                class="bg-red-600 rounded text-white text-md px-2 sm:px-4 py-1 sm:py-2 mr-1 sm:mr-3 hover:bg-red-500">
                <font-awesome-icon icon="fa-solid fa-angles-left" class="px-2 items-center" />
                Cancel
            </button>
            <button @click="sendModal"
                class="bg-green-600 rounded text-white text-md px-2 sm:px-4 py-1 sm:py-2 ml-1 sm:ml-2 mr-1 sm:mr-3  hover:bg-green-500 "><font-awesome-icon
                    icon="fa-solid fa-cloud-arrow-up" class="px-2 items-center" /> Import</button>
            <button v-if="selected_category_list && selected_category_list.length > 0" @click="openRenameModal"
                class="bg-blue-600 rounded text-white text-md px-2 sm:px-4 py-1 sm:py-2 ml-1 sm:ml-2 hover:bg-blue-500 ">
                <font-awesome-icon icon="fa-solid fa-pencil" class="px-2 items-center" /> Rename</button>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type" @update:show="show = false"></Toaster>
        <serviceCategoryRename :show-modal="rename_modal" :item_data="selected_category_list" :message_err="exist_msg"
            @close-Modal="closeRenameModal" :isMobile="isMobile"> </serviceCategoryRename>
    </div>
</template>

<script>
import preview from '../categories/create_form/preview.vue';
import serviceCategoryRename from './serviceCategoryRename.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'addServiceCategory',
    props: {
        showModal: Boolean,
        editData: Object,
        companyId: String,
        getplanfeatures: {
            type: Function,
            required: false,
        }
    },
    components: {
        preview,
        serviceCategoryRename
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            isMobile: false,
            validate_msg: '',
            isOpen: false,
            focussed: {},
            open_loader: false,
            show: false,
            message: '',
            type: 'info',
            service_form: [],
            //----form preview----
            dynamicForm: null,
            formValues: {},
            currentStep: false,
            selected_category: [],
            selected_category_list: [],
            //---rename modal----
            rename_modal: false,
            exist_msg: '',
        }
    },
    computed: {
        ...mapGetters('importCloud', ['currentCategoryList']),
    },
    methods: {
        ...mapActions('importCloud', ['fetchCategoryList']),
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        cancelModal(data) {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                if (data && data.name !== undefined) {
                    this.$emit('close-modal', data);

                } else {
                    this.$emit('close-modal');
                }
                this.validate_msg = '';
            }, 300);
        },
        cancelModalData() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
            this.validate_msg = '';
        },
        sendModal() {
            if (this.getplanfeatures('service_category')) {
                this.$emit('opennoaccess');
            } else {
                if (this.service_form && this.service_form.length > 0) {
                    // let filtered_data = this.service_form.filter((_, index) => this.selected_category[index]);
                    // Handle sending logic here      
                    if (this.selected_category_list && this.selected_category_list.length > 0) {
                        // filtered_data = filtered_data.map(opt => ({ ...opt, service_status: '1' }));
                        this.open_loader = true;
                        const payload = {
                            service_categories: this.selected_category_list,
                            company_id: this.companyId
                        };

                        axios.post('/service_categories', payload)
                            .then(response => {
                                this.open_loader = false;
                                if (response.data.error) {
                                    this.selected_category_list = [];
                                    this.service_form.forEach((opt, index) => {
                                        if (response.data.error.includes(opt.name)) {
                                            this.selected_category[index] = true;
                                            this.selected_category_list.push({ service_category: opt.name, service_status: '1', form: JSON.stringify(opt.form), company_id: this.companyId })
                                        } else {
                                            this.selected_category[index] = false;
                                        }
                                        if (index === this.service_form.length - 1) {
                                            this.exist_msg = 'This category already exist';
                                            this.rename_modal = true;
                                        }
                                    })
                                    this.fetchServiceCategoryList();

                                } else {
                                    this.updateKeyWithTime('service_category_update');
                                    this.message = 'Service category has been created successfully...!';
                                    this.type = 'success';
                                    this.show = true;
                                    this.selected_category = [false, false, false];
                                    this.selected_category_list = [];
                                    this.cancelModalData();
                                }
                            })
                            .catch(error => {
                                console.error('Error', error);
                                this.open_loader = false;
                                this.exist_msg = error.response.data.message;
                                this.rename_modal = true;
                            })
                        // }
                    } else {
                        this.message = 'Please select the service category..!'
                        this.type = 'warning';
                        this.show = true;
                    }
                } else {
                    this.message = 'Please wait loading the service category list..!'
                    this.type = 'warning';
                    this.show = true;
                }
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

        handleOutsideClick(event) {
            if (this.$refs.modal && !this.$refs.modal.contains(event.target) && this.showModal && this.isOpen) {
                this.cancelModalData(event);
            }
        },
        //---selected index---
        selectCategory(index) {
            if (index >= 0 && this.service_form.length > 0) {
                this.selected_category[index] = !this.selected_category[index];
            }
        },
        //---preview page---
        previewCategory(index) {
            if (index >= 0 && this.service_form.length > 0) {
                if (this.selected_category.length > 0) {
                    this.selected_category[index] = true;
                    // let find_category = this.selected_category.find(opt => opt == index);
                    // if (!find_category) {
                    //     this.selected_category.push(index);
                    // }
                }
                this.dynamicForm = this.service_form[index].form;
                this.currentStep = true;
            }
        },
        nextStep() {
            this.currentStep = false;
            this.sendModal();
        },
        backStep() {
            this.currentStep = false;
        },
        //---rename modal---
        openRenameModal() {
            this.rename_modal = true;
        },
        closeRenameModal(data) {
            if (data && data.length > 0) {
                let index_count = 0;
                this.service_form = this.service_form.map((item, index) => {
                    if (this.selected_category[index]) {
                        index_count += 1;
                        return { ...item, name: data[index_count - 1] };
                    }
                    return item;
                });
                this.selected_category_list = this.selected_category_list.map((opt, index) => {
                    return { ...opt, service_category: data[index_count - 1] };
                });
                this.rename_modal = false;
            } else {
                this.rename_modal = false;
            }
            this.exist_msg = '';
        }
    },
    mounted() {
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
        // document.addEventListener('click', this.handleOutsideClick);
    },
    beforeDestroy() {
        // document.removeEventListener('click', this.handleClickOutside);
    },
    watch: {

        showModal(newValue) {
            if (newValue) {
                if (this.currentCategoryList && this.currentCategoryList.length > 0) {
                    this.service_form = [...this.currentCategoryList];
                    this.selected_category = new Array(this.currentCategoryList.length - 1).fill(false);
                    this.fetchCategoryList();
                } else {
                    this.open_loader = true;
                    this.fetchCategoryList();
                }
            }
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        currentCategoryList: {
            deep: true,
            handler(newValue) {
                this.open_loader = false;
                if (newValue && newValue.length > 0) {
                    // console.log(newValue, 'EEEEEEEEEEEEEEE');
                    this.service_form = newValue;
                    this.selected_category = new Array(newValue.length - 1).fill(false);
                }
            }
        },
        selected_category: {
            deep: true,
            handler(newValue) {
                let filtered_data = this.service_form.filter((_, index) => this.selected_category[index]);
                if (filtered_data && filtered_data.length > 0) {
                    filtered_data = filtered_data.map(opt => ({ service_category: opt.name, service_status: '1', form: JSON.stringify(opt.form), company_id: this.companyId }));
                    this.selected_category_list = filtered_data;
                } else {
                    this.selected_category_list = [];
                }
            }
        }
    },
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
/* @media (max-width: 767px) {
    .modal {
        height: 80vh; */
/* Adjust the height as needed */
/* }

    .modal-content {
        overflow-y: auto;
    }
} */
</style>
