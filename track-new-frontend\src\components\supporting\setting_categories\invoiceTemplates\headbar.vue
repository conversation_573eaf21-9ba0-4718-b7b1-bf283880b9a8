<template>
    <div class="non-printable headBar" :class="{ 'fixed top-0 left-0 z-50 w-full': isMobile }">
        <div class="hidden fixed-header flex justify-between items-center set-header-background">
            <!-- Menu icon button for mobile view -->
            <button v-if="isMobile" @click="toggleSidebar" class="block sm:hidden text-xl px-2 sm:px-4 py-2 text-black">
                ☰
            </button>
            <div v-if="!isMobile"
                class="h-[18px] sm:h-[18px] text-left center text-md sm:text-2xl items-center flex justify-center p-5">
                Invoice
            </div>
            <!--search bar-->
            <!-- <div class="relative">
            <input type="text" :placeholder= placeholderText
                class="border border-gray-300 rounded p-2 focus:border-blue-500 outline-none lg:w-[500px] bg-search-icon"
                v-model="searchQuery" @input="filterCustomers" @change="showDropdown" ref="searchInput" /> -->
            <!-- Dropdown container -->
            <!-- <div v-if="showSuggestions" class="absolute mt-2 bg-white border rounded shadow-md w-full"
                :style="{ 'z-index': 999 }"> -->
            <!-- List of customer suggestions -->
            <!-- <ul v-if="page === 'Customers'">
                    <li v-for="customer in filteredCustomers" :key="customer.id" @click="selectCustomer(customer)">
                        {{ customer.first_name }} {{ customer.last_name }} - {{ customer.phone }} - {{ customer.email }}
                    </li>
                </ul>
                <ul v-if="page === 'Services'">
                    <li v-for="customer in filteredCustomers" :key="customer.id" @click="selectCustomer(customer)">
                        {{ customer.first_name }} {{ customer.last_name }} - {{ customer.phone }} - {{ customer.email }}
                    </li>
                </ul>
            </div>
        </div> -->
            <div class="flex items-center p-2 relative">
                <span
                    class="mb-7 ml-3 absolute bg-red-500 text-white font-bold text-[10px] rounded rouned-full px-1 cursor-pointer"
                    title="Hold List" @click="displayHoldList()">{{ hold_list.length }}</span>
                <img class="w-[20px] h-[20px] sm:w-[20px] sm:h-[20px] mr-5" :src="hold_icon" alt="hold"
                    title="Hold List" @click="displayHoldList()" />
                <!-- <img class="h-[25px] sm:h-[30px] mr-3" :src="images[1]" alt="notification" /> -->
                <!-- <span
                class="inline-flex items-center rounded-full bg-red-500 pl-1 pr-1 text-[8px] sm:text-[10px] font-medium text-white ring-1 ring-inset ring-red-600/10 absolute ml-3 sm: mr-5 top-[15px] sm:top-[30px]">
                {{ showBadges_notify }}
            </span>
            <span
                class="inline-flex items-center rounded-full bg-violet-500 pl-1 pr-1 text-[8px] sm:text-[10px] font-medium text-white text-center ring-1 ring-inset ring-green-600/10 right-[15px] mt-5 sm:right-[15px] sm: mt-3 absolute">
                &#10003;
            </span> -->
                <img class="w-[35px] h-[35px] mr-2" :src="images[0]" alt="user" @click="showLogoutModal = true" />
            </div>
            <div v-show="showLogoutModal" class="absolute z-50 transform right-[210px] mt-10">
                <div v-show="showLogoutModal" class="absolute bg-white border border-gray-300 p-4 rounded shadow-md">
                    <h3 class="text-lg font-semibold mb-2">Confirm Logout</h3>
                    <p class="text-sm mb-4">Are you sure you want to logout?</p>
                    <div class="flex justify-between">
                        <button @click="logout"
                            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none focus:bg-red-600 mr-3">Logout</button>
                        <button @click="showLogoutModal = false"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 focus:outline-none focus:bg-gray-400">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex justify-start px-4 py-2 items-center text-white set-header-background">
            <div class="pr-5 py-2 cursor-pointer" @click="goBack">
                <span>
                    <font-awesome-icon icon="fa-solid fa-arrow-left" size="lg" />
                </span>
            </div>
            <div class="w-full">
                <h6 class="text-xl">{{ type === 'invoice' ? 'Invoices' : 'Preview' }}</h6>
            </div>
            <div class="flex items-center p-2 relative">
                <span
                    class="mb-7 ml-3 absolute bg-red-500 text-white font-bold text-[10px] rounded rouned-full px-1 cursor-pointer"
                    title="Hold List" @click="displayHoldList()">{{ hold_list.length }}</span>
                <img class="w-[20px] h-[20px] sm:w-[20px] sm:h-[20px] mr-5" :src="hold_icon" alt="hold"
                    title="Hold List" @click="displayHoldList()" />
            </div>
            <div>
                <button @click="refreshPage" :title01="'Refresh'" class="flex items-center">
                    <!-- <img :src="refresh_icon" alt="refresh_icon" class="w-6 h-6" /> -->
                    <font-awesome-icon icon="fa-solid fa-rotate-right" size="lg" />
                </button>
            </div>
        </div>
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'headbar',
    props: {
        page: String,
        customerData: Object,
        refresh: Boolean,
        type: String,
        typeOfInvoice: String,
        currentLocalDataList: Object
    },
    data() {
        return {
            images: [
                '/images/head_bar/User.png',
                '/images/head_bar/Notification.png',
            ],
            hold_icon: '/images/pos/hold.png',
            showBadges_notify: 2,
            showBadges_status: true,
            isMobile: false,
            searchQuery: '',
            showSuggestions: false,
            placeholderText: '',
            showLogoutModal: false,
            companyId: null,
            userId: null,
            hold_list: [],
        };
    },

    methods: {
        ...mapActions('holdsList', ['fetchHoldsList']),
        goBack() {
            // this.$router.go(-1);
            this.backToSetting();
        },
        refreshPage() {
            // window.location.reload();
            this.fetchHoldsList();
            this.$emit('refresh_store');
        },
        toggleSidebar() {
            this.$emit('toggle-sidebar');
        },
        updateIsMobile() {
            // Update isMobile based on the viewport width
            // this.$forceUpdate();
            this.isMobile = window.innerWidth < 1024;
        },
        //----dropdown---
        filterCustomers() {
            if (this.searchQuery !== '' && this.searchQuery.length > 3) {
                // console.log(this.searchQuery, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            } else {
                this.showSuggestions = false;
            }
        },
        showDropdown() {
            // Show dropdown on input focus
            this.showSuggestions = true;
            // Add a click event listener to the document to close the dropdown when clicking outside                
            document.addEventListener('click', this.handleDocumentClick);
        },
        hideDropdown() {
            // Hide dropdown on input blur
            this.showSuggestions = false;
            // Remove the click event listener when hiding the dropdown
            document.removeEventListener('click', this.handleDocumentClick);
        },
        selectCustomer(customer) {
            // Handle the selected customer (e.g., emit an event, update data, etc.)
            console.log('Selected Customer:', customer);
            this.searchQuery = customer.first_name + ' ' + customer.last_name + ' - ' + customer.phone + ' - ' + customer.email; // Set the input value to the selected customer name
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            this.$emit('searchData', customer); //---filter data
            document.removeEventListener('click', this.handleDocumentClick); // Remove the event listener after selection
        },
        handleDocumentClick(event) {
            // Close the dropdown when clicking outside the input and dropdown
            const isClickInside = this.$refs.searchInput.contains(event.target) || this.$refs.searchInput.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.hideDropdown();
            }
        },
        isNumeric(str) {
            // Helper method to check if a string is numeric
            return /^\d+$/.test(str);
        },
        logout() {
            // Clear the 'track_new' key from local storage
            localStorage.removeItem('track_new');
            this.$router.push('/login');
        },
        getHoldList() {
            axios.get('/hold_invoices', { params: { company_id: this.companyId, per_page: 1000, page: 1 } })
                .then(response => {
                    console.log(response.data, 'hold list');
                    this.hold_list = response.data.data;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---get list---
        displayHoldList() {
            this.$emit('display_hold_list');
        },
        //---go back---
        backToSetting() {
            // this.printing = false;
            if (this.page_name === 'setting') {
                this.$emit('goSetting');
            } else if (this.$route.query.type === 'sales_home') {
                this.$router.go(-1);
            } else if (this.checkRoles(['Sub_Admin', 'admin', 'Account Manager', 'Service Manager', 'Sales man'])) {
                if (this.typeOfInvoice !== 'estimation' && this.typeOfInvoice !== 'proforma') {
                    this.$router.push('/sales');
                } else if (this.typeOfInvoice === 'estimation') {
                    this.$router.push('/estimation');
                } else {
                    this.$router.push('/proforma');
                }
            } else {
                this.$router.push('/');
            }
        },
        //---validate the role
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        }
    },
    mounted() {
        const collectForm01 = localStorage.getItem('track_new');
        if (collectForm01) {
            let dataParse = JSON.parse(collectForm01);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile();
        // this.getHoldList();
        if (this.currentHoldsList && this.currentHoldsList.length > 0) {
            this.hold_list = this.currentHoldsList;
            this.fetchHoldsList();
        } else {
            this.fetchHoldsList();
        }
        // Initial check
        // Add a resize event listener to dynamically update isMobile
        window.addEventListener('resize', this.updateIsMobile);
        this.placeholderText = this.page === 'Customers' ? 'Search customer' : this.page === 'Services' ? 'Search service name' : 'Enter some text';
    },
    beforeDestroy() {
        // Remove the resize event listener when the component is destroyed
        window.removeEventListener('resize', this.updateIsMobile);
    },
    computed: {
        ...mapGetters('holdsList', ['currentHoldsList']),
        filteredCustomers() {
            // console.log('it is executed..!!');
            // Filter customers based on search query
            if (this.searchQuery !== '') {
                const query = this.searchQuery.toLowerCase();
                return this.customerData.filter(customer => {
                    const fullName = `${customer.first_name} ${customer.last_name}`.toLowerCase();
                    const email = customer.email.toLowerCase();
                    const phone = customer.phone.toLowerCase();
                    return (
                        fullName.includes(query) ||
                        email.includes(query) ||
                        (this.isNumeric(query) && phone.includes(query))
                    );
                });
            } else {
                return this.customerData;
            }
        },
    },
    watch: {
        refresh: {
            deep: true,
            handler(newValue) {
                // this.getHoldList();
                this.fetchHoldsList();
            }
        },
        currentHoldsList: {
            deep: true,
            handler(newValue) {
                this.hold_list = newValue;
            }
        }
    }
};
</script>
<style scoped>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}

/* Style for the dropdown */
ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

li {
    padding: 8px;
    cursor: pointer;
}

li:hover {
    background-color: #f0f0f0;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
    }

    .non-printable {
        display: none;
    }
}
</style>