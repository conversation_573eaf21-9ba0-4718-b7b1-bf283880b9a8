<template>
    <div class="relative">
        <!-- Header -->
        <h1 class="sm:text-xl text-lg text-center mt-5 underline">WhatsApp Settings</h1>
        <div class="absolute right-2 -top-4">
            <button class="bg-gray-300 hover:bg-gray-400 border p-2 sm:px-4 sm:py-2 mt-4 rounded items-center"
                @click="openAvailableOptions">
                <font-awesome-icon icon="fa-solid fa-circle-info" /> Info</button>
        </div>
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>

        <div v-if="!open_skeleton">
            <div class="flex flex-col items-center mt-1 sm:mt-3">
                <div class="w-full mb-4 relative text-sm">
                    <!-- Template List -->
                    <div class="w-full sm:hidden mt-5 overflow-x-auto bg-white border shadow-lg p-2">
                        <!-- Mobile View: Horizontal Scrollable Menu -->
                        <div class="flex space-x-4">
                            <div clas="w-full">
                                <button class="cursor-pointer p-1 px-2 rounded-md whitespace-nowrap"
                                    :class="{ 'bg-blue-300 text-blue-800 font-bold': activeKey === 'profile', 'text-gray-600': activeKey !== 'profile' }"
                                    @click="toggleVisibility('profile')">WhatApp
                                    Profile
                                </button>
                            </div>

                            <div v-if="templates_list && Object.keys(templates_list).length > 0">
                                <div v-for="(template, key) in templates_list" :key="key" @click="toggleVisibility(key)"
                                    :class="{ 'bg-blue-300 text-blue-600': activeKey === key, 'text-gray-600': activeKey !== key }"
                                    class="cursor-pointer p-1 px-2 rounded-md whitespace-nowrap">
                                    {{ formatKey(key) }}
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Keys List -->
                    <div class="mt-1 sm:mt-3 relative">
                        <div class="flex flex-col sm:flex-row sm:space-x-4 space-y-2 sm:space-y-0">
                            <!-- Vertical Template List (Desktop) -->
                            <div
                                class="sm:w-1/4 sm:flex flex-col hidden bg-white p-4 border shadow-lg sm:space-y-2 text-sm">
                                <p class="bg-gray-200 text-center p-2">Setting</p>
                                <div clas="w-full">
                                    <button @click="toggleVisibility('profile')"
                                        :class="{ 'bg-blue-300 text-blue-800 font-bold': activeKey === 'profile', 'text-gray-600': activeKey !== 'profile' }"
                                        class="cursor-pointer p-2 border rounded-lg hover:bg-blue-100 hover:text-blue-600 w-full text-left">WhatApp
                                        Profile
                                    </button>
                                </div>
                                <p class="bg-gray-200 text-center p-2">Templates</p>
                                <div v-if="templates_list && Object.keys(templates_list).length > 0">
                                    <div v-for="(template, key) in templates_list" :key="key" class="w-full">
                                        <div @click="toggleVisibility(key)"
                                            :class="{ 'bg-blue-300 text-blue-800 font-bold': activeKey === key, 'text-gray-600': activeKey !== key }"
                                            class="cursor-pointer p-2 border rounded-lg hover:bg-blue-100 hover:text-blue-600 w-full">
                                            {{ formatKey(key) }}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Form Display on Remaining Width -->
                            <div v-if="templates_list && Object.keys(templates_list).length > 0"
                                class="sm:w-3/4 w-full">
                                <!-- WhatsApp Profile Design for Active Key -->
                                <div v-if="activeKey === 'profile'" class="w-full p-4 border shadow-lg">
                                    <div class="bg-white p-4 rounded-lg">
                                        <h3 class="text-lg font-bold text-center mb-2">WhatsApp Profile
                                        </h3>

                                        <!-- If whatsapp_id is null, fetch QR Code -->
                                        <div v-if="!whatsapp_data.whatsapp_id || retry_qr"
                                            class="flex flex-col items-center relative">
                                            <div class="flex flex-col sm:flex-row justify-between items-start mb-8">
                                                <!-- Left Section: Instructions -->
                                                <div class="flex flex-col space-y-4 w-full max-w-md">
                                                    <h1 class="text-3xl font-semibold text-gray-800">Log into
                                                        WhatsApp</h1>
                                                    <ol
                                                        class="list-decimal list-inside text-sm text-gray-600 space-y-2">
                                                        <li>Open WhatsApp on your phone</li>
                                                        <li>Tap Menu <span class="font-semibold">on
                                                                Android</span> or Settings <span
                                                                class="font-semibold">on
                                                                iPhone</span></li>
                                                        <li>Tap Linked devices and then Link a device</li>
                                                        <li>Point your phone at this screen to scan the QR code
                                                        </li>
                                                    </ol>
                                                </div>

                                                <!-- Right Section: QR Code -->
                                                <div class="justify-center items-center">
                                                    <img ref="qrcode" :src="qrCodeBase64 ? qrCodeBase64 : ''"
                                                        alt="QR Code" class="mt-4 w-full" />
                                                    <!-- Timer Display -->
                                                    <!-- Timer Display -->
                                                    <div class="text-center mb-3">
                                                        <p class="text-sm text-green-600">Next check in: {{
                                                            60 - timer }}
                                                            seconds
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <!--loader circle-->
                                            <div v-if="loading_qrcode"
                                                class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
                                                <CircleLoader :loading="true"></CircleLoader>
                                            </div>

                                        </div>

                                        <!-- If whatsapp_id exists -->
                                        <div v-else>
                                            <!-- Disconnected Status -->
                                            <div v-if="!whatsapp_data || whatsapp_data.whatsapp_status === 0"
                                                class="text-center bg-white p-6 space-y-4">
                                                <!-- <img src="https://via.placeholder.com/150" alt="Disconnected"
                                                    class="w-24 h-24 mx-auto rounded-full border-4 border-red-500 shadow-lg" /> -->
                                                <div
                                                    class="w-24 h-24 mx-auto rounded-full bg-red-500 flex items-center justify-center text-white text-2xl font-bold">
                                                    <font-awesome-icon icon="fa-solid fa-plug-circle-xmark"
                                                        class="text-4xl" />
                                                </div>
                                                <p class="text-red-500 text-lg font-semibold mt-2">Status: OFF</p>
                                                <button @click="retryQRCode"
                                                    class="bg-red-600 text-white py-2 px-6 rounded-full hover:bg-red-500 transition-all duration-300">
                                                    Retry <font-awesome-icon icon="fa-solid fa-rotate-right"
                                                        rotation=180 class="px-1" />
                                                </button>
                                            </div>

                                            <div v-else-if="whatsapp_data.whatsapp_status === 1"
                                                class="text-center bg-white p-6 space-y-4">
                                                <div v-if="whatsapp_data.whatsapp_auth"
                                                    class="flex flex-col items-center space-x-4 justify-center mb-4">
                                                    <!-- User Avatar -->
                                                    <img v-if="whatsapp_data.whatsapp_auth.avatar"
                                                        :src="whatsapp_data.whatsapp_auth.avatar" alt="User Avatar"
                                                        class="w-20 h-20 sm:w-28 sm:h-28 rounded-full"
                                                        onerror="this.onerror=null; this.src='./images/horizontal_bar/Whatsapp.png';" />
                                                    <!-- <img v-if="whatsapp_data.whatsapp_auth.avatar"
                                                        :src="whatsapp_data.whatsapp_auth.avatar" alt="User Avatar"
                                                        class="w-20 h-20 sm:w-28 sm:h-28 rounded-full" /> -->
                                                    <div class="text-center mt-4">
                                                        <!-- User Name -->
                                                        <p class="text-lg font-semibold">{{
                                                            whatsapp_data.whatsapp_auth.name.replace('@s.whatsapp.net',
                                                                '') || 'Unknown User' }}</p>
                                                        <p class="text-sm text-gray-600">
                                                            +{{ whatsapp_data.whatsapp_auth.id.split(':')[0] ||
                                                                'No ID available' }}
                                                        </p>
                                                    </div>
                                                </div>
                                                <!--<img src="https://via.placeholder.com/150" alt="Connected"
                                                    class="w-24 h-24 mx-auto rounded-full border-4 border-green-500 shadow-lg" /> -->
                                                <button @click="openDialog"
                                                    class="absolute -top-2 right-1 sm:top-6 sm:right-6 bg-red-500 text-white p-2 sm:py-2 sm:px-4 rounded text-sm hover:bg-red-600 focus:outline-none">
                                                    <font-awesome-icon icon="fa-solid fa-plug-circle-xmark" />
                                                    Disconnect
                                                </button>
                                                <div class="flex justify-center items-center">
                                                    <p class="text-green-500 text-lg font-semibold mt-2">
                                                        <font-awesome-icon icon="fa-solid fa-circle-check"
                                                            class="text-xl" /> Connected
                                                    </p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-for="(template, key) in templates_list" :key="key"
                                    :class="{ 'hidden': activeKey === 'profile' }">
                                    <!--variables-->
                                    <!--display options-->
                                    <div v-if="variables && Object.keys(variables).length > 0 && variables[activeKey]"
                                        class="ml-1 sm:ml-4 no-capitalize">
                                        <p class="text-left font-bold bg-gray-300 p-1">{{
                                            formatKey(activeKey) }}
                                            Variable</p>
                                        <div class="text-sm  grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 py-1">
                                            <div v-for="(list, index) in variables[activeKey]" :key="index"
                                                class="flex justify-start items-center border-b">
                                                <p class="px-2">{{ list.label }} :</p>
                                                <p class="px-2 text-[#c90546] cursor-pointer relative"
                                                    @click="copyToClipboard(list.key, index)"
                                                    @mouseover="hoverIndex = index" @mouseleave="hoverIndex = null">
                                                    <span>{{ '{' }}</span>{{ list.key }}<span>{{ '}' }}</span>
                                                    <font-awesome-icon icon="fa-solid fa-copy"
                                                        :class="['pl-2', hoverIndex === index ? 'text-gray-500' : 'text-gray-400']" />

                                                    <!-- Hover Tooltip -->
                                                <div v-if="hoverIndex === index && copiedIndex !== index"
                                                    class="absolute -top-7 right-2 p-1 bg-black text-white text-xs rounded z-5">
                                                    Copy
                                                </div>

                                                <!-- Copied Feedback Tooltip -->
                                                <div v-if="copiedIndex === index"
                                                    class="absolute -top-7 right-2 p-1 bg-black text-white text-xs rounded">
                                                    Copied! ✅
                                                </div>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <transition name="fade">
                                        <div v-show="activeKey === key" class="ml-1 sm:ml-4 mt-2 text-sm">
                                            <p class="text-left font-bold bg-gray-300 p-1 my-2">{{ formatKey(activeKey)
                                            }}
                                                Templates</p>
                                            <!-- Handle if Value is String -->
                                            <div v-if="typeof template === 'string'" class="relative">
                                                <!-- Reset button above the input field -->
                                                <button @click="resetField(key)"
                                                    class="absolute right-2 top-2 text-red-500 hover:text-red-700 mb-2">
                                                    Reset
                                                </button>
                                                <textarea v-model="formValues[key]" class="w-full p-2 border"
                                                    :class="{ 'border-red-500': hasError[key] ? hasError[key] : false }"
                                                    :placeholder="'Enter message for ' + formatKey(key)" rows="10"
                                                    @input="validateField(key, formValues[key])"></textarea>
                                                <p v-if="hasError[key]" class="text-red-500 text-sm mt-1">You cannot
                                                    <!-- Check if key is 'services' and display a custom message -->
                                                    <template v-if="key === 'services'">
                                                        Make sure the variables in the curly braces {} match the
                                                        {{ key }} variables listed in the help info table. Some
                                                        keys might be missing or incorrect.
                                                    </template>
                                                    <!-- Default error message for other cases -->
                                                    <template v-else>
                                                        You cannot modify content inside curly braces {}
                                                    </template>
                                                </p>
                                            </div>
                                            <!-- Handle if Value is Object (Subkeys) -->
                                            <div v-if="typeof template === 'object'">
                                                <div v-for="(subTemplate, subKey) in template" :key="subKey"
                                                    class="mb-4">
                                                    <div class="font-semibold pb-1 text-blue-600">{{ formatKey(subKey)
                                                    }}
                                                    </div>
                                                    <!-- Subsections for Arrays -->
                                                    <div v-if="Array.isArray(subTemplate)">
                                                        <div v-for="(subValue, index) in subTemplate" :key="index"
                                                            class="mt-2 relative">
                                                            <!-- Reset button above the input field -->
                                                            <button @click="resetField(key + ',' + subKey)"
                                                                class="absolute right-2 top-2 text-red-500 hover:text-red-700 mb-2">
                                                                Reset
                                                            </button>
                                                            <textarea v-model="formValues[key][subKey][index]"
                                                                class="w-full p-2 border"
                                                                :class="{ 'border-red-500': hasError[key][subKey] ? hasError[key][subKey] : false }"
                                                                :placeholder="'Enter message for ' + formatKey(subKey)"
                                                                rows="10"
                                                                @input="validateField(key + ',' + subKey, formValues[key][subKey][index])"></textarea>
                                                            <p v-if="hasError[key][subKey]"
                                                                class="text-red-500 text-sm mt-1">
                                                                <!-- Check if key is 'services' and display a custom message -->
                                                                <template v-if="key === 'services'">
                                                                    Make sure the variables in the curly braces {} match
                                                                    the
                                                                    {{ key }} variables listed in the help info table.
                                                                    Some
                                                                    keys might be missing or incorrect.
                                                                </template>
                                                                <!-- Default error message for other cases -->
                                                                <template v-else>
                                                                    You cannot modify content inside curly braces {}
                                                                </template>
                                                            </p>
                                                        </div>
                                                    </div>
                                                    <div v-else-if="typeof subTemplate === 'object'">
                                                        <div v-if="subTemplate && Object.keys(subTemplate).length > 0">
                                                            <div v-for="(supersub, i) in Object.keys(subTemplate)"
                                                                :key="i" class="mt-2 relative">
                                                                <div v-if="formatKey(supersub) !== 'Message' && formatKey(supersub) !== 'Status'"
                                                                    class="font-semibold pb-1 text-blue-400">
                                                                    {{ formatKey(supersub) }}
                                                                </div>
                                                                <!-- Toggle Switch for Status -->
                                                                <div v-if="formatKey(supersub) === 'Status'"
                                                                    class="absolute -top-10 left-[40%] sm:left-[30%] lg:left-[20%] flex inline items-center space-x-2 mt-2">
                                                                    <label
                                                                        class="relative inline-flex items-center cursor-pointer">
                                                                        <!-- Hidden checkbox for toggling -->
                                                                        <input type="checkbox"
                                                                            v-model="formValues[key][subKey][supersub]"
                                                                            class="sr-only hidden"
                                                                            :class="{ 'bg-green-500': formValues[key][subKey][supersub], 'bg-gray-300': !formValues[key][subKey][supersub] }" />

                                                                        <!-- Custom switch design -->
                                                                        <span
                                                                            class="w-10 h-6 rounded-full relative transition-colors duration-300 ease-in-out"
                                                                            :class="{ 'bg-green-500': formValues[key][subKey][supersub], 'bg-gray-300': !formValues[key][subKey][supersub] }">
                                                                            <span
                                                                                class="absolute w-4 h-4 bg-white rounded-full shadow-md top-1 left-1 transition-transform duration-300 ease-in-out"
                                                                                :class="{ 'transform translate-x-4': formValues[key][subKey][supersub], 'translate-x-0': !formValues[key][subKey][supersub] }" />
                                                                        </span>
                                                                    </label>
                                                                    <span class="text-sm ml-2"
                                                                        :class="{ 'text-green-500': formValues[key][subKey][supersub], 'text-gray-600': !formValues[key][subKey][supersub] }">
                                                                        {{ formValues[key][subKey][supersub] ? 'ON' :
                                                                            'OFF' }}
                                                                    </span>
                                                                </div>
                                                                <!-- Handle string case (supersub is a string) -->
                                                                <div v-if="typeof subTemplate[supersub] === 'string'"
                                                                    class="relative">
                                                                    <!-- Reset button above the input field -->
                                                                    <button
                                                                        @click="resetField(key + ',' + subKey + ',' + supersub)"
                                                                        class="absolute right-2 -top-6 text-red-500 hover:text-red-700 mb-2">
                                                                        Reset
                                                                    </button>
                                                                    <textarea
                                                                        v-model="formValues[key][subKey][supersub]"
                                                                        class="w-full p-2 border"
                                                                        :class="{ 'border-red-500': hasError[key][subKey][supersub] ? hasError[key][subKey][supersub] : false }"
                                                                        :placeholder="'Enter message for ' + formatKey(supersub)"
                                                                        rows="10"
                                                                        @input="validateField(key + ',' + subKey + ',' + supersub, formValues[key][subKey][supersub])">
                        </textarea>
                                                                    <p v-if="hasError[key][subKey][supersub]"
                                                                        class="text-red-500 text-sm mt-1">
                                                                        <!-- Check if key is 'services' and display a custom message -->
                                                                        <template v-if="key === 'services'">
                                                                            Make sure the variables in the curly braces
                                                                            {} match the
                                                                            {{ key }} variables listed in the help info
                                                                            table. Some
                                                                            keys might be missing or incorrect.
                                                                        </template>
                                                                        <!-- Default error message for other cases -->
                                                                        <template v-else>
                                                                            You cannot modify content inside curly
                                                                            braces {}
                                                                        </template>
                                                                    </p>
                                                                </div>
                                                                <!-- Handle object case (supersub is an object) -->
                                                                <div
                                                                    v-else-if="typeof subTemplate[supersub] === 'object'">
                                                                    <div v-for="(nestedKey, nestedIndex) in Object.keys(subTemplate[supersub])"
                                                                        :key="nestedIndex" class="mt-2">
                                                                        <div v-if="formatKey(nestedKey) !== 'Message' && formatKey(nestedKey) !== 'Status'"
                                                                            class="pb-1 text-gray-500">
                                                                            {{ formatKey(nestedKey) }}
                                                                        </div>
                                                                        <!-- Toggle Switch for Status -->
                                                                        <div v-if="formatKey(nestedKey) === 'Status'"
                                                                            class="absolute -top-2 left-[40%] sm:left-[30%] lg:left-[20%] flex inline items-center space-x-2 mt-2">
                                                                            <label
                                                                                class="relative inline-flex items-center cursor-pointer">
                                                                                <!-- Hidden checkbox for toggling -->
                                                                                <input type="checkbox"
                                                                                    v-model="formValues[key][subKey][supersub][nestedKey]"
                                                                                    class="sr-only hidden"
                                                                                    :class="{ 'bg-green-500': formValues[key][subKey][supersub][nestedKey], 'bg-gray-300': !formValues[key][subKey][supersub][nestedKey] }" />

                                                                                <!-- Custom switch design -->
                                                                                <span
                                                                                    class="w-10 h-6 rounded-full relative transition-colors duration-300 ease-in-out"
                                                                                    :class="{ 'bg-green-500': formValues[key][subKey][supersub][nestedKey], 'bg-gray-300': !formValues[key][subKey][supersub][nestedKey] }">
                                                                                    <span
                                                                                        class="absolute w-4 h-4 bg-white rounded-full shadow-md top-1 left-1 transition-transform duration-300 ease-in-out"
                                                                                        :class="{ 'transform translate-x-4': formValues[key][subKey][supersub][nestedKey], 'translate-x-0': !formValues[key][subKey][supersub][nestedKey] }" />
                                                                                </span>
                                                                            </label>
                                                                            <span class="text-sm ml-2"
                                                                                :class="{ 'text-green-500': formValues[key][subKey][supersub][nestedKey], 'text-gray-600': !formValues[key][subKey][supersub][nestedKey] }">
                                                                                {{
                                                                                    formValues[key][subKey][supersub][nestedKey]
                                                                                        ? 'ON' : 'OFF' }}
                                                                            </span>
                                                                        </div>
                                                                        <div
                                                                            :class="{ 'hidden': nestedKey === 'status' }">
                                                                            <!-- Reset button above the input field -->
                                                                            <button
                                                                                @click="resetField(key + ',' + subKey + ',' + supersub + ',' + nestedKey)"
                                                                                class="absolute right-2 -top-1 text-red-500 hover:text-red-700 mb-2">
                                                                                Reset
                                                                            </button>

                                                                            <textarea
                                                                                v-model="formValues[key][subKey][supersub][nestedKey]"
                                                                                class="w-full p-2 border"
                                                                                :class="{ 'border-red-500': hasError[key][subKey][supersub][nestedKey] ? hasError[key][subKey][supersub][nestedKey] : false }"
                                                                                :placeholder="'Enter message for ' + formatKey(nestedKey)"
                                                                                rows="10"
                                                                                @input="validateField(key + ',' + subKey + ',' + supersub + ',' + nestedKey, formValues[key][subKey][supersub][nestedKey])">
                                </textarea>
                                                                            <p v-if="hasError[key][subKey][supersub][nestedKey]"
                                                                                class="text-red-500 text-sm mt-1">
                                                                                <!-- Check if key is 'services' and display a custom message -->
                                                                                <template v-if="key === 'services'">
                                                                                    Make sure the variables in the curly
                                                                                    braces {} match the
                                                                                    {{ key }} variables listed in the
                                                                                    help
                                                                                    info table. Some
                                                                                    keys might be missing or incorrect.
                                                                                </template>
                                                                                <!-- Default error message for other cases -->
                                                                                <template v-else>
                                                                                    You cannot modify content inside
                                                                                    curly braces {}
                                                                                </template>
                                                                            </p>
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <!-- Normal Object Handling -->
                                                    <div v-else class="relative">
                                                        <!-- Reset button above the input field -->
                                                        <button @click="resetField(key + ',' + subKey)"
                                                            class="absolute right-2 -top-6 text-red-500 hover:text-red-700 mb-2">
                                                            Reset
                                                        </button>
                                                        <textarea v-model="formValues[key][subKey]"
                                                            class="w-full p-2 border"
                                                            :class="{ 'border-red-500': hasError[key][subKey] ? hasError[key][subKey] : false }"
                                                            :placeholder="'Enter message for ' + formatKey(subKey)"
                                                            rows="10"
                                                            @input="validateField(key + ',' + subKey, formValues[key][subKey])"></textarea>
                                                        <p v-if="hasError[key][subKey]"
                                                            class="text-red-500 text-sm mt-1">
                                                            <!-- Check if key is 'services' and display a custom message -->
                                                            <template v-if="key === 'services'">
                                                                Make sure the variables in the curly braces {} match the
                                                                {{ key }} variables listed in the help info table. Some
                                                                keys might be missing or incorrect.
                                                            </template>
                                                            <!-- Default error message for other cases -->
                                                            <template v-else>
                                                                You cannot modify content inside curly braces {}
                                                            </template>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </transition>
                                </div>
                            </div>
                        </div>
                        <div
                            class="fixed sm: bottom-12 lg:bottom-0 left-0 right-0 flex justify-center items-center m-4 mb-4 lg:mb-2">
                            <!-- Save Button Fixed at the Bottom and Centered -->
                            <button @click="saveSettings"
                                class="bg-green-600 text-white w-1/2 sm:w-1/4 lg:w-1/6 py-2 px-5 rounded-md block hover:bg-green-500">
                                <font-awesome-icon icon="fa-regular fa-floppy-disk" class="px-1" /> {{ type === 'add' ?
                                    'Save' : 'Update' }}
                            </button>
                        </div>
                    </div>

                </div>
            </div>
            <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
            <Loader :showModal="open_loader"></Loader>
            <whatsApTemplateHelp :show-modal="show_available" @close-Modal="closeAvailableOptions" :isMobile="isMobile"
                :variables="variables">
            </whatsApTemplateHelp>
            <dialogConfirmBox :visible="dialog_confirm" :message="dialog_message" @ok="disconnectWhatsApp"
                @cancel="cancelDialog">
            </dialogConfirmBox>
            <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
            <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
        </div>
    </div>
</template>

<script>
import dialogAlert from '@/components/supporting/dialog_box/dialogAlert.vue';
import { mapActions, mapGetters } from 'vuex';
import whatsApTemplateHelp from '../dialog_box/whatsApTemplateHelp.vue';
import dialogConfirmBox from '../dialog_box/dialogConfirmBox.vue';
import noAccessModel from '../dialog_box/noAccessModel.vue';
export default {
    name: 'whatsapp',
    emits: ['updateIsOpen'],
    props: {
        isMobile: Boolean,
        store_refresh: Boolean,
        updateModalOpen: Boolean,
    },
    components: {
        dialogAlert,
        whatsApTemplateHelp,
        dialogConfirmBox,
        noAccessModel
    },
    data() {
        return {
            formValues: {},
            type: 'add',
            open_message: false,
            message: '',
            //--skeleton
            open_skeleton: false,
            number_of_columns: 1,
            number_of_rows: 4,
            gap: 5,
            open_loader: false,
            //--templates list--
            templates_list: {},
            activeKey: null,
            hasError: {},
            //---whatsapp data---
            whatsapp_data: {},
            retry_qr: false, // Flag to trigger QR code retry
            timer: 0, // Timer to track time (in seconds)
            interval: null, // Interval ID for polling
            qrCodeBase64: null,
            loading_qrcode: false,
            //---info---
            show_available: false,
            variables: {
                services: [
                    { label: 'Customer Name', key: 'customer_name' },
                    { label: 'Service Code', key: 'service_code' },
                    { label: 'Device Brand', key: 'device_brand' },
                    { label: 'Device Model', key: 'device_model' },
                    { label: 'Company / Shop Name', key: 'company_name' },
                    { label: 'Service Track customer URL link', key: 'long_url' },
                    { label: 'Service Final Amount', key: 'final_amount' },
                    { label: 'Problem title', key: 'problem_title' },
                    { label: 'Service Category', key: 'service_category' },
                    { label: 'Estimate Amount', key: 'estimate_amount' },
                    { label: 'Paid Amount', key: 'advance_amount' },
                    { label: 'Discount value', key: 'discount' },
                    { label: 'Employee Name', key: 'employee_name' },
                    { label: 'Employee Contact', key: 'employee_contact' },
                ]
            },
            //--dialog--
            dialog_confirm: false,
            dialog_message: 'Are you sure you want to disconnect the WhatsApp?',
            //---toaster--
            show: false,
            message: '',
            type_toaster: 'info',
            //---no access---
            no_access: false,
            copiedIndex: null,
            hoverIndex: null,
            copyTimeout: null, // Track the index of the copied item
        };
    },
    computed: {
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData']),
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('companies', ['fetchCompanyList']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'updateData']),
        formatKey(key) {
            return key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        },

        // Toggle visibility function, stop fetching QR codes when switching active keys
        toggleVisibility(key) {
            if (this.activeKey === key) {
                this.activeKey = key;
                clearInterval(this.interval); // Stop the interval when toggling visibility
                this.interval = 0;
                this.retry_qr = false;
            } else {
                this.activeKey = key;
                clearInterval(this.interval);
                this.retry_qr = false;
                this.interval = 0;
            }
            // console.log(this.whatsapp_data, 'GGGGGGGGGGGGG');

            if (this.whatsapp_data && Object.keys(this.whatsapp_data).length == 0 && key === 'profile') {
                clearInterval(this.interval);
                this.retryQRCode();
            }
        },

        async validateField(key, value) {
            let keyData = key.split(',');  // Split the key into parts
            let template = '';  // Get the original template for this key
            const regex = /\{([^}]+)\}/g;  // Detect content inside {}   
            // console.log(keyData && keyData.length > 0 && keyData[0] === 'services', 'GGGGGGGGGGGGGGGGG');

            if (keyData && keyData.length > 0 && keyData[0] === 'services') {
                if (value && this.variables[keyData[0]]) {
                    let match;
                    // Reset errors before validating
                    await this.resetHasErrorForNestedKey(keyData, false);  // Reset the error state for the key

                    // Loop over the value to find any content inside {}
                    while ((match = regex.exec(value)) !== null) {
                        const contentInsideBrackets = match[1];  // Extract content inside the brackets
                        // Check if the content inside {} exists as a key in the services list
                        const variableExists = this.variables[keyData[0]].some(service => service.key === contentInsideBrackets);

                        // If the variable is not found in the services list, flag as error
                        if (!variableExists) {
                            if (keyData && keyData.length > 0) {
                                await this.resetHasErrorForNestedKey(keyData, true);  // Set error for the modified field
                            }
                            return;  // Stop further processing once an error is found
                        }
                    }
                }
            } else {
                // Traverse the templates_list using the split parts
                if (keyData && keyData.length > 0) {
                    template = this.templates_list[this.activeKey];

                    // Use the rest of the keyData to traverse deeper into the nested structure
                    template = keyData.reduce((acc, currentKey) => {
                        return acc ? acc[currentKey] : undefined;  // Traverse the nested structure
                    }, this.templates_list);  // Start from this.templates_list
                }


                // Reset errors before validating
                if (keyData && keyData.length > 0) {
                    await this.resetHasErrorForNestedKey(keyData, false);  // Reset the error state for the key
                }

                let match;
                let templateContent = template;
                // Loop over the value to find any content inside {}
                while ((match = regex.exec(templateContent)) !== null) {
                    const contentInsideBrackets = match[1];  // Extract content inside the brackets

                    // Check if the same content inside {} exists in the user input
                    const userInputRegex = new RegExp(`\{${contentInsideBrackets}\}`, 'g');
                    const originalPlaceholderMatch = userInputRegex.test(value);

                    // If the content inside {} is modified by the user, flag as error
                    if (!originalPlaceholderMatch) {
                        if (keyData && keyData.length > 0) {
                            await this.resetHasErrorForNestedKey(keyData, true);  // Set error for the modified field
                        }
                        return;  // Stop further processing once an error is found
                    }
                }
            }
        },

        // Function to reset error state for the nested keys in `hasError`
        async resetHasErrorForNestedKey(keyData, value) {

            if (keyData && keyData.length > 0) {
                let currentObject = this.hasError;  // Start with the root object (this.hasError)

                // Traverse through keyData to reach the last key
                for (let i = 0; i < keyData.length - 1; i++) {
                    const currentKey = keyData[i];

                    // If the current object doesn't have the key, initialize it as an empty object
                    if (!currentObject[currentKey]) {
                        currentObject[currentKey] = {};  // Initialize missing keys as empty objects
                    }
                    // Move deeper into the nested object
                    currentObject = currentObject[currentKey];
                }
                // Set the value for the last key in the path
                currentObject[keyData[keyData.length - 1]] = value;
            }
        },
        // Recursive method to initialize `hasError` based on `template` structure
        initializeHasError(template, hasError = this.hasError) {
            // Iterate over each key in the template
            for (const key in template) {
                if (template.hasOwnProperty(key)) {
                    // If the value is an object (but not an array), initialize it recursively
                    if (typeof template[key] === 'object' && !Array.isArray(template[key])) {
                        // If the object exists in `hasError`, ensure it's initialized as an object
                        if (!hasError[key]) {
                            hasError[key] = {};  // Initialize the error object for this key
                        }

                        // Recurse for nested objects
                        this.initializeHasError(template[key], hasError[key]);

                    } else if (typeof template[key] === 'string') {
                        // If the value is a string, initialize `hasError[key]` to false (no error)
                        if (!hasError[key]) {
                            hasError[key] = false;  // Initialize error state for string fields
                        }
                    }
                }
            }
        },
        // Check if there are any errors in the form
        hasErrors() {
            // Recursively check the `hasError` object to ensure all keys are false
            const checkErrors = (obj) => {
                for (const key in obj) {
                    if (obj[key] === true) {
                        return true; // If any key is true, we have an error
                    }
                    if (typeof obj[key] === 'object') {
                        // If it's an object, recursively check its nested keys
                        if (checkErrors(obj[key])) {
                            return true;
                        }
                    }
                }
                return false; // No errors found
            };

            return checkErrors(this.hasError);
        },

        saveSettings() {
            if (this.getplanfeatures('whatsapp_msg')) {
                this.no_access = true;
            } else {
                // Check for errors before allowing save
                if (this.hasErrors()) {
                    this.openMessage('Please fix all errors before saving!');
                    return; // Stop further execution if there are errors
                }
                // Your save logic here
                if (this.formValues) {
                    this.open_loader = true;
                    let sent_data = {
                        company_id: this.currentLocalDataList.company_id, whatsapp: JSON.stringify(this.formValues)
                    };
                    this.open_loader = false;
                    axios.put(`/company_settings/${this.currentLocalDataList.company_id}`, sent_data)
                        .then(response => {
                            // this.formValues = response.data.data;
                            this.updateData();
                            this.open_loader = false;
                            this.openMessage(response.data.message);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.openMessage(error.response.data.message);
                            this.open_loader = false;
                        })
                } else {
                    this.openMessage('Please validate Templates..!');
                }
            }
        },
        openMessage(msg) {
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.open_message = false;
            this.message = '';
        },
        updateTemplateList(data) {
            this.templates_list = JSON.parse(data);
            // console.log(this.templates_list, 'What happening in the data...');
            this.formValues = JSON.parse(data);
            this.initializeHasError(this.templates_list); // Initialize the error tracking structure
            // Set the first key as the active key by default
            const firstKey = Object.keys(this.templates_list)[0];
            this.activeKey = firstKey; // Select the first template key by default
        },
        //---whatsapp templates list--
        getTemplatesList() {
            this.open_skeleton = true;
            axios.get('/whatsapp_templates')
                .then(response => {
                    if (response.data.data) {
                        this.updateTemplateList(response.data.data.value);
                    }
                    this.open_skeleton = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        //---whatsapp templates list--
        getExistData(is_identify) {
            // Show skeleton if data is not identified
            if (!is_identify) {
                this.open_skeleton = true;
            }

            // Fetch company settings data
            axios.get(`/company_settings/${this.currentLocalDataList.company_id}`, {
                params: { company_id: this.currentLocalDataList.company_id }
            })
                .then(response => {
                    // If response has data, process it
                    if (response.data && response.data.data) {
                        this.type = 'edit';
                        this.whatsapp_data = response.data.data;

                        // Safely parse the whatsapp_auth field if it exists
                        if (this.whatsapp_data && this.whatsapp_data.whatsapp_auth) {
                            try {
                                this.whatsapp_data.whatsapp_auth = this.isValidJson(this.whatsapp_data.whatsapp_auth)
                                    ? JSON.parse(this.whatsapp_data.whatsapp_auth)
                                    : null;
                            } catch (e) {
                                console.error("Invalid JSON in whatsapp_auth", e);
                                this.whatsapp_data.whatsapp_auth = null; // Set to null if invalid
                            }
                        }

                        // If not identifying, update template list
                        if (!is_identify) {
                            this.updateTemplateList(this.whatsapp_data.whatsapp);
                        } else {
                            // If whatsapp status exists, reset the timer and QR retry flag
                            if (this.whatsapp_data.whatsapp_status) {
                                clearInterval(this.interval);
                                this.retry_qr = false;
                                this.timer = 0;
                                this.updateData();
                            }
                        }
                    }

                    // If no data or response, handle add type
                    else if (!is_identify) {
                        this.type = 'add';
                        this.getTemplatesList();
                    }

                    // Hide skeleton loading
                    this.open_skeleton = false;
                })
                .catch(error => {
                    console.error('Error fetching company settings data:', error);

                    // Handle error state, reset skeleton, and go to add type
                    this.open_skeleton = false;
                    this.type = 'add';
                    this.getTemplatesList();
                });
        },

        // Utility function to check if a string is valid JSON
        isValidJson(str) {
            try {
                JSON.parse(str);
                return true; // Valid JSON string
            } catch (e) {
                return false; // Invalid JSON string
            }
        },

        resetField(key) {
            // Split the key if it's a nested structure (handle both parent key and subkey)
            const keyData = key.split(',');
            let target = this.formValues;
            let template = this.templates_list;
            let errorTarget = this.hasError;

            // Traverse to the correct position in the formValues, templates_list, and hasError (in case of nested objects)
            for (let i = 0; i < keyData.length - 1; i++) {
                target = target[keyData[i]]; // Traverse formValues
                template = template[keyData[i]]; // Traverse templates_list
                errorTarget = errorTarget[keyData[i]]; // Traverse hasError for nested structure
            }

            // Reset the specific field's value in formValues using the corresponding value from templates_list
            target[keyData[keyData.length - 1]] = template[keyData[keyData.length - 1]];

            // Reset the hasError state for the corresponding key
            errorTarget[keyData[keyData.length - 1]] = false;
        },

        // Get the QR Code by calling the API with instance_id
        async getQRCode() {
            this.loading_qrcode = true;
            axios.get(`whatsapp-qrcode/${this.currentLocalDataList.company_id}`)
                .then(response => {
                    if (response.data.status === 'success') {
                        this.qrCodeBase64 = response.data.base64;
                    }
                    this.loading_qrcode = false;
                    this.startStatusCheckInterval();
                })
                .catch(error => {
                    this.error = error;
                    console.error('Error occurred while fetching QR code:', error);
                    this.loading_qrcode = false;
                    this.retry_qr = false;
                    this.message = 'The server is currently busy. Please try again in 5 minutes. Thank you for your patience!';
                    this.type_toaster = 'info';
                    this.show = true;
                });
        },
        //---retry qr code----
        retryQRCode() {
            this.retry_qr = true; // Retry fetching the QR code
            this.timer = 0; // Reset timer for retry
            this.getQRCode(); // Fetch the QR code again 
            // if (this.loading_qrcode) {
            //     this.startStatusCheckInterval();
            // }

        },
        // Function to start a 10-second interval to validate whatsapp_status
        async startStatusCheckInterval() {
            this.interval = setInterval(async () => {
                try {
                    this.timer += 1; // Increment the timer by 10 seconds
                    // If the status is 1, stop the timer
                    if (this.timer % 5 === 0) {
                        await this.getExistData(true);
                    }
                    // If the timer reaches 60, stop the polling and reset timer
                    if (this.timer === 60) {
                        // clearInterval(this.interval);
                        clearInterval(this.interval);
                        this.retry_qr = false;
                        this.timer = 0;
                        // this.getQRCode(); // Refresh the QR code when timer reaches 60
                    }
                } catch (error) {
                    console.error('Error during status check interval:', error);
                }
            }, 1000);
        },
        //---display available options---
        openAvailableOptions() {
            this.show_available = true;
        },
        closeAvailableOptions() {
            this.show_available = false;
        },
        //--disconnect whatsapp---
        disconnectWhatsApp() {
            this.open_loader = true;
            axios.get(`/whatsapp-disconnect/${this.currentLocalDataList.company_id}`, { params: { id: this.currentLocalDataList.company_id } })
                .then(response => {
                    // console.log(response.data, 'Get exist data');
                    if (response.data.success) {
                        this.updateData();
                        this.getExistData(true);
                        this.open_loader = false;
                        this.cancelDialog();
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                    this.cancelDialog();
                })
        },
        //--dialog box--
        openDialog() {
            this.dialog_confirm = true;
        },
        cancelDialog() {
            this.dialog_confirm = false;
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        copyToClipboard(text, index) {
            // Check if the Clipboard API is available
            if (navigator.clipboard) {
                let textformat = `{${text}}`
                navigator.clipboard.writeText(textformat).then(() => {
                    // Show feedback that the text was copied
                    this.copiedIndex = index;  // Use the passed index directly
                    // Clear previous timeout if any
                    if (this.copyTimeout) clearTimeout(this.copyTimeout);
                    // Hide feedback after 2 seconds
                    this.copyTimeout = setTimeout(() => {
                        this.copiedIndex = null;
                    }, 2000);
                }).catch((err) => {
                    console.error('Failed to copy text:', err);
                });
            } else {
                // Handle the case where the Clipboard API isn't supported
                alert('Clipboard API is not supported in this browser.');
            }
        },

        // To get the current index of the clicked item (used for showing "Copied!" tooltip)
        getCurrentIndex() {
            return this.$refs["keyElement"].index;
        },
    },
    mounted() {
        this.fetchLocalDataList();
        this.fetchCompanyList();
        //---fetch data----
        this.fetchWhatsappList();
        //-----
        if (this.currentLocalDataList.company_id) {
            this.getExistData();
        }
        let querydata = this.$route.query;
        // console.log(querydata, 'query data');
    },
    beforeUnmount() {
        clearInterval(this.interval);
    },
    beforeDestroy() {
        clearInterval(this.interval);
    },
    watch: {
        currentWhatsappData: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    // console.log(newValue, 'What about the data......');
                }
            }
        }
    }
};
</script>

<style scoped>
/* Add some smooth transitions to dropdown */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

/* Add some styling for the horizontal scroll bar on mobile */
@media (max-width: 640px) {
    .w-full {
        width: 100% !important;
    }

    .sm:hidden {
        display: block !important;
    }

    .flex {
        display: flex !important;
        flex-wrap: nowrap;
    }

    .overflow-x-auto {
        overflow-x: auto;
    }

    /* Save button styling */
    .save-button {
        position: fixed;
        bottom: 16px;
        left: 16px;
        right: 16px;
        margin-bottom: 0;
    }
}
</style>
