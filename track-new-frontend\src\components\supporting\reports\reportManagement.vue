<template>
    <div class="text-sm" :class="{ 'mb-[60px]': isMobile, 'mt-5': !isMobile }">
        <div class="my-custom-margin">
            <div>
                <horizontalOpt @change-opt="getChangeOption" :change_option="change_option"></horizontalOpt>
                <selectDateCate v-if="selected_option !== 'Outstanding'" :selected_option="selected_option"
                    @filter-data="filteredData" :refresh="refresh">
                </selectDateCate>
            </div>
            <reportHome v-if="table_data && table_data.length > 0 && selected_option !== 'GSTR'" :isMobile="isMobile"
                :isAndroid="isAndroid" :table_data="table_data" :columns_data="columns_data"
                :selected_option="selected_option" :currentCompanyList="currentCompanyList" :filtered_by="filtered_by"
                :only_sales_service="only_sales_service">
            </reportHome>
            <!--in case empty-->
            <div v-if="table_data.length === 0 && selected_option !== 'GSTR'">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <!-- <p class="text-2xl font-bold text-gray-500 text-center"></p> -->
            </div>
        </div>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'categories'"></bottombar> -->
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import reportHome from './reportHome.vue';
import horizontalOpt from './horizontalOpt.vue';
// import bottombar from '../dashboard/bottombar.vue';
import selectDateCate from './selectDateCate.vue';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'reports_management',
    components: {
        reportHome,
        horizontalOpt,
        // bottombar,
        selectDateCate
    },
    props: {
        isMobile: Boolean,
        isAndroid: Boolean,
        refresh: Boolean
    },

    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            isSelected: false,
            selected_option: '',
            table_data: [],
            columns_data: [],
            filtered_by: {}, //----get filter information...
            change_option: '', //---update options
            open_loader: false,
            only_sales_service: 0, //-----Only service amount total--
            getStatusOption: [{ name: 'Service Taken', total: 0 }, { name: 'Hold', total: 0 }, { name: 'In-progress', total: 0 }, { name: 'New Estimate', total: 0 }, { name: 'Ready To Deliver', total: 0 }, { name: 'Delivered', total: 0 }, { name: 'Cancelled', total: 0 }, { name: 'Completed', total: 0 }],
            table_option: [
                {
                    name: 'Services', columns: [
                        // {
                        //     label: "ID",
                        //     field: "id",
                        //     width: "3%",
                        //     sortable: true,
                        //     isKey: true,
                        // },
                        {
                            label: "Customer",
                            field: "customer",
                            width: "15%",
                            sortable: true,
                        },
                        // {
                        //     label: "Problem Title",
                        //     field: "problem_title",
                        //     width: "15%",
                        //     sortable: true,
                        // },
                        {
                            label: "Category",
                            field: "category",
                            width: "15%",
                            sortable: true,
                        },
                        // {
                        //     label: "Expected Date",
                        //     field: "expected_date",
                        //     width: "15%",
                        //     sortable: true,
                        // },
                        // {
                        //     label: "Service Code",
                        //     field: "service_code",
                        //     width: "15%",
                        //     sortable: true,
                        // },
                        {
                            label: "Assing to",
                            field: "assign_to",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Service type",
                            field: "service_type",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Invoice No",
                            field: "invoice_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Grand Total",
                            field: "grand_total",
                            width: "20%",
                            sortable: true,
                        },
                        {
                            label: "Paid",
                            field: "paid",
                            width: "20%",
                            sortable: true,
                        },
                        {
                            label: "Due",
                            field: "due",
                            width: "20%",
                            sortable: true,
                        },
                        {
                            label: "Status",
                            field: "status",
                            width: "15%",
                            sortable: true,
                        },
                    ],
                },
                {
                    name: 'Sales', columns: [
                        // {
                        //     label: "ID",
                        //     field: "id",
                        //     width: "3%",
                        //     sortable: true,
                        //     isKey: true,
                        // },
                        {
                            label: "Customer",
                            field: "customer",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Invoice No",
                            field: "invoice_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Invoice To",
                            field: "invoice_to",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Invoice Type",
                            field: "invoice_type",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Due In",
                            field: "due_in",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Grand Total",
                            field: "grand_total",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Due Amount",
                            field: "due_amount",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Payment Status",
                            field: "payment_status",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Payment List",
                            field: "payment",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Status",
                            field: "status",
                            width: "15%",
                            sortable: true,
                        }
                    ]
                },
                {
                    name: 'Proforma', columns: [
                        //     {
                        //     label: "ID",
                        //     field: "id",
                        //     width: "3%",
                        //     sortable: true,
                        //     isKey: true,
                        // },
                        {
                            label: "Customer",
                            field: "customer",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Proforma No",
                            field: "proforma_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Grand Total",
                            field: "grand_total",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Invoice No",
                            field: "invoice_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Estimation No",
                            field: "estimation_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Advance Payment",
                            field: "advance_paid",
                            width: "15%",
                            sortable: true,
                        },
                    ]
                },
                {
                    name: 'Estimation', columns: [
                        // {
                        //     label: "ID",
                        //     field: "id",
                        //     width: "3%",
                        //     sortable: true,
                        //     isKey: true,
                        // },
                        {
                            label: "Customer",
                            field: "customer",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Estimate No",
                            field: "estimate_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Grand total",
                            field: "grand_total",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Invoice / Proforma",
                            field: "invoice_id",
                            width: "15%",
                            sortable: true,
                        },
                    ]
                },
                {
                    name: 'Purchase', columns:
                        [
                            //     {
                            //     label: "ID",
                            //     field: "id",
                            //     width: "3%",
                            //     sortable: true,
                            //     isKey: true,
                            // },
                            {
                                label: "Supplier",
                                field: "supplier",
                                width: "15%",
                                sortable: true,
                            },
                            {
                                label: "Purchase Order",
                                field: "purchase_no",
                                width: "15%",
                                sortable: true,
                            },
                            {
                                label: "Grand Total",
                                field: "grand_total",
                                width: "15%",
                                sortable: true,
                            },
                            {
                                label: "Paid",
                                field: "paid",
                                width: "15%",
                                sortable: true,
                            },
                            {
                                label: "Due",
                                field: "due",
                                width: "15%",
                                sortable: true,
                            },
                            {
                                label: "Due Intervals",
                                field: "due_interval",
                                width: "15%",

                                sortable: true,
                            }]
                },
                {
                    name: 'Leads', columns: [
                        // {
                        //     label: "ID",
                        //     field: "id",
                        //     width: "3%",
                        //     sortable: true,
                        //     isKey: true,
                        // },
                        {
                            label: "Customer",
                            field: "customer",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Title",
                            field: "title",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Assign to",
                            field: "assign_to",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Type",
                            field: "type",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Followup",
                            field: "follow_up",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Status",
                            field: "status",
                            width: "15%",
                            sortable: true,
                        },
                    ]
                },
                {
                    name: 'AMC', columns: [
                        // {
                        //     label: "ID",
                        //     field: "id",
                        //     width: "3%",
                        //     sortable: true,
                        //     isKey: true,
                        // },
                        {
                            label: "Customer",
                            field: "customer",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Title",
                            field: "title",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Assign to",
                            field: "assign_to",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Type",
                            field: "type",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Num of Services",
                            field: "num_of_service",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Status",
                            field: "status",
                            width: "15%",
                            sortable: true,
                        },
                    ]
                },
                {
                    name: 'Expense', columns: [
                        // {
                        //     label: "ID",
                        //     field: "id",
                        //     width: "3%",
                        //     sortable: true,
                        //     isKey: true,
                        // },
                        {
                            label: "Date",
                            field: "date",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Purpose",
                            field: "purpose",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Type",
                            field: "type",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Amount",
                            field: "amount",
                            width: "15%",
                            sortable: true,
                        }
                    ]
                },
                {
                    name: 'Stock', columns: [
                        // {
                        //     label: "ID",
                        //     field: "id",
                        //     width: "3%",
                        //     sortable: true,
                        //     isKey: true,
                        // },
                        {
                            label: "Product",
                            field: "product",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Sales Price",
                            field: "sales_price",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Purchase Price",
                            field: "purchase_price",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Total Qty",
                            field: "total_qty",
                            width: "15%",
                            sortable: true,
                        }
                    ]
                },
                {
                    name: 'RMA', columns: [
                        {
                            label: "RMA ID",
                            field: "rma_id",
                            width: "3%",
                            sortable: true,
                            isKey: true,
                        },
                        {
                            label: "Customer",
                            field: "customer",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Product",
                            field: "product",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Serial No",
                            field: "serial_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Complete Date",
                            field: "complete_date",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Grand Total",
                            field: "grand_total",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Status",
                            field: "status",
                            width: "15%",
                            sortable: true,
                        },
                    ]
                },
                {
                    name: 'Customer Ledger', columns: [
                        {
                            label: "Date",
                            field: "date",
                            width: "3%",
                            sortable: true,
                            // isKey: true,
                        },
                        {
                            label: "Transaction Type",
                            field: "transaction_type",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Transaction No.",
                            field: "transaction_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Original Invoice No",
                            field: "original_invoice_no",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Credit",
                            field: "credit",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Debit",
                            field: "debit",
                            width: "15%",
                            sortable: true,
                        },
                        // {
                        //     label: "TDS By Party",
                        //     field: "tds_by_party",
                        //     width: "15%",
                        //     sortable: true,
                        // },
                        // {
                        //     label: "TDS By Self",
                        //     field: "tds_by_self",
                        //     width: "15%",
                        //     sortable: true,
                        // },
                        {
                            label: "Payment Mode",
                            field: "payment_mode",
                            width: "15%",
                            sortable: true,
                        },
                    ]
                },
                {
                    name: 'Outstanding', columns: [
                        {
                            label: "Name",
                            field: "name",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Contact",
                            field: "contact_number",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Address",
                            field: "address",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "GST Number",
                            field: "gst_number",
                            width: "15%",
                            sortable: true,
                        },
                        {
                            label: "Balance",
                            field: "balance_amount",
                            width: "15%",
                            sortable: true,
                        }
                    ]
                },
            ]
        };
    },
    methods: {
        ...mapActions('companies', ['fetchCompanyList']),
        ...mapActions('customer', ['fetchCustomerList']),

        getChangeOption(data) {
            if (data) {
                this.selected_option = data;
            }
        },
        //---get servie items total---
        // Function to calculate the total amount
        calculateTotalAmount(dataArray) {
            return dataArray.reduce((total, item) => {
                try {
                    // Parse the JSON string in service_items
                    const serviceItems = JSON.parse(item.service_items);

                    // Sum the amount in each service_item
                    const itemTotal = serviceItems.reduce((sum, serviceItem) => sum + ((serviceItem.total * 1) || 0), 0);

                    // Add to the overall total
                    return total + itemTotal;
                } catch (error) {
                    console.error('Error parsing service_items:', error);
                    return total;
                }
            }, 0);
        },

        filteredData(data, filterData) {
            if (filterData) {
                this.filtered_by = filterData;
            }
            if (data && Array.isArray(data) && data.length > 0) {
                let array_data = [];
                if (this.selected_option === 'Services') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            id: opt.id,
                            customer: opt.customer && opt.customer.id
                                ? `${opt.customer.first_name || ''} ${opt.customer.last_name || ''} - ${opt.customer.contact_number || ''}`.trim()
                                : '',
                            problem_title: opt.service_data && JSON.parse(opt.service_data) && JSON.parse(opt.service_data).problem_title && Array.isArray(JSON.parse(opt.service_data).problem_title) ? JSON.parse(opt.service_data).problem_title.join(', ') : '',
                            category: opt.servicecategory && opt.servicecategory.service_category ? opt.servicecategory.service_category : '',
                            expected_date: opt.service_data && JSON.parse(opt.service_data) && JSON.parse(opt.service_data).expected_date ? JSON.parse(opt.service_data).expected_date : '',
                            service_code: opt.service_code,
                            assign_to: opt.assign_to && Array.isArray(opt.assign_to) ? opt.assign_to.map(assign => assign.name).join(', ') : '',
                            service_type: opt.service_data && JSON.parse(opt.service_data) && JSON.parse(opt.service_data).service_type ? JSON.parse(opt.service_data).service_type : '',
                            invoice_no: opt.invoice_id ? opt.invoice_id : '',
                            grand_total: opt.grand_total ? opt.grand_total : '',
                            paid: opt.grand_total ? opt.grand_total - (opt.due_amount >= 0 ? opt.due_amount : 0) : '',
                            due: opt.due_amount >= 0 ? opt.due_amount : 0,
                            status: opt.status >= 0 && this.getStatusOption[opt.status] ? this.getStatusOption[opt.status].name : ''
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[0].columns;
                    }
                }
                else if (this.selected_option === 'Sales') {
                    this.only_sales_service = this.calculateTotalAmount(data);

                    let create_array = data.map((opt) => {
                        array_data.push({
                            id: opt.id,
                            customer: opt.customer && opt.customer.id
                                ? `${opt.customer.first_name || ''} ${opt.customer.last_name || ''} - ${opt.customer.contact_number || ''}`.trim()
                                : '',
                            invoice_no: opt.invoice_id ? opt.invoice_id : '',
                            invoice_to: opt.invoice_to ? opt.invoice_to : '',
                            invoice_type: opt.invoice_type ? opt.invoice_type : '',
                            due_in: opt['due_amount'] > 0 ? this.calculateDaysUntilDue(opt['current_date']) >= 0 ? this.calculateDaysUntilDue(opt['current_date']) + '' +
                                'days to due' : Math.abs(this.calculateDaysUntilDue(opt['current_date'])) + '' + 'days to overdue' : 'no due',
                            grand_total: opt.grand_total ? opt.grand_total : '',
                            due_amount: opt.due_amount ? opt.due_amount : 0,
                            payment_status: opt['due_amount'] > 0 ? 'Unpaid' : 'Paid',
                            payment: this.calculatePayment(opt),
                            status: opt['status'] ? opt['status'] : ''
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[1].columns;
                    }
                }
                else if (this.selected_option === 'Proforma') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            id: opt.id,
                            customer: opt.customers && opt.customers.id
                                ? `${opt.customers.first_name || ''} ${opt.customers.last_name || ''} - ${opt.customers.contact_number || ''}`.trim()
                                : '',
                            proforma_no: opt.proforma_no ? opt.proforma_no : '',
                            grand_total: opt.grand_total ? opt.grand_total : '',
                            invoice_no: opt.sales && opt.sales.invoice_id ? opt.sales.invoice_id : '',
                            estimation_no: opt.estimation && opt.estimation.estimate_num ? opt.estimation.estimate_num : '',
                            advance_paid: this.calculatetotal(opt['payment_mode'])
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[2].columns;
                    }
                }
                else if (this.selected_option === 'Estimation') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            id: opt.id,
                            customer: opt.customers && opt.customers.id
                                ? `${opt.customers.first_name || ''} ${opt.customers.last_name || ''} - ${opt.customers.contact_number || ''}`.trim()
                                : '',
                            estimate_no: opt.estimate_num ? opt.estimate_num : '',
                            grand_total: opt.grand_total ? opt.grand_total : '',
                            invoice_id: opt.invoice_id ? opt.invoice_id : '',
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[3].columns;
                    }
                }
                else if (this.selected_option === 'Purchase') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            id: opt.id,
                            supplier: opt.supplier && opt.supplier_id
                                ? `${opt.supplier.name || ''} - ${opt.supplier.contact_number || ''}`.trim()
                                : '',
                            purchase_no: opt.purchase_order ? opt.purchase_order : '',
                            grand_total: opt.total ? opt.total : '',
                            paid: opt.paid ? opt.paid : 0,
                            due: opt.balance_amount >= 0 ? opt.balance_amount : '',
                            due_interval: opt.due_interval ? opt.due_interval : ''
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[4].columns;
                    }
                }
                else if (this.selected_option === 'Leads') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            id: opt.id,
                            customer: opt.customer && opt.customer.id
                                ? `${opt.customer.first_name || ''} ${opt.customer.last_name || ''} - ${opt.customer.contact_number || ''}`.trim()
                                : '',
                            title: opt.title ? opt.title : '',
                            assign_to: opt.assign_to && Array.isArray(opt.assign_to) ? opt.assign_to.map(assign => assign.name).join(', ') : '',
                            type: opt.lead_type && opt.lead_type.id ? opt.lead_type.name : '',
                            follow_up: opt.follow_up && Array.isArray(opt.follow_up) && opt.follow_up.length > 0 ? opt.follow_up[opt.follow_up.length - 1].date_and_time : '',
                            status: opt.lead_status ? opt.lead_status : '',
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[5].columns;
                    }
                }
                else if (this.selected_option === 'AMC') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            id: opt.id,
                            customer: opt.customer && opt.customer.id
                                ? `${opt.customer.first_name || ''} ${opt.customer.last_name || ''} - ${opt.customer.contact_number || ''}`.trim()
                                : '',
                            title: opt.title ? opt.title : '',
                            assign_to: opt.assign_to && Array.isArray(opt.assign_to) ? opt.assign_to.map(assign => assign.name).join(', ') : '',
                            type: opt.amc_payment_type ? opt.amc_payment_type : '',
                            num_of_service: opt.number_of_service && opt.number_of_service !== '' ? opt.number_of_service : '',
                            status: opt.amc_status ? opt.amc_status : '',
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[6].columns;
                    }
                }
                else if (this.selected_option === 'Expense') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            id: opt.id,
                            date: opt.date ? opt.date : '',
                            purpose: opt.name_of_purpose ? opt.name_of_purpose : '',
                            type: opt.expense_type && opt.expense_type.id ? opt.expense_type.name : '',
                            num_of_service: opt.number_of_service && opt.number_of_service !== '' ? opt.number_of_service : '',
                            amount: opt.amount ? opt.amount : 0,
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[7].columns;
                    }
                }
                else if (this.selected_option === 'Stock') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            // id: opt.id,
                            product: opt.products && opt.products.product_name ? opt.products.product_name : '',
                            sales_price: opt.sales_price ? opt.sales_price : 0,
                            purchase_price: opt.purchase_price ? opt.purchase_price : 0,
                            total_qty: opt.total_qty !== null && opt.total_qty !== undefined ? opt.total_qty : 0
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[8].columns;
                    }
                }
                else if (this.selected_option === 'RMA') {
                    let create_array = data.map((opt) => {
                        array_data.push({
                            rma_id: opt.rma_id,
                            customer: opt.customer && opt.customer.id
                                ? `${opt.customer.first_name || ''} ${opt.customer.last_name || ''} - ${opt.customer.contact_number || ''}`.trim()
                                : '',
                            product: opt.product && opt.product.id ? opt.product.product_name : '',
                            serial_no: opt.serial_number ? opt.serial_number : '',
                            complete_date: opt.complete_date ? opt.complete_date : '',
                            grand_total: opt.total_cost ? opt.total_cost : 0,
                            status: opt.rma_status ? opt.rma_status : '',
                        });
                    })
                    //array_data.length > 0
                    if (array_data && Array.isArray(array_data)) {
                        this.table_data = array_data;
                        this.columns_data = this.table_option[9].columns;
                    }
                } else if (this.selected_option === 'Customer Ledger') {
                    if (data && Array.isArray(data)) {
                        // Process the data and format dates
                        const array_data = data.map((opt) => {
                            const formattedDate = this.formatDate(opt.date);
                            return {
                                ...opt,
                                date: formattedDate // Ensure 'date' is updated
                            };
                        });

                        // console.log(array_data, 'Formatted Array Data');

                        // Set table data and columns if the array is not empty
                        if (array_data.length > 0) {
                            this.table_data = [...array_data];
                            this.columns_data = this.table_option[10].columns;
                        }
                    }
                }
            } else {
                this.table_data = [];
            }
        },
        //---calculate paymet Data--
        calculatePayment(data) {
            let total_payment = 0;
            // If sales_payment exists and has entries
            if (data.sales_payment && data.sales_payment.length > 0) {
                // Sum up all the payment amounts by type (cash, online, card, other)
                let payment_details = data.sales_payment.reduce((acc, payment) => {
                    // Add payment amount to respective payment type
                    if (!acc[payment.payment_type]) {
                        acc[payment.payment_type] = 0;
                    }
                    acc[payment.payment_type] += payment.payment_amount;
                    return acc;
                }, {});
                // Create a display-friendly payment status based on the payment types
                total_payment = Object.values(payment_details).reduce((sum, val) => sum + val, 0);

                // Optionally, you can format the payment details in a user-friendly way
                let payment_info = Object.keys(payment_details).map(key => {
                    return `${key}: ${payment_details[key]}`;
                }).join(", ");

                // Add payment info to the row
                return payment_info = payment_info || 'No payments recorded';
            } else {
                return '0';
            }
        },
        //----Overdue calculation--
        calculateDaysUntilDue(invoiceDateData) {
            if (this.now) {
                const invoiceDate = new Date(invoiceDateData);
                let dueDays = this.currentInvoice && this.currentInvoice.length > 0 ? this.currentInvoice[0].due_duration : 30;
                // console.log(dueDays, 'TT waht happeninggggg going the data....!');
                if (isNaN(invoiceDate.getTime())) {
                    console.error('Invalid invoice date');
                    return 0; // Return 0 as a fallback value in case of error
                }
                if (isNaN(this.now.getTime())) {
                    console.error('Invalid invoice date');
                    return 0; // Return 0 as a fallback value in case of error               
                }

                // Create a new date object for the due date
                const dueDate = new Date(invoiceDate);
                dueDate.setDate(invoiceDate.getDate() + dueDays);

                const currentTime = this.now.getTime();
                const dueTime = dueDate.getTime();

                // Calculate the difference in milliseconds
                const difference = dueTime - currentTime;

                // Convert milliseconds to days
                const differenceInDays = Math.ceil(difference / (1000 * 60 * 60 * 24));

                // If today is counted as the first day, increment the due days
                if (differenceInDays >= 0 && dueDate > this.now && differenceInDays <= dueDays) {
                    return differenceInDays;
                } else if (differenceInDays > dueDays) {
                    return dueDays;
                } else {
                    // Calculate the days until due (positive if due date is in the future, negative if overdue)
                    return differenceInDays;
                }
            }
        },
        validateDateTime(dateTimeString) {
            const datePart = dateTimeString.substring(0, 10);
            const isValidDate = /^\d{4}-\d{2}-\d{2}$/.test(datePart);
            return isValidDate;
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculatetotal(payment_data) {
            // Helper function to calculate total
            // console.log(payment_data, 'WWWWWWWWWWWWWW happening....');
            const calculateTotal = (data) => data.reduce((total, payment) => total + (payment.paid_amount ? payment.paid_amount : payment.payment_amount ? payment.payment_amount : 0), 0);

            // Check if payment_data is a string
            if (typeof payment_data === 'string') {
                try {
                    let payment_details = JSON.parse(payment_data);

                    if (Array.isArray(payment_details) && payment_details.length > 0) {
                        return calculateTotal(payment_details);
                    }
                } catch (error) {
                    console.error("Error parsing payment_data:", error.message);
                    return 0;
                }

            } else if (Array.isArray(payment_data) && payment_data.length > 0) {
                return calculateTotal(payment_data);
            } else {
                // console.error("Invalid payment_data: must be a non-empty array or a valid JSON string representing a non-empty array.");
                return 0;
            }
        },
        //--make format date---
        formatDate(input) {
            const date = new Date(input);

            // Check if the time is midnight (00:00:00)
            if (date.getUTCHours() === 0 && date.getUTCMinutes() === 0 && date.getUTCSeconds() === 0) {
                // Only return the date part in YYYY-MM-DD format
                return date.toISOString().split('T')[0];
            } else {
                // Format the date and time, using toLocaleString for proper formatting
                return date.toLocaleString('en-US', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: 'numeric',
                    minute: '2-digit',
                    hour12: true,
                    timeZone: 'UTC'
                });
            }
        },
        //---filter the customer list----
        filterCustomerList() {
            if (this.currentCustomer && this.currentCustomer.length > 0) {
                let array_data = [];
                let create_array = this.currentCustomer.map((opt) => {
                    array_data.push({
                        name: opt.id
                            ? `${opt.first_name || ''} ${opt.last_name || ''}`.trim()
                            : '',
                        contact_number: opt.contact_number,
                        address: opt.address,
                        gst_number: opt.gst_number,
                        balance_amount: opt.balance_amount,
                    });
                })
                //array_data.length > 0
                if (array_data && Array.isArray(array_data)) {
                    let filter_data = array_data.filter(opt => opt.balance_amount > 0);
                    if (filter_data && filter_data.length > 0) {
                        this.table_data = filter_data;
                    } else {
                        this.table_data = [];
                    }
                    this.columns_data = this.table_option[11].columns;
                } else {
                    this.table_data = [];
                }
            } else {
                this.table_data = [];
            }
            this.open_loader = false;
        }
    },
    mounted() {
        this.fetchCompanyList();
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        if (this.selected_option === 'Outstanding') {
            this.open_loader = true;
            this.filterCustomerList();
        }
        //---get query data---
        let query_data = this.$route.query.type;
        if (query_data) {
            this.selected_option = query_data;
            this.change_option = query_data;
        }
    },
    beforeDestroy() {

    },
    watch: {
        selected_option: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'Waht happening the data......!!');
                this.table_data = [];
                if (newValue === 'Outstanding') {
                    this.open_loader = true;
                    this.fetchCustomerList();
                    this.filterCustomerList();
                }
            }
        },
        refresh: {
            deep: true,
            handler(newValue) {
                this.table_data = [];
            }
        },
        currentCustomer: {
            deep: true,
            handler(newValue) {
                if (this.selected_option === 'Outstanding') {
                    this.filterCustomerList();
                }

            }
        }
    },
    computed: {
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('customer', ['currentCustomer']),
    }
};
</script>

<style scoped></style>