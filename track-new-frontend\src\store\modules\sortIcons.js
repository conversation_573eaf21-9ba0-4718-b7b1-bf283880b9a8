// store/modules/sort.js

export default {
    namespaced: true,
    state: {
        shortVisible: [] // Contains sorting information for columns
    },
    mutations: {
        // Initialize sortVisible data
        SET_SHORT_VISIBLE(state, payload) {
            state.shortVisible = payload;
        },
        // Update visibility on mouseover
        SHOW_SORT_ICON(state, field) {
            const entry = state.shortVisible.find((item) => item.label === field);
            if (entry) entry.visible = true;
        },
        // Reset visibility on mouseleave
        HIDE_SORT_ICON(state, field) {
            const entry = state.shortVisible.find((item) => item.label === field);
            if (entry) entry.visible = false;
        },
        // Update sort type
        UPDATE_SORT_TYPE(state, { field, type }) {
            state.shortVisible.forEach((item) => {
                if (item.label === field) {
                    item.type = type;
                } else {
                    item.type = ''; // Reset other fields
                }
            });
        },
        RESET_STATE(state) {
            state.shortVisible = [];      
          },
    },
    actions: {
        setShortVisible({ commit }, payload) {
            commit('SET_SHORT_VISIBLE', payload);
        },
        showSortIcon({ commit }, field) {
            commit('SHOW_SORT_ICON', field);
        },
        hideSortIcon({ commit }, field) {
            commit('HIDE_SORT_ICON', field);
        },
        updateSortType({ commit }, payload) {
            commit('UPDATE_SORT_TYPE', payload);
        }
    },
    getters: {
        isShortVisible: (state) => (field) => {
            const entry = state.shortVisible.find((item) => item.label === field);
            
            return entry ? entry.visible || entry.type !== '' : false;
        },
        getSortTypeDisplay: (state) => (field) => {
            const entry = state.shortVisible.find((item) => item.label === field);
            return entry ? entry.type || '' : '';
        },
        isHighlighted: (state) => (field) => {
            const entry = state.shortVisible.find((item) => item.label === field);
            return entry ? entry.type !== '' : false;
        }
    }
};
