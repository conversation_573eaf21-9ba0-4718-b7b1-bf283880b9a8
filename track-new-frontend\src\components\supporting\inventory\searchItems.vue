<template>
    <div>
        <!--search bar-->
        <div class="relative">
            <p v-if="searchQuery !== ''" class="absolute flex justify-end left-3 mt-[7px] text-red-500 cursor-pointer"
                @click="clearSearchQuery()">
                <font-awesome-icon icon="fa-solid fa-xmark" />
            </p>
            <span v-else class="absolute text-gray-400 text-lg items-center px-2"><font-awesome-icon
                    icon="fa-solid fa-magnifying-glass" /></span>
            <input type="text" placeholder='Enter product name or code'
                class="border border-gray-300 rounded-lg p-2 py-1 focus:border-blue-500 outline-none lg:w-[300px] w-full pl-[35px]"
                v-model="searchQuery" @input="filteredProduct" @change="filteredProduct" ref="searchInput"
                @keydown.enter="handleEnterKey" @keydown.down.prevent="handleDownArrow(filteredItems_list)"
                @keydown.up.prevent="handleUpArrow(filteredItems_list)" />
            <div v-if="showSuggestions" class="absolute mt-0 bg-white border shadow-md w-full"
                :style="{ 'z-index': 999 }">
                <ul style="max-height: 200px;" class="overflow-auto" v-if="searchQuery && searchQuery.length > 2">
                    <!-- Loading state -->
                    <li v-if="isLoading" class="px-2 py-2 text-center text-gray-500">
                        <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500 inline"
                            xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                            </circle>
                            <path class="opacity-75" fill="currentColor"
                                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                            </path>
                        </svg>
                        Loading...
                    </li>

                    <!-- Results -->
                    <template v-else>
                        <li v-for="(product, index) in filteredItems_list" :key="product.id"
                            class="px-2 cursor-pointer py-1" @click="selectProduct(product)"
                            :class="{ 'bg-gray-200': index === selectedIndex }">
                            {{ product.barcodes.barcode + ' - ' + product.products.product_name }}
                        </li>
                        <li v-if="filteredItems_list && filteredItems_list.length === 0 && searchQuery && searchQuery.length > 2"
                            class="py-1 px-2 bg-gray-100 text-gray-400">Item not found
                        </li>
                    </template>
                </ul>
            </div>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';

export default {
    props: {
        isMobile: Boolean,
        pagination: Object
    },
    data() {
        return {
            searchQuery: '',
            showSuggestions: false,
            selectedIndex: 0,
            filteredItems_list: [],
            recent_get_data: false,
            isLoading: false,
        };
    },
    computed: {
        ...mapGetters('items', ['currentItems', 'currentItemsPagination']),
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleDocumentClick);
    },
    mounted() {
        // if (this.currentItems && this.currentItems.length === 0) {
        //     this.fetchItemList(this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0 ? (Number(this.currentItemsPagination.total) + 50) : 1000);
        // } else {
        //     this.fetchItemList(this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0 ? (Number(this.currentItemsPagination.total) + 50) : 1000);
        // }
    },
    watch: {
        showSuggestions: {
            deep: true,
            handler(newValue) {

            }
        },
        currentItems: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    setTimeout(() => {
                        this.isLoading = false;
                    }, 1000);
                    this.recent_get_data = false;
                    this.filteredProduct();
                }
            }
        }
    },
    methods: {
        ...mapActions('items', ['fetchItemList']),
        filteredProduct() {
            if (this.currentItems && this.currentItems.length > 0) {
                if (this.searchQuery !== '' && this.searchQuery.length > 2) {
                    this.showDropdown();
                    const query = this.searchQuery.toLowerCase();
                    this.filteredItems_list = this.currentItems.filter(product => {
                        const barcode = product.barcodes && product.barcodes.barcode ? product.barcodes.barcode.toLowerCase() : '';

                        const name = product.products && product.products.product_name ? product.products.product_name.toLowerCase() : '';

                        return (
                            barcode.includes(query) || name.includes(query) || (barcode + ' - ' + name).includes(query)
                        );
                    });
                    if (this.filteredItems_list && !this.recent_get_data) {
                        this.recent_get_data = true;

                        if (this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0 && (this.currentItemsPagination.total !== this.currentItems.length)) {
                            this.isLoading = true;
                            this.fetchItemList(this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0 ? (Number(this.currentItemsPagination.total) + 50) : 1000);
                        }
                    }
                    // if (this.filteredItems_list.length > 0 && this.isLoading) {
                    //     this.isLoading = false;
                    // }
                } else {
                    this.filteredItems_list = this.currentItems;
                }
            } else if (this.currentItems.length === 0 && !this.recent_get_data) {
                this.isLoading = true;
                this.recent_get_data = true;
                this.fetchItemList(this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0 ? (Number(this.currentItemsPagination.total) + 50) : 1000);
            }
        },
        clearSearchQuery() {
            this.searchQuery = '';
            this.$emit('resetData', {});
        },
        //------search bar
        //----dropdown---
        filterProduct() {
            if (this.searchQuery !== '' && this.searchQuery.length > 1) {
                // console.log(this.searchQuery, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            } else {
                this.showSuggestions = false;
            }
        },
        showDropdown() {
            // console.log('What happening go there currentItems......!!!!!!');
            if (this.searchQuery !== '') {
                // Show dropdown on input focus
                this.showSuggestions = true;
                // Add a click event listener to the document to close the dropdown when clicking outside
                document.addEventListener('click', this.handleDocumentClick);
            }
        },
        hideDropdown() {
            // Hide dropdown on input blur
            this.showSuggestions = false;
        },
        selectProduct(product) {
            // Handle the selected customer (e.g., emit an event, update currentItems, etc.)
            // console.log('Selected product:', product);
            this.searchQuery = product.barcodes.barcode + ' - ' + product.products.product_name; // Set the input value to the selected customer name
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            this.$emit('searchData', product);
            document.removeEventListener('click', this.handleDocumentClick);
        },
        handleDocumentClick(event) {
            try {
                // Close the dropdown when clicking outside the input and dropdown
                const isClickInside = this.$refs.searchInput.contains(event.target);
                if (!isClickInside) {
                    this.hideDropdown();
                }
            } catch {
                document.removeEventListener('click', this.handleDocumentClick);
            }
        },
        isNumeric(str) {
            // Helper method to check if a string is numeric
            return /^\d+$/.test(str);
        },
        //--on press enter key--
        handleEnterKey() {
            // Check if filteredProductList has at least one item
            if (this.filteredItems_list.length > 0) {
                // Call selectedProductData with the first item in filteredProductList
                this.selectProduct(this.filteredItems_list[this.selectedIndex]);
                this.selectedIndex = 0;
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //---find any one record is editing is true--
        findEditingValue() {
            let findData = this.currentItems.filter((opt) => opt.editing === true);
            if (findData.length !== 0) {
                return true;
            } else {
                return false;
            }
        },
        //---product list
        //---item product name--
        handleProductChange(product_name, index) {
            this.isDropdownOpenProduct = index;
            if (product_name) {
                const enteredProduct = product_name.trim().toLowerCase();

                this.filteredProductList = this.currentItems.filter(opt => {
                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.product_name.toLowerCase().includes(enteredProduct);

                    return nameMatch;
                });
                // console.log(this.filteredProductList, 'Waht happening.......@@@');
            }
        },

        //---selected product
        selectedProductData(item, index, option) {
            // console.log(option, 'option', index, 'index', 'item', item);
            this.isDropdownOpenProduct = false;
        },
        //----control dropdown--
        //----customer dropdown--
        closeDropdown() {
            // console.log('hello input');
            if (!this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpenProduct = false;
            }
        },

    },
};
</script>

<style>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}
</style>