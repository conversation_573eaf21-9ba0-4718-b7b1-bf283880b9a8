<template>
    <div class="p-5 bg-white rounded shadow-md">
      <h1 class="text-xl font-bold mb-4">Contact Page</h1>
      <form @submit.prevent="submitContact">
        <div class="mb-4">
          <label class="block mb-2">Contact Email</label>
          <input type="email" v-model="contactEmail" class="border p-2 rounded w-full" />
        </div>
        <div class="mb-4">
          <label class="block mb-2">Phone Number</label>
          <input type="text" v-model="phoneNumber" class="border p-2 rounded w-full" />
        </div>
        <button class="bg-blue-500 text-white py-2 px-4 rounded" type="submit">Save</button>
      </form>
    </div>
  </template>
  
  <script>
  export default {
    data() {
      return {
        contactEmail: '',
        phoneNumber: '',
      };
    },
    methods: {
      submitContact() {
        alert('Contact information saved!');
        // Handle save logic here
      },
    },
  };
  </script>
  