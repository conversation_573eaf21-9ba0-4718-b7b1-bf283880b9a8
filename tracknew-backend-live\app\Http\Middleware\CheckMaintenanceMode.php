<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;

class CheckMaintenanceMode
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // Check if the application is in maintenance mode
        if (env('APP_MODE') == '1') {
            // Return a maintenance response
            return response()->json([
                'code' => 'maintenance',
                'message' => 'The application is under maintenance. Please try again later.'
            ], 503);
        }

        return $next($request);
    }
}
