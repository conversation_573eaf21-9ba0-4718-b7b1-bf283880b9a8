<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" class="non-printable" @refresh_store="refresh_store"></headbar> -->
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
                :rows="number_of_rows" :gap="gap" :type="'grid'">
            </skeleton>
            <!-- print preview data -->
            <div v-if="!open_skeleton" class="relative w-full sm:px-3 py-3" ref="pdf">
                <paymentIn :isMobile="isMobile" :isAndroid="isAndroid" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></paymentIn>

            </div>
        </div>
        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden non-printable"
            @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>
<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/sales/paymentIN/headbar.vue';
import paymentIn from '../supporting/sales/paymentIN/paymentIn.vue';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';
export default {
    name: 'payment_in',
    components: {
        // sidebar,
        // headbar,
        paymentIn
    },
    data() {
        return {
            isMobile: false,
            isAndroid: false,
            isSidebarOpen: false,
            viewServiceData: null,
            typeofService: null,
            route_item: 5,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            data: [],
            shareData: {},
            service_id: null,
            service_data: {},
            //---api integration---
            companyId: null,
            userId: null,
            exist_data: null,
            typeOfInvoice: 'Product',
            invoice_setting: null,
            customer_data: {},
            items: [],
            get_all_data: {},
            paymentData: {},
            collect_invoice_data: {},
            formValues: {},
            logo_img: '/images/head_bar/logo_01.png',
            shipping_details: {},
            company_data: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 20,
            gap: 5,
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Add PaymentIN';
        const pageDescription = 'Easily manage and track all your payments in one place. Secure, fast, and convenient payment solutions.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },


    methods: {
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),

        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        updateIsAndroid() {
            this.isAndroid = window.innerWidth < 768;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        this.updateIsAndroid();
        this.updateIsEnableBottom(false);
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');

            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

        window.addEventListener('resize', this.updateIsMobile);
        window.addEventListener('resize', this.updateIsAndroid);
    },
    beforeDestroy() {
        this.updateIsEnableBottom(true);
        window.removeEventListener('resize', this.updateIsMobile);
        window.removeEventListener('resize', this.updateIsAndroid);
    },
    watch: {
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.updateIsMobile(); // Initial check
                this.updateIsAndroid();

            }
        },
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnableBottom(true);

            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}

/* Center content on larger screens */
.center-screen {
    display: flex;
    align-items: center;
    justify-content: center;
}


@media print {

    /* Additional styles for printing */
    body {
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */

    }

    .non-printable {
        display: none;
    }

    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }

    /* Apply different styles for printing */
    .center-content {
        width: 100%;
        margin: 0;
    }
}
</style>