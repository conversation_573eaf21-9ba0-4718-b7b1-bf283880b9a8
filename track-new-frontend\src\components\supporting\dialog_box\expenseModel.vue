<template>
    <div v-if="showModal" class="fixed text-sm sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="justify-between items-center flex py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-xl">
                    {{ type == 'edit' ? 'Edit Expense' : 'Add Expense' }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!-- Content based on selected option -->
            <div class="mt-5 pl-4 pr-4"> <!--grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4-->

                <!---NAME OF THE PURPOSE-->
                <div class="flex mt-5">
                    <div class="mr-2 w-10 flex justify-center items-center" :title="'Name of the purpose'">
                        <img :src="user_name" alt="name_of_purpose" class="w-7 h-7">
                    </div>
                    <div class="relative flex w-full mr-2">
                        <label for="name_of_purpose"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.name_of_purpose || isInputFocused.name_of_purpose, 'text-blue-700': isInputFocused.name_of_purpose }">Name
                            of the purpose<span v-if="formValues.name_of_purpose || isInputFocused.name_of_purpose"
                                class="text-red-600">*</span></label>
                        <input id="name_of_purpose" v-model="formValues.name_of_purpose"
                            @focus="isInputFocused.name_of_purpose = true"
                            @blur="isInputFocused.name_of_purpose = false" ref="name_of_purpose"
                            class="w-full border py-2 pl-2 pr-10 leading-5 text-gray-900 focus:ring-0 rounded rounded-tr-none rounded-br-none outline-none" />
                    </div>
                </div>
                <!---Expense type-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'Expense type'">
                        <img :src="category" alt="expense_type" class="w-7 h-7">
                    </div>
                    <div class="flex w-full mr-2 relative">
                        <!--tooltip-->
                        <div v-if="tooltip.type && (!category_list || category_list.length === 0)"
                            class="absolute flex flex-col items-center group-hover:flex -mt-8">
                            <span
                                class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                <p>Click on + icon to add new expense or edit</p>
                            </span>
                            <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                        </div>
                        <label for="expense_type"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.expense_type !== undefined) || isInputFocused.expense_type, 'text-blue-700': isInputFocused.expense_type }">
                            Expense type<span v-if="formValues.expense_type || isInputFocused.expense_type"
                                class="text-red-600">*</span></label>
                        <select id="expense_type" v-model="formValues.expense_type"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                            @focus="isInputFocused.expense_type = true, tooltip.type = true"
                            @blur="isInputFocused.expense_type = false, tooltip.type = false">
                            <option disabled value="">Select type</option>
                            <option v-for="(opt, index) in category_list" :key="index" :value="opt.id">{{ opt.name }}
                            </option>
                        </select>
                        <div class="flex justify-center items-center">
                            <button @click="openCategory"
                                class="border border-teal-600 bg-teal-600 px-2 text-lg font-bold py-1 mt-1  hover:border-green-700 text-white">+</button>
                        </div>
                    </div>
                </div>
                <!---Expense AMOUNT-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'amount'">
                        <img :src="amc_payment" alt="expense_payment" class="w-7 h-7">
                    </div>
                    <div class="flex w-full mr-2 relative">
                        <label for="amount"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.amount !== undefined) || isInputFocused.amount, 'text-blue-700': isInputFocused.amount }">
                            Amount<span v-if="formValues.amount || isInputFocused.amount"
                                class="text-red-600">*</span></label>
                        <input id="amount" v-model="formValues.amount" type="number" :max="10000000"
                            @input="validateAmountValue"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none"
                            @focus="isInputFocused.amount = true" @blur="isInputFocused.amount = false" />
                    </div>
                </div>

                <!---Expense Date-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'expense date'">
                        <img :src="date" alt="date" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="date"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-[12px] sm:-top-3 text-xs bg-white px-1 text-gray-700': formValues.date || isInputFocused.date, 'text-blue-700': isInputFocused.date }">
                            Date<span v-if="formValues.date || isInputFocused.date"
                                class="text-red-600">*</span></label>
                        <input id="date" v-model="formValues.date" type="date" v-datepicker placeholder=" "
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                    </div>
                </div>

                <!---Attachment-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'attachment image'">
                        <img :src="attachment_img" alt="attachment_img" class="w-7 h-7">
                    </div>
                    <!--attachment image-->
                    <div class="flex  items-center justify-center mr-2 cursor-pointer w-full">
                        <div class="w-full relative items-center mt-3">
                            <div class="grid grid-cols-2 gap-6 relative">
                                <div v-if="formValues.attachment && formValues.attachment.length > 0 && formValues.attachment[formValues.attachment.length - 1]"
                                    v-for="(opt, index) in formValues.attachment" class="relative">
                                    <div class="absolute flex py-1 -mt-7 text-lg">
                                        <div title="View" class="px-2 text-black-700 hover:text-black-500"
                                            @click="displayImageModal(index)">
                                            <font-awesome-icon icon="fa-regular fa-eye" />
                                        </div>
                                        <div title="Delete" class="px-10 text-red-600 hover:text-red-500"
                                            @click="removeAttachmentImage(index)">
                                            <font-awesome-icon icon="fa-regular fa-trash-can" />
                                        </div>
                                    </div>
                                    <img :src="opt" class="w-[120px] h-[120px] justify-center border border-gray-300"
                                        :class="{ 'filter': isHovered }" />
                                </div>
                            </div>
                            <!--loader circle-->
                            <div v-if="circle_loader_photo" class="flex justify-center items-center">
                                <CircleLoader :loading="circle_loader_photo"></CircleLoader>
                            </div>
                            <div title="add bill" @click="openFileInput" @mouseover="isHovered = true"
                                @mouseleave="isHovered = false"
                                class="text-center border-2 border-dashed border-gray-300 py-2 mt-4">
                                <p><font-awesome-icon icon="fa-solid fa-arrow-up-from-bracket" /> Attach bill</p>

                                <input ref="fileInput" type="file" style="display: none" accept="image/*"
                                    @change="handleImageChangeProfile" />
                            </div>
                        </div>
                    </div>
                    <!-- <div class="w-full mr-2"> 
                        <div class="border rounded p-4 mt-2 relative text-sm flex">
                            <label class="absolute text-xs font-bold bg-white px-1 -mt-[25px] text-blue-700 px-2"
                                for="fileInput">Attach a bill</label>                           
                            <input id="fileInput" type="file" @change="handleFileUpload" accept="image/*"
                                class="display">
                            <div v-if="circle_loader" class="flex">
                                <CircleLoader :loading="circle_loader"></CircleLoader>
                            </div>
                            <div v-if="formValues.attachment" class="flex ml-1 text-sm mr-2 items-center justify-end">
                                <button class="border border-white rounded px-3 hover:bg-white"
                                    @mouseover="tooltip.image = true" @mouseleave="tooltip.image = false"
                                    @click="openAttachment"><img :src="table_view" alt="view img"
                                        class="w-7 h-7" /></button>

                                <button class="border border-white rounded px-3 hover:bg-white"
                                    @mouseover="tooltip.remove = true" @mouseleave="tooltip.remove = false"
                                    @click="removeAttachmentImage()">
                                    <img :src="table_del" alt="delete product" class="w-6 h-6"></button>


                    <div v-if="tooltip.image"
                        class="absolute flex flex-col items-center group-hover:flex mb-[60px] mr-10 lg:mr-10">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p class="mb-2">view image</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                    <div v-if="tooltip.remove"
                        class="absolute flex flex-col items-center group-hover:flex mb-[60px] ml-12 lg:ml-12">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p class="mb-2">delete image</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                </div>
            </div>
        </div> -->
                </div>
                <!---Expense Description-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'Expense description'">
                        <img :src="notes" alt="description" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="description"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.description || isInputFocused.description, 'text-blue-700': isInputFocused.description }">Description</label>
                        <textarea id="description" v-model="formValues.description" rows="3"
                            @focus="isInputFocused.description = true" @blur="isInputFocused.description = false"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"></textarea>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex justify-end items-center m-3 mt-5 mb-[60px] md:mb-[0px]">
                    <button @click="cancelModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  p-1 pl-10 pr-10 mr-8 py-2">Cancel</button>
                    <button @click="sendModal"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white p-1 pl-10 pr-10 mr-8 py-2 ">Save</button>
                </div>
            </div>
        </div>
        <displayImage :showModal="showImageModal" :showImageModal="selected_image" @close-modal="closeImageModal">
        </displayImage>
        <dialogAlert :showModal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <expenseCategory :showModal="open_category" :companyId="companyId" :userId="userId"
            :categoriesData="category_list" @closeModal="closeCategory"></expenseCategory>
        <confirmbox :show-modal="open_confirm" @on-cancel="cancelDelete" @on-confirm="confirmDelete"></confirmbox>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import displayImage from './displayImage.vue';
import dialogAlert from './dialogAlert.vue';
import expenseCategory from './expenseCategory.vue';
import confirmbox from './confirmbox.vue';
import { mapActions, mapGetters } from 'vuex';

export default {
    name: 'expense',
    components: {
        displayImage,
        dialogAlert,
        expenseCategory,
        confirmbox,
    },
    props: {
        showModal: Boolean,
        editData: Object,
        type: String,
        companyId: String,
        userId: String
    },
    data() {
        return {
            showModal_add_service: false,
            'overlay-active': this.showModal,
            isMobile: false,
            formValues: {},
            isMessageDialogVisible: false,
            message: null,
            isOpen: false,
            updatedData: null,
            //----icons
            user_name: '/images/service_page/User.png',
            date: '/images/service_page/schedule.png',
            notes: '/images/service_page/Writing.png',
            service_product: '/images/service_page/service_product.png',
            service_date: '/images/service_page/service_date.png',
            attachment_img: '/images/setting_page/Cloud_computing.png',
            table_del: '/images/service_page/del.png',
            table_view: '/images/service_page/tabler_eye.png',
            amc_payment: '/images/service_page/amc_payment.png',
            category: '/images/service_page/Add.png',
            //---
            isInputFocused: { date: true, name_of_purpose: true, amount: true, expense_type: true },
            //----------           
            showImageModal: false,
            //--category--
            open_category: false,
            category_list: [],
            tooltip: {},
            open_confirm: false,
            //---loader--
            open_loader: false,
            circle_loader: false,
            tooltip: {},
            //---profile image---
            circle_loader_photo: false,
            isHovered: false,
            upload_profile: '/images/setting_page/profile.png',
            selected_image: null,
            selected_index: null,
            //--toaster---
            show: false,
            type_toaster: 'warning',

        }
    },
    computed: {
        ...mapGetters('expensesTypeList', ['currentExpenseType', 'currentExpenseTypePagination']),
    },
    methods: {
        ...mapActions('expensesTypeList', ['fetchExpenseTypeList']),

        getCurrentDate(date) {
            const currentDate = new Date(date);
            const month = String(currentDate.getMonth() + 1).padStart(2, '0');
            const day = String(currentDate.getDate()).padStart(2, '0');
            const year = currentDate.getFullYear();
            return `${year}-${month}-${day}`;
        },
        arraysAreEqual(arr1, arr2) {
            if (arr1.length !== arr2.length) return false;
            arr1.sort();
            arr2.sort();
            for (let i = 0; i < arr1.length; i++) {
                if (arr1[i] !== arr2[i]) return false;
            }
            return true;
        },
        validateExistdata(data) {
            if (data && Object.keys(data).length > 0) {
                for (let key of Object.keys(data)) {
                    if (key !== 'attachment' && key !== 'expense_type') {
                        if (data[key] !== this.formValues[key]) {
                            return false;
                        }
                    } else if (key === 'expense_type') {
                        if (data[key].id !== this.formValues[key]) {
                            return false;
                        }
                    } else if (key === 'attachment') {
                        if (this.parseValidUrls(this.editData.attachment)) {
                            if (!this.arraysAreEqual(this.parseValidUrls(this.editData.attachment), this.formValues.attachment)) {
                                return false;
                            }
                        } else if (!this.arraysAreEqual([this.editData.attachment], this.formValues.attachment)) {
                            return false;
                        }
                    }
                }
                return true;
            }
            return true;
        },
        cancelModal() {
            // this.$emit('close-modal');
            if (this.type !== 'edit') {
                this.isOpen = false;
                // Add a delay to allow the transition before emitting the close event
                setTimeout(() => {
                    this.$emit('close-modal');
                }, 300);
            } else {
                if (this.validateExistdata(this.editData)) {
                    this.isOpen = false;
                    // Add a delay to allow the transition before emitting the close event
                    setTimeout(() => {
                        this.$emit('close-modal');
                    }, 300);
                } else {
                    this.message = 'Please save latest update or changes';
                    this.show = true;
                    this.isOpen = false;
                    // Add a delay to allow the transition before emitting the close event
                    setTimeout(() => {
                        this.$emit('close-modal');
                    }, 300);
                }
            }
        },
        sendModal() {
            this.open_loader = true;
            // console.log(this.formValues, 'WWWWW');            
            if (this.formValues.name_of_purpose && this.formValues.name_of_purpose !== '' && this.formValues.date && this.formValues.amount >= 1 && this.formValues.date) {
                let sent_data = {
                    date: this.getCurrentDate(this.formValues.date),
                    company_id: this.companyId,
                    ...this.formValues,
                    attachment: JSON.stringify(this.formValues.attachment),
                };
                if (typeof sent_data.expense_type === 'string') {
                    sent_data.expense_type = 1 * sent_data.expense_type;
                }
                if (this.type !== 'edit') {
                    axios.post('/expenses', { ...sent_data })
                        .then(response => {
                            // console.log(response.data, 'response data..!');
                            this.updatedData = response.data.data;
                            this.formValues = {};
                            this.open_loader = false;
                            // this.openMessageDialog(response.data.message);
                            this.$emit('close-modal', this.updatedData);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                        })
                } else {
                    // console.log(sent_data.attachment, 'Waht happening', typeof sent_data.attachment);

                    axios.put(`/expenses/${this.editData.id}`, { ...sent_data })
                        .then(response => {
                            // console.log(response.data, 'response data..!');
                            this.open_loader = false;
                            this.updatedData = response.data.data;
                            this.formValues = {};
                            // this.openMessageDialog(response.data.message);
                            this.$emit('close-modal', this.updatedData);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                        })

                }
            } else {
                this.message = !this.formValues.name_of_purpose || this.formValues.name_of_purpose === '' ? 'Please fill purpose of expense' : !this.formValues.date ? 'Please select date' : this.formValues.amount <= 0 || !this.formValues.amount ? 'Please enter an amount & amount greater than 0' : `Please fill all requied input fields...!`;
                this.type_toaster = 'warning';
                this.show = true;
                // this.openMessageDialog(!this.formValues.name_of_purpose || this.formValues.name_of_purpose === '' ? 'Please fill purpose of expense' : !this.formValues.date ? 'Please select date' : !this.formValues.amount >= 0 ? 'Please enter the amount' : `Please fill all requied input fields...!`);
                this.open_loader = false;
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        parseValidUrls(input) {
            try {
                const parsed = JSON.parse(input);
                return parsed;
            } catch (_) {
                return false;
            }
        },


        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                // console.log(this.editData, 'EWEWEWEWEW what happening....');

                this.formValues = { ...this.editData, attachment: this.parseValidUrls(this.editData.attachment) ? this.parseValidUrls(this.editData.attachment) : this.editData.attachment !== '' ? [this.editData.attachment] : [] };
                if (this.formValues.date) {
                    this.formValues.date = this.formValues.date.substring(0, 10);
                }
                if (this.formValues.expense_type) {
                    this.formValues.expense_type = this.formValues.expense_type.id;
                }

            } else {

                this.formValues = {};
            }
        },

        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            // console.log(this.updatedData, 'EEEEEEEEEEEEEE');
            if (this.updatedData) {
                this.$emit('close-modal', this.updatedData);
                if (this.updatedData) {
                    this.updatedData = null;
                }
                // this.initializeData();
            } else {
                // this.$emit('close-modal');
                // this.initializeData();
            }
        },
        //--upload image--
        //----Profile image--
        openFileInput() {
            // Trigger a click on the hidden file input

            this.$refs.fileInput.click();
        },
        async handleImageChangeProfile(event) {
            const file = event.target.files[0];
            // this.circle_loader_photo = true;
            // if (file) {
            //     this.uploadImageProfile(file);
            // }
            if (!file) return;

            // Check file size (in bytes)
            const maxSizeBytes = 500 * 1024; // 500kb in bytes
            if (file.size > maxSizeBytes) {
                // Image exceeds 500kb, compress it
                try {
                    this.circle_loader_photo = true; // Show loader
                    const compressedFile = await this.compressImage(file);

                    this.uploadImageProfile(compressedFile);
                } catch (error) {
                    console.error("Error compressing image:", error);
                    this.circle_loader_photo = false; // Hide loader on error
                }
            } else {
                // Image is <= 500kb, upload directly
                try {
                    this.circle_loader_photo = true;

                    this.uploadImageProfile(file);
                } catch (error) {
                    console.error("Error uploading image:", error);
                    this.circle_loader_photo = false; // Hide loader on error
                }
            }
        },
        uploadImageProfile(file) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "Expenses");
            formData.append("company_id", this.companyID);
            axios.post('/image', formData)
                .then(response => {
                    this.circle_loader_photo = false;
                    //  let data_save = { image_path: response.data.image_path };
                    if (this.formValues.attachment && this.formValues.attachment !== '' && Array.isArray(this.formValues.attachment)) {

                        this.formValues.attachment.push(response.data.media_url);
                    } else {
                        this.formValues.attachment = [response.data.media_url];
                    }
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                    this.circle_loader_photo = false;
                });
        },

        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        //---display the image---
        displayImageModal(index) {
            if (this.formValues.attachment && this.formValues.attachment.length > 0 && index >= 0) {
                this.selected_image = this.formValues.attachment[index];
                this.showImageModal = true;
            }
        },
        closeImageModal() {
            this.showImageModal = false;
        },
        cancelDelete() {
            this.selected_index = null;
            this.open_confirm = false;
        },
        confirmDelete() {
            if (this.formValues.attachment && this.formValues.attachment && this.selected_index >= 0) {
                // console.log(this.formValues.attachment, 'RRRRRR');
                axios.delete('/delete-image', { params: { model: "Expenses", image_url: this.formValues.attachment[this.selected_index] } })
                    .then(response => {
                        // console.log(response.data, 'delete image..!');
                        this.open_confirm = false;
                        // this.openMessageDialog(response.data.message);
                        const fileInput = document.getElementById('fileInput');
                        if (fileInput) {
                            fileInput.value = '';
                        }
                        this.formValues.attachment.splice(this.selected_index, 1);
                        this.selected_index = null;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                        this.open_confirm = false;
                    })
            }
        },
        //---remove attachment--
        removeAttachmentImage(index) {
            this.selected_index = index;
            this.open_confirm = true;
        },
        openAttachment() {
            // console.log('oooooo');
            // Open the attachment URL in a new tab
            window.open(this.formValues.attachment, '_blank');
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.name_of_purpose;
            if (inputField && this.type !== 'edit') {
                inputField.focus();
                inputField.click();
            }
        },
        //---Category modal--
        openCategory() {
            this.open_category = true;
        },
        closeCategory(data) {
            if (data) {
                this.category_list = data;
                this.fetchExpenseTypeList();
            }
            this.open_category = false;
        },
        getExpenseCategory() {
            axios.get('/expensesTypes', { params: { company_id: this.companyId, page: 1, per_page: 'all' } })
                .then(response => {
                    // console.log(response.data, 'GET expense Category');
                    this.category_list = response.data.data;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---validate amount fields--
        validateAmountValue() {
            if (this.formValues.amount >= 1) {
                // Check if the value is a decimal
                if (this.formValues.amount % 1 !== 0) {
                    this.formValues.amount = Math.ceil(this.formValues.amount);
                }

                // Check if the value exceeds 1 crore (10,000,000)
                if (this.formValues.amount > 10000000) {
                    this.message = 'The maximum accepted amount is 1 crore.';
                    this.type_toaster = 'warning';
                    this.show = true;
                    this.formValues.amount = 10000000;
                }
            }
        }
    },
    mounted() {
        this.updateIsMobile();

        // Use nextTick to wait for the DOM to be updated before accessing the input field
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    if (this.category_list.length === 0 && this.companyId) {
                        // this.getExpenseCategory();
                        if (this.currentExpenseType && this.currentExpenseType.length > 0) {
                            this.category_list = this.currentExpenseType;
                            this.fetchExpenseTypeList();

                        } else {
                            this.fetchExpenseTypeList();
                        }
                    }
                    if (this.type !== 'edit') {
                        if (this.formValues && this.formValues.id) {
                            this.formValues = {};
                        }
                        this.formValues.date = this.getCurrentDate(new Date());
                    }
                    this.handleFocus();
                }
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
        currentExpenseType: {
            deep: true,
            handler(newValue) {
                this.category_list = newValue;
            }
        }
    }
}
</script>

<style scoped>
/* Modal styling */
.small-iframe {
    width: 100px;
    height: 100px;
}

.modal {
    position: fixed;
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Make modal content scrollable on mobile */
.modal-content {
    overflow-y: auto;
    /* Add this line to make the content scrollable */
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;
    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>