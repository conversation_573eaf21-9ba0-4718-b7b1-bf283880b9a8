// store/modules/customerCategories_list.js
import axios from "axios";

const state = {
  customerCategories_list: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  };

  const mutations = {
      SET_CUSTOMERCATEGORYSLIST(state, { data, pagination}) {
          state.customerCategories_list = {data: data, pagination: pagination};
    },
      RESET_STATE(state) {
        state.customerCategories_list = {};
        state.lastFetchTime = null;
        state.isFetching = false;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
  };

  const actions = {
    updateCustomerCategoryName({ commit }, customerCategories_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update customerCategories_list name
      setTimeout(() => {
        // Commit mutation to update customerCategories_list name
        commit('SET_CUSTOMERCATEGORYSLIST', customerCategories_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchCustomerCategoryList({ state, commit, rootState }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['customer_category_update']; 
  
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)  {
        return; // Skip request if less than 30 seconds have passed since the last request
      }  
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          commit('SET_IS_FETCHING', true);
          axios.get('/customer-categories', { params: { company_id: company_id, page: 1, per_page: 'all' } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'CustomerCategory list..!');
              let { data, pagination} = response.data;             
              commit('SET_CUSTOMERCATEGORYSLIST', { data, pagination });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return data;
            })
            .catch(error => {
              // Handle error
              commit('SET_IS_FETCHING', false);
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    
  };

  const getters = {
    currentCustomerCategoryList(state) {
      return state.customerCategories_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
