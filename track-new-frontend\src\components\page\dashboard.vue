<template>
    <div>
        <!-- Headbar with its content -->
        <!-- <headbar @toggle-sidebar="toggleSidebar" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
        </headbar> -->
        <!-- Main content area below the headbar -->
        <div class="flex h-screen overflow-auto relative">
            <!-- Sidebar (conditionally rendered based on isMobile) -->
            <!-- <div v-if="!isMobile && currentLocalDataList && currentLocalDataList.company_id"
                class="w-1/10 custom-scrollbar-hidden">
                <div>
                    <sidebar v-if="!isMobile" :route_item="route_item" :page_name="'dashboard'"
                        :update_list="update_sidebar" :updateModalOpen="updateModalOpen"
                        @updateIsOpen="emitUpdateIsOpen"></sidebar>
                </div>
            </div> -->
            <!-- Content area with its own scroll -->
            <div class="flex flex-col flex-grow overflow-y-auto w-full"
                :class="{ 'mb-[60px] pt-[60px]': isMobile, 'pt-[50px]': !isMobile }">
                <div>
                    <bannerDesign :move_top="true"></bannerDesign>
                </div>
                <topHeader :class="{ 'blurred-content': isExpired }" :isMobile="isMobile"
                    :currentFeatureList="currentFeatureList" :currentCompanyList="currentCompanyList"
                    :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></topHeader>
                <!--plan expired card-->
                <div v-if="isExpired" class="expired-card bg-white shadow-md rounded-lg p-6 max-w-sm mx-auto mt-6 mx-2">
                    <div class="card-content text-center">
                        <h3 v-if="checkRestriction()" class="text-xl font-semibold text-red-500">Subscription Expired
                        </h3>
                        <p v-if="checkRestriction()" class="text-gray-600 mt-4">Your subscription plan has expired.
                            Please renew to continue using
                            our services.</p>
                        <h3 v-if="!checkRestriction()" class="text-xl font-semibold text-red-500">Account Expired</h3>
                        <p v-if="!checkRestriction()" class="text-gray-600 mt-4">Your account has expired. Please
                            contact our support team.</p>
                        <button v-if="checkRestriction()"
                            class="buy-now-button bg-green-500 text-white px-4 py-2 rounded mt-6 hover:bg-green-600 transition shadow-inner shadow-green-100 border border-green-500"
                            @click="navigateToSubscription">
                            Buy Now
                        </button>
                        <div class="bg-gray-200 rounded p-2 m-2 text-sm">
                            <p class="text-gray-600">Support / Queries</p>
                            <div class="py-2 w-full flex justify-center items-center">
                                <button @click="dialPhoneNumber(**********)"
                                    class="flex justify-center text-blue-700"><font-awesome-icon
                                        icon="fa-solid fa-phone" size="lg" /> <span class="ml-2">+91-**********</span>,
                                </button>
                                <button @click="dialPhoneNumber(7200110301)"
                                    class="flex justify-center text-blue-700"><span
                                        class="ml-2">+91-7200110301</span></button>
                            </div>
                            <div class="text-orange-700">
                                <font-awesome-icon icon="fa-brands fa-edge" size="lg" /> <a
                                    href="https://track-new.com/" target="_blank"
                                    class="hover:text-green-700 hover:underline ml-2 no-capitalize">https://track-new.com/</a>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- services home -->
                <div class="relative">
                    <dashboardHome :isMobile="isMobile" :isBlur="isExpired"></dashboardHome>
                </div>
                <footerBar v-if="!isPad"></footerBar>
                <signupForm :showModal="openSignup" @close-modal="closeSignup"></signupForm>
            </div>

            <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
            <!-- <div v-if="isMobile && isSidebarOpen && currentLocalDataList && currentLocalDataList.company_id"
                class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
                <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                    :update_list="update_sidebar" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </sidebar>
            </div> -->
        </div>

    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/dashboard/headbar.vue';
import dashboardHome from '../supporting/dashboard/dashboardHome.vue';
import topHeader from '../supporting/dashboard/designs/topHeader.vue';
import footerBar from '../supporting/dashboard/designs/footerBar.vue';
import signupForm from '../supporting/dialog_box/signupForm.vue';
import { useMeta } from '@/composables/useMeta';
import { mapActions, mapGetters } from 'vuex';
import bannerDesign from '../supporting/banner/bannerDesign.vue';
import { isRestrictedDevice } from '@/utils/deviceUtils';

export default {
    name: 'dashboard',
    components: {
        // sidebar,
        // headbar,
        dashboardHome,
        topHeader,
        footerBar,
        signupForm,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            route_item: 1,
            isPad: false,
            openSignup: false,
            update_sidebar: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Dashboard';
        const pageDescription = 'Quick snapshot of your accounts key metrics and recent activity';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('features_list', ['currentFeatureList']),
        ...mapGetters('companies', ['currentCompanyList']),
        isExpired() {
            if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0) {
                if (this.currentCompanyList && this.currentCompanyList.expiry_date) {
                    const expireDate = this.currentCompanyList.expiry_date;
                    const today = new Date().toISOString().split('T')[0];
                    if (expireDate < today) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        }
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('features_list', ['fetchFeatureList', 'updateFeatureList']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates']),
        checkRestriction() {
            return !isRestrictedDevice();
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
            this.isPad = window.innerWidth < 768;
        },
        closeSignup() {
            this.openSignup = false;
        },
        updateFeaturesOpen() {
            if (this.currentLocalDataList && this.currentLocalDataList.company_id) {
                let result = [1, 2, 3, 4, 5, 6, 7, 8];
                axios.post(`/assign_modules/companies/${this.currentLocalDataList.company_id}`, { modules: result })
                    .then(response => {
                        localStorage.setItem('features', JSON.stringify({ update: true }));
                        this.update_sidebar = true;
                        this.fetchFeatureList();
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);
                    });
            }
        },
        //----update all feature are off---
        featuresUpdated() {
            if (this.currentFeatureList && this.currentFeatureList.length > 0) {
                let is_it_new = false;
                let get_is_new = localStorage.getItem('features');
                if (get_is_new) {
                    let parseData = JSON.parse(get_is_new);
                    is_it_new = parseData && (parseData.update == false || parseData.update) ? false : true;
                } else {
                    is_it_new = true;
                }
                const is_status = this.currentFeatureList.every(opt => opt.hasAccess === false);
                if (is_status) {
                    this.updateFeaturesOpen();
                }
            }
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        },
        navigateToSubscription() {
            this.$router.push('/subscription'); // Navigate to subscription page
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
            }
        },
    },
    mounted() {
        this.fetchApiUpdates();
        this.updateIsMobile(); // Initial check
        this.fetchLocalDataList();
        // this.updateCompanyList();
        if (this.currentFeatureList && this.currentFeatureList.length > 0) {
            this.featuresUpdated();
            this.fetchFeatureList();
        } else {
            this.fetchFeatureList();
        }
        if (this.currentLocalDataList && !this.currentLocalDataList.company_id && this.currentLocalDataList.user_id) {
            this.openSignup = true;
        }
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        currentFeatureList: {
            deep: true,
            handler(newValue) {
                this.featuresUpdated();
            }
        },
        currentLocalDataList: {
            deep: true,
            handler(newValue) {
                if (newValue && !newValue.company_id && this.currentLocalDataList.user_id) {
                    this.openSignup = true;
                } else {
                    this.fetchFeatureList();
                }
            }
        },
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        },
        openSignup: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        },
        currentCompanyList: {
            deep: true,
            handler(newValue) {
                if (newValue && Object.keys(newValue).length > 0) {

                } else {
                    this.fetchCompanyList();
                }
            }
        }
    },
    //---validate is any modal open and strict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}

.blurred-content {
    filter: blur(3px);
    pointer-events: none;
    opacity: 0.4;
}
</style>