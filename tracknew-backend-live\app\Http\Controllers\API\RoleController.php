<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
//use Spatie\Permission\Models\Role;
use App\Models\Role;
use App\Http\Requests\API\RoleRequest;
use App\Http\Resources\api\RoleResource;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

class RoleController extends AppBaseController
{
        /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/roles",
     *      summary="getRolesList",
     *      tags={"Roles"},
     *      description="Get all Roles",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Roles")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        // $roles = Role::all();
        // return RoleResource::collection($roles)->response()->setStatusCode(200);

        // $companyId = $request->query('company_id');

        // if ($companyId === null) {
        //     return $this->sendError('Please provide company.', 400);
        // } 

        $perPage = $request->query('per_page', 100);
        $page = $request->query('page', 1);
        
        $Query = Role::where('id','!=',2)->where('id','!=',1);
       // $Query = Role::with('permissions')->where('company_id', $companyId);

        if ($perPage === 'all') {
            $perPage = $Query->count();
        }     

        $roles = $Query->orderBy('name', 'asc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => RoleResource::collection($roles), 
            'pagination' => [
                'total' => $roles->total(),
                'per_page' => $roles->perPage(),
                'current_page' => $roles->currentPage(),
                'last_page' => $roles->lastPage(),
                'from' => $roles->firstItem(),
                'to' => $roles->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

     /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/roles/{id}",
     *      summary="getRolesItem",
     *      tags={"Roles"},
     *      description="Get Roles",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Roles",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Roles"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */

    public function show($id)
    {
        $role = Role::findOrFail($id);
        return (new RoleResource($role))->response()->setStatusCode(200);
    }

    
    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/roles",
     *      summary="createRoles",
     *      tags={"Roles"},
     *      description="Create Roles",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Role")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Roles"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */

    // public function store(RoleRequest $request)
    // {
    //     // Validate the request data
    //     $validatedData = $request->validate([
    //         'name' => 'required|string|max:255', // Adjust validation rules as needed
    //     ]);

    //     // Create the role
    //     $role = Role::create([
    //         'name' => $validatedData['name'],
    //         'guard_name' => 'api',
    //         'company_id' => $request->company_id
    //     ]);


    //     return $this->sendResponse(new RoleResource($role), 'Role saved successfully');

    //     // Return the created role resource with success status
    //   // return (new RoleResource($role))->response()->setStatusCode(Response::HTTP_CREATED);
    // }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/roles/{id}",
     *      summary="updateRoles",
     *      tags={"Roles"},
     *      description="Update Roles",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Roles",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Role")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Roles"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */

    // public function update(RoleRequest $request, $id)
    // {
    //     // Validate the request data
    //     $validatedData = $request->validate([
    //         'name' => 'required|string|max:255', // Adjust validation rules as needed
            
    //     ]);

    //     // Find the role by id
    //     $role = Role::findOrFail($id);

    //     // Update the role attributes
    //     $role->update([
    //         'name' => $validatedData['name'],
    //         'company_id' => $request->company_id
    //     ]);

    //     // Return the response
    //     return $this->sendResponse(new RoleResource($role), 'Role updated successfully');
    // }


    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/roles/{id}",
     *      summary="deleteRoles",
     *      tags={"Roles"},
     *      description="Delete Roles",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Roles",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */

    // public function destroy($id)
    // {
    //     $role = Role::findOrFail($id);
    //     $role->delete();

    //     return response()->json(null, Response::HTTP_NO_CONTENT);
    // }
}

