<template>
    <div>
        <div v-if="!isPad && table_view_list && table_view_list.length > 0"
            class="grid grid-cols-1 gap-4 mt-4 sm:mb-[100px] lg:mb-[0px]">
            <div class="p-2" v-if="view_type === 'Services'">
                <!--bg-white rounded-lg shadow border border-gray-200 p-5-->
                <p class="font-bold text-lg mb-3">View latest 5 Services of {{ stats.length > 0 && stats[0] &&
                    stats[0].total ? stats[0].total : 0 }} (<span @click="goHomeList('Services')"
                        class="cursor-pointer text-blue-500 hover:text-blue-700 px-1">View All</span>)</p>
                <div class="overflow-auto">
                    <table class="table-auto w-full">
                        <thead>
                            <tr class="bg-blue-500 text-white">
                                <th class="border px-1 py-2 font-normal font-normal">Created At</th>
                                <th class="border px-1 py-2 font-normal font-normal">Title</th>
                                <th class="border px-1 py-2 font-normal font-normal">Services Details</th>
                                <th class="border px-1 py-2 font-normal font-normal">Customer</th>
                                <th v-if="!isPad" class="border px-1 py-2 font-normal">Status</th>
                                <th v-if="!isPad" class="border px-1 py-2 font-normal">Actions</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            <tr v-if="table_view_list[0].data !== 0" v-for="(service, index) in table_view_list[0].data"
                                :key="index" class="hover:bg-blue-100 cursor-pointer"
                                @click="editRecordServices(service)">
                                <!-- <td class="border px-1 py-2">{{ service.service_code }}</td> -->
                                <td class="border px-1 py-2 cursor-pointer" :title="formattedDate(service.created_at)">
                                    {{
                                        calculateDaysAgo(formattedDate(service.created_at)) }}</td>
                                <td class="border px-1 py-2">{{ service.service_data &&
                                    JSON.parse(service.service_data)
                                    &&
                                    JSON.parse(service.service_data)['problem_title'] &&
                                    JSON.parse(service.service_data)['problem_title'].length > 0
                                    ?
                                    JSON.parse(service.service_data)['problem_title'].join(', ') : '' }}</td>
                                <td class="border px-1 py-2">{{ service.service_data &&
                                    JSON.parse(service.service_data)
                                    &&
                                    JSON.parse(service.service_data).brand &&
                                    JSON.parse(service.service_data).device_model
                                    ?
                                    JSON.parse(service.service_data).brand +
                                    ' ' + JSON.parse(service.service_data).device_model : '' }}</td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ service.customer_name + ' - ' + service.contact_number }}</td>
                                <!-- <td class="border px-1 py-2">{{ Array.isArray(service.technicians) ?
                                service.technicians.join(', ') : '' }}</td> -->
                                <td v-if="!isPad" class="border px-1 py-2 capitalize">{{ service_status.length >=
                                    service.status
                                    ?
                                    service_status[service.status] : '' }}</td>
                                <!--actions-->
                                <td class="border px-1 py-2" style="text-align: center;">
                                    <button class="text-blue-500 hover:text-blue-700"
                                        @click="editRecordServices(service)">
                                        <font-awesome-icon icon="fa-solid fa-pen-to-square" />
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="table_view_list[0].data === 0">
                                <td colspan="4" class="text-center border">No records</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="p-2" v-if="view_type === 'Leads'">
                <!--bg-white rounded-lg shadow border border-gray-200 p-5-->
                <p class="font-bold text-lg mb-3">View latest 5 leads of {{ stats.length > 0 && stats[1] &&
                    stats[1].total ? stats[1].total : 0 }} (<span @click="goHomeList('Leads')"
                        class="cursor-pointer text-blue-500 hover:text-blue-700 px-1">View All</span>)</p>
                <div class="overflow-auto">
                    <table class="w-full bg-white">
                        <thead>
                            <tr class="bg-green-500 text-white">
                                <!-- <th class="border px-1 py-2 font-normal">Lead ID</th> -->
                                <th class="border px-1 py-2 font-normal">Create At</th>
                                <th class="border px-1 py-2 font-normal">Title</th>
                                <th class="border px-1 py-2 font-normal">Customer</th>
                                <th class="border px-1 py-2 font-normal">Status</th>
                                <th class="border px-1 py-2 font-normal">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            <tr v-if="table_view_list[1].data.length !== 0"
                                v-for="(lead, index) in table_view_list[1].data" :key="index"
                                @click="$router.push({ path: '/leads/edit', query: { recordId: lead.id } })"
                                class="hover:bg-green-100 cursor-pointer">
                                <!-- <td class="border px-1 py-2 cursor-pointer">{{ lead.id }}</td> -->
                                <!-- <td class="border px-1 py-2 cursor-pointer">{{ lead.title }}</td>
                            <td class="border px-1 py-2 cursor-pointer">
                                {{ Array.isArray(lead.technicians) ? lead.technicians.join(', ') : '' }}
                            </td> -->
                                <td class="border px-1 py-2 cursor-pointer" :title="formattedDate(lead.created_at)">{{
                                    calculateDaysAgo(formattedDate(lead.created_at)) }}</td>
                                <td class="border px-1 py-2 cursor-pointer">{{ lead.title }}</td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ lead.customer_name + ' - ' + lead.contact_number }}</td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ lead.leadstatus_id === 0
                                        ?
                                        'Open' : lead.leadstatus_id === 0
                                            ? 'Progress' : 'Completed' }}</td>
                                <!--actions-->
                                <td class="border px-1 py-2" style="text-align: center;">
                                    <button class="text-green-500 hover:text-green-700" @click="startEditLeads(lead)">
                                        <font-awesome-icon icon="fa-solid fa-pen-to-square" />
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="table_view_list[1].data.length === 0">
                                <td colspan="4" class="text-center border">No records</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="p-2" v-if="view_type === 'AMC'">
                <!--bg-white rounded-lg shadow border border-gray-200 p-5-->
                <p class="font-bold text-lg mb-3">View latest 5 AMC of {{ stats.length > 0 && stats[2] &&
                    stats[2].total ? stats[2].total : 0 }} (<span @click="goHomeList('AMC')"
                        class="cursor-pointer text-blue-500 hover:text-blue-700 px-1">View All</span>)</p>
                <div class="overflow-auto">
                    <table class="table-auto w-full">
                        <thead>
                            <tr class="bg-orange-500 text-white">
                                <!-- <th class="border px-1 py-2 font-normal">AMC ID</th> -->
                                <th class="border px-1 py-2 font-normal">Create At</th>
                                <th class="border px-1 py-2 font-normal">Title</th>
                                <th class="border px-1 py-2 font-normal">Customer</th>
                                <th class="border px-1 py-2 font-normal">Status</th>
                                <th class="border px-1 py-2 font-normal">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            <tr v-if="table_view_list[2].data.length > 0"
                                v-for="(amc, index) in table_view_list[2].data" :key="index"
                                @click="$router.push({ path: '/amc/view', query: { recordId: amc.id } })"
                                class="hover:bg-orange-100 cursor-pointer">
                                <td class="border px-1 py-2 cursor-pointer" :title="formattedDate(amc.created_at)">{{
                                    calculateDaysAgo(formattedDate(amc.created_at)) }}</td>
                                <td class="border px-1 py-2 cursor-pointer">{{ amc.title }}</td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ amc.customer_name + ' - ' + amc.contact_number }}</td>
                                <!-- <td class="border px-1 py-2 cursor-pointer">{{
                                Array.isArray(amc.technicians) ?
                                    amc.technicians.join(', ') : '' }}</td> -->
                                <td class="border px-1 py-2 cursor-pointer">{{ amc.amc_status === 0 ?
                                    'Open'
                                    : amc.amc_status === 1 ?
                                        'Progress' : 'Completed' }}</td>
                                <!--actions-->
                                <td class="border px-1 py-2" style="text-align: center;">
                                    <button class="text-orange-500 hover:text-orange-700" @click="viewRecordAMC(amc)">
                                        <font-awesome-icon icon="fa-solid fa-pen-to-square" />
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="table_view_list[2].data.length === 0">
                                <td colspan="4" class="text-center border">No records</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="p-2" v-if="view_type === 'RMA'">
                <p class="font-bold text-lg mb-3">View latest 5 RMA of {{ stats.length > 0 && stats[3] &&
                    stats[3].total ? stats[3].total : 0 }} (<span @click="goHomeList('RMA')"
                        class="cursor-pointer text-blue-500 hover:text-blue-700 px-1">View All</span>)</p>
                <div class="overflow-auto">
                    <table class="table-auto w-full">
                        <thead>
                            <tr class="bg-pink-500 text-white">
                                <th class="border px-1 py-2 font-normal">Create At</th>
                                <th class="border px-1 py-2 font-normal">RMA ID</th>
                                <th class="border px-1 py-2 font-normal">Customer</th>
                                <th class="border px-1 py-2 font-normal">Status</th>
                                <th class="border px-1 py-2 font-normal">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            <tr v-if="table_view_list[3].data.length > 0"
                                v-for="(rma, index) in table_view_list[3].data" :key="index"
                                @click="$router.push({ path: `/openrma/${rma.id}/edit` })"
                                class="hover:bg-pink-100 cursor-pointer">
                                <td class="border px-1 py-2 cursor-pointer" :title="formattedDate(rma.created_at)">{{
                                    calculateDaysAgo(formattedDate(rma.created_at)) }}</td>
                                <td class="border px-1 py-2 cursor-pointer">{{ rma.rma_id }}</td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ rma.customer_name + ' - ' + rma.customer_phone }}</td>
                                <!-- <td class="border px-1 py-2 cursor-pointer">{{
                                Array.isArray(amc.technicians) ?
                                    amc.technicians.join(', ') : '' }}</td> -->
                                <td class="border px-1 py-2 capitalize">
                                    {{ rma_status && rma_status.length >= 0 && rma.rma_status >= 0 ?
                                        rma_status[rma.rma_status].label : '' }}
                                </td>
                                <!--actions-->
                                <td class="border px-1 py-2" style="text-align: center;">
                                    <button class="text-pink-500 hover:text-pink-700" @click="viewRecordRMA(rma)">
                                        <font-awesome-icon icon="fa-solid fa-pen-to-square" />
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="table_view_list[3].data.length === 0">
                                <td colspan="4" class="text-center border">No records</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="p-2" v-if="view_type === 'Sales'">
                <p class="font-bold text-lg mb-3">View latest 5 Sales of {{ stats.length > 0 && stats[4] &&
                    stats[4].total ? stats[4].total : 0 }} (<span @click="goHomeList('Sales')"
                        class="cursor-pointer text-blue-500 hover:text-blue-700 px-1">View All</span>)</p>
                <div class="overflow-auto">
                    <table class="table-auto w-full">
                        <thead>
                            <tr class="bg-lime-500 text-white">
                                <th class="border px-1 py-2 font-normal">Create At</th>
                                <th class="border px-1 py-2 font-normal">Invoice No</th>
                                <th class="border px-1 py-2 font-normal">Customer</th>
                                <th class="border px-1 py-2 font-normal">Grand Total</th>
                                <th class="border px-1 py-2 font-normal">Due</th>
                                <th class="border px-1 py-2 font-normal">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">
                            <tr v-if="table_view_list[4].data.length > 0"
                                v-for="(sale, index) in table_view_list[4].data" :key="index"
                                @click="this.$router.push({ name: 'print-preview', query: { type: 'sales_home', invoice_no: sale.id } })"
                                class="hover:bg-lime-100 cursor-pointer">
                                <td class="border px-1 py-2 cursor-pointer" :title="formattedDate(sale.created_at)">{{
                                    calculateDaysAgo(formattedDate(sale.created_at)) }}</td>
                                <td class="border px-1 py-2 cursor-pointer">{{ sale.invoice_id }}</td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ sale.customer_name + ' - ' + sale.customer_phone }}</td>
                                <!-- <td class="border px-1 py-2 cursor-pointer">{{
                                Array.isArray(amc.technicians) ?
                                    amc.technicians.join(', ') : '' }}</td> -->
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }}
                                    {{ sale.grand_total }}
                                </td>
                                <td class="border px-1 py-2 cursor-pointer text-red-600">
                                    {{ currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }}
                                    {{ sale.due_amount }}
                                </td>
                                <!--actions-->
                                <td class="border px-1 py-2" style="text-align: center;">
                                    <button class="text-lime-500 hover:text-lime-700" @click="viewRecordSale(sale)">
                                        <font-awesome-icon icon="fa-solid fa-pen-to-square" />
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="table_view_list[4].data.length === 0">
                                <td colspan="6" class="text-center border">No records</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="p-2" v-if="view_type === 'Proforma'">
                <p class="font-bold text-lg mb-3">View latest 5 Proforma of {{ stats.length > 0 && stats[5] &&
                    stats[5].total ? stats[5].total : 0 }} (<span @click="goHomeList('Proforma')"
                        class="cursor-pointer text-blue-500 hover:text-blue-700 px-1">View All</span>)</p>
                <div class="overflow-auto">
                    <table class="table-auto w-full">
                        <thead>
                            <tr class="bg-yellow-500 text-white">
                                <th class="border px-1 py-2 font-normal">Create At</th>
                                <th class="border px-1 py-2 font-normal">Proforma No</th>
                                <th class="border px-1 py-2 font-normal">Customer</th>
                                <th class="border px-1 py-2 font-normal">Grand Total</th>
                                <th class="border px-1 py-2 font-normal">Status</th>
                                <th class="border px-1 py-2 font-normal">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">

                            <tr v-if="table_view_list[5].data.length > 0"
                                v-for="(proforma, index) in table_view_list[5].data" :key="index"
                                @click="this.$router.push({ name: 'print-preview', query: { type: 'proforma', proforma_no: proforma.id, back: 'home' } });"
                                class="hover:bg-yellow-100 cursor-pointer">
                                <td class="border px-1 py-2 cursor-pointer" :title="formattedDate(proforma.created_at)">
                                    {{
                                        calculateDaysAgo(formattedDate(proforma.created_at)) }}</td>
                                <td class="border px-1 py-2 cursor-pointer">{{ proforma.proforma_no }}
                                </td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ proforma.customer_name + ' - ' + proforma.contact_number }}</td>
                                <!-- <td class="border px-1 py-2 cursor-pointer">{{
                                Array.isArray(amc.technicians) ?
                                    amc.technicians.join(', ') : '' }}</td> -->
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }}
                                    {{ proforma.grand_total }}</td>
                                <td class="border px-1 py-2 cursor-pointer"
                                    :class="{ 'text-green-600': proforma.status == 1, 'text-red-600': proforma.status != 1 }">
                                    {{ proforma.status == 1 ? 'Success' : 'Pending' }}
                                </td>
                                <!--actions-->
                                <td class="border px-1 py-2" style="text-align: center;">
                                    <button class="text-yellow-500 hover:text-yellow-700"
                                        @click="viewRecordProforma(proforma)">
                                        <font-awesome-icon icon="fa-solid fa-pen-to-square" />
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="table_view_list[5].data.length === 0">
                                <td colspan="4" class="text-center border">No records</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="p-2" v-if="view_type === 'Estimate'">
                <p class="font-bold text-lg mb-3">View latest 5 Estimate of {{ stats.length > 0 && stats[6] &&
                    stats[6].total ? stats[6].total : 0 }} (<span @click="goHomeList('Estimate')"
                        class="cursor-pointer text-blue-500 hover:text-blue-700 px-1">View All</span>)</p>
                <div class="overflow-auto">
                    <table class="table-auto w-full">
                        <thead>
                            <tr class="bg-sky-500 text-white">
                                <th class="border px-1 py-2 font-normal">Create At</th>
                                <th class="border px-1 py-2 font-normal">Estimate No</th>
                                <th class="border px-1 py-2 font-normal">Customer</th>
                                <th class="border px-1 py-2 font-normal">Grand Total</th>
                                <th class="border px-1 py-2 font-normal">Status</th>
                                <th class="border px-1 py-2 font-normal">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">

                            <tr v-if="table_view_list[6].data.length > 0"
                                v-for="(estimate, index) in table_view_list[6].data" :key="index"
                                @click="this.$router.push({ name: 'print-preview', query: { type: 'estimation', est_no: estimate.id, back: 'home' } });"
                                class="hover:bg-sky-100 cursor-pointer">
                                <td class="border px-1 py-2 cursor-pointer" :title="formattedDate(estimate.created_at)">
                                    {{
                                        calculateDaysAgo(formattedDate(estimate.created_at)) }}</td>
                                <td class="border px-1 py-2 cursor-pointer">{{ estimate.estimate_num }}
                                </td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ estimate.customer_name + ' - ' + estimate.contact_number }}</td>
                                <!-- <td class="border px-1 py-2 cursor-pointer">{{
                                Array.isArray(amc.technicians) ?
                                    amc.technicians.join(', ') : '' }}</td> -->
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }}
                                    {{ estimate.grand_total }}</td>
                                <td class="border px-1 py-2 cursor-pointer"
                                    :class="{ 'text-green-600': estimate.status == 1, 'text-red-600': estimate.status != 1 }">
                                    {{ estimate.status == 1 ? 'Success' : 'Pending' }}
                                </td>
                                <!--actions-->
                                <td class="border px-1 py-2" style="text-align: center;">
                                    <button class="text-sky-500 hover:text-sky-700"
                                        @click="viewRecordEstimate(estimate)">
                                        <font-awesome-icon icon="fa-solid fa-pen-to-square" />
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="table_view_list[6].data.length === 0">
                                <td colspan="4" class="text-center border">No records</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            <div class="p-2" v-if="view_type === 'Expense'">
                <p class="font-bold text-lg mb-3">View latest 5 Expense of {{ stats.length > 0 && stats[7] &&
                    stats[7].total ? stats[7].total : 0 }} (<span @click="goHomeList('Expense')"
                        class="cursor-pointer text-blue-500 hover:text-blue-700 px-1">View All</span>)</p>
                <div class="overflow-auto">
                    <table class="table-auto w-full">
                        <thead>
                            <tr class="bg-violet-500 text-white">
                                <th class="border px-1 py-2 font-normal ">Create At</th>
                                <th class="border px-1 py-2 font-normal">Purpose</th>
                                <th class="border px-1 py-2 font-normal">Amount</th>
                                <th class="border px-1 py-2 font-normal">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-center">

                            <tr v-if="table_view_list[7].data.length > 0"
                                v-for="(expense, index) in table_view_list[7].data" :key="index"
                                @click="this.$router.push('/expense');" class="hover:bg-violet-100 cursor-pointer">
                                <td class="border px-1 py-2 cursor-pointer" :title="formattedDate(expense.created_at)">
                                    {{
                                        calculateDaysAgo(formattedDate(expense.created_at)) }}</td>
                                <td class="border px-1 py-2 cursor-pointer">{{ expense.name_of_purpose
                                    }}
                                </td>
                                <td class="border px-1 py-2 cursor-pointer">
                                    {{ currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency }}
                                    {{ expense.amount }}</td>
                                <!--actions-->
                                <td class="border px-1 py-2" style="text-align: center;">
                                    <button class="text-violet-500 hover:text-violet-700">
                                        <font-awesome-icon icon="fa-solid fa-pen-to-square" />
                                    </button>
                                </td>
                            </tr>
                            <tr v-if="table_view_list[7].data.length === 0">
                                <td colspan="4" class="text-center border">No records</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        view_type: String,
        stats: Object,
        isPad: Boolean,
        table_view_list: Object,
        service_status: Object,
        rma_status: Object,
        currentCompanyList: Object
    },
    data() {
        return {
            now: null,
        };
    },
    created() {
        // Update current time every second
        setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    methods: {
        //--services---
        editRecordServices(record) {
            this.$router.push({ name: 'service-data-view', params: { serviceId: record.id, type: record.category_name, id: record.category_id } });
        },
        //---record---
        startEditLeads(record) {
            this.$router.push({
                name: 'leadEdit',
                query: {
                    recordId: record.id
                }
            });
        },
        //---view Record--
        viewRecordAMC(record) {
            if (record) {
                this.$router.push({
                    name: 'amcView',
                    query: {
                        recordId: record.id
                    }
                });
            }
        },
        viewRecordRMA(record) {
            if (record) {
                this.$router.push({
                    path: `/openrma/${record.id}/edit`
                });
            }
        },
        viewRecordSale(record) {
            if (record) {
                if (record.invoice_type === 'Services') {
                    this.getServiceData(record);

                } else if (record.invoice_type === 'Product' || record.invoice_type === 'product') {
                    this.$router.push({
                        name: 'sales-invoice',
                        query: { type: 'edit', invoice_no: record.id }
                    });
                } else if (record.invoice_type === 'Direct_Services') {
                    this.$router.push({
                        name: 'sales-invoice',
                        query: { type: 'edit', invoice_no: record.id, invoice_type: 'direct' }
                    });
                }
            }
        },
        viewRecordProforma(record) {
            this.$router.push({
                name: 'addProformaInvoice',
                params: { type: 'product' },
                query: {
                    type: 'edit',
                    proforma_no: record.id,
                }
            });
        },
        viewRecordEstimate(record) {
            this.$router.push({
                name: 'addEstimation',
                params: { type: 'product' },
                query: {
                    type: 'edit',
                    est_no: record.id,
                }
            });
        },
        //---go dashboard---
        goHomeList(match) {
            switch (match) {
                case 'Services': {
                    this.$router.push('/services');
                    break;
                }
                case 'Leads': {
                    this.$router.push('/leads');
                    break;
                }
                case `AMC`: {
                    this.$router.push('/amc');
                    break;
                }
                case `RMA`: {
                    this.$router.push('/openrma');
                    break;
                }
                case 'Sales': {
                    this.$router.push('/sales');
                    break;
                }
                case `Proforma`: {
                    this.$router.push('/proforma');
                    break;
                }
                case 'Estimate': {
                    this.$router.push('/estimation');
                    break;
                }
                case 'Expense': {
                    this.$router.push('/expense');
                    break;
                }
                default: {
                    break;
                }
            }
        },
        //---formated display date---
        formattedDate(timestamp) {
            const date = new Date(timestamp);
            // console.log(date.toISOString().split('T')[0], 'Date formated.....!');
            return date.toISOString().slice(0, 16).replace('T', ' '); // Extracting date portion
        },
        //---get time
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
    }
};
</script>