<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateCompaniesAPIRequest;
use App\Http\Requests\API\UpdateCompaniesAPIRequest;
use App\Models\Companies;
use App\Models\Orders;
use App\Models\User;
use App\Repositories\CompaniesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;
use Response;
use Auth;

/**
 * Class CompaniesController
 * @package App\Http\Controllers\API
 */

class CompaniesAPIController extends AppBaseController
{
    /** @var  CompaniesRepository */
    private $companiesRepository;

    public function __construct(CompaniesRepository $companiesRepo)
    {
      	
        $this->companiesRepository = $companiesRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/companies",
     *      summary="getCompaniesList",
     *      tags={"Companies"},
     *      description="Get all Companies",
     *      @OA\Parameter(
     *         name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Companies")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {   
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }    

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $companiesQuery = Companies::where('id', $companyId);

        if ($perPage === 'all') {
            $perPage = $companiesQuery->count();
        }


        $companies = $companiesQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $companies->items(), // Get the paginated items
            'pagination' => [
                'total' => $companies->total(),
                'per_page' => $companies->perPage(),
                'current_page' => $companies->currentPage(),
                'last_page' => $companies->lastPage(),
                'from' => $companies->firstItem(),
                'to' => $companies->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
 * @param Request $request
 * @return Response
 *
 * @OA\Post(
 *      path="/companies",
 *      summary="createCompanies",
 *      tags={"Companies"},
 *      description="Create Companies",
 *      @OA\RequestBody(
 *        required=true,
 *        @OA\MediaType(
 *            mediaType="multipart/form-data",
 *            @OA\Schema(
 *                type="object",
 *                required={"name", "currency", "logo"},
 *                @OA\Property(
 *                    property="name",
 *                    description="desc",
 *                    type="string"
 *                ),
 *                @OA\Property(
 *                    property="currency",
 *                    description="desc",
 *                    type="string"
 *                ),
 *                @OA\Property(
 *                    property="logo",
 *                    description="desc",
 *                    type="string",
 *                    format="binary"
 *                )
 *            )
 *        )
 *      ),
 *      @OA\Response(
 *          response=200,
 *          description="successful operation",
 *          @OA\Schema(
 *              type="object",
 *              @OA\Property(
 *                  property="success",
 *                  type="boolean"
 *              ),
 *              @OA\Property(
 *                  property="data",
 *                  ref="#/definitions/Companies"
 *              ),
 *              @OA\Property(
 *                  property="message",
 *                  type="string"
 *              )
 *          )
 *      )
 * )
 */

    public function store(CreateCompaniesAPIRequest $request)
    {
        $input = $request->all();

        $companies = $this->companiesRepository->create($input);

        return $this->sendResponse($companies->toArray(), 'Company details saved');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/companies/{id}",
     *      summary="getCompaniesItem",
     *      tags={"Companies"},
     *      description="Get Companies",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Companies",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Companies"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
  public function show($id, Request $request)
    {
        // Retrieve company with related user and plan data
        $company = Companies::with('user.planData')->find($id);

        if (empty($company)) {
            return $this->sendError('Company not found');
        }

        // Check if the user has any orders
        $hasOrder = $company->user ? Orders::where('user_id', $company->user_id)->exists() : false;

        $companyData = $company->toArray();
      
      	$user = User::with('planData')->where('id', $company->user_id)->first();

        $companyData['plan_name'] = $hasOrder ? ($user->planData->title ?? 'No Plan') : 'Trail';

       
      
      	

        $companyData['expiry_date'] = $user->will_expire;

         // Calculate the remaining time until expiration, considering only the date
        $willExpireDate = Carbon::parse($user->will_expire)->startOfDay();
        $now = Carbon::now()->startOfDay();
        $daysRemaining = $now->diffInDays($willExpireDate, false);

        $message = null;

         if ($willExpireDate < $now) {
            $message = $hasOrder ?
                __('Your subscription payment has expired, Please renew the subscription') :
                __('Your Trail has expired, Please renew the subscription');
        } elseif ($user->will_expire == null) {
            $message = $hasOrder ?
                __('Your subscription payment is not completed') :
                __('Your Trail has expired, Please renew the subscription');
        } else {
            if ($daysRemaining === 0) {
                $message = __('Your subscription will expire at today');
            } elseif ($daysRemaining === 1) {
                $message = __('Your subscription will expire in 1 day');
            } elseif ($daysRemaining > 1) {
                $message = __('Your subscription will expire in :days days', ['days' => $daysRemaining]);
            } else {
                $message = __('Your subscription will expire very soon');
            }
        }

        $companyData['plans'] = $company->user->planData;
        $companyData['message'] = $message;
     
    	
        $planLimit = 0;
        $extraLimit = 0;

        if (!empty($company->user->planData) && isset($company->user->planData->data['messages_limit'])) {
            $planLimit = (int) $company->user->planData->data['messages_limit'];
        }

        if (!empty($company->user->message_limit)) {
            $extraLimit = (int) $company->user->message_limit;
        }

        $totalMessageLimit = $planLimit + $extraLimit;

        $totalSmsTransactions = \DB::table('sms_transactions')
            ->where('company_id', $company->id)
            ->count();

        $remainingSmsBalance = max(0, $totalMessageLimit - $totalSmsTransactions);

        $companyData['sms_balance'] = $remainingSmsBalance;  
     

        // Unset user data if needed
        unset($companyData['user']); 

        return $this->sendResponse($companyData, 'Company data retrieved.');
    }


    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/companies/{id}",
     *      summary="updateCompanies",
     *      tags={"Companies"},
     *      description="Update Companies",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Companies",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Companies"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateCompaniesAPIRequest $request)
    {
        $input = $request->all();

        /** @var Companies $companies */
        $companies = $this->companiesRepository->find($id);

        if (empty($companies)) {
            return $this->sendError('Companies not found');
        }

        $companies = $this->companiesRepository->update($input, $id);

        return $this->sendResponse($companies->toArray(), 'Company details updated');
    }
    
    public function sociallinksUpdate($id, UpdateCompaniesAPIRequest $request)
    {
        $input = $request->all();
        /** @var Companies $companies */
        $companies = $this->companiesRepository->find($id);

        if (empty($companies)) {
            return $this->sendError('Companies not found');
        }

        $companies = $this->companiesRepository->update($input, $id);

        return $this->sendResponse($companies->toArray(), 'Company social links are updated');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/companies/{id}",
     *      summary="deleteCompanies",
     *      tags={"Companies"},
     *      description="Delete Companies",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Companies",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Companies $companies */
        $companies = $this->companiesRepository->find($id);

        if (empty($companies)) {
            return $this->sendError('Companies not found');
        }

        $companies->delete();

        return $this->sendSuccess('Companies deleted successfully');
    }
}
