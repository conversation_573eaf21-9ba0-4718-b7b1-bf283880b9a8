<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateleadsAPIRequest;
use App\Http\Requests\API\UpdateleadsAPIRequest;
use App\Models\leads;
use App\Repositories\leadsRepository;
use App\Repositories\LeadFollowsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Http\Resources\api\UserResource;
use App\Http\Resources\api\LeadResource;
use Response;
use Auth;

/**
 * Class leadsController
 * @package App\Http\Controllers\API
 */

class LeadsAPIController extends AppBaseController
{
    /** @var  leadsRepository */
    private $leadsRepository;
    private $leadFollowRepository;

    public function __construct(leadsRepository $leadsRepo, LeadFollowsRepository $leadFollowRepository)
    {
        $this->leadsRepository = $leadsRepo;
        $this->leadFollowsRepository = $leadFollowRepository;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/leads",
     *      summary="getleadsList",
     *      tags={"leads"},
     *      description="Get all leads",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/leads")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }
        
        // Extract pagination parameters from the request
        $perPage = $request->query('per_page', 10); // Default to 10 items per page
        $page = $request->query('page', 1); // Default to page 1
        
        if (Auth::check()) {
            $user = Auth::user();
            
            //$isAdmin = $user->hasRole('admin');
            
            $isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();
            
        
        
            $leadsQuery = Leads::where('company_id', $companyId);

          if (!$isAdmin) {
              $leadsQuery->where('assign_to', 'like', '%' . $user->id . '%');
          }

        // Clone the query before modifying it to ensure correct counts
        $totalProgress = (clone $leadsQuery)->where('leadstatus_id', 1)->count();
        $totalComplete = (clone $leadsQuery)->where('leadstatus_id', 2)->count(); // Assuming 'complete' status is 2
        $totalCancel = (clone $leadsQuery)->where('leadstatus_id', 3)->count(); 
        $totalHold = (clone $leadsQuery)->where('leadstatus_id', 4)->count(); 
        $totalOpen = (clone $leadsQuery)->where('leadstatus_id', 0)->count();

            
            
        
            // If perPage is set to 'all', get all leads without pagination
            if ($perPage === 'all') {
                $leads = $leadsQuery->get();
            } else {
                // Paginate the results
                $leads = $leadsQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
            }
        
            return response()->json([
                'total_open' => $totalOpen ?? 0,
                'total_progress' => $totalProgress ?? 0,
                'total_complete' => $totalComplete ?? 0,
                'total_cancel' => $totalCancel ?? 0,
                'total_hold' => $totalHold ?? 0,
                'success' => true,
                'data' => LeadResource::collection($leads), // Get the paginated items
                'pagination' => [
                    'total' => $leads->total(),
                    'per_page' => $leads->perPage(),
                    'current_page' => $leads->currentPage(),
                    'last_page' => $leads->lastPage(),
                    'from' => $leads->firstItem(),
                    'to' => $leads->lastItem(),
                ],
            ]);
        } else {
            return response()->json(['error' => 'Please login to access.'], 401);
        }
            
        
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/leads",
     *      summary="createleads",
     *      tags={"leads"},
     *      description="Create leads",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/leads")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/leads"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateleadsAPIRequest $request)
    {
        $input = $request->all();
        $input['created_by'] = auth()->user()->id;
        $input['updated_by'] = auth()->user()->id;

        $leads = $this->leadsRepository->create($input);
        
        if (isset($input['follow_up'])) {
            
            $followUps = json_decode($request->input('follow_up'), true);
            
            if (is_array($followUps)) {
            
                foreach ($followUps as $followUp) {                    
                    $followUpData = [
                        'lead_id' => $leads->id,
                        'date_and_time' => $followUp['date_and_time'] ?? '',
                        'description' => $followUp['description'] ?? '',
                        'created_by' => $followUp['updated_by']['id'],
                        'updated_by' => $followUp['updated_by']['id']
                    ];                    
                    $this->leadFollowsRepository->create($followUpData);
                }
            }
        }
      
      	$activity_types = ['add_leads', 'assigned_leads'];

          // Loop through each activity type
          foreach ($activity_types as $activity_type) {
              // Define the activity data for each type
              $activity_data = [
                  'activity_type' => $activity_type,
                  'service_id' => $leads->id,
                  'services' => $leads
              ];

              // Call the function to save the activity
              saveServiceActivity($activity_data);
          }

        return $this->sendResponse(new LeadResource($leads), 'Leads saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/leads/{id}",
     *      summary="getleadsItem",
     *      tags={"leads"},
     *      description="Get leads",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of leads",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/leads"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var leads $leads */
        $leads = $this->leadsRepository->find($id);

        if (empty($leads)) {
            return $this->sendError('Leads not found');
        }

        return $this->sendResponse(new LeadResource($leads), 'Leads saved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/leads/{id}",
     *      summary="updateleads",
     *      tags={"leads"},
     *      description="Update leads",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of leads",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
    *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/leads")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/leads"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateleadsAPIRequest $request)
    {
         $input = $request->all();

        /** @var leads $leads */
        $leads = $this->leadsRepository->find($id);

        if (empty($leads)) {
            return $this->sendError('Leads not found');
        }
        $input['updated_by'] = auth()->user()->id;

        $leads = $this->leadsRepository->update($input, $id);
        
        
            if (isset($input['follow_up'])) {
                $followUps = json_decode($input['follow_up'], true);
                
                
        
                // Update or create follow-up entries
                if (is_array($followUps)) {
                    foreach ($followUps as $followUp) {
                        
                     
                        $followUpData = [
                            'lead_id' => $id,
                            'date_and_time' => $followUp['date_and_time'] ?? '',
                            'description' => $followUp['description'] ?? '',
                            'updated_by' => $followUp['updated_by']['id'],
                            // Optionally, you can also update 'created_by' if needed
                        ];
        
                        // Check if this follow-up entry already exists and update it, otherwise create a new one
                        if (isset($followUp['id'])) {
                            // Update existing follow-up entry
                            $existingFollowUp = $this->leadFollowsRepository->update($followUpData, $followUp['id']);
                        } else {
                            // Create new follow-up entry
                            $newFollowUp = $this->leadFollowsRepository->create($followUpData);
                        }
                    }
                }
        }
        return $this->sendResponse(new LeadResource($leads), 'leads updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/leads/{id}",
     *      summary="deleteleads",
     *      tags={"leads"},
     *      description="Delete leads",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of leads",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var leads $leads */
        $leads = $this->leadsRepository->find($id);

        if (empty($leads)) {
            return $this->sendError('Leads not found');
        }
        
        $this->leadFollowsRepository->deleteByLeadId($id);


        $leads->delete();

        return $this->sendSuccess('Leads deleted successfully');
    }
}
