const state = {
    anyModalOpen: false
  };
  
  const mutations = {
    setModalState(state, isOpen) {
      state.anyModalOpen = isOpen;
      },
      RESET_STATE(state) {
        state.anyModalOpen = false;
    }
  };
  
  const actions = {
    openModal({ commit }) {
      commit('setModalState', true);
    },
    closeModal({ commit }) {
      commit('setModalState', false);
    },
    toggleModal({ commit, state }) {
      commit('setModalState', !state.anyModalOpen);
    }
  };
  
  const getters = {
    anyModalOpen: state => state.anyModalOpen
  };
  
  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
  