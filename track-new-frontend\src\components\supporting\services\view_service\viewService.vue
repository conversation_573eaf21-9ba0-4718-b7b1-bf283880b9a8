<template>
    <div class="main-content" :class="{ 'mt-[60px]': isMobile, 'mb-[60px]': !isMobile }">
        <div class="my-custom-margin">
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
                :rows="number_of_rows" :gap="gap" :type="'grid'">
            </skeleton>
            <div v-if="!isMobile">
                <!-- <div v-if="!open_skeleton" class="flex justify-between">
                <div class="flex mr-2">
                    <button @click="goBackButton" :class="{ 'mr-2': isMobile }"
                        class="text-green-600 hover:text-green-700 text-sm text-center"><span
                            class=" text-center font-bold">&#8592;</span> <span v-if="!isMobile" class="text-center">Go
                            Back</span>
                    </button>

                </div>
            </div> -->
                <p v-if="!open_skeleton" class="font-bold text-sm mt-3">
                    <span @click="goBackToHomePage"
                        class="text-gray-500 hover:text-black hover:underline cursor-pointer">Services</span>
                    <span class="px-2 text-gray-500"><font-awesome-icon icon="fa-solid fa-angles-right" /></span>
                    <span @click="goBack" class="text-gray-500 hover:text-black hover:underline cursor-pointer">
                        {{ category_name }}</span>
                    <span class="px-2 text-gray-500"><font-awesome-icon icon="fa-solid fa-angles-right" /></span>
                    <span @click="reloadPage" class="text-blue-700 cursor-pointer hover:text-blue-500">{{ type ===
                        'edit' ? 'Update' :
                        'Create New'
                    }}</span>
                </p>
            </div>
            <!--New headbar-->

            <!-- Dynamic form creation -->
            <div v-if="!isFormEmpty && dynamicForm && dynamicForm.length !== 0 && !open_skeleton">
                <preview :dynamicForm="dynamicForm" @collectData="collectData" :status="getDataFromForm"
                    :existData="data" :companyId="companyId" :userId="userId" :isCompleted="isItCompleted"
                    @saveComments="submitData" :updateModalOpen="updateModalOpen" @update-is-modal-open="isModalOpen"
                    @collectSearchData="collectSearchData">
                </preview>
            </div>
            <div class="flex relative">
                <!--Buttons-->
                <div v-if="!open_skeleton" class="fixed-buttons-container">
                    <!--sms, Whatsapp & email--->
                    <div v-if="!isMobile" class="flex flex-row w-full mt-1 items-center space-x-6"
                        :class="{ 'justify-center': !sidebaropen, 'justify-start': sidebaropen }">
                        <!-- Save and Cancel -->
                        <div class="flex items-center justify-center space-x-4">
                            <!-- Cancel Button -->
                            <div class="w-36 flex justify-center items-center rounded bg-red-700 text-white cursor-pointer p-2 hover:bg-red-800 transition-colors duration-200"
                                @click="cancelData" title="Cancel Action">
                                <font-awesome-icon icon="fa-regular fa-rectangle-xmark" :style="{ color: 'white' }"
                                    class="px-2" />
                                <p class="text-center text-sm">Cancel</p>
                            </div>

                            <!-- Save Button -->
                            <div class="w-36 flex justify-center items-center justify-center rounded space-x-2 cursor-pointer p-2"
                                :class="{ 'text-gray-500': !isFormModified, 'bg-gray-400': !isFormModified, 'bg-green-700 text-white': isFormModified }"
                                @click="submitData" title="Save Changes">
                                <font-awesome-icon icon="fa-solid fa-floppy-disk" class="px-2"
                                    :style="{ color: isFormModified ? 'white' : 'gray' }" />
                                <p class="text-center text-sm">Save</p>
                            </div>
                            <!-- Jobsheet Button -->
                            <div class="w-36 flex justify-center items-center bg-gray-700 rounded text-white cursor-pointer p-2 hover:bg-gray-800 transition-colors duration-200"
                                @click="openJobSheet" title="Open Job Sheet">
                                <font-awesome-icon icon="fa-solid fa-receipt" style="color: #74C0FC;" class="px-2" />
                                <p v-if="!isMobile" class="text-center text-sm">Jobsheet</p>
                            </div>

                            <!-- Generate Invoice Button -->
                            <div v-if="validateIsFeature()"
                                class="w-42 flex justify-center items-center bg-blue-600 text-white rounded cursor-pointer p-2 hover:bg-blue-700 transition-colors duration-200"
                                @click="generateInvoice" title="Generate or Edit Invoice">
                                <font-awesome-icon icon="fa-solid fa-file-invoice-dollar" style="color: #f9ea05;"
                                    class="px-1" />
                                <p v-if="!isMobile" class="text-center text-sm">
                                    {{ data && data.invoice_id ? 'View Invoice' : 'Generate Invoice' }}
                                </p>
                                <font-awesome-icon v-if="getplanfeatures('service_estimations')"
                                    icon="fa-solid fa-crown" class="text-yellow-500 rounded-lg pl-1" />
                            </div>
                            <!--service history-->
                            <!-- Jobsheet Button -->
                            <div class="w-35 flex justify-center items-center bg-gray-700 rounded text-white cursor-pointer p-2 hover:bg-gray-800 transition-colors duration-200"
                                @click="openServiceHistory" title="Open Job Sheet">
                                <font-awesome-icon icon="fa-solid fa-timeline" class="px-2" />
                                <p v-if="!isMobile" class="text-center text-sm">service history</p>
                            </div>
                        </div>
                    </div>

                    <!---IsMobile view-->
                    <div v-if="isMobile" class="fixed bottom-0 left-0 z-50 w-full bg-white border-t border-gray-200">
                        <div class="grid h-full max-w-lg grid-cols-4 mx-auto font-medium">
                            <button type="button" @click="cancelData"
                                class="inline-flex flex-col items-center justify-center px-5 py-2 hover:bg-gray-50"
                                :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'cancel' }">
                                <span class="w-5 h-5 text-gray-50"
                                    :class="{ 'grayscale': selected_btn_btm !== 'cancel', 'grayscale-0': selected_btn_btm === 'cancel' }">
                                    <font-awesome-icon icon="fa-regular fa-rectangle-xmark" size="xl"
                                        :style="{ color: 'red' }" />
                                </span>
                                <span class="text-[10px] pt-1"
                                    :class="{ 'text-blue-700': selected_btn_btm === 'cancel', 'text-gray-500': selected_btn_btm !== 'cancel' }">Cancel</span>
                            </button>
                            <button type="button" @click="submitData"
                                class="inline-flex flex-col items-center justify-center px-5 py-2 hover:bg-gray-50"
                                :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'save' }">
                                <span class="w-5 h-5"
                                    :class="{ 'grayscale': selected_btn_btm !== 'save', 'grayscale-0': selected_btn_btm === 'save' }">
                                    <font-awesome-icon icon="fa-solid fa-floppy-disk" size="xl"
                                        :style="{ color: getIconColor('save') }" />
                                </span>
                                <span class="text-[10px] pt-1"
                                    :class="{ 'text-blue-700': selected_btn_btm === 'save', 'text-gray-500': selected_btn_btm !== 'save' }">Save</span>
                            </button>
                            <button type="button" @click="openJobSheet"
                                class="inline-flex flex-col items-center justify-center px-5 py-2 hover:bg-gray-50"
                                :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'jobsheet' }">
                                <span class="w-5 h-5"
                                    :class="{ 'grayscale': selected_btn_btm !== 'jobsheet', 'grayscale-0': selected_btn_btm === 'jobsheet' }">
                                    <font-awesome-icon icon="fa-solid fa-receipt" :style="{ color: '#74C0FC' }"
                                        size="xl" />
                                </span>
                                <span class="text-[10px] pt-1"
                                    :class="{ 'text-blue-700': selected_btn_btm === 'jobsheet', 'text-gray-500': selected_btn_btm !== 'jobsheet' }">Jobsheet
                                </span>
                            </button>
                            <button v-if="validateIsFeature()" type="button" @click="generateInvoice"
                                class="inline-flex flex-col items-center justify-center px-5 py-2 hover:bg-gray-50"
                                :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'invoice' }">
                                <span class="w-5 h-5"
                                    :class="{ 'grayscale': selected_btn_btm !== 'invoice', 'grayscale-0': selected_btn_btm === 'invoice' }">
                                    <font-awesome-icon icon="fa-solid fa-file-invoice-dollar"
                                        :style="{ color: '#f09a05' }" size="xl" />
                                </span>
                                <span class="text-[10px] pt-1"
                                    :class="{ 'text-blue-700': selected_btn_btm === 'invoice', 'text-gray-500': selected_btn_btm !== 'invoice' }">
                                    {{ data && data.invoice_id ? 'View' : 'Generate' }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Import the child component -->
        <smsWhatsappEmail :show-modal="showModal" :selected-option="selectedOption" @close-modal="closeModal">
        </smsWhatsappEmail>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModalCustomer" :userName="userName"
            :companyId="companyId">
        </customerRegister>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
    </div>
    <Loader :showModal="open_loader"></Loader>
    <jobsheet :showModal="show_job" :item_data="data" @close-modal="closeJobSheet" :companyId="companyId"></jobsheet>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <notificationAlert :showModal="show_notification" @onCancel="cancelTheNotification"
        @onConfirm="enableTheNotification"></notificationAlert>
    <completedService :showModal="open_completed" @onClose="closeOpenModalService"
        :customer_id="formValues.customer_id ? formValues.customer_id : null"></completedService>
    <serviceHistory :is-modal-open="showHistory" :service-details="data" :category_id="category_id"
        :companyId="companyId" @close="closeHistory"></serviceHistory>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>

<script>
import smsWhatsappEmail from '../../dialog_box/dialog_sms_whatsapp_email.vue';
import customerRegister from '../../dialog_box/customerRegister.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import preview from '../../categories/create_form/preview.vue';
import axios from 'axios';
import jobsheet from '../../dialog_box/jobsheet.vue';
import notificationAlert from '../../dialog_box/notificationAlert.vue';
import { mapActions, mapGetters } from 'vuex';
import completedService from '../../dialog_box/completedService.vue';
import serviceHistory from '../../dialog_box/serviceHistory.vue';
import noAccessModel from '../../dialog_box/noAccessModel.vue';

export default {
    name: 'view_services',
    emits: ['updatedData', 'updateIsOpen'],
    components: {
        smsWhatsappEmail,
        customerRegister,
        dialogAlert,
        preview,
        jobsheet,
        notificationAlert,
        completedService,
        serviceHistory,
        noAccessModel
    },
    props: {
        data: Object,
        category_name: String,
        labelsName: Object,
        fieldKey: Object,
        category_id: String,
        companyId: String,
        userId: String,
        isCompleted: Boolean,
        updateModalOpen: Boolean,
    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('features_list', ['currentFeatureList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures', 'sidebaropen']),
    },
    data() {
        return {
            isMobile: false,
            isFormEmpty: false,
            showModal_customer: false,
            isMessageDialogVisible: false,
            originalFormValues: {},
            isFormModified: false,
            type: 'edit',
            userName: null,
            showModal: false,
            selectedOption: '',
            serviceCategories: [],
            formValues: {},
            isDropdownOpen: false,
            filteredCustomerOptions: [],
            dynamicForm: [],
            getDataFromForm: true,
            message: null,
            backup_data: null,
            overall_backup: null,
            //--skeleton
            open_skeleton: true,
            number_of_columns: 2,
            number_of_rows: 20,
            gap: 5,
            open_loader: false,
            show_job: false,
            isItCompleted: false,
            selected_btn_btm: '',
            //--toaster---
            show: false,
            type_toaster: 'success',
            show_notification: false,
            notification_validate: false,
            //---completed services----
            open_completed: false,
            //--service history--
            showHistory: false,
            //---no access---
            no_access: false,
        };
    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('features_list', ['fetchFeatureList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),

        openJobSheet() {
            this.selected_btn_btm = 'jobsheet';
            this.show_job = true;
        },
        closeJobSheet() {
            this.show_job = false;
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        openModal(option) {
            // console.log(option, "What happening ...");
            this.selectedOption = option;
            this.showModal = true;
        },
        //---go back button--
        goBackButton() {
            if (!this.isFormModified) {
                this.$router.go(-1);
            } else {
                this.openMessageDialog('Please save latest updation..!');
            }
        },
        closeModal() {
            this.showModal = false;
        },
        goBackToHomePage() {
            // this.$router.push('/services');
            this.$router.go(-1);
        },
        reloadPage() {
            window.location.reload();
        },
        goBack() {
            // Go back to the previous page
            this.$router.push(-1);
        },
        editRecord() {
            // Handle edit action
            // console.log("Edit", this.data);
            // this.$emit('showAddServiceComponent', record);
            this.$router.push({ name: 'service-category-edit', params: { editId: this.data.id } });
        },
        findData(data) {
            return this.data.data;
        },
        //--validate in object values
        validateAdditionalMaterial(data) {
            // console.log('Validate................', data, 'What happening..!');
            if (data && data.length > 0) {
                // console.log(data.every(opt => opt.qty >= 1 && opt.price > 0 && opt.status === "approved"), 'Status');
                if (data.every(opt => opt.qty >= 1 && opt.price > 0 && opt.status === "approved")) {
                    return true;
                } else {
                    // console.log('hello');
                    this.openMessageDialog('Please get additional materials approved by the customer or obtain manual approval. Please set all quantities to non-zero values and ensure that prices are not set to zero.');
                    return false;
                }
            } else if (data && data.length === 0) {
                return true;
            }
            else {
                // this.openMessageDialog('Please validate additional material information.');
                return true;
            }
        },
        generateInvoice() {
            if (!this.getplanfeatures('service_estimations')) {
                this.selected_btn_btm = 'invoice';
                let find_status_list = this.dynamicForm.find(opt => opt.fieldKey === 'status');
                //     find_status_list && (this.formValues.status === find_status_list.option[4] || this.formValues.status === find_status_list.option[5] || this.formValues.status === find_status_list.option[6] || this.formValues.status === find_status_list.option[7]), 'Hellooo', !this.isFormModified, 'Hia', (this.formValues.estimateAmount > 0 || this.formValues.serviceAmount > 0));
                //---(this.formValues.estimateAmount > 0 || this.formValues.serviceAmount > 0)
                if (find_status_list && (this.formValues.status === find_status_list.option[4] || this.formValues.status === find_status_list.option[5] || this.formValues.status === find_status_list.option[6] || this.formValues.status === find_status_list.option[7] || this.formValues.status === find_status_list.option[9] || this.formValues.status === find_status_list.option[10] || this.formValues.status === find_status_list.option[11]) && !this.isFormModified) {

                    if (this.data && this.data.additional && this.validateAdditionalMaterial(this.data.additional)) {

                        if (this.data.sale_id) {
                            this.$router.push({
                                name: 'print-preview',
                                query: { invoice_no: this.data.sale_id }
                            });

                        } else {
                            this.$router.push({ name: 'generate-invoice', params: { serviceId: this.data.id }, query: { type: 'add' } });
                        }
                    } else if (this.validateAdditionalMaterial(this.data && this.data.additional)) {
                        if (this.data.sale_id) {
                            this.$router.push({
                                name: 'print-preview',
                                query: { invoice_no: this.data.sale_id }
                            });

                        } else {
                            this.$router.push({ name: 'generate-invoice', params: { serviceId: this.data.id }, query: { type: 'add' } });
                        }
                    }
                } else {
                    const { status, estimateAmount, serviceAmount } = this.formValues;
                    const statusLower = status.toLowerCase();

                    const isStatusValid =
                        find_status_list &&
                        (statusLower === find_status_list.option[4]?.toLowerCase() ||
                            statusLower === find_status_list.option[5]?.toLowerCase() ||
                            statusLower === find_status_list.option[6]?.toLowerCase() ||
                            statusLower === find_status_list.option[7]?.toLowerCase());

                    const hasAmounts = estimateAmount > 0 || serviceAmount > 0;

                    let message;
                    if (this.isFormModified) {
                        message = 'Please save latest updation and generate invoice..!';
                    } else if (!(find_status_list && isStatusValid)) {
                        message = 'Please change the status to be Success or cancelled';
                    } else if (!hasAmounts) {
                        message = 'Please update the service amount or estimation amount';
                    } else {
                        message = 'Please change the status to be Success or cancelled';
                    }

                    this.openMessageDialog(message);
                }
            } else {
                //----enable not have access to current plan  
                this.no_access = true;
            }
        },
        openModalCustomer(data) {
            this.userName = data;
            // console.log(data, 'What happening...!');
            this.isDropdownOpen = false;
            this.showModal_customer = true;
        },
        closeModalCustomer() {
            this.showModal_customer = false;
        },

        cancelData() {
            this.selected_btn_btm = 'cancel';
            // console.log('cancel the data');
            this.isFormModified = false;
            this.$router.go(-1);
            // window.location.reload();
        },
        collectSearchData(data) {
            if (data) {
                this.searchData = data;
            }
        },
        addFormUpdatedData() {
            let is_find = false;
            if (this.searchData && this.searchData.problem_title && this.searchData.problem_title !== '') {
                let findData = this.dynamicForm.find(opt => opt.fieldKey == 'problem_title');
                if (findData && !findData.option.includes(this.searchData.problem_title)) {
                    findData.option.push(this.searchData.problem_title);
                    if (this.formValues.problem_title) {
                        this.formValues.problem_title.push(this.searchData.problem_title);
                    } else {
                        this.formValues.problem_title = [this.searchData.problem_title];
                    }
                    is_find = true;
                } else if (!this.formValues.problem_title.includes(this.searchData.problem_title)) {
                    this.formValues.problem_title.push(this.searchData.problem_title);
                }
            }
            if (this.formValues && this.formValues.brand && this.formValues.brand !== '') {
                let findData = this.dynamicForm.find(opt => opt.fieldKey == 'brand');
                if (findData && !findData.option.includes(this.formValues.brand)) {
                    findData.option.push(this.formValues.brand);
                    is_find = true;
                }
            }
            if (this.formValues && this.formValues.device_model && this.formValues.device_model !== '') {
                let findData = this.dynamicForm.find(opt => opt.fieldKey == 'device_model');
                if (findData && !findData.option.includes(this.formValues.device_model)) {
                    findData.option.push(this.formValues.device_model);
                    is_find = true;
                }
            }
            if (this.category_id && is_find) {
                //---service category---
                axios.put(`/service_categories/${this.category_id}`, { company_id: this.companyId, form: JSON.stringify(this.dynamicForm), service_category: this.category_name })
                    .then(response => {
                        // console.log(response.data.data, 'response for updated options');
                        if (response.data.data.form) {
                            this.dynamicForm = JSON.parse(response.data.data.form);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    })
            }
        },

        async submitData(status_cmt) {
            if (this.getplanfeatures('service')) {
                this.no_access = true;
            } else {
                this.selected_btn_btm = 'save';
                if (!this.isFormEmpty && this.isFormModified) {
                    await this.addFormUpdatedData();
                    // if (this.formValues.notification.length > 0 || this.notification_validate || status_cmt === true) {
                    this.open_loader = true;
                    // Validate required fields
                    const requiredFields = this.dynamicForm.filter(field =>
                        field.required === 'yes' &&
                        field.enable && !['estimateAmount', 'advanceAmount', 'serviceAmount'].includes(field.fieldKey)  // Exclude specific field keys
                    );
                    const invalidFields = requiredFields.filter(field => {
                        if (!this.formValues['warranty_type'] || this.formValues['warranty_type'] !== 'Free') {
                            if ((field.fieldKey === 'document' && this.getplanfeatures('service_image')) || (field.fieldKey === 'additional' && this.getplanfeatures('additional_materials'))) {
                                // console.log('it is working......');
                            } else {
                                const value = this.formValues[field.fieldKey];
                                return value === undefined || value === null;
                            }
                        } else if (field.fieldKey !== 'estimateAmount' && field.fieldKey !== 'advanceAmount' && field.fieldKey !== 'serviceAmount') {
                            if ((field.fieldKey === 'document' && this.getplanfeatures('service_image')) || (field.fieldKey === 'additional' && this.getplanfeatures('additional_materials'))) {
                                console.log('it is working......');
                            } else {
                                const value = this.formValues[field.fieldKey];
                                return value === undefined || value === null;
                            }
                        }
                    });

                    if (invalidFields.length > 0) {
                        // Show error message for empty required fields
                        this.openMessageDialog(`Please fill in all required fields are ${invalidFields.map(field => field.lableName).join(', ')} `);
                        // this.open_skeleton = false;
                        this.open_loader = false;
                        return;
                    }

                    let serviceTrack = [];
                    if (this.data && this.data.service_track && this.data.service_track.length > 0) {
                        serviceTrack = this.data.service_track;
                    } else {
                        let find_status_list = this.dynamicForm.find(opt => opt.fieldKey === 'status');

                        if (find_status_list) {
                            serviceTrack = find_status_list.option.map((opt, i) => {
                                return { name: opt, date: '', status: false };
                            });
                        }
                    }
                    const findTheObject = this.serviceCategories.find((opt) => opt.id === Number(this.category_id));
                    if (findTheObject) {
                        if (this.type === 'edit') {
                            let { customer_id, expected_date, estimateAmount, advanceAmount, serviceAmount, status, notification, document, additional, assignWork, comments, service_track } = this.formValues;

                            let findIndex = serviceTrack.findIndex(opt => opt.name.toLowerCase() === status.toLowerCase());
                            if ((status === 'delivered' || status === 'to be delivered' || status === 'completed') && serviceAmount === 0 || !serviceAmount) {
                                //----get service amount to add additional amount
                                // let get_total_service = 0;
                                // if (estimateAmount > 0) {
                                //     get_total_service = get_total_service + estimateAmount;
                                // }
                                // if (additional && additional.length > 0) {
                                //     let total = additional.reduce((acc, item) => acc + item.total, 0);
                                //     get_total_service += total;
                                // }
                                // this.formValues.serviceAmount = get_total_service;
                            }
                            if (additional && additional.length > 0) {
                                // Filter out items with qty === 0 and calculate total of remaining items
                                let filteredAdditional = additional.filter(item => item.qty !== 0);

                                // Calculate the total of remaining items
                                let total = filteredAdditional.reduce((acc, item) => acc + item.total, 0);

                                // Update the 'additional' array with filtered items (optional)
                                additional = filteredAdditional;

                                // Use 'total' for further processing
                                // console.log('Total of non-zero qty items:', total, filteredAdditional);
                            }
                            if (findIndex !== -1) {
                                if (serviceTrack[findIndex].status !== true) {
                                    serviceTrack[findIndex].status = true;
                                    serviceTrack[findIndex].date = this.getCurrentDateTime();
                                }
                                // else if (assignWork) {
                                //     serviceTrack[findIndex].assign_to = JSON.stringify(assignWork.map(obj => ({ user_name: obj.name })));
                                // }
                                serviceTrack.map((opt, i) => {
                                    if (i !== findIndex && (i > findIndex || i === 1 || i === 6)) {
                                        opt.status = false;
                                    } else if (i !== findIndex && i < findIndex && i !== 1 && i !== 6) {
                                        if (opt.status === false) {
                                            opt.status = true;
                                        }
                                        if (opt.date === '') {
                                            opt.date = this.getCurrentDateTime();
                                        }
                                    }
                                })
                            }

                            let service_send_data = {
                                customer_id: customer_id,
                                servicecategory_id: Number(this.category_id),
                                expected_date: expected_date && expected_date,
                                estimate_amount: estimateAmount ? estimateAmount : 0,
                                advance_amount: advanceAmount ? advanceAmount : 0,
                                service_amount: serviceAmount ? serviceAmount : 0,
                                status: JSON.stringify(findIndex >= 0 ? findIndex : ''),
                                notification: JSON.stringify(notification),
                                document: JSON.stringify(document),
                                materials: JSON.stringify(additional),
                                assign_to: assignWork && JSON.stringify(assignWork.map(obj => ({ user_id: obj.id }))),
                                service_track: JSON.stringify(serviceTrack),
                                comments: JSON.stringify(comments),
                                service_expense: this.formValues['service_expense'] ? this.formValues['service_expense'].filter(opt => opt.value > 0) : [],
                            };
                            axios.put(`/services/${this.formValues.id}`, { company_id: this.companyId, ...service_send_data, notification: status_cmt === true ? JSON.stringify(['']) : this.originalFormValues['status'] !== status ? JSON.stringify(notification) : JSON.stringify(['']), service_data: JSON.stringify({ ...this.formValues, additional: additional }) })
                                .then(response => {
                                    this.message = 'Service has been updated succesfully...!';
                                    this.type_toaster = 'success';
                                    this.show = true;
                                    this.updateKeyWithTime('service_update');
                                    this.updateKeyWithTime('service_category_update');
                                    //---make congratulations---
                                    if (response.data.data.status == 5 || response.data.data.status == 7) {
                                        this.open_completed = true;
                                    }
                                    this.goPermission = true;
                                    this.isFormModified = false;
                                    this.notification_validate = false;
                                    let response_data = response.data.data;
                                    if (response_data.status == 5 || response_data.status == 6 || response_data.status == 7) {
                                        this.isItCompleted = true;
                                    }
                                    // this.open_skeleton = false;
                                    this.open_loader = false;
                                    let exist_data = JSON.parse(response_data.service_data);
                                    if (exist_data.additional && exist_data.additional.length > 0) {
                                        let material_list = JSON.parse(response_data.materials);
                                        if (material_list && material_list.length > 0) {
                                            exist_data.additional = material_list;
                                            this.backup_data = material_list;
                                            exist_data.assignWork = response_data.assign_to;
                                            if (exist_data.notification) {
                                                exist_data.notification = JSON.parse(response_data.notification);
                                            }
                                            // console.log(exist_data.notification, 'What happening in notification data..!');
                                            if (response_data.document) {
                                                exist_data.document = JSON.parse(response_data.document);
                                                // console.log(exist_data.document, 'What happningin document...!');
                                            }

                                        }
                                    } if (this.validateType(exist_data.customer) === 'Number') {
                                        // console.log(response_data.customer, 'TTTTTTTTTTTTTTTTT')
                                        let { first_name, last_name, contact_number } = response_data.customer;
                                        exist_data.customer = `${first_name} ${last_name} - ${contact_number}`;
                                        exist_data.customer_id = response_data.customer_id;
                                    }
                                    if (!Array.isArray(exist_data.assignWork) && this.validateType(exist_data.assignWork) === 'Number') {
                                        let getList = exist_data.assignWork.split(', ');
                                        if (getList.length !== 0) {
                                            const filteredArray = this.employee_list.filter(obj => getList.includes(String(obj.id)));
                                            if (filteredArray.length !== 0) {
                                                exist_data.assignWork = filteredArray;
                                            }
                                        }
                                    }
                                    if (typeof exist_data.problem_title === 'string') {
                                        const issuesArray = exist_data.problem_title
                                            // Remove brackets and split by comma
                                            .slice(1, -1)
                                            .split(',')
                                            // Trim each issue
                                            .map(issue => issue.trim());
                                        if (Array.isArray(issuesArray) && issuesArray.length !== 0) {
                                            exist_data.problem_title = issuesArray;
                                        }
                                    }
                                    //---go back
                                    if (status_cmt === undefined) {
                                        this.$router.go(-1);
                                    }
                                    // if(response_data.comments){
                                    //     exist_data.comments = JSON.parse(response_data.comments);
                                    // }
                                    this.formValues = { ...exist_data, id: response_data.id, invoice_id: response_data.invoice_id, };
                                    this.$emit('updatedData', this.formValues);
                                    this.overall_backup = { ...exist_data, id: response_data.id, invoice_id: response_data.invoice_id };
                                    this.originalFormValues = { ...this.formValues };

                                    // this.openMessageDialog(response.data.message);
                                    // setTimeout(() => {
                                    //     this.$emit('getExistdata', response.data.data);
                                    // }, 500);
                                })
                                .catch(error => {
                                    console.error('Error', error);
                                    this.open_loader = false;
                                    // this.open_skeleton = false;                                    
                                    this.openMessageDialog(error.response.data.message);

                                })
                        }
                    } else {
                        // Handle case where the category is not found
                        console.error('Category not found');
                    }
                    this.isFormModified = false;
                    // } else {
                    //     this.show_notification = true;
                    // }

                } else {
                    this.openMessageDialog("Form data already save!");
                }
            }
        },
        //---validate data type--
        validateType(value) {
            if (!isNaN(value) && !isNaN(parseFloat(value))) {
                return 'Number';
            } else if (typeof value === 'string') {
                return 'String';
            } else {
                return 'Invalid';
            }
        },
        //---status track function--
        // Get current date and time in the desired format (e.g., "2024-03-19 6:20PM")
        getCurrentDateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const meridiem = (hours < 12) ? 'AM' : 'PM';
            const formattedHours = (hours % 12) || 12;
            return `${year}-${month}-${day} ${formattedHours}:${minutes}${meridiem}`;
        },
        goBackToHomePage() {
            this.$router.push('/services');
        },
        reloadPage() {
            window.location.reload();
        },
        goBack() {
            // Go back to the previous page
            this.$router.push({ name: 'categories' });
        },
        //-----
        //---customers dropdown
        selectDropdownOption(fields, option) {
            this.formValues[fields.fieldKey] = option.name;
            this.isDropdownOpen = false; // Close the dropdown
        },

        handleDropdownInput(fields) {
            const inputValue = this.formValues[fields.fieldKey];

            if (inputValue !== undefined && inputValue !== null) {
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = fields.option.filter(
                        (option) => option.phone.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = fields.option.filter(
                        (option) => option.name.toLowerCase().includes(inputName)
                    );
                }
            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = [];
            }
        },
        //---update based on category--
        updateComponentBasedOnCategoryName(newCategoryName) {
            let findCategory = this.serviceCategories.find((opt) => opt.id === Number(this.category_id));
            if (findCategory.form && findCategory.form.length !== 0) {
                this.dynamicForm = JSON.parse(findCategory.form);
            } else {
                this.isFormEmpty = true;
            }
        },
        // Function to set originalFormValues when data changes
        setOriginalFormValues() {
            this.originalFormValues = { ...this.formValues };

        },

        checkFormModification() {
            this.isFormModified = !Object.keys(this.formValues).every((key, j) => {
                if (key === 'additional') {
                    if (this.formValues[key].length !== 0 && this.originalFormValues['additional'] && this.originalFormValues['additional'].length !== 0) {
                        if (this.formValues[key].length === this.backup_data.length) {
                            // console.log(this.formValues[key].map((subKey, index) => subKey.qty + ' ' + this.originalFormValues['additional'][index].qty), this.originalFormValues['additional'], 'Hello');
                            let res_data = this.formValues[key].every((subKey, index) =>
                                subKey.product_name === this.backup_data[index].product_name &&
                                subKey.qty === this.backup_data[index].qty &&
                                subKey.price === this.backup_data[index].price &&
                                subKey.status === this.backup_data[index].status
                            );
                            return res_data
                        }
                        else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                }
                else if (key === 'notification') {
                    if (this.formValues[key].length === this.overall_backup[key].length) {
                        if (this.formValues[key].length === this.overall_backup[key].length) {
                            let isEqual = true;
                            this.formValues[key].forEach((data, l) => {
                                if (data !== this.overall_backup[key][l]) {
                                    isEqual = false;
                                }
                            });
                            return isEqual;
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                }
                else if (key === 'document') {
                    if (Array.isArray(this.formValues[key]) && this.formValues[key].length > 0 && Array.isArray(this.overall_backup[key]) && this.overall_backup[key].length > 0) {
                        if (this.formValues[key].length === this.overall_backup[key].length) {
                            for (let i = 0; i < this.formValues[key].length; i++) {
                                const formData = this.formValues[key][i];
                                const backupData = this.overall_backup[key][i];
                                if (!this.isEqualObjects(formData, backupData)) {
                                    return false;
                                }
                            }
                            return true;
                        } else {
                            return true;
                        }
                    } else {
                        return true;
                    }
                } else if (key === 'pre_repair') {
                    return this.isEqualObjects(this.formValues[key], this.overall_backup[key]);
                } else if (key === 'problem_title') {
                    if (this.formValues[key].length === this.originalFormValues[key].length) {
                        const originalValues = this.originalFormValues[key];
                        const currentValues = this.formValues[key];
                        return originalValues.every((value, index) => value === currentValues[index]);
                    } else {
                        return this.formValues[key].length === this.originalFormValues[key].length;
                    }
                } else if (key === 'assignWork') {
                    if (Array.isArray(this.overall_backup[key]) && this.formValues[key].length === this.overall_backup[key].length) {
                        const originalValues = this.overall_backup[key];
                        const currentValues = this.formValues[key];
                        return originalValues.every((value, index) => value.name === currentValues[index].name);
                    } else {
                        return this.formValues[key].length === this.overall_backup[key].length;
                    }
                } else if (key === 'service_expense') {
                    if (Array.isArray(this.overall_backup[key]) && this.formValues[key].length === this.overall_backup[key].length) {
                        const originalValues = this.overall_backup[key];
                        const currentValues = this.formValues[key];

                        return originalValues.every((value, index) => value.description === currentValues[index].description && value.value === currentValues[index].value);
                    } else if (Array.isArray(this.formValues[key])) {
                        return this.formValues[key].length === this.overall_backup[key].length;
                    }
                }
                // console.log(this.formValues[key] === this.originalFormValues[key], 'rrr', this.formValues[key], 'ooo', this.originalFormValues[key], 'Key', key);
                return this.formValues[key] === this.originalFormValues[key];
            });
        },
        isEqualObjects(obj1, obj2) {
            const keys1 = Object.keys(obj1);
            const keys2 = Object.keys(obj2);
            if (keys1.length !== keys2.length) {
                return false;
            }
            for (const key of keys1) {
                if (obj1[key] !== obj2[key]) {
                    return false;
                }
            }
            return true;
        },

        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.goPermission === true) {
                // this.$router.go(-1);
            }
        },

        //---collect data from form
        collectData(data) {
            this.formValues = data;
        },
        //---service category---
        serviceCategoryList() {
            if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                this.open_skeleton = false;
                this.serviceCategories = this.currentServiceCategory;
                this.updateComponentBasedOnCategoryName(this.category_name);
            } else {
                this.fetchServiceCategoryList();
            }
        },
        //-- icon colors
        getIconColor(type) {
            // Determine the color based on the condition
            if (!this.isFormEmpty && this.isFormModified) {
                return type === 'cancel' ? 'red' : 'green';
            } else {
                return '#808080';
            }
        },
        //---notification--
        cancelTheNotification() {
            this.show_notification = false;
            this.notification_validate = true;
            this.submitData();
        },
        enableTheNotification() {
            this.show_notification = false;
            this.notification_validate = true;
            this.formValues.notification = ['SMS'];
            this.submitData();
        },
        //----update is modal open--
        isModalOpen(type) {
            if (type !== undefined) {
                this.$emit('updateIsOpen', type);
            }
        },
        //----completed services----        
        closeOpenModalService() {
            this.open_completed = false;
        },
        //----feature list in sales---
        validateIsFeature() {
            if (this.currentFeatureList && this.currentFeatureList.length > 0) {
                const find_data = this.currentFeatureList.find((opt) => opt.name === 'Sales');
                if (find_data && find_data.hasAccess) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        },
        //---service history--
        openServiceHistory() {
            this.showHistory = true;
        },
        closeHistory() {
            this.showHistory = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        }
    },
    mounted() {
        this.fetchApiUpdates();
        this.fetchLocalDataList();
        // console.log('What about data..!,', this.data);
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);

        if (!this.currentServiceCategory || this.currentServiceCategory.length === 0) {
            this.fetchServiceCategoryList();
        } else {
            this.fetchServiceCategoryList();
        }
        this.serviceCategoryList();
        if (this.data) {
            this.setOriginalFormValues();
        }
        // console.log(this.isCompleted, 'what happening.....');
        this.isItCompleted = this.isCompleted;
        this.isFormModified = false;
    },
    watch: {
        // Watch for changes in formValues
        formValues: {
            handler(newFormValues) {
                this.checkFormModification();
            },
            deep: true, // Enable deep watching to detect changes in nested objects
        },
        // Watch for changes in data
        data: {
            deep: true,
            handler(newData) {
                // Update your component's data properties               
                this.formValues = newData;
                // if (newData.customer)
                if (newData.additional) {
                    this.backup_data = JSON.parse(JSON.stringify(newData.additional));
                }
                this.overall_backup = JSON.parse(JSON.stringify(newData));
                // Set originalFormValues when data changes
                this.setOriginalFormValues();
                // Check for form modification
                this.checkFormModification();
            },
            immediate: true,
        },
        isCompleted: {
            deep: true,
            handler(newValue) {
                this.isItCompleted = newValue;
            }

        },
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.open_skeleton = false;
                    this.serviceCategories = newValue;
                    this.updateComponentBasedOnCategoryName(this.category_name);
                }
            }
        }
    },
};
</script>

<style scoped>
.fixed-buttons-container {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    z-index: 10;
}

.main-content {
    margin-bottom: 50px;
}


@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
    }

    .fixed-buttons-container {
        left: 0px;
        width: 100%;
    }

    .main-content {
        margin-bottom: 50px;
    }
}
</style>
