<template>
    <div>
        <div v-if="!open_skeleton && data.length > 0" class="m-2">
            <div
                :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="border-b border-gray-400">
                                <!--dynamic-->
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    :class="{ 'hidden': !column.visible }" class="py-2 text-left px-2">
                                    <p>{{ column.label }}</p>
                                </th>
                                <th class="py-2 leading-none">
                                    Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(record, index) in data" :key="index"
                                class="border-b border-gray-400 cursor-pointer hover:bg-gray-200">
                                <td v-for="(column, colIndex) in columns" :key="colIndex" @click="printRecord(record)"
                                    :class="{ 'hidden': !column.visible }" class="px-1">
                                    <span
                                        v-if="column.field !== 'customer' && column.field !== 'current_date' && column.field !== 'invoice_no' && column.field !== 'status' && column.field !== 'due_amount'">{{
                                            record[column.field] }}</span>
                                    <span v-if="column.field === 'customer'">
                                        {{ record[column.field] ? (record[column.field].first_name ?
                                            record[column.field].first_name : '') + ' ' +
                                            (record[column.field].last_name ? record[column.field].last_name : '') +
                                            ' - ' + (record[column.field].contact_number ?
                                                record[column.field].contact_number :
                                                '') : '' }}
                                    </span>
                                    <span v-if="column.field === 'current_date'">
                                        {{ record[column.field].substring(0, 10) !== '-000001-11' ?
                                            formatDateTime(record[column.field]) : '' }}</span>
                                    <!--due amount-->
                                    <span v-if="column.field === 'due_amount'" class="text-red-600">
                                        {{ record['status'] == 'Success' ? record[column.field] : 0 }}</span>
                                    <!--status-->
                                    <span v-if="column.field === 'status'" class="rounded-full px-2 py-1 text-xs"
                                        :class="{ 'bg-green-200 text-green-600': record[column.field] == 'Success', 'bg-red-200 text-red-600': record[column.field] == 'Cancel' }">
                                        {{ record[column.field] }}</span>
                                    <span v-if="column.field === 'invoice_no'">{{ typeof
                                        record['invoice_id'] === 'string' ?
                                        record['invoice_id'] : '' }}</span>
                                </td>
                                <td class="py-2 text-center">
                                    <div class="flex justify-center">
                                        <div class="flex relative">
                                            <button v-if="!record.editing" @click="printRecord(record)" title="Edit"
                                                class="text-gray-700 px-1 py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-solid fa-print" style="color: gray;" />
                                            </button>
                                            <button v-if="!record.editing" @click="startEdit(record)" title="Print"
                                                class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                            </button>
                                            <!-- <button @click.stop="displayAction(index)"
                                                class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                            </button> -->
                                        </div>
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-7 absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="printRecord(record)"
                                                        class="text-gray-700 px-2 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-print" style="color: gray;"
                                                            size="lg" />
                                                        <span class="px-2">Print</span>
                                                    </button>
                                                </li>
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="startEdit(record)"
                                                        class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" size="lg" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <!-- <li>
                                                <button v-if="!record.editing" @click="confirmDelete(index)"
                                                    class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-regular fa-trash-can"
                                                        style="color: #ef4444" />
                                                    <span class="px-2">Delete</span>
                                                </button>
                                            </li> -->
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--card view-->
                <div>
                    <div v-if="items_category === 'list'" class="grid grid-cols-1 custom-scrollbar-hidden" :class="{
                        'sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4': showContactInfo == undefined,
                        'sm:grid-cols-1  lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-2 lg:gap-4': showContactInfo == false,
                        'sm:grid-cols-1  lg:grid-cols-1 xl:grid-cols-1 2xl:grid-cols-2 gap-2 lg:gap-4': showContactInfo
                    }">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <div
                                class="max-w-md mx-auto bg-white rounded-xl shadow-lg overflow-hidden md:max-w-2xl border border-gray-200">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center p-4 py-2">
                                    <!-- Left Side (Can be your dynamic content) -->
                                    <div class="text-xs text-red-500">
                                        <p>{{ calculateDaysAgo(formattedDate(record.current_date, true)) }}</p>
                                    </div>
                                    <!--status-->
                                    <span class="rounded-full px-2 py-1 text-xs"
                                        :class="{ 'bg-green-200 text-green-600': record['status'] == 'Success', 'bg-red-200 text-red-600': record['status'] == 'Cancel' }">
                                        {{ record['status'] }}</span>
                                    <!-- Right Side (Actions) -->
                                    <div class="flex justify-center items-center">
                                        <div class="bg-gray-100 rounded-md p-2 items-center">
                                            <p class="text-sm text-gray-500"># {{ record['invoice_id'] }}</p>
                                        </div>

                                        <div class="flex justify-center items-center relative">
                                            <div class="relative ml-2">
                                                <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button>
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-[90px] absolute bg-slate-200 divide-y divide-gray-100 rounded-lg shadow-lg items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li>
                                                        <button v-if="!record.editing" @click="printRecord(record)"
                                                            class="text-gray-700 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-print"
                                                                style="color: gray;" size="lg" />
                                                            <span class="px-2">Print</span>
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <!-- <li>
                                                <button v-if="!record.editing" @click="confirmDelete(index)"
                                                    class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-regular fa-trash-can"
                                                        style="color: #ef4444" />
                                                    <span class="px-2">Delete</span>
                                                </button>
                                            </li> -->
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Middle Section -->
                                <div class="px-4 py-2">
                                    <!-- Customer Details (Can be your dynamic content) -->
                                    <!-- <div class="flex items-center mb-2 -mt-4">
                                    <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                        :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-lime-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-blue-600': index === 0 }">
                                        {{ record.customer && record.customer.first_name ?
                                            record.customer.first_name[0].toUpperCase() :
                                            'C' }}
                                    </div>
                                    <div>
                                        <h4 class="leading-6 font-semibold text-gray-900 mb-1">
                                            {{ record.customer && record.customer.first_name ?
                                                record.customer.first_name + ' ' + (record.customer.last_name ?
                                                    record.customer.last_name : '') : '' }}</h4>
                                        <p class="text-gray-500 cursor-pointer"
                                            @click="dialPhoneNumber(record.customer.contact_number)">+91-{{
                                                record.customer.contact_number }}</p>
                                    </div>
                                </div> -->

                                    <!-- Invoice Details (Should iterate over your data) -->
                                    <!-- <div class="grid grid-cols-2 gap-2 mb-1">
                                    <div class="bg-gray-100 rounded-md p-2 items-center">
                                        <p class="text-sm text-gray-700 font-semibold">Invoice No: </p>
                                        <p class="text-sm text-gray-500">{{ record['invoice_id'] }}</p>
                                    </div>
                                    <div class="bg-gray-100 rounded-md p-2 items-center">
                                        <p class="text-sm text-gray-700 font-semibold">Invoice Date: </p>
                                        <p class="text-sm text-gray-500">{{ formatDateTime(record['current_date']) }}
                                        </p>
                                    </div>
                                </div> -->

                                    <!-- Invoice Actions (Can be your dynamic actions) -->
                                    <div class="flex justify-between bg-gray-100 rounded-md p-3 py-1">
                                        <div>
                                            <p class="text-sm text-gray-700 font-semibold">Grand Total:</p>
                                            <p class="text-sm text-gray-900 font-semibold">{{ currentCompanyList &&
                                                currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }} {{ record['grand_total'] }}
                                            </p>
                                        </div>
                                        <div class="flex items-center">
                                            <span v-if="record['status'] == 'Success'"
                                                class="px-3 py-1 rounded-full text-sm font-medium mr-2"
                                                :class="{ 'bg-green-100 text-green-800': record['due_amount'] === 0, 'bg-red-100 text-red-800': record['due_amount'] !== 0 }">
                                                {{ record['due_amount'] === 0 ? 'Paid' : 'Due' }}</span>
                                            <button @click="printRecord(record)"
                                                class="px-2 py-1 rounded-md text-sm font-medium bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                                View Details
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--no data found-->
                    <div class="text-sm mb-[100px]">
                        <p class="font-bold text-center py-2 text-green-700">
                            <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                            Finished !
                        </p>
                    </div>
                </div>
            </div>
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
            <Loader :showModal="open_loader"></Loader>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        isMobile: Boolean,
        data: Object,
        companyId: String,
        userId: String,
        selected_category: String,
        items_category: String,
        open_skeleton: Boolean,
        now: Date,
        confirm_del: Boolean,
        customer_data: Object,
        updateModalOpen: Boolean,
        currentCompanyList: Object,
        showContactInfo: Boolean
    },

    data() {
        return {
            display_option: false,
            deleteIndex: null,
            open_loader: false,
            editData: {},
            showAmcModal: false,
            empty_data: '/images/dashboard/empty.svg',
        }
    },
    computed: {
        dynamicFields() {
            const fields = [];
            let order = [];
            if (this.selected_category === 'services') {
                order = ['created_at', 'problem_title', 'category', 'expected_date', 'service_code', 'assign_to', 'service_type', 'invoice_id', 'status'];
            } else if (this.selected_category === 'leads') {
                order = ['lead_date', 'title', 'assign_to', 'assign_date', 'lead_type', 'follow_up', 'lead_status'];
            } else if (this.selected_category === 'amcs') {
                order = ['created_at', 'title', 'assign_to', 'amc_payment_type', 'amc_details', 'number_of_interval', 'number_of_service', 'amc_status'];
            } else if (this.selected_category === 'sales') {
                order = ['current_date', 'invoice_no', 'invoice_to', 'invoice_type', 'discount', 'due_amount', 'shipping', 'status'];
            } else if (this.selected_category === 'estimations') {
                order = ['current_date', 'estimate_num', 'estimate_type', 'grand_total', 'invoice_id'];
            }

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data && this.data.length > 0) {
                order.forEach((key) => {
                    // Check if the key exists in the data object
                    if (this.data && this.data.length > 0) { //---&& this.data[0].hasOwnProperty(key)
                        const label = formatLabel(key);
                        if (key !== 'created_by' && key !== 'updated_by') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                });
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
    },
    methods: {
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //--format date    
        formatDate(dateString) {
            if (!dateString || typeof dateString !== 'string') {
                return '';
            }
            // Parse the input date string as a Date object
            const dateObject = new Date(dateString);

            if (isNaN(dateObject.getTime())) {
                return ''; // Return empty string if parsing fails
            }

            // Define an array of month names
            const monthNames = ["January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"];

            // Extract day, month, and year components from the date object
            const day = dateObject.getDate();
            const monthIndex = dateObject.getMonth(); // Months are zero-based (0 = January)
            const year = dateObject.getFullYear();

            // Format the date as "Month day, year"
            const formattedDateData = `${monthNames[monthIndex]} ${day}, ${year}`;

            return formattedDateData;
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },

        //----services data--- 
        //--image
        hasDocument(record) {
            try {
                const documentData = JSON.parse(record.document);
                return Array.isArray(documentData) && documentData.length > 0;
            } catch (error) {
                console.error('Failed to parse document:', error);
                return false;
            }
        },
        getLastDocumentUrl(record) {
            if (this.hasDocument(record)) {
                const documents = JSON.parse(record.document);
                return documents[documents.length - 1].url;
            }
            return null;
        },
        //---formated display date---
        formattedDate(timestamp) {
            //---formatted display date---
            const date = new Date(timestamp);
            date.setHours(date.getHours() + 5); // Add 5 hours
            date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //--get service data---
        getServiceData(record) {
            axios.get(`/services/${record.service_id}`, { company_id: this.companyId })
                .then(response => {
                    // console.log(response.data, 'What happening the get service..!');
                    this.service_data = response.data.data;
                    // let service_track_data = JSON.parse(newValue.service_data);
                    this.$router.push({
                        name: 'generate-invoice',
                        params: { type: this.service_data.servicecategory.service_category, id: this.service_data.servicecategory.id, serviceId: record.service_id },
                        query: { type: 'edit', invoice_no: record.id }
                    });
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---record---
        startEdit(record) {
            if (record.invoice_type === 'Services') {
                this.getServiceData(record);

            } else if (record.invoice_type === 'Product' || record.invoice_type === 'product') {
                this.$router.push({
                    name: 'sales-invoice',
                    query: { type: 'edit', invoice_no: record.id }
                });
            } else if (record.invoice_type === 'Direct_Services') {
                this.$router.push({
                    name: 'sales-invoice',
                    query: { type: 'edit', invoice_no: record.id, invoice_type: 'direct' }
                });
            }
        },
        //---print record---
        printRecord(record) {
            this.$router.push({ name: 'print-preview', query: { type: 'sales_home', invoice_no: record.id } });
        },

        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.open_loader = true;
                axios.delete(`/amcs/${this.data[this.deleteIndex].id}`, { params: { company_id: this.companyId } })
                    .then(response => {
                        // console.log(response.data, 'What happening...!!!!');
                        this.open_loader = false;
                        this.deleteIndex = null;
                        window.location.reload();
                    })
                    .catch(error => {
                        this.open_loader = false;
                        console.error('Error', error);
                    })
            }

        },

        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.$emit('openconfirmbox', index);
        },
        cancelDelete() {
            this.deleteIndex = null;
        },
        getDateStatusClass(fieldValue) {
            try {
                const parsedValue = typeof fieldValue === 'string' ? JSON.parse(fieldValue) : fieldValue;
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    const followUpDate = new Date(parsedValue[0].date_and_time);
                    const currentDate = new Date();

                    if (followUpDate < currentDate) {
                        return 'text-red-500';
                    } else {
                        return 'text-green-500';
                    }
                }
            } catch (error) {
                console.error('Error parsing date:', error);
            }
            return ''; // Default empty string if date cannot be parsed
        },
        getFollowUpDate(fieldValue) {
            // console.log(fieldValue, 'EEEEEEEEEEEEEEEEEEEEEEE');
            if (fieldValue !== '' && typeof fieldValue === 'string') {
                const parsedValue = JSON.parse(fieldValue);
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    return this.generateDate(parsedValue[0].date_and_time);
                }
            } else if (typeof fieldValue === 'object') {
                if (Array.isArray(fieldValue) && fieldValue.length > 0 && fieldValue[0].date_and_time) {
                    return this.generateDate(fieldValue[0].date_and_time);
                }
            }
        },
        generateDate(data) {
            // console.log(data, 'What about the data......');
            const dateTimeString = data;
            const date = new Date(dateTimeString);
            const formattedDate = date.toLocaleDateString('en-GB', { year: 'numeric', month: '2-digit', day: '2-digit' });
            const formattedTime = date.toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' });
            return formattedDate + ' ' + formattedTime;
        },
        //---view Record--
        viewRecord(record) {
            if (record) {
                this.$router.push({
                    name: 'amcView',
                    query: {
                        recordId: record.id
                    }
                });
            }
        },
        //---closeAmcModal
        closeAmcModal(newData) {
            if (newData && this.typeOfAmc !== 'edit') {
                // console.log(this.data, 'EEEEEEEEEEEEEEEEEEEEEEEEEEEEEEEE')
                this.data.unshift(newData);
                // console.log(newData, 'What happening....!');
            }
            else if (newData && this.typeOfAmc === 'edit') {
                let findIndexData = this.data.findIndex((opt) => opt.id === this.editData.id);
                this.data.splice(findIndexData, 1, newData);
                // this.data;
            }

            this.showAmcModal = false;
        },
    },
    watch: {
        confirm_del: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.deleteRecord();
                } else {
                    this.cancelDelete();
                }
            }
        }
    }
}
</script>
<style>
.image-container01 {
    width: 150px;
    height: 70px;
    /* Set your desired fixed height */
    object-fit: cover;
    /* Maintain aspect ratio and crop as needed */
    object-position: center;
    border: 1px solid rgb(218, 218, 218);
    /* box-shadow: 1px 1px 2px 2px rgb(82, 81, 81); */
}
</style>