<template>
    <div>
        <div class="flex h-screen overflow-auto relative">
            <div class="flex flex-col flex-grow overflow-y-auto w-full"
                :class="{ 'mb-[80px] pt-[60px]': isMobile, 'pt-[50px]': !isMobile }">
                <ecommerse :isMobile="isMobile"></ecommerse>
            </div>
        </div>
    </div>
</template>

<script>
import { useMeta } from '@/composables/useMeta';
import ecommerse from '../supporting/ecommerce/ecommerse.vue';

export default {
    name: 'ecommerce',
    components: {
        ecommerse,
    },
    data() {
        return {
            isMobile: false,
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Ecommerce Builder';
        const pageDescription = 'Create a custom e-commerce website with ease using our intuitive builder. Design, manage, and grow your online store';
        useMeta(pageTitle, pageDescription);
        return { pageTitle };
    },
    methods: {
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }

    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);

    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },

    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>