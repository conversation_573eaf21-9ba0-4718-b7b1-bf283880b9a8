// store/modules/items.js
import axios from "axios";

const state = {
  supplier_list: [],
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
};

const mutations = {
  SET_ITEMS(state, itemData) {
    state.supplier_list = itemData;
  },
  SET_LAST_FETCH_TIME(state, time) {
    state.lastFetchTime = time; // Save the timestamp when the API was last accessed
  },
  SET_IS_FETCHING(state, status) {
    state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
  },
  RESET_STATE(state) {
    state.supplier_list = [];
    state.lastFetchTime = null;
    state.isFetching = false;
  },
};

const actions = {
  updateCustomerName({ commit }, itemData) {
    // Simulate an asynchronous operation (e.g., API call) to update customer name
    setTimeout(() => {
      // Commit mutation to update customer name
      commit("SET_ITEMS", itemData);
    }, 1000); // Simulated delay of 1 second
  },
  async fetchISupplierList({ state, commit, rootState }) {
    const now = new Date().toISOString();
    const thirtySecondsInMilliseconds = 5 * 1000;
    const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['supplier_update']; 
    // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
    if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime) {
      return; // Skip request if less than 30 seconds have passed since the last request
    }
    try {
      const { company_id } = JSON.parse(localStorage.getItem("track_new")) || {};
      if (company_id !== "") {
        commit('SET_IS_FETCHING', true);
        axios
          .get("/suppliers", {
            params: { company_id: company_id, page: 1, per_page: "all" },
          })
          .then((response) => {
            // Handle response
            // console.log(response.data, 'supplier list..!');
            let items_list = response.data.data;
            commit("SET_ITEMS", items_list);
            commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
            return items_list;
          })
          .catch((error) => {
            // Handle error
            console.error("Error:", error);
            commit('SET_IS_FETCHING', false);
            return error;
          });
      }
    } catch (error) {
      console.error("Error fetching item list:", error);
      commit('SET_IS_FETCHING', false);
    }
  },
};

const getters = {
  currentSupplier(state) {
    return state.supplier_list;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
