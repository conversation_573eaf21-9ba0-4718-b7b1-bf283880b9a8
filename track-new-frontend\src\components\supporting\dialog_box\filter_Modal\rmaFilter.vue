<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-display relative"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex justify-between items-center relative w-full bg-teal-600 px-4 py-3 set-header-background">
                <p for="leadFilter" class="text-white font-bold text-center flex justify-end ml-3 text-xl">
                    Filter
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block mt-1  bg pl-4 pr-4 pb-4">
                <div v-if="selectedByValue === 'Custom'" class="text-green-600">
                    <p>Note: At least fill in any one field..!</p>
                </div>
                <!---from to date-->
                <div class="block text-sm mt-7 relative">
                    <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'" class="flex justify-end">
                        <button @click="resetTheValues(['from', 'to'])"
                            class="absolute text-xs -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>
                    <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'"
                        class="grid grid-cols-2 gap-4">
                        <!-- From Date -->
                        <div class="relative">
                            <label for="fromDate"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.from || isInputFocused.from, 'text-blue-700': isInputFocused.from }">
                                From Date
                            </label>
                            <!----@input="validateDates"-->
                            <input id="fromDate" v-model="formValues.from" type="date" v-datepicker placeholder=" "
                                :max="formValues.to" @change="updateMinToDate"
                                class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                        </div>

                        <!-- To Date -->
                        <div class="relative">
                            <label for="toDate"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.to || isInputFocused.to, 'text-blue-700': isInputFocused.to }">
                                To Date
                            </label>
                            <!--@input="validateDates" -->
                            <input id="toDate" v-model="formValues.to" type="date" v-datepicker placeholder=" "
                                :min="formValues.from" :max="maxDate"
                                class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                        </div>
                    </div>
                </div>
                <!---Date-->
                <!-- <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'" class="items-center mt-5">
                    <div v-if="formValues.date" class="flex justify-end ">
                        <button @click="resetTheValue('date')"
                            class="absolute -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="date"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 bg-white px-1 text-gray-700': formValues.date || isInputFocused.date, 'text-blue-700': isInputFocused.date }">
                            Date</label>
                        <input id="date" v-model="formValues.date" type="date"  v-datepicker placeholder=" "
                            class=" p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                    </div>
                </div> -->
                <!--Customer-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Customer'" class="mt-5">
                    <div v-if="formValues.customer" class="flex justify-end ">
                        <button @click="resetTheValue('customer')"
                            class="absolute  -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" size="sm" style="color: #ec2727;" /></button>
                    </div>
                    <div class="relative flex w-full mr-2">
                        <label for="customer"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 bg-white px-1 text-gray-700': formValues.customer || isInputFocused.customer, 'text-blue-700': isInputFocused.customer }">Customer<span
                                v-if="formValues.customer || isInputFocused.customer"
                                class="text-red-600">*</span></label>
                        <input id="customer" v-model="formValues.customer" @input="handleDropdownInput(fields)"
                            @focus="isDropdownOpen = true, isInputFocused.customer = true, handleDropdownInput(fields)"
                            @blur="closeDropdown('customer')"
                            @keydown.enter="handleEnterKey('customer', filteredCustomerOptions)"
                            @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="customer"
                            class="w-full border py-2 pl-2 pr-10 leading-5 text-gray-900 focus:ring-0 rounded rounded-tr-none rounded-br-none outline-none" />

                        <!-- Right-aligned icon based on isDropdownOpen -->
                        <span class="absolute py-2 ml-[80%] sm:ml-[78%] lg:ml-[85%] cursor-pointer"
                            @click="isDropdownOpen = !isDropdownOpen">
                            <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </span>
                    </div>
                </div>
                <!-- Display filtered options as the user types -->
                <div v-if="isDropdownOpen">
                    <div class="absolute mt-1 max-h-60 w-3/4 overflow-auto ml-2 rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none text-sm"
                        style="z-index: 100;" @mousedown.prevent="preventBlur('customer')">
                        <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                            @click="selectDropdownOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                            class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                            {{ option.first_name + (option.last_name ? ' ' + option.last_name : '') }} - {{
                                option.contact_number
                            }}
                        </p>
                    </div>
                </div>
                <!--Assign To-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Employee'" class="items-center mt-5">
                    <div v-if="formValues.assign_to" class="flex justify-end ">
                        <button @click="resetTheValue('assign_to')"
                            class="absolute  -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" size="sm" style="color: #ec2727;" /></button>
                    </div>
                    <div class="relative w-full mr-2">
                        <label for="assign_to"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 bg-white px-1 text-gray-700': formValues.assign_to || isInputFocused.assign_to, 'text-blue-700': isInputFocused.assign_to }">Assign
                            to</label>
                        <div class="border py-2 px-2 flex flex-wrap"
                            :class="{ 'border-blue-300': isInputFocused.assign_to === true }">
                            <div v-for="(selectedOption, index) in formValues.assign_to" :key="index"
                                class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                {{ selectedOption.name }}
                                <span @click="removeOption(selectedOption)"
                                    class="text-red-500 font-semibold cursor-pointer">x</span>
                            </div>
                            <input type="text" v-model="search" @input="filterOptions" @focus="filterOptions"
                                @click="filterOptions" ref="search" @blur="hideOptions"
                                class="h-[35px] mt-1 outline-none border rounded-lg px-2"
                                @keydown.enter="handleEnterKey('multiple', filteredOptions())"
                                @keydown.down.prevent="handleDownArrow(filteredOptions())"
                                @keydown.up.prevent="handleUpArrow(filteredOptions())" />
                        </div>
                        <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                            v-if="showOptions" :class="{ 'h-auto': isInputFocused.assign_to === true }">
                            <div v-for="(option, index) in filteredOptions()" :key="index"
                                class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                :class="{ 'bg-green-300': index === selectedIndex }"
                                @click="selectOptionMultiple(option)">
                                {{ option.name }}
                            </div>
                        </div>
                    </div>
                </div>
                <!--type-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by category/Type'"
                    class="items-center mt-5 border">
                    <div v-if="formValues.type >= 0" class="flex justify-end ">
                        <button @click="resetTheValue('type')"
                            class="absolute  -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="type"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top duration-300"
                            :class="{ '-top-3 sm:-top-2 bg-white px-1 text-gray-700': isInputFocused.type, 'text-blue-700': isInputFocused.type }">
                            Type
                        </label>
                        <div class="flex flex-wrap py-4 px-2 items-center">
                            <button v-for="(data, index) in typeList" :key="index"
                                class="flex items-center bg-gray-200 py-2 px-3 rounded mr-3 mt-3" :class="{
                                    'bg-gray-700': formValues['type'] === index
                                }" @click="selectStatusOption(data, 'type', index)">
                                <span class=" text-gray-500" :class="{
                                    'text-white': formValues['type'] === index
                                }">{{ data }}</span>
                            </button>
                        </div>
                    </div>
                </div>
                <!--status-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Status'"
                    class="items-center mt-5 border">
                    <div v-if="formValues.status >= 0" class="flex justify-end ">
                        <button @click="resetTheValue('status')"
                            class="absolute  -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="type"
                            class="text-xs font-bold absolute left-2 top-3 text-gray-300 transition-top duration-300"
                            :class="{ '-top-3 sm:-top-2 bg-white px-1 text-gray-700': isInputFocused.status, 'text-blue-700': isInputFocused.status }">
                            Status
                        </label>
                        <div class="flex flex-wrap py-4 px-2 items-center">
                            <button v-for="(data, index) in statusList" :key="index"
                                class="flex items-center bg-gray-200 py-2 px-3 rounded mr-3 mt-3"
                                :class="{ 'bg-gray-700': formValues['status'] === index }"
                                @click="selectStatusOption(data, 'status', index)">
                                <span class=" text-gray-500"
                                    :class="{ 'text-white': formValues['status'] === index }">{{ data.label }}</span>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Buttons -->
                <div class="flex justify-center items-center m-2 mt-5 mx-auto">
                    <button @click="closeModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  px-4 mr-8 py-2">Cancel</button>
                    <button v-if="showResetButton && selectedByValue === 'Custom'" @click="resetAll"
                        class="bg-blue-700 hover:bg-blue-600 rounded-[30px] text-white px-4 mr-8 py-2">Reset
                        All</button>
                    <button @click="submitFilter"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white px-4 mr-8 py-2">Submit</button>
                </div>
            </div>
        </div>
        <dialogAlert :showModal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>


<script>
import dialogAlert from '../dialogAlert.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
    props: {
        showModal: Boolean,
        typeList: Object,
        statusList: Object,
        selectedByValue: String
    },
    components: {
        dialogAlert
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            customer_list: [],
            employeeList: [],
            pagination: {},
            isInputFocused: { date: true, type: true, status: true },
            isDropdownOpen: false,
            selectedIndex: 0,
            showOptions: false,
            search: '',
            companyId: '',
            userId: '',
            filteredCustomerOptions: [],
            open_message: false,
            message: '',
            open_loader: false,
            maxDate: new Date().toISOString().split('T')[0], // current date
            minDate: '',
        };
    },
    methods: {
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('employess', ['fetchEmployeeList']),
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter');
            }, 300);
        },
        //--submit---
        submitFilter() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter', this.formValues);
                this.formValues = {};
            }, 300);
        },

        //----Customer----
        handleDropdownInput() {
            // console.log(this.customer_list, 'This is customer list..@')
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                            (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                    );
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = this.customer_list;
            }

            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    // this.openModal();
                    // this.openModal(product_name);
                }
            }
            else if (type === 'multiple') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectOptionMultiple(optionArray[this.selectedIndex]);
                    this.selectedIndex = 0;
                }
                // else {
                //     this.openModalEmployee();
                // }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else if (this.selectedIndex > 0) {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----customer dropdown--
        closeDropdown(type) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                this.isInputFocused.customer = false;

            }
        },
        //---customers dropdown
        selectDropdownOption(option) {
            this.formValues.customer_id = option.id;
            this.formValues.customer = option.first_name + (option.last_name ? ' ' + option.last_name : '') + ' - ' + option.contact_number;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
        },
        preventBlur(type) {
            if (type && type === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            }
        },
        //---multiple dropdown---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            // this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                // console.log('hello');
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.mobile_number + ''.includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination.employee.current_page !== this.pagination.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.mobile_number && option.mobile_number + ''.includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination.employee.current_page !== this.pagination.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    }
                } else if (enteredValue.length > 1) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination.employee.current_page !== this.pagination.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            if (this.pagination && this.pagination.employee.current_page !== this.pagination.employee.last_page) {
                                this.extendsData();
                            } else {
                                this.showAddNew = true;
                                return [];
                            }
                        }
                    }
                } else {
                    return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList;
                }
            }
            // console.log(this.formValues.assign_to, 'RRRRR');
            // Return an empty array if no options match the filter
            // console.log(this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList);
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList;
        },

        selectOptionMultiple(option) {
            if (!this.formValues.assign_to) {
                this.formValues.assign_to = []; // Initialize assign_to as an array if it's not already
            }
            if (this.formValues.assign_to.length === 0) {
                this.formValues.assign_to.push(option); // Add the selected option to assign_to
                this.search = ''; // Clear the search input
                // this.showOptions = false; // Hide the options dropdown
                this.$nextTick(() => {
                    // Focus on the search input after selecting an option
                    if (this.$refs['search']) {
                        this.$refs['search'].focus();
                        // this.$refs['search'].click();
                    }
                });
                this.selectedIndex = 0;
            } else {
                this.message = 'Employee filters can be selected one at a time';
                this.open_message = true;
            }
        },
        removeOption(option) {
            this.formValues['assign_to'] = this.formValues['assign_to'].filter(selected => selected !== option);
        },

        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
                this.isInputFocused.assign_to = false;
            }, 300); // Add delay to allow click events on options
        },
        openModalEmployee() {
            this.showModal_employee = true;
            this.EmployeeName = this.search;
            this.showOptions = false;
            this.showAddNew = null;
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.customer;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---status and type--
        selectStatusOption(option, type, index) {
            if (option && type) {
                this.formValues[type] = index;
            }
        },
        //---reset All---
        resetAll() {
            this.formValues = {};
        },
        //---reset---
        resetTheValue(key) {
            delete this.formValues[key];
        },
        getCustomerList(page, per_page) {
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    console.log(response.data.data);
                    this.customer_list = response.data.data;
                    this.pagination.customer = response.data.pagination;
                    this.open_loader = false;
                })
                .catch(error => {
                    console.error('Error get customer', error);
                    this.open_loader = false;
                })
        },
        getEmployeeList(page, per_page) {
            //--employee list---
            axios.get('/employees', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    console.log(response.data.data);
                    this.employeeList = response.data.data;
                    this.pagination.employee = response.data.pagination;
                    if (this.formValues && this.formValues.assign_to && Array.isArray(this.formValues.assign_to)) {
                        let find_duplicate = this.formValues.assign_to.find(opt => opt.id == this.userId);
                        if (this.userId && !find_duplicate) {
                            this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
                        }
                    } else {
                        this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
                    }
                    if (this.employeeList.length > 2) {
                        // Sorting the array alphabetically based on the 'name' key
                        this.employeeList.sort((a, b) => {
                            // Convert both names to lowercase to ensure case-insensitive sorting
                            const nameA = a.name.toLowerCase();
                            const nameB = b.name.toLowerCase();
                            // Compare the two names
                            if (nameA < nameB) {
                                return -1;
                            }
                            if (nameA > nameB) {
                                return 1;
                            }
                            return 0;
                        });
                    }
                    this.open_loader = false;
                })
                .catch(error => {
                    console.error('Error get employee', error);
                    this.open_loader = false;
                })
        },
        closeMessage() {
            this.message = '';
            this.open_message = false;
        },
        validateDates() {
            if (this.formValues.from && this.formValues.to) {
                const fromDate = new Date(this.formValues.from);
                const toDate = new Date(this.formValues.to);

                if (toDate < fromDate) {
                    // Reset 'to' date and show error message (you can handle this according to your UI)
                    this.formValues.to = null;
                    this.message = 'To Date cannot be earlier than From Date';
                    this.open_message = true;
                }
            }
        },
        //--update minimum date
        updateMinToDate() {
            this.minDate = this.formValues.from;
        },
        resetTheValues(fields) {
            // Reset specified form fields
            fields.forEach(field => {
                this.formValues[field] = null;
            });
        },
    },
    computed: {
        ...mapGetters('employess', ['currentEmployee']),
        ...mapGetters('customer', ['currentCustomer']),

        showResetButton() {
            // Check if formValues is not empty
            return Object.keys(this.formValues).length > 0;
        }
    },
    watch: {
        showModal(newValue) {
            if (this.currentCustomer && this.currentCustomer.length === 0) {
                this.fetchCustomerList();
            }
            // if (this.currentEmployee && this.currentEmployee.length === 0) {
            //     this.fetchEmployeeList();
            // }
            setTimeout(() => {
                //--get customer--
                if (this.customer_list && this.customer_list.length === 0) {
                    this.open_loader = true;
                    // this.getCustomerList(1, 'all');
                    if (this.currentCustomer && this.currentCustomer.length > 0) {
                        this.customer_list = this.currentCustomer;
                        this.open_loader = false;
                    }
                }
                //--get employee--
                // if (this.employeeList && this.employeeList.length === 0) {
                //     this.open_loader = true;
                //     // this.getEmployeeList(1, 'all');
                //     if (this.currentEmployee && this.currentEmployee.length > 0) {
                //         this.employeeList = this.currentEmployee;
                //         this.open_loader = false;
                //     }
                // }
                this.isOpen = newValue;
            }, 100);

            if (this.selectedByValue == 'by Date') {
                // Get current date in YYYY-MM-DD format
                const currentDate = new Date().toISOString().split('T')[0];
                // Set default values for from and to in formValues
                this.formValues.from = currentDate;
                this.formValues.to = currentDate;
            }
        },
        currentCustomer: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.open_loader = false;
                    this.customer_list = newValue;
                }
            }
        },
        // currentEmployee: {
        //     deep: true,
        //     handler(newValue) {
        //         if (newValue && newValue.length > 0) {
        //             this.open_loader = false;
        //             this.employeeList = newValue;
        //         }
        //     }
        // }
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>