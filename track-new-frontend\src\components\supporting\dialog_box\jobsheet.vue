<template>
    <div v-if="showModal"
        class="fixed top-0 right-0 bottom-0 left-0 bg-black bg-opacity-50 flex justify-center items-center z-50 h-screen">
        <!-- Modal -->
        <div class="bg-white sm:w-3/4 w-full sm:mx-0 shadow-lg overflow-hidden h-screen" ref="modalContainer">
            <!-- Modal Header -->
            <div class="flex justify-between p-3 text-white set-header-background">
                <p class="px-2 text-xl">Job Sheet</p>
                <button @click="closeModal" class="text-white hover:text-red-700 focus:outline-none">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <!---message-->
            <!-- <div v-if="messageData" class="m-3">
                <p class="font-bold">{{ messageData }} <span style='font-size:30px;'>&#127881;</span></p>
            </div> -->
            <div class="flex justify-center item-center p-2">
                <div ref="shareMenu">
                    <button @click="toggleShareMenu"
                        class="border-2 rounded  rounded-xl flex justify-center items-center py-2 px-2 hover:border-green-700 hover:text-green-700">
                        <span class="mr-2"><font-awesome-icon icon="fa-solid fa-share-nodes" size="lg" /></span>
                        <span class="hidden sm:flex">Share</span>
                    </button>

                    <!-- Share menu -->
                    <div v-show="showShareMenu" class="absolute mt-1 w-48 bg-white overflow-visible shadow-xl">
                        <button @click="shareViaEmail"
                            class="flex items-center w-full px-4 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none">
                            <span class="mr-2"><font-awesome-icon icon="fa-regular fa-envelope" size="lg"
                                    style="color: #74C0FC;" /></span>
                            Email
                        </button>
                        <button v-if="companywhatsapp" @click="openWhatsApp"
                            class="flex items-center w-full px-4 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none">
                            <span class="mr-2"><font-awesome-icon icon="fa-brands fa-whatsapp" size="lg"
                                    style="color: green" /></span>
                            WhatsApp
                        </button>
                        <button v-if="!companywhatsapp" class="text-red-700 text-xs" @click="navigateToWhatsApp">
                            <font-awesome-icon icon="fa-brands fa-whatsapp" />
                            Connect WhatsApp
                        </button>
                    </div>
                </div>

                <!-- Download Button with Options -->
                <div class="relative ml-4" ref="downloadMenu">
                    <button @click="toggleDownloadMenu"
                        class="border-2 rounded-xl flex justify-center items-center py-2 px-2 hover:border-green-700 hover:text-green-700">
                        <span class="mr-2"><font-awesome-icon icon="fa-solid fa-download" size="lg" /></span>
                        <span class="hidden sm:flex">Download</span>
                    </button>

                    <!-- Download options -->
                    <div v-show="showDownloadMenu" class="absolute mt-2 w-48 bg-white overflow-visible shadow-xl">
                        <button @click="downloadJobsheet('official')"
                            class="flex items-center w-full px-4 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none">
                            Official Copy
                        </button>
                        <button @click="downloadJobsheet('customer')"
                            class="flex items-center w-full px-4 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none">
                            Customer Copy
                        </button>
                        <button @click="downloadJobsheet('both')"
                            class="flex items-center w-full px-4 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none">
                            Both
                        </button>
                    </div>
                </div>
                <!--print jobsheet v-if="!isMobile"-->
                <button @click="printJobsheet"
                    class="ml-4 border-2 rounded rounded-xl flex justify-center items-center py-2 px-2 hover:border-green-700 hover:text-green-700">
                    <span class="mr-2"><font-awesome-icon icon="fa-solid fa-print" size="lg" /></span>
                    <span class="hidden sm:flex">Print</span>
                </button>
                <button @click="qrcodeData"
                    class="ml-4 border-2 rounded rounded-xl flex justify-center items-center py-2 px-2 hover:border-green-700 hover:text-green-700">
                    <span class="mr-2"><font-awesome-icon icon="fa-solid fa-qrcode" /></span>
                    <span class="hidden sm:flex">QR/Bar code</span>
                </button>
            </div>
            <div class="p-4 h-screen flex">
                <iframe ref="jobSheetIframe" class="w-full h-full pb-[150px]" :src="isUrl" frameborder="2"
                    allowfullscreen></iframe>
            </div>
        </div>
        <servicecodemanage v-if="qrcode" :isModalOpen="qrcode" @close="closeQrcode" :item_data="item_data"
            :type="'print'">
        </servicecodemanage>
        <!--whatsapp message-->
        <whatsappMessage :showModal="openWhatsAppMessage" :type="'jobsheet'" :data="item_data"
            @close="closeWhatsappMessage">
        </whatsappMessage>
    </div>
</template>
<script>
import servicecodemanage from '../services/Search/servicecodemanage.vue';
import whatsappMessage from './whatsappMessage.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    props: {
        showModal: Boolean,
        item_data: Object,
        companyId: String,
        userId: String,
        shortenedUrl: '',
        messageData: String
    },
    components: {
        servicecodemanage,
        whatsappMessage
    },
    data() {
        return {
            isUrl: null,
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            showShareMenu: false,
            isMobile: false,
            showDownloadMenu: false,
            qrcode: false,
            //---whats app message---
            openWhatsAppMessage: false,
        };
    },
    created() {
        if (this.item_data && this.item_data.service_code) {
            this.isUrl = axios.defaults.baseURL + `/view-job-sheet/${this.item_data.service_code}`;
        }
    },
    mounted() {
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
    },
    computed: {
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
        document.removeEventListener('click', this.handleClickOutside);
    },
    methods: {
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        toggleDownloadMenu() {
            this.showDownloadMenu = !this.showDownloadMenu;
            if (this.showDownloadMenu) {
                // Event listener to detect clicks outside the dropdown
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Close dropdown if clicked outside of it
            if (this.showDownloadMenu && !this.$refs.downloadMenu.contains(event.target)) {
                this.showDownloadMenu = false;
            }
        },
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-Modal');
                this.message = '';
                this.showShareMenu = false;
            }, 300);
        },
        viewJobsheet() {
            if (this.item_data) {
                console.log(this.item_data);
                let link_data = axios.defaults.baseURL + `/view-job-sheet/${this.item_data.service_code}`;

                // Open the link in a new tab
                window.open(link_data, '_blank');
            }
        },
        downloadJobsheet(copyType) {
            if (this.item_data) {
                let link_data = axios.defaults.baseURL + `/job-sheet/${this.item_data.service_code}`;
                // Add query parameters (like copyType) to the URL
                link_data += `?copy_type=${copyType}`;
                // Create an anchor element
                let anchor = document.createElement('a');
                anchor.href = link_data;
                anchor.setAttribute('download', `job_sheet_${copyType}`); // Set the download attribute to trigger a download
                anchor.style.display = 'none';

                // Append the anchor to the document body and click it programmatically
                document.body.appendChild(anchor);
                anchor.click();

                // Cleanup: remove the anchor from the document body
                document.body.removeChild(anchor);
            }
            this.showDownloadMenu = false; // Close dropdown after download
        },
        async printJobsheet() {
            if (this.item_data) {
                let link_data = axios.defaults.baseURL + `/view-job-sheet/${this.item_data.service_code}`;

                try {
                    const response = await fetch(link_data);
                    const content = await response.text();

                    if (content) {
                        // Create a hidden iframe
                        const iframe = document.createElement('iframe');
                        iframe.style.position = 'fixed';
                        iframe.style.width = '0';
                        iframe.style.height = '0';
                        iframe.style.border = 'none';
                        document.body.appendChild(iframe);

                        // Ensure the iframe is fully loaded before writing content
                        iframe.onload = function () {
                            const iframeDoc = iframe.contentDocument || iframe.contentWindow.document;
                            iframeDoc.open();
                            iframeDoc.write(content);
                            iframeDoc.close();

                            // Trigger the print dialog
                            iframe.contentWindow.focus();
                            iframe.contentWindow.print();

                            // Remove the iframe after printing
                            setTimeout(() => {
                                document.body.removeChild(iframe);
                            }, 1000);
                        };

                        // Set the iframe's src to trigger the onload event
                        iframe.src = 'about:blank';
                    } else {
                        console.error('Job sheet content is empty.');
                    }
                } catch (error) {
                    console.error('Failed to fetch the job sheet content:', error);
                }
            }
        },
        toggleShareMenu() {
            this.showShareMenu = !this.showShareMenu;
            if (this.showShareMenu) {
                // Add click event listener to close share menu on outside click
                document.addEventListener('click', this.closeShareMenuOutsideClick);
            }
        },
        shareViaEmail() {
            if (this.item_data) {
                this.showShareMenu = !this.showShareMenu;
                let link_data = axios.defaults.baseURL + `/view-job-sheet/${this.item_data.service_code}?copy_type=customer`;
                window.open(`mailto:?subject=Check out this job sheet&body=${encodeURIComponent(link_data)}`);
            }
        },
        shareViaWhatsApp() {
            if (this.item_data) {
                this.showShareMenu = !this.showShareMenu;
                let link_data = axios.defaults.baseURL + `/job-sheet/${this.item_data.service_code}?copy_type=customer`;
                //let srt_url = this.shortenUrl(link_data);
                window.open(`https://web.whatsapp.com/send?text=${encodeURIComponent(link_data)}`);
            }
        },

        closeShareMenuOutsideClick(event) {
            // Check if the click is outside the share menu and the modal container
            // console.log(this.$refs.shareMenu, 'EEEEEEE');
            if (this.showShareMenu && !this.$refs.shareMenu.contains(event.target)) {
                this.showShareMenu = false;
                // Remove click event listener
                // document.removeEventListener('click', this.closeShareMenuOutsideClick);
            }
        },

        //  shortenUrl(longUrl) {
        //     try {
        //         const response = await fetch('https://t.track-new.com/api/url/add', {
        //         method: 'POST',
        //         headers: {
        //             'Authorization': 'Bearer rOuXNgCMFSZpvepMQjTmSVVdJcTdoWUA',
        //             'Content-Type': 'application/json'
        //         },
        //         body: JSON.stringify({ url: longUrl })
        //         });

        //         if (!response.ok) {
        //         throw new Error('Failed to shorten URL');
        //         }

        //         const responseData = await response.json();

        //         if (responseData.shorturl) {
        //         console.log(responseData);
        //         return responseData.shorturl;
        //         } else {
        //         throw new Error('Failed to get shortened URL');
        //         }
        //     } catch (error) {
        //         console.error('Error occurred while shortening URL:', error);
        //         throw error;
        //     }
        // }

        qrcodeData() {
            this.qrcode = true;
        },
        closeQrcode() {
            this.qrcode = false;
        },
        //---connect whatsApp---
        navigateToWhatsApp() {
            this.$router.push({ name: 'whatsappsetting' });
        },
        //--send wahtsapp reminder
        openWhatsApp(record) {
            this.openWhatsAppMessage = true;
        },
        closeWhatsappMessage() {
            this.openWhatsAppMessage = false;
            this.selected_customer = {};
        },
    },
    watch: {
        item_data: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.service_code) {
                    this.isUrl = axios.defaults.baseURL + `/view-job-sheet/${newValue.service_code}`;
                }
            }
        }
    }
}
</script>

<style scoped>
.container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* Three columns with equal width */
    grid-gap: 10px;
    /* Gap between grid items */
}

.container>div {
    padding: 10px;
    /* Add padding to grid items */
}

/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>