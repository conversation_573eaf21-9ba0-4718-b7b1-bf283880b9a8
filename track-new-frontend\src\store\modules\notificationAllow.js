// store/modules/notification_allow.js
import axios from "axios";

const state = {
  notification_allow: true,
  };

  const mutations = {
    SET_LOCALSTORAGE(state, { local_data }) {    
          state.notification_allow = local_data;
    },
      RESET_STATE(state) {
          state.notification_allow = true;
      }
  };

  const actions = {
    updateLocalNotifyName({ commit }, notification_allowData) {
      // Simulate an asynchronous operation (e.g., API call) to update notification_allow name
      setTimeout(() => {
        // Commit mutation to update notification_allow name
        commit('SET_LOCALSTORAGE', notification_allowData);
      }, 1000); // Simulated delay of 1 second
    },
    updateLocalallow({ commit }, notification_allowData) {     
        // Commit mutation to update notification_allow name
      commit('SET_LOCALSTORAGE', { local_data: notification_allowData });
    },
    async fetchLocalNotifyList({ commit }) {
      try {
          const local_data = JSON.parse(localStorage.getItem('notification'));
        if (local_data !== undefined) {          
              commit('SET_LOCALSTORAGE', {local_data});
           
        }  
      } catch (error) {
        console.error('Error fetching notifications:', error);
      }
    }
    
  };

  const getters = {
    currentLocalNotifyList(state) {
      return state.notification_allow;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
