<template>
    <div class="px-2 sm:px-4 text-sm" :class="{ 'mt-[60px] mb-[60px]': isMobile }">
        <!--header-->
        <!--subscriptions  list-->
        <div class="mt-2 lg:mt-5">
            <licenseInfo v-if="orders && orders.length > 0" :isMobile="isMobile" :isAndroid="isAndroid"
                :table_data="orders" :currentLocalDataList="currentLocalDataList"
                :currentCompanyList="currentCompanyList"></licenseInfo>
            <div class="bg-white rounded-lg shadow mt-6 p-2">
                <h2 class="text-sm sm:text-lg font-semibold mb-4">Payment History</h2>
                <dataTablehistory v-if="orders && orders.length > 0" :isMobile="isMobile" :isAndroid="isAndroid"
                    :table_data="orders" :columns_data="columns" :selected_option="'orders'"
                    :currentCompanyList="currentCompanyList">
                </dataTablehistory>

                <!--in case empty-->
                <div v-else>
                    <div class="flex justify-center items-center">
                        <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                    </div>
                    <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
                </div>
            </div>
        </div>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'categories'"></bottombar> -->
        <Loader :showModal="open_loader"></Loader>

    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
// import bottombar from '@/components/supporting/dashboard/bottombar.vue';
import dataTablehistory from './dataTablehistory.vue';
import licenseInfo from './licenseInfo.vue';
export default {
    props: {
        isMobile: Boolean,
        isAndroid: Boolean,
        refresh: Boolean
    },
    components: {
        // bottombar,
        dataTablehistory,
        licenseInfo
    },
    data() {
        return {
            orders: [],
            empty_data: '/images/dashboard/empty.svg',
            open_loader: false,
            columns: [
                // {
                //     label: "ID",
                //     field: "id",
                //     width: "3%",
                //     sortable: true,
                //     isKey: true,
                // },
                {
                    label: "Plan",
                    field: "plan_name",
                    width: "25%",
                    sortable: true,
                },
                {
                    label: "Duration",
                    field: "plan_duration",
                    width: "25%",
                    sortable: true,
                },
                // {
                //     label: "Invoice No",
                //     field: "invoice_no",
                //     width: "15%",
                //     sortable: true,
                // },
                {
                    label: "Invoice Date",
                    field: "invoice_date",
                    width: "25%",
                    sortable: true,
                },
                {
                    label: "Amount",
                    field: "amount",
                    width: "25%",
                    sortable: true,
                },
                {
                    label: "End Date",
                    field: "will_expire",
                    width: "25%",
                    sortable: true,
                },
                // {
                //     label: "Gateway",
                //     field: "gateway_name",
                //     width: "15%",
                //     sortable: true,
                // },
                // {
                //     label: "Remaning Time",
                //     field: "remaning_time",
                //     width: "15%",
                //     sortable: true,
                // }
            ],

        };
    },

    created() {
        if (this.currentSubscriptionHistoryList && this.currentSubscriptionHistoryList.length > 0) {
            this.open_loader = false;
            this.orders = [...this.currentSubscriptionHistoryList];
            this.updatetheValue();
            this.fetchSubscriptionHistoryList();
        } else {
            this.open_loader = true;
            this.fetchSubscriptionHistoryList();
        }
        this.fetchLocalDataList();
        this.fetchCompanyList();
    },
    computed: {
        ...mapGetters('subscriptionHistory', ['currentSubscriptionHistoryList']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('companies', ['currentCompanyList']),
    },
    methods: {
        ...mapActions('subscriptionHistory', ['fetchSubscriptionHistoryList']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        capitalizeFirstLetter(str) {
            return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
        },
        formatLabel(key) {
            const words = key.split(/(?=[A-Z])|_/);
            const formattedLabel = words.map(word => this.capitalizeFirstLetter(word)).join(' ');
            return formattedLabel;
        },
        calculateTime(days) {
            if (days < 30) {
                return `${days} day${days > 1 ? 's' : ''}`;
            } else if (days < 365) {
                const months = Math.floor(days / 30);
                return `${months} month${months > 1 ? 's' : ''}`;
            } else {
                const years = Math.floor(days / 365);
                const remainingDays = days % 365;
                const months = Math.floor(remainingDays / 30);
                return `${years} year${years > 1 ? 's' : ''}` +
                    (months ? ` and ${months} month${months > 1 ? 's' : ''}` : '');
            }
        },
        formatDateString(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-GB');
        },
        calculateRemainingTime(dateString) {
            const endDate = new Date(dateString);
            const now = new Date();

            // Calculate the difference in milliseconds
            const diffInMs = endDate - now;

            // Calculate the remaining time in various units
            const diffInDays = Math.ceil(diffInMs / (1000 * 60 * 60 * 24));
            const diffInWeeks = Math.ceil(diffInDays / 7);
            const diffInMonths = Math.ceil(diffInDays / 30); // Approximate
            const diffInYears = Math.ceil(diffInDays / 365); // Approximate

            // Determine the highest remaining unit
            const highestValue = Math.max(diffInYears, diffInMonths, diffInWeeks, diffInDays);

            // Return the highest value and its corresponding unit
            if (highestValue === diffInYears) {
                return `${diffInYears} year${diffInYears > 1 ? 's' : ''}`;
            } else if (highestValue === diffInMonths) {
                return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''}`;
            } else if (highestValue === diffInWeeks) {
                return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''}`;
            } else if (highestValue === diffInDays) {
                return `${diffInDays} day${diffInDays > 1 ? 's' : ''}`;
            } else {
                return `Expired`;
            }
        },
        calculateTimePlan(days) {
            if (days < 30) {
                return `${days} day${days > 1 ? 's' : ''}`;
            } else if (days < 365) {
                const months = Math.floor(days / 30);
                return `${months} month${months > 1 ? 's' : ''}`;
            } else {
                const years = Math.floor(days / 365);
                const remainingDays = days % 365;
                const months = Math.floor(remainingDays / 30);
                return `${years} year${years > 1 ? 's' : ''}` +
                    (months ? ` and ${months} month${months > 1 ? 's' : ''}` : '');
            }
        },
        formatDatePlan(dateString) {
            const date = new Date(dateString);
            // Add 5 hours and 30 minutes
            date.setHours(date.getHours() - 5);
            date.setMinutes(date.getMinutes() - 30);

            // Extract date components
            const day = String(date.getDate()).padStart(2, '0');
            const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-based
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = String(date.getMinutes()).padStart(2, '0');

            // Convert to 12-hour format with AM/PM
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = String(hours % 12 || 12).padStart(2, '0');

            // Format date and time
            return `${day}-${month}-${year} ${formattedHours}:${minutes}${period}`;
        },
        updatetheValue() {
            if (this.orders && this.orders.length > 0) {
                this.orders = this.orders.map(opt => ({ ...opt, will_expire: this.formatDateString(opt.will_expire), plan_name: opt.plan.title, gateway_name: opt.gateway.name, remaning_time: this.calculateRemainingTime(opt.will_expire), plan_duration: this.calculateTimePlan(opt.plan.days), invoice_date: this.formatDatePlan(opt.created_at) }))
            }
        }
    },
    watch: {
        refresh: {
            deep: true,
            handler(newValue) {
                this.fetchSubscriptionHistoryList();
            }
        },
        currentSubscriptionHistoryList: {
            deep: true,
            handler(newValue) {
                this.open_loader = false;
                if (newValue && newValue.length > 0) {
                    this.orders = [...newValue];
                    this.updatetheValue();
                }
            }
        },
    },

};
</script>

<style>
body {
    font-family: 'Nunito', sans-serif;
}
</style>