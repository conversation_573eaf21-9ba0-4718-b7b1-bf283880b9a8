<template>
    <!--Form -->
    <div class="text-sm rounded m-1">
        <!---paragraph-->
        <div class="flex justify-between items-center font-bold pb-2">
            <h1 class="text-red-600 text-lg">{{ Array.isArray(formValues['problem_title']) ?
                formValues['problem_title'].join(', ') :
                formValues['problem_title'] }}</h1>
            <div v-if="formValues.service_code" class="lg:mt-0 mt-2">
                <a :href="'https://tracking.track-new.com/' + formValues.service_code" target="_blank">
                    <button
                        class="p-1 px-2 text-blue-600 hover:text-blue-700 border rounded border-blue-700 hover:bg-blue-200"
                        title="Track Service">
                        <span class="pr-1">Track Service</span>
                        <font-awesome-icon icon="fa-solid fa-location-dot" />
                    </button>
                </a>
            </div>
        </div>
        <!---div1 fileds-->
        <!-- <div v-if="fields.place === 'div1'"> -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-2" :class="{
            'lg:grid-cols-1':
                !filterDynamicForm('div01').length > 0 && shouldDisplaySection('div2') || formValues['service_priority'] &&
                !formValues['problem_description'] || formValues['notes'] &&
                !filterDynamicForm('div2').length > 0 && shouldDisplaySection('div2') &&
                !filterDynamicForm('div3').length > 0 && shouldDisplaySection('div3') &&
                !filterDynamicForm('div3').length > 0 && shouldDisplaySection('div3') && enable_password
        }">
            <div class="divStyleCard p-2 sm:p-3 lg:p-5">
                <p class="font-bold text-blue-700">Customer</p>
                <div class="grid grid-cols-1">
                    <div v-for="(fields, index) in filterDynamicForm('div1')" :key="index"
                        :class="{ 'hidden': !formValues[fields.fieldKey], 'cols-span-2': index === 0 || index === 1 }"
                        class="flex justify-even py-2 items-center">
                        <!--Display image-->
                        <div>
                            <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                :alt="index">
                        </div>
                        <div v-if="fields.fieldKey !== 'customer'" class="grid grid-cols-1 sm:grid-cols-2 w-full">
                            <div class="flex items-center">
                                <p class="block font-semibold pr-2">{{ fields.lableName + ':' }}</p>
                                <p>{{ formValues[fields.fieldKey] ? formValues[fields.fieldKey] : '- -' }}</p>
                            </div>
                            <p v-if="fields.fieldKey === 'service_type' && formValues['pickup_address']"
                                class="flex justify- between items-center" :class="{ 'pt-2': 'isMobile' }">
                                <font-awesome-icon icon="fa-solid fa-location-dot" v-if="!isMobile" size="lg"
                                    :style="{ color: 'green', padding: '0 5px 0 0' }" /><span
                                    class="font-semibold pr-2">{{ formValues['service_type'] }} Address:</span>
                                {{ formValues['pickup_address'] }}
                            </p>
                        </div>
                        <div v-if="fields.fieldKey === 'customer'" class="grid grid-cols-1 sm:grid-cols-2 w-full">
                            <p><span class="font-semibold pr-2">Customer:</span>
                                {{ formValues[fields.fieldKey] ? formValues[fields.fieldKey].split(' - ')[0] : '' }}
                            </p>
                            <p class="flex items-center" :class="{ 'pt-2': isMobile }"><font-awesome-icon
                                    v-if="!isMobile" icon="fa-solid fa-phone" size="lg"
                                    :style="{ color: 'green', padding: '0 5px 0 0' }" /><span
                                    class="font-semibold pr-2">Phone:</span>
                                {{ formValues[fields.fieldKey] ? formValues[fields.fieldKey].split(' - ')[1] : '' }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <!---div 01 brand, model, etc fields-->
            <div v-if="filterDynamicForm('div01').length > 0 && shouldDisplaySection('div2') || formValues['service_priority']"
                class="divStyleCard p-2 sm:p-3 lg:p-5">
                <p class="font-bold text-blue-700">Details</p>
                <div class="grid grid-cols-1 sm:grid-cols-2">
                    <div v-for="(fields, index) in filterDynamicForm('div01')" :key="index"
                        class="flex justify-even py-2 items-center"
                        :class="{ 'hidden': fields.fieldKey === 'problem_title' || fields.fieldKey === 'problem_description' || !formValues[fields.fieldKey] }">
                        <!--Display image-->
                        <div>
                            <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                :alt="index">
                        </div>
                        <div class="break-words overflow-hidden">
                            <p class="block font-semibold pr-2 sm:pr-1 lg:pr-2">{{ fields.lableName + ':' }}</p>
                            <p class="break-words overflow-hidden">{{ formValues[fields.fieldKey] ?
                                formValues[fields.fieldKey] : '- -' }}</p>
                        </div>
                    </div>
                    <!---div 05 fields service priority-->
                    <div v-for="(fields, index) in filterDynamicForm('div5')" :key="index" class="flex justify-even py-2 item
                        s-center"
                        :class="{ 'hidden': fields.fieldKey !== 'service_priority' || !formValues[fields.fieldKey] }">
                        <div>
                            <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                :alt="index">
                        </div>
                        <div class="break-all overflow-hidden">
                            <p class="block text-sm font-semibold pr-2">{{ fields.lableName + ':' }} </p>
                            <p>{{ formValues[fields.fieldKey] ? formValues[fields.fieldKey] : '- -' }}</p>
                        </div>
                    </div>
                </div>
            </div>
            <!--problem title and description div01  fields.fieldKey !== 'problem_title' && -->
            <div v-if="formValues['problem_description'] || formValues['notes']" class="divStyleCard p-2 sm:p-3 lg:p-5">
                <p class="font-bold text-blue-700">Comments</p>
                <div class="grid grid-cols-1">
                    <div v-for="(fields, index) in filterDynamicForm('div01')" :key="index"
                        class="flex justify-even py-2 items-center"
                        :class="{ 'hidden': fields.fieldKey !== 'problem_description' || !formValues[fields.fieldKey] }">
                        <!--Display image-->
                        <div>
                            <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                :alt="index">
                        </div>
                        <div class="flex justify-center items-center">
                            <p class="block font-semibold pr-2">{{ fields.lableName + ':' }}</p>
                            <p>{{ fields.fieldKey === 'problem_title' && Array.isArray(formValues[fields.fieldKey]) ?
                                formValues[fields.fieldKey].join(', ') : formValues[fields.fieldKey] ?
                                    formValues[fields.fieldKey] : '- -' }}</p>
                        </div>
                    </div>
                    <!---div 05 fields-->
                    <div v-for="(fields, index) in filterDynamicForm('div5')" :key="index"
                        :class="{ 'hidden': fields.fieldKey === 'assignWork' || fields.fieldKey === 'expected_date' || fields.fieldKey === 'document' || fields.fieldKey === 'notification' || fields.fieldKey === 'service_priority' || !formValues[fields.fieldKey] }">
                        <div class="flex justify-even items-center py-2 ">
                            <!--Display image-->
                            <div>
                                <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                    :alt="index">
                            </div>
                            <div class="flex justify-center items-center">
                                <p class="block text-sm font-semibold pr-2">{{ fields.lableName + ':' }}
                                    <span><font-awesome-icon v-if="getplanfeatures('service_comments')"
                                            icon="fa-solid fa-crown" class="text-yellow-500  px-1 rounded-lg" /></span>
                                </p>
                                <p v-if="Array.isArray(formValues[fields.fieldKey])">
                                    {{ formValues[fields.fieldKey].join(', ') }}
                                </p>
                                <p v-else-if="typeof formValues[fields.fieldKey] === 'object'">
                                    <span v-for="(value, key, index) in formValues[fields.fieldKey]" :key="index">
                                        <span class="pr-2 font-bold text-gray-600">{{ key + ':' }}</span> {{ value }}
                                        <br>
                                    </span>
                                </p>
                                <p v-else>{{ formValues[fields.fieldKey] ? formValues[fields.fieldKey] : '- -' }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!--Amc Product list data-->
                <div v-if="formValues && formValues.amc_product_list && formValues.amc_product_list.length > 0">
                    <label class="service-label">AMC Product List </label>
                    <div class="overflow-x-auto">
                        <table class="w-full border border-gray-200 bg-white shadow-lg rounded-lg">
                            <thead>
                                <tr class="bg-gray-100">
                                    <th class="px-4 py-2 text-left font-semibold text-gray-700">Product</th>
                                    <th class="px-4 py-2 text-left font-semibold text-gray-700">Description</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(item, index) in formValues.amc_product_list" :key="index"
                                    :class="index % 2 === 0 ? 'bg-gray-50' : 'bg-white'">
                                    <td class="px-4 py-2 border-b text-gray-800 font-medium">{{ item.product }}</td>
                                    <td class="px-4 py-2 border-b text-gray-600">{{ item.description }}</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!---div 02 custom fileds-->
            <div v-if="filterDynamicForm('div2').length > 0 && shouldDisplaySection('div2')"
                class="divStyleCard p-2 sm:p-3 lg:p-5">
                <p class="font-bold text-blue-700">Custom Fields</p>
                <div class="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div v-for="(fields, index) in filterDynamicForm('div2')" :key="index"
                        class="flex justify-even py-2 items-center"
                        :class="{ 'hidden': !formValues[fields.fieldKey], 'sm:col-span-2': fields.editData === 'Upload' && fields.type === 'multiple' }">
                        <!--Display image-->
                        <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" :alt="index">
                        <div class="flex justify-center items-center">
                            <p class="block font-semibold pr-2"
                                :class="{ 'whitespace-nowrap': fields.editData === 'Upload' && fields.type === 'multiple' }">
                                {{ fields.lableName + ':' }}</p>
                            <p
                                v-if="fields.type !== 'file' && !(fields.editData === 'Upload' && fields.type === 'multiple')">
                                {{ Array.isArray(formValues[fields.fieldKey]) ? formValues[fields.fieldKey].join(', ') :
                                    formValues[fields.fieldKey] ? formValues[fields.fieldKey] : '- -' }}</p>
                        </div>
                        <!-- Attachment files display image -->
                        <div v-if="fields.type === 'file' && formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0 && formValues[fields.fieldKey][0].url && formValues[fields.fieldKey][0].url !== ''"
                            class="flex justify-between items-center">
                            <div class="flex-col justify-center relative p-1 rounded"
                                style="box-shadow: 2px 2px 5px 0px rgb(89, 90, 90)">
                                <!-- <iframe :src="formValues[fields.fieldKey][0].url" class="w-full h-full"
                                frameborder="0"></iframe> -->
                                <p @click="toggleImageVisibility(null, fields)"
                                    class="cursor-pointer hover:text-blue-700 text-blue-600 line-clamp-1">{{
                                        formValues[fields.fieldKey][0].image ? formValues[fields.fieldKey][0].image :
                                            'File uploaded' }}
                                </p>
                                <div class="flex justify-between items-center px-2">
                                    <button @click="toggleImageVisibility(null, fields)"
                                        class="flex justify-center items-center cursor-pointer hover:text-green-700 text-green-600 px-1">
                                        <font-awesome-icon icon="fa-solid fa-eye" class="text-sm sm:text-lg" />
                                        <span v-if="!isMobile" class="px-1">View</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!---Multiple files-->
                        <div v-if="fields.editData === 'Upload' && fields.type === 'multiple'"
                            class="text-sm rounded-lg overflow-auto">
                            <!--image gallery-->
                            <div
                                :class="{ 'grid gap-2 grid-cols-2 sm:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-4': formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0, 'flex justify-center items-center': !(formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0) }">
                                <!-- Iterate over each image item in formValues[fields.fieldKey] -->
                                <div v-if="formValues[fields.fieldKey] && Array.isArray(formValues[fields.fieldKey]) && formValues[fields.fieldKey].length > 0"
                                    v-for="(item, index) in formValues[fields.fieldKey]" :key="index"
                                    class="relative m-1 rounded" style="box-shadow: 2px 2px 5px 0px rgb(89, 90, 90)">
                                    <!--loader circle-->
                                    <div v-if="circle_loader_proof === index && loader_enable == fields.fieldKey"
                                        class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
                                        <CircleLoader :loading="true"></CircleLoader>
                                    </div>
                                    <div class="flex justify-start p-1">
                                        <!-- <iframe :src="formValues[fields.fieldKey][0].url" class="w-full h-full"
                                frameborder="0"></iframe> -->
                                        <p @click="toggleImageVisibility(index, fields)"
                                            class="cursor-pointer hover:text-blue-700 text-blue-600 line-clamp-1">{{
                                                formValues[fields.fieldKey][index].image ?
                                                    formValues[fields.fieldKey][index].image :
                                                    'File uploaded' }}
                                        </p>
                                    </div>
                                    <div class="flex justify-between items-center px-2">
                                        <button @click="toggleImageVisibility(index, fields)"
                                            class="flex justify-center items-center cursor-pointer hover:text-green-700 text-green-600 px-1">
                                            <font-awesome-icon icon="fa-solid fa-eye" class="text-sm sm:text-lg" />
                                            <span v-if="!isMobile" class="px-1">View</span>
                                        </button>
                                        <!-- <button class="text-red-600 flex justify-center items-center px-1"
                                            @click="deleteRow(index, fields)">
                                            <font-awesome-icon icon="fa-solid fa-trash-can"
                                                class="text-sm sm:text-lg" />
                                            <span v-if="!isMobile" class="px-1">Delete</span>
                                        </button> -->
                                    </div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!---div 03 fileds-->
            <div v-if="filterDynamicForm('div3').length > 0 && shouldDisplaySection('div3')"
                class="divStyleCard p-2 sm:p-3 lg:p-5">
                <div>
                    <p class="font-bold text-blue-700">Pre Repair Check</p>
                    <div class="grid grid-cols-1">
                        <div v-for="(fields, index) in filterDynamicForm('div3')" :key="index"
                            class="flex justify-even items-top py-2">
                            <div v-if="fields.fieldKey === 'pre_repair'" class="ml-1 w-full">
                                <div v-if="typeof formValues[fields.fieldKey] === 'object'">
                                    <p v-for="(value, key, index) in formValues[fields.fieldKey]" :key="index"
                                        class="flex py-1">
                                    <p class="pr-2 font-bold text-gray-600 w-[80%]"><font-awesome-icon
                                            icon="fa-regular fa-hand-point-right" /><span class="pl-2">{{ key + ':'
                                            }}</span></p>
                                    <p>
                                        <span class="rounded px-2 whitespace-nowrap"
                                            :class="{ 'bg-green-300': value === 'yes', 'bg-red-300': value === 'no', 'bg-gray-300': value === 'not_applicable' }">
                                            {{ value === 'not_applicable' ? 'not applicable' : value }}</span>
                                    </p>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!---device password-->
            <div v-if="filterDynamicForm('div3').length > 0 && shouldDisplaySection('div3') && enable_password"
                class="divStyleCard p-2 sm:p-3 lg:p-5">
                <!---device password-->
                <!-- <p>{{ filterDynamicForm('div3') }}</p> -->
                <div class="grid grid-cols-1 sm:grid-cols-1 2xl:grid-cols-2 gap-2">
                    <div v-for="(fields, index) in filterDynamicForm('div3')" :key="index"
                        class="flex justify-even py-2 items-center"
                        :class="{ 'hidden': fields.fieldKey == 'pre_repair' }">
                        <!--Display image-->
                        <div>
                            <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                :alt="index">
                        </div>
                        <div class="flex justify-center items-center" v-if="fields.fieldKey === 'device_password'">
                            <p class="block font-semibold pr-2">{{ fields.lableName + ':' }}</p>
                            <p>{{ formValues[fields.fieldKey] ? formValues[fields.fieldKey] : '- -' }}</p>
                        </div>
                        <div class="flex justify-center items-center" v-if="fields.fieldKey === 'device_pattern'"
                            :class="{ 'filter blur-sm': !fields.enable }">
                            <p class="block font-semibold pr-2">{{ fields.lableName + ':' }}</p>
                            <div class="border border-gray-200 rounded cursor-pointer p-2 flex space-x-2"
                                @click="openMobilePattern(formValues[fields.fieldKey])">
                                <font-awesome-icon icon="fa-solid fa-eye" size="xl"
                                    class="text-purple-600 hover:text-purple-700" />
                                <p v-if="formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0"
                                    class="px-2">
                                    {{ typeof formValues[fields.fieldKey] == 'string' ?
                                        JSON.parse(formValues[fields.fieldKey]).join(' -> ') :
                                        Array.isArray(formValues[fields.fieldKey]) ? formValues[fields.fieldKey].join(' ->')
                                            :
                                            formValues[fields.fieldKey] }}</p>
                                <p v-else>- -</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!---div 05 fileds-->
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 divStyle">
            <div v-for="(fields, index) in filterDynamicForm('div5')" :key="index" class="flex m-2 sm:m-3 lg:m-5"
                :class="{ 'hidden': fields.fieldKey === 'document' || fields.fieldKey === 'notification' || fields.fieldKey === 'service_priority' || fields.fieldKey === 'notes' }">
                <!--Display image-->
                <div>
                    <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" :alt="index">
                </div>
                <div class="w-full sm:w-full lg:w-3/4">
                    <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                            v-if="fields.required === 'yes'" class="text-red-500 font-bold">&#9913;</span></label>
                    <!---multiple dropdown add new button-->
                    <div v-if="fields.editData === 'Dropdown' && fields.type === 'multiple' && fields.fieldKey !== 'customer'"
                        class='absolute flex border justify-center items-center -mt-8 ml-[55%] sm:ml-[23%] lg:ml-[26%]  text-white add-button cursor-pointer rounded'
                        @click="fields.fieldKey === 'assignWork' ? openModalEmployee(fields) : openModalName(fields)">

                        <p class="text-xs px-1">add new</p>
                        <span class="px-2 text-center text-lg">+</span>
                    </div>
                    <input
                        v-if="['Date', 'date', 'datetime-local', 'month', 'time', 'week'].includes(fields.type) || fields.editData === 'Date'"
                        :ref="fields.fieldKey" v-model="formValues[fields.fieldKey]"
                        :type="fields.type === 'Date' ? 'date' : fields.type" :placeholder="fields.placeholderText"
                        :name="fields.fieldKey" class="w-full p-2 border rounded" v-datepicker />
                    <input ref="fileInput" v-model="formValues[fields.fieldKey]" :type="fields.type"
                        v-if="fields.fieldKey !== 'document' && (fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email')"
                        :placeholder="fields.placeholderText" :name="fields.fieldKey"
                        class="w-full p-2 border rounded" />
                    <!--Radio button-->
                    <div v-if="fields.type === 'radio'" class="mb-2 grid grid-cols-2 gap-2 sm:grid-cols-3">
                        <div v-for="(option, index) in fields.option" :key="index">
                            <!-- <input type="radio" :id="'option' + index" :value="option"
                                v-model="formValues[fields.fieldKey]" />
                            <label class="pl-1 text-sm" :for="'option' + index"
                                :class="{ 'selected': formValues[fields.fieldKey] === option }">{{ option
                                }}</label> -->
                            <label :for="option + index"
                                :class="{ 'bg-gray-200 text-gray-600': formValues[fields.fieldKey] !== option, 'bg-gray-600 text-white': formValues[fields.fieldKey] === option }"
                                class="px-4 py-2 cursor-pointer inline-block rounded-md transition-colors duration-300 hover:bg-gray-400 hover:text-white">
                                {{ option }}
                                <input type="radio" :id="option + index" :value="option"
                                    v-model="formValues[fields.fieldKey]"
                                    :class="{ 'hidden': formValues[fields.fieldKey] !== option }" />
                            </label>
                        </div>
                    </div>

                    <!--Notes section-->
                    <div v-if="fields.editData === 'Notes'">
                        <textarea v-model="formValues[fields.fieldKey]" :placeholder="fields.placeholderText"
                            :name="fields.fieldKey" class="w-full p-2 border rounded" :cols="fields.colsCount"
                            :rows="fields.rowsCount">
                                </textarea>
                    </div>
                    <!--Drop downs-->
                    <!--single-->
                    <select
                        v-if="fields.editData === 'Dropdown' && fields.type === 'single' && fields.fieldKey !== 'customer'"
                        v-model="formValues[fields.fieldKey]" id="dropdown" class="w-full p-2 border rounded">
                        <option v-if="formValues[fields.fieldKey] === undefined" value="" selected
                            class="text-gray-400">
                            select</option>
                        <option v-for="(option, index) in fields.option" :key="index" :value="option">
                            {{ option }}
                        </option>
                    </select>
                    <!--multiple-->
                    <div class="relative"
                        v-if="fields.editData === 'Dropdown' && fields.type === 'multiple' && fields.fieldKey !== 'customer'">
                        <div class="py-2 px-2 flex flex-wrap items-center"
                            :class="{ 'border border-blue-300': showOptions === fields.fieldKey }">
                            <div v-for="(selectedOption, index) in formValues[fields.fieldKey]" :key="index"
                                class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                {{ selectedOption.name }}
                                <span @click="removeOption(fields, selectedOption)"
                                    class="text-red-500 font-semibold cursor-pointer">x</span>
                            </div>
                            <input type="text" v-model="search[fields.fieldKey]" @input="filterOptions(fields)"
                                @focus="showOptions = fields.fieldKey" @blur="hideOptions"
                                class="h-[35px] outline-none border rounded-lg px-2" placeholder="Search here"
                                @keydown.enter="handleEnterKey('multiple', fields, filteredOptions(fields))"
                                @keydown.down.prevent="handleDownArrow(filteredOptions(fields))"
                                @keydown.up.prevent="handleUpArrow(filteredOptions(fields))" />
                        </div>

                        <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                            v-if="showOptions === fields.fieldKey"
                            :class="{ 'h-auto': showOptions === fields.fieldKey }">
                            <div v-for="(option, index) in filteredOptions(fields)" :key="index"
                                class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                :class="{ 'bg-green-300': index === selectedIndex }"
                                @click="selectOptionMultiple(fields, option)">
                                {{ option.name }}
                            </div>
                            <button
                                v-if="showAddNew === fields.fieldKey && search[fields.fieldKey] && search[fields.fieldKey].length > 1"
                                class="text-white px-3 py-1 rounded-lg add-button"
                                @click="openModalEmployee(fields)">Add New</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!---div05 document and notification-->
        <div class="grid grid-cols-1 gap-2 divStyle">
            <div v-for="(fields, index) in filterDynamicForm('div5')" :key="index" class="flex m-2 sm:m-3 lg:m-5"
                :class="{ 'hidden': fields.fieldKey !== 'document' }">
                <!--Display image-->
                <div>
                    <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" :alt="index">
                </div>
                <div class="w-full " :class="{ 'sm:w-full lg:w-1/2': fields.fieldKey === 'notification' }">
                    <label class="text-sm font-semibold pb-2 block space-x-4"
                        :class="{ 'flex justify-between': formValues['document'] && formValues['document'].length > 0 }">
                        <span>{{ fields.lableName }} <font-awesome-icon v-if="getplanfeatures('service_image')"
                                icon="fa-solid fa-crown" class="text-yellow-500  px-1 rounded-lg" /></span>
                        <span
                            class="border-2 p-1 border-dashed cursor-pointer border-blue-500 rounded hover:bg-blue-100">
                            <span v-if="fields.required === 'yes'" class="text-red-500 font-bold">&#9913;</span>
                            <!---intial time v-if="!formValues['document'] || !formValues['document'].length > 0"-->
                            <button @click="openFileInput" class="px-3">
                                <label for="fileInput" class="cursor-pointer text-blue-600 font-normal">
                                    <span class="text-center pr-2">+</span>
                                    <span class="text-center">Upload Image</span>
                                </label>
                                <!-- @change="handleImageChange($event, 0, filterDynamicForm('div5').find(opt => opt.fieldKey === 'document'))" -->
                                <input type="file" id="fileInput" style="display: none;" accept="image/*"
                                    @change="handleImageChange($event, Array.isArray(formValues['document']) ? formValues['document'].length : 0, filterDynamicForm('div5').find(opt => opt.fieldKey === 'document'))" />
                            </button>
                        </span>
                    </label>
                    <!--Check Box-->
                    <div v-if="fields.type === 'checkbox'" class="mb-2 grid grid-cols-2 gap-0 sm:grid-cols-3">
                        <div v-for="(option, index) in fields.option" :key="index">
                            <input type="checkbox" :id="option" :value="option"
                                :checked="formValues[fields.fieldKey] && formValues[fields.fieldKey].includes(option)"
                                @change="updateCheckbox(fields.fieldKey, option)" />
                            <label class="pl-2 text-sm" :for="option">{{ option }}</label>
                        </div>
                    </div>
                    <!---Upload image in table-->
                    <div v-if="fields.fieldKey === 'document' && formValues['document'] && formValues['document'].length > 0"
                        class="text-sm rounded-lg overflow-auto">
                        <!--image gallery-->
                        <div
                            :class="{ 'grid gap-2 grid-cols-2 sm:grid-cols-4 xl:grid-cols-6 2xl:grid-cols-8': formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0, 'flex justify-center items-center': !(formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0) }">
                            <!-- Iterate over each image item in formValues[fields.fieldKey] -->
                            <div v-if="formValues[fields.fieldKey] && Array.isArray(formValues[fields.fieldKey]) && formValues[fields.fieldKey].length > 0"
                                v-for="(item, index) in formValues[fields.fieldKey]" :key="index"
                                class="relative m-1 rounded" style="box-shadow: 2px 2px 5px 0px rgb(89, 90, 90)">
                                <!--loader circle-->
                                <div v-if="circle_loader_proof === index"
                                    class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
                                    <CircleLoader :loading="true"></CircleLoader>
                                </div>
                                <!-- Image container with fixed dimensions -->
                                <div @click="toggleImageVisibility(index, fields)"
                                    class="relative w-full h-24 overflow-hidden">
                                    <img :src="item.url" :alt="'image ' + index"
                                        class="object-center object-contain w-full h-full" />
                                </div>
                                <!-- Absolute positioned buttons for visibility and deletion -->
                                <div class="py-1 flex justify-between">
                                    <!-- Edit icon -->
                                    <button @click="toggleImageVisibility(index, fields)"
                                        class="flex justify-center items-center ml-2 text-lg text-green-700 hover:text-green-600"><i
                                            class="material-icons">visibility</i> <span
                                            class="text-sm pl-1">view</span></button>
                                    <!-- Delete icon -->
                                    <button @click="deleteRow(index, fields)"
                                        class="flex justify-center items-center ml-2 text-lg font-bold text-red-700 hover:text-red-600"><i
                                            class="material-icons">delete</i>
                                        <!-- <span v-if="!isMobile" class="text-sm pr-1">delete</span> -->
                                    </button>
                                </div>
                            </div>

                            <!-- Container for adding new images -->
                            <!-- <div class="rounded flex justify-center items-center" @click="openFileInput"
                                :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 border-2 py-2 px-10 border-dashed cursor-pointer border-blue-500': formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0 }">
                                <label for="fileInput" class="cursor-pointer text-blue-400"
                                    :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 border-2 py-2 px-7 border-dashed cursor-pointer border-blue-500 -ml-6 sm:-ml-10': !(formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0) }">
                                    <span class="text-center pr-2">+</span>
                                    <span class="text-center">Upload Image</span>
                                </label>
                                <input type="file" id="fileInput" style="display: none;" accept="image/*"
                                    @change="handleImageChange($event, Array.isArray(formValues[fields.fieldKey]) ? formValues[fields.fieldKey].length : 0, fields)" />
                            </div> -->
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <!---Comments by technician-->
        <div v-if="existData" class="divStyle">
            <div class="m-2 sm:m-3 lg:m-5">
                <div class="flex py-2 sm:py-3 lg:py-5">
                    <div class="flex justify-center items-center">
                        <img :src="comments_img" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" alt="comments">
                    </div>
                    <h3 class="font-bold">Comments by technician <font-awesome-icon
                            v-if="getplanfeatures('service_comments')" icon="fa-solid fa-crown"
                            class="text-yellow-500  px-1 rounded-lg" /></h3>
                    <!--v-if="!(formValues.comments && formValues.comments.length > 0)"-->
                    <div class="rounded flex justify-center items-center px-6" @click="openComments">
                        <button
                            class="border-2 p-1 text-sm border-dashed cursor-pointer border-blue-500 rounded hover:bg-blue-100 text-blue-600">
                            + Add Comments</button>
                    </div>
                </div>
                <div v-if="formValues.comments && formValues.comments.length > 0" class="grid gap-2 pb-3"
                    :class="{ 'grid-cols-1 sm:grid-cols-2 ': formValues.comments && formValues.comments.length > 0, 'grid-cols-1': !(formValues.comments && formValues.comments.length > 0) }">
                    <!---Comments by technician-->
                    <!-- <div class="flex justify-center items-center w-full">
                        <div class="rounded flex justify-center items-center  py-2 shadow-sm shadow-blue-300 bg-slate-50 border-2 border-dashed border-blue-500"
                            @click="openComments"
                            :class="{ 'px-6 mb-5': !(formValues.comments && formValues.comments.length > 0), 'px-2': formValues.comments && formValues.comments.length > 0 }">
                            <button class="text-blue-600 hover:text-blue-500">
                                + Add Comments</button>
                        </div>
                    </div> -->
                    <!----Comments list-->
                    <div v-if="formValues.comments && formValues.comments.length > 0"
                        v-for="(cmt, k) in formValues.comments" :key="k" class="flex m-2 sm:m-2 lg:m-2">
                        <!--Display image-->
                        <div v-if="!isMobile" class="flex justify-center items-center">
                            <img :src="comments_img" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                alt="comments">
                        </div>
                        <div
                            class="w-full p-4 rounded-lg bg-blue-50 shadow-md shadow-blue-400 border-l-4 border-blue-500">
                            <div class="flex justify-between items-center pb-2 mb-2">
                                <!-- Date and Time -->
                                <p v-if="cmt.current_date"
                                    class="flex items-center text-sm text-blue-700 cursor-pointer hover:text-blue-800 info-msg"
                                    :title02="cmt.current_date">
                                    <font-awesome-icon icon="fa-regular fa-clock" class="text-lg px-1" />
                                    <span>{{ calculateTimeAgo(cmt.current_date) }}</span>
                                </p>

                                <!-- Updated by -->
                                <p class="flex items-center text-sm text-gray-700 text-blue-700" v-if="cmt.updated_by">
                                    <font-awesome-icon icon="fa-solid fa-user-tie" class="text-lg px-1" />
                                    <span>{{ cmt.updated_by.name ? cmt.updated_by.name : '' }}</span>
                                </p>
                            </div>

                            <!-- Comments Section -->
                            <p class="flex items-center text-sm text-gray-800">
                                <font-awesome-icon icon="fa-solid fa-comments" class="text-lg px-1 text-blue-700" />
                                <span v-if="!isMobile" class="font-bold pr-2 text-blue-700">Comments:</span>
                                <span class="no-capitalize">{{ cmt.comments }}</span>
                            </p>
                        </div>

                        <!-- <div class="flex">
                        <button @click="editComments(cmt, k)"  class="text-blue-700 px-1 py-1 mr-2 hover:text-blue-600"> -->
                        <!-- <img :src="table_edit" alt="edit"  class="h-[25px] w-[25px]" /> -->
                        <!-- <i class="material-icons">
                                edit
                            </i>
                            </button>
                        <button @click="deleteComments(k)" class="text-red-700 hover:text-red-600"> -->
                        <!-- <img :src="table_del" alt="delete" class="h-[25px] w-[25px]" /> -->
                        <!-- <i class="material-icons">
                                delete
                            </i>
                            </button>
                    </div> -->
                    </div>
                </div>
            </div>
        </div>

        <!--Service estimations-->
        <div class="divStyle p-2 sm:p-3 lg:p-5">
            <!---div 04 amount-->
            <div :class="{
                'grid grid-cols-1 gap-2': true,
                ['sm:grid-cols-' + Math.min(filterDynamicForm('div4').length, 3)]: true,
                'hidden': formValues['warranty_type'] == 'Free' || !isValidateDiv('div4')
            }">
                <div v-for="(fields, index) in filterDynamicForm('div4')" :key="index" class="flex m-2 sm:m-2 lg:m-2"
                    :class="{ 'hidden': fields.fieldKey !== 'estimateAmount' }">
                    <!--Display image-->
                    <div class="flex w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3 items-center">
                        <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" :alt="index">
                    </div>
                    <div class="w-full sm:w-full lg:w-3/4">
                        <label class="block text-sm font-semibold pb-2">{{ fields.lableName }}
                            ({{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }})
                            <span v-if="fields.required === 'yes'" class="text-red-500 font-bold">&#9913;</span></label>
                        <input v-model="formValues[fields.fieldKey]" :type="fields.type"
                            v-if="fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file'"
                            :placeholder="fields.placeholderText" :name="fields.fieldKey"
                            :value="formValues[fields.fieldKey] ? formValues[fields.fieldKey] : 0"
                            class="w-full p-2 border rounded" />
                        <!-- Payment type buttons -->
                        <div v-if="fields.fieldKey === 'advanceAmount' && formValues.advanceAmount > 0"
                            class="mt-2 block text-xs">
                            <button v-for="(opt, index) in paymentOptions" :key="index"
                                @click="updateAdvancePaymentType(opt.type)" class="py-1 px-2 border rounded mr-2"
                                :class="{ 'bg-green-700 text-white': formValues.advance_payment_type === opt.type }">
                                {{ opt.type }}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!--Extra materials divStyle p-2 sm:p-3 lg:p-5-->
            <div class="grid grid-cols-1 gap-2"
                :class="{ 'hidden': validateAdditional() && filterDynamicForm('div8').length === 0 }">
                <div class="m-2 sm:m-2 lg:m-2">
                    <div>
                        <div class="flex items-center mb-2">
                            <img :src="additional_img" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                alt="materials">
                            <div class="w-full sm:flex">
                                <p class="font-semibold text-sm">Additional material/services <font-awesome-icon
                                        v-if="getplanfeatures('additional_materials')" icon="fa-solid fa-crown"
                                        class="text-yellow-500  px-1 rounded-lg" /></p>
                                <!--material length 0-->
                                <div v-if="!formValues['additional'] || !formValues['additional'].length > 0"
                                    class="px-1 sm:px-3">
                                    <button @click="addRow($event)" class="text-blue-600"> + Add Material</button>
                                </div>
                            </div>
                            <!--view design-->
                            <div v-if="formValues['additional'] && formValues['additional'].length > 0"
                                class="flex justify-end items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                                <div class="px-2 py-1 flex-shrink-0 cursor-pointer info-msg border rounded-l-full border-r-0 border-gray-500 z-10"
                                    :class="{ 'bg-blue-200': additional_table }" @click="toggleView"
                                    :title02="`Table view`">
                                    <font-awesome-icon v-if="additional_table" icon="fa-solid fa-check"
                                        class="pr-1 text-green-600 font-bold" />
                                    <font-awesome-icon icon="fa-solid fa-bars" />
                                    <!-- <font-awesome-icon v-else icon="fa-solid fa-grip" size="lg" /> -->
                                </div>
                                <div class="px-2 py-1 rounded flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border rounded-r-full border border-gray-500 z-10"
                                    :class="{ 'bg-blue-200': !additional_table }" @click="toggleView"
                                    :title02="`Card view`">
                                    <font-awesome-icon v-if="!additional_table" icon="fa-solid fa-check"
                                        class="pr-1 text-green-600 font-bold" />
                                    <font-awesome-icon icon="fa-solid fa-grip" />
                                </div>
                            </div>
                        </div>

                        <!--grid view-->
                        <div v-if="formValues['additional'] && formValues['additional'].length > 0" class="relative">
                            <div v-if="!additional_table" class="grid gap-2 relative"
                                :class="{ 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3': (formValues['additional'] && formValues['additional'].length > 0) || enable_additional, 'grid-cols-1': !((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                                <div v-if="(formValues['additional'] && formValues['additional'].length > 0) || enable_additional"
                                    v-for="(item, index) in formValues['additional']" :key="index"
                                    class="rounded m-1 bg-yellow-100 px-2 py-1 "
                                    style="box-shadow: 2px 2px 5px 0px rgb(89, 90, 90)">
                                    <div class="flex">
                                        <div :ref="'dropdownContainer' + index" class="relative flex items-center">
                                            <p class="pr-2 w-[90px]">
                                                <font-awesome-icon icon="box" size="sm"
                                                    :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                                <span class="font-bold">Part:</span>
                                            </p>
                                            <div>
                                                <input v-model="item.product_name"
                                                    @input="handleProductChange(item, index)" list="productList"
                                                    class="px-1 py-1 border border-white rounded"
                                                    @focus="handleProductChange(item, index)"
                                                    :ref="'productNameInput' + index"
                                                    @blur="validateProductIsSelected(index)"
                                                    @keydown.enter="handleEnterKey('additional', item, filteredProductList, index), selectedIndex = index"
                                                    @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                                    @keydown.up.prevent="handleUpArrow(filteredProductList)" />

                                                <div v-if="isDropdownOpenProduct === index"
                                                    class="absolute mt-1 max-h-60 w-[80%] sm:w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                                    style="z-index: 30;">
                                                    <p v-for="(option, index) in filteredProductList" :key="index"
                                                        @click="selectedProductData(item, option)"
                                                        class="cursor-pointer hover:bg-gray-100 p-2 border-b"
                                                        :class="{ 'bg-gray-200': index === selectedIndex }">
                                                        {{ option.products.product_name }}
                                                    </p>
                                                    <!-- Add New product button && item.product_name.length > 1  -->
                                                    <button
                                                        v-if="filteredProductList.length === 0 && !productList.some((opt) => opt.product_name === item.product_name)"
                                                        @click="openAddItem(index)"
                                                        class="w-full text-xs text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                                                        + Add Product
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex justify-end  w-full">
                                            <button @click="removeRow(index)" class="text-red-500 px-2 py-1 rounded"><i
                                                    class="material-icons">delete</i></button>
                                        </div>
                                    </div>
                                    <div class="flex items-center py-1">
                                        <p class="pr-2 w-[90px]">
                                            <font-awesome-icon icon="fa-solid fa-boxes-stacked" size="sm"
                                                :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                            <span class="font-bold">Qty:</span>
                                        </p>
                                        <div>
                                            <button class="mr-1 text-center text-lg text-red-600"
                                                @click="item.qty = item.qty > 0 ? item.qty - 1 : item.qty, calculateTotalAndTax(item.qty, index)">
                                                <font-awesome-icon icon="fa-solid fa-circle-minus" /></button>

                                            <input v-model="item.qty" type="number"
                                                class="text-center border py-1 rounded w-[80px]"
                                                @input="validateQuantity(item.qty, index), calculateTotalAndTax(item.qty, index)"
                                                @click="isDropdownOpenProduct = false"
                                                @focus="isDropdownOpenProduct = false" />
                                            <button class="ml-1 text-center text-lg text-green-600"
                                                @click="validateQuantity(item.qty = item.qty + 1, index), calculateTotalAndTax(item.qty, index)">
                                                <font-awesome-icon icon="fa-solid fa-circle-plus" />
                                            </button>
                                        </div>
                                    </div>
                                    <!--price-->
                                    <div class="flex items-center mb-1">
                                        <p class="w-[90px]">
                                            <font-awesome-icon icon="fa-solid fa-dollar-sign" size="sm"
                                                :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                            <span class="font-bold">Price:</span>
                                        </p>
                                        <div>
                                            <input v-model="item.price" type="number"
                                                @input="calculateTotalAndTax(item.qty, index)"
                                                class="py-1 px-1 border border-white rounded"
                                                @keyup.enter="addRow($event)" />
                                        </div>
                                    </div>
                                    <!--sutotal-->
                                    <div class="flex items-center">
                                        <p class="w-[90px]">
                                            <font-awesome-icon icon="fa-solid fa-dollar-sign" size="sm"
                                                :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                            <span class="font-bold">Subtotal:</span>
                                        </p>
                                        <div>
                                            <input v-model="item.total" type="number" readonly
                                                class="py-1 px-1 border border-white rounded"
                                                @keyup.enter="addRow($event)" />
                                            <span
                                                class="text-xs px-1 py-1 flex-shrink-0 cursor-pointer info-msg font-normal"
                                                :title02="`This field is read-only`"><font-awesome-icon
                                                    icon="fa-solid fa-circle-info" class="px-2 text-blue-700 text-lg" />
                                            </span>
                                        </div>
                                    </div>
                                    <div class="flex py-2 mb-1 ">
                                        <p class="pr-2">
                                            <font-awesome-icon icon="fa-solid fa-user" size="sm"
                                                :style="{ color: 'green', padding: '0 5px 0 0' }" />
                                            <span class="font-bold">Customer:</span>
                                        </p>
                                        <div class="flex">
                                            <label class="flex items-center ml-2">
                                                <input v-model="item.status" type="radio" value="approved" class="mr-1">
                                                <span class="text-xs text-green-700">Approved</span>
                                            </label>
                                            <label class="flex items-center mt-1 ml-2">
                                                <input v-model="item.status" type="radio" value="pending" class="mr-1">
                                                <span class="text-xs text-red-700">Pending</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="rounded m-1 flex justify-center items-center" @click="addRow($event)"
                                    :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-2 py-2 border-2 border-blue-500 border-dashed ': ((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                                    <button class="text-blue-700 text-sm text-center px-2 py-1"
                                        :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-8 py-2 border-2 border-blue-500 border-dashed ': !((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                                        + Add Materials
                                    </button>
                                </div>
                            </div>
                            <div v-if="!additional_table"
                                class="grid gap-2 relative grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 m-2">
                                <div class="flex justify-between font-bold text-center items-center bg-gray-200">
                                    <p class="border py-2 w-full">Material Total</p>
                                    <p class="border py-2 w-full">
                                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }}
                                        {{ calculateTotalMaterialCost }}
                                    </p>
                                </div>
                            </div>
                            <div v-if="additional_table" class="overflow-x-auto lg:mx-auto py-2">
                                <table class="w-full mx-auto">
                                    <thead>
                                        <tr class="set-header-background text-white">
                                            <th class="border text-xs sm:text-sm py-1">Product</th>
                                            <th class="border text-xs sm:text-sm">Qty</th>
                                            <th class="border text-xs sm:text-sm">Price</th>
                                            <th class="border text-xs sm:text-sm">Subtotal</th>
                                            <th class="border text-xs sm:text-sm">Client Approval</th>
                                            <th class="border text-xs sm:text-sm">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr v-for="(item, index) in formValues['additional']" :key="index"
                                            class="text-center py-2">
                                            <td class="border py-1 px-2">
                                                <div :ref="'dropdownContainer' + index">
                                                    <input v-model="item.product_name"
                                                        @input="handleProductChange(item, index)"
                                                        @blur="validateProductIsSelected(index)" list="productList"
                                                        class="text-center py-1 border rounded w-[120px] sm:w-full"
                                                        @focus="handleProductChange(item, index)"
                                                        :ref="'productNameInput' + index"
                                                        @keydown.enter="handleEnterKey('additional', item, filteredProductList, index), selectedIndex = index"
                                                        @keydown.down.prevent="handleDownArrow(filteredProductList)"
                                                        @keydown.up.prevent="handleUpArrow(filteredProductList)" />

                                                    <div v-if="isDropdownOpenProduct === index"
                                                        class="absolute mt-1 max-h-60 w-full sm:w-[30%] lg:w-[30%] overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                                                        style="z-index: 100;">
                                                        <p v-for="(option, index) in filteredProductList" :key="index"
                                                            @click="selectedProductData(item, option)"
                                                            class="cursor-pointer hover:bg-gray-100 p-2 border-b"
                                                            :class="{ 'bg-gray-200': index === selectedIndex }">
                                                            {{ option.products.product_name }}
                                                        </p>
                                                        <button
                                                            v-if="filteredProductList.length === 0 && !productList.some((opt) => opt.product_name === item.product_name)"
                                                            @click="openAddItem(index)"
                                                            class="w-full text-xs text-left p-2 hover:bg-gray-100 border-b text-green-700 flex justify-center items-center">
                                                            + Add Product
                                                        </button>
                                                    </div>
                                                </div>
                                            </td>

                                            <td class="border px-1">
                                                <div class="flex justify-center items-center">
                                                    <button class="mr-1 text-center text-lg text-red-600"
                                                        @click="item.qty = item.qty > 0 ? item.qty - 1 : item.qty, calculateTotalAndTax(item.qty, index)">
                                                        <font-awesome-icon icon="fa-solid fa-circle-minus" /></button>

                                                    <input v-model="item.qty" type="number"
                                                        class="text-center border py-1 rounded w-[80px]"
                                                        @input="validateQuantity(item.qty, index), calculateTotalAndTax(item.qty, index)"
                                                        @click="isDropdownOpenProduct = false"
                                                        @focus="isDropdownOpenProduct = false" />
                                                    <button class="ml-1 text-center text-lg text-green-600"
                                                        @click="validateQuantity(item.qty = item.qty + 1, index), calculateTotalAndTax(item.qty, index)">
                                                        <font-awesome-icon icon="fa-solid fa-circle-plus" />
                                                    </button>
                                                </div>
                                            </td>
                                            <td class="border">
                                                <input v-model="item.price" type="number"
                                                    @input="calculateTotalAndTax(item.qty, index)"
                                                    class="text-center py-1 border rounded w-[100px]"
                                                    @keyup.enter="addRow($event)" />
                                            </td>
                                            <td class="border">
                                                <input v-model="item.total" type="number" readonly
                                                    class="text-center py-1 border rounded w-[100px]"
                                                    @keyup.enter="addRow($event)" />
                                                <span
                                                    class="text-xs px-1 py-1 flex-shrink-0 cursor-pointer info-msg font-normal"
                                                    :title02="`This field is read-only`"><font-awesome-icon
                                                        icon="fa-solid fa-circle-info"
                                                        class="px-2 text-blue-700 text-lg" />
                                                </span>
                                            </td>
                                            <td class="border justify-center">
                                                <div class="items-center justify-center">
                                                    <label class="flex items-center ml-2">
                                                        <input v-model="item.status" type="radio" value="approved"
                                                            class="mr-1">
                                                        <span class="text-xs text-green-700">Approved</span>
                                                    </label>
                                                    <label class="flex items-center mt-1 ml-2">
                                                        <input v-model="item.status" type="radio" value="pending"
                                                            class="mr-1">
                                                        <span class="text-xs text-red-700">Pending</span>
                                                    </label>
                                                </div>
                                            </td>
                                            <td class="border">
                                                <button @click="removeRow(index)"
                                                    class="text-red-500 px-2 py-1 rounded"><i
                                                        class="material-icons">delete</i></button>
                                            </td>
                                        </tr>
                                        <tr class="text-center border">
                                            <td class="py-2 px-5 border">
                                                <button @click="addRow($event)"
                                                    class="bg-green-700 hover:bg-green-600 text-white px-2 py-1 rounded">+
                                                    Add
                                                    Row</button>
                                            </td>
                                            <td v-for="mat in 4" :key="mat" class="border"></td>

                                        </tr>
                                        <tr class="font-bold text-center bg-gray-200">
                                            <td colspan="3" class="border py-2"><span
                                                    class="flex justify-end px-3">Total</span></td>
                                            <td class="border py-2">
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9'
                                                    : currentCompanyList.currency }}
                                                {{ calculateTotalMaterialCost }}
                                            </td>
                                            <td colspan="2" class="border py-2"></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div v-else-if="formValues['additional'] && formValues['additional'].length > 0"
                            class="rounded m-1 flex justify-center items-center" @click="addRow($event)"
                            :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-2 py-2 border-2 border-blue-500 border-dashed ': ((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                            <button class="text-blue-300 text-sm text-center px-2 py-1"
                                :class="{ 'shadow-sm shadow-blue-300 rounded shadow-sm bg-slate-50 text-blue-700 bg-gray-50 px-8 py-2 border-2 border-blue-500 border-dashed ': !((formValues['additional'] && formValues['additional'].length > 0) || enable_additional) }">
                                + Add Materials
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!--Estimate amount-->
            <div class="grid grid-cols-1 sm:grid-cols-2">

                <!---div for advance amount-->
                <!---['sm:grid-cols-' + Math.min(filterDynamicForm('div4').length, 3)]: true,-->
                <div :class="{
                    'grid grid-cols-1 gap-2 sm:grid-cols-1': true,
                    'hidden': formValues['warranty_type'] == 'Free' || !isValidateDiv('div4'),
                }">
                    <!--discount && Advance Amount-->

                    <div v-for="(fields, index) in filterDynamicForm('div4')" :key="index"
                        class="flex m-2 sm:m-2 lg:m-2"
                        :class="{ 'hidden': fields.fieldKey !== 'advanceAmount' && fields.fieldKey !== 'discountValue' }">
                        <!--Display image-->
                        <div class="flex">
                            <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                                :alt="index">
                        </div>
                        <div v-if="fields.fieldKey === 'advanceAmount'" class="w-full sm:w-full lg:w-3/4">
                            <!--{{ fields.lableName }}-->
                            <label class="block text-sm font-semibold pb-2"> {{ fields.fieldKey === 'advanceAmount' ?
                                `Paid Amount (${currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9'
                                    : currentCompanyList.currency})` : fields.lableName ? fields.lableName : '' }}
                                <span v-if="fields.required === 'yes'"
                                    class="text-red-500 font-bold">&#9913;</span></label>
                            <input v-model="formValues[fields.fieldKey]" :type="fields.type"
                                v-if="fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file'"
                                :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                :value="formValues[fields.fieldKey] ? formValues[fields.fieldKey] : 0"
                                class="w-full p-2 border rounded" />
                            <!-- Payment type buttons -->
                            <div v-if="fields.fieldKey === 'advanceAmount' && formValues.advanceAmount > 0"
                                class="mt-2 block text-xs">
                                <button v-for="(opt, index) in paymentOptions" :key="index"
                                    @click="updateAdvancePaymentType(opt.type)" class="py-1 px-2 border rounded mr-2"
                                    :class="{ 'bg-green-700 text-white': formValues.advance_payment_type === opt.type }">
                                    {{ opt.type }}
                                </button>
                            </div>
                        </div>
                        <div v-if="fields.fieldKey === 'discountValue' && typeof formValues[fields.fieldKey] === 'object'"
                            class="w-full sm:w-full lg:w-3/4">
                            <label class="block text-sm font-semibold pb-2"> {{ fields.lableName }}
                                <span v-if="fields.required === 'yes'"
                                    class="text-red-500 font-bold">&#9913;</span></label>
                            <div class="flex justify-between items-center space-x-3">
                                <select v-model="formValues[fields.fieldKey].type" class="w-full p-2 border rounded">
                                    <option value="Fixed">Fixed ({{ currentCompanyList && currentCompanyList.currency
                                        === 'INR' ?
                                        '\u20b9' : currentCompanyList.currency }})</option>
                                    <option value="Percentage">Percentage (%)</option>
                                </select>
                                <input v-model="formValues[fields.fieldKey].value" :type="fields.type"
                                    v-if="fields.type === 'text' || fields.type === 'number' || fields.editData === 'Number' || fields.type === 'password' || fields.type === 'email' || fields.editData === 'Date' || fields.type === 'file'"
                                    :placeholder="fields.placeholderText" :name="fields.fieldKey"
                                    :value="formValues[fields.fieldKey].value ? formValues[fields.fieldKey].value : 0"
                                    class="w-full p-2 border rounded" />
                            </div>
                        </div>
                    </div>
                </div>
                <!--table data-->
                <div v-if="formValues['estimateAmount'] > 0 || (formValues['additional'] && formValues['additional'].length > 0) || (formValues['discountValue'] && Object.keys(formValues['discountValue']).length > 0 && formValues['discountValue'].value > 0) || formValues['advanceAmount'] > 0"
                    class="m-2 sm:m-2 g:m-2">
                    <div class="flex">
                        <img :src="estimate_img" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3"
                            :alt="'estimate image'">
                        <div class="w-full sm:w-full lg:w-3/4">
                            <label class="block text-sm font-semibold pb-2">Final Estimate:</label>
                            <table class="w-full p-2">
                                <thead>
                                    <tr class="set-header-background text-white">
                                        <th class="border">Descriptions</th>
                                        <th class="border py-1">Amount</th>
                                    </tr>
                                </thead>
                                <tbody class="border">
                                    <tr v-if="formValues['estimateAmount'] >= 0" class="text-center">
                                        <td class="border py-1 text-left px-2">Service Estimate</td>
                                        <td class="font-semibold border">{{ formValues['estimateAmount'] }}
                                        </td>
                                    </tr>
                                    <tr v-if="formValues['additional'] && formValues['additional'].length > 0"
                                        class="text-center">
                                        <td class="border py-1 text-left px-2">Material Estimate</td>
                                        <td class="font-semibold py-1 border">{{
                                            calculateTotalMaterialCost }}
                                        </td>
                                    </tr>
                                    <tr v-if="formValues['discountValue'] && Object.keys(formValues['discountValue']).length > 0 && formValues['discountValue'].value > 0"
                                        class="text-center text-red-500">
                                        <td class="border py-1 text-left px-2">Discount ( - )</td>
                                        <td class="font-semibold py-1 border">
                                            {{ calculateTotalDiscount }}
                                        </td>
                                    </tr>
                                    <tr class="text-center bg-gray-200">
                                        <td class="font-semibold border py-1 text-left px-2">Total</td>
                                        <td class="font-semibold border items-center">
                                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }}
                                            {{
                                                isNaN((((calculateTotalMaterialCost ?? 0) +
                                                    (formValues['estimateAmount'] >= 0 ? formValues['estimateAmount'] * 1 : 0))
                                                    - calculateTotalDiscount)) ? 0 :
                                                    (((calculateTotalMaterialCost ?? 0) +
                                                        (formValues['estimateAmount'] >= 0 ? formValues['estimateAmount'] * 1 : 0))
                                                        - calculateTotalDiscount)
                                            }}
                                        </td>
                                    </tr>
                                    <!---paid-->
                                    <tr v-if="formValues['advanceAmount'] > 0"
                                        class="text-blue-700 font-bold bg-blue-50">
                                        <td class="border py-1 text-left px-2">Paid Total</td>
                                        <td class="py-1 border">
                                            <p class="text-center"> {{ currentCompanyList && currentCompanyList.currency
                                                === 'INR' ?
                                                '\u20b9' :
                                                currentCompanyList.currency }} {{ formValues['advanceAmount'] }}</p>
                                        </td>
                                    </tr>
                                    <!--balance / return-->
                                    <tr v-if="formValues['advanceAmount'] > 0"
                                        :class="{ 'text-red-600 font-bold bg-red-50': calculateBalanceTotal >= 1, 'text-green-600 font-bold bg-green-50': calculateBalanceTotal <= 0 }">
                                        <td class="border py-1 text-left px-2">
                                            {{ calculateBalanceTotal >= 0 ? 'Balance Amount' : 'Return Amount' }}
                                        </td>
                                        <td class="border items-center">
                                            <p class="text-center py-1">
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9' :
                                                    currentCompanyList.currency }}
                                                {{ calculateBalanceTotal >= 0 ? calculateBalanceTotal :
                                                    (calculateBalanceTotal * -1)
                                                }}
                                            </p>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--- div07 fields-->
        <div v-if="filterDynamicForm('div7') && filterDynamicForm('div7').length > 0 && filterDynamicForm('div7').find(opt => opt.fieldKey === 'service_expense')"
            class="grid grid-cols-1 gap-2 divStyle p-2 sm:p-3 lg:p-5  overflow-hidden">
            <div v-for="(fields, index) in filterDynamicForm('div7')" :key="index" class="m-2 sm:m-2 lg:m-2"
                :class="{ 'hidden': fields.fieldKey !== 'service_expense' }">
                <!--Display image-->
                <div class="flex">
                    <div>
                        <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" :alt="index">
                    </div>
                    <div class="w-full">
                        <label class="service-label">{{ fields.lableName }} <span v-if="fields.required === 'yes'"
                                class="text-red-500 font-bold">&#9913;</span>
                            <span v-if="fields.fieldKey == 'service_expense'"
                                class="text-xs px-2 py-1 flex-shrink-0 cursor-pointer info-msg font-normal"
                                :title02="`These will not be reflected in your invoice (internal purposes only)`"><font-awesome-icon
                                    icon="fa-solid fa-circle-info" class="px-2 text-blue-700 text-lg" />
                            </span>
                            <span
                                v-if="(!formValues[fields.fieldKey] || !formValues[fields.fieldKey].length > 0) && fields.fieldKey == 'service_expense'">
                                <button class="bg-green-600 text-white text-sm py-1 px-3 rounded font-normal"
                                    @click="addExpenseData(fields.fieldKey)">+
                                    Add Expenses</button>
                            </span>
                        </label>
                    </div>
                </div>
                <!---expense details-->
                <div v-if="fields.fieldKey == 'service_expense'" class="mx-2 sm:mx-10">
                    <!---fields-->
                    <div v-if="formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0"
                        class="grid grid-cols-1 gap-2">
                        <table class="w-full mx-auto">
                            <thead>
                                <tr class="set-header-background text-white">
                                    <th class="border text-xs sm:text-sm py-1">Sr.no</th>
                                    <th class="border text-xs sm:text-sm">Descriptions</th>
                                    <th class="border text-xs sm:text-sm">Amount
                                        <span title="This record will store at least 1 or 1 greater than value."
                                            class="text-xs cursor-pointer">(min 1 value)</span>
                                    </th>
                                    <th class="border text-xs sm:text-sm">Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(opt, exnum) in formValues[fields.fieldKey]" :key="exnum"
                                    class="text-center py-2">
                                    <td class="border py-1 px-2 relative">{{ exnum + 1 }}</td>
                                    <td class="border py-1 px-2 relative">
                                        <div>
                                            <input type="text" placeholder="Enter purpose of expense" ref="expenses"
                                                class="py-2 outline-none border rounded-lg px-2 w-full"
                                                v-model="opt.description" />
                                        </div>
                                    </td>
                                    <td class="border py-1 px-2 relative">
                                        <div>
                                            <input type="number" placeholder="Enter amount of expense"
                                                class="py-2 outline-none border rounded-lg px-2 w-full"
                                                v-model="opt.value" />
                                        </div>
                                    </td>
                                    <td class="border py-1 px-2 relative"><button class="text-red-600 items-center"
                                            @click="reduceExpenses(exnum, fields.fieldKey)"><font-awesome-icon
                                                icon="fa-solid fa-trash-can" class="items-center" /></button>
                                    </td>
                                </tr>
                                <!--Add Expenses-->
                                <tr>
                                    <td class="border"></td>
                                    <td class="border py-1 px-2">
                                        <div class="flex justify-left">
                                            <button class="bg-green-600 text-white text-sm py-1 px-3 rounded "
                                                @click="addExpenseData(fields.fieldKey)">+
                                                Add
                                                Expenses</button>
                                        </div>
                                    </td>
                                    <td class="border py-1 px-2 relative"></td>
                                    <td class="border py-1 px-2 relative"></td>
                                </tr>
                                <tr class="font-bold bg-gray-200">
                                    <td colspan="2" class="border">
                                        <p class="px-2 text-center">Total:</p>
                                    </td>
                                    <td class="border">
                                        <div class="py-2 relative flex justify-center items-center text-sm">
                                            <p>{{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                '\u20b9' : currentCompanyList.currency }}
                                                {{ totalExpense(fields.fieldKey) }}
                                            </p>
                                        </div>
                                    </td>
                                    <td></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div v-else-if="formValues[fields.fieldKey] && formValues[fields.fieldKey].length > 0">
                        <button class="bg-green-600 text-white text-sm py-1 px-3 rounded"
                            @click="addExpenseData(fields.fieldKey)">+
                            Add
                            Expenses</button>
                    </div>
                </div>
            </div>
        </div>
        <!--Schedule date & hours-->
        <div v-if="filterDynamicForm('div7') && filterDynamicForm('div7').length > 0 && filterDynamicForm('div7').find(opt => opt.fieldKey === 'schedule_date' || opt.fieldKey === 'schedule_time')"
            class="grid grid-cols-1 sm:grid-cols-2 gap-2 divStyle p-2 sm:p-3 lg:p-5  overflow-hidden">
            <!--schedule on/off-->
            <div class="m-2 sm:m-2 lg:m-2 sm:col-span-2">
                <div class="flex items-center">
                    <!-- Schedule Icon -->
                    <div class="flex-shrink-0">
                        <img :src="'/images/service_page/schedule.png'"
                            class="w-[25px] h-[25px] sm:w-[35px] sm:h-[35px] mr-3" :alt="'schedule'">
                    </div>
                    <!-- Label and Switch -->
                    <div class="w-full flex justify-between items-center">
                        <label class="text-sm font-semibold text-gray-800">Schedule Services</label>
                    </div>
                </div>
                <div class='flex flex-col sm:flex-row ml-[30px] sm:ml-[40px]'>
                    <!-- Dynamic Form Fields -->
                    <div v-for="(fields, index) in filterDynamicForm('div7')" :key="index" class="m-2"
                        :class="{ 'hidden': fields.fieldKey == 'service_expense' }">
                        <p class="text-sm space-x-2 text-gray-700"><span class="font-bold">{{ fields.lableName
                        }}:</span>
                            <span>{{ formValues[fields.fieldKey] ? fields.fieldKey == 'schedule_date' ?
                                formatDateTime(formValues[fields.fieldKey]) : formValues[fields.fieldKey] : '---'
                                }}</span>
                        </p>
                    </div>
                    <div>
                        <button @click="is_enable = !is_enable"
                            class="flex items-center space-x-2 px-4 py-1 bg-blue-500 text-white rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300">
                            <span>Edit</span>
                            <font-awesome-icon icon="fa-solid fa-pencil" />
                        </button>
                    </div>
                </div>
            </div>
            <div v-for="(fields, index) in filterDynamicForm('div7')" :key="index" class="m-2 sm:m-2 lg:m-2"
                :class="{ 'hidden': fields.fieldKey == 'service_expense' || !is_enable }">
                <!--Display image-->
                <div class="flex">
                    <div>
                        <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" :alt="index">
                    </div>
                    <div class="w-full">
                        <label class="service-label">{{ fields.lableName }} <span v-if="fields.required === 'yes'"
                                class="text-red-500 font-bold">&#9913;</span>
                        </label>
                    </div>
                </div>
                <!--schedule date-->
                <div class="mx-2 sm:mx-10">
                    <input
                        v-if="(['Date', 'date', 'datetime-local', 'month', 'time', 'week'].includes(fields.type) || fields.editData === 'Date') && fields.fieldKey == 'schedule_date'"
                        :ref="fields.fieldKey" v-model="formValues[fields.fieldKey]"
                        :type="fields.type === 'Date' ? 'date' : fields.type" :placeholder="fields.placeholderText"
                        :name="fields.fieldKey" class="w-full p-2 border rounded" v-datepicker />
                    <select v-if="fields.fieldKey == 'schedule_time'" v-model="formValues[fields.fieldKey]"
                        class="w-full p-2 mt-1 border rounded">
                        <option value="" disabled selected>Select a time</option>
                        <option v-for="n in Array.from({ length: 24 }, (_, i) => i + 1)" :key="n" :value="n">
                            {{ String(n).padStart(2, '0') }}:00
                        </option>
                    </select>
                </div>
            </div>
        </div>
        <!---div 06 fileds-->
        <div class="grid grid-cols-1 gap-2 divStyle text-capitalize">
            <div v-for="(fields, index) in filterDynamicForm('div6')" :key="index" class="flex m-2 sm:m-3 lg:m-5">
                <!--Display image-->
                <div class="flex">
                    <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" :alt="index">
                </div>
                <div class="w-full">
                    <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                            v-if="fields.required === 'yes'" class="text-red-500 font-bold">&#9913;</span></label>
                    <!--Drop downs-->
                    <div class="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 2xl:grid-cols-6 gap-2"
                        v-if="fields.editData === 'Dropdown' && fields.type === 'single' && fields.fieldKey !== 'customer'">
                        <!-- Render the button at index 8 separately if needed -->
                        <button v-for="(option, index) in fields.option.filter((_, i) => i === 8)" :key="index"
                            class="flex items-center py-2 px-1 rounded" :class="{
                                'bg-amber-800': formValues[fields.fieldKey] === option,
                                'bg-gray-200': formValues[fields.fieldKey] !== option,
                                'hidden': fields.option_status && fields.option_status[8] !== true
                            }" @click="selectStatusOption(option, fields)">
                            <img :src="statusIcons[8]" :class="{ 'grayscale': formValues[fields.fieldKey] !== option }"
                                class="w-5 h-5 mr-1" />
                            <span class="text-sm text-gray-500 text-nowrap" :class="{
                                'text-white': formValues[fields.fieldKey] === option
                            }">{{ option }}</span>
                        </button>
                        <button v-for="(option, index) in fields.option.filter((_, i) => i !== 8)" :key="index"
                            class="flex items-center bg-gray-200 py-2 px-1 rounded" :class="{
                                'bg-gray-700': formValues[fields.fieldKey] === option && option === fields.option[0],
                                'bg-yellow-700': formValues[fields.fieldKey] === option && option === fields.option[1],
                                'bg-violet-400': formValues[fields.fieldKey] === option && option === fields.option[2],
                                'bg-yellow-400': formValues[fields.fieldKey] === option && option === fields.option[3],
                                'bg-green-400': formValues[fields.fieldKey] === option && option === fields.option[4],
                                'bg-green-700': formValues[fields.fieldKey] === option && option === fields.option[5],
                                'bg-red-700': formValues[fields.fieldKey] === option && option === fields.option[6],
                                'bg-green-600': formValues[fields.fieldKey] === option && option === fields.option[7],
                                // Unique colors for indices 8 to 11
                                //'bg-amber-700': formValues[fields.fieldKey] === option && option === fields.option[8],
                                'bg-indigo-500': formValues[fields.fieldKey] === option && option === fields.option[9],
                                'bg-lime-600': formValues[fields.fieldKey] === option && option === fields.option[10],
                                'bg-pink-500': formValues[fields.fieldKey] === option && option === fields.option[11],
                                'unselected': formValues[fields.fieldKey] !== option,
                                'hidden': fields.option_status && fields.option_status[index] !== true
                            }" @click="selectStatusOption(option, fields)">
                            <img :src="statusIcons[index]"
                                :class="{ 'grayscale': formValues[fields.fieldKey] !== option }" class="w-5 h-5 mr-1" />
                            <span class="text-sm text-gray-500 text-nowrap" :class="{
                                'text-white': formValues[fields.fieldKey] === option
                                    && (option === fields.option[0] || option === fields.option[1] || option === fields.option[2] || option === fields.option[3] || option === fields.option[4] || option === fields.option[5] || option === fields.option[6] || option === fields.option[7] || option === fields.option[8] || option === fields.option[9] || option === fields.option[10] || option === fields.option[11])
                            }">{{ option }}</span>
                        </button>
                    </div>
                </div>
            </div>
            <!--otp validations-->
            <div
                v-if="this.formValues['otp_validated'] && Object.keys(this.formValues['otp_validated']).length > 0 && this.formValues['otp_validated'].status">
                <div class="flex justify-center items-center">
                    <p class="text-blue-600">OTP Verified Date: {{ this.formValues['otp_validated'].date_time }}</p>
                </div>
            </div>
        </div>
        <!---deliver with OTP validation-->
        <div v-if="show_delivery_otp" class="grid grid-cols-1 gap-2 divStyle ">
            <div class="flex justify-center items-center m-2 sm:m-2 lg:m-2">
                <div v-if="is_send_otp" class="w-full mx-0 sm:mx-10">
                    <h2 class="font-bold text-gray-800 text-center mb-2">Secure Product Delivery with OTP
                        Validation</h2>
                    <p v-if="formValues && Object.keys(formValues).length > 0 && formValues.customer"
                        class="text-gray-600 text-center mb-2">
                        Sent OTP to: <span class="font-bold">{{ formValues.customer }}</span>
                    </p>

                    <!-- OTP Input Fields -->
                    <div class="flex justify-center space-x-2 mb-2">
                        <input v-for="(digit, index) in otp" :key="index" ref="otpInput" v-model="otp[index]"
                            type="number" maxlength="1"
                            class="w-12 h-12 text-center text-xl border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors duration-200"
                            @input="focusNextInput(index)" @keydown.backspace="focusPreviousInput(index)"
                            @keydown.enter="validateOtp" />
                    </div>

                    <!-- Countdown or Resend OTP -->
                    <div v-if="isCounting">
                        <p class="text-center text-gray-600">Resend OTP in {{ countdown }} seconds</p>
                    </div>
                    <div v-else class="flex justify-center">
                        <button @click="resendOtp"
                            class="py-2 rounded-lg text-blue-600 hover:text-blue-700 transition-colors duration-200 hover:underline cursor-pointer">
                            Resend OTP
                        </button>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mt-3 flex space-x-4">
                        <button @click="validateOtp"
                            class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-600 transition-colors duration-200">
                            Validate OTP
                        </button>
                        <button @click="openDenied"
                            class="w-full bg-red-300 text-red-700 py-2 rounded-lg hover:bg-red-400 transition-colors dration-200">
                            Denied
                        </button>
                        <button @click="closedeliverWithOtp"
                            class="w-full bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors dration-200">
                            Cancel
                        </button>
                    </div>
                </div>
                <!-- Action Buttons -->
                <div v-if="!is_send_otp" class="w-full mx-0 sm:mx-10">
                    <h2 class="font-bold text-gray-800 text-center mb-2">Secure Product Delivery with OTP
                        Validation</h2>
                    <p v-if="formValues && Object.keys(formValues).length > 0 && formValues.customer"
                        class="text-gray-600 text-center mb-2">
                        Sent OTP to: <span class="font-bold">{{ formValues.customer }}</span>
                    </p>
                    <div class="mt-3 flex space-x-4">
                        <button @click="sendOtp"
                            class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-600 transition-colors duration-200">
                            Generate OTP
                        </button>
                        <button @click="openDenied"
                            class="w-full bg-red-300 text-red-700 py-2 rounded-lg hover:bg-red-400 transition-colors dration-200">
                            Denied
                        </button>
                        <button @click="closedeliverWithOtp"
                            class="w-full bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors dration-200">
                            Cancel
                        </button>
                    </div>
                </div>

            </div>
        </div>
        <!---div05 notification-->
        <div class="grid grid-cols-1 gap-2 divStyle ">
            <div v-for="(fields, index) in filterDynamicForm('div5')" :key="index" class="flex m-2 sm:m-3 lg:m-5"
                :class="{ 'hidden': fields.fieldKey !== 'notification' }">
                <!--Display image-->
                <div>
                    <img :src="fields.image" class="w-[20px] h-[20px] sm:w-[30px] sm:h-[30px] mr-3" :alt="index">
                </div>
                <div class="w-full " :class="{ 'sm:w-full': fields.fieldKey === 'notification' }">
                    <label class="block text-sm font-semibold pb-2">{{ fields.lableName }} <span
                            v-if="fields.required === 'yes'" class="text-red-500 font-bold">&#9913;</span></label>

                    <!--new design-->
                    <div v-if="fields.type === 'checkbox'"
                        class="mb-2 sm-mb-0 grid grid-cols-1  lg:grid-cols-4 sm:grid-cols-3 gap-4 sm:gap-4 lg:gap-4">
                        <div v-for="(option, ind) in fields.option" :key="ind" v-show="ind < (companywhatsapp ? 2 : 1)"
                            class="switch-container bg-[#36474f] rounded p-3 text-white">
                            <div class="switch-wrapper w-full">
                                <!-- Display the label text -->
                                <p><font-awesome-icon v-if="ind === 0" icon="fa-regular fa-message" size="lg"
                                        :style="{ color: 'white', padding: '0 5px 0 0' }" />
                                    <font-awesome-icon v-if="ind === 1" icon="fa-brands fa-whatsapp" size="lg"
                                        :style="{ color: 'white', padding: '0 5px 0 0' }" />
                                    <font-awesome-icon v-if="ind === 2" icon="fa-solid fa-envelope" size="lg"
                                        :style="{ color: 'white', padding: '0 5px 0 0' }" />
                                    {{ `Send ` + option + (ind === 2 ? ' To Customer' : ind === 1 ? '' : '')
                                        + ' ?' }}
                                </p>
                                <!-- Wrap the checkbox and slider in the label for proper interaction -->
                                <div class="flex justify-end">
                                    <label class="switch-label" :for="'checkbox-' + option">
                                        <input type="checkbox" :id="'checkbox-' + option" :value="option"
                                            :checked="formValues[fields.fieldKey] && formValues[fields.fieldKey].includes(option)"
                                            @change="updateCheckbox(fields.fieldKey, option)" class="switch-checkbox" />
                                        <span class="switch-slider"></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div v-if="!companywhatsapp"
                            class="flex items-center p-3 text-center border rounded border-red-700 hover:bg-red-50 mb-[8px]"
                            @click="navigateToWhatsApp">
                            <button class="text-red-600">Connect WhatsApp <font-awesome-icon
                                    icon="fa-brands fa-whatsapp" size="lg"
                                    :style="{ color: 'green', padding: '0 5px 0 0' }" /></button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!---Send share review websit link-->
        <div v-if="isDelivered" class="grid grid-cols-1 gap-2 divStyle ">
            <div class="flex m-2 sm:m-3 lg:m-5">
                <!--Display image-->
                <div>
                    <font-awesome-icon icon="fa-solid fa-comment-sms"
                        class="items-center text-green-600 text-lg mr-3" />
                </div>
                <div>
                    <h2 class="font-bold text-gray-800 text-left mb-2">
                        Send the customer a link to leave a Google review
                    </h2>
                    <!-- Action Buttons -->
                    <div class="mt-3 flex space-x-4">
                        <button v-if="companywhatsapp" @click="sendSms('whatsapp')"
                            class="bg-green-600 text-white p-2 rounded-lg hover:bg-green-600 transition-colors duration-200">
                            <font-awesome-icon icon="fa-brands fa-whatsapp" class="px-1" /> WhatsApp
                        </button>
                        <button v-else @click="navigateToWhatsApp"
                            class="bg-red-600 text-white p-2 rounded-lg hover:bg-red-600 transition-colors duration-200">
                            <font-awesome-icon icon="fa-brands fa-whatsapp" class="px-1" /> Connect WhatsApp
                        </button>
                        <!-- <button @click="sendSms('sms')"
                            class="w-full bg-green-600 text-white py-2 rounded-lg hover:bg-green-600 transition-colors duration-200">
                            <font-awesome-icon icon="fa-solid fa-comment-sms" class="px-1" /> SMS
                        </button> -->
                    </div>
                </div>

            </div>
        </div>

        <customerRegister :show-modal="showModal_customer" @close-modal="closeModal" :type="typeOfRegister"
            :userName="customerName">
        </customerRegister>
        <addName :show-modal="showModalName" :title="titleType" :userName="newProduct" @close-modal="closeModalName"
            :category_id="category_id">
        </addName>
        <employeeRegister :show-modal="showModal_employe" @close-modal="closeModalEmployee" :type="'add'"
            :user_name="EmployeeName">
        </employeeRegister>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete">
        </confirmbox>
        <commentsByTechnician :showModal="open_comments" :comments="comments" @close-modal="closeComments">
        </commentsByTechnician>
        <addNewItem :show-modal="showModalProduct" :product_name="newProduct" @close-modal="closeModalProduct">
        </addNewItem>
        <displayImage :showModal="showImageModal" :showImageModal="showImageModalUrl" @close-modal="closeTheImageModal">
        </displayImage>
        <DialogAlert :showModal="open_message" :message="message" @close="closeMessage"></DialogAlert>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <mobile_pattern :showModal="show_mobile_pattern" @close-Modal="closeMobilePattern" :pattern_data="pattern_data"
            :view_page="true">
        </mobile_pattern>
        <dialogConfirmBox :visible="deny_open" :message="deny_message" :type="'services'" @cancel="cancelDeny"
            @ok="confirmDeny"></dialogConfirmBox>
        <Loader :showModal="open_loader"></Loader>
        <posMinusSaleSetting :show-modal="minus_sales_open" @close-modal="closeMinusSales"
            :invoice_setting="currentInvoice" :companyId="companyId">
        </posMinusSaleSetting>
        <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
    </div>
</template>

<script>
import customerRegister from '../../dialog_box/customerRegister.vue';
import addName from '../../dialog_box/addName.vue';
import employeeRegister from '../../dialog_box/employeeRegister.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import commentsByTechnician from '../../dialog_box/commentsByTechnician.vue';
import addNewItem from '../../dialog_box/addNewItem.vue';
import axios from 'axios';
import displayImage from '../../dialog_box/displayImage.vue';
import DialogAlert from '../../dialog_box/dialogAlert.vue';
import { mapGetters, mapActions } from 'vuex';
import mobile_pattern from '../../dialog_box/mobile_pattern.vue';
import dialogConfirmBox from '../../dialog_box/dialogConfirmBox.vue';
import posMinusSaleSetting from '../../dialog_box/posMinusSaleSetting.vue';
import noAccessModel from '../../dialog_box/noAccessModel.vue';
export default {
    props: {
        dynamicForm: Object,
        status: Boolean,
        existData: Object,
        category_name: String,
        category_id: String,
        companyId: String,
        userId: String,
        isMobile: Boolean,
        isCompleted: Boolean,
        updateModalOpen: Boolean
    },
    components: {
        customerRegister,
        addName,
        employeeRegister,
        confirmbox,
        commentsByTechnician,
        addNewItem,
        displayImage,
        DialogAlert,
        mobile_pattern,
        dialogConfirmBox,
        posMinusSaleSetting,
        noAccessModel
    },
    computed: {
        ...mapGetters('employess', ['currentEmployee']),
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('customer', ['currentPagination']),
        ...mapGetters('items', ['currentItems', 'currentItemsPagination']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
        isDelivered() {
            if (this.formValues.service_track && this.formValues.service_track.length > 0 && this.formValues.status) {
                let find_index_new = this.formValues.service_track.findIndex(opt => opt.name.toLowerCase() == this.formValues.status.toLowerCase());
                if (find_index_new == 5 || find_index_new == 7) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        },
        calculateTotalMaterialCost() {
            if (this.formValues['additional'] && this.formValues['additional']) {
                let sub_total = this.formValues['additional'].reduce((sum, item) => (1 * sum) + (1 * item.total), 0);
                return sub_total;
            }
        },
        calculateTotalDiscount() {
            if (this.formValues && this.formValues['discountValue']) {
                const { additional = [], estimateAmount = 0, advanceAmount = 0 } = this.formValues;

                // Ensure amounts are non-negative
                const validEstimateAmount = Math.max(estimateAmount, 0);

                // Calculate total material cost if additional exists
                const totalMaterialCost = additional.length > 0 ? (this.calculateTotalMaterialCost || 0) : 0;

                // Balance amount: Add total material cost and estimate amount
                const balanceAmt = totalMaterialCost + validEstimateAmount;

                const discountValue = this.formValues['discountValue'];
                const discountAmount = discountValue.value || 0;
                if (discountValue.type === 'Fixed') {
                    return Math.max(discountAmount, 0);
                } else if (discountValue.type === 'Percentage') {
                    return Math.max((balanceAmt * (discountAmount / 100)).toFixed(2), 0);
                }

                return 0; // Default case if no discount type
            }
            return 0; // If discountValue is not present
        },

        calculateBalanceTotal() {
            const { additional = [], estimateAmount = 0, advanceAmount = 0 } = this.formValues;

            // Ensure amounts are non-negative
            const validEstimateAmount = Math.max(estimateAmount, 0);
            const validAdvanceAmount = Math.max(advanceAmount, 0);

            // Calculate total material cost if additional exists
            const totalMaterialCost = additional.length > 0 ? (this.calculateTotalMaterialCost || 0) : 0;

            // Calculate the discount total
            const discountTotal = this.calculateTotalDiscount || 0;

            // Calculate balance amount: Add total material cost and estimate amount, subtract discount and advance amount
            const balanceAmt = totalMaterialCost + validEstimateAmount - discountTotal - validAdvanceAmount;

            return balanceAmt.toFixed(2); // Return non-negative balance amount
        }
    },
    data() {
        return {
            formValues: {},
            filteredCustomerOptions: [],
            isDropdownOpen: false,
            isDropdownOpenProduct: false,
            //--extra product details
            items: [
                { product_name: '', qty: 0, price: 0 },
            ],
            productList: [],
            customer_list: [],
            showModal_customer: false,
            typeOfRegister: 'add',
            getFieldKey: null,
            filteredProductList: [],
            showImageModal: false,
            showImageModalUrl: '',
            comments_img: '/images/service_page/comments.png',
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            additional_img: '/images/customer_page/product.png',
            payment_img: '/images/service_page/Money.png',
            estimate_img: '/images/service_page/estimate.png',
            filteredDataOptions: [],
            dropdownOpenData: null,
            statusIcons: ['/images/service_page/order_taken.png', '/images/service_page/hold.png', '/images/service_page/work-in-progress.png', '/images/service_page/estimate.png', '/images/service_page/delivery.png', '/images/service_page/delivered.png', '/images/service_page/cancelled.png', '/images/service_page/checked.png', '/images/service_page/new_service.png', '/images/service_page/awaiting_payment.png', '/images/service_page/service_complete.png', '/images/service_page/retrun_customer.png'],
            paymentOptions: [],
            //----new multiple---
            search: {},
            employeeList: [],
            showOptions: false,
            showAddNew: false,
            selectedIndex: 0,
            customerName: null,
            mouseDownOnDropdown: false,
            showModalName: false,
            titleType: '',
            newProduct: '',
            deviceAndProblemDrop: false,
            fieldsBackup: null,
            showModal_employe: false,
            EmployeeName: '',
            open_confirmBox: false,
            deleteIndex: null,
            isDeleteAdditional: false,
            //---comments---
            open_comments: false,
            comments: '',
            comments_edit_index: null,
            comments_del_action: false,
            //---api integration---
            pagination_data: {},
            //---add new item---
            showModalProduct: false,
            circle_loader: false,
            circle_loader_proof: false,
            delete_type: null,
            //---additional---
            additional_table: false,
            enable_additional: false,
            now: null,
            open_message: false,
            message: '',
            //--toaster---
            show: false,
            type_toaster: 'success',
            //--mobile pattern,
            show_mobile_pattern: false,
            //---pattern data----
            pattern_data: [],
            //--delete expenses--
            delete_expense: false,
            //---deliver with OTP---
            show_delivery_otp: false,
            otp_validated: false,
            otp: ['', '', '', ''], // Array to hold the 4 OTP digits
            countdown: 60, // Countdown timer starts from 60 seconds
            isCounting: true, // Determines whether countdown is active
            countdownInterval: null, // Holds the countdown interval
            open_loader: false,
            //---deny confirm--
            deny_open: false,
            deny_message: '',
            is_send_otp: false,
            //--pasword--
            enable_password: false,
            //--minus sales setting---
            minus_sales_open: false,
            //---no access---
            no_access: false,
            //---schedule date--
            is_enable: false,
            flag: false,
            backup_item: null,
        };
    },
    methods: {
        ...mapActions('employess', ['fetchEmployeeList']),
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('items', ['fetchItemList']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),

        //---validate all the fileds are empty 
        shouldDisplaySection(sectionName) {
            // Filter fields for the specified section
            const sectionFields = this.filterDynamicForm(sectionName);
            if (sectionName == 'div3') {
                this.isValidate();
            }
            // Check if all fields in the section are empty
            const allFieldsEmpty = sectionFields.every(fields => {
                const value = this.formValues[fields.fieldKey];
                // Check if value is empty or undefined
                return value === '' || value === undefined || value === null;
            });

            // Return false if all fields are empty, otherwise true
            return !allFieldsEmpty;
        },

        //---pre checklist
        selectOption(key, option, formValuesKey, preChecklist) {
            const previousOption = this.formValues[formValuesKey];
            const previousOptionIndex = preChecklist.findIndex(opt => opt.value === previousOption);
            // console.log(previousOptionIndex, 'previous options index...!');
            if (previousOptionIndex !== -1) {
                preChecklist[previousOptionIndex].color = '';
            }
            // Delay background color change for a smooth transition
            if (this.formValues[formValuesKey]) {
                setTimeout(() => {
                    // Set the new selected option
                    this.formValues[formValuesKey][key] = option;
                }, 100);
            } else {
                setTimeout(() => {
                    // Set the new selected option
                    this.formValues[formValuesKey] = {};
                    this.formValues[formValuesKey][key] = option;
                }, 100);
            }
        },
        //--check box
        updateCheckbox(fieldKey, option) {
            if (!this.formValues[fieldKey]) {
                // Initialize the nested array if it doesn't exist
                this.formValues[fieldKey] = [];
            }
            const index = this.formValues[fieldKey].indexOf(option);
            if (index === -1) {
                this.formValues[fieldKey].push(option);
            } else {
                this.formValues[fieldKey].splice(index, 1);
            }
        },
        //---customers dropdown
        openModal(fieldKey) {
            // console.log(fieldKey, 'what happening....!', this.formValues[fieldKey]);
            if (!this.existData) {
                this.customerName = this.formValues[fieldKey];
                this.getFieldKey = fieldKey;
                this.showModal_customer = true;
                this.isDropdownOpen = false;
            }
        },
        closeModal(data) {
            console.log(data);
            if (data) {
                // console.log('it is exist');
                let findDuplication = this.customer_list.findIndex((opt) => opt.id === data.id)
                if (findDuplication !== -1) {
                    console.log(data, 'It is not exist...!');
                    this.customer_list[findDuplication] = data;
                } else {
                    // console.log('it is not exist..');
                    // console.log('heelooo', 'what happening...!', this.customer_list);
                    // console.log(this.customer_list, 'What happening...1')
                    this.customer_list.push(data);
                    this.formValues[this.getFieldKey] = data.first_name + (data.last_name ? ' ' + data.last_name : '') + ' - ' + data.contact_number;
                    this.formValues.customer_id = data.id;
                }
            }
            this.showModal_customer = false;
        },
        //---customers dropdown
        selectDropdownOption(fields, option) {
            this.formValues[fields.fieldKey] = option.first_name + (option.last_name ? ' ' + option.last_name : '') + ' - ' + option.contact_number;
            this.formValues.customer_id = option.id;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
        },

        handleDropdownInput(fields) {
            const inputValue = this.formValues[fields.fieldKey];
            // console.log(inputValue, 'WWWWWWW');
            if (inputValue !== undefined && inputValue !== null) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                    if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                        this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    }
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.last_name ? (option.first_name + ' ' + option.last_name).toLowerCase().includes(inputName) :
                            (option.first_name).toLowerCase().includes(inputName)
                    );
                    if (this.filteredCustomerOptions.length === 0 && this.pagination_data.customer && (1 * this.pagination_data.customer.current_page) !== (1 * this.pagination_data.customer.last_page)) {
                        this.getCustomerList((1 * this.pagination_data.customer.current_page) + 1, 'all');
                    }
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = this.customer_list;
            }
        },

        //---multiple dropdowwn
        //----select---
        toggleMultipleDropdown(key) {
            // Update the dynamicForm directly
            let findTheIndex = this.dynamicForm.findIndex(opt => opt.fieldKey === key);
            this.dynamicForm[findTheIndex].multipleDropdown = !this.dynamicForm[findTheIndex].multipleDropdown;

            // Ensure selectedOptions is initialized as an array
            if (!Array.isArray(this.dynamicForm[findTheIndex].selectedOptions)) {
                this.dynamicForm[findTheIndex].selectedOptions = [];
            }
        },
        filterTheData(fields, options, value) {
            if (value !== '') {
                let data = value.split(', ').filter(item => item !== '');
                fields.option = this.filterOptionsValue(options, value);
            } else {
                fields.option = options;
            }
        },

        filterOptionsValue(options, searchInput) {
            if (searchInput && searchInput !== '') {
                // console.log(searchInput, 'What happening....Q');
                let data = searchInput.split(', ').filter(item => item !== '');
                return options.filter(option => !data.includes(option));
            } else {
                return options;
            }
        },

        getOptionId(index) {
            // Generate a unique ID for each option in the dropdown
            return `option_${index}`;
        },
        getOptionRequest(fields, option) {
            // console.log(fields, 'in requrest');
            if (fields.searchInput !== '') {
                return true;
            }
            else {
                return true;
            }
        },
        // Handle selected options
        selectedOptionRequest(fields, option) {
            if (this.formValues[fields.fieldKey]) {
                // console.log('wwffwfwfwfwfwfwfwf', option);
                let findData = this.dynamicForm.find(opt => opt.fieldKey === fields.fieldKey);
                this.formValues[fields.fieldKey] = this.formValues[fields.fieldKey] + ', ' + option;
                return;
            } else {
                this.formValues[fields.fieldKey] = option;
                return;
            }
        },
        //----filter form by the div place---
        filterDynamicForm(parameter) {
            // console.log(this.dynamicForm, 'Is it there object');
            // Filter the dynamicForm array based on the given parameter
            const filteredForm = this.dynamicForm.filter(item => item.place === parameter && item.enable === true);

            // Return the filtered form
            return filteredForm;
        },
        //---extra items
        addRow(event) {
            if (!this.getplanfeatures('additional_materials')) {
                event.stopPropagation();
                if (this.formValues['additional'] && this.formValues['additional'].length > 0) {
                    if (this.formValues['additional'][this.formValues['additional'].length - 1]['product_id']) {
                        this.formValues['additional'].push({ product_name: '', qty: 0, price: 0, status: 'pending' });
                    } else {
                        this.message = 'Please select the product from the already created row or card.';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                    // this.formValues['additional'].push({ product_name: '', qty: 0, price: 0, status: 'pending' });
                } else {
                    this.formValues['additional'] = [{ product_name: '', qty: 0, price: 0, status: 'pending' }];
                }
                this.$nextTick(() => {
                    // console.log(this.$refs['productNameInput' + (this.formValues['additional'].length - 1)], 'WWWREWRWR');
                    if (this.$refs['productNameInput' + (this.formValues['additional'].length - 1)]) {
                        this.$refs['productNameInput' + (this.formValues['additional'].length - 1)][0].focus();
                        this.$refs['productNameInput' + (this.formValues['additional'].length - 1)][0].click();
                    }
                });
            } else {
                //----enable not have access to current plan  
                this.no_access = true;
            }
        },
        //----selected add product---
        validateProductIsSelected(index) {
            if (index >= 0) {
                setTimeout(() => {
                    if (this.deleteIndex != index && this.isDeleteAdditional == false && this.open_confirmBox == false) {
                        if (this.formValues['additional'][index] && !(this.formValues['additional'][index]['product_id']) && !this.showModalProduct && this.$refs[`productNameInput${index}`][0] && this.formValues['additional'].length > index) {
                            // console.log(this.formValues['additional'][index], 'WWWWWWWWW happening the index data...');
                            // this.$nextTick(() => {                        
                            this.$refs[`productNameInput${index}`][0].focus();
                            // });
                            this.message = 'Please select the exist in Itms list or make ADD NEW +';
                            this.type_toaster = 'warning';
                            this.show = true;
                        }
                    }
                }, 100);
            }
        },
        handleProductChange(item, index) {
            const enteredProduct = item.product_name.trim();
            this.isDropdownOpenProduct = index;
            this.backup_item = null;
            this.filteredProductList = this.productList.filter(opt => opt.products && opt.products.product_name && opt.products.product_type === 'Product' &&
                opt.products.product_name.toLowerCase().includes(enteredProduct.toLowerCase()) &&
                !this.formValues['additional'].some(item => item.product_name === opt.products.product_name)
            );
            this.selectedIndex = 0;
            if (this.filteredProductList.length === 0 && this.pagination_data.product && (1 * this.pagination_data.product.current_page) !== this.pagination_data.product.last_page && this.pagination_data.product.current_page < this.pagination_data.product.last_page && !this.flag) {
                this.flag = true;
                this.backup_item = item;
                this.getProductList((this.pagination_data.product.current_page * 1) + 1, 100);
            }
        },
        selectedProductData(item, option) {
            item.company_id = this.companyId;
            item.product_name = option.products.product_name;
            item.product_type = option.products.product_type;
            item.price = option.sales_price;
            item.hsn_code = option.products.hsn_code;
            item.taxvalue = option.gst_value;
            item.tax_type = option.gst_type;
            item.qty = 1;
            item.tax = option.sales_price * (option.gst_value / 100);
            item.total = 1 * option.sales_price;
            item.product_id = option.product_id;
            item.barcode_id = option.barcode_id;
            item.discount_data = { type: option.discount_type ? option.discount_type : 'Fixed', value: option.discount ? option.discount : 0 },
                item.discount = option.discount_type === 'Percentage' ? option.sales_price * (option.discount / 100) : option.discount ? option.discount : 0,
                this.isDropdownOpenProduct = false;
            this.filteredProductList = this.productList;
            this.selectedIndex = 0;
        },
        //--product stock validate
        validateQuantity(value, index) {
            // console.log(value, 'EEEEEEE');
            let product_id = this.formValues['additional'][index].product_id;
            const selectedProductIndex = this.productList.findIndex(product => product.product_id === product_id);
            // console.log(selectedProductIndex, 'TTETETE');
            if (selectedProductIndex !== -1) {
                const selectedProduct = this.productList[selectedProductIndex];

                if (value > 0 && value <= selectedProduct.total_qty) {
                    // console.log(selectedProduct.total_qty, 'qty what happening..1');
                    // selectedProduct.sold_stocks = selectedProduct.total_qty - value;

                    // Check if sold stocks are negative (indicating the product is oversold)
                    // if (selectedProduct.sold_stocks < 0) {



                    // Reset the sold_stocks property
                    // selectedProduct.sold_stocks = 0;
                    // }
                    // Trigger Vue reactivity
                    this.productList[selectedProductIndex] = Object.assign({}, selectedProduct);

                    // Save the updated productList to localStorage
                    // localStorage.setItem('inventory', JSON.stringify(this.productList));
                } else {
                    // this.formValues['additional'][index].qty = selectedProduct.total_qty;
                    alert('Product stock is empty..!');
                }
            }
        },

        removeRow(index) {
            // Stop event propagation to avoid triggering any parent handlers
            event.stopPropagation();
            // this.formValues['additional'].splice(index, 1);           
            if (index >= 0 && this.formValues['additional'].length > index && ((this.formValues['additional'][index].product_id && this.formValues['additional'][index].product_id !== '') || this.formValues['additional'][index].qty > 0 || this.formValues['additional'][index].price > 0)) {
                this.deleteIndex = index;
                this.isDeleteAdditional = true;
                this.open_confirmBox = true;
            } else if (index >= 0 && this.formValues['additional'].length > index) {
                this.formValues['additional'].splice(index, 1);
            }
        },
        addNewProduct(newProductName) {
            // Here, you can implement logic to show a modal or form for adding a new product
            // For simplicity, let's just prompt the user for a new product name
            // const newProductName = prompt("Enter the name of the new product:");
            if (newProductName && !this.productList.some(product => product.product_name.includes(newProductName))) {
                // Add the new product to the product list
                this.productList.push({ product_name: newProductName.trim(), barcode: '', product_code: '', total_qty: 0, price_per_qty: 0, tax: 0, sold_stocks: 0, available_stocks: 0, purchase_order: '', editing: false });
                localStorage.setItem('inventory', JSON.stringify(this.productList))
                // Set the input field value to the new product name
                this.isDropdownOpenProduct = false;
                this.items.forEach(item => {
                    if (item.product_name === newProductName.trim()) {
                        item.product_name = newProductName.trim();
                    }
                });
            }
        },
        //---images view
        toggleImageVisibility(index, fields) {
            if (index !== null && this.formValues[fields.fieldKey][index]) {
                // console.log(index, 'what about ....!', this.formValues[fields.fieldKey][index].url);
                this.showImageModalUrl = this.formValues[fields.fieldKey][index].url;
                this.showImageModal = true;
                // window.open(this.formValues[fields.fieldKey][index].url, '_blank');
            }
            else {
                this.showImageModalUrl = this.formValues[fields.fieldKey][0].url;
                this.showImageModal = true;
                // window.open(this.showImageModal = this.formValues[fields.fieldKey], '_blank');
            }
        },

        // async handleImageChange(event, index, fields) {
        //     if (index >= 0) {
        //         this.circle_loader_proof = index;
        //     } else {
        //         this.circle_loader = true;
        //     }
        //     const file = event.target.files[0];
        //     if (index !== null) {
        //         if (Array.isArray(this.formValues[fields.fieldKey])) {
        //             this.formValues[fields.fieldKey].push({ image: file.name })
        //         } else {
        //             this.formValues[fields.fieldKey] = [{ image: file.name }];
        //         }
        //     }
        //     if (file) {
        //         this.uploadImage(file, index, fields);
        //     } else {
        //         if (index >= 0) {
        //             this.circle_loader_proof = false;
        //         } else {
        //             this.circle_loader = false;
        //         }
        //     }
        // },
        async handleImageChange(event, index, fields) {
            if (fields && fields.fieldKey === 'document' && this.getplanfeatures('service_image')) {
                this.no_access = true;
            } else {
                if (index >= 0) {
                    this.circle_loader_proof = index;
                } else {
                    this.circle_loader = true;
                }

                const file = event.target.files[0];
                if (!file) return;
                // Check if the file is an image type
                const imageTypes = ['image/jpeg', 'image/png', 'image/gif'];
                const fileType = file.type;
                let fileName = file.name;

                // Check if the image was deleted before (allows re-upload of deleted images)
                const uploadedImages = this.formValues[fields.fieldKey] || [];
                let imageExists = uploadedImages.some((uploadedFile) => uploadedFile.image === fileName);
                // If image exists, append a count to make it unique
                let count = 1;
                while (imageExists) {
                    const fileExtension = fileName.substring(fileName.lastIndexOf('.'));
                    const baseFileName = fileName.substring(0, fileName.lastIndexOf('.'));
                    fileName = `${baseFileName}(${count})${fileExtension}`;
                    imageExists = uploadedImages.some((uploadedFile) => uploadedFile.image == fileName);
                    count++;
                }

                if (imageExists) {
                    console.log("This image is already uploaded and is not deleted. You can upload a new image.");
                    return;
                }

                // Check file size (in bytes)
                const maxSizeBytes = 500 * 1024; // 500kb in bytes
                // console.log(file.size > maxSizeBytes, 'WWWWWWW happening...');
                if (file.size > maxSizeBytes) {
                    // Image exceeds 500kb, compress it
                    try {
                        this.circle_loader = true; // Show loader
                        const compressedFile = await this.compressImage(file);

                        if (index !== null) {
                            if (Array.isArray(this.formValues[fields.fieldKey])) {
                                this.formValues[fields.fieldKey].push({ image: fileName });
                            } else {
                                this.formValues[fields.fieldKey] = [{ image: fileName }];
                            }
                        }

                        this.uploadImage(compressedFile, index, fields, fileName);
                        this.resetFileInput(event); // Reset the input field after the upload completes
                    } catch (error) {
                        console.error("Error compressing image:", error);
                        this.circle_loader = false; // Hide loader on error
                    }
                } else {
                    // Image is <= 500kb, upload directly
                    try {
                        this.circle_loader = true; // Show loader

                        if (index !== null) {
                            if (Array.isArray(this.formValues[fields.fieldKey])) {
                                this.formValues[fields.fieldKey].push({ image: fileName });
                            } else {
                                this.formValues[fields.fieldKey] = [{ image: fileName }];
                            }
                        }

                        this.uploadImage(file, index, fields, fileName);
                        this.resetFileInput(event); // Reset the input field after the upload completes
                    } catch (error) {
                        console.error("Error uploading image:", error);
                        this.circle_loader = false; // Hide loader on error
                    }
                }
            }
        },
        // Reset the file input field after the image is uploaded
        resetFileInput(event) {
            event.target.value = '';
        },
        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        uploadImage(file, index, fields, fileName) {
            const formData = new FormData();
            formData.append("image", file, fileName);
            formData.append("model", "service");
            formData.append("company_id", this.companyId);
            // Make an API request to Laravel backend
            axios.post('/image', formData)
                .then(response => {
                    // console.log(response.data, 'What happening....Q', index);
                    //  let data_save = { image_path: response.data.image_path };
                    if (index >= 0) {
                        if (Array.isArray(this.formValues[fields.fieldKey])) {
                            let length = this.formValues[fields.fieldKey].length;
                            // this.formValues[fields.fieldKey].push({ image: file.name, url: response.data.media_url });
                            this.formValues[fields.fieldKey][index].url = response.data.media_url;

                            // this.formValues[fields.fieldKey][length - 1].url = response.data.media_url;
                        } else {
                            this.formValues[fields.fieldKey] = [{ image: file.name, url: response.data.media_url }];
                        }
                        // this.formValues[fields.fieldKey][index].url = response.data.media_url;
                        this.circle_loader_proof = false;
                    } else {
                        this.formValues[fields.fieldKey] = [{ image: file.name, url: response.data.media_url }];
                        // this.formValues[fields.fieldKey] = response.data.media_url;
                        this.circle_loader = true;
                    }
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                    this.circle_loader_proof = false;
                    if (error.response.data && error.response.data.error && Array.isArray(error.response.data.error.image) && error.response.data.error.image[0]) {
                        this.message = error.response.data.error.image[0];
                        this.open_message = true;
                    }
                    if (index >= 0) {
                        // console.log(Array.isArray(this.formValues[fields.fieldKey]), 'RRRRRRRRRRRRRRRR');
                        if (Array.isArray(this.formValues[fields.fieldKey])) {
                            this.formValues[fields.fieldKey].splice(index, 1);
                        }
                    }
                });
        },
        closeMessage() {
            this.open_message = false;
            this.message = '';

        },
        removeAttachmentImage(index, fields) {
            this.deleteIndex = index;
            this.fieldsBackup = fields;
            this.delete_type = 'url';
            this.open_confirmBox = true;
        },
        //---compress the image--


        //--brand, devicemodel and defect tiltle
        handleDisplay(fields) {
            // this.deviceAndProblemDrop = fields.fieldKey;
            this.filteredDataOptions = [];
            if (fields.option && Array.isArray(fields.option) && this.formValues[fields.fieldKey] && this.formValues[fields.fieldKey].length >= 0) {
                // Filter the options based on certain criteria
                let enteredValue = this.formValues[fields.fieldKey].toLowerCase();
                this.filteredDataOptions = fields.option.filter((opt) => opt.toLowerCase().includes(enteredValue));
                // console.log(this.filteredDataOptions, 'Filtered Data Options');
            } else {
                this.filteredDataOptions = fields.option;
            }
        },
        selectOptionData(fields, option) {
            if (option) {
                this.formValues[fields.fieldKey] = option;
                this.dropdownOpenData = null;
                this.filteredDataOptions = [];
                this.selectedIndex = 0;
            }
        },
        AddnewOption(fields) {
            let value = this.fieldsBackup.type === 'single' ? this.formValues[fields.fieldKey] : this.formValues[fields.fieldKey][this.formValues[fields.fieldKey].length - 1];
            if (value) {
                let findDuplicate = fields.option.findIndex((opt) => this.fieldsBackup.fieldKey === 'assignWork' ? opt['name'].toLowerCase() : opt.toLowerCase() === value.toLowerCase());
                if (findDuplicate === -1) {
                    fields.option.push(value);

                    if (this.category_id) {
                        //---service category---
                        axios.get(`/service_categories/${this.category_id}`, { params: { company_id: this.companyId } })
                            .then(response => {
                                // console.log(response.data.data, 'Please colles category');
                                let resData = response.data.data;
                                if (resData.form) {
                                    let formData = JSON.parse(resData.form);
                                    // console.log(formData, 'How it is ther');
                                    let findFormIndex = formData.findIndex((opt) => opt.fieldKey === fields.fieldKey);
                                    formData[findFormIndex]['option'].push(value);
                                    // console.log(formData, 'How it is happning');
                                    axios.put(`/service_categories/${this.category_id}`, { company_id: this.companyId, form: JSON.stringify(formData), service_category: this.category_name })
                                        .then(response => {
                                            console.log(response.data.data, 'response for updated options');
                                        })
                                        .catch(error => {
                                            console.error('Error:', error);
                                        })
                                }
                            })
                            .catch(error => {
                                console.error('Error:', error);
                            })
                    }
                } else {
                    if (this.fieldsBackup.type === 'single') {
                        this.formValues[fields.fieldKey] = fields.option[findDuplicate];
                    } else if (this.fieldsBackup.type === 'multiple') {
                        if (this.formValues[fields.fieldKey]) {
                            this.formValues[fields.fieldKey].push(fields.option[findDuplicate]);

                        } else {
                            this.formValues[fields.fieldKey] = [fields.option[findDuplicate]];
                        }
                    }
                }
            }

            this.dropdownOpenData = null;
            this.filteredDataOptions = [];
        },
        deleteRow(index, fields) {
            // console.log(index, 'WWRWRWRWRWRW');
            this.fieldsBackup = fields;
            this.deleteIndex = index;
            this.open_confirmBox = true;
            // this.formValues.document.splice(index, 1);
            // this.formValues['url'].splice(index, 1);
            // Handle deleting the row from formValues
        },
        addRowImage(fields) {
            if (this.formValues[fields.fieldKey]) {
                this.formValues[fields.fieldKey].push({ name: '', image: '' }); // Add a new empty row to formValues
            }
            else {
                this.formValues[fields.fieldKey] = [{ name: '', image: '' }];
            }
            this.$nextTick(() => {
                if (this.$refs['inputDocument' + (this.formValues[fields.fieldKey].length - 1)][0]) {
                    this.$refs['inputDocument' + (this.formValues[fields.fieldKey].length - 1)][0].focus();
                    this.$refs['inputDocument' + (this.formValues[fields.fieldKey].length - 1)][0].click();
                }
            })
        },
        checkRolesData() {
            if (this.currentLocalDataList && this.currentLocalDataList.roles) {
                if (this.currentLocalDataList.roles[0].toLowerCase() == 'admin') {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        },
        //---selected option--
        selectStatusOption(option, fields) {
            if (option) {
                // console.log(option, fields.fieldKey, 'What happpning in the data............');
                // console.log(this.checkRolesData(), 'RWRWRWRWRWr');

                if (!this.isCompleted || option === 'Completed' || this.checkRolesData()) {
                    let find_index_new = this.existData.service_track.findIndex(opt => opt.name.toLowerCase() == option.toLowerCase());
                    let find_index_exist = this.existData.service_track.findIndex(opt => opt.name.toLowerCase() == this.existData.status.toLowerCase());
                    if (find_index_new == 5 && this.currentInvoice && Array.isArray(this.currentInvoice) && this.currentInvoice.length > 0 && this.currentInvoice[0].delivery_otp && find_index_exist !== 5) {
                        this.formValues[fields.fieldKey] = option;
                        this.opendeliverWithOtp();
                    } else {
                        this.formValues[fields.fieldKey] = option;
                    }
                } else {
                    // alert('Status cannot be change');
                    this.message = "Status cannot be change";
                    this.type_toaster = "warning";
                    this.show = true;
                }
            }
        },
        //---payment option in advance amount
        updateAdvancePaymentType(paymentType) {
            this.formValues.advance_payment_type = paymentType;
        },
        //-----multiple option---
        filterOptions(fields) {
            // console.log(('hello'));
            this.showOptions = fields.fieldKey;
            // this.filteredOptions(fields);
        },
        filteredOptions(fields) {
            // console.log(this.search[fields.fieldKey], 'TTTTTTTTTTTTTTTTTTTTT');
            if (this.search[fields.fieldKey]) {
                // console.log(fields, 'WWWWW', this.search[fields.fieldKey], fields.option, 'Waht happening...');
                let enteredValue = this.search[fields.fieldKey].toLowerCase();
                // console.log(fields && enteredValue && enteredValue.length, 'RRRRRRRRRRRRRRRRRRRRRRRRRRR');
                if (fields && enteredValue && enteredValue.length >= 0) {
                    if (this.formValues[fields.fieldKey]) {
                        if (fields.fieldKey === 'assignWork') {
                            let findArray = this.employeeList.filter(option => option['name'].toLowerCase().includes(enteredValue) && !this.formValues.assignWork.some(opt => opt.name === option.name));
                            if (findArray.length > 0) {
                                this.showAddNew = null;
                                return findArray;
                            }
                            else {
                                if (findArray.length === 0 && this.pagination_data.assign_to && (1 * this.pagination_data.assign_to.current_page) !== (1 * this.pagination_data.assign_to.last_page)) {
                                    this.getEmployeeList((1 * this.pagination_data.assign_to.current_page) + 1, 100);
                                } else {
                                    this.showAddNew = this.employeeList;
                                }
                            }
                        } else {
                            let findArray = fields.option.filter(option => option.toLowerCase().includes(enteredValue) && !this.formValues[fields.fieldKey].includes(option));
                            if (findArray.length >= 0) {
                                this.showAddNew = null;
                                return findArray;
                            } else {
                                this.showAddNew = fields.fieldKey;
                            }
                        }
                    } else {
                        if (fields.fieldKey === 'assignWork') {
                            let findArray = this.employeeList.filter(option => option['name'].toLowerCase().includes(enteredValue));
                            if (findArray.length >= 0) {
                                this.showAddNew = null;
                                return findArray;
                            } else {
                                if (findArray.length === 0 && this.pagination_data.assign_to && (1 * this.pagination_data.assign_to.current_page) !== (1 * this.pagination_data.assign_to.last_page)) {
                                    this.getEmployeeList((1 * this.pagination_data.assign_to.current_page) + 1, 100);
                                } else {
                                    this.showAddNew = this.employeeList;
                                }
                                // console.log(findArray, 'hello');
                            }
                        } else {
                            let findArray = fields.option.filter(option => option.toLowerCase().includes(enteredValue));
                            if (findArray.length >= 0) {
                                this.showAddNew = null;
                                return findArray;
                            }
                            else {
                                // console.log(findArray, 'hello');
                                this.showAddNew = fields.option;
                            }
                        }
                    }
                }
                else if (fields.fieldKey === 'assignWork') {
                    if (this.formValues.assignWork.length === 0) {
                        return this.formValues.assignWork ? this.employeeList.filter(option => !this.formValues.assignWork.some(opt => opt.name === option.name)) : this.employeeList;
                    }
                }
            } else {
                if (this.formValues[fields.fieldKey] && fields.fieldKey !== 'assignWork') {
                    let findArray = fields.option.filter(option => !this.formValues[fields.fieldKey].includes(option));
                    if (findArray.length >= 0) {
                        this.showAddNew = null;
                        return findArray;
                    }
                } else if (fields.fieldKey === 'assignWork') {
                    if (this.employeeList && this.employeeList.length > 0) {
                        if (this.formValues.assignWork && this.formValues.assignWork.length > 0) {
                            let findArray = this.employeeList.filter(option => !this.formValues.assignWork.some(opt => opt.name === option.name));
                            if (findArray.length > 0) {
                                this.showAddNew = null;
                                return findArray;
                            }
                        } else {
                            return this.employeeList;
                        }
                    }
                } else {
                    return fields.option;
                }
            }
        },
        selectOptionMultiple(fields, option) {
            // console.log(option, 'What happening...!');
            if (this.formValues[fields.fieldKey]) {
                if (fields.fieldKey === 'assignWork') {
                    this.formValues[fields.fieldKey].push(option);
                } else {
                    this.formValues[fields.fieldKey].push(option);
                }

            } else {
                this.formValues[fields.fieldKey] = [];
                if (fields.fieldKey === 'assignWork') {
                    this.formValues[fields.fieldKey].push(option);
                } else {
                    this.formValues[fields.fieldKey].push(option);
                }
            }
            this.search[fields.fieldKey] = '';
            // console.log(this.$refs[fields.fieldKey], 'On refernce...!');
            this.selectedIndex = 0;
            this.showOptions = false;
            this.$nextTick(() => {
                if (this.$refs[fields.fieldKey]) {
                    this.$refs[fields.fieldKey].focus();
                    this.$refs[fields.fieldKey].click();
                }
            })
        },
        removeOption(fields, option) {

            this.formValues[fields.fieldKey] = this.formValues[fields.fieldKey].filter(selected => selected !== option);
        },
        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
            }, 300); // Add delay to allow click events on options
        },
        addNewOption(fields) {
            fields.option.push(this.search[fields.fieldKey]);
            this.formValues[fields.fieldKey].push(this.search[fields.fieldKey]);
            this.search[fields.fieldKey] = '';
            this.showOptions = false;
            this.showAddNew = false;
        },
        //----table--
        handleEnterKey(type, fields, optionArray, index) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0 && fields) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(fields, optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    this.openModal(fields.fieldKey);
                    // this.openModal(product_name);
                }
            }
            else if (type === 'div01') {

                if (optionArray && optionArray.length > 0 && fields) {
                    this.selectOptionData(fields, optionArray[this.selectedIndex]);

                }
                else {
                    this.openModalName(fields);
                }

            } else if (type === 'multiple') {
                // console.log(optionArray, 'WWWWW');

                if (optionArray && optionArray.length > 0 && fields) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectOptionMultiple(fields, optionArray[this.selectedIndex]);
                }
                else {
                    if (fields.fieldKey !== 'assignWork') {
                        this.openModalName(fields);
                    } else {
                        this.openModalEmployee(fields);
                    }
                }

            } else if (type === 'additional') {
                if (optionArray && optionArray.length > 0 && fields) {
                    this.selectedProductData(fields, optionArray[this.selectedIndex]);
                } else {
                    // this.openModalName(fields, 'additional');
                    this.openAddItem(index);
                }

            }

        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----customer dropdown--
        closeDropdown(type, fields) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;

            } else if (type && type === 'div01' && fields && this.deviceAndProblemDrop !== this.dropdownOpenData) {
                this.dropdownOpenData = null;
                // this.deviceAndProblemDrop = null;
            }
        },
        preventBlur(type, fields) {
            if (type && type === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            } else if (type && type === 'div01' && fields) {
                this.deviceAndProblemDrop = fields.fieldKey;
                setTimeout(() => {
                    this.deviceAndProblemDrop = null;
                });
            }
        },
        //----brand and device model and defect tittle
        //--name model---
        openModalName(fields, type) {
            if (fields && fields.type === 'single') {
                this.fieldsBackup = fields;
                this.newProduct = this.formValues[fields.fieldKey];
                this.titleType = fields.fieldKey === 'brand' ? 'Add Brand Name' : fields.fieldKey === 'device_model' ? 'Add Device Model' : 'Add Problem Title';
                // this.showModalName = true;
            } else if (fields && fields.type === 'multiple') {
                this.fieldsBackup = fields;
                this.newProduct = this.search[fields.fieldKey];
                this.titleType = `Add ${fields.lableName}`;
            } else if (type === 'additional') {
                this.fieldsBackup = fields;
                this.newProduct = fields.product_name;
                this.titleType = `Add Product Name`;
            }
            this.showModalName = true;
        },
        closeModalName(data) {
            if (data && data !== '') {
                if (this.fieldsBackup.type === 'single') {
                    this.formValues[this.fieldsBackup.fieldKey] = data;
                    // console.log(this.formValues[this.fieldsBackup.fieldKey], 'waht happening...!');
                } else if (this.fieldsBackup.type === 'multiple') {
                    if (this.formValues[this.fieldsBackup.fieldKey]) {
                        this.formValues[this.fieldsBackup.fieldKey].push(data);
                        this.search[this.fieldsBackup.fieldKey] = '';
                    } else {
                        this.formValues[this.fieldsBackup.fieldKey] = [data];
                        this.search[this.fieldsBackup.fieldKey] = '';
                    }

                    this.$nextTick(() => {
                        if (this.$refs[this.fieldsBackup.fieldKey]) {
                            this.$refs[this.fieldsBackup.fieldKey].focus();
                            this.$refs[this.fieldsBackup.fieldKey].click();
                        }
                    })
                } else if (Object.keys(this.fieldsBackup)[0] === 'product_name') {
                    this.addNewProduct(data);
                }
                if (this.fieldsBackup.type === 'single' || this.fieldsBackup.type === 'multiple') {
                    this.AddnewOption(this.fieldsBackup);
                }
            }
            this.showModalName = false;
            this.dropdownOpenData = null;
        },
        //----Add employee---
        openModalEmployee(fields) {
            if (this.checkRoles(['admin'])) {
                if (fields) {
                    this.fieldsBackup = fields;
                    this.EmployeeName = this.search[fields.fieldKey];
                }
                this.showModal_employe = true;
            } else {
                this.message = 'Please contact the admin and add the new employee';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        closeModalEmployee(data) {
            if (data) {
                if (this.formValues[this.fieldsBackup.fieldKey]) {
                    this.formValues[this.fieldsBackup.fieldKey].push(this.fieldsBackup.fieldKey !== 'assignWork' ? data : data);
                    this.search[this.fieldsBackup.fieldKey] = '';
                } else {
                    this.formValues[this.fieldsBackup.fieldKey] = [this.fieldsBackup.fieldKey !== 'assignWork' ? data : data];
                    this.search[this.fieldsBackup.fieldKey] = '';
                }

                this.$nextTick(() => {
                    if (this.$refs[this.fieldsBackup.fieldKey]) {
                        this.$refs[this.fieldsBackup.fieldKey].focus();
                        this.$refs[this.fieldsBackup.fieldKey].click();
                    }
                })
                if (this.fieldsBackup.fieldKey !== 'assignWork') {
                    this.AddnewOption(this.fieldsBackup);
                }
            }
            this.showModal_employe = false;
            this.dropdownOpenData = null;
        },
        deleteRecord() {
            if (this.fieldsBackup) {
                if ((this.deleteIndex >= 0 && this.formValues[this.fieldsBackup.fieldKey][this.deleteIndex].url && this.formValues[this.fieldsBackup.fieldKey][this.deleteIndex].url !== '') || (this.deleteIndex === null && this.formValues[this.fieldsBackup.fieldKey] && this.formValues[this.fieldsBackup.fieldKey] !== '')) {
                    axios.delete('/delete-image', { params: { model: "service", image_url: this.deleteIndex >= 0 ? this.formValues[this.fieldsBackup.fieldKey][this.deleteIndex].url : this.formValues[this.fieldsBackup.fieldKey] } })
                        .then(response => {
                            // this.openMessageDialog(response.data.message);
                            if (this.deleteIndex >= 0) {
                                if (this.delete_type !== 'url' || !this.delete_type) {
                                    this.formValues[this.fieldsBackup.fieldKey].splice(this.deleteIndex, 1);
                                }
                                const fileInput = document.getElementById('file' + this.deleteIndex);
                                if (fileInput) {
                                    fileInput.value = '';
                                }
                                if (this.delete_type === 'url') {
                                    this.formValues[this.fieldsBackup.fieldKey][this.deleteIndex].url = null;
                                }
                            } else {
                                const fileInput = document.getElementById('file' + this.fieldsBackup.fieldKey);
                                if (fileInput) {
                                    fileInput.value = '';
                                }
                                this.formValues[this.fieldsBackup.fieldKey] = null;
                            }

                            this.deleteIndex = null;
                            this.fieldsBackup = null;
                            this.delete_type = null;
                            this.open_confirmBox = false;
                        })
                        .catch(error => {
                            console.error('Error', error);
                            if (error.response.status === 404) {
                                if (this.deleteIndex >= 0) {
                                    if (this.delete_type !== 'url' || !this.delete_type) {
                                        this.formValues[this.fieldsBackup.fieldKey].splice(this.deleteIndex, 1);
                                    }
                                    const fileInput = document.getElementById('file' + this.deleteIndex);
                                    if (fileInput) {
                                        fileInput.value = '';
                                    }
                                    if (this.delete_type === 'url') {
                                        this.formValues[this.fieldsBackup.fieldKey][this.deleteIndex].url = null;
                                    }
                                } else {
                                    const fileInput = document.getElementById('file' + this.fieldsBackup.fieldKey);
                                    if (fileInput) {
                                        fileInput.value = '';
                                    }
                                    this.formValues[this.fieldsBackup.fieldKey] = null;
                                }
                            }
                            this.deleteIndex = null;
                            this.fieldsBackup = null;
                            this.delete_type = null;
                            this.open_confirmBox = false;
                        })
                } else {
                    if (this.deleteIndex !== null && (this.delete_type !== 'url' || !this.delete_type)) {
                        this.formValues[this.fieldsBackup.fieldKey].splice(this.deleteIndex, 1);
                    }
                    this.open_confirmBox = false;
                    this.deleteIndex = null;
                    this.fieldsBackup = null;

                }

            } else if (this.isDeleteAdditional) {
                this.formValues['additional'].splice(this.deleteIndex, 1);
                this.open_confirmBox = false;
                this.deleteIndex = null;
                this.fieldsBackup = null;
                this.isDeleteAdditional = false;
            } else if (this.comments_del_action && this.comments_edit_index >= 0) {
                // console.log(this.comments_del_action, 'Hello');
                this.formValues.comments.splice(this.comments_edit_index, 1);
                this.comments_edit_index = null;
                this.comments_del_action = false;
                this.open_confirmBox = false;
                this.deleteIndex = null;
                this.fieldsBackup = null;
            }
            else if (this.delete_expense) {
                if (this.formValues['service_expense'] && Array.isArray(this.formValues['service_expense']) && this.formValues['service_expense'].length > 0 && this.selectedIndex >= 0) {
                    this.formValues['service_expense'].splice(this.deleteIndex, 1);
                    this.deleteIndex = null;
                }
                this.open_confirmBox = false;
                this.delete_expense = false;
            }

        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.deleteIndex = null;
            this.fieldsBackup = null;
            this.isDeleteAdditional = false;
            this.comments_del_action = false;
        },
        //---validate is already exist customer--
        isExistOption(fields) {
            let findDataExist = this.customer_list.find((option) => {
                if (this.formValues[fields.fieldKey]) {
                    let splitData = this.formValues[fields.fieldKey].split('-');
                    if (splitData[0]) {
                        // console.log((option.last_name ? (option.first_name + ' ' + option.last_name).toLowerCase().includes(splitData[0].toLowerCase()) :
                        //     (option.first_name).toLowerCase().includes(splitData[0].toLowerCase())))
                        // let nameValue = data.first_name + (data.last_name ? ' ' + data.last_name : '').toLowerCase();
                        return (option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(splitData[0].toLowerCase()) :
                            (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(splitData[0].toLowerCase()));
                    }
                }
            });
        },
        //---open comments--
        openComments(record) {
            if (!this.getplanfeatures('service_comments')) {
                if (!record.comments) {
                    this.comments = '';
                    this.comments_edit_index = null;
                }
                this.open_comments = true;
            } else {
                //----enable not have access to current plan  
                this.no_access = true;
            }
        },
        editComments(record, index) {
            if (record) {
                this.comments = record.comments;
                this.comments_edit_index = index;
                this.openComments(record);
            }
        },
        deleteComments(index) {
            this.comments_del_action = true;
            this.comments_edit_index = index;
            this.open_confirmBox = true;
        },
        //---close comments--
        closeComments(val) {
            // console.log(val, 'Waht happening...!');
            if (val && this.comments === '') {
                if (this.formValues.comments) {
                    this.formValues.comments = [{ ...val }, ...this.formValues.comments];
                    setTimeout(() => {
                        this.$emit('saveComments', true);
                    }, 200);
                }
                else {
                    this.formValues.comments = [{ ...val }];
                    setTimeout(() => {
                        this.$emit('saveComments', true);
                    }, 200);
                }
            } else if (val && this.comments_edit_index !== null && this.comments !== '') {
                this.formValues.comments[this.comments_edit_index] = { ...val };
                setTimeout(() => {
                    this.$emit('saveComments', true);
                }, 200);
            }
            this.open_comments = false;
        },
        //---customer---
        getCustomerList(page, per_page) {
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    console.log(response.data, 'customer list....!');
                    this.customer_list = response.data.data;
                    this.pagination_data.customer = response.data.pagination;
                    if (this.customer_list.length > 2) {
                        // Sorting the array alphabetically based on the 'name' key
                        this.customer_list.sort((a, b) => {
                            // Convert both names to lowercase to ensure case-insensitive sorting
                            const nameA = a.first_name.toLowerCase();
                            const nameB = b.first_name.toLowerCase();
                            // Compare the two names
                            if (nameA < nameB) {
                                return -1;
                            }
                            if (nameA > nameB) {
                                return 1;
                            }
                            return 0;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //-----get Employee list----
        getEmployeeList(page, per_page) {
            //---employee list
            axios.get('/employees', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    this.employeeList = response.data.data;
                    this.pagination_data.assign_to = response.data.pagination;
                    if (this.formValues['assignWork'] && Array.isArray(this.formValues['assignWork'])) {
                        let find_data = this.formValues['assignWork'].find(opt => opt.id == this.userId);
                        if (!find_data) {
                            this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
                        }
                    } else {
                        this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
                    }

                    if (this.employeeList.length > 2) {
                        // Sorting the array alphabetically based on the 'name' key
                        this.employeeList.sort((a, b) => {
                            // Convert both names to lowercase to ensure case-insensitive sorting
                            const nameA = a.name.toLowerCase();
                            const nameB = b.name.toLowerCase();
                            // Compare the two names
                            if (nameA < nameB) {
                                return -1;
                            }
                            if (nameA > nameB) {
                                return 1;
                            }
                            return 0;
                        });
                    }
                })
                .catch(error => {
                    console.error('Error get employee', error);
                })
        },
        //--get product list ----
        getProductList(page, per_page) {
            axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data.data);
                    this.productList = response.data.data;
                    this.pagination_data.product = response.data.pagination;
                    this.flag = true;
                    this.fetchItemList(this.pagination_data.product.total);
                })
                .catch(error => {
                    console.error('Error response', error);
                })
        },
        //--get invoice setting ----
        getInvoiceSetting() {
            axios.get('/invoice_settings', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data.data);
                    let settingData = response.data.data;
                    if (settingData.length > 0 && settingData[0].payment_opt) {
                        // console.log(JSON.parse(settingData[0].payment_opt), 'RRRRRR');
                        this.paymentOptions = JSON.parse(settingData[0].payment_opt);
                    }
                })
                .catch(error => {
                    console.error('Error response', error);
                })
        },
        calculateTotalAndTax(value, index) {
            this.formValues['additional'][index].total = this.formValues['additional'][index].price * value;
            this.formValues['additional'][index].tax = this.formValues['additional'][index].total * (this.formValues['additional'][index].tax_value / 100);
        },
        //---add new item----
        openAddItem(index) {
            this.showModalProduct = true;
            this.selectedIndex = index;
            this.newProduct = this.formValues['additional'][index].product_name;
            this.isDropdownOpenProduct = false;
        },
        closeModalProduct(data) {
            if (data && data.product_id && this.selectedIndex !== null) {
                this.selectedProductData(this.formValues['additional'][this.selectedIndex], data);
                this.selectedIndex = null;
                this.productList.push(data);
            } else if (this.selectedIndex !== null && this.formValues['additional'][this.selectedIndex] && !this.formValues['additional'][this.selectedIndex]['product_id']) {
                this.formValues['additional'].splice(this.selectedIndex, 1);
                this.selectedIndex = null;
            }

            this.showModalProduct = false;
            this.isDropdownOpenProduct = false;
        },

        calculateTimeAgo(data) {
            const currentTime = this.now ? this.now.getTime() : new Date().getTime();
            const match = data.match(/^(\d{1,2})-(\d{1,2})-(\d{4}) (\d{1,2}):(\d{2}) (AM|PM)$/i);
            if (!match) {
                return 'invalid date';
            }

            let [, day, month, year, hour, minute, period] = match;
            day = parseInt(day, 10);
            month = parseInt(month, 10); // Keep as 1-based for ISO
            year = parseInt(year, 10);
            hour = parseInt(hour, 10);
            minute = parseInt(minute, 10);
            period = period.toUpperCase();

            // Adjust hour for AM/PM
            if (period === 'PM' && hour !== 12) {
                hour += 12;
            } else if (period === 'AM' && hour === 12) {
                hour = 0;
            }

            // Create ISO string with timezone offset +05:30
            const isoStr = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}T${String(hour).padStart(2, '0')}:${String(minute).padStart(2, '0')}:00+05:30`;
            const targetTime = new Date(isoStr);

            if (isNaN(targetTime.getTime())) {
                return 'invalid date';
            }

            const timeDifference = currentTime - targetTime.getTime();
            if (timeDifference < 0) {
                return 'future date';
            }

            const seconds = Math.floor(timeDifference / 1000);
            const minutes = Math.floor(seconds / 60);
            const hours = Math.floor(minutes / 60);
            const days = Math.floor(hours / 24);

            if (days > 1) {
                return `${days} days ago`;
            } else if (days === 1) {
                return 'yesterday';
            } else if (hours > 0) {
                return `${hours} hour${hours !== 1 ? 's' : ''} ago`;
            } else if (minutes > 0) {
                return `${minutes} minute${minutes !== 1 ? 's' : ''} ago`;
            } else {
                return 'just now';
            }
        },
        parseTargetTime(data) {
            const match = data.match(/(\d+)-(\d+)-(\d+) (\d+):(\d+) (AM|PM)/i);
            if (!match) return null;

            const [, month, day, year, hours, minutes, period] = match;
            const adjustedHours = period.toLowerCase() === 'pm' ? parseInt(hours) + 12 : parseInt(hours);

            const dateString = `${month} ${day} ${year} ${adjustedHours}:${minutes}:00 GMT+0530`;
            return new Date(dateString);
        },
        closeTheImageModal() {
            this.showImageModal = false;
            this.showImageModalUrl = '';
        },
        //--validate roles
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //----open mobile pattern
        openMobilePattern(data) {
            if (data && typeof data === 'string' && Array.isArray(JSON.parse(data))) {
                this.pattern_data = JSON.parse(data);
                this.show_mobile_pattern = true;
            }
            else {
                this.show_mobile_pattern = true;
            }
        },
        closeMobilePattern(data, status) {
            if (data) {
                this.message = "Deivce pattern cannot edit...!";
                this.type_toaster = 'warning';
                this.show = true;
                this.show_mobile_pattern = false;

            } else {
                if (status && this.formValues.device_pattern && typeof this.formValues.device_pattern === 'string' && Array.isArray(JSON.parse(this.formValues.device_pattern))) {
                    this.message = "Deivce pattern cannot edit...!";
                    this.type_toaster = 'warning';
                    this.show = true;
                    this.show_mobile_pattern = false;
                } else {
                    this.show_mobile_pattern = false;
                }
            }
        },
        //----add expense ---
        addExpenseData(key) {
            if (this.formValues[key] && Array.isArray(this.formValues[key]) && this.formValues[key].length > 0) {
                this.formValues[key].push({ description: '', value: 0 });
                this.$nextTick(() => {
                    const inputElement = this.$refs.expenses;
                    if (inputElement && inputElement[this.formValues[key].length - 1]) {
                        inputElement[this.formValues[key].length - 1].focus();
                    }
                });
            } else {
                this.formValues[key] = [{ description: '', value: 0 }];
                this.$nextTick(() => {
                    const inputElement = this.$refs.expenses;
                    if (inputElement && inputElement[this.formValues[key].length - 1]) {
                        inputElement[this.formValues[key].length - 1].focus();
                    }
                });
            }
        },
        totalExpense(key) {
            if (this.formValues[key] && Array.isArray(this.formValues[key]) && this.formValues[key].length > 0) {
                return this.formValues[key].reduce((total, opt) => total + opt.value, 0);
            }
            return 0;
        },
        reduceExpenses(index, key) {
            if (index >= 0 && (this.formValues[key][index]['description'] !== '' || this.formValues[key][index]['value'] > 0)) {
                this.delete_expense = true;
                this.deleteIndex = index;
                this.open_confirmBox = true;
            } else if (index >= 0 && this.formValues[key][index]) {
                if (this.formValues[key] && Array.isArray(this.formValues[key]) && this.formValues[key].length > 0) {
                    this.formValues[key].splice(index, 1);
                }
            }
        },
        anyModalOpen() {
            // Check if any of the modals are currently open
            return (
                this.showModal_customer ||
                this.showModalName ||
                this.showModal_employe ||
                this.open_confirmBox ||
                this.open_comments ||
                this.showModalProduct ||
                this.showImageModal ||
                this.open_message ||
                this.show_mobile_pattern
            );
        },
        closeAllModals() {
            // Close all modals
            this.showModal_customer = false;
            this.showModalName = false;
            this.showModal_employe = false;
            this.open_confirmBox = false;
            this.open_comments = false;
            this.showModalProduct = false;
            this.showImageModal = false;
            this.open_message = false;
            this.show_mobile_pattern = false;
        },
        //---deliver with OTP---
        opendeliverWithOtp() {
            this.show_delivery_otp = true;
        },
        closedeliverWithOtp(data) {
            if (data === true) {
                this.otp_validated = data;
                if (data) {
                    if (this.existData.service_track && this.existData.service_track.length > 0 && this.existData.service_track[5]) {
                        this.formValues.status = this.existData.service_track[5].name;
                    }
                    setTimeout(() => {
                        this.$emit('saveComments');
                    }, 200);
                }
            }

            this.show_delivery_otp = false;
        },
        // Simulated API call to send OTP (you can replace this with actual API logic)
        sendOtp() {
            if (this.formValues && Object.keys(this.formValues).length > 0 && this.formValues.id) {
                this.open_loader = true;
                axios.post('services/send-otp', { service_id: this.formValues.id })
                    .then(response => {
                        this.is_send_otp = true;
                        // console.log('waht about response', response.data);
                        this.startCountdown();
                        this.handleFocus(0);
                        this.open_loader = false;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })
            }
        },
        // Resend OTP
        resendOtp() {
            this.otp = ['', '', '', '']; // Clear the OTP input fields
            this.sendOtp(); // Resend OTP via API
        },
        // Focus on the next input field
        focusNextInput(index) {
            if (this.otp[index] >= 0 && index < 3) {
                this.$refs.otpInput[index + 1].focus();

                if (this.otp[index] >= 0 && this.otp[index].toString().length > 1) {
                    this.otp[index] = parseInt(this.otp[index].toString().slice(0, 1));
                }
            } else if (index === 3) {
                if (this.otp[index] >= 0 && this.otp[index].toString().length > 1) {
                    this.otp[index] = parseInt(this.otp[index].toString().slice(0, 1));
                }
            }
        },
        // Focus on the previous input field when pressing backspace
        focusPreviousInput(index) {
            if (!this.otp[index] && index > 0) {
                this.$refs.otpInput[index - 1].focus();
            }
        },
        // Start the countdown from 60 seconds
        startCountdown() {
            this.isCounting = true;
            this.countdown = 60;
            clearInterval(this.countdownInterval); // Clear any existing intervals
            this.countdownInterval = setInterval(() => {
                if (this.countdown > 0) {
                    this.countdown--;
                } else {
                    this.isCounting = false; // Stop the countdown
                    clearInterval(this.countdownInterval); // Clear the interval
                }
            }, 1000); // Decrease the countdown every second
        },
        // Validate OTP (you can replace this with actual API validation logic)
        validateOtp() {
            this.open_loader = true;
            if (this.otp.join('').length === 4) {
                axios.post('services/verify-otp', { service_id: this.formValues.id, otp: (this.otp.join('') * 1) })
                    .then(response => {
                        // console.log('waht about response', response.data);
                        this.open_loader = false;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                        this.formValues.otp_validated = { date_time: this.getFormattedDateTime(), status: true };
                        this.otp = ['', '', '', '']
                        this.closedeliverWithOtp(true);
                        this.is_send_otp = false;
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })

            } else {
                this.message = 'Please enter a valid 4-digit OTP.';
                this.type_toaster = 'warning';
                this.show = true;
                this.open_loader = false;
            }
        },
        handleFocus(index) {
            // Using nextTick to ensure the DOM is updated before focusing
            this.$nextTick(() => {
                const inputElement = this.$refs.otpInput;
                if (inputElement && inputElement[index]) {
                    inputElement[index].focus();  // Focus on the specific input
                }
            });
        },
        openDenied() {
            this.deny_open = true;
            this.deny_message = "Are you sure you want to deny the OTP validation? This action cannot be undone. Please confirm your decision.";
        },
        cancelDeny() {
            this.deny_open = false;
            this.deny_message = '';
        },
        confirmDeny() {
            this.deny_open = false;
            this.deny_message = '';
            this.closedeliverWithOtp(true);
        },
        //---send sms links website--
        sendSms(type) {
            if (this.formValues.customer_id && this.formValues.customer_id !== '') {
                this.open_loader = true;
                let send_data = type === 'sms' ? { is_sms: true } : { is_whatsapp: true };
                axios.post(`send-review-sms/${this.formValues.customer_id}`, send_data)
                    .then(response => {
                        // console.log('waht about response', response.data);
                        this.open_loader = false;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })
            }
        },
        //---toggle view---
        toggleView() {
            if (this.additional_table) {
                this.additional_table = false;
            } else {
                this.additional_table = true;
            }
        },
        isValidate() {
            let data = this.filterDynamicForm('div3');
            if (data && data.length > 0) {
                data.map(opt => {
                    if (opt.fieldKey !== 'pre_repair' && opt.enable) {
                        this.enable_password = true;
                        return true;
                    }
                })
            }
        },
        //---get optp validated time--
        getFormattedDateTime() {
            const now = new Date();

            // Get the individual parts of the date and time
            let hours = now.getHours();
            const minutes = now.getMinutes();
            const seconds = now.getSeconds();
            const isAm = hours < 12;

            // Format hours in 12-hour format
            hours = hours % 12;
            hours = hours ? hours : 12; // if hour is 0, change it to 12

            // Format minutes and seconds with leading zeros if needed
            const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;
            const formattedSeconds = seconds < 10 ? `0${seconds}` : seconds;

            // Get AM/PM format
            const amPm = isAm ? 'AM' : 'PM';

            // Format the date (MM/DD/YYYY) or change to DD/MM/YYYY based on your preference
            const formattedDate = `${now.getMonth() + 1}/${now.getDate()}/${now.getFullYear()}`;

            // Return the formatted date and time in AM/PM format
            const formattedDateTime = `${formattedDate} ${hours}:${formattedMinutes}:${formattedSeconds} ${amPm}`;
            return formattedDateTime;
        },
        //---additiona materials---
        validateAdditional() {
            if (this.dynamicForm && this.dynamicForm.length > 0) {
                const filteredForm = this.dynamicForm.filter(item => item.fieldKey === 'additional');
                if (filteredForm && filteredForm.length > 0) {
                    return true;
                } else {
                    return false;
                }
            }
        },
        //---close minus sales--
        //---minus sales----
        closeMinusSales() {
            this.minus_sales_open = false;
        },
        isValidateDiv(divdata, type) {
            let data = this.filterDynamicForm(divdata);
            if (data && data.length > 0) {
                if (divdata == 'div4') {
                    let find_data = data.filter((opt) => opt.fieldKey == 'estimateAmount' || opt.fieldKey == 'advanceAmount' || opt.fieldKey == 'serviceAmount');
                    if (find_data && find_data.length > 0) {
                        return true;
                    } else {
                        return false;
                    }
                }
            } else {
                return false;
            }
        },
        //--discount initialize---
        initializeFormValues() {
            if (this.dynamicForm && Array.isArray(this.dynamicForm) && this.dynamicForm.length > 0) {
                return this.dynamicForm.map(field => {
                    if (field.fieldKey === 'discountValue' && field.enable && (!this.formValues[field.fieldKey] || typeof this.formValues[field.fieldKey] !== 'object')) {
                        this.formValues[field.fieldKey] = { type: 'Fixed', value: 0 };
                    }
                    return field;
                });
            }
        },
        navigateToWhatsApp() {
            this.$router.push({ name: 'whatsappsetting' });
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        //--cloase no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---schedule date--
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year}`;

            return formattedDateTime;
        },
    },
    mounted() {
        //---get customer list--
        if (this.customer_list.length === 0) {
            if (this.currentCustomer.length > 0) {
                this.customer_list = this.currentCustomer;
            } else {
                this.fetchCustomerList();
                this.customer_list = this.currentCustomer;
            }
            // this.getCustomerList(1, 'all');
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLoclDataList();
        }
        this.fetchCompanyList();
        this.fetchWhatsappList();
        //----inventory list--
        if (this.productList.length === 0) {
            if (!this.currentItems || this.currentItems.length === 0) {
                this.getProductList(1, 1);
            }
            if (this.currentItems.length > 0) {
                this.productList = this.currentItems;
            } else if (this.pagination_data && this.pagination_data.product && this.pagination_data.product.total) {
                this.fetchItemList(this.pagination_data.product.total);
                this.productList = this.currentItems;
            }
            // this.getProductList(1, 1000);
        }

        //----document values---
        if (this.existData && Object.keys(this.existData).length !== 0) {
            // console.log(this.existData, 'RWRWRWRW happpppp in customer_id');
            this.formValues = JSON.parse(JSON.stringify(this.existData));
        }
        //----payment type--
        if (this.paymentOptions.length === 0) {
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                if (this.currentInvoice[0].payment_opt) {
                    this.paymentOptions = JSON.parse(this.currentInvoice[0].payment_opt);
                }
            } else {
                this.fetchInvoiceSetting();
                this.paymentOptions = this.currentInvoice;
            }
            // this.getInvoiceSetting();
        }
        if (Array.isArray(this.currentInvoice) && this.currentInvoice.length > 0 && this.currentInvoice[0].is_whatsapp) {
            this.formValues.notification = this.companywhatsapp ? ['WhatsApp'] : [];
            if (this.currentInvoice[0].is_sms) {
                this.formValues.notification.push('SMS');
            }
        } else if (Array.isArray(this.currentInvoice) && this.currentInvoice.length > 0 && this.currentInvoice[0].is_sms) {
            this.formValues.notification = ['SMS'];
        }
        //---employee list---
        if (this.employeeList.length === 0) {
            if (this.currentEmployee.length > 0) {
                this.employeeList = this.currentEmployee;
            } else if (this.userId) {
                this.fetchEmployeeList(this.userId);
                this.employeeList = this.currentEmployee;
            }
            // this.getEmployeeList(1, 'all');
        }


        this.clickOutsideListener = (event) => {
            // console.log(this.$refs['dropdownContainer' + this.isDropdownOpenProduct], 'iiii', event.target);
            if (this.$refs['dropdownContainer' + this.isDropdownOpenProduct] && this.$refs['dropdownContainer' + this.isDropdownOpenProduct][0] && !this.$refs['dropdownContainer' + this.isDropdownOpenProduct][0].contains(event.target)) {
                this.isDropdownOpenProduct = false; // Close the dropdown
                // console.log('Hellooooooo');
            }
        };
        //---discount---
        this.initializeFormValues();
        document.addEventListener('click', this.clickOutsideListener);

    },
    created() {
        // Update current time every second
        setInterval(() => {
            this.now = new Date();
        }, 1000);

        let getObjAdd = localStorage.getItem('service_form_add');
        if (getObjAdd) {
            let parseData = JSON.parse(getObjAdd);
            this.additional_table = parseData.additional_table;
        }
    },
    beforeDestroy() {
        // Remove click outside event listener when component is destroyed
        document.removeEventListener('click', this.clickOutsideListener);
    },
    watch: {
        formValues: {
            handler(newFormValues) {
                if (this.status) {
                    // Emit the collectData event whenever formValues are updated
                    this.$emit('collectData', newFormValues);
                } else {
                    this.$emit('collectData', newFormValues);
                }
                if (this.show_delivery_otp && this.formValues['status'] != 'Delivered') {
                    this.show_delivery_otp = false;
                }
            },
            deep: true, // Watch changes deeply within the object
        },
        currentEmployee: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.employeeList = newValue;
                }
            }
        },
        currentCustomer: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'customer');
                if (newValue && newValue.length > 0) {
                    this.customer_list = newValue;
                }
            }
        },
        currentItems: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.productList = newValue;
                    if (this.backup_item && this.isDropdownOpenProduct) {
                        this.handleProductChange(this.backup_item, this.isDropdownOpenProduct);
                    }
                }
            }
        },
        currentItemsPagination: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.pagination_data = { ...newValue };
                    this.flag = false;
                }
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    // this.paymentOptions = newValue;
                    if (newValue[0].payment_opt) {
                        // console.log(JSON.parse(newValue[0].payment_opt), 'RRRRRR');
                        this.paymentOptions = JSON.parse(newValue[0].payment_opt);
                    }
                    if (newValue[0].is_whatsapp) {
                        this.formValues.notification = this.companywhatsapp ? ['WhatsApp'] : [];
                        if (newValue[0].is_sms) {
                            this.formValues.notification.push('SMS');
                        }
                    } else if (newValue[0].is_sms) {
                        this.formValues.notification = ['SMS'];
                    }
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        showModal_customer: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        showModalName: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        showModal_employe: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_comments: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        showModalProduct: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        showImageModal: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        show_mobile_pattern: {
            deep: true,
            handler(newValue) {
                this.$emit('update-is-modal-open', newValue);
            }
        },
        additional_table: {
            deep: true,
            handler(newValue) {
                localStorage.setItem('service_form_add', JSON.stringify({ additional_table: newValue }));
            }
        }
    },

};
</script>

<style scoped>
.image-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 20px;
    background: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.custom-radio-box {
    display: inline-block;
    padding: 5px;
    /* margin: 0px; */
    cursor: pointer;
    /* border: 1px solid #ccc; */
    /* border-radius: 5px; */
    transition: background-color 0.3s ease-in-out;
}

/* Additional styling for the selected option */
.selected-option {
    background-color: #228B22;
    /* Change to the color you want for the selected option */
    color: #fff;
    /* Change to the text color you want for the selected option */
}

/* Custom styles for the switch checkbox */
.switch-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.switch-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.switch-label {
    display: inline-block;
    cursor: pointer;
    margin-left: 8px;
    /* Adjust spacing between label and checkbox */
}


.switch-slider {
    position: relative;
    display: inline-block;
    width: 35px;
    height: 15px;
    background-color: #ccc;
    /* Default color of the switch */
    border-radius: 20px;
    transition: background-color 0.3s;
}

.switch-slider:before {
    content: "";
    position: absolute;
    top: -2px;
    left: 1px;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: white;
    transition: transform 0.3s;
}

.switch-checkbox:checked+.switch-slider {
    background-color: #007bff;
    /* Color when the switch is checked */
}

.switch-checkbox:checked+.switch-slider:before {
    transform: translateX(20px);
    /* Move the slider to the right when checked */
}

.switch-label input {
    display: none;
    /* Hide the actual checkbox */
}
</style>
