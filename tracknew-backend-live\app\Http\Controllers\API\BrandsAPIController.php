<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateBrandsAPIRequest;
use App\Http\Requests\API\UpdateBrandsAPIRequest;
use App\Models\Brands;
use App\Repositories\BrandsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class BrandsController
 * @package App\Http\Controllers\API
 */

class BrandsAPIController extends AppBaseController
{
    /** @var  BrandsRepository */
    private $brandsRepository;

    public function __construct(BrandsRepository $brandsRepo)
    {
        $this->brandsRepository = $brandsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/brands",
     *      summary="getBrandsList",
     *      tags={"Brands"},
     *      description="Get all Brands",
     *      @OA\Parameter(
     *      name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Brands")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
       
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $brandsQuery = Brands::where('company_id', $companyId);

        if ($perPage === 'all') {
            $perPage = $brandsQuery->count();
        }     

        $brands = $brandsQuery->orderBy('brand_name', 'asc')->paginate($perPage, ['*'], 'page', $page);

       

        $response = [
            'success' => true,
            'data' => $brands->items(),//$sales->items(),LeadResource::collection($sales), // Get the paginated items
            'pagination' => [
                'total' => $brands->total(),
                'per_page' => $brands->perPage(),
                'current_page' => $brands->currentPage(),
                'last_page' => $brands->lastPage(),
                'from' => $brands->firstItem(),
                'to' => $brands->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/brands",
     *      summary="createBrands",
     *      tags={"Brands"},
     *      description="Create Brands",
      *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Brands")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Brands"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateBrandsAPIRequest $request)
    {
        $input = $request->all();

        $brands = $this->brandsRepository->create($input);

        return $this->sendResponse($brands->toArray(), 'Brands saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/brands/{id}",
     *      summary="getBrandsItem",
     *      tags={"Brands"},
     *      description="Get Brands",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Brands",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Brands"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Brands $brands */
        $brands = $this->brandsRepository->find($id);

        if (empty($brands)) {
            return $this->sendError('Brands not found');
        }

        return $this->sendResponse($brands->toArray(), 'Brands retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/brands/{id}",
     *      summary="updateBrands",
     *      tags={"Brands"},
     *      description="Update Brands",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Brands",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Brands")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Brands"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateBrandsAPIRequest $request)
    {
        $input = $request->all();

        /** @var Brands $brands */
        $brands = $this->brandsRepository->find($id);

        if (empty($brands)) {
            return $this->sendError('Brands not found');
        }

        $brands = $this->brandsRepository->update($input, $id);

        return $this->sendResponse($brands->toArray(), 'Brands updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/brands/{id}",
     *      summary="deleteBrands",
     *      tags={"Brands"},
     *      description="Delete Brands",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Brands",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Brands $brands */
        $brands = $this->brandsRepository->find($id);

        if (empty($brands)) {
            return $this->sendError('Brands not found');
        }

        $brands->delete();

        return $this->sendSuccess('Brands deleted successfully');
    }
}
