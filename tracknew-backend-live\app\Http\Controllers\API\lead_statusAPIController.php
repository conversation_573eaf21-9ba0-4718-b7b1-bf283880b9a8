<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\Createlead_statusAPIRequest;
use App\Http\Requests\API\Updatelead_statusAPIRequest;
use App\Models\lead_status;
use App\Repositories\lead_statusRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class lead_statusController
 * @package App\Http\Controllers\API
 */

class lead_statusAPIController extends AppBaseController
{
    /** @var  lead_statusRepository */
    private $leadStatusRepository;

    public function __construct(lead_statusRepository $leadStatusRepo)
    {
        $this->leadStatusRepository = $leadStatusRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/lead_statuses",
     *      summary="getlead_statusList",
     *      tags={"lead_status"},
     *      description="Get all lead_statuses",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/lead_status")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $leadsQuery = lead_status::where('company_id', $companyId);

        if ($perPage === 'all') {
            $perPage = $leadsQuery->count();
        }

        $leads = $leadsQuery->orderBy('name', 'asc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $leads->items(), // Get the paginated items
            'pagination' => [
                'total' => $leads->total(),
                'per_page' => $leads->perPage(),
                'current_page' => $leads->currentPage(),
                'last_page' => $leads->lastPage(),
                'from' => $leads->firstItem(),
                'to' => $leads->lastItem(),
            ],
        ];
        
        return response()->json($response);

    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/lead_statuses",
     *      summary="createlead_status",
     *      tags={"lead_status"},
     *      description="Create lead_status",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/lead_status")
     *        
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/lead_status"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(Createlead_statusAPIRequest $request)
    {
        $input = $request->all();

        $leadStatus = $this->leadStatusRepository->create($input);

        return $this->sendResponse($leadStatus->toArray(), 'Lead Status saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/lead_statuses/{id}",
     *      summary="getlead_statusItem",
     *      tags={"lead_status"},
     *      description="Get lead_status",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of lead_status",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/lead_status"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var lead_status $leadStatus */
        $leadStatus = $this->leadStatusRepository->find($id);

        if (empty($leadStatus)) {
            return $this->sendError('Lead Status not found');
        }

        return $this->sendResponse($leadStatus->toArray(), 'Lead Status retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/lead_statuses/{id}",
     *      summary="updatelead_status",
     *      tags={"lead_status"},
     *      description="Update lead_status",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of lead_status",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/lead_status")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/lead_status"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, Updatelead_statusAPIRequest $request)
    {
        $input = $request->all();

        /** @var lead_status $leadStatus */
        $leadStatus = $this->leadStatusRepository->find($id);

        if (empty($leadStatus)) {
            return $this->sendError('Lead Status not found');
        }

        $leadStatus = $this->leadStatusRepository->update($input, $id);

        return $this->sendResponse($leadStatus->toArray(), 'lead_status updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/lead_statuses/{id}",
     *      summary="deletelead_status",
     *      tags={"lead_status"},
     *      description="Delete lead_status",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of lead_status",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var lead_status $leadStatus */
        $leadStatus = $this->leadStatusRepository->find($id);

        if (empty($leadStatus)) {
            return $this->sendError('Lead Status not found');
        }

        $leadStatus->delete();

        return $this->sendSuccess('Lead Status deleted successfully');
    }
}
