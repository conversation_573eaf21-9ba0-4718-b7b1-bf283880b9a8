<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateWarehouseAPIRequest;
use App\Http\Requests\API\UpdateWarehouseAPIRequest;
use App\Models\Warehouse;
use App\Repositories\WarehouseRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class WarehouseController
 * @package App\Http\Controllers\API
 */

class WarehouseAPIController extends AppBaseController
{
    /** @var  WarehouseRepository */
    private $warehouseRepository;

    public function __construct(WarehouseRepository $warehouseRepo)
    {
        $this->warehouseRepository = $warehouseRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/warehouses",
     *      summary="getWarehouseList",
     *      tags={"Warehouse"},
     *      description="Get all Warehouses",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Warehouse")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
                $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
       
        $Query = Warehouse::where('company_id', $companyId);


        if ($perPage === 'all') {
            $perPage = $Query->count();
        }    

        $result = $Query->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $result->items(), // Get the paginated items
            'pagination' => [
                'total' => $result->total(),
                'per_page' => $result->perPage(),
                'current_page' => $result->currentPage(),
                'last_page' => $result->lastPage(),
                'from' => $result->firstItem(),
                'to' => $result->lastItem(),
            ]
        ];

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/warehouses",
     *      summary="createWarehouse",
     *      tags={"Warehouse"},
     *      description="Create Warehouse",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Warehouse")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Warehouse"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateWarehouseAPIRequest $request)
    {
        $input = $request->all();

        $warehouse = $this->warehouseRepository->create($input);

        return $this->sendResponse($warehouse->toArray(), 'Warehouse saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/warehouses/{id}",
     *      summary="getWarehouseItem",
     *      tags={"Warehouse"},
     *      description="Get Warehouse",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Warehouse",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Warehouse"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Warehouse $warehouse */
        $warehouse = $this->warehouseRepository->find($id);

        if (empty($warehouse)) {
            return $this->sendError('Warehouse not found');
        }

        return $this->sendResponse($warehouse->toArray(), 'Warehouse retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/warehouses/{id}",
     *      summary="updateWarehouse",
     *      tags={"Warehouse"},
     *      description="Update Warehouse",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Warehouse",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Warehouse")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Warehouse"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateWarehouseAPIRequest $request)
    {
        $input = $request->all();

        /** @var Warehouse $warehouse */
        $warehouse = $this->warehouseRepository->find($id);

        if (empty($warehouse)) {
            return $this->sendError('Warehouse not found');
        }

        $warehouse = $this->warehouseRepository->update($input, $id);

        return $this->sendResponse($warehouse->toArray(), 'Warehouse updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/warehouses/{id}",
     *      summary="deleteWarehouse",
     *      tags={"Warehouse"},
     *      description="Delete Warehouse",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Warehouse",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Warehouse $warehouse */
        $warehouse = $this->warehouseRepository->find($id);

        if (empty($warehouse)) {
            return $this->sendError('Warehouse not found');
        }

        $warehouse->delete();

        return $this->sendSuccess('Warehouse deleted successfully');
    }
}
