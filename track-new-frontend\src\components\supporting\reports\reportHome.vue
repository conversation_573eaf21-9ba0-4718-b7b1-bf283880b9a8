<template>
    <div class="text-sm p-2">
        <div class="py-2 flex justify-between items-center">
            <div class="flex justify-center items-center">
                <label>SearchBy:</label>
                <input v-model="searchTerm" class="px-2 border border-gray-400 rounded mx-3 py-2 w-full" />
            </div>
            <div class="flex justify-between items-center space-x-4">
                <button @click="exportToXLS" class="btn btn-blue">
                    <font-awesome-icon icon="fa-solid fa-file-excel" size="lg" />
                    <!-- <span class="text-[10px] block text-center">XLSX</span> -->
                </button>
                <button @click="exportToPDF" class="btn btn-red">
                    <font-awesome-icon icon="fa-solid fa-file-pdf" size="lg" />
                    <!-- <span class="text-[10px] block text-center">PDF</span> -->
                </button>
            </div>
        </div>
        <div>
            <vue3-table-lite :is-static-mode="true" :columns="table.columns" :rows="table.rows"
                :total="table.totalRecordCount" :sortable="table.sortable"></vue3-table-lite>
        </div>
        <div class="text-right py-2">
            <div v-for="(total, key) in totals" :key="key">
                <span class="font-bold">{{ key }}: </span>{{ currentCompanyList && currentCompanyList.currency === 'INR'
                    ? '\u20b9' : currentCompanyList.currency }} {{ key == 'payments' ?
                    formatPayment(total) : total }}
            </div>
            <p v-if="only_sales_service >= 0 && selected_option === 'Sales'"><span class="font-bold">Service
                    Total:</span> {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                        currentCompanyList.currency }} {{
                    only_sales_service.toFixed(2) }}</p>
        </div>
    </div>
</template>

<script>
import { defineComponent, reactive, ref, computed, watch } from "vue";
import Vue3TableLite from "vue3-table-lite";
import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
import { jsPDF } from 'jspdf';
import 'jspdf-autotable';

export default defineComponent({
    name: "Datatable",
    components: { Vue3TableLite },
    props: {
        isMobile: Boolean,
        isAndroid: Boolean,
        table_data: {
            type: Array,
            required: true
        },
        columns_data: {
            type: Array,
            required: true
        },
        selected_option: {
            type: String,
            required: true
        },
        currentCompanyList: Object,
        filtered_by: Object,
        only_sales_service: Number,
    },
    setup(props) {
        const searchTerm = ref("");
        const recordsPerPage = ref(20);
        // Search text
        // Reactive data based on props
        const data = reactive({ rows: [...props.table_data] });
        const columns = reactive([...props.columns_data]);
        // Watch for changes in table_data and columns_data props
        watch(
            () => props.table_data,
            (newData) => {
                data.rows = [...newData];
            }
        );

        watch(
            () => props.columns_data,
            (newColumns) => {
                columns.length = 0;
                columns.push(...newColumns);
            }
        );

        // Reactive data based on props
        // const data = reactive([...props.table_data]);
        // for (let i = 0; i < 126; i++) {
        //     data.push({
        //         id: i,
        //         name: "TEST" + i,
        //         email: "test" + i + "@example.com",
        //     });
        // }
        const filteredRows = computed(() => {
            if (props.selected_option === 'Services' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        // x.id.includes(searchTerm.value.toLowerCase()) ||
                        x.customer.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        // x.problem_title.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.category.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        // x.expected_date.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        // x.service_code.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.assign_to.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.service_type.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.invoice_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        // x.grand_total.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        // x.paid.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        // x.due.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.status.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else if (props.selected_option === 'Sales' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.customer.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.invoice_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.invoice_to.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.invoice_type.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.due_in.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.payment_status.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.status.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else if (props.selected_option === 'Proforma' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.customer.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.proforma_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.invoice_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.estimation_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.due_in.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else if (props.selected_option === 'Estimation' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.customer.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.proforma_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.invoice_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.estimation_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.due_in.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            }
            else if (props.selected_option === 'Purchase' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.supplier.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.purchase_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.due_interval.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            }
            else if (props.selected_option === 'Leads' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.customer.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.title.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.assign_to.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.type.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.follow_up.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.status.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else if (props.selected_option === 'AMC' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.customer.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.title.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.assign_to.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.type.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.num_of_service.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.status.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else if (props.selected_option === 'Expense' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.date.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.purpose.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.type.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else if (props.selected_option === 'Stock' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.product.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else if (props.selected_option === 'RMA' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.customer.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.product.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.serial_no.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.complete_date.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
                        x.status.toLowerCase().includes(searchTerm.value.toLowerCase())
                );
            } else if (props.selected_option === 'Customer Ledger' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.date.toLowerCase().includes(searchTerm.value) ||
                        x.transaction_type.includes(searchTerm.value) ||
                        x.transaction_no.includes(searchTerm.value) ||
                        x.original_invoice_no.includes(searchTerm.value) ||
                        x.payment_mode.includes(searchTerm.value)
                );
            } else if (props.selected_option === 'Outstanding' && data.rows.length > 0) {
                return data.rows.filter(
                    (x) =>
                        x.name.toLowerCase().includes(searchTerm.value) ||
                        x.contact_number.includes(searchTerm.value) ||
                        (x.address && x.address.includes(searchTerm.value)) ||
                        (x.gst_number && x.gst_number.includes(searchTerm.value))
                );
            } else {
                return [];
            }
        });
        // Table config
        const table = reactive({
            columns: columns,
            rows: filteredRows,
            totalRecordCount: computed(() => filteredRows.value.length),
            sortable: {
                order: "id",
                sort: "asc",
            },
        });
        // Table config
        // const table = reactive({
        //     columns: props.columns_data,
        //     rows: filteredRows,
        //     totalRecordCount: computed(() => filteredRows.value.length),
        //     sortable: {
        //         order: "id",
        //         sort: "asc",
        //     },
        // });
        //     [
        //     {
        //         label: "ID",
        //         field: "id",
        //         width: "3%",
        //         sortable: true,
        //         isKey: true,
        //     },
        //     {
        //         label: "Name",
        //         field: "name",
        //         width: "10%",
        //         sortable: true,
        //     },
        //     {
        //         label: "Email",
        //         field: "email",
        //         width: "15%",
        //         sortable: true,
        //     },
        // ],
        //     rows: computed(() => {                
        //         return data.filter(
        //             (x) =>
        //                 x.email.toLowerCase().includes(searchTerm.value.toLowerCase()) ||
        //                 x.name.toLowerCase().includes(searchTerm.value.toLowerCase())
        //         );
        //     }),
        //     totalRecordCount: computed(() => {
        //         return table.rows.length;
        //     }),
        //     sortable: {
        //         order: "id",
        //         sort: "asc",
        //     },
        // });
        const totals = computed(() => {
            if (props.selected_option === 'Services' && data.rows.length > 0) {
                const totalObj = {
                    grand_total: 0,
                    paid: 0,
                    due: 0,
                };
                filteredRows.value.forEach(row => {
                    totalObj.grand_total += parseFloat(row.grand_total) || 0;
                    totalObj.paid += parseFloat(row.paid) || 0;
                    totalObj.due += parseFloat(row.due) || 0;
                });
                return totalObj;
            } else if (props.selected_option === 'Sales' && data.rows.length > 0) {
                const totalObj = {
                    grand_total: 0,
                    paid: 0,
                    due: 0,
                    payments: {}
                };
                filteredRows.value.forEach(row => {
                    totalObj.grand_total += parseFloat(row.grand_total) || 0;
                    totalObj.due += parseFloat(row.due_amount) || 0;
                    totalObj.paid += parseFloat(row.grand_total - row.due_amount) || 0;
                    // Assuming the payment info is stored as a string like "Cash: 600, Online: 7400, Card: 1000, Other: 1000"
                    // Validate if row.payment is neither 0 nor empty
                    const paymentInfo = row.payment && row.payment !== '0' && row.payment !== '' ? row.payment : null;

                    if (paymentInfo) {
                        const paymentTypes = paymentInfo.split(',').map(payment => payment.trim());

                        paymentTypes.forEach(payment => {
                            const [type, amount] = payment.split(':').map(str => str.trim());
                            const paymentAmount = parseFloat(amount) || 0;

                            // Dynamically handle any payment type
                            if (!totalObj.payments[type.toLowerCase()]) {
                                totalObj.payments[type.toLowerCase()] = 0; // Initialize if not already present
                            }
                            totalObj.payments[type.toLowerCase()] += paymentAmount;
                        });
                    }
                });
                return totalObj;
            } else if (props.selected_option === 'Proforma' && data.rows.length > 0) {
                const totalObj = {
                    payment_total: 0,
                };
                filteredRows.value.forEach(row => {
                    totalObj.payment_total += parseFloat(row.advance_paid) || 0;
                });
                return totalObj;
            }
            else if (props.selected_option === 'Purchase' && data.rows.length > 0) {
                const totalObj = {
                    grand_total: 0,
                    paid: 0,
                    due: 0,
                };
                filteredRows.value.forEach(row => {
                    totalObj.grand_total += parseFloat(row.grand_total) || 0;
                    totalObj.due += parseFloat(row.due) || 0;
                    totalObj.paid += parseFloat(row.paid) || 0;
                });
                return totalObj;
            }
            else if (props.selected_option === 'Expense' && data.rows.length > 0) {
                const totalObj = {
                    grand_total: 0,
                };
                filteredRows.value.forEach(row => {
                    totalObj.grand_total += parseFloat(row.amount) || 0;
                });
                return totalObj;
            }
            else if (props.selected_option === 'RMA' && data.rows.length > 0) {
                const totalObj = {
                    grand_total: 0,
                };
                filteredRows.value.forEach(row => {
                    totalObj.grand_total += parseFloat(row.grand_total) || 0;
                });
                return totalObj;
            }
            else if (props.selected_option === 'Outstanding' && data.rows.length > 0) {
                const totalObj = { balance_amount: 0 };
                filteredRows.value.forEach(row => {
                    totalObj.balance_amount += parseFloat(row.balance_amount) || 0;
                });
                return totalObj;
            }
        });
        // const exportToXLS = () => {
        //     const ws = XLSX.utils.json_to_sheet(filteredRows.value);
        //     const wb = XLSX.utils.book_new();
        //     XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');
        //     const totalRow = props.selected_option === 'Services' ? [['Total', '', '', '', '', '', totals.value.grand_total, totals.value.paid, totals.value.due]] : props.selected_option === 'Sales' ? [['Total', '', '', '', '', '', totals.value.grand_total, totals.value.due, totals.value.paid]] :
        //         props.selected_option === 'Proforma' ? ['', 'Total', ':', '', '', '', totals.value.payment_total] :
        //             props.selected_option === 'Purchase' ? ['', 'Total', ':', '', '', '', totals.value.payment_total] :
        //                 props.selected_option === 'Expense' ? ['', 'Total', ':', '', totals.value.grand_total] :
        //                     props.selected_option === 'RMA' ? ['', 'Total', ':', '', '', totals.value.grand_total] : [];
        //     XLSX.utils.sheet_add_aoa(ws, totalRow, { origin: -1 });
        //     XLSX.writeFile(wb, `${props.selected_option}.xlsx`);
        // };
        const exportToXLS = () => {
            const sheetData = [];

            // Add the header row (table.columns should be the columns in your table)
            const headerRow = table.columns.map(col => col.label || col.field); // Ensure you're using the column labels or field names
            sheetData.push(headerRow); // Push the header row as the first row

            // Add the rows of data
            filteredRows.value.forEach(row => {
                const rowData = table.columns.map(col => row[col.field] || ''); // Map each row based on columns
                sheetData.push(rowData);
            });

            // Add total row based on selected option
            const totalRow = props.selected_option === 'Services' ? ['Total', '', '', '', '', '', totals.value.grand_total, totals.value.paid, totals.value.due] :
                props.selected_option === 'Sales' ? ['Total', '', '', '', '', '', totals.value.grand_total, totals.value.due, totals.value.paid] :
                    props.selected_option === 'Proforma' ? ['Total', '', '', '', '', '', totals.value.payment_total] :
                        props.selected_option === 'Purchase' ? ['Total', '', '', '', '', '', totals.value.payment_total] :
                            props.selected_option === 'Expense' ? ['Total', '', '', '', totals.value.grand_total] :
                                props.selected_option === 'RMA' ? ['Total', '', '', '', '', totals.value.grand_total] : [];

            // Create header with company and filter details
            const filterDetails = getDynamicFilters(props.filtered_by).map(filter => `${filter.key}: ${filter.value}`).join(', ');
            const header = [
                [`Company Details:`],
                [`${props.currentCompanyList.company_name}`],
                [`${props.currentCompanyList.address}`],
                [`PH: ${props.currentCompanyList.company_phone_no}`],
                [`Email: ${props.currentCompanyList.email}`],
                [`GST: ${props.currentCompanyList.gst_number}`],
                [``, `Filter Details: ${filterDetails}`], // Empty cell before filter details
                [``]
            ];

            let final_data = [...header, ...sheetData, totalRow];
            if (props.selected_option == 'Sales') {
                let create_total = formatPayment(totals.value.payments);
                if (create_total && create_total !== '') {
                    let payment_data = create_total.split(', ');
                    if (payment_data && payment_data.length > 0) {
                        final_data.push(['Total Payment', ...payment_data]);
                    }
                }
            }

            // Add sheet data and total row to the worksheet
            const ws = XLSX.utils.json_to_sheet(final_data);

            // Create workbook and add worksheet
            const wb = XLSX.utils.book_new();
            XLSX.utils.book_append_sheet(wb, ws, 'Sheet1');

            // Save the file
            XLSX.writeFile(wb, `${props.selected_option}.xlsx`);
        };

        // Function to get dynamic filters
        const getDynamicFilters = (filterObject) => {
            const filters = [];
            if (filterObject.from) {
                filters.push({ key: 'From Date', value: filterObject.from });
            }
            if (filterObject.to) {
                filters.push({ key: 'To Date', value: filterObject.to });
            }
            if (filterObject.selected_category && filterObject.selected_category !== 'all') {
                filters.push({ key: 'Category', value: filterObject.selected_category });
            }
            if (filterObject.selected_employee && filterObject.selected_employee !== 'all') {
                filters.push({ key: 'Employee', value: filterObject.selected_employee });
            }
            if (filterObject.supplier && filterObject.supplier !== 'all') {
                filters.push({ key: 'Supplier', value: filterObject.supplier });
            }
            if (filterObject.selected_status >= 0 && filterObject.selected_status !== 'all') {
                filters.push({ key: 'Status', value: filterObject.selected_status });
            }
            if (filterObject.service_type && filterObject.service_type !== 'all') {
                filters.push({ key: 'Service Type', value: filterObject.service_type });
            }
            if (filterObject.customer && filterObject.customer !== '') {
                filters.push({ key: 'Customer', value: filterObject.customer });
            }
            if (filterObject.opening_balance >= 0 && filterObject.opening_balance !== '') {
                filters.push({ key: 'Opening Balance', value: filterObject.opening_balance });
            }
            if (filterObject.closing_balance >= 0 && filterObject.closing_balance !== '') {
                filters.push({ key: 'Closing Balance', value: filterObject.closing_balance });
            }
            return filters;
        };




        // const exportToPDF = () => {
        //     const doc = new jsPDF();
        //     doc.autoTable({
        //         head: [table.columns.map(col => col.label)],
        //         body: [
        //             ...filteredRows.value.map(row => table.columns.map(col => row[col.field])),
        //             props.selected_option === 'Services' ? ['Total', ': ', '', '', '', '', totals.value.grand_total, totals.value.paid, totals.value.due] : props.selected_option === 'Sales' ? ['Total', ': ', '', '', '', '', totals.value.grand_total, totals.value.due, totals.value.paid] :
        //                 props.selected_option === 'Proforma' ? ['', 'Total', ':', '', '', '', totals.value.payment_total] :
        //                     props.selected_option === 'Purchase' ? ['', 'Total', ':', totals.value.grand_total, totals.value.paid, totals.value.due, ''] :
        //                         props.selected_option === 'Expense' ? ['', 'Total', ':', '', totals.value.grand_total] :
        //                             props.selected_option === 'RMA' ? ['', 'Total', ':', '', '', totals.value.grand_total] : []
        //         ],
        //     });
        //     doc.save(`${props.selected_option}.pdf`);
        // };

        const exportToPDF = () => {
            const doc = new jsPDF();
            //---filter data----            
            let filters = [];
            if (props.filtered_by && Object.keys(props.filtered_by).length > 0) {
                filters = getDynamicFilters(props.filtered_by);
            }

            // Prepare the table data
            const tableData = [
                ...filteredRows.value.map(row => table.columns.map(col => row[col.field])),
                props.selected_option === 'Services' ? ['Total', ': ', '', '', '', totals.value.grand_total, totals.value.paid, totals.value.due] :
                    props.selected_option === 'Sales' ? ['Total', ': ', '', '', '', totals.value.grand_total, totals.value.due, totals.value.paid] :
                        props.selected_option === 'Proforma' ? ['Total', ':', '', '', '', totals.value.payment_total] :
                            props.selected_option === 'Purchase' ? ['Total', ':', totals.value.grand_total, totals.value.paid, totals.value.due, ''] :
                                props.selected_option === 'Expense' ? ['Total', ':', '', totals.value.grand_total] :
                                    props.selected_option === 'RMA' ? ['Total', ':', '', '', '', totals.value.grand_total] : []
            ];
            if (tableData && tableData.length > 0 && props.only_sales_service && props.only_sales_service >= 0 && props.selected_option === 'Sales') {
                tableData.push(['Service', 'total Amount', ':', '', '', '', props.only_sales_service.toFixed(2), '', '',]);
            }
            if (props.selected_option == 'Sales') {
                let create_total = formatPayment(totals.value.payments);
                if (create_total && create_total !== '') {
                    let payment_data = create_total.split(', ');
                    if (payment_data && payment_data.length > 0) {
                        tableData.push(['Total Payment', ...payment_data]);
                    }
                }
            }

            // Add the table headers
            const tableHeaders = [table.columns.map(col => col.label)];
            // Calculate the total number of pages based on recordsPerPage
            const totalPages = Math.ceil(tableData.length / recordsPerPage.value);
            // Function to format page information
            const getPageInfo = (pageNumber) => {
                const startIndex = (pageNumber - 1) * recordsPerPage.value + 1;
                const endIndex = Math.min(pageNumber * recordsPerPage.value, tableData.length);
                return `Page ${pageNumber} of ${totalPages}, Showing records ${startIndex} to ${endIndex} of ${tableData.length}`;
            };
            // Customer details to be added to the top of the first page
            const companyDetails = [
                `${props.currentCompanyList.company_name}`,
                `${props.currentCompanyList.address}`,
                `PH: ${props.currentCompanyList.company_phone_no}`,
                `Email: ${props.currentCompanyList.email}`,
                `GST: ${props.currentCompanyList.gst_number}`
            ];
            //---company details---
            let yPositionCom = 15;
            // Function to add wrapped text
            const addWrappedText = (text, x, y, maxWidth) => {
                let lines = doc.splitTextToSize(text, maxWidth);
                lines.forEach((line, index) => {
                    doc.text(line, x, y + ((index + 1) * 5));
                    if (index > 0) {
                        yPositionCom += 5
                    }
                });
            };
            // Add autoTable configuration
            const autoTableConfig = {
                head: tableHeaders,
                body: tableData,
                startY: 80, // Start table at Y-position 60 after the company details
                showHead: 'everyPage', // Ensure the table headers are shown on every page
                styles: {
                    fontSize: 8, // Set default font size
                },
                headStyles: {
                    fontStyle: 'bold', // Bold font for headers
                    fontSize: 8, // Set header font size to 10
                },
                bodyStyles: {
                    fontStyle: 'normal', // Normal font for body
                    fontSize: 8, // Set body font size to 10
                },
                didDrawPage: function (data) {
                    if (data.pageNumber === 1) {
                        // Add company details
                        // doc.text('Company Details:', 14, 10); // Adjust the position as needed
                        // companyDetails.forEach((line, index) => {
                        //     if (index == 0) {
                        //         doc.setFontSize(12);
                        //         doc.setFont('helvetica', 'bold'); // Set font to bold
                        //     } else {
                        //         doc.setFont('helvetica', 'normal'); // Reset font to normal
                        //         doc.setFontSize(10);
                        //     }
                        //     doc.text(line, 14, 15 + (index * 5)); // Adjust the position as needed
                        // });
                        // Add company details                        

                        companyDetails.forEach((line, index) => {
                            if (index == 0) {
                                doc.setFontSize(12);
                                doc.setFont('helvetica', 'bold'); // Set font to bold
                                addWrappedText(line, 14, yPositionCom, 120);
                                yPositionCom += 5;
                            }
                            else {
                                doc.setFont('helvetica', 'normal'); // Reset font to normal
                                doc.setFontSize(10);
                                addWrappedText(line, 14, yPositionCom, 120); // Wrap text within 140 units
                                yPositionCom += 5; // Adjust the vertical position for the next line
                            }
                        });
                        // Party Ledger Report
                        doc.setFontSize(12);
                        doc.setFont('helvetica', 'bold');
                        doc.text(`${props.selected_option} Report`, 140, 20);
                        doc.setFontSize(10);
                        //---draw line---------
                        // Draw the top border for filter details
                        const filterSectionStartY = 53;
                        const filterSectionWidth = 180; // Width of the filter details section
                        const borderHeight = 0; // Height of the border (0 for top border only)

                        // Draw the top border
                        doc.setDrawColor(128, 128, 128); // Black border color
                        doc.setLineWidth(0.3); // Border thickness
                        doc.line(14, filterSectionStartY - 5, 14 + filterSectionWidth, filterSectionStartY - 5); // Draw the top border line
                        // Add filter details
                        // doc.text('Filter Details:', 14, 40); // Adjust the position as needed
                        if (filters.length > 0) {
                            let yPosition = 55;
                            let xposition = 140;
                            const lineHeight = 5; // Vertical space between lines
                            let find_customer = filters.find(opt => opt.key == 'Customer');
                            if (find_customer) {
                                doc.text('To:', 14, 55); // Adjust the position as needed
                                doc.text(find_customer.value, 14, 60);
                            }
                            // filters.forEach((filter, index) => {
                            //     doc.text(`${filter.key}: ${filter.value}`, 14, 45 + (index * 5)); // Adjust the position as needed
                            // });
                            // Add filter details as a single line
                            // const filterDetails = filters.map(filter => `${filter.key}: ${filter.value}`).join(', ');
                            // doc.text('Filter Details: ' + filterDetails, 14, 50); // Adjust the position as needed
                            filters.forEach((filter, index) => {
                                const keyValue = `${filter.key}: ${filter.value}`;
                                if (index % 2 === 0 && filter.key != 'Customer') {
                                    // Print pairs for the current line
                                    doc.setFont('helvetica', 'normal');
                                    doc.text(keyValue, xposition, yPosition);
                                    yPosition += lineHeight;
                                } else if (filter.key != 'Customer') {
                                    doc.setFont('helvetica', 'normal');
                                    doc.text(keyValue, xposition, yPosition); // Adjust X position for the second pair on the same line
                                    yPosition += lineHeight; // Move to the next line after two pairs
                                }
                            });
                        }
                    }
                    // Add page information on every page
                    doc.setFontSize(10);
                    doc.setFont('helvetica', 'normal');
                    doc.text(getPageInfo(data.pageNumber), 14, doc.internal.pageSize.height - 10);
                }
            };

            // Add autoTable to document
            doc.autoTable(autoTableConfig);

            // Save the PDF file
            doc.save(`${props.selected_option}.pdf`);
            /*
                        // Add page information to the document
                        doc.text(getPageInfo(1), 14, 10); // Add page information at position (14, 10)
                        // AutoTable configuration
                        const autoTableConfig = {
                            head: tableHeaders,
                            body: tableData,
                            startY: 20, // Start table at Y-position 20 after the page information
                            showHead: 'everyPage', // Ensure the table headers are shown on every page
                            didDrawPage: function (data) {
                                // Add page information on every page except the first
                                if (data.pageNumber > 1) {
                                    doc.text(getPageInfo(data.pageNumber), 14, 10); // Add page information at position (14, 10)
                                }
                            }
                        };
                        // Add autoTable to document
                        doc.autoTable(autoTableConfig);
            
                        // Add record count and total records count at the end of the document
                        const lastPageInfo = `Total Records: ${tableData.length}`;
                        doc.text(lastPageInfo, 14, doc.internal.pageSize.height - 10); // Add last page information at the bottom
            
                        // Save the PDF file
                        doc.save(`${props.selected_option}.pdf`);*/
        };
        const formatPayment = (paymentData) => {
            // Check if paymentData is an object (e.g., { cash: 600, online: 7400, card: 1000, other: 1000 })
            if (typeof paymentData === 'object' && paymentData !== null) {
                return Object.entries(paymentData)
                    .map(([type, amount]) => `${capitalize(type)}: ${amount}`)
                    .join(', ');
            } else {
                return paymentData; // Return the data as is if it's not an object
            }
        };
        const capitalize = (str) => {
            return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase(); // Capitalize the first letter
        };

        return {
            searchTerm,
            table,
            totals,
            formatPayment,
            capitalize,
            exportToXLS,
            exportToPDF,
        };
    },

});
</script>
<style scoped>
.btn {
    @apply bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded;
}

.btn-red {
    @apply bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded;
}
</style>