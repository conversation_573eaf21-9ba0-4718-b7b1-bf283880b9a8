<template>
    <div v-if="showModal"
        class="fixed top-0 right-0 bottom-0 left-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
        <!-- Modal -->
        <div class="bg-white w-full sm:max-w-sm mx-4 sm:mx-0 rounded-md shadow-lg overflow-hidden" ref="modalContainer">
            <!-- Modal Header -->
            <div class="flex justify-between bg-blue-500 p-2 text-white">
                <p class="px-2">JOB Sheet</p>
                <button @click="closeModal" class="text-white hover:text-red-700 focus:outline-none">
                    <span class="material-icons">close</span>
                </button>
            </div>
            <!---message-->
            <div v-if="messageData" class="m-3">
                <p class="font-bold">{{ messageData }} <span style='font-size:30px;'>&#127881;</span></p>
            </div>
            <div class="p-4 flex">
                <button @click="toggleShareMenu"
                    class="border rounded  rounded-xl flex justify-center items-center py-2 px-2 hover:border-green-700 hover:text-green-700">
                    <span class="material-icons mr-2">share</span>
                    Share
                </button>
                <!-- Share menu -->
                <div v-show="showShareMenu" class="absolute mt-12 w-48 bg-white overflow-visible shadow-xl"
                    ref="shareMenu">
                    <button @click="shareViaEmail"
                        class="flex items-center w-full px-4 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none">
                        <span class="material-icons mr-2">email</span>
                        Email
                    </button>
                    <button @click="shareViaWhatsApp"
                        class="flex items-center w-full px-4 py-2 text-gray-700 hover:bg-gray-100 focus:outline-none">
                        <span class="material-icons mr-2">chat</span>
                        WhatsApp
                    </button>
                </div>

                <button @click="viewJobsheet"
                    class="ml-4 mr-4 border rounded  rounded-xl flex justify-center items-center py-2 px-2 hover:border-green-700 hover:text-green-700">
                    <span class="material-icons mr-2">visibility</span>
                    View
                </button>
                <button @click="downloadJobsheet"
                    class="border rounded  rounded-xl flex justify-center items-center py-2 px-2 hover:border-green-700 hover:text-green-700">
                    <span class="material-icons mr-2">download</span>
                    Download
                </button>
            </div>

        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        item_data: Object,
        companyId: String,
        userId: String,
        messageData: String
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            showShareMenu: false,
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-Modal');
                this.message = '';
                this.showShareMenu = false;
            }, 300);
        },
        viewJobsheet() {
            if (this.item_data) {
                console.log(this.item_data);
                let link_data = `https://api.track-new.com/api/view-job-sheet/${this.item_data.service_code}`;

                // Open the link in a new tab
                window.open(link_data, '_blank');
            }
        },
        downloadJobsheet() {
            if (this.item_data) {
                let link_data = `https://api.track-new.com/api/job-sheet/${this.item_data.service_code}`;

                // Create an anchor element
                let anchor = document.createElement('a');
                anchor.href = link_data;
                anchor.setAttribute('download', 'job_sheet'); // Set the download attribute to trigger a download
                anchor.style.display = 'none';

                // Append the anchor to the document body and click it programmatically
                document.body.appendChild(anchor);
                anchor.click();

                // Cleanup: remove the anchor from the document body
                document.body.removeChild(anchor);
            }
        },
        toggleShareMenu() {
            this.showShareMenu = !this.showShareMenu;
            if (this.showShareMenu) {
                // Add click event listener to close share menu on outside click
                document.addEventListener('click', this.closeShareMenuOutsideClick);
            } else {
                // Remove click event listener
                document.removeEventListener('click', this.closeShareMenuOutsideClick);
            }
        },
        shareViaEmail() {
            if (this.item_data) {
                this.showShareMenu = !this.showShareMenu;
                let link_data = `https://api.track-new.com/api/view-job-sheet/${this.item_data.service_code}`;
                window.open(`mailto:?subject=Check out this job sheet&body=${encodeURIComponent(link_data)}`);
            }
        },
        shareViaWhatsApp() {
            if (this.item_data) {
                this.showShareMenu = !this.showShareMenu;
                let link_data = `https://api.track-new.com/api/view-job-sheet/${this.item_data.service_code}`;
                window.open(`https://web.whatsapp.com/send?text=${encodeURIComponent(link_data)}`);
            }
        },
        closeShareMenuOutsideClick(event) {
            // Check if the click is outside the share menu and the modal container
            console.log(this.$refs.shareMenu, 'EEEEEEE');
            if (this.$refs.shareMenu && !this.$refs.shareMenu.contains(event.target) && this.$refs.modalContainer && !this.$refs.modalContainer.contains(event.target)) {
                this.showShareMenu = false;
                // Remove click event listener
                document.removeEventListener('click', this.closeShareMenuOutsideClick);
            }
        }
    }
}
</script>

<style scoped>
.container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    /* Three columns with equal width */
    grid-gap: 10px;
    /* Gap between grid items */
}

.container>div {
    padding: 10px;
    /* Add padding to grid items */
}

/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>