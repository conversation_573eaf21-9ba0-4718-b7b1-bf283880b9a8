<!-- DeleteConfirmationModal.vue -->
<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="bg-white overflow-hidden max-w-md mx-auto px-2 shadow-lg">
                <div class="flex justify-end items-center my-2 text-xl font-medium">
                    <font-awesome-icon icon="fa-regular fa-circle-xmark" color="red" @click="cancel"
                        class="cursor-pointer" />
                </div>
                <div class="px-6 py-2">
                    <form @submit.prevent="handleSubmit">
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="customer-name">{{ type ===
                                'payment_out' ? 'Supplier' : 'Customer' }}
                                Name</label>
                            <input id="customer-name" v-model="formValues.customer" type="text"
                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                placeholder="Enter customer name" readonly />
                        </div>
                        <div class="mb-1">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="amount">Amount</label>
                            <input id="amount" v-model="formValues.payment_amount" type="number"
                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                placeholder="Enter amount" required readonly />
                        </div>
                        <!---due amount-->
                        <div v-if="sales_data && this.formValues.payment_amount >= 0 && sales_data.sales"
                            class="flex justify-between py-2">
                            <p class="text-red-600">Due:
                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                    currentCompanyList.currency }}
                                {{ sales_data.sales.due_amount >= 0 &&
                                    ((sales_data.sales.due_amount + sales_data.payment_amount) - formValues.payment_amount)
                                    > 0 ?
                                    ((sales_data.sales.due_amount + sales_data.payment_amount) - formValues.payment_amount)
                                    : 0 }}</p>
                            <p class="text-green-600">Return:
                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                    currentCompanyList.currency }}
                                {{ sales_data.sales.due_amount >= 0 &&
                                    (formValues.payment_amount - (sales_data.sales.due_amount + sales_data.payment_amount))
                                    > 0 ?
                                    (formValues.payment_amount - (sales_data.sales.due_amount + sales_data.payment_amount))
                                    : 0 }}</p>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="payment-date">Payment
                                Date</label>
                            <input id="payment-date" v-model="formValues.payment_date" type="date" v-datepicker
                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                required readonly />
                        </div>
                        <div>
                            <label for="paymentType" class="text-sm block font-semibold text-gray-500">Payment
                                Type:</label>
                            <select id="paymentType" v-model="formValues.payment_type"
                                class="border border-gray-300 rounded w-full py-2 px-3 leading-tight focus:outline-none focus:shadow-outline">
                                <option
                                    v-if="currentInvoice && currentInvoice.length > 0 && currentInvoice[0].payment_opt && Array.isArray(JSON.parse(currentInvoice[0].payment_opt))"
                                    v-for="(opt, index) in JSON.parse(currentInvoice[0].payment_opt)" :value="opt.type"
                                    :key="index">
                                    {{ opt.type }}</option>
                            </select>
                        </div>
                        <div class="mb-4">
                            <label class="block text-gray-700 text-sm font-bold mb-2" for="note">Note</label>
                            <textarea id="note" v-model="formValues.payment_notes"
                                class="shadow appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                placeholder="Enter any notes"></textarea>
                        </div>
                        <div class="flex items-center justify-end">
                            <button type="button" @click="closeModal"
                                class="bg-gray-400 hover:bg-gray-500 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline mr-2">
                                Cancel
                            </button>
                            <button type="button" @click="handleSubmit"
                                class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                Submit
                            </button>
                        </div>
                    </form>
                </div>

            </div>
        </div>
        <!-- </div> -->
        <Loader :showModal="circle_loader"></Loader>
    </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex';
export default {
    name: 'Payment_in',
    props: {
        showModal: Boolean,
        sales_data: Object,
        type: String,
        currentCompanyList: Object
    },
    data() {
        return {
            'overlay-active': this.showModal,
            isOpen: false,
            circle_loader: false,
            formValues: {
                customer: '',
                payment_amount: '',
                payment_date: '',
                payment_notes: '',
                payment_type: ''
            },
            formData: {},
        }
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        cancel(data) {
            // this.$emit('onCancel');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            if (data && data.id) {
                setTimeout(() => {
                    this.$emit('onCancel', data);
                }, 300);
            } else {
                setTimeout(() => {
                    this.$emit('onCancel');
                }, 300);
            }
        },
        handleSubmit() {
            console.log(this.sales_data, 'EEEEEEEEEEEEEEEEEEEEEEEEEE');
            if (this.sales_data && Object.keys(this.sales_data).length > 0 && this.formValues.payment_amount && this.formValues.payment_amount > 0) {
                let send_data = {};
                if (this.type === 'payment_in') {
                    send_data = {
                        payment_amount: this.formValues.payment_amount, payment_date: this.formValues.payment_date, payment_type: this.formValues.payment_type, payment_notes: this.formValues.payment_notes ? this.formValues.payment_notes : '',
                        sales_id: this.sales_data.sales.id,
                        due_amount: this.sales_data.sales.due_amount >= 0 &&
                            ((this.sales_data.sales.due_amount + this.sales_data.payment_amount) - this.formValues.payment_amount)
                            > 0 ?
                            ((this.sales_data.sales.due_amount + this.sales_data.payment_amount) - this.formValues.payment_amount)
                            : 0,
                        return_amount: this.sales_data.sales.due_amount >= 0 &&
                            (this.formValues.payment_amount - (this.sales_data.sales.due_amount + this.sales_data.payment_amount))
                            > 0 ?
                            (this.formValues.payment_amount - (this.sales_data.sales.due_amount + this.sales_data.payment_amount))
                            : 0,
                        balance_amount: this.sales_data.sales.due_amount >= 0 &&
                            ((this.sales_data.sales.due_amount + this.sales_data.payment_amount) - this.formValues.payment_amount)
                            > 0 ?
                            ((this.sales_data.sales.due_amount + this.sales_data.payment_amount) - this.formValues.payment_amount)
                            : 0,
                    }
                    axios.put(`/sales_payments/${this.sales_data.id}`, { ...send_data })
                        .then(response => {
                            // console.log(response.data, 'update response...!');
                            this.open_loader = false;
                            this.closeModal(response.data);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                        })
                } else if (this.type === 'payment_out') {
                    send_data = {
                        payment_amount: this.formValues.payment_amount, payment_date: this.formValues.payment_date, payment_type: this.formValues.payment_type, payment_notes: this.formValues.payment_notes ? this.formValues.payment_notes : '',
                        supplier_id: this.sales_data.supplier.id,
                        purchaseorder_id: this.sales_data.purchase.id,
                        return_amount: this.sales_data.purchase.due_amount >= 0 &&
                            (this.formValues.payment_amount - (this.sales_data.purchase.due_amount + this.sales_data.payment_amount))
                            > 0 ?
                            (this.formValues.payment_amount - (this.sales_data.purchase.due_amount + this.sales_data.payment_amount))
                            : 0,
                        balance_amount: this.sales_data.purchase.due_amount >= 0 &&
                            ((this.sales_data.purchase.due_amount + this.sales_data.payment_amount) - this.formValues.payment_amount)
                            > 0 ?
                            ((this.sales_data.purchase.due_amount + this.sales_data.payment_amount) - this.formValues.payment_amount)
                            : 0,
                        due_amount: this.sales_data.purchase.due_amount >= 0 &&
                            ((this.sales_data.purchase.due_amount + this.sales_data.payment_amount) - this.formValues.payment_amount)
                            > 0 ?
                            ((this.sales_data.purchase.due_amount + this.sales_data.payment_amount) - this.formValues.payment_amount)
                            : 0,
                    }
                    axios.put(`/purchase_order_payments/${this.sales_data.id}`, { ...send_data })
                        .then(response => {
                            console.log(response.data, 'update response...!');
                            this.open_loader = false;
                            this.closeModal(response.data.data);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader = false;
                        })

                }


            }
        },
        closeModal(data) {
            this.cancel(data);
        },
        //---get current data--
        getCurrentDate() {
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                // console.log(newValue, 'WWWWWWWWWWWWWWWWWWWWWWWW');
                this.isOpen = newValue;
                this.circle_loader = false;
            }, 100);
        },
        sales_data: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'WWWWWWWWWWWWWWWWWWWW');
                if (newValue && Object.keys(newValue).length > 0) {
                    if (newValue.customer && this.type === 'payment_in') {
                        this.formValues.customer = newValue.customer.first_name + '' + (newValue.customer.last_name ? ' ' + newValue.customer.last_name : '') + ' - ' + newValue.customer.contact_number;
                    }
                    if (newValue.supplier && this.type === 'payment_out') {
                        this.formValues.customer = newValue.supplier.name + ' - ' + newValue.supplier.contact_number;
                    }
                    if (newValue.sales) {
                        this.formData.balance_amount = newValue.sales.due_amount;
                        this.formData.invoice_id = newValue.sales.invoice_id;
                    }
                    if (newValue.purchase) {
                        this.formData.balance_amount = newValue.purchase.due_amount;
                        this.formData.purchaseorder_id_id = newValue.purchase.id;
                    }
                    this.formValues.payment_amount = newValue.payment_amount;
                    this.formValues.payment_notes = newValue.payment_notes;
                    this.formValues.payment_type = newValue.payment_type;
                }
                if (this.formValues.payment_date === '') {
                    this.formValues.payment_date = this.getCurrentDate();
                }
            }
        }

    },
    mounted() {
        if (this.currentInvoice && this.currentInvoice.length > 0) {
            if (!this.formValues.payment_type && this.currentInvoice[0].payment_opt && Array.isArray(JSON.parse(this.currentInvoice[0].payment_opt))) {
                let find_payment_type = JSON.parse(this.currentInvoice[0].payment_opt).find(opt => opt.status === true);
                if (find_payment_type) {
                    this.formValues.payment_type = find_payment_type.type;
                }
            }
            this.fetchInvoiceSetting();
        } else {
            this.fetchInvoiceSetting();
        }
        if (this.formValues.payment_date === '') {
            this.formValues.payment_date = this.getCurrentDate();
        }
    }
};
</script>

<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        width: 90%;
        /* Adjust the width as needed */
        max-width: 320px;
        /* Set a maximum width for smaller screens */
    }

    .modal-content {
        padding: 1rem;
        /* Add padding to the content for better spacing */
    }


}
</style>