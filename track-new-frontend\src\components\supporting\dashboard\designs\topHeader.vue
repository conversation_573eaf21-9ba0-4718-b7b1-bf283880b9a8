<template>
    <div>
        <div class="p-1 sm:p-3 md:p-4 lg:p-5 bg-white flex justify-between items-center">
            <div class="flex">
                <!-- <button class="block text-xl px-2 sm:px-4 py-2 text-black">
                    ☰
                </button> -->
                <div
                    class="h-[18px] sm:h-[18px] text-left center text-md sm:text-lg items-center flex justify-center p-5">
                    Dashboard
                </div>
            </div>
            <div class="grid gap-2 items-center text-[#1e857b]"
                :class="{ 'grid-cols-4': featureisEnable('Services') === false && featureisEnable('Estimate') === false, 'grid-cols-3': featureisEnable('Services') === false || featureisEnable('Estimate') === false, 'grid-cols-3': featureisEnable('Services') || featureisEnable('Estimate') }">
                <!--Services-->
                <div v-if="!featureisEnable('Services') && !checkPlanDetails('Services')"
                    class="text-center cursor-pointer hover:bg-gray-100 py-1" @click="addService">
                    <div>
                        <p class="text-center"><font-awesome-icon icon="fa-solid fa-wrench" rotation=270 /></p>
                        <p class="text-center ml-6 -mt-[10px]"><font-awesome-icon icon="fa-solid fa-plus" /></p>
                    </div>
                    <p :class="{ 'text-xs': !isMobile, 'text-[10px]': isMobile }">Services</p>
                </div>
                <!--Products-->
                <div class="text-center cursor-pointer hover:bg-gray-100 py-1" @click="addItem">
                    <div>
                        <p class="text-center"><font-awesome-icon icon="fa-solid fa-tag" rotation=90 /></p>
                        <p class="text-center ml-6 -mt-[10px]"><font-awesome-icon icon="fa-solid fa-plus" /></p>
                    </div>
                    <p :class="{ 'text-xs': !isMobile, 'text-[10px]': isMobile }">Products</p>
                </div>
                <!--Customers-->
                <div class="text-center cursor-pointer hover:bg-gray-100 py-1" @click="addCustomer">
                    <div>
                        <p class="text-center"><font-awesome-icon icon="fa-solid fa-user" /></p>
                        <p class="text-center ml-6 -mt-[10px]"><font-awesome-icon icon="fa-solid fa-plus" /></p>
                    </div>
                    <p :class="{ 'text-xs': !isMobile, 'text-[10px]': isMobile }">Customers</p>
                </div>

                <!--Estimate-->
                <div v-if="!featureisEnable('Estimate')" class="text-center cursor-pointer hover:bg-gray-100 py-1"
                    @click="createNewEstimateimation">
                    <div>
                        <p class="text-center"><font-awesome-icon icon="fa-solid fa-file-lines" /></p>
                        <p class="text-center ml-6 -mt-[10px]"><font-awesome-icon icon="fa-solid fa-plus" /></p>
                    </div>
                    <p :class="{ 'text-xs': !isMobile, 'text-[10px]': isMobile }">Estimate</p>

                </div>
            </div>
            <!--services-->
            <serviceCategory :showModal="open_service_category" @close-modal="closeCategory"
                @updateModalclose="updateModalclose" :page="'dashboard'" @updateMode="updateStatus">
            </serviceCategory>
            <!---add new item-->
            <addNewItem :showModal="open_add_item" @close-modal="closeAddNewItemModal"></addNewItem>
            <!--customer register-->
            <customerRegister :show-modal="showModal_customer" @close-modal="closeModal"></customerRegister>
        </div>
        <div v-if="!isMobile" class="px-8 py-2 border text-sm">
            <div class="flex">
                <p @click="reloadThePage" class="cursor-pointer"><font-awesome-icon icon="fa-solid fa-house" /> <span
                        class="ml-2">Home</span>
                </p>
                <p class="px-2 text-gray-400">/</p>
                <p class="text-gray-400">Dashboard</p>
            </div>
        </div>
    </div>
</template>
<script>
import serviceCategory from '../../dialog_box/serviceCategory.vue';
import addNewItem from '../../dialog_box/addNewItem.vue';
import customerRegister from '../../dialog_box/customerRegister.vue';
export default {
    emits: ['updateIsOpen'],
    props: {
        isMobile: Boolean,
        currentFeatureList: Object,
        updateModalOpen: Boolean,
        currentCompanyList: Object,
    },
    components: {
        serviceCategory,
        addNewItem,
        customerRegister
    },
    data() {
        return {
            open_service_category: false,
            open_add_item: false,
            showModal_customer: false,
            //---service navigation----
            serviceIsGo: false,
        };
    },
    methods: {
        reloadThePage() {
            // window.location.reload();
            this.$router.go(0);
        },
        addService() {
            this.open_service_category = true;
        },
        addItem() {
            this.open_add_item = true;
        },
        addCustomer() {
            this.showModal_customer = true;
        },
        closeCategory(status) {
            this.open_service_category = false;
        },
        //---items---
        closeAddNewItemModal(data) {
            this.open_add_item = false;
        },
        //---customer create new--
        closeModal(data) {
            this.showModal_customer = false;
        },
        createNewEstimateimation() {
            if (window.location.pathname !== '/estimation/product') {
                this.$router.push({
                    name: 'addEstimation',
                    params: { type: 'product' },
                    query: { type: 'add' }
                });
            }
        },
        featureisEnable(type) {
            if (this.currentFeatureList && this.currentFeatureList.length > 0 && type) {
                let find_feature = this.currentFeatureList.find(opt => opt.name == type);
                // console.log(find_feature, 'EEEEEEE');
                if (find_feature && find_feature.hasAccess) {
                    // console.log(find_feature.hasAccess, 'RRRRRRRRRRRRRRRRRR');
                    return false;
                } else {
                    return true;
                }
            } else {
                return false;
            }
        },
        closeAllModals() {
            // Close all modals
            this.open_service_category = false;
            this.open_add_item = false;
            this.showModal_customer = false;
        },
        updateModalclose() {
            this.$emit('updateIsOpen', false);
        },
        updateStatus() {
            this.serviceIsGo = true;
            this.$emit('updateIsOpen', false);
        },
        checkPlanDetails(option) {
            let currentPlan = this.currentCompanyList && this.currentCompanyList.plans ? this.currentCompanyList.plans : null;
            if (['Services', 'Leads', 'amcs', 'rmas'].includes(option) && currentPlan && [12, 13].includes(currentPlan.id)) {
                return true;
            } else { return false; }
        }
    },
    watch: {
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        showModal_customer: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_add_item: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_service_category: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    }
};
</script>