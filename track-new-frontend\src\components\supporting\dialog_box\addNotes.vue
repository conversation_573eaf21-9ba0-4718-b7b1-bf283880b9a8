<template>
    <div v-if="showmodal" class="text-sm fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white p-6 w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isModalOpen, 'scale-0': !isModalOpen }">
            <h2 class="text-xl font-semibold mb-4">{{ lable.label ? lable.label : '' }}</h2>
            <div v-if="lable.field === 'description' || lable.field === 'notes'">
                <textarea v-model="text_data" rows="6" class="w-full border p-2 mb-4 rounded-md"
                    placeholder="Type something..."></textarea>
            </div>
            <!---Assign to--->
            <div v-if="lable.field === 'assign_to'" class="flex items-center my-4">
                <div class="mr-2 w-10" :title="'assign To'">
                    <img :src="assign" alt="assign_to" class="w-7 h-7">
                </div>
                <div class="relative w-full mr-2 bg-white">
                    <label for="assign_to"
                        class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.assign_to || isInputFocused.assign_to, 'text-blue-700': isInputFocused.assign_to }">Assign
                        to</label>
                    <div class="border py-2 px-2 flex flex-wrap"
                        :class="{ 'border-blue-300': isInputFocused.assign_to === true }">

                        <div v-for="(selectedOption, index) in formValues.assign_to" :key="index"
                            class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                            {{ selectedOption.name }}
                            <span @click="removeOption(selectedOption)"
                                class="text-red-500 font-semibold cursor-pointer">x</span>
                        </div>
                        <input type="text" v-model="search" @input="filterOptions" @focus="filterOptions"
                            @click="filterOptions" ref="search" @blur="hideOptions" placeholder="Select staff"
                            class="h-[35px] mt-1 outline-none border rounded-lg px-2"
                            @keydown.enter="handleEnterKey('multiple', filteredOptions())"
                            @keydown.down.prevent="handleDownArrow(filteredOptions())"
                            @keydown.up.prevent="handleUpArrow(filteredOptions())" />
                    </div>
                    <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                        v-if="showOptions" :class="{ 'h-auto': isInputFocused.assign_to === true }">
                        <div v-for="(option, index) in filteredOptions()" :key="index"
                            class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                            :class="{ 'bg-green-300': index === selectedIndex }" @click="selectOptionMultiple(option)">
                            {{ option.name }}
                        </div>
                        <button v-if="showAddNew !== null && search.length > 1"
                            class="bg-green-700 text-white hover:bg-green-600 px-3 py-1 rounded-lg"
                            @click="openModalEmployee">Add New</button>
                    </div>
                </div>
            </div>
            <!-- Buttons: Save and Cancel -->
            <div class="flex justify-end space-x-4">
                <button @click="saveText" class="bg-green-500 text-white px-4 py-2 rounded">Save</button>
                <button @click="closeModal" class="bg-red-500 text-white px-4 py-2 rounded">Cancel</button>
            </div>
        </div>
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    props: {
        showmodal: Boolean,
        lable: {
            type: [Object, String]
        },
        data: Object,
    },
    data() {
        return {
            isModalOpen: false,  // Modal visibility flag
            text_data: '',  // Text area value
            formValues: {},
            isInputFocused: { assign_to: true },
            employeeList: [],
            search: '',
            showOptions: false,
            isMessageDialogVisible: false,
            message: '',
            showModal_employee: false,
            EmployeeName: '',
            showAddNew: false,
            selectedIndex: 0,
            assign: '/images/service_page/personService.png',
        };
    },
    computed: {
        ...mapGetters('employess', ['currentEmployee']),
    },
    methods: {
        ...mapActions('employess', ['fetchEmployeeList']),
        closeModal() {
            this.isModalOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 100);
        },
        saveText() {
            this.isModalOpen = false;
            setTimeout(() => {
                this.$emit('close-modal', this.lable.field === 'assign_to' ? this.formValues.assign_to : this.text_data);
            }, 100);
        },
        //---multiple dropdown---
        filterOptions() {
            // console.log('hello');
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.mobile_number + ''.includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {

                            this.showAddNew = true;
                            return [];
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.mobile_number && option.mobile_number + ''.includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                } else if (enteredValue.length > 1) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                } else {
                    return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList;
                }
            }
            // console.log(this.formValues.assign_to, 'RRRRR');
            // Return an empty array if no options match the filter
            // console.log(this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList);
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList;
        },

        selectOptionMultiple(option) {
            if (!this.formValues.assign_to) {
                this.formValues.assign_to = []; // Initialize assign_to as an array if it's not already
            }
            this.formValues.assign_to.push(option); // Add the selected option to assign_to
            this.search = ''; // Clear the search input
            // this.showOptions = false; // Hide the options dropdown
            this.$nextTick(() => {
                // Focus on the search input after selecting an option
                if (this.$refs['search']) {
                    this.$refs['search'].focus();
                    this.$refs['search'].click();
                }
            });
        },
        removeOption(option) {
            this.formValues['assign_to'] = this.formValues['assign_to'].filter(selected => selected.id !== option.id);
        },

        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
                this.isInputFocused.assign_to = false;
            }, 300); // Add delay to allow click events on options
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
    },
    mounted() {
        if (this.currentEmployee && this.currentEmployee.length > 0) {
            this.employeeList = this.currentEmployee;
        } else {
            this.fetchEmployeeList();
        }
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside);
    },
    watch: {
        showmodal: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    if (this.lable.field !== 'assign_to') {
                        this.text_data = this.data[this.lable.field ? this.lable.field : ''];
                    } else {
                        this.formValues.assign_to = this.data[this.lable.field ? this.lable.field : ''];
                    }
                    setTimeout(() => {
                        this.isModalOpen = newValue;
                    }, 100);
                }
            }
        },
        currentEmployee: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.employeeList = newValue;
                }
            }
        }
    }
};
</script>

<style scoped>
/* Add transition for top-to-bottom animation */
.transition-all {
    transition: all 0.5s ease-in-out;
}
</style>