<template>
    <div class="min-h-screen flex flex-col">
      <header class="bg-gray-800 text-white p-4">
        <nav class="flex justify-between items-center">
          <div class="text-lg font-semibold">Website Builder</div>
          <div class="space-x-4">
            <button @click="preview" class="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded">
            Preview
          </button>
            <router-link to="/" class="hover:underline">Exit</router-link>
           
          
          </div>
        </nav>
      </header>
      <!-- <sidebar-layout> -->
      <main class="flex-1 p-4">
        <slot />
      </main>
          <!-- </sidebar-layout> -->
      <footer class="bg-gray-800 text-white p-4 text-center">
        © {{ new Date().getFullYear() }} TrackNew. All rights reserved.
      </footer>
    </div>
  </template>
  
  <script>
  import SidebarLayout from './sidebar.vue'; 
  export default {
    props: {
      username: {
        type: String,
        required: true,
      },
    },
    components: {
      SidebarLayout,
    },
    methods: {
      logout() {
        console.log('Logging out');
        // Add your logout logic here
      },
      preview() {
        window.open(`https://${this.username}`, '_blank');
      // Add your preview logic here
    },
    },
  };
  </script>
  