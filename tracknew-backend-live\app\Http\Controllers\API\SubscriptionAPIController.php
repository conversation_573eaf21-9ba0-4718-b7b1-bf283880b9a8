<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Plans As Plan;
use App\Models\Gateways AS Gateway;
use App\Models\Orders;
use App\Models\UserGatewaySubscribers;
use App\Models\User;
use App\Models\Coupon;
// use App\Traits\Notifications;
use Session;
use Auth;
use DB;
use Storage;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class SubscriptionApiController extends Controller
{
   // use Notifications;

    public function index()
    {
        $plans = Plan::where('status', 1)->where('is_featured', 0)->where('price', '>', 0)->latest()->get();

        return response()->json([
            'status' => 'success',
            'data' => $plans
        ]);
    }

    public function show($id)
    {
        $plan = Plan::where('status', 1)->where('price', '>', 0)->find($id);

        if (!$plan) {
            return response()->json([
                'status' => 'error',
                'message' => __('Please select a valid plan')
            ], 404);
        }

        $gateways = Gateway::where('status', 1)->get();
        $tax = get_option('tax');
        $tax = $tax > 0 ? ($plan->price / 100) * $tax : 0;
        $total = (double)$tax + $plan->price;
        $invoice_data = get_option('invoice_data', true);

        return response()->json([
            'status' => 'success',
            'data' => compact('plan', 'gateways', 'tax', 'total', 'invoice_data')
        ]);
    }
  
  	public function vaildateVpa(Request $request, $gatewayid)
    {
      $vpa = $request->vpa;
      $gateway = Gateway::where('status', 1)->findOrFail($gatewayid);
   
      return $gateway->namespace::validateVPA($vpa);
      
    }
  
  	public function debitInit()
    {
     	$gateway = Gateway::where('status', 1)->findOrFail(1);
    	$user 	= 	UserGatewaySubscribers::where('user_id', 148)->where('subscribe_state', 'ACTIVE')->first();
   		$url = url('api/debit/success?transaction_id=' . $user->txd);
      	return $gateway->namespace::initDebit($user->subscription_id, 1, $url, $user->txd);
      
    }
  	 public function debitExecute()
    {
     	$gateway = Gateway::where('status', 1)->findOrFail(1);
    	$user 	= 	UserGatewaySubscribers::where('user_id', 148)->where('subscribe_state', 'ACTIVE')->first();
   		$url = url('api/debit/success?transaction_id=' . $user->txd);
      	return $gateway->namespace::debitExecute($user->subscription_id, 1, $url, $user->txd);
      
    }

    public function subscribe(Request $request, $gatewayid, $planid)
    {     
        $plan = Plan::where('status', 1)->where('price', '>', 0)->findOrFail($planid);
        $gateway = Gateway::where('status', 1)->findOrFail($gatewayid);
 
       $tax_value = get_option('tax');

        $tax = $tax_value > 0 ? ($plan->price / 100) * $tax_value : 0;
        $total = (double)$tax + $plan->price;
        $iscoupon = 0;
        $discount = 0;
        $discount_type = null;
      	if ($request->has('code') && !empty($request->code)) {          
              $couponValidation = $this->validateCouponCode($request->code, $plan->price);
              if ($couponValidation['status'] == 'error') {
                  return;
              }
              // Get the total after applying the coupon
              $total_dis = $couponValidation['total_after_discount'];
              // Recalculate the tax based on the discounted price
              $tax_data = $tax_value > 0 ? ($total_dis / 100) * $tax_value : 0;
              // Calculate the new total
              $total = (double)$tax_data + $total_dis;
              // Store the coupon details (if needed)
              $coupon = $couponValidation['coupon'];

              $iscoupon = 1;
              $discount = $couponValidation['dis'];
              $discount_type = $couponValidation['type'];
          }
      
        $payable = $total * $gateway->multiply + $gateway->charge;

        if ($gateway->min_amount > $payable) {
            return response()->json([
                'status' => 'error',
                'message' => __('The minimum transaction amount is :amount', ['amount' => $gateway->min_amount])
            ], 400);
        }
  
        if ($gateway->max_amount != -1 && $gateway->max_amount < $payable) {
            return response()->json([
                'status' => 'error',
                'message' => __('The maximum transaction amount is :amount', ['amount' => $gateway->max_amount])
            ], 400);
        }

        if ($gateway->is_auto == 0) {
            $request->validate([
                'comment' => ['required', 'string', 'max:500'],
                'image' => ['required', 'image', 'max:2048'], // 2MB
            ]);

            $payment_data['comment'] = $request->input('comment');
            if ($request->hasFile('image')) {
                $path = 'uploads' . '/payments' . date('/y/m/');
                $name = uniqid() . "." . $request->file('image')->extension();
                Storage::put($path . $name, file_get_contents($request->file('image')));
                $payment_data['screenshot'] = Storage::url($path . $name);
            }
        }       
      
    	$txd = 'trd' . date('YmdHis') . rand(1000, 9999);
        DB::table('session_payments')->insert([
          'transaction_id' => $txd,
          'user_id' => Auth::id(),
          'plan_id' => $plan->id,
          'gateway_id' => $gateway->id,
          'namespace' => $gateway->namespace,
          'amount' => $payable,
          'is_auto' => $request->is_auto ? 1 :0,
          'created_at' => now(),
          'is_coupon' => $iscoupon,
          'discount' => $discount,
          'discount_type' => $discount_type,
          'coupon' => $coupon ?? null,
        ]);
      
       	$payment_data = [         	
            'currency' => $gateway->currency ?? 'USD',
            'email' => Auth::user()->email ?? '<EMAIL>',
            'name' => Auth::user()->name ?? 'Vadivelan',
            'phone' => $request->mobile_number ?? '9629090020',
            'billName' => 'Plan Name: ' . $plan->title,
            'amount' => $total,
            'test_mode' => $gateway->test_mode,
            'charge' => $gateway->charge ?? 0,
            'pay_amount' => str_replace(',', '', number_format($payable)),
            'getway_id' => $gateway->id,         	
          	'callback_url' => url('api/subscription/plan/success?transaction_id=' . $txd)
        ];

        
        Session::put('plan_id', $plan->id);
        Session::put('user_id', Auth::id());
      	Session::put('gateway_namespace', $gateway->namespace);

        if (!empty($gateway->data)) {
            foreach (json_decode($gateway->data ?? '') ?? [] as $key => $info) {
                $payment_data[$key] = $info;
            }
        }
      	if(isset($request->is_auto) && $request->is_auto){
          	$existingSubscriber = UserGatewaySubscribers::where('user_id', Auth::id())
                ->whereIn('subscribe_state', ['SUSPENDED','REVOKED','CANCELLED','PAUSED','EXPIRED','FAILED','CANCEL_IN_PROGRESS','PENDING'])
                ->exists();

            if ($existingSubscriber) {
                // Create a new record if a subscription with CREATED or ACTIVE status exists
                $userSubscriber = UserGatewaySubscribers::create([
                    'user_id' => Auth::id(),
                    'gateway_id' => $gateway->id,
                    'gateway' => 'PhonePe',
                    'amount' => $payable,
                    'plan_id' => $plan->id,
                    'txd' => $txd
                ]);
            } else {
                // Otherwise, update or create
                $userSubscriber = UserGatewaySubscribers::updateOrCreate(
                    ['user_id' => Auth::id()],
                    [
                        'gateway_id' => $gateway->id,
                        'gateway' => 'PhonePe',
                        'amount' => $payable,
                        'plan_id' => $plan->id,
                       
                    ]
                );
            }

          $vpas = isset($request->vpa) ? $request->vpa : 'test';
          $is_qr  = isset($request->is_qr) ? $request->is_qr : false;
          return $gateway->namespace::init_subscription($payment_data, $userSubscriber->txd, $vpas, $is_qr);
        }
 
       return $gateway->namespace::make_payment($payment_data, $txd);
      
      
    }
  	public function recurringStatus(Request $request, $status)
    {
      $transactionId = $request->query('transaction_id');
      $callbackData = json_decode($request->getContent(), true);
       if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('Failed to decode PhonePe callback JSON', ['error' => json_last_error_msg()]);
            return response()->json(['status' => 'error', 'message' => 'Invalid JSON received'], 400);
        }
      	if (isset($callbackData['response'])) {
          	 
            $decodedResponse = json_decode(base64_decode($callbackData['response']), true);
          	if($decodedResponse['success'] === true && $decodedResponse['code'] === 'SUCCESS'){
               	
    			$payState = $decodedResponse['data']['subscriptionDetails']['state'] ?? 'FAILED';
              	$callType = $decodedResponse['data']['callbackType'] ?? 'AUTH';            
              
                if($callType === 'NOTIFY'){
                    $notificationState = $decodedResponse['data']['notificationDetails']['state'] ?? 'FAILED';
                    if($notificationState === 'NOTIFIED' && $payState === "ACTIVE"){
                        $this->successNotify($decodedResponse, $transactionId);
                    }
                    if($notificationState === 'FAILED' && $payState === "ACTIVE"){                
                        $this->faildNotify($decodedResponse, $transactionId);                
                    }
              	}
              
                if($callType === 'DEBIT'){
                      $transactionState = $decodedResponse['data']['transactionDetails']['state'] ?? 'FAILED';
                      $notificationState = $decodedResponse['data']['notificationDetails']['state'] ?? 'FAILED';
                      if($notificationState === 'NOTIFIED' && $payState === "ACTIVE" && $transactionState === 'COMPLETED'){
                          $this->successDebit($decodedResponse, $transactionId);
                      }
                      if($notificationState === 'NOTIFIED' && $payState === "ACTIVE" && $transactionState === 'FAILED'){                
                          $this->faildDebit($decodedResponse, $transactionId);                
                      }
                }
            }          
        }
    }

    public function status(Request $request, $status)
    {
      
      		//Log::error('Transaction not found for callback');
         $transactionId = $request->query('transaction_id');
  			Log::error('Transaction not found for callback', ['transaction_id' => $transactionId]);
          // Retrieve stored payment data
         
		$paymentSession = DB::table('session_payments')->where('transaction_id', $transactionId)->first();
         
    
      
        

        // Decode the JSON request
        $callbackData = json_decode($request->getContent(), true);
      
        //Log::info('PhonePe Callback received23', ['raw_request' => $callbackData]);

        // Check if the decoding was successful
        if (json_last_error() !== JSON_ERROR_NONE) {
            Log::error('Failed to decode PhonePe callback JSON', ['error' => json_last_error_msg()]);
            return response()->json(['status' => 'error', 'message' => 'Invalid JSON received'], 400);
        }

        // Extract the necessary data from the decoded callback
        if (isset($callbackData['response'])) {
          	 
            $decodedResponse = json_decode(base64_decode($callbackData['response']), true);

            if ($decodedResponse['success'] === true && $decodedResponse['code'] === 'PAYMENT_SUCCESS') {
             
				$response = $paymentSession->namespace::verify_payment($paymentSession->m_txd);  
                
				return $status == 'success' ? $this->success($paymentSession, $decodedResponse) : $this->faild($paymentSession, $decodedResponse);
               
            }
          	if($decodedResponse['success'] === true && $decodedResponse['code'] === 'SUCCESS'){
               	
    			$payState = $decodedResponse['data']['subscriptionDetails']['state'] ?? 'FAILED';
              	$callType = $decodedResponse['data']['callbackType'] ?? 'AUTH';
              
              if($callType === 'AUTH'){
                $transactionState = $decodedResponse['data']['transactionDetails']['state'] ?? 'FAILED';
                
                 if($transactionState === 'COMPLETED' && $payState === "ACTIVE"){
                    $this->success($paymentSession, $decodedResponse);
                  }
                
                  if($transactionState === 'FAILED' && $payState === "FAILED"){                
                    $this->faild($paymentSession, $decodedResponse);                
                  }
                
              }
            
              
             
              
          	}
        }

 
      
     
        
    }
  	public function successNotify($callbackData, $txd)
    {
      	Log::error('success notity');    
    	$subscriptionDetails = $callbackData['data']['subscriptionDetails'] ?? [];
        $notificationDetails = $decodedResponse['data']['notificationDetails'] ?? [];
      	UserGatewaySubscribers::updateOrCreate(
          [             
              'txd' => $txd
          ],
          [           
             
              'notifiaction_id' => $notificationDetails['notificationId'] ?? null,             
              'subscribe_state' => $subscriptionDetails['state'] ?? 'ACTIVE',
              'notification_state' => $notificationDetails['state'] ?? 'NOTIFIED',
              'data' => json_encode($callbackData),
          ]
          );
        
     }
   public function failedNotify($callbackData, $txd)
   {
      	 Log::error('failed notity');
     	 $subscriptionDetails = $callbackData['data']['subscriptionDetails'] ?? [];
         $notificationDetails = $decodedResponse['data']['notificationDetails'] ?? [];
         UserGatewaySubscribers::updateOrCreate(
              [             
                  'txd' => $txd
              ],
              [           

                  'notifiaction_id' => $notificationDetails['notificationId'] ?? null,             
                  'subscribe_state' => $subscriptionDetails['state'] ?? 'ACTIVE',
                  'notification_state' => $notificationDetails['state'] ?? 'FAILED',
                  'data' => json_encode($callbackData),
              ]
          );
   }
  
   	public function successDebit($callbackData, $txd)
    {
      	Log::error('success debit');
      	$subscriptionDetails = $callbackData['data']['subscriptionDetails'] ?? [];
        $notificationDetails = $decodedResponse['data']['notificationDetails'] ?? [];
      	$transactionDetails = $callbackData['data']['transactionDetails'] ?? [];
      	$payData =  UserGatewaySubscribers::updateOrCreate(
              [             
                  'txd' => $txd
              ],
              [           

                  'notifiaction_id' => $notificationDetails['notificationId'] ?? null, 
                  'transaction_state' => $notificationDetails['state'] ?? 'ACTIVE',                	
                  'subscribe_state' => $subscriptionDetails['state'] ?? 'ACTIVE',
                  'notification_state' => $notificationDetails['state'] ?? 'FAILED',
                  'data' => json_encode($callbackData),
              ]
          );
      	$plan = Plan::findOrFail($payData->plan_id);
    	$user = User::findOrFail($payData->user_id);     
        $user->plan = json_encode($plan->data);
        $user->plan_id = $plan->id;
        $user->will_expire = Carbon::now()->addDays($plan->days);
        $user->save();

        $tax = get_option('tax');
        $tax = $tax > 0 ? ($plan->price / 100) * $tax : 0;
      
        $order = new Orders;
        $order->plan_id = $plan->id;
      	$order->price = $plan->price;
        $order->payment_id = $payData->subscription_id;
        $order->user_id = $user->id;
        $order->gateway_id = $payData->gateway_id;
        $order->amount = $payData->amount;
        $order->tax = $tax;
        $order->status = 1;
        $order->will_expire = Carbon::now()->addDays($plan->days);
            
     
            $order->meta = json_encode($callbackData);
        
        $order->save();
    }
   	public function failedDebit($callbackData, $txd)
    {
      	Log::error('failed debit');
      $payDataF  = UserGatewaySubscribers::updateOrCreate(
              [             
                  'txd' => $txd
              ],
              [           

                  'notifiaction_id' => $notificationDetails['notificationId'] ?? null, 
                  'transaction_state' => $notificationDetails['state'] ?? 'FAILED',                	
                  'subscribe_state' => $subscriptionDetails['state'] ?? 'ACTIVE',
                  'notification_state' => $notificationDetails['state'] ?? 'NOTIFIED',
                  'data' => json_encode($callbackData),
              ]
          );
    
      	$plan = Plan::findOrFail($payDataF->plan_id);
    	$user = User::findOrFail($payDataF->user_id);     
        $user->plan = json_encode($plan->data);
        $user->plan_id = $plan->id;
        $user->will_expire = Carbon::now()->addDays($plan->days);
        $user->save();

        $tax = get_option('tax');
        $tax = $tax > 0 ? ($plan->price / 100) * $tax : 0;
      
        $order = new Orders;
        $order->plan_id = $plan->id;
      	$order->price = $plan->price;
        $order->payment_id = $payDataF->subscription_id;
        $order->user_id = $user->id;
        $order->gateway_id = $payDataF->gateway_id;
        $order->amount = $payDataF->amount;
        $order->tax = $tax;
        $order->status = 1;
        $order->will_expire = Carbon::now()->addDays($plan->days);      
        
            $order->meta = json_encode($callbackData);
        
        $order->save();
   	}

    public function success($paymentSession, $callbackData)
    {
      	Log::error('Transaction not found for callbacksssss');
      
        //abort_if(!Session::has('payment_info'), 404);
        //$paymentInfo = Session::get('payment_info');      
      	//Session::forget('payment_info');    
       	//Session::forget('call_back');
      
         // ALTER TABLE `orders` ADD `discount` DOUBLE(10,2) NOT NULL DEFAULT '0' AFTER `type`, ADD `discount_type` VARCHAR(15) NULL DEFAULT NULL AFTER `discount`, ADD `is_coupon` TINYINT(1) NOT NULL DEFAULT '0' COMMENT '0=no, 1=yes' AFTER `discount_type`;
      
        $plan = Plan::findOrFail($paymentSession->plan_id);
    	$user = User::findOrFail($paymentSession->user_id);     
        $user->plan = json_encode($plan->data);
        $user->plan_id = $plan->id;
        $user->will_expire = Carbon::now()->addDays($plan->days);
        $user->save();

        $tax = get_option('tax');
        $tax = $tax > 0 ? ($plan->price / 100) * $tax : 0;
      
        $order = new Orders;
        $order->plan_id = $plan->id;
      	$order->price = $plan->price;
        $order->payment_id = $paymentSession->payment_id;
        $order->user_id = $user->id;
        $order->gateway_id = $paymentSession->gateway_id;
        $order->amount = $paymentSession->amount;
        $order->tax = $tax;
        $order->status = $paymentSession->status ?? 1;
        $order->will_expire = Carbon::now()->addDays($plan->days);      
        if (isset($paymentSession->meta)) {
            $order->meta = json_decode($paymentSession->meta, true);
        }
        $order->is_coupon = $paymentSession->is_coupon; 
        $order->discount = $paymentSession->discount;
        $order->discount_type = $paymentSession->discount_type;
        $order->coupon = $paymentSession->coupon;
        $order->save();
      
      	if($paymentSession->is_auto){
      		$transactionDetails = $callbackData['data']['transactionDetails'] ?? [];
    		$subscriptionDetails = $callbackData['data']['subscriptionDetails'] ?? [];
          

      	UserGatewaySubscribers::updateOrCreate(
          [
              
              'txd' => $paymentSession->transaction_id, // Match by transaction ID
          ],
          [           
             
             
              'transaction_state' => $transactionDetails['state'] ?? 'ACTIVE',
              'subscribe_state' => $subscriptionDetails['state'] ?? 'ACTIVE',
    
              'data' => json_encode($callbackData),
          ]
          );
        }
      
		DB::table('session_payments')->where('transaction_id', $paymentSession->transaction_id)->delete();
        
        return response()->json([
            'status' => 'success',
            'message' => __('Your subscription payment is complete')
        ]);
    }
  
    public function faild($paymentSession, $decodedResponse)
    {	
      
      	Log::error('Transaction not found for callbackFFF');
        $plan_id = Session::get('plan_id');
        Session::forget('payment_info');
        Session::forget('call_back');
        Session::forget('plan_id');
        Log::error('data', [$paymentSession]);
      	if($paymentSession->is_auto){
          	
            $subscriptionDetails = $decodedResponse['data']['subscriptionDetails'] ?? [];
            $transactionDetails = $decodedResponse['data']['transactionDetails'] ?? [];
          
            $userSubscriber = UserGatewaySubscribers::updateOrCreate(
              [   
                'txd' => $paymentSession->transaction_id,
                        
              ],
              [              
              
                'subscribe_state' => $subscriptionDetails['state'] ?? 'FAILED',
                'transaction_state' => $transactionDetails['state'] ?? 'FAILED',
                'data' => json_encode($decodedResponse),
              ]

            );
        }
		DB::table('session_payments')->where('transaction_id', $paymentSession->transaction_id)->delete();
        return response()->json([
            'status' => 'error',
            'message' => __('Payment failed, please try again')
        ], 400);
    }
  
    public function log()
    {
        $orders = Orders::where('user_id', Session::put('user_id'))->with('plan', 'gateway')->latest()->paginate(20);
        return response()->json([
            'status' => 'success',
            'data' => $orders
        ]);
    }
  	public function validateCoupon(Request $request)
    {
        $request->validate([
            'code' => 'required|string', // Ensure coupon code is provided
            'total' => 'required|numeric|min:0', // Ensure total is provided and is a valid number
        ]);

        // Call the method to validate the coupon
        $result = $this->validateCouponCode($request->code, $request->total);

        return response()->json($result);
    }

    private function validateCouponCode($code, $total)
    {
        $coupon = Coupon::where('code', $code)
            ->where('is_active', true)
            ->where('valid_from', '<=', now())
            ->where('valid_to', '>=', now())
            ->first();

        if (!$coupon) {
            return [
                'status' => 'error',
                'message' => __('Invalid or expired coupon code.')
            ];
        }

        $discountAmount = 0;
        if ($coupon->type == 'percentage') {
            $discountAmount = ($total * $coupon->discount) / 100;
        } else {
            $discountAmount = $coupon->discount;
        }

        $totalAfterDiscount = $total - $discountAmount;
      	$discountType = $coupon->type; // 'percentage' or 'fixed'
        $discountValue = (int)$coupon->discount;

        if ($discountType == 'percentage') {
            $formattedDiscountValue = $discountValue . '%'; // Format as percentage (e.g., '10%')
        } else {
            $formattedDiscountValue = $discountValue; // Use as is for fixed (e.g., '10')
        }

        return [
            'status' => 'success',
            'type' => $discountType,
            'dis' => $coupon->discount, 
          	'coupon' => $coupon->code,
            'discount_amount' => $discountAmount,
          	'discount' => $formattedDiscountValue,          	
            'total_after_discount' => $totalAfterDiscount,
            
        ];
    }
}
