<?php

namespace App\Http\Controllers;

use App\DataTables\ServiceAssignsDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateServiceAssignsRequest;
use App\Http\Requests\UpdateServiceAssignsRequest;
use App\Repositories\ServiceAssignsRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class ServiceAssignsController extends AppBaseController
{
    /** @var ServiceAssignsRepository $serviceAssignsRepository*/
    private $serviceAssignsRepository;

    public function __construct(ServiceAssignsRepository $serviceAssignsRepo)
    {
        $this->serviceAssignsRepository = $serviceAssignsRepo;
    }

    /**
     * Display a listing of the ServiceAssigns.
     *
     * @param ServiceAssignsDataTable $serviceAssignsDataTable
     *
     * @return Response
     */
    public function index(ServiceAssignsDataTable $serviceAssignsDataTable)
    {
        return $serviceAssignsDataTable->render('service_assigns.index');
    }

    /**
     * Show the form for creating a new ServiceAssigns.
     *
     * @return Response
     */
    public function create()
    {
        return view('service_assigns.create');
    }

    /**
     * Store a newly created ServiceAssigns in storage.
     *
     * @param CreateServiceAssignsRequest $request
     *
     * @return Response
     */
    public function store(CreateServiceAssignsRequest $request)
    {
        $input = $request->all();

        $serviceAssigns = $this->serviceAssignsRepository->create($input);

        Flash::success('Service Assigns saved successfully.');

        return redirect(route('serviceAssigns.index'));
    }

    /**
     * Display the specified ServiceAssigns.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $serviceAssigns = $this->serviceAssignsRepository->find($id);

        if (empty($serviceAssigns)) {
            Flash::error('Service Assigns not found');

            return redirect(route('serviceAssigns.index'));
        }

        return view('service_assigns.show')->with('serviceAssigns', $serviceAssigns);
    }

    /**
     * Show the form for editing the specified ServiceAssigns.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $serviceAssigns = $this->serviceAssignsRepository->find($id);

        if (empty($serviceAssigns)) {
            Flash::error('Service Assigns not found');

            return redirect(route('serviceAssigns.index'));
        }

        return view('service_assigns.edit')->with('serviceAssigns', $serviceAssigns);
    }

    /**
     * Update the specified ServiceAssigns in storage.
     *
     * @param int $id
     * @param UpdateServiceAssignsRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateServiceAssignsRequest $request)
    {
        $serviceAssigns = $this->serviceAssignsRepository->find($id);

        if (empty($serviceAssigns)) {
            Flash::error('Service Assigns not found');

            return redirect(route('serviceAssigns.index'));
        }

        $serviceAssigns = $this->serviceAssignsRepository->update($request->all(), $id);

        Flash::success('Service Assigns updated successfully.');

        return redirect(route('serviceAssigns.index'));
    }

    /**
     * Remove the specified ServiceAssigns from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $serviceAssigns = $this->serviceAssignsRepository->find($id);

        if (empty($serviceAssigns)) {
            Flash::error('Service Assigns not found');

            return redirect(route('serviceAssigns.index'));
        }

        $this->serviceAssignsRepository->delete($id);

        Flash::success('Service Assigns deleted successfully.');

        return redirect(route('serviceAssigns.index'));
    }
}
