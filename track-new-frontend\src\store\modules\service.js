// store/modules/service.js

const state = {
    search_query: '',
    search_data: {},
  };

  const mutations = {
    SET_SEARCH_QUERY(state, newValue) {
      state.search_query = newValue;
    },
    SET_SEARCH_DATA(state, newValue){
        state.search_data = newValue;
    },
    RESET_STATE(state) {
      state.search_query = '';
        state.search_data = {};        
    },
  };

  const actions = {
    updateSearchQuery({ commit }, newValue) {
      commit('SET_SEARCH_QUERY', newValue);
    },
    updateSearchData({commit}, newValue){
        commit('SET_SEARCH_DATA', newValue);
    }
  };

  const getters = {
    getSearchQuery(state) {
      return state.search_query;
    },
    getSearchData(state){
        return state.search_data;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
  };
