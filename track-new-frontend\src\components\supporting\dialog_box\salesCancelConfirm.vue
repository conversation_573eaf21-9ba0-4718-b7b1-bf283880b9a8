<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden p-4"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="bg-white overflow-hidden max-w-md mx-auto">
                <div class="flex justify-center items-center py-1">
                    <font-awesome-icon icon="fa-solid fa-circle-info" class="text-orange-500 text-3xl cursor-pointer"
                        @click="cancel" />
                </div>
                <div class="py-2">
                    <p class="text-xl font-bold text-center text-neutral-500">Are you sure?</p>
                    <p class="text-center py-1 text-gray-600">Canceling the {{ type === 'estimation' ? 'estimation' :
                        'invoice' }} cannot revert to success</p>
                </div>
                <!-- reason text area value -->
                <div>
                    <label for="reason" class="font-bold">{{ type === 'estimation' ? 'Estimation' :
                        'Invoice' }} cancel reason <span class="text-red-700"> * </span>:</label>
                    <textarea id="reason" ref="reason" v-model="formValues.reason" rows="2"
                        class="p-1 mt-1 border border-gray-300 w-full"></textarea>
                </div>
                <div class="flex justify-center py-2">
                    <button @click="confirm"
                        class="bg-red-600 text-white px-8 py-2 hover:bg-red-500 rounded mr-4">OK</button>
                    <button @click="cancel"
                        class="bg-green-600 text-white px-4 py-2  hover:bg-green-500 rounded ml-4">Cancel</button>
                </div>
            </div>
        </div>
        <!-- </div> -->
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
export default {
    name: 'confirm_dialog',
    props: {
        showModal: Boolean,
        type: String
    },
    emits: ['onConfirm', 'onCancel'],
    data() {
        return {
            'overlay-active': this.showModal,
            isOpen: false,
            formValues: {},
            //--toaster---
            show: false,
            type_toaster: 'warning',
            message: ''
        }
    },
    methods: {
        confirm() {
            if (this.formValues && this.formValues.reason && this.formValues.reason.length > 3) {
                // console.log(this.showModal, 'what happening....');
                this.$emit('onConfirm', this.formValues);
            } else {
                this.message = `Please fill the ${this.type == 'estimation' ? 'estimation' : 'invoice'} cancel reason details...!`;
                this.show = true;
            }
        },
        cancel() {
            // this.$emit('onCancel');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('onCancel');
            }, 300);
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.formValues = {};
                }
            }, 100);
        },
    }
};
</script>

<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        width: 90%;
        /* Adjust the width as needed */
        max-width: 320px;
        /* Set a maximum width for smaller screens */
    }

    .modal-content {
        padding: 1rem;
        /* Add padding to the content for better spacing */
    }


}
</style>