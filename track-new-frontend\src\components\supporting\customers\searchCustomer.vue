<template>
    <div>
        <!--search bar-->
        <div class="relative text-sm">
            <span v-if="searchQuery !== ''" class="absolute items-center text-red-500 pl-2 cursor-pointer"
                :class="{ 'text-md mt-[6px]': !(typePage && typePage === 'headerHome'), 'py-1': (typePage && typePage === 'headerHome') }"
                @click="clearSearchQuery()">
                <font-awesome-icon icon="fa-solid fa-xmark" class="items-center" />
            </span>
            <span v-else class="absolute text-gray-400 items-center pl-2"
                :class="{ 'text-md mt-[6px]': !(typePage && typePage === 'headerHome'), 'py-1': (typePage && typePage === 'headerHome') }">
                <font-awesome-icon icon="fa-solid fa-magnifying-glass" class="items-center" />
            </span>
            <input type="text" :placeholder="typePage === 'headerHome' ? 'Search...' : 'Name or Mobile Number'"
                class="border border-gray-300 rounded  focus:border-blue-500 outline-none"
                :class="{ 'w-full p-1 px-2 pl-8 bg-gray-100 text-gray-700 focus:outline-none': typePage === 'headerHome', 'rounded-lg p-1 py-1 w-full pl-[30px] pr-[8px]': !(typePage && typePage === 'headerHome') }"
                v-model="searchQuery" @input="handleDropdownInput" @change="handleDropdownInput"
                @focus="handleDropdownInput" @blur="closeDropdown"
                @keydown.enter="handleEnterKey(filteredCustomerOptions)"
                @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="searchInput" />
            <!-- Dropdown container -->
            <div v-if="showSuggestions && searchQuery && searchQuery.length > 2"
                class="absolute mt-2 bg-white border rounded shadow-md w-full"
                :style="{ 'z-index': 999, 'max-height': '200px', 'overflow-y': 'auto' }"
                @mousedown.prevent="preventBlur">
                <!-- List of customer suggestions -->
                <ul style="max-height: 200px;">
                    <li v-for="(leads, index) in filteredCustomerOptions" :key="index" :ref="'listItems' + index"
                        @click="selectCustomer(leads)" :class="{ 'bg-gray-300': index === selectedIndex }"
                        class="py-1 px-2 cursor-pointer hover:bg-gray-300">
                        {{ leads.first_name + ' ' + (leads.last_name ? leads.last_name : '') + ' - ' +
                            leads.contact_number }}
                    </li>
                    <li v-if="is_loading && filterBySerial" class="flex justify-center items-center h-full">
                        <div class="animate-spin rounded-full h-12 w-12 border-t-4 border-blue-500 border-opacity-75">
                        </div>
                        <p class="text-blue-600 font-bold space-x-4">Loading</p>
                    </li>
                    <li v-if="filteredData.length > 0 && searchQuery.length > 3 && filterBySerial" class="py-1 px-2">
                        <span class="text-gray-500 text-xs">Searching by services</span>
                    </li>
                    <li v-if="filterBySerial" v-for="(option, index) in filteredData" :key="index"
                        @click="selectServicesOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                        class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                        {{ JSON.parse(option.service_data).serial_number }} - {{ option.service_code }} - {{
                            JSON.parse(option.service_data).problem_title ?
                                Array.isArray(JSON.parse(option.service_data).problem_title) ?
                                    JSON.parse(option.service_data).problem_title.join(', ') :
                                    JSON.parse(option.service_data).problem_title : '' }}
                    </li>
                    <li v-if="filteredCustomerOptions && filteredCustomerOptions.length === 0 && searchQuery && searchQuery.length > 3 && filteredData && filteredData.length === 0"
                        class="py-1 px-2">No
                        Records Found..!</li>
                </ul>
            </div>
        </div>
    </div>
</template>

<script>
import { mapState, mapGetters, mapActions } from 'vuex';

export default {
    props: {
        isMobile: Boolean,
        typePage: String,
        resetSearch: Boolean,
    },
    data() {
        return {
            searchQuery: '',
            filteredCustomerOptions: [],
            showSuggestions: false,
            selectedIndex: 0,
            //----search by serial----
            filterBySerial: false,
            is_loading: false,
            filteredData: [],
            page: 1,
        };
    },
    computed: {
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('servicesSearchSerial', ['currentServiceList']),
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleDocumentClick);
    },
    // mounted() {
    //     this.fetchCustomerList();
    // },
    methods: {
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('servicesSearchSerial', ['fetchServiceList']),
        clearSearchQuery() {
            this.searchQuery = '';
            this.$emit('resetData', {});
            this.filterBySerial = false;
        },
        handleDropdownInput() {
            this.showSuggestions = true;
            if (this.currentCustomer && this.currentCustomer.length === 0) {
                this.fetchCustomerList();
            } else {
                this.fetchCustomerList();
            }
            const inputValue = this.searchQuery;
            if (inputValue && inputValue.length > 3) {
                if (!isNaN(inputValue)) {
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.currentCustomer.filter(option =>
                        option.contact_number.toLowerCase().includes(inputNumber)
                    );
                    // If no customer is found, search by service serial number
                    if (this.filteredCustomerOptions.length === 0 && this.typePage === 'headerHome') {
                        this.onSearchInput();
                    } else {
                        this.filterBySerial = false;
                    }
                } else {
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.currentCustomer.filter(option =>
                        option.last_name
                            ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                            : (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                    );
                    // If no customer is found, search by service serial number
                    if (this.filteredCustomerOptions.length === 0 && this.typePage === 'headerHome') {
                        this.onSearchInput();
                    } else {
                        this.filterBySerial = false;
                    }
                }
            } else {
                this.filteredCustomerOptions = this.currentCustomer.length > 0 ? this.currentCustomer : [];
            }
            document.addEventListener('click', this.handleDocumentClick);
        },
        selectCustomer(lead) {
            this.searchQuery = lead.last_name
                ? lead.first_name + ' ' + lead.last_name + ' - ' + lead.contact_number
                : lead.first_name + ' - ' + lead.contact_number;
            this.showSuggestions = false;
            this.$emit('searchData', lead);
            document.removeEventListener('click', this.handleDocumentClick);
        },
        //---search by serial number---
        onSearchInput() {
            this.filterBySerial = true;
            if (this.searchQuery && this.searchQuery.length > 1) {

                if (!this.currentServiceList.list.pagination && !this.is_loading) {
                    this.is_loading = true;
                    this.fetchServiceList({ id: this.category, page: this.page, per_page: 50 });
                } else if (this.currentServiceList.list.pagination && this.currentServiceList.list.pagination.current_page < this.currentServiceList.list.pagination.last_page && !this.is_loading) {
                    this.is_loading = true;
                    this.fetchServiceList({ id: this.category, page: this.page, per_page: 50 });
                } else {
                    this.filterDataServices(this.currentServiceList);
                }
            } else if (this.searchQuery && this.searchQuery.length === 0) {
                this.filteredData = [];
                // this.page = 1;
            } else if (this.currentServiceList) {
                this.filterDataServices(this.currentServiceList);
            }
        },
        async selectServicesOption(options) {
            if (options) {
                this.$router.push({ name: 'service-data-view', params: { serviceId: options.id, type: options.servicecategory.service_category, id: options.servicecategory.id } });
                this.$emit('refreshPage');
                this.clearSearchQuery();
            }
        },
        filterDataServices(newValue) {
            if (newValue && newValue.list && newValue.list.data && newValue.list.data.length > 0) {
                const query = this.searchQuery.toLowerCase(); // Convert search query to lowercase
                this.filteredData = newValue.list.data.filter(opt => {
                    const serviceData = opt.service_data ? JSON.parse(opt.service_data) : null;
                    const serviceId = opt.service_id ? opt.service_id.toString().toLowerCase() : '';
                    const serviceCode = opt.service_code ? opt.service_code.toString().toLowerCase() : '';
                    const serialNumber = serviceData && serviceData.serial_number ? serviceData.serial_number.toLowerCase() : '';

                    return serviceData && serviceData.serial_number && (
                        serialNumber.includes(query) ||
                        serialNumber === query ||
                        serviceId.includes(query) ||
                        serviceCode.includes(query)
                    );
                });
            }
        },
        handleDocumentClick(event) {
            try {
                // Ensure the reference and event target are defined
                if (this.$refs.searchInput && event.target) {
                    const isClickInside = this.$refs.searchInput.contains(event.target);
                    if (!isClickInside) {
                        this.hideDropdown(); // Call the method to hide the dropdown
                    }
                }
            } catch (error) {
                this.hideDropdown();
            }
        },
        handleEnterKey(optionArray) {
            if (optionArray && optionArray.length > 0) {
                this.selectCustomer(optionArray[this.selectedIndex]);
                this.selectedIndex = 0;
            }
        },
        focusOnItem(index) {
            let refName = `listItems${index}`;
            if (this.$refs[refName] && index >= 0) {
                this.$nextTick(() => {
                    const element = this.$refs[refName][0];
                    if (element) {
                        element.focus();
                        element.scrollIntoView({
                            behavior: 'smooth', // Smooth scrolling effect
                            block: 'nearest',  // Ensures item is visible
                            inline: 'nearest'
                        });
                    }
                });
            }
        },
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex += 1;
                this.focusOnItem(this.selectedIndex);
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
                this.focusOnItem(this.selectedIndex);
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
                this.focusOnItem(this.selectedIndex);
            } else {
                this.selectedIndex -= 1;
                this.focusOnItem(this.selectedIndex);
            }
        },
        hideDropdown() {
            this.showSuggestions = false;
            document.removeEventListener('click', this.handleDocumentClick);
        },
    },
    watch: {
        resetSearch: {
            deep: true,
            handler(newValue) {
                if (newValue !== undefined) {
                    this.clearSearchQuery();
                }
            }
        },
        currentServiceList: {
            deep: true,
            handler(newValue) {
                this.is_loading = false;
                this.page += 1;
                this.filterDataServices(newValue);
            }
        },
        currentCustomer: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.handleDropdownInput();
                }
            }
        }
    }
};
</script>

<style>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}
</style>