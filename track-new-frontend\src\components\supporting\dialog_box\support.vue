<template>
    <div v-if="showModal" class="text-sm fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50">
        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen sm:pb-[150px] lg:pb-[70px] pb-[150px]"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="justify-between items-center flex py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-lg">
                    Create A Ticket</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!-- Content based on selected option -->
            <div class="mt-5 pl-4 pr-2">

                <p class="font-bold text-sm py-2 mt-3">Describe the problem:</p>
                <!-- Description-->
                <div class="flex items-center mt-5">
                    <div class="mr-2 w-10" :title="'description'">
                        <img :src="notes" alt="description" class="w-7 h-7">
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="description"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.description || isInputFocused.description, 'text-blue-700': isInputFocused.description }">Describe
                            the problem</label>
                        <textarea id="description" v-model="formValues.description" rows="2"
                            @focus="isInputFocused.description = true" placeholder="Add description"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"></textarea>
                    </div>
                </div>

                <p class="font-bold text-sm py-2 mt-3">Attachments:</p>
                <!---Attachment-->
                <div class="flex items-center">
                    <div class="mr-2 w-10" :title="'Attachment Image'">
                        <img :src="attachment_img" alt="Attachment Image" class="w-7 h-7">
                    </div>

                    <div class="flex flex-col">
                        <div class="border rounded p-4 mt-2 relative text-sm flex items-center">
                            <!-- Label for the file input -->

                            <label class="absolute top-[-14px] left-2 text-xs font-bold bg-white px-2 text-blue-700"
                                for="fileInput">
                                Attachment
                            </label>

                            <!-- File input -->
                            <input v-if="!formValues.ticket_attachment" id="fileInput" type="file"
                                @change="handleFileUpload" accept="*/*" multiple class="w-full">

                            <!-- Uploaded Image Preview -->
                            <div v-if="formValues.ticket_attachment" class="flex ml-2 items-center cursor-pointer"
                                :title="'Click to view the image'" @click="displayImageModal">
                                <img :src="formValues.ticket_attachment" alt="Uploaded Image"
                                    class="border rounded w-[50px] h-auto">
                            </div>

                            <!-- Loader Circle -->
                            <div v-if="circle_loader" class="absolute inset-0 flex items-center justify-center">
                                <CircleLoader :loading="circle_loader"></CircleLoader>
                            </div>
                        </div>

                        <div v-if="errorMessage" class="text-red-500 text-sm mt-2 lowercase">
                            {{ errorMessage }}
                        </div>

                        <!-- Multiple Attachments Grid -->
                        <div v-if="formValues.ticket_attachments.length"
                            class="grid grid-cols-3 sm:grid-cols-4 lg:grid-cols-6 gap-2 mt-4">
                            <div v-for="(file, index) in formValues.ticket_attachments" :key="index" class="relative">
                                <div v-if="isImage(file)">
                                    <img :src="file" alt="Uploaded Image"
                                        class="w-20 h-20 border rounded cursor-pointer"
                                        @click="displayImageModal(file)">
                                </div>
                                <div v-else>
                                    <img src="/images/icons/document.png" alt="PDF File"
                                        class="w-20 h-20 border rounded cursor-pointer"
                                        @click="displayImageModal(file)">
                                </div>
                                <button @click="removeAttachmentImage(index)"
                                    class="absolute top-0 right-0 bg-red-500 text-white p-1 text-xs rounded-full">
                                    ✕
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Buttons -->
                <div class="fixed bottom-0 left-1/2 transform -translate-x-1/2 bg-white flex justify-center items-center sm:w-1/2 lg:w-1/3 w-full py-1 ease-in-out duration-300"
                    :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen, 'mb-[70px]': isMobile }">

                    <button @click="cancelModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white p-1 py-2 pl-10 pr-10 mr-8">Cancel</button>
                    <button @click="sendModal"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white p-1 py-2 pl-10 pr-10">Save</button>
                </div>

            </div>
        </div>
    </div>
    <displayImage :showModal="showImageModal" :showImageModal="formValues.ticket_attachment"
        @close-modal="closeImageModal">
    </displayImage>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    <Loader :showModal="open_loader"></Loader>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
</template>
<script>
import displayImage from './displayImage.vue';
import confirmbox from './confirmbox.vue';
export default {
    emits: ['close-modal'],
    name: 'support',
    components: {
        displayImage,
        confirmbox,
    },
    props: {
        showModal: Boolean,
        companyId: String,
    },
    data() {
        return {
            message: '',
            'overlay-active': this.showModal,
            isMobile: false,
            isOpen: false,
            isInputFocused: { description: true },
            // formValues: {},
            notes: '/images/service_page/Writing.png',
            attachment_img: '/images/setting_page/Cloud_computing.png',
            table_del: '/images/service_page/del.png',
            circle_loader: false,
            showImageModal: false,
            open_confirmBox: false,
            delete_type: null,
            deleteIndex: null,
            updatedData: null,
            open_loader: false,
            //--toaster--
            show: false,
            type_toaster: 'warning',
            isMessageDialogVisible: false,
            formValues: {
                ticket_attachments: [],
                ticket_attachment_names: [], // This is for storing the image names
                ticket_attachment_name: null, // For a single attachment (if applicable)
            },
            circle_loader: false,
            errorMessage: '',
        }
    },
    methods: {
        cancelModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        async handleFileUpload(event) {
            console.log("event", event);
            event.preventDefault();

            const files = event.target.files;
            if (!files.length) {
                return;
            }
            this.circle_loader = true;
            this.errorMessage = '';
            for (const file of files) {
                const maxSizeBytes = 500 * 1024; // 500 KB
                if (file.size > maxSizeBytes) {
                    this.errorMessage = 'File exceeds 500 KB. please reduce the size.';
                    this.circle_loader = false;
                    return;
                }
                let fileToUpload = file;
                if (file.size <= maxSizeBytes) {
                    await this.uploadImageProfile(fileToUpload);
                }
            }
            this.circle_loader = false;
            this.errorMessage = '';
        },

        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }
                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        uploadImageProfile(file) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "ticket");
            formData.append("company_id", this.companyId);

            axios.post('/image', formData)
                .then(response => {
                    this.circle_loader = false;
                    // Store the image URL in ticket_attachments
                    this.formValues.ticket_attachments.push(response.data.media_url);

                    // Store the image name in ticket_attachment_names
                    this.formValues.ticket_attachment_names.push(response.data.media_name);  // Assuming the response provides a media_name
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                    this.circle_loader = false;
                });
        },

        isImage(file) {
            const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp'];
            return imageExtensions.some(ext => file.toLowerCase().endsWith(ext));
        },
        //---display the image---
        displayImageModal() {
            if (this.formValues.ticket_attachment) {
                this.showImageModal = true;
                // window.open(this.formValues.amc_attachment, '_blank');
            }
        },
        closeImageModal() {
            this.showImageModal = false;
        },
        //---remove attachment--
        removeAttachmentImage(index) {
            console.log("index", index);

            const imageToDelete = this.formValues.ticket_attachments[index];

            if (!imageToDelete) return;

            axios.delete('/delete-image', { params: { model: "ticket", image_url: imageToDelete } })
                .then(() => {
                    // Remove the image from the ticket_attachments array after successful deletion
                    this.formValues.ticket_attachments.splice(index, 1);

                    // Remove corresponding image name
                    if (this.formValues.ticket_attachment_names && this.formValues.ticket_attachment_names.length > index) {
                        this.formValues.ticket_attachment_names.splice(index, 1);
                    }

                    // Clear ticket_attachment_name if no images are left
                    if (this.formValues.ticket_attachments.length === 0) {
                        this.formValues.ticket_attachment_name = null;
                    }

                    // Reset file input field
                    const fileInput = document.getElementById('fileInput');
                    if (fileInput) fileInput.value = '';
                })
                .catch(error => {
                    console.error('Error deleting image:', error);
                    this.$toast.error('Failed to delete image. Please try again.');
                });
        },


        removeProduct(index) {
            this.selectedIndex = index;
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        deleteRecord() {
            if (this.deleteIndex !== null) {
                // Remove the selected attachment from the array
                this.formValues.ticket_attachments.splice(this.deleteIndex, 1);

                // Clear the attachment name if no more images are left
                if (this.formValues.ticket_attachments.length === 0) {
                    this.formValues.ticket_attachment_name = null;  // Clear the attachment name
                }

                // Clear the file input field after deleting the image
                const fileInput = document.getElementById('fileInput');
                if (fileInput) fileInput.reset = '';  // Clear file input

                console.log("reset file")

                this.open_confirmBox = false;
            } else if (this.delete_type === 'url') {
                // Handle deletion from URL if necessary
                axios.delete('/delete-image', { params: { model: "ticket", image_url: this.formValues.ticket_attachment } })
                    .then(() => {
                        this.formValues.ticket_attachment = null;

                        // Clear the attachment name
                        this.formValues.ticket_attachment_name = null;

                        this.open_confirmBox = false;
                    })
                    .catch((error) => {
                        console.error('Error deleting image', error);
                        this.open_confirmBox = false;
                    });
            } else {
                this.open_confirmBox = false;
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
            if (this.delete_type === 'url') {
                this.delete_type = null;
            }
        },
        async sendModal() {
            if (this.formValues.description) {
                const payload = {
                    company_id: this.companyId,
                    description: this.formValues.description,
                    images: this.formValues.ticket_attachments, // Send all images
                };

                axios.post('/tickets', payload)
                    .then(response => {
                        this.openMessageDialog(response.data.message);
                        this.type_toaster = 'success';
                        this.show = true;
                        this.formValues = { ticket_attachments: [] };
                        this.cancelModal();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.openMessageDialog(error.response.data.message);
                        this.type_toaster = 'warning';
                        this.show = true;
                    });
            }
        },

        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },
    },
    mounted() {
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.$nextTick(() => {
                    if (this.$refs.messagetext) {
                        this.$refs.messagetext.focus();
                        this.$refs.messagetext.click();
                    }
                })
            }, 100);
        },
    },

}
</script>
<style scoped>
/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}
</style>