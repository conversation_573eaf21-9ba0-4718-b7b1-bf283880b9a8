<template>
    <div :class="{ 'manualStyle text-sm sm:mb-[40px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        ref="scrollContainer" @scroll="handleScroll">
        <div class="my-custom-margin">
            <!--page header-->
            <div v-if="!isMobile" class="m-1 my-3 flex items-center space-x-4">
                <p class="font-bold text-xl">Expenses</p>
                <div v-if="!open_skeleton"
                    class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                    <p class="text-gray-700">Total Expenses
                        :</p>
                    <p class="font-semibold pl-1">
                        {{ pagination.total }}
                    </p>
                </div>
            </div>
            <!--new design header-->
            <div v-if="!isMobile" class="flex justify-between m-1 mt-3">
                <div class="flex mr-2 space-x-4">
                    <button @click="openExpenseModel" :class="{ 'mr-2': isMobile }"
                        class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center">
                        <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
                        <span v-if="!isMobile" class="text-center">Create</span></button>
                    <!--view-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="px-2 py-1 flex-shrink-0 cursor-pointer info-msg border rounded-lg border-gray-500"
                            :class="{ 'bg-white': items_category === 'tile' }" @click="toggleView"
                            :title02="`Table view`">
                            <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border rounded-lg border border-gray-500"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="`Card view`">
                            <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <!----Filter Model-->
                <!--Setting-->
                <div class="flex icon-color">
                    <button @click="toggleFilterSelected" :class="{ 'mr-2': isMobile }"
                        class="flex items-center border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 px-2 py-1 hover:bg-blue-300 ml-2 rounded-lg">
                        <font-awesome-icon icon="fa-solid fa-filter" size="lg" class="pr-2" />
                        <span v-if="!isMobile">Filter</span>
                    </button>
                </div>
            </div>
            <!---fiter information-->
            <div v-if="Object.keys(filteredBy).length > 0" class="text-xs flex m-4 -mb-3 flex-row overflow-auto">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2">
                    <p class="mr-1">{{ key }} = </p>
                    <p>{{ key === 'expense_type' ? 'type' : value }}</p>
                </div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>
            <!---load skeleton-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton && data.length > 0" class="text-sm m-1 mt-5">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                    </div>
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label === 'Current Date' ? 'Date' : column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="dropdownSetting"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in data" :key="index"
                                    class="cursor-pointer hover:bg-gray-200">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex" @click="startEdit(record)"
                                        :class="{ 'hidden': !column.visible }" class="px-1 py-2 table-border">
                                        <span v-if="column.field === 'date'">{{
                                            validateDateTime(record[column.field]) ?
                                                formatDateTime(record[column.field], true) : '' }}</span>
                                        <span v-if="column.field === 'purpose'">{{ record['name_of_purpose']
                                        }}</span>
                                        <span v-if="column.field === 'type'">{{ record['expense_type'] &&
                                            record['expense_type'].name ?
                                            record['expense_type'].name : '' }}</span>
                                        <span v-if="column.field === 'created_at'" class="text-blue-800"
                                            :title="calculateDaysAgo(formattedDate(record[column.field]))">{{
                                                formatDateTime(formattedDate(record[column.field])) }}</span>
                                        <span
                                            v-if="column.field !== 'date' && column.field !== 'expense_type' && column.field !== 'created_at' && column.field !== 'updated_by'">{{
                                                record[column.field] }}</span>
                                        <!--updatd by-->
                                        <span v-if="column.field === 'updated_by'">{{ record[column.field] &&
                                            record[column.field].name ? record[column.field].name : '' }}
                                            - <span>{{
                                                record[column.field] &&
                                                    record[column.field].mobile_number ?
                                                    record[column.field].mobile_number
                                                    :
                                                    '' }}</span>
                                        </span>
                                    </td>
                                    <td class="px-1 py-2 text-center table-border">
                                        <div class="flex justify-center">
                                            <!--More actions-->
                                            <div class="flex relative">
                                                <button v-if="!record.editing" @click="startEdit(record)" title="Edit"
                                                    class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-solid fa-pencil"
                                                        style="color: #3b82f6;" />
                                                </button>
                                                <button v-if="!record.editing && checkRoles(['admin'])" title="Delete"
                                                    @click="confirmDelete(index)"
                                                    class="text-red-500 px-1 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-regular fa-trash-can"
                                                        style="color: #ef4444" />
                                                </button>
                                                <!-- <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-8 absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">

                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>

                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                    <td v-if="Object.keys(filteredBy).length === 0" colspan="5">
                                        <button
                                            class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="openExpenseModel">
                                            + Expense
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 sm:gap-4 custom-scrollbar-hidden">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <div class="max-w-md mx-auto bg-white rounded-xl border border-gray-300 md:max-w-2xl">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center px-4 py-2">
                                    <!-- Left Side (Can be your dynamic content) -->
                                    <div class="text-xs text-red-500 items-center"
                                        :title="formatDateTime(record['created_at'])">
                                        <p>{{ calculateDaysAgo(formattedDate(record['created_at'])) }}</p>
                                    </div>
                                    <!-- Right Side (Actions) -->
                                    <div class="flex space-x-4 relative items-center">
                                        <!--category-->
                                        <div v-if="record.expense_type.name" class="text-xs">
                                            <p class="bg-gray-300 rounded px-2 py-1">{{ record.expense_type.name }}
                                            </p>
                                        </div>

                                        <div class="flex justify-center">
                                            <!--More actions-->
                                            <div class="relative">
                                                <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button>
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-8 absolute bg-white divide-y divide-gray-100 right-0 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">

                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex px-4">
                                    <p class="pr-1 text-gray-400">Purpose:</p>
                                    <p>{{ record.name_of_purpose }}</p>
                                </div>
                                <!--expense data-->
                                <div class="flex justify-between items-center px-4 py-2">
                                    <!--date data-->
                                    <div>
                                        <p class="pr-1 text-gray-400">Date:</p>
                                        <p>{{ validateDateTime(record['date']) ? formatDateTime(record['date'], true) :
                                            ''
                                        }}
                                        </p>
                                    </div>
                                    <div>
                                        <p class="pr-1 text-gray-400">Amount:</p>
                                        <p>
                                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                '\u20b9' :
                                                currentCompanyList.currency }} {{ record['amount'] }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && this.pagination.last_page > 0 && this.pagination.last_page === this.pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>
                    <!---Filter total expenes-->
                    <div v-if="Object.keys(filteredBy).length > 0 && this.data.length > 0">
                        <p class="font-bold text-md text-center" :class="{ 'mt-3': !isMobile }">Total Amount:
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }} {{
                                getTotalExpenses }}</p>
                    </div>
                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="pagination && !isMobile">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>
                        <ul class="flex list-none">
                            <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                    class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                    <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                    <span class="pl-1" v-if="!isMobile">Prev</span>
                                </button>
                            </li>
                            <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                <button @click="updatePage(pageNumber)"
                                    :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                    class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">
                                    {{ pageNumber }}</button>
                            </li>
                            <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                :class="{ 'bg-gray-500': currentPage === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== pagination.last_page }">
                                <button @click="updatePage(currentPage + 1)"
                                    :disabled="currentPage === pagination.last_page"
                                    class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs">
                                    <span class="pr-1" v-if="!isMobile">Next</span>
                                    <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
            <div v-if="isMobile" class="fixed bottom-36 right-5 z-50 bg-green-700 text-white rounded-full">
                <div class="flex justify-end">
                    <button @click="toggleFilterSelected" type="button"
                        class="flex items-center justify-center px-[10px] py-2">
                        <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                    </button>
                </div>
            </div>
        </div>
        <!---in mobile view create new expense-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openExpenseModel" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <expenseModel :showModal="open_expense_model" :companyId="companyId" :userId="userId"
            @close-modal="closeExpenseModel" :editData="edit_record" :type="expense_type">
        </expenseModel>
        <!--filter-->
        <expenseFilter :showModal="expense_filter" :companyId="companyId" :userId="userId" @closeFilter="closeFilter">
        </expenseFilter>
        <Loader :showModal="open_loader"></Loader>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'expense'"></bottombar> -->
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>
<script>
import confirmbox from '../dialog_box/confirmbox.vue';
import expenseModel from '../dialog_box/expenseModel.vue';
import expenseFilter from '../dialog_box/filter_Modal/expenseFilter.vue';
// import bottombar from '../dashboard/bottombar.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    emits: ['updateIsOpen'],
    components: {
        confirmbox,
        expenseModel,
        expenseFilter,
        // bottombar
    },
    props: {
        isMobile: Boolean,
        companyId: String,
        userId: String,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            filter_icon: '/images/customer_page/filter.png',
            setting_icon: '/images/customer_page/settings.png',
            info_icon: '/images/customer_page/info.png',
            refresh_icon: '/images/customer_page/refresh.png',
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            //---
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            columns: [],
            data: [],
            originalData: [],
            open_message: false,
            message: '',
            //----expense----
            open_expense_model: false,
            expense_type: 'add',
            edit_record: null,
            //---filter--
            expense_filter: false,
            filteredBy: {},
            pagination: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 5,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            now: null,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            open_skeleton_isMobile: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---table data filter asending and decending----
            is_filter: false,
        }
    },

    computed: {
        ...mapGetters('expensesList', ['currentExpensesList']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        paginatedData() {
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                this.length_category = filteredData.length;
                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];
            const order = ['created_at', 'updated_by', 'date', 'purpose', 'type', 'amount'];
            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data) {
                for (const key of order) {
                    if (key !== 'id') {
                        const label = formatLabel(key);
                        if (key !== 'attachment') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        getTotalExpenses() {
            if (!this.data || this.data.length === 0) {
                return 0; // Return 0 if there are no expenses
            }

            // Using parseFloat to ensure proper addition of floating-point numbers
            let total = this.data.reduce((sum, expense) => sum + parseFloat(expense.amount), 0);
            return total;
        }
    },

    methods: {
        ...mapActions('expensesList', ['fetchExpensesList']),
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        validateDateTime(dateTimeString) {
            const datePart = dateTimeString.substring(0, 10);
            const isValidDate = /^\d{4}-\d{2}-\d{2}$/.test(datePart);
            return isValidDate;
        },
        //---get expenses list---
        getExpensesList(page, per_page, is_delete) {
            if (page == 1) {
                this.fetchExpensesList({ page, per_page, is_delete });
                if (this.currentExpensesList && this.currentExpensesList.data) {
                    this.data = this.currentExpensesList.data;
                    this.pagination = this.currentExpensesList.pagination;
                }
            } else {
                this.open_skeleton = true;
                if (this.isMobile && page === 1 && per_page < 20) {
                    per_page = 20;
                }
                axios.get('/expenses', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        // console.log(response.data, 'Response...!');
                        if (Object.keys(this.filteredBy).length === 0) {
                            this.data = response.data.data;
                            this.pagination = response.data.pagination;
                            this.open_skeleton = false;
                        } else {
                            // Apply filters to the response data
                            const filteredData = response.data.data.filter(expense => {
                                // Check if the expense matches the filters
                                let matchesFilters = true;
                                // console.log(this.filteredBy.start_date, 'Helloooo', this.filteredBy.end_date);
                                // Check if start_date and end_date are in between the provided dates
                                if (this.filteredBy.start_date && this.filteredBy.end_date) {
                                    const startDate = new Date(expense.date);
                                    const endDate = new Date(expense.date);
                                    const filterStartDate = new Date(this.filteredBy.start_date);
                                    const filterEndDate = new Date(this.filteredBy.end_date);
                                    matchesFilters = startDate >= filterStartDate && endDate <= filterEndDate;
                                    // console.log(matchesFilters, 'Date filters');
                                }
                                // Check if expense_type matches the filtered value
                                if (this.filteredBy.expense_type !== undefined) {
                                    matchesFilters = matchesFilters && expense.expense_type.id === this.filteredBy.expense_type;
                                    // console.log(matchesFilters, 'expense type filters');
                                }
                                if (this.filteredBy.name_of_purpose !== undefined) {
                                    matchesFilters = matchesFilters && expense.name_of_purpose.toLowerCase().includes(this.filteredBy.name_of_purpose.toLowerCase());
                                    // console.log(matchesFilters, 'expense Name of purposes filters');
                                }

                                return matchesFilters;
                            });
                            // console.log(filteredData, 'What happning...!');
                            // Set the filtered data and pagination
                            this.data = filteredData;
                            this.pagination = response.data.pagination;
                            this.open_skeleton = false;

                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },

        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },

        //---delete the record
        deleteRecord() {
            try {
                if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                    axios.delete(`/expenses/${this.data[this.deleteIndex].id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            this.updateKeyWithTime('expenses_update');
                            // console.log(response.data, 'Response data...!');
                            this.open_confirmBox = false;
                            this.deleteIndex = null;
                            this.getExpensesList((this.data.length > 1 ? this.currentPage - 1 : 1), this.recordsPerPage, true);
                        })
                        .catch(error => {
                            console.error('Error', error);
                        })
                }
            } catch (error) {
                console.error('Error', error);
            }
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.isDropdownOpen || this.items_category === 'tile') {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            try {
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },


        refreshPage() {
            window.location.reload();
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        //---close the message--
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---record---
        startEdit(record) {
            this.edit_record = { ...record };
            this.expense_type = 'edit';
            this.open_expense_model = true;
            // this.display_option = false;
        },

        //------Open filter---
        //---filter--- 
        toggleFilterSelected() {
            this.expense_filter = true;
            this.resetTheFilter();
        },
        //--close lead
        closeFilter(searchData) {
            // console.log(searchData, 'What is happening.....!');
            if (searchData) {
                this.filteredBy = { ...searchData };
                this.getExpensesList(1, 'all');
            }

            this.expense_filter = false;
        },
        //--get formayt date
        formatDate(dateString) {
            // Create a new Date object using the components in the correct order (year, month - 1, day)
            const date = new Date(dateString);
            // Get the parts of the date (year, month, day) in the correct format
            const formattedYear = date.getFullYear();
            const formattedMonth = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-indexed
            const formattedDay = String(date.getDate()).padStart(2, '0');
            // Concatenate the parts with dashes to form the desired format
            return `${formattedMonth}/${formattedDay}/${formattedYear}`;
        },
        //---reset filter--
        resetTheFilter() {
            this.filteredBy = {};
            this.currentPage = 1;
            this.getExpensesList(1, this.recordsPerPage);
        },
        //---open expense modal
        openExpenseModel() {
            this.expense_type = 'add';
            this.open_expense_model = true;
        },
        //--close expense modal
        closeExpenseModel(data) {
            try {
                if (data && data.id) {
                    if (this.expense_type === 'add') {
                        this.data.unshift(data);
                        this.message = 'Expense add successfully..!';
                        this.show = true;
                    } else if (this.expense_type === 'edit') {
                        let find_data = this.data.findIndex(opt => opt.id === data.id);
                        if (find_data !== -1) {
                            this.data.splice(find_data, 1, data);
                            this.message = 'Expense updated successfully..!';
                            this.show = true;
                        }
                    }
                    this.fetchApiUpdates();
                }
                this.open_expense_model = false;
                // console.log('hhhhhhhhh');
            } catch (error) {
                console.error('Error'.error);
                this.open_expense_model = false;
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        formatDateTime(dynamicDate, is_date) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;
            let formattedDateTime = '';
            if (is_date) {
                // Construct the formatted date and time string
                formattedDateTime = `${day}-${month}-${year}`;
            } else {
                // Construct the formatted date and time string
                formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;
            }
            return formattedDateTime;
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5);
                date.setMinutes(date.getMinutes() + 30);
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data);
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            console.log('load nex data.....');
            axios.get('/expenses', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        //---initial store data---
        getInitialStoreData(store_data) {
            if (Object.keys(this.filteredBy).length === 0) {
                this.data = store_data.data;
                this.pagination = store_data.pagination;
                this.open_skeleton = false;
            } else if (store_data && store_data.data && store_data.data.length > 0) {
                // Apply filters to the response data
                const filteredData = store_data.data.filter(expense => {
                    // Check if the expense matches the filters
                    let matchesFilters = true;
                    // console.log(this.filteredBy.start_date, 'Helloooo', this.filteredBy.end_date);
                    // Check if start_date and end_date are in between the provided dates
                    if (this.filteredBy.start_date && this.filteredBy.end_date) {
                        const startDate = new Date(expense.date);
                        const endDate = new Date(expense.date);
                        const filterStartDate = new Date(this.filteredBy.start_date);
                        const filterEndDate = new Date(this.filteredBy.end_date);
                        matchesFilters = startDate >= filterStartDate && endDate <= filterEndDate;
                        // console.log(matchesFilters, 'Date filters');
                    }
                    // Check if expense_type matches the filtered value
                    if (this.filteredBy.expense_type !== undefined) {
                        matchesFilters = matchesFilters && expense.expense_type.id === this.filteredBy.expense_type;
                        // console.log(matchesFilters, 'expense type filters');
                    }
                    if (this.filteredBy.name_of_purpose !== undefined) {
                        matchesFilters = matchesFilters && expense.name_of_purpose.toLowerCase().includes(this.filteredBy.name_of_purpose.toLowerCase());
                        // console.log(matchesFilters, 'expense Name of purposes filters');
                    }

                    return matchesFilters;
                });
                // console.log(filteredData, 'What happning...!');
                // Set the filtered data and pagination
                this.data = filteredData;
                this.pagination = store_data.pagination;
                this.open_skeleton = false;

            }
        },
        //---validate the roles--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'expenses' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'expenses', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'expenses' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'expenses', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        closeAllModals() {
            // Close all modals
            this.open_confirmBox = false;
            this.open_expense_model = false;
            this.expense_filter = false;
        },
        //---sort icons--
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //--refresh page--
        refreshDataTable() {
            // this.open_loader = true;
            this.resetTheFilter();
        }
    },
    mounted() {
        this.fetchApiUpdates();
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        const view = localStorage.getItem('expense_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        this.fetchCompanyList();
        // if (this.companyId) {
        //     this.getExpensesList(this.currentPage, this.recordsPerPage);
        // }
        if (this.currentExpensesList && this.currentExpensesList.data && this.currentExpensesList.data.length > 0) {
            this.open_skeleton = true;
            this.getInitialStoreData(this.currentExpensesList);
            this.fetchExpensesList({ page: 1, per_page: this.recordsPerPage });
        } else {
            if (this.currentExpensesList && Object.keys(this.currentExpensesList).length == 0) {
                this.open_skeleton = true;
                this.fetchExpensesList({ page: 1, per_page: this.recordsPerPage });
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        //---sortIcons---
        const initialShortVisible = ['date', 'purpose', 'type', 'amount'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
        setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
    },
    watch: {
        companyId: {
            deep: true,
            handler(newValue) {
                this.getExpensesList(this.currentPage, this.recordsPerPage);
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    this.getExpensesList(1, newValue);
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                this.getExpensesList(newValue, this.recordsPerPage);
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('expense_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentExpensesList: {
            deep: true,
            handler(newValue) {
                this.getInitialStoreData(newValue);
                this.open_loader = false;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.resetTheFilter();
                this.fetchExpensesList({ page: 1, per_page: this.recordsPerPage });
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_expense_model: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        expense_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    },
}
</script>

<style scoped>
.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
    z-index: 100;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}
</style>