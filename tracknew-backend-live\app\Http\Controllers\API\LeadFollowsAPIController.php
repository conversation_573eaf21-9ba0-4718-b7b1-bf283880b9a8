<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateLeadFollowsAPIRequest;
use App\Http\Requests\API\UpdateLeadFollowsAPIRequest;
use App\Models\LeadFollows;
use App\Repositories\LeadFollowsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class LeadFollowsController
 * @package App\Http\Controllers\API
 */

class LeadFollowsAPIController extends AppBaseController
{
    /** @var  LeadFollowsRepository */
    private $leadFollowsRepository;

    public function __construct(LeadFollowsRepository $leadFollowsRepo)
    {
        $this->leadFollowsRepository = $leadFollowsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/leadFollows",
     *      summary="getLeadFollowsList",
     *      tags={"LeadFollows"},
     *      description="Get all LeadFollows",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/LeadFollows")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $leadFollows = $this->leadFollowsRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($leadFollows->toArray(), 'Lead Follows retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/leadFollows",
     *      summary="createLeadFollows",
     *      tags={"LeadFollows"},
     *      description="Create LeadFollows",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/LeadFollows"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateLeadFollowsAPIRequest $request)
    {
        $input = $request->all();

        $leadFollows = $this->leadFollowsRepository->create($input);

        return $this->sendResponse($leadFollows->toArray(), 'Lead Follows saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/leadFollows/{id}",
     *      summary="getLeadFollowsItem",
     *      tags={"LeadFollows"},
     *      description="Get LeadFollows",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of LeadFollows",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/LeadFollows"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var LeadFollows $leadFollows */
        $leadFollows = $this->leadFollowsRepository->find($id);

        if (empty($leadFollows)) {
            return $this->sendError('Lead Follows not found');
        }

        return $this->sendResponse($leadFollows->toArray(), 'Lead Follows retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/leadFollows/{id}",
     *      summary="updateLeadFollows",
     *      tags={"LeadFollows"},
     *      description="Update LeadFollows",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of LeadFollows",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/LeadFollows"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateLeadFollowsAPIRequest $request)
    {
        $input = $request->all();

        /** @var LeadFollows $leadFollows */
        $leadFollows = $this->leadFollowsRepository->find($id);

        if (empty($leadFollows)) {
            return $this->sendError('Lead Follows not found');
        }

        $leadFollows = $this->leadFollowsRepository->update($input, $id);

        return $this->sendResponse($leadFollows->toArray(), 'LeadFollows updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/leadFollows/{id}",
     *      summary="deleteLeadFollows",
     *      tags={"LeadFollows"},
     *      description="Delete LeadFollows",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of LeadFollows",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var LeadFollows $leadFollows */
        $leadFollows = $this->leadFollowsRepository->find($id);

        if (empty($leadFollows)) {
            return $this->sendError('Lead Follows not found');
        }

        $leadFollows->delete();

        return $this->sendSuccess('Lead Follows deleted successfully');
    }
}
