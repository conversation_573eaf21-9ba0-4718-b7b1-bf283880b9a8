// store/modules/lead_type.js
import axios from "axios";

const state = {
  lead_type: [],
  };

  const mutations = {
    SET_LEADTYPE(state, lead_typeData) {
      state.lead_type = lead_typeData;
    },
    RESET_STATE(state) {
      state.lead_type = [];     
    },
  };

  const actions = {
    updateLeadTypeName({ commit }, lead_typeData) {
      // Simulate an asynchronous operation (e.g., API call) to update lead_type name
      setTimeout(() => {
        // Commit mutation to update lead_type name
        commit('SET_LEADTYPE', lead_typeData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchLeadTypeList({ commit }) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
            axios.get('/lead_types', {params: {company_id: company_id}})
            .then(response => {
              // Handle response
              // console.log(response.data, 'LeadType list..!');
              let lead_type_list = response.data.data;              
              
              commit('SET_LEADTYPE', lead_type_list);
              return lead_type_list;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },    
  };

  const getters = {
    currentLeadType(state) {
      return state.lead_type;
    },    
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
