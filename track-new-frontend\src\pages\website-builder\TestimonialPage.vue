<template>
  <div class="builder-page">
    <div class="flex flex-col md:flex-row justify-between items-center">
      <h1 class="web-head">Manage Testimonials</h1>
      <div class="w-full md:w-auto md:self-end">
        <EnablePageName :pages="is_onSetting" @updatePagesEnabled="handleToggle"></EnablePageName>
      </div>
    </div>
    <!-- Add Testimonial Button -->
    <div class="flex justify-end mb-6">
      <button @click="openModal" class="bg-blue-500 text-white py-2 px-4 rounded shadow">
        Add Testimonial <font-awesome-icon icon="fa-solid fa-plus-circle" />
      </button>
    </div>

    <!-- Testimonials Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="(testimonial, index) in testimonials" :key="index" class="bg-white shadow-lg rounded-lg p-4"
        :draggable="true" @dragstart="onDragStart(index, $event)" @dragover.prevent @drop="onDrop(index, $event)"
        @dragenter="onDragEnter(index, $event)" @dragend="onDragEnd">
        <div class="cursor-pointer" @click="editTestimonial(index)">
          <img v-if="testimonial.customerImageUrl" :src="testimonial.customerImageUrl" alt="Customer Image"
            class="w-full h-32 object-cover rounded-lg mb-4" />
          <h3 class="text-lg font-semibold">{{ testimonial.customerName }}</h3>
          <div class="flex items-center space-x-1 my-2">
            <font-awesome-icon v-for="star in 5" :key="star"
              :icon="star <= testimonial.rating ? 'fas fa-star' : 'far fa-star'" class="text-yellow-500" />
          </div>
          <p class="mb-4">{{ testimonial.review }}</p>
        </div>
        <div class="flex justify-between">
          <button @click="editTestimonial(index)" class="text-blue-500">
            <font-awesome-icon icon="fa-solid fa-edit" />
          </button>
          <button @click="deleteTestimonial(index)" class="text-red-500">
            <font-awesome-icon icon="fa-solid fa-trash" />
          </button>
        </div>
      </div>
    </div>

    <!-- Testimonial Modal -->
    <testimonial-modal :show="showModal" :isEditMode="isEditMode" :testimonial="currentTestimonial"
      :companyId="companyId" @save="handleSaveTestimonial" @close="closeModal" />

    <!-- Navigation Buttons with Finish button -->
    <div class="mt-6">
      <NavigationButtons :pageTitle="'Testimonial Page'" :isFinishPage="true" @goToPrevPage="$emit('goToPrevPage')"
        @finish="finishPage" @preview="openNewTab" />
    </div>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
  </div>
</template>

<script>
import TestimonialModal from './TestimonialModal.vue'; // Modal component for testimonial details
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import imageService from '../../services/imageService';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import EnablePageName from './EnablePageName.vue';
import { mapActions, mapGetters } from 'vuex';

export default {
  props: {
    companyId: {
      type: String,
      required: true
    },
    username: {
      type: String,
      required: true
    },
    testimonialData: {
      type: Array,
      default: () => []
    },
    pages: {
      type: Object,
      required: true
    },
    is_updated: { type: Boolean, required: true },
  },
  components: {
    TestimonialModal,
    FontAwesomeIcon,
    NavigationButtons,
    confirmbox,
    EnablePageName
  },
  data() {
    // console.log(this.testimonialData)
    return {
      testimonials: [], // Copy initial data for testimonials
      showModal: false,
      isEditMode: false,
      currentTestimonial: null, // Stores testimonial data for editing
      currentIndex: null, // Tracks index of testimonial being edited
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      //----is on settings---
      is_onSetting: { is_on: true, name: 'Testmonials' },
      draggedIndex: null,
      currentDraggedElement: null,
    };
  },
  computed: {
    ...mapGetters('websiteBuilder', ['selectedId', 'previewUrl']),
  },
  mounted() {
    if (this.testimonialData) {
      this.testimonials = [...this.testimonialData];
    }
    if (this.pages && this.pages.testimonial !== undefined) {
      this.is_onSetting = this.pages.testimonial;
    }
  },
  methods: {
    openModal() {
      this.isEditMode = false;
      this.currentTestimonial = null;
      this.showModal = true;
    },
    editTestimonial(index) {
      this.isEditMode = true;
      this.currentIndex = index;
      this.currentTestimonial = { ...this.testimonials[index] };
      this.showModal = true;
    },
    handleSaveTestimonial(testimonial) {
      if (this.isEditMode) {
        this.testimonials.splice(this.currentIndex, 1, testimonial); // Works for Vue 3 as well
      } else {
        this.testimonials.push(testimonial);
      }
      this.showModal = false;
      this.$emit('updateTestimonials', [...this.testimonials]);
      this.$emit('updatePagesSetting', { testimonial: this.is_onSetting });
      this.$emit('submitData');
    },
    async deleteTestimonial(index) {
      this.deleteIndex = index;
      this.open_confirmBox = true;
    },
    closeModal() {
      this.showModal = false;

    },
    openNewTab() {
      window.open(this.previewUrl, '_blank');
      this.$emit('submitData');
    },
    finishPage() {
      this.$emit('submitData');
    },
    //--file delete actions--
    async deleteRecord() {
      this.$emit('updateLoader', true);
      const testimonial = this.testimonials[this.deleteIndex];
      if (testimonial.customerImageUrl) {
        try {
          await imageService.deleteImage(testimonial.customerImageUrl);
          this.testimonials.splice(this.deleteIndex, 1);
          this.closeconfirmBoxData();
          this.$emit('toasterMessages', { msg: 'Testmonial details removed successfully', type: 'success' });
        } catch (error) {
          console.error("Error deleting customer image:", error);
          if (error.response.data.error === 'Image not found') {
            this.testimonials.splice(this.deleteIndex, 1);
          }
          this.closeconfirmBoxData();
          this.$emit('toasterMessages', { msg: 'Testmonial cannot found', type: 'warning' });
        }
      } else {
        this.testimonials.splice(this.deleteIndex, 1);
        this.closeconfirmBoxData();
        this.$emit('toasterMessages', { msg: 'Testmonial cannot found', type: 'warning' });
      }
    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
      this.open_loader = false;
    },
    //--handle switch--
    handleToggle(data) {
      // Perform any additional actions when toggled
      if (data) {
        this.is_onSetting = data;
      }
    },
    // Handle the drag start event
    onDragStart(index, event) {
      this.draggedIndex = index;
      this.currentDraggedElement = event.target;
      this.currentDraggedElement.classList.add('dragging'); // Optional visual feedback
    },

    // Handle the drag enter event (when dragged item enters another item)
    onDragEnter(index, event) {
      event.preventDefault();
      const targetElement = event.target;
      if (this.draggedIndex !== index) {
        targetElement.classList.add('drag-over'); // Highlight the target element
      }
    },

    // Handle the drop event to reorder the list
    onDrop(targetIndex, event) {
      event.preventDefault();
      if (this.draggedIndex !== targetIndex) {
        const movedTestimonial = this.testimonials.splice(this.draggedIndex, 1)[0];
        this.testimonials.splice(targetIndex, 0, movedTestimonial);
      }
      this.resetDragState(event);
    },

    // Handle the drag end event (reset visual feedback)
    onDragEnd(event) {
      this.resetDragState(event);
    },

    // Reset the drag state
    resetDragState(event) {
      if (this.currentDraggedElement) {
        this.currentDraggedElement.classList.remove('dragging'); // Remove the dragging class
      }
      const targetElement = event.target;
      targetElement.classList.remove('drag-over'); // Remove the drag-over class
    },
  },
  watch: {
    is_updated: {
      deep: true,
      handler(newValue) {
        this.$emit('updateTestimonials', [...this.testimonials]);
        this.$emit('updatePagesSetting', { testimonial: this.is_onSetting });
      }
    },
    is_onSetting: {
      deep: true,
      handler(newValue) {
        this.$emit('updatePagesSetting', { testimonial: newValue });
      }
    },
    testimonialData: {
      deep: true,
      handler(newValue) {
        this.testimonials = [...newValue];
      }
    }
  }
};
</script>

<style scoped>
/* Add additional styles if needed */
</style>
