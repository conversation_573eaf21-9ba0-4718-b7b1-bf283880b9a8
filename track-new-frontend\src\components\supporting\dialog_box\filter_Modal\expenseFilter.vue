<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-display relative"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex justify-between items-center relative w-full px-4 py-3 set-header-background">
                <p for="leadFilter" class="text-white font-bold text-center text-xl flex justify-end ml-3">
                    Filter
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block mt-1 text-sm bg pl-4 pr-4 pb-4">
                <!---Services Expenses-->
                <div class="items-center mt-5">
                    <div v-if="formValues.name_of_purpose" class="flex justify-end ">
                        <button @click="resetTheValue('name_of_purpose')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="name_of_purpose"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.name_of_purpose || isInputFocused.name_of_purpose, 'text-blue-700': isInputFocused.name_of_purpose }"
                            @click="isInputFocused.name_of_purpose = true">Name
                            Of The Purpose</label>
                        <input type="text" v-model="formValues.name_of_purpose"
                            @input="formValues.name_of_purpose = formValues.name_of_purpose.replace(/#/g, '')"
                            @focus="isInputFocused.name_of_purpose = true"
                            @blur="isInputFocused.name_of_purpose = false"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded"
                            placeholder="" />
                    </div>
                </div>
                <!---Category-->
                <div class="items-center mt-5">
                    <div v-if="formValues.expense_type" class="flex justify-end ">
                        <button @click="resetTheValue('expense_type')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="expense_type"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.expense_type || isInputFocused.expense_type, 'text-blue-700': isInputFocused.expense_type }"
                            @click="isInputFocused.expense_type = true">
                            Category</label>
                        <select id="expense_type" v-model="formValues.expense_type"
                            @focus="isInputFocused.expense_type = true"
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded">
                            <option v-for="(type, index) in categories" :key="index" :value="type.id">{{ type.name }}
                            </option>
                        </select>
                    </div>
                </div>

                <!---start Date-->
                <div class="items-center mt-5">
                    <div v-if="formValues.start_date" class="flex justify-end ">
                        <button @click="resetTheValue('start_date')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="start_date"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-[12px] sm:-top-3 text-xs bg-white px-1 text-gray-700': formValues.start_date || isInputFocused.start_date, 'text-blue-700': isInputFocused.start_date }">
                            Start Date</label>
                        <input id="start_date" v-model="formValues.start_date" type="date" v-datepicker placeholder=" "
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                    </div>
                </div>
                <!---end Date-->
                <div class="items-center mt-5">
                    <div v-if="formValues.end_date" class="flex justify-end ">
                        <button @click="resetTheValue('end_date')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="end_date"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-[12px] sm:-top-3 text-xs bg-white px-1 text-gray-700': formValues.end_date || isInputFocused.end_date, 'text-blue-700': isInputFocused.end_date }">
                            End Date</label>
                        <input id="end_date" v-model="formValues.end_date" type="date" v-datepicker placeholder=" "
                            class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rousnded" />
                    </div>
                </div>

                <!-- Buttons -->
                <div class="flex justify-center items-center m-2 mt-5">
                    <button @click="closeModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  px-4 mr-8 py-2">Cancel</button>
                    <button v-if="showResetButton" @click="resetAll"
                        class="bg-blue-700 hover:bg-blue-600 rounded-[30px] text-white text-xs px-4 mr-8 py-2">Reset
                        All</button>
                    <button @click="submitFilter"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white px-4 mr-8 py-2">Submit</button>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
export default {
    props: {
        showModal: Boolean,
        companyId: String,
        userId: String
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            customer_list: [],
            isInputFocused: { start_date: true, end_date: true },
            isDropdownOpen: false,
            selectedIndex: 0,
            showOptions: false,
            search: '',
            categories: [],
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter');
            }, 300);
        },
        //--submit---
        submitFilter() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter', this.formValues);
                this.formValues = {};
            }, 300);
        },


        //---reset All---
        resetAll() {
            this.formValues = {};
        },
        //---reset---
        resetTheValue(key) {
            delete this.formValues[key];
        },
        getExpenseCategory() {
            axios.get('/expensesTypes', { params: { company_id: this.companyId, page: 1, per_page: 'all' } })
                .then(response => {
                    console.log(response.data, 'GET expense Category');
                    this.categories = response.data.data;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        }

    },
    computed: {
        showResetButton() {
            // Check if formValues is not empty
            return Object.keys(this.formValues).length > 0;
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                if (this.categories.length === 0) {
                    this.getExpenseCategory();
                }
                this.isOpen = newValue;
            }, 100);
        },

    },
    mounted() {

    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>