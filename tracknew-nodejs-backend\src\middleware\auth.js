const jwt = require('jsonwebtoken');
const { User, Company } = require('../models');
const { AppError, catchAsync } = require('./errorHandler');

// Generate JWT token
const signToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  });
};

// Generate refresh token
const signRefreshToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_REFRESH_SECRET, {
    expiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '30d'
  });
};

// Create and send token response
const createSendToken = (user, statusCode, res) => {
  const token = signToken(user.id);
  const refreshToken = signRefreshToken(user.id);
  
  const cookieOptions = {
    expires: new Date(
      Date.now() + 7 * 24 * 60 * 60 * 1000 // 7 days
    ),
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict'
  };

  res.cookie('jwt', token, cookieOptions);
  res.cookie('refreshToken', refreshToken, {
    ...cookieOptions,
    expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days
  });

  // Remove password from output
  user.password = undefined;

  res.status(statusCode).json({
    status: 'success',
    token,
    refreshToken,
    data: {
      user
    }
  });
};

// Protect routes - verify JWT token
const protect = catchAsync(async (req, res, next) => {
  // 1) Getting token and check if it's there
  let token;
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies.jwt) {
    token = req.cookies.jwt;
  }

  if (!token) {
    return next(
      new AppError('You are not logged in! Please log in to get access.', 401)
    );
  }

  // 2) Verification token
  const decoded = jwt.verify(token, process.env.JWT_SECRET);

  // 3) Check if user still exists
  const currentUser = await User.findByPk(decoded.id, {
    include: [
      {
        model: Company,
        as: 'company',
        attributes: ['id', 'company_name', 'status', 'subscription_status']
      }
    ]
  });

  if (!currentUser) {
    return next(
      new AppError('The user belonging to this token does no longer exist.', 401)
    );
  }

  // 4) Check if user account is active
  if (!currentUser.status) {
    return next(new AppError('Your account has been deactivated.', 401));
  }

  // 5) Check if user account is locked
  if (currentUser.isAccountLocked()) {
    return next(new AppError('Your account is temporarily locked due to multiple failed login attempts.', 423));
  }

  // 6) Check if company is active (if user belongs to a company)
  if (currentUser.company && !currentUser.company.status) {
    return next(new AppError('Your company account has been deactivated.', 401));
  }

  // Grant access to protected route
  req.user = currentUser;
  next();
});

// Restrict to certain roles
const restrictTo = (...roles) => {
  return (req, res, next) => {
    if (!roles.includes(req.user.user_type)) {
      return next(
        new AppError('You do not have permission to perform this action', 403)
      );
    }
    next();
  };
};

// Check if user belongs to the same company
const restrictToCompany = catchAsync(async (req, res, next) => {
  // Skip for admin users
  if (req.user.user_type === 'admin') {
    return next();
  }

  // Check if the resource belongs to the same company
  const resourceCompanyId = req.params.companyId || req.body.company_id || req.query.company_id;
  
  if (resourceCompanyId && parseInt(resourceCompanyId) !== req.user.company_id) {
    return next(
      new AppError('You can only access resources from your own company', 403)
    );
  }

  next();
});

// Refresh token
const refreshToken = catchAsync(async (req, res, next) => {
  let refreshToken;
  
  if (req.body.refreshToken) {
    refreshToken = req.body.refreshToken;
  } else if (req.cookies.refreshToken) {
    refreshToken = req.cookies.refreshToken;
  }

  if (!refreshToken) {
    return next(new AppError('No refresh token provided', 401));
  }

  // Verify refresh token
  const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET);

  // Check if user still exists
  const currentUser = await User.findByPk(decoded.id, {
    include: [
      {
        model: Company,
        as: 'company',
        attributes: ['id', 'company_name', 'status']
      }
    ]
  });

  if (!currentUser) {
    return next(new AppError('The user belonging to this token does no longer exist.', 401));
  }

  if (!currentUser.status) {
    return next(new AppError('Your account has been deactivated.', 401));
  }

  // Generate new tokens
  createSendToken(currentUser, 200, res);
});

// Optional authentication (for public routes that can benefit from user context)
const optionalAuth = catchAsync(async (req, res, next) => {
  let token;
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    token = req.headers.authorization.split(' ')[1];
  } else if (req.cookies.jwt) {
    token = req.cookies.jwt;
  }

  if (token) {
    try {
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const currentUser = await User.findByPk(decoded.id, {
        include: [
          {
            model: Company,
            as: 'company',
            attributes: ['id', 'company_name', 'status']
          }
        ]
      });

      if (currentUser && currentUser.status) {
        req.user = currentUser;
      }
    } catch (error) {
      // Token is invalid, but that's okay for optional auth
    }
  }

  next();
});

module.exports = {
  signToken,
  signRefreshToken,
  createSendToken,
  protect,
  restrictTo,
  restrictToCompany,
  refreshToken,
  optionalAuth
};
