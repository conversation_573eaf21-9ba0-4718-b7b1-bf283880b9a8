# Track New - Database Schema Guide

## Database Overview

The Track New system uses MySQL as the primary database with a comprehensive schema designed to handle service management, customer relationships, inventory, sales, and reporting. The database follows Laravel naming conventions and includes proper indexing for performance.

## Core Tables Structure

### 1. Users & Authentication

#### users
```sql
CREATE TABLE users (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    mobile_number VARCHAR(15) NULL,
    user_name VARCHAR(255) NULL,
    user_type ENUM('admin', 'sub_admin', 'employee') DEFAULT 'employee',
    email_verified_at TIMESTAMP NULL,
    password VARCHAR(255) NOT NULL,
    company_id BIGINT UNSIGNED NULL,
    plan_id BIGINT UNSIGNED NULL,
    will_expire DATE NULL,
    status BOOLEAN DEFAULT 1,
    dob DATE NULL,
    skills TEXT NULL,
    total_experience VARCHAR(50) NULL,
    avatar VARCHAR(255) NULL,
    proof VARCHAR(255) NULL,
    address TEXT NULL,
    notes TEXT NULL,
    fcm_token TEXT NULL,
    last_notification_seen TIMESTAMP NULL,
    message_limit INT DEFAULT 0,
    remember_token VARCHAR(100) NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    deleted_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    INDEX idx_email (email),
    INDEX idx_mobile (mobile_number),
    INDEX idx_status (status)
);
```

#### companies
```sql
CREATE TABLE companies (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_name VARCHAR(255) NOT NULL,
    company_email VARCHAR(255) NULL,
    company_phone VARCHAR(20) NULL,
    company_address TEXT NULL,
    company_logo VARCHAR(255) NULL,
    gst_number VARCHAR(50) NULL,
    pan_number VARCHAR(50) NULL,
    website VARCHAR(255) NULL,
    status BOOLEAN DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_name (company_name),
    INDEX idx_status (status)
);
```

### 2. Customer Management

#### customers
```sql
CREATE TABLE customers (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NULL,
    customer_phone VARCHAR(20) NULL,
    customer_mobile VARCHAR(20) NULL,
    customer_address TEXT NULL,
    customer_city VARCHAR(100) NULL,
    customer_state VARCHAR(100) NULL,
    customer_pincode VARCHAR(10) NULL,
    customer_category_id BIGINT UNSIGNED NULL,
    gst_number VARCHAR(50) NULL,
    pan_number VARCHAR(50) NULL,
    customer_type ENUM('individual', 'business') DEFAULT 'individual',
    status BOOLEAN DEFAULT 1,
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    INDEX idx_customer_name (customer_name),
    INDEX idx_customer_phone (customer_phone),
    INDEX idx_customer_email (customer_email),
    INDEX idx_status (status),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_category_id) REFERENCES customer_categories(id) ON DELETE SET NULL
);
```

#### customer_categories
```sql
CREATE TABLE customer_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    category_name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    status BOOLEAN DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);
```

### 3. Service Management

#### services
```sql
CREATE TABLE services (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    service_code VARCHAR(50) UNIQUE NOT NULL,
    company_id BIGINT UNSIGNED NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    service_category_id BIGINT UNSIGNED NULL,
    service_type VARCHAR(100) NOT NULL,
    problem_description TEXT NOT NULL,
    solution_description TEXT NULL,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    status ENUM('0', '1', '2', '3', '4', '5') DEFAULT '0', -- 0:Pending, 1:Assigned, 2:In Progress, 3:On Hold, 4:Completed, 5:Closed
    estimated_cost DECIMAL(10,2) NULL,
    actual_cost DECIMAL(10,2) NULL,
    scheduled_date DATE NULL,
    scheduled_time TIME NULL,
    completion_date DATETIME NULL,
    customer_signature TEXT NULL,
    engineer_notes TEXT NULL,
    customer_feedback TEXT NULL,
    rating INT NULL CHECK (rating >= 1 AND rating <= 5),
    warranty_period INT NULL, -- in months
    warranty_start_date DATE NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_service_code (service_code),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_status (status),
    INDEX idx_scheduled_date (scheduled_date),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (service_category_id) REFERENCES service_categories(id) ON DELETE SET NULL
);
```

#### service_categories
```sql
CREATE TABLE service_categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    category_name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    status BOOLEAN DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);
```

#### service_assigns
```sql
CREATE TABLE service_assigns (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    service_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    assigned_by BIGINT UNSIGNED NOT NULL,
    assigned_date DATETIME NOT NULL,
    notes TEXT NULL,
    status ENUM('active', 'completed', 'reassigned') DEFAULT 'active',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_service_id (service_id),
    INDEX idx_user_id (user_id),
    INDEX idx_assigned_date (assigned_date),
    
    FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_by) REFERENCES users(id) ON DELETE CASCADE
);
```

### 4. Lead Management

#### leads
```sql
CREATE TABLE leads (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    lead_code VARCHAR(50) UNIQUE NOT NULL,
    company_id BIGINT UNSIGNED NOT NULL,
    customer_id BIGINT UNSIGNED NULL,
    lead_type_id BIGINT UNSIGNED NULL,
    leadstatus_id BIGINT UNSIGNED NOT NULL,
    lead_source VARCHAR(100) NULL,
    lead_title VARCHAR(255) NOT NULL,
    lead_description TEXT NULL,
    estimated_value DECIMAL(10,2) NULL,
    probability INT NULL CHECK (probability >= 0 AND probability <= 100),
    expected_close_date DATE NULL,
    assigned_to BIGINT UNSIGNED NULL,
    priority ENUM('low', 'medium', 'high') DEFAULT 'medium',
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_lead_code (lead_code),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_leadstatus_id (leadstatus_id),
    INDEX idx_assigned_to (assigned_to),
    INDEX idx_expected_close_date (expected_close_date),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE SET NULL,
    FOREIGN KEY (lead_type_id) REFERENCES lead_types(id) ON DELETE SET NULL,
    FOREIGN KEY (leadstatus_id) REFERENCES lead_status(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL
);
```

#### lead_types
```sql
CREATE TABLE lead_types (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    type_name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    status BOOLEAN DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);
```

#### lead_status
```sql
CREATE TABLE lead_status (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    color VARCHAR(7) NULL, -- Hex color code
    is_default BOOLEAN DEFAULT 0,
    sort_order INT DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL
);
```

### 5. AMC Management

#### amc
```sql
CREATE TABLE amc (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    amc_code VARCHAR(50) UNIQUE NOT NULL,
    company_id BIGINT UNSIGNED NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    amc_title VARCHAR(255) NOT NULL,
    amc_description TEXT NULL,
    amc_type ENUM('comprehensive', 'non_comprehensive') DEFAULT 'comprehensive',
    start_date DATE NOT NULL,
    end_date DATE NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    paid_amount DECIMAL(10,2) DEFAULT 0,
    pending_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    payment_terms TEXT NULL,
    amc_status ENUM('0', '1', '2', '3') DEFAULT '0', -- 0:Draft, 1:Active, 2:Expired, 3:Cancelled
    renewal_reminder_days INT DEFAULT 30,
    auto_renewal BOOLEAN DEFAULT 0,
    terms_conditions TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_amc_code (amc_code),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_amc_status (amc_status),
    INDEX idx_start_date (start_date),
    INDEX idx_end_date (end_date),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE
);
```

#### amc_products
```sql
CREATE TABLE amc_products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    amc_id BIGINT UNSIGNED NOT NULL,
    product_id BIGINT UNSIGNED NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    serial_number VARCHAR(255) NULL,
    model_number VARCHAR(255) NULL,
    quantity INT NOT NULL DEFAULT 1,
    service_frequency ENUM('monthly', 'quarterly', 'half_yearly', 'yearly') DEFAULT 'quarterly',
    last_service_date DATE NULL,
    next_service_date DATE NULL,
    notes TEXT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_amc_id (amc_id),
    INDEX idx_product_id (product_id),
    INDEX idx_next_service_date (next_service_date),
    
    FOREIGN KEY (amc_id) REFERENCES amc(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

### 6. Sales Management

#### sales
```sql
CREATE TABLE sales (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    invoice_number VARCHAR(50) UNIQUE NOT NULL,
    company_id BIGINT UNSIGNED NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    sale_date DATE NOT NULL,
    due_date DATE NULL,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    paid_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    pending_amount DECIMAL(10,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    payment_status ENUM('pending', 'partial', 'paid', 'overdue') DEFAULT 'pending',
    payment_terms TEXT NULL,
    notes TEXT NULL,
    status ENUM('draft', 'sent', 'paid', 'cancelled') DEFAULT 'draft',
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_invoice_number (invoice_number),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_sale_date (sale_date),
    INDEX idx_payment_status (payment_status),
    INDEX idx_status (status),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);
```

#### sales_items
```sql
CREATE TABLE sales_items (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    sales_id BIGINT UNSIGNED NOT NULL,
    product_id BIGINT UNSIGNED NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    quantity DECIMAL(8,2) NOT NULL,
    unit_price DECIMAL(10,2) NOT NULL,
    discount_percentage DECIMAL(5,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    tax_percentage DECIMAL(5,2) DEFAULT 0,
    tax_amount DECIMAL(10,2) DEFAULT 0,
    line_total DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_sales_id (sales_id),
    INDEX idx_product_id (product_id),
    
    FOREIGN KEY (sales_id) REFERENCES sales(id) ON DELETE CASCADE,
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE
);
```

#### sales_payments
```sql
CREATE TABLE sales_payments (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    sales_id BIGINT UNSIGNED NOT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    payment_method ENUM('cash', 'cheque', 'bank_transfer', 'card', 'upi', 'other') NOT NULL,
    reference_number VARCHAR(255) NULL,
    notes TEXT NULL,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_sales_id (sales_id),
    INDEX idx_payment_date (payment_date),
    
    FOREIGN KEY (sales_id) REFERENCES sales(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);
```

### 7. Product & Inventory Management

#### products
```sql
CREATE TABLE products (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    product_name VARCHAR(255) NOT NULL,
    product_code VARCHAR(100) NULL,
    description TEXT NULL,
    brand_id BIGINT UNSIGNED NULL,
    category_id BIGINT UNSIGNED NULL,
    unit_id BIGINT UNSIGNED NULL,
    purchase_price DECIMAL(10,2) NULL,
    selling_price DECIMAL(10,2) NULL,
    mrp DECIMAL(10,2) NULL,
    tax_percentage DECIMAL(5,2) DEFAULT 0,
    hsn_code VARCHAR(50) NULL,
    barcode VARCHAR(255) NULL,
    sku VARCHAR(255) NULL,
    minimum_stock INT DEFAULT 0,
    current_stock INT DEFAULT 0,
    status BOOLEAN DEFAULT 1,
    is_service BOOLEAN DEFAULT 0,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    INDEX idx_product_name (product_name),
    INDEX idx_product_code (product_code),
    INDEX idx_barcode (barcode),
    INDEX idx_sku (sku),
    INDEX idx_status (status),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (brand_id) REFERENCES brands(id) ON DELETE SET NULL,
    FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE SET NULL,
    FOREIGN KEY (unit_id) REFERENCES units(id) ON DELETE SET NULL
);
```

#### brands
```sql
CREATE TABLE brands (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    brand_name VARCHAR(255) NOT NULL,
    description TEXT NULL,
    logo VARCHAR(255) NULL,
    status BOOLEAN DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);
```

#### categories
```sql
CREATE TABLE categories (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    category_name VARCHAR(255) NOT NULL,
    parent_id BIGINT UNSIGNED NULL,
    description TEXT NULL,
    status BOOLEAN DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES categories(id) ON DELETE SET NULL
);
```

#### units
```sql
CREATE TABLE units (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    unit_name VARCHAR(100) NOT NULL,
    unit_symbol VARCHAR(10) NOT NULL,
    description TEXT NULL,
    status BOOLEAN DEFAULT 1,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE
);
```

### 8. Estimation Management

#### estimations
```sql
CREATE TABLE estimations (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    estimation_number VARCHAR(50) UNIQUE NOT NULL,
    company_id BIGINT UNSIGNED NOT NULL,
    customer_id BIGINT UNSIGNED NOT NULL,
    lead_id BIGINT UNSIGNED NULL,
    estimation_date DATE NOT NULL,
    valid_until DATE NULL,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    tax_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    status ENUM('draft', 'sent', 'approved', 'rejected', 'expired') DEFAULT 'draft',
    notes TEXT NULL,
    terms_conditions TEXT NULL,
    created_by BIGINT UNSIGNED NOT NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_estimation_number (estimation_number),
    INDEX idx_company_id (company_id),
    INDEX idx_customer_id (customer_id),
    INDEX idx_lead_id (lead_id),
    INDEX idx_status (status),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES customers(id) ON DELETE CASCADE,
    FOREIGN KEY (lead_id) REFERENCES leads(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);
```

### 9. Supporting Tables

#### notifications
```sql
CREATE TABLE notifications (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error') DEFAULT 'info',
    is_read BOOLEAN DEFAULT 0,
    data JSON NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_user_id (user_id),
    INDEX idx_is_read (is_read),
    INDEX idx_created_at (created_at),
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

#### reminders
```sql
CREATE TABLE reminders (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    company_id BIGINT UNSIGNED NOT NULL,
    user_id BIGINT UNSIGNED NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT NULL,
    reminder_date DATETIME NOT NULL,
    reminder_type ENUM('service', 'amc', 'payment', 'follow_up', 'other') NOT NULL,
    related_id BIGINT UNSIGNED NULL, -- ID of related record
    status ENUM('pending', 'sent', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    INDEX idx_company_id (company_id),
    INDEX idx_user_id (user_id),
    INDEX idx_reminder_date (reminder_date),
    INDEX idx_status (status),
    
    FOREIGN KEY (company_id) REFERENCES companies(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);
```

## Database Relationships Summary

### Primary Relationships
1. **Users** belong to **Companies** (many-to-one)
2. **Customers** belong to **Companies** (many-to-one)
3. **Services** belong to **Companies** and **Customers** (many-to-one)
4. **Services** can be assigned to multiple **Users** through **ServiceAssigns** (many-to-many)
5. **Leads** belong to **Companies** and can be associated with **Customers** (many-to-one)
6. **AMC** belongs to **Companies** and **Customers** (many-to-one)
7. **AMC** can have multiple **Products** through **AmcProducts** (many-to-many)
8. **Sales** belong to **Companies** and **Customers** (many-to-one)
9. **Sales** have multiple **SalesItems** (one-to-many)
10. **Products** belong to **Companies** and can have **Brands**, **Categories**, **Units** (many-to-one)

### Key Indexes for Performance
- Company-based queries (most tables have company_id index)
- Date-based queries (created_at, scheduled_date, due_date indexes)
- Status-based filtering (status indexes on relevant tables)
- Search functionality (name, code, email, phone indexes)
- Foreign key relationships (automatic indexes)

This database schema provides a solid foundation for the Track New service management system with proper normalization, relationships, and performance considerations.
