<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="set-header-background justify-between items-center flex py-2">
                <h2 class="text-white text-center ml-12 text-xl py-1">
                    {{ type == 'type' ? 'Lead Type List' : 'Lead Status List' }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>

            <!-- Form for CRUD operations -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-4 m-4">
                <form @submit.prevent="submitForm" class="text-sm text-center">
                    <input type="text" v-model="categoryName" placeholder="Category Name" ref="inputName"
                        class="block mb-3 border px-3 py-2 w-full">
                    <button type="submit"
                        class=" rounded rounded-md px-3 py-2 mt-3 bg-green-700 hover:bg-green-600 text-white">
                        {{ updateIndex === null ? 'Create' : 'Update' }}</button>
                    <button class=" rounded rounded-md px-3 py-2 mt-3 bg-red-700 hover:bg-red-600 text-white ml-5"
                        @click="cancelModal">Close</button>
                </form>
            </div>

            <!-- Display categories -->
            <div class="border mt-5 border-blue-600 rounded rouned-full lg:p-5 p-3 m-4 text-sm"
                :key="categories.length">
                <p class="font-bold underline mb-2">List:</p>
                <ul>
                    <li v-for="(category, index) in categories" :key="index" class="flex justify-between">
                        <div>{{ category.name }}</div>
                        <div class="flex justify-between">
                            <button @click="editCategory(index)">
                                <img :src="table_edit" alt="table-edit" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                            <button @click="deleteCategory(index)">
                                <img :src="table_del" alt="table-delete" class="bg-white hover:bg-gray-100 w-6 h-6" />
                            </button>
                        </div>
                    </li>
                </ul>
            </div>
        </div>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import axios from 'axios';
import confirmbox from './confirmbox.vue';
import dialogAlert from './dialogAlert.vue';
export default {
    name: 'leadList',
    components: {
        confirmbox,
        dialogAlert
    },
    props: {
        showModal: Boolean,
        type: String,
        categoriesData: Object,
    },
    data() {
        return {
            isMobile: false,
            formValues: {},
            isOpen: false,
            categoryName: '',
            categories: [],
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            deleteIndex: null,
            open_confirmBox: false,
            updateIndex: null,
            isMessageDialogVisible: false,
            message: '',
            //--api integration---
            companyId: null,
            userId: null,
            //--toaster----
            show: false,
            type_toaster: 'warning'
        };
    },
    methods: {
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeModal', this.categories);
                this.categories = [];
            }, 300);

        },
        submitForm() {
            // console.log(this.updateIndex, 'What is the index valuye...!');
            if (this.categoryName !== '' && this.categoryName && !this.categories.some((data) => data.name.toLowerCase().includes(this.categoryName.toLowerCase())) && this.updateIndex === null) {
                // console.log(this.companyId);
                // Create category
                if (this.type === 'type') {
                    axios.post('/lead_types', { name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            // console.log('Response:', response.data);
                            this.categories.push(response.data.data);
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                } else {
                    axios.post('/lead_statuses', { name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            console.log('Response:', response.data);
                            this.categories.push(response.data.data);
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                }

                // this.saveToLocalStorage();
                this.categoryName = ''; // Clear input
            } else if (this.updateIndex !== null && this.categoryName !== '' && this.categories[this.updateIndex].name) {
                if (this.type === 'type') {
                    axios.put(`/lead_types/${this.categories[this.updateIndex].id}`, { name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            console.log('Response:', response.data);
                            this.categories[this.updateIndex] = response.data.data;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });

                } else {
                    axios.put(`/lead_statuses/${this.categories[this.updateIndex].id}`, { name: this.categoryName, company_id: this.companyId })
                        .then(response => {
                            // Handle success response
                            console.log('Response:', response.data);
                            this.categories[this.updateIndex].name = response.data.data.name;
                        })
                        .catch(error => {
                            // Handle error
                            console.error('Error:', error);
                        });
                }
                this.categoryName = '';
                this.updateIndex = null;
            } else if (this.categoryName) {
                this.isMessageDialogVisible = true;
                this.message = `${this.categoryName} already exist..!`;
            }
        },
        editCategory(index) {
            // Edit category (not implemented in this example)
            // console.log('Edit category:', this.categories[index]);
            this.categoryName = this.categories[index].name;
            this.updateIndex = index;
            // console.log(index, 'WWWWWW');
        },
        deleteCategory(index) {
            // Delete category
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        // saveToLocalStorage() {
        //     if (this.type === 'type') {
        //         // Save categories to local storage
        //         localStorage.setItem('leadType', JSON.stringify(this.categories));
        //     } else {
        //         localStorage.setItem('leadStatus', JSON.stringify(this.categories));
        //     }
        // },
        loadFromLocalStorage() {
            // Load categories from local storage
            // console.log(this.type, 'What happening...!');
            let storedCategories = [];
            if (this.type === 'type') {
                //---lead type---
                axios.get('/lead_types', {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // Handle response
                        console.log(response.data.data);
                        storedCategories = response.data.data;
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);
                    });

            } else if (this.type === 'status') {
                // Make GET request with parameter company_id=1
                axios.get('/lead_statuses', {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // Handle response
                        console.log(response.data.data);
                        storedCategories = response.data.data;
                    })
                    .catch(error => {
                        // Handle error
                        console.error('Error:', error);
                    });
                // storedCategories = localStorage.getItem('leadStatus');                
            }
            if (storedCategories.length !== 0) {
                // console.log(storedCategories, 'WERWRWRW');
                this.categories = storedCategories;
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.inputName;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---confirm box funxctions
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                // console.log(this.deleteIndex, 'Whatjjjjjj');
                if (this.type === 'type') {
                    axios.delete(`/lead_types/${this.categories[this.deleteIndex].id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            console.log(response.data);
                            this.categories.splice(this.deleteIndex, 1);
                        })
                        .catch(error => {
                            this.message = error.response.data.message;
                            this.show = true;
                            console.error(error);
                        });
                } else {
                    axios.delete(`/lead_statuses/${this.categories[this.deleteIndex].id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            console.log(response.data);
                            this.categories.splice(this.deleteIndex, 1);
                        })
                        .catch(error => {
                            this.message = error.response.data.message;
                            this.show = true;
                            console.error(error);
                        });

                }

                // this.saveToLocalStorage();
                this.open_confirmBox = false;
            }
        },

        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---close message--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
        }
    },

    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                    this.categories = this.categoriesData;
                }
            }, 100);
        },
        // categoriesData(newValue) {
        //     this.categories = newValue;
        //     console.log(newValue, 'What is happening....! by the moduleis...!');
        // }
    },
};
</script>

<style>
/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.close {
    color: white;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: red;
    text-decoration: none;
    cursor: pointer;
}
</style>