<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateInvoicesAPIRequest;
use App\Http\Requests\API\UpdateInvoicesAPIRequest;
use App\Models\Invoices;
use App\Models\InvoiceSettings;
use App\Models\Estimation;
use App\Repositories\InvoicesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Repositories\SalesRepository;
use App\Repositories\SalesItemsRepository;
use App\Repositories\SalesPaymentRepository;
use App\Repositories\EstimationRepository;
use Carbon\Carbon;
use Response;

/**
 * Class InvoicesController
 * @package App\Http\Controllers\API
 */

class InvoicesAPIController extends AppBaseController
{
    /** @var  InvoicesRepository */
    private $invoicesRepository;
    private $salesRepository;
    private $salesItemsRepository;
    private $salesPaymentRepository;
    private $estimationRepository;

    public function __construct(InvoicesRepository $invoicesRepo, SalesRepository $salesRepo, SalesItemsRepository $salesItemRepo, SalesPaymentRepository $salesPaymentRepo,
    EstimationRepository $estimationRepo)
    {
        $this->invoicesRepository = $invoicesRepo;
        $this->salesRepository = $salesRepo;
        $this->salesItemsRepository = $salesItemRepo;
        $this->salesPaymentRepository = $salesPaymentRepo;
        $this->estimationRepository = $estimationRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/invoices",
     *      summary="getInvoicesList",
     *      tags={"Invoices"},
     *      description="Get all Invoices",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Invoices")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $invoices = $this->invoicesRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($invoices->toArray(), 'Invoices retrieved successfully');
    }
    
    
            /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/invoice-last-number",
     *      summary="Get Last Invoice Number",
     *      tags={"Invoices"},
     *      description="Get the last invoice number",
     *      @OA\Parameter(
     *         name="company_id",
     *          description="ID of the company whose services are to be fetched",
     *          @OA\Schema(
     *              type="string"
     *          ),
     *          required=true,
     *          in="query"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Invoices")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function getLastInvoiceNumber(Request $request)
    {
      
        $companyId = $request->query('company_id');

        $fy = getFinancialYearLabel();

        $invoiceSettings = InvoiceSettings::where('company_id', $companyId)->first();
        $resetFY = $invoiceSettings->reset_fy ?? false;
        $queryGroup0 = Invoices::where('invoice_group', 0)
                ->where('company_id', $companyId);

            if ($resetFY) {
                $queryGroup0->where('fin_year', $fy);
            }

            $lastInvoiceGroup0 = $queryGroup0->latest()->first();
            $lastInvoiceNumberGroup0 = $lastInvoiceGroup0 ? $lastInvoiceGroup0->invoice_no : 0;
<<<<<<< HEAD

       
=======
>>>>>>> dev
        $lastInvoiceGroup1 = Invoices::where('invoice_group', 1)->where('company_id', $companyId)->latest()->first();
        $lastInvoiceNumberGroup1 = $lastInvoiceGroup1 ? $lastInvoiceGroup1->invoice_no  : 0;
        
        return response()->json([
            'success' => true,
            'data' => [
                'last_invoice_number_group_a' => $lastInvoiceNumberGroup0,
                'last_invoice_number_group_b' => $lastInvoiceNumberGroup1,
            ]
            
        ]);



    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/invoices",
     *      summary="createInvoices",
     *      tags={"Invoices"},
     *      description="Create Invoices",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Invoices")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Invoices"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateInvoicesAPIRequest $request)
    {
        $input = $request->all();        

        $salesData = $input['sales_data'];

        $sales = $this->salesRepository->create($salesData); 

        foreach ($salesData['sales_item_data'] as $itemData) {
            $salesItemData = [
                "product_code" => $itemData['product_code'],
                "product_name" => $itemData['product_name'],
                "description" => $itemData['description'],
                "price" => $itemData['price'],
                "hsn_code" => $itemData['hsn_code'],
                "taxvalue" => $itemData['taxvalue'],
                "tax_type" => $itemData['tax_type'],
                "qty" => $itemData['qty'],
                "tax" => $itemData['tax'],
                "total" => $itemData['total'],
                "sales_id" => $sales->id,
                "company_id" => $itemData['company_id']
            ];
    
            // Create each sales item
            $salesItems = $this->salesItemsRepository->create($salesItemData);
        }

        $salesPaymentDate = [
            "payment_code" => $salesData['payment_code'],
            "company_id" => $salesData['company_id'],
            "sales_id" => $sales->id,
            "payment_date" => $salesData['payment_date'],
            "payment_amount" => $salesData['payment_amount'],
            "customer_id" => $salesData['customer_id'],
            "payment_notes" => $salesData['payment_notes'],
            "created_by" => $salesData['created_by'],
            "payment_type" => $salesData['payment_type']
        ];
        $salesPayment = $this->salesPaymentRepository->create($salesPaymentDate);

        $invoiceData = $input['invoice_data'];
        $invoiceData["sales_id"] = $sales->id;
        $invoices = $this->invoicesRepository->create($invoiceData);

        
        if ($salesData['estimation_id'] != 0) {
            $estimation = Estimation::find($salesData['estimation_id']);
        
            if ($estimation) {
                $estimationData = ["status" => 2];
                $estimation->update($estimationData);
            }
        }

        return $this->sendResponse($invoices->toArray(), 'Invoices saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/invoices/{id}",
     *      summary="getInvoicesItem",
     *      tags={"Invoices"},
     *      description="Get Invoices",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Invoices",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Invoices"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Invoices $invoices */
        $invoices = $this->salesRepository->findCompanyRelated($id);

        if (empty($invoices)) {
            return $this->sendError('Invoices not found');
        }

        return $this->sendResponse($invoices->toArray(), 'Invoices retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/invoice/getinvoicebyclientid/{id}",
     *      summary="Get Invoices by Client ID",
     *      tags={"Invoices"},
     *      description="Get Invoices",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Invoices",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Invoices"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */

    //fetch based on company_id
    public function GetInvoicebyClientId($id, Request $request)
    {
        /** @var Invoices $invoices */
        // $invoices = $this->salesRepository->findCompanyRelated($id);

        // if (empty($invoices)) {
        //     return $this->sendError('Invoices not found');
        // }

        // return $this->sendResponse($invoices->toArray(), 'Invoices retrieved successfully');
        $perPage = $request->query('per_page', 2);
        $page = $request->query('page', 1);

        $invoices = $this->salesRepository->findCompanyRelatedPaginated($id, $perPage, $page);

        if ($invoices->isEmpty()) {
            return $this->sendError('Invoices not found');
        }

        return $this->sendResponse([
            'pagination' => [
                'total' => $invoices->total(),
                'per_page' => $invoices->perPage(),
                'current_page' => $invoices->currentPage(),
                'last_page' => $invoices->lastPage(),
                'from' => $invoices->firstItem(),
                'to' => $invoices->lastItem(),
            ],
            'invoices' => $invoices->toArray()
        ], 'Invoices retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/invoices/{id}",
     *      summary="updateInvoices",
     *      tags={"Invoices"},
     *      description="Update Invoices",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Invoices",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
       *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Invoices")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Invoices"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateInvoicesAPIRequest $request)
    {
        // $input = $request->all();

        // /** @var Invoices $invoices */
        // $invoices = $this->invoicesRepository->find($id);

        // if (empty($invoices)) {
        //     return $this->sendError('Invoices not found');
        // }

        // $invoices = $this->invoicesRepository->update($input, $id);

        // return $this->sendResponse($invoices->toArray(), 'Invoices updated successfully');

        $input = $request->all();        

        $salesData = $input['sales_data'];
        $sale = $this->salesRepository->find($id);

        if (empty($sale)) {
            return $this->sendError('Sales not found');
        }

        $sales = $this->salesRepository->update($salesData, $id);

        foreach ($salesData['sales_item_data'] as $itemData) {
            $salesItemId = $itemData['id'];
            $salesItemData = [
                "product_code" => $itemData['product_code'],
                "product_name" => $itemData['product_name'],
                "description" => $itemData['description'],
                "price" => $itemData['price'],
                "hsn_code" => $itemData['hsn_code'],
                "taxvalue" => $itemData['taxvalue'],
                "tax_type" => $itemData['tax_type'],
                "qty" => $itemData['qty'],
                "tax" => $itemData['tax'],
                "total" => $itemData['total'],
                "sales_id" => $sales->id,
                "company_id" => $itemData['company_id']
            ];
    
            $salesItems = $this->salesItemsRepository->update($salesItemData, $salesItemId);
        }

        $salesPaymentDate = [
            "payment_code" => $salesData['payment_code'],
            "company_id" => $salesData['company_id'],
            "sales_id" => $sales->id,
            "payment_date" => $salesData['payment_date'],
            "payment_amount" => $salesData['payment_amount'],
            "customer_id" => $salesData['customer_id'],
            "payment_notes" => $salesData['payment_notes'],
            "created_by" => $salesData['created_by'],
            "payment_type" => $salesData['payment_type']
        ];
        $salesPayment = $this->salesPaymentRepository->model()::where('sales_id', $id)->update($salesPaymentDate);

        $invoiceData = $input['invoice_data'];
        $invoices = $this->invoicesRepository->model()::where('sales_id', $id)->update($invoiceData);

        return $this->sendResponse($invoices, 'Invoices updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/invoices/{id}",
     *      summary="deleteInvoices",
     *      tags={"Invoices"},
     *      description="Delete Invoices",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Invoices",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Invoices $invoices */
        $invoices = $this->invoicesRepository->find($id);

        if (empty($invoices)) {
            return $this->sendError('Invoices not found');
        }

       // $invoices->delete();

        return $this->sendSuccess('Invoices deleted successfully');
    }
}
