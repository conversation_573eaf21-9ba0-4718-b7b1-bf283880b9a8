<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-auto">
        <div class="bg-white w-full sm:w-3/4 transform transition-transform ease-in-out duration-300 rounded-lg h-screen overflow-auto"
            :class="{
                'scale-100': isOpen, 'scale-0': !isOpen, 'pb-[60px]': isMobile
            }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background rounded-t">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Available Options Are
                </p>
                <p class="close" @click="closeModal">&times;</p>
            </div>
            <div class="text-sm p-5 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2">
                <!--display options-->
                <!--category list-->
                <div v-if="category_list && Array.isArray(category_list) && category_list.length > 0">
                    <p class="text-center font-bold bg-gray-300 py-1">Category List</p>
                    <table class="mb-3 w-full">
                        <thead>
                            <tr class="text-white set-header-background">
                                <th>Sr.No</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(category, index) in category_list" :key="index" class="text-center border-b-2">
                                <td>{{ index + 1 }}</td>
                                <td>{{ category.category_name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--brand list-->
                <div v-if="brand_list && Array.isArray(brand_list) && brand_list.length > 0">
                    <p class="text-center font-bold bg-gray-300 py-1">Brand List</p>
                    <table class="mb-3 w-full">
                        <thead>
                            <tr class="text-white set-header-background">
                                <th>Sr.No</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(brand, index) in brand_list" :key="index" class="text-center border-b-2">
                                <td>{{ index + 1 }}</td>
                                <td>{{ brand.brand_name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--Tax list-->
                <div v-if="tax_list && Array.isArray(tax_list) && tax_list.length > 0">
                    <p class="text-center font-bold bg-gray-300 py-1">Tax Value List</p>
                    <table class="mb-3 w-full">
                        <thead>
                            <tr class="text-white set-header-background">
                                <th>Sr.No</th>
                                <th>Name</th>
                                <th>Value</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(tax, index) in tax_list" :key="index" class="text-center border-b-2">
                                <td>{{ index + 1 }}</td>
                                <td>{{ tax.tax_name }}</td>
                                <td>{{ tax.value }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--Tax Type list-->
                <div v-if="type !== 'customer'">
                    <p class="text-center font-bold bg-gray-300 py-1">Tax Type List</p>
                    <table class="mb-3 w-full">
                        <thead>
                            <tr class="text-white set-header-background">
                                <th>Sr.No</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-center border-b-2">
                                <td>1</td>
                                <td>Exclusive</td>
                            </tr>
                            <tr class="text-center border-b-2">
                                <td>2</td>
                                <td>Inclusive</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--Discount Type list-->
                <div v-if="type !== 'customer'">
                    <p class="text-center font-bold bg-gray-300 py-1">Discount Type List</p>
                    <table class="mb-3 w-full">
                        <thead>
                            <tr class="text-white set-header-background">
                                <th>Sr.No</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr class="text-center border-b-2">
                                <td>1</td>
                                <td>Percentage</td>
                            </tr>
                            <tr class="text-center border-b-2">
                                <td>2</td>
                                <td>Fixed</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--unit list-->
                <div v-if="unit_list && Array.isArray(unit_list) && unit_list.length > 0">
                    <p class="text-center font-bold bg-gray-300 py-1">Unit List</p>
                    <table class="mb-3 w-full">
                        <thead>
                            <tr class="text-white set-header-background">
                                <th>Sr.No</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(unit, index) in unit_list" :key="index" class="text-center border-b-2">
                                <td>{{ index + 1 }}</td>
                                <td>{{ unit }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--customer category list-->
                <div
                    v-if="customer_category && Array.isArray(customer_category) && customer_category.length > 0 && type === 'customer'">
                    <p class="text-center font-bold bg-gray-300 py-1">Customer Category List</p>
                    <table class="mb-3 w-full">
                        <thead>
                            <tr class="text-white set-header-background">
                                <th>Sr.No</th>
                                <th>Name</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(category, index) in customer_category" :key="index"
                                class="text-center border-b-2">
                                <td>{{ index + 1 }}</td>
                                <td>{{ category.category_name }}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        category_list: Object,
        brand_list: Object,
        unit_list: Object,
        tax_list: Object,
        isMobile: Boolean,
        type: String,
        customer_category: Object,
    },
    data() {
        return {
            isOpen: false,
            formValues: {},
            message: '',
            isInputFocused: {}
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event

            setTimeout(() => {
                this.$emit('close-Modal');
            }, 300);
        },

    },
    watch: {
        showModal(newValue) {
            // console.log(newValue, 'Waht happening..!', this.unit_list);
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },


    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>
