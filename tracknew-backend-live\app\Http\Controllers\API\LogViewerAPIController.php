<?php

namespace App\Http\Controllers\API;




use App\Http\Controllers\AppBaseController;
use Illuminate\Support\Facades\File;
use Illuminate\Http\Request;


/**
 * Class NotificationController
 * @package App\Http\Controllers\API
 */

class LogViewerAPIController extends AppBaseController
{	
  
    public function showLogs(Request $request)
	{
    $date = $request->query('date', now()->format('Y-m-d')); 
       
    $logPath = storage_path("logs/laravel-{$date}.log");

    // Check if the file exists
    if (!File::exists($logPath)) {
        return response()->json([
            'success' => false,
            'message' => 'Log file does not exist.',
        ], 200);
    }



    // Read the log file
    $logContent = File::get($logPath);

    // Split log file into lines
    $logLines = explode(PHP_EOL, $logContent);

    // Parse logs into structured data
    $logs = [];
    foreach ($logLines as $line) {
        if (preg_match('/^\[(\d{4}-\d{2}-\d{2}) (\d{2}:\d{2}:\d{2})\] (.+?): (.+)$/', $line, $matches)) {
            $logDate = $matches[1];
            $logTime = $matches[2];
            $logLevel = $matches[3];
            $logMessage = $matches[4];

            if (!$date || $logDate === $date) {
                $logs[] = [
                    'date' => $logDate,
                    'time' => $logTime,
                    'level' => $logLevel,
                    'message' => $logMessage,
                ];
            }
        }
    }

    return response()->json([
        'success' => true,
        'logs' => array_reverse($logs),
    ]);
    }

}
