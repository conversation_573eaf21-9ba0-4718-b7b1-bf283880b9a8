<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 "
        style="z-index: 200;">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">

            <div class="bg-teal-600 justify-between items-center flex py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-lg">Comments by Technician</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!-- Content based on selected option -->
            <div class="p-4">
                <div class="w-full p-1 pl-4 pr-4">
                    <label for="name" class="text-sm font-bold">Comments<span
                            class="text-red-700 font-bold">*</span></label>
                    <textarea id="name" ref="comments" v-model="formValues.comments_by_technician"
                        class="text-sm p-1 mt-1 border border-gray-300 w-full" rows="5"></textarea>
                </div>
            </div>
            <!-- Buttons -->
            <div class="flex justify-end items-center m-3 text-sm">
                <button @click="cancelModal"
                    class="bg-red-700 hover:bg-red-600 mr-8 px-3 py-2 text-white rounded rounded-lg">Cancel</button>
                <button @click="sendModal" v-if="showButton"
                    class="bg-green-700 hover:bg-green-600 mr-8 px-5 py-2 text-white rounded rounded-lg ">Save</button>
            </div>
        </div>
    </div>
</template>

<script>

export default {
    name: 'comments',
    props: {
        showModal: Boolean,
        comments: String,
    },
    data() {
        return {
            isMobile: false,
            formValues: {},
            isOpen: false,
            updatedData: null,
            showButton: true,
            user: null
        }
    },
    methods: {
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
                // this.formValues = {};
            }, 300);
        },
        sendModal() {
            if (this.formValues.comments_by_technician && this.showButton) {
                this.isOpen = false;
                setTimeout(() => {
                    this.$emit('close-modal', { comments: this.formValues.comments_by_technician, current_date: this.getCurrentDattAndTime(), updated_by: this.user ? this.user : '' });
                    this.formValues = {};
                }, 300);

            }
            this.hideButton();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        //---get current date and time---
        getCurrentDattAndTime() {
            const currentDate = new Date();
            const day = currentDate.getDate().toString().padStart(2, '0');
            const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
            const year = currentDate.getFullYear();
            const hours = currentDate.getHours() % 12 || 12;
            const minutes = currentDate.getMinutes().toString().padStart(2, '0');
            const formattedDate = `${day}-${month}-${year} ${hours}:${minutes} ${currentDate.getHours() >= 12 ? 'PM' : 'AM'}`;
            return formattedDate;
        },


        //--hide button
        hideButton() {
            this.showButton = false;
            setTimeout(() => {
                this.showButton = true;
            }, 1000); // 10 seconds in milliseconds
        },
        handleFocus() {
            this.$nextTick(() => {
                if (this.$refs.comments) { // Corrected ref name
                    // console.log(this.$refs.productname, "What happening..!");
                    this.$refs.comments.focus();
                    this.$refs.comments.click();
                }
            });
        }
    },
    mounted() {
        this.updateIsMobile();
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.user = { id: dataParse.user_id, name: dataParse.name ? dataParse.name : '' }
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        window.addEventListener('resize', this.updateIsMobile);
        // this.handleFocus();
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.handleFocus();
            }, 100);
        },
        comments: {
            handler() {
                this.formValues.comments_by_technician = this.comments;
                // console.log(this.userName, 'Waht about value...!');
                // this.handleFocus();
            },
        }
    },
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 999;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;
        /* Adjust the height as needed */
    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>
