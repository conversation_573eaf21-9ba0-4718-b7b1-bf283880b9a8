// store/modules/searchserial.js
import axios from "axios";

const state = {
  serial_list: [],
  };

  const mutations = {
      SET_SERIALLIST(state, { data}) {
          // console.log(data, 'data', pagination, 'pagination', status_counts, 'status');
          state.serial_list = data;
    },
      RESET_STATE(state) {
          state.serial_list = [];
      }

  };

  const actions = {
    updateSerialName({ commit }, serial_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update serial_list name
      setTimeout(() => {
        // Commit mutation to update serial_list name
        commit('SET_SERIALLIST', serial_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchSerialList({ commit }, {serialno}) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get(`/sales-items/${serialno}`, { params: { company_id: company_id} })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Serial list..!', response.data.total_revenue, 'TTTTTTT',  response.data.total_sum);
              let { data } = response;  
              commit('SET_SERIALLIST', {data});
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
          console.error('Error fetching item list:', error);
          if (error.response && error.response.data && error.response.data.message === 'No sales items found') {              
              commit('SET_SERIALLIST', { data: [] });  
          }
      }
    },
    
  };

  const getters = {
    currentSerialList(state) {
      return state.serial_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
