export default {
    namespaced: true,
    state: {
      recordsPerPage: 10, // Default value, you can change it as needed
    },
    mutations: {
        setRecordsPerPage(state, value) {
            // console.log(value, 'what happening in the data');
            
        state.recordsPerPage = value;
        localStorage.setItem('recordsPerPage', value); // Save to localStorage
      },
      initializeRecordsPerPage(state) {
        const savedValue = localStorage.getItem('recordsPerPage');
        if (savedValue) {
          state.recordsPerPage = parseInt(savedValue, 10);
        }
        },
        RESET_STATE(state) {
            state.recordsPerPage = 10;
        }
    },
    actions: {
      updateRecordsPerPage({ commit }, value) {
        commit('setRecordsPerPage', value);
      },
      initialize({ commit }) {
        commit('initializeRecordsPerPage');
      },
    },
    getters: {
      recordsPerPageCount: (state) => state.recordsPerPage,
    },
  };
  