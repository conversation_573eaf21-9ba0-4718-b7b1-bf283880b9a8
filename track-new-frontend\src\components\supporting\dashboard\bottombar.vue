<template>
    <div>
        <div class="fixed bottom-0 left-0 z-10 w-full bg-white border-t border-gray-200">
            <div class="grid h-full max-w-lg grid-cols-5 mx-auto font-medium">
                <button v-if="list_data && list_data.length > 0" v-for="(opt, index) in list_data" :key="index"
                    type="button" @click.stop="navigate(opt)"
                    class="inline-flex flex-col items-center justify-center px-5 pt-1 hover:bg-gray-50 space-y-1"
                    :class="{
                        hidden: !isRoleAllowed(index),
                    }">
                    <div v-if="selected_btn_btm === opt.name"
                        class="absolute top-0 transform  border-t-8 border-blue-700 rounded-b-lg"
                        :style="{ width: '50px' }">
                    </div>
                    <!--- && selected_btn_btm !== 'home' && selected_btn_btm !== 'service' && selected_btn_btm !== 'lead' && selected_btn_btm !== 'amc' && selected_btn_btm !== 'sale'-->
                    <div v-else-if="opt.name === 'menu' && findIsList"
                        class="absolute top-0 transform  border-t-8 border-blue-700 rounded-b-lg"
                        :style="{ width: '50px' }">
                    </div>
                    <!-- Icon or Menu -->
                    <div class="w-7 h-7 pt-1">
                        <img v-if="opt.name !== 'menu'" :src="opt.image" :alt="opt.name" />
                        <span v-else class="text-2xl">☰</span>
                    </div>

                    <!-- Name -->
                    <span class="text-[14px] capitalize text-center" :class="{ 'font-bold': isSelected(opt) }">
                        {{ opt.name }}
                    </span>
                </button>
            </div>
        </div>
        <!--@click="closeSidebar"-->
        <div v-if="isSidebarOpen" class="fixed inset-0 bg-gray-800 bg-opacity-50 z-30 custom-scrollbar-hidden">
            <sidebar @selectSidebarOption="handleSelectSidebarOption"></sidebar>
        </div>
    </div>
</template>
<script>
import sidebar from '../sidebar.vue';
import { mapGetters, mapActions } from 'vuex';

export default {
    components: {
        sidebar,
    },
    props: {
        selected_btn_btm: String,
        update_list: Boolean,
        openSidebarMobile: Boolean
    },
    data() {
        return {
            list_data: [],
            list_backup_data: [
                { id: 0, name: 'home', image: '/images/side_bar/home.png', url: '/' },
                { id: 1, name: 'services', image: '/images/side_bar/Clock.png', url: '/services' },
                { id: 2, name: 'lead', image: '/images/side_bar/Leads.png', url: '/leads' },
                { id: 5, name: 'sale', image: '/images/side_bar/Sales.png', url: '/sales' },
                { id: 3, name: 'amc', image: '/images/side_bar/amc.png', url: '/amc' },
                { id: 4, name: 'rma', image: '/images/side_bar/RMA.png', url: '/openrma' },
                { id: 6, name: 'proforma', image: '/images/side_bar/proforma.png', url: '/proforma' },
                { id: 7, name: 'estimate', image: '/images/side_bar/quote.png', url: '/estimation' },
                { id: 8, name: 'expense', image: '/images/side_bar/expense.png', url: '/expense' },
                { id: 9, name: 'menu', image: '/images/side_bar/Sales.png', url: '' }
            ],
            isSidebarOpen: false,
            route_item: 3,
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('features_list', ['currentFeatureList']),
        findIsList() {
            if (this.list_data && this.list_data.length > 0) {
                // console.log(this.list_data, 'Waht happening the servicesss ');
                let find_data = this.list_data.find(opt => opt.name === this.selected_btn_btm);
                if (!find_data) {
                    return true;
                } else {
                    return false;
                }
            }
        }
    },
    created() {
        if (this.currentFeatureList && this.currentFeatureList.length > 0) {
            this.filteredBottomItems();
            this.fetchFeatureList();
        } else {
            this.fetchFeatureList();
        }
        // this.list_data = [...this.list_backup_data];
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }

    },
    methods: {
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('features_list', ['fetchFeatureList']),
        navigate(data01) {
            // this.selected_btn_btm = data01.name;
            if (data01.name !== 'menu') {
                this.$router.push(data01.url);
            } else if (data01.name === 'menu') {
                this.$emit('togglesidebar');
                // let findPageData = this.$route.path;
                // switch (findPageData) {
                //     case '/dashboard': {
                //         this.route_item = 1;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/services': {
                //         this.route_item = 3;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/leads': {
                //         this.route_item = 2;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/amc': {
                //         this.route_item = 11;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/sales': {
                //         this.route_item = 5;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/estimation': {
                //         this.route_item = 10;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/items': {
                //         this.route_item = 6;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/items/purchaseOrder': {
                //         this.route_item = 6;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/items/purchaseOrder/supplier': {
                //         this.route_item = 6;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/items/purchaseOrder/warehouse': {
                //         this.route_item = 6;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/items/purchaseOrder/supplier/stock': {
                //         this.route_item = 6;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/items/purchaseOrder/paymentout': {
                //         this.route_item = 6;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/items/purchaseOrder/paymentout/createnew': {
                //         this.route_item = 6;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/customers': {
                //         this.route_item = 4;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/expense': {
                //         this.route_item = 12;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/setting': {
                //         this.route_item = 9;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/categories': {
                //         this.route_item = 7;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     case '/pages': {
                //         this.route_item = 13;
                //         this.toggleSidebar();
                //         break;
                //     }
                //     default: {
                //         this.route_item = 1;
                //         this.toggleSidebar();
                //         break;
                //     }                
                // };

            }
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            this.closeSidebar();
        },
        isRoleAllowed(index) {
            // Define allowed roles for the button at the given index
            const allowedRoles = ["Sub_Admin", "admin", "Account Manager", "Service Manager", "Sales man"];

            // Check roles for a specific button (example: index 3 has role restrictions)
            if (index === 3) {
                return this.checkRoles(allowedRoles);
            }

            // Default: Allow all other buttons
            return true;
        },
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---filter bottombar data ---
        filteredBottomItems() {
            const accessMap = this.currentFeatureList.reduce((map, item) => {
                map[item.id] = item.hasAccess;
                return map;
            }, {});

            let itemsToDisplay = [this.list_backup_data.find(item => item.id === 0)]; // Always include 'home'

            // Define the main groups based on IDs
            const primaryGroup = [1, 2, 5, 3]; // Corresponds to 'services', 'amc', 'sales'
            const servicesFallback = [5, 2, 6, 7]; // Corresponds to 'sales', 'proforma', 'estimate'
            const salesFallback = [1, 2, 3, 4, 5, 6, 7, 8]; // Corresponds to 'services', 'amc', 'rma'

            // Check access for the primary group
            let primaryItems = primaryGroup.filter(id => accessMap[id]);
            // console.log(primaryItems, 'What happening....');

            // If primary group is fully accessible
            if (primaryItems.length === 3) {
                itemsToDisplay = itemsToDisplay.concat(
                    this.list_backup_data.filter(item => primaryItems.includes(item.id))
                );
            }
            // Handle fallback scenarios
            else if (accessMap[1] === false && accessMap[5] === true) {
                itemsToDisplay = itemsToDisplay.concat(
                    this.list_backup_data.filter(item => servicesFallback.includes(item.id) && accessMap[item.id])
                );
            } else if (accessMap[5] === false && accessMap[1] === true) {
                itemsToDisplay = itemsToDisplay.concat(
                    this.list_backup_data.filter(item => salesFallback.includes(item.id) && accessMap[item.id])
                );
            } else {
                // If none of the fallback conditions are met, fill with any accessible items
                itemsToDisplay = itemsToDisplay.concat(
                    this.list_backup_data.filter(item =>
                        item.id !== 0 && item.id !== 9 && accessMap[item.id]
                    ).slice(0, 3)
                );
            }
            // console.log(itemsToDisplay.length < 4, 'What happening.... the datat', itemsToDisplay);
            if (itemsToDisplay.length > 4) {
                itemsToDisplay = itemsToDisplay.slice(0, 4);

            }

            // Ensure at least 3 items (including 'home' and 'menu')
            if (itemsToDisplay.length < 4) {
                const remainingItems = this.list_backup_data.filter(item =>
                    !itemsToDisplay.includes(item) && accessMap[item.id] && item.id !== 0 && item.id !== 8
                ).slice(0, 3 - itemsToDisplay.length);

                itemsToDisplay = itemsToDisplay.concat(remainingItems);
            }

            itemsToDisplay.push(this.list_backup_data.find(item => item.id === 9)); // Always include 'menu'

            this.list_data = itemsToDisplay;
        },
        isSelected(opt) {
            return (
                this.selected_btn_btm === opt.name ||
                (opt.name === "menu" &&
                    this.selected_btn_btm &&
                    !["service", "lead", "amc", "sale"].includes(this.selected_btn_btm))
            );
        },
    },
    watch: {
        currentFeatureList: {
            deep: true,
            handler(newValue) {
                this.filteredBottomItems();
            }
        },
        update_list: {
            deep: true,
            handler(newValue) {
                this.fetchFeatureList();
            }
        }
    }
};
</script>
<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
    -webkit-text-size-adjust: none;
    text-size-adjust: none;
}
</style>