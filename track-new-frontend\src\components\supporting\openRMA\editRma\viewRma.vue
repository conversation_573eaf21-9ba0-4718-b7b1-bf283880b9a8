<template>
    <div class="m-1" :class="{ 'mt-[60px] mb-[50px]': isMobile, 'mt-5': !isMobile }">
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>
        <div v-if="!open_skeleton" class="my-custom-margin bg-white p-2 border rounded">
            <!-- Top Section with Title and Save Button -->
            <div class="flex justify-between items-center mb-4">
                <div>
                    <h1 v-if="formValues.rma_id" class="text-sm sm:text-lg font-semibold">{{ formValues.rma_id }}</h1>
                </div>
                <div class="flex space-x-2">
                    <div ref="printRef">
                        <button @click="handleDropdownPrint"
                            class="px-4 py-2 shadow-inner shadow-gray-100 border border-gray-500 text-gray-600 rounded">
                            <font-awesome-icon icon="fa-solid fa-print" size="lg" />
                            {{ isMobile ? '' : 'Print' }}</button>
                        <!--dropdown options-->
                        <div v-if="dropdownOpenPrint"
                            class="absolute text-sm mt-2 bg-white border border-gray-300 rounded shadow-lg z-10">
                            <button class="block px-4 py-2 text-gray-700 hover:bg-gray-100"
                                @click="printPDF('inward')">Check - In</button>
                            <button class="block px-4 py-2 text-gray-700 hover:bg-gray-100"
                                @click="printPDF('outward')">Check - Out</button>
                        </div>
                    </div>
                    <div ref="downloadRef">
                        <button @click="handleDropdown"
                            class="px-4 py-2 shadow-inner shadow-red-100 border border-red-500 text-red-600 rounded">
                            <font-awesome-icon icon="fa-regular fa-file-pdf" size="lg" />
                            {{ isMobile ? '' : 'Download' }}</button>
                        <!--dropdown options-->
                        <div v-if="dropdownOpen"
                            class="absolute text-sm mt-2 bg-white border border-gray-300 rounded shadow-lg z-10">
                            <button class="block px-4 py-2 text-gray-700 hover:bg-gray-100"
                                @click="downloadPDF('inward')">Check - In</button>
                            <button class="block px-4 py-2 text-gray-700 hover:bg-gray-100"
                                @click="downloadPDF('outward')">Check - Out</button>
                        </div>
                    </div>
                    <button
                        class="px-4 py-2 shadow-inner shadow-blue-100 border border-blue-500 bg-blue-500 text-white rounded"
                        @click="saveRMAData"><font-awesome-icon icon="fa-regular fa-floppy-disk" size="lg" />
                        {{ isMobile ? '' : 'Save' }}
                    </button>
                </div>

            </div>
            <div class="p-2">
                <!-- Tabs -->
                <div class="flex overflow-auto custom-scrollbar-hidden">
                    <button class="py-4 px-4 relative" @click="selectOption(1)"
                        :class="{ 'text-blue-500 border-t-2 border-t-blue-500 border-l border-r': selected_option === 1, 'border-b': selected_option !== 1 }">
                        <font-awesome-icon icon="fa-solid fa-info" class="items-center px-1" />
                        <span :class="{ 'text-[10px] line-clamp-2': isAndroid }">RMA Info</span>
                    </button>
                    <button class="py-4 px-4" @click="selectOption(2)"
                        :class="{ 'text-blue-500 border-t-2 border-t-blue-500 border-l border-r': selected_option === 2, 'border-b': selected_option !== 2 }">
                        <font-awesome-icon icon="fa-solid fa-wrench" class="items-center px-1" />
                        <span :class="{ 'text-[10px] line-clamp-2': isAndroid }">Repair Info</span></button>
                    <button class="py-4 px-4" @click="selectOption(3)"
                        :class="{ 'text-blue-500 border-t-2 border-t-blue-500 border-l border-r': selected_option === 3, 'border-b': selected_option !== 3 }">
                        <font-awesome-icon icon="fa-solid fa-tags" class="items-center px-1" />
                        <span :class="{ 'text-[10px] line-clamp-2': isAndroid }">Items and Cost</span></button>
                    <button class="py-4 px-4" @click="selectOption(4)"
                        :class="{ 'text-blue-500 border-t-2 border-t-blue-500 border-l border-r': selected_option === 4, 'border-b': selected_option !== 4 }">
                        <font-awesome-icon icon="fa-solid fa-paperclip" class="items-center px-1" />
                        <span :class="{ 'text-[10px] line-clamp-2': isAndroid }">Attachments</span></button>
                    <button class="py-4 px-4" @click="selectOption(5)"
                        :class="{ 'text-blue-500 border-t-2 border-t-blue-500 border-l border-r': selected_option === 5, 'border-b': selected_option !== 5 }">
                        <font-awesome-icon icon="fa-regular fa-clipboard" class="items-center px-1" />
                        <span :class="{ 'text-[10px] line-clamp-2': isAndroid }">Additional Products</span></button>
                    <button class="py-4 px-4" @click="selectOption(6)"
                        :class="{ 'text-blue-500 border-t-2 border-t-blue-500 border-l border-r': selected_option === 6, 'border-b': selected_option !== 6 }">
                        <font-awesome-icon icon="fa-solid fa-money-bill" class="items-center px-1" />
                        <span :class="{ 'text-[10px] line-clamp-2': isAndroid }">Payments</span>
                    </button>
                </div>

                <!-- Main Content -->
                <div class="border p-4">
                    <!--RMA info-->
                    <div v-if="selected_option === 1" class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6">
                        <!-- Customer Info Section -->
                        <div>
                            <h3 class="text-md font-semibold mb-4 border-b py-1 border-b-gray-400">Customer Info</h3>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Customer
                                    *</label>
                                <input type="text" v-model="customer_data.name"
                                    class="mt-1 w-full border-gray-300 rounded border py-2 px-3" readonly
                                    @focus="showTooltip('customer')" @blur="hideTooltip" />
                                <div v-if="tooltipVisible && tooltipField === 'customer'" class="tooltip">{{
                                    tooltips.customer }}</div>
                            </div>

                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Customer
                                    Info</label>
                                <textarea v-model="customer_data.customer_info"
                                    class="mt-1 w-full border-gray-300 rounded border py-2 px-3" rows="2" readonly
                                    @focus="showTooltip('customer_info')" @blur="hideTooltip"></textarea>
                                <div v-if="tooltipVisible && tooltipField === 'customer_info'" class="tooltip">{{
                                    tooltips.customer_info }}</div>
                            </div>

                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Client
                                    name</label>
                                <input type="text" v-model="customer_data.client_name"
                                    class="mt-1 w-full border-gray-300 rounded border py-2 px-3" readonly
                                    @focus="showTooltip('client_name')" @blur="hideTooltip" />
                                <div v-if="tooltipVisible && tooltipField === 'client_name'" class="tooltip">{{
                                    tooltips.client_name }}</div>
                            </div>

                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Mobile
                                    *</label>
                                <input type="text" v-model="customer_data.mobile_number"
                                    class="mt-1 w-full border-gray-300 rounded border py-2 px-3" readonly
                                    @focus="showTooltip('mobile_number')" @blur="hideTooltip" />
                                <div v-if="tooltipVisible && tooltipField === 'mobile_number'" class="tooltip">{{
                                    tooltips.mobile_number }}</div>
                            </div>

                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Phone</label>
                                <input type="text" v-model="customer_data.phone_number"
                                    class="mt-1 w-full border-gray-300 rounded border py-2 px-3" readonly
                                    @focus="showTooltip('phone_number')" @blur="hideTooltip" />
                                <div v-if="tooltipVisible && tooltipField === 'phone_number'" class="tooltip">{{
                                    tooltips.phone_number }}</div>
                            </div>
                        </div>

                        <!-- RMA Repair Info Section -->
                        <div>
                            <h3 class="text-md font-semibold mb-4 border-b py-1 border-b-gray-400">RMA Repair Info</h3>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700" :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">RMA
                                    id *</label>
                                <input type="text" v-model="formValues.rma_id"
                                    class="mt-1 w-full border-gray-300 rounded  border py-2 px-3" readonly
                                    @focus="showTooltip('rma_id')" @blur="hideTooltip" />
                                <div v-if="tooltipVisible && tooltipField === 'rma_id'" class="tooltip">{{
                                    tooltips.rma_id }}</div>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="block text-gray-700 w-1/3">RMA status *</label>
                                <div class="relative inline-block w-full" ref="rmaStatus">
                                    <div @click="toggleDropdown('status')"
                                        class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                        <span class="py-1 px-2 rounded"
                                            :class="formValues.rma_status >= 0 ? options[formValues.rma_status].class : 'text-gray-400'">{{
                                                formValues.rma_status >= 0 ? options[formValues.rma_status].label :
                                                    'Select an option' }}</span>

                                        <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                    </div>

                                    <div v-if="isOpen.status"
                                        class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                        <input type="text" ref="statusInput" v-model="searchTerm.status"
                                            @keydown.enter="handleEnterKeyProduct('status', filteredOptions)"
                                            @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                            @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                            class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                        <ul class="max-h-60 overflow-auto">
                                            <li v-for="(option, index) in filteredOptions" :key="index"
                                                @click="selectOptionData(option)"
                                                class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                                :class="{ 'bg-gray-200': index === selectedIndex }">
                                                <span class="py-1 px-2 rounded" :class="option.class">{{ option.label
                                                }}</span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Technician</label>
                                <div class="relative inline-block w-full" ref="rmaTechnician">
                                    <div @click="toggleDropdown('assign_to')"
                                        class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                        <span class="py-1 px-2"
                                            :class="{ 'text-gray-400': !(formValues.assign_to && Array.isArray(formValues.assign_to) && formValues.assign_to.length > 0) }">
                                            {{ formValues.assign_to && Array.isArray(formValues.assign_to) &&
                                                formValues.assign_to.length > 0 ?
                                                getTechnicianName(formValues.assign_to[0].id) : 'select a assign_to' }}
                                        </span>

                                        <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                    </div>

                                    <div v-if="isOpen.assign_to"
                                        class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                        <input type="text" ref="assign_toInput" v-model="searchTerm.assign_to"
                                            @keydown.enter="handleEnterKeyProduct('assign_to', filteredOptions)"
                                            @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                            @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                            class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                        <ul class="max-h-60 overflow-auto">
                                            <li v-for="(option, index) in filteredOptions" :key="index"
                                                @click="selectOptionData(option)"
                                                class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                                :class="{ 'bg-gray-200': index === selectedIndex }">
                                                <span class="py-1 px-2 rounded">
                                                    {{ option.name }}
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700" :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Date
                                    created</label>
                                <div class="relative inline-block w-full">
                                    <div
                                        class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                        <span class="py-1 px-2">
                                            {{ formatDate(formValues.created_at) }}
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Estimated
                                    completion
                                    date</label>
                                <input type="date" v-datepicker v-model="formValues.estimate_complete_date"
                                    class="mt-1 w-full border-gray-300 rounded  border py-2 px-3" />
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700" :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Date
                                    completed</label>
                                <input type="date" v-datepicker v-model="formValues.complete_date"
                                    class="mt-1 w-full border-gray-300 rounded  border py-2 px-3" />
                            </div>
                        </div>
                    </div>
                    <!-- Product Info Section -->
                    <div v-if="selected_option === 2" class="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6">
                        <div class="p-1">
                            <h2 class="font-semibold text-md mb-4 border-b py-1 border-b-gray-400">Product Info</h2>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Product
                                    for repair *</label>
                                <input v-if="formValues.product && formValues.product.product_name" type="text"
                                    v-model="formValues.product.product_name"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    readonly @focus="showTooltip('product')" @blur="hideTooltip" />
                                <div v-if="tooltipVisible && tooltipField === 'product'" class="tooltip">
                                    {{ tooltips.product ? tooltips.product : 'Cannot be change' }}
                                </div>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700" :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Brand
                                    *</label>
                                <div class="relative inline-block w-full" ref="rmaBrand">
                                    <div @click="toggleDropdown('brand')"
                                        class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                        <span class="py-1 px-2"
                                            :class="{ 'text-gray-400': !(formValues.brand_id && formValues.brand_id !== '') }">
                                            {{ formValues.brand_id && formValues.brand_id !== '' ?
                                                getBrandName(formValues.brand_id) : 'select a brand' }}
                                        </span>

                                        <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                    </div>

                                    <div v-if="isOpen.brand"
                                        class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                        <input type="text" ref="brandInput" v-model="searchTerm.brand"
                                            @keydown.enter="handleEnterKeyProduct('brand', filteredOptions)"
                                            @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                            @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                            class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                        <ul class="max-h-60 overflow-auto">
                                            <li v-for="(option, index) in filteredOptions" :key="index"
                                                @click="selectOptionData(option)"
                                                class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                                :class="{ 'bg-gray-200': index === selectedIndex }">
                                                <span class="py-1 px-2 rounded">
                                                    {{ option.brand_name }}
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700" :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">
                                    Supplier*</label>
                                <div class="relative inline-block w-full" ref="rmaSupplier">
                                    <div @click="toggleDropdown('supplier')"
                                        class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                        <span class="py-1 px-2"
                                            :class="{ 'text-gray-400': !(formValues.supplier_id && formValues.supplier_id !== '') }">
                                            {{ formValues.supplier_id && formValues.supplier_id !== '' ?
                                                getSupplierName(formValues.supplier_id) : 'select a supplier' }}
                                        </span>

                                        <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                    </div>

                                    <div v-if="isOpen.supplier"
                                        class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                        <input type="text" ref="supplierInput" v-model="searchTerm.supplier"
                                            @keydown.enter="handleEnterKeyProduct('supplier', filteredOptions)"
                                            @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                            @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                            class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                        <ul class="max-h-60 overflow-auto">
                                            <li v-for="(option, index) in filteredOptions" :key="index"
                                                @click="selectOptionData(option)"
                                                class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                                :class="{ 'bg-gray-200': index === selectedIndex }">
                                                <span class="py-1 px-2 rounded">
                                                    {{ option.name }}
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Serial
                                    numbers *</label>
                                <input type="text" v-model="formValues.serial_number"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Warranty</label>
                                <div class="relative inline-block w-full" ref="rmaWarranty">
                                    <input type="number" id="warranty" v-model="formValues.warranty"
                                        class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                                    <!-- <div @click="toggleDropdown('warranty')"
                                        class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                        <span class="py-1 px-2"
                                            :class="{ 'text-gray-400': !(formValues.warranty !== '') }">
                                            {{ formValues.warranty >= 0 ?
                                                option_waranty[formValues.warranty] : 'select a warranty' }}
                                        </span>

                                        <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                    </div>

                                    <div v-if="isOpen.warranty"
                                        class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                        <input type="text" ref="warrantyInput" v-model="searchTerm.warranty"
                                            @keydown.enter="handleEnterKeyProduct('warranty', filteredOptions)"
                                            @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                            @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                            class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                        <ul class="max-h-60 overflow-auto">
                                            <li v-for="(option, index) in filteredOptions" :key="index"
                                                @click="selectOptionData(option)"
                                                class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                                :class="{ 'bg-gray-200': index === selectedIndex }">
                                                <span class="py-1 px-2 rounded">
                                                    {{ option }}
                                                </span>
                                            </li>
                                        </ul>
                                    </div> -->
                                </div>
                            </div>
                            <div class="mb-8 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Product
                                    condition</label>
                                <input type="text" v-model="formValues.product_condition"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Accessories</label>
                                <div
                                    class="absolute right-0 border text-xs border-green-700 text-green-700 px-2 -top-[25px] rounded">
                                    <button @click="openAccessories">Add +</button>
                                </div>
                                <div class="relative inline-block w-full" ref="rmaAccessories">
                                    <div
                                        class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex flex-wrap items-center px-4 py-2">
                                        <!-- <span class="py-1 px-2"
                                            :class="{ 'text-gray-400': !(formValues.accessories && formValues.accessories !== '') }">
                                            {{ formValues.accessories && Array.isArray(formValues.accessories)  ?
                                                options_accessories[formValues.accessories] : 'select a accessories' }}
                                        </span>
                                        <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" /> -->
                                        <div class="flex items-center bg-gray-500 text-white rounded px-2 py-1 mr-2 mb-2"
                                            v-if="formValues.accessories && Array.isArray(formValues.accessories) && formValues.accessories.length > 0"
                                            v-for="(opt, i) in formValues.accessories">
                                            {{ opt.name }}
                                            <button @click="removeOption(opt)" class="ml-1">x</button>
                                        </div>
                                        <div>
                                            <input @click="toggleDropdown('accessories')" placeholder="click here..."
                                                class="px-2 py-1 rounded border" />
                                        </div>
                                    </div>

                                    <div v-if="isOpen.accessories"
                                        class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                        <input type="text" ref="accessoriesInput" v-model="searchTerm.accessories"
                                            @keydown.enter="handleEnterKeyProduct('accessories', filteredOptions)"
                                            @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                            @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                            class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                        <ul class="max-h-60 overflow-auto">
                                            <li v-for="(option, index) in filteredOptions" :key="index"
                                                @click="selectOptionData(option)"
                                                class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                                :class="{ 'bg-gray-200': index === selectedIndex }">
                                                <span class="py-1 px-2 rounded">
                                                    {{ option.name }}
                                                </span>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Shelf</label>
                                <input type="text" v-model="formValues.shelf"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                        </div>

                        <!-- Repair Info Section -->
                        <div class="p-1">
                            <h2 class="font-semibold text-md mb-4 border-b py-1 border-b-gray-400">Repair Info</h2>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex flex-wrap items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Problem
                                    description</label>
                                <textarea v-model="formValues.problem_description"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    rows="4"></textarea>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex flex-wrap items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Technician notes</label>
                                <textarea v-model="formValues.technician_notes"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    rows="4"></textarea>
                            </div>
                            <div class="mb-4 space-x-1 relative"
                                :class="{ 'flex flex-wrap items-center': !isAndroid, 'flex flex-wrap items-center': isAndroid }">
                                <!--Internal notes-->
                                <label class="text-gray-700"
                                    :class="{ 'w-1/3': !isAndroid, 'w-full': isAndroid }">Courier Name / Number</label>
                                <textarea v-model="formValues.internal_notes"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    rows="4"></textarea>
                            </div>
                        </div>
                    </div>
                    <!-- Custom Fields Section -->
                    <div v-if="selected_option === 2" class="p-1">
                        <h2 class="font-semibold text-md mb-4 border-b py-1 border-b-gray-400">Custom Fields</h2>
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-6 mb-4">
                            <div>
                                <label class="text-gray-700">Customer Invoice</label>
                                <input type="text" v-model="formValues.customer_invoice"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label class="text-gray-700">Date of Purchase</label>
                                <input type="date" v-datepicker v-model="formValues.date_of_purchase"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label class="text-gray-700">Supplier RMA</label>
                                <input type="text" v-model="formValues.supplier_rma"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                            </div>
                            <div>
                                <label class="text-gray-700">Entered under IPR</label>
                                <input type="text" v-model="formValues.under_ipr"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    value="">
                            </div>
                            <div>
                                <label class="text-gray-700">Received Serial Number</label>
                                <input type="text" v-model="formValues.received_serial_no"
                                    class="mt-1 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                                    value="" placeholder="Enter received serial number" />
                            </div>
                        </div>
                    </div>

                    <!-- Repair Costs Section -->
                    <div v-if="selected_option === 3 || selected_option === 6" class="mb-4">
                        <h2 class="font-semibold text-md mb-4 border-b py-1 border-b-gray-400">Repair Costs</h2>
                        <div class="grid grid-cols-2 sm:grid-cols-3 gap-2 sm:gap-4">
                            <div v-if="selected_option === 3">
                                <label class="text-gray-700">Cost</label>
                                <div class="mt-1 flex items-center relative">
                                    <span class="absolute left-3 text-gray-500">{{ currentCompanyList &&
                                        currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}</span>
                                    <input type="number" v-model="formValues.cost" @input="calculateAllPayment"
                                        class="mt-1 pl-7 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>
                            <div v-if="selected_option === 3">
                                <label class="text-gray-700">Shipping</label>
                                <div class="mt-1 flex items-center relative">
                                    <span class="absolute left-3 text-gray-500">{{ currentCompanyList &&
                                        currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}</span>
                                    <input type="number" v-model="formValues.shipping" @input="calculateAllPayment"
                                        class="block w-full pl-7 pr-3 py-2 border border-gray-300 rounded shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                </div>
                            </div>
                            <div v-if="selected_option === 3">
                                <label class="text-gray-700">Tax</label>
                                <div class="mt-1 flex items-center relative">
                                    <span class="absolute left-3 text-gray-500">{{ currentCompanyList &&
                                        currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}</span>
                                    <input type="number" v-model="formValues.tax"
                                        class="mt-1 pl-7 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-200"
                                        readonly>
                                </div>
                            </div>
                            <div>
                                <label class="text-gray-700">Total Cost</label>
                                <div class="mt-1 flex items-center relative">
                                    <span class="absolute left-3 text-gray-500">{{ currentCompanyList &&
                                        currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}</span>
                                    <input type="number" v-model="formValues.total_cost"
                                        class="mt-1 pl-7 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-200"
                                        readonly>
                                </div>
                            </div>
                            <div>
                                <label class="text-gray-700">Total Payments</label>
                                <div class="mt-1 flex items-center relative">
                                    <span class="absolute left-3 text-gray-500">{{ currentCompanyList &&
                                        currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}</span>
                                    <input type="number" v-model="formValues.total_payments"
                                        class="mt-1 pl-7 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-200"
                                        readonly>
                                </div>
                            </div>
                            <div>
                                <label class="text-gray-700">Balance Due</label>
                                <div class="mt-1 flex items-center relative">
                                    <span class="absolute left-3 text-gray-500">{{ currentCompanyList &&
                                        currentCompanyList.currency === 'INR' ? '\u20b9' :
                                        currentCompanyList.currency }}</span>
                                    <input type="number" v-model="formValues.balance_due"
                                        class="mt-1 pl-7 block w-full border border-gray-300 rounded py-2 px-3 shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm bg-gray-200"
                                        readonly>
                                </div>
                            </div>
                        </div>
                        <!---return amount-->
                        <div v-if="formValues.return_amount" class="py-2">
                            <p class="text-green-600">Return Amount: <span class="text-gray-500">{{ currentCompanyList
                                &&
                                currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency}}</span> {{
                                        this.formValues.return_amount }}
                            </p>
                        </div>

                        <h2 v-if="selected_option === 3"
                            class="font-semibold text-md mb-4 border-b py-1 border-b-gray-400 mt-4">Included Products
                        </h2>
                        <div v-if="selected_option === 3" class="overflow-auto py-2">
                            <table class="min-w-full divide-y divide-gray-20 overflow-auto">
                                <thead class="bg-gray-50">
                                    <tr class="text-center">
                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Description
                                        </th>
                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Quantity
                                        </th>
                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Unit price
                                        </th>
                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            Total
                                        </th>
                                        <th scope="col"
                                            class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                            <span class="">Actions</span>
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-white divide-y divide-gray-200">
                                    <tr v-if="formValues.rma_items && Array.isArray(formValues.rma_items) && formValues.rma_items.length > 0"
                                        v-for="(opt, index) in formValues.rma_items" :key="index"
                                        class="text-center border-b">
                                        <td class="px-2 py-2 whitespace-nowrap">{{ opt.description }}</td>
                                        <td class="px-2 py-2 whitespace-nowrap">{{ opt.quantity }}</td>
                                        <td class="px-2 py-2 whitespace-nowrap">
                                            <span class="text-gray-500">{{ currentCompanyList &&
                                                currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }}</span>
                                            {{ opt.unit_price }}
                                        </td>
                                        <td class="px-2 py-2 whitespace-nowrap">
                                            <span class="text-gray-500">{{ currentCompanyList &&
                                                currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }}</span> {{ opt.total }}
                                        </td>
                                        <td class="px-2 py-2 whitespace-nowrap text-sm font-medium">
                                            <button @click="openConfirmBox('rma_items', index)"
                                                class="text-red-600 hover:text-red-900">
                                                <font-awesome-icon icon="fa-solid fa-xmark" />
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                            <div class="mt-4 flex items-center justify-between">
                                <button @click="addproductItems"
                                    class="bg-blue-500 hover:bg-teal-600 text-white py-2 px-4 rounded">
                                    Add a Product
                                </button>
                                <div class="text-gray-600">
                                    Products Total: <span class="font-semibold">
                                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                            currentCompanyList.currency }} {{ calculateTotalCost() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Included Products Section -->
                    <div v-if="selected_option === 5" class="mb-4">
                        <h2 class="font-semibold text-md mb-4 border-b py-1 border-b-gray-400">Additional RMA Products
                            for Repair</h2>
                        <div class="border rounded p-4">
                            <div class="mb-4 overflow-auto py-2">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr class="text-center">
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Part Code
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Description
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Serials
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Quantity
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <span class="">Actions</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-if="formValues.rma_additional_products && Array.isArray(formValues.rma_additional_products) && formValues.rma_additional_products.length > 0"
                                            v-for="(opt, index) in formValues.rma_additional_products" :key="index"
                                            class="text-center border-b">
                                            <td class="px-2 py-2 whitespace-nowrap">{{ opt.part_code }}</td>
                                            <td class="px-2 py-2 whitespace-nowrap">{{ opt.description }}</td>
                                            <td class="px-2 py-2 whitespace-nowrap">
                                                {{ opt.serials }}
                                            </td>
                                            <td class="px-2 py-2 whitespace-nowrap">
                                                {{ opt.quantity }}
                                            </td>
                                            <td class="px-2 py-2 whitespace-nowrap text-sm font-medium">
                                                <button @click="openConfirmBox('rma_additional_products', index)"
                                                    class="text-red-600 hover:text-red-900">
                                                    <font-awesome-icon icon="fa-solid fa-xmark" />
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>

                            </div>
                            <button class="px-4 py-2 bg-blue-500 text-white rounded mb-4" @click="openAdditional">Add A
                                Product</button>
                        </div>
                    </div>

                    <!-- Attachments Section -->
                    <div v-if="selected_option === 4" class="mb-4">
                        <h2 class="font-semibold text-md mb-4 border-b py-1 border-b-gray-400">Attachments</h2>
                        <div class="border rounded p-4">
                            <div v-if="formValues && formValues.attachments && formValues.attachments.length > 0"
                                class="flex flex-wrap gap-4">
                                <div v-for="(image, index) in formValues.attachments" :key="index"
                                    class="relative w-48 h-48">
                                    <div
                                        class="relative w-full h-full overflow-hidden rounded-lg border border-gray-300">
                                        <img :src="image.attachment" alt="Image" class="object-cover w-full h-full">
                                        <button @click="openConfirmBox('attachments', index)"
                                            class="absolute top-2 right-2 bg-gray-700 bg-opacity-50 text-white rounded-full w-6 h-6 flex items-center justify-center">
                                            &times;
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <button class="px-4 py-2 my-4 bg-blue-500 text-white rounded mb-4"
                                @click="openAttachment">Add
                                an
                                Attachment</button>
                        </div>
                    </div>

                    <!-- Payments Section -->
                    <div v-if="selected_option === 6" class="mb-4">
                        <h2 class="font-semibold text-md mb-4 border-b py-1 border-b-gray-400">Payments</h2>
                        <div class="border rounded p-4">
                            <div class="mb-4 overflow-auto py-2">
                                <table class="min-w-full divide-y divide-gray-200">
                                    <thead class="bg-gray-50">
                                        <tr class="text-center">
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Method
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Date
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Amount
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Reference
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                Notes
                                            </th>
                                            <th scope="col"
                                                class="px-6 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider">
                                                <span class="">Actions</span>
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-white divide-y divide-gray-200">
                                        <tr v-if="formValues.rma_payments && Array.isArray(formValues.rma_payments) && formValues.rma_payments.length > 0"
                                            v-for="(opt, index) in formValues.rma_payments" :key="index"
                                            class="text-center border-b">
                                            <td class="px-2 py-2 whitespace-nowrap">{{ opt.method }}</td>
                                            <td class="px-2 py-2 whitespace-nowrap">{{ formatDateEstimate(opt.date) }}
                                            </td>
                                            <td class="px-2 py-2 whitespace-nowrap">
                                                {{ opt.amount }}
                                            </td>
                                            <td class="px-2 py-2 whitespace-nowrap">
                                                {{ opt.reference }}
                                            </td>
                                            <td class="px-2 py-2 whitespace-nowrap">
                                                {{ opt.notes }}
                                            </td>
                                            <td class="px-2 py-2 whitespace-nowrap text-sm font-medium">
                                                <button @click="openConfirmBox('rma_payments', index)"
                                                    class="text-red-600 hover:text-red-900">
                                                    <font-awesome-icon icon="fa-solid fa-xmark" />
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <button class="px-4 py-2 bg-blue-500 text-white rounded mb-4" @click="openTotalPayment">Add
                                A Payment</button>
                        </div>
                    </div>
                    <div class="flex justify-end items-center mt-4">
                        <div class="flex space-x-2">
                            <!--notifications-->
                            <div class="flex items-center">
                                <label v-if="companywhatsapp" class="flex justify-center items-center mr-2">
                                    <div class="relative w-12 h-6 rounded-full transition-colors duration-300 flex items-center"
                                        @click="iswhatsapp = !iswhatsapp"
                                        :class="{ 'bg-green-600': iswhatsapp, 'bg-gray-300': !iswhatsapp }">
                                        <div class="absolute top-0 left-0 w-6 h-6 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                            :class="{ 'translate-x-6': iswhatsapp, 'translate-x-0': !iswhatsapp }">
                                        </div>
                                    </div>
                                    <font-awesome-icon icon="fa-brands fa-whatsapp" size="xl" class="ml-2"
                                        style="color:green" />
                                </label>
                                <button v-else @click="navigateToWhatsApp" class="text-red-600">Connect
                                    <font-awesome-icon icon="fa-brands fa-whatsapp" size="xl" class="ml-2"
                                        style="color:red" /></button>
                                <label class="flex justify-center items-center ml-2">
                                    <div class="relative w-12 h-6 rounded-full transition-colors duration-300 flex items-center"
                                        @click="issms = !issms"
                                        :class="{ 'bg-green-600': issms, 'bg-gray-300': !issms }">
                                        <div class="absolute top-0 left-0 w-6 h-6 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                            :class="{ 'translate-x-6': issms, 'translate-x-0': !issms }">
                                        </div>
                                    </div>
                                    <font-awesome-icon icon="fa-solid fa-comment-sms" size="xl" class="ml-2"
                                        style="color:blueviolet" />
                                </label>
                                <p v-if="!isMobile" class="ml-1 text-xs">Send alert to Customer</p>
                                <div>
                                    <div class=" ml-1" @mouseover="tooltip.info_notify = true"
                                        @mouseleave="tooltip.info_notify = false">
                                        <font-awesome-icon icon="fa-solid fa-circle-info" size="lg"
                                            class="text-blue-800" />
                                    </div>
                                    <!---tooltip-->
                                    <div v-if="tooltip.info_notify"
                                        class="absolute flex flex-col items-center group-hover:flex -mt-14 -ml-[55px]">
                                        <span
                                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                                            <p>sent alert to customer</p>
                                        </span>
                                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                                    </div>
                                </div>
                            </div>
                            <button
                                class="px-4 py-2 shadow-inner shadow-blue-100 border border-blue-500 bg-blue-500 text-white rounded"
                                @click="saveRMAData"><font-awesome-icon icon="fa-regular fa-floppy-disk" />
                                Save</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'categories'"></bottombar> -->
        <accessories :showModal="open_accessories" @close-modal="closeAccessories" :type="'Accessories Management'"
            :categoriesData="currentAccessoriesList">
        </accessories>
        <productItemCostRma :showModal="open_product_cost" @closeModal="closeRmaProductcost"
            :currentItems="currentItems">
        </productItemCostRma>
        <confirmbox :show-modal="delete_data" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <rmaattachments :showModal="open_rma_attachment" @close-modal="closeAttachments" :companyId="companyId">
        </rmaattachments>
        <additionalProductRma :showModal="open_additional_product" @close-modal="closeAdditional"
            :currentItems="currentItems">
        </additionalProductRma>
        <rmaTotalPayment :showModal="open_total_payment" @close-modal="closeTotalPayment"></rmaTotalPayment>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <Loader :showModal="open_loader"></Loader>
        <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
    </div>
</template>
<script>
import { mapActions, mapGetters } from 'vuex';
// import bottombar from '../../dashboard/bottombar.vue';
import accessories from '@/components/supporting/dialog_box/accessories.vue';
import productItemCostRma from '../../dialog_box/productItemCostRma.vue';
import confirmbox from '../../dialog_box/confirmbox.vue';
import rmaattachments from '../../dialog_box/rmaattachments.vue';
import additionalProductRma from '../../dialog_box/additionalProductRma.vue';
import rmaTotalPayment from '../../dialog_box/rmaTotalPayment.vue';
import noAccessModel from '../../dialog_box/noAccessModel.vue';
export default {
    components: {
        // bottombar,
        accessories,
        productItemCostRma,
        confirmbox,
        rmaattachments,
        additionalProductRma,
        rmaTotalPayment,
        noAccessModel
    },
    props: {
        isMobile: Boolean,
        isAndroid: Boolean,
        updateModalOpen: Boolean
    },
    emits: ['updateIsOpen'],
    data() {
        return {
            selected_option: 1,
            quick_filter: false,
            filter_option: ['Option 1', 'Option 2', 'Option 3'], // Example options
            formValues: {},
            isOpen: { status: false, assign_to: false, accessories: false, warranty: false, brand: false, supplier: false },
            searchTerm: { status: '', assign_to: '', accessories: '', warranty: '', brand: '', supplier: '' },
            selectedOption: { status: null, assign_to: null, accessories: null, warranty: null, brand: null, supplier: null },
            customer_data: {},
            delete_data: false,
            confirm_box: { rma_items: false, attachments: false, rma_additional_products: false },
            confirm_index: null,
            selectedIndex: 0,
            options_accessories: [
                { value: 1, label: 'Software' },
                { value: 2, label: 'Case' },
                { value: 3, label: 'Charger' },
                { value: 4, label: 'Battery' },
                { value: 5, label: 'Monitor' },
                { value: 6, label: 'Keyboard' },
                { value: 7, label: 'Mouse' }
            ],
            options: [
                { value: 1, label: 'Awaiting Customer Confirmation', class: 'bg-green-500', labelClass: 'ml-2' },
                { value: 2, label: 'Awaiting Parts', class: 'bg-purple-500 text-white', labelClass: 'ml-2' },
                { value: 3, label: 'Awaiting Repair', class: 'bg-orange-500', labelClass: 'ml-2' },
                { value: 4, label: 'Awaiting Supplier', class: 'bg-[#ffb6c1]', labelClass: 'ml-2' },
                { value: 5, label: 'Awaiting to be sent to Supplier', class: 'bg-indigo-500 text-white', labelClass: 'ml-2' },
                { value: 6, label: 'Credit', class: 'bg-gray-300', labelClass: 'ml-2' },
                { value: 7, label: 'Ready to Deliver', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 8, label: 'Repair Completed', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 9, label: 'Repair in Process', class: 'bg-[#87b14b]', labelClass: 'ml-2' },
                { value: 10, label: 'Repaired/Replacement from Supplier', class: 'bg-[#ff7f50]', labelClass: 'ml-2' },
                { value: 11, label: 'Sent to Customer', class: 'bg-blue-600 text-white', labelClass: 'ml-2' },
                { value: 12, label: 'Sent to Supplier', class: 'bg-[#ffc0cb]', labelClass: 'ml-2' },
                { value: 13, label: 'Waiting New Battery', class: 'bg-red-600 text-white', labelClass: 'ml-2' },
                { value: 14, label: 'Delivered', class: 'bg-green-600 text-white', labelClass: 'ml-2' },
                { value: 15, label: 'Cancel', class: 'bg-red-600 text-white', labelClass: 'ml-2' },
            ],
            option_waranty: ['3 Month', '6 Month', '1 Year', '2 Year', '3 Year', 'no warranty'],
            pagination: {},

            //--api integration---
            companyId: null,
            userId: null,
            //--skeleton
            open_skeleton: false,
            number_of_columns: 2,
            number_of_rows: 20,
            gap: 5,
            //---tooltip---
            tooltipVisible: false,
            tooltipField: '',
            tooltips: {
                customer: 'Customer cannot change',
                customer_info: 'Cannot chage',
                client_name: 'Cannot chage',
                mobile_number: 'Cannot chage',
                phone_number: 'Cannot chage',
                rma_id: 'Id cannot be change',
            },
            //---accessories---
            open_accessories: false,
            open_product_cost: false,
            open_rma_attachment: false,
            open_additional_product: false,
            open_total_payment: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            open_loader: false,
            //--pdfurl--
            pdfUrl: '',
            //--dropdown--
            dropdownOpen: false,
            dropdownOpenPrint: false,
            //---no access---
            no_access: false,
            //--notifications---
            iswhatsapp: false,
            issms: false,
            //---tooltip---
            tooltip: {},
        };
    },
    mounted() {
        /// Set base URL based on hostname
        const hostname = window.location.hostname;
        let baseURL;

        if (hostname === 'app.track-new.com') {
            // Production URL
            baseURL = 'https://api.track-new.com/api';
        } else if (hostname === 'devapp.track-new.com') {
            // Development URL
            baseURL = 'https://devapi.track-new.com/api';
        } else if (hostname === ' ************' || hostname === ' ************:8000') {
            // Local development URL (adjust as needed)
            baseURL = 'https://devapi.track-new.com/api';
            // baseURL = 'https://api.track-new.com/api';

        } else {
            // Default fallback URL
            baseURL = 'https://devapi.track-new.com/api';
        }
        this.pdfUrl = baseURL;
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.parseUrl();
        this.fetchCompanyList();
        if (this.currentEmployee && this.currentEmployee.length > 0) {
            this.fetchEmployeeList();
        } else {
            this.fetchEmployeeList();
        }
        this.fetchWhatsappList();
        if (this.currentInvoice && this.currentInvoice.length > 0) {
            if (this.currentInvoice && Array.isArray(this.currentInvoice) && this.currentInvoice.length > 0) {
                this.issms = this.currentInvoice[0].is_sms >= 0 ? this.currentInvoice[0].is_sms ? true : false : false;
                this.iswhatsapp = this.companywhatsapp ? this.currentInvoice[0].is_whatsapp >= 0 ? this.currentInvoice[0].is_whatsapp ? true : false : false : false;
            }
            this.fetchInvoiceSetting();
        } else {
            this.fetchInvoiceSetting();
        }
        // if (this.currentCustomer && this.currentCustomer.length === 0) {
        //     this.fetchCustomerList();
        // } else {
        //     this.fetchCustomerList();
        // }
        if ((!this.currentItems || this.currentItems.length === 0) && this.companyId) {
            this.getProductListData(1, 2);
            if (this.currentItems && this.currentItems.length > 0) {
                this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
            }
            this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
        }
        if (this.currentBrandList && this.currentBrandList.length === 0) {
            this.fetchBrandList();
        } else {
            this.fetchBrandList();
        }
        if (this.currentSupplier && this.currentSupplier.length === 0) {
            this.fetchISupplierList();
        } else {
            this.fetchISupplierList();
        }
        if (this.currentAccessoriesList && this.currentAccessoriesList.length === 0) {
            this.fetchAccessoriesList();
        } else {
            this.fetchAccessoriesList();
        }
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside($event, 'all'));
        document.removeEventListener('click', this.handleClick);
        document.removeEventListener('click', this.handleClickPrint);
    },
    computed: {
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('items', ['currentItems', 'currentItemsPagination']),
        ...mapGetters('brandUnitCategoryItem', ['currentBrandList']),
        ...mapGetters('supplier', ['currentSupplier']),
        ...mapGetters('employess', ['currentEmployee']),
        ...mapGetters('accessories', ['currentAccessoriesList']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        filteredOptions() {
            if (this.isOpen.status) {
                return this.options.filter(option =>
                    option.label.toLowerCase().includes(this.searchTerm.status.toLowerCase())
                );
            } else if (this.isOpen.assign_to && this.currentEmployee && this.currentEmployee.length > 0) {
                return this.currentEmployee.filter(option =>
                    option.name.toLowerCase().includes(this.searchTerm.assign_to.toLowerCase())
                );
            } else if (this.isOpen.brand && this.currentBrandList && this.currentBrandList.length > 0) {
                return this.currentBrandList.filter(option =>
                    option.brand_name.toLowerCase().includes(this.searchTerm.brand.toLowerCase())
                );
            } else if (this.isOpen.supplier && this.currentSupplier && this.currentSupplier.length > 0) {
                return this.currentSupplier.filter(option =>
                    option.name.toLowerCase().includes(this.searchTerm.supplier.toLowerCase())
                );
            } else if (this.isOpen.warranty && this.option_waranty && this.option_waranty.length > 0) {
                return this.option_waranty.filter(option =>
                    option.toLowerCase().includes(this.searchTerm.warranty.toLowerCase())
                );
            } else if (this.isOpen.accessories && this.currentAccessoriesList && this.currentAccessoriesList.length > 0) {
                return this.currentAccessoriesList.filter(option =>
                    option.name.toLowerCase().includes(this.searchTerm.accessories.toLowerCase()) && !this.isSelected(option)
                );
            }
        },
    },
    methods: {
        ...mapActions('employess', ['fetchEmployeeList']),
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('items', ['fetchItemList']),
        ...mapActions('brandUnitCategoryItem', ['fetchBrandList']),
        ...mapActions('supplier', ['fetchISupplierList']),
        ...mapActions('accessories', ['fetchAccessoriesList']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),
        ...mapActions('rmaList', ['fetchRmasList']),
        handleClickOutside(event, type) {
            if (type !== 'all') {
                if (this.isOpen.status) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaStatus;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.status = false;
                    }
                }
                else if (this.isOpen.assign_to) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaTechnician;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.assign_to = false;
                    }
                }
                else if (this.isOpen.brand) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaBrand;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.brand = false;
                    }
                } else if (this.isOpen.supplier) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaSupplier;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.supplier = false;
                    }
                } else if (this.isOpen.warranty) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaWarranty;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.warranty = false;
                    }
                } else if (this.isOpen.accessories) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaAccessories;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.accessories = false;
                    }
                }

            } else {
                this.isOpen = { status: false, assign_to: false, accessories: false, warranty: false, brand: false, supplier: false };
            }
        },

        toggleDropdown(type) {
            if (type === 'status') {
                this.isOpen.status = !this.isOpen.status;
                this.isOpen.assign_to = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.status) {
                    this.$nextTick(() => {
                        this.$refs.statusInput.focus();
                    });
                }
            }
            else if (type === 'assign_to') {
                this.isOpen.assign_to = !this.isOpen.assign_to;
                this.isOpen.status = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.assign_to) {
                    this.$nextTick(() => {
                        this.$refs.assign_toInput.focus();
                    });
                }
            }
            else if (type === 'brand') {
                this.isOpen.brand = !this.isOpen.brand;
                this.isOpen.supplier = false;
                this.isOpen.warranty = false;
                this.isOpen.accessories = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.brand) {
                    this.$nextTick(() => {
                        this.$refs.brandInput.focus();
                    });
                }
            } else if (type === 'supplier') {
                this.isOpen.supplier = !this.isOpen.supplier;
                this.isOpen.brand = false;
                this.isOpen.warranty = false;
                this.isOpen.accessories = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.supplier) {
                    this.$nextTick(() => {
                        this.$refs.supplierInput.focus();
                    });
                }
            } else if (type === 'warranty') {
                this.isOpen.warranty = !this.isOpen.warranty;
                this.isOpen.brand = false;
                this.isOpen.supplier = false;
                this.isOpen.accessories = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.warranty) {
                    this.$nextTick(() => {
                        this.$refs.warrantyInput.focus();
                    });
                }
            } else if (type === 'accessories') {
                this.isOpen.accessories = !this.isOpen.accessories;
                this.isOpen.brand = false;
                this.isOpen.supplier = false;
                this.isOpen.warranty = false;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.accessories) {
                    this.$nextTick(() => {
                        this.$refs.accessoriesInput.focus();
                    });
                }
            }
        },
        selectOptionData(option) {
            if (this.isOpen.status) {
                this.formValues.rma_status = option.value - 1;
                this.isOpen.status = false;
                this.searchTerm.status = '';
            } else if (this.isOpen.assign_to) {
                this.formValues.assign_to = [{ id: option.id }];
                this.isOpen.assign_to = false;
                this.searchTerm.assign_to = '';
            } else if (this.isOpen.brand) {
                this.formValues.brand_id = option.id;
                this.isOpen.brand = false;
                this.searchTerm.brand = '';
            } else if (this.isOpen.supplier) {
                this.formValues.supplier_id = option.id;
                this.isOpen.supplier = false;
                this.searchTerm.supplier = '';
            } else if (this.isOpen.warranty) {
                this.formValues.warranty = this.option_waranty.findIndex(opt => opt == option);
                this.isOpen.warranty = false;
                this.searchTerm.warranty = '';
            } else if (this.isOpen.accessories) {
                if (this.formValues.accessories && Array.isArray(this.formValues.accessories)) {
                    this.formValues.accessories.push(option);
                } else {
                    this.formValues.accessories = [option];
                }
                // this.formValues.accessories = this.formValues.accessories && this.formValues.accessories !== '' ? [...this.formValues.accessories, option] : [];
                this.isOpen.accessories = false;
                this.searchTerm.accessories = '';
            }
        },
        quickFilterdata() {
            this.quick_filter = !this.quick_filter;
        },
        selectOption(data) {
            this.selected_option = data;
        },
        removeOption(option) {
            this.formValues.accessories = this.formValues.accessories.filter(o => o.id !== option.id);
        },
        isSelected(option) {
            if (this.formValues.accessories && Array.isArray(this.formValues.accessories) && this.formValues.accessories.length > 0) {
                return this.formValues.accessories.some(o => o.id === option.id);
            }
        },
        async parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const checkEditOrAdd = urlParts[urlParts.length - 1];

            if (checkEditOrAdd === 'edit') {
                const id = urlParts[urlParts.length - 2];
                if (id) {
                    this.getRmaList(id);
                }
            }

        },
        getRmaList(id) {
            this.open_skeleton = true;
            axios.get(`/rmas/${id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    this.open_skeleton = false;
                    this.formValues = response.data.data;
                    if (this.formValues && this.formValues.customer_id) {
                        const firstName = this.formValues.customer.first_name || '';
                        const lastName = this.formValues.customer.last_name || '';
                        const address = this.formValues.address || '';
                        const cityName = this.formValues.city_name || '';
                        const districtName = this.formValues.district_name || '';
                        const stateName = this.formValues.state_name || '';
                        const pincode = this.formValues.pincode || '';
                        const email = this.formValues.email ? `email: ${this.formValues.email}\n` : '';
                        const contactNumber = this.formValues.contact_number ? `mobile: ${this.formValues.contact_number}\n` : '';

                        this.customer_data.name = `${firstName} ${lastName}`.trim();
                        this.customer_data.customer_info = `${address}\n${cityName}\n${districtName}\n${stateName}\n${pincode}\n${email}${contactNumber}`.trim();
                        this.customer_data.client_name = `${firstName} ${lastName}`.trim();
                        this.customer_data.mobile_number = this.formValues.customer.contact_number || '';
                        this.customer_data.phone_number = this.formValues.customer.alternative_number || '';
                    }
                    if (this.formValues.date_of_purchase) {
                        this.formValues.date_of_purchase = this.convertToDateString(this.formValues.date_of_purchase);
                    }
                    if (this.formValues.estimate_complete_date) {
                        this.formValues.estimate_complete_date = this.convertToDateString(this.formValues.estimate_complete_date);
                    }
                    if (this.formValues.complete_date) {
                        this.formValues.complete_date = this.convertToDateString(this.formValues.complete_date);
                    }
                    if (this.formValues.attachments && JSON.parse(this.formValues.attachments).length > 0) {
                        this.formValues.attachments = JSON.parse(this.formValues.attachments);
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        //--select tectnician name---
        getTechnicianName(id) {
            if (this.currentEmployee && this.currentEmployee.length > 0) {
                let find_index = this.currentEmployee.findIndex(opt => opt.id === id);
                if (find_index !== -1) {
                    return this.currentEmployee[find_index].name;
                }
            }
        },
        //---selected brand-name--
        getBrandName(id) {
            if (this.currentBrandList && this.currentBrandList.length > 0) {
                let find_index = this.currentBrandList.findIndex(opt => opt.id === id);
                if (find_index !== -1) {
                    return this.currentBrandList[find_index].brand_name;
                }
            }
        },
        getSupplierName(id) {
            if (this.currentSupplier && this.currentSupplier.length > 0) {
                let find_index = this.currentSupplier.findIndex(opt => opt.id === id);
                if (find_index !== -1) {
                    return this.currentSupplier[find_index].name;
                }
            }
        },
        formatDate(dateString) {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return 'Invalid Date';
            }

            const day = String(date.getUTCDate()).padStart(2, '0');
            const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are zero-indexed
            const year = date.getUTCFullYear();

            let hours = date.getUTCHours();
            const minutes = String(date.getUTCMinutes()).padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';

            hours = hours % 12;
            hours = hours ? hours : 12; // the hour '0' should be '12'

            const formattedDate = `${day}/${month}/${year}`;
            const formattedTime = `${String(hours).padStart(2, '0')}:${minutes}${ampm}`;

            return `${formattedDate} ${formattedTime}`;
        },
        //get product list---
        getProductListData(page, per_page) {
            axios.get('/products_details', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    this.pagination.product = response.data.pagination;
                    this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);

                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        handleEnterKeyProduct(type, list_data) {
            if (type === 'status') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            } else if (type === 'assign_to') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            } else if (type === 'brand') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            } else if (type === 'supplier') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            } else if (type === 'warranty') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            } else if (type === 'accessories') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        showTooltip(field) {
            this.tooltipField = field;
            this.tooltipVisible = true;
        },
        hideTooltip() {
            this.tooltipVisible = false;
            this.tooltipField = '';
        },
        //----date format
        convertToDateString(datetime) {
            const date = new Date(datetime);
            const year = date.getUTCFullYear();
            const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are zero-indexed
            const day = String(date.getUTCDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        },
        //--accessories---
        openAccessories() {
            this.open_accessories = true;
        },
        closeAccessories(data) {
            if (data && data.id) {
                this.fetchAccessoriesList();
                this.open_accessories = false;
            } else {
                this.open_accessories = false;
            }
        },
        addproductItems() {
            this.open_product_cost = true;
            this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
        },
        closeRmaProductcost(data) {
            if (data && data.product_id) {
                if (this.formValues.rma_items && Array.isArray(this.formValues.rma_items) && this.formValues.rma_items.length > 0) {
                    let find_duplicate = this.formValues.rma_items.findIndex(opt => opt.product_id == data.product_id);
                    if (find_duplicate !== -1) {
                        this.formValues.rma_items[find_duplicate].quantity += (1 * data.quantity);
                    } else {
                        if (this.formValues.rma_id) {
                            data.rma_id = this.formValues.rma_id;
                        }
                        this.formValues.rma_items.push(data);
                    }
                } else {
                    if (this.formValues.rma_id) {
                        data.rma_id = this.formValues.rma_id;
                    }
                    this.formValues.rma_items = [data];
                }
                this.calculateAllPayment();
                this.open_product_cost = false;
            } else {
                this.open_product_cost = false;
            }
        },
        calculateTotalCost() {
            if (this.formValues.rma_items && Array.isArray(this.formValues.rma_items) && this.formValues.rma_items.length > 0) {
                const totalCost = this.formValues.rma_items.reduce((amount, opt) => amount + parseFloat(opt.total) || 0, 0);
                return parseFloat(totalCost.toFixed(2));
            }
            return 0.00; // Return 0 if rma_items is not present or is empty
        },
        calculateAllPayment() {
            if (this.formValues.rma_items && Array.isArray(this.formValues.rma_items) && this.formValues.rma_items.length > 0) {
                // Calculate tax
                let calculate_tax = this.formValues.rma_items.reduce((totalTax, opt) => {
                    if (opt.tax > 0) {
                        if (opt.tax_type === 'exclusive') {
                            return totalTax + (opt.quantity * opt.unit_price * (opt.tax / 100));
                        } else {
                            return totalTax + (opt.quantity * (opt.unit_price * opt.tax) / 100 + opt.tax);
                        }
                    }
                    return totalTax;
                }, 0);

                // Update tax value
                if (calculate_tax > 0) {
                    this.formValues.tax = calculate_tax;
                }

                // Calculate total cost
                let calculate_total = this.calculateTotalCost();
                if (calculate_total > 0) {
                    let totalCost = calculate_total;
                    if (this.formValues.cost) {
                        totalCost += this.formValues.cost;
                    }
                    if (this.formValues.shipping) {
                        totalCost += this.formValues.shipping;
                    }
                    this.formValues.total_cost = totalCost;
                }
                //---total payments--
                if (this.formValues.rma_payments && this.formValues.rma_payments.length > 0) {
                    let calculate_payment = this.formValues.rma_payments.reduce((totalPay, opt) => {
                        if (opt.amount > 0) {
                            return totalPay + (opt.amount);
                        }
                        return totalPay;
                    }, 0);
                    // Update payment value
                    if (calculate_payment > 0) {
                        this.formValues.total_payments = calculate_payment;
                    }
                }

                // Calculate balance due
                if (this.formValues.total_payments || this.formValues.total_cost) {
                    this.formValues.balance_due = this.formValues.total_payments > this.formValues.total_cost ? 0 : this.formValues.total_cost - this.formValues.total_payments;
                    if (this.formValues.total_payments > this.formValues.total_cost) {
                        this.formValues.return_amount = this.formValues.total_payments - this.formValues.total_cost;
                    }
                } else if (this.formValues.total_cost) {
                    this.formValues.balance_due = this.formValues.total_cost;
                }
            } else {
                this.formValues.total_cost = 0;
                this.formValues.tax = 0;
                this.formValues.balance_due = 0;
                //---total payments--
                if (this.formValues.rma_payments && this.formValues.rma_payments.length > 0) {
                    let calculate_payment = this.formValues.rma_payments.reduce((totalPay, opt) => {
                        if (opt.amount > 0) {
                            return totalPay + (opt.amount);
                        }
                        return totalPay;
                    }, 0);
                    // Update payment value
                    if (calculate_payment > 0) {
                        this.formValues.total_payments = calculate_payment;
                    }
                }
                // Handle cases where rma_items is not present
                let totalCost = 0;
                if (this.formValues.cost) {
                    totalCost += this.formValues.cost;
                }
                if (this.formValues.shipping) {
                    totalCost += this.formValues.shipping;
                }
                this.formValues.total_cost = totalCost;

                if (this.formValues.total_payments || this.formValues.total_cost) {
                    this.formValues.balance_due = this.formValues.total_payments > this.formValues.total_cost ? 0 : this.formValues.total_cost - this.formValues.total_payments;
                    if (this.formValues.total_payments > this.formValues.total_cost) {
                        this.formValues.return_amount = this.formValues.total_payments - this.formValues.total_cost;
                    }
                } else if (this.formValues.total_cost) {
                    this.formValues.balance_due = this.formValues.total_cost;
                }
            }
        },
        openConfirmBox(type, index) {
            if (type === 'rma_items') {
                this.confirm_box.rma_items = true;
                this.delete_data = true;
                this.confirm_index = index;
            } else if (type === 'attachments') {
                this.confirm_box.attachments = true;
                this.delete_data = true;
                this.confirm_index = index;
            } else if (type === 'rma_additional_products') {
                this.confirm_box.rma_additional_products = true;
                this.delete_data = true;
                this.confirm_index = index;
            } else if (type === 'rma_payments') {
                this.confirm_box.rma_payments = true;
                this.delete_data = true;
                this.confirm_index = index;
            }

        },
        deleteRecord() {
            if (this.confirm_box.rma_items && this.confirm_index !== null) {
                if (Array.isArray(this.formValues.rma_items) && this.confirm_index >= 0 && this.confirm_index < this.formValues.rma_items.length) {
                    this.formValues.rma_items.splice(this.confirm_index, 1);
                }
                this.confirm_box.rma_items = false;
                this.delete_data = false;
                this.confirm_index = null;
            } else if (this.confirm_box.attachments && this.confirm_index !== null) {
                if (Array.isArray(this.formValues.attachments) && this.confirm_index >= 0 && this.confirm_index < this.formValues.attachments.length) {
                    axios.delete('/delete-image', { params: { model: "RMA", image_url: this.formValues.attachments[this.confirm_index].attachment } })
                        .then(response => {
                            this.formValues.attachments.splice(this.confirm_index, 1);
                            // console.log(response.data, 'delete image attachment..!');
                            this.confirm_box.attachments = false;
                            this.delete_data = false;
                            this.confirm_index = null;
                        })
                        .catch(error => {
                            console.error('Error', error);
                        })
                } else {
                    this.confirm_box.attachments = false;
                    this.delete_data = false;
                    this.confirm_index = null;
                }
            } else if (this.confirm_box.rma_additional_products && this.confirm_index !== null) {
                if (Array.isArray(this.formValues.rma_additional_products) && this.confirm_index >= 0 && this.confirm_index < this.formValues.rma_additional_products.length) {
                    this.formValues.rma_additional_products.splice(this.confirm_index, 1);
                }
                this.confirm_box.rma_additional_products = false;
                this.delete_data = false;
                this.confirm_index = null;
            } else if (this.confirm_box.rma_payments && this.confirm_index !== null) {
                if (Array.isArray(this.formValues.rma_payments) && this.confirm_index >= 0 && this.confirm_index < this.formValues.rma_payments.length) {
                    this.formValues.rma_payments.splice(this.confirm_index, 1);
                }
                this.confirm_box.rma_payments = false;
                this.delete_data = false;
                this.confirm_index = null;
            }
        },
        cancelDelete() {
            if (this.confirm_box.rma_items) {
                this.confirm_box.rma_items = false;
                this.delete_data = false;
                this.confirm_index = null;
            } else if (this.confirm_box.attachments) {
                this.confirm_box.attachments = false;
                this.delete_data = false;
                this.confirm_index = null;
            } else if (this.confirm_box.rma_additional_products) {
                this.confirm_box.rma_additional_products = false;
                this.delete_data = false;
                this.confirm_index = null;
            } else if (this.confirm_box.rma_payments) {
                this.confirm_box.rma_payments = false;
                this.delete_data = false;
                this.confirm_index = null;
            }
        },
        //----attachments---
        openAttachment() {
            this.open_rma_attachment = true;
        },
        closeAttachments(data) {
            if (data && data.attachment) {
                if (!Array.isArray(this.formValues.attachments)) {
                    this.formValues.attachments = [];
                }

                const isDuplicate = this.formValues.attachments.some(
                    (attachment) => attachment.attachment === data.attachment
                );

                if (!isDuplicate) {
                    this.formValues.attachments.push(data);
                    console.log(this.formValues.attachments, 'Updated Attachments');
                } else {
                    console.log('Duplicate attachment found, not adding:', data);
                }
            }
            this.open_rma_attachment = false;
        },
        //---aditional product---
        openAdditional() {
            this.open_additional_product = true;
            this.fetchItemList();
        },
        closeAdditional(data) {
            if (data && data.description && data.quantity) {
                if (this.formValues.rma_additional_products && Array.isArray(this.formValues.rma_additional_products) && this.formValues.rma_additional_products.length > 0) {
                    if (this.formValues.rma_id) {
                        data.rma_id = this.formValues.rma_id;
                    }
                    this.formValues.rma_additional_products.push(data);
                } else {
                    if (this.formValues.rma_id) {
                        data.rma_id = this.formValues.rma_id;
                    }
                    this.formValues.rma_additional_products = [data];
                }
                this.open_additional_product = false;
            }
            this.open_additional_product = false;
        },
        //---total payment 
        openTotalPayment() {
            this.open_total_payment = true;
        },
        closeTotalPayment(data) {
            if (data && data.amount) {
                if (this.formValues.rma_payments && Array.isArray(this.formValues.rma_payments) && this.formValues.rma_payments.length > 0) {
                    if (this.formValues.rma_id) {
                        data.rma_id = this.formValues.rma_id;
                    }
                    this.formValues.rma_payments.push(data);
                } else {
                    if (this.formValues.rma_id) {
                        data.rma_id = this.formValues.rma_id;
                    }
                    this.formValues.rma_payments = [data];
                }
                this.open_total_payment = false;
            } else {
                this.open_total_payment = false;
            }
        },
        //--save the RMA----
        saveRMAData() {
            if (this.getplanfeatures('rma')) {
                this.no_access = true;
            } else {
                if (this.formValues && this.formValues.id) {
                    this.open_loader = true;
                    let sent_data = {
                        ...this.formValues, company_id: this.companyId,
                        iswhatsapp: this.companywhatsapp ? this.iswhatsapp : false,
                        issms: this.issms,
                    };
                    if (this.formValues.attachments && Array.isArray(this.formValues.attachments) && this.formValues.attachments.length > 0) {
                        sent_data.attachments = JSON.stringify(this.formValues.attachments);
                    }
                    axios.put(`/rmas/${this.formValues.id}`, sent_data)
                        .then(response => {
                            // console.log(response.data);
                            this.updateKeyWithTime('rma_update');
                            this.open_loader = false;
                            this.message = 'RMA updated successfully...!';
                            this.show = true;
                            this.fetchRmasList(1, 10);
                            this.$router.go(-1);
                        })
                        .catch(error => {
                            console.error('Error Post Employee', error);
                            this.open_loader = false;
                        })
                } else {
                    this.message = 'RMA data is missing..!';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            }
        },
        formatDateEstimate(dateString) {
            const date = new Date(dateString);
            if (isNaN(date.getTime())) {
                return 'Invalid Date';
            }

            const day = String(date.getUTCDate()).padStart(2, '0');
            const month = String(date.getUTCMonth() + 1).padStart(2, '0'); // Months are zero-indexed
            const year = date.getUTCFullYear();

            let hours = date.getUTCHours();
            const minutes = String(date.getUTCMinutes()).padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';

            hours = hours % 12;
            hours = hours ? hours : 12; // the hour '0' should be '12'

            const formattedDate = `${day}/${month}/${year}`;

            return `${formattedDate}`;
        },
        //----download pdf----
        async downloadPDF(type) {
            if (this.formValues && this.formValues.id && type) {
                this.open_loader = true;
                try {
                    let link_data = `${this.pdfUrl}/download-${type}/${this.formValues.id}`;

                    // Create an anchor element
                    let anchor = document.createElement('a');
                    anchor.href = link_data;
                    anchor.setAttribute('download', 'invoices'); // Set the download attribute to trigger a download
                    anchor.style.display = 'none';

                    // Append the anchor to the document body and click it programmatically
                    document.body.appendChild(anchor);
                    anchor.click();

                    // Cleanup: remove the anchor from the document body
                    document.body.removeChild(anchor);
                    this.open_loader = false;
                    this.message = 'RMA inward downloaded successfully...!';
                    this.show = true;
                } catch (error) {
                    this.open_loader = false;
                    console.error('Error downloading inward PDF:', error);
                }
            }
        },
        async printPDF(type) {
            if (this.formValues && this.formValues.id && type) {
                this.open_loader = true;
                try {
                    let link_data = `${this.pdfUrl}/download-${type}/${this.formValues.id}`;
                    const response = await axios.get(link_data, { responseType: 'blob' });

                    const blob = new Blob([response.data], { type: 'application/pdf' });
                    const url = window.URL.createObjectURL(blob);
                    const printWindow = window.open(url);

                    printWindow.addEventListener('load', () => {
                        printWindow.print();
                    });
                    this.open_loader = false;
                } catch (error) {
                    console.error('Error printing PDF:', error);
                    this.open_loader = false;
                }
            }
        },
        handleDropdown() {
            this.dropdownOpen = !this.dropdownOpen;
            if (this.dropdownOpen) {
                document.addEventListener('click', this.handleClick);
            }
        },
        handleDropdownPrint() {
            this.dropdownOpenPrint = !this.dropdownOpenPrint;
            if (this.dropdownOpenPrint) {
                document.addEventListener('click', this.handleClickPrint);
            }
        },
        handleClick(event) {
            if (this.$refs.downloadRef && !this.$refs.downloadRef.contains(event.target)) {
                this.dropdownOpen = false;
            }
        },
        handleClickPrint(event) {
            if (this.$refs.printRef && !this.$refs.printRef.contains(event.target)) {
                this.dropdownOpenPrint = false;
            }
        },
        closeAllModals() {
            // Close all modals
            this.open_accessories = false;
            this.open_product_cost = false;
            this.delete_data = false;
            this.open_rma_attachment = false;
            this.open_additional_product = false;
            this.open_total_payment = false;
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        navigateToWhatsApp() {
            this.$router.push({ name: 'whatsappsetting' });
        },
    },
    watch: {
        'formValues.rma_items': {
            deep: true,
            handler(newValue) {
                this.calculateAllPayment();
            }
        },
        'formValues.rma_payments': {
            deep: true,
            handler(newValue) {
                this.calculateAllPayment();
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_accessories: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        pen_product_cost: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        delete_data: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_rma_attachment: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_additional_product: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_total_payment: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && Array.isArray(newValue) && newValue.length > 0) {
                    this.issms = newValue[0].is_sms >= 0 ? newValue[0].is_sms ? true : false : false;
                    this.iswhatsapp = this.companywhatsapp ? newValue[0].is_whatsapp >= 0 ? newValue[0].is_whatsapp ? true : false : false : false;
                }
            }
        }
    }

};
</script>

<style>
/* Add your styles here */
.tooltip {
    position: absolute;
    left: 50%;
    top: -35px;
    /* Adjust to position the tooltip correctly above the input */
    transform: translateX(-50%);
    background-color: #4e4e53;
    /* Change this to the desired background color */
    color: #fff;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    white-space: nowrap;
    z-index: 10;
    box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.1);
    /* Optional: Add a shadow for better visibility */
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: -20px;
    /* Position the arrow at the bottom of the tooltip */
    left: 50%;
    transform: translateX(-50%);
    border-width: 10px;
    border-style: solid;
    border-color: #4e4e53 transparent transparent transparent;
    /* Match the tooltip background color */
}
</style>