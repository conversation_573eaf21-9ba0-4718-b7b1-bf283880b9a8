// store/modules/amc_list.js
import axios from "axios";

const state = {
  amc_list: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  previousPerPage: null,
  amcData: {},
  };

  const mutations = {
      SET_AMCSLIST(state, { data, pagination, status_counts }) {
        //   console.log(data, 'data', pagination, 'pagination', status_counts, 'status');
          state.amc_list = {data: data, pagination: pagination, status_counts: status_counts};
    },
      RESET_STATE(state) {
          state.amc_list = {};
          state.lastFetchTime = null;
        state.isFetching = false;
        state.previousPerPage = null;
        state.amcData = {};
      },
      SET_LAST_FETCH_TIME(state, time) {
        state.lastFetchTime = time; // Save the timestamp when the API was last accessed
      },
      SET_IS_FETCHING(state, status) {
        state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    SET_PREVIOUS_PER_PAGE(state, perPage) {
      state.previousPerPage = perPage; // Store the previous per_page value
    },
    setAmcData(state, { day, data, currentTime }) {
      if (!state.amcData[day]) {
        state.amcData[day] = {data: data, lastFetch: currentTime};
      }
      state.amcData[day].data = data;  // Store the fetched data
      state.amcData[day].lastFetch = currentTime;  // Store the last fetch time
    },
  };

  const actions = {
    updateAmcsName({ commit }, amc_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update amc_list name
      setTimeout(() => {
        // Commit mutation to update amc_list name
        commit('SET_AMCSLIST', amc_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchAmcsList({ commit, state, rootState }, { page, per_page, is_delete }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['amc_update'];       
      if (state.previousPerPage !== per_page) {
        commit('SET_PREVIOUS_PER_PAGE', per_page); // Update the per_page tracking
        commit('SET_LAST_FETCH_TIME', null); // Force a new request if per_page changed
      }
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (!is_delete && (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)) {
        return; // Skip request if less than 30 seconds have passed since the last request
      } 
      
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          // Set the request status to true (indicating that the request is in progress)
         commit('SET_IS_FETCHING', true);
          axios.get('/amcs', { params: { company_id: company_id, page: page, per_page: per_page } })
            .then(response => {
              // Handle response
            //   console.log(response.data, 'Amcs list..!');
                let {data, pagination, status_counts} = response.data; 
                
                // console.log(data, 'data', pagination, 'pagination', status_counts, 'status', 'Initialllllly');
              commit('SET_AMCSLIST', { data, pagination, status_counts });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    //----Amc data on dashboard--
    async fetchAmcData({ commit, state, rootState }, day) {
      const now = new Date().toISOString();  // Current time in ISO format
      const thirtySecondsInMilliseconds = 3 * 1000;  
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['amc_update'];  // Get the last API update time
      const lastFetch = state.amcData[day]?.lastFetch;  // Get the last fetch time for the specific day
      // Convert lastFetch to Date object for proper comparison
    const lastFetchDate = lastFetch ? new Date(lastFetch) : null;      
      // If no lastFetch or if the request was made more than 30 seconds ago, proceed
      if (!lastFetchDate || new Date(now) - lastFetchDate >= thirtySecondsInMilliseconds) {
        // Check if the last API update time is more recent than the last fetch
        if (lastFetchDate && lastUpdateTime < lastFetchDate.getTime()) {
          return; // Skip fetch if the last update is earlier than the last fetch
        }
        
        try {
          const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
          if (company_id && company_id !== '') {
            axios.get('/amc/seven-days-report', { params: { day: day, company_id: company_id } })
              .then(response => {
                // Handle response
                let data = response.data;
                commit('setAmcData', { day, data: data, currentTime: now });
                return data;
              })
              .catch(error => {
                // Handle error
                console.error('Error:', error);
                return error;
              });
          }
          }catch (error) {
            console.error('Error Amc data:', error);
          }
        
      } else {
        return;  // Return cached data if it's recently fetched or request is ongoing
      }
    }   
    
  };

  const getters = {
    currentAmcsList(state) {
      return state.amc_list;
    },
    getAmcData(state) {
      return state.amcData;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
