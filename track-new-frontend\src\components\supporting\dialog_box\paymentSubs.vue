<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md max-h-screen overflow-y-auto transform transition-transform ease-in-out duration-300 rounded-lg overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <!-- Mo<PERSON> Header -->
            <h3 class="text-2xl font-bold mb-4 text-center text-gray-900 rounded-t-lg py-4 text-white"
                :style="{ backgroundColor: plan.labelcolor }">
                {{
                    plan.title }}</h3>
            <div class="p-6 pt-1">
                <template v-if="step === 1">
                    <!-- Price Details -->
                    <div class="mb-4 space-y-2 ">
                        <div class="flex justify-between text-lg">
                            <p class="text-gray-700">Price:</p>
                            <p class="text-gray-900">₹{{ plan.price }}</p>
                        </div>
                        <div v-if="couponApplied" class="flex justify-between text-lg border-t border-gray-200 pt-2">
                            <p class="text-green-500">Coupon Discount: </p>
                            <p class="text-green-500">- ₹{{ coupon_data.discount_amount }}</p>
                        </div>
                        <div v-if="couponApplied" class="flex justify-between text-lg border-t border-gray-200 pt-2">
                            <p class="text-gray-700">Subtotal: </p>
                            <p class="text-gray-900">₹{{ planPrice }}</p>
                        </div>
                        <div class="flex justify-between text-lg border-t border-gray-200 pt-2">
                            <p class="text-gray-700">Tax (18%):</p>
                            <p class="text-gray-900">₹{{ taxAmount }}</p>
                        </div>
                        <div class="flex justify-between text-lg border-t border-gray-200 pt-2">
                            <p class="text-gray-700">Grand Total:</p>
                            <p class="text-gray-900">₹{{ grandTotal }}</p>
                        </div>
                    </div>
                    <!-- Coupon Code Section -->
                    <div class="mb-4 space-y-2">
                        <!-- Coupon Code Label and Input -->
                        <div class="flex flex-col items-start">
                            <label class="block text-xs text-gray-700 w-full">Coupon Code</label>
                            <div class="flex items-center space-x-4 w-full">
                                <input v-model="couponCode" type="text" placeholder="Enter coupon code"
                                    class="w-full p-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-600" />
                                <button v-if="!couponApplied" @click="applyCoupon"
                                    class="py-2 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200">
                                    Apply
                                </button>
                                <button v-if="couponApplied" @click="resetCoupon"
                                    class="py-2 px-4 bg-red-600 text-white rounded-lg hover:bg-red-700 transition duration-200">
                                    Reset
                                </button>
                            </div>
                        </div>
                        <p v-if="couponApplied" class="text-sm text-green-600 mt-2">Coupon applied successfully! {{
                            this.coupon_data.discount }}
                            discount has been deducted.</p>
                        <p v-if="couponError" class="text-sm text-red-600 mt-2">Invalid coupon code. Please try again.
                        </p>
                    </div>

                    <!-- Agreement Checkboxes -->
                    <div class="mb-6 space-y-4">
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" v-model="agreedTerms" class="form-checkbox text-blue-600" />
                            <span class="text-gray-700">I agree to the <a href="https://track-new.com/terms-conditions/"
                                    target="_blank" class="text-blue-600 hover:underline">Terms and
                                    Conditions</a></span>
                        </label>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" v-model="agreedPrivacy" class="form-checkbox text-blue-600" />
                            <span class="text-gray-700">I agree to the <a href="https://track-new.com/privacy-policy/"
                                    class="text-blue-600 hover:underline" target="_blank">Privacy Policy</a></span>
                        </label>
                        <label class="flex items-center space-x-2">
                            <input type="checkbox" v-model="agreedRefunds" class="form-checkbox text-blue-600" />
                            <span class="text-gray-700">I agree to the <a href="https://track-new.com/refund-policy/"
                                    class="text-blue-600 hover:underline" target="_blank">Refunds Policy</a></span>
                        </label>
                    </div>
                    <div class="flex justify-end space-x-4">
                        <button @click="closeModal"
                            class="py-2 px-4 bg-gray-300 border border-gray-400 text-gray-800 rounded-lg hover:bg-gray-400 transition duration-200">
                            Cancel
                        </button>
                        <button @click="nextStep"
                            class="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200">
                            Next
                        </button>
                    </div>
                </template>
                <template v-if="step === 2">
                    <h3 class="text-xl font-semibold text-gray-900 mb-4 text-center">Select Payment Method</h3>

                    <div class="bg-gray-100 p-4 rounded-lg">
                        <div class="flex flex-col space-y-4">
                            <!-- Auto Pay Option -->
                            <!-- Auto Pay Option -->
                            <label
                                class="flex flex-col p-3 border rounded-lg cursor-pointer hover:bg-gray-200 transition"
                                :class="{
                                    'border-green-500 bg-green-50': paymentMethod === 'auto',
                                    'opacity-50 cursor-not-allowed': couponApplied
                                }" :style="{ filter: couponApplied ? 'blur(0.5px)' : 'none' }"
                                :disabled="couponApplied">
                                <div class="flex items-center">
                                    <input type="radio" v-model="paymentMethod" value="auto" class="hidden"
                                        :disabled="couponApplied" />
                                    <div class="w-5 h-5 border border-gray-400 rounded-full flex items-center justify-center"
                                        :class="{ 'border-green-600 bg-green-600': paymentMethod === 'auto' }">
                                        <svg v-if="paymentMethod === 'auto'" class="w-4 h-4 text-white" fill="none"
                                            stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7">
                                            </path>
                                        </svg>
                                    </div>
                                    <span class="ml-3 text-gray-900 font-medium">
                                        Auto Pay (Automatic {{ plan.days === 365 ? 'Yearly' : 'Monthly' }} Payment)
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1 ml-8">
                                    Your payment will be deducted automatically every {{ plan.days === 365 ? 'year' :
                                        'month' }}.
                                </p>
                            </label>

                            <!-- Manual Pay Option -->
                            <label
                                class="flex flex-col p-3 border rounded-lg cursor-pointer hover:bg-gray-200 transition"
                                :class="{ 'border-green-500 bg-green-50': paymentMethod === 'subscription' }">
                                <div class="flex items-center">
                                    <input type="radio" v-model="paymentMethod" value="subscription" class="hidden" />
                                    <div class="w-5 h-5 border border-gray-400 rounded-full flex items-center justify-center"
                                        :class="{ 'border-green-600 bg-green-600': paymentMethod === 'subscription' }">
                                        <svg v-if="paymentMethod === 'subscription'" class="w-4 h-4 text-white"
                                            fill="none" stroke="currentColor" stroke-width="2" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7">
                                            </path>
                                        </svg>
                                    </div>
                                    <span class="ml-3 text-gray-900 font-medium">
                                        Manual Pay (Manual {{ plan.days === 365 ? 'Yearly' : 'Monthly' }} Payment)
                                    </span>
                                </div>
                                <p class="text-sm text-gray-600 mt-1 ml-8">
                                    You will need to manually make a payment every {{ plan.days === 365 ? 'year' :
                                        'month' }}.
                                </p>
                            </label>
                        </div>
                    </div>

                    <!-- Navigation Buttons -->
                    <div class="flex justify-end space-x-4 mt-6">
                        <button @click="step = 1"
                            class="py-2 px-4 bg-gray-300 border border-gray-400 text-gray-800 rounded-lg hover:bg-gray-400 transition duration-200">
                            Back
                        </button>
                        <button v-if="paymentMethod === 'auto'" @click="step = 3"
                            class="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                            Next
                        </button>
                        <button v-else @click="confirmSubscription"
                            class="py-2 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200">
                            Confirm ₹{{ grandTotal }}
                        </button>
                    </div>
                </template>

                <!-- Step 3: Auto Pay - UPI Payment Selection -->
                <div v-if="step === 3">
                    <h3 class="text-lg font-semibold text-gray-900 mb-3">Choose UPI Payment Method</h3>

                    <div class="flex space-x-4">
                        <button @click="upiMethod = 'input'"
                            :class="{ 'bg-blue-600 text-white': upiMethod === 'input', 'bg-gray-200 text-gray-800': upiMethod !== 'input' }"
                            class="w-1/2 py-2 rounded-lg border border-gray-400 transition">
                            Enter UPI ID
                        </button>
                        <button @click="processQrPayment(), upiMethod = 'qr'"
                            :class="{ 'bg-blue-600 text-white': upiMethod === 'qr', 'bg-gray-200 text-gray-800': upiMethod !== 'qr' }"
                            class="w-1/2 py-2 rounded-lg border border-gray-400 transition">
                            Scan QR Code
                        </button>
                    </div>

                    <!-- UPI ID Input Field -->
                    <div v-if="upiMethod === 'input'" class="mt-4">
                        <!-- Auto Pay: UPI ID Input -->
                        <div v-if="paymentMethod === 'auto'" class="mt-6">
                            <label class="block text-gray-700 font-medium">Enter UPI ID</label>
                            <div class="relative mt-2">
                                <input type="text" v-model="upiId"
                                    :class="{ 'border-green-600': valid_upi && valid_upi.success, 'border-red-600': valid_upi && valid_upi.success !== undefined && !valid_upi.success }"
                                    class="w-full border border-gray-300 rounded-lg p-2 pr-10 focus:border-blue-500 focus:ring focus:ring-blue-200"
                                    placeholder="example@upi">
                            </div>
                            <p v-if="valid_upi && valid_upi.success" class="text-green-600 text-sm mt-1">
                                <font-awesome-icon icon="fa-solid fa-circle-check" class="pr-1" /> {{ valid_upi.data &&
                                    valid_upi.data.name ? valid_upi.data.name : 'Valid UPI ID' }}
                            </p>
                            <p v-if="valid_upi && valid_upi.success !== undefined && !valid_upi.success"
                                class="text-red-600 text-sm mt-1">
                                <font-awesome-icon icon="fa-solid fa-circle-exclamation" class="pr-1" /> {{
                                    valid_upi.message ? valid_upi.message : 'Invalid UPI ID' }}
                            </p>
                        </div>
                    </div>

                    <!-- QR Code Display -->
                    <div v-if="upiMethod === 'qr'" class="mt-4 flex flex-col items-center">
                        <!-- QR Code Image -->
                        <img v-if="upiQrCode && upiQrCode !== ''" :src="upiQrCode" alt="UPI QR Code"
                            class="sm:w-60 sm:h-60 w-40 h-40 border rounded-lg shadow-lg" />
                        <!-- Step-by-step Instructions -->
                        <div class="mt-4 text-left text-sm text-gray-600">
                            <p class="text-center text-lg font-semibold text-green-600 mb-1">
                                Scan & Pay ₹ {{ grandTotal }}
                            </p>
                            <p class="font-semibold">Follow these steps to complete your payment:</p>
                            <ol class="list-decimal list-inside space-y-2">
                                <li>Open your preferred UPI payment app (e.g., PhonePe, Paytm, Google Pay).</li>
                                <li>Look for the 'Scan & Pay' option in the app.</li>
                                <li>Point your phone’s camera at the QR code above.</li>
                                <li>Confirm the payment amount and details shown on your screen.</li>
                                <li>Click 'Pay' to complete the transaction.</li>
                                <li class="font-semibold mt-2">
                                    After a successful payment, click the button below to refresh the page and complete
                                    your
                                    transaction.
                                </li>
                            </ol>
                            <!-- Refresh Button -->
                            <div class="mt-4 text-center">
                                <button @click="refreshPage"
                                    class="bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                                    <font-awesome-icon icon="fa-solid fa-arrow-rotate-right" class="pr-1" /> Refresh
                                    Page
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="flex justify-end space-x-4 mt-6">
                        <button @click="step = 2"
                            class="py-2 px-4 bg-gray-300 border border-gray-400 text-gray-800 rounded-lg hover:bg-gray-400 transition duration-200">
                            Back
                        </button>
                        <button v-if="upiMethod !== 'qr'" @click="validateUpi"
                            class="py-2 px-4 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition duration-200">
                            Verify UPI ID
                        </button>
                    </div>
                </div>
                <template v-if="step === 4">
                    <div class="text-center">
                        <p v-if="valid_upi && valid_upi.success" class="text-green-600 text-lg mt-1 py-1">
                            <font-awesome-icon icon="fa-solid fa-circle-check" class="pr-1" /> Name: {{ valid_upi.data
                                &&
                                valid_upi.data.name ? valid_upi.data.name : 'Valid UPI ID' }}
                        </p>
                        <p class="text-lg text-green-900 mb-4 font-medium"><font-awesome-icon
                                icon="fa-solid fa-circle-check" class="pr-1" /> UPI ID: {{ upiId }}</p>
                        <button v-if="!sentreq" @click="sendReqUPIID"
                            class="w-full py-2 px-4 bg-green-600 text-white rounded-lg hover:bg-green-700 transition duration-200">
                            Send Request to UPI ₹{{ grandTotal }}
                        </button>
                    </div>
                    <div v-if="sentreq" class="mt-4 text-left text-sm text-gray-600">
                        <p class="text-gray-700 py-1 font-semibold"> please follow these steps:</p>
                        <ul class="list-decimal list-inside space-y-2">
                            <li>Open your UPI app (e.g., Google Pay, PhonePe, Paytm, etc.).</li>
                            <li>Look for the payment request from the UPI ID that was sent to you.</li>
                            <li>Allow the payment request to process and authorize the payment.</li>
                            <li>Once the payment is successful, <span class="font-bold">refresh the page </span> to
                                continue using our services.</li>
                            <li>If you encounter any issues, feel free to reach out to customer
                                support.</li>
                        </ul>
                    </div>
                    <!-- Refresh Button -->
                    <div v-if="sentreq" class="mt-6 text-center">
                        <button @click="refreshPage"
                            class="bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-700">
                            <font-awesome-icon icon="fa-solid fa-arrow-rotate-right" class="pr-1" /> Refresh Page
                        </button>
                    </div>
                </template>

                <!-- Modal Footer -->
                <!-- <div class="flex justify-end space-x-4">
                    <button @click="closeModal"
                        class="py-2 px-4 bg-gray-300 shadow-inner shadow-gray-200 border border-gray-400 text-gray-800 rounded-lg hover:bg-gray-400 transition duration-200">
                        Cancel
                    </button>
                <button @click="confirmSubscription"
                    class="py-2 px-4 bg-green-600 text-white rounded-lg shadow-inner shadow-green-200 border border-green-700 hover:bg-green-700 transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                    Confirm ₹{{ grandTotal }}
                </button>
            </div> -->
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import QRCode from 'qrcode';
export default {
    props: {
        showModal: Boolean,
        plan: Object,
        buttonText: String,
    },
    data() {
        return {
            step: 1,
            agreedTerms: false,
            agreedPrivacy: false,
            agreedRefunds: false,
            isOpen: false,
            paymentMethod: 'auto',
            upiId: '',
            payment_type: 1,
            valid_upi: {},
            valid_qr: {},
            is_auto: false,
            upiMethod: 'input',
            upiQrCode: '',
            sentreq: false,
            couponCode: '',
            couponApplied: false,
            couponError: false,
            coupon_data: {},
            //---toaster----
            type_toaster: 'info',
            message: '',
            show: false,
            open_loader: false,
        };
    },
    computed: {
        planPrice() {
            let price = this.plan.price;
            if (this.couponApplied) {
                price = this.coupon_data.total_after_discount;
                //     const discount = 0.35;
                //     const discountAmount = price * discount;
                //     price = price - discountAmount;
            }
            return price.toFixed(2);  // Ensuring 2 decimal places
        },
        taxAmount() {
            return (this.planPrice * 0.18).toFixed(2);
        },
        grandTotal() {
            return (parseFloat(this.planPrice) + parseFloat(this.taxAmount)).toFixed();
        },
        canProceed() {
            return this.agreedTerms && this.agreedPrivacy && this.agreedRefunds;
        },
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            this.resetCoupon();
            setTimeout(() => {
                this.$emit('close');
            }, 300);
        },
        confirmSubscription() {
            if (this.canProceed) {
                let send_data = this.couponApplied ? this.couponCode : null;
                this.$emit('confirm', this.plan.id, send_data);
                this.agreedTerms = false;
                this.agreedPrivacy = false;
                this.agreedRefunds = false;
                this.step = 1;
                this.couponCode = '';
                this.couponApplied = false;
                this.couponError = false;
                this.coupon_data = {};
            } else {
                this.message = 'Please select the checkbox before procedding payment..!';
                this.show = true;
            }
        },
        nextStep() {
            if (this.canProceed) {
                this.step = 2;
            } else {
                this.message = 'Please select the checkbox before procedding payment..!';
                this.show = true;
            }
        },
        validateUpi() {
            if (this.upiId && this.upiId !== '') {
                this.open_loader = true;
                axios.post(`/subscription/validate-vpa/${this.payment_type}`, { vpa: this.upiId })
                    .then(response => {
                        // console.log(response.data, 'What happning...! response');
                        this.valid_upi = response.data;
                        if (this.valid_upi.success) {
                            this.step = 4;
                            this.is_auto = true;
                        }
                        this.open_loader = false;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            } else {
                this.message = 'Please fill the UPI ID before procedding verify UPI ID..!';
                this.show = true;
            }
        },
        processQrPayment() {
            this.open_loader = true;
            this.is_auto = true;
            axios.post(`/subscription/subscribe/${this.payment_type}/${this.plan.id}`, { is_auto: true, is_qr: true })
                .then(response => {
                    this.valid_qr = response.data;
                    if (this.valid_qr.status == 'success' && this.valid_qr.data.redirect_url) {
                        this.generateQrCode(this.valid_qr.data.redirect_url);
                    }
                    this.open_loader = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                });
        },
        // Generate QR Code from the UPI Payment String
        async generateQrCode(qrString) {
            try {
                this.upiQrCode = await QRCode.toDataURL(qrString);
            } catch (error) {
                console.error('QR Code generation failed:', error);
            }
        },
        sendReqUPIID() {
            if (this.upiId && this.upiId !== '' && this.plan && this.plan.id) {
                this.open_loader = true;
                axios.post(`/subscription/subscribe/${this.payment_type}/${this.plan.id}`, { is_auto: this.is_auto, vpa: this.upiId ? this.upiId : null })
                    .then(response => {
                        let data_value = response.data.data;
                        this.sentreq = true;
                        this.open_loader = false;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
        },
        applyCoupon() {
            if (this.couponCode && this.couponCode !== '' && this.plan && this.plan.id) {
                this.open_loader = true;
                axios.post(`/validate-coupon`, { code: this.couponCode, total: this.plan.price })
                    .then(response => {
                        this.coupon_data = response.data;
                        if (this.coupon_data.status === 'success') {
                            this.paymentMethod = 'subscription';
                            this.couponApplied = true;
                            this.couponError = false;
                        } else {
                            this.couponApplied = false;
                            this.couponError = true;
                        }

                        this.open_loader = false;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                        this.couponError = true;
                        this.couponApplied = false;
                    })
            } else {
                this.couponError = false;
                this.couponApplied = false;
            }
        },
        resetCoupon() {
            this.couponCode = '';
            this.couponError = false;
            this.couponApplied = false;
            this.coupon_data = {};
            this.paymentMethod = 'auto';
            this.show = false;
        },
        refreshPage() {
            window.location.reload();
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
    }
};
</script>

<style scoped>
/* Optional: Additional styling for modal */
</style>