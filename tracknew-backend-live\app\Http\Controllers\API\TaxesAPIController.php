<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateTaxesAPIRequest;
use App\Http\Requests\API\UpdateTaxesAPIRequest;
use App\Models\Taxes;
use App\Repositories\TaxesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class TaxesController
 * @package App\Http\Controllers\API
 */

class TaxesAPIController extends AppBaseController
{
    /** @var  TaxesRepository */
    private $taxesRepository;

    public function __construct(TaxesRepository $taxesRepo)
    {
        $this->taxesRepository = $taxesRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/taxes",
     *      summary="getTaxesList",
     *      tags={"Taxes"},
     *      description="Get all Taxes",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Taxes")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $countryId = $request->get('country_id');

        // Get tax groups only (is_group = 1) and filter by country
        $taxGroups = Taxes::where('is_group', 1)
            ->when($countryId, function ($query) use ($countryId) {
                $query->where('country_id', $countryId);
            })
            ->get();

        $result = $taxGroups->map(function ($tax) {
        
            $groupIds = array_filter(explode(',', $tax->ids));

            $groupTaxes = Taxes::whereIn('id', $groupIds)->get(['id', 'name']);

            return [
                'id' => $tax->id,
                'name' => $tax->name,
                'groups' => $groupTaxes->map(function ($g) {
                    return [
                        'id' => $g->id,
                        'name' => $g->name,
                    ];
                })->values()
            ];
        });

        return $this->sendResponse($result->toArray(), 'Tax groups retrieved successfully');
    }


    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/taxes",
     *      summary="createTaxes",
     *      tags={"Taxes"},
     *      description="Create Taxes",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Taxes"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateTaxesAPIRequest $request)
    {
        $input = $request->all();

        $taxes = $this->taxesRepository->create($input);

        return $this->sendResponse($taxes->toArray(), 'Taxes saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/taxes/{id}",
     *      summary="getTaxesItem",
     *      tags={"Taxes"},
     *      description="Get Taxes",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Taxes",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Taxes"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Taxes $taxes */
        $taxes = $this->taxesRepository->find($id);

        if (empty($taxes)) {
            return $this->sendError('Taxes not found');
        }

        return $this->sendResponse($taxes->toArray(), 'Taxes retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/taxes/{id}",
     *      summary="updateTaxes",
     *      tags={"Taxes"},
     *      description="Update Taxes",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Taxes",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Taxes"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateTaxesAPIRequest $request)
    {
        $input = $request->all();

        /** @var Taxes $taxes */
        $taxes = $this->taxesRepository->find($id);

        if (empty($taxes)) {
            return $this->sendError('Taxes not found');
        }

        $taxes = $this->taxesRepository->update($input, $id);

        return $this->sendResponse($taxes->toArray(), 'Taxes updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/taxes/{id}",
     *      summary="deleteTaxes",
     *      tags={"Taxes"},
     *      description="Delete Taxes",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Taxes",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Taxes $taxes */
        $taxes = $this->taxesRepository->find($id);

        if (empty($taxes)) {
            return $this->sendError('Taxes not found');
        }

        $taxes->delete();

        return $this->sendSuccess('Taxes deleted successfully');
    }
}
