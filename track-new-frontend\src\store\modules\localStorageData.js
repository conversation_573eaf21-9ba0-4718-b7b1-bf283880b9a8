// store/modules/localstorage_data.js
import axios from "axios";

const state = {
  localstorage_data: {},
  plan_features: {},
  planFeatures: {
    service: true,
    amc: true,
    rma: true,
    leads: true,
    sms_alert: true,
    service_estimations: true,
    additional_materials: true,
    service_image: true,
    service_comments: true,    
    service_category: true,    
    website: true,
    whatsapp_msg: true,
    website_enquiry: true,
  },
  is_sidebar_open: true,
  };

  const mutations = {
      SET_LOCALSTORAGE(state, { local_data}) {
          state.localstorage_data = local_data;
    },
      RESET_STATE(state) {
        state.localstorage_data = {};
        state.plan_features = {};
      }
  };

  const actions = {
    updateLocalDataName({ commit }, localstorage_dataData) {
      // Simulate an asynchronous operation (e.g., API call) to update localstorage_data name
      setTimeout(() => {
        // Commit mutation to update localstorage_data name
        commit('SET_LOCALSTORAGE', localstorage_dataData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchLocalDataList({ state, commit, rootState}) {
      try {
        const local_data = JSON.parse(localStorage.getItem('track_new'));     
        if (local_data !== '' && local_data) {  
          const company_plan = rootState.companies.companies;
          if (state.plan_features && Object.keys(state.plan_features).length == 0 && company_plan.plans && company_plan.plans.id && [12, 13, 14, 15, 16, 17, 18, 19].includes(company_plan.plans.id)) {
            if ([12, 13].includes(company_plan.plans.id)) {
              state.plan_features = { ...state.planFeatures,
                amc: false,
                rma: false,
                service: false,
                service_category: false,
                leads: false,
                website: false,
                whatsapp_msg: false,
                website_enquiry: false,
                service_estimations: false,
                additional_materials: false,
                service_image: false,
                service_comments: false,                
              };
            }
            else if ([14, 15].includes(company_plan.plans.id)) {
              state.plan_features = { ...state.planFeatures,
                // amc: false,
                // rma: false,
                // service_estimations: false,
                // additional_materials: false,
                // service_image: false,
                // service_comments: false,
                // whatsapp_msg: false
              };
            } else if ([16, 17].includes(company_plan.plans.id)) {
              state.plan_features = { ...state.planFeatures,
                // rma: false,
              };              
            } else {
              state.plan_features = { ...state.planFeatures };
           }           
          } else {
            if (company_plan.plans && company_plan.plans.id && company_plan.plans.id && [5].includes(company_plan.plans.id)) {
              state.plan_features = { ...state.planFeatures };              
            }            
          }
          commit('SET_LOCALSTORAGE', { local_data }); 
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    async validateRoles({ dispatch, state }, roles) {
      await dispatch('fetchLocalDataList');
      const userRole = state.localstorage_data.roles;
    if (userRole && Array.isArray(roles)) {
      return roles.includes(userRole[0]);
    }
    return false;
    },
    async sidebarUpdate({state }, value) {
      state.is_sidebar_open = value;
    }
  };

  const getters = {
    currentLocalDataList(state) {
      return state.localstorage_data;
    },
    getPlanfeatures(state) {
      return state.plan_features;
    },
    sidebaropen(state) {
      return state.is_sidebar_open;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
