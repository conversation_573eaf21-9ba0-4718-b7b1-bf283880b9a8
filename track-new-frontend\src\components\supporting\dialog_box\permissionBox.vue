<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-full lg:w-3/4 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <!-- <div class="modal-content"> -->
            <div class="w-full bg-teal-600 justify-between items-center flex py-3">
                <h2 class="text-white font-bold text-center flex justify-end ml-12 text-lg">Permissions</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <div></div>
            <div class="w-full p-2">
                <!--button-->
                <div class="text-sm mb-2 grid sm:grid-cols-5 grid-cols-2 gap-5">
                    <button
                        class="py-2 px-5 text-green-700 border border-green-400 rounded rounded-full hover:text-white hover:bg-green-500 shadow-md"
                        @click="checkedList('all')">All ON/OFF</button>
                    <button
                        class="py-2 px-5 text-green-600 border border-green-500 rounded rounded-full hover:text-white hover:bg-green-500 shadow-md"
                        @click="checkedList('create')">Create ON/OFF</button>
                    <button
                        class="py-2 px-5 text-blue-500 border border-blue-400 rounded rounded-full hover:text-white hover:bg-blue-400 shadow-md"
                        @click="checkedList('read')">Only Read ON/OFF</button>
                    <button
                        class="py-2 px-5 text-violet-500 border border-violet-400 rounded rounded-full hover:text-white hover:bg-violet-400 shadow-md"
                        @click="checkedList('update')">Only Update ON/OFF</button>
                    <button
                        class="py-2 px-5 text-red-500 border border-red-400 rounded rounded-full hover:text-white hover:bg-red-400 shadow-md"
                        @click="checkedList('delete')">Only Delete ON/OFF</button>
                </div>
                <!---table-->
                <table v-if="permission_list.length > 0" class="min-w-full bg-white border border-gray-200">
                    <thead>
                        <tr class="bg-green-700 text-white">
                            <th class="px-3 py-2 border-b border-gray-200 text-center">Descriptions</th>
                            <th class="px-3 py-2 border-b border-gray-200 text-center">Status</th>
                            <th v-if="!isMobile" class="px-3 py-2 border-b border-gray-200 text-center">Descriptions
                            </th>
                            <th v-if="!isMobile" class="px-3 py-2 border-b border-gray-200 text-center">Status</th>
                            <th v-if="!isMobile" class="px-3 py-2 border-b border-gray-200 text-center">Descriptions
                            </th>
                            <th v-if="!isMobile" class="px-3 py-2 border-b border-gray-200 text-center">Status</th>
                            <th v-if="!isMobile" class="px-3 py-2 border-b border-gray-200 text-center">Descriptions
                            </th>
                            <th v-if="!isMobile" class="px-3 py-2 border-b border-gray-200 text-center">Status</th>
                        </tr>
                    </thead>
                    <tbody>
                        <template
                            v-for="(listChunk, index) in (isMobile ? Math.ceil(permission_list.length / 1) : Math.ceil(permission_list.length / 4))"
                            :key="index">
                            <tr class="justify-center border">
                                <!-- Loop through each item in the current chunk -->
                                <template v-for="(list, innerIndex) in getChunk(permission_list, index)">
                                    <td class="flex text-center items-center justify-center py-2">
                                        {{ formatPermissionName(list.name) }}</td>
                                    <td class=" text-center items-center justify-center py-2">
                                        <label class="switch">
                                            <input type="checkbox" v-model="list.checked">
                                            <span class="slider round"
                                                :data-content="list.checked ? 'on' : 'off'"></span>
                                        </label>
                                    </td>
                                </template>
                            </tr>
                        </template>
                    </tbody>
                </table>
            </div>
            <!-- Buttons -->
            <div class="flex justify-center items-center m-3 mt-3">
                <button @click="cancelModal"
                    class="bg-pink-600 rounded-[30px] text-white p-1 py-2 px-10 mr-8 hover:bg-pink-500">Cancel</button>
                <button @click="sendModal"
                    class="bg-green-600 rounded-[30px] text-white p-1 py-2 px-10 mr-8 hover:bg-green-500">Save</button>
            </div>
            <!-- </div> -->
        </div>
        <dialogAlert :showModal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>
<script>
import dialogAlert from './dialogAlert.vue';

export default {
    name: 'permissionBox',
    components: {
        dialogAlert,
    },
    props: {
        showModal: Boolean,
        editData: Object,
        companyId: String,
        userId: String
    },
    data() {
        return {
            isMobile: false,
            permission_list: [],
            isOpen: false,
            open_message: false,
            message: '',
            type: null,
            open_loader: false,
        }
    },
    methods: {
        cancelModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        closeModal(data) {
            this.isOpen = false;
            if (data) {
                setTimeout(() => {
                    this.closeMessage();
                    this.$emit('close-modal', data);
                }, 300);

            } else {
                setTimeout(() => {
                    this.closeMessage();
                    this.$emit('close-modal');
                }, 300);
            }
        },

        sendModal() {
            // Handle sending logic here
            // console.log('Sending message:', this.message);
            if (this.permission_list.length > 0 && this.editData.id) {
                this.open_loader = true;
                let get_data = this.permission_list
                    .filter(permission => permission.checked).map(permission => permission.id);
                // console.log(get_data, 'filter_Data');
                axios.post('/roles/permissions/assign', { company_id: this.companyId, user_id: this.userId, role_id: this.editData.id, permission_id: get_data })
                    .then(response => {
                        // console.log(response.data, 'What happening..!');
                        this.open_loader = false;
                        this.openMessage(response.data.message);
                        this.closeModal(response.data.data);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        // Add a new method to initialize data when editData is provided
        initializeData(data) {
            // console.log(data, 'DDDDD');
            if (data && data.permissions && data.permissions.length > 0 && this.permission_list.length > 0) {
                // console.log(data.permission_id, 'EEEEEE');
                let id_list = data.permissions;
                id_list.forEach(obj => {
                    const permission = this.permission_list.find(permission => permission.id === obj.id);
                    if (permission) {
                        permission.checked = true;
                    } else {
                        permission.checked = false;
                    }
                });
            } else {
                this.permission_list.map(opt => opt.checked = false);
            }
        },
        formatPermissionName(name) {
            // Replace underscores with spaces
            let formattedName = name.replace(/_/g, ' ');
            // Capitalize first letter of each word
            formattedName = formattedName.replace(/\b\w/g, char => char.toUpperCase());
            return formattedName;
        },
        checkedList(type) {
            // Iterate through the permission_list
            const allChecked = this.permission_list.every(permission => permission.checked);
            this.permission_list.forEach(permission => {
                switch (type) {
                    case 'all':
                        if (permission.checked && allChecked) {
                            permission.checked = false;
                        }
                        else {
                            permission.checked = true;
                        }
                        break;
                    case 'create':
                        if (!permission.checked) {
                            // If 'create' is already checked, don't change its state
                            permission.checked = permission.name.includes('create_');
                        }
                        else if (permission.name.includes('create_')) {
                            // Otherwise, set it based on the name
                            permission.checked = false;
                        }
                        break;
                    case 'read':
                        if (!permission.checked) {
                            // If 'read' is already checked, don't change its state
                            permission.checked = permission.name.includes('read_');
                        }
                        else if (permission.name.includes('read_')) {
                            // Otherwise, set it based on the name
                            permission.checked = false;
                        }
                        break;
                    case 'update':
                        if (!permission.checked) {
                            // If 'update' is already checked, don't change its state
                            permission.checked = permission.name.includes('update_');
                        }
                        else if (permission.name.includes('update_')) {
                            // Otherwise, set it based on the name
                            permission.checked = false;
                        }
                        break;
                    case 'delete':
                        if (!permission.checked) {
                            // If 'delete' is already checked, don't change its state
                            permission.checked = permission.name.includes('delete_');
                        }
                        else if (permission.name.includes('delete_')) {
                            // Otherwise, set it based on the name
                            permission.checked = false;
                        }
                        break;
                    default:
                        break;
                }
            });
        },
        getChunk(array, index) {
            const chunkSize = this.isMobile ? 1 : 4;
            const start = index * chunkSize;
            const end = Math.min(start + chunkSize, array.length);
            return array.slice(start, end);
        },
        getPermissionList() {
            if (this.permission_list.length === 0) {
                axios.get('/permissions', { company_id: this.companyId })
                    .then(response => {
                        console.log(response.data, 'Waht happening');
                        this.permission_list = response.data.permissions;
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }
        },
        openMessage(msg) {
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.message = ''
            this.open_message = false;
        }
    },
    mounted() {
        if (this.companyId) {
            this.getPermissionList();
        }
        this.updateIsMobile();
        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler(newValue) {
                this.initializeData(newValue);
            },
        },
        //---pemission list--
        companyId: {
            deep: true,
            handler(newValue) {
                this.getPermissionList();
            }
        }
    },
    computed: {
        // Computed property to determine if content should be visible
        isContentVisible() {
            // Check if any permission in the list is checked
            return this.permission_list.some(permission => permission.checked);
        }
    },
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.switch {
    position: relative;
    display: inline-block;
    width: 40px;
    height: 15px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
}

input:checked+.slider {
    background-color: #008000;
}

.slider:before {
    position: absolute;
    content: attr(data-content);
    height: 18px;
    width: 18px;
    left: 0px;
    bottom: 0px;
    background-color: rgb(29, 28, 28);
    -webkit-transition: .4s;
    transition: .4s;
    font-size: 10px;
    text-align: center;
    color: white;
}

input:checked+.slider:before {
    background-color: #008000;
}

input:checked+.slider {
    background-color: #2196F3;
}

input:focus+.slider {
    box-shadow: 0 0 1px #2196F3;
}


input:checked+.slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
    content: attr(data-content);

}

/* Rounded sliders */
.slider.round {
    border-radius: 34px;

}

.slider.round:before {
    border-radius: 50%;
}
</style>