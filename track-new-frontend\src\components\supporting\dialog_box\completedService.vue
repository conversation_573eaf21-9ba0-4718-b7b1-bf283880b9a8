<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-40 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div ref="modalBox"
                class="relative bg-white rounded-md mx-auto m-5 p-5 transform transition-transform duration-500 ease-in-out">
                <!-- Close Button -->
                <button @click="closeModal"
                    class="absolute z-10 -top-4 right-0 text-sm font-medium text-red-500 rounded-full p-1 hover:text-red-600 focus:outline-none"
                    title="close">
                    <font-awesome-icon icon="fa-solid fa-xmark" class="text-xl" />
                </button>
                <!-- Success Message -->
                <div class="flex flex-col items-center justify-center space-y-5">
                    <div class="text-green-500">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16" fill="none" viewBox="0 0 24 24"
                            stroke="currentColor" stroke-width="2">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M5 13l4 4L19 7" />
                        </svg>
                    </div>
                    <h2 class="text-xl font-bold text-gray-800">Service Completed Successfully !</h2>
                    <p class="text-gray-600 text-center"> <font-awesome-icon icon="fa-solid fa-globe" class="px-1" />
                        Would you like to send an google review link ?</p>
                </div>
                <!-- Buttons -->
                <div class="flex justify-center items-center mt-8 space-x-4">
                    <!-- <button @click="sendSMS('sms')"
                        class="px-6 py-2 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 focus:outline-none">
                        <font-awesome-icon icon="fa-solid fa-comment-sms" class="px-1" /> SMS
                    </button> -->
                    <!-- Send SMS Button -->
                    <button @click="sendSMS('whatsapp')"
                        class="px-6 py-2 text-sm font-medium text-white bg-blue-500 rounded-md hover:bg-blue-600 focus:outline-none">
                        <font-awesome-icon icon="fa-brands fa-whatsapp" class="px-1" /> WhatsApp
                    </button>
                </div>
            </div>
        </div>
        <!-- Confetti container -->
        <canvas id="confetti"></canvas>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        customer_id: Number
    },
    data() {
        return {
            isOpen: false,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
        };
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.startConfetti();
                }
            }, 100);
        },
    },
    mounted() {
        // Ensure confetti is correctly sized when the component is mounted
        this.resizeCanvas();
        window.addEventListener('resize', this.resizeCanvas);
        if (this.showModal) {
            setInterval(() => {
                this.startConfetti();
            }, 200);
        }
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.resizeCanvas);
    },
    methods: {
        resizeCanvas() {
            const canvas = document.getElementById('confetti');
            if (canvas) {
                canvas.width = window.innerWidth;
                canvas.height = window.innerHeight;
            }
        },
        startConfetti() {
            const retina = window.devicePixelRatio;
            const PI = Math.PI,
                random = Math.random,
                cos = Math.cos,
                sin = Math.sin;

            const canvas = document.getElementById('confetti');
            const ctx = canvas.getContext('2d');
            canvas.width = window.innerWidth * retina;
            canvas.height = window.innerHeight * retina;

            const speed = 50,
                duration = (1.0 / speed),
                confettiRibbonCount = 15,
                ribbonPaperCount = 30,
                ribbonPaperDist = 8.0,
                ribbonPaperThick = 8.0,
                confettiPaperCount = 95,
                DEG_TO_RAD = PI / 180,
                colors = [
                    ["#df0049", "#660671"],
                    ["#00e857", "#005291"],
                    ["#2bebbc", "#05798a"],
                    ["#ffd200", "#b06c00"],
                ];

            // Vector2 Class
            function Vector2(_x, _y) {
                this.x = _x;
                this.y = _y;
                this.Length = function () {
                    return Math.sqrt(this.SqrLength());
                };
                this.SqrLength = function () {
                    return this.x * this.x + this.y * this.y;
                };
                this.Add = function (_vec) {
                    this.x += _vec.x;
                    this.y += _vec.y;
                };
                this.Sub = function (_vec) {
                    this.x -= _vec.x;
                    this.y -= _vec.y;
                };
                this.Div = function (_f) {
                    this.x /= _f;
                    this.y /= _f;
                };
                this.Mul = function (_f) {
                    this.x *= _f;
                    this.y *= _f;
                };
                this.Normalize = function () {
                    const sqrLen = this.SqrLength();
                    if (sqrLen !== 0) {
                        const factor = 1.0 / Math.sqrt(sqrLen);
                        this.x *= factor;
                        this.y *= factor;
                    }
                };
            }

            // EulerMass Class
            function EulerMass(_x, _y, _mass, _drag) {
                this.position = new Vector2(_x, _y);
                this.mass = _mass;
                this.drag = _drag;
                this.force = new Vector2(0, 0);
                this.velocity = new Vector2(0, 0);
            }

            EulerMass.prototype = {
                AddForce: function (_f) {
                    this.force.Add(_f);
                },
                Integrate: function (_dt) {
                    const acc = this.CurrentForce(this.position);
                    acc.Div(this.mass);
                    const posDelta = new Vector2(this.velocity.x, this.velocity.y);
                    posDelta.Mul(_dt);
                    this.position.Add(posDelta);
                    acc.Mul(_dt);
                    this.velocity.Add(acc);
                    this.force = new Vector2(0, 0);
                },
                CurrentForce: function (_pos) {
                    const totalForce = new Vector2(this.force.x, this.force.y);
                    const speed = this.velocity.Length();
                    const dragVel = new Vector2(this.velocity.x, this.velocity.y);
                    dragVel.Mul(this.drag * this.mass * speed);
                    totalForce.Sub(dragVel);
                    return totalForce;
                },
            };

            // ConfettiPaper Class
            function ConfettiPaper(_x, _y) {
                this.pos = new Vector2(_x, _y);
                this.rotationSpeed = (random() * 600 + 800);
                this.angle = DEG_TO_RAD * random() * 360;
                this.rotation = DEG_TO_RAD * random() * 360;
                this.cosA = 1.0;
                this.size = 5.0;
                this.oscillationSpeed = random() * 1.5 + 0.5;
                this.xSpeed = 40.0;
                this.ySpeed = random() * 60 + 50.0;
                this.corners = [];
                this.time = random();
                const ci = Math.round(random() * (colors.length - 1));
                this.frontColor = colors[ci][0];
                this.backColor = colors[ci][1];
                for (let i = 0; i < 4; i++) {
                    const dx = cos(this.angle + DEG_TO_RAD * (i * 90 + 45));
                    const dy = sin(this.angle + DEG_TO_RAD * (i * 90 + 45));
                    this.corners[i] = new Vector2(dx, dy);
                }
            }

            ConfettiPaper.prototype = {
                Update: function (dt) {
                    this.time += dt;
                    this.rotation += this.rotationSpeed * dt;
                    this.cosA = cos(DEG_TO_RAD * this.rotation);
                    this.pos.x += cos(this.time * this.oscillationSpeed) * this.xSpeed * dt;
                    this.pos.y += this.ySpeed * dt;
                    if (this.pos.y > ConfettiPaper.bounds.y) {
                        this.pos.x = random() * ConfettiPaper.bounds.x;
                        this.pos.y = 0;
                    }
                },
                Draw: function (g) {
                    g.fillStyle = this.cosA > 0 ? this.frontColor : this.backColor;
                    g.beginPath();
                    g.moveTo(
                        (this.pos.x + this.corners[0].x * this.size) * retina,
                        (this.pos.y + this.corners[0].y * this.size * this.cosA) * retina
                    );
                    for (let i = 1; i < 4; i++) {
                        g.lineTo(
                            (this.pos.x + this.corners[i].x * this.size) * retina,
                            (this.pos.y + this.corners[i].y * this.size * this.cosA) * retina
                        );
                    }
                    g.closePath();
                    g.fill();
                },
            };

            // ConfettiRibbon Class with wavy movement
            function ConfettiRibbon(_x, _y, _count, _dist, _thickness, _angle, _mass, _drag) {
                this.particleDist = _dist;
                this.particleCount = _count;
                this.particles = [];
                const ci = Math.round(Math.random() * (colors.length - 1));
                this.frontColor = colors[ci][0];
                this.backColor = colors[ci][1];
                this.xOff = cos(DEG_TO_RAD * _angle) * _thickness;
                this.yOff = sin(DEG_TO_RAD * _angle) * _thickness;
                this.oscillationSpeed = random() * 0.5 + 0.5;  // Slower for smooth ribbon
                this.oscillationDistance = random() * 10 + 10;  // Smaller oscillation for gentle movement
                this.ySpeed = random() * 20 + 80;  // Slow falling ribbons
                this.position = new Vector2(_x, _y);
                this.prevPosition = new Vector2(_x, _y);
                this.velocityInherit = random() * 1 + 2;
                this.time = random() * 100;

                for (let i = 0; i < this.particleCount; i++) {
                    this.particles.push(new EulerMass(_x, _y - i * this.particleDist, _mass, _drag));
                }
            }

            ConfettiRibbon.prototype = {
                Update: function (dt) {
                    this.time += dt * this.oscillationSpeed;
                    this.position.y += this.ySpeed * dt;
                    this.position.x += cos(this.time) * this.oscillationDistance * dt;
                    this.particles[0].position = this.position;

                    let delta;
                    for (let i = 1; i < this.particleCount; i++) {
                        delta = new Vector2(this.particles[i].position.x, this.particles[i].position.y);
                        delta.Sub(this.particles[i - 1].position);
                        delta.Normalize();
                        delta.Mul(this.particleDist);
                        this.particles[i].position = new Vector2(
                            this.particles[i - 1].position.x + delta.x,
                            this.particles[i - 1].position.y + delta.y
                        );
                    }
                },
                // Add the Side function to ConfettiRibbon
                Side: function (x1, y1, x2, y2, x3, y3) {
                    return (x1 - x2) * (y3 - y2) - (y1 - y2) * (x3 - x2);
                },
                Draw: function (_g) {
                    for (let i = 0; i < this.particleCount - 1; i++) {
                        // Calculate current and next particle positions with offsets
                        const p0 = new Vector2(
                            this.particles[i].position.x + this.xOff,
                            this.particles[i].position.y + this.yOff
                        );
                        const p1 = new Vector2(
                            this.particles[i + 1].position.x + this.xOff,
                            this.particles[i + 1].position.y + this.yOff
                        );

                        // Determine which side of the ribbon we are drawing
                        if (
                            this.Side(
                                this.particles[i].position.x,
                                this.particles[i].position.y,
                                this.particles[i + 1].position.x,
                                this.particles[i + 1].position.y,
                                p1.x,
                                p1.y
                            ) < 0
                        ) {
                            _g.fillStyle = this.frontColor;
                            _g.strokeStyle = this.frontColor;
                        } else {
                            _g.fillStyle = this.backColor;
                            _g.strokeStyle = this.backColor;
                        }

                        // Begin drawing the ribbon
                        if (i === 0) {
                            _g.beginPath();
                            _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
                            _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
                            _g.lineTo(
                                ((this.particles[i + 1].position.x + p1.x) * 0.5) * retina,
                                ((this.particles[i + 1].position.y + p1.y) * 0.5) * retina
                            );
                            _g.closePath();
                            _g.stroke();
                            _g.fill();
                        } else if (i === this.particleCount - 2) {
                            _g.beginPath();
                            _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
                            _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
                            _g.lineTo(
                                ((this.particles[i].position.x + p0.x) * 0.5) * retina,
                                ((this.particles[i].position.y + p0.y) * 0.5) * retina
                            );
                            _g.closePath();
                            _g.stroke();
                            _g.fill();
                        } else {
                            // For middle particles, draw continuous segments
                            _g.beginPath();
                            _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
                            _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
                            _g.lineTo(p1.x * retina, p1.y * retina);
                            _g.lineTo(p0.x * retina, p0.y * retina);
                            _g.closePath();
                            _g.stroke();
                            _g.fill();
                        }
                    }
                }
            };
            // function ConfettiRibbon(_x, _y, _count, _dist, _thickness, _angle, _mass, _drag) {
            //     this.particleDist = _dist;
            //     this.particleCount = _count;
            //     this.particleMass = _mass;
            //     this.particleDrag = _drag;
            //     this.particles = new Array();
            //     var ci = Math.round(Math.random() * (colors.length - 1));
            //     this.frontColor = colors[ci][0];
            //     this.backColor = colors[ci][1];
            //     this.xOff = (cos(DEG_TO_RAD * _angle) * _thickness);
            //     this.yOff = (sin(DEG_TO_RAD * _angle) * _thickness);
            //     this.position = new Vector2(_x, _y);
            //     this.prevPosition = new Vector2(_x, _y);
            //     this.velocityInherit = (Math.random() * 2 + 4);
            //     this.time = Math.random() * 100;
            //     this.oscillationSpeed = (Math.random() * 2 + 2);
            //     this.oscillationDistance = (Math.random() * 40 + 40);
            //     this.ySpeed = (Math.random() * 40 + 80);
            //     for (var i = 0; i < this.particleCount; i++) {
            //         this.particles[i] = new EulerMass(_x, _y - i * this.particleDist, this.particleMass, this.particleDrag);
            //     }

            // }
            // ConfettiRibbon.prototype = {
            //     Update: function (_dt) {
            //         var i = 0;
            //         this.time += _dt * this.oscillationSpeed;
            //         this.position.y += this.ySpeed * _dt;
            //         this.position.x += cos(this.time) * this.oscillationDistance * _dt;
            //         this.particles[0].position = this.position;
            //         var dX = this.prevPosition.x - this.position.x;
            //         var dY = this.prevPosition.y - this.position.y;
            //         var delta = Math.sqrt(dX * dX + dY * dY);
            //         this.prevPosition = new Vector2(this.position.x, this.position.y);
            //         let delta1;
            //         for (i = 1; i < this.particleCount; i++) {
            //             delta1 = new Vector2(this.particles[i - 1].position, this.particles[i].position);
            //             delta1.Normalize();
            //             delta1.Mul((delta / _dt) * this.velocityInherit);
            //             this.particles[i].AddForce(delta1);
            //         }
            //         for (i = 1; i < this.particleCount; i++) {
            //             this.particles[i].Integrate(_dt);
            //         }
            //         for (i = 1; i < this.particleCount; i++) {
            //             var rp2 = new Vector2(this.particles[i].position.x, this.particles[i].position.y);
            //             rp2.Sub(this.particles[i - 1].position);
            //             rp2.Normalize();
            //             rp2.Mul(this.particleDist);
            //             rp2.Add(this.particles[i - 1].position);
            //             this.particles[i].position = rp2;
            //         }
            //         if (this.position.y > ConfettiRibbon.bounds.y + this.particleDist * this.particleCount) {
            //             this.Reset();
            //         }
            //     },
            //     Reset: function () {
            //         this.position.y = -Math.random() * ConfettiRibbon.bounds.y;
            //         this.position.x = Math.random() * ConfettiRibbon.bounds.x;
            //         this.prevPosition = new Vector2(this.position.x, this.position.y);
            //         this.velocityInherit = Math.random() * 2 + 4;
            //         this.time = Math.random() * 100;
            //         this.oscillationSpeed = Math.random() * 2.0 + 1.5;
            //         this.oscillationDistance = (Math.random() * 40 + 40);
            //         this.ySpeed = Math.random() * 40 + 80;
            //         var ci = Math.round(Math.random() * (colors.length - 1));
            //         this.frontColor = colors[ci][0];
            //         this.backColor = colors[ci][1];
            //         this.particles = new Array();
            //         for (var i = 0; i < this.particleCount; i++) {
            //             this.particles[i] = new EulerMass(this.position.x, this.position.y - i * this.particleDist, this.particleMass, this.particleDrag);
            //         }
            //     },
            //     Draw: function (_g) {
            //         for (var i = 0; i < this.particleCount - 1; i++) {
            //             var p0 = new Vector2(this.particles[i].position.x + this.xOff, this.particles[i].position.y + this.yOff);
            //             var p1 = new Vector2(this.particles[i + 1].position.x + this.xOff, this.particles[i + 1].position.y + this.yOff);
            //             if (this.Side(this.particles[i].position.x, this.particles[i].position.y, this.particles[i + 1].position.x, this.particles[i + 1].position.y, p1.x, p1.y) < 0) {
            //                 _g.fillStyle = this.frontColor;
            //                 _g.strokeStyle = this.frontColor;
            //             } else {
            //                 _g.fillStyle = this.backColor;
            //                 _g.strokeStyle = this.backColor;
            //             }
            //             if (i == 0) {
            //                 _g.beginPath();
            //                 _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
            //                 _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
            //                 _g.lineTo(((this.particles[i + 1].position.x + p1.x) * 0.5) * retina, ((this.particles[i + 1].position.y + p1.y) * 0.5) * retina);
            //                 _g.closePath();
            //                 _g.stroke();
            //                 _g.fill();
            //                 _g.beginPath();
            //                 _g.moveTo(p1.x * retina, p1.y * retina);
            //                 _g.lineTo(p0.x * retina, p0.y * retina);
            //                 _g.lineTo(((this.particles[i + 1].position.x + p1.x) * 0.5) * retina, ((this.particles[i + 1].position.y + p1.y) * 0.5) * retina);
            //                 _g.closePath();
            //                 _g.stroke();
            //                 _g.fill();
            //             } else if (i == this.particleCount - 2) {
            //                 _g.beginPath();
            //                 _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
            //                 _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
            //                 _g.lineTo(((this.particles[i].position.x + p0.x) * 0.5) * retina, ((this.particles[i].position.y + p0.y) * 0.5) * retina);
            //                 _g.closePath();
            //                 _g.stroke();
            //                 _g.fill();
            //                 _g.beginPath();
            //                 _g.moveTo(p1.x * retina, p1.y * retina);
            //                 _g.lineTo(p0.x * retina, p0.y * retina);
            //                 _g.lineTo(((this.particles[i].position.x + p0.x) * 0.5) * retina, ((this.particles[i].position.y + p0.y) * 0.5) * retina);
            //                 _g.closePath();
            //                 _g.stroke();
            //                 _g.fill();
            //             } else {
            //                 _g.beginPath();
            //                 _g.moveTo(this.particles[i].position.x * retina, this.particles[i].position.y * retina);
            //                 _g.lineTo(this.particles[i + 1].position.x * retina, this.particles[i + 1].position.y * retina);
            //                 _g.lineTo(p1.x * retina, p1.y * retina);
            //                 _g.lineTo(p0.x * retina, p0.y * retina);
            //                 _g.closePath();
            //                 _g.stroke();
            //                 _g.fill();
            //             }
            //         }
            //     },
            //     Side: function (x1, y1, x2, y2, x3, y3) {
            //         return ((x1 - x2) * (y3 - y2) - (y1 - y2) * (x3 - x2));
            //     }
            // };
            ConfettiRibbon.bounds = new Vector2(0, 0);
            const confetti = {};
            confetti.Context = function (id) {
                var i = 0;
                var canvas = document.getElementById(id);
                var canvasParent = canvas.parentNode;
                var canvasWidth = canvasParent.offsetWidth;
                var canvasHeight = canvasParent.offsetHeight;
                canvas.width = canvasWidth * retina;
                canvas.height = canvasHeight * retina;
                var context = canvas.getContext('2d');
                var interval = null;
                var confettiRibbons = new Array();
                ConfettiRibbon.bounds = new Vector2(canvasWidth, canvasHeight);
                for (i = 0; i < confettiRibbonCount; i++) {
                    confettiRibbons[i] = new ConfettiRibbon(random() * canvasWidth, -random() * canvasHeight * 2, ribbonPaperCount, ribbonPaperDist, ribbonPaperThick, 45, 1, 0.05);
                }
                var confettiPapers = new Array();
                ConfettiPaper.bounds = new Vector2(canvasWidth, canvasHeight);
                for (i = 0; i < confettiPaperCount; i++) {
                    confettiPapers[i] = new ConfettiPaper(random() * canvasWidth, random() * canvasHeight);
                }
                this.resize = function () {
                    canvasWidth = canvasParent.offsetWidth;
                    canvasHeight = canvasParent.offsetHeight;
                    canvas.width = canvasWidth * retina;
                    canvas.height = canvasHeight * retina;
                    ConfettiPaper.bounds = new Vector2(canvasWidth, canvasHeight);
                    ConfettiRibbon.bounds = new Vector2(canvasWidth, canvasHeight);
                }
                this.start = function () {
                    this.stop()
                    var context = this;
                    this.update();
                }
                this.stop = function () {
                    cAF(this.interval);
                }
                this.update = function () {
                    var i = 0;
                    context.clearRect(0, 0, canvas.width, canvas.height);
                    for (i = 0; i < confettiPaperCount; i++) {
                        confettiPapers[i].Update(duration);
                        confettiPapers[i].Draw(context);
                    }
                    for (i = 0; i < confettiRibbonCount; i++) {
                        confettiRibbons[i].Update(duration);
                        confettiRibbons[i].Draw(context);
                    }
                    this.interval = rAF(function () {
                        confetti.update();
                    });
                }
            };

            ConfettiPaper.bounds = new Vector2(canvas.width, canvas.height);
            ConfettiRibbon.bounds = new Vector2(canvas.width, canvas.height);

            const confettiPapers = [];
            const confettiRibbons = [];

            for (let i = 0; i < confettiPaperCount; i++) {
                confettiPapers.push(new ConfettiPaper(random() * canvas.width, random() * canvas.height));
            }
            for (let i = 0; i < confettiRibbonCount; i++) {
                confettiRibbons.push(new ConfettiRibbon(random() * canvas.width, -random() * canvas.height * 2, ribbonPaperCount, ribbonPaperDist, ribbonPaperThick, 45, 1, 0.05));
            }

            function updateConfetti() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                for (let i = 0; i < confettiPaperCount; i++) {
                    confettiPapers[i].Update(duration);
                    confettiPapers[i].Draw(ctx);
                }
                for (let i = 0; i < confettiRibbonCount; i++) {
                    confettiRibbons[i].Update(duration);
                    confettiRibbons[i].Draw(ctx);
                }
                requestAnimationFrame(updateConfetti);
            }

            updateConfetti();
        },
        closeModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('onClose');
            }, 300);
        },
        sendSMS(type) {
            if (this.customer_id && this.customer_id !== '') {
                this.open_loader = true;
                let send_data = type === 'sms' ? { is_sms: true } : { is_whatsapp: true };
                axios.post(`send-review-sms/${this.customer_id}`, send_data)
                    .then(response => {
                        // console.log('waht about response', response.data);
                        this.open_loader = false;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                        this.closeModal();
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })
            }

        }
    },
};
</script>

<style scoped>
canvas {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    pointer-events: none;
}
</style>