<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mt-[60px]': isMobile, 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" :customerData="dataFromChild" @searchData="getFiteredDataList"
                @refresh_store="refresh_store">
            </headbar> -->

            <!-- services home -->
            <div class="relative m-1">
                <!-- <home v-if="showViewCustomer === false" :data="viewCustomerData" @showViewCustomer = "showViewCustomerComponent" ></home> -->
                <viewCustomers @dataToParent="handleDataFromChild" :searchedData="getFilteredData"
                    :store_refresh="store_refresh" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </viewCustomers>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/customers/view_customer/headbar.vue';
// import home from '../supporting/customers/home.vue';
import viewCustomers from '../supporting/customers/view_customer/viewCustomers.vue';
import { useMeta } from '@/composables/useMeta';

export default {
    name: 'customers_view',
    components: {
        // sidebar,
        // headbar,
        viewCustomers,
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            showViewCustomer: false,
            viewCustomerData: null,
            route_item: 4,
            data: [],
            dataFromChild: [],
            getFilteredData: [],
            //--api integration--
            companyId: null,
            userId: null,
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Customer Overview';
        const pageDescription = 'View key customer information and activity at a glance. Streamline relationship management with organized data.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    methods: {
        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        showViewCustomerComponent(data) {
            // console.log(data, 'what about data..!');
            this.showViewCustomer = true;
            this.viewCustomerData = data;
        },
        // Method to go back to home
        goBackToHome() {
            this.showViewCustomer = false;
        },
        // Method to parse the URL
        parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const id = urlParts[urlParts.length - 1];
            const existData = this.data.find((record => record.id === Number(id)));
            // console.log(existData, 'What happening the exist data....');
            if (existData) {
                // console.log('Is it working...! ');
                this.viewCustomerData = existData;
                // console.log(this.viewServiceData, 'Is it updated..!');
            }
        },
        handleDataFromChild(data) {
            this.dataFromChild = data;
        },
        getFiteredDataList(data) {
            this.getFilteredData = data;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>