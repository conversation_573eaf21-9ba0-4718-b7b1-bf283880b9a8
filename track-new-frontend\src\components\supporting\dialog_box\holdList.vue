<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Hold List
                </p>
                <p class="close" @click="closeModal">&times;</p>
            </div>
            <!---Display hold list-->
            <div class="table-container overflow-auto mt-2 mb-5 ml-1 mr-1">
                <table class="table w-full">
                    <thead>
                        <tr class="bg-gray-600 text-white">
                            <th class="border py-2">ID</th>
                            <th class="border py-2">Date</th>
                            <th class="border py-2">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(record, index) in hold_list" :key="index" class="text-center">
                            <td class="py-2 border">{{ record.id }}</td>
                            <td class="py-2 border">{{ record.created_at.substring(0, 10) }}</td>
                            <td class="py-2 border">
                                <div class="flex justify-center">
                                    <button @click="openEdit(record)" class="mr-1 text-blue-800">
                                        <font-awesome-icon icon="fa-solid fa-pencil" size="lg" />
                                    </button>
                                    <button @click="confirmDelete(record)" class="text-red-600 ml-1">
                                        <font-awesome-icon icon="fa-solid fa-trash-can" size="lg" />
                                    </button>
                                </div>
                            </td>

                        </tr>
                    </tbody>
                </table>
            </div>
            <div v-if="pagination" class="flex justify-between px-2 mb-2">
                <p class="text-xs"><span class="">Total records:</span> <span class="font-bold">{{ pagination.total
                        }}</span></p>
                <div class="flex justify-center items-center text-xs">
                    <button @click="updatePage(pagination.current_page - 1)" :disabled="pagination.current_page === 1"
                        :class="{ 'bg-blue-600 hover:bg-blue-500': pagination.current_page !== 1, 'bg-gray-300 hover:bg-gray-300 text-gray-200': pagination.current_page === 1 }"
                        class="flex  px-1 py-1 text-white justify-between sm:text-md mr-2 text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                        <span class="pl-1">Prev</span>
                    </button>
                    <button @click="updatePage(pagination.current_page + 1)"
                        :disabled="pagination.current_page === pagination.last_page"
                        :class="{ 'bg-blue-600 hover:bg-blue-500': pagination.current_page !== pagination.last_page, 'bg-gray-300 hover:bg-gray-300 text-gray-200': pagination.current_page === pagination.last_page }"
                        class="flex px-1 py-1 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                        <span class="pr-1">Next</span>
                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                    </button>
                </div>
            </div>
        </div>
        <!--confirm box---->
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <confirmDialog :show-modal="open_confirm" @onConfirm="editRecord" @onCancel="cancelRecord"></confirmDialog>

    </div>
</template>

<script>
import confirmbox from './confirmbox.vue';
import confirmDialog from './confirmDialog.vue';
export default {
    components: {
        confirmbox,
        confirmDialog
    },
    props: {
        showModal: Boolean,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            formValues: {},
            message: '',
            isInputFocused: {},
            companyId: null,
            userId: null,
            hold_list: [],
            records_per_page: 10,
            pagination: {},
            open_confirmBox: false,
            selected_id: null,
            //---confirm---
            open_confirm: false,
            selected_record: null,
        };
    },
    methods: {
        closeModal(data) {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event

            setTimeout(() => {
                if (data) {
                    this.$emit('close-Modal', data);
                } else {
                    this.$emit('close-Modal');
                }
                this.message = '';
            }, 300);
        },
        //---close message---
        closeMessageDialog() {
            this.isMessage = false;
        },

        //---add new--
        displayNextField(index, type) {
            // console.log(this.formValues.serial_number[index], 'Waht happening..!');
            if (this.formValues.serial_number[index].length > 0 && this.formValues.serial_number[index + 1] === undefined) {
                this.formValues.serial_number.push('');
            }
            else if (type === 'enter' && index === this.formValues.serial_number.length - 1) {
                this.formValues.serial_number.push('');
            }
        },

        getHoldList(per_page, page) {
            axios.get('/hold_invoices', { params: { company_id: this.companyId, per_page: per_page, page: page } })
                .then(response => {
                    console.log(response.data, 'hold list');
                    this.hold_list = response.data.data;
                    this.pagination = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        deleteRecord() {
            if (this.selected_id !== null) {
                axios.delete(`/hold_invoices/${this.selected_id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        console.log(response.data, 'hold delete');
                        this.hold_list = this.hold_list.filter(opt => opt.id !== this.selected_id);
                        this.$emit('refresh_hold');
                        this.cancelDelete();
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }

        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.selected_id = null;
        },
        updatePage(page_number) {
            this.getHoldList(this.records_per_page, page_number);
        },
        confirmDelete(record) {
            this.selected_id = record.id;
            this.open_confirmBox = true;
        },
        openEdit(record) {
            this.selected_record = record;
            this.open_confirm = true;
        },
        editRecord() {
            this.open_confirm = false;
            this.closeModal(this.selected_record);
        },
        cancelRecord() {
            this.open_confirm = false;
            this.selected_record = null;
        }

    },
    mounted() {
        const collectForm01 = localStorage.getItem('track_new');
        if (collectForm01) {
            let dataParse = JSON.parse(collectForm01);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        //---get hold list---
        // this.getHoldList(this.records_per_page, 1);
    },
    watch: {
        showModal(newValue) {
            if (newValue === true) {
                //---get hold list---
                this.getHoldList(this.records_per_page, 1);
            }
            // console.log(newValue, 'Waht happening..!');
            setTimeout(() => {
                this.isOpen = newValue;
                // this.handleFocus(this.formValues.serial_number.length - 1);
            }, 100);
        },
    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>