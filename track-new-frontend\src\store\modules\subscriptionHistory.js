// store/modules/subscriptionHistory.js
import axios from "axios";

const state = {
  subscription_history: [],
  };

  const mutations = {
      SET_SUBSCRIPTIONHistory(state, {data}) {
          state.subscription_history = data;
    },
      RESET_STATE(state) {
          state.subscription_history = [];
      }

  };

  const actions = {
    updateSubscriptionHistoryName({ commit }, subscription_historyData) {
      setTimeout(() => {
        // Commit mutation to update subscription_history name
        commit('SET_SUBSCRIPTIONHistory', subscription_historyData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchSubscriptionHistoryList({ commit }) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get(`/orders`, { params: { company_id: company_id, per_page: 'all'} })
            .then(response => {
              // Handle response
              let { data } = response.data; 
              // Add 5 hours and 30 minutes to each invoice_date
              if (data && data.length > 0) {
                data.forEach(item => {
                  if (item.created_at) {                   
                    
                    let invoiceDate = new Date(item.created_at);
                    invoiceDate.setHours(invoiceDate.getHours() + 5);
                    invoiceDate.setMinutes(invoiceDate.getMinutes() + 30);
                    item.created_at = invoiceDate.toISOString();
                  }
                });
              }
              commit('SET_SUBSCRIPTIONHistory', {data});
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    
  };

  const getters = {
    currentSubscriptionHistoryList(state) {
      return state.subscription_history;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
