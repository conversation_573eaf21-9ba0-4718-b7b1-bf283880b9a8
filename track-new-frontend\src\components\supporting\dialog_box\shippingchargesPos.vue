<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Shipping Charges
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block text-sm bg p-5">
                <!-- Disclaimer -->
                <div class="w-full mb-4 relative">
                    <!-- <div > -->
                    <label for="shipping"
                        class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                        @click="isInputFocused.shipping = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.shipping !== undefined && formValues.shipping >= 0) || isInputFocused.shipping, 'text-blue-700': isInputFocused.shipping }">
                        shipping Chages</label>
                    <input v-model="formValues.shipping" type="number"
                        class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                        @focus="isInputFocused.shipping = true" @blur="isInputFocused.shipping = false" />
                    <!-- </div> -->

                    <p v-if="message !== ''" class="text-red-500 font-bold text-sm py-2">{{ message }}</p>
                </div>
                <!-- shipping via -->
                <div class="w-full mb-4 relative">
                    <!-- <div > -->
                    <label for="shipping_type"
                        class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                        @click="isInputFocused.shipping_type = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.shipping_type !== undefined && formValues.shipping_type) || isInputFocused.shipping_type, 'text-blue-700': isInputFocused.shipping_type }">
                        Shipping Via</label>
                    <input v-model="formValues.shipping_type" type="text"
                        class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                        @focus="isInputFocused.shipping_type = true" @blur="isInputFocused.shipping_type = false" />

                </div>
                <!-- </div> -->
                <div class="w-full mb-4 relative">
                    <label for="cod"
                        class="text-sm font-bold absolute left-2 top-3 rounded text-gray-300 transition-top linear duration-300"
                        @click="isInputFocused.cod = true"
                        :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.cod !== undefined && formValues.cod) || isInputFocused.cod, 'text-blue-700': isInputFocused.cod }">
                        Shipping ID</label>
                    <input v-model="formValues.cod" type="text"
                        class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                        @focus="isInputFocused.cod = true" @blur="isInputFocused.cod = false" />
                </div>
                <!--buttons-->
                <div class="flex justify-center items-center">
                    <button class="border rounded text-white bg-green-700 font-bold px-4 py-2 hover:bg-green-600"
                        @click="saveTerms">Add</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        itemData: Object,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            formValues: {},
            message: '',
            isInputFocused: {}
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-Modal');
                this.message = '';
            }, 300);
        },
        //---close message---
        closeMessageDialog() {
            this.isMessage = false;
        },
        //---save terms and condition---
        saveTerms() {
            if (this.formValues.shipping >= 0 && this.formValues.shipping !== '') {
                this.isOpen = false;
                setTimeout(() => {
                    this.$emit('close-Modal', this.formValues);
                }, 300);

            } else {
                // this.isMessage = true;
                this.message = 'Field is empty, Please fill..!';
            }

        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        formValues: {
            deep: true,
            handler(newValue) {
                this.message = '';
            }
        },
        itemData: {
            deep: true,
            handler(newValue) {
                this.formValues = newValue;
            }
        }

    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>