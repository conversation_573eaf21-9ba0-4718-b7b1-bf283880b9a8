// store/modules/companies.js
import axios from "axios";

const state = {
  companies: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  };

  const mutations = {
      SET_COMPANY(state, {data}) {
        //   console.log(data, 'data', pagination, 'pagination', status_counts, 'status');
      state.companies = data;
       // After updating, dispatch the action to localStorageData module
       this.dispatch('localStorageData/fetchLocalDataList');
    },
      RESET_STATE(state) {
        state.companies = {};
        state.lastFetchTime = null;
        state.isFetching = false;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    }

  };

  const actions = {
    updateCompanyName({ commit }, companiesData) {
      // Simulate an asynchronous operation (e.g., API call) to update companies name
      setTimeout(() => {
        // Commit mutation to update companies name
        commit('SET_COMPANY', companiesData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchCompanyList({ state, commit, dispatch }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;     
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds)) {
        return; // Skip request if less than 30 seconds have passed since the last request
      }      
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '' && Object.keys(state.companies).length === 0) {
          // Set the request status to true (indicating that the request is in progress)
          commit('SET_IS_FETCHING', true);
          axios.get(`/companies/${company_id}`, { params: { company_id: company_id } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Company list..!');
              let { data } = response.data;
              commit('SET_COMPANY', { data });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              //---update loacl storage data--
            // dispatch('localStorageData/fetchLocalDataList');
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
          
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    }, 
    async updateCompanyList({ state, commit }) {      
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get(`/companies/${company_id}`, { params: { company_id: company_id} })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Company list..!');
              let { data} = response.data; 
              commit('SET_COMPANY', { data });              
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
          
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    }, 
  };

  const getters = {
    currentCompanyList(state) {
      return state.companies;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
