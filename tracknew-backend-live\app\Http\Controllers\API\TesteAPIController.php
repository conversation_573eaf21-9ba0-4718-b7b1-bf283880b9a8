<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateTesteAPIRequest;
use App\Http\Requests\API\UpdateTesteAPIRequest;
use App\Models\Teste;
use App\Repositories\TesteRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class TesteController
 * @package App\Http\Controllers\API
 */

class TesteAPIController extends AppBaseController
{
    /** @var  TesteRepository */
    private $testeRepository;

    public function __construct(TesteRepository $testeRepo)
    {
        $this->testeRepository = $testeRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/testes",
     *      summary="getTesteList",
     *      tags={"Teste"},
     *      description="Get all Testes",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Teste")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $testes = $this->testeRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($testes->toArray(), 'Testes retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/testes",
     *      summary="createTeste",
     *      tags={"Teste"},
     *      description="Create Teste",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Teste"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateTesteAPIRequest $request)
    {
        $input = $request->all();

        $teste = $this->testeRepository->create($input);

        return $this->sendResponse($teste->toArray(), 'Teste saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/testes/{id}",
     *      summary="getTesteItem",
     *      tags={"Teste"},
     *      description="Get Teste",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Teste",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Teste"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Teste $teste */
        $teste = $this->testeRepository->find($id);

        if (empty($teste)) {
            return $this->sendError('Teste not found');
        }

        return $this->sendResponse($teste->toArray(), 'Teste retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/testes/{id}",
     *      summary="updateTeste",
     *      tags={"Teste"},
     *      description="Update Teste",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Teste",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Teste"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateTesteAPIRequest $request)
    {
        $input = $request->all();

        /** @var Teste $teste */
        $teste = $this->testeRepository->find($id);

        if (empty($teste)) {
            return $this->sendError('Teste not found');
        }

        $teste = $this->testeRepository->update($input, $id);

        return $this->sendResponse($teste->toArray(), 'Teste updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/testes/{id}",
     *      summary="deleteTeste",
     *      tags={"Teste"},
     *      description="Delete Teste",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Teste",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Teste $teste */
        $teste = $this->testeRepository->find($id);

        if (empty($teste)) {
            return $this->sendError('Teste not found');
        }

        $teste->delete();

        return $this->sendSuccess('Teste deleted successfully');
    }
}
