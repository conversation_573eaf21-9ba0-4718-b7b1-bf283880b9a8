// store/modules/service_list.js
import axios from "axios";

const state = {
  service_list: [],
  status_counts: [],
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  previousPerPage: null,
  previousid: null,
  };
  const mutations = {
    SET_SERVICELIST(state, { data, pagination, status_counts, id }) {     
      let findIndex = state.service_list.findIndex(opt => opt.category === id);
      // console.log(findIndex, 'Waht is INDEX', status_counts);
      
      if (findIndex !== -1) {
          //----this condition is not satishfied ????----
          if (pagination.current_page > state.service_list[findIndex].pagination.current_page) {
            state.service_list[findIndex].data = [...state.service_list[findIndex].data, ...data];
            state.service_list[findIndex].pagination = pagination;
          } else {
            if (pagination.current_page == 1) {
              state.service_list[findIndex].data = data;
              state.service_list[findIndex].pagination = pagination;
            } else {
              state.service_list[findIndex].data = [data.splice(((pagination.current_page) * (state.service_list[findIndex].pagination.per_page) - 1), pagination.per_page)];
              state.service_list[findIndex].pagination = pagination;             
           }            
          }
          if (status_counts && Object.keys(status_counts).length > 0) {
            state.status_counts = status_counts;           
          }else if(status_counts.length === 0){
            state.status_counts = status_counts;             
          }  
        } else {
        state.service_list.push({ category: id, data: data, pagination: pagination });        
          if (status_counts && Object.keys(status_counts).length > 0) {
            state.status_counts = status_counts;           
          } else if(status_counts.length === 0){
            state.status_counts = status_counts;             
          }          
      } 
      
    },
      RESET_STATE(state) {
        state.service_list = [];
        state.status_counts = [];  
        state.lastFetchTime = null;
        state.isFetching = false;
        state.previousid = null;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
    SET_PREVIOUS_PER_PAGE(state, perPage) {
      state.previousPerPage = perPage; // Store the previous per_page value
    },
    SET_PREVIOUS_ID(state, id) {
      state.previousid = id;
    }
  };
  const actions = {
    updateServiceName({ commit }, data) {
      // Simulate an asynchronous operation (e.g., API call) to update service_list name
      setTimeout(() => {
        // Commit mutation to update service_list name
        commit('SET_SERVICELIST', data);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchServiceList({ commit, state, rootState }, { id, page, per_page }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['service_update']; 
      if (state.previousPerPage != per_page) {
        commit('SET_PREVIOUS_PER_PAGE', per_page); // Update the per_page tracking
        commit('SET_LAST_FETCH_TIME', null); // Force a new request if per_page changed
      }
      if (state.previousid != id) {
        commit('SET_PREVIOUS_ID', id); // Update the per_page tracking
        commit('SET_LAST_FETCH_TIME', null); // Force a new request if per_page changed
      }
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (state.lastFetchTime && (new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds)) || lastUpdateTime < state.lastFetchTime) {
        return; // Skip request if less than 30 seconds have passed since the last request
      }
      
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          // Set the request status to true (indicating that the request is in progress)
      commit('SET_IS_FETCHING', true);
          let send_data = { company_id:  company_id, page: page, per_page: per_page };
          const numberPattern = /^[0-9]+$/;

          if (id !== 'all' && numberPattern.test(id)) {
              send_data.category_id = id;
          }

          axios.get(`/services`, { params: { ...send_data } })
            .then(response => {          
                // console.log(response.data, 'EWEWEWE service data.....!!!');
                
                const { data, pagination, status_counts } = response.data;               
              commit('SET_SERVICELIST', { data, pagination, status_counts, id });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },    
  };

  const getters = {
    currentServiceList(state) {
      return { list: state.service_list, status_counts: state.status_counts };
    },   
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
