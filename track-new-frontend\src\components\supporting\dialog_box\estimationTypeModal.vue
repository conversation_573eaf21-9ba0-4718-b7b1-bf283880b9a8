
<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex justify-between items-center relative w-full bg-teal-600 px-4 py-2 rounded rounded-b-none">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">Select Estimation Type
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block mt-4 text-sm bg p-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="product"
                            class="flex justify-center items-center px-5 py-5 border m-3 rounded rounded-lg hover:bg-blue-500 hover:text-white">
                            <input type="radio" id="product" name="salesType" v-model="selectedSalesType" value="product"
                                style="display: none;" @change="handleProductEst">
                            Product Estimation
                        </label>
                    </div>
                    <div>
                        <label for="service"
                            class="flex justify-center items-center px-5 py-5 border m-3 rounded rounded-lg hover:bg-blue-500 hover:text-white">
                            <input type="radio" id="service" name="salesType" v-model="selectedSalesType" value="service"
                                style="display: none;" @change="handleServiceEst">
                            Service Estimation</label>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
  
  
<script>
export default {
    props: {
        showModal: Boolean,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,

        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeEstModal');
            }, 300);
        },
        handleProductEst() {
            this.$router.push({
                name: 'addEstimation',
                params: { type: 'add' },
                query: { type: 'product' }
            });
        },
        handleServiceEst() {
            this.$router.push({
                name: 'addEstimation',
                params: { type: 'add' },
                query: { type: 'service' }
            });
        },
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },

    }
};
</script>
  
<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
        transform: rotate(0deg);
    }
    .rotate-720 {
        transform: rotate(720deg);
    }
</style>
  