<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar"></headbar> -->

            <!-- services home -->
            <div class="relative">
                <!--loader-->
                <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
                    :rows="number_of_rows" :gap="gap" :type="'grid'">
                </skeleton>
                <!-- Display home component by default -->
                <viewServiceData v-if="viewServiceData" :data="viewServiceData" :category_name="category_name"
                    :labelsName="labelsName" :fieldKey="fieldKey" :category_id="category_id" :companyId="companyId"
                    :userId="userId" @updatedData="getUpdatedData" :isCompleted="isCompleted"
                    :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </viewServiceData>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>
<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/services/viewServiceGrid/headbar.vue';
import viewServiceData from '../supporting/services/viewServiceGrid/viewServiceData.vue';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';
export default {
    name: 'view_service_data',
    components: {
        // sidebar,
        // headbar,
        viewServiceData,
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            viewServiceData: null,
            typeofService: null,
            route_item: 3,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            data: [],
            filteredCustomerOptions: [],
            //--api integration---
            companyId: null,
            userId: null,
            pagination: {},
            employee_list: [],
            open_skeleton: true,
            //--skeleton
            number_of_columns: 2,
            number_of_rows: 20,
            gap: 5,
            isCompleted: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'View Service';
        const pageDescription = 'Follow the service journey from start to finish, ensuring every step is tracked and completed efficiently';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),

        getUpdatedData(data) {
            if (data) {
                this.viewServiceData = data;
            }
        },
        handleSkeleton() {
            this.open_skeleton = !this.open_skeleton;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        // Method to parse the URL
        async parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const category = urlParts[urlParts.length - 4];
            const categoryID = urlParts[urlParts.length - 3]
            const id = urlParts[urlParts.length - 1];
            this.category_id = categoryID;
            this.category_name = decodeURIComponent(category);
            // Check if servicecategory_data is defined
            if (this.servicecategory_data.length !== 0) {
                const categoryData = this.servicecategory_data.find(data => data.id === Number(categoryID));
                // console.log(categoryData, 'EEEE');
                if (categoryData) {
                    await this.serviceDataList(id);
                    //--backup data--
                    // console.log(this.data, 'DADADAAD', categoryData.form);

                    if (categoryData.form) {
                        const labelsNameSubset = JSON.parse(categoryData.form).map((opt) => opt.lableName);
                        const fieldKeySubset = JSON.parse(categoryData.form).map((opt) => opt.fieldKey);

                        // Collect values at indices 0 to 3
                        this.labelsName = labelsNameSubset.slice(0, 4);
                        this.fieldKey = fieldKeySubset.slice(0, 4);
                    }
                    this.category_data = categoryData;
                }
            }
        },
        //---service category---
        serviceCategoryList() {
            if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                this.servicecategory_data = this.currentServiceCategory;
                this.parseUrl();
            } else {
                this.fetchServiceCategoryList();
            }
        },
        //---service category---
        serviceDataList(id) {
            axios.get(`/services/${id}`, { params: { company_id: this.companyId, category_id: id } })
                .then(response => {
                    // console.log(response.data, 'Edit data exist datttt..!');
                    let response_data = response.data.data;
                    let exist_data = JSON.parse(response_data.service_data);
                    if (response_data.status == 5 || response_data.status == 6 || response_data.status == 7) {
                        this.isCompleted = true;
                    }
                    if (exist_data.additional && exist_data.additional.length > 0) {
                        let material_list = JSON.parse(response_data.materials);
                        if (material_list && material_list.length > 0) {
                            exist_data.additional = material_list;
                            exist_data.assignWork = response_data.assign_to;
                            if (exist_data.notification) {
                                exist_data.notification = JSON.parse(response_data.notification);
                                // exist_data.notification = [];
                            }
                            // console.log(exist_data.notification, 'What happening in notification data..!');
                            if (response_data.document) {
                                exist_data.document = JSON.parse(response_data.document);
                                // console.log(exist_data.document, 'What happningin document...!');
                            }
                        }
                    }
                    if (this.validateType(exist_data.customer) === 'Number' || response_data.customer) {
                        // console.log(response_data.customer, 'TTTTTTTTTTTTTTTTT')
                        let { first_name, last_name, contact_number } = response_data.customer;
                        exist_data.customer = `${first_name} ${last_name} - ${contact_number}`;
                        exist_data.customer_id = response_data.customer.id;
                    }

                    if (Array.isArray(response_data.assign_to) && response_data.assign_to.length > 0) {
                        // console.log(response_data.assign_to, 'EEEEEEEEEEEE');
                        exist_data.assignWork = response_data.assign_to;
                        // console.log(exist_data.assignWork, 'Assign work data..!!!');
                    }
                    if (typeof exist_data.problem_title === 'string') {
                        const issuesArray = exist_data.problem_title
                            // Remove brackets and split by comma
                            .slice(1, -1)
                            .split(',')
                            // Trim each issue
                            .map(issue => issue.trim());
                        if (Array.isArray(issuesArray) && issuesArray.length !== 0) {
                            exist_data.problem_title = issuesArray;
                        }
                    }
                    if (response_data.service_track && typeof response_data.service_track === 'string') {
                        exist_data.service_track = JSON.parse(response_data.service_track);
                    }
                    // if(response_data.comments){
                    //     exist_data.comments = JSON.parse(response_data.comments);
                    // }
                    if (Array.isArray(response_data.service_expense) && response_data.service_expense.length > 0) {
                        exist_data.service_expense = response_data.service_expense.map(opt => ({ ...opt, value: opt.amount }));
                    }
                    this.viewServiceData = { ...exist_data, id: response_data.id, invoice_id: response_data.invoice_id, sale_id: response_data.sale_id, service_code: response_data.service_code, notification: [], service_id: response_data.service_id };
                    if (this.viewServiceData) {
                        this.open_skeleton = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    this.open_skeleton = false;
                })
        },
        //---get employee list--
        getEmployeeList() {
            axios.get(`/employees`, { params: { company_id: this.companyId, page: 1, per_page: 100 } })
                .then(response => {
                    // console.log(response.data, 'Employee list..!')
                    this.employee_list = response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---validate data type--
        validateType(value) {
            if (!isNaN(value) && !isNaN(parseFloat(value))) {
                return 'Number';
            } else if (typeof value === 'string') {
                return 'String';
            } else {
                return 'Invalid';
            }
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }

    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile(); // Initial check
        this.updateIsEnableBottom(false);
        if (!this.currentServiceCategory || this.currentServiceCategory.length === 0) {
            this.fetchServiceCategoryList();
        }
        this.serviceCategoryList();
        if (this.employee_list.length === 0) {
            this.getEmployeeList();
        }

        // const collectForm = localStorage.getItem('CategoriesForm');
        // if (collectForm) {
        //     let dataParse = JSON.parse(collectForm);
        //     // console.log(dataParse, 'WWWWWWWWWWWW');
        //     this.servicecategory_data = dataParse;
        // }
        // this.parseUrl(); // Call the function to parse the URL
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        this.updateIsEnableBottom(true);
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.servicecategory_data = newValue;
                    this.parseUrl();
                }
            }
        },
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnableBottom(true);
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>
