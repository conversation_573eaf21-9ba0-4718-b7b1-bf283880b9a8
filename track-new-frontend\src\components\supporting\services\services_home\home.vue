<template>
    <div class="mr-2 ml-2">
        <div class="sm:pt-1 pt-[0px] mb-1">
            <p class="font-bold">
                <span @click="goBackToHomePage"
                    class="text-md text-gray-500 text-sm hover:text-black hover:underline cursor-pointer">Services</span>
                /
                <span @click="reloadPage" class="hover:underline cursor-pointer">{{ category_name }} </span>
            </p>
        </div>
        <!--new design header-->
        <div class="mb-1 justify-between">
            <!---status view-->
            <div v-if="getStatusOption && getStatusOption.length > 0" class="text-sm"
                :class="{ 'flex overflow-auto justify-between': isMobile, 'grid': !isMobile, ['grid-cols-' + (getStatusOption.length + 1)]: !isMobile }">
                <div v-for="(opt, index) in getStatusOption " :key="index"
                    class="px-2 cursor-pointer border border-white bg-gray-200 hover:border-blue-700"
                    @click="getStatusLabel(index, 1)"
                    :class="{ 'rounded-tl-full rounded-bl-full': index === 0, 'text-white': status_select === index, 'bg-blue-800': status_select === index }">
                    <div class="p-2 flex justify-between" :class="{ 'w-[150px]': isMobile }">
                        <p>{{ opt.name }}</p>
                        <p>{{ opt.total }}</p>
                    </div>
                </div>
                <div class="px-2 cursor-pointer border border-white bg-gray-200 hover:border-blue-700 rounded-tr-full rounded-br-full"
                    :class="{ 'text-white': status_select === 8, 'bg-blue-900': status_select === 8 }"
                    @click="getStatusLabel(8, 1)">
                    <div class="p-2 flex justify-between" :class="{ 'w-[150px]': isMobile }">
                        <p>All</p>
                        <p>{{ all_count }}</p>
                    </div>
                </div>
            </div>
            <!--Expected completion date-->
            <div class="mt-3 text-xs">
                <div class="flex justify-end">
                    <p v-if="!isMobile" class="mr-1 font-bold flex items-center justify-center">Followup:</p>
                    <p v-else><i class="material-icons px-1 text-gray-400">date_range</i></p>
                    <div class="grid grid-cols-6 gap-0">
                        <button v-for="(option, index) in followupOptions" :key="index"
                            class="bg-blue-500 py-1 text-white hover:text-blue-600 hover:bg-white border border-white"
                            :class="{ 'bg-white text-blue-700': followup_select === option.value, 'rounded-tl-full rounded-bl-full': index === 0, 'rounded-tr-full rounded-br-full': index === 5 }"
                            @click="handleFollowup(option.value, 1)">
                            {{ option.label }}
                        </button>
                    </div>
                </div>
            </div>
            <div class="flex mb-3 justify-between mt-2">
                <div class="flex mr-2">
                    <button @click="addServices" :class="{ 'mr-2': isMobile }"
                        class="bg-green-600 px-3 rounded p-1 text-white hover:bg-green-700 text-sm text-center"><span
                            class="text-center">+</span> <span v-if="!isMobile"
                            class="text-center">Create</span></button>
                    <span v-if="!isMobile" class="mr-2 ml-4 text-lg text-gray-500 p-1 border-l border-gray-400"></span>
                    <button @click="toggleFilter" :class="{ 'mr-2': isMobile }" @blur="hiddenDropdown"
                        class="flex items-center border border-gray-400 px-3 rounded p-1 text-sm">
                        <!-- <img :src="filter_icon" class="w-4 h-4 mr-1" alt="filter_icon" /> -->
                        <i class="material-icons">
                            filter_alt
                        </i>
                        <span v-if="!isMobile">Filter</span></button>
                    <div v-if="showFilterOptions"
                        class="absolute text-xs block bg-white border border-gray-400 p-2 mt-10 ml-0 sm:ml-[100px]  rounded"
                        @mouseover="mouseOverOption" @mouseleave="mouseLeaveOption">
                        <button v-for="(option, index) in filterOptions" :key="index"
                            @click="toggleFilterSelected(option)"
                            class="block w-full text-left px-2 py-1 hover:bg-gray-200">
                            {{ option }}
                        </button>
                    </div>
                    <span v-if="!isMobile" class="mr-2 ml-4 text-xl text-gray-500 p-1 border-l border-gray-400"></span>
                    <button @click="refreshPage" :title01="'Refresh'"
                        class="border rounded px-3 border-gray-400 refresh-button flex items-center">
                        <!-- <img :src="refresh_icon" alt="refresh_icon" class="w-6 h-6" /> -->
                        <i class="material-icons">
                            refresh
                        </i>
                    </button>
                </div>

                <!--Setting-->
                <div class="flex">
                    <!--view design-->
                    <div v-if="!isMobile" class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div class="mr-2 pl-2 pr-2 rounded border bg-gray-100 flex-shrink-0 cursor-pointer hover:border-blue-500"
                            :class="{ 'border-blue-500': items_category === 'tile' }" @click="items_category = 'tile'">
                            <p class="text-[24px] text-cyan-700">&#9638;</p>
                        </div>
                        <div class="ml-2 pl-2 pr-2 rounded border bg-gray-100 flex-shrink-0 cursor-pointer hover:border-blue-500"
                            :class="{ 'border-blue-500': items_category !== 'tile' }" @click="items_category = 'list'">
                            <p class="text-[24px] text-cyan-700">&#9776;</p>
                        </div>
                    </div>

                    <div class="flex items-center">
                        <div v-if="!isMobile" class="info-msg flex justify-center mr-2"
                            :title02="'Enable and disable columns to customize this table.'">
                            <!-- <img :src="info_icon" class="w-5 h-5 ml-2 mr-2 items-center cursor-pointer" /> -->
                            <i class="material-icons">
                                info
                            </i>
                        </div>
                        <div class="flex justify-center items-center border border-gray-400 rounded p-1 px-3">
                            <!-- <img @click="toggleDropdown" :src="setting_icon" class="w-7 h-7 mr-1" /> -->
                            <i @click.stop="toggleDropdown" class="material-icons cursor-pointer">
                                settings
                            </i>
                            <span class="text-xl text-gray-500 ml-1 mr-1">&#10072;</span>
                            <div @click.stop="toggleDropdown" class="cursor-pointer flex justify-center"
                                ref="settingOPtion">
                                <i v-if="!isDropdownOpen" class="material-icons">
                                    expand_more
                                </i>
                                <i v-else class="material-icons">
                                    expand_less
                                </i>
                            </div>
                            <div v-if="isDropdownOpen"
                                class="absolute mt-2 bg-white border border-gray-400 rounded top-[130px] right-[5px]">
                                <div v-for="(column, index) in columns" :key="index" class="flex items-center p-2">
                                    <input type="checkbox" v-model="column.visible"
                                        class="form-checkbox h-5 w-5 text-gray-600 border" />
                                    <span class="text-xs ml-2">{{ column.label }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!---fiter information-->
        <div v-if="Object.keys(filteredBy).length > 0" class="text-xs flex -mb-3 flex-row overflow-auto">
            <p class="text-blue-600 mr-2">Filtered By:</p>
            <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2">
                <p class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                <p>{{ key === 'assign_to' ? value.join(', ') : key === 'type' ? typeList[value] : key === 'status' ?
                    statusList[value] : value }}</p>
            </div>
            <button @click="resetTheFilter" title="reset filter"
                class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
        </div>
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'table'">
        </skeleton>
        <div v-if="!open_skeleton" class="mt-5">
            <!-- <select v-model="recordsPerPage" @change="changePage" id="recordsPerPage"
                class="mt-1 p-2 border border-gray-300 rounded pr-5 mr-3 mb-6">
                <option v-for="option in options" :key="option" :value="option" class="text-xs">{{ option }}</option>
            </select>
            <label for="recordsPerPage">Records per page</label> -->

            <div class="table-container overflow-x-auto">
                <table class="table w-full">
                    <thead>
                        <tr>
                            <!--dynamic-->
                            <th v-for="(column, index) in dynamicFields" :key="index"
                                :class="{ 'hidden': !column.visible }"
                                class="border text-xs py-2 bg-teal-600 text-white  text-xs font-bold">
                                <p>{{ column.label }}</p>
                            </th>
                            <!---by labels name-->
                            <!-- <th v-for="(opt, index) in labelsName" :key="index"
                                class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold leading-none">
                                {{ opt }}
                            </th> -->
                            <!---direct-->
                            <!-- <th
                                class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold leading-none">
                                Name</th>
                            <th
                                class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold leading-none">
                                Phone</th>
                            <th
                                class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold leading-none">
                                E-mail</th>>-->
                            <th class="border text-xs py-2 bg-teal-600 text-white  text-xs font-bold">
                                Onsite / Carry
                            </th>
                            <th
                                class="border px-4 py-2 bg-teal-600 text-white sm:text-[14px] text-xs font-extrabold leading-none">
                                Actions
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr v-for="(record, index) in paginatedData" :key="index">
                            <td class="border py-2 text-sm text-center" v-for="(column, index) in columns" :key="index"
                                :class="{
                                    'hidden': !column.visible, 'w-[110px]': column.field === 'status', 'px-1': column.field !== 'status'
                                }">
                                <span v-if="column.field === 'notification'">
                                    {{ parseNotification(record[column.field]).join(', ') }}
                                </span>
                                <span v-if="column.field === 'status' && record[column.field] !== null" :class="{
                                    'bg-yellow-500 rounded py-2 px-1 font-bold': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[0] ? serviceTrack[0].name : 'order taken'),
                                    'bg-red-500 rounded py-2 px-1 font-bold': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[1] ? serviceTrack[1].name : 'hold'),
                                    'bg-cyan-500 rounded py-2 px-1 font-bold': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[2] ? serviceTrack[2].name : 'in-progress'),
                                    'bg-violet-400 rounded py-2 px-1 font-bold': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[3] ? serviceTrack[3].name : 'new estimate'),
                                    'bg-blue-300 rounded py-2 px-1 font-bold': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[4] ? serviceTrack[4].name : 'to be delivered'),
                                    'bg-blue-500 rounded py-2 px-1 font-bold': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[5] ? serviceTrack[5].name : 'delivered'),
                                    'bg-gray-500 rounded py-2 px-1 font-bold': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[6] ? serviceTrack[6].name : 'cancelled'),
                                    'bg-green-500 rounded py-2 px-1 font-bold': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[7] ? serviceTrack[7].name : 'completed'),
                                }" class="font-bold">
                                    {{ serviceTrack[Number(record[column.field])].name }}
                                </span>
                                <span
                                    v-if="!Array.isArray(record[column.field]) && typeof record[column.field] !== 'object' && column.field !== 'notification' && column.field !== 'notification' && column.field !== 'status' && column.field !== 'invoice_id'">
                                    {{ column.field === 'expected_date' ?
                                        /^\d{4}-\d{2}-\d{2}$/.test(record[column.field].substring(0, 10)) &&
                                            !isNaN(Date.parse(record[column.field].substring(0, 10))) ?
                                            record[column.field].substring(0, 10) : '' : column.field === "materials" ?
                                            typeof record[column.field] === 'string' ?
                                                JSON.parse(record[column.field]).reduce((total, item) => total + (item.qty *
                                                    item.price), 0)
                                                : record[column.field] === 'object' && Array.isArray(record[column.field]) &&
                                                record[column.field].reduce((total, item) => total + (item.qty * item.price), 0) :
                                            record[column.field] }}</span>
                                <span
                                    v-if="Array.isArray(record[column.field]) && record[column.field].length > 0 && column.field !== 'notification' && column.field !== 'status' && column.field !== 'invoice_id'"
                                    v-for="(opt, i) in record[column.field]" :key="i">
                                    <!-- <template v-if="column.field === 'notification'">{{ 'hello' }}</template> -->
                                    <template
                                        v-if="typeof opt === 'object' && opt !== null && column.field !== 'assign_to' && column.field !== 'materials'">
                                        {{ Object.keys(opt).map(key => `${key}: ${opt[key]}`).join(', ') }}
                                    </template>
                                    <template
                                        v-else-if="column.field === 'assign_to' && record[column.field].length > 0">
                                        Name: {{ opt.name }}<br>
                                    </template>
                                    <!-- <template v-else>
                                        {{ opt + 'hello' }}
                                    </template> -->
                                </span>
                                <span class="cursor-pointer hover:underline" style="color: #0000FF;"
                                    v-if="!Array.isArray(record[column.field]) && typeof record[column.field] === 'object' && record[column.field] !== null && column.field !== 'notification' && column.field !== 'status' && column.field !== 'invoice_id'"
                                    @click="viewRecordCustomer(record[column.field])">
                                    {{ record[column.field].first_name + ' ' + record[column.field].last_name +
                                        ' - ' + record[column.field].contact_number }}<br>
                                </span>
                                <span class="cursor-pointer hover:underline" @click="printRecord(record)"
                                    style="color: #0000FF;" v-if="column.field === 'invoice_id'">
                                    {{ record[column.field] }}</span>

                                <!-- <span
                                    v-if="!Array.isArray(JSON.parse(record.service_data)[column.field]) && typeof JSON.parse(record.service_data)[column.field] !== 'object'">
                                    {{ JSON.parse(record.service_data)[column.field] }}</span>
                                <span
                                    v-if="Array.isArray(JSON.parse(record.service_data)[column.field]) && JSON.parse(record.service_data)[column.field].length > 0"
                                    v-for="(opt, i) in JSON.parse(record.service_data)[column.field]" :key="i">
                                    {{ typeof opt === 'object' && opt !== null ? Object.keys(opt).map(key => `${key}:
                                    ${opt[key]}`).join(', ') : opt }}<br>
                                </span>
                                <span
                                    v-if="!Array.isArray(JSON.parse(record.service_data)[column.field]) && typeof JSON.parse(record.service_data)[column.field] === 'object'">
                                    {{ Object.keys(JSON.parse(record.service_data)[column.field]).map(key => `${key}:
                                    ${JSON.parse(record.service_data)[column.field][key]}`).join(', ') }}<br></span> -->
                            </td>
                            <td v-if="record.service_data"
                                class="border py-2 sm:text-[14px] text-xs text-left sm:text-center">
                                <span>{{ JSON.parse(record.service_data) ? JSON.parse(record.service_data).service_type
                                    : '' }}</span>
                            </td>
                            <td class="border py-2 sm:text-[14px] text-xs text-left sm:text-center">
                                <div class="flex justify-center">
                                    <button @click="viewRecord(record)" class="mr-1">
                                        <img :src="table_view" alt="table-view"
                                            class="bg-white hover:bg-gray-100 w-6 h-6" />
                                    </button>
                                    <!-- <button @click="editRecord(JSON.parse(record.service_data))" class="mr-3 mb-2">
                                    <img :src="table_edit" alt="table-view" class="bg-white hover:bg-gray-100 w-6 h-6" />
                                </button> -->
                                    <button @click="confirmDelete(index)">
                                        <img :src="table_del" alt="table-view"
                                            class="bg-white hover:bg-gray-100 w-6 h-6" />
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr v-if="!data || data.length === 0" class="items-center justify-center flex border">
                            <button class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                @click="addServices">+ Add
                                Services</button>
                        </tr>
                    </tbody>
                </table>
            </div>

            <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs" v-if="totalPages">
                <!-- <p class="pagination-info">Page {{ currentPage }} of {{ totalPages }}</p> -->
                <div class="mt-4">
                    <select v-model="recordsPerPage" @change="changePage" id="recordsPerPage"
                        class="mt-1 p-2 border border-gray-300 rounded pr-5 mr-3 mb-6">
                        <option v-for="option in options" :key="option" :value="option" class="text-xs">{{ option }}
                        </option>
                    </select>
                    <label v-if="!isMobile" for="recordsPerPage">Records per page</label>
                </div>
                <div class="flex justify-end w-1/2">
                    <ul class="flex list-none overflow-auto">
                        <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                            :class="{ 'bg-gray-500': pagination.current_page === 1, 'bg-teal-600 hover:bg-teal-500': pagination.current_page !== 1 }">
                            <button @click="updatePage(pagination.current_page - 1)"
                                :disabled="pagination.current_page === 1"
                                class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]"><span
                                    class="mr-2 sm:text-md text-xs">&#8592;</span> Prev</button>
                        </li>
                        <li v-for="pageNumber in visiblePageNumbers()" :key="pageNumber">
                            <button @click="updatePage(pageNumber)"
                                :class="{ 'bg-teal-600 text-white': pageNumber === pagination.current_page }"
                                class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{ pageNumber
                                }}</button>
                        </li>

                        <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                            :class="{ 'bg-gray-500': pagination.current_page === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': pagination.current_page !== pagination.last_page }">
                            <button @click="updatePage(pagination.current_page + 1)"
                                :disabled="pagination.current_page === pagination.last_page"
                                class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs">Next
                                <span class="ml-2 sm:text-md text-xs">&#8594;</span></button>
                        </li>
                    </ul>
                </div>
            </div>
            <!---total records per page-->
            <div class="text-xs -mt-3">
                <p>Total records: <span class="font-bold">{{ pagination.total }}</span></p>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!--filter-->
        <serviceFilter :showModal="lead_filter" @closeFilter="closeLeadFilter" :typeList="typeList"
            :statusList="statusList" :selectedByValue="selectedByValue"></serviceFilter>
        <Loader :showModal="open_loader"></Loader>
    </div>

</template>

<script>
import confirmbox from '../../dialog_box/confirmbox.vue';
import serviceFilter from '../../dialog_box/filter_Modal/serviceFilter.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
export default {
    name: 'services_home',
    emits: ['filter_update', 'paginationGetData', 'updateIsOpen'],
    components: {
        confirmbox,
        serviceFilter,
        dialogAlert
    },
    props: {
        data: Object,
        category_id: String,
        category_type: String,
        category_name: String,
        labelsName: Object,
        fieldKey: Object,
        isMobile: Boolean,
        searchedData: Object,
        category_data: Object,
        pagination: Object,
        companyId: String,
        open_skeleton: Boolean,
        serviceTrack: Object,
        updateModalOpen: Boolean
    },
    data() {
        return {
            service_customer: '/images/service_page/Customer.png',
            service_add_group: '/images/service_page/Add_group.png',
            outline_img: '/images/service_page/Ellipse.png',
            table_view: '/images/service_page/tabler_eye.png',
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            filter_icon: '/images/customer_page/filter.png',
            setting_icon: '/images/customer_page/settings.png',
            info_icon: '/images/customer_page/info.png',
            refresh_icon: '/images/customer_page/refresh.png',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            length_category: null,
            serviceCategories: null,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataList: [],
            columns: [],
            originalData: this.data,
            //--filter
            showFilterOptions: false,
            filterOptions: ['by Date', 'by Customer', 'by Employee', 'by category/Type', 'by Status', 'Custom'],
            selectedByValue: null,
            lead_filter: false,
            typeList: [],
            statusList: [],
            //--dialog alert
            open_message: false,
            message: '',
            //--filter list--
            mouseOverIsNot: false,
            filteredBy: {},
            // serviceTrack: [
            //     { name: 'order taken', date: '', status: false },
            //     { name: 'hold', date: '', status: false },
            //     { name: 'in-progress', date: '', status: false },
            //     { name: 'new estimate', date: '', status: false },
            //     { name: 'to be delivered', date: '', status: false },
            //     { name: 'delivered', date: '', status: false },
            //     { name: 'cancelled', date: '', status: false },
            //     { name: 'completed', date: '', status: false },
            // ],
            //--skeleton
            number_of_columns: 11,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            items_category: 'tile',
            getStatusOption: [],
            status_select: 8,
            followup_select: 'all',
            followupOptions: [
                { label: 'Today', value: 'today' },
                { label: 'Tomorrow', value: 'tomorrow' },
                { label: 'Week', value: 'this_week' },
                { label: 'Month', value: 'this_month' },
                { label: 'Year', value: 'this_year' },
                { label: 'All', value: 'all' }
            ],
            all_count: 0,
        };
    },
    computed: {
        paginatedData() {
            // console.log(this.labelsName, 'IIIIIIIIIII', this.data);
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                // Filter records based on category_type and category_id
                // const filteredData = this.data.filter(record => {
                //     return (
                //         (record.category_type === this.category_type) &&
                //         (record.category_id === Number(this.category_id))
                //     );
                // });
                this.length_category = filteredData.length;

                return this.data;
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                // const totalFilteredRecords = this.data.filter(record => {
                //     return (
                //         (record.category_type === this.category_type) &&
                //         (record.category_id === Number(this.category_id))
                //     );
                // }).length;

                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data.length > 0) {
                // console.log(JSON.parse(this.data[0].service_data), 'Test 0001');
                for (const key in this.data[0]) {
                    // console.log(key, 'What happening...!');
                    if (key !== 'id' && key !== 'comments' && key !== 'service_data' && key !== 'document' && key !== 'created_at' && key !== 'deleted_at' && key !== 'updated_at' && key !== 'servicecategory' && key !== 'service_track' && key !== 'sale_id') { // Exclude the 'id' field
                        const label = formatLabel(key);
                        fields.push({ label, field: key, visible: true });
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },

    },
    methods: {
        updatePage(pageNumber) {
            // console.log('jjjjj', pageNumber, this.pagination.last_page, 'uuuu', pageNumber >= 1 && pageNumber <= this.pagination.last_page);
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
                this.$emit('paginationGetData', pageNumber, this.recordsPerPage);
            }
        },
        viewRecord(record) {
            // Handle view action
            console.log("View", record);
            this.$router.push({ name: 'service-category-view', params: { viewId: record.id } });

        },
        editRecord(record) {
            // Handle edit action
            console.log("Edit", record);
            // this.$emit('showAddServiceComponent', record);
            this.$router.push({ name: 'service-category-edit', params: { editId: record.id } });
        },
        deleteRecord() {
            if (this.deleteIndex !== null) {
                this.open_loader = true;
                // console.log(this.data[this.deleteIndex], 'YYYY');
                axios.delete(`/services/${this.data[this.deleteIndex].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // console.log(response.data);
                        this.deleteIndex = null;
                        this.open_loader = false;
                        this.$emit('paginationGetData', this.pagination.current_page, this.recordsPerPage);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })

            }
            this.open_confirmBox = false;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.open_loader = false;
        },
        addServices() {
            // this.$emit('showAddServiceComponent');
            this.$router.push({ name: 'service-category-add' });
        },
        goBackToHomePage() {
            this.$router.push('/services');
        },
        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;
            if (this.isDropdownOpen) {
                // Add event listener when dropdown is opened
                document.addEventListener('click', this.handleOutsideClick);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },
        handleOutsideClick(event) {
            const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.toggleDropdown();
            }
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        // closeFilterModal() {
        //     this.showFilterModal = false;
        //     this.resetFilter();
        // },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // Implement logic to filter data based on filter values
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //----print prechecklist
        formattedString(listData) {
            let returnData = Object.entries(listData)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ');
            return returnData;
        },
        //---print all arrat data
        formattedData(data) {
            if (Array.isArray(data)) {
                return data.map(item => {
                    if (typeof item === 'object' && item !== null) {
                        return Object.keys(item).map(key => `${key}: ${item[key]}`).join(', ');
                    } else {
                        return item.toString();
                    }
                });
            } else {
                return data.toString();
            }
        },
        //------Open filter---
        //---Filter---
        toggleFilter() {
            // console.log('hello');
            this.showFilterOptions = !this.showFilterOptions;
            this.typeList = this.category_data.form.find((opt) => opt.fieldKey === 'service_type').option;
            this.statusList = this.category_data.form.find((opt) => opt.fieldKey === 'status').option;
            // this.lead_filter = true;

            // this.filteredDataList = this.data;
            // console.log(this.filteredDataList, 'EEERRRASR');
        },
        //---filter---
        toggleFilterSelected(option) {
            // console.log(option, 'GGGGGGGGGGGGGGG');
            this.selectedByValue = option;
            this.lead_filter = true;
            this.$emit('filter_update', 'reset');
            this.showFilterOptions = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //--close filter
        closeLeadFilter(searchData) {
            // console.log(searchData, 'What is happening.....!');
            if (searchData) {
                const keysData = Object.keys(searchData);
                // console.log(this.data, 'RRRRRRRRRRRRRRR');
                let filterTheData = this.data.filter(option => {
                    // Check if all search criteria match
                    return keysData.every(key => {
                        if (key === 'date') {
                            return option.lead_date === searchData.date;
                        }
                        if (key === 'customer') {
                            return option.customer === searchData.customer;
                        }
                        if (key === 'assign_to') {
                            // return option.assign_to === searchData.assign_to;
                            return option.assignWork.some(assignee => searchData.assign_to.includes(assignee));
                        }
                        if (key === 'type') {
                            return option.service_type === this.typeList[searchData.type];
                        }
                        if (key === 'status') {
                            // console.log(option.status, 'pppppppppp', searchData.status);
                            return option.status === this.statusList[searchData.status];
                        }
                        return true; // For other keys, consider it as matched
                    });
                });
                // console.log(filterTheData, 'EEEEEEEEEEEEEEEEEEE');
                if (filterTheData.length > 0) {
                    this.filteredBy = searchData;
                    // this.data = filterTheData;
                    this.$emit('filter_update', 'filter', filterTheData);
                } else {
                    filterTheData = this.data.filter(option => {
                        return (searchData.date && option.service_date === searchData.date) ||
                            (searchData.customer && option.customer === searchData.customer) ||
                            (searchData.assign_to && option.assignWork.some(assignee => searchData.assign_to.includes(assignee))) ||
                            (searchData.type && option.service_type === this.typeList[searchData.type]) ||
                            (searchData.status && option.status === this.statusList[searchData.status]);
                    });
                    if (filterTheData.length > 0) {
                        this.filteredBy = searchData;
                        // this.data = filterTheData;
                        this.$emit('filter_update', 'filter', filterTheData);
                    } else {
                        this.message = 'The filter does not match any records..!';
                        this.open_message = true;
                        // this.data = this.originalData;
                        // this.$emit('filter_update', 'reset');
                    }
                }

                // console.log(this.data, 'Final data after filtering:', filterTheData);
            }
            this.lead_filter = false;
        },
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //--hidden dropdown--
        hiddenDropdown() {
            console.log();
            if (!this.mouseOverIsNot) {
                this.showFilterOptions = false;
            }
        },
        mouseOverOption() {
            this.mouseOverIsNot = true;
        },
        mouseLeaveOption() {
            this.mouseOverIsNot = false;
        },
        //---reset filter--
        resetTheFilter() {
            this.$emit('filter_update', 'reset');
            this.filteredBy = {};
        },
        //--change page--
        changePage() {
            // console.log(this.pagination.current_page, 'ppppp', this.recordsPerPage);
            this.$emit('paginationGetData', 1, this.recordsPerPage);
        },
        //---handle the data---
        parseNotification(notification) {
            try {
                // Attempt to parse as JSON
                const parsedNotification = JSON.parse(notification.replace(/\\"/g, '"'));
                if (Array.isArray(parsedNotification)) {
                    // Remove square brackets from each item in the array, if present
                    return parsedNotification.map(item => typeof item === 'string' ? item.replace(/^\[|\]$/g, '') : item);
                } else {
                    // Wrap non-array value in an array and remove square brackets if present
                    return [parsedNotification.replace(/^\[|\]$/g, '')];
                }
            } catch (error) {
                // Parsing as JSON failed, handle the case where notification is a string
                return notification.split(',').map(item => item.trim());
            }
        },

        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available
            // console.log(Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index), 'RRRRR');
            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        //---print record---
        printRecord(record) {
            this.$router.push({ name: 'print-preview', query: { type: 'sales_home', invoice_no: record.sale_id } });
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        //---close all modal--
        closeAllModal() {

        }
    },
    watch: {
        category_data: {
            deep: true,
            handler(newValue) {
                console.log(newValue, 'EEEEEE');
                if (newValue && newValue.form && Array.isArray(JSON.parse(newValue.form))) {
                    let findStatus = JSON.parse(newValue.form).find((opt) => opt.fieldKey === 'status');
                    if (findStatus && findStatus.option && Array.isArray(findStatus.option)) {
                        this.getStatusOption = findStatus.option.map((opt, index) => ({
                            name: opt,
                            status: findStatus.option_status ? findStatus.option_status[index] : true,
                            total: 0
                        }));
                        console.log(this.getStatusOption, 'What is happening.......');
                    }
                }
            }
        },
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModal();
            }
        }
    }
}
</script>

<style scoped>
.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
    }
}
</style>
