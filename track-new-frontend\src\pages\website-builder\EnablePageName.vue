<template>
    <div class="p-2 bg-white rounded shadow-md max-w-xl mx-auto border border-gray-200 mb-2 text-sm">
        <!-- Wrapper for Flex Layout -->
        <div class="flex flex-col sm:flex-row sm:space-x-4 space-y-4 sm:space-y-0 justify-center items-center">
            <!-- Page Name Input -->
            <div class="w-full sm:w-1/2">
                <label for="pageName" class="block mb-2 font-bold">Custom Page Name</label>
                <input id="pageName" type="text" v-model="is_onSetting.name" placeholder="Enter the name of the page"
                    class="border border-gray-300 p-2 rounded w-full" />
            </div>
            <!-- Enable Page Toggle -->
            <div class="w-full sm:w-1/2">
                <label for="enablePage" class="block mb-2 font-bold">Enable Page</label>
                <label class="inline-flex items-center cursor-pointer">
                    <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                        @click="handleToggle"
                        :class="{ 'bg-blue-600': is_onSetting.is_on, 'bg-gray-200': !is_onSetting.is_on }">
                        <div class="absolute -top-0.5 left-0 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                            :class="{ 'translate-x-6': is_onSetting.is_on, 'translate-x-0': !is_onSetting.is_on }">
                        </div>
                    </div>
                    <p class="px-2">{{ is_onSetting.is_on ? "Enabled" : "Disabled" }}</p>
                </label>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        pages: { type: Object, required: true },
    },
    data() {
        return {
            is_onSetting: { is_on: true, name: 'Page' },
        };
    },
    mounted() {
        if (this.pages) {
            this.is_onSetting = this.pages;
        }
    },
    methods: {
        //--handle switch--
        handleToggle() {
            // Perform any additional actions when toggled
            this.is_onSetting.is_on = !this.is_onSetting.is_on;
        },
    },
    watch: {
        is_onSetting: {
            deep: true,
            handler(newValue) {
                this.$emit('updatePagesEnabled', newValue);
            }
        }
    }
}
</script>