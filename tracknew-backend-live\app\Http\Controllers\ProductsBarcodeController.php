<?php

namespace App\Http\Controllers;

use App\DataTables\ProductsBarcodeDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateProductsBarcodeRequest;
use App\Http\Requests\UpdateProductsBarcodeRequest;
use App\Repositories\ProductsBarcodeRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class ProductsBarcodeController extends AppBaseController
{
    /** @var ProductsBarcodeRepository $productsBarcodeRepository*/
    private $productsBarcodeRepository;

    public function __construct(ProductsBarcodeRepository $productsBarcodeRepo)
    {
        $this->productsBarcodeRepository = $productsBarcodeRepo;
    }

    /**
     * Display a listing of the ProductsBarcode.
     *
     * @param ProductsBarcodeDataTable $productsBarcodeDataTable
     *
     * @return Response
     */
    public function index(ProductsBarcodeDataTable $productsBarcodeDataTable)
    {
        return $productsBarcodeDataTable->render('products_barcodes.index');
    }

    /**
     * Show the form for creating a new ProductsBarcode.
     *
     * @return Response
     */
    public function create()
    {
        return view('products_barcodes.create');
    }

    /**
     * Store a newly created ProductsBarcode in storage.
     *
     * @param CreateProductsBarcodeRequest $request
     *
     * @return Response
     */
    public function store(CreateProductsBarcodeRequest $request)
    {
        $input = $request->all();

        $productsBarcode = $this->productsBarcodeRepository->create($input);

        Flash::success('Products Barcode saved successfully.');

        return redirect(route('productsBarcodes.index'));
    }

    /**
     * Display the specified ProductsBarcode.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $productsBarcode = $this->productsBarcodeRepository->find($id);

        if (empty($productsBarcode)) {
            Flash::error('Products Barcode not found');

            return redirect(route('productsBarcodes.index'));
        }

        return view('products_barcodes.show')->with('productsBarcode', $productsBarcode);
    }

    /**
     * Show the form for editing the specified ProductsBarcode.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $productsBarcode = $this->productsBarcodeRepository->find($id);

        if (empty($productsBarcode)) {
            Flash::error('Products Barcode not found');

            return redirect(route('productsBarcodes.index'));
        }

        return view('products_barcodes.edit')->with('productsBarcode', $productsBarcode);
    }

    /**
     * Update the specified ProductsBarcode in storage.
     *
     * @param int $id
     * @param UpdateProductsBarcodeRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateProductsBarcodeRequest $request)
    {
        $productsBarcode = $this->productsBarcodeRepository->find($id);

        if (empty($productsBarcode)) {
            Flash::error('Products Barcode not found');

            return redirect(route('productsBarcodes.index'));
        }

        $productsBarcode = $this->productsBarcodeRepository->update($request->all(), $id);

        Flash::success('Products Barcode updated successfully.');

        return redirect(route('productsBarcodes.index'));
    }

    /**
     * Remove the specified ProductsBarcode from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $productsBarcode = $this->productsBarcodeRepository->find($id);

        if (empty($productsBarcode)) {
            Flash::error('Products Barcode not found');

            return redirect(route('productsBarcodes.index'));
        }

        $this->productsBarcodeRepository->delete($id);

        Flash::success('Products Barcode deleted successfully.');

        return redirect(route('productsBarcodes.index'));
    }
}
