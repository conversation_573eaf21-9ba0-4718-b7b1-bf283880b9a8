<template>
    <div class="relative">
        <!-- Website Intro -->
        <!-- <label class="block font-bold mb-2">{{ label }}</label> -->
        <!-- <textarea v-model="websiteData.intro" class="border p-2 rounded w-full"
            placeholder="Enter your company intro here"></textarea> -->
        <ckeditor v-model="editorData" :editor="editor" :config="editorConfig" tag-name="textarea"
            @ready="initializeEditor" />
    </div>
</template>
<script>
import { ClassicEditor, Alignment, Autoformat, Bold, Italic, Underline, Strikethrough, Essentials, Paragraph, Undo, Heading, Link, List, TodoList, BlockQuote, Code, CodeBlock, Highlight, FontColor, FontSize, FontBackgroundColor, Image, ImageStyle, ImageCaption, ImageUpload, ImageResize, ImageToolbar, Table, TableToolbar, TableCellProperties, TableProperties, MediaEmbed, PasteFromOffice, SpecialCharacters, SpecialCharactersArrows, SpecialCharactersCurrency, SpecialCharactersMathematical, SpecialCharactersText, FindAndReplace, HorizontalLine, PageBreak, WordCount, Mention, Clipboard, DragDrop, RemoveFormat, SourceEditing } from 'ckeditor5';

import { Ckeditor } from '@ckeditor/ckeditor5-vue';
import 'ckeditor5/ckeditor5.css';
import imageService from '@/services/imageService';

//--custom uplaod image---
// Custom Upload Adapter
class CustomUploadAdapter {
    constructor(loader, companyId) {
        this.loader = loader; // CKEditor's file loader instance
        this.companyId = companyId; // Company ID for upload
    }

    async upload() {
        this.isUploading = true; // Set uploading flag
        try {
            const file = await this.loader.file;
            const compressedFile = await imageService.compressImage(file);
            const response = await imageService.uploadImage(compressedFile, "editor", this.companyId);

            return {
                default: response.media_url,
            };
        } catch (error) {
            console.error("Error uploading image:", error);
            throw error;
        }
    }
    abort() { }
}

// Plugin to integrate the custom upload adapter
function CustomUploadAdapterPlugin(editor) {
    editor.plugins.get("FileRepository").createUploadAdapter = (loader) => {
        return new CustomUploadAdapter(loader, editor.config.get("companyId"));
    };
}

//---
export default {
    props: {
        label: {
            type: String,
            required: true,
        },
        textData: {
            type: String,
            required: true,
        },
        company_id: {
            type: String,
            required: true
        },
    },
    components: {
        Ckeditor
    },

    data() {
        return {
            editor_data: '',
            editor: ClassicEditor,
            editorData: '',
            editorConfig: {
                plugins: [Essentials, Autoformat, Bold, Italic, Underline, Strikethrough, Paragraph, Heading, Link, List, TodoList, BlockQuote, Code, CodeBlock, Highlight, FontColor, FontSize, FontBackgroundColor, Image, ImageStyle, ImageCaption, ImageUpload, ImageResize, ImageToolbar, Table, TableToolbar, TableCellProperties, TableProperties, MediaEmbed, PasteFromOffice, SpecialCharacters, SpecialCharactersArrows, SpecialCharactersCurrency, SpecialCharactersMathematical, SpecialCharactersText, FindAndReplace, HorizontalLine, PageBreak, WordCount, Mention, Clipboard, DragDrop, RemoveFormat, Alignment, SourceEditing],
                toolbar: [
                    'heading', '|',
                    'bold', 'italic', 'underline', 'strikethrough', '|',
                    'fontColor', 'fontBackgroundColor', 'fontSize', '|',
                    'link', 'bulletedList', 'numberedList', '|',
                    'insertTable', 'imageUpload', "toggleImageCaption", 'mediaEmbed', '|',
                    'code', 'codeBlock', 'highlight', '|',
                    'alignment', 'blockQuote', 'horizontalLine', 'pageBreak', '|',
                    'findAndReplace', 'specialCharacters', '|',
                    'undo', 'redo', 'sourceEditing'
                ],
                image: {
                    styles: ["full", "alignLeft", "alignCenter", "alignRight", "side"], // Add valid styles
                    toolbar: [
                        // "imageStyle:full", // Ensure these match the styles defined above
                        "imageStyle:alignLeft",
                        "imageStyle:alignCenter",
                        "imageStyle:alignRight",
                        // "imageStyle:side",
                        "|",
                        "toggleImageCaption",
                        "imageTextAlternative",
                    ],
                },
                table: {
                    contentToolbar: [
                        'tableColumn', 'tableRow', 'mergeTableCells'
                    ]
                },
                extraPlugins: [CustomUploadAdapterPlugin], // Add custom upload adapter
                companyId: this.company_id, // Pass companyId to the plugin
                removedImages: [],
                isUploading: false,
            }
        };
    },
    methods: {
        base64Encode(data) {
            try {
                // Handle Unicode characters using TextEncoder
                const utf8Array = new TextEncoder().encode(data); // Convert string to UTF-8 byte array
                const binaryString = Array.from(utf8Array, (byte) => String.fromCharCode(byte)).join(""); // Convert byte array to binary string
                return btoa(binaryString); // Encode binary string to Base64
            } catch (error) {
                console.error("Error encoding Base64 data:", error);
                return ""; // Return empty string on error
            }
        },
        base64Decode(encodedData) {
            try {
                const binaryString = atob(encodedData); // Decode Base64 to binary string
                const utf8Array = Uint8Array.from(binaryString, (char) => char.charCodeAt(0)); // Convert binary string to byte array
                return new TextDecoder().decode(utf8Array); // Decode byte array to Unicode string
            } catch (error) {
                console.error("Error decoding Base64 data:", error);
                return ""; // Return empty string on error
            }
        },
        isValidBase64(str) {
            try {
                // Check if the string matches Base64 format
                return !!str.match(/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/);
            } catch (error) {
                console.error("Error validating Base64 string:", error);
                return false;
            }
        },
        //--image upload--
        initializeEditor(editorInstance) {
            this.editorInstance = editorInstance;
            this.previousImages = this.extractImageUrls(editorInstance.getData()); // Initialize image URLs
            this.trackImageDeletions();
        },
        extractImageUrls(content) {
            // Extract all image URLs from the given content
            const tempDiv = document.createElement("div");
            tempDiv.innerHTML = content;
            return Array.from(tempDiv.querySelectorAll("img")).map((img) => img.src);
        },
        async deleteImage(imageUrl) {
            try {
                await imageService.deleteImage(imageUrl, "editor"); // Call your delete service
                console.log(`Image deleted: ${imageUrl}`);
            } catch (error) {
                console.error(`Failed to delete image: ${imageUrl}`, error);
            }
        },
        trackImageDeletions() {
            if (!this.editorInstance) return;

            this.editorInstance.model.document.on("change:data", () => {
                if (this.isUploading) return; // Skip during upload
                const currentContent = this.editorInstance.getData();
                const currentImages = this.extractImageUrls(currentContent); // Get current image URLs

                // Identify removed images
                const removedImages = this.previousImages.filter(
                    (src) => !currentImages.includes(src)
                );

                // Send delete request for each removed image
                removedImages.forEach((url) => {
                    this.deleteImage(url);
                });

                // Update the previousImages array
                this.previousImages = currentImages;
            });
        },
    },
    mounted() {
        if (this.textData && this.textData !== "") {
            if (this.isValidBase64(this.textData)) {
                try {
                    const decodedData = this.base64Decode(this.textData); // Decode Base64
                    this.editorData = decodedData; // Update editor data
                } catch (error) {
                    console.error("Error decoding Base64 during mount:", error);
                    this.editorData = `<p>${this.textData}</p>`; // Fallback to wrapping in <p>
                }
            } else {
                this.editorData = `<p>${this.textData}</p>`; // Wrap plain text in <p> for CKEditor
            }
        }
        // Dynamically bind the custom upload adapter plugin to the editor
        this.editorConfig.uploadImageFunction = this.uploadImage;
    },
    watch: {
        textData: {
            immediate: true,
            handler(newValue) {
                if (newValue) {
                    if (this.isValidBase64(newValue)) {
                        try {
                            const decodedData = this.base64Decode(newValue); // Decode Base64
                            this.editorData = decodedData;
                        } catch (error) {
                            console.error("Error decoding Base64 data:", error);
                            this.editorData = `<p>${newValue}</p>`; // Wrap invalid Base64 in paragraph
                        }
                    } else {
                        // console.log("Provided textData is not Base64 encoded. Wrapping in <p> tags.");
                        this.editorData = `<p>${newValue}</p>`; // Wrap non-Base64 in paragraph
                    }
                } else {
                    this.editorData = ""; // Reset if no value is provided
                }
            },
        },
        editorData: {
            deep: true,
            handler(newValue) {
                const encodedDataValue = this.base64Encode(newValue); // Use the reusable encode function
                this.$emit("editorSubmit", encodedDataValue); // Emit encoded data
            },
        },
    },
};
</script>
<style>
/* Headings */
.ck-content h1 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 1rem;
    color: #2c3e50;
    /* Replace with your desired color */
}

.ck-content h2 {
    font-size: 1.75rem;
    font-weight: bold;
    margin-bottom: 0.75rem;
    color: #34495e;
}

.ck-content h3 {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #7f8c8d;
}

.ck-content h4 {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #7f8c8d;
}

.ck-content h5 {
    font-size: 1rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #7f8c8d;
}

.ck-content h6 {
    font-size: 0.875rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
    color: #7f8c8d;
}

/* Bold */
.ck-content b,
strong {
    font-weight: bold;
    color: #333;
    /* Replace with your desired color */
}

.ck-content img {
    object-fit: contain;
    /* Maintain aspect ratio */
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1rem 0;
}

/* List styles */
.ck-content ul,
.ck-content ol {
    margin: 0;
    padding-left: 1.5rem;
    /* Adjust padding for proper indentation */
}

.ck-content li {
    margin-bottom: 0.5rem;
    /* Add spacing between list items */
}

/* Styling for ordered lists (numbers) */
.ck-content ol {
    list-style-type: decimal;
    /* Show numbers in ordered lists */
}

/* Styling for unordered lists (bullets) */
.ck-content ul {
    list-style-type: disc;
    /* Show bullets in unordered lists */
}

/* For nested lists (sub-lists) */
.ck-content ul ul,
.ck-content ol ol {
    margin-left: 1.5rem;
    /* Indent nested lists */
}
</style>