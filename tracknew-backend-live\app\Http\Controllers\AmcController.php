<?php

namespace App\Http\Controllers;

use App\DataTables\AmcDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateAmcRequest;
use App\Http\Requests\UpdateAmcRequest;
use App\Repositories\AmcRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class AmcController extends AppBaseController
{
    /** @var AmcRepository $amcRepository*/
    private $amcRepository;

    public function __construct(AmcRepository $amcRepo)
    {
        $this->amcRepository = $amcRepo;
    }

    /**
     * Display a listing of the Amc.
     *
     * @param AmcDataTable $amcDataTable
     *
     * @return Response
     */
    public function index(AmcDataTable $amcDataTable)
    {
        return $amcDataTable->render('amcs.index');
    }

    /**
     * Show the form for creating a new Amc.
     *
     * @return Response
     */
    public function create()
    {
        return view('amcs.create');
    }

    /**
     * Store a newly created Amc in storage.
     *
     * @param CreateAmcRequest $request
     *
     * @return Response
     */
    public function store(CreateAmcRequest $request)
    {
        $input = $request->all();

        $amc = $this->amcRepository->create($input);

        Flash::success('Amc saved successfully.');

        return redirect(route('amcs.index'));
    }

    /**
     * Display the specified Amc.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $amc = $this->amcRepository->find($id);

        if (empty($amc)) {
            Flash::error('Amc not found');

            return redirect(route('amcs.index'));
        }

        return view('amcs.show')->with('amc', $amc);
    }

    /**
     * Show the form for editing the specified Amc.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $amc = $this->amcRepository->find($id);

        if (empty($amc)) {
            Flash::error('Amc not found');

            return redirect(route('amcs.index'));
        }

        return view('amcs.edit')->with('amc', $amc);
    }

    /**
     * Update the specified Amc in storage.
     *
     * @param int $id
     * @param UpdateAmcRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateAmcRequest $request)
    {
        $amc = $this->amcRepository->find($id);

        if (empty($amc)) {
            Flash::error('Amc not found');

            return redirect(route('amcs.index'));
        }

        $amc = $this->amcRepository->update($request->all(), $id);

        Flash::success('Amc updated successfully.');

        return redirect(route('amcs.index'));
    }

    /**
     * Remove the specified Amc from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $amc = $this->amcRepository->find($id);

        if (empty($amc)) {
            Flash::error('Amc not found');

            return redirect(route('amcs.index'));
        }

        $this->amcRepository->delete($id);

        Flash::success('Amc deleted successfully.');

        return redirect(route('amcs.index'));
    }
}
