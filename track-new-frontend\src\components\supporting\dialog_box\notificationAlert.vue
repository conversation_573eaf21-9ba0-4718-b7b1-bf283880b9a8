<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="w-full max-w-md p-4 transform transition-transform-custom ease-in-out duration-300 rounded rouded-full"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full': !isOpen }">
            <div
                class="bg-white rounded-lg overflow-hidden shadow-xl max-w-md p-6 transform transition-transform-custom ease-in-out duration-300">
                <div class="flex justify-center items-center mb-6">
                    <font-awesome-icon icon="fa-solid fa-comment-sms" class="text-5xl text-blue-500" />
                </div>
                <div class="mb-8">
                    <h2 class="text-3xl font-semibold text-center text-gray-800">Share Notification SMS?</h2>
                </div>
                <div class="flex justify-center">
                    <button @click="confirm"
                        class="bg-green-600 text-white font-semibold px-6 py-3 mr-4 rounded-md hover:bg-green-500">Share</button>
                    <button @click="cancel"
                        class="bg-red-600 text-white font-semibold px-6 py-3 rounded-md hover:bg-red-500">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'confirm_dialog',
    props: {
        showModal: Boolean,
    },
    emits: ['onConfirm', 'onCancel'],
    data() {
        return {
            'overlay-active': this.showModal,
            isOpen: false,
        }
    },
    methods: {
        confirm() {
            this.$emit('onConfirm');
        },
        cancel() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('onCancel');
            }, 300);
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },

    }
};
</script>

<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(8px);
    z-index: 49;
    transition: opacity 0.3s ease;
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        width: 90%;
        max-width: 320px;
    }

    .modal-content {
        padding: 1rem;
    }
}
</style>
