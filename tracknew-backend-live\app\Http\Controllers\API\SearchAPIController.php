<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\AppBaseController;
use App\Models\Products;
use App\Models\ProductsBarcode;
use App\Models\Leads;
use App\Models\LeadsFollows;
use App\Http\Resources\api\LeadResource;
use App\Http\Resources\api\ServiceDataResource;
use App\Http\Resources\api\AmcResource;
use App\Http\Resources\api\SaleResource;
use App\Http\Resources\api\PurchaseResource;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Validation\ValidationException;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use DateTime;


class SearchAPIController extends AppBaseController
{
  
  	public function __construct()
    {   
       // $this->middleware('doNotCacheResponse', ['only' => ['searchResults']]);     
    }
    public function index()
    {
        $roles = Role::all();
        return RoleResource::collection($roles)->response()->setStatusCode(200);
    }

    public function searchResults(Request $request)
    {
        $items = array();
        $value = $request->q;
        $filter = $request->filter;
       
      
        switch ($request->type) {
            case 'products':

                // var_dump("welcome");
                // exit();
                // $items = \App\Models\Permission::select('id', 'name as text')->whereNull('parent_id');
                // if ($value != '') {
                //     $items->where('name', 'LIKE', $value . '%');
                // }
                // $items = $items->get();

                $items = Products::select('products.id', 'products.product_name as text')
                ->leftJoin('products_barcode', 'products.id', '=', 'products_barcode.product_id')
                ->where(function($query) use ($value) {
                    $query->where('products.product_name', 'LIKE', '%' . $value . '%')
                        ->orWhere('products_barcode.barcode', 'LIKE', '%' . $value . '%');
                })
                ->get();

                break;
                
            case 'leads':                
                $perPage = $request->query('per_page', 10);                 
                $page = $request->query('page', 1);                
                $customer_id = $request->query('customer_id');                
                $employer_id = $request->query('employer_id');                
                $lead_type = $request->query('category');                
                $created_at  = $request->query('created_at');            	
            	$from_date = $request->query('from_date');
                $to_date = $request->query('to_date');
                $status = $request->query('status');
          
                if(Auth::check()) {
                    
                    $user = Auth::user();
                    
                    $companyId = $user->company_id;
                    
                    //$isAdmin = $user->hasRole('admin');
                  	$isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();
                
           
                    $leadsQuery = \App\Models\leads::where('company_id', $companyId);
                  	if (!$isAdmin) {
                        $leadsQuery->where('assign_to', 'like', '%' . $user->id . '%');
                    }
                    
                    if($value !== '' && isset($value)){
                        
                        if ($value === 'pending') {
                            $leadsQuery->whereIn('leadstatus_id', ['0', '1', '4']);
                        } else {
                            $leadsQuery->where('leadstatus_id', $value);
                        }
                      
                      // $leadsQuery->where('leadstatus_id', $value); 
                    }
                    else{
                      $leadsQuery->whereIn('leadstatus_id', ['0','1','2','3','4']);   
                    }
                    
                  
                    
                    // if ($from_to && strpos($from_to, '  ') !== false) {
                    //     list($fromDate, $toDate) = array_map('trim', explode('  ', $from_to));
                    
                    //     // Ensure valid date formats
                    //     $fromDate = date('Y-m-d', strtotime($fromDate));
                    //     $toDate = date('Y-m-d', strtotime($toDate));
                    // }
                    
                  
                    // if (!$isAdmin) {
                    //     $leadsQuery->where('assign_to', 'like', '%' . $user->id . '%');
                    // }
                    
                    if(isset($customer_id) && $customer_id !== ''){
                        
                        $leadsQuery->where('customer_id', $customer_id);
                    }
                    
                    if(isset($employer_id) && $employer_id !== ''){
                          
                        
                        $leadsQuery->where('assign_to', 'like', '%' . $employer_id . '%');
                        
                    }
                    if(isset($lead_type) && $lead_type !== ''){
                         
                        $leadsQuery->where('leadtype_id', $lead_type);
                        
                    }
                    if(isset($created_at) && $created_at !== ''){
                       
                        $leadsQuery->whereDate('created_at', $created_at);
                        
                    }
                  
                  	if ($from_date) {
                        $leadsQuery->whereDate('updated_at', '>=', $from_date);
                    }

                    if ($to_date) {
                        $leadsQuery->whereDate('updated_at', '<=', $to_date);
                    }
                    // if(isset($updated_at) && $updated_at !== ''){
                        
                    //     $leadsQuery->whereDate('updated_at', $updated_at);
                        
                    // }
                    
                    // if ($fromDate && $toDate) {
                    //     $leadsQuery->whereBetween('lead_date', [$fromDate, $toDate]);
                    // }
                    
                    $filter = $request->query('filter', 'all');
                
                    if ($filter !== 'all' && $filter !== '' && isset($filter)) {
                          
                        $leadsQuery->whereHas('leadFollows', function ($query) use ($filter) {
                            switch ($filter) {
                                case 'today':
                                    $query->whereDate('date_and_time', '<=', now()->toDateString());
                                    break;
                                case 'tomorrow':
                                    $query->whereDate('date_and_time', now()->addDay()->toDateString());
                                    break;
                                case 'this_week':
                                    $query->whereBetween('date_and_time', [now()->startOfWeek(), now()->endOfWeek()]);
                                    break;
                                case 'this_month':
                                    $query->whereMonth('date_and_time', now()->month);
                                    break;
                                case 'this_year':
                                    $query->whereYear('date_and_time', now()->year);
                                    break;
                                default:
                                    // Handle other filters or invalid filter values
                                    break;
                            }
                        });
                        
                       // $leadsQuery->whereIn('leadstatus_id', ['0', '1']);
                    }
    
                      
                    // If perPage is set to 'all', get all leads without pagination
                    if ($perPage === 'all') {
                        $perPage = $leadsQuery->count();
                    }
                     
                        $leads = $leadsQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
                    
                    
                    
                
                    return response()->json([
                        'success' => true,
                        'data' => LeadResource::collection($leads), // Get the paginated items
                        'pagination' => [
                            'total' => $leads->total(),
                            'per_page' => $leads->perPage(),
                            'current_page' => $leads->currentPage(),
                            'last_page' => $leads->lastPage(),
                            'from' => $leads->firstItem(),
                            'to' => $leads->lastItem(),
                        ],
                    ]);
                } 
                else {
                    return response()->json(['error' => 'Please login to access.'], 401);
                }
                break;
            case 'services':
                
                $perPage = $request->query('per_page', 10); 
                
                $page = $request->query('page', 1);
                
                $customer_id = $request->query('customer_id');
                
                $employer_id = $request->query('employer_id');
                
                $category = $request->query('category');
                
                $created_at  = $request->query('created_at');
                
                $filter = $request->query('filter', 'all');
            	
            	$from_date = $request->query('from_date'); 
            
                $to_date = $request->query('to_date'); 
                
                if (Auth::check()) {
                    
                    $user = Auth::user();
                    
                     
                    $companyId = $user->company_id;
        
                    // $isAdmin = $user->roles()->exists() && $user->roles()->where('name', 'admin')->exists();
                    $isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();

                    $cacheKeyBase = "search_services_{$companyId}_{$perPage}_{$page}";

                    if ($category !== null) {
                        $cacheKeyBase .= "_{$category}";
                    }
                    if ($customer_id !== null) {
                        $cacheKeyBase .= "_customer{$customer_id}";
                    }
                    if ($employer_id !== null) {
                        $cacheKeyBase .= "_employer{$employer_id}";
                    }
                    if ($created_at !== null) {
                        $cacheKeyBase .= "_created{$created_at}";
                    } 
                    if ($filter !== 'all' && $filter !== '') {
                        $cacheKeyBase .= "_filter_{$filter}";
                    }
                    
                    if($value !== null){
                        $cacheKeyBase .= "_value{$value}";
                    }
                    
                   
    
                    // $services = Cache::remember($cacheKeyBase, now()->addMinutes(10), function () use ($companyId, $category, $perPage, $page, $isAdmin, $employer_id, $customer_id, $filter, $created_at, $user, $value) {

                    $servicesQuery = \App\Models\Services::where('company_id', $companyId);
        
                    if (!$isAdmin) {
                      //  var_dump('sdmin');
                        $servicesQuery->whereHas('users', function ($query) use ($user) {
                            $query->where('users.id', $user->id);
                        });
                    }
                    
                    
                     if(isset($employer_id) && $employer_id !== ''){
                       //   var_dump('emp');
                         $servicesQuery->whereHas('users', function ($query) use ($employer_id) {
                             $query->where('users.id', $employer_id);
                         });
                        
                       // $leadsQuery->where('assign_to', 'like', '%' . $employer_id . '%');
                        
                    }                 
                   
        
                    if (isset($category) && $category !== '') {
                        // var_dump('cat');
                        $servicesQuery->where('servicecategory_id', $category);
                    }
                    
                    if(isset($customer_id) && $customer_id !== ''){
                       //  var_dump('cus');
                        $servicesQuery->where('customer_id', $customer_id);
                    }       
                  
                   if ($from_date !== null) {
                        $servicesQuery->whereDate('updated_at', '>=', $from_date);
                    }

                    if ($to_date !== null) {
                        $servicesQuery->whereDate('updated_at', '<=', $to_date);
                    }
                    
                    
                    
                
                    if ($filter !== 'all' && $filter !== '' && isset($filter)) {    
                         //var_dump('filter');
                        switch ($filter) {
                            case 'today':
                                $servicesQuery->whereDate('expected_date', '<=', now()->toDateString());
                                break;
                            case 'tomorrow':
                                $servicesQuery->whereDate('expected_date', now()->addDay()->toDateString());
                                break;
                            case 'this_week':
                                $servicesQuery->whereBetween('expected_date', [now()->startOfWeek(), now()->endOfWeek()]);
                                break;
                            case 'this_month':
                                $servicesQuery->whereMonth('expected_date', now()->month);
                                break;
                            case 'this_year':
                                $servicesQuery->whereYear('expected_date', now()->year);
                                break;
                            default:
                                // Handle other filters or invalid filter values
                                break;
                        }   
                        
                         
                    }
                    
                    
                    if (isset($value) && $value !== '') {
                        if ($value === 'pending') {
                            
                            $servicesQuery->whereIn('status', ['0', '1', '2', '3']);
                        } else {
                           
                            $servicesQuery->where('status', $value);
                        }
                    }else{
                        $servicesQuery->whereIn('status', ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10' ,'11']);         
                        
                    }
        
                    if ($perPage === 'all') {
                        $perPage = $servicesQuery->count();
                    }     
                    
                      $services = $servicesQuery->orderBy('updated_at', 'desc')->paginate($perPage, ['*'], 'page', $page);

    
                        $statusCounts = $servicesQuery->selectRaw('status, count(*) as count')
                            ->groupBy('status')
                            ->pluck('count', 'status')
                            ->toArray();
                    
         
                
                 return response()->json([
                    'success' => true,
                    'status_counts' => $statusCounts ?? [],
                    'data' => ServiceDataResource::collection($services),
                    'pagination' => [
                        'total' => $services->total(),
                        'per_page' => $services->perPage(),
                        'current_page' => $services->currentPage(),
                        'last_page' => $services->lastPage(),
                        'from' => $services->firstItem(),
                        'to' => $services->lastItem(),
                    ],
                ]); 
                } 
                else {
                    
                    return response()->json(['error' => 'Please login to access.'], 401);
                    
                }

            break;

            case 'amcs':
                
                $perPage = $request->query('per_page', 10); 
                
                $page = $request->query('page', 1);
                
                $customer_id = $request->query('customer_id');
                
                $employer_id = $request->query('employer_id');
                
                $category = $request->query('category');
            	
            	$from_date = $request->query('from_date');
                $to_date = $request->query('to_date');
                $status = $request->query('status');
                
                $filter = $request->query('filter', '');
                
                if (Auth::check()) {
                    
                    $user = Auth::user();
                    
                    $companyId = $user->company_id;
                
                    $isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();
                
                    $amcQuery = \App\Models\Amc::where('company_id', $companyId);
                
                    if ($value !== '' && isset($value)) {
                        
                        if ($value === 'pending') {
                             
                            $amcQuery->whereIn('amc_status', ['0', '1']); 
                        }
                        else{
                        $amcQuery->where('amc_status', $value); 
                        }
                        
                    }
                    else{
                        $amcQuery->whereIn('amc_status', ['0', '1', '2']); 
                        
                    }
                  
                  	if ($from_date !== null) {
                        $amcQuery->whereDate('updated_at', '>=', $from_date);
                    }

                    if ($to_date !== null) {
                        $amcQuery->whereDate('updated_at', '<=', $to_date);
                    }
                    
                    
                    if (isset($category) && $category !== '') {
                        // var_dump('cat');
                        $amcQuery->where('amc_payment_type', $category);
                    }
                    
                    
                    
                
                    if (!$isAdmin) {
                        $amcQuery->whereHas('users', function ($query) use ($user) {
                            $query->where('users.id', $user->id);
                        });
                    }
                    if(isset($customer_id) && $customer_id !== ''){
                     
                        $amcQuery->where('customer_id', $customer_id);
                    }    
                
                    if($employer_id !== '' && isset($employer_id)) {
                        $amcQuery->whereHas('users', function ($query) use ($employer_id) {
                            $query->where('users.id', $employer_id);
                        });
                    }
                
                    if ($filter !== 'all' && $filter !== '' && isset($filter)) {
                          
                        $amcQuery
                        ->whereHas('amcDates', function ($query) use ($filter) {
                            switch ($filter) {
                                case 'today':
                                    $query->whereDate('date', '<=', now()->toDateString());
                                    break;
                                case 'tomorrow':
                                    $query->whereDate('date', now()->addDay()->toDateString());
                                    break;
                                case 'this_week':
                                    $query->whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()]);
                                    break;
                                case 'this_month':
                                    $query->whereMonth('date', now()->month);
                                    break;
                                case 'this_year':
                                    $query->whereYear('date', now()->year);
                                    break;
                                default:
                                    // Handle other filters or invalid filter values
                                    break;
                            }
                        });
                        
                       // $leadsQuery->whereIn('leadstatus_id', ['0', '1']);
                    }
                
                     if ($perPage === 'all') {
                        $perPage = $amcQuery->count();
                    }
                    
                    $amcs = $amcQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
                    
                    $statusCounts = $amcQuery->selectRaw('amc_status, count(*) as count')->groupBy('amc_status')->pluck('count', 'amc_status')->toArray(); 
                    
                    $response = [
                        'success' => true,
                        'status_counts' => $statusCounts,
                        'data' => AmcResource::collection($amcs), // Get the paginated items
                        'pagination' => [
                            'total' => $amcs->total(),
                            'per_page' => $amcs->perPage(),
                            'current_page' => $amcs->currentPage(),
                            'last_page' => $amcs->lastPage(),
                            'from' => $amcs->firstItem(),
                            'to' => $amcs->lastItem(),
                        ],
                    ];
                    
                    return response()->json($response);    
                } 
                else {
                    return response()->json(['error' => 'Please login to access.'], 401);
                }


                
            break;
            case 'sales':
                $perPage = $request->query('per_page', 10); 
                $page = $request->query('page', 1);
                $customer_id = $request->query('customer_id');                
                $invoice_id = $request->query('invoice_id');
            	$from_date = $request->query('from_date'); // New parameter
    			$to_date = $request->query('to_date'); 
                $invoice_group = $request->query('invoice_group');
            	$invoice_type = $request->query('invoice_type', 'all'); 
            
               
                if (Auth::check()) {
                    
                    $user = Auth::user();
                    
                    $companyId = $user->company_id;
                    if($value == 'due'){
                        $dueQuery = \App\Models\Sales::where('company_id', $companyId)->where('due_amount', '>', 0)->where('status', 'Success');                        
                    }
                    if($value == 'success'){
                        $dueQuery = \App\Models\Sales::where('company_id', $companyId)->where('due_amount',  0)->where('status', 'Success');                        
                    }
                    if($value == 'search'){
                        $dueQuery = \App\Models\Sales::where('company_id', $companyId);                        
                    }
                    
                    if ($customer_id !== '' && isset($customer_id)) {                       
                        $dueQuery->where('client_id', $customer_id);                            
                    }
                        
                    if ($invoice_id !== '' && isset($invoice_id)) {                   
                        $dueQuery->where('invoice_id', $invoice_id);                        
                    }
                  
                  	if ($from_date !== null) {
                        $dueQuery->whereDate('updated_at', '>=', $from_date);
                    }

                    if ($to_date !== null) {
                        $dueQuery->whereDate('updated_at', '<=', $to_date);
                    }   
                  
                  	if($invoice_group !== '' && isset($invoice_group) && $invoice_group !== 'all'){
                        $dueQuery->whereHas('invoice', function($query) use ($invoice_group) {
                            $query->where('invoice_group', $invoice_group);
                        });
                    }
                  	if ($invoice_type !== 'all' && in_array($invoice_type, ['services', 'sales'])) {
                        $dueQuery->where('invoice_type', $invoice_type);
                    }                        
                    
                    if ($perPage === 'all') {
                        $perPage = $dueQuery->count();
                    }
                    
                    
                    if($value == 'due'){
                         $sales = $dueQuery->paginate($perPage, ['*'], 'page', $page);
                    }
                    else{
                        $sales = $dueQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
                        
                    }
                    $response = [
                        'success' => true,
                        'data' => SaleResource::collection($sales),//$sales->items(),LeadResource::collection($sales), // Get the paginated items
                        'pagination' => [
                            'total' => $sales->total(),
                            'per_page' => $sales->perPage(),
                            'current_page' => $sales->currentPage(),
                            'last_page' => $sales->lastPage(),
                            'from' => $sales->firstItem(),
                            'to' => $sales->lastItem()
                        ],
                       
                    ];
                    
                    return response()->json($response);
                }
                else {
                    return response()->json(['error' => 'Please login to access.'], 401);
                }
            break;
            case 'sales_gst':
                $perPage = $request->query('per_page', 10); 
                $page = $request->query('page', 1);
                $customer_id = $request->query('customer_id');                
                $invoice_id = $request->query('invoice_id');
            	$from_date = $request->query('from_date'); // New parameter
    			$to_date = $request->query('to_date'); 
                $invoice_group = $request->query('invoice_group');
            	$invoice_type = $request->query('invoice_type', 'all'); 
            
               
                if (Auth::check()) {
                    
                    $user = Auth::user();
                    
                    $companyId = $user->company_id;
                    if($value == 'due'){
                        $dueQuery = \App\Models\Sales::where('company_id', $companyId)->where('due_amount', '>', 0)->where('status', 'Success');                        
                    }
                    if($value == 'success'){
                        $dueQuery = \App\Models\Sales::where('company_id', $companyId)->where('due_amount',  0)->where('status', 'Success');                        
                    }
                    if($value == 'search'){
                        $dueQuery = \App\Models\Sales::where('company_id', $companyId);                        
                    }
                    
                    if ($customer_id !== '' && isset($customer_id)) {                       
                        $dueQuery->where('client_id', $customer_id);                            
                    }
                        
                    if ($invoice_id !== '' && isset($invoice_id)) {                   
                        $dueQuery->where('invoice_id', $invoice_id);                        
                    }
                  
                  	if ($from_date !== null) {
                        $dueQuery->whereDate('current_date', '>=', $from_date);
                    }

                    if ($to_date !== null) {
                        $dueQuery->whereDate('current_date', '<=', $to_date);
                    }   
                  
                  	if($invoice_group !== '' && isset($invoice_group) && $invoice_group !== 'all'){
                        $dueQuery->whereHas('invoice', function($query) use ($invoice_group) {
                            $query->where('invoice_group', $invoice_group);
                        });
                    }
                  	if ($invoice_type !== 'all' && in_array($invoice_type, ['services', 'sales'])) {
                        $dueQuery->where('invoice_type', $invoice_type);
                    }                        
                    
                    if ($perPage === 'all') {
                        $perPage = $dueQuery->count();
                    }
                    
                    
                    if($value == 'due'){
                         $sales = $dueQuery->paginate($perPage, ['*'], 'page', $page);
                    }
                    else{
                        $sales = $dueQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
                        
                    }
                    $response = [
                        'success' => true,
                        'data' => SaleResource::collection($sales),//$sales->items(),LeadResource::collection($sales), // Get the paginated items
                        'pagination' => [
                            'total' => $sales->total(),
                            'per_page' => $sales->perPage(),
                            'current_page' => $sales->currentPage(),
                            'last_page' => $sales->lastPage(),
                            'from' => $sales->firstItem(),
                            'to' => $sales->lastItem()
                        ],
                       
                    ];
                    
                    return response()->json($response);
                }
                else {
                    return response()->json(['error' => 'Please login to access.'], 401);
                }
            break;
            
           case 'purchases':
                $perPage = $request->query('per_page', 10); 
                $page = $request->query('page', 1);
                $customer_id = $request->query('supplier_id');
            	$from_date = $request->query('from_date'); 
    			$to_date = $request->query('to_date');                 
                $invoice_id = $request->query('invoice_id');
                
               
                if (Auth::check()) {
                    
                    $user = Auth::user();
                    
                    $companyId = $user->company_id;
                    if($value == 'due'){
                     
                        $purQuery = \App\Models\PurchaseOrder::where('company_id', $companyId)->where('balance_amount', '>', 0);
                        
                    }
                    if($value == 'success'){
                     
                        $purQuery = \App\Models\Sales::where('company_id', $companyId)->where('balance_amount',  0);;
                        
                    }
                    if($value == 'search'){
                     
                        $purQuery = \App\Models\Sales::where('company_id', $companyId);
                        
                    }
                    
                    if ($customer_id !== '' && isset($customer_id)) {
                     
                        $purQuery->where('supplier_id', $customer_id);
                            
                    }
                  
                  
                  	if ($from_date) {
                        $purQuery->whereDate('updated_at', '>=', $from_date);
                    }

                    if ($to_date) {
                        $purQuery->whereDate('updated_at', '<=', $to_date);
                    }
                        
    
                    
                    if ($perPage === 'all') {
                        $perPage = $purQuery->count();
                    }
                     
                    
                    if($value == 'due'){
                         $purchase = $purQuery->paginate($perPage, ['*'], 'page', $page);
                    }
                    else{
                        $purchase = $purQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);                        
                    }
                    $response = [
                        'success' => true,
                        'data' => PurchaseResource::collection($purchase),
                        'pagination' => [
                            'total' => $purchase->total(),
                            'per_page' => $purchase->perPage(),
                            'current_page' => $purchase->currentPage(),
                            'last_page' => $purchase->lastPage(),
                            'from' => $purchase->firstItem(),
                            'to' => $purchase->lastItem()
                        ],
                       
                    ];
                    
                    return response()->json($response);
                }
                else {
                    return response()->json(['error' => 'Please login to access.'], 401);
                }
            break;
            
           case 'enquiry':
              $perPage = $request->query('per_page', 10); 
              $page = $request->query('page', 1);
              $from_date = $request->query('from_date'); 
              $to_date = $request->query('to_date'); 
             

              if (Auth::check()) {
                  $user = Auth::user();
                  $com_id = $user->company_id;

                  $enQuery = \App\Models\Enquires::where('company_id', $com_id);

                  if ($from_date) {
                      $enQuery->whereDate('updated_at', '>=', $from_date);
                  }

                  if ($to_date) {
                      $enQuery->whereDate('updated_at', '<=', $to_date);
                  }

                  if (!is_null($value) && $value !== '') {
                      $enQuery->where('status', $value); 
                  }

                  if ($perPage === 'all') {
                      $perPage = $enQuery->count();
                  }

                  $enquiries = $enQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

                  $response = [
                      'success' => true,
                      'data' => $enquiries->items(),
                      'pagination' => [
                          'total' => $enquiries->total(),
                          'per_page' => $enquiries->perPage(),
                          'current_page' => $enquiries->currentPage(),
                          'last_page' => $enquiries->lastPage(),
                          'from' => $enquiries->firstItem(),
                          'to' => $enquiries->lastItem()
                      ],
                  ];

                  return response()->json($response);
              } else {
                  return response()->json(['error' => 'Please login to access.'], 401);
          		}
            break;
            
         

            
            default:
                break;
        }

        return $this->sendResponse($items, 'successfully');
       
    }
    
    
    public function get_by_date($table_date)
	{
		$dates = $this->input->post('dates');
		if($dates=='Today'){
      		//$this->db->where("$table_date > DATE_SUB(NOW(), INTERVAL 1 DAY)");
      		$this->db->where("$table_date",date("Y-m-d"));
      	}
      	if($dates=='Weekly'){
      		$this->db->where("$table_date > DATE_SUB(NOW(), INTERVAL 1 WEEK)");
      	}
      	if($dates=='Monthly'){
      		$this->db->where("$table_date > DATE_SUB(NOW(), INTERVAL 1 MONTH)");
      	}
      	if($dates=='Yearly'){
      		$this->db->where("$table_date > DATE_SUB(NOW(), INTERVAL 1 YEAR)");
      	}
	}

  

}

