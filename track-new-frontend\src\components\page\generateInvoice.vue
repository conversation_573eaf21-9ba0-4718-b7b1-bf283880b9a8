<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" class="non-printable" @display_hold_list="displayHold"
                :refresh="refresh" @refresh_store="refresh_store" :type="'invoice'"></headbar> -->


            <!-- services home -->
            <div class="relative w-full px-3 py-3">
                <!-- Display home component by default -->
                <!-- <template_1 :formData="shareData" @goSetting="goBackPage" :viewServiceData="viewServiceData"
                    :categoryID="category_id" :service_id="service_id" :category_name="category_name"></template_1> -->
                <pos :companyId="companyId" :userId="userId" :typeOfInvoice="typeOfInvoice" :service_data="service_data"
                    :enable_hold="enable_hold" @close_hold="displayHold" @refresh_hold="refreshhold"
                    :store_refresh="store_refresh"></pos>

            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden non-printable"
            @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"></sidebar>
        </div> -->
    </div>
</template>
<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/setting_categories/invoiceTemplates/headbar.vue';
import pos from '../supporting/sales/pos/pos.vue';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';

export default {
    name: 'generate_invoice',
    components: {
        // sidebar,
        // headbar,
        pos
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            viewServiceData: null,
            typeofService: null,
            route_item: 3,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            data: [],
            shareData: {},
            service_id: null,
            //---api integration---
            companyId: null,
            userId: null,
            service_data: {},
            typeOfInvoice: 'services',
            //---hold list--
            enable_hold: false,
            refresh: false,
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Add / Update Services Invoice';
        const pageDescription = 'Create or update services invoices seamlessly with detailed input options for accurate billing, ensuring efficient tracking and streamlined financial management.';
        useMeta(pageTitle, pageDescription);
        return { pageTitle };
    },
    methods: {
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),
        ...mapActions('holdsList', ['fetchHoldsList', 'updateIsEnable', 'updateIsShowList']),
        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        // Method to parse the URL
        parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const category = urlParts[urlParts.length - 4];
            const categoryID = urlParts[urlParts.length - 3]
            const id = urlParts[urlParts.length - 1];
            this.category_id = Number(categoryID);
            this.service_id = decodeURIComponent(id);
            this.category_name = decodeURIComponent(category);
        },
        goBackPage() {
            this.$router.go(1);
        },
        //--get service data---
        getServiceData(id) {
            axios.get(`/services/${id}`, { company_id: this.companyId })
                .then(response => {
                    // console.log(response.data, 'What happening the get service..!');
                    this.service_data = response.data.data;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        displayHold() {
            this.enable_hold = !this.enable_hold;
        },
        refreshhold() {
            this.refresh = !this.refresh;
        }

    },
    mounted() {
        this.updateIsMobile(); // Initial check
        this.updateIsEnableBottom(false);
        this.updateIsEnable(true);
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');

            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        // let existData = localStorage.getItem('invoiceSetting');
        // if (existData) {
        //     let parseData = JSON.parse(existData);
        //     if (parseData.disclaimer !== '') {
        //         let disclaimerMSG = parseData.disclaimer.split('\n');
        //         this.shareData = { ...parseData, disclaimer: disclaimerMSG };

        //     } else {
        //         this.shareData = parseData;
        //     }
        // }
        // const collectForm = localStorage.getItem('CategoriesForm');
        // if (collectForm) {
        //     let dataParse = JSON.parse(collectForm);
        //     // console.log(dataParse, 'WWWWWWWWWWWW');
        //     this.servicecategory_data = dataParse;
        // }
        this.parseUrl(); // Call the function to parse the URL
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        this.updateIsEnable(false);
        this.updateIsShowList(false);
        this.updateIsEnableBottom(true);
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        service_id: {
            deep: true,
            handler(newValue) {
                // console.log(newValue, 'What happening...!');
                if (newValue !== 'sales') {
                    this.typeOfInvoice = 'services'
                    this.getServiceData(newValue);
                } else {
                    this.typeOfInvoice = 'direct_services'
                    this.service_data = null;
                }
            }
        }

    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen || this.isStoreData) {
            if (this.isStoreData) {
                // alert('are you sure get back?');
                this.message = 'Are you sure you want to go back? Unsaved data may be lost...!';
                this.show_confirm_box = true;
            }
            if (this.anyModalOpen) {
                // If any modal is open, close all modals
                this.updateModalOpen = !this.updateModalOpen;
            }
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnable(false);
            this.updateIsShowList(false);
            this.updateIsEnableBottom(true);
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}

/* Center content on larger screens */
.center-screen {
    display: flex;
    align-items: center;
    justify-content: center;
}

@media print {

    /* Additional styles for printing */
    body {
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */
    }

    .non-printable {
        display: none;
    }

    /* Apply different styles for printing */
    .center-content {
        width: 100%;
        margin: 0;
    }
}
</style>