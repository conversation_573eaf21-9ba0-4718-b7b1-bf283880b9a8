import axios from "axios";
import { retry } from "ckeditor5";

const state = {
    whatsapp_msg: {
        employee: {
            service: {
                status: true,
                message: "**New Service Request**\n\n👤 *Customer*: {customer_name}\n\n *ID*: {service_code}\n\n *Product*: {device_brand} {device_model}\n\n *Type*: {service_type}\n\n *Assigned*: {assigned_employee}\n\n *Track*: {long_url}\n\n"
            },
            amc: {
                status: true,
                message: "**New AMC Request**\n\n👤 *Customer*: {customer_name}\n\n *ID*: {amc_code}\n\n *Product*: {device_brand} {device_model}\n\n *Contract*: {contract_type}\n\n *Dates*: {start_date} to {end_date}\n\n *Assigned*: {assigned_employee}\n\n *Track*: {long_url}\n\n"
            },
            rma: {
                status: true,
                message: "**New RMA Request**\n\n👤 *Customer*: {customer_name}\n\n *ID*: {rma_code}\n\n *Product*: {device_brand} {device_model}\n\n *Issue*: {issue_description}\n\n *Assigned*: {assigned_employee}\n\n *Track*: {long_url}\n\n"
            },
            lead: {
                status: true,
                message: "**New Lead**\n\n👤 *Customer*: {customer_name}\n\n *ID*: {lead_code}\n\n *Product*: {product_interested}\n\n *Source*: {lead_source}\n\n *Assigned*: {assigned_employee}\n\n *Contact*: {customer_phone}\n\n"
            }
        },
        sales: {
                "status": true,
                "message": "Dear {customer_name},\n\nThank you for your purchase! 🎉\n\n📦 *Order ID*: {order_id}\n\n🛒 *Product*: {product_name}\n\n💰 *Total Amount*: {total_amount}\n\n🚚 *Delivery Date*: {delivery_date}\n\n🔗 *Track Your Order*: {short_url}\n\n📞 *Contact Us*: {company_contact}\n\nThank you for choosing {company_name}! 🙏"     
        },
        estimations:{
            "status": true,
            "message": "Dear {customer_name},\n\nHere is your estimation details: 📄\n\n📝 *Estimation ID*: {estimation_id}\n\n🛠️ *Service/Product*: {service_product}\n\n💲 *Estimated Amount*: {estimated_amount}\n\n📅 *Valid Until*: {valid_until_date}\n\n🔗 *View Estimation*: {short_url}\n\n📞 *Contact Us*: {company_contact}\n\nThank you for considering {company_name}! 🙏"
        },
        proforma: {
                "status": true,
                "message": "Dear {customer_name},\n\nYour proforma invoice is ready! 📄\n\n📄 *Proforma ID*: {proforma_id}\n\n🛒 *Product/Service*: {product_service}\n\n💲 *Total Amount*: {total_amount}\n\n📅 *Valid Until*: {valid_until_date}\n\n🔗 *View Proforma*: {short_url}\n\n📞 *Contact Us*: {company_contact}\n\nThank you for choosing {company_name}! 🙏"
        },
        leads: {
                "status": true,
                "message": "Dear {customer_name},\n\nThank you for your interest in {product_service}! 🎉\n\n📝 *Lead ID*: {lead_id}\n\n🛒 *Product/Service*: {product_service}\n\n📞 *Contact Us*: {company_contact}\n\nWe will get back to you shortly. Thank you for choosing {company_name}! 🙏"
        }

    },
  };

  const mutations = {
      RESET_STATE(state) {
    },

};

const actions = {
  
 
  };

const getters = {
    getTemplates(state) {
        return state.whatsapp_msg;
      }
   
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
