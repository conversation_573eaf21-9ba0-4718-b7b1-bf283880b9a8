<template>
    <div v-if="loading">
      <!-- This div will display the loading text -->
      <div class="loader"></div>
      <!-- The loader animation will be displayed here -->
      <div class="loading-text text-sm absolute">Loading.....</div>
    </div>
  </template>

<script>

export default {
    name: 'CircleLoader',
    props: {
        loading: {
            type: Boolean,
            default: false,
        },
        // color: {
        //     type: String,
        //     default: '#7f58af',
        // },
        // size: {
        //     type: Number,
        //     default: 64,
        // },
        // duration: {
        //     type: String,
        //     default: '2.4s',
        //     validator: validateDuration,
        // },
    },
    data() {
        return {
            color:'#00FFFF',
            size: 64,
            duration: '2.4s',
        }
    },
}
</script>

<style scoped>

.loading-text {
color:'blue';
  font-size: 16px;
  margin-top: 8px;
  animation: fade-in 1s ease-in-out infinite;
}


@keyframes fade-in {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}
.loader {
  border: 5px solid #f3f3f3;
  border-radius: 50%;
  border-top: 5px solid #3498db;
  width: 30px;
  height: 30px;
  -webkit-animation: spin 2s linear infinite; /* Safari */
  animation: spin 2s linear infinite;
}

/* Safari */
@-webkit-keyframes spin {
  0% { -webkit-transform: rotate(0deg); }
  100% { -webkit-transform: rotate(360deg); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>