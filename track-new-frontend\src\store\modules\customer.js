// store/modules/customer.js
import axios from "axios";

const state = {
  customer: [],
  pagination: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  };

  const mutations = {
    SET_CUSTOMER(state, customerData, paginationData) {
      state.customer = customerData;
      state.pagination = paginationData;
    },
    RESET_STATE(state) {
      state.customer = [];
        state.pagination = {};        
        state.lastFetchTime = null;
        state.isFetching = false;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    }

  };

  const actions = {
    updateCustomerName({ commit }, customerData) {
      // Simulate an asynchronous operation (e.g., API call) to update customer name
      setTimeout(() => {
        // Commit mutation to update customer name
        commit('SET_CUSTOMER', customerData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchCustomerList({ commit, state, rootState, dispatch }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 1 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['customer_update']; 
      const iscustomer = rootState.apiUpdates.is_update['customer_list']; 
  
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (!iscustomer && (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds && lastUpdateTime <= state.lastFetchTime) || lastUpdateTime < state.lastFetchTime)) { 
        return; // Skip request if less than 30 seconds have passed since the last request
      }
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          commit('SET_IS_FETCHING', true);
          axios.get('/customers', { params: { company_id: company_id, page: 1, per_page: 'all' } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Customer list..!');
              let customer_list = response.data.data;              
              if (customer_list.length > 2) {
                // Sorting the array alphabetically based on the 'name' key
                customer_list.sort((a, b) => {
                  // Convert both names to lowercase to ensure case-insensitive sorting
                  const nameA = (a.first_name || '').toLowerCase(); // Use an empty string as a fallback
                  const nameB = (b.first_name || '').toLowerCase(); // Use an empty string as a fallback
                  // Compare the two names
                  if (nameA < nameB) {
                    return -1;
                  }
                  if (nameA > nameB) {
                    return 1;
                  }
                  return 0;
                });
              }
              commit('SET_CUSTOMER', customer_list, response.data.pagination);
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              dispatch('apiUpdates/updateKeyIsUpdate', { key: 'customer_list', value: false}, { root: true });
              return customer_list;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              commit('SET_IS_FETCHING', false);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    filterByCustomer({ commit }, searchData) {       
      const inputValue = searchData;
      let filteredCustomerOptions = [];
        // console.log(inputValue, 'WWWWWWW');
      // Access customer list from Vuex state
      const customerList = state.customer;

      if (customerList.length > 0) {
        if (inputValue !== "") {
          // console.log('Waht happening....!', this.isDropdownOpen);
          if (!isNaN(inputValue)) {
            // If input is a number, filter options by phone number
            const inputNumber = inputValue.toLowerCase();
            filteredCustomerOptions = customerList.filter(
              (option) => option.contact_number.toLowerCase().includes(inputNumber)
            );
          } else {
            // If input is not a number, filter options by name
            const inputName = inputValue.toLowerCase();
            filteredCustomerOptions = customerList.filter(
              (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
            );
          }
        } else {
          // Handle the case where formValues[fields.fieldKey] is undefined or null
          // You might want to set this.filteredCustomerOptions to an empty array or another default value
          filteredCustomerOptions = this.customer_list;
        }
      }
    }
  };

  const getters = {
    currentCustomer(state) {
      return state.customer;
    },
    currentPagination(state) {
      return state.pagination;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
