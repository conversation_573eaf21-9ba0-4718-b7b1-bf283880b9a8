<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateServicesAPIRequest;
use App\Http\Requests\API\UpdateServicesAPIRequest;
use App\Models\Services;
use App\Models\Customer;
use App\Models\Companies;
use App\Models\User;
use App\Models\AmcDates;
use App\Models\InvoiceSettings;
use App\Repositories\ServicesRepository;
use App\Repositories\ServiceAssignsRepository;
use App\Repositories\ExpensesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use App\Http\Controllers\API\SalesAPIController;
use App\Repositories\SalesRepository;
use App\Repositories\SalesItemsRepository;
use App\Repositories\SalesPaymentRepository;
use App\Http\Resources\api\ServiceResource;
use App\Http\Resources\api\ServiceDataResource;
use App\Http\Services\RelayMessage;
use App\Http\Services\SmsDeliveryService;
use App\Http\Services\SmsServiceManagement;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Response;
use Auth;
use DateTime;
use DateTimeZone;

/**
 * Class ServicesController
 * @package App\Http\Controllers\API
 */

class ServicesAPIController extends AppBaseController
{
    /** @var  ServicesRepository */
    private $servicesRepository;
    private $serviceAssignsRepository;
    protected $salesApiController;
    private $salesRepository;
    private $salesItemsRepository;
    private $salesPaymentRepository;
  	private $expensesRepository;
    private $smsDeliveryService;
    private $smsServiceManagement;
    

    public function __construct(ServicesRepository $servicesRepo, ServiceAssignsRepository $serviceAssignsRepo,
    SalesRepository $salesRepo, SalesItemsRepository $salesItemRepo, SalesPaymentRepository $salesPaymentRepo, ExpensesRepository $expensesRepo, SmsDeliveryService $smsDeliveryService, SmsServiceManagement $smsServiceManagement)
    {        
        $this->servicesRepository = $servicesRepo;        
        $this->salesRepository    = $salesRepo;
        $this->salesItemsRepository = $salesItemRepo;
        $this->salesPaymentRepository = $salesPaymentRepo;
        $this->serviceAssignsRepository = $serviceAssignsRepo;
      	$this->expensesRepository = $expensesRepo;
        $this->smsDeliveryService = $smsDeliveryService;
        $this->smsServiceManagement = $smsServiceManagement;
    }
    
    public function createPDF(Request $request, $id)
    {

        $user = Auth::user();

		$copyTypes = [
            'official' => 0,
            'customer' => 0,
        ];

        // Get the copy_type query parameter
        $copyType = $request->query('copy_type', '');

        // Check if copy_type is "both" or specific types
        if ($copyType === 'both') {
            // Enable both copies if "both" is specified
            $copyTypes['official'] = 1;
            $copyTypes['customer'] = 1;
        } else {
            // Otherwise, handle individual copy types
            foreach (explode(',', $copyType) as $type) {
                if (array_key_exists($type, $copyTypes)) {
                    $copyTypes[$type] = 1;
                }
            }
        }

        // Now you can access $copyTypes['official'] and $copyTypes['customer']
        $off_cpy = $copyTypes['official'];
        $cust_cpy = $copyTypes['customer'];

        // var_dump($user);
        // exit();
        /** @var Services $services */
       
 
       
        //$services = $this->servicesRepository->find($id);//$id
        
        $services = Services::where('service_code', $id)->first();
        


        if (empty($services)) {
            return $this->sendError('Services not found');
        }


        $service = $services->company_id;
        
        
        // $updated_at_utc = new DateTime($services->updated_at, new DateTimeZone('UTC'));

        // Add the GMT offset of +5 hours and 30 minutes
        // $updated_at_local = $updated_at_utc->modify('+5 hours 30 minutes');
        
        // Format the updated date and time according to your requirement
        // $service_data['updated_at'] = $updated_at_local->format('Y-m-d H:i:s');
       $rawCreatedAt = $services->getRawOriginal('created_at');
       $service_data['updated_at'] = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $rawCreatedAt)
          ->setTimezone('Asia/Kolkata')
          ->format('d-m-Y h:i:sA');
        
       // $service_data['updated_at'] =  $services->updated_at;
        $service_data['code'] =  $services->service_code;
        $service_data['id'] =  $services->id;
        $service_data['customer_copies'] = $cust_cpy;
        $service_data['official_copies'] = $off_cpy;
       	$service_data['service_id'] = $services->service_id;
      
        $invoice = InvoiceSettings::where('company_id', $service)->first();
        $customer =  Customer::where('id', $services->customer_id)->first();
         
        $company = Companies::where('id', $service)->first();

        // $data = AppSetting::take(1)->first();
        // $jobsdata = Jobs::with('jobsPayment', 'jobsPlans', 'user', 'district', 'city')->find($id);


        // $pdf = Pdf::loadView('jobs.invoice', ['bookingdata' => $jobsdata, 'data' => $data]);
        // return $pdf->download('invoice.pdf');
        $pdf = Pdf::loadView('pdf.jobsheet', ['customer' => $customer, 'data' => json_decode($services['service_data']), 'company' => $company, 'invoice' => $invoice, 'code' => $service_data]);
        return $pdf->download($services->service_code.'_jobsheet.pdf');    
    }
    
    
     public function viewPDF($id)
     {
        $user = Auth::user();   
        /** @var Services $services */       
        //$services = $this->servicesRepository->find($id);//$id        
        $services = Services::where('service_code', $id)->first();    
        if (empty($services)) {
            return $this->sendError('Services not found');
        }


        $service = $services->company_id;
        
       // $updated_at_utc = new DateTime($services->updated_at, new DateTimeZone('UTC'));

        // Add the GMT offset of +5 hours and 30 minutes
      //  $updated_at_local = $updated_at_utc->modify('+5 hours 30 minutes');
        
        // Format the updated date and time according to your requirement
       // $service_data['updated_at'] = $updated_at_local->format('Y-m-d H:i:s');
       
       	$rawCreatedAt = $services->getRawOriginal('created_at');
      	$service_data['updated_at'] = \Carbon\Carbon::createFromFormat('Y-m-d H:i:s', $rawCreatedAt)
          ->setTimezone('Asia/Kolkata')
          ->format('d-m-Y h:i:sA');
        
        //$service_data['updated_at'] =  $services->updated_at;
        $service_data['code'] =  $services->service_code;
        $service_data['id'] =  $services->id;
        $service_data['customer_copies'] = 1;
        $service_data['official_copies'] = 1;
       	$service_data['service_id'] = $services->service_id;
      
        $invoice = InvoiceSettings::where('company_id', $service)->first();
        
        $customer =  Customer::where('id', $services->customer_id)->first();
         
        $company = Companies::where('id', $service)->first();

        // $data = AppSetting::take(1)->first();
        // $jobsdata = Jobs::with('jobsPayment', 'jobsPlans', 'user', 'district', 'city')->find($id);


        // $pdf = Pdf::loadView('jobs.invoice', ['bookingdata' => $jobsdata, 'data' => $data]);
        // return $pdf->download('invoice.pdf');
        // $pdf = Pdf::loadView('pdf.jobsheet', ['customer' => $customer, 'data' => json_decode($services['service_data']), 'company' => $company, 'invoice' => $invoice, 'code' => $service_data]);
       
        // return $pdf->stream($services->service_code.'_jobsheet.pdf');
      
        
        return view('pdf.jobsheet')->with([
          'customer' => $customer, 
          'data' => json_decode($services['service_data']), 
          'company' => $company, 
          'invoice' => $invoice, 
          'code' => $service_data
        ]);

       

     
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/services",
     *      summary="getServicesList",
     *      tags={"Services"},
     *      description="Get all Services",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="category_id",
    *          description="ID of the category to filter the services",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Services")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');
        $category = $request->query('category_id');
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

       
        if (Auth::check()) {
            $user = Auth::user();

           // $isAdmin = $user->roles()->exists() && $user->roles()->where('name', 'admin')->exists();
           
           $isAdmin = $user->roles()->whereIn('name', ['admin', 'Sub_Admin'])->exists();
            
             $servicesQuery = Services::with('category')
                                ->where('company_id', $companyId)
                                ->whereHas('category', function ($query) {
                                    $query->whereNull('deleted_at');
                                });
                               

            if (!$isAdmin) {
                $servicesQuery->whereHas('users', function ($query) use ($user) {
                    $query->where('users.id', $user->id);
                });
            }

            if ($category !== null) {
                $servicesQuery->where('servicecategory_id', $category);
            }

            if ($perPage === 'all') {
                $perPage = $servicesQuery->count();
            }       

            $services = $servicesQuery->orderBy('updated_at', 'desc')->paginate($perPage, ['*'], 'page', $page);
            
            $statusCounts = $servicesQuery->selectRaw('status, count(*) as count')->groupBy('status')->pluck('count', 'status')->toArray(); 

            return response()->json([
                'success' => true,
                'status_counts' => $statusCounts,
                'data' => ServiceDataResource::collection($services),
                'pagination' => [
                    'total' => $services->total(),
                    'per_page' => $services->perPage(),
                    'current_page' => $services->currentPage(),
                    'last_page' => $services->lastPage(),
                    'from' => $services->firstItem(),
                    'to' => $services->lastItem(),
                ],
            ]);       
        } else {
            return response()->json(['error' => 'Please login to access.'], 401);
        }
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/services",
     *      summary="createServices",
     *      tags={"Services"},
     *      description="Create Services",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Services")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Services"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateServicesAPIRequest $request)
    {
        
        
        $input = $request->all();
   
      
      if (isset($input['company_id']) && $input['company_id'] === null) {
            return $this->sendError('Please provide company.', 400);
        }
      
        $company = Companies::find($input['company_id']);
       if (!$company) {
            return $this->sendError('Invalid company ID.', 400);
        }
        $company_name = $company->company_name;
      	$first_two_chars = substr($company_name, 0, 2);
         
        do {
            $code = $first_two_chars.$this->genServiceCode();
            $service_code = Services::where('service_code', $code)->first();
        } while ($service_code);
        
        $input['service_code'] = $code;
       
        $input['created_by'] = auth()->user()->id;
        $input['updated_by'] = auth()->user()->id;
        
        $services = $this->servicesRepository->create($input);
      	if (!$services) {
            return $this->sendError('Service creation failed.', 500);
        }
      
        $notification = $services->notification;
        $array = json_decode($notification, true);  
      	
           if (!empty($input['amc']) && is_array($input['amc'])) {
           
              $amc_id = $input['amc']['amc_id'] ?? null;
              $amcdate_id = $input['amc']['amcdate_id'] ?? null;
           
              if ($amcdate_id) { // Ensure amcdate_id is not null
                  $amc_date = AmcDates::find($amcdate_id);

                  if ($amc_date) { // Ensure record exists before updating
                      $amc_date->service_status = $services->status;
                      $amc_date->service_id = $services->id;
                      $amc_date->save();
                  }
                  if ($amc_date) {
                      $services->module = 1;
                      $services->module_id = $amc_date->id;
                      $services->save(); // Ensure this is saved to the database
                  }
              }
          }        

        

        
        if(isset($input['assign_to'])){
            
            $assignTo = json_decode($input['assign_to'], true);

            if ($assignTo !== null) {
                foreach ($assignTo as $userData) {     
                  	// Access the "user_id" key within the associative array
                    $user_id = $userData['user_id'];
                	
                    $data['service_id'] =  $services->id;
                    $data['user_id'] =  $user_id;             
                    // $delete_amc_user = $this->serviceAssignsRepository->deleteByServiceId($services->id, $user_id);    
                    $this->serviceAssignsRepository->create($data); 
                   	if (is_array($array)) {
                    	foreach ($array as $type) {    
                          
                        	switch ($type) {                   
                           
                            	case 'WhatsApp': 
                                
                                	$assignedUser = User::find($user_id);
                                    if($assignedUser) {
                                      $mobileNumber = $assignedUser->mobile_number ?? ''; // Using common field name
                                      $employeeName = $assignedUser->name ?? '';

                                      $messageContent =  __('messages.service_assign_whatsapp_message', ['empname' => $employeeName, 'id' => $services->service_id, 'category' => $services->service_category]);
                                      $messageContent = str_replace('\n', PHP_EOL, $messageContent);
                                      sendWaMessageToServer($mobileNumber, $messageContent, $company->id);
                                    }                           
                                	break;                             	 	
                       	 	}
                    	}
                	}                  
                  	
                }
              
            if($services){        
            $customer = Customer::find($services->customer_id);           
            if ($customer) {                
                $contacts = $customer->contact_number;
                $relayMessage = new RelayMessage();
                $status = $services->status; 
                //$response = $relayMessage->sendWhatsAppMessage($status, $customer, $services, $company_name, $company);              
                if (is_array($array)) {
                    foreach ($array as $type) {     
                      
                        switch ($type) {
                            
                            case 'SMS':  
                                 if(checkSmsBalance($company->id)){    
                                  
                                    $this->smsServiceManagement->sendStatusUpdate($status, $customer, $services, $company_name, $input['company_id']); // Set the company ID in the service management class                     
                                	//$response = $relayMessage->sendSMSBasedOnStatus($status, $customer, $services, $company_name, $input['company_id']);
                                 }
                                break;
                            case 'WhatsApp':
                                // Send WhatsApp message
                                $response = $relayMessage->sendWhatsAppMessage($status, $customer, $services, $company_name, $company);                            
                                break;
                            case 'Email':
                                // Send Email
                               // $this->sendEmail($services);
                                break;
                            default:
                                // Handle unknown notification type
                                break;
                        }
                    }
                }
             
            }
          if (isset($input['service_expense'])) {
          	$expenses = $input['service_expense'];
            foreach ($expenses as $expense) {           
              $data = [
                'description' => $expense['description'],
                'amount' => $expense['value'],
                'expense_type' => 0,
                'date' => now(),
                'name_of_purpose' => 'Service Expense-'.$services->service_code,
                'service_id' => $services->id,
                'created_by' => auth()->user()->id, // Assign the authenticated users ID
                'updated_by' => auth()->user()->id, // Same for updated_by
                'company_id' => $services->company_id,
              ];            
              $this->expensesRepository->create($data);
            }
          }
        }
              
              	$activity_data1 = [
                  'activity_type' => 'add_service',
                  'service_id' => $services->id,
                  'services' => $services,
              	];
        		saveServiceActivity($activity_data1);
              	$activity_data = [
                    'activity_type' => 'assigned_service',
                    'service_id' => $services->id,
                    'services' => $services                        
                ];            
                saveServiceActivity($activity_data); 
            } 
            else {
                // Handle JSON decoding error
                return $this->sendError('Invalid JSON format in assign_to field', 400);
            }
        }     
      
        if (is_array($array)) {
          foreach ($array as $type) {   

            switch ($type) {                          

              case 'WhatsApp':
                $adminUser = User::where('user_type', 'admin')
                  ->where('company_id', $company->id)
                  ->first();

                if ($adminUser) {
                  $mobileNo = $adminUser->mobile_number ?? '';
                  $empName = $adminUser->name ?? ''; // More concise null coalescence             

                  $messageContentAdmin = __('messages.service_admin_whatsapp_message', ['event'=>'New Service Created','adminName' => $empName, 'id' => $services->service_id, 'category' => $services->category->service_category]);
                  $messageContentAdmin = str_replace('\n', PHP_EOL, $messageContentAdmin);
                  sendWaMessageToServer($mobileNo, $messageContentAdmin, $company->id);
                }                         
                break;                           

            }
          }
        }    
        return $this->sendResponse(new ServiceDataResource($services), 'Services saved successfully');
        
    }


    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/services/{id}",
     *      summary="getServicesItem",
     *      tags={"Services"},
     *      description="Get Services",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Services",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Services"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Services $services */
        $services = $this->servicesRepository->find($id);

        if (empty($services)) {
            return $this->sendError('Services not found');
        }

        return $this->sendResponse(new ServiceDataResource($services), 'Services retrieved successfully');
    }



       /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/service-track/{service_code}",
     *      summary="getServicesTrack",
     *      tags={"Services"},
     *      description="Get track",
     *      @OA\Parameter(
     *          name="service_code",
     *          description="service code of Services",
     *           @OA\Schema(
     *             type="string"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Services"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function getTrack(Request $request)
    {
        $id = $request->service_code;
       
        /** @var Services $services */
        $services = Services::where('service_code', $id)->first();

        if (empty($services)) {
            return $this->sendError('Services not found');
        }   
        
        return $this->sendResponse(new ServiceResource($services), 'Track retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/services/{id}",
     *      summary="updateServices",
     *      tags={"Services"},
     *      description="Update Services",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Services",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Services")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Services"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
 public function update($id, UpdateServicesAPIRequest $request)
    {
        $input = $request->all();

        /** @var Services $services */
        $services = $this->servicesRepository->find($id);

        if (empty($services)) {
            return $this->sendError('Services not found');
        }
        
         $input['updated_by'] = auth()->user()->id;

        $services = $this->servicesRepository->update($input, $id);

        $notification = $services->notification;
        $array_update = json_decode($notification, true);
   		$company = Companies::find($services->company_id);
 


        if(isset($input['assign_to'])){            
            
            $assignTo = json_decode($input['assign_to'], true);           
          
            // Check if JSON decoding was successful
            if ($assignTo !== null) {
                 $this->serviceAssignsRepository->deleteByServiceId($id);    
                foreach ($assignTo as $userData) {                    
                    $user_id = $userData['user_id'];                
                    $data['service_id'] =  $id;
                    $data['user_id'] =  $user_id;             
                   
                    $this->serviceAssignsRepository->create($data);  
                  	if (is_array($array_update)) {
                    	foreach ($array_update as $type) {   
                      
                        	switch ($type) {                          
                           
                            	case 'WhatsApp':
                                
                                	$assignedUser = User::find($user_id);
                                    if($assignedUser) {
                                      $mobileNumber = $assignedUser->mobile_number ?? ''; // Using common field name
                                      $employeeName = $assignedUser->name ?? '';

                                      $messageContent =  __('messages.service_assign_whatsapp_message', ['empname' => $employeeName, 'id' => $services->service_id, 'category' => $services->service_category]);
                                      $messageContent = str_replace('\n', PHP_EOL, $messageContent);
                                      sendWaMessageToServer($mobileNumber, $messageContent, $company->id);
                                    }                           
                                	break;                           
                           	 	
                       	 	}
                    	}
                	}
                  	
                }
            } else {
                // Handle JSON decoding error
                return $this->sendError('Invalid JSON format in assign_to field', 400);
            }
        }
   		if($services->module === 1){
            $amc_date = AmcDates::find($services->module_id);
            if ($amc_date) { 
                $amc_date->amc_status = $services->status;                  
                $amc_date->save();
            }              
      	}
   			
          
        
        if($services){        
            $customer = Customer::find($services->customer_id);
             
            if ($customer) {
                $company_name = $company->company_name;
                $contacts = $customer->contact_number;
                $relayMessage = new RelayMessage();
                $status = $services->status; 
                if (is_array($array_update)) {
                    foreach ($array_update as $type) {
                        switch ($type) {
                            case 'SMS':                                
                                if(checkSmsBalance($company->id)){     
                                    
                                    $this->smsServiceManagement->sendStatusUpdate($status, $customer, $services, $company_name, $services->company_id); 
                                	//$response = $relayMessage->sendSMSBasedOnStatus($status, $customer, $services, $company_name, $services->company_id);
                                }
                                // var_dump($response);
                                // exit();
                                break;
                            case 'WhatsApp':
                                // Send WhatsApp message
                            	$response = $relayMessage->sendWhatsAppMessage($status, $customer, $services, $company_name, $company);
                              
                                break;
                            case 'Email':
                                // Send Email
                              // $this->sendEmail($services);
                                break;
                            default:
                                // Handle unknown notification type
                                break;
                        }
                    }
                }
             
            }
          	if (isset($input['service_expense'])) {
          	    $expenses = $input['service_expense']; 
          
          		$keepIds = array_filter(array_column($expenses, 'id'));
          
                \App\Models\Expenses::where('service_id', $services->id)
                    ->whereNotIn('id', $keepIds)
                    ->forceDelete();
              	foreach ($expenses as $expense) {              
                    if (isset($expense['id']) && !empty($expense['id'])) {                        
                        $data = [
                            'description' => $expense['description'],
                            'amount' => $expense['value']                     
                           
                        ];                        
                        $this->expensesRepository->update($data, $expense['id']);
                    } 
                  	else {
                        $data = [
                            'description' => $expense['description'],
                            'amount' => $expense['value'],
                            'expense_type' => 0,
                            'date' => now(),
                            'name_of_purpose' => 'Service Expense-'.$services->service_code,	
                            'service_id' => $services->id,
                          	'company_id' => $services->company_id,
                            'created_by' => auth()->user()->id,
                            'updated_by' => auth()->user()->id, 
                        ];
                        
                        $this->expensesRepository->create($data);
                    }
              	}
            }
        }
       
           if (is_array($array_update)) {
             foreach ($array_update as $type) {
               switch ($type) {                          

                 case 'WhatsApp':
                   $adminUser = User::where('user_type', 'admin')
                     ->where('company_id', $company->id)
                     ->first();

                   if ($adminUser) {
                     $mobileNo = $adminUser->mobile_number ?? '';
                     $empName = $adminUser->name ?? ''; // More concise null coalescence             

                     $messageContentAdmin = __('messages.service_admin_whatsapp_message', ['event'=>'Service Updated','adminName' => $empName, 'id' => $services->service_id, 'category' => $services->category->service_category]);
                     $messageContentAdmin = str_replace('\n', PHP_EOL, $messageContentAdmin);
                     sendWaMessageToServer($mobileNo, $messageContentAdmin, $company->id);
                   }                         
                   break;                           

               }
             }
           }  

        return $this->sendResponse(new ServiceDataResource($services), 'Services updated successfully');
    }

     /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/material-update/{id}",
     *      summary="updateServices",
     *      tags={"Services"},
     *      description="Update Services",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Services",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *    @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/json",
     *            @OA\Schema(
     *                type="object",
     *                required={"materials"},
     *                @OA\Property(
     *                    property="materials",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Services"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function materialStatus($id, UpdateServicesAPIRequest $request)
    {
        $input = $request->all();
        /** @var Services $services */
        $services = $this->servicesRepository->find($id);

        if (empty($services)) {
            return $this->sendError('Services not found');
        }

        $services = $this->servicesRepository->update($input, $id);

        return $this->sendResponse($services->toArray(), 'Services updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/services/{id}",
     *      summary="deleteServices",
     *      tags={"Services"},
     *      description="Delete Services",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Services",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Services $services */
        $services = $this->servicesRepository->find($id);

        if (empty($services)) {
            return $this->sendError('Services not found');
        }

        $services->delete();

        return $this->sendSuccess('Services deleted successfully');
    }
    private function genServiceCode($length = 6) {
        // Define character set for the service code
        $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    
        // Initialize the service code variable
        $serviceCode = '';
    
        // Generate random characters
        for ($i = 0; $i < $length; $i++) {
            $serviceCode .= $characters[rand(0, strlen($characters) - 1)];
        }
    
        return $serviceCode;
    }
  
  
  	public function sendOTPToCustomer(Request $request)
    {
        $input = $request->all();
        // Validate the input
        $request->validate([
            'service_id' => 'required|integer|exists:services,id',
        ]);

        // Find the service by ID
        $service = Services::find($input['service_id']);
    	
		if (!$service) {
        	return $this->sendError('Service not found.', 404);
        }    

        // Find the customer
        $customer = Customer::find($service->customer_id);

      	if (!$customer) {
        	return $this->sendError('Customer not found.', 404);
      	}

        // Generate OTP
        $otp = rand(1000, 9999); 
        
        $service->otp = $otp;
        $service->save();
        $smsStatus = 0;        
        $response = $this->smsDeliveryService->sendDeliveryOtp($customer->first_name, $customer->contact_number, $otp);      
      	if ($response) {
            $smsStatus = 1; // success
        }
		
        // Store SMS transaction
        $smsTransactionData = [
            'template' => "Delivery OTP",
          	'temp_id' => 1707172810776080232,
            'message' => "Hello!,{$customer->first_name} . Your service Product Delivery OTP is {$otp}",
            'status' => $smsStatus,
            'user_id' => auth()->user()->id, 
            'company_id' => $service->company_id, 
            'sent_date' => Carbon::now()->toDateString(),
            'to' => $customer->contact_number,
            'customer_id' => $customer->id,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];

        \DB::table('sms_transactions')->insert($smsTransactionData);

        if ($response) {
            return $this->sendResponse(['otp' => $otp], 'OTP sent successfully.');
        } else {
            return $this->sendError('Failed to send OTP.', 500);
        }
    }
  	
  	public function verifyOTP(Request $request)
    {
        $input = $request->all();

        // Validate the input
        $request->validate([
            'service_id' => 'required|integer|exists:services,id',
            'otp' => 'required|digits:4',
        ]);

        // Find the service by ID
        $service = Services::find($input['service_id']);

        // Check if the OTP is correct and not expired
        if ($service->otp !== $input['otp']) {
            return $this->sendError('Invalid OTP.', 400);
        }

        
        // If OTP is valid, mark the service as verified or perform desired actions
        $service->otp_verified = true;
        $service->save();

        return $this->sendResponse(['message' => 'OTP verified successfully.'], 'OTP verification successful.');
    }
}
