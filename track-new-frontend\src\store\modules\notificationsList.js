// store/modules/notificationList.js
import axios from "axios";

const state = {
  notification_list: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  };

  const mutations = {
      SET_NOTIFICATIONSLIST(state, { notification_data, pagination}) {        
      // state.notification_list = { data: notification_data, pagination: pagination };
      const currentData = state.notification_list.data || [];
          if (pagination && pagination.current_page > 1) {
              // Append new data if the current page is greater than 1
              state.notification_list = {
                  data: [...currentData, ...notification_data], // Append new data
                  pagination: pagination
              };
          } else {
              // Replace data if it's the first page or no pagination
              state.notification_list = {
                  data: [...notification_data], // Replace with new data
                  pagination: pagination
              };
          }
    },
    // Mutation to update a specific notification at a particular index
    UPDATE_NOTIFICATION_AT_INDEX(state, { index, newNotification }) {
      // Replace the notification at the specific index with the new data
      if (state.notification_list.data && state.notification_list.data[index]) {
        state.notification_list.data.splice(index, 1, newNotification);
        state.notification_list.pagination.all_unread_count -= 1;
      }
    },
      RESET_STATE(state) {
        state.notification_list = {};
        state.lastFetchTime = null;
      state.isFetching = false;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    }
  };

  const actions = {
    updateNotificationName({ commit }, notification_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update notification_list name
      setTimeout(() => {
        // Commit mutation to update notification_list name
        commit('SET_NOTIFICATIONSLIST', notification_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchNotificationList({ state, commit, rootState }, { page, per_page }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['notification_update']; 
  
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)  {
        return; // Skip request if less than 30 seconds have passed since the last request
      }  
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          commit('SET_IS_FETCHING', true);
          axios.get('/notification-lists', { params: { company_id: company_id, page: page, per_page: per_page } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Notification list..!');
              let { notification_data, all_unread_count, current_page, last_page, per_page,total} = response.data; 
              commit('SET_NOTIFICATIONSLIST', { notification_data, pagination: { all_unread_count: all_unread_count, current_page: current_page, last_page: last_page, per_page: per_page, total: total } });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return notification_data;
            })
            .catch(error => {
              // Handle error
              commit('SET_IS_FETCHING', false);
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
     // Action to update a particular notification at a given index
     updateNotificationAtIndex({ commit }, { index, newNotification }) {
      commit('UPDATE_NOTIFICATION_AT_INDEX', { index, newNotification });
  }
    
  };

  const getters = {
    currentNotificationList(state) {
      return state.notification_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
