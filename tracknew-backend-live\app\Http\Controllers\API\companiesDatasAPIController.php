<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreatecompaniesDatasAPIRequest;
use App\Http\Requests\API\UpdatecompaniesDatasAPIRequest;
use App\Models\companiesDatas;
use App\Repositories\companiesDatasRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class companiesDatasController
 * @package App\Http\Controllers\API
 */

class companiesDatasAPIController extends AppBaseController
{
    /** @var  companiesDatasRepository */
    private $companiesDatasRepository;

    public function __construct(companiesDatasRepository $companiesDatasRepo)
    {
        $this->companiesDatasRepository = $companiesDatasRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/companiesDatas",
     *      summary="getcompaniesDatasList",
     *      tags={"companiesDatas"},
     *      description="Get all companiesDatas",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/companiesDatas")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companiesDatas = $this->companiesDatasRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($companiesDatas->toArray(), 'Companies Datas retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/companiesDatas",
     *      summary="createcompaniesDatas",
     *      tags={"companiesDatas"},
     *      description="Create companiesDatas",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/companiesDatas"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreatecompaniesDatasAPIRequest $request)
    {
        $input = $request->all();

        $companiesDatas = $this->companiesDatasRepository->create($input);

        return $this->sendResponse($companiesDatas->toArray(), 'Companies Datas saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/companiesDatas/{id}",
     *      summary="getcompaniesDatasItem",
     *      tags={"companiesDatas"},
     *      description="Get companiesDatas",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of companiesDatas",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/companiesDatas"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var companiesDatas $companiesDatas */
        $companiesDatas = $this->companiesDatasRepository->find($id);

        if (empty($companiesDatas)) {
            return $this->sendError('Companies Datas not found');
        }

        return $this->sendResponse($companiesDatas->toArray(), 'Companies Datas retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/companiesDatas/{id}",
     *      summary="updatecompaniesDatas",
     *      tags={"companiesDatas"},
     *      description="Update companiesDatas",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of companiesDatas",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/companiesDatas"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdatecompaniesDatasAPIRequest $request)
    {
        $input = $request->all();

        /** @var companiesDatas $companiesDatas */
        $companiesDatas = $this->companiesDatasRepository->find($id);

        if (empty($companiesDatas)) {
            return $this->sendError('Companies Datas not found');
        }

        $companiesDatas = $this->companiesDatasRepository->update($input, $id);

        return $this->sendResponse($companiesDatas->toArray(), 'companiesDatas updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/companiesDatas/{id}",
     *      summary="deletecompaniesDatas",
     *      tags={"companiesDatas"},
     *      description="Delete companiesDatas",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of companiesDatas",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var companiesDatas $companiesDatas */
        $companiesDatas = $this->companiesDatasRepository->find($id);

        if (empty($companiesDatas)) {
            return $this->sendError('Companies Datas not found');
        }

        $companiesDatas->delete();

        return $this->sendSuccess('Companies Datas deleted successfully');
    }
}
