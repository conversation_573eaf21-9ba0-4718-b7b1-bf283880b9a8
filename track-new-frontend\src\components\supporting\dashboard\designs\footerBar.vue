<template>
    <footer class="bg-white p-4 py-2 text-sm headBar text-white">
        <div class="flex justify-between items-center">
            <p><span class="text-gray-400">Made with</span><font-awesome-icon icon="fa-solid fa-heart" color="red"
                    size="lg" class="pl-2" /><a href="https://eagleminds.net" target="_blank"
                    class="hover:text-blue-500 hover:underline ml-2">Eagleminds Technologies Pvt Ltd</a></p>
            <p class="py-1 text-center"><span class="text-gray-400">version:</span> v2024.11.16.02</p>
            <!-- <div>
                <button @click="openSupport" class="px-4 py-1 rounded border rounded hover:underline"><font-awesome-icon
                        icon="fa-solid fa-headset" /> Support</button>
            </div> -->
            <support :showModal="show_support" @close-modal="closeSupport"></support>
        </div>
    </footer>
</template>
<script>
import support from '../../dialog_box/support.vue';
export default {
    components: {
        support
    },
    data() {
        return {
            //---support modal--
            show_support: false,
        };
    },
    methods: {
        //----support modal--
        openSupport() {
            this.show_support = true;
        },
        closeSupport() {
            this.show_support = false;
        }
    }
};
</script>