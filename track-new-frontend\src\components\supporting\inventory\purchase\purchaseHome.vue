<template>
    <div :class="{ 'manualStyle text-sm mt-[10px] sm:mb-[60px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        class="custom-scrollbar-hidden" ref="scrollContainer" @scroll="handleScroll">
        <div class="my-custom-margin">
            <!--search-->
            <div v-if="isMobile" class="mt-2 sm:m-4" :class="{ 'mt-1': isMobile }">
                <searchPurchase :isMobile="isMobile" @searchData="searchPurchaseData" @resetData="resetPurchase"
                    :resetData="resetData"></searchPurchase>
            </div>
            <!--new design header-->
            <div v-if="!isMobile" class="flex justify-between mt-5 m-1">
                <div class="flex mr-2 space-x-4">
                    <button @click="addServices" :class="{ 'mr-2': isMobile }"
                        class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center">
                        <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
                        <span v-if="!isMobile" class="text-center lg:inline hidden">New Purchase</span>
                    </button>
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="px-2 py-1 flex-shrink-0 cursor-pointer info-msg border rounded-lg border-gray-500 hover:bg-blue-200"
                            :class="{ 'bg-white': items_category === 'tile' }" @click="toggleView"
                            :title02="`Table view`">
                            <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded-lg flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border border border-gray-500  hover:bg-blue-200"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="`Card view`">
                            <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <!----Filter Model-->
                <!--Setting-->
                <div class="flex icon-color">
                    <div v-if="!open_skeleton && pagination.purchase"
                        class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                        <p class="text-gray-700">Total Purchase
                            :</p>
                        <p class="font-semibold pl-1">
                            {{ pagination.purchase.total }}
                        </p>
                    </div>
                    <!----filter options----->
                    <!-- Main Filter Button -->
                    <div ref="dropdownContainerFilter" class="ml-5 items-center relative">
                        <button @click="toggleMainDropdown" :disabled="data.length == 0"
                            class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                            :class="{ 'cursor-not-allowed': data.length == 0 }">
                            <span class="inline-flex items-center w-full pointer-events-none">
                                <font-awesome-icon icon="fa-solid fa-filter" class="px-1" /> Filter
                                <font-awesome-icon v-if="!isMainDropdownOpen" icon="fa-solid fa-angle-down"
                                    class="pl-3" />
                                <font-awesome-icon v-if="isMainDropdownOpen" icon="fa-solid fa-angle-up" class="pl-3" />
                            </span>
                        </button>
                        <!-- Main Dropdown -->
                        <div v-if="isMainDropdownOpen" ref="mainDropdown"
                            class="absolute mt-2 w-56 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border right-0">
                            <div class="py-1">
                                <!-- Other Options -->
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('by Date')">By Date</button>
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('supplier')">By Supplier</button>
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('serial_number')">By Serial Number</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!---fiter information-->
            <div v-if="Object.keys(filteredBy).length > 0" class="text-xs flex -mb-3  flex-row overflow-auto">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2">
                    <p class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                    <p>{{ key === 'assign_to' ? value.join(', ') : key === 'type' ? typeList[value] : key === 'status' ?
                        statusList[value] : key === 'invoice_to' ? invoice_to[value] : value }}</p>
                </div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton" class="text-sm mt-5 m-1">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                        <!--search bar--->
                        <div>
                            <searchPurchase :isMobile="isMobile" @searchData="searchPurchaseData"
                                @resetData="resetPurchase" :resetData="resetData"></searchPurchase>
                        </div>
                    </div>
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p v-if="column.label !== 'Warehouse Id' && column.label !== 'Supplier Id'">
                                                {{
                                                    column.label }}</p>
                                            <p v-if="column.label === 'Warehouse Id' || column.label === 'Supplier Id'">
                                                {{
                                                    column.label === 'Warehouse Id' ? 'Warehouse' : 'Supplier' }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in data" :key="index"
                                    class="border-b border-gray-400 hover:bg-gray-200 cursor-pointer">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex" @click="startEdit(record)"
                                        :class="{ 'hidden': !column.visible || column.field === 'editing' || column.field === 'items' }"
                                        class="px-2 py-2 table-border">
                                        <!-- Display value instead of input field -->
                                        <span
                                            v-if="column.field !== 'purchase_order_date' && column.field !== 'warehouse_id' && column.field !== 'supplier_id' && column.field !== 'created_at'"
                                            :class="{ 'text-xs': column.field === 'sos' }">{{
                                                record[column.field] }}</span>
                                        <span v-if="column.field === 'created_at'" class="text-xs text-blue-600"
                                            :title="calculateDaysAgo(formattedDate(record.created_at))">
                                            {{ formatDateTime(formattedDate(record.created_at)) }}
                                        </span>

                                        <span v-if="column.field === ' purchase_order_date'">{{ new
                                            Date(record[column.field]).toISOString().split('T')[0] }}</span>
                                        <span v-if="column.field === 'warehouse_id'" class="text-xs">{{
                                            warehouseName(record[column.field])
                                            }}</span>
                                        <span v-if="column.field === 'supplier_id'">{{
                                            supplierName(record['supplier_id'])
                                                &&
                                                Object.keys(supplierName(record['supplier_id'])).length > 0 &&
                                                supplierName(record['supplier_id']).name ?
                                                supplierName(record[column.field]).name :
                                                ''
                                        }}</span>
                                    </td>
                                    <td class="px-2 py-2 text-center table-border">
                                        <div class="flex justify-center items-center">
                                            <div class="flex relative">
                                                <button v-if="!record.editing" @click="startEdit(record)"
                                                    class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-solid fa-pencil"
                                                        style="color: #3b82f6;" />
                                                    <span class="px-2">Edit</span>
                                                </button>
                                                <button v-if="!record.editing && checkRoles(['admin'])"
                                                    @click="confirmDelete(index)"
                                                    class="text-red-500 px-1 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-regular fa-trash-can"
                                                        style="color: #ef4444" />
                                                    <span class="px-2">Delete</span>
                                                </button>
                                                <!-- <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-[80px] absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                    <td>
                                        <button class="text-green-600 text-md font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="addServices">
                                            + Add Purchase
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4 custom-scrollbar-hidden">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <div
                                class="max-w-md mx-auto bg-white rounded-xl border border-gray-300 overflow-hidden md:max-w-2xl shadow-lg">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center p-4 py-1">
                                    <!-- Left Side (Can be your dynamic content) -->
                                    <div class="text-xs text-red-500 cursor-pointer"
                                        :title="formatDateTime(record.created_at)">
                                        <p>{{ calculateDaysAgo(formattedDate(record.created_at)) }}</p>
                                    </div>
                                    <!-- Right Side (Actions) -->
                                    <div class="flex justify-center items-center relative">
                                        <div class="bg-gray-100 rounded-md p-2 items-center">
                                            <p class="text-sm text-gray-500"># {{ record['purchase_order'] }}</p>
                                        </div>
                                        <div class="relative ml-2">
                                            <button @click.stop="displayAction(index)"
                                                class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                            </button>
                                        </div>
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-[90px] right-0 absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700 text-xs"
                                                aria-labelledby="dropdownDefaultButton">

                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="startEdit(record)"
                                                        class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" size="lg" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                        <span class="px-2">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- Middle Section -->
                                <div class="px-4 py-1">
                                    <!-- Customer Details (Can be your dynamic content) -->
                                    <div class="flex items-center mb-2 -mt-4">
                                        <div class="h-10 w-10 bg-blue-500 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                            :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                            {{ supplierName(record['supplier_id']) &&
                                                Object.keys(supplierName(record['supplier_id'])).length > 0 &&
                                                supplierName(record['supplier_id']).name ?
                                                supplierName(record['supplier_id']).name[0].toUpperCase() : '' }}
                                        </div>
                                        <div>
                                            <h4 class="text-sm leading-6 font-medium text-gray-900 mb-1 cursor-pointer"
                                                @click="startEdit(record)">
                                                {{ supplierName(record['supplier_id']) &&
                                                    Object.keys(supplierName(record['supplier_id'])).length > 0 &&
                                                    supplierName(record['supplier_id']).name ?
                                                    supplierName(record['supplier_id']).name : '' }}</h4>
                                            <p class="text-sm text-gray-500 cursor-pointer"
                                                @click="dialPhoneNumber(supplierName(record['supplier_id']) &&
                                                    Object.keys(supplierName(record['supplier_id'])).length > 0 &&
                                                    supplierName(record['supplier_id'])['contact_number'] ? supplierName(record['supplier_id'])['contact_number'] : '')">
                                                +91-{{
                                                    supplierName(record['supplier_id']) &&
                                                        Object.keys(supplierName(record['supplier_id'])).length > 0 &&
                                                        supplierName(record['supplier_id'])['contact_number'] ?
                                                        supplierName(record['supplier_id'])['contact_number'] : '' }}</p>
                                        </div>
                                    </div>

                                    <!-- Invoice Details (Should iterate over your data) -->
                                    <!-- <div class="grid grid-cols-2 gap-2 mb-1">
                                <div class="bg-gray-100 rounded-md p-2 items-center">
                                    <p class="text-sm text-gray-700 font-semibold">Purchase No: </p>
                                    <p class="text-sm text-gray-500">{{ record['purchase_order'] }}</p>
                                </div>
                                <div class="bg-gray-100 rounded-md p-2 items-center">
                                    <p class="text-sm text-gray-700 font-semibold">Invoice Date: </p>
                                    <p class="text-sm text-gray-500">{{ formatDateTime(record['purchase_order_date']) }}
                                    </p>
                                </div>
                            </div> -->
                                    <!--due intervals-->
                                    <div class="flex justify-end px-1 cursor-pointer" @click="startEdit(record)">
                                        <p>Due Intervals:</p>
                                        <p class="pl-1 text-red-500">{{ record['due_interval'] }}</p>
                                    </div>
                                    <!-- Invoice Actions (Can be your dynamic actions) -->
                                    <div class="flex justify-between bg-gray-100 rounded-md p-3 py-1 cursor-pointer"
                                        @click="startEdit(record)">
                                        <div>
                                            <p class="sm:text-xs lg:text-sm text-sm text-gray-700 font-semibold">Grand
                                                Total:
                                            </p>
                                            <p class="text-lg text-gray-900 font-semibold">{{ currentCompanyList &&
                                                currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }} {{ record['total'] }}</p>
                                        </div>
                                        <div class="flex items-center">
                                            <span
                                                class="px-3 py-1 rounded-full sm:text-xs lg:text-sm text-sm font-medium mr-2"
                                                :class="{ 'bg-green-100 text-green-800': record['balance_amount'] === 0, 'bg-red-100 text-red-800': record['balance_amount'] !== 0 }">
                                                {{ record['balance_amount'] === 0 ? 'Paid' : 'Due' }}</span>
                                            <!--paid total-->
                                            <div class="block ml-3">
                                                <p class="sm:text-xs lg:text-sm text-sm">Paid:</p>
                                                <p class="text-green-700">{{ currentCompanyList &&
                                                    currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                    currentCompanyList.currency }} {{ record['paid']
                                                    }}</p>
                                            </div>
                                            <!--balance amount-->
                                            <div class="block ml-3">
                                                <p class="sm:text-xs lg:text-sm text-sm">Balance:</p>
                                                <p class=text-red-500>{{ currentCompanyList &&
                                                    currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                    currentCompanyList.currency }} {{ record['balance_amount'] }}</p>
                                            </div>
                                            <!-- <button @click="printRecord(record)"
                                        class="px-4 py-2 rounded-md text-sm font-medium bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                        View Details
                                    </button> -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && this.pagination.purchase.last_page > 0 && this.pagination.purchase.last_page == this.pagination.purchase.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>


                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="pagination.purchase && !isMobile">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.purchase.current_page - 1) * pagination.purchase.per_page) + 1 }} to
                            {{ Math.min(pagination.purchase.current_page * pagination.purchase.per_page,
                                pagination.purchase.total) }} of
                            {{ pagination.purchase.total }} entries
                        </p>

                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none overflow-auto">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px] flex">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span>
                                    </button>
                                </li>
                                <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                            pageNumber
                                        }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': pagination.purchase.current_page === pagination.purchase.last_page, 'bg-teal-600 hover:bg-teal-500': pagination.purchase.current_page !== pagination.purchase.last_page }">
                                    <button @click="updatePage(pagination.purchase.current_page + 1)"
                                        :disabled="pagination.purchase.current_page === pagination.purchase.last_page"
                                        class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs flex items-center">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!---in mobile view Filter Service-->
        <div v-if="isMobile" ref="dropdownContainerFilter">
            <div class="fixed bottom-36 right-5 z-50 bg-green-700 rounded-full">
                <div class="flex justify-end">
                    <button @click="toggleMainDropdown" type="button"
                        class="flex items-center justify-center px-[10px] py-2 text-white "
                        :class="{ 'cursor-not-allowed blur-sm': data.length == 0 }" :disabled="data.length == 0">
                        <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                    </button>
                </div>
            </div>
            <!-- Main Dropdown -->
            <div v-if="isMainDropdownOpen" ref="mainDropdown"
                class="fixed bottom-48 sm:bottom-48  left-full transform -translate-x-full w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border">
                <div class="py-1">
                    <!-- Other Options -->
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('by Date')">By Date</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('supplier')">By Supplier</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('serial_number')">By Serial Number</button>
                </div>
            </div>
        </div>
        <!---in mobile view create new sale-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="addServices" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'purchase'"></bottombar> -->
        <purchaseFilter :showModal="purchase_filter" @closeFilter="closeLeadFilter" :selectedByValue="selectedByValue">
        </purchaseFilter>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import axios from 'axios';
import confirmbox from '../../dialog_box/confirmbox.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
// import bottombar from '../../dashboard/bottombar.vue';
import searchPurchase from './searchPurchase.vue';
import purchaseFilter from '../../dialog_box/filter_Modal/purchaseFilter.vue';
import { mapGetters, mapActions } from 'vuex';
import { logError } from 'ckeditor5';
export default {
    name: 'inventory_home',
    emits: ['updateIsOpen'],
    components: {
        confirmbox,
        dialogAlert,
        // bottombar,
        searchPurchase,
        purchaseFilter
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean

    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            length_category: null,
            serviceCategories: null,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataList: [],
            columns: [],
            data: [],
            originalData: [],
            supplier_list: [],
            warehouse_list: [],
            isImportModalOpen: false,
            open_message: false,
            message: '',
            //---added new--
            open_purchase: false,
            type: 'add',
            editRecord: null,
            //--serach bar
            searchQuery: '',
            showSuggestions: false,
            selectedIndex: 0,
            //---api integration--
            companyId: null,
            userId: null,
            pagination: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 4,
            number_of_rows: 10,
            gap: 5,
            now: null,
            open_loader: false,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            now: null,
            open_skeleton_isMobile: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---table data filter asending and decending----
            is_filter: false,
            //---filter design--
            purchase_filter: false,
            selectedByValue: null,
            isMainDropdownOpen: false,
            filteredBy: {},
            resetData: false,
        };
    },
    computed: {
        ...mapGetters('purchaseList', ['currentPurchaseList']),
        ...mapGetters('supplier', ['currentSupplier']),
        ...mapGetters('warehouse', ['currentWarehouse', 'currentWarehousePagination']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        paginatedData() {
            // console.log(this.data, '..sgkdgkdogk');
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data;
                this.length_category = filteredData.length;

                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];
            const order = ['created_at', 'purchase_order_date', 'purchase_order', 'supplier_id', 'total', 'paid', 'balance_amount', 'due_interval', 'sos', 'return_amount', 'payment_type', 'warehouse_id'];
            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };
            // console.log(this.data, 'WWWWWhfhfhfhf');
            // Iterate over the first record in data to get field names
            if (this.data && this.data.length !== 0) {
                for (const key of order) {
                    // if (this.data && this.data[0].hasOwnProperty(key) && key !== 'id' && key !== 'product_id' && key !== 'barcode_id') {
                    const label = formatLabel(key);
                    if (key !== 'id' && key !== 'purchase_items' && key !== 'company_id' && key !== 'updated_at' && key !== 'deleted_at' && key !== 'purchase_order_date' && key !== 'return_amount' && key !== 'payment_type') {
                        fields.push({ label, field: key, visible: true });
                    } else {
                        fields.push({ label, field: key, visible: false });
                    }
                    // }
                }
                this.columns = fields;
                return fields;
            }
            // if (this.data) {
            //     for (const key in this.data[0]) {
            //         if (key !== 'id' && key !== 'purchase_items' && key !== 'company_id' && key !== 'created_at' && key !== 'updated_at' && key !== 'deleted_at') { // Exclude the 'id' field
            //             const label = formatLabel(key);
            //             fields.push({ label, field: key, visible: true });
            //         }
            //     }
            //     this.columns = fields;
            //     return fields;
            // } else {
            //     return
            // }
        },
        filteredPurchase() {
            // console.log('it is executed..!!', this.productData);
            // Filter customers based on search query
            if (this.searchQuery !== '') {
                const query = this.searchQuery.toLowerCase();
                return this.data.filter(product => {
                    const productCode = product.purchase_order.toLowerCase();

                    return productCode.includes(query);
                });
            } else {
                return this.productData;
            }
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.purchase.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        }
    },
    mounted() {
        this.fetchApiUpdates();
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.fetchCompanyList();
        //---get warhouse list--
        if (this.currentWarehouse && this.currentWarehouse.length > 0) {
            this.warehouse_list = this.currentWarehouse;
            this.pagination.warehouse = this.currentWarehousePagination;
            this.fetchWarehouseList();
        } else {
            this.fetchWarehouseList();
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        //---get supplier list---
        if (this.currentSupplier && this.currentSupplier.length > 0) {
            this.supplier_list = this.currentSupplier;
            this.fetchISupplierList();
        } else {
            this.fetchISupplierList();
        }
        //--get purchase lsit--
        if (this.currentPurchaseList && this.currentPurchaseList.data && this.currentPurchaseList.data.length > 0) {
            this.open_skeleton = true;
            this.getInitialStoreData(this.currentPurchaseList);
            this.fetchPurchaseList({ page: 1, per_page: this.recordsPerPage });
        } else {
            if (this.currentPurchaseList && Object.keys(this.currentPurchaseList).length == 0) {
                this.open_skeleton = true;
                this.fetchPurchaseList({ page: 1, per_page: this.recordsPerPage });
            }
        }
        // this.getPurchaseList(1, this.recordsPerPage);
        // this.getSupplierList(1, 'all');
        // this.getWarehouseList(1, 'all');

        const view = localStorage.getItem('purchase_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        if (this.isMobile) {
            this.items_category = 'list';
        }
        //---sortIcons---
        const initialShortVisible = ['created_at', 'purchase_order', 'balance_amount', 'due_interval'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
    },
    methods: {
        ...mapActions('purchaseList', ['fetchPurchaseList']),
        ...mapActions('supplier', ['fetchISupplierList']),
        ...mapActions('warehouse', ['fetchWarehouseList']),
        ...mapActions('localStorageData', ['validateRoles', 'fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        getPurchaseList(page, per_page) {
            if (page == 1) {
                this.fetchPurchaseList({ page, per_page });
                if (this.currentPurchaseList && this.currentPurchaseList.data) {
                    this.data = this.currentPurchaseList.data;
                    this.pagination.purchase = this.currentPurchaseList.pagination;
                }
            } else {
                this.open_skeleton = true;
                //---get list data
                axios.get('/purchase_orders', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        // console.log(response.data);
                        this.open_skeleton = false;
                        this.data = response.data.data;
                        this.originalData = this.data;
                        this.pagination.purchase = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error get purchase', error);
                        this.open_skeleton = false;
                    })
            }
        },
        getSupplierList(page, per_page) {
            //---get list supplier--
            axios.get('/suppliers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data);
                    this.supplier_list = response.data.data;
                    this.pagination.supplier = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        getWarehouseList(page, per_page) {
            //---get warehouse list--
            axios.get('/warehouses', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data);
                    this.warehouse_list = response.data.data;
                    this.pagination.warehouse = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error get purchase', error);
                })
        },
        //---warehose name---
        warehouseName(id) {
            if (this.warehouse_list && this.warehouse_list.length > 0) {
                let findData = this.warehouse_list.find(opt => opt.id === id);
                if (findData) {
                    return findData.name;
                }
            }
        },
        //---supplier name---
        supplierName(id) {
            // console.log(this.supplier_list, 'Supplier', id);
            let findData = this.supplier_list.find(opt => opt.id === id);
            // console.log(findData, 'sSSS');
            if (findData) {
                return findData;
            }
        },
        //--supllier--
        goToSupplier() {
            this.$router.push({ name: 'supplier' })

        },
        goToProduct() {
            // this.$router.push('/purchaseOrder');
            this.$router.push('/inventory');
        },
        goToWarehouse() {
            // this.$router.push('/purchaseOrder/warehouse');
            this.$router.push({
                name: 'warehouse',
            });
        },
        updatePage(pageNumber) {
            // console.log(this.pagination.from, 'ooo', pageNumber >= this.pagination.from, 'hello', pageNumber <= this.pagination.last_page, 'Page', pageNumber);
            if (pageNumber >= 1 && pageNumber <= this.pagination.purchase.last_page) {
                this.currentPage = pageNumber;
                // this.paginateRequstAndResponse();
            }
        },

        // Start editing a record
        startEdit(record) {

            this.$router.push({ name: 'add_purchase_order', query: { type: 'edit', po: record.id } });
        },

        //---delete the record
        deleteRecord() {
            this.open_loader = true;
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                let indexToDelete = this.deleteIndex;
                axios.delete(`/purchase_orders/${this.data[indexToDelete].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        this.updateKeyWithTime('purchase_update');
                        // console.log(response.data, 'deleted data...');
                        this.data.splice(indexToDelete, 1);
                        this.open_loader = false;
                        this.message = 'Purchase deleted successfully..!';
                        this.show = true;
                    })
                    .catch(error => {
                        console.error('Error delete', error);
                        this.open_loader = false;
                    })

            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        addServices() {
            // this.open_purchase = true; //---purchase open
            // this.editRecord = null;
            this.$router.push({ name: 'add_purchase_order', query: { type: 'add' } });

            // Navigate to the last page
            this.currentPage = this.totalPages;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            try {
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //----print prechecklist
        formattedString(listData) {
            let returnData = Object.entries(listData)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ');
            return returnData;
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //----import model box--
        checkForDuplication(recordToCheck) {
            return this.data.some(record => (
                record.product_code === recordToCheck.product_code ||
                record.product_name === recordToCheck.product_name
            ));
        },
        openImportModal() {
            this.isImportModalOpen = true;
        },
        closeImportModal(collectedData) {
            if (collectedData) {
                const seenCodes = new Set();
                const uniqueData = collectedData.filter(record => {
                    const key = `${record.product_code}_${record.product_name}`;
                    if (seenCodes.has(key)) {
                        // alert(`Duplicate entry for Product Code: ${record.product_code} and Product Name: ${record.product_name}`);
                        this.open_message = true;
                        this.message = `Duplicate entry for Product Code: ${record.product_code} and Product Name: ${record.product_name}`;
                        return false;
                    }
                    seenCodes.add(key);
                    return true;
                });
                // console.log(uniqueData, 'What happening the data...!!!!');
                // Append the unique data to the existing data array
                let getDataUnique = uniqueData.filter((opt) => {
                    // console.log(!this.checkForDuplication(opt), 'OYYIYIUURTUR');
                    if (!this.checkForDuplication(opt)) {
                        this.data.push({ ...opt, editing: false });
                    }

                });
                // console.log(getDataUnique, 'WWWWWWWWWWWWW');
                this.originalData = this.data;
                this.isImportModalOpen = false;
            } else {
                this.isImportModalOpen = false;
            }
        },
        collectImportedData(collectedData) {
            // Check for duplication based on product code and name
            const isDuplicate = this.checkForDuplication(collectedData);

            if (isDuplicate) {
                // Show alert message for duplication
                // alert('Product code or name is duplicate!');
                this.open_message = true;
                this.message = 'Product code or name is duplicate!';
                return; // Prevent saving if duplicate
            }
            this.data = [...this.data, { ...collectedData, editing: false }];
            this.originalData = this.data;
            // Continue with saving the imported data
            // console.log(collectedData, 'How to achieve..!');
        },
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---add new---
        closePurchase() {
            this.open_purchase = false; //---close purchase
        },
        //----search--
        //----dropdown---
        filterPurchase() {
            if (this.searchQuery !== '' && this.searchQuery.length > 1) {
                // console.log(this.searchQuery, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            } else {
                this.showSuggestions = false;
                this.data = this.originalData
            }
        },
        showDropdown() {
            // console.log('What happening go there data......!!!!!!');
            if (this.searchQuery !== '') {
                // Show dropdown on input focus
                this.showSuggestions = true;
                // Add a click event listener to the document to close the dropdown when clicking outside                
                // document.addEventListener('click', this.handleDocumentClick);
            }
        },
        hideDropdown() {
            // Hide dropdown on input blur
            this.showSuggestions = false;
            // Remove the click event listener when hiding the dropdown
            // document.removeEventListener('click', this.handleDocumentClick);
        },
        selectPurchase(product) {
            // Handle the selected customer (e.g., emit an event, update data, etc.)
            // console.log('Selected product:', product);
            this.searchQuery = product.purchase_order; // Set the input value to the selected customer name
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            // this.$emit('searchData', product); //---filter data
            this.data = [{ ...product }];
            this.selectedIndex = 0;
            // document.removeEventListener('click', this.handleDocumentClick); // Remove the event listener after selection
        },
        handleDocumentClick(event) {
            // Close the dropdown when clicking outside the input and dropdown
            const isClickInside = this.$refs.searchInput.contains(event.target) || this.$refs.searchInput.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.hideDropdown();
            }
        },
        isNumeric(str) {
            // Helper method to check if a string is numeric
            return /^\d+$/.test(str);
        },
        //--on press enter key--
        handleEnterKey() {
            // Check if filteredProductList has at least one item
            if (this.data.length > 0) {
                // Call selectedPurchaseData with the first item in filteredPurchaseList
                this.selectPurchase(this.data[this.selectedIndex]);
                this.$refs.searchInput.blur();
                this.showSuggestions = false;

            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //---get pageination data
        // paginateRequstAndResponse() {
        //     axios.get('/purchase_orders', { params: { company_id: this.companyId, page: this.currentPage, per_page: this.recordsPerPage  } })
        //     .then(response => {
        //         console.log(response.data);
        //         this.data = response.data.data;
        //         this.originalData = this.data;
        //         this.pagination. = response.data.pagination;
        //         // console.log(this.data, ' sss');
        //     })
        //     .catch(error => {
        //         console.error('Error get purchase', error);
        //     })
        // }
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;
                if (this.filteredBy.serial_no && this.filteredBy.serial_no !== '') {
                    this.open_skeleton_isMobile = false;
                    return;
                }
                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.purchase.last_page > this.pagination.purchase.current_page && !this.open_skeleton_isMobile && this.pagination.purchase.last_page > this.pagination.purchase.current_page) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        if (Object.keys(this.filteredBy).length === 0) {
                            this.loadNextPage(this.pagination.purchase.current_page + 1);
                        } else {
                            this.getPurchaseReport(this.filteredBy, this.pagination.purchase.current_page + 1);
                        }
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            axios.get('/purchase_orders', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination.purchase = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })

        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---get initialize store data
        getInitialStoreData(store_data) {
            this.open_skeleton = false;
            this.data = store_data.data;
            this.originalData = this.data;
            this.pagination.purchase = store_data.pagination;
        },
        //---validate roles--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'purchase' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'purchase', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'purchase' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'purchase', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        closeAllModals() {
            // Close all modals
            this.open_confirmBox = false;
            this.open_message = false;
        },
        //--filter dropdown---
        toggleMainDropdown() {
            this.isMainDropdownOpen = !this.isMainDropdownOpen;
            if (this.isMainDropdownOpen) {
                document.addEventListener('click', this.handleClickOutsideFilter);
            } else {
                // this.showSubDropdown = false;
                document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },

        handleClickOutsideFilter(event) {
            const mainDropdown = this.$refs.dropdownContainerFilter;
            // console.log(event.target, 'Waht happning the data....', mainDropdown.contains(event.target));
            if (mainDropdown && !mainDropdown.contains(event.target)) {
                this.toggleMainDropdown();
                // document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        //--filter option--
        changeFilter(opt) {
            this.filter_option = opt;
            this.toggleMainDropdown();
            if (opt === 'date') {
                this.filter_date = !this.filter_date;
                if (!this.filter_date) {
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutsideDate);
                } else {
                    document.addEventListener('click', this.handleClickOutsideDate);
                }
            }
            if (opt === 'customer') {
                this.toggleFilterSelected('by Customer');
            }
            if (opt === 'supplier') {
                this.toggleFilterSelected('by Supplier');
            }
            if (opt === 'by Date') {
                this.toggleFilterSelected('by Date');
            }
            if (opt === 'serial_number') {
                this.toggleFilterSelected('by Serial');
            }
        },
        toggleFilterSelected(option) {
            // console.log(option, 'GGGGGGGGGGGGGGG');
            this.selectedByValue = option;
            this.purchase_filter = true;
            this.data = this.originalData;
            this.isMainDropdownOpen = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        getPurchaseReport(response_data, page) {
            this.open_loader = true;
            let send_data = {
                from_date: response_data.from,
                to_date: response_data.to,
                per_page: this.recordsPerPage, page: page, supplier_id: response_data.supplier_id ? response_data.supplier_id : '',
                company_id: this.companyId
            };
            axios.get('/purchase_orders', { params: { ...send_data } })
                .then(response => {
                    console.log(response.data, 'Followup Data');
                    this.open_loader = false;
                    this.open_skeleton_isMobile = false;
                    this.data = response.data.data;
                    // this.pagination = response.data.pagination;
                    this.pagination.purchase = response.data.pagination;
                    if (this.data && this.data.length > 0) {
                        this.message = 'Purchase filter successfully...!';
                        this.type_toaster = 'success';
                        this.show = true;
                    } else {
                        this.message = 'Purchase data is empty...!';
                        this.type_toaster = 'warning';
                        this.show = true;
                    }
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                    this.open_skeleton_isMobile = false;
                })
        },
        //--filter
        closeLeadFilter(searchData, bySerial, search) {
            // console.log(searchData, 'What is happening.....!', bySerial);
            if (searchData) {
                this.filteredBy = searchData;
                // console.log(searchData, 'Waht abouit datattata.....!!');
                this.getPurchaseReport(searchData, 1);
                this.currentPage = 1;
            } else if (bySerial && Object.keys(bySerial).length > 0 && !Array.isArray(bySerial)) {
                this.data = [{ ...bySerial }];
                this.filteredBy = { serial_no: search };
            } else if (bySerial && Array.isArray(bySerial)) {
                this.data = [...bySerial];
                this.filteredBy = { serial_no: search };
            }

            this.purchase_filter = false;
        },
        //---reset filter--
        resetTheFilter() {
            this.filteredBy = {};
            this.resetData = !this.resetData;
            this.fetchPurchaseList({ page: 1, per_page: this.recordsPerPage });
            this.currentPage = 1;
        },
        //---sort--
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //--search purchase--
        searchPurchaseData(data) {
            if (data) {
                this.filteredBy = { search: data.purchase_order };
                this.data = [{ ...data }];
            }
        },
        resetPurchase() {
            this.filteredBy = {};
            this.resetData = false;
        },
        //--refresh--
        refreshDataTable() {
            this.resetTheFilter();
        }
    },
    watch: {
        currentPage: {
            deep: true,
            handler(newValue) {
                if (Object.keys(this.filteredBy).length === 0) {
                    this.getPurchaseList(newValue, this.recordsPerPage);
                } else {
                    this.getPurchaseReport(this.filteredBy, newValue);
                }
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    if (Object.keys(this.filteredBy).length === 0) {
                        this.getPurchaseList(1, newValue);
                    } else {
                        this.getPurchaseReport(this.filteredBy, 1);
                    }
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('purchase_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentWarehouse: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.warehouse_list = newValue;
                    this.pagination.warehouse = this.currentWarehousePagination;
                }
            }
        },
        currentSupplier: {
            deep: true,
            handler(newValue) {
                this.supplier_list = newValue;
            }
        },
        currentPurchaseList: {
            deep: true,
            handler(newValue) {
                this.getInitialStoreData(newValue);
                this.open_loader = false;
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchWarehouseList();
                this.fetchISupplierList();
                this.fetchPurchaseList({ page: 1, per_page: this.recordsPerPage });
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    // console.log(newValue, 'New Value data...............');
                    this.is_filter = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    }
}
</script>

<style scoped>
.manualStyle {
    overflow: auto;
    height: 100vh;
}

.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

/* Add these styles */
.w-full.text-center.border-none:focus,
.w-full.text-center.border-none:focus-within {
    border: none;
    /* Remove border on focus or focus within */
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}
</style>