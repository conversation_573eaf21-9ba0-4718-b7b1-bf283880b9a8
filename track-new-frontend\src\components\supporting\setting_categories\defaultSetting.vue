<template>
    <div class="w-full">
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>
        <div v-if="!open_skeleton" class="non-printable">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-2 gap-4 p-1 sm:p-4">
                <!--IS whatsapp ENABLE-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold">Enable whatsApp notifications as default:</label>
                    <font-awesome-icon v-if="companywhatsapp" icon="fa-brands fa-whatsapp"
                        class="text-xl px-2 text-green-800" />
                    <label v-if="companywhatsapp" class="inline-flex items-center cursor-pointer mt-2 ">
                        <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                            @click="toggleSwitch('is_whatsapp')"
                            :class="{ 'bg-blue-600': formData.is_whatsapp, 'bg-gray-200': !formData.is_whatsapp }">
                            <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-6': formData.is_whatsapp, 'translate-x-0': !formData.is_whatsapp }">
                            </div>
                        </div>
                        <span class="pl-3"
                            :class="{ 'text-green-600': formData.is_whatsapp, 'text-red-600': !formData.is_whatsapp }">{{
                                formData.is_whatsapp ? 'Enabled' : 'Disabled' }}</span>
                    </label>
                    <button v-else class="text-red-700 flex justify-center items-center hover:text-red-500 mt-2"
                        @click="navigateToWhatsApp">
                        <font-awesome-icon icon="fa-brands fa-whatsapp" class="text-xl px-2 text-red-800" />Connect
                        WhatsApp</button>
                </div>
                <!--IS sms ENABLE-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold">Enable SMS notifications as default:</label>
                    <label class="inline-flex items-center cursor-pointer mt-2">
                        <font-awesome-icon icon="fa-solid fa-comment-sms" class="text-xl px-2 text-yellow-800" />
                        <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                            @click="toggleSwitch('is_sms')"
                            :class="{ 'bg-blue-600': formData.is_sms, 'bg-gray-200': !formData.is_sms }">
                            <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-6': formData.is_sms, 'translate-x-0': !formData.is_sms }">
                            </div>
                        </div>
                        <span class="pl-3"
                            :class="{ 'text-green-600': formData.is_sms, 'text-red-600': !formData.is_sms }">{{
                                formData.is_sms ? 'Enabled' : 'Disabled' }}</span>
                    </label>
                </div>

                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <h2 class="text-lg font-bold mb-4">Choose Signature Option</h2>

                    <div v-for="(option, index) in signatureOptions" :key="index" class="flex items-center mb-2">
                        <input type="radio" :id="option.id" :value="option.value" v-model="formData.is_signature"
                            class="form-radio text-blue-600 focus:ring-blue-500" />
                        <label :for="option.id" class="ml-2">{{ option.label }}</label>
                    </div>
                </div>

            </div>
            <!-- Save Button - Centered -->
            <button @click="saveData"
                class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 text-lg text-white py-2 px-5 rounded-md mx-auto block hover:bg-green-500 mt-5">
                Save
            </button>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'default',
    emits: ['is-sales-save', 'updatesalesData'],
    props: {
        companyId: String,
        userId: String,
        isMobile: Boolean,
        store_refresh: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            formData: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            is_updated: false,
            signatureOptions: [
                { id: 'noSignature', value: '0', label: 'No Signature' },
                { id: 'withSignature', value: '1', label: 'With Signature' },
                { id: 'computerizeGenerator', value: '2', label: 'Computerize Generator' }
            ]
        };
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('companies', ['fetchCompanyList']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),

        saveData() {
            this.open_loader = true;
            if (Object.keys(this.shareData).length > 0) {
                axios.put(`/invoice_settings/${this.shareData.id}`, { ...this.formData, company_id: this.companyId })
                    .then(response => {
                        console.log(response.data, 'Response update');
                        this.shareData = response.data.data;
                        this.open_loader = false;
                        this.fetchInvoiceSetting(true);
                        this.message = 'Default Setting are updated..!';
                        this.show = true;
                        this.$emit('is-sales-save', false);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            } else {
                this.$emit('updatesalesData');
            }
        },

        filteredDisclaimer(data) {
            if (this.formData.disclaimer !== '') {
                return data.split('\n').filter(line => line.trim() !== '');
            }
        },


        getInitialStoreData(store_data) {
            let parseData = JSON.parse(JSON.stringify(store_data));
            if (parseData && parseData.length > 0) {

                if (parseData.length !== 0) {
                    // let disclaimerMSG = parseData[0].disclaimer.split('\n');
                    this.formData = { is_whatsapp: parseData[0].is_whatsapp, is_sms: parseData[0].is_sms, is_signature: parseData[0].is_signature };
                    this.shareData = { ...parseData[0] };
                    // console.log(get_company_data, 'What happeniggg.......');                         
                }
            }

            this.open_skeleton = false;
        },
        //--is sms and whatsapp enble set as default---
        toggleSwitch(key) {
            if (key) {
                this.formData[key] = this.formData[key] == 0 ? 1 : 0;
            }
        },
        navigateToWhatsApp() {
            this.$router.push({ name: 'whatsappsetting' });
        },
    },
    mounted() {
        if (this.companyId !== null) {
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                this.getInitialStoreData(this.currentInvoice);
                this.open_skeleton = true;
                setTimeout(() => {
                    this.open_skeleton = false;
                }, 300);
                this.fetchInvoiceSetting();
            } else {
                this.open_skeleton = true;
                this.fetchInvoiceSetting();
            }
            this.fetchWhatsappList();
        }
    },
    watch: {

        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.getInitialStoreData(newValue);
                } else {
                    this.open_skeleton = false;
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchInvoiceSetting();
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.saveData();
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.is_updated = false;
                    this.$emit('is-sales-save', newValue);
                }
            }
        }
    }
};
</script>

<style scoped>
input[type="radio"] {
    /* remove standard background appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* create custom radiobutton appearance */
    display: inline-block;
    width: 20px;
    height: 20px;
    padding: 3px;
    /* background-color only for content */
    background-clip: content-box;
    border: 2px solid #bbbbbb;
    background-color: #e7e6e7;
    border-radius: 50%;
}

/* appearance for checked radiobutton */
input[type="radio"]:checked {
    background-color: #05810f;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
        /* Use the actual background color of your content */
        color: black;
        /* Use the actual text color of your content */
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */
    }

    .non-printable {
        display: none;
    }
}
</style>
