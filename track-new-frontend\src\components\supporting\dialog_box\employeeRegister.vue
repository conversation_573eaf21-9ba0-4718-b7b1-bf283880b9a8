<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-3/4 lg:w-1/2 w-full top-0 overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen text-sm"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <!-- <div class="modal-content"> -->
            <div class="flex justify-between items-center  py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-2 text-lg">
                    {{ type == 'edit' ? 'Edit a Employee' : type == 'view' ? 'View a Employee' : 'Register a Employee'
                    }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <!---Profile image-->
            <div class="flex items-center justify-center pl-4 pr-4">
                <div class="relative items-center mt-3" title="Change profile" @click="openFileInput"
                    @mouseover="isHovered = true" @mouseleave="isHovered = false">
                    <img v-if="formValues.avatar" :src="formValues.avatar"
                        class="w-[120px] h-[120px] justify-center border border-gray-300"
                        :class="{ 'filter': isHovered && type !== 'view' }" />
                    <img v-if="!formValues.avatar" :src="upload_profile" class="w-[100px] h-[100px] justify-center"
                        :class="{ 'filter': isHovered }" />
                    <input ref="fileInput" type="file" style="display: none" accept="image/*"
                        @change="handleImageChangeProfile" />
                    <div v-if="type !== 'view'" class="absolute inset-0 flex mt-6 justify-center items-center"
                        v-show="isHovered">
                        <span class="text-gray-500 text-xs font-bold text-center">Change profile</span>
                    </div>
                    <!--loader circle-->
                    <div v-if="circle_loader_photo" class="flex">
                        <CircleLoader :loading="circle_loader_photo"></CircleLoader>
                    </div>
                </div>
            </div>
            <!-- Content based on selected option -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 mt-3">
                <!--name-->
                <div class="p-1 pl-4 pr-4">
                    <label for="name" class="font-bold">Name<span class="text-red-700">*</span></label>
                    <input id="name" v-model="formValues.name" type="text" ref="first_name"
                        placeholder="Enter full name" class="p-1 mt-1 border border-gray-300 w-full"
                        :readonly="type === 'view'" />
                </div>
                <!--Mobile-->
                <div class="p-1 pl-4 pr-4">
                    <label for="mobile_number" class="font-bold">Phone Number<span class="text-red-700">*</span></label>
                    <input id="mobile_number" v-model="formValues.mobile_number" type="tel"
                        placeholder="Enter contact number" @input="validatePhoneNumber(formValues.mobile_number)"
                        class="p-1 mt-1  border border-gray-300 w-full" :readonly="type === 'view'" />
                    <span v-if="validationMessage !== ''" class="block text-xs text-red-500">{{ validationMessage
                        }}</span>
                </div>
                <!-- User name or Email -->
                <!-- <div class="p-1 pl-4 pr-4">
                    <label class="block font-bold">Create user name<span class="text-red-700">*</span></label>
                    <input type="text" v-model="formValues.user_name" class="mt-1 p-1 border border-gray-300 w-full"
                        placeholder="Enter user name" :readonly="type === 'view'" />
                </div> -->
                <!--Email-->
                <div class="p-1 pl-4 pr-4">
                    <label for="email" class="font-bold">Email <span class="text-red-700">*</span></label>
                    <input id="email" v-model="formValues.email" type="email"
                        class="p-1 mt-1  border border-gray-300 w-full" :readonly="type === 'view'" />
                </div>

                <!-- Password -->
                <div class="p-1 pl-4 pr-4">
                    <label class="block font-bold">Password <span class="text-red-700">*</span></label>
                    <input type="password" v-model="formValues.password" class="mt-1 p-1 border border-gray-300 w-full"
                        placeholder="Enter password" :readonly="type === 'view'" />
                    <span v-if="formValues.password && formValues.password.length < 7"
                        class="text-xs text-red-700">Please enter maximum 8 characters</span>
                </div>

                <!-- Confirm Password -->
                <div class="p-1 pl-4 pr-4">
                    <label class="block font-bold">Confirm Password</label>
                    <input type="password" v-model="formValues.confirm_password"
                        class="mt-1 p-1 border border-gray-300 w-full" placeholder="Enter confirm password"
                        :readonly="type === 'view'" />
                    <span v-if="formValues.password && formValues.password !== formValues.confirm_password"
                        class="text-red-600 text-xs">
                        {{ formValues.confirm_password ? 'Please enter valid confirm password' :
                            'Please enter confirm password' }}</span>
                </div>

                <!-- Role -->
                <div class="p-1 pl-4 pr-4">
                    <label class="block font-bold">Role<span class="text-red-700">*</span></label>
                    <div class="flex justify-center items-center">
                        <select v-model="formValues.role_id"
                            class="mt-1 p-1 border border-gray-300 w-full rounded-tr-none rounded-br-none h-full"
                            :disabled="type === 'view'">
                            <option v-for="(opt, i) in roles_list" :key="i" :value="opt.id">{{ opt.name }}</option>
                        </select>
                        <!-- <button @click="roleOpen"
                            class="px-2 border font-bold text-green-700 text-xl mt-1 hover:border-green-700">+</button> -->
                    </div>
                </div>

                <!--status-->
                <div class="p-1 pl-4 pr-4">
                    <label for="status" class="font-bold">Status<span class="text-red-700">*</span></label>
                    <select for="status" v-model="formValues.status" class="w-full py-1 border border-gray-300 pt-1"
                        :disabled="type === 'view'">
                        <option value=0>&#x1F5D9; Deactive</option>
                        <option value=1>&#x2611; Active</option>
                    </select>
                </div>

                <!--DOB-->
                <div class="p-1 pl-4 pr-4">
                    <label for="dob" class="font-bold">DOB</label>
                    <input id="dob" v-model="formValues.dob" type="date" v-datepicker
                        class="p-1 mt-1  border border-gray-300 w-full" :readonly="type === 'view'" />
                </div>


                <!--Address-->
                <div class="p-1 pl-4 pr-4">
                    <label for="address" class="font-bold">Address</label>
                    <textarea id="address" v-model="formValues.address" rows="2"
                        class="p-1 mt-1  border border-gray-300 w-full" :readonly="type === 'view'"></textarea>
                </div>
                <!--Total experience-->
                <div class="p-1 pl-4 pr-4">
                    <label for="experience" class="font-bold">Select Total Experience in year</label>
                    <select for="experience" v-model="formValues.total_experience"
                        class="w-full py-2 border border-gray-300 pt-1" :disabled="type === 'view'">
                        <option value="0">Fresher</option>
                        <option value="1">0-1 Year</option>
                        <option value="2">1-2 Year</option>
                        <option value="3">2-3 Year</option>
                        <option value="4">3-4 Year</option>
                        <option value="5">4-5 Year</option>
                        <option value="6">More than 5 Year</option>
                    </select>
                </div>
                <!--Skills-->
                <div class=" w-full p-1 pl-4 pr-4 relative"> <!-- Ensure the parent div is relative -->
                    <label for="skills" class="font-bold">Skills</label>
                    <textarea id="notes" v-model="formValues.skills" rows="2"
                        class="p-1 mt-1 border border-gray-300 w-full" :readonly="type === 'view'"></textarea>
                    <!-- <div class="border py-2 px-2 flex flex-wrap relative"
                        :class="{ 'border-blue-300': showOptions === true }">
                        <div v-for="(selectedOption, index) in formValues.skills" :key="index"
                            class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                            {{ selectedOption }}
                            <span @click="removeOption(selectedOption)"
                                class="text-red-500 font-semibold cursor-pointer">x</span>
                        </div>
                        <input v-if="type !== 'view'" type="text" ref="searchModel" v-model="search"
                            @input="filterOptions" @focus="showOptions = true" @blur="hideOptions"
                            @keydown.enter="handleEnterKey()"
                            @keydown.down.prevent="handleDownArrow(filteredOption(skillsList))"
                            @keydown.up.prevent="handleUpArrow(filteredOption(skillsList))"
                            class="h-[35px] mt-1 outline-none border border-gray-300 rounded-lg px-2"
                            :readonly="type === 'view'">
                    </div>
                    <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-[93%] border-t-0 transition-all duration-500 ease-in-out"
                        v-show="showOptions && type !== 'view'" :class="{ 'h-auto': showOptions }">
                        <div v-for="(option, index) in filteredOption(skillsList)" :key="index"
                            class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                            :class="{ 'bg-green-300': selectedIndex === index }" @click="selectOptionMultiple(option)">
                            {{ option }}
                        </div>
                        <button v-if="showAddNew && search.length > 1"
                            class="bg-green-700 text-white hover:bg-green-600 px-3 py-1 rounded-lg"
                            @click="addNewOption()">Add New</button>
                    </div> -->
                </div>
            </div>
            <div class="grid grid-cols-1 gap-4 mt-5">
                <!--Proof-->
                <div class="p-1 pl-4 pr-4 overflow-auto">
                    <label v-if="formValues.proof || proofIs" for="proof" class="font-bold">Upload proof</label>
                    <div class="flex flex-wrap justify-center">
                        <div v-for="(item, index) in formValues.proof" :key="index"
                            class="max-w-xs mx-2 my-4 bg-white rounded-lg overflow-hidden shadow-md">
                            <!-- Card Header -->
                            <div class="bg-gray-200 px-4 py-2">
                                <h3 class="text-lg font-semibold text-gray-700">Proof {{ index + 1 }}</h3>
                            </div>
                            <!-- Card Body -->
                            <div class="p-4">
                                <!-- Name Input -->
                                <div class="mb-4">
                                    <label for="'proofName' + index"
                                        class="block text-gray-700 font-bold mb-2">Name</label>
                                    <input v-model="item.name" type="text" :id="'proofName' + index"
                                        :ref="'proof' + index"
                                        class="text-sm appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        :readonly="type === 'view'" />
                                </div>
                                <!-- Number Input -->
                                <div class="mb-4">
                                    <label for="'proofNumber' + index"
                                        class="block text-gray-700 font-bold mb-2">Number</label>
                                    <input v-model="item.number" type="text" :id="'proofNumber' + index"
                                        class="text-sm appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                        :readonly="type === 'view'" />
                                </div>
                                <!-- Image Display -->
                                <div class="mb-4 relative" v-if="item.url">
                                    <label class="block text-gray-700 font-bold mb-2">Uploaded Image</label>
                                    <img :src="item.url" alt="Uploaded Image" class="w-full h-24 object-contain" />
                                    <button v-if="item.url && item.url !== ''" class="absolute right-0 top-0"
                                        style="color:red" @click="removeAttachmentImage(index)">
                                        <font-awesome-icon icon="fa-solid fa-x" /> <span>Remove</span>
                                    </button>
                                </div>
                                <!-- Image Upload -->
                                <div class="mb-4" v-if="type !== 'view' && !item.url">
                                    <label for="'file' + index" class="block text-gray-700 font-bold mb-2">Upload
                                        Image</label>
                                    <input type="file" :id="'file' + index" accept="image/*"
                                        @change="handleImageChange($event, index)"
                                        class="text-sm appearance-none border rounded w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline" />
                                </div>
                                <!-- Loader -->
                                <div v-if="circle_loader_proof === index" class="flex justify-center mb-4">
                                    <CircleLoader :loading="true"></CircleLoader>
                                </div>
                                <!-- Action Buttons -->
                                <div class="flex justify-between">
                                    <!-- Toggle Visibility Button -->
                                    <button @click="toggleImageVisibility(index)"
                                        class="bg-blue-500 hover:bg-blue-700 text-white py-2 px-4 rounded focus:outline-none focus:shadow-outline"
                                        :class="{ 'grayscale': !item.url, 'grayscale-0': item.url, }">
                                        <font-awesome-icon icon="fa-solid fa-eye" size="lg" />
                                        View
                                    </button>
                                    <!-- Delete Button -->
                                    <button @click="deleteRow(index)"
                                        class="bg-red-500 hover:bg-red-700 text-white py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                                        <font-awesome-icon icon="fa-solid fa-trash-can" size="lg" />
                                        Delete
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- Add Button -->
                    <div class="flex justify-center mt-4">
                        <button v-if="type !== 'view'" @click="addRowImage()"
                            class="bg-green-700 hover:bg-green-600 text-white py-2 px-4 rounded focus:outline-none focus:shadow-outline">
                            Add Proof
                        </button>
                    </div>
                </div>
                <!--notes-->
                <div class="p-1 pl-4 pr-4">
                    <label for="notes" class="font-bold">Notes</label>
                    <textarea id="notes" v-model="formValues.notes" rows="2"
                        class="p-1 mt-1 border border-gray-300 w-full" :readonly="type === 'view'"></textarea>
                </div>
            </div>

            <!-- Buttons -->
            <div class="flex justify-end items-center m-3" :class="{ 'mb-[100px]': isMobile }">
                <button @click="cancelModal"
                    class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  p-1 pl-10 pr-10 py-2 mr-8">{{ type ===
                        'view' ? 'Go Back' : 'Cancel' }}</button>
                <button v-if="type !== 'view'" @click="sendModal"
                    class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white p-1 pl-10 pr-10 mr-8 py-2">{{ type
                        === 'edit' ? 'Update' : 'Save' }}</button>
            </div>
            <!-- </div> -->
        </div>
        <addName :show-modal="showModalProduct" :title="'Add Skill'" :userName="newProduct"
            @close-modal="addNewProduct">
        </addName>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <dialogrole :showModal="dialog_role_open" @close-modal="roleClose" :companyId="companyID" :userId="userId">
        </dialogrole>
        <Loader :showModal="open_loader"></Loader>
        <displayImage :showModal="showImageModal" :showImageModal="showImageModalUrl" @close-modal="closeTheImageModal">
        </displayImage>
    </div>
</template>

<script>
import dialogAlert from './dialogAlert.vue';
import confirmbox from './confirmbox.vue';
import addName from './addName.vue';
import dialogrole from './dialog_role.vue';
import displayImage from './displayImage.vue';
import { mapGetters, mapActions } from 'vuex';
export default {
    name: 'employeeRegister',
    components: {
        dialogAlert,
        confirmbox,
        addName,
        dialogrole,
        displayImage
    },
    props: {
        showModal: Boolean,
        editData: Object,
        type: String,
        user_name: String,
    },
    data() {
        return {
            isMobile: false,
            formValues: {},
            isMessageDialogVisible: false,
            message: null,
            isOpen: false,
            updatedData: null,
            validationMessage: '',
            validationMessageAlter: '',
            proofIs: false,
            //-----
            showOptions: false,
            skillsList: [],
            search: '',
            showAddNew: false,
            showImageModal: false,
            showImageModalUrl: '',
            deleteIndex: null,
            open_confirmBox: false,
            showModalProduct: false,
            newProduct: '',
            selectedIndex: 0,
            isHovered: false,
            upload_profile: '/images/setting_page/profile.png',
            //---api integration--
            companyID: null,
            userId: null,
            roles_list: [],
            circle_loader_photo: false,
            circle_loader_proof: false,
            delete_type: null,
            //---role--
            dialog_role_open: false,
            open_loader: false,

        }
    },
    methods: {
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        roleOpen() {
            this.dialog_role_open = true;
        },
        roleClose(data) {
            // console.log(data, 'Waht happening rols list..!!!!')
            if (data && data.id) {
                this.roles_list.push(data);
            }
            this.dialog_role_open = false;

        },
        //--mobile number validation--
        validatePhoneNumber(inputtxt) {
            // console.log(inputtxt, 'TTTTT');
            // Regular expression for phone numbers
            var phoneno = /^(?:0|\+91)?[6-9]\d{9}$/;

            // Check if the input matches the regular expression
            if ((inputtxt + '').match(phoneno)) {
                this.validationMessage = '';

                return true; // Phone number is valid
            } else {
                // alert("Invalid phone number format. Please enter a valid phone number.");
                // this.message = "Invalid phone number format. Please enter a valid phone number.";
                // this.open_message = true;

                this.validationMessage = 'Enter valid mobile number';

                return false; // Phone number is invalid
            }
        },
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
                this.proofIs = false;
            }, 300);
        },
        sendModal() {
            if (this.formValues.name && this.formValues.mobile_number && this.validatePhoneNumber(this.formValues.mobile_number) && ((this.formValues.password && this.formValues.password.length > 7) || this.type === 'edit') && this.formValues.email && this.formValues.role_id && this.formValues.status) {
                this.open_loader = true;
                let send_data = this.formValues;
                if (this.type === 'edit') {
                    if (send_data.proof) {
                        send_data.proof = JSON.stringify(send_data.proof);
                    }
                    if (send_data.status && typeof send_data.status === 'string') {
                        send_data.status = Number(send_data.status);
                    }
                    if (send_data.roles) {
                        delete send_data.roles;
                    }
                    if (send_data.total_experience && typeof send_data.total_experience === 'string') {
                        send_data.total_experience = Number(send_data.total_experience);
                    }
                    // if (send_data.skills) {
                    //     send_data.skills = send_data.skills.join(', ');
                    // }
                    axios.put(`/employees/${send_data.id}`, { ...send_data, company_id: this.companyID })
                        .then(response => {
                            // console.log(response.data.data);
                            this.updatedData = response.data.data;
                            this.open_loader = false;
                            this.openMessageDialog(response.data.message);
                            this.formValues = {};
                            this.proofIs = false;
                        })
                        .catch(error => {
                            console.error('Error Post Employee', error);
                            this.open_loader = false;
                            this.updatedData = null;
                            this.openMessageDialog(error.response.data.message);
                            if (send_data.proof) {
                                this.formValues.proof = JSON.parse(this.formValues.proof);
                            }
                        })

                } else {
                    // console.log(send_data, 'What happening...!');
                    if (send_data.proof) {
                        send_data.proof = JSON.stringify(send_data.proof);
                    }
                    // if (send_data.skills) {
                    //     send_data.skills = send_data.skills.join(', ');
                    // }
                    if (send_data.status && typeof send_data.status === 'string') {
                        send_data.status = Number(send_data.status);
                    }
                    if (send_data.total_experience && typeof send_data.total_experience === 'string') {
                        send_data.total_experience = Number(send_data.total_experience);
                    }
                    axios.post('/employees', { ...send_data, company_id: this.companyID })
                        .then(response => {
                            console.log(response.data.data);
                            this.updatedData = response.data.data;
                            this.open_loader = false;
                            this.openMessageDialog(response.data.message);
                            this.formValues = {};
                        })
                        .catch(error => {
                            console.error('Error Post Employee', error);
                            this.updatedData = null;
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                            if (send_data.proof) {
                                this.formValues.proof = JSON.parse(this.formValues.proof);
                            }
                        })

                }
            } else {
                this.updatedData = null;
                this.openMessageDialog(!this.formValues.name ? 'Please fill the name field' : !this.formValues.mobile_number || !this.validatePhoneNumber(this.formValues.mobile_number) ? 'Please enter the valid mobile number' : (!this.formValues.password || !this.formValues.password.length > 7) || this.type === 'edit' ? 'Please enter the valid password' : !this.formValues.email ? 'Please enter the email' : !this.formValues.role_id ? 'Please select the role' : !this.formValues.status ? 'Please select the status' : 'Please fill in all required fields..!');
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                if (this.editData.proof && typeof this.editData.proof === 'string') {
                    this.editData.proof = JSON.parse(this.editData.proof);
                    if (this.editData.proof && typeof this.editData.proof === 'string') {
                        this.editData.proof = JSON.parse(this.editData.proof);
                    }
                }
                // if (this.editData.skills && typeof this.editData.skills === 'string') {
                //     this.editData.skills = this.editData.skills.split(', ');
                // }
                if (this.editData.password) {
                    this.editData.confirm_password = this.editData.password;
                }
                if (this.editData.status && typeof this.editData.status === 'number') {
                    this.editData.status = String(this.editData.status);
                }

                //    console.log(this.editData, 'RRRRRRRRRRRRRRRRRRRRRRRRRRRRRRRR');
                if (this.editData.roles && this.editData.roles.length > 0) {
                    this.editData.role_id = this.editData.roles[0].id;
                }
                this.formValues = this.editData;

            } else {

                this.formValues = {};
            }
        },

        //---open alert--
        openMessageDialog(message) {
            // console.log(message, 'RRRRRRRRRRRRRRRRRRRR');
            this.message = message;
            this.isMessageDialogVisible = true;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.updatedData) {
                this.$emit('close-modal', this.updatedData);
                // this.initializeData();
                this.formValues = {};
            }
            // else {
            //     // this.$emit('close-modal');
            //     // this.initializeData();
            // }
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.first_name;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //-----skills-----
        //-----multiple option---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
        },
        filteredOption(data) {
            // console.log('WWWWW', data);
            if (this.formValues.skills) {
                let findArray = data.filter(option => option.toLowerCase().includes(this.search.toLowerCase()) && !this.formValues.skills.includes(option));
                if (findArray.length > 0) {
                    this.showAddNew = false;
                    return findArray
                }
                else {
                    this.showAddNew = true;
                    return findArray;
                }
            }
            else {
                let findArray = data.filter(option => option.toLowerCase().includes(this.search.toLowerCase()));
                if (findArray.length > 0) {
                    this.showAddNew = false;
                    return findArray
                }
                else {
                    this.showAddNew = true;
                    return findArray;
                }
            }
        },
        selectOptionMultiple(option) {
            // console.log(option, 'What happening...!');
            if (this.formValues.skills) {
                this.formValues.skills.push(option);
            } else {
                this.formValues['skills'] = [option];
            }
            this.search = '';
            this.showOptions = false;
        },
        removeOption(option) {

            this.formValues.skills = this.formValues.skills.filter(selected => selected !== option);
        },
        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
            }, 300); // Add delay to allow click events on options
        },
        addNewOption() {
            this.newProduct = this.search;
            this.showModalProduct = true;
        },
        //----proof--
        toggleImageVisibility(index) {
            if (index !== null && this.formValues.proof[index].url) {
                this.showImageModalUrl = this.formValues.proof[index].url;
                this.showImageModal = true;
                // console.log(index, 'what about ....!', this.formValues);
                // window.open(this.formValues.proof[index].url, '_blank');

            }
        },

        async handleImageChange(event, index) {
            // console.log(event, 'What about event....!')
            const file = event.target.files[0];
            // console.log(file, fields, 'What happening......!');
            // if (index !== null && (!this.formValues.proof[index].url || this.formValues.proof[index].url === '')) {
            //     this.formValues.proof[index].image = file.name
            // }
            // if (file && (!this.formValues.proof[index].url || this.formValues.proof[index].url === '')) {
            //     this.uploadImage(file, index);
            // } else {
            //     this.openMessageDialog('image already exist, Please delete the exist than upload new proof');
            // }
            if (!file) return;

            // Check file size (in bytes)
            const maxSizeBytes = 500 * 1024; // 500kb in bytes
            if (file.size > maxSizeBytes) {
                // Image exceeds 500kb, compress it
                try {
                    this.circle_loader = true; // Show loader
                    const compressedFile = await this.compressImage(file);

                    this.uploadImage(compressedFile, index);
                } catch (error) {
                    console.error("Error compressing image:", error);
                    this.circle_loader = false; // Hide loader on error
                }
            } else {
                // Image is <= 500kb, upload directly
                try {
                    this.circle_loader = true; // Show loader

                    this.uploadImage(file, index);
                } catch (error) {
                    console.error("Error uploading image:", error);
                    this.circle_loader = false; // Hide loader on error
                }
            }
        },
        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Calculate new dimensions while maintaining aspect ratio
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg', // Set desired output mime type
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // Adjust quality as needed (0.7 is 70%)
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        uploadImage(file, index) {
            this.circle_loader_proof = index;
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "Employee");
            formData.append("company_id", this.companyID);
            // Make an API request to Laravel backend
            // console.log(file, 'RRRR');
            axios.post('/image', formData)
                .then(response => {
                    // console.log(response.data, 'What happrning....Q');
                    this.circle_loader_proof = false;
                    //  let data_save = { image_path: response.data.image_path };
                    // this.formValues.attachment = response.data.media_url;
                    if (index !== null) {
                        let data_save = { image_path: response.data.image_path };
                        // console.log(fields, 'Waht about the data...!');
                        if (this.formValues.proof) {
                            if (this.formValues.proof[index]) {
                                this.formValues.proof[index].url = response.data.media_url;
                                // this.formValues[fields.fieldKey][index].image = response.data.media_url;
                            } else {
                                this.formValues.proof[index].url = response.data.media_url;
                                // this.formValues[fields.fieldKey][index].image = response.data.media_url;
                            }
                        } else {
                            if (index === 0) {
                                this.formValues.proof[index].url = response.data.media_url;
                                // this.formValues[fields.fieldKey][index].image = response.data.media_url;
                            }
                        }
                    } else {
                        this.formValues.proof[index].url = response.data.media_url;
                    }
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                });
        },
        confirmDeleteProof() {
            // console.log(this.formValues.proof[this.deleteIndex], 'What happening...!!!!')
            if (this.deleteIndex !== null && this.formValues.proof[this.deleteIndex].url) {
                // console.log(this.formValues.attachment, 'RRRRRR');
                axios.delete('/delete-image', { params: { model: "Employee", image_url: this.formValues.proof[this.deleteIndex].url } })
                    .then(response => {
                        // console.log(response.data, 'delete image..!');
                        if (this.delete_type !== 'url') {
                            this.formValues.proof.splice(this.deleteIndex, 1);
                            this.open_confirmBox = false;
                            this.deleteIndex = null;
                        } else {
                            // this.openMessageDialog(response.data.message);
                            const fileInput = document.getElementById('file' + this.deleteIndex);
                            if (fileInput) {
                                fileInput.value = '';
                            }
                            this.formValues.proof[this.deleteIndex].url = null;
                            this.open_confirmBox = false;
                            this.delete_type = null;
                            this.deleteIndex = null;
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            } else if (this.delete_type !== 'url') {
                this.formValues.proof.splice(this.deleteIndex, 1);
                this.open_confirmBox = false;
                this.deleteIndex = null;
                this.delete_type = null;
            } else {
                this.open_confirmBox = false;
                this.deleteIndex = null;
            }
        },
        //---remove attachment--
        removeAttachmentImage(index) {
            this.deleteIndex = index;
            this.delete_type = 'url';
            this.open_confirmBox = true;

        },

        addRowImage() {
            if (this.formValues.proof) {
                this.formValues.proof.push({ name: '', number: '', image: '', url: '' });
                this.handleFocusAdd(this.formValues.proof.length - 1);
            }
            else {
                this.formValues.proof = [{ name: '', number: '', image: '', url: '' }];
                this.handleFocusAdd(0);
            }
        },
        handleFocusAdd(index) {
            this.$nextTick(() => {
                const inputField = this.$refs['proof' + index][0];
                // console.log(inputField, 'ERRRR');
                if (inputField) {
                    inputField.focus();
                    inputField.click();
                }
            });
        },
        //----delete record--
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null && this.delete_type !== 'url') {
                this.confirmDeleteProof();
            } else {
                this.confirmDeleteProof();
            }

        },

        deleteRow(index) {
            // this.formData.selectedTax.splice(index, 1);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---add new skill--
        closeModal() {
            this.showModalProduct = false;
            this.newProduct = '';
            // this.skillsList.push(this.search);
            // this.formValues.skills.push(this.search);
            this.search = '';
            this.showOptions = false;
            this.showAddNew = false;

            this.$nextTick(() => {
                // console.log(this.$refs, 'EEEEEEE');
                const productNameInput = this.$refs.searchModel;
                if (productNameInput) {
                    productNameInput.focus();

                } else {
                    console.error("searchModel is undefined.");
                }
            });
        },

        addNewProduct(name) {
            if (name) {
                const enteredSkill = name;
                // console.log(enteredSkill, 'Entered product name..!');
                const existingSkill = this.skillsList.find(
                    (skill) => skill.toLowerCase() === enteredSkill.toLowerCase()
                );

                if (!existingSkill && enteredSkill.trim() !== '') {
                    this.skillsList.push(enteredSkill);
                    if (this.formValues.skills) {
                        this.formValues.skills = [...this.formValues.skills, enteredSkill]
                    } else {
                        this.formValues['skills'] = [enteredSkill];

                    }
                    let getLocalData = localStorage.getItem('skills');
                    if (getLocalData) {
                        let parseData = JSON.parse(getLocalData);
                        if (parseData) {
                            parseData.push(enteredSkill);
                            localStorage.setItem('skills', JSON.stringify(parseData));
                        }
                    } else {
                        let data = [...this.skillsList, enteredSkill];
                        localStorage.setItem('skills', JSON.stringify(data));
                    }
                }
            }

            this.closeModal();
        },
        //---on enter key press
        handleEnterKey() {
            // Check if filteredProductList has at least one item
            let findArray = this.filteredOption(this.skillsList);
            if (findArray.length > 0) {
                // Call selectedProductData with the first item in filteredProductList
                this.selectOptionMultiple(findArray[this.selectedIndex]);
                this.selectedIndex = 0;
                this.search = ''
            } else {
                this.addNewOption();
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----Profile image--
        openFileInput() {
            // Trigger a click on the hidden file input
            if (this.type !== 'view') {
                this.$refs.fileInput.click();
            }
        },
        async handleImageChangeProfile(event) {
            const file = event.target.files[0];
            // this.circle_loader_photo = true;
            // if (file) {
            //     this.uploadImageProfile(file);
            // }
            if (!file) return;

            // Check file size (in bytes)
            const maxSizeBytes = 500 * 1024; // 500kb in bytes
            if (file.size > maxSizeBytes) {
                // Image exceeds 500kb, compress it
                try {
                    this.circle_loader_photo = true; // Show loader
                    const compressedFile = await this.compressImage(file);

                    this.uploadImageProfile(compressedFile);
                } catch (error) {
                    console.error("Error compressing image:", error);
                    this.circle_loader_photo = false; // Hide loader on error
                }
            } else {
                // Image is <= 500kb, upload directly
                try {
                    this.circle_loader_photo = true;

                    this.uploadImageProfile(file);
                } catch (error) {
                    console.error("Error uploading image:", error);
                    this.circle_loader_photo = false; // Hide loader on error
                }
            }
        },
        uploadImageProfile(file) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", "Employee");
            formData.append("company_id", this.companyID);
            // Make an API request to Laravel backend
            // console.log(file, 'RRRR');
            axios.post('/image', formData)
                .then(response => {
                    console.log(response.data, 'What happrning....Q');
                    this.circle_loader_photo = false;
                    //  let data_save = { image_path: response.data.image_path };
                    if (this.formValues.avatar && this.formValues.avatar !== '') {
                        this.removeExistAvatar(this.formValues.avatar);
                    }
                    this.formValues.avatar = response.data.media_url;
                })
                .catch(error => {
                    console.error("Error uploading image", error);
                    this.circle_loader_photo = false;
                });
        },
        removeExistAvatar(url) {
            axios.delete('/delete-image', { params: { model: "Employee", image_url: url } })
                .then(response => {
                    console.log(response.data, 'delete image avatar..!');
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },

        userAddNew() {
            // console.log(this.user_name);
            if (/^\d+$/.test(this.user_name)) {
                this.formValues.mobile_number = this.user_name;
            } else {
                if (this.user_name) {
                    this.formValues.name = this.user_name;
                }
            }
        },
        getRolesList() {
            if (this.roles_list.length === 0) {
                axios.get('/roles', { params: { company_id: this.companyID, page: 1, per_page: 'all' } })
                    .then(response => {
                        // console.log(response.data);
                        this.roles_list = response.data.data;
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })

            }

        },
        closeTheImageModal() {
            this.showImageModal = false;
            this.showImageModalUrl = '';
        }
    },
    mounted() {
        this.updateIsMobile();
        // let getSkills = localStorage.getItem('skills');
        // if (getSkills) {
        //     let parseSkills = JSON.parse(getSkills);
        //     if (parseSkills) {
        //         this.skillsList = parseSkills;
        //     }
        // }
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyID = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        // Use nextTick to wait for the DOM to be updated before accessing the input field

        window.addEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.getRolesList();
                    this.handleFocus();
                }
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
        user_name: {
            handler() {
                this.userAddNew();
                // this.handleFocus();
            },
        },
    },
}
</script>
<style scoped>
/* Modal styling */
.model {
    position: fixed;
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
}


.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .model {
        height: 100%;
        /* Adjust the height as needed */
    }

    .modal-content {
        overflow-y: auto;
    }
}

.image-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 20px;
    background: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
}

.filter {
    filter: blur(10px);
    /* Initial blur value */
    transition: filter 0.5s ease-in-out;
    /* Smooth transition */
}

.filter:hover {
    filter: blur(0);
    /* Remove blur on hover */
}
</style>
