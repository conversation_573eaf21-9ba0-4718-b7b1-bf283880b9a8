<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreatePurchaseOrderAPIRequest;
use App\Http\Requests\API\UpdatePurchaseOrderAPIRequest;
use App\Models\PurchaseOrder;
use App\Models\ProductsDetails;
use App\Models\PurchaseOrderPayments;
use App\Repositories\PurchaseOrderRepository;
use App\Repositories\PurchaseOrderItemRepository;
use App\Repositories\ProductsDetailsRepository;
use App\Repositories\PurchaseOrderPaymentsRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\AppBaseController;
use App\Http\Resources\api\PurchaseResource;
use Ramsey\Uuid\Uuid;
use Response;

/**
 * Class PurchaseOrderController
 * @package App\Http\Controllers\API
 */

class PurchaseOrderAPIController extends AppBaseController
{
    /** @var  PurchaseOrderRepository */
    private $purchaseOrderRepository;
  	private $purchaseOrderItemRepository;
  	private $productsDetailsRepository;
  	private $purchaseOrderPaymentRepository;

    public function __construct(PurchaseOrderRepository $purchaseOrderRepo, PurchaseOrderItemRepository $purchaseOrderItemRepo, ProductsDetailsRepository $productsDetailsRepo, PurchaseOrderPaymentsRepository $purchaseOrderPaymentRepo) // Add PurchaseOrderItemRepository parameter
    {
        $this->purchaseOrderRepository = $purchaseOrderRepo;
        $this->purchaseOrderItemRepository = $purchaseOrderItemRepo;
        $this->productsDetailsRepository = $productsDetailsRepo;
      	$this->purchaseOrderPaymentRepository = $purchaseOrderPaymentRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/purchase_orders",
     *      summary="getPurchaseOrderList",
     *      tags={"PurchaseOrder"},
     *      description="Get all PurchaseOrders",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/PurchaseOrder")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        // $purchaseOrders = $this->purchaseOrderRepository->all(
        //     $request->except(['skip', 'limit']),
        //     $request->get('skip'),
        //     $request->get('limit')
        // );

        $companyId = $request->query('company_id');
      
      	$from_date = $request->query('from_date'); 
    			
      	$to_date = $request->query('to_date'); 
      
      	$customer_id = $request->query('supplier_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $purchaseOrdersQuery = PurchaseOrder::where('company_id', $companyId);
      	
      	if ($customer_id !== '' && isset($customer_id)) {                     
          	$purchaseOrdersQuery->where('supplier_id', $customer_id);

        }
      
      	if ($from_date) {
        	$purchaseOrdersQuery->whereDate('updated_at', '>=', $from_date);
      	}

      	if ($to_date) {
        	$purchaseOrdersQuery->whereDate('updated_at', '<=', $to_date);
      	}

        if ($perPage === 'all') {
            $perPage = $purchaseOrdersQuery->count();
        }

      

        $purchaseOrders = $purchaseOrdersQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        
    

        $response = [
            'success' => true,
            'data' => PurchaseResource::collection($purchaseOrders), // Get the paginated items
            'pagination' => [
                'total' => $purchaseOrders->total(),
                'per_page' => $purchaseOrders->perPage(),
                'current_page' => $purchaseOrders->currentPage(),
                'last_page' => $purchaseOrders->lastPage(),
                'from' => $purchaseOrders->firstItem(),
                'to' => $purchaseOrders->lastItem(),
            ],
        ];
        
        return response()->json($response);

       // return $this->sendResponse($purchaseOrders->toArray(), 'Purchase Orders retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/purchase_orders",
     *      summary="createPurchaseOrder",
     *      tags={"PurchaseOrder"},
     *      description="Create PurchaseOrder",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/PurchaseOrder")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrder"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreatePurchaseOrderAPIRequest $request)
    {
        $input = $request->all();     
        // Store each item separately
      
      	$payCode = PurchaseOrderPayments::generatePaymentCode();
      
      	 if (!isset($input['items']) || !is_array($input['items']) || !isset($input['payments']) || !is_array($input['payments'])) {
            return $this->sendError('Invalid input format. Expected arrays for items and payments.');
        }
        $input['created_by'] = auth()->user()->id;
        $input['updated_by'] = auth()->user()->id;
  
        $purchaseOrder = $this->purchaseOrderRepository->create($input); // Create purchase order and get its ID
      	$items = $input['items'];
      	$payments = $input['payments'];
        foreach ($items as $item) {
            $uuid = Uuid::uuid4();
            $item['purchaseorder_id'] = $purchaseOrder->id; // Assign the purchaseorder_id to each item
            $purchaseOrderItem = $this->purchaseOrderItemRepository->create($item); // Store the item
            $product_items['total_qty'] =  $item['total_qty'];
            $product_items['product_id'] =  $item['product_id'];
            $product_items['barcode_id'] =  $item['barcode_id'];
            $product_items['sales_price'] =  $item['total_price'];
            $product_items['company_id'] =  $input['company_id'];
          	$product_items['serial_no'] =  $input['serial_no'] ?? '';
            $product_items['tax_name'] = $input['tax_name'] ?? null;
            $product_items['discount'] = !empty($input['discount']) ? $input['discount'] : 0;
            $product_items['tax_id'] = $input['tax_id'] ?? 0;        
            $product_items['gst_type'] = $input['tax_type'] ?? null;
            $product_items['gst_value'] = $input['tax_value'] ?? 0;
            $product_items['id'] = $uuid->toString();
            $product_items['pd_id'] =$purchaseOrderItem->id; 
          	$product_items['supplier_id'] =$purchaseOrder->supplier_id;    
            $productDetails = $this->productsDetailsRepository->create($product_items);
        }
      
      	 foreach ($input['payments'] as $payment) {
           $payment['created_by'] = $input['created_by'];
            $payment['purchaseorder_id'] = $purchaseOrder->id; // Assign the purchaseorder_id to each payment
           $payment['payment_code'] = $payCode;
          
            $this->purchaseOrderPaymentRepository->create($payment);
        }

        

        return $this->sendResponse(new PurchaseResource($purchaseOrder), 'Purchase Order saved successfully');
    }


    

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/purchase_orders/{id}",
     *      summary="getPurchaseOrderItem",
     *      tags={"PurchaseOrder"},
     *      description="Get PurchaseOrder",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrder",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrder"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var PurchaseOrder $purchaseOrder */
        $purchaseOrder = $this->purchaseOrderRepository->find($id);

        if (empty($purchaseOrder)) {
            return $this->sendError('Purchase Order not found');
        }

        return $this->sendResponse(new PurchaseResource($purchaseOrder), 'Purchase Order retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/purchase_orders/{id}",
     *      summary="updatePurchaseOrder",
     *      tags={"PurchaseOrder"},
     *      description="Update PurchaseOrder",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrder",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/PurchaseOrder")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrder"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdatePurchaseOrderAPIRequest $request)
    {
        $input = $request->all();
        
        $items = $input['items'];
		$payCode = PurchaseOrderPayments::generatePaymentCode();
        /** @var PurchaseOrder $purchaseOrder */
        $purchaseOrder = $this->purchaseOrderRepository->find($id);

        if (empty($purchaseOrder)) {
            return $this->sendError('Purchase Order not found');
        }

        $purchaseOrder = $this->purchaseOrderRepository->update($input, $id);
        
          foreach ($items as $item) {
            $item['purchaseorder_id'] = $purchaseOrder->id; // Assign the purchaseorder_id to each item

            // Check if the item ID exists to determine whether to update or create a new item
            if (isset($item['id'])) {
                // Update existing item
                $purchaseOrderItem = $this->purchaseOrderItemRepository->find($item['id']);

                if ($purchaseOrderItem) {
                    $purchaseOrderItem = $this->purchaseOrderItemRepository->update($item, $item['id']);
                } else {
                    // Handle case where item ID is not found (though it should not happen with a valid ID)
                    return $this->sendError('Purchase Order Item not found');
                }
            } else {
                // Create new item
                $purchaseOrderItem = $this->purchaseOrderItemRepository->create($item);
            }

            if (isset($item['product_id'])) {
                // Find the product by pd_id
                $product = ProductsDetails::where("pd_id", $item['product_id'])->first();

                if ($product) {
                    // Update product details
                    $product->total_qty = $item['total_qty'];
                    $product->product_id = $item['product_id'];
                    $product->barcode_id = $item['barcode_id'];
                    $product->sales_price = $item['total_price'];
                    $product->save();
                }
            }
        }
      
      	$keepPayIds = [];
        foreach ($input['payments'] as $payDatas) {
            if (isset($payDatas['id'])) {
                $keepPayIds[] = $payDatas['id'];
            }
        }
          
        PurchaseOrderPayments::where('purchaseorder_id', $id)->whereNotIn('id', $keepPayIds)
            ->delete();
      	 foreach ($input['payments'] as $payData) {
          	
            $payData['purchaseorder_id'] = $id;
           $payData['created_by'] = auth()->user()->id;
          	
            if(isset($payData['id'])){
              
                $salesPaymentId = $payData['id'];
               	$payData['company_id'] = $purchaseOrder->company_id;
                $payData['created_by'] = auth()->user()->id;
                $this->purchaseOrderPaymentRepository->update($payData, $salesPaymentId);               
            }  
            else{
                $payData['payment_code'] = $payCode;
                $this->purchaseOrderPaymentRepository->create($payData);
                
            }       
            
        }
        return $this->sendResponse(new PurchaseResource($purchaseOrder), 'PurchaseOrder updated successfully');          
    }
  
  	
  	public function processSupplierPayments(UpdatePurchaseOrderAPIRequest $request)
    {
        $input = $request->all();
        $userId = auth()->user()->id;
        $purchaseDataArray = $input['purchases_data'];
        DB::beginTransaction();
        $payCode = PurchaseOrderPayments::generatePaymentCode();
        try {
            foreach ($purchaseDataArray as $purchaseData) {
                $inputData['balance_amount'] = $purchaseData['balance_amount'];
                $purchaseOrderId = $purchaseData['id'];
                $customerId = $purchaseData['supplier_id'];
                $companyId = $purchaseData['company_id'];
    
                $purchaseOrder = $this->purchaseOrderRepository->update($inputData, $purchaseOrderId);
    
                $payIds = [];
                foreach ($purchaseData['payments'] as $payDatas) {
                    if (isset($payDatas['id'])) {
                        $payIds[] = $payDatas['id'];
                    }
                }
                
                // Verify if PurchaseOrderPayments exist before attempting to delete
                $existingPayments = PurchaseOrderPayments::where('purchaseorder_id', $purchaseOrderId)->get();
                if ($existingPayments->isEmpty()) {
                   // throw new \Exception('No existing payments found for PurchaseOrder ID ' . $purchaseOrderId);
                }
    
                PurchaseOrderPayments::where('purchaseorder_id', $purchaseOrderId)
                    ->whereNotIn('id', $payIds)
                    ->delete();
                
                $purchasePaymentDataArray = $purchaseData['payments'];
                foreach ($purchasePaymentDataArray as $payment) {
                    $payment['company_id'] = $companyId;
                    $payment['created_by'] = $userId;
                    $payment['supplier_id'] = $customerId;
                    $payment['purchaseorder_id'] = $purchaseOrderId;
                    $timestamp = strtotime(str_replace('/', '-', $payment['payment_date']));
                    $date = new \DateTime();
                    $date->setTimestamp($timestamp);
                    $payment['payment_date'] = $date->format('Y-m-d\TH:i:s.u\Z');
    
                    if (isset($payment['id'])) {
                        $existingPayment = PurchaseOrderPayments::find($payment['id']);
                        if (!$existingPayment) {
                            throw new \Exception('No query results for model [App\\Models\\PurchaseOrderPayments] ' . $payment['id']);
                        }
                        $this->purchaseOrderPaymentRepository->update($payment, $payment['id']);
                    } else if (isset($payment['payment_type'], $payment['payment_amount'])) {
                        $this->purchaseOrderPaymentRepository->create([
                            'payment_code' => $payCode,
                            'payment_date' => $date->format('Y-m-d\TH:i:s.u\Z'),
                            'payment_type' => $payment['payment_type'],
                            'payment_amount' => $payment['payment_amount'],
                            'payment_notes' => $payment['payment_notes'],
                            'payment_for' => $payment['payment_for'],
                            'purchaseorder_id' => $purchaseOrderId,
                            'company_id' => $companyId,
                            'created_by' => $userId,
                            'supplier_id' => $customerId                      
                        ]);
                    }
                }
            }
            DB::commit();
            return $this->sendResponse(true, 'Supplier payments processed successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('An error occurred while processing supplier payments: ' . $e->getMessage());
        }
    }




    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/purchase_orders/{id}",
     *      summary="deletePurchaseOrder",
     *      tags={"PurchaseOrder"},
     *      description="Delete PurchaseOrder",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrder",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
 
  
  	public function destroy($id)
    {
        // Retrieve the purchase order
        $purchaseOrder = $this->purchaseOrderRepository->find($id);

        if (empty($purchaseOrder)) {
            return $this->sendError('Purchase Order not found');
        }

        // Retrieve all purchase order items associated with the purchase order
        $purchaseOrderItems = $this->purchaseOrderItemRepository->findWhere('purchaseorder_id', $purchaseOrder->id);

        // Loop through each purchase order item and delete corresponding product details
        foreach ($purchaseOrderItems as $purchaseOrderItem) {
            $product = ProductsDetails::where("pd_id", $purchaseOrderItem->id)->first();
            if ($product) {
                $product->delete();
            }

            // Delete the purchase order item
            $this->purchaseOrderItemRepository->delete($purchaseOrderItem->id);
        }

        // Delete purchase order payments
        $purchaseOrderPayments = $this->purchaseOrderPaymentRepository->findBy('purchaseorder_id', $purchaseOrder->id);
        foreach ($purchaseOrderPayments as $payment) {
            $this->purchaseOrderPaymentRepository->delete($payment->id);
        }

        // Delete the purchase order
        $this->purchaseOrderRepository->delete($id);

        return $this->sendSuccess('Purchase Order deleted successfully');
    }


}
