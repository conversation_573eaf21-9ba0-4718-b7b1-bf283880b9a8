<template>
    <div class="amc-container p-6 bg-white rounded-lg shadow-lg">
        <!-- Period Selection Buttons -->
        <div class="period-selection flex sm:justify-center justify-start gap-4 mb-6 overflow-y-auto">
            <button v-for="(label, period) in periodLabels" :key="period" @click="fetchData(period)"
                :class="['py-2 px-4 rounded-md text-white text-nowrap', selectedDay === period ? 'bg-blue-600' : 'bg-blue-500 hover:bg-blue-700']">
                {{ label }}
            </button>
        </div>

        <!-- Data Display Section -->
        <div class="amc-data">
            <div v-if="getAmcData && getAmcData[selectedDay] && getAmcData[selectedDay].data.length > 0"
                class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 2xl:grid-cols-4 gap-4">
                <!-- Render each AMC item dynamically -->
                <div v-for="item in getAmcData[selectedDay].data" :key="item.id"
                    class="border p-2 rounded-lg bg-gray-50 shadow-sm hover:shadow-md transition duration-300">

                    <!-- Title of the service -->
                    <p class="font-semibold text-lg">{{ item.title || 'Untitled' }}</p>

                    <!-- Customer Information -->
                    <p class="text-gray-600">Customer: {{ item.customer.first_name }}</p>
                    <!-- <p class="text-gray-600">Service ID: {{ item.id }}</p> -->
                    <!-- Status -->
                    <p class="text-gray-600">Status:
                        <span class="text-white px-1 py-1 rounded text-xs"
                            :class="{ 'bg-yellow-500': item.status == '0', 'bg-[#0D7CBF]': item.status == '1', 'bg-green-500': item.status == '2', 'bg-[#DA1C1C]': item.status == '3' }">
                            {{ item.status === 0 ? 'Open' : item.status === 1 ?
                                'Progress'
                                : item.status === 2 ? 'Completed' : item.status === 3
                                    ?
                                    'Cancelled' : '' }}
                        </span>
                    </p>

                    <!-- Dates of the service -->
                    <div v-if="item.dates.length > 0" class="mt-2">
                        <p class="text-sm text-gray-400">Scheduled Dates:</p>
                        <ul class="list-disc pl-6">
                            <li v-for="date in item.dates" :key="date.id" class="text-sm text-gray-500">
                                {{ formatDateTime(formattedDate(date.date)) }}
                            </li>
                        </ul>
                    </div>

                    <!-- Additional info can go here -->
                </div>
            </div>
            <div v-else class="text-center text-gray-500">No data available for this period.</div>
        </div>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    data() {
        return {
            selectedDay: 'today',
            periodLabels: {
                today: 'Today',
                tomorrow: 'Tomorrow',
                thisWeek: 'This Week',
                nextWeek: 'Next Week',
                thisMonth: 'This Month',
                nextMonth: 'Next Month',
                thisYear: 'This Year',
                nextYear: 'Next Year',
            },
        };
    },
    computed: {
        ...mapGetters('amcsList', ['getAmcData']),
    },
    methods: {
        ...mapActions('amcsList', ['fetchAmcData']),
        fetchData(period) {
            this.selectedDay = period;
            // Dispatch the action to fetch data for the selected period
            this.fetchAmcData(period);
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---formated display date---
        formattedDate(timestamp) {
            const date = new Date(timestamp);
            date.setUTCHours(date.getUTCHours() + 5); // Add 5 hours for the time zone offset
            date.setUTCMinutes(date.getUTCMinutes() + 30); // Add 30 minutes for the time zone offset
            const formattedDate = date.toISOString().slice(0, 16).replace('T', ' ');
            return formattedDate;
        },
    },
    mounted() {
        if (!(this.getAmcData && this.getAmcData[this.selectedDay] && this.getAmcData[this.selectedDay].data.length > 0)) {
            this.fetchAmcData(this.selectedDay);
        }
    },
    watch: {
        getAmcData: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    // console.log(newValue, 'New value data');
                }
            }
        }
    }
};
</script>

<style scoped>
/* TailwindCSS classes are used, so you can override if necessary */
</style>