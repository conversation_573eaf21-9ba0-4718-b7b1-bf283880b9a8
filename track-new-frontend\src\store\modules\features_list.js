// store/modules/features_list.js
import axios from "axios";

const state = {
  features_list: [],
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  };

  const mutations = {
    SET_FEATURELIST(state, features_listData) {
      state.features_list = features_listData;
    },
    RESET_STATE(state) {
      state.features_list = [];
      state.lastFetchTime = null;
      state.isFetching = false;
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    }
  };

  const actions = {
    updateFeatureListName({ commit }, features_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update features_list name
      setTimeout(() => {
        // Commit mutation to update features_list name
        commit('SET_FEATURELIST', features_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchFeatureList({ state, commit, rootState }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['features_update'];      
     
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)  { 
        return; // Skip request if less than 30 seconds have passed since the last request
      } 
      //---&& state.features_list.length === 0
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          // Set the request status to true (indicating that the request is in progress)
            commit('SET_IS_FETCHING', true);
            await axios.get(`/companies_modules/${company_id}`)
            .then(response => {
              // Handle response
              // console.log(response.data, 'FeatureList list..!');
              let features_list = response.data.modules;             
              commit('SET_FEATURELIST', features_list);
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return features_list;
            })
            .catch(error => {
              // Handle error
              commit('SET_IS_FETCHING', false);
              console.error('Error:', error);
              return error;
            })
            .finally(() => {
              // Set the request status to false (indicating that the request has completed)
              commit('SET_IS_FETCHING', false);
            });          
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
        commit('SET_IS_FETCHING', false);
      }
    },
    async updateFeatureList({ state, commit }) { 
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id !== '' && state.features_list.length === 0) {
          await axios.get(`/companies_modules/${company_id}`)
            .then(response => {
              // Handle response
              // console.log(response.data, 'FeatureList list..!');
              let features_list = response.data.modules;
              commit('SET_FEATURELIST', features_list);
              return features_list;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }
          } catch (error) {
            console.error('Error fetching item list:', error);
            commit('SET_IS_FETCHING', false);
          }

    }
  };

  const getters = {
    currentFeatureList(state) {
      return state.features_list;
    },    
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
