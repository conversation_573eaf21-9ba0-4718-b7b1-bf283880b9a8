# Track New - Frontend Implementation Guide (Vue.js 3)

## Project Structure

```
track-new-frontend/
├── public/
│   ├── index.html
│   ├── firebase-messaging-sw.js
│   └── images/
├── src/
│   ├── components/
│   │   ├── page/
│   │   │   ├── dashboard.vue
│   │   │   ├── login.vue
│   │   │   ├── serviceHome.vue
│   │   │   ├── leads.vue
│   │   │   ├── amcHome.vue
│   │   │   ├── sales.vue
│   │   │   └── ...
│   │   └── supporting/
│   │       ├── dashboard/
│   │       ├── dialog_box/
│   │       ├── customers/
│   │       ├── services/
│   │       └── ...
│   ├── layouts/
│   ├── pages/
│   ├── router/
│   │   └── index.js
│   ├── store/
│   │   ├── index.js
│   │   └── modules/
│   │       ├── auth.js
│   │       ├── dashboard.js
│   │       ├── service.js
│   │       ├── customer.js
│   │       └── ...
│   ├── services/
│   │   └── api.js
│   ├── utils/
│   ├── assets/
│   ├── App.vue
│   ├── main.js
│   ├── bootstrap.js
│   └── firebaseConfig.js
├── package.json
├── vite.config.js
├── tailwind.config.js
└── postcss.config.js
```

## Step 1: Vue.js Project Setup

### 1.1 Create New Vue Project
```bash
npm create vue@latest track-new-frontend
cd track-new-frontend
```

### 1.2 Install Required Dependencies
```bash
# Core dependencies
npm install vue@^3.4.21 vue-router@^4.3.2 vuex@^4.1.0 axios@^1.7.2

# UI and Styling
npm install tailwindcss@^3.4.3 autoprefixer postcss

# Charts and Visualization
npm install chart.js@^4.4.3 vue-chartjs@^5.3.1

# Firebase for notifications
npm install firebase@^11.3.1

# PDF and Excel
npm install jspdf@^1.5.3 jspdf-autotable@^3.5.0 xlsx@^0.18.5

# Date picker
npm install flatpickr@^4.6.13 vue-flatpickr-component@^11.0.5

# QR Code and Barcode
npm install qrcode@^1.5.4 jsbarcode@^3.11.6

# Rich text editor
npm install @ckeditor/ckeditor5-vue@^7.3.0 ckeditor5@^43.3.1

# Icons
npm install @fortawesome/fontawesome-svg-core @fortawesome/free-solid-svg-icons @fortawesome/vue-fontawesome

# Table component
npm install vue3-table-lite@^1.4.0

# File operations
npm install file-saver@^2.0.5 papaparse@^5.5.2

# PWA
npm install vite-plugin-pwa@^0.21.1

# Print functionality
npm install vue-html-to-paper@^2.0.3

# QR Scanner
npm install @zxing/browser@^0.1.5
```

### 1.3 Configure Build Tools

#### vite.config.js
```javascript
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { VitePWA } from 'vite-plugin-pwa'
import { resolve } from 'path'

export default defineConfig({
  plugins: [
    vue(),
    VitePWA({
      registerType: 'autoUpdate',
      workbox: {
        globPatterns: ['**/*.{js,css,html,ico,png,svg}']
      },
      manifest: {
        name: 'Track New',
        short_name: 'TrackNew',
        description: 'Service Management System',
        theme_color: '#ffffff',
        icons: [
          {
            src: 'pwa-192x192.png',
            sizes: '192x192',
            type: 'image/png'
          }
        ]
      }
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 4000,
    host: true
  }
})
```

#### tailwind.config.js
```javascript
module.exports = {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#eff6ff',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
        }
      }
    },
  },
  plugins: [],
}
```

## Step 2: State Management (Vuex)

### 2.1 Main Store Configuration
```javascript
// src/store/index.js
import { createStore } from 'vuex'
import auth from './modules/auth'
import dashboard from './modules/dashboard'
import service from './modules/service'
import customer from './modules/customer'
import lead from './modules/lead'
import amc from './modules/amc'
import sales from './modules/sales'
import estimation from './modules/estimation'
import rma from './modules/rma'

const store = createStore({
  modules: {
    auth,
    dashboard,
    service,
    customer,
    lead,
    amc,
    sales,
    estimation,
    rma
  }
})

export default store
```

### 2.2 Auth Module
```javascript
// src/store/modules/auth.js
import axios from 'axios'

const state = {
  user: null,
  token: localStorage.getItem('track_new_token') || null,
  isAuthenticated: false,
  loading: false
}

const getters = {
  currentUser: state => state.user,
  isAuthenticated: state => state.isAuthenticated,
  authToken: state => state.token,
  isLoading: state => state.loading
}

const actions = {
  async login({ commit }, credentials) {
    commit('SET_LOADING', true)
    try {
      const response = await axios.post('/api/auth/login', credentials)
      const { user, token } = response.data
      
      commit('SET_USER', user)
      commit('SET_TOKEN', token)
      commit('SET_AUTHENTICATED', true)
      
      localStorage.setItem('track_new_token', token)
      localStorage.setItem('track_new_user', JSON.stringify(user))
      
      // Set default authorization header
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.response?.data?.message || 'Login failed')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async register({ commit }, userData) {
    commit('SET_LOADING', true)
    try {
      const response = await axios.post('/api/auth/register', userData)
      const { user, token } = response.data
      
      commit('SET_USER', user)
      commit('SET_TOKEN', token)
      commit('SET_AUTHENTICATED', true)
      
      localStorage.setItem('track_new_token', token)
      localStorage.setItem('track_new_user', JSON.stringify(user))
      
      return response.data
    } catch (error) {
      commit('SET_ERROR', error.response?.data?.message || 'Registration failed')
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async logout({ commit }) {
    try {
      await axios.post('/api/auth/logout')
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      commit('CLEAR_AUTH')
      localStorage.removeItem('track_new_token')
      localStorage.removeItem('track_new_user')
      delete axios.defaults.headers.common['Authorization']
    }
  },

  async refreshToken({ commit, state }) {
    try {
      const response = await axios.post('/api/auth/refresh')
      const { token } = response.data
      
      commit('SET_TOKEN', token)
      localStorage.setItem('track_new_token', token)
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
      
      return token
    } catch (error) {
      commit('CLEAR_AUTH')
      throw error
    }
  },

  initializeAuth({ commit }) {
    const token = localStorage.getItem('track_new_token')
    const user = localStorage.getItem('track_new_user')
    
    if (token && user) {
      commit('SET_TOKEN', token)
      commit('SET_USER', JSON.parse(user))
      commit('SET_AUTHENTICATED', true)
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`
    }
  }
}

const mutations = {
  SET_USER(state, user) {
    state.user = user
  },
  SET_TOKEN(state, token) {
    state.token = token
  },
  SET_AUTHENTICATED(state, status) {
    state.isAuthenticated = status
  },
  SET_LOADING(state, status) {
    state.loading = status
  },
  SET_ERROR(state, error) {
    state.error = error
  },
  CLEAR_AUTH(state) {
    state.user = null
    state.token = null
    state.isAuthenticated = false
    state.error = null
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
```

### 2.3 Service Module
```javascript
// src/store/modules/service.js
import axios from 'axios'

const state = {
  services: [],
  currentService: null,
  serviceCategories: [],
  loading: false,
  pagination: {
    current_page: 1,
    last_page: 1,
    per_page: 15,
    total: 0
  }
}

const getters = {
  allServices: state => state.services,
  currentService: state => state.currentService,
  serviceCategories: state => state.serviceCategories,
  isLoading: state => state.loading,
  pagination: state => state.pagination
}

const actions = {
  async fetchServices({ commit }, params = {}) {
    commit('SET_LOADING', true)
    try {
      const response = await axios.get('/api/services', { params })
      commit('SET_SERVICES', response.data.data.data)
      commit('SET_PAGINATION', {
        current_page: response.data.data.current_page,
        last_page: response.data.data.last_page,
        per_page: response.data.data.per_page,
        total: response.data.data.total
      })
      return response.data
    } catch (error) {
      console.error('Error fetching services:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  async createService({ commit }, serviceData) {
    try {
      const response = await axios.post('/api/services', serviceData)
      commit('ADD_SERVICE', response.data.data)
      return response.data
    } catch (error) {
      console.error('Error creating service:', error)
      throw error
    }
  },

  async updateService({ commit }, { id, data }) {
    try {
      const response = await axios.put(`/api/services/${id}`, data)
      commit('UPDATE_SERVICE', response.data.data)
      return response.data
    } catch (error) {
      console.error('Error updating service:', error)
      throw error
    }
  },

  async deleteService({ commit }, id) {
    try {
      await axios.delete(`/api/services/${id}`)
      commit('REMOVE_SERVICE', id)
    } catch (error) {
      console.error('Error deleting service:', error)
      throw error
    }
  },

  async fetchServiceById({ commit }, id) {
    try {
      const response = await axios.get(`/api/services/${id}`)
      commit('SET_CURRENT_SERVICE', response.data.data)
      return response.data
    } catch (error) {
      console.error('Error fetching service:', error)
      throw error
    }
  }
}

const mutations = {
  SET_SERVICES(state, services) {
    state.services = services
  },
  SET_CURRENT_SERVICE(state, service) {
    state.currentService = service
  },
  SET_SERVICE_CATEGORIES(state, categories) {
    state.serviceCategories = categories
  },
  SET_LOADING(state, status) {
    state.loading = status
  },
  SET_PAGINATION(state, pagination) {
    state.pagination = pagination
  },
  ADD_SERVICE(state, service) {
    state.services.unshift(service)
  },
  UPDATE_SERVICE(state, updatedService) {
    const index = state.services.findIndex(s => s.id === updatedService.id)
    if (index !== -1) {
      state.services.splice(index, 1, updatedService)
    }
  },
  REMOVE_SERVICE(state, id) {
    state.services = state.services.filter(s => s.id !== id)
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
```

## Step 3: Router Configuration

### 3.1 Router Setup
```javascript
// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import store from '@/store'

// Import components
import Login from '@/components/page/login.vue'
import Dashboard from '@/components/page/dashboard.vue'
import ServiceHome from '@/components/page/serviceHome.vue'
import Leads from '@/components/page/leads.vue'
import AmcHome from '@/components/page/amcHome.vue'
import Sales from '@/components/page/sales.vue'
import Customers from '@/components/page/customers.vue'
import Reports from '@/components/page/reports.vue'
import Settings from '@/components/page/setting.vue'

const routes = [
  {
    path: '/login',
    name: 'login',
    component: Login,
    meta: { requiresGuest: true }
  },
  {
    path: '/',
    name: 'dashboard',
    component: Dashboard,
    meta: { requiresAuth: true }
  },
  {
    path: '/services',
    name: 'services',
    component: ServiceHome,
    meta: { 
      requiresAuth: true,
      requiredPermissions: ['view_services']
    }
  },
  {
    path: '/leads',
    name: 'leads',
    component: Leads,
    meta: { 
      requiresAuth: true,
      requiredPermissions: ['view_leads']
    }
  },
  {
    path: '/amc',
    name: 'amc',
    component: AmcHome,
    meta: { 
      requiresAuth: true,
      requiredPermissions: ['view_amc']
    }
  },
  {
    path: '/sales',
    name: 'sales',
    component: Sales,
    meta: { 
      requiresAuth: true,
      requiredPermissions: ['view_sales']
    }
  },
  {
    path: '/customers',
    name: 'customers',
    component: Customers,
    meta: { 
      requiresAuth: true,
      requiredPermissions: ['view_customers']
    }
  },
  {
    path: '/reports',
    name: 'reports',
    component: Reports,
    meta: { 
      requiresAuth: true,
      requiredPermissions: ['view_reports']
    }
  },
  {
    path: '/settings',
    name: 'settings',
    component: Settings,
    meta: { 
      requiresAuth: true,
      requiredRoles: ['admin']
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// Navigation guards
router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters['auth/isAuthenticated']
  const user = store.getters['auth/currentUser']

  // Check if route requires authentication
  if (to.meta.requiresAuth && !isAuthenticated) {
    next('/login')
    return
  }

  // Check if route requires guest (not authenticated)
  if (to.meta.requiresGuest && isAuthenticated) {
    next('/')
    return
  }

  // Check role-based access
  if (to.meta.requiredRoles && user) {
    const hasRequiredRole = to.meta.requiredRoles.some(role => 
      user.roles?.includes(role)
    )
    if (!hasRequiredRole) {
      next('/no-access')
      return
    }
  }

  // Check permission-based access
  if (to.meta.requiredPermissions && user) {
    const hasRequiredPermission = to.meta.requiredPermissions.some(permission => 
      user.permissions?.includes(permission)
    )
    if (!hasRequiredPermission) {
      next('/no-access')
      return
    }
  }

  next()
})

export default router
```

## Step 4: API Service Layer

### 4.1 API Configuration
```javascript
// src/services/api.js
import axios from 'axios'
import store from '@/store'
import router from '@/router'

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
})

// Request interceptor
api.interceptors.request.use(
  (config) => {
    const token = store.getters['auth/authToken']
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response
  },
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        await store.dispatch('auth/refreshToken')
        return api(originalRequest)
      } catch (refreshError) {
        store.dispatch('auth/logout')
        router.push('/login')
        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  }
)

export default api
```

### 4.2 Service APIs
```javascript
// src/services/serviceApi.js
import api from './api'

export const serviceApi = {
  // Get all services
  getServices(params = {}) {
    return api.get('/services', { params })
  },

  // Get service by ID
  getService(id) {
    return api.get(`/services/${id}`)
  },

  // Create new service
  createService(data) {
    return api.post('/services', data)
  },

  // Update service
  updateService(id, data) {
    return api.put(`/services/${id}`, data)
  },

  // Delete service
  deleteService(id) {
    return api.delete(`/services/${id}`)
  },

  // Assign service to engineer
  assignService(id, data) {
    return api.post(`/services/${id}/assign`, data)
  },

  // Update service status
  updateServiceStatus(id, status) {
    return api.put(`/services/${id}/status`, { status })
  },

  // Get service categories
  getServiceCategories() {
    return api.get('/service-categories')
  }
}

// src/services/authApi.js
export const authApi = {
  login(credentials) {
    return api.post('/auth/login', credentials)
  },

  register(userData) {
    return api.post('/auth/register', userData)
  },

  logout() {
    return api.post('/auth/logout')
  },

  refreshToken() {
    return api.post('/auth/refresh')
  },

  forgotPassword(email) {
    return api.post('/auth/forgot-password', { email })
  },

  resetPassword(data) {
    return api.post('/auth/reset-password', data)
  }
}
```

## Step 5: Main Components

### 5.1 App.vue
```vue
<template>
  <div class="relative">
    <!-- Header -->
    <Headbar 
      v-if="isAuthenticated" 
      @toggle-sidebar="toggleSidebar"
      :is-sidebar-open="isSidebarOpen"
    />
    
    <!-- Main content area -->
    <div class="flex h-screen overflow-auto relative">
      <!-- Sidebar -->
      <div 
        v-if="!isMobile && isAuthenticated && isSidebarOpen"
        class="w-1/5 custom-scrollbar-hidden"
      >
        <Sidebar 
          :route-item="sidebarSelected"
          :page-name="'dashboard'"
        />
      </div>
      
      <!-- Content area -->
      <div 
        class="w-full"
        :class="{ 'flex flex-col flex-grow overflow-y-auto': isAuthenticated }"
      >
        <router-view :key="refreshKey" />
      </div>
      
      <!-- Mobile Sidebar -->
      <div 
        v-if="isMobile && isSidebarOpen && isAuthenticated"
        class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50"
        @click="closeSidebar"
      >
        <div class="w-64 bg-white h-full">
          <Sidebar />
        </div>
      </div>
    </div>
    
    <!-- Global components -->
    <Toaster />
    <Loader v-if="globalLoading" />
  </div>
</template>

<script>
import { computed, onMounted, ref } from 'vue'
import { useStore } from 'vuex'
import Headbar from '@/components/supporting/dashboard/headbar.vue'
import Sidebar from '@/components/supporting/sidebar.vue'
import Toaster from '@/components/supporting/toaster.vue'
import Loader from '@/components/supporting/dialog_box/loader.vue'

export default {
  name: 'App',
  components: {
    Headbar,
    Sidebar,
    Toaster,
    Loader
  },
  setup() {
    const store = useStore()
    const isSidebarOpen = ref(true)
    const isMobile = ref(false)
    const refreshKey = ref(0)
    const globalLoading = ref(false)

    const isAuthenticated = computed(() => store.getters['auth/isAuthenticated'])

    const toggleSidebar = () => {
      isSidebarOpen.value = !isSidebarOpen.value
    }

    const closeSidebar = () => {
      if (isMobile.value) {
        isSidebarOpen.value = false
      }
    }

    const checkMobile = () => {
      isMobile.value = window.innerWidth < 768
    }

    onMounted(() => {
      // Initialize auth from localStorage
      store.dispatch('auth/initializeAuth')
      
      // Check mobile
      checkMobile()
      window.addEventListener('resize', checkMobile)
    })

    return {
      isAuthenticated,
      isSidebarOpen,
      isMobile,
      refreshKey,
      globalLoading,
      toggleSidebar,
      closeSidebar
    }
  }
}
</script>
```

### 5.2 Login Component
```vue
<template>
  <div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
      <div>
        <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
          Sign in to Track New
        </h2>
      </div>
      <form class="mt-8 space-y-6" @submit.prevent="handleLogin">
        <div class="rounded-md shadow-sm -space-y-px">
          <div>
            <input
              v-model="form.login"
              type="text"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-t-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="Email or Mobile Number"
            />
          </div>
          <div>
            <input
              v-model="form.password"
              type="password"
              required
              class="appearance-none rounded-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-b-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
              placeholder="Password"
            />
          </div>
        </div>

        <div>
          <button
            type="submit"
            :disabled="loading"
            class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50"
          >
            <span v-if="loading">Signing in...</span>
            <span v-else>Sign in</span>
          </button>
        </div>

        <div class="text-center">
          <router-link to="/register" class="text-indigo-600 hover:text-indigo-500">
            Don't have an account? Sign up
          </router-link>
        </div>
      </form>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router'

export default {
  name: 'Login',
  setup() {
    const store = useStore()
    const router = useRouter()
    
    const form = ref({
      login: '',
      password: ''
    })
    
    const loading = ref(false)

    const handleLogin = async () => {
      loading.value = true
      try {
        await store.dispatch('auth/login', form.value)
        router.push('/')
      } catch (error) {
        console.error('Login error:', error)
        // Handle error (show toast, etc.)
      } finally {
        loading.value = false
      }
    }

    return {
      form,
      loading,
      handleLogin
    }
  }
}
</script>
```

This frontend implementation guide provides the foundation for building the Track New Vue.js application. Continue with additional components, pages, and features as needed.
