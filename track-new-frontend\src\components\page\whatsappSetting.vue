<template>
    <div class="flex h-screen relative">
        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mt-[50px] m-1 mb-[57px]': isMobile, 'm-2 pt-5 p-2 mt-[30px]': !isMobile }">
            <!-- services home -->
            <div class="relative my-custom-margin">
                <whatsappSetting :isMobile="isMobile" :store_refresh="store_refresh" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen">
                </whatsappSetting>
            </div>
        </div>
    </div>
</template>

<script>
import whatsappSetting from '@/components/supporting/whatsappSetting/whatsappSetting.vue';
import { useMeta } from '@/composables/useMeta';
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'Whatsapp Setting',
    emits: ['updateIsOpen'],
    components: {
        whatsappSetting,
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            route_item: 11,
            viewLeads: [],
            getFilteredData: [],
            dataFromChild: [],
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Whatsapp Settings';
        const pageDescription = 'Quickly send and receive WhatsApp messages right from our software.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    methods: {
        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleDataFromChild(data) {
            this.dataFromChild = data;
            // console.log(data,'WWWWWW');
        },
        getFiteredDataList(data) {
            // console.log(data, 'WWWW');
            this.getFilteredData = data;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>