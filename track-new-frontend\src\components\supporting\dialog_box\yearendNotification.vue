<template>
    <!-- Financial Year End Modal -->
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="bg-white p-8 rounded-lg w-full max-w-lg text-center shadow-xl relative">
                <!-- Modal Header -->
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-xl font-semibold">End of Financial Year - Choose an Action</h2>
                    <button @click="closeModal" class="absolute right-2 top-1 text-gray-500 hover:text-red-500">
                        <font-awesome-icon icon="fa-solid fa-xmark" class="text-2xl" />
                    </button>
                </div>
                <!-- Modal Content -->
                <p class="text-sm text-gray-600 mb-6">Please choose one of the following options for your financial
                    year:
                </p>

                <div class="space-y-5 text-left">
                    <!-- Option 1: Close Financial Year -->
                    <div class="flex items-center">
                        <input type="radio" name="financial" v-model="formvalues.closeYearOption" id="closeYear"
                            class="mr-3 h-5 w-5 text-green-500" />
                        <label for="closeYear" class="text-sm text-gray-700">Close Financial Year and Start New</label>
                    </div>
                    <!-- Option 2: Continue Existing Year -->
                    <div class="flex items-center">
                        <input type="radio" name="financial" v-model="formvalues.continueExistingYearOption"
                            id="continueYear" class="mr-3 h-5 w-5 text-blue-500" />
                        <label for="continueYear" class="text-sm text-gray-700">Continue with Existing Year</label>
                    </div>
                </div>

                <!-- Modal Action Buttons -->
                <div class="mt-8 flex justify-between gap-4">
                    <button @click="submitChoice"
                        class="w-full bg-green-500 text-white px-6 py-3 rounded-lg hover:bg-green-400 focus:outline-none focus:ring-2 focus:ring-green-500 transition duration-200">
                        Submit
                    </button>
                    <button @click="closeModal"
                        class="w-full bg-red-500 text-white px-6 py-3 rounded-lg hover:bg-red-400 focus:outline-none focus:ring-2 focus:ring-red-500 transition duration-200">
                        Cancel
                    </button>
                </div>
            </div>
        </div>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>
<script>

export default {
    props: {
        showModal: Boolean,
        invoice_setting: {
            type: [Object]
        },
        companyId: String
    },
    emits: ['close-modal'],
    data() {
        return {
            'overlay-active': this.showModal,
            isOpen: false,
            formvalues: { closeYearOption: false, continueExistingYearOption: false },
            open_loader: false,
        }
    },
    methods: {
        // Submit function to handle the user selection
        submitChoice() {
            if (this.invoice_setting && this.invoice_setting.length > 0 && this.invoice_setting[0].id && this.formvalues.closeYearOption || this.formvalues.continueExistingYearOption) {
                this.open_loader = true;
                let send_data = { reset_fy: this.formvalues.closeYearOption ? true : false };
                axios.put(`/invoice_settings/${this.invoice_setting[0].id}`, { ...send_data, company_id: this.companyId })
                    .then(response => {
                        // console.log(response.data, 'Response update');
                        this.isOpen = false;
                        setTimeout(() => {
                            this.$emit('close-modal', true);
                        }, 100);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        setTimeout(() => {
                            this.open_loader = false;
                        }, 300);
                    })
            }
        },
        closeModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 100);
        }
    },
    watch: {
        showModal: {
            deep: true,
            handler(newValue) {
                setTimeout(() => {
                    this.isOpen = newValue;
                }, 100);
            }
        },
    }
};
</script>
<style scoped>
input[type="radio"] {
    /* remove standard background appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* create custom radiobutton appearance */
    display: inline-block;
    width: 20px;
    height: 20px;
    padding: 3px;
    /* background-color only for content */
    background-clip: content-box;
    border: 2px solid #bbbbbb;
    background-color: #e7e6e7;
    border-radius: 50%;
}

/* appearance for checked radiobutton */
input[type="radio"]:checked {
    background-color: #05810f;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
        /* Use the actual background color of your content */
        color: black;
        /* Use the actual text color of your content */
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */
    }

    .non-printable {
        display: none;
    }
}
</style>
