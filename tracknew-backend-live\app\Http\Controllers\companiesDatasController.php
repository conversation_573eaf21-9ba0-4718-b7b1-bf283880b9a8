<?php

namespace App\Http\Controllers;

use App\DataTables\companiesDatasDataTable;
use App\Http\Requests;
use App\Http\Requests\CreatecompaniesDatasRequest;
use App\Http\Requests\UpdatecompaniesDatasRequest;
use App\Repositories\companiesDatasRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class companiesDatasController extends AppBaseController
{
    /** @var companiesDatasRepository $companiesDatasRepository*/
    private $companiesDatasRepository;

    public function __construct(companiesDatasRepository $companiesDatasRepo)
    {
        $this->companiesDatasRepository = $companiesDatasRepo;
    }

    /**
     * Display a listing of the companiesDatas.
     *
     * @param companiesDatasDataTable $companiesDatasDataTable
     *
     * @return Response
     */
    public function index(companiesDatasDataTable $companiesDatasDataTable)
    {
        return $companiesDatasDataTable->render('companies_datas.index');
    }

    /**
     * Show the form for creating a new companiesDatas.
     *
     * @return Response
     */
    public function create()
    {
        return view('companies_datas.create');
    }

    /**
     * Store a newly created companiesDatas in storage.
     *
     * @param CreatecompaniesDatasRequest $request
     *
     * @return Response
     */
    public function store(CreatecompaniesDatasRequest $request)
    {
        $input = $request->all();

        $companiesDatas = $this->companiesDatasRepository->create($input);

        Flash::success('Companies Datas saved successfully.');

        return redirect(route('companiesDatas.index'));
    }

    /**
     * Display the specified companiesDatas.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $companiesDatas = $this->companiesDatasRepository->find($id);

        if (empty($companiesDatas)) {
            Flash::error('Companies Datas not found');

            return redirect(route('companiesDatas.index'));
        }

        return view('companies_datas.show')->with('companiesDatas', $companiesDatas);
    }

    /**
     * Show the form for editing the specified companiesDatas.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $companiesDatas = $this->companiesDatasRepository->find($id);

        if (empty($companiesDatas)) {
            Flash::error('Companies Datas not found');

            return redirect(route('companiesDatas.index'));
        }

        return view('companies_datas.edit')->with('companiesDatas', $companiesDatas);
    }

    /**
     * Update the specified companiesDatas in storage.
     *
     * @param int $id
     * @param UpdatecompaniesDatasRequest $request
     *
     * @return Response
     */
    public function update($id, UpdatecompaniesDatasRequest $request)
    {
        $companiesDatas = $this->companiesDatasRepository->find($id);

        if (empty($companiesDatas)) {
            Flash::error('Companies Datas not found');

            return redirect(route('companiesDatas.index'));
        }

        $companiesDatas = $this->companiesDatasRepository->update($request->all(), $id);

        Flash::success('Companies Datas updated successfully.');

        return redirect(route('companiesDatas.index'));
    }

    /**
     * Remove the specified companiesDatas from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $companiesDatas = $this->companiesDatasRepository->find($id);

        if (empty($companiesDatas)) {
            Flash::error('Companies Datas not found');

            return redirect(route('companiesDatas.index'));
        }

        $this->companiesDatasRepository->delete($id);

        Flash::success('Companies Datas deleted successfully.');

        return redirect(route('companiesDatas.index'));
    }
}
