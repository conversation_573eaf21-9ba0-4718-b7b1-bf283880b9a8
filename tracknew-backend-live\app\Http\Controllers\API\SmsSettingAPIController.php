<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateSmsSettingAPIRequest;
use App\Http\Requests\API\UpdateSmsSettingAPIRequest;
use App\Models\SmsSetting;
use App\Repositories\SmsSettingRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class SmsSettingController
 * @package App\Http\Controllers\API
 */

class SmsSettingAPIController extends AppBaseController
{
    /** @var  SmsSettingRepository */
    private $smsSettingRepository;

    public function __construct(SmsSettingRepository $smsSettingRepo)
    {
        $this->smsSettingRepository = $smsSettingRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/sms_settings",
     *      summary="getSmsSettingList",
     *      tags={"SmsSetting"},
     *      description="Get all SmsSettings",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/SmsSetting")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $Query = SmsSetting::where('company_id', $companyId);

        if ($perPage === 'all') {
            $perPage = $Query->count();
        }

        $sms = $Query->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $sms->items(), 
            'pagination' => [
                'total' => $sms->total(),
                'per_page' => $sms->perPage(),
                'current_page' => $sms->currentPage(),
                'last_page' => $sms->lastPage(),
                'from' => $sms->firstItem(),
                'to' => $sms->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/sms_settings",
     *      summary="createSmsSetting",
     *      tags={"SmsSetting"},
     *      description="Create SmsSetting",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SmsSetting"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateSmsSettingAPIRequest $request)
    {
        $input = $request->all();

        $smsSetting = $this->smsSettingRepository->create($input);

        return $this->sendResponse($smsSetting->toArray(), 'Sms Setting saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/sms_settings/{id}",
     *      summary="getSmsSettingItem",
     *      tags={"SmsSetting"},
     *      description="Get SmsSetting",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SmsSetting",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SmsSetting"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var SmsSetting $smsSetting */
        $smsSetting = $this->smsSettingRepository->find($id);

        if (empty($smsSetting)) {
            return $this->sendError('Sms Setting not found');
        }

        return $this->sendResponse($smsSetting->toArray(), 'Sms Setting retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/sms_settings/{id}",
     *      summary="updateSmsSetting",
     *      tags={"SmsSetting"},
     *      description="Update SmsSetting",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SmsSetting",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SmsSetting"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateSmsSettingAPIRequest $request)
    {
        $input = $request->all();

        /** @var SmsSetting $smsSetting */
        $smsSetting = $this->smsSettingRepository->find($id);

        if (empty($smsSetting)) {
            return $this->sendError('Sms Setting not found');
        }

        $smsSetting = $this->smsSettingRepository->update($input, $id);

        return $this->sendResponse($smsSetting->toArray(), 'SmsSetting updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/sms_settings/{id}",
     *      summary="deleteSmsSetting",
     *      tags={"SmsSetting"},
     *      description="Delete SmsSetting",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SmsSetting",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var SmsSetting $smsSetting */
        $smsSetting = $this->smsSettingRepository->find($id);

        if (empty($smsSetting)) {
            return $this->sendError('Sms Setting not found');
        }

        $smsSetting->delete();

        return $this->sendSuccess('Sms Setting deleted successfully');
    }
}
