<template>
    <div class="builder-page">
        <!-- <h1 class="web-head">Website Customization</h1> -->
        <!-- Removed @submit.prevent to avoid form submission cancellation issue -->

        <!-- First Row: Website Name and Tagline -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-2 lg:mb-6">
            <!-- site name and tagline-->
            <div>
                <div class="flex justify-between items-center mb-2">
                    <label class="font-bold">Site Name</label>
                </div>
                <input type="text" v-model="websiteData.site_name" class="border p-2 rounded w-full"
                    placeholder="Site Name" />
            </div>
            <!-- Tagline -->
            <div>
                <label class="block font-bold mb-2">Tag line (Optional)</label>
                <input type="text" v-model="websiteData.tagline" class="border p-2 rounded w-full"
                    placeholder="like, All is well" />
            </div>
        </div>
        <!-- Logo Upload -->
        <div class="flex items-center">
            <!-- File Input Section -->
            <div class="flex-1">
                <div class="flex justify-between items-center mb-2">
                    <label class="block font-bold">Logo Setting</label>
                </div>
            </div>
        </div>
        <!-- Second Row: Logo and Favicon -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-2 lg:mb-6">
            <!-- Text Logo -->
            <div>
                <div class="flex justify-between items-center mb-2">
                    <label class="block font-normal">Text Logo </label>
                    <label class="inline-flex items-center cursor-pointer">
                        <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                            @click="handleToggleName"
                            :class="{ 'bg-blue-600': websiteData.is_name, 'bg-gray-200': !websiteData.is_name }">
                            <div class="absolute -top-0.5 left-0 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-6': websiteData.is_name, 'translate-x-0': !websiteData.is_name }">
                            </div>
                        </div>
                        <p class="px-2 text-sm">{{ websiteData.is_name ? "ON" : "OFF" }}</p>
                    </label>
                </div>
                <input type="text" v-model="websiteData.name" class="border p-2 rounded w-full"
                    placeholder="Company Name" required />
            </div>
            <!--logo image-->
            <div class="flex items-center">
                <!-- File Input Section -->
                <div class="flex-1">
                    <div class="flex justify-between items-center mb-2">
                        <label class="block font-normal">Image Logo</label>
                        <label class="inline-flex items-center cursor-pointer">
                            <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                                @click="handleToggleLogo"
                                :class="{ 'bg-blue-600': websiteData.is_logo, 'bg-gray-200': !websiteData.is_logo }">
                                <div class="absolute -top-0.5 left-0 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                    :class="{ 'translate-x-6': websiteData.is_logo, 'translate-x-0': !websiteData.is_logo }">
                                </div>
                            </div>
                            <p class="px-2 text-sm">{{ websiteData.is_logo ? "ON" : "OFF" }}</p>
                        </label>
                    </div>
                    <input type="file" @change="onFileChange($event, 'logo')" class="border p-2 rounded w-full"
                        accept="image/png, image/jpeg" />
                </div>
                <!-- Logo Preview Section with Remove Option -->
                <div
                    class="ml-4 w-16 h-16 flex items-center justify-center border rounded overflow-hidden bg-gray-100 relative">
                    <div v-if="circle_loader_photo === 'logo'"
                        class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
                        <CircleLoader :loading="true"></CircleLoader>
                    </div>
                    <template v-if="websiteData.logo">
                        <img :src="websiteData.logo" alt="Logo Preview" class="w-full h-full object-cover" />
                        <button @click="removeImage({ type: 'logo', url: websiteData.logo })"
                            class="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center">
                            &times;
                        </button>
                    </template>
                    <template v-else>
                        <span class="text-gray-400 text-sm">No Logo</span>
                    </template>
                </div>
            </div>
            <!-- Favicon Upload Section -->
            <div class="flex items-center">
                <!-- Favicon Input Section -->
                <div class="flex-1">
                    <label class="block font-bold mb-2">Favicon</label>
                    <input type="file" @change="onFileChange($event, 'favicon')" class="border p-2 rounded w-full"
                        accept="image/png, image/jpeg" />
                </div>

                <!-- Favicon Preview Section with Reserved Space -->
                <div
                    class="ml-4 w-10 h-10 flex items-center justify-center border rounded overflow-hidden bg-gray-100 relative">
                    <div v-if="circle_loader_photo === 'favicon'"
                        class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
                        <CircleLoader :loading="true"></CircleLoader>
                    </div>
                    <template v-if="websiteData.favicon">
                        <img :src="websiteData.favicon" alt="Favicon Preview" class="w-full h-full object-cover" />
                        <button @click="removeImage({ type: 'favicon', url: websiteData.favicon })"
                            class="absolute top-0 right-0 bg-red-500 text-white rounded-full w-5 h-5 flex items-center justify-center">
                            &times;
                        </button>
                    </template>
                    <template v-else>
                        <span class="text-gray-400 text-sm">No Favicon</span>
                    </template>
                </div>
            </div>
        </div>

        <!-- Address -->
        <div class="mb-2 lg:mb-6">
            <label class="block font-bold mb-2">Address <span class="text-red-500">*</span></label>
            <textarea v-model="websiteData.address" class="border p-2 rounded w-full" placeholder="Enter Address"
                required></textarea>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <!-- Contact Numbers -->
            <div class="mb-2 lg:mb-6">
                <label class="block font-bold mb-2">Contact Numbers <span class="text-red-500">*</span></label>

                <div v-for="(contact, index) in websiteData.contacts" :key="index"
                    class="flex items-center space-x-2 mb-2">
                    <select v-model="contact.type" class="border p-2 rounded">
                        <option value="support">Support</option>
                        <option value="sales">Sales</option>
                    </select>
                    <input type="text" v-model="contact.number" class="border p-2 rounded w-full"
                        placeholder="Enter Contact Number" required />
                    <button type="button" @click="removeImage({ type: 'contact', index: index })" class="text-red-500"
                        title="Remove">
                        <font-awesome-icon icon="fa-solid fa-trash-can" />
                    </button>
                </div>
                <button type="button" @click="addContact" class="bg-blue-500 text-white py-1 px-3 rounded">Add
                    More
                    Contacts</button>
            </div>
            <!-- Email Addresses -->
            <div class="mb-2 lg:mb-6">
                <label class="block font-bold mb-2">Email Addresses (Optional)</label>
                <div v-for="(email, index) in websiteData.emails" :key="index" class="flex items-center space-x-2 mb-2">
                    <input type="email" v-model="websiteData.emails[index]" class="border p-2 rounded w-full"
                        placeholder="Enter email address" />
                    <button type="button" @click="removeImage({ type: 'email', index: index })" class="text-red-500"
                        title="Remove">
                        <font-awesome-icon icon="fa-solid fa-trash-can" />
                    </button>
                </div>
                <button type="button" @click="addEmail" class="bg-blue-500 text-white py-1 px-3 rounded">Add
                    more
                    Mail</button>
            </div>
        </div>
        <!-- Google Map Address -->
        <div class="mb-3">
            <label class="block font-bold mb-2">Google Map Address link <span class="text-red-500">*</span></label>
            <input type="text" v-model="websiteData.mapAddress" class="border p-2 rounded w-full"
                @input="extractCoordinates" @change="extractCoordinates" placeholder="Enter Google Map Link" required />
        </div>
        <!-- Latitude and Longitude Fields -->
        <div class="my-4 grid grid-cols-2 gap-4 ">
            <div>
                <label class="block font-bold mb-2">Map Latitude</label>
                <input type="text" v-model="websiteData.latitude" class="border p-2 rounded w-full"
                    placeholder="Latitude" />
            </div>
            <div>
                <label class="block font-bold mb-2">Map Longitude</label>
                <input type="text" v-model="websiteData.longitude" class="border p-2 rounded w-full"
                    placeholder="Longitude" />
            </div>
        </div>

        <!-- Company Timings -->
        <!-- <div class="mb-2 lg:mb-6">
        <label class="block font-bold mb-2">Company Timings <span class="text-red-500">*</span></label>
        <div v-for="(timing, index) in websiteData.timings" :key="index"
          class="flex flex-wrap sm:flex-nowrap items-center space-y-2 sm:space-y-0 sm:space-x-2 mb-2">
          <select v-model="timing.day" class="border p-2 rounded w-full sm:w-auto">
            <option value="Mon-Sat">Mon-Sat</option>
            <option value="Sun">Sun</option>
          </select>
          <input type="time" v-model="timing.openTime" class="border p-2 rounded w-full sm:w-auto"
            placeholder="Open Time" />
          <input type="time" v-model="timing.closeTime" class="border p-2 rounded w-full sm:w-auto"
            placeholder="Close Time" />
          <div class="w-full sm:w-auto">
            <input type="checkbox" v-model="timing.isClosed" /> Closed Day
          </div>
        </div>
      </div> -->


        <!-- Bottom Navigation Buttons -->
        <div class="mt-6">
            <NavigationButtons :pageTitle="'Product Details'" :showBackButton="false" @goToNextPage="goToNextPage"
                @goToPrevPage="goToPrevPage" />
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    </div>
</template>

<script>
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import CkEditorForm from './CkEditorForm.vue';
import Sidebar from '@/layouts/sidebar.vue';

export default {
    props: {
        companyId: {
            type: String,
            required: true
        },
        domain: {
            type: [String, null], // Accepts both String and null
            required: true, // Ensures the prop must be passed
            validator: (value) => {
                return value === null || typeof value === 'string';
            },
        },
        websiteSettingsData: {
            type: Object,
            required: true,
        },
        is_updated: {
            type: Boolean,
            required: true,
        },
        isMobile: {
            type: Boolean,
            required: true,
        }
    },
    components: {
        NavigationButtons,
        confirmbox,
        CkEditorForm,
        Sidebar
    },

    data() {
        return {
            websiteData: {}, // Initialize with prop data
            domainName: this.domain,
            loading: false,
            //--confirmbox--
            open_confirmBox: false,
            deleteIndex: null,
            //--loader--
            circle_loader_photo: null,
        };
    },
    mounted() {
        if (this.websiteData.is_name === undefined) {
            this.websiteData.is_name = true;
        }
        if (this.websiteData.is_logo === undefined) {
            this.websiteData.is_logo = true;
        }
        if (this.websiteSettingsData && Object.keys(this.websiteSettingsData).length > 0) {
            this.websiteData = { ...this.websiteSettingsData };
        }
    },
    watch: {
        // Watch for changes in websiteSettingsData and update websiteData accordingly
        // websiteSettingsData: {
        //     handler(newData) {
        //         this.websiteData = { ...newData };
        //     },
        //     deep: true,
        //     immediate: true
        // },
        is_updated: {
            deep: true,
            handler(newValue) {
                this.$emit('updateWebsiteSettings', this.websiteData);
            }
        }
    },


    methods: {
        // Handle file uploads
        async onFileChange(event, type) {
            const file = event.target.files[0];
            if (!file) return;

            const maxSizeBytes = 500 * 1024; // 500kb in bytes

            try {
                this.circle_loader_photo = type; // Show loader while processing

                // Check file size and compress if necessary
                const uploadFile = file.size > maxSizeBytes ? await this.compressImage(file) : file;

                // Upload the (possibly compressed) file
                await this.uploadImageProfile(uploadFile, type);

            } catch (error) {
                console.error("Error processing file:", error);
            } finally {
                this.circle_loader_photo = null; // Hide loader once complete
            }
        },

        uploadImageProfile(file, type) {
            const formData = new FormData();
            formData.append("image", file);
            formData.append("model", type === 'logo' || type === 'logo_text' ? "website/WebsiteLogo" : "website/Favicon");
            formData.append("company_id", this.companyId);

            return axios.post('/image', formData)
                .then(response => {
                    // Set logoUrl or faviconUrl based on the uploaded file type
                    if (type === 'logo') {
                        this.websiteData.logo = response.data.media_url;
                    } else if (type === 'logo_text') {
                        this.websiteData.logo_text = response.data.media_url;
                    } else if (type === 'favicon') {
                        this.websiteData.favicon = response.data.media_url;
                    }
                    this.$emit('updateWebsiteSettings', this.websiteData);

                })
                .catch(error => {
                    console.error("Error uploading image", error);
                    throw error; // Re-throw to handle in calling function
                });
        },

        compressImage(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = (event) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        // Set maximum width and height for compressed image
                        const maxWidth = 800;
                        const maxHeight = 600;

                        let width = img.width;
                        let height = img.height;

                        // Maintain aspect ratio while resizing
                        if (width > maxWidth || height > maxHeight) {
                            const aspectRatio = width / height;
                            if (width > height) {
                                width = maxWidth;
                                height = width / aspectRatio;
                            } else {
                                height = maxHeight;
                                width = height * aspectRatio;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;
                        ctx.drawImage(img, 0, 0, width, height);

                        // Convert to blob with desired quality
                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, {
                                type: 'image/jpeg',
                                lastModified: Date.now()
                            });
                            resolve(compressedFile);
                        }, 'image/jpeg', 0.7); // 70% quality
                    };
                    img.src = event.target.result;
                };
                reader.readAsDataURL(file);
            });
        },
        //--remove Image
        removeImage(type) {
            if (type) {
                if (type.type === 'contact' && type.index >= 0 && this.websiteData.contacts[type.index] && this.websiteData.contacts[type.index].number === '') {
                    this.removeContact(type.index);
                } else if (type.type === 'email' && type.index >= 0 && this.websiteData.emails[type.index] !== undefined && this.websiteData.emails[type.index] === '') {
                    this.removeEmail(type.index);
                } else {
                    this.deleteIndex = type;
                    this.open_confirmBox = true;
                }
            } else {
                this.open_confirmBox = true;
            }

        },
        //--confirmbox delete confirm
        deleteRecord() {
            if (this.deleteIndex && (this.deleteIndex.type === 'logo' || this.deleteIndex.type === 'logo_text' || this.deleteIndex.type === 'favicon')) {
                this.$emit('updateLoader', true);
                axios.delete('/delete-image', { params: { model: this.deleteIndex.type ? this.deleteIndex.type : '', image_url: this.deleteIndex.url ? this.deleteIndex.url : '' } })
                    .then(response => {
                        // console.log(response.data, 'this is delete responses');
                        this.$emit('updateWebsiteSettings', { ...this.websiteData, logo: this.deleteIndex.type === 'logo' ? '' : this.websiteData.logo, logo_text: this.deleteIndex.type === 'logo_text' ? '' : this.websiteData.logo_text, favicon: this.deleteIndex.type === 'favicon' ? '' : this.websiteData.favicon });
                        this.$emit('toasterMessages', { msg: response.data.message, type: 'success' });
                        this.$emit('submitData');
                        this.closeconfirmBoxData();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.$emit('toasterMessages', { msg: error.response.data.error, type: 'warning' });
                        if (error.response.data.error === 'Image not found') {
                            this.$emit('updateWebsiteSettings', { ...this.websiteData, logo: this.deleteIndex.type === 'logo' ? '' : this.websiteData.logo, logo_text: this.deleteIndex.type === 'logo_text' ? '' : this.websiteData.logo_text, favicon: this.deleteIndex.type === 'favicon' ? '' : this.websiteData.favicon });
                            this.$emit('submitData');
                        }
                        this.closeconfirmBoxData();
                    })
            } else if (this.deleteIndex && !(this.deleteIndex.type === 'logo' || this.deleteIndex.type === 'logo_text' || this.deleteIndex.type === 'favicon')) {
                if (this.deleteIndex.type === 'contact' && this.deleteIndex.index >= 0) {
                    this.removeContact(this.deleteIndex.index);
                    this.$emit('toasterMessages', { msg: 'Contact data removed successfully', type: 'success' });
                    this.closeconfirmBoxData();
                } else if (this.deleteIndex.type === 'email' && this.deleteIndex.index >= 0) {
                    this.removeEmail(this.deleteIndex.index);
                    this.$emit('toasterMessages', { msg: 'Email data removed successfully', type: 'success' });
                    this.closeconfirmBoxData();
                } else {
                    this.closeconfirmBoxData();
                    this.$emit('toasterMessages', { msg: 'Data not found', type: 'warning' });
                }
            } else {

                this.closeconfirmBoxData();
            }
        },
        //--confirmbox delete cancel
        cancelDelete() {
            this.closeconfirmBoxData();
        },
        closeconfirmBoxData() {
            this.deleteIndex = null;
            this.$emit('updateLoader', false);
            this.open_confirmBox = false;
        },
        // Add/Remove contacts
        addContact() {
            if (Array.isArray(this.websiteData.contacts)) {
                this.websiteData.contacts.push({ type: 'support', number: '' });
            } else {
                this.websiteData.contacts = [{ type: 'support', number: '' }];
            }
        },
        removeContact(index) {
            this.websiteData.contacts.splice(index, 1);
        },

        // Add/Remove emails
        addEmail() {
            if (Array.isArray(this.websiteData.emails)) {
                this.websiteData.emails.push('');
            } else {
                this.websiteData.emails = [''];
            }
        },
        removeEmail(index) {
            this.websiteData.emails.splice(index, 1);
        },
        registerDomin() {

            const websiteDomainData = {
                domain_name: this.domainName,
                company_id: this.companyId

            };

            // Add companyId to the registration data
            this.loading = true;


            //console.log(registrationData);

            // Call the API to register the website with the provided data
            axios.post('/website-domain/update', websiteDomainData)
                .then(response => {
                    console.log("Website Domain registered successfully");

                    this.loading = false;
                    //this.fetchWebsiteSettings();

                })
                .catch(error => {
                    console.error("Error Domain registering website:", error);
                    this.isLoading = false;
                    // Handle error response, such as validation errors
                    if (error.response && error.response.data) {
                        alert(`Error: ${error.response.data.message || 'Registration failed.'}`);
                    } else {
                        alert('An unexpected error occurred. Please try again.');
                    }
                });


        },
        goToNextPage() {
            this.$emit('updateWebsiteSettings', this.websiteData);
            this.$emit('submitData');
            this.$emit('goToNextPage');
        },
        goToPrevPage() {
            this.$emit('updateWebsiteSettings', this.websiteData);
            this.$emit('submitData');
            this.$emit('goToPrevPage'); // Emit event to the parent component
        },
        // Submit form data to parent
        submitForm() {
            this.$emit('updateWebsiteSettings', this.websiteData); // Emit to parent
            console.log('Website Settings Submitted:', this.websiteData);
        },
        //--editorData--
        handleEditorSubmit(data) {
            if (data) {
                this.websiteData.intro = data;
            } else {
                this.websiteData.intro = '';
            }
        },
        handleEditorSubmitData(data) {
            if (data) {
                this.websiteData.intro_after = data;
            } else {
                this.websiteData.intro_after = '';
            }
        },
        //--exract from link
        extractCoordinates() {
            // Regular expression to match coordinates in a Google Maps URL
            const regex = /@([-+]?[0-9]*\.?[0-9]+),([-+]?[0-9]*\.?[0-9]+)/;
            const match = this.websiteData.mapAddress.match(regex);
            if (match) {
                this.websiteData.latitude = match[1]; // Extract latitude
                this.websiteData.longitude = match[2]; // Extract longitude
            } else {
                this.websiteData.latitude = "";
                this.websiteData.longitude = "";
            }
        },
        //--website name / logo
        handleToggleName() {
            if (this.websiteData.is_name !== undefined) {
                this.websiteData.is_name = !this.websiteData.is_name;
            } else {
                this.websiteData.is_name = true;
            }
        },
        handleToggleLogo() {
            if (this.websiteData.is_logo !== undefined) {
                this.websiteData.is_logo = !this.websiteData.is_logo;
            } else {
                this.websiteData.is_logo = true;
            }
        }
    },
};
</script>