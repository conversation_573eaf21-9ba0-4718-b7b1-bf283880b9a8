<template>
    <div>
        <div id="printable-content" v-if="formData && formData.length > 0"
            class="m-2 mx-auto w-full px-5 prindContent text-sm" ref="templateContent"
            :class="{ 'text-sm': isAndroid && isPrinting, 'text-[5px]': isAndroid && !isPrinting, 'mt-[50px]': isMobile && !isPrinting }">
            <div class="w-full flex justify-between px-2"
                :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                <div class="flex items-center mb-2">
                    <div class="mr-2">{{ typeOfInvoice !== 'estimation' ? 'TAX INVOICE' : 'ESTIMATE INVOICE' }}
                    </div>
                    <div class="items-center">
                        <p class="border border-[#868597] rounded text-[#868597] uppercase px-1 text-center">Original
                            for Recipient</p>
                    </div>
                </div>
                <!--invoice type-->
                <div v-if="invoice_data.invoice_type">
                    <p class="border border-[#868597] rounded text-[#868597] uppercase px-1 items-center">{{
                        invoice_data.invoice_type === 'b2c' ? 'B2C' : 'BB' }}</p>
                </div>
            </div>
            <div class="grid grid-cols-3 items-center border border-b-0 bg-white">
                <!--qr code-->
                <div>
                    <div>
                        <img v-if="formData && formData.length > 0 && formData[0].bank_details && (JSON.parse(formData[0].bank_details).upi_id || JSON.parse(formData[0].bank_details).g_pay || JSON.parse(formData[0].bank_details).phone_pay)"
                            :src="qrCodeUrl" alt="UPI QR Code" class="mx-auto w-20 h-20" />
                    </div>

                </div>
                <div class="items-center px-2 py-1 font-bold">
                    <div class="flex items-center">
                        <!---company name-->
                        <div>
                            <p class="font-bold mb-1"
                                :class="{ 'text-xl': !isAndroid || isPrinting, 'text-sm': isAndroid && !isPrinting }">
                                {{ formData[0].name }}
                            </p>
                        </div>
                    </div>
                    <!---company details-->
                    <div class="items-center"
                        :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                        <p>{{ formData[0].address }}</p>
                        <p v-if="formData[0].email !== ''"><span class="font-bold">Email:</span> {{ formData[0].email }}
                        </p>
                        <p v-if="formData[0].business_contact !== ''"><span class="font-bold">Contact:</span> {{
                            formData[0].business_contact
                            }}</p>
                    </div>
                </div>
                <!--gst number-->
                <div v-if="currentCompanyList && currentCompanyList.gst_number && currentCompanyList.gst_number !== ''"
                    class="relative">
                    <p class="absolute -top-10 right-1 text-xs">Original/Duplicate/Triplicate</p>
                </div>
            </div>
            <!--state code-->
            <div v-if="currentCompanyList && currentCompanyList.gst_number && currentCompanyList.gst_number !== ''"
                class="px-2 py-1  flex justify-between items-center font-bold border-x">
                <p><span>State Code :</span> {{ getStateCode }}</p>
                <p>
                    <span>GST :</span> {{ currentCompanyList.gst_number }}
                </p>
            </div>
            <!--invoice to shipping to-->
            <div class="grid border-l border-r border-t bg-white"
                :class="{ 'grid-cols-3': invoice_data.is_billaddress, 'grid-cols-3': !invoice_data.is_billaddress }">
                <div class="border-r px-2 py-2"
                    :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting, 'col-span-2': !invoice_data.is_billaddress }">
                    <div v-if="customer_data">
                        <p class="font-bold">BILL TO</p>
                        <p class="font-bold">{{ customer_data.last_name ? customer_data.first_name : '' + ' ' +
                            customer_data.last_name
                            ?
                            customer_data.first_name : '' }}</p>
                        <p v-if="customer_data.address"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Address:</span>{{
                                customer_data.address
                            }}
                        </p>
                        <p v-if="customer_data.email"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Email:</span> {{
                                customer_data.email }}
                        </p>
                        <p :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Contact:</span> {{ customer_data.contact_number }}
                        </p>
                        <p v-if="invoice_data.invoice_type === 'b2b'"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>GST Tin -
                            </span>
                            {{ customer_data.gst_number }}
                        </p>
                    </div>
                </div>
                <div v-if="invoice_data.is_billaddress == 0" class="flex px-2 py-2"
                    :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                    <div v-if="customer_data">
                        <p class="font-bold">SHIP TO</p>
                        <p class="font-bold">{{ customer_data.last_name ? customer_data.first_name : '' + ' ' +
                            customer_data.last_name
                            ?
                            customer_data.first_name : '' }}</p>
                        <p v-if="customer_data.address"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Address:</span> {{
                                customer_data.address
                            }}
                        </p>
                        <p v-if="customer_data.email"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Email:</span> {{
                                customer_data.email }}
                        </p>
                        <p :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Contact:</span> {{ customer_data.contact_number }}
                        </p>
                        <p v-if="invoice_data.invoice_type === 'b2b'"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>GST Tin -
                            </span>
                            {{ customer_data.gst_number }}
                        </p>
                        <p v-if="invoice_data.shipping_type"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Shipping
                                via:</span>
                            {{ invoice_data.shipping_type }}
                        </p>
                        <p v-if="invoice_data.cod"
                            :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                            <span>Shipping ID:</span>
                            {{ invoice_data.cod }}
                        </p>
                    </div>
                </div>
                <!--invoice details-->
                <div class="items-center border-l"
                    :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                    <div class="items-center font-bold">
                        <p class="border-b text-center py-1">TAX INVOICE</p>
                    </div>
                    <div class="flex text-center px-1 space-x-1 font-semibold uppercase">
                        <p>
                            {{ typeOfInvoice === 'estimation' ? 'Estimation No :' : 'Invoice No :' }}
                        </p>

                        <p>{{ invoice_data.invoice_number }}</p>
                    </div>
                    <div class="flex text-center px-1 font-semibold space-x-1">
                        <p class="font-semibold uppercase">
                            <span>
                                {{ typeOfInvoice === 'estimation' ? 'Estimation' : 'Invoice' }}
                            </span> Date :
                        </p>
                        <p>{{
                            getFormattedDateTime(invoice_data.current_date) }}</p>
                    </div>
                    <div v-if="typeOfInvoice !== 'estimation'" class="flex text-center px-1 font-semibold space-x-1">
                        <p class="font-semibold uppercase">
                            Due Date :
                        </p>
                        <p>{{
                            getFormattedDateTime(invoice_data.current_date) }}</p>
                    </div>
                </div>
            </div>

            <div class="w-full overflow-x-auto">
                <table class="w-full">
                    <thead :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                        <tr class="border uppercase"
                            :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">
                            <th class="font-medium border-r">S.NO.</th>
                            <th class="p-1 font-medium border-r">Items</th>
                            <th class="font-medium border-r">HSN Code</th>
                            <th class="font-medium border-r">Qty</th>
                            <th class="font-medium border-r">Price</th>
                            <th class="font-medium border-r">Discount</th>
                            <th class="font-medium border-r">Tax</th>
                            <th class="font-medium">Total</th>
                        </tr>
                    </thead>
                    <tbody>

                        <!---add extra materials-->
                        <tr v-for="(item, index) in items_data" :key="index" class="text-center"
                            :class="{ 'page-break': (index + 1) === 20 || ((index + 1) % 30 === 0 && index + 1 !== 30), 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                            <!--Sr.no-->
                            <td class="p-1 border text-center">
                                <span class="mr-2">{{ index + 1 }}</span>
                            </td>
                            <!--Product name / description-->
                            <td class="p-1 border relative text-start px-3 py-1">
                                <p><span>{{ item.product_name }}</span></p>
                                <p v-if="item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0"
                                    :class="{ 'text-[10px]': !isAndroid || isPrinting, 'text-[3px]': isAndroid && !isPrinting }">
                                    Serial no: {{ item.serial_no.join(', ') }}</p>
                            </td>
                            <!--HSN code-->
                            <td class="p-1 border">
                                {{ item.hsn_code }}
                            </td>
                            <!--Quantity-->
                            <td class="p-1 border">
                                {{ item.qty }}
                            </td>
                            <!--Price-->
                            <td class="p-1 border">
                                <span>₹</span>{{ item.price }}
                            </td>
                            <!--discount value-->
                            <td class="p-1 border">
                                <span>₹</span>{{ item.discount }}
                            </td>
                            <!--tax-->
                            <td class="p-1 border">
                                <span>₹</span>{{ item.tax }}
                            </td>
                            <!--total value-->
                            <td class="p-1 border text-center">
                                <span>₹</span>{{ item.total }}
                            </td>
                        </tr>
                        <!-- Add empty rows to fill remaining space -->
                        <tr :style="{ height: isPrinting ? calculateRemainingSpace() + 'px' : '5px' }">
                            <td v-for="index in 8" :key="index" class="p-1 border">
                                <!-- Your content here -->
                            </td>
                        </tr>
                        <!---make gap 1-->
                        <tr>
                            <td colspan="8" class="py-1"></td>
                        </tr>
                        <!---new design-->
                        <tr :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                            <!---amount in words && payment details-->
                            <td colspan="4" class="h-full border">
                                <div class="grid grid-cols-1 px-1">
                                    <!---amount in words -->
                                    <div>
                                        <p class="font-semibold ">Total Amount (in words):</p>
                                        <p class="px-1">{{
                                            convertToWords(Math.round(get_all_data.grand_total)) }} Only</p>
                                    </div>
                                    <!--bank details-->
                                    <div v-if="formData[0].bank_details" class="mt-1">
                                        <p class="font-semibold ">Payment Information:</p>
                                        <p v-for="(line, l) in formData[0].bank_details.split('\n')" :key="l"
                                            class="px-1">
                                            {{ line }}
                                        </p>
                                    </div>
                                </div>
                            </td>
                            <!--amount details-->
                            <td colspan="4" class="border">
                                <div class="grid grid-cols-1 gap-1">
                                    <!--discount-->
                                    <div v-if="items_data && items_data.length > 0 && (Number(totalDiscount) + Number(get_all_data.discount_total)) > 0"
                                        class="grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }"
                                        :class="{ 'non-printable': totalDiscount <= 0 && get_all_data.discount_total <= 0 }">
                                        <div class="text-left p-1 uppercase">Discount (₹)</div>
                                        <div class="flex items-center">
                                            <p class="px-1 text-end"><span>₹</span>
                                                {{ (Number(totalDiscount) +
                                                    Number(get_all_data.discount_total)) }}</p>
                                        </div>
                                    </div>
                                    <!--Sub total-->
                                    <div v-if="items_data && items_data.length > 0"
                                        class="font-semibold grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">
                                        <div class="text-left p-1 uppercase">Sub Total</div>
                                        <!-- <div class="text-center p-1">{{ get_all_data.total_qty }}</div> -->
                                        <div class="text-end p-1"><span>₹</span>
                                            {{ Number(get_all_data.sub_total -
                                                get_all_data.total_tax).toFixed(2) }}
                                        </div>
                                    </div>
                                    <!--total tax-->
                                    <div v-if="items_data && items_data.length > 0 && formData[0].gstNumber !== ''"
                                        class="grid grid-cols-2"
                                        :class="{ 'non-printable': get_all_data.total_tax === 0 }"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">

                                        <div class="text-left p-1">
                                            CGST (₹)
                                        </div>
                                        <div class="text-end p-1"><span>₹</span>
                                            {{ (get_all_data.total_tax / 2).toFixed(2) }}
                                        </div>
                                    </div>
                                    <div v-if="items_data && items_data.length > 0 && formData[0].gstNumber !== ''"
                                        :class="{ 'non-printable': get_all_data.total_tax === 0 }"
                                        class="grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">

                                        <div class="text-left p-1">SGST (₹)</div>
                                        <div class="text-end p-1"><span>₹</span> {{ (get_all_data.total_tax /
                                            2).toFixed(2) }}
                                        </div>
                                    </div>
                                    <!--shipping charges-->
                                    <div v-if="items_data && items_data.length > 0 && get_all_data.shipping > 0"
                                        class="grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }"
                                        :class="{ 'non-printable': get_all_data.shipping === '' || get_all_data.shipping === 0 }">
                                        <div class="text-left p-1">Shipping (₹)</div>
                                        <div class="flex items-center justify-end">
                                            <p class="p-1 text-center"><span>₹</span> {{ get_all_data.shipping
                                            }}</p>
                                        </div>
                                    </div>
                                    <!--grand total-->
                                    <div v-if="items_data && items_data.length > 0" class="grid grid-cols-2"
                                        :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }">
                                        <div class="text-left p-1 text-md font-semibold border border-red-400"><span
                                                class="uppercase">Grand
                                                Total
                                                (₹)</span></div>
                                        <div class="text-end text-md p-1 font-semibold"><span>₹</span>
                                            {{ formatCurrency(Math.round(get_all_data.grand_total)) }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                        <!---make gap 2-->
                        <tr>
                            <td colspan="8" class="py-1"></td>
                        </tr>
                        <!--Payment Details-->
                        <tr v-if="typeOfInvoice !== 'estimation'" class="border"
                            :style="{ backgroundColor: formData[0].color, color: formData[0].text_color }"
                            :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                            <td colspan="8" class="text-center font-semibold py-1">Payment Details</td>
                        </tr>
                        <!--Payament type-->
                        <tr v-if="typeOfInvoice !== 'estimation' && paymentData && paymentData.length > 0"
                            class="border"
                            :class="{ 'non-printable': !paymentData[0].paid_amount, 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }"
                            v-for="(row, rowIndex) in paymentData" :key="rowIndex">
                            <td colspan="4" class="text-center px-2 border-r font-semibold">
                                {{ row.paid_type }}
                            </td>
                            <td colspan="4" class="px-1">
                                <p class="text-center">{{ row.paid_amount ? row.paid_amount.toFixed(2) : 0.00 }}
                                </p>
                            </td>
                        </tr>
                        <!--Return amount or balance-->
                        <tr v-if="typeOfInvoice !== 'estimation' && (return_amount.balance > 0 || return_amount.return > 0)"
                            class="border"
                            :class="{ 'non-printable': return_amount.balance === 0 && return_amount.return === 0, 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">
                            <td colspan="4" class="text-center p-1 border-r font-semibold">
                                <!-- <span>{{return_amount}}</span>                         -->
                                <span v-if="return_amount.return > 0">Return(₹)</span>
                                <span v-if="return_amount.balance > 0">Balance(₹)</span>
                            </td>
                            <td colspan="4" class="px-1 text-center font-bold">₹ {{ return_amount.balance > 0 ?
                                return_amount.balance.toFixed(2) : return_amount.return.toFixed(2) }}</td>
                        </tr>
                        <!-- Display grand total in text format -->
                        <!-- <tr class="border">
                        <td colspan="8" class="text-center p-1 text-md border-r"><strong>Total Amount (in words) : {{
                            convertToWords(Math.round(get_all_data.grand_total)) }} Only</strong>
                        </td>
                    </tr> -->
                    </tbody>
                </table>
            </div>
            <!---Amoint in words-->
            <!-- <div class="bg-white mt-2 p-3 py-2 border">
            <p>Total Amount (in words):</p>
            <p class="font-semibold text-gray-500">{{ convertToWords(Math.round(get_all_data.grand_total)) }} Only</p>

        </div> -->
            <!--footer-->
            <div class="flex w-full bg-white py-2">
                <div v-if="formData[0].disclaimer !== ''" class="p-3 py-2 border w-full">
                    <p class="font-bold"
                        :class="{ 'text-sm': !isAndroid || isPrinting, 'text-[7px]': isAndroid && !isPrinting }">Terms &
                        Conditions:</p>
                    <p class="px-2"
                        :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }"
                        v-if="typeOfInvoice !== 'estimation' && formData[0].disclaimer"
                        v-for="(line, index) in formData[0].disclaimer.split('\n')" :key="index">
                        <span>{{ index + 1 }}. </span>{{ line }}
                    </p>
                    <p class="px-2"
                        :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }"
                        v-if="typeOfInvoice === 'estimation' && formData[0].est_disclaimer"
                        v-for="(line, index) in formData[0].est_disclaimer.split('\n')" :key="index">
                        <span>{{ index + 1 }}. </span>{{ line }}
                    </p>
                </div>
                <div class="w-1/3 border-r border-b border-t pt-3">
                    <p class="px-2 font-semibold text-center w-full"
                        :class="{ 'text-xs': !isAndroid || isPrinting, 'text-[5px]': isAndroid && !isPrinting }">
                        For {{
                            formData[0].name }}</p>
                </div>
            </div>
        </div>
        <div class="flex justify-center text-sm  items-center mt-5 non-printable" :class="{ 'hidden': isPrinting }">
            <button @click="backToSetting"
                class="p-2 bg-gray-700 hover:bg-gray-600 text-white rounded pl-3 pr-3 mr-2 sm:mr-4 lg:mr-12">Back</button>
            <button @click="editTheESTInvoice"
                class="p-2 bg-blue-700 hover:bg-blue-600 text-white rounded pl-3 pr-3 mr-2 sm:mr-4 lg:mr-12">Edit
                {{ typeOfInvoice === 'estimation' ? 'Estimation' : 'Invoice' }}</button>

            <button @click="printInvoice"
                class="text-sm p-2 bg-green-700 hover:bg-green-600 text-white rounded pl-3 pr-3">
                {{ typeOfInvoice === 'estimation' ? 'Print Estimation' : 'Print Invoice' }}
            </button>
            <button @click="generatePDF"
                class="text-sm p-2 bg-green-700 hover:bg-green-600 text-white rounded pl-3 pr-3 ml-3">
                Download
            </button>
        </div>
        <div class="non-printable">
            <Loader :showModal="open_loader"></Loader>
        </div>
    </div>

</template>

<script>
import jsPDF from 'jspdf';
import QRCode from 'qrcode';
import { mapGetters, mapActions } from 'vuex';
export default {
    props: {
        formData: {
            type: Object,
            default: () => ({})
        },
        viewServiceData: {
            type: Object,
            default: () => ({})
        },
        categoryID: {
            type: [Number, String],
            default: null
        },
        typeOfInvoice: {
            type: String,
            default: ''
        },
        service_id: {
            type: [Number, String],
            default: null
        },
        category_name: {
            type: [Number, String],
            default: null
        },
        customer_data: {
            type: Object,
            default: () => ({})
        },
        items_data: {
            type: Object,
            default: () => ({})
        },
        get_all_data: {
            type: Object,
            default: () => ({})
        },
        paymentData: {
            type: Object,
            default: () => ({})
        },
        payment_display: {
            type: Object,
            default: () => ({})
        },
        invoice_data: {
            type: Object,
            default: () => ({})
        },
        return_amount: {
            type: Object,
            default: () => ({})
        },
        logo_img: {
            type: String,
            default: null
        },
        page_name: {
            type: String,
            default: null
        },
        exist_data: {
            type: Object,
            default: () => ({})
        },
        companyId: {
            type: String,
            default: null,
        },
        isMobile: {
            type: Boolean,
            default: false,
        },
        isAndroid: {
            type: Boolean,
            default: false,
        }
    },
    data() {
        return {
            currentDate: '',
            currentTime: '',
            isPrinting: false,
            open_loader: false,
            qrCodeUrl: '',
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('statecode', ['getStateCode']),
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),
        //---total discount--
        totalDiscount() {
            if (this.items_data && this.items_data.length > 0) {
                return this.items_data.reduce((sum, item) => (1 * sum) + (1 * item.discount), 0);
            }
        },
        isViewSalesPage() {
            return window.location.href.includes('viewsales');
        }
    },
    methods: {
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('statecode', ['getStateCodeData']),
        formatCurrency(value) {
            // Ensure value is a number before using toFixed
            const numericValue = parseFloat(value);
            if (isNaN(numericValue)) {
                // Handle the case where the value is not a valid number
                return "Invalid Amount";
            }

            return `${numericValue.toFixed(2)}`;
        },
        getCurrentDateTime() {
            const now = new Date();
            // Format the date
            const options = { day: '2-digit', month: '2-digit', year: 'numeric' };
            this.currentDate = now.toLocaleDateString(undefined, options).replace(/\//g, '/');
            // Format the time
            this.currentTime = now.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        },
        convertToWords(amount) {
            const oneToTwenty = ['Zero', 'One', 'Two', 'Three', 'Four', 'Five', 'Six', 'Seven', 'Eight', 'Nine', 'Ten', 'Eleven', 'Twelve', 'Thirteen', 'Fourteen', 'Fifteen', 'Sixteen', 'Seventeen', 'Eighteen', 'Nineteen'];
            const tens = ['', '', 'Twenty', 'Thirty', 'Forty', 'Fifty', 'Sixty', 'Seventy', 'Eighty', 'Ninety'];

            const convertBelowHundred = (num) => {
                if (num < 20) {
                    return oneToTwenty[num];
                } else {
                    return tens[Math.floor(num / 10)] + ' ' + oneToTwenty[num % 10];
                }
            };

            const convertBelowThousand = (num) => {
                if (num < 100) {
                    return convertBelowHundred(num);
                } else {
                    return oneToTwenty[Math.floor(num / 100)] + ' Hundred ' + convertBelowHundred(num % 100);
                }
            };

            const convertGroup = (num, suffix) => {
                if (num === 0) {
                    return '';
                } else {
                    return convertBelowThousand(num) + ' ' + suffix;
                }
            };

            if (amount === 0) {
                return 'Zero Rupees';
            } else {
                const groups = [];
                let remaining = amount;

                const suffixes = ['', 'Thousand', 'Million', 'Billion', 'Trillion', 'Quadrillion', 'Quintillion', /* and so on... */];

                for (let i = 0; remaining > 0; i++) {
                    const groupValue = remaining % 1000;
                    remaining = Math.floor(remaining / 1000);
                    groups.push(convertGroup(groupValue, suffixes[i]));
                }
                return groups.reverse().join(' ').trim() + ' Rupees';
            }
        },
        //---go back
        backToSetting() {
            // this.printing = false;
            if (this.page_name === 'setting') {
                this.$emit('goSetting');
            } else if (this.$route.query.type === 'sales_home') {
                this.$router.go(-1);
            } else if (this.checkRoles(['Sub_Admin', 'admin', 'Account Manager', 'Service Manager', 'Sales man'])) {
                if (this.typeOfInvoice !== 'estimation') {
                    this.$router.push('/sales');
                } else {
                    this.$router.push('/estimation');
                }
                /*if (this.typeOfInvoice === 'Product' && this.exist_data) {
                    this.$router.push({
                        name: 'sales-invoice',
                        query: { type: 'edit', invoice_no: this.exist_data.id }
                    });

                } else if (this.typeOfInvoice === 'Services' && this.exist_data) {
                    this.getServiceData(this.exist_data);
                } else if (this.typeOfInvoice === 'estimation' && this.exist_data && this.$route.query.back !== 'home') {
                    this.$router.push({
                        name: 'addEstimation', // Name of the route
                        params: { type: 'product' }, // Parameter passed in the route path
                        query: { // Query parameters passed in the URL
                            type: 'edit',
                            est_no: this.exist_data.id
                        }
                    });
                }
                else {
                    this.$router.go(-1);
                }*/
            } else {
                this.$router.push('/');
            }
        },
        editTheESTInvoice() {
            if (this.typeOfInvoice !== 'estimation') {
                this.$router.push({
                    name: 'sales-invoice',
                    query: { type: 'edit', invoice_no: this.exist_data.id }
                });
            } else {
                this.$router.push({
                    name: 'addEstimation', // Name of the route
                    params: { type: 'product' }, // Parameter passed in the route path
                    query: { // Query parameters passed in the URL
                        type: 'edit',
                        est_no: this.exist_data.id
                    }
                });

            }
        },

        printInvoice() {
            this.isPrinting = true;
            this.open_loader = true;
            // Use window.print() to trigger the browser's print dialog
            this.$emit('sucessPrint');

            setTimeout(() => {
                // if (!this.isAndroid) {
                const options = {
                    name: "_blank",
                    specs: [
                        "fullscreen=yes",
                        "titlebar=yes",
                        "scrollbars=yes"
                    ],
                    styles: [
                        "https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css",
                        "https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css",
                        "https://unpkg.com/kidlat-css/css/kidlat.css",
                        "../../../../assets/main.css",
                    ],
                    timeout: 3000,
                    autoClose: true,
                    windowTitle: `${this.invoice_data.invoice_number ? this.invoice_data.invoice_number : 'invoice'}`,
                    paperSize: {
                        format: 'A4',
                        orientation: 'portrait', // or 'landscape' if you want
                        margin: '1cm' // Default margin is 1cm
                    }
                };
                this.$htmlToPaper('printable-content', options);
                // } else 
                // this.print();
                // }
                setTimeout(() => {
                    this.open_loader = false;
                    this.isPrinting = false;
                }, 500);
                // if (this.page_name !== 'setting' && this.typeOfInvoice !== 'estimation') {
                //     this.$router.push('/sales')
                // }
                // if (this.page_name !== 'setting' && this.typeOfInvoice === 'estimation') {
                //     this.$router.push('/estimation')
                // }
            }, 500);

            // After printing, reset the state
            // this.printing = false;
            // this.backToSetting();
            // this.$router.go(-1);
        },

        // printInvoice() {
        //     this.isPrinting = true;
        //     setTimeout(() => {
        //         html2pdf(document.getElementById("printable-content"), {
        //             margin: [10, 10, 10, 10],
        //             padding: 0,
        //             filename: "invoice_data.pdf",
        //             image: { type: 'jpeg', quality: 1.0 },
        //             html2canvas: { dpi: 75, scale: 1, scrollY: 0, dpi: 192, letterRendering: true },
        //             pagebreak: { mode: ['avoid-all', 'css', 'legacy'] },
        //             jsPDF: { orientation: 'portrait', unit: 'mm', format: 'a4', compressPDF: true },
        //         });
        //         this.isPrinting = false;
        //     }, 500)

        // },
        getServiceData(record) {
            axios.get(`/services/${record.service_id}`, { company_id: this.companyId })
                .then(response => {
                    console.log(response.data, 'What happening the get service..!');
                    this.service_data = response.data.data;
                    // let service_track_data = JSON.parse(newValue.service_data);
                    this.$router.push({
                        name: 'generate-invoice',
                        params: { type: this.service_data.servicecategory.service_category, id: this.service_data.servicecategory.id, serviceId: record.service_id },
                        query: { type: 'edit', invoice_no: record.id }
                    });
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        calculateRemainingSpace() {
            // Calculate the height of the printed content
            const printedContentHeight = document.querySelector('.prindContent').offsetHeight;
            // Height of A4 paper in pixels (approximately 842px)
            const a4PaperHeight = !this.isAndroid ? 1024 : 600;
            // Calculate the remaining height of the A4 paper
            const remainingHeight = a4PaperHeight - printedContentHeight;
            // Log the calculated remaining space
            // console.log('Remaining Space:', remainingHeight);
            // console.log(remainingHeight, 'RRRRR');
            // Return the remaining height
            return remainingHeight;
        },
        getFormattedDateTime(dateData, isDate) {
            const currentDate = new Date(dateData);
            const day = String(currentDate.getUTCDate()).padStart(2, '0');
            const month = String(currentDate.getUTCMonth() + 1).padStart(2, '0');
            const year = currentDate.getUTCFullYear();
            let hours = currentDate.getUTCHours();
            const minutes = String(currentDate.getUTCMinutes()).padStart(2, '0');
            const ampm = hours >= 12 ? 'PM' : 'AM';
            hours = hours % 12;
            hours = hours ? hours : 12;
            if ((hours === 12 && ampm == 'AM') || isDate) {
                const formattedDate = `${day}-${month}-${year}`;
                return formattedDate;
            } else {
                const formattedDate = `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
                return formattedDate;
            }
        },
        //---download pdf---
        generatePDF() {
            // console.log(this.exist_data, 'GGGGGGGGG');

            // console.log(this.exist_data, 'What about the data...', this.invoice_data);
            const doc = new jsPDF("p", "mm", "a4");
            const pageHeight = doc.internal.pageSize.height;
            const pageWidth = doc.internal.pageSize.width;
            const marginTop = 15; // Reduced header height
            const marginBottom = 50;
            const rowHeight = 8; // Reduce row height for better fit
            const recordsPerPage = 20;
            let currentY = marginTop;
            let pageNumber = 1;

            const formatDate = (isoDate) => {
                if (!isoDate) return "";
                const date = new Date(isoDate);
                return date.toLocaleDateString("en-GB", { day: "2-digit", month: "2-digit", year: "numeric" });
            };

            const drawHeader = (doc, pageNumber) => {
                doc.setFontSize(8);
                doc.setFont("helvetica", "bold");
                const taxInvoiceX = 14;
                const originalBoxX = pageWidth / 2 - 25;
                const originalBoxY = 8;
                const b2bTextX = pageWidth - 15;
                // Draw "TAX INVOICE" (Left)
                doc.text("TAX INVOICE", taxInvoiceX, originalBoxY + 5);
                // Draw "ORIGINAL FOR RECIPIENT" (Centered with rounded border)
                // doc.roundedRect(originalBoxX, originalBoxY, 60, 8, 2, 2);
                doc.setFont("helvetica", "normal").text("ORIGINAL FOR RECIPIENT", originalBoxX + 5, originalBoxY + 5);
                // Draw "B2B / B2C" (Right)
                doc.text(this.invoice_data?.invoice_type?.toUpperCase() || "B2B / B2C", b2bTextX, originalBoxY + 5, { align: "right" });
                // Business Details Box
                doc.setDrawColor(200, 200, 200);
                doc.rect(10, 15, pageWidth - 20, 25);
                // Business Name (Bold & Larger)
                doc.setFontSize(10).setFont("helvetica", "bold").text(this.formData[0].name, 14, 20);
                // Business Info (Address, GST, Mobile)
                doc.setFontSize(8);
                doc.setFont("helvetica", "normal");
                doc.text(this.formData[0].address, 14, 25);
                doc.text(`GSTIN: ${this.formData[0].gst_number}`, 14, 30);
                doc.text(`Mobile: ${this.formData[0].business_contact}`, 14, 35);

                // Invoice Details (Right-aligned)
                const invoiceSectionX = pageWidth - 70;
                doc.line(invoiceSectionX, 15, invoiceSectionX, 40);
                doc.setFont("helvetica", "bold").text("Invoice No.", pageWidth - 15, 20, { align: "right" });
                doc.setFont("helvetica", "normal").text(`${this.invoice_data?.invoice_number || ''}`, pageWidth - 15, 25, { align: "right" });
                doc.setFont("helvetica", "bold").text("Invoice Date", pageWidth - 15, 30, { align: "right" });
                doc.setFont("helvetica", "normal").text(`${formatDate(this.invoice_data?.current_date)}`, pageWidth - 15, 35, { align: "right" });
            };

            const drawShippingAndBillingDetails = (doc, pageNumber) => {
                let yPosition = 47; // Starting position for "BILL TO" section
                let billtoX = 14;
                let shiptoX = (pageWidth / 2) - 5;
                let yBillTo = yPosition;
                let yShipTo = yPosition;

                doc.setFontSize(8).setFont("helvetica", "bold");

                // Calculate height dynamically
                const getTextHeight = (text) => (text ? 5 : 0); // Each line adds 5 height

                let billToHeight = 0;
                let shipToHeight = 0;

                if (this.exist_data.customer?.first_name || this.exist_data.customer?.last_name) {
                    billToHeight += getTextHeight(true);
                    shipToHeight += getTextHeight(true);
                }

                if (this.exist_data.customer?.address) {
                    billToHeight += getTextHeight(true);
                    shipToHeight += getTextHeight(true);
                }

                if (this.exist_data.customer?.gst_number) {
                    billToHeight += getTextHeight(true);
                    shipToHeight += getTextHeight(true);
                }

                if (this.exist_data.customer?.contact_number) {
                    billToHeight += getTextHeight(true);
                    shipToHeight += getTextHeight(true);
                }

                // Calculate final box height (max content height + padding)
                let boxHeight = Math.max(billToHeight, shipToHeight) + 5;

                // Draw Box dynamically based on content height
                doc.setDrawColor(200, 200, 200);
                doc.rect(10, 42, pageWidth - 20, boxHeight + 5);

                // **BILL TO Section**
                if (this.exist_data.customer?.first_name || this.exist_data.customer?.last_name) {
                    doc.text('BILL TO', billtoX, yBillTo);
                    yBillTo += 5;
                    doc.text(`${this.exist_data.customer?.first_name || ''} ${this.exist_data.customer?.last_name || ''}`, billtoX, yBillTo);
                    yBillTo += 5;
                }

                doc.setFont("helvetica", "normal");

                if (this.exist_data.customer?.address) {
                    doc.text(this.exist_data.customer.address, billtoX, yBillTo);
                    yBillTo += 5;
                }

                if (this.exist_data.customer?.gst_number) {
                    doc.text(`GSTIN: ${this.exist_data.customer.gst_number}`, billtoX, yBillTo);
                    yBillTo += 5;
                }

                if (this.exist_data.customer?.contact_number) {
                    doc.text(`Mobile: ${this.exist_data.customer.contact_number}`, billtoX, yBillTo);
                }

                // **SHIP TO Section**
                if (billToHeight > 0 || shipToHeight > 0) {
                    doc.line(shiptoX - 4, 42, shiptoX - 4, 42 + boxHeight + 5);
                }

                doc.setFontSize(8).setFont("helvetica", "bold");

                if (this.exist_data.customer?.first_name || this.exist_data.customer?.last_name) {
                    doc.text('SHIP TO', shiptoX, yShipTo);
                    yShipTo += 5;
                    doc.text(`${this.exist_data.customer?.first_name || ''} ${this.exist_data.customer?.last_name || ''}`, shiptoX, yShipTo);
                    yShipTo += 5;
                }

                doc.setFont("helvetica", "normal");

                if (this.exist_data.customer?.address) {
                    doc.text(this.exist_data.customer.address, shiptoX, yShipTo);
                    yShipTo += 5;
                }

                if (this.exist_data.customer?.gst_number) {
                    doc.text(`GSTIN: ${this.exist_data.customer.gst_number}`, shiptoX, yShipTo);
                    yShipTo += 5;
                }

                if (this.exist_data.customer?.contact_number) {
                    doc.text(`Mobile: ${this.exist_data.customer.contact_number}`, shiptoX, yShipTo);
                }
            };



            const drawFooter = (doc, pageNumber) => {
                const footerY = pageHeight - 65; // Adjust footer position

                // Line above footer
                doc.setDrawColor(200, 200, 200);
                doc.line(10, footerY, pageWidth - 10, footerY);

                // **Total Amount in Words**
                doc.setFontSize(8);
                doc.setFont("helvetica", "bold");
                doc.text("Total Amount (in words):", 14, footerY + 5);
                doc.setFont("helvetica", "normal");
                doc.text("Five Thousand Two Hundred And Forty Rupees Only", 55, footerY + 5);

                // **Payment Details Table**
                doc.rect(10, footerY + 10, pageWidth - 20, 10); // Table Border
                doc.setFont("helvetica", "bold");
                doc.text("Payment Details", pageWidth / 2 - 15, footerY + 16);

                doc.rect(10, footerY + 20, (pageWidth - 20) / 2, 10); // Left cell
                doc.rect(10 + (pageWidth - 20) / 2, footerY + 20, (pageWidth - 20) / 2, 10); // Right cell

                doc.setFont("helvetica", "normal");
                doc.text(`Total Paid (₹): ${this.exist_data.paidAmount || "0.00"}`, 14, footerY + 26);
                doc.text(`Remaining Balance (₹): ${this.exist_data.balanceAmount || "0.00"}`, pageWidth / 2 + 14, footerY + 26);

                // **Terms and Conditions Box**
                const termsY = footerY + 35;
                doc.rect(10, termsY, pageWidth - 60, 25); // Box
                doc.setFont("helvetica", "bold");
                doc.text("Terms and Conditions", 14, termsY + 5);

                doc.setFont("helvetica", "normal");
                doc.setFontSize(7);
                doc.text("1. The invoice requires immediate payment; late fees apply.", 14, termsY + 10);
                doc.text("2. This is a legal document, and all payments are final.", 14, termsY + 15);
                doc.text("3. The client must pay additional charges for delays.", 14, termsY + 20);

                // **Authorized Signatory Box**
                const signX = pageWidth - 50;
                doc.rect(signX, termsY, 40, 25); // Signature Box
                doc.text("Authorized Signatory", signX + 7, termsY + 5);

                // Signature Line
                doc.line(signX + 5, termsY + 20, signX + 35, termsY + 20);
                doc.text("Authorized Signatory", signX + 7, termsY + 24);

                // **Page Number**
                doc.text(`Page ${pageNumber}`, pageWidth - 20, pageHeight - 2);
            };


            let records = [...(this.exist_data.sales_item_data || []), ...(JSON.parse(this.exist_data.service_items) || [])];
            let totalRecords = records.length;
            let startIndex = 0;

            while (startIndex < totalRecords) {
                drawHeader(doc, pageNumber);
                // Call function inside generatePDF()
                drawShippingAndBillingDetails(doc, pageNumber);

                // Adjust space between header and table
                currentY = 70;

                // Table Header (Dynamic Colors)
                const headerBgColor = [0, 0, 0]; // Light Gray Background
                const headerTextColor = [0, 0, 0]; // Black Text
                const columnWidths = [14, 36, 20, 15, 20, 20, 20, 20, 25]; // Adjust column widths

                doc.setFillColor(...headerBgColor);
                doc.setTextColor(...headerTextColor);
                doc.setFontSize(8);
                doc.setFont("helvetica", "bold");

                // **Table Header Fix**
                const headers = ["S.NO.", "ITEMS", "HSN CODE", "QTY", "RATE", "DISCOUNT", "TAX TYPE", "TAX", "AMOUNT"];
                let colX = 10;

                doc.setFontSize(8).setFont("helvetica", "bold");
                headers.forEach((header, i) => {
                    doc.rect(colX, currentY, columnWidths[i], rowHeight, "S");
                    doc.text(header, colX + 2, currentY + 5);
                    colX += columnWidths[i];
                });

                doc.setFont("helvetica", "normal");
                currentY += rowHeight;

                // **Item List with Wrapped Text**
                let pageRecords = records.slice(startIndex, startIndex + recordsPerPage);
                pageRecords.forEach((item, index) => {
                    if (!item) return;

                    let itemName = doc.splitTextToSize(item?.product_name || "", columnWidths[1] - 2);
                    let hsnCode = String(item?.hsn_code || "");
                    let itemQty = String(item?.qty || "0");
                    let itemRate = `₹${item?.price || "0"}`;
                    let itemDiscount = `₹${item?.discount || "0"}`;
                    let itemTaxValue = `${item?.taxvalue || "0"}%`;
                    let itemTax = `₹${item?.tax || "0"}`;
                    let itemAmount = `₹${item?.total || "0"}`;

                    colX = 10;
                    let maxLines = Math.max(itemName.length, 1);
                    let cellHeight = maxLines * 10; // Adjust row height based on content

                    // Draw Table Cells
                    doc.rect(colX, currentY, columnWidths[0], cellHeight, "S").text(String(startIndex + index + 1), colX + 2, currentY + 5, { align: "center" });
                    colX += columnWidths[0];

                    doc.rect(colX, currentY, columnWidths[1], cellHeight, "S").text(itemName, colX + 2, currentY + 5);
                    colX += columnWidths[1];

                    doc.rect(colX, currentY, columnWidths[2], cellHeight, "S").text(hsnCode, colX + 2, currentY + 5);
                    colX += columnWidths[2];

                    doc.rect(colX, currentY, columnWidths[3], cellHeight, "S").text(itemQty, colX + 2, currentY + 5);
                    colX += columnWidths[3];

                    doc.rect(colX, currentY, columnWidths[4], cellHeight, "S").text(itemRate, colX + 2, currentY + 5);
                    colX += columnWidths[4];

                    doc.rect(colX, currentY, columnWidths[5], cellHeight, "S").text(itemDiscount, colX + 2, currentY + 5);
                    colX += columnWidths[5];

                    doc.rect(colX, currentY, columnWidths[6], cellHeight, "S").text(itemTaxValue, colX + 2, currentY + 5);
                    colX += columnWidths[6];

                    doc.rect(colX, currentY, columnWidths[7], cellHeight, "S").text(itemTax, colX + 2, currentY + 5);
                    colX += columnWidths[7];

                    doc.rect(colX, currentY, columnWidths[8], cellHeight, "S").text(itemAmount, colX + 2, currentY + 5);

                    currentY += cellHeight;
                });


                drawFooter(doc, pageNumber);
                if (startIndex + recordsPerPage < totalRecords) {
                    doc.addPage();
                    pageNumber++;
                } else {
                    break;
                }

                startIndex += recordsPerPage;
            }

            doc.save("invoice.pdf");
        },
        //---print methods----
        print() {
            //---second type---
            // Get HTML to print from element
            const prtHtml = document.getElementById('printable-content').innerHTML;

            // Get all stylesheets HTML
            let stylesHtml = '';
            for (const node of [...document.querySelectorAll('link[rel="stylesheet"], style')]) {
                stylesHtml += node.outerHTML;
            }
            // Include Tailwind CSS directly in stylesHtml
            stylesHtml += `
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css">
        <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
        <link rel="stylesheet" href="https://unpkg.com/kidlat-css/css/kidlat.css">`;
            //---<link rel="stylesheet" href="../../../../assets/main.css">

            // Open the print window
            const WinPrint = window.open('', '', 'left=0,top=0,width=800,height=900,toolbar=0,scrollbars=0,status=0');
            WinPrint.document.write(`<!DOCTYPE html><html><head>${stylesHtml}</head><body>${prtHtml}</body></html>`);
            WinPrint.document.close();
            WinPrint.focus();
            WinPrint.print();
            WinPrint.close();
        },
        //---validate the role
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        generateQRCode(bank_details) {
            console.log(bank_details, 'What happpp');
            let upiUrl = '';
            let total = this.get_all_data ? Math.round(this.get_all_data.grand_total) : 0;
            if (bank_details.upi_id && bank_details.upi_id !== '') {
                // Example format: upi://pay?pa=<UPI_ID>&pn=<Name>&mc=<MerchantCode>&tid=<TransactionId>&tn=<TransactionNote>&am=<Amount>&cu=<CurrencyCode>
                upiUrl = `upi://pay?pa=${bank_details.upi_id}&pn=YourBusinessName&am=${total}&cu=INR`;
            } else if (bank_details.g_pay && bank_details.g_pay !== '') {
                upiUrl = `upi://pay?pa=${bank_details.g_pay}@upi&pn=JohnDoeBusiness&am=${total}&cu=INR`;

            } else if (bank_details.phone_pay && bank_details.phone_pay !== '') {
                upiUrl = `upi://pay?pa=${bank_details.phone_pay}@upi&pn=JohnDoeBusiness&am=${total}&cu=INR`;
            }
            // Generate QR Code
            QRCode.toDataURL(upiUrl)
                .then(url => {
                    this.qrCodeUrl = url;
                    return url;
                })
                .catch(err => {
                    console.error("Error generating QR code:", err);
                });
        }

    },
    mounted() {

        this.getCurrentDateTime(); // Update every second if you want a live clock
        setInterval(() => {
            this.getCurrentDateTime();
        }, 60000);
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        if (this.formData && this.formData.length > 0 && this.formData[0].bank_details && (JSON.parse(this.formData[0].bank_details).upi_id || JSON.parse(this.formData[0].bank_details).g_pay || JSON.parse(this.formData[0].bank_details).phone_pay)) {
            this.generateQRCode(JSON.parse(this.formData[0].bank_details));
        }
        this.getStateCodeData();
    },
    watch: {
        formData: {
            deep: true,
            handler(newvalue) {
                if (newvalue) {
                    if (newvalue && newvalue.length > 0 && newvalue[0].bank_details && (JSON.parse(newvalue[0].bank_details).upi_id || JSON.parse(newvalue[0].bank_details).g_pay || JSON.parse(newvalue[0].bank_details).phone_pay)) {
                        this.generateQRCode(JSON.parse(newvalue[0].bank_details));
                    }
                }
            }
        }
    }

};
</script>

<style scoped>
#printable-content {
    /* font-family: Arial, sans-serif; */
    font-size: 14px;
    line-height: 1.5;
    padding: 20px;
    overflow: visible;
}

* {
    margin-top: 0px;
    padding-top: 0px;
    height: auto;
}

.print-only {
    display: block;
}

.outline {
    border: 1px solid black;
    /* Set your desired border style and color */
    border-radius: 5px;
    padding: 2px;
}

.filter {
    filter: blur(10px);
    /* Initial blur value */
    transition: filter 0.5s ease-in-out;
    /* Smooth transition */
}

.filter:hover {
    filter: blur(0);
    /* Remove blur on hover */
}

.actions-column {
    position: absolute;
    transform: translateY(-0%);
}

/* .border {
    border: 1px solid black;
}

.border-r {
    border-right: 1px solid black;
}

.border-l {
    border-left: 1px solid black;
}

.border-t {
    border-top: 1px solid black;
}

.border-b {
    border-bottom: 1px solid black;
} */

@media print {
    @tailwind base;
    @tailwind components;
    @tailwind utilities;

    .page-break {
        page-break-after: always;
        clear: both;
    }

    /* Additional styles for printing */
    body {
        height: 100%;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Define different page sizes based on @page rule */
    @page {
        size: A4;
    }

    /* Ensure the container fills the entire page */
    .m-2 {
        margin: 5px 5px;
        height: 100%;
    }

    /* Ensure all elements fit within the page */
    * {
        overflow: hidden !important;
    }

    .non-printable {
        display: none;
    }
}
</style>
