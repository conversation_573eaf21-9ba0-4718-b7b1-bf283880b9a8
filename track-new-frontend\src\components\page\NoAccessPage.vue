<template>
    <div
        class="flex items-center justify-center min-h-screen bg-gradient-to-r from-purple-500 via-indigo-500 to-blue-500">
        <!-- Content Wrapper -->
        <div class="w-full max-w-md p-8 bg-white rounded-xl shadow-lg">
            <div class="text-center">
                <!-- Header -->
                <h1 class="text-3xl font-extrabold text-gray-900 mb-4">Upgrade Required</h1>

                <!-- Message -->
                <p class="text-md text-gray-600 mb-6">
                    You do not have access to this page based on your current plan. Please upgrade your plan to unlock
                    this feature.
                </p>

                <!-- Action Button -->
                <router-link to="/subscription"
                    class="inline-block px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-all duration-200 transform hover:scale-105">
                    Upgrade Plan
                </router-link>
            </div>
        </div>
    </div>
</template>

<script>
import { useMeta } from '@/composables/useMeta';

export default {
    name: 'NoAccessPage',
    data() {
        return {
            isMobile: false,
        };
    },
    setup() {
        const pageTitle = 'No Access';
        const pageDescription = 'You do not have access to this page based on your current plan';
        useMeta(pageTitle, pageDescription);
        return { pageTitle };
    },
    methods: {
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
};
</script>

<style scoped>
/* Hide scrollbar for a cleaner look */
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>