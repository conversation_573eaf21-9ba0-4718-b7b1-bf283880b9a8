<template>
    <div class="fixed-header flex justify-between items-center non-printable headBar ">
        <!-- Menu icon button for mobile view -->
        <button v-if="isMobile" @click="toggleSidebar" class="block sm:hidden text-xl px-2 sm:px-4 py-2 text-black">
            ☰
        </button>
        <div class="flex">
            <div class="h-[18px] py-9 sm:h-[18px] text-left center text-sm items-center flex justify-center p-5 cursor-pointer hover:bg-white rounded-tr-[20px]"
                :class="{ 'bg-white': checkPageUrl().toLowerCase() === 'items' }" @click="navigatePage('items')">
                <span v-if="!isMobile" class="px-1">Items</span>
                <img :src="items" alt="product" class="w-5 h-5" />
            </div>
            <!---Purchase order--->
            <div class="h-[18px] py-9 sm:h-[18px] text-left center text-sm items-center flex justify-center p-5 cursor-pointer hover:bg-white rounded-tr-[20px]  rounded-tl-[20px] sm:ml-1"
                :class="{ 'bg-white': checkPageUrl().toLowerCase() === 'purchaseorder' }"
                @click="navigatePage('purchase')">
                <span v-if="!isMobile" class="px-1">Purchase</span>
                <img :src="product" alt="purchase image" class="w-5 h-5" />
            </div>
            <!---Supplier--->
            <div class="h-[18px] py-9 sm:h-[18px] text-left center text-sm items-center flex justify-center p-5 cursor-pointer hover:bg-white rounded-tr-[20px]  rounded-tl-[20px] sm:ml-1"
                :class="{ 'bg-white': checkPageUrl().toLowerCase() === 'supplier' }" @click="navigatePage('supplier')">
                <span v-if="!isMobile" class="px-1">Supplier</span>
                <img :src="supplier" alt="supplier image" class="w-5 h-5" />
            </div>
            <!---Warehouse--->
            <div class="h-[18px] py-9 sm:h-[18px] text-left center text-sm items-center flex justify-center p-5 cursor-pointer hover:bg-white rounded-tr-[20px]  rounded-tl-[20px] sm:ml-1"
                :class="{ 'bg-white': checkPageUrl().toLowerCase() === 'warehouse' }"
                @click="navigatePage('warehouse')">
                <span v-if="!isMobile" class="px-1">Warehouse</span>
                <img :src="warehouse" alt="warehouse image" class="w-5 h-5" />
            </div>
            <!---stock adjustment--->
            <div class="h-[18px] py-9 sm:h-[18px] text-left center text-sm items-center flex justify-center p-5 cursor-pointer hover:bg-white rounded-tr-[20px]  rounded-tl-[20px] sm:ml-1"
                :class="{ 'bg-white': checkPageUrl().toLowerCase() === 'stock' }" @click="navigatePage('stock')">
                <span v-if="!isMobile" class="px-1">Stock</span>
                <img :src="adjustment" alt="stock image" class="w-5 h-5" />
            </div>
        </div>

        <div class="flex items-center p-2">
            <p v-if="!isMobile" class="text-xs px-3">&#128222; Help: <a href="https://eagleminds.net" target="_blank"
                    class="hover:text-green-700 hover:underline">+91-8233823309</a></p>
            <!-- <img class="h-[25px] sm:h-[30px] mr-3" :src="images[1]" alt="notification" /> -->
            <!-- <span
                class="inline-flex items-center rounded-full bg-red-500 pl-1 pr-1 text-[8px] sm:text-[10px] font-medium text-white ring-1 ring-inset ring-red-600/10 absolute ml-3 sm: mr-5 top-[15px] sm:top-[30px]">
                {{ showBadges_notify }}
            </span>
            <span
                class="inline-flex items-center rounded-full bg-violet-500 pl-1 pr-1 text-[8px] sm:text-[10px] font-medium text-white text-center ring-1 ring-inset ring-green-600/10 right-[15px] mt-5 sm:right-[15px] sm: mt-3 absolute">
                &#10003;
            </span> -->
            <img class="w-[40px] h-[40px] sm:w-[50px] sm:h-[50px] mr-2" :src="images[0]" alt="user"
                @click="showLogoutModal = true" />
        </div>
        <div v-show="showLogoutModal" class="absolute z-50 transform right-[210px] mt-10">
            <div v-show="showLogoutModal" class="absolute bg-white border border-gray-300 p-4 rounded shadow-md">
                <h3 class="text-lg font-semibold mb-2">Confirm Logout</h3>
                <p class="text-sm mb-4">Are you sure you want to logout?</p>
                <div class="flex justify-between">
                    <button @click="logout"
                        class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none focus:bg-red-600 mr-3">Logout</button>
                    <button @click="showLogoutModal = false"
                        class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 focus:outline-none focus:bg-gray-400">Cancel</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    name: 'headbar',
    props: {
        productData: Object,
    },
    data() {
        return {
            images: [
                '/images/head_bar/User.png',
                '/images/head_bar/Notification.png',
            ],
            showBadges_notify: 2,
            showBadges_status: true,
            isMobile: false,
            items: '/images/customer_page/product.png',
            product: '/images/customer_page/purchase.png',
            supplier: '/images/customer_page/supplier.png',
            warehouse: '/images/customer_page/warehouse.png',
            adjustment: '/images/customer_page/adjustment.png',
            showLogoutModal: false,
        };
    },

    methods: {
        toggleSidebar() {
            this.$emit('toggle-sidebar');
        },
        updateIsMobile() {
            // Update isMobile based on the viewport width
            // this.$forceUpdate();
            this.isMobile = window.innerWidth < 1024;
        },
        //---navigation---
        navigatePage(type) {
            if (type === 'items') {
                this.$router.push('/items');
            }
            else if (type === 'purchase') {
                this.$router.push({ name: 'purchase_order' });
            }
            else if (type === 'warehouse') {
                this.$router.push({ name: 'warehouse' });
            } else if (type === 'supplier') {
                this.$router.push({ name: 'supplier' });
            } else if (type === 'stock') {
                this.$router.push({ name: 'stock' });
            }
        },
        //---validate page url--
        checkPageUrl() {
            // Get the current URL
            const currentUrl = window.location.pathname;

            // Split the URL by '/'
            const urlParts = currentUrl.split('/');

            // Extract the last part of the URL
            const lastPart = urlParts[urlParts.length - 1];
            // console.log(lastPart, 'What happening...!');
            // Return the last part of the URL
            return lastPart;
        },
        logout() {
            // Clear the 'track_new' key from local storage
            localStorage.removeItem('track_new');
            this.$router.push('/login');
        }

    },
    mounted() {
        this.updateIsMobile(); // Initial check
        // Add a resize event listener to dynamically update isMobile
        window.addEventListener('resize', this.updateIsMobile);
        this.placeholderText = 'Enter the product details';
    },
    beforeDestroy() {
        // Remove the resize event listener when the component is destroyed
        window.removeEventListener('resize', this.updateIsMobile);
    },
};
</script>
<style scoped>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}

/* Style for the dropdown */
ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

li {
    padding: 8px;
    cursor: pointer;
}

li:hover {
    background-color: #f0f0f0;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
    }

    .non-printable {
        display: none;
    }
}
</style>