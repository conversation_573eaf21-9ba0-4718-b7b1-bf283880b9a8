<template>
    <div class="bg-gradient-to-r from-green-400 to-blue-500 min-h-screen flex items-center justify-center">
        <div
            class="flex flex-col md:flex-row w-full max-w-5xl h-auto md:h-[90vh] shadow-lg rounded-lg overflow-hidden bg-white">
            <!-- Left Side -->
            <div class="relative w-full md:w-1/2 hidden md:block">
                <img :src="imgUrl" class="absolute inset-0 w-full h-full object-cover" />
                <div class="bg-blue-500 opacity-85 absolute inset-0"></div>
                <img :src="'/images/head_bar/tracknew logo 1.png'" class="relative h-10 w-44 inset-0 mt-4">
                <div class="relative flex flex-col justify-center items-center h-full p-6 z-10 text-center">
                    <h1 class="text-3xl text-white font-bold mt-32 mb-2 glow">TRACK NEW</h1>
                    <div class="quote-selector flex flex-col items-start pl-4">
                        <fa icon="quote-left" class="text-green-500 text-4xl mb-2" />
                        <p class="text-white italic">{{ currentQuote }}</p>
                    </div>
                    <div class="icon-selector flex space-x-4 mt-4 mb-4 justify-center">
                        <fa icon="minus" class=" text-white" :class="{ 'glow-icon': currentIndex === 0 }" />
                        <fa icon="minus" class=" text-white" :class="{ 'glow-icon': currentIndex === 1 }" />
                        <fa icon="minus" class="text-white" :class="{ 'glow-icon': currentIndex === 2 }" />
                    </div>
                </div>
            </div>

            <!-- Right Side with Registration Form -->
            <div class="w-full md:w-1/2 bg-white flex flex-col justify-center items-center p-8 overflow-y-auto">
                <!-- Mobile screen: show the image, hide it on larger screens -->
                <img :src="'/images/head_bar/logo-back.png'" class="h-10 w-44 mt-6 block mx-auto md:hidden">
                <h2 class="text-2xl font-bold text-blue-700 mt-2 mb-2">Welcome Back!</h2>
                <p class="text-gray-500 mb-2">Sign in to continue to Track New.</p>
                <div class="w-full ">

                    <div class="mb-4">
                        <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                        <input type="text" v-model="formValues.login" placeholder="Enter Email / Mobile" ref="user_name"
                            @input="validatePhoneNumber"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-blue-700': isfocussed.email, 'border-red-700': showError }"
                            @focus="isfocussed.email = true" @blur="isfocussed.email = false" />
                        <p v-if="showError && validate_msg" class="text-red-500 text-xs mt-1">{{ validate_msg }}</p>
                    </div>

                    <div class="mb-4">
                        <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
                        <input type="password" v-model="formValues.password" placeholder="Enter password"
                            @input="checkPasswordLength"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-blue-700': isfocussed.password, 'border-red-700': showPasswordError }"
                            @focus="isfocussed.password = true" @blur="isfocussed.password = false"
                            @keyup.enter="loginUser" />
                        <p v-if="showPasswordError && passwordErrorMsg" class="text-red-500 text-xs mt-1">{{
                            passwordErrorMsg }}</p>
                    </div>
                    <div class="mb-4">
                        <button type="button" @click="loginUser"
                            class="w-full bg-green-500 text-white py-2 rounded hover:bg-green-600">Sign
                            In</button>
                    </div>
                </div>

                <!-- <div class="mt-4 text-center">
                    <p class="text-gray-300 mb-4">
                        ------------------------- <span class="text-gray-600">Sign In with</span>
                        ---------------------------
                    </p>
                    <div class="flex justify-center space-x-4">
                        <button class="p-2 bg-blue-600 text-white rounded-full">
                            <font-awesome-icon :icon="['fab', 'facebook-f']" />
                        </button>
                        <button class="p-2 bg-red-500 text-white rounded-full">
                            <font-awesome-icon :icon="['fab', 'google']" />
                        </button>
                        <button class="p-2 bg-gray-800 text-white rounded-full">
                            <font-awesome-icon :icon="['fab', 'github']" />
                        </button>
                        <button class="p-2 bg-blue-400 text-white rounded-full">
                            <font-awesome-icon :icon="['fab', 'twitter']" />
                        </button>
                    </div>
                </div> -->
                <!-- Signup and Mobile OTP buttons for admin -->
                <div class="flex justify-center mt-5  space-x-3">
                    <button @click="$router.push('/signup')"
                        class="text-md text-blue-500 hover:text-blue-600 hover:underline">Sign
                        Up</button>
                    <button @click="$router.push('/mobilelogin')"
                        class="text-md text-blue-500 hover:underline ml-16hover:text-blue-600 border-l-2 pl-2">OTP Login
                    </button>
                </div>
            </div>
        </div>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="validate_msg" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>


<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { mapActions, mapGetters } from 'vuex';

export default {
    components: {
        fa: FontAwesomeIcon,
    },
    data() {
        return {
            formValues: {
                login: '',
                password: ''
            },
            validate_msg: '',
            passwordErrorMsg: '',
            successMessage: '',
            quotes: [
                "If we want users to like our software, we should design it to behave like a likeable person.",
                "People who are really serious about software should make their own hardware.",
                "Biology is the most powerful technology ever created. DNA is software, proteins are hardware, cells are factories."
            ],
            currentIndex: 0,
            imgUrl: './images/login/wallper.png',
            isfocussed: {},
            showError: false,
            showPasswordError: false,
            //---toaster---
            show: false,
            type_toaster: 'warning',
            //--loader--
            open_loader: false,
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        currentQuote() {
            return this.quotes[this.currentIndex];
        },
    },
    mounted() {
        this.startQuoteRotation();
        const email = this.$route.query.email || null;
        const mobile = this.$route.query.mobile || null;

        // Use the parameters as needed
        if (email) {
            // console.log(`Email: ${email}`);
            this.formValues.login = email;
        }
        else if (mobile) {
            // console.log(`Mobile: ${mobile}`);
            this.formValues.login = mobile;
        }
        this.fetchLocalDataList();
        if(this.currentLocalDataList && this.currentLocalDataList.company_id){
            this.$router.push('/');
        }
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        startQuoteRotation() {
            setInterval(() => {
                this.currentIndex = (this.currentIndex + 1) % this.quotes.length;
            }, 5000); // Change quote every 5 seconds
        },
        validatePhoneNumber() {
            const inputtxt = this.formValues.login;
            const phoneRegex = /^(?:0|\+91)?[6-9]\d{9}$/;
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

            if (/^\d+$/.test(inputtxt)) {
                if (inputtxt.match(phoneRegex)) {
                    this.validate_msg = '';
                    this.showError = false;
                    return true;
                } else {
                    this.validate_msg = 'Enter a valid mobile number';
                    this.showError = true;
                }
            } else if (!emailRegex.test(inputtxt)) {
                this.validate_msg = 'Please enter a valid email address.';
                this.showError = true;
            } else {
                this.validate_msg = '';
                this.showError = false;
                return true;
            }
        },
        // Add a method to check password length
        checkPasswordLength() {
            if (this.formValues.password.length < 8) {
                this.passwordErrorMsg = 'Please enter at least 8 characters';
                this.showPasswordError = true;
            } else {
                this.passwordErrorMsg = '';
                this.showPasswordError = false;
            }
        },
        loginUser() {
            this.validate_msg = '';
            this.passwordErrorMsg = '';
            this.showError = false;
            this.showPasswordError = false;

            this.checkPasswordLength();
            // Validate email or mobile number
            const isValidEmailOrPhone = this.validatePhoneNumber();
            if (this.formValues.login && this.formValues.password && this.formValues.password.length > 7 && isValidEmailOrPhone) {
                this.open_loader = true;
                axios.post('/auth/login', { ...this.formValues })
                    .then(response => {
                        localStorage.setItem('track_new', JSON.stringify(response.data.user));
                        this.$router.push('/');
                        this.open_loader = false;

                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                    })
                    .catch(error => {
                        if (error.response) {
                            console.error('Response data:', error.response.data);
                            console.error('Response status:', error.response.status);
                            // Customize error messages based on the server response
                            // const errorMessage = error.response?.data?.message || 'An unexpected error occurred. Please try again.';
                            // console.log("showError:", this.showError);
                            // console.log("showPasswordError:", this.showPasswordError);
                            this.open_loader = false;
                            this.validate_msg = error.response.data.message;
                            this.type_toaster = 'warning';
                            this.show = true;
                        }

                        // if (errorMessage.includes('User not found')) {
                        //     // Email/Phone not found
                        //     this.validate_msg = 'This email or mobile number is not registered.';
                        //     this.showError = true;
                        //     this.showPasswordError = false;
                        // } else if (errorMessage.includes('Invalid password')) {
                        //     // Password is incorrect
                        //     this.passwordErrorMsg = 'Incorrect password entered.';
                        //     this.showPasswordError = true;
                        //     this.showError = false;
                        // } else {
                        //     // Generic error message
                        //     this.validate_msg = 'Invalid credentials. Please check your details.';
                        //     this.showError = true;
                        //     this.showPasswordError = false;
                        // }
                    });
            } else {
                if (!this.formValues.login) {
                    this.validate_msg = 'Please enter a valid email / mobile number';
                } else if (!this.formValues.password) {
                    this.passwordErrorMsg = 'Please enter a password';
                }

                this.showError = !!this.validate_msg;
                this.showPasswordError = !!this.passwordErrorMsg;
            }
        },
        clearPasswordError() {
            this.passwordErrorMsg = '';
            this.showPasswordError = false;
        },

        handleFocus() {
            this.$nextTick(() => {
                let ref_data = this.$refs.user_name;
                if (ref_data) {
                    ref_data.focus();
                }
            })
        },
    },
};
</script>

<style scoped>
body {
    margin: 0;
    padding: 0;
    overflow-y: auto;
}

.quote-selector {
    margin: 20px 0;
}

.glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.7), 0 0 20px rgba(0, 255, 255, 0.5);
}

.glow-icon {
    animation: glow 1.5s infinite alternate;
    /* Animation for glowing effect */
}

.text-xs.text-red-700 {
    color: #e53e3e;
    /* Red color for error messages */
    font-size: 0.75rem;
}

@keyframes glow {
    0% {
        color: rgba(255, 255, 255, 1);
    }

    100% {
        color: rgba(255, 255, 255, 0.5);
    }
}

/* Responsive Styles */
@media (max-width: 870px) {

    .bg-gradient-to-r {
        padding: 2rem 1rem;
    }

    .quote-selector {
        margin: 10px 0;
    }

    .icon-selector {
        margin: 20px 0;
        flex-direction: column;
    }

    .icon-selector fa {
        font-size: 3rem;
    }
}

.icon-selector {

    justify-content: center;
    align-items: center;
    flex-direction: row;

}

.icon-selector fa {
    font-size: 2.5rem;
    transition: transform 0.3s ease;
}

.icon-selector fa:hover {
    transform: scale(1.2);
}

.bg-gradient {
    background: linear-gradient(to right, #68d391, #4299e1);
    /* Custom gradient */
    height: 100vh;
}

.bg-gradient-to-r .flex {
    display: flex;
    width: 100%;
    height: 100%;
}

.bg-gradient-to-r .flex>div {
    flex: 1;
    min-width: 0;
}

.bg-gradient-to-r .flex>div {
    flex: 2;
    min-width: 0;
}

.text-sm {
    font-size: 0.875rem;
}

.text-gray-300 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Responsive Styles */
@media (max-width: 870px) {
    .text-gray-300 {
        font-size: 0.875rem;
        white-space: nowrap;
    }
}

@media (max-width: 150px) {
    .text-gray-300 {
        display: none;
    }

    .flex.justify-center.space-x-4 {
        flex-direction: column;
        align-items: center;
    }

    .flex.justify-center.space-x-4 button {
        margin-bottom: 10px;
        width: 50px;
        height: 50px;
        display: block;
    }

    .flex.justify-center.space-x-4 button .fa {
        font-size: 1.5rem;
    }
}
</style>
