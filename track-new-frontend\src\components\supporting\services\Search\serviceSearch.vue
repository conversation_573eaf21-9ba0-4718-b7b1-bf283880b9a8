<template>
    <div class="p-1" ref="tableWrapper">
        <!-- Category Selection with Search -->
        <div class="flex justify-start  items-center">
            <input v-model="searchQuery" @input="handleSearch" @focus="toggleTableVisibility(true)" type="text"
                placeholder="Search..." class="p-1 border rounded w-full" />
        </div>

        <!-- Table Displaying Service Data with Transition -->
        <transition name="fade" @before-enter="beforeEnter" @enter="enter" @leave="leave">

            <div v-if="is_table"
                class="overflow-y-auto absolute h-96 w-full bg-blue-200 z-10 p-2 mt-1 shadow-lg text-xs">
                <div v-if="data_list && data_list.length > 0">
                    <div v-if="table_view">
                        <div class="flex justify-end items-center">
                            <!-- Dropdown (Filter) button -->
                            <button @click.stop="toggleDropdown" class="flex items-center p-1 rounded-lg">
                                <font-awesome-icon icon="fa-solid fa-filter" />
                            </button>
                            <!-- Dropdown filter menu -->
                            <div v-if="isDropdownOpen" ref="settingOPtion"
                                class="absolute mt-1 bg-white border border-gray-400 rounded top-[15px] sm:top-[35px] right-[20px] z-10 overflow-auto h-52 w-48"
                                style="max-height: 300px;">
                                <div v-for="(column, index) in getcolumns" :key="index" class="flex items-center p-2">
                                    <input type="checkbox" v-model="column.visible"
                                        class="form-checkbox h-5 w-5 text-gray-600 border" />
                                    <span class="text-xs ml-2">{{ column.label }}</span>
                                </div>
                            </div>
                        </div>

                        <!-- List View -->
                        <div v-for="(service, num) in data_list" :key="num"
                            class="bg-white mt-1 p-1 shadow-lg rounded flex justify-start items-center hover:bg-gray-200 cursor-pointer">
                            <!-- Actions Section -->
                            <div class="flex items-center">
                                <div class="flex items-center">
                                    <button v-if="!service.editing" @click="viewRecord(service)"
                                        class="p-1 flex hover:bg-violet-100 justify-center items-center" title="View">
                                        <font-awesome-icon icon="fa-solid fa-eye" style="color: #8b5cf6;" />
                                        <span v-if="!isMobile" class="px-1">View</span>
                                    </button>
                                    <button v-if="!service.editing && checkRoles(['Sub_Admin', 'admin'])"
                                        @click="editRecord(service)"
                                        class="p-1 flex hover:bg-blue-100 justify-center items-center" title="Edit">
                                        <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                        <span v-if="!isMobile" class="px-1">Edit</span>
                                    </button>
                                </div>
                            </div>

                            <!-- Service Data in List Format -->
                            <div class="flex justify-start items-center">
                                <div v-for="(column, index) in getcolumns" :key="index"
                                    :class="{ 'hidden': !column.visible }">
                                    <div class="flex flex-wrap p-1">
                                        <!-- <span class="font-semibold">{{ column.label }}:</span> -->
                                        <span class="flex flex-wrap"
                                            v-if="column.key !== 'service_id' && column.key !== 'sale_id' && column.key !== 'service_code'">
                                            <span
                                                v-html="highlightText(formatData(JSON.parse(service.service_data)[column.key], column.key), searchQuery)"></span>
                                        </span>
                                        <span
                                            v-if="(column.key === 'service_id' || column.key === 'sale_id' || column.key === 'service_code') && service[column.key]">
                                            <span v-html="highlightText(service[column.key], searchQuery)"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <table class="table-auto">
                        <thead>
                            <tr>
                                <th class="border px-2 py-1">
                                    <div class="flex justify-between">
                                        <p>Actions</p>
                                        <button @click.stop="toggleDropdown" class="flex items-center p-1 rounded-lg">
                                            <font-awesome-icon icon="fa-solid fa-filter" />
                                        </button>
                                        <div v-if="isDropdownOpen" ref="settingOPtion"
                                            class="absolute mt-1 bg-white border border-gray-400 rounded top-[1px] sm:top-[35px] left-[20px] z-10 overflow-auto h-52 w-48"
                                            style="max-height: 300px;">
                                            <div v-for="(column, index) in getcolumns" :key="index"
                                                class="flex items-center p-2">
                                                <input type="checkbox" v-model="column.visible"
                                                    class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                <span class="text-xs ml-2">{{ column.label }}</span>
                                            </div>
                                        </div>
                                    </div>
                                </th>
                                <!-- <th class="border px-2 py-1"
                                    v-for="(key, index) in Object.keys(JSON.parse(data_list[0].service_data))"
                                    :class="{ 'hidden': special_keys.includes(key) }" :key="index">{{
                                        changeHeaderName(key) }}</th> -->
                                <th class="border px-2 py-1" v-for="(column, index) in getcolumns" :key="index"
                                    :class="{ 'hidden': !column.visible }">
                                    {{ column.label }}
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(service, num) in data_list" :key="num" class="hover:bg-gray-200">
                                <td class="border p-1">
                                    <div class="flex justify-center items-center space-x-2">
                                        <button v-if="!service.editing" @click="viewRecord(service)"
                                            class="p-1 flex border border-[#8b5cf6;] rounded hover:bg-violet-100 justify-cneter items-center"
                                            title="View">
                                            <font-awesome-icon icon="fa-solid fa-eye" style="color: #8b5cf6;" />
                                            <span v-if="!isMobile" class="px-1 ">View</span></button>
                                        <button v-if="!service.editing && checkRoles(['Sub_Admin', 'admin'])"
                                            @click="editRecord(service)"
                                            class="p-1 flex border border-[#3b82f6;] rounded hover:bg-blue-100 justify-cneter items-center"
                                            title="Edit">
                                            <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                            <span v-if="!isMobile" class="px-1">Edit</span>
                                        </button>
                                    </div>
                                </td>
                                <!-- <td class="border px-2 py-1"
                                    v-for="(key, index) in Object.keys(JSON.parse(data_list[0].service_data))"
                                    :class="{ 'hidden': special_keys.includes(key) }" :key="index">
                                    <span
                                        v-html="highlightText(formatData(JSON.parse(service.service_data)[key], key), searchQuery)"></span>
                                </td> -->
                                <td class="border p-1" v-for="(column, index) in getcolumns" :key="index"
                                    :class="{ 'hidden': !column.visible }">
                                    <span
                                        v-if="column.key !== 'service_id' && column.key !== 'sale_id' && column.key !== 'service_code'"
                                        v-html="highlightText(formatData(JSON.parse(service.service_data)[column.key], column.key), searchQuery)">
                                    </span>
                                    <span
                                        v-if="(column.key === 'service_id' || column.key === 'sale_id' || column.key === 'service_code') && service[column.key]"
                                        v-html="highlightText(service[column.key], searchQuery)">

                                    </span>
                                </td>
                            </tr>
                        </tbody>
                    </table>

                    <!-- Pagination -->
                    <div v-if="load_more && isLoadMore" class="flex justify-center mt-4">
                        <button @click="loadMore" class="bg-blue-500 text-white px-4 py-2 rounded">Load More</button>
                    </div>
                </div>
                <div v-if="searchQuery !== '' && data_list.length === 0"> No data Match..!</div>
                <!-- Loader -->
                <div v-if="isLoading" class="text-center mt-4">Loading...</div>
                <div v-if="searchQuery === ''">Please enter the Search value..!</div>
            </div>
        </transition>


        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';


export default {
    props: {
        selectedCategory: {
            type: [String, Number],
            default: 'All',
        },
        isMobile: Boolean,
        empty_data: String
    },
    data() {
        return {
            searchQuery: '',
            currentPage: 0,
            perPage: 50,
            isLoading: false,
            load_more: true,
            isOpen: false,
            data_list: [],
            //----"service_data", ---
            special_keys: ["created_at", "id", "status", "updated_at", "servicecategory", "service_data", "service_track", "customer_id", "pre_repair", "notification", "comments"],
            sub_keys: ["id", "pivot", "user_id", "service_id"],
            //--toaster---
            show: false,
            type_toaster: 'warning',
            message: '',
            //---dropdown--
            isDropdownOpen: false,
            columns: [],
            //--table--
            is_table: false,
            table_view: false,
        };
    },
    computed: {
        ...mapGetters('servicesAdvanceSearch', ['currentServiceList', 'getCurrentCategory']),
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        categories() {
            return this.currentServiceCategory || [];
        },
        filteredServices() {
            // Check if the service list exists and filter based on search query
            if (this.currentServiceList && this.currentServiceList.list) {
                return this.currentServiceList.list.filter(service => {
                    const query = this.searchQuery.toLowerCase();

                    // Filter based on category first
                    const matchesCategory = service.categoryId == this.selectedCategory || this.selectedCategory === 'all';

                    // Filter based on search query for dynamic service_data
                    const matchesSearchQuery = this.searchQuery === '' || this.matchServiceData(service);

                    return matchesCategory && matchesSearchQuery;
                });
            }
        },
        getcolumns() {
            if (this.data_list && this.data_list.length > 0 && this.data_list[0].service_data) {
                const default_true = ["customer", "service_id", "service_code", "invoice_id", "problem_title", "brand", "device_model", "serial_number", "service_type", "status", "comments"];
                // return Object.keys(JSON.parse(this.data_list[0].service_data))
                //     .filter(opt => !this.special_keys.includes(opt))  // Exclude special keys
                //     .map(opt => {
                //         return {
                //             key: opt,
                //             label: this.changeHeaderName(opt),  // You can format the label here
                //             visible: default_true.includes(opt)  // Set visibility based on default_true
                //         };
                //     });
                // Parse the service_data and get the keys
                const serviceData = JSON.parse(this.data_list[0].service_data);
                const serviceKeys = Object.keys(serviceData);

                // Ensure the three new keys are added if they don't exist
                const allKeys = [...new Set(["service_id", "service_code", "invoice_id", ...serviceKeys])];

                return allKeys
                    .filter(opt => !this.special_keys.includes(opt))  // Exclude special keys
                    .map(opt => {
                        return {
                            key: opt,
                            label: this.changeHeaderName(opt),  // You can format the label here
                            visible: default_true.includes(opt)  // Set visibility based on default_true
                        };
                    });
            }
            return [];  // Return empty array if no valid data
        },
        isLoadMore() {
            if (this.currentServiceList && this.currentServiceList.list) {
                const find_category = this.currentServiceList.list.find(opt => opt.categoryId == this.selectedCategory);

                if (find_category) {
                    if (find_category.pagination && (find_category.pagination.current_page < find_category.pagination.last_page) && this.currentPage !== (find_category.pagination.current_page * 1) + 1) {
                        return true;

                    } else {
                        return false;
                    }
                } else {
                    return true;
                }
            }
        },

    },
    methods: {
        ...mapActions('servicesAdvanceSearch', ['fetchServiceList']),
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),

        // Check if any field in service_data matches the search query
        matchServiceData(service) {
            try {
                const searchQuery = this.searchQuery.toLowerCase();
                // Check if invoice_id, service_code, or service_id matches the search query
                if (
                    service.invoice_id && service.invoice_id.toString().toLowerCase().includes(searchQuery) ||
                    service.service_code && service.service_code.toString().toLowerCase().includes(searchQuery) ||
                    service.service_id && service.service_id.toString().toLowerCase().includes(searchQuery)
                ) {
                    return true;
                }

                // If not found in the specific fields, check in service_data (if exists)
                if (service.service_data) {
                    const serviceData = JSON.parse(service.service_data);
                    if (typeof serviceData === 'object' && serviceData !== null) {
                        return this.searchDeep(serviceData, searchQuery);
                    }
                }

                return false; // Return false if none of the fields or service_data match the search query
            } catch (e) {
                console.error('Error during the search:', e);
                return false; // Return false if there's an error during the search
            }
        },

        // Recursively search through object keys or array values
        searchDeep(data, query) {
            if (Array.isArray(data)) {
                // If it's an array, loop through each item
                return data.some(item => this.searchDeep(item, query));
            } else if (typeof data === 'object' && data !== null) {
                // If it's an object, loop through each key and value
                return Object.values(data).some(value => this.searchDeep(value, query));
            } else {
                // For primitive values, check if they match the query
                return data && data.toString().toLowerCase().includes(query);
            }
        },

        // Handle category selection change
        handleCategoryChange() {
            this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
        },

        // Handle search input change and filter services
        async handleSearch() {
            if (this.searchQuery && this.searchQuery.length > 1) {
                this.is_table = true;
            }
            if (this.currentServiceList && this.currentServiceList.list) {
                const find_category = this.currentServiceList.list.find(opt => opt.categoryId == this.selectedCategory);

                if (find_category) {
                    if (find_category.pagination && (find_category.pagination.current_page < find_category.pagination.last_page) && this.currentPage !== (find_category.pagination.current_page * 1) + 1) {
                        this.load_more = true;
                        this.currentPage = (find_category.pagination.current_page * 1) + 1;
                        await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                    } else {
                        // Filter the data based on the service data and search query
                        let filteredData = find_category.data.filter(opt => this.matchServiceData(opt));

                        // Remove duplicates based on a unique identifier (e.g., `id` or `service_id`)
                        filteredData = this.removeDuplicates(filteredData);

                        // Update the data_list with the filtered and unique data
                        this.data_list = filteredData;

                        if (this.data_list && this.data_list.length === 0) {
                            setTimeout(async () => {
                                if (find_category.pagination && (find_category.pagination.current_page < find_category.pagination.last_page) && this.currentPage !== (find_category.pagination.current_page * 1) + 1) {
                                    this.currentPage = (find_category.pagination.current_page * 1) + 1;
                                    await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                                }
                            }, 500);
                        }

                        this.load_more = false;
                    }
                } else {
                    if (this.currentPage === 0) {
                        this.load_more = true;
                        this.currentPage = 1;
                        await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                    }
                }
            }
        },

        // Load more data (pagination)
        async loadMore() {
            this.isLoading = true;
            if (this.currentServiceList && this.currentServiceList.list) {
                const find_category = this.currentServiceList.list.find(opt => opt.categoryId == this.selectedCategory);

                if (find_category) {
                    if (find_category.pagination && (find_category.pagination.current_page < find_category.pagination.last_page) && this.currentPage !== (find_category.pagination.current_page * 1) + 1) {
                        this.currentPage = (find_category.pagination.current_page * 1) + 1;
                        await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                    } else {
                        // Filter the data based on the service data and search query
                        this.data_list = find_category.data.filter(opt => this.matchServiceData(opt));
                    }
                } else {
                    this.currentPage = 1;
                    await this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                }
            }
            this.isLoading = false;
        },

        // Close modal
        closeModal() {
            this.$emit('close');
            setTimeout(() => {
                this.isOpen = false;
            }, 100);
        },
        //---higlight and format data----
        // Format the value to display it correctly in the table
        formatData(value, key) {
            // Handle Arrays
            if (Array.isArray(value)) {
                if (typeof value[0] === 'object') {
                    // If array contains objects
                    if (key === 'assignWork') {
                        return value.map(item => `Name: ${item.name || ''}`).join(', ');
                    } else if (key === 'comments') {
                        return value.map(item =>
                            `Message: ${item.comments || ''}, Date: ${item.current_date || ''}, Updated By: ${item.updated_by?.name || ''}`
                        ).join(', ');
                    } else if (key === 'document') {
                        return value.map(item => `File Name: ${item.image || ''}`).join(', ');
                    } else if (key === 'additional') {
                        return value.map(item =>
                            `Item Name: ${item.product_name || ''} -> QTY: ${item.qty || ''}`
                        ).join(', ');
                    } else {
                        return value.map(item =>
                            Object.entries(item)
                                .map(([subKey, subVal]) => !this.sub_keys.includes(subKey) ? `${subKey}: ${this.formatData(subVal)}` : null)
                                .filter(Boolean) // Filter out undefined/null values
                                .join(', ')
                        ).join(', '); // Join all object representations with commas
                    }
                } else {
                    // If it's an array of strings
                    return value.join(', ');
                }
            }

            // Handle Objects
            if (typeof value === 'object' && value !== null) {
                return Object.entries(value)
                    .map(([subKey, subVal]) => {
                        if (!this.sub_keys.includes(subKey)) {
                            return `${subKey}: ${this.formatData(subVal)}`;
                        }
                        return null;
                    })
                    .filter(Boolean) // Filter out undefined/null values
                    .join(', ');
            }

            // For other types like string or primitives
            return value || '';
        },

        // Highlight matched query in text
        highlightText(text, query) {
            // Ensure text is a string before using replace
            text = String(text);

            if (!query) return text; // Return text if no search query

            const regex = new RegExp(`(${query})`, 'gi'); // Case-insensitive match
            return text.replace(regex, '<span style="background-color: yellow;">$1</span>'); // Wrap the match in <span> with background color
        },
        //--make header clean---
        changeHeaderName(value) {
            // Replace underscores with spaces and capitalize each word's first letter
            return value.replace(/_/g, ' ')
                .replace(/\b\w/g, (match) => match.toUpperCase());
        },
        //--role based on--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        editRecord(record) {
            if (!record.invoice_id) {
                this.$router.push({ name: 'service-category-view', params: { viewId: record.id, type: record.servicecategory.service_category, id: record.servicecategory.id } });
            } else {
                this.message = 'This service has already been invoiced, so we cannot edit it';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        viewRecord(record) {
            this.$router.push({ name: 'service-data-view', params: { serviceId: record.id, type: record.servicecategory.service_category, id: record.servicecategory.id } });

        },
        //--setting
        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;
            if (this.isDropdownOpen) {
                // Add event listener when dropdown is opened
                document.addEventListener('click', this.handleOutsideClickKey);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClickKey);
            }
        },
        handleOutsideClickKey(event) {
            try {
                // console.log(event, 'What happening.....');
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },
        removeDuplicates(data) {
            // Use the `id` (or any other unique field like `service_id`) to remove duplicates
            const seen = new Set();
            return data.filter(item => {
                const id = item.id || item.service_id; // Assuming `id` or `service_id` is the unique identifier
                if (seen.has(id)) {
                    return false; // Skip if already seen
                } else {
                    seen.add(id);
                    return true; // Include if not seen before
                }
            });
        },
        // Handle outside click to close the table or dropdown
        handleOutsideClick(event) {
            try {
                // console.log(event, 'What happening.....');
                const isClickInside = this.$refs.tableWrapper.contains(event.target) || this.$refs.tableWrapper.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleTableVisibility(false);
                }
            } catch (error) {
                this.toggleTableVisibility(false);
            }
        },

        // Toggle the visibility of the table
        toggleTableVisibility(show) {
            this.is_table = show;
            if (this.is_table) {
                // Add event listener when dropdown is opened
                document.addEventListener('click', this.handleOutsideClick);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },
        // Before enter hook (optional)
        beforeEnter(el) {
            el.style.opacity = 0;
            el.style.transition = 'opacity 0.5s';
        },
        // Enter hook to animate the element in (optional)
        enter(el, done) {
            setTimeout(() => {
                el.style.opacity = 1;
                done(); // Call done() to indicate the transition has finished
            }, 0);
        },
        // Leave hook to animate the element out (optional)
        leave(el, done) {
            setTimeout(() => {
                el.style.opacity = 0;
                done(); // Call done() to indicate the transition has finished
            }, 0);
        }

    },
    watch: {
        showModal(newValue) {
            if (newValue) {
                this.fetchServiceList({ categoryId: this.selectedCategory, page: this.currentPage, per_page: this.perPage });
                this.fetchServiceCategoryList();
            }
            setTimeout(() => {

                this.isOpen = newValue;
            }, 100);
        },

        // Watch changes in current service list and filter based on search query
        currentServiceList: {
            deep: true,
            handler(newValue) {
                if (this.showModal && newValue && newValue.list && newValue.list.length > 0) {
                    // this.getcolumns();
                    const find_data = newValue.list.find(opt => opt.categoryId == this.selectedCategory);
                    if (find_data && find_data.data) {
                        if (this.searchQuery === '') {
                            this.data_list = find_data.data;
                        } else {
                            this.data_list = find_data.data.filter(opt => this.matchServiceData(opt));
                        }
                    }
                }
            }
        },

        selectedCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && this.showModal) {
                    this.load_more = true;
                    this.currentPage = 1;
                }
            }
        },
        isLoading: {
            handler(newValue) {
                if (newValue && this.data_list && this.data_list.length > 0) {
                    this.isLoading = false;
                }
            }
        }
    }
};
</script>
<style scoped>
/* Fade transition */
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s ease;
}

.fade-enter,
.fade-leave-to

/* .fade-leave-active in <2.1.8 */
    {
    opacity: 0;
}

/* Optional: Add a slide effect */
.fade-enter-active,
.fade-leave-active {
    transition: transform 0.5s ease, opacity 0.5s ease;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
    transform: translateY(-20px);
    /* Slide up effect */
}
</style>
