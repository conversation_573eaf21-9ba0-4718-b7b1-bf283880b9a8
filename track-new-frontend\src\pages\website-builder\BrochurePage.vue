<template>
  <div class="builder-page">
    <div class="flex flex-col md:flex-row justify-between items-center">
      <h1 class="web-head">Brochure Page</h1>
      <div class="w-full md:w-auto md:self-end">
        <EnablePageName :pages="is_onSetting" @updatePagesEnabled="handleToggle"></EnablePageName>
      </div>
    </div>

    <!-- Brochure Upload Section -->
    <div class="mb-4">
      <label class="block mb-2 font-bold">Brochure PDF Upload (<span class="text-gray-500 text-xs">Max
          2MB</span>)</label>
      <input type="file" @change="handleFileUpload" class="border p-2 rounded w-full" ref="brochurefile"
        accept="application/pdf" />

      <!-- Loader -->
      <div v-if="circle_loader_photo"
        class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
        <CircleLoader :loading="true"></CircleLoader>
      </div>
    </div>

    <!-- Brochure List -->
    <div v-if="Array.isArray(brochureList) && brochureList.length" class="mt-4">
      <h2 class="text-lg font-semibold mb-2">Uploaded Brochures</h2>
      <ul class="space-y-3">
        <li v-for="(brochure, index) in brochureList" :key="index" :draggable="true"
          @dragstart="dragStart(index, $event)" @dragover.prevent @drop="drop(index, $event)"
          class="flex justify-between items-center border p-3 rounded bg-gray-100 hover:bg-gray-200">
          <div class="flex items-center gap-3">
            <!-- Editable Name -->
            <input v-model="brochure.name" class="border p-2 rounded" placeholder="Enter brochure name" />

          </div>
          <div class="space-x-3">
            <button @click="viewBrochure(brochure.url)" class="text-blue-500 hover:text-blue-600">
              <font-awesome-icon icon="fa-solid fa-eye" /> View
            </button>
            <button @click.prevent="removeBrochure(index)" class="text-red-500 hover:text-red-600">
              <font-awesome-icon icon="fa-solid fa-trash-can" /> Remove
            </button>
          </div>
        </li>
      </ul>
    </div>
    <div class="mt-6">
      <NavigationButtons :pageTitle="'Brochure Details'" @goToNextPage="goToNextPage" @goToPrevPage="goToPrevPage" />
    </div>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="confirmDelete" @onCancel="cancelDelete"></confirmbox>
  </div>
</template>

<script>
import imageService from '../../services/imageService';
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import EnablePageName from './EnablePageName.vue';

export default {
  props: {
    companyId: {
      type: String,
      required: true
    },
    brochureUrl: {
      type: Object,
      default: '',
    },
    is_updated: { type: Boolean, required: true },
    pages: {
      type: Object,
      required: true
    },
  },
  components: {
    NavigationButtons,
    confirmbox,
    EnablePageName
  },
  data() {
    return {
      localBrochureUrl: [],
      brochureList: [],
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      //--loader--
      circle_loader_photo: false,
      //----is on settings---
      is_onSetting: { is_on: true, name: 'Brochure' },
      draggedIndex: null,
    };
  },
  watch: {
    brochureUrl: {
      deep: true,
      handler(newVal) {
        if (newVal) {
          // Update localBrochureUrl when brochureUrl prop changes
          this.localBrochureUrl = newVal;
        }
      }
    },
    brochureList(newVal) {
      // Emit changes back to parent to keep state consistent
      this.$emit('updateBrochureUrl', newVal);
    },
    is_updated: {
      deep: true,
      handler(newValue) {
        this.$emit('updateBrochureUrl', this.brochureList);
        this.$emit('updatePagesSetting', { brochure: this.is_onSetting });
      }
    },
    is_onSetting: {
      deep: true,
      handler(newValue) {
        this.$emit('updatePagesSetting', { brochure: newValue });
      }
    }
  },
  mounted() {
    if (this.brochureUrl) {
      this.localBrochureUrl = this.brochureUrl;
      this.brochureList = Array.isArray(this.brochureUrl) ? [...this.brochureUrl] : [{ name: 'General', url: this.brochureUrl }];
    }
    if (this.pages && this.pages.brochure !== undefined) {
      this.is_onSetting = this.pages.brochure;
    }
  },
  methods: {
    async handleFileUpload(event) {
      this.circle_loader_photo = true;
      const file = event.target.files[0]; // Get the single selected file
      if (file) {
        try {
          // Upload the file and add it to the brochure list
          const response = await imageService.uploadFile(file, 'brochure', this.companyId);
          if (response.media_url) {
            this.brochureList.push({
              name: file.name.replace(/\.[^/.]+$/, ''), // Extract the file name without extension
              url: response.media_url, // Store the response URL
            });
            this.$refs.brochurefile.value = '';
            this.$emit('toasterMessages', { msg: `Brochure "${file.name}" uploaded successfully!`, type: 'success' });
            this.$emit('updateBrochureUrl', this.brochureList);
          }
        } catch (error) {
          console.error('Error uploading brochure:', error);
          this.$emit('toasterMessages', { msg: 'Failed to upload brochure.', type: 'warning' });
        } finally {
          this.circle_loader_photo = false;
        }
      } else {
        this.$emit('toasterMessages', { msg: 'Please select a brochure file to upload.', type: 'warning' });
        this.circle_loader_photo = false;
      }
    },
    viewBrochure(url) {
      // Open the brochure URL in a new tab
      window.open(url, '_blank');
    },
    removeBrochure(index) {
      // Show confirm box before removing
      this.deleteIndex = index;
      this.open_confirmBox = true;
    },
    async confirmDelete() {
      this.open_confirmBox = false;
      this.$emit('updateLoader', true);
      const brochure = this.brochureList[this.deleteIndex];
      if (brochure) {
        try {
          // Delete the brochure
          await imageService.deleteFile(brochure.url, 'brochure');
          this.brochureList.splice(this.deleteIndex, 1); // Remove from list
          this.$emit('toasterMessages', { msg: `Brochure "${brochure.name}" removed successfully.`, type: 'success' });
          this.$emit('updateLoader', false);
          this.$emit('updateBrochureUrl', this.brochureList);
        } catch (error) {
          console.error('Error deleting brochure:', error);
          if (error.response.data && error.response.data.error === 'Image not found') {
            if (Array.isArray(this.brochureList) && this.deleteIndex >= 0) {
              this.brochureList.splice(this.deleteIndex, 1);
            } else {
              this.brochureList = null; // Clear image URL
            }
          }
          this.$emit('toasterMessages', { msg: `Failed to delete "${brochure.name}".`, type: 'warning' });
          this.$emit('updateLoader', false);
        }
      }
    },
    cancelDelete() {
      this.open_confirmBox = false;
    },
    goToNextPage() {
      this.$emit('updateBrochureUrl', this.brochureList);
      this.$emit('submitData');
      this.$emit('goToNextPage'); // Emit event to the parent component
    },
    goToPrevPage() {
      this.$emit('updateBrochureUrl', this.brochureList);
      this.$emit('submitData');
      this.$emit('goToPrevPage'); // Emit event to the parent component
    },
    //--handle switch--
    handleToggle(data) {
      // Perform any additional actions when toggled
      if (data) {
        this.is_onSetting = data;
      }
    },
    //--drag and drop order list--
    dragStart(index, event) {
      // Store the index of the dragged item
      this.draggedIndex = index;
      // Add a class to highlight the dragged item (optional)
      event.target.classList.add('dragging');
    },

    drop(targetIndex, event) {
      // Ensure we only reorder if the dragged item is not dropped on itself
      if (this.draggedIndex !== targetIndex) {
        // Move the item to the new position
        const movedBrochure = this.brochureList.splice(this.draggedIndex, 1)[0];
        this.brochureList.splice(targetIndex, 0, movedBrochure);
      }
      this.draggedIndex = null; // Reset draggedIndex
      event.target.classList.remove('dragging');
    },
  },
};
</script>
<style scoped>
.dragging {
  opacity: 0.5;
}
</style>
