<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex justify-between items-center relative w-full bg-teal-600 px-4 py-4 rounded rounded-b-none">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    Manage stock Adjustment Item
                </p>
                <p class="close" @click="closeModal">&times;</p>
            </div>
            <div class="block text-sm p-5">
                <!--Item_name-->
                <p><span class="font-bold">Item Name: </span> {{ item_data.products.product_name }}</p>
                <!--description-->
                <div class="bg-gray-300 px-3 py-3 mt-2 mb-3 rounded">
                    <label class="font-bold">Description</label>
                    <!-- Disclaimer -->
                        <textarea id="description" v-model="formValues.description" rows="2"
                            @focus="inputFocussed.description = true" @blur="inputFocussed.description = false"
                            class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none rounded outline-none"
                            :class="{ 'border-blue-600': inputFocussed.description }"
                            placeholder=""></textarea>
                        <p></p>
                </div>

                <!--buttons-->
                <div class="flex justify-end items-center">
                    <button class="border rounded text-white bg-red-700 font-bold px-4 py-2 hover:bg-red-600 mr-3"
                        @click="closeModal">Cancel</button>
                    <button class="border rounded text-white bg-green-700 font-bold px-4 py-2 hover:bg-green-600 ml-3"
                        @click="saveDes">save</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        item_data: Object
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            isMessage: false,
            message: '',
            inputFocussed: {}
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-Modal');
                this.message = '';
            }, 300);
        },
        //---close message---
        closeMessageDialog() {
            this.isMessage = false;
        },
        //---save terms and condition---
        saveDes() {
            if (this.formValues.description && this.formValues.description !== '') {
                setTimeout(() => {
                    this.$emit('close-Modal', this.formValues.description);
                    this.item_data.description = this.formValues.description;
                    this.formValues = {};
                }, 300);

            } else {
                // this.isMessage = true;
                this.message = 'Field is empty, Please fill terms and conditions..!';
            }

        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        item_data:{
            deep: true,
            handler(newValue){
                if(newValue.description){
                this.formValues.description = newValue.description;
                }else{
                    this.formValues.description = '';
                }
            }
        },
        formValues: {
            deep: true,
            handler(newValue) {
                this.message = '';
            }
        }

    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>