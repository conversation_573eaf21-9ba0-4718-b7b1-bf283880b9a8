<template>
    <div v-if="showModal" class="fixed sm:top-0 bottom-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50">

        <!-- Modal -->
        <div ref="modal"
            class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-y-0': isOpen, 'translate-y-full bottom-12': !isOpen }">
            <!-- <div class="modal-content"> -->

            <div class="bg-teal-600 justify-between items-center flex py-3 set-header-background">
                <p class="text-white tems-center text-center flex justify-end ml-12 text-xl">
                    Create Next Followup
                </p>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <div class="p-4">
                <div v-if="formValues.leadstatus_id !== 2 && formValues.leadstatus_id !== 3 && formValues.leadstatus_id !== 4"
                    class="grid grid-cols-1 gap-4 ">
                    <!---follwup Date-->
                    <div class="flex items-center mt-5">
                        <div class="mr-2 w-10" :title="'assign date'">
                            <img :src="date" alt="assign_date" class="w-7 h-7">
                        </div>
                        <div class="w-full mr-2 relative">
                            <label for="assign_date"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.follow_date || isInputFocused.follow_date, 'text-blue-700': isInputFocused.follow_date }">Next
                                follow
                                up
                                Date & Time</label>
                            <input id="follow_date" v-model="formValues.follow_date" type="datetime-local"
                                placeholder=" " ref="dateandtime"
                                class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 outline-none focus:border-blue-500 rounded" />
                            <!-- @focus="isInputFocused.follow_date = true" @blur="isInputFocused.follow_date = false"  -->
                        </div>
                    </div>
                    <!---Lead Description-->
                    <div class="flex items-center mt-5">
                        <div class="mr-2 w-10" :title="'description'">
                            <img :src="notes" alt="description" class="w-7 h-7">
                        </div>
                        <div class="w-full mr-2 relative">
                            <label for="follow_des"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.follow_des || isInputFocused.follow_des, 'text-blue-700': isInputFocused.follow_des }">Add
                                note</label>
                            <textarea id="follow_des" v-model="formValues.follow_des" rows="2" ref="description"
                                @focus="isInputFocused.follow_des = true" @blur="isInputFocused.follow_des = false"
                                class="text-sm p-1 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 outline-none rounded"></textarea>
                        </div>
                    </div>
                    <!---lead Type-->
                    <!-- <div class="flex items-center mt-5">
                        <div class="mr-2 w-10" :title="'lead type'">
                            <img :src="category" alt="lead_type" class="w-7 h-7">
                        </div>
                        <div class="flex w-full mr-2 relative">
                            <label for="leadtype_id"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': (formValues.leadtype_id !== undefined && formValues.leadtype_id >= 0) || isInputFocused.leadtype_id, 'text-blue-700': isInputFocused.leadtype_id }">Lead
                                Type</label>
                            <select id="leadtype_id" v-model="formValues.leadtype_id"
                                class="text-sm p-1 py-2 mt-1 border border-gray-300 w-full py-2 focus:border-blue-500 rounded rounded-tr-none rounded-br-none outline-none "
                                @focus="isInputFocused.leadtype_id = true" @blur="isInputFocused.leadtype_id = false">
                                <option disabled value="">Select type</option>
                                <option v-for="(opt, index) in leadType" :key="index" :value="opt.id">{{ opt.name }}
                                </option>
                            </select>
                            <div class="w-1/10 text-center bg-teal-700 text-lg py-2 px-2 mt-1 border font-bold text-white cursor-pointer border-teal-700 rounded-tr rounded-br"
                                @click="openLeadList('type')">+</div>
                        </div>
                    </div> -->
                    <!---Assign to--->
                    <div class="flex items-center mt-5">
                        <div class="mr-2 w-10" :title="'assign To'">
                            <img :src="assign" alt="assign_to" class="w-7 h-7">
                        </div>
                        <div class="relative w-full mr-2 bg-white">
                            <label for="assign_to"
                                class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                                :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.assign_to || isInputFocused.assign_to, 'text-blue-700': isInputFocused.assign_to }">Assign
                                to</label>
                            <div class="border py-2 px-2 flex flex-wrap"
                                :class="{ 'border-blue-300': isInputFocused.assign_to === true }">

                                <div v-for="(selectedOption, index) in formValues.assign_to" :key="index"
                                    class="selectedData-option inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-red-100 cursor-pointer">
                                    {{ selectedOption.name }}
                                    <span @click="removeOption(selectedOption)"
                                        class="text-red-500 font-semibold cursor-pointer">x</span>
                                </div>
                                <input type="text" v-model="search" @input="filterOptions" @focus="filterOptions"
                                    @click="filterOptions" ref="search" @blur="hideOptions" placeholder="Select staff"
                                    class="h-[35px] mt-1 outline-none border rounded-lg px-2"
                                    @keydown.enter="handleEnterKey('multiple', filteredOptions())"
                                    @keydown.down.prevent="handleDownArrow(filteredOptions())"
                                    @keydown.up.prevent="handleUpArrow(filteredOptions())" />
                            </div>
                            <div class="absolute bg-white z-50 border border-blue-300 py-2 px-2 flex flex-wrap w-full border-t-0 transition-all duration-500 ease-in-out"
                                v-if="showOptions" :class="{ 'h-auto': isInputFocused.assign_to === true }">
                                <div v-for="(option, index) in filteredOptions()" :key="index"
                                    class="inline-block mb-2 mr-2 px-4 py-2 bg-gray-200 rounded-lg relative hover:bg-green-300 cursor-pointer"
                                    :class="{ 'bg-green-300': index === selectedIndex }"
                                    @click="selectOptionMultiple(option)">
                                    {{ option.name }}
                                </div>
                                <button v-if="showAddNew !== null && search.length > 1"
                                    class="bg-green-700 text-white hover:bg-green-600 px-3 py-1 rounded-lg"
                                    @click="openModalEmployee">Add New</button>
                            </div>
                        </div>
                    </div>
                </div>


                <!-- Buttons -->
                <div class="flex justify-center items-center mt-5">
                    <button @click="cancelModal"
                        class=" text-white bg-gradient-to-r from-red-400 via-red-500 to-red-600 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 shadow-lg shadow-red-500/50 dark:shadow-lg dark:shadow-red-800/80 font-medium rounded-lg text-sm px-5 py-2.5 text-center mr-5 mb-2">Cancel</button>
                    <button @click="sendModal"
                        class="text-white bg-gradient-to-r from-green-400 via-green-500 to-green-600 hover:bg-gradient-to-br focus:ring-4 focus:outline-none focus:ring-green-300 dark:focus:ring-green-800 shadow-lg shadow-green-500/50 dark:shadow-lg dark:shadow-green-800/80 font-medium rounded-lg text-sm px-7 py-2.5 text-center me-2 mb-2">Save</button>
                </div>
            </div>
            <!-- </div> -->
        </div>
        <Loader :showModal="open_loader"></Loader>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <employeeRegister :show-modal="showModal_employee" @close-modal="closeModalEmployee" :type="'add'"
            :user_name="EmployeeName">
        </employeeRegister>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
import dialogAlert from './dialogAlert.vue';
import employeeRegister from './employeeRegister.vue';
export default {
    props: {
        showModal: Boolean,
        editData: Object,
        companyId: String,
        userId: String,
        lead_data: Object,
        record: Object,
        updated_by: Object,
        getplanfeatures: {
            type: Function,
            required: false,
        }
    },
    components: {
        dialogAlert,
        employeeRegister,
    },
    data() {
        return {
            isMobile: false,
            validate_msg: '',
            isOpen: false,
            focussed: {},
            formValues: {},
            employeeList: [],
            open_loader: false,
            isInputFocused: { follow_date: true, follow_des: true },
            search: '',
            showOptions: false,
            isMessageDialogVisible: false,
            message: '',
            showModal_employee: false,
            EmployeeName: '',
            showAddNew: false,
            selectedIndex: 0,
            //----icons
            user_name: '/images/service_page/User.png',
            date: '/images/service_page/schedule.png',
            assign: '/images/service_page/personService.png',
            statusOf: '/images/service_page/statusIcon.png',
            notes: '/images/service_page/Writing.png',
            category: '/images/service_page/Add.png',
            table_edit: '/images/service_page/edit.png',
            table_del: '/images/service_page/del.png',
            pagination_data: {},

        }
    },
    computed: {
        ...mapGetters('employess', ['currentEmployee']),

    },
    methods: {
        ...mapActions('employess', ['fetchEmployeeList']),
        closeMessageDialog() {
            this.message = '';
            this.isMessageDialogVisible = false;
        },
        openMessageDialog(msg) {
            this.isMessageDialogVisible = true;
            this.message = msg;
        },
        cancelModal(data) {
            this.isOpen = false;
            this.closeMessageDialog();
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                if (data && data.id) {
                    this.$emit('close-modal', data);
                    if (this.editData) {
                        this.formValues = {};
                    }
                } else {
                    this.$emit('close-modal');
                    if (this.editData) {
                        this.formValues = {};
                    }
                }
                this.validate_msg = '';
            }, 300);
        },
        cancelModalData() {
            this.isOpen = false;
            this.closeMessageDialog();
            setTimeout(() => {
                this.$emit('close-modal');
                if (this.editData) {
                    this.formValues = {};
                }
            }, 300);
            this.validate_msg = '';
        },
        sendModal(type, data) {
            if (this.getplanfeatures('leads')) {
                this.emit('openNoAccess');
            } else {
                if ((((this.formValues.follow_des && this.formValues.follow_date) || type === 'del') && (this.formValues.assign_to && this.formValues.assign_to.length > 0)) || this.formValues.leadstatus_id === 3 || this.formValues.leadstatus_id === 2 || this.formValues.leadstatus_id === 4) {
                    this.open_loader = true;
                    let send_data = { ...this.formValues, id: this.record.id, follow_up: this.record.follow_up };
                    if (send_data.assign_to && send_data.assign_to.length === 0) {
                        delete send_data.assign_to;
                    }

                    if (send_data.assign_to && send_data.assign_to.length > 0) {
                        send_data.assign_to = send_data.assign_to.map(opt => opt.id).join(', ');
                    }
                    // console.log(send_data, 'Befor....');
                    if (send_data.follow_up && type !== 'del' && send_data.leadstatus_id !== 3 && send_data.leadstatus_id !== 2 && this.formValues.leadstatus_id !== 4) {
                        // console.log('It is true 11111');
                        if (Array.isArray(send_data.follow_up) && send_data.follow_up.length > 0) {
                            if (this.editFollowUpIndex !== null && this.editFollowUpIndex !== undefined && this.editFollowUpIndex >= 0) {
                                send_data.follow_up[this.editFollowUpIndex] = { description: this.formValues.follow_des, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.formattedDateTime() };
                                this.editFollowUpIndex = null;
                            } else {
                                send_data.follow_up.unshift({ description: this.formValues.follow_des, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.formattedDateTime() });
                            }
                        } else {
                            send_data.follow_up = [{ description: this.formValues.follow_des, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.formattedDateTime() }];
                        }
                        send_data.follow_up = send_data.follow_up;
                    } else if (this.formValues.follow_des && this.formValues.follow_date && send_data.leadstatus_id !== 3 && send_data.leadstatus_id !== 2 && this.formValues.leadstatus_id !== 4) {
                        // console.log('It is two true....');
                        send_data.follow_up = [{ description: this.formValues.follow_des, date_and_time: this.formValues.follow_date, updated_by: this.updated_by, updated_at: this.formattedDateTime() }];
                    } else {
                        send_data.follow_up = data;
                        if (send_data.follow_date) {
                            delete send_data.follow_date;
                        }
                    }
                    if (send_data.leadstatus_id && send_data.leadstatus_id !== 3 && send_data.leadstatus_id !== 2 && send_data.notes && this.formValues.leadstatus_id !== 4) {
                        delete send_data.notes;
                    }
                    if (send_data.leadstatus_id == 0) {
                        send_data.leadstatus_id = 1;
                    }
                    // if (!send_data.customer_id) {
                    //     send_data.customer_id = send_data.customer.id;
                    // }
                    //---update the data---
                    axios.put(`/leads/${send_data.id}`, { ...send_data, cmpany_id: this.companyId, follow_up: JSON.stringify(send_data.follow_up) })
                        .then(response => {
                            // console.log(response.data.data, 'Response..........');
                            this.updatedData = response.data.data;
                            this.open_loader = false;
                            if (type !== 'del') {
                                this.openMessageDialog(response.data.message);
                                this.cancelModal(response.data.data);
                            }
                            this.formValues = {};
                            //---get lead data---
                            // this.getLeadData();
                            this.getDefaultDate();
                        })
                        .catch(error => {
                            console.error('Error for post', error);
                            this.open_loader = false;
                            this.openMessageDialog(error.response.data.message);
                        })

                } else {
                    if (!this.formValues.follow_des) {
                        this.openMessageDialog(`Please fill the Description fields`);
                        this.$refs.description.focus();
                    } else if (!this.formValues.follow_date) {
                        this.openMessageDialog(`Please select the next followup date and time`);
                        this.$refs.dateandtime.focus();
                    } else if (!this.formValues.assign_to || this.formValues.assign_to.length === 0) {
                        this.openMessageDialog(`Please assign the lead to employee`);
                        this.$refs.search.focus();
                    }
                }
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

        handleOutsideClick(event) {
            if (this.$refs.modal && !this.$refs.modal.contains(event.target) && this.showModal && this.isOpen) {
                this.cancelModalData(event);
            }
        },
        //---multiple dropdown---
        filterOptions() {
            // console.log('hello');
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.mobile_number + ''.includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {

                            this.showAddNew = true;
                            return [];
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.mobile_number && option.mobile_number + ''.includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                } else if (enteredValue.length > 1) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.map(emp => emp.name).includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                } else {
                    return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList;
                }
            }
            // console.log(this.formValues.assign_to, 'RRRRR');
            // Return an empty array if no options match the filter
            // console.log(this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList);
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.map(emp => emp.id).includes(option.id)) : this.employeeList;
        },

        selectOptionMultiple(option) {
            if (!this.formValues.assign_to) {
                this.formValues.assign_to = []; // Initialize assign_to as an array if it's not already
            }
            this.formValues.assign_to.push(option); // Add the selected option to assign_to
            this.search = ''; // Clear the search input
            // this.showOptions = false; // Hide the options dropdown
            this.$nextTick(() => {
                // Focus on the search input after selecting an option
                if (this.$refs['search']) {
                    this.$refs['search'].focus();
                    this.$refs['search'].click();
                }
            });
        },
        removeOption(option) {
            this.formValues['assign_to'] = this.formValues['assign_to'].filter(selected => selected.id !== option.id);
        },

        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
                this.isInputFocused.assign_to = false;
            }, 300); // Add delay to allow click events on options
        },
        openModalEmployee() {
            this.showModal_employee = true;
            this.EmployeeName = this.search;
            this.showOptions = false;
            this.showAddNew = null;
        },

        closeModalEmployee(data) {
            if (data) {
                if (this.formValues.assign_to) {
                    this.formValues.assign_to.push(data);
                } else {
                    this.formValues.assign_to = [];
                    this.formValues.assign_to.push(data);
                }
                this.employeeList.push(data);
            }
            this.showModal_employee = false;
            this.showOptions = false;
            this.search = '';

        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    this.openModal();
                    // this.openModal(product_name);
                }
            }
            else if (type === 'multiple') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectOptionMultiple(optionArray[this.selectedIndex]);
                }
                else {
                    this.openModalEmployee();
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        getDefaultDate() {
            const currentDate = new Date();
            currentDate.setHours(currentDate.getHours() + 5);
            currentDate.setMinutes(currentDate.getMinutes() + 30);
            this.formValues.follow_date = currentDate.toISOString().slice(0, 16);
        },
        formattedDateTime() {
            const currentDate = new Date();
            currentDate.setHours(currentDate.getHours() + 5);
            currentDate.setMinutes(currentDate.getMinutes() + 30);
            return currentDate.toISOString().slice(0, 16);
        },
        //---employee list
        getEmployeeList() {
            this.fetchEmployeeList();
            //     axios.get('/employees', { params: { company_id: this.companyId, page: 1, per_page: 'all' } })
            //         .then(response => {
            //             this.employeeList = response.data.data;
            //             if (this.record && this.record.assign_to && Array.isArray(this.record.assign_to)) {
            //                 let find_duplicate = this.record.assign_to.find(opt => opt.id == this.userId);
            //                 if (this.userId && !find_duplicate) {
            //                     this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
            //                 }
            //             } else {
            //                 this.employeeList.unshift({ id: this.userId, name: 'Assign to me' });
            //             }
            //             if (this.employeeList.length > 2) {
            //                 // Sorting the array alphabetically based on the 'name' key
            //                 this.employeeList.sort((a, b) => {
            //                     // Convert both names to lowercase to ensure case-insensitive sorting
            //                     const nameA = a.name.toLowerCase();
            //                     const nameB = b.name.toLowerCase();
            //                     // Compare the two names
            //                     if (nameA < nameB) {
            //                         return -1;
            //                     }
            //                     if (nameA > nameB) {
            //                         return 1;
            //                     }
            //                     return 0;
            //                 });
            //             }
            //         })
            //         .catch(error => {
            //             console.error('Error get employee', error);
            //         })
            // },
        }
    },
    mounted() {
        this.updateIsMobile();
        // console.log(this.editData, 'What happppppp');
        if (!this.editData) {
            this.formValues = { service_status: '1' };
        }
        if (this.currentEmployee && this.currentEmployee.length > 0) {
            this.employeeList = this.currentEmployee;
        } else {
            this.getEmployeeList();
        }
        setInterval(() => {
        }, 100); // 10 minutes in milliseconds
        //--get default--
        this.getDefaultDate();
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                // console.log(this.record, 'WWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWW');
                this.formValues.assign_to = this.lead_data && this.lead_data.assign_to ? this.lead_data.assign_to : [];
                this.formValues.leadstatus_id = this.lead_data && this.lead_data.leadstatus_id ? this.lead_data.leadstatus_id : '';
                this.formValues.notes = this.lead_data && this.lead_data.notes ? this.lead_data.notes : '';
                if (this.employeeList.length === 0) {
                    this.getEmployeeList();
                }
                this.$nextTick(() => {
                    if (this.$refs.serviceList) {
                        this.$refs.serviceList.focus();
                        this.$refs.serviceList.click();
                    }
                })
            }, 100);
        },
        currentEmployee: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.employeeList = newValue;
                }
            }
        }

    },
}
</script>
<style scoped>
.overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

/* Responsive styling for mobile view */
/* @media (max-width: 767px) {
    .modal {
        height: 80vh; */
/* Adjust the height as needed */
/* }

    .modal-content {
        overflow-y: auto;
    }
} */
</style>
