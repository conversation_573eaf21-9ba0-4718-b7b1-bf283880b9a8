<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\Models\User;
use App\Models\Product;
use App\Models\Lead;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Http\Services\MediaUploader;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ImageApiController extends Controller
{

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/image",
     *      summary="Upload image for model",
     *      tags={"Image"},
     *      description="Uploads an image for a specified model (user, product, or lead).",
     *      @OA\RequestBody(
     *          required=true,
     *          description="Image upload data",
     *          @OA\MediaType(
     *              mediaType="multipart/form-data",
     *              @OA\Schema(
     *                  type="object",
     *                  @OA\Property(
     *                      property="model",
     *                      type="string",
     *                      description="The type of model to associate the image with (user, product, or lead).",
     *                      example="user"
     *                  ),
     *                  @OA\Property(
     *                      property="company_id",
     *                      type="integer",
     *                      description="The ID of the model instance to associate the image with.",
     *                      example=1
     *                  ),
     *                  @OA\Property(
     *                      property="image",
     *                      type="string",
     *                      format="binary",
     *                      description="The image file to upload."
     *                  )
     *              )
     *          )
     *      ),
     *      @OA\Response(
     *          response=201,
     *          description="Image uploaded successfully",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(
     *                  property="message",
     *                  type="string",
     *                  description="Success message",
     *                  example="Image uploaded successfully"
     *              ),
     *              @OA\Property(
     *                  property="media_url",
     *                  type="string",
     *                  description="URL of the uploaded image",
     *                  example="https://example.com/image.jpg"
     *              )
     *          )
     *      ),
  
     *      @OA\Response(
     *          response=404,
     *          description="Not Found",
     *          @OA\JsonContent(
     *              type="object",
     *              @OA\Property(
     *                  property="error",
     *                  type="string",
     *                  description="Error message",
     *                  example="Model not found"
     *              )
     *          )
     *      )
     * )
     */
    public function store(Request $request)
    {
        // Validate the request data
        $validator = Validator::make($request->all(), [
            'model' => 'required',
            'company_id' => 'required',
            'image' => 'required|file|max:2048|mimes:jpeg,png,gif,pdf,txt,csv,webp', // Example validation for image upload
        ]);
    
        if ($validator->fails()) {
            return response()->json(['error' => $validator->errors()], 400);
        }
    
      
    
        
        // Store the image and get the URL
        $imageUrl = $this->storeMediaFileData($request->image, $request->company_id.'/'.$request->model.'/'.date('Y').'/'.date('m'));
    
        if (!$imageUrl) {
            return response()->json(['error' => 'Failed to store image'], 500);
        }
        $imageUrl = 'https://storage.track-new.com/' . substr($imageUrl, strpos($imageUrl, '.com/') + 5);
    
        // Return the response with URL and status
        return response()->json(['status' => true, 'media_url' => $imageUrl], 201);
    }

    public function storeMediaFileData($file, $collectionName)
    {
        if ($file) {
            
    
            // Upload the file(s) to the specified collection
            if (is_array($file)) {
                foreach ($file as $value) {
                    $filename = $value->getClientOriginalName();
                    \Storage::disk('s3')->putFileAs($collectionName, $value, $filename);
                    // Get the URL for the uploaded file
                    $url = \Storage::disk('s3')->url("$collectionName/$filename");
                    // Optionally, you can perform additional actions with the URL
                }
            } else {
                $filename = $file->getClientOriginalName();
             
                \Storage::disk('s3')->putFileAs($collectionName, $file, $filename);
                // Get the URL for the uploaded file
                $url = \Storage::disk('s3')->url("$collectionName/$filename");
                // Optionally, you can perform additional actions with the URL
            }
    
            return $url ?? null; // Return the URL of the uploaded file
        }
    
        return null;
    }


public function delete(Request $request)
{
    // Validate the request data
    $validator = Validator::make($request->all(), [
        'model' => 'required',
        'image_url' => 'required|url', // Validate the URL of the image to be deleted
    ]);

    if ($validator->fails()) {
        return response()->json(['error' => $validator->errors()], 400);
    }

    // Extract the path from the image URL
    $path = parse_url($request->image_url, PHP_URL_PATH);

    // Log the extracted path
    //Log::info('Extracted Path: ' . $path);

    // Decode the path (in case of URL-encoded characters)
    $decoded_path = urldecode($path);

    // Delete the image from storage
    if (Storage::disk('s3')->exists($decoded_path)) {
        Storage::disk('s3')->delete($decoded_path);
        return response()->json(['status' => true, 'message' => 'Image deleted successfully'], 200);
    } else {
        return response()->json(['error' => 'Image not found'], 404);
    }
}




}
