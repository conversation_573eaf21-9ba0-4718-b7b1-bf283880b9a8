<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" @reloadDiv="reloadTheService"
                    :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar"></headbar> -->
            <!-- services home -->
            <div class="flex relative" ref="serviceDiv">
                <!-- Display home component by default -->
                <addService :data="viewServiceData" :category_id="category_id" :category_name="category_name"
                    :type="typeofService" :companyId="companyId" :userId="userId" :form_data="form_data"
                    :open_skeleton="open_skeleton" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </addService>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                @reloadDiv="reloadTheService" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
            </sidebar>
        </div> -->
    </div>

</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/services/add_service/headbar.vue';
import addService from '../supporting/services/add_service/addService.vue';
import { mapGetters, mapActions } from 'vuex';
import { useMeta } from '@/composables/useMeta';

export default {
    name: 'add_edit_services',
    components: {
        // sidebar,
        // headbar,
        addService,
    },
    computed: {
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            viewServiceData: null,
            typeofService: null,
            route_item: 3,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            data: [],
            form_data: [],
            //--api integration---
            companyId: null,
            userId: null,
            open_skeleton: true,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Add New Service';
        const pageDescription = 'Effortlessly create new services tailored to your needs and track every step from start to finish for optimal performance and customer satisfaction.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),

        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        // Method to parse the URL
        async parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const checkEditOrAdd = urlParts[urlParts.length - 2];

            if (checkEditOrAdd === 'edit') {
                const category = urlParts[urlParts.length - 4];
                const categoryID = urlParts[urlParts.length - 3];
                const id = urlParts[urlParts.length - 1];
                this.category_name = decodeURIComponent(category);
                // console.log(this.category_name, 'Name', id, 'ID', category, 'Category data', this.servicecategory_dat);
                let findCategory = this.servicecategory_data.find((data) => data.id === Number(categoryID));
                // console.log(findCategory, 'EEE Waht happening....@@@');
                if (findCategory) {
                    const existData = await this.serviceDataList(Number(id));

                    if (existData) {
                        // Find all file form fields
                        let fileFields = JSON.parse(findCategory.form).filter((opt) => opt.type === 'file');

                        // Make a copy of the existing data and set all file fields to empty
                        this.viewServiceData = { ...existData };

                        fileFields.forEach((field) => {
                            this.viewServiceData[field.fieldKey] = '';
                        });

                        this.typeofService = 'edit';
                        this.category_id = categoryID;
                    }
                    if (findCategory.form) {
                        this.form_data = JSON.parse(findCategory.form);
                    }
                }
            } else {
                const category = urlParts[urlParts.length - 3];
                // console.log(category, 'RRRRRRRRRRRR');
                const id = urlParts[urlParts.length - 2];
                // console.log(id, 'What happening...');
                this.category_name = decodeURIComponent(category);
                // console.log(this.category_name, 'OOOOOOoooo');
                this.category_id = id;
                this.typeofService = 'add';
                let findCategory = this.servicecategory_data.find((data) => data.id === Number(id));

                if (findCategory && findCategory.form) {
                    let fileFields = JSON.parse(findCategory.form).filter((opt) => opt.type === 'file');

                    // Create an object with keys from fileFields and empty string values
                    this.viewServiceData = {};

                    fileFields.forEach((field) => {
                        this.viewServiceData[field.fieldKey] = '';
                    });
                }
                if (findCategory && findCategory.form) {
                    this.form_data = JSON.parse(findCategory.form);
                }
                this.open_skeleton = false;
            }
        },
        //---service category---
        serviceCategoryList() {
            // axios.get(`/service_categories`, { params: { company_id: this.companyId } })
            //     .then(response => {
            //         console.log(response.data, 'service gatgory get..!');
            //         this.servicecategory_data = response.data.data;
            //         this.parseUrl();
            //     })
            //     .catch(error => {
            //         console.error('Error:', error);
            //     })
            if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                this.servicecategory_data = this.currentServiceCategory;
                this.parseUrl();
            }
        },
        //---service category---
        serviceDataList(id) {
            // this.open_skeleton = true;
            axios.get(`/services/${id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'service gatgory get..!');
                    this.open_skeleton = false;
                    return response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                    this.open_skeleton = false;
                })
        },
        changeLoaderSkeleton(val) {
            // console.log(val, 'What happening....!!!');
            this.open_skeleton = val;
        },
        //----relaod the div---
        reloadTheService(status) {
            if (status) {
                this.fetchServiceCategoryList();
            }
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }
    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        if (this.currentServiceCategory.length === 0) {
            this.fetchServiceCategoryList();
        } else {
            this.fetchServiceCategoryList();
        }
        this.updateIsMobile();
        this.serviceCategoryList();
        this.updateIsEnableBottom(false);
        // this.parseUrl(); // Call the function to parse the URL
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        this.updateIsEnableBottom(true);

        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.servicecategory_data = newValue;
                    this.parseUrl();
                }
            }
        },
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnableBottom(true);
            // If no modals are open, allow the navigation
            next();
        }
    },

};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
    -webkit-text-size-adjust: none;
    text-size-adjust: none;
}
</style>
