<template>
    <div :class="['w-full']">
        <div :class="['rounded-lg shadow-lg text-white relative', color, `bg-[url(${img_data})]`]">

            <div class="absolute  right-[50px] sm:right-[80px] lg:right-[100px] 2xl:right-[100px] top-[40px]" :class="{
                'text-blue-400': title === 'Services', 'text-green-400': title === 'Leads', 'text-orange-400': title === 'Amc', 'text-pink-400': title === `R<PERSON>'s`, 'text-lime-400': title === 'Sales', 'text-yellow-400': title === `<PERSON><PERSON><PERSON>'s`, 'text-sky-400': title === 'Estimations', 'text-violet-400': title === 'Expenses'
            }">
                <!-- <img :src="img_data" class="ml-[150px] mt-5 w-[50px] h-[50px]  grayscale-0" /> -->
                <font-awesome-icon v-if="title === 'Services'" icon="fa-solid fa-wrench" size="xl"
                    class="w-[50px] h-[50px] z-0" />
                <font-awesome-icon v-if="title === 'Leads'" icon="fas fa-address-book" size="xl"
                    class="w-[50px] h-[50px] z-0" />
                <font-awesome-icon v-if="title === 'Amc'" icon="fas fa-calendar-check" size="xl"
                    class="w-[50px] h-[50px] z-0" />
                <font-awesome-icon v-if="title === `Rma's`" icon="fas fa-exchange-alt" size="xl"
                    class="w-[50px] h-[50px] z-0" />
                <font-awesome-icon v-if="title === 'Sales'" icon="fas fa-shopping-cart" size="xl"
                    class="w-[50px] h-[50px] z-0" />
                <font-awesome-icon v-if="title === `Proforma's`" icon="fas fa-file-alt" size="xl"
                    class="w-[50px] h-[50px] z-0" />
                <font-awesome-icon v-if="title === 'Estimations'" icon="fas fa-calculator" size="xl"
                    class="w-[50px] h-[50px] z-0" />
                <font-awesome-icon v-if="title === 'Expenses'" icon="fas fa-money-bill-wave" size="xl"
                    class="w-[50px] h-[50px] z-0" />
            </div>
            <div class="p-6 py-2 z-50">
                <div
                    class="flex justify-center items-center text-lg sm:text-sm lg:text-xl 2xl:text-xl 3xl:text-2xl font-bold">
                    <p class="z-10">{{ title === 'Amc' ? 'AMC' : title === `Rma's` ? 'RMA' : title }}</p>
                </div>
                <div class="text-sm">
                    <div class="flex justify-between items-center">
                        <p class="z-10">Total</p>
                        <p class="z-10">{{ total }}</p>
                    </div>
                    <div class="flex justify-between items-center">
                        <p class="z-10">{{ title === 'Sales' ? 'Paid' : title === 'Expenses' ? 'Total' :
                            'Completed' }} </p>
                        <p class="z-10">
                            {{ (title === 'Sales' || title == 'Expenses') ? currentCompanyList.currency === 'INR' ?
                                '\u20b9' : currentCompanyList.currency : '' }}
                            {{ title === 'Sales' || title === 'Expenses' ? formatNumber(complete) : complete }}</p>
                    </div>
                    <div class="flex justify-between items-center">
                        <p class="z-10" v-if="title !== 'Expenses'">{{ title === 'Sales' ? 'Unpaid' : 'Pending' }}
                        </p>
                        <p class="z-10" v-if="title !== 'Expenses'">
                            {{ title === 'Sales' ? currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency : '' }}
                            {{ title === 'Sales' ? formatNumber(pending) :
                                pending }}
                        </p>
                        <p v-else>&nbsp;</p>
                    </div>
                </div>
            </div>
            <div :class="['flex justify-center items-center py-1', view_color]">
                <p class="font-bold px-4 flex items-center cursor-pointer text-sm">
                    view <font-awesome-icon icon="fa-solid fa-circle-arrow-right" class="ml-2" />
                </p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        title: {
            type: String,
            required: true
        },
        total: {
            type: Number,
            required: true
        },
        complete: {
            type: [String, Number],
            required: true
        },
        pending: {
            type: Number,
            required: true
        },
        color: {
            type: String,
            required: true
        },
        view_color: {
            type: String,
            required: null
        },
        img_data: {
            type: String,
            required: null
        },
        index_num: {
            type: Number,
            required: null
        },
        currentCompanyList: {
            type: Object,
            required: null
        }

    },
    methods: {
        formatNumber(value) {
            if (value >= 1_00_00_000) {
                // Crore and above
                return (value / 1_00_00_000).toFixed(1) + 'Cr';
            } else if (value >= 1_00_000) {
                // Lakhs
                return (value / 1_00_000).toFixed(1) + 'L';
            } else if (value >= 1_000) {
                // Thousands
                return (value / 1_000).toFixed(1) + 'K';
                // } else if (value >= 100) {
                //     // Hundreds
                //     return (value / 100).toFixed(1) + 'H';
            } else {
                // Less than 100
                return value.toString();
            }
        }
    },
    watch: {

    }
}
</script>

<style scoped>
button {
    transition: background-color 0.3s;
}

button:hover {
    background-color: #f7f7f7;
}
</style>