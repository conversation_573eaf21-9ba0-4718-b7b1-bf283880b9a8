<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateSalesAPIRequest;
use App\Http\Requests\API\UpdateSalesAPIRequest;
use App\Models\InvoiceTemplate;
use App\Models\Sales;
use App\Models\SalesItems;
use App\Models\Invoices;
use App\Models\Customer;
use App\Models\Companies;
use App\Models\SalesPayment;
use App\Models\InvoiceSettings;
use App\Repositories\InvoicesRepository;
use App\Repositories\SalesRepository;
use App\Repositories\SalesItemsRepository;
use App\Repositories\SalesPaymentRepository;
use App\Repositories\ServicesRepository;
use App\Repositories\EstimationRepository;
use App\Repositories\ProformaRepository;
use App\Http\Resources\api\SaleResource;
use Illuminate\Http\Request;
use Ramsey\Uuid\Uuid;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Http\Controllers\AppBaseController;
use App\Http\Services\RelayMessage;
use Illuminate\Support\Facades\Log;
use Response;

/**
 * Class SalesController
 * @package App\Http\Controllers\API
 */

class SalesAPIController extends AppBaseController
{
    /** @var  SalesRepository */
    private $salesRepository;
    private $salesItemsRepository;
    private $salesPaymentRepository;
    private $invoicesRepository;
    private $serviceRepository;
    private $estRepository;
  	private $proRepository;


    public function __construct(ProformaRepository $proRepo, ServicesRepository $serviceRepo, InvoicesRepository $invoicesRepo, SalesRepository $salesRepo, SalesItemsRepository $salesItemRepo, SalesPaymentRepository $salesPaymentRepo, EstimationRepository $estRepository)
    {   
 
        $this->invoicesRepository = $invoicesRepo;
        $this->salesRepository = $salesRepo;
        $this->salesItemsRepository = $salesItemRepo;
        $this->salesPaymentRepository = $salesPaymentRepo;
        $this->serviceRepository = $serviceRepo;
        $this->estRepository = $estRepository;
      	$this->proRepository = $proRepo;
    }
    
    
       	public function createPDF($id, $sign = 0)
    	{
            // ini_set('memory_limit', '1002M');

         	$sales = Sales::where('id', $id)->first(); //$this->salesRepository->findWithRelated($id);

        	if (empty($sales)) {
            	return $this->sendError('Sales not found');
        	}

    
            $company_id = $sales->company_id;

            $invoice = InvoiceSettings::where('company_id', $company_id)->first();
            $customer =  Customer::where('id', $sales->client_id)->first();
            $template = $invoice->template_id; // Replace with your actual column name
            $templateData = InvoiceTemplate::where('id', $template)->first();

       		$saleItems =  SalesItems::where('sales_id', $sales->id)->get();
         	$payments =    SalesPayment::where('sales_id', $sales->id)->get();   
    		$company = Companies::where('id', $company_id)->first();
        	// $pdf = Pdf::loadView("pdf.$templateData->template", [
        	$pdf = Pdf::loadView("pdf.test", [ //temp pdf tamplate2
             	'data' => $sales,
             	'company' => $company,
             	'invoice' => $invoice,
             	'customer' => $customer,
           		'sale_items' => $saleItems,
                'sale_payment' => $payments,
              	'sign' => $sign 
            ]);
          	$filename = $sales->invoice_id ? $sales->invoice_id : 'inv';
              $pdf->set_option('isRemoteEnabled', true);
        	return $pdf->download($filename.'.pdf');      
    	}
    
     	public function viewPDF($id, $sign = 0)
    	{          
        	$sales = Sales::where('id', $id)->first(); //$this->salesRepository->findWithRelated($id);
            if (empty($sales)) {
                return $this->sendError('Sales not found');
            }
            $company_id = $sales->company_id;
            $invoice = InvoiceSettings::where('company_id', $company_id)->first();
            $customer =  Customer::where('id', $sales->client_id)->first();       
            $saleItems =  SalesItems::where('sales_id', $sales->id)->get();
            $payments =    SalesPayment::where('sales_id', $sales->id)->get();         
            $company = Companies::where('id', $company_id)->first();
            $template = $invoice->template_id; // Replace with your actual column name
            $templateData = InvoiceTemplate::where('id', $template)->first();
            // dd($templateData->template);
            // return view("pdf.$templateData->template")->with([
            return view("pdf.test")->with([
                'data' => $sales,
                'company' => $company,
                'invoice' => $invoice,
                'customer' => $customer,
                'sale_items' => $saleItems,
                'sale_payment' => $payments,
             	'sign' => $sign 
           ]);
     
    	}
    

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/sales",
     *      summary="getSalesList",
     *      tags={"Sales"},
     *      description="Get all Sales",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Sales")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        // $sales = $this->salesRepository->all(
        //     $request->except(['skip', 'limit']),
        //     $request->get('skip'),
        //     $request->get('limit')
        // );

        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

         // Base query to filter by company_id
        $baseQuery = Sales::where('company_id', $companyId);
    
        // Clone the base query for separate calculations
        $dueCountQuery = clone $baseQuery;
        $dueSumQuery = clone $baseQuery;
        $totalAmountQuery = clone $baseQuery;
        $revenueQuery = clone $baseQuery;
    
        // Count of records with due_amount > 0
        $dueCount = $dueCountQuery->where('due_amount', '>', 0)->where('status', 'Success')->count();
    
        // Sum of due_amount where due_amount > 0
        $dueSum = $dueSumQuery->where('due_amount', '>', 0)->where('status', 'Success')->sum('due_amount');
        
       
        
        $revenueBalSum = $revenueQuery->where('due_amount', '>', 0)->where('status', 'Success')->sum('balance_amount');
                                  
    
        // Sum of grand_total where due_amount = 0
        $totalAmount = $totalAmountQuery->where('status', 'Success')->sum('grand_total');
    
        // Optimize pagination query
        $salesQuery = $baseQuery->orderBy('id', 'desc');
    
        // Adjust perPage for all records request
        if ($perPage === 'all') {
            $perPage = $salesQuery->count();
        }
    
        // Paginate results
        $sales = $salesQuery->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => SaleResource::collection($sales),//$sales->items(),LeadResource::collection($sales), // Get the paginated items
            'pagination' => [
                'total' => $sales->total(),
                'per_page' => $sales->perPage(),
                'current_page' => $sales->currentPage(),
                'last_page' => $sales->lastPage(),
                'from' => $sales->firstItem(),
                'to' => $sales->lastItem()
            ],
            'due_count' => $dueCount,
            'due_sum' => $dueSum,
            'total_sum' => $totalAmount,
            'total_revenue' => (int)$totalAmount-(int)$revenueBalSum
        ];
        
        return response()->json($response);
        

       
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/sales",
     *      summary="createSales",
     *      tags={"Sales"},
     *      description="Create Sales",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Sales")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Sales"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function Datastore(CreateSalesAPIRequest $request)
    {
        $input = $request->all();           
       // Log::info('Sales Data', $input);    
        
        $salesData = $input['sales_data'];        
      	if (isset($salesData['company_id']) && $salesData['company_id'] === null) {
            return $this->sendError('Please provide company.', 400);
        }
        $salesData['client_id'] = isset($salesData['client_id']) ? $salesData['client_id'] : $salesData['customer_id'];        
        
        if(isset($salesData['invoice_data']['invoice_group'])) {
             
            $lastInvoice = Invoices::where('invoice_group', $salesData['invoice_data']['invoice_group'])->where('company_id', $salesData['company_id'])->latest()->first();
            $lastInvoiceNumber = $lastInvoice ? $lastInvoice->invoice_no + 1 : 1;            

            // Generate the invoice ID
            $invoiceData = $salesData['invoice_data'];
            $invoiceId = $invoiceData['invoice_prefix'] . $lastInvoiceNumber;            
            // Assign the invoice number and ID to invoice data           
            $invoiceData['invoice_no'] = $lastInvoiceNumber;
            $invoiceData['invoice_id'] = $invoiceId;            
            // Create the invoice
            $invoices = $this->invoicesRepository->create($invoiceData);             
                           
            // Update the sales data with the invoice ID
            $salesData['invoice_id'] = $invoices->invoice_id;
              
        }
        
        // if($salesData['invoice_type'] == 'Services' && isset($salesData['service_id']) && !empty($salesData['service_id'])){
            
        //     $serviceData['invoice_id'] =  $invoices->invoice_id;
            
        //     $this->serviceRepository->update($serviceData, $salesData['service_id']);
            
        // }
        $salesData['user_id'] = auth()->user()->id;
        $salesData['updated_by'] = auth()->user()->id;
        
        $uuid = Uuid::uuid4();
        
        $salesData['uuid'] = (string)$uuid;
         
        $sales = $this->salesRepository->create($salesData);  
      
      	
        	if (isset($salesData['estimation_id']) && $salesData['estimation_id'] !== null && !empty($salesData['estimation_id'])) {
                $estData['status'] = 1;
                $estData['inv_id'] = $sales->id;
                $estData['invoice_id'] =  $sales->invoice_id;
                $this->estRepository->update($estData, $salesData['estimation_id']);
            }
          
           if (isset($salesData['proforma_id']) && $salesData['proforma_id'] !== null && !empty($salesData['proforma_id'])) {
                $proData['status'] = 1;
                $proData['invoice_id'] =  $sales->id;
                $this->proRepository->update($proData, $salesData['proforma_id']);
            }   
        
        
        if($salesData['invoice_type'] == 'Services' && isset($salesData['service_id']) && !empty($salesData['service_id'])){
            
            $serviceData['invoice_id'] =  $sales->id;
            
            $this->serviceRepository->update($serviceData, $salesData['service_id']);
            
        }

        foreach ($salesData['sales_item_data'] as $itemData) {
            
            //  var_dump($itemData);
           
            // //  $itemData['sale_id'] = $sales->id;
            
            $salesItemData = [              
                    "product_code" => $itemData['product_code'],
                    "product_name" => $itemData['product_name'],
                    "description" => $itemData['description'],
                    "price" => $itemData['price'] ?? 0,
                    "hsn_code" => $itemData['hsn_code'],
                    "taxvalue" => $itemData['taxvalue'],
                    "tax_type" => $itemData['tax_type'],
                    "tax_name" => $itemData['tax_name'] ?? null,
                    "tax_id" => $itemData['tax_id'] ?? 0,
                    "qty" => $itemData['qty'] ?? 0,
                    "tax" => $itemData['tax'],
                    "total" => $itemData['total'] ?? 0,
                    "discount" => $itemData['discount'] ?? 0,
                    "discount_data" => $itemData['discount_data'],                    
                    "sales_id" => $sales->id,
                    "company_id" => $itemData['company_id'],
                    "barcode_id" => $itemData['barcode_id'],
                    "product_id" => $itemData['product_id'],
                    'serial_no' => $itemData['serial_no'],
                    'notes' => $itemData['notes']
                ];
    
            // Create each sales item
            $salesItems = $this->salesItemsRepository->create($salesItemData);
        }

        $payments = $salesData['sales_payment'];
          //Log::info('Sales Data', $payments);
            foreach ($payments as $payment) {
                // Ensure the payment array has the required keys and valid values
                $timestamp = strtotime(str_replace('/', '-', $payment['payment_date']));
                
                // Create a DateTime object from the timestamp
                $date = new \DateTime();
                $date->setTimestamp($timestamp);
                
                // Format the DateTime object to the desired ISO 8601 format with microseconds
                $outputDate = $date->format('Y-m-d\TH:i:s.u\Z');
                
                
                if (isset($payment['payment_type'], $payment['payment_amount']) && !empty($payment['payment_type']) && $payment['payment_amount'] !== 0) {
                    $this->salesPaymentRepository->create([
                      	'payment_code' => SalesPayment::generatePaymentCode(),
                        'payment_date' => $outputDate,
                        'payment_type' => $payment['payment_type'],
                        'payment_amount' => $payment['payment_amount'],
                        'payment_for' => $payment['payment_for'],
                        'sales_id' => $sales->id,
                        'company_id' => $sales->company_id,
                        'created_by' => $sales->user_id,
                        'customer_id' => $sales->client_id,                        
                        "status" => '0',
                    ]);
                  }  
        
        } 
        
       
        $customer = Customer::find($sales->client_id);
       
        
        
        $relayMessage = new RelayMessage();
        $url = 'https://api.track-new.com/api/download-invoice/'.$sales->id;
     
        if (isset($salesData['issms']) && $salesData['issms'] == true) {
          	if(checkSmsBalance($salesData['company_id']) !== 0){ 
            	$relayMessage->sendSaleEstimate($customer, $url, 'sale');
            }
        }
        
        if (isset($salesData['iswhatsapp']) && $salesData['iswhatsapp'] == true) {
          
          	$customer_name = $customer->first_name ?? ''. ' ' . $customer->last_name ?? '';
        	$contact_number = $customer->contact_number;
            $relayMessage->saleEstimateWhatsAppMessage($customer_name, $contact_number, $url, $sales->company_id, 'sale');
        }

        return $this->sendResponse(new SaleResource($sales), 'Sales saved successfully');
    }
  
  public function store(CreateSalesAPIRequest $request)
    {
        $input = $request->all();   	
        
        $salesData = $input['sales_data'];
      	if (isset($salesData['company_id']) && $salesData['company_id'] === null) {
            return $this->sendError('Please provide company.', 400);
        }
        
        $salesData['client_id'] = isset($salesData['client_id']) ? $salesData['client_id'] : $salesData['customer_id'] ;        
        
        if (isset($salesData['invoice_data']['invoice_group'])) {

            $invoiceData = $salesData['invoice_data'];
            $invoiceGroup = $invoiceData['invoice_group'];
            $companyId = $salesData['company_id'];
            $prefix = $invoiceData['invoice_prefix'] ?? 'INVA';
            $fy = getFinancialYearLabel();
        
            // Fetch reset setting (boolean)
            $invoiceSettings = InvoiceSettings::where('company_id', $companyId)->first();
            $resetFY = $invoiceSettings->reset_fy ?? false;
        
            // Build base query
            $query = Invoices::where('invoice_group', $invoiceGroup)
                ->where('company_id', $companyId);

            if($resetFY){
                $query->where('fin_year', $fy);
            }
                
        
            // Get last invoice number
            $lastInvoice = $query->latest()->first();
            $lastInvoiceNumber = $lastInvoice ? ((int) $lastInvoice->invoice_no + 1) : 1;            

            // Generate invoice ID like 25-26/INVA000001
            $formattedNumber = $prefix . $lastInvoiceNumber;
            $invoiceId = $resetFY ? ($prefix . '/'.$fy . '/' . $lastInvoiceNumber) : $formattedNumber;

        
            // Assign values to invoice data
            $invoiceData['invoice_no'] = $lastInvoiceNumber;
            $invoiceData['invoice_id'] = $invoiceId;        
            $invoiceData['fin_year'] = $fy;
            
           
        
            // Create invoice
            $invoices = $this->invoicesRepository->create($invoiceData);
        
            // Update sales data
            $salesData['invoice_id'] = $invoices->invoice_id;
        }
        
        
        // if($salesData['invoice_type'] == 'Services' && isset($salesData['service_id']) && !empty($salesData['service_id'])){
            
        //     $serviceData['invoice_id'] =  $invoices->invoice_id;
            
        //     $this->serviceRepository->update($serviceData, $salesData['service_id']);
            
        // }
        $salesData['user_id'] = auth()->user()->id;
        $salesData['updated_by'] = auth()->user()->id;
        
        $uuid = Uuid::uuid4();
        
        $salesData['uuid'] = (string)$uuid;
         
        $sales = $this->salesRepository->create($salesData);  
      
      	
        if (isset($salesData['estimation_id']) && $salesData['estimation_id'] !== null && !empty($salesData['estimation_id'])) {
                $estData['status'] = 1;
                $estData['inv_id'] = $sales->id;
                $estData['invoice_id'] =  $sales->invoice_id;
                $this->estRepository->update($estData, $salesData['estimation_id']);
            }
          
           if (isset($salesData['proforma_id']) && $salesData['proforma_id'] !== null && !empty($salesData['proforma_id'])) {
                $proData['status'] = 1;
                $proData['invoice_id'] =  $sales->id;
                $this->proRepository->update($proData, $salesData['proforma_id']);
            }   
        
        
        if($salesData['invoice_type'] == 'Services' && isset($salesData['service_id']) && !empty($salesData['service_id'])){
            
            $serviceData['invoice_id'] =  $sales->id;
            
            $this->serviceRepository->update($serviceData, $salesData['service_id']);
            
        }

        foreach ($salesData['sales_item_data'] as $itemData) {
            
            //  var_dump($itemData);
           
            // //  $itemData['sale_id'] = $sales->id;
            
            $salesItemData = [              
                    "product_code" => $itemData['product_code'],
                    "product_name" => $itemData['product_name'],
                    "description" => $itemData['description'],
                    "price" => $itemData['price'],
                    "hsn_code" => $itemData['hsn_code'],
                    "taxvalue" => $itemData['taxvalue'],
                    "tax_type" => $itemData['tax_type'],
                    "tax_name" => $itemData['tax_name'] ?? null,
                    "tax_id" => $itemData['tax_id'] ?? 0,
                    "qty" => $itemData['qty'],
                    "tax" => $itemData['tax'],
                    "total" => $itemData['total'],
                    "discount" => $itemData['discount'],
                    "discount_data" => $itemData['discount_data'],                    
                    "sales_id" => $sales->id,
                    "company_id" => $itemData['company_id'],
                    "barcode_id" => $itemData['barcode_id'],
                    "product_id" => $itemData['product_id'],
                    'serial_no' => $itemData['serial_no'],
                    'notes' => $itemData['notes']
                ];
    
            // Create each sales item
            $salesItems = $this->salesItemsRepository->create($salesItemData);
        }

        $payments = $salesData['sales_payment'];

       
            foreach ($payments as $payment) {
                // Ensure the payment array has the required keys and valid values
                $timestamp = strtotime(str_replace('/', '-', $payment['payment_date']));
                
                // Create a DateTime object from the timestamp
                $date = new \DateTime();
                $date->setTimestamp($timestamp);
                
                // Format the DateTime object to the desired ISO 8601 format with microseconds
                $outputDate = $date->format('Y-m-d\TH:i:s.u\Z');
                
                
                if (isset($payment['payment_type'], $payment['payment_amount']) && $payment['payment_amount'] !== 0) {
                    $this->salesPaymentRepository->create([
                      	'payment_code' => SalesPayment::generatePaymentCode(),
                        'payment_date' => $outputDate,
                        'payment_type' => $payment['payment_type'] ?? 'Cash',
                        'payment_amount' => $payment['payment_amount'],
                        'payment_for' => $payment['payment_for'],
                        'sales_id' => $sales->id,
                        'company_id' => $sales->company_id,
                        'created_by' => $sales->user_id,
                        'customer_id' => $sales->client_id,                        
                        "status" => '0',
                    ]);
                  }  
        
        }         
       
         $customer = Customer::find($sales->client_id);
       
        
        
        $relayMessage = new RelayMessage();
        $url = 'https://api.track-new.com/api/download-invoice/'.$sales->id;
     
        if (isset($salesData['issms']) && $salesData['issms'] == true) {
          	if(checkSmsBalance($salesData['company_id']) !== 0){ 
            	$relayMessage->sendSaleEstimate($customer, $url, 'sale');
            }
        }
        
        if (isset($salesData['iswhatsapp']) && $salesData['iswhatsapp'] == true) {
          
          	$customer_name = $customer->first_name ?? ''. ' ' . $customer->last_name ?? '';
        	$contact_number = $customer->contact_number;
            $relayMessage->saleEstimateWhatsAppMessage($customer_name, $contact_number, $url, $sales->company_id, 'sale');
        }

        return $this->sendResponse(new SaleResource($sales), 'Sales saved successfully');
      	
      	

        
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/sales/{id}",
     *      summary="getSalesItem",
     *      tags={"Sales"},
     *      description="Get Sales",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Sales",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Sales"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Sales $sales */
        $sales = $this->salesRepository->findWithRelated($id);

        if (empty($sales)) {
            return $this->sendError('Sales not found');
        }

        return $this->sendResponse(new SaleResource($sales), 'Sales retrieved successfully');
    }

    //Need to fetch details based on clint_id
    // public function getClientDetails($clintId)
    // {
    //     $sales = Sales::where('client_id', $clientId)->get();

    //     if ($sales->isEmpty()) {
    //         return 'No sales records found for the given client ID.';
    //     }

    //     return $sales;
    // }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/sales/{id}",
     *      summary="updateSales",
     *      tags={"Sales"},
     *      description="Update Sales",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Sales",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Sales")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Sales"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateSalesAPIRequest $request)
    {      
        
        try {
        // Start a database transaction
        DB::beginTransaction();

        $input = $request->all();  
        

        $salesData = $input['sales_data'];

        $sale = $this->salesRepository->find($id);

        if (empty($sale)) {
                return $this->sendError('Sales not found');
            }
          
         // Check if the status is Cancel and if there are any items or payments
      
        
            
        $salesData['updated_by'] = auth()->user()->id;
        unset($salesData['invoice_id']);
        $sales = $this->salesRepository->update($salesData, $id);
        
          if ($sales->status == 'Cancel') {
            // Update sales items and payments status to 1
            $updatedSalesItems =  SalesItems::where('sales_id', $id)->update(['status' => 1]);
            $updatedSalesPayments = SalesPayment::where('sales_id', $id)->update(['status' => 1]);

            // Debug output
            if (!$updatedSalesItems) {
                DB::rollBack();
                return $this->sendError('Failed to update SalesItems status');
            }

            if (!$updatedSalesPayments) {
                DB::rollBack();
                return $this->sendError('Failed to update SalesPayment status');
            }
        }
        if(isset($salesData['invoice_data'])){

                $invoiceData = $salesData['invoice_data'];
                //$invoices = \App\Models\Invoices::where('invoice_id', $sales->invoice_id)->update($invoiceData);
               // $invoices = $this->invoicesRepository->update($invoiceData, $salesData['invoice_id']);            
                //$salesData['invoice_id'] = $invoices->invoice_id;
                
                if (isset($salesData['estimation_id']) && $salesData['estimation_id'] !== null && !empty($salesData['estimation_id'])) {
                        $estData['status'] = 1;
                        $estData['inv_id'] = $sales->id;
                        $estData['invoice_id'] =  $sale->invoice_id;
                        $this->estRepository->update($estData, $salesData['estimation_id']);
                }
          
          		if (isset($salesData['proforma_id']) && $salesData['proforma_id'] !== null && !empty($salesData['proforma_id'])) {
                    $estData['status'] = 1;
                    $estData['invoice_id'] =  $sale->id;
                    $this->proRepository->update($estData, $salesData['proforma_id']);
                }    
            }
      
        $keepIds = [];
        foreach ($salesData['sales_item_data'] as $itemData) {
            if (isset($itemData['id'])) {
                $keepIds[] = $itemData['id'];
            }
        }

    
        \App\Models\SalesItems::where('sales_id', $id)->whereNotIn('id', $keepIds)
            ->delete();
            
        
        foreach ($salesData['sales_item_data'] as $itemData) {
          
            $itemData['sales_id'] = $id;
          	
            if(isset($itemData['id'])){
                $salesItemId = $itemData['id'];
                $salesItems = $this->salesItemsRepository->update($itemData, $salesItemId);         
                
            }  
            else{
                
                $salesItems = $this->salesItemsRepository->create($itemData);
                
            }       
            
        }
        

       $keepPayIds = [];
        foreach ($salesData['sales_payment'] as $payDatas) {
            if (isset($payDatas['id'])) {
                $keepPayIds[] = $payDatas['id'];
            }
        }
          
          SalesPayment::where('sales_id', $id)->whereNotIn('id', $keepPayIds)
            ->delete();

        foreach ($salesData['sales_payment'] as $payData) {
          
            $payData['sales_id'] = $id;
          	
            if(isset($payData['id'])){
              
                $salesPaymentId = $payData['id'];
               	$payData['company_id'] =  $sales->company_id;
                $payData['created_by'] = $salesData['updated_by'];
                $this->salesPaymentRepository->update($payData, $salesPaymentId);               
            }  
            else{
                
                $this->salesPaymentRepository->create($payData);
                
            }       
            
        }       
        
          $customer = Customer::find($sales->client_id);        

          $relayMessage = new RelayMessage();
          $url = 'https://api.track-new.com/api/download-invoice/'.$sales->id;

          if (isset($salesData['issms']) && $salesData['issms'] == true) {
            if(checkSmsBalance($sales->company_id) !== 0){ 
            	$relayMessage->sendSaleEstimate($customer, $url, 'sale');
            }
          }

          if (isset($salesData['iswhatsapp']) && $salesData['iswhatsapp'] == true) {
            
            $customer_name = $customer->first_name ?? ''. ' ' . $customer->last_name ?? '';
        	$contact_number = $customer->contact_number;
            
           	$is = $relayMessage->saleEstimateWhatsAppMessage($customer_name, $contact_number, $url, $sales->company_id, 'sale');

          }
        
        DB::commit();
        return $this->sendResponse(new SaleResource($sales), 'Sales updated successfully');
     } catch (\Exception $e) {
            // If any operation fails, rollback the transaction
            DB::rollBack();    
            // Handle the error or log it
            return $this->sendError('Updated Failed'.$e);
        }
        
    }
  
  
public function processCustomerPayments(UpdateSalesAPIRequest $request)
{
    $input = $request->all();  
    $salesDataArray = $input['sales_data']; // Assume sales_data is an array of sales entries

    DB::beginTransaction();
  	$payCode = SalesPayment::generatePaymentCode();
    try {
        foreach ($salesDataArray as $salesData) {
            $sale = $this->salesRepository->find($salesData['id']);

            if (empty($sale)) {
                DB::rollBack();
                return $this->sendError('Sales not found for ID: ' . $salesData['id']);
            }
       		$this->salesRepository->update($salesData, $sale->id);

            $payIds = array_column($salesData['sales_payment'], 'id');
            SalesPayment::where('sales_id', $sale->id)->whereNotIn('id', $payIds)->delete();

            foreach ($salesData['sales_payment'] as $payment) {
                $payment['company_id'] = $sale->company_id;
                $payment['updated_by'] = auth()->user()->id;
                $payment['customer_id'] = $sale->client_id;
                $payment['sales_id'] = $sale->id;
              	// Ensure the payment array has the required keys and valid values
                $timestamp = strtotime(str_replace('/', '-', $payment['payment_date']));
                
                // Create a DateTime object from the timestamp
                $date = new \DateTime();
                $date->setTimestamp($timestamp);
                
                // Format the DateTime object to the desired ISO 8601 format with microseconds
                $outputDate = $date->format('Y-m-d\TH:i:s.u\Z');
              	

                if (isset($payment['id'])) {
                  	$payment['payment_date'] = $outputDate;
                    $this->salesPaymentRepository->update($payment, $payment['id']);
                } else if (isset($payment['payment_type'], $payment['payment_amount'])) {
                    $this->salesPaymentRepository->create([
                      	'payment_code' => $payCode,
                        'payment_date' => $outputDate,
                        'payment_type' => $payment['payment_type'],
                        'payment_amount' => $payment['payment_amount'],
                        'payment_for' => $payment['payment_for'],
                        'sales_id' => $sale->id,
                        'company_id' => $sale->company_id,
                        'created_by' => auth()->user()->id,
                        'customer_id' => $sale->client_id,
                        'status' => '0',
                    ]);
                }
            }
        }
        DB::commit();
        return $this->sendResponse('true', 'All customer payments processed successfully');
    } catch (\Exception $e) {
        DB::rollBack();
        return $this->sendError('An error occurred while processing customer payments: ' . $e->getMessage());
    }
}


    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/sales/{id}",
     *      summary="deleteSales",
     *      tags={"Sales"},
     *      description="Delete Sales",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Sales",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    // public function destroy($id)
    // {
    //     /** @var Sales $sales */
    //     $sales = $this->salesRepository->find($id);
        
    //     if($sales->invoice_type == 'Services'){
    //          $this->serviceRepository->find($sales->service_idS);
    //     }
        
    //      $salesItems = $this->salesItemsRepository->find($id);
         
    //       $invoices = \App\Models\Invoices::where('invoice_id', $sales->invoice_id);
          
    //       \App\Models\SalesPayment::where('sales_id', $id);

    //     if (empty($sales)) {
    //         return $this->sendError('Sales not found');
    //     }

    //     $sales->delete();

    //     return $this->sendSuccess('Sales deleted successfully');
    // }
    
    public function destroy($id)
    {
        // Find the Sales record
        $sales = $this->salesRepository->find($id);
        
        // Check if the Sales record exists
        if (empty($sales)) {
            return $this->sendError('Sales not found');
        }
        
        // Delete related records based on invoice type
        if ($sales->invoice_type == 'Services') {
            
               // Delete related records based on invoice type
            if ($sales->invoice_type == 'Services') {                    

              $service = $this->serviceRepository->find($sales->service_id);
                 if (!empty($service)) {            
                    $serviceData['invoice_id'] =  NULL;
                    $this->serviceRepository->update($serviceData, $sales->service_id);
                 }
                // Delete related Service record
                //$this->serviceRepository->delete($sales->service_id);      

            }
            
            
            
        }
        
        // Delete SalesItems records
        \App\Models\SalesItems::where('sales_id', $id)->delete();
        
        // Delete related Invoices records
        \App\Models\Invoices::where('invoice_id', $sales->invoice_id)->where('customer_id', $sales->client_id)->where('company_id', $sales->company_id)->delete();
        
        // Delete related SalesPayment records
        \App\Models\SalesPayment::where('sales_id', $id)->delete();
    
        // Delete the Sales record
        $sales->delete();
    
        // Return success response
        return $this->sendSuccess('Sales deleted successfully');
    }
    
   function convertAmountToWords($amount) {
    $ones = array(
        0 => 'Zero',
        1 => 'One',
        2 => 'Two',
        3 => 'Three',
        4 => 'Four',
        5 => 'Five',
        6 => 'Six',
        7 => 'Seven',
        8 => 'Eight',
        9 => 'Nine',
        10 => 'Ten',
        11 => 'Eleven',
        12 => 'Twelve',
        13 => 'Thirteen',
        14 => 'Fourteen',
        15 => 'Fifteen',
        16 => 'Sixteen',
        17 => 'Seventeen',
        18 => 'Eighteen',
        19 => 'Nineteen'
    );

    $tens = array(
        0 => 'Twenty',
        1 => 'Thirty',
        2 => 'Forty',
        3 => 'Fifty',
        4 => 'Sixty',
        5 => 'Seventy',
        6 => 'Eighty',
        7 => 'Ninety'
    );

    $hundreds = array(
        'Hundred',
        'Thousand',
        'Lakh',
        'Crore'
    );

    $num = number_format($amount, 2, '.', '');
    $num_arr = explode('.', $num);
    $amount_in_words = '';

    foreach ($num_arr as $key => $val) {
        $val = (int)$val;
        if ($val >= 0 && $val <= 19) {
            $amount_in_words .= $ones[$val];
        } elseif ($val >= 20 && $val <= 99) {
            $tens_val = (int)($val / 10);
            $ones_val = $val % 10;
            $amount_in_words .= $tens[$tens_val - 2];
            if ($ones_val > 0) {
                $amount_in_words .= ' ' . $ones[$ones_val];
            }
        } elseif ($val >= 100 && $val <= 999) {
            $hundreds_val = (int)($val / 100);
            $remainder = $val % 100;
            $amount_in_words .= $ones[$hundreds_val] . ' ' . $hundreds[0];
            if ($remainder > 0) {
                $amount_in_words .= ' ' . convertAmountToWords($remainder);
            }
        } elseif ($val >= 1000) {
            $hundreds_val = (int)($val / 1000);
            $remainder = $val % 1000;
            $amount_in_words .= convertAmountToWords($hundreds_val) . ' ' . $hundreds[1];
            if ($remainder > 0) {
                $amount_in_words .= ' ' . convertAmountToWords($remainder);
            }
        }
        if ($key == 0) {
            $amount_in_words .= ' Rupees';
        } elseif ($key == 1) {
            $amount_in_words .= ' and Paise';
        }
    }

    return $amount_in_words;
}








}
