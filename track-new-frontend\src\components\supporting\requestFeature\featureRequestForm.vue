<template>
    <div class="max-w-3xl mx-auto p-4 bg-white rounded shadow">
        <h2 class="text-2xl font-bold mb-4">Request Feature</h2>
        <form @submit.prevent="submitRequest">
            <div class="mb-4">
                <label class="block text-gray-700">Feature Title</label>
                <input type="text" v-model="title" class="w-full px-3 py-2 border border-gray-300 rounded"
                    placeholder="Enter feature title" required />
            </div>
            <div class="mb-4">
                <label class="block text-gray-700">Description</label>
                <textarea v-model="description" class="w-full px-3 py-2 border border-gray-300 rounded"
                    placeholder="Describe the feature" required></textarea>
            </div>
            <div class="mb-4">
                <label class="block text-gray-700">Priority</label>
                <select v-model="priority" class="w-full px-3 py-2 border border-gray-300 rounded">
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                </select>
            </div>
            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600">
                Submit Request
            </button>
        </form>

        <!-- DataTable to display submitted features -->
        <div class="mt-8">
            <h3 class="text-lg font-semibold mb-4">Submitted Requests</h3>
            <table class="min-w-full bg-white border border-gray-300">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="px-4 py-2 text-left">Title</th>
                        <th class="px-4 py-2 text-left">Description</th>
                        <th class="px-4 py-2 text-left">Priority</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="request in requests" :key="request.id">
                        <td class="px-4 py-2">{{ request.title }}</td>
                        <td class="px-4 py-2">{{ request.description }}</td>
                        <td class="px-4 py-2">{{ request.priority }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            title: '',
            description: '',
            priority: 'low',
            requests: [] // Store submitted feature requests
        };
    },
    methods: {
        submitRequest() {
            const newRequest = {
                id: Date.now(),
                title: this.title,
                description: this.description,
                priority: this.priority
            };
            this.requests.push(newRequest);

            // Clear form fields after submission
            this.title = '';
            this.description = '';
            this.priority = 'low';
        }
    }
};
</script>