<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded-lg" :class="{
            'scale-100': isOpen, 'scale-0': !isOpen,
            'h-auto max-h-screen': formValues.serial_number.length <= 4,  // Less content, center and fit screen
            'h-screen overflow-auto': formValues.serial_number.length > 4  // More content, enable scrolling
        }">
            <div
                class="flex justify-between items-center relative w-full px-4 py-4 rounded rounded-b-none set-header-background rounded-t">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-12">
                    Item Serial Number
                </p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block text-sm bg p-5">
                <p class="mb-3"><span class="font-bold text-sm">Item Name:</span><span class="ml-2">{{
                    item_data.product_name }}</span></p>
                <!-- Serial Number-->
                <div class="w-full mb-4 relative">
                    <div v-if="typeOfInvoice && typeOfInvoice !== 'estimation'" class="w-full">
                        <label for="serial_number" class="text-sm font-bold">
                            Serial Number:
                        </label>
                        <div v-for="(serial, index) in formValues.serial_number" :key="index"
                            class="flex flex-col justify-center items-center w-full mt-1 relative">
                            <input v-model="formValues.serial_number[index]" type="text"
                                @keyup.enter="handleFocus(index + 1)"
                                @keydown.arrow-down.prevent="handleFocus(formValues.serial_number.length - 1 > index ? index + 1 : 0)"
                                @keydown.arrow-up.prevent="handleFocus(0 !== index ? index - 1 : formValues.serial_number.length - 1)"
                                :ref="'serial' + index"
                                class="text-sm p-1 py-1 border border-gray-300 w-full focus:border-blue-500 rounded outline-none"
                                placeholder="Enter the serial number" />
                            <!-- <textarea v-model="formValues.notes[index]" rows="2"
                            class="mt-1 w-full border rounded px-2 py-1" placeholder="Enter the serial number notes">
                            </textarea> -->
                            <!-- <span :class="{ 'hidden': index === 0 }" @click="removeSerial(index)"
                            class="cursor-pointer px-2">
                            <img :src="del_icon" alt="delete icon" class="hidden w-6 h-6" /></span>
                        <span :class="{ 'hidden': index > 0 }" @click="removeSerial(index)"
                            class="cursor-pointer px-5 w-6 h-6"></span> -->
                        </div>
                    </div>
                    <div class="pb-3">
                        <label class="text-gray-900 py-1 block">Notes:</label>
                        <textarea rows="4" v-model="formValues.notes" ref="noteItem"
                            class="p-1 border border-gray-400 w-full"></textarea>
                    </div>
                </div>
                <p v-if="message !== '' && formValues.serial_number[0] === ''" class="text-red-600 text-xs">{{ message
                    }}</p>

                <!--buttons-->
                <div class="flex justify-center items-center">
                    <button
                        class="border border-green-700 rounded text-white bg-green-700 shadow-inner shadow-green-200 px-6 py-2 hover:bg-green-600"
                        @click="saveSerial">Save</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        item_data: Object,
        typeOfInvoice: String,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            del_icon: '/images/service_page/del.png',
            isOpen: false,
            formValues: { serial_number: [''] },  //, notes: [''] 
            message: '',
            isInputFocused: {}
        };
    },
    methods: {
        closeModal(data) {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event

            setTimeout(() => {
                //--Array.isArray(data)
                if (data.serial_no || data.notes) {
                    this.$emit('close-Modal', data);
                } else {
                    this.$emit('close-Modal');
                }
                this.message = '';
            }, 300);
        },
        //---close message---
        closeMessageDialog() {
            this.isMessage = false;
        },
        //---save terms and condition---
        saveSerial() {
            // Filter out empty strings from the serial numbers array
            // console.log(this.formValues.serial_number);
            if (this.typeOfInvoice !== 'estimation') {
                if (Array.isArray(this.formValues.serial_number)) {
                    const nonEmptySerials = this.formValues.serial_number.filter(serial => serial.trim() !== '');
                    // if (nonEmptySerials.length > 0) {
                    this.formValues.serial_number = [''];
                    // console.log('hello', nonEmptySerials);
                    let share_data = { serial_no: nonEmptySerials, notes: this.formValues.notes };
                    this.closeModal(share_data);
                    // }
                    // else {
                    //     this.message = 'Field is empty. Please fill in at least one serial number.';
                    // }
                }
                else if (!Array.isArray(this.formValues.serial_number) && this.formValues && this.formValues.notes && this.formValues.notes != '') {
                    let share_data = { serial_no: [''], notes: this.formValues.notes };
                    this.closeModal(share_data);
                }
            }
            else {
                if (this.formValues.notes) {
                    let share_data = { notes: this.formValues.notes };
                    this.closeModal(share_data);
                }
            }
        },
        //---add new--
        displayNextField(index, type) {
            // console.log(this.formValues.serial_number[index], 'Waht happening..!');
            if (this.formValues.serial_number[index].length > 0 && this.formValues.serial_number[index + 1] === undefined) {
                this.formValues.serial_number.push('');
            }
            else if (type === 'enter' && index === this.formValues.serial_number.length - 1) {
                this.formValues.serial_number.push('');
            }
        },
        //---remove index--
        removeSerial(index) {
            this.formValues.serial_number.splice(index, 1); // Remove the input field at the specified index
        },
        handleFocus(index) {
            this.$nextTick(() => {
                if (this.typeOfInvoice && this.typeOfInvoice !== 'estimation') {
                    const lastIndex = index;
                    const refName = 'serial' + lastIndex;
                    const inputElement = this.$refs[refName];
                    // console.log(inputElement, 'EEEEe');
                    if (inputElement && inputElement[0]) {
                        inputElement[0].focus();
                    }
                } else {
                    const noteField = this.$refs.noteItem;
                    if (noteField) {
                        noteField.focus();
                    }

                }
            });
        },
        //---make update serial number---
        updateSerialNumber(newValue) {
            if (newValue && newValue.serial_no && newValue.serial_no.length > 0) {
                if (newValue.serial_no.length == newValue.qty) {
                    this.formValues.serial_number = newValue.serial_no;

                } else if (newValue.serial_no.length < newValue.qty) {
                    let extends_array = Array(newValue.qty - newValue.serial_no.length).fill('');
                    this.formValues.serial_number = [...newValue.serial_no, ...extends_array];
                } else if (newValue.serial_no.length > newValue.qty) {
                    this.formValues.serial_number = newValue.serial_no.slice(0, newValue.qty);
                }
                //----purchase updated---
                if (newValue.serial_no.length == newValue.total_qty) {
                    this.formValues.serial_number = newValue.serial_no;
                } else if (newValue.serial_no.length < newValue.total_qty) {
                    let extends_array = Array(newValue.total_qty - newValue.serial_no.length).fill('');
                    this.formValues.serial_number = [...newValue.serial_no, ...extends_array];
                } else if (newValue.serial_no.length > newValue.total_qty) {
                    this.formValues.serial_number = newValue.serial_no.slice(0, newValue.total_qty);
                }
                // this.formValues.serial_number.push('');
            } else if (newValue.qty) {
                let qty = newValue.qty;
                let newArray = Array(qty).fill('');
                this.formValues.serial_number = newArray;
            } else if (this.typeOfInvoice === 'purchase') {
                let qty = newValue.total_qty;
                let newArray = Array(qty).fill('');
                this.formValues.serial_number = newArray;
            }
            if (newValue.notes) {
                this.formValues.notes = newValue.notes;
            } else {
                this.formValues.notes = '';
            }
        }
    },
    watch: {
        showModal(newValue) {
            // console.log(newValue, 'Waht happening..!', this.formValues.serial_number);
            setTimeout(() => {
                this.isOpen = newValue;
                if (this.item_data && this.item_data.serial_no && this.item_data.serial_no.length > 0) {
                    this.handleFocus(this.formValues.serial_number.length - 1);
                    // console.log(this.item_data, 'RRRRRRRRRRRRRRRRRRRRRRR');
                    if (this.item_data.serial_no.length == this.item_data.qty) {
                        this.formValues.serial_number = this.item_data.serial_no;

                    } else if (this.item_data.serial_no.length < this.item_data.qty) {
                        let extends_array = Array(this.item_data.qty - this.item_data.serial_no.length).fill('');
                        this.formValues.serial_number = [...this.item_data.serial_no, ...extends_array];
                    } else if (this.item_data.serial_no.length > this.item_data.qty) {
                        this.formValues.serial_number = this.item_data.serial_no.slice(0, this.item_data.qty);
                    }
                    if (this.formValues.serial_number.length > 0) {
                        this.handleFocus(this.formValues.serial_number.length - 1);
                    }
                }
                // console.log('hello');
            }, 100);
        },
        item_data: {
            deep: true,
            handler(newValue) {
                this.updateSerialNumber(newValue);
            }
        }

    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>
