// store/modules/warehouse.js
import axios from "axios";

const state = {
  warehouse_list: [],
  pagination: {},
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
};

const mutations = {
  SET_WAREHOUSE(state, { warehouse_list, pagination }) {
    state.warehouse_list = warehouse_list;
    state.pagination = pagination;
  },
  RESET_STATE(state) {
    state.warehouse_list = [];
    state.pagination = {};
    state.lastFetchTime = null;
    state.isFetching = false;
  },
  SET_LAST_FETCH_TIME(state, time) {
    state.lastFetchTime = time; // Save the timestamp when the API was last accessed
  },
  SET_IS_FETCHING(state, status) {
    state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
  }
};

const actions = {
  updateWarehouseName({ commit }, warehouse_list) {
    // Simulate an asynchronous operation (e.g., API call) to update customer name
    setTimeout(() => {
      // Commit mutation to update customer name
      commit("SET_WAREHOUSE", warehouse_list);
    }, 1000); // Simulated delay of 1 second
  },
  async fetchWarehouseList({ commit, state, rootState }) {
    const now = new Date().toISOString();
    const thirtySecondsInMilliseconds = 5 * 1000;
    const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['warehouse_update'];
    // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
    if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)  {
      return; // Skip request if less than 30 seconds have passed since the last request
    }
    try {
      const { company_id } =
        JSON.parse(localStorage.getItem("track_new")) || {};
      if (company_id !== "") {
        commit('SET_IS_FETCHING', true);
        axios.get("/warehouses", {
            params: { company_id: company_id, page: 1, per_page: "all" },
          })
          .then((response) => {
            // Handle response
            // console.log(response.data, 'warehouse list..!');
            let warehouse_list = response.data.data;
            let pagination = response.data.pagination;

            commit("SET_WAREHOUSE", { warehouse_list, pagination });
            commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
            return warehouse_list;
          })
          .catch((error) => {
            // Handle error
            commit('SET_IS_FETCHING', false);
            console.error("Error:", error);
            return error;
          });
      }
    } catch (error) {
      commit('SET_IS_FETCHING', false);
      console.error("Error fetching warehouse list:", error);
    }
  },
};

const getters = {
  currentWarehouse(state) {
    return state.warehouse_list;
  },
  currentWarehousePagination(state) {
    return state.pagination;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
