// src/composables/useMeta.js
import { onMounted } from 'vue';

export function useMeta(title, metaDescription = '') {
  onMounted(() => {
    document.title = title;
    
    let meta = document.querySelector('meta[name="description"]');
    if (meta) {
      meta.setAttribute('content', metaDescription);
    } else {
      meta = document.createElement('meta');
      meta.setAttribute('name', 'description');
      meta.setAttribute('content', metaDescription);
      document.head.appendChild(meta);
    }
  });
}
