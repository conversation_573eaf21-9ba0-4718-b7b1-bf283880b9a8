// Store module for service categories
import axios from "axios";

const state = {
  service_list: [],  // Holds service data per category
  status_counts: [],
  pagination: null,
  currentCategory: null, // Holds the current selected category
};

const mutations = {
  SET_SERVICELIST(state, { data, pagination, status_counts, categoryId }) {
    // Check if service data for the given category already exists
    const existingCategoryIndex = state.service_list.findIndex(item => item.categoryId === categoryId);
    if (existingCategoryIndex !== -1) {
      // Append the new data if the category already exists
      state.service_list[existingCategoryIndex].data = [
        ...state.service_list[existingCategoryIndex].data,
        ...data
      ];
      state.service_list[existingCategoryIndex].pagination = pagination;
    } else {
      // Add new category data to the store
      state.service_list.push({ categoryId, data, pagination });
    }
    state.status_counts = status_counts;  // Save status counts
  },
  RESET_STATE(state) {
    state.service_list = [];
    state.status_counts = [];
  },
  SET_CURRENT_CATEGORY(state, categoryId) {
    state.currentCategory = categoryId;
  },
};

const actions = {
  async fetchServiceList({ commit, state }, { categoryId, page, per_page}) {
    try {
      const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};

      if (!company_id) return;

      // Set category ID for tracking
      commit('SET_CURRENT_CATEGORY', categoryId);

      const sendData = {
        company_id,
        page,
        per_page,
      };

      // If a specific category is provided, include it in the request
      if (categoryId !== 'all') {
        sendData.category_id = categoryId;
      }

      const response = await axios.get('/services', { params: sendData });

      const { data, pagination, status_counts } = response.data;
      commit('SET_SERVICELIST', { data, pagination, status_counts, categoryId });

      return data;
    } catch (error) {
      console.error('Error fetching service list:', error);
    }
  },

  resetServiceList({ commit }) {
    commit('RESET_STATE');
  }
};

const getters = {
  currentServiceList(state) {
    return {
      list: state.service_list,
      status_counts: state.status_counts,
    };
  },
  getCurrentCategory(state) {
    return state.currentCategory;
  },
};

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};
