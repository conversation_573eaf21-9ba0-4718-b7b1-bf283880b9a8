<template>
  <div v-if="showModal" class="spinner-container">
    <div class="spinner-bg"></div>
    <div class="spinner spinner--socker sm:top-0 right-0 inset-0 flex justify-center z-50 ">
      <div class="spinner-inner" :style="innerStyles">
        <div class="cube panelLoad">
          <div v-for="{ side, letter } of faces" :key="side" :class="`cube-face cube-face-${side}`">{{ letter }}</div>
        </div>
        <div class=" w-full mt-10">
          <p class="text-white font-bold mb-4 text-xl">Please Wait</p>
          <div class="loading-dots">
            <div class="dot" v-for="index in 6" :key="index"
              :style="{ animationDelay: index * 0.1 + 's', width: dotSize, height: dotSize }"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    showModal: {
      type: Boolean,
      default: false
    },
    //   size: {
    //     default: '40px'
    //   },
    //   letters: {
    //     type: Array,
    //     default: () => ['T', 'R', 'A', 'C', 'K', 'N', 'E', 'W'],
    //     validator: letters => letters.length === 8
    //   },
    //   color: {
    //     default: '#41b883'
    //   }
  }, data() {
    return {
      size: '80px',
      letters: ['T', 'R', 'A', 'C', 'K', 'N', 'E', 'W'],
      color: '#41b883',
      isMobile: false,
      dotSize: '20px',

    }
  },
  computed: {
    innerStyles() {
      let size = parseInt(this.size) / 2
      return {
        transform: 'scale(' + (size / 75) + ')',
        '--bg-color': this.color
      }
    },
    styles() {
      return {
        width: this.size,
        height: this.size
      }
    },
    faces() {
      const faces = ['front', 'back', 'left', 'right', 'bottom', 'top']

      return faces.map((face, index) => ({
        side: face,
        letter: this.letters[index]
      }))
    }
  },
  methods: {
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
  },
  mounted() {
    this.updateIsMobile();
    window.addEventListener('resize', this.updateIsMobile);
  }
}
</script>

<style scoped>
/* Define CSS for the spinner container */
.spinner-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  /* Ensure spinner is on top of other content */
}

/* Define CSS for the blurred background */
.spinner-bg {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  /* Adjust opacity to control blur effect */
  backdrop-filter: blur(5px);
  /* Adjust blur radius as needed */
}

/* Define CSS for the spinner loading effect */
.spinner {
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
}

.cube {
  width: 80px;
  height: 80px;
  line-height: 80px;
  text-align: center;
  transform-style: preserve-3d;
  transition: transform 0.5s 0.5s;
  perspective: 9999px;
  color: #333;
  opacity: 1;
  position: relative;

  &.panelLoad {
    z-index: 11;
    top: 50%;
    animation: letter-cube-panel 3.2s infinite forwards;

    .cube-face {
      font-size: 50px;
      color: var(--bg-color);
      box-shadow: inset 0 0 0 1px var(--bg-color), 0 0 1px 1px var(--bg-color);
    }
  }

  .cube-face {
    width: inherit;
    height: inherit;
    position: absolute;
    background: white;
    box-shadow: inset 0 0 0 1px #333, 0 0 1px 1px #333;
    opacity: 1;
  }

  .cube-face-front {
    transform: translate3d(0, 0, 40px);
    font-size: 57px;
  }

  .cube-face-back {
    transform: rotateY(180deg) translate3d(0, 0, 40px);
  }

  .cube-face-left {
    transform: rotateY(-90deg) translate3d(0, 0, 40px);
  }

  .cube-face-right {
    transform: rotateY(90deg) translate3d(0, 0, 40px);
  }

  .cube-face-top {
    transform: rotateX(90deg) translate3d(0, 0, 40px);
  }

  .cube-face-bottom {
    transform: rotateX(-90deg) translate3d(0, 0, 40px);
  }
}

.loading-dots {
  display: flex;
  justify-content: center;
  align-items: center;
}

.dot {
  width: 10px;
  height: 10px;
  background-color: white;
  border-radius: 50%;
  margin: 0 5px;
  animation: dot-animation 1s infinite alternate;
}

@keyframes dot-animation {
  0% {
    transform: scale(1);
    background-color: white;
  }

  100% {
    transform: scale(1.2);
    background-color: #41b883;
  }
}

@keyframes letter-cube-panel {
  0% {
    transform: rotateY(0deg) rotateZ(0deg);
  }

  20% {
    transform: rotateY(90deg) rotateZ(0deg);
  }

  40% {
    transform: rotateX(45deg) rotateZ(45deg);
  }

  60% {
    transform: rotateX(90deg) rotateY(180deg) rotateX(90deg);
  }

  80% {
    transform: rotateX(310deg) rotateZ(230deg)
  }

  100% {
    transform: rotateX(360deg) rotateZ(360deg)
  }
}
</style>