<template>
    <auth-layout>
      <div class="p-4 bg-white shadow rounded-md max-w-lg mx-auto">
        <h1 class="text-2xl font-bold mb-4">Add Item</h1>
        <form @submit.prevent="submitForm">
          <div class="mb-4">
            <label for="itemName" class="block text-sm font-medium text-gray-700">Item Name</label>
            <input
              id="itemName"
              v-model="itemName"
              type="text"
              class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div class="mb-4">
            <label for="itemDescription" class="block text-sm font-medium text-gray-700">Item Description</label>
            <textarea
              id="itemDescription"
              v-model="itemDescription"
              class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            ></textarea>
          </div>
          <div class="mb-4">
            <label for="itemPrice" class="block text-sm font-medium text-gray-700">Item Price</label>
            <input
              id="itemPrice"
              v-model="itemPrice"
              type="number"
              class="mt-1 block w-full p-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
              required
            />
          </div>
          <div class="flex justify-end">
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
              Submit
            </button>
          </div>
        </form>
      </div>
    </auth-layout>
  </template>
  
  <script>
  import AuthLayout from '../layouts/authLayout.vue';
  
  export default {
    components: {
      AuthLayout,
    },
    data() {
      return {
        itemName: '',
        itemDescription: '',
        itemPrice: '',
      };
    },
    methods: {
      submitForm() {
        console.log({
          itemName: this.itemName,
          itemDescription: this.itemDescription,
          itemPrice: this.itemPrice,
        });
        alert('Item added successfully!');
      },
    },
  };
  </script>
  