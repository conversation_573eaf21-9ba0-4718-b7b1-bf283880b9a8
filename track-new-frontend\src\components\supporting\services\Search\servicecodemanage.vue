<template>
    <!-- Modal -->
    <div v-if="isModalOpen"
        class="text-sm fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-center z-50 ">
        <div class="bg-white w-full sm:w-3/4 transform transition-transform ease-in-out duration-300 rounded overflow-auto sm:h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': isOpen }">
            <div
                class="flex justify-between items-center relative text-lg w-full px-4 py-3 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white text-center flex justify-end ml-3">
                    {{ type == 'print' ? 'Service QR-Code Print' : 'Service Code Scan' }}
                </p>
                <p class="close" @click.stop="closeModal">&times;</p>
            </div>
            <div class="container mx-auto p-4">
                <!-- Tabs Navigation -->
                <div class="flex space-x-4 mb-8 justify-center">
                    <button v-if="type == 'print'" @click="activeTab = 'create'" :class="tabClasses('create')">
                        Create Labels
                    </button>
                    <button v-if="type != 'print'" @click="activeTab = 'scan'" :class="tabClasses('scan')">
                        Scan Codes
                    </button>
                </div>
                <!--Type-->
                <div class="my-2 text-center">
                    <label class="block font-bold pb-1">Choose Code Type</label>
                    <div class="flex justify-center items-center space-x-4 py-1">
                        <!-- Enable Option -->
                        <div class="flex items-center">
                            <input type="radio" id="qr" name="select_code" value="qr" v-model="selectedCode"
                                class="mr-2 h-5 w-5 text-green-500" />
                            <label for="qr" class="text-sm text-gray-700">QR Code</label>
                        </div>
                        <!-- Disable Option -->
                        <div class="flex items-center">
                            <input type="radio" id="barcode" name="select_code" value="barcode" v-model="selectedCode"
                                class="mr-2 h-5 w-5 text-red-500" />
                            <label for="barcode" class="text-sm text-gray-700">Barcodes</label>
                        </div>
                    </div>
                </div>
                <!-- Choose Label Layout Section -->
                <div v-if="type == 'print'" class="my-4 text-center">
                    <label class="block font-bold pb-1">Choose Label Layout</label>
                    <div class="flex justify-center items-center space-x-4 py-1">
                        <!-- 2 Labels per Row -->
                        <button @click="setLabelLayout(2)"
                            class="bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600">
                            2 Labels per Row
                        </button>
                        <!-- 3 Labels per Row -->
                        <button @click="setLabelLayout(3)"
                            class="bg-green-500 text-white px-6 py-2 rounded hover:bg-green-600">
                            3 Labels per Row
                        </button>
                    </div>
                </div>

                <!-- Create Tab -->
                <div v-if="type == 'print'" class="space-y-8">
                    <!-- Label Creation -->
                    <div v-if="!selectedService" class="max-w-md mx-auto bg-white p-6 rounded-lg shadow-md">
                        <h2 class="text-xl font-semibold mb-4">Create New Service Code</h2>
                        <div class="space-y-4">
                            <button @click="createServiceCode"
                                class="w-full bg-blue-500 text-white p-2 rounded hover:bg-blue-600">
                                Generate QR Code
                            </button>
                        </div>
                    </div>

                    <!-- Label Preview & Print -->
                    <div class="max-w-4xl mx-auto">
                        <div class="flex justify-center items-center">
                            <LabelPrinter v-if="selectedService" :service="selectedService" :label-size="labelSize"
                                :selectedCode="selectedCode" :label_count="label_count" />
                        </div>
                        <!-- Label Size Selector -->
                        <!-- <div class="mt-4 flex gap-4 justify-center">
                            <button v-for="size in labelSizes" :key="size" @click="labelSize = size" :class="size === labelSize
                                ? 'bg-green-500 text-white'
                                : 'bg-gray-200'" class="px-4 py-2 rounded">
                                {{ size }}
                            </button>
                        </div> -->
                    </div>

                </div>

                <!-- Scan Tab -->
                <div v-if="activeTab === 'scan'" class="max-w-2xl mx-auto">
                    <QRScanner @code-scanned="handleScannedCode" :selectedCode="selectedCode" />

                    <!-- Scan Result -->
                    <div v-if="scannedResult" class="mt-8 bg-gray-100 p-4 rounded-lg">
                        <h3 class="text-lg font-semibold mb-2">Scanned Result:</h3>
                        <div class="bg-white p-4 rounded">
                            <pre class="whitespace-pre-wrap py-1">Service Code: {{ scannedResult }}</pre>
                            <searchCustomer :isMobile="isMobile" :typePage="'scanner'" @searchData="selectedCustomer"
                                @resetData="resetToSearch" :resetSearch="resetSearch" :scannedResult="scannedResult"
                                @refreshPage="refreshPage">
                            </searchCustomer>
                        </div>
                        <button @click="scannedResult = null" class="mt-4 bg-red-500 text-white px-4 py-2 rounded">
                            Clear Result
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import LabelPrinter from './LabelPrinter.vue';
import QRScanner from './QRScanner.vue';
import searchCustomer from '../../customers/searchCustomer.vue';

export default {
    props: {
        isModalOpen: Boolean,
        item_data: Object,
        type: String,
        isMobile: {
            type: [Boolean, undefined]
        }
    },
    components: {
        LabelPrinter,
        QRScanner,
        searchCustomer
    },
    data() {
        return {
            isOpen: false,
            activeTab: 'create',
            labelSizes: ['2x4', '4x6', '3x3'],
            labelSize: '4x6',
            newService: { name: '', code: '' },
            serviceCodes: [],
            selectedService: null,
            scannedResult: null,
            selectedCode: 'barcode',
            //---refresh---
            refreshButtonVisible: true,
            formValues: {},
            //---reset search--
            resetSearch: false,
            //--label count--
            label_count: 2,
        };
    },
    methods: {
        tabClasses(tabName) {
            return [
                'px-6 py-2 rounded',
                this.activeTab === tabName
                    ? 'bg-blue-500 text-white'
                    : 'bg-gray-200 hover:bg-gray-300'
            ];
        },
        createServiceCode() {
            const serviceCode = this.item_data && (this.item_data.service_id || this.item_data.service_code);
            if (serviceCode) {
                // Create the service code object dynamically based on item_data
                const service = {
                    code: serviceCode,
                    customer: this.item_data.customer && typeof this.item_data.customer === 'object'
                        ? `${this.item_data.customer.first_name || ''} ${this.item_data.customer.last_name || ''} - ${this.item_data.customer.contact_number || ''}`
                        : this.item_data.customer || ''
                };
                // this.serviceCodes.push(service);
                this.selectedService = service;
            } else {
                console.error('Service code or service ID is missing!');
            }
        },

        selectService(service) {
            this.selectedService = service;
        },

        formatDate(dateString) {
            return new Date(dateString).toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        },

        handleScannedCode(code) {
            this.scannedResult = code;
            const existingCode = this.serviceCodes.find((s) => s.code === code);
            if (existingCode) {
                this.activeTab = 'create';
                this.selectedService = existingCode;
            }
        },

        closeModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close');
                this.selectedService = null;
            }, 100);
        },
        //-----Search functions------
        async selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                this.formValues.customer_id = selectedData.id;
                await this.$router.push({ name: 'customers-view', params: { id: this.formValues.customer_id } });
                this.refreshPage();
                // setTimeout(() => {
                //     this.resetSearch = !this.resetSearch;
                // }, 100);
            }
        },
        resetToSearch() {
            this.formValues.customer_id = '';
        },
        refreshPage() {
            // window.location.reload();
            this.$emit('refreshRouterView');
            this.refreshButtonVisible = false;  // Hide the button      
            // Show the button again after 1 minute (60000 milliseconds)
            setTimeout(() => {
                this.refreshButtonVisible = true;
            }, 30000);
        },
        //--label count--
        setLabelLayout(count) {
            this.label_count = count;
        }
    },
    mounted() {
        if (this.type == 'print') {
            this.createServiceCode();
        } else {
            this.activeTab = 'scan';
        }
    },
    watch: {
        isModalOpen: {
            deep: true,
            handler(newValue) {
                setTimeout(() => {
                    this.isOpen = newValue;
                }, 100);
            }
        }
    }
};
</script>