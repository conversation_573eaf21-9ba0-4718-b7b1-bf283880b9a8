// store/modules/permissions.js
const state = {
    permissions: []
  };

  const getters = {
    getPermissions: (state) => state.permissions
  };

  const mutations = {
    setPermissions(state, permissions) {
      state.permissions = permissions;
    },
    RESET_STATE(state) {
      state.permissions = [];       
    },
  };

  const actions = {
    fetchPermissions({ commit }) {
      // Retrieve user data from localStorage
      const userData = JSON.parse(localStorage.getItem('track_new'));
      if (userData && userData.permissions) {
        const permissions = userData.permissions;
        commit('setPermissions', permissions);
      }
    }
  };

  export default {
    namespaced: true,
    state,
    getters,
    mutations,
    actions
  };
