<template>
  <div
    class="no-capitalize w-60 lg:w-full non-printable sideBar h-screen custom-scrollbar-hidden relative overflow-y-auto pb-[70px] md:pb-10"
    :class="{ 'pt-[60px]': page_name === 'dashboard' && !isMobile }">
    <div
      class="cursor-pointer hover:bg-gray-600 m-2 p-2 border border-gray-500 rounded-lg shadow-sm flex items-center space-x-3"
      @click.stop="this.$router.push('/')">
      <div class="flex-shrink-0">
        <img v-if="is_company"
          class="h-10 w-10 rounded-full ring-2 ring-gray-300 dark:ring-gray-500 object-cover bg-white"
          :src="currentCompanyList.logo" alt="logo" />
        <div v-else
          class="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center ring-2 ring-gray-300 dark:ring-gray-500">
          <span class="text-xs text-gray-500">Logo</span>
        </div>
      </div>
      <div class="relative text-xs">
        <p class="text-white line-clamp-1">{{ currentCompanyList.company_name || 'User' }}</p>
        <p class="text-gray-300 line-clamp-1 overflow-hidden text-ellipsis whitespace-nowrap">{{
          currentLocalDataList.email || '' }}</p>
      </div>
    </div>
    <!-- <div class="Line2 w-full left-0 -mt-3 relative shadow border border-zinc-100"></div> -->
    <!-- Repeat the following block for each sidebar item -->
    <div v-for="(item, index) in sidebarGroups" :key="item.id" class="cursor-pointer relative"
      :class="{ 'mt-3': index === 0, 'hidden': checkRestrictionios && item.id == 111 }">
      <!-- Adjust image size and add space 'hidden': !isMobile && item.id === 13, -->
      <div class="m-2 mb-2">
        <div class="flex justify-between items-center py-1 px-2" @mouseover="handleMouseOver(item.id, true)"
          @mouseout="handleMouseOut(true)" @click="selectedButtonGroup(item, 'list')"
          :class="{ 'bg-white text-black rounded ': item.isHovered, 'shadow-sm bg-white text-black rounded ': item.isClicked, ' text-white': !item.isHovered && !item.isClicked }"
          :id="`item-${item.id}`">
          <div class="flex items-center w-full">
            <font-awesome-icon :icon="item.icon" class="w-4 h-4" />
            <p class="flex justify-center items-center text-sm font-medium pl-2">{{ item.label }}
              <font-awesome-icon v-if="item.premium && getplanfeatures('group', item.label)" icon="fa-solid fa-crown"
                class="text-yellow-500  px-1 rounded-lg items-center" />

              <span v-if="item.id === 106 || item.id === 111"
                class="text-xs text-white bg-gradient-to-r from-blue-500 to-green-500 px-2 rounded-full shadow-md transform transition-transform duration-200 ease-in-out hover:scale-110 ml-1">
                New
              </span>
            </p>
          </div>
          <!-- <p v-if="item.id === 5 || item.id === 10 || item.id === 12" class="text-xs text-center text-yellow-600">coming soon...!</p> -->
          <!--drop down-->
          <div
            v-if="item.id === 103 || item.id === 104 || item.id === 107 || item.id === 108 || item.id === 109 || item.id === 112 || item.id === 110"
            class="flex items-center text-sm">
            <button>
              <font-awesome-icon icon="fa-solid fa-angle-up" v-if="item.isClicked" />
              <font-awesome-icon icon="fa-solid fa-angle-down" v-if="!item.isClicked" />
            </button>
          </div>
        </div>
        <!--Sub categories-->
        <!--&& item.id !== 9 && item.id !== 13 && item.id !== 8 && item.id !== 16 |||| && item.id !== 9 && item.id !== 13 && item.id !== 110  -->
        <transition-group name="slide-fade" tag="div">
          <div
            v-if="item.id !== 101 && item.id !== 102 && item.id !== 105 && item.id !== 106 && item.id !== 111 && item.isClicked"
            class="text-white text-sm flex flex-col py-1">
            <!-- Sub Items with animation -->

            <div v-for="subItem in item.items" :key="subItem.id"
              class="py-1 pl-2 text-sm text-white hover:bg-gray-700 cursor-pointer rounded"
              :class="{ 'bg-gray-700': route_item == subItem.id, 'hidden': [16, 27].includes(subItem.id) && isIOS }"
              @click="selectedButton(subItem, 'list view')">
              <div class=" flex items-center w-full">
                <!-- <img class="w-5 h-5" :src="subItem.imageSrc" :alt="subItem.alt" /> -->
                <span class="w-4 h-4"></span>
                <p class="text-sm font-medium pl-2">- {{ subItem.label }}
                  <font-awesome-icon v-if="subItem.premium && getplanfeatures('items', subItem.label)"
                    icon="fa-solid fa-crown" class="text-yellow-500  px-1 rounded-lg" />
                  <span v-if="subItem.id === 26"
                    class="text-xs text-white bg-gradient-to-r from-blue-500 to-green-500 px-2 rounded-full shadow-md transform transition-transform duration-200 ease-in-out hover:scale-110 ml-1">
                    New
                  </span>
                </p>
              </div>
            </div>
            <!---sub items end-->
          </div>
        </transition-group>
      </div>
    </div>
    <!--logout option is mobile view-->
    <div v-if="isMobile">
      <button @click="openLogout" class="w-full flex items-center px-4 py-2 text-sm text-red-500 hover:bg-red-100">
        <i class="fas fa-sign-out-alt mr-2"></i> Logout
      </button>
    </div>
    <!-- Android App Section -->
    <div class="w-full flex items-center px-2 py-2 text-sm text-white hover:bg-gray-700 cursor-pointer rounded text-sm"
      :class="{ 'hidden': checkRestrictionios }">
      <a href="https://play.google.com/store/apps/details?id=com.eagleminds.tracknew&hl=en" target="_blank"
        class="w-full flex items-center py-2 px-4 rounded-lg bg-gradient-to-r from-blue-500 to-green-500 text-white  transition duration-300 ease-in-out transform hover:bg-blue-700 hover:scale-105">

        <!-- FontAwesome Icon -->
        <font-awesome-icon icon="fa-solid fa-mobile-screen" class="mr-2 text-lg" />

        <!-- Button Text -->
        <span>Download Android App</span>

      </a>
    </div>

    <!--services-->
    <serviceCategory :showModal="open_service_category" @close-modal="closeCategory" @updateMode="updateStatus">
    </serviceCategory>
    <!--leads-->
    <addLead :show-modal="showLeadModal" @closeLeadModal="closeLeadModal" :companyId="companyId" :userId="userId">
    </addLead>
    <!--AMC-->
    <addAmc :show-modal="showAmcModal" @closeAmcModal="closeAmcModal" :companyId="companyId" :userId="userId">
    </addAmc>
    <!---add new item-->
    <addNewItem :showModal="open_add_item" @close-modal="closeAddNewItemModal"></addNewItem>
    <!--customer register-->
    <customerRegister :show-modal="showModal_customer" @close-modal="closeModal"></customerRegister>
    <!--expense new-->
    <expenseModel :showModal="open_expense_model" :companyId="companyId" :userId="userId"
      @close-modal="closeExpenseModel"></expenseModel>
    <openRMARegister :showModal="openModalRegister" @close-modal="closeTheModal" :companyId="companyId"
      :isMobile="isMobile" :userId="userId"></openRMARegister>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <logoutCofirmation :show-modal="showLogoutModal" @onConfirm="logout" @onCancel="cancelLogout">
    </logoutCofirmation>
  </div>
</template>

<script>
import serviceCategory from './dialog_box/serviceCategory.vue';
import addLead from './dialog_box/addLead.vue';
import addAmc from './dialog_box/addAmc.vue';
import addNewItem from './dialog_box/addNewItem.vue';
import customerRegister from './dialog_box/customerRegister.vue';
import expenseModel from './dialog_box/expenseModel.vue';
import openRMARegister from './dialog_box/openRMARegister.vue';
import { mapActions, mapGetters } from 'vuex';
import logoutCofirmation from './dialog_box/logoutCofirmation.vue';
import { isRestrictedDevice } from '@/utils/deviceUtils';
export default {
  components: {
    serviceCategory,
    addLead,
    addAmc,
    addNewItem,
    customerRegister,
    expenseModel,
    openRMARegister,
    logoutCofirmation
  },
  data() {
    return {
      sidebarItems: [],
      backup_items: [
        { id: 1, label: 'Dashboard', imageSrc: "/images/side_bar/Layout.png", alt: 'dashboard', isHovered: false, isClicked: false, route: '/', premium: false },
        { id: 3, label: 'Services', imageSrc: '/images/side_bar/Clock.png', alt: 'services', isHovered: false, isClicked: false, route: '/services', premium: true },
        { id: 7, label: 'Categories', imageSrc: '/images/side_bar/Add.png', alt: 'categories', isHovered: false, isClicked: false, route: '/categories', premium: true },
        { id: 2, label: 'Leads', imageSrc: "/images/side_bar/Leads.png", alt: 'leads', isHovered: false, isClicked: false, route: '/leads', premium: true },
        { id: 11, label: 'AMC', imageSrc: "/images/side_bar/amc.png", alt: 'amc', isHovered: false, isClicked: false, route: '/amc', premium: true },
        { id: 14, label: 'RMA', imageSrc: "/images/side_bar/RMA.png", alt: 'openrma', isHovered: false, isClicked: false, route: '/openrma', premium: true },
        { id: 5, label: 'Sales / Invoice', imageSrc: '/images/side_bar/Sales.png', alt: 'sales', isHovered: false, isClicked: false, route: '/sales', premium: false },
        { id: 23, label: 'Payment In', imageSrc: '/images/side_bar/pages.png', alt: 'payment in', isHovered: false, isClicked: false, route: '/sales/invoice/paymentin', premium: false },
        { id: 15, label: 'Proforma', imageSrc: "/images/side_bar/proforma.png", alt: 'proforma', isHovered: false, isClicked: false, route: '/proforma', premium: false },
        { id: 10, label: 'Quote / Estimate', imageSrc: "/images/side_bar/quote.png", alt: 'quote', isHovered: false, isClicked: false, route: '/estimation', premium: false },
        { id: 6, label: 'Items', imageSrc: '/images/side_bar/Inventory.png', alt: 'inventory', isHovered: false, isClicked: false, route: '/items', premium: false },
        { id: 4, label: 'Customers', imageSrc: '/images/side_bar/User.png', alt: 'customers', isHovered: false, isClicked: false, route: '/customers', premium: false },
        { id: 12, label: 'Expense', imageSrc: '/images/side_bar/expense.png', alt: 'expense', isHovered: false, isClicked: false, route: '/expense', premium: false },
        { id: 9, label: 'Setting', imageSrc: '/images/side_bar/Settings.png', alt: 'setting', isHovered: false, isClicked: false, route: '/setting', premium: false },
        { id: 28, label: 'List', imageSrc: '/images/side_bar/Settings.png', alt: 'list', isHovered: false, isClicked: false, route: '/users', premium: false },
        // { id: 29, label: 'Team', imageSrc: '/images/side_bar/Settings.png', alt: 'team', isHovered: false, isClicked: false, route: '/users', premium: false },
        // { id: 30, label: 'Services', imageSrc: '/images/side_bar/pages.png', alt: 'service', isHovered: false, isClicked: false, route: '/reports/services', premium: true },
        // { id: 31, label: 'Sales', imageSrc: '/images/side_bar/pages.png', alt: 'sales', isHovered: false, isClicked: false, route: '/reports/sales', premium: true },
        // { id: 32, label: 'RMA', imageSrc: '/images/side_bar/pages.png', alt: 'rma', isHovered: false, isClicked: false, route: '/reports/rma', premium: true },
        // { id: 33, label: 'GSTR', imageSrc: '/images/side_bar/pages.png', alt: 'gstr', isHovered: false, isClicked: false, route: '/reports/gstr', premium: true },
        // { id: 34, label: 'Expense', imageSrc: '/images/side_bar/pages.png', alt: 'expense', isHovered: false, isClicked: false, route: '/reports/expense', premium: true },
        { id: 8, label: 'Reports', imageSrc: '/images/side_bar/Statistics.png', alt: 'reports', isHovered: false, isClicked: false, route: '/reports', premium: false },
        { id: 16, label: 'Pricing Plan', imageSrc: '/images/side_bar/subscription.png', alt: 'subscription', isHovered: false, isClicked: false, route: '/subscription', premium: false },
        { id: 27, label: 'Subscription', imageSrc: '/images/side_bar/subscription.png', alt: 'subscription', isHovered: false, isClicked: false, route: '/subscription/history', premium: false },
        { id: 17, label: 'Website', imageSrc: '/images/side_bar/pages.png', alt: 'website', isHovered: false, isClicked: false, route: '/websites/dashboard', premium: true },
        { id: 18, label: 'Purchase', imageSrc: '/images/side_bar/pages.png', alt: 'purchase', isHovered: false, isClicked: false, route: '/items/purchaseOrder', premium: false },
        { id: 19, label: 'Supplier', imageSrc: '/images/side_bar/pages.png', alt: 'supplier', isHovered: false, isClicked: false, route: '/items/purchaseOrder/supplier', premium: false },
        { id: 20, label: 'Warehouse', imageSrc: '/images/side_bar/pages.png', alt: 'warehouse', isHovered: false, isClicked: false, route: '/items/purchaseOrder/warehouse', premium: false },
        { id: 21, label: 'Payment Out', imageSrc: '/images/side_bar/pages.png', alt: 'payment out', isHovered: false, isClicked: false, route: '/items/purchaseOrder/paymentout', premium: false },
        { id: 22, label: 'Stock Adjust', imageSrc: '/images/side_bar/pages.png', alt: 'stock adjust', isHovered: false, isClicked: false, route: '/items/purchaseOrder/supplier/stock', premium: false },
        { id: 24, label: 'Website Enquiry', imageSrc: '/images/side_bar/pages.png', alt: 'website enquiry', isHovered: false, isClicked: false, route: '/websiteenquiry', premium: true },
        { id: 25, label: 'Ecommerce', imageSrc: '/images/side_bar/pages.png', alt: 'ecommerce', isHovered: false, isClicked: false, route: '/ecommerce', premium: false },
        { id: 26, label: 'WhatsApp Alert', imageSrc: '/images/side_bar/pages.png', alt: 'whatsapp', isHovered: false, isClicked: false, route: '/whatsapp', premium: true },
        { id: 13, label: 'Pages', imageSrc: '/images/side_bar/pages.png', alt: 'pages', isHovered: false, isClicked: false, route: '/pages', premium: false },
      ],
      logo: '/images/head_bar/logo_01.png',
      list_view_enable: null,
      isMobile: false,
      isAdmin: false,
      open_service_category: false,
      selected_item: null,
      companyId: null,
      userId: null,
      showLeadModal: false,
      showAmcModal: false,
      open_add_item: false,
      showModal_customer: false,
      open_expense_model: false,
      is_company: false,
      openModalRegister: false,
      //--toaster---
      show: false,
      type_toaster: 'success',
      message: '',
      //---service navigation----
      serviceIsGo: false,
      //----sidebar group---
      groupedSidebarItems: [],
      sidebarGroups: [],
      backup_sidebarGroups: [{ id: 101, label: 'Dashboard', icon: 'fa-solid fa-house', alt: 'dashboard', isHovered: false, isClicked: false, route: '/', items: [], premium: false },
      { id: 102, label: 'Customers', icon: 'fa-solid fa-users', alt: 'customers', isHovered: false, isClicked: false, route: '/customers', items: [], premium: false },
      { id: 104, label: 'Services', icon: 'fa-solid fa-tools', alt: 'services', isHovered: false, isClicked: false, route: '/services', items: [], premium: true },
      { id: 103, label: 'Sales', icon: 'fa-solid fa-file-invoice', alt: 'sales', isHovered: false, isClicked: false, route: '/sales', items: [], premium: false },
      { id: 108, label: 'Leads', icon: 'fa-solid fa-user-tie', alt: 'leads', isHovered: false, isClicked: false, route: '/leads', items: [], premium: true },
      { id: 105, label: 'Expenses', icon: 'fa-solid fa-money-bill-wave', alt: 'expense', isHovered: false, isClicked: false, route: '/expense', items: [], premium: false },
      { id: 109, label: 'Inventory', icon: 'fa-solid fa-boxes', alt: 'inventory', isHovered: false, isClicked: false, route: '/items', items: [], premium: false },
      { id: 110, label: 'Reports', icon: 'fa-solid fa-chart-line', alt: 'reports', isHovered: false, isClicked: false, route: '/reports', items: [], premium: false },
      { id: 112, label: 'Employees', icon: 'fa-solid fa-user-tie', alt: 'employees', isHovered: false, isClicked: false, route: '/users', items: [], premium: false },
      { id: 107, label: 'Settings', icon: 'fa-solid fa-cog', alt: 'setting', isHovered: false, isClicked: false, route: '/setting', items: [], premium: false },
      { id: 106, label: 'Website', icon: 'fa-solid fa-globe', alt: 'website', isHovered: false, isClicked: false, route: '/websites/dashboard', items: [], premium: true },
      { id: 111, label: 'Ecommerce', icon: 'fa-solid fa-store', alt: 'ecommerce', isHovered: false, isClicked: false, route: 'https://myonlinewebstore.in/', items: [], premium: false },
      ],
      //--logout options---
      showLogoutModal: false,
      checkRestrictionios: isRestrictedDevice(),
    };
  },
  name: 'sidebar',
  props: {
    route_item: Number,
    page_name: String,
    update_list: Boolean,
    updateModalOpen: Boolean
  },
  computed: {
    ...mapGetters('companies', ['currentCompanyList']),
    ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
    ...mapGetters('features_list', ['currentFeatureList']),
    ...mapGetters('device', ['isIOS']),
  },
  methods: {
    ...mapActions('apiUpdates', ['fetchApiUpdates']),
    ...mapActions('companies', ['fetchCompanyList']),
    ...mapActions('localStorageData', ['fetchLocalDataList', 'validateRoles']),
    ...mapActions('features_list', ['fetchFeatureList']),
    ...mapActions('device', ['detectIOS']),
    //--services--
    closeCategory(status) {
      this.open_service_category = false;
      if (status) {
        this.$emit('reloadDiv', true);
      }
    },
    //--leads--
    closeLeadModal(data) {
      this.showLeadModal = false;
      if (window.location.pathname === '/leads' && data) {
        // Refresh the page
        window.location.reload();
      }
    },
    //---amc
    closeAmcModal(data) {
      this.showAmcModal = false;
      if (window.location.pathname === '/amc' && data) {
        // Refresh the page
        window.location.reload();
      }
    },
    //---items---
    closeAddNewItemModal(data) {
      this.open_add_item = false;
      // console.log(data, 'RRRR');
      if (window.location.pathname === '/items' && data) {
        // Refresh the page
        window.location.reload();
      }
    },
    //---customer create new--
    closeModal(data) {
      this.showModal_customer = false;
      if (window.location.pathname === '/customers' && data) {
        // Refresh the page
        window.location.reload();
      }
    },
    //---expense modal---
    closeExpenseModel(data) {
      this.open_expense_model = false;
      if (window.location.pathname === '/expense' && data) {
        // Refresh the page
        window.location.reload();
      }
    },
    handleMouseOver(itemId, group) {
      if (group) {
        this.sidebarGroups.forEach(groupItem => {
          groupItem.isHovered = groupItem.id === itemId;
        });
      } else {
        this.sidebarGroups.forEach(groupItem => {
          groupItem.items.forEach(item => {
            item.isHovered = item.id === itemId;
          });
        });
      }
    },
    handleMouseOut(group) {
      if (group) {
        this.sidebarGroups.forEach(groupItem => {
          groupItem.isHovered = false;
        });
      } else {
        this.sidebarGroups.forEach(groupItem => {
          groupItem.items.forEach(item => {
            item.isHovered = false;
          });
        });
      }
    },
    handleItemClick(itemId) {
      this.sidebarGroups.forEach(groupItem => {
        // Reset isClicked for all group items and their items
        groupItem.isClicked = false;
        groupItem.items.forEach(item => item.isClicked = false);

        if (groupItem.items && groupItem.items.length > 0) {
          groupItem.items.forEach(item => {
            if (item.id === itemId) {
              // Set isClicked based on conditions
              if (itemId === 1 && groupItem.id === 101) { //----Dashboard
                groupItem.isClicked = true;
              } else if (itemId === 4 && groupItem.id === 102) { //---customers
                groupItem.isClicked = true;
              } else if ([5, 23, 15, 10].includes(itemId) && groupItem.id === 103) { //---sales
                groupItem.isClicked = true;
              } else if ([3, 7, 11, 14].includes(itemId) && groupItem.id === 104) { //--services
                groupItem.isClicked = true;
              } else if (itemId === 12 && groupItem.id === 105) { //--Expenses

                groupItem.isClicked = true;
              } else if (itemId === 17 && groupItem.id === 106) { //--website
                groupItem.isClicked = true;
              } else if ([9, 16, 13, 26, 27, 28].includes(itemId) && groupItem.id === 107) { //--settings
                if ([16, 27].includes(itemId) && isRestrictedDevice()) {
                } else {
                  groupItem.isClicked = true;
                }
                groupItem.isClicked = true;
              } else if ([2, 24].includes(itemId) && groupItem.id === 108) { //---Leads
                groupItem.isClicked = true;
              } else if ([6, 18, 19, 20, 21, 22].includes(itemId) && groupItem.id === 109) { //----Inventory
                groupItem.isClicked = true;
              } else if ([30, 31, 32, 33, 34, 8].includes(itemId) && groupItem.id === 110) { //---Reports
                groupItem.isClicked = true;
              } else if ([28, 29].includes(itemId) && groupItem.id === 112) { //---Reports
                groupItem.isClicked = true;
              } else {
                groupItem.isClicked = false;
              }

              // Toggle the clicked state for the selected item
              item.isClicked = !item.isClicked;

              if (item.isClicked) {
                this.scrollToCenter(itemId);
              } else {
                this.$router.push(item.route);
              }
            }
          });
        }
      });
    },
    // Method to scroll window to center the clicked item
    scrollToCenter(itemId) {
      // Find the element by its ID
      const element = document.getElementById(`item-${itemId}`);
      // console.log(element, 'WWWWWWWWWWW');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    },

    selectSidebarOption() {
      // Logic to handle selected sidebar option
      // Emit an event to notify the parent component
      this.$emit('selectSidebarOption');
    },
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
    displayedSidebarItems() {
      if (this.isAdmin) {
        if (this.checkRoles(['admin'])) {
          return this.sidebarItems;
        } else {
          this.sidebarItems = this.sidebarItems.filter(item => ![9, 16, 26, 27, 28].includes(item.id));
        }
      } else {
        if (this.checkRoles(['Service Engineer'])) {
          this.sidebarItems = this.sidebarItems.filter(item => ![4, 5, 6, 7, 8, 9, 10, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 26, 27, 28, 29, 30, 31, 32, 33, 34].includes(item.id));
          this.sidebarGroups = this.sidebarGroups.filter(group => ![102, 103, 106, 109, 110, 111, 112].includes(group.id));
        } else {
          // If user is not admin, filter out specific items (customize as needed)      
          this.sidebarItems = this.sidebarItems.filter(item => ![4, 6, 7, 9, 8, 16, 17, 18, 19, 20, 21, 22, 26, 27, 28, 29, 30, 31, 32, 33, 34].includes(item.id)); // Filter out AMC, Expense, Setting
          // console.log(this.sidebarItems, 'EEEEE');
          this.sidebarGroups = this.sidebarGroups.filter(group => ![102, 106, 109, 110, 111, 112].includes(group.id));
        }
      }
    },
    checkUserRole() {
      const currentUser = JSON.parse(localStorage.getItem('track_new'));
      const userRoles = currentUser ? currentUser.roles || [] : [];
      const requiredRoles = ['admin', 'Sub_Admin']
      const canAccessRoute = requiredRoles.some(role => userRoles.includes(role));
      if (canAccessRoute) {
        // User does not have all required permissions for the route
        this.isAdmin = true;
      }
    },
    //---list--
    buttonClasses(id) {
      if (this.route_item === id) {
        return {
          'px-1 bg-white rounded text-black': ['services', 'leads', 'amcs', 'sales', 'estimation', 'proforma', 'items', 'customers', 'expense', 'rma'].includes(this.selectedOptions())
        };
      }
    },
    selectedButton(typeData, navigateto) {
      // console.log(navigateto, 'What happening in the data....!');
      if (typeData) {
        //--refresh the data---
        const currentPath = this.$route.path;
        const targetPath = typeData.route;

        if (currentPath === targetPath) {
          // Emit event if the user selects an already active route
          this.$emit('refreshRouterView');
        }

        if (typeData.label && navigateto === 'list') {
          this.selected_item = typeData.id;
          typeData.isClicked = !typeData.isClicked;
          if (typeData.isClicked && this.checkRolesAndCategory(typeData.id) && (typeData.id === 1 || typeData.id === 9 || typeData.id === 13 || typeData.id === 8 || typeData.id === 16)) {
            // if (typeData.isClicked && this.checkRolesAndCategory(typeData.id)) {
            this.$router.push(typeData.route);
            this.scrollToCenter(typeData.id);
          }
        }
        else if (typeData.label && navigateto === 'list view') {
          //----this.checkRolesAndCategory(typeData.id)--
          if (typeData.id) {
            this.list_view_enable = typeData.id;
            this.selected_item = typeData.id;
            this.$router.push(typeData.route);
            this.scrollToCenter(typeData.id);
            if (this.isMobile) {
              this.$emit('selectSidebarOption');
            }
          }
        }
        else if (typeData.label && navigateto === 'new') {
          switch (typeData.label) {
            case 'Services': {
              this.open_service_category = true;
              break;
            }
            case 'Leads': {
              this.showLeadModal = true;
              break;
            }
            case 'AMC': {
              this.showAmcModal = true;
              break;
            }
            case 'Sales / Invoice': {
              this.handleSalesTypeSelection('product');
              break;
            }
            case 'Proforma': {
              this.createNewProforma();
              break;
            }
            case 'Quote / Estimate': {
              this.createNewEstimateimation();
              break;
            }
            case 'Inventory': {
              this.open_add_item = true;
              break;
            }
            case 'Customers': {
              this.showModal_customer = true;
              break;
            }
            case 'Expense': {
              this.open_expense_model = true;
              break;
            }
            case 'RMA': {
              this.openModalRegister = true;
              break;
            }
            default: {
              break;
            }
          }
        } else if (typeData.label && navigateto !== 'sales&due' && navigateto !== 'payment') {
          if (typeData.id !== 3 && typeData.id !== 4) {
            this.$router.push(navigateto);
          } else if (typeData.id === 3 || typeData.id === 4) {
            this.$router.push({
              path: navigateto,
              query: { page: typeData.id === 4 ? 'customer' : 'services' }
            });
          }
        }
        else if (typeData.label && navigateto === 'sales&due') {
          this.$router.push({
            path: '/sales',
            query: { type: 'due_list' }
          });

        } else if (typeData.label && navigateto === 'payment') {
          this.$router.push({
            path: '/sales/invoice/paymentin',
          });
        }
      }
    },
    //---group selections---
    selectedButtonGroup(typeData, navigateto) {
      if (typeData) {
        //--refresh the data---
        const currentPath = this.$route.path;
        const targetPath = typeData.route;
        if (currentPath === targetPath) {
          // Emit event if the user selects an already active route
          this.$emit('refreshRouterView');
        }

        if (typeData.label && navigateto === 'list') {
          this.selected_item = typeData.id;
          typeData.isClicked = true;
          //---!typeData.isClicked---
          //---this.checkRolesAndCategory(typeData.id, true)  typeData.id === 110 || 
          if (typeData.isClicked && typeData.id && (typeData.id === 101 || typeData.id === 102 || typeData.id === 105 || typeData.id === 106 || typeData.id === 111)) {
            // Check if it's the Ecommerce item, then open the link in a new tab
            if (typeData.id === 111) {
              window.open(typeData.route, '_blank');  // Open external URL in a new tab
              return;
            }
            this.$router.push(typeData.route);
            this.scrollToCenter(typeData.id);
            if (this.isMobile) {
              this.$emit('selectSidebarOption');
            }
          } else if (!([101, 102, 105, 106, 111].includes(typeData.id))) {
            // Reset isClicked for other items in sidebarGroups
            this.sidebarGroups.forEach(opt => {
              if (opt.id !== typeData.id) {
                opt.isClicked = false;
              }
            });
          }
        } else if (typeData.label && navigateto === 'list view') {
          // ----(this.checkRolesAndCategory(typeData.id, true))---
          if (typeData.id) {
            this.list_view_enable = typeData.id;
            this.selected_item = typeData.id;
            this.$router.push(typeData.route);
            this.scrollToCenter(typeData.id);
          }
        }
      }
    },
    //--sales create new---
    handleSalesTypeSelection(selectedType) {
      // console.log(selectedType, 'what type of the data....!');
      if (selectedType === 'product' && window.location.pathname !== '/sales/invoice') {
        this.$router.push({
          name: 'sales-invoice',
          query: { type: 'add' }
        });
      }
    },
    //---create new proforma---
    createNewProforma() {
      if (window.location.pathname !== '/proforma/product') {
        this.$router.push({
          name: 'addProformaInvoice',
          params: { type: 'product' },
          query: { type: 'add' }
        });
      }
    },
    //---estimation create new---
    createNewEstimateimation() {
      if (window.location.pathname !== '/estimation/product') {
        this.$router.push({
          name: 'addEstimation',
          params: { type: 'product' },
          query: { type: 'add' }
        });
      }
    },
    //---is Mbile true only---
    handleOutsideClick(event) {
      // Check if the click target is outside the sidebar
      if (!this.$el.contains(event.target) &&
        this.showLeadModal === false &&
        this.showAmcModal === false &&
        this.open_add_item === false &&
        this.showModal_customer === false &&
        this.open_expense_model === false &&
        this.openModalRegister === false) {
        // Emit an event to notify the parent component
        this.$emit('selectSidebarOption');
      }
    },
    //----selected options---
    selectedOptions() {
      let path_data = window.location.pathname;
      switch (path_data) {
        case '/services': {
          if (!this.open_service_category) {
            return 'services';
          }
          break;
        }
        case '/leads': {
          if (!this.showLeadModal) {
            return 'leads';
          }
          break;
        }
        case '/amc': {
          if (!this.showAmcModal) {
            return 'amcs'
          }
          break;
        }
        case '/sales': {
          if (this.$route.query.type === 'due_list') {
            return 'sales&due';
          } else {
            return 'sales';
          }

        }
        case '/sales/invoice': {
          return 'sales new';
        }
        case '/estimation': {
          return 'estimation';
        }
        case '/estimation/product': {
          return 'estimation new';
        }
        case '/proforma': {
          return 'proforma';
        }
        case '/proforma/product': {
          return 'proforma new';
        }
        case '/items': {
          if (!this.open_add_item) {
            return 'items';
          }
          break;
        }
        case '/customers': {
          if (!this.showModal_customer) {
            return 'customers';
          }
          break;
        }
        case '/expense': {
          if (!this.open_expense_model) {
            return 'expense';
          }
          break;
        }
        case '/categories': {
          return 'categories';
        }
        case '/openrma': {
          return 'rma';
        }
        case '/items/purchaseOrder': {
          return 'purchase';
        }
        case '/items/purchaseOrder/supplier': {
          return 'supplier';
        }
        case '/items/purchaseOrder/warehouse': {
          return 'warehouse';
        }
        case '/items/purchaseOrder/paymentout': {
          return 'paymentout';
        }
        case '/items/purchaseOrder/paymentout/createnew': {
          return 'paymentout';
        }
        case '/items/purchaseOrder/supplier/stock': {
          return 'stock';
        }
        case '/sales/invoice/paymentin': {
          return 'payment';
        }
        case '/sales/invoice/paymentin/createnew': {
          return 'payment';
        }
        default: {
          break;
        }
      }
    },
    checkPathnameServices() {
      const pathname = window.location.pathname;
      return pathname.startsWith('/services') && pathname.endsWith('/add');

    },
    //--role based on--
    checkRoles(roles) {
      if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
        return roles.includes(this.currentLocalDataList.roles[0]);
      }
      return false;
    },
    checkRolesAndCategory(id, group) {
      if (group === undefined) {
        let isRoles = this.checkRoles(['Sub_Admin', 'admin', 'Account Manager', 'Service Manager', 'Sales man']);
        if (!isRoles) {
          return false;
        }
        return true;
      } else {
        let isRoles = this.checkRoles(['Sub_Admin', 'admin', 'Account Manager', 'Service Manager', 'Sales man']);
        if (!isRoles) {
          return false;
        }
        return true;

      }
    },
    //---close the rma----
    closeTheModal(data) {
      if (data && data.id) {
        // console.log(data, 'waht happening');
        this.openModalRegister = false;
        this.message = 'RMA created successfully...!';
        this.show = true;
      } else {
        this.openModalRegister = false;
      }
    },
    //----validate new---
    validateIsitNew(id) {
      if (this.selectedOptions() === 'sales new') {
        return id == 5;
      } else if (this.selectedOptions() === 'estimation new') {
        return id == 10;
      } else if (this.selectedOptions() === 'proforma new') {
        return id == 15;
      } else if (this.open_service_category) {
        return false;
      }
      else if (this.showLeadModal) {
        return id == 2;
      }
      else if (this.showAmcModal) {
        return id == 11;
      }
      else if (this.open_add_item) {
        return id == 6;
      }
      else if (this.showModal_customer) {
        return id == 4;
      }
      else if (this.open_expense_model) {
        return id == 12;
      }
      else if (this.openModalRegister) {
        return id == 14;
      }
      else if (this.checkPathnameServices()) {
        return id == 3;
      }
    },
    //--filter the sidebar---
    async filteredSidebarItems() {
      // Create a map of response data
      const accessMap = this.currentFeatureList.reduce((acc, item) => {
        acc[item.name] = item.hasAccess;
        return acc;
      }, {});

      // Define label mappings for special cases
      const labelMap = {
        'Sales / Invoice': 'Sales',
        'Quote / Estimate': 'Estimate'
      };
      this.sidebarGroups = this.backup_sidebarGroups.filter(opt => this.featureisEnable(opt.label));
      // Filter sidebar items based on accessMap and labelMap
      this.sidebarItems = this.backup_items.filter(item => {
        const itemName = labelMap[item.label] || item.label;
        return accessMap[itemName] !== false;
      });
      await this.displayedSidebarItems();
      await this.groupSidebarItems();
    },
    featureisEnable(type) {
      if (type && ['Services', 'Leads', 'Sales', 'Expenses'].includes(type)) {
        if (this.currentFeatureList && this.currentFeatureList.length > 0) {
          let type_data = type;
          if (['Expenses'].includes(type)) {
            type_data = 'Expense';
          }
          let find_feature = this.currentFeatureList.find(opt => opt.name == type_data);
          if (find_feature && find_feature.hasAccess) {
            return true;
          } else if (find_feature && !find_feature.hasAccess) {
            // Additional conditions for Services and Sales
            if (type_data === 'Services') {
              let rmaFeature = this.currentFeatureList.find(opt => opt.name === 'RMA');
              let amcFeature = this.currentFeatureList.find(opt => opt.name === 'AMC');
              if ((rmaFeature && !rmaFeature.hasAccess) && (amcFeature && !amcFeature.hasAccess)) {
                return false;
              } else {
                return true;
              }
            } else if (type_data === 'Sales') {
              let estimateFeature = this.currentFeatureList.find(opt => opt.name === 'Estimate');
              let proformaFeature = this.currentFeatureList.find(opt => opt.name === 'Proforma');
              if ((estimateFeature && !estimateFeature.hasAccess) && (proformaFeature && !proformaFeature.hasAccess)) {
                return false;
              } else {
                return true;
              }
            } else {
              return false;
            }
          }
        } else {
          return true;
        }
      } else {
        return true;
      }
    },

    //---close open modal box--
    closeAllModals() {
      // Close all modals
      if (!this.serviceIsGo) {
        this.open_service_category = false;
      }
      this.showLeadModal = false;
      this.showAmcModal = false;
      this.open_add_item = false;
      this.showModal_customer = false;
      this.open_expense_model = false;
      this.openModalRegister = false;
    },
    updateStatus() {
      this.serviceIsGo = true;
      this.$emit('updateIsOpen', false);
    },
    //----update filter sidebar items----
    groupSidebarItems() {
      // Clear any existing items in the groups
      this.sidebarGroups.forEach(group => {
        group.items = [];
      });

      // Populate groups based on `sidebarItems`
      this.sidebarItems.forEach(item => {
        if (item.id === 1) {
          this.sidebarGroups.find(group => group.id === 101).items.push(item); // Dashboard
        } else if (item.id === 4) {
          this.sidebarGroups.find(group => group.id === 102).items.push(item); // Customers
        } else if ([5, 23, 15, 10].includes(item.id) && this.sidebarGroups.find(group => group.id === 103)) {
          this.sidebarGroups.find(group => group.id === 103).items.push(item); // Sales
        } else if ([3, 7, 11, 14].includes(item.id) && this.sidebarGroups.find(group => group.id === 104)) {
          this.sidebarGroups.find(group => group.id === 104).items.push(item); // Services
        } else if (item.id === 12 && this.sidebarGroups.find(group => group.id === 105)) {
          this.sidebarGroups.find(group => group.id === 105).items.push(item); // Expenses
        } else if (item.id === 17) {
          this.sidebarGroups.find(group => group.id === 106 && group).items.push(item); // Website
        } else if ([9, 16, 13, 26, 27].includes(item.id)) {
          // Restrict certain IDs on Apple devices
          const restrictedOnApple = [16, 27]; // Add any other IDs you want to restrict
          // Restrict certain IDs on Apple devices
          if (isRestrictedDevice()) {
            // If restricted device, filter out items with id 16 and 27
            if (!restrictedOnApple.includes(item.id)) {
              this.sidebarGroups.find(group => group.id === 107).items.push(item); // Settings
            }
          } else {
            // If not a restricted device, push all items
            this.sidebarGroups.find(group => group.id === 107).items.push(item); // Settings
          }

        } else if ([2, 24].includes(item.id) && this.sidebarGroups.find(group => group.id === 108)) {
          this.sidebarGroups.find(group => group.id === 108).items.push(item); // Leads
        } else if ([6, 18, 19, 20, 21, 22].includes(item.id)) {
          this.sidebarGroups.find(group => group.id === 109).items.push(item); // Inventory
        } else if ([28, 29].includes(item.id)) {
          this.sidebarGroups.find(group => group.id === 112).items.push(item); // Inventory
        } else if ([30, 31, 32, 33, 34, 8].includes(item.id)) {
          this.sidebarGroups.find(group => group.id === 110).items.push(item); // Reports
        }
      });

      this.handleItemClick(this.route_item);
    },
    //---logut options---
    logout() {
      // Clear the 'track_new' key from local storage
      this.showLogoutModal = false;
      localStorage.removeItem('track_new');
      localStorage.removeItem('features');
      this.$store.dispatch('resetAll');
      this.$router.push('/login');
    },
    openLogout() {
      this.showLogoutModal = true;
    },
    cancelLogout() {
      this.showLogoutModal = false;
    },
    checkPlanDetails() {
      let currentPlan = this.currentCompanyList && this.currentCompanyList.plans ? this.currentCompanyList.plans : null;
      if (currentPlan && [12, 13].includes(currentPlan.id)) {
        return true;
      } else { return false; }
    },
    //---plan based on strict--
    getplanfeatures(type, label, key) {
      if (type == 'group') {
        if (label == 'Services') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['service']) {
            return true;
          } else {
            return false;
          }
        }
        else if (label == 'Leads') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['leads']) {
            return true;
          } else {
            return false;
          }
        }
        else if (label == 'Website') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['website']) {
            return true;
          } else {
            return false;
          }

        }
      } else if (type == 'items') {
        if (label == 'Services') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['service']) {
            return true;
          } else {
            return false;
          }
        }
        else if (label == 'Categories') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['service_category']) {
            return true;
          } else {
            return false;
          }
        }
        else if (label == 'Leads') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['leads']) {
            return true;
          } else {
            return false;
          }
        }
        else if (label == 'Website Enquiry') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['website_enquiry']) {
            return true;
          } else {
            return false;
          }
        }
        else if (label == 'AMC') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['amc']) {
            return true;
          } else {
            return false;
          }
        }
        else if (label == 'RMA') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['rma']) {
            return true;
          } else {
            return false;
          }
        }
        else if (label == 'Website') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['website']) {
            return true;
          } else {
            return false;
          }
        } else if (label == 'WhatsApp Alert') {
          if (this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures['whatsapp_msg']) {
            return true;
          } else {
            return false;
          }
        }
      } else {
        if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
          return true;
        } else {
          return false;
        }
      }
    },
  },
  mounted() {
    this.fetchApiUpdates();
    this.updateIsMobile();
    this.checkUserRole();
    if (this.currentFeatureList && this.currentFeatureList.length > 0) {
      this.filteredSidebarItems();
      this.fetchFeatureList();
    } else {
      this.fetchFeatureList();
    }
    // this.sidebarItems = [...this.backup_items];
    const collectForm = localStorage.getItem('track_new');
    if (collectForm) {
      let dataParse = JSON.parse(collectForm);
      this.companyId = dataParse.company_id;
      this.userId = dataParse.user_id + '';
    }
    if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0) {
      // console.log(this.currentCompanyList && this.currentCompanyList.logo !== '' && this.currentCompanyList.logo);
      if (this.currentCompanyList && this.currentCompanyList.logo !== '' && this.currentCompanyList.logo) {
        this.is_company = true
      }
    } else {
      this.fetchCompanyList();
    }
    if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
      this.fetchLocalDataList();
    } else {
      this.fetchLocalDataList();
    }
    // console.log(this.route_item, 'RRRRRRRRRRRRRRRRRR');    
    // this.displayedSidebarItems();
    this.handleItemClick(this.route_item);

    //---sidebar icons--
    if (this.isMobile) {
      document.body.addEventListener('click', this.handleOutsideClick);
    }
    window.addEventListener('resize', this.updateIsMobile);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateIsMobile);
    //---sidebar icons--
    if (this.isMobile) {
      document.body.removeEventListener('click', this.handleOutsideClick);
    }
  },
  watch: {
    currentCompanyList: {
      deep: true,
      handler(newValue) {
        // console.log(newValue && Object.keys(newValue).length > 0);
        if (newValue && Object.keys(newValue).length > 0) {
          // console.log(newValue && newValue.logo !== '' && newValue.logo);
          if (newValue && newValue.logo !== '' && newValue.logo) {
            this.is_company = true;
          }
        }
      }
    },
    currentFeatureList: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.filteredSidebarItems();
        }
      }
    },
    update_list: {
      deep: true,
      handler(newValue) {
        this.fetchFeatureList();
      }
    },
    //---current data update---
    //-----navigate controll---
    updateModalOpen: {
      deep: true,
      handler(newValue) {
        this.closeAllModals();
      }
    },
    //---modalbox data----
    open_service_category: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
    showLeadModal: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
    showAmcModal: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
    open_add_item: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
    open_expense_model: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
    openModalRegister: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
    showModal_customer: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
    route_item: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.handleItemClick(newValue);
        }
      }
    },
  }
};
</script>

<style scoped>
.border-radius-left {
  border-top-left-radius: 999px;
  border-bottom-left-radius: 999px;
}

@media print {

  /* Additional styles for printing */
  body {
    background-color: white;
  }

  .non-printable {
    display: none;
  }
}

.sideBar {
  overflow-y: auto;
  /* Enable vertical scrolling */
  scroll-behavior: smooth;
  /* Smooth scrolling behavior */
}

.custom-scrollbar-hidden::-webkit-scrollbar {
  display: none;
  -webkit-text-size-adjust: none;
  text-size-adjust: none;
}

/*---Animation----*/
/* Define the sliding and fading transition .slide-fade-leave-active 
.slide-fade-leave-to  */
.slide-fade-enter-active {
  transition: transform 0.5s ease, opacity 0.5s ease;
}

.slide-fade-enter {
  transform: translateY(-20px);
  /* Slide from top */
  opacity: 0;
}
</style>
