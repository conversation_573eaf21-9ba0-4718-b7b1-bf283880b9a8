<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateSalesPaymentAPIRequest;
use App\Http\Requests\API\UpdateSalesPaymentAPIRequest;
use App\Models\SalesPayment;
use App\Models\Sales;
use App\Models\Customer;
use App\Http\Resources\api\SalePaymentResource;
use App\Repositories\SalesPaymentRepository;
use App\Repositories\SalesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon; // Import Carbon for date handling
use Response;

/**
 * Class SalesPaymentController
 * @package App\Http\Controllers\API
 */

class SalesPaymentAPIController extends AppBaseController
{
    /** @var  SalesPaymentRepository */
    private $salesPaymentRepository;
  	private $salesRepository;

    public function __construct(SalesPaymentRepository $salesPaymentRepo, SalesRepository $salesRepo)
    {
        $this->salesPaymentRepository = $salesPaymentRepo;
      	$this->salesRepository = $salesRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/sales_payments",
     *      summary="getSalesPaymentList",
     *      tags={"SalesPayment"},
     *      description="Get all SalesPayments",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/SalesPayment")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
          $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        // Group by payment_code and sum payment_amount
        $subQuery = SalesPayment::select('payment_code', DB::raw('SUM(payment_amount) as total_payment_amount'))
            ->where('company_id', $companyId)
          	->where('salespayment.status', 0)
            ->groupBy('payment_code');

        // Join the subquery to select all columns
        $salesQuery = SalesPayment::joinSub($subQuery, 'grouped_sales', function ($join) {
                $join->on('salespayment.payment_code', '=', 'grouped_sales.payment_code');
            })
            ->select('salespayment.*', 'grouped_sales.total_payment_amount')
            ->where('salespayment.company_id', $companyId)
          	->where('salespayment.payment_for', 'payment_in')
            ->where('salespayment.status', 0)
            ->groupBy('salespayment.payment_code'); // Add other columns here as needed

        if ($perPage === 'all') {
            $perPage = $salesQuery->count();
        }

        // Paginate the grouped results
        $sales = $salesQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => SalePaymentResource::collection($sales),
            'pagination' => [
                'total' => $sales->total(),
                'per_page' => $sales->perPage(),
                'current_page' => $sales->currentPage(),
                'last_page' => $sales->lastPage(),
                'from' => $sales->firstItem(),
                'to' => $sales->lastItem()
            ],
        ];

        return response()->json($response);
    

        
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/sales_payments",
     *      summary="createSalesPayment",
     *      tags={"SalesPayment"},
     *      description="Create SalesPayment",
*      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/SalesPayment")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SalesPayment"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateSalesPaymentAPIRequest $request)
    {
        $input = $request->all();

        $salesPayment = $this->salesPaymentRepository->create($input);

        return $this->sendResponse($salesPayment->toArray(), 'Sales Payment saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/sales_payments/{id}",
     *      summary="getSalesPaymentItem",
     *      tags={"SalesPayment"},
     *      description="Get SalesPayment",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SalesPayment",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SalesPayment"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var SalesPayment $salesPayment */
        $salesPayment = $this->salesPaymentRepository->find($id);

        if (empty($salesPayment)) {
            return $this->sendError('Sales Payment not found');
        }

        return $this->sendResponse($salesPayment->toArray(), 'Sales Payment retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/sales_payments/{id}",
     *      summary="updateSalesPayment",
     *      tags={"SalesPayment"},
     *      description="Update SalesPayment",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SalesPayment",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
*      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/SalesPayment")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SalesPayment"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )UPDATE payments SET payment_code = CONCAT('PAY-', id) WHERE payment_code IS NULL;

     */
    public function update($id, UpdateSalesPaymentAPIRequest $request)
    {
        $input = $request->all();
      
      	$sale = $this->salesRepository->find($input['sales_id']);

            if (empty($sale)) {
                
                return $this->sendError('Sales not found for ID: ' . $input['sales_id']);
            }
      		$salesData['due_amount'] = $input['due_amount'];
      		$salesData['return_amount'] = $input['return_amount'] ?? 0;
      
       		$this->salesRepository->update($salesData, $sale->id);

        /** @var SalesPayment $salesPayment */
        $salesPayment = $this->salesPaymentRepository->find($id);

        if (empty($salesPayment)) {
            return $this->sendError('Sales Payment not found');
        }

        $salesPayment = $this->salesPaymentRepository->update($input, $id);

        return $this->sendResponse($salesPayment->toArray(), 'SalesPayment updated successfully');
    }
  
     public function processPayments(Request $request)
    {
        // Fetch sales data with JSON payments
        $salesData = DB::table('sales')->select('id', 'company_id', 'client_id', 'user_id','payment_mode', 'updated_at')->get();

        foreach ($salesData as $sale) {
            // Try to decode the JSON
            $payments = json_decode($sale->payment_mode, true);

            // Check if the decoded JSON is valid
            if (json_last_error() === JSON_ERROR_NONE && is_array($payments)) {
                foreach ($payments as $payment) {
                    // Ensure the payment array has the required keys and valid values
                    if (isset($payment['paid_type'], $payment['paid_amount'])) {
                        Salespayment::create([
                            'payment_date' =>  $sale->updated_at,
                            'payment_type' => $payment['paid_type'],
                            'payment_amount' => $payment['paid_amount'],
                            'sales_id' => $sale->id,
                            'company_id' => $sale->company_id,
                            'created_by' => $sale->user_id,
                            'customer_id' => $sale->client_id
                        ]);
                      } 
                   // else 
                    // {
                    //     // Log or handle invalid payment data
                    //  return response()->json(['Invalid payment data_11', ['sale_id' => $sale->id, 'payment' => $payment]]);
                    // }
                }
            
            } 
            // else {
            //     // Log or handle invalid JSON
            //    return response()->json(['Invalid JSON in payment_mode_ok', ['sale_id' => $sale->id, 'payment_mode' => $sale->payment_mode]]);
            // }
        }

        return response()->json(['message' => 'Payments processed successfully']);
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/sales_payments/{id}",
     *      summary="deleteSalesPayment",
     *      tags={"SalesPayment"},
     *      description="Delete SalesPayment",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SalesPayment",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var SalesPayment $salesPayment */
        $salesPayment = $this->salesPaymentRepository->find($id);

        if (!$salesPayment) {
            return $this->sendError('Sales Payment not found');
        }

        $sale = $this->salesRepository->find($salesPayment->sales_id);

        if (!$sale) {
            return $this->sendError('Sales not found for ID: ' . $salesPayment->sales_id);
        }

        $data['balance_amount'] = (int)$sale->balance_amount + (int)$salesPayment->payment_amount;
        $data['due_amount'] = (int)$sale->due_amount + (int)$salesPayment->payment_amount;

        $this->salesRepository->update($data, $sale->id);

        $salesPayment->delete();

        return $this->sendSuccess('Sales Payment deleted successfully');

    }
  
  	public function destroyByPaymentCode($payment_code)
    {
        /** @var Collection $salesPayments */
        $salesPayments = $this->salesPaymentRepository->findBy('payment_code', $payment_code);

        if ($salesPayments->isEmpty()) {
            return $this->sendError('Sales Payments not found for payment code: ' . $payment_code);
        }

        foreach ($salesPayments as $salesPayment) {
            $sale = $this->salesRepository->find($salesPayment->sales_id);

            if (!$sale) {
                return $this->sendError('Sales not found for ID: ' . $salesPayment->sales_id);
            }

            $data['balance_amount'] = (int)$sale->balance_amount + (int)$salesPayment->payment_amount;
            $data['due_amount'] = (int)$sale->due_amount + (int)$salesPayment->payment_amount;

            $this->salesRepository->update($data, $sale->id);

            $salesPayment->delete();
        }

        return $this->sendSuccess('Sales Payments deleted successfully for payment code: ' . $payment_code);
    }
  	
  
	public function getCustomerLedger(Request $request)
    {
        // Validate the request inputs
        $request->validate([
            'customer_id' => 'required|integer',
            'from_date' => 'required|date',
            'to_date' => 'required|date',
        ]);

        $customerId = $request->input('customer_id');
       // $fromDate = $request->input('from_date');
       // $toDate = $request->input('to_date');
        $fromDate = Carbon::parse($request->input('from_date'))->startOfDay();
        $toDate = Carbon::parse($request->input('to_date'))->endOfDay(); 

        // Fetch all sales (debits) within the date range
        $sales = Sales::where('client_id', $customerId)
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->where('status', 'Success')
            ->get();      

        // Fetch all payments (credits) within the date range based on sales_id
        $salesIds = $sales->pluck('id')->toArray();
        $payments = SalesPayment::whereIn('sales_id', $salesIds)
            ->whereBetween('created_at', [$fromDate, $toDate])
            ->select('payment_code', \DB::raw('SUM(payment_amount) as total_payment'), 'sales_id', 'created_at', 'payment_type')
            ->groupBy('payment_code') 
            ->get();

        // Calculate the opening balance (before the date range)
        $openingSales = Sales::where('client_id', $customerId)
            ->where('status', 'Success')
            ->whereDate('created_at', '<', $fromDate)
            ->sum('grand_total');

        $openingPayments = SalesPayment::whereIn('sales_id', Sales::where('client_id', $customerId)
            ->whereDate('created_at', '<', $fromDate)                                                 
            ->pluck('id')->toArray())      
            ->sum('payment_amount');
      
      	$openingBal = $openingSales - $openingPayments;
      	$customer = Customer::find($customerId);

        if ($customer) {
            $openingBalance = $openingBal+$customer->opening_balance;
           
        } else {
            $openingBalance = $openingBal;
        }

        

        // Prepare the ledger data (transactions within the date range)
        $salesLedger = $sales->map(function ($sale) {
            return [
                'date' => $sale->created_at,
                'transaction_type' => 'Sale',
                'transaction_no' =>  '-',
                'original_invoice_no' => $sale->invoice_id ?? '-',
                'credit' => '',
                'debit' => $sale->grand_total,  // Adjusted to use grand_total for debits
                'tds_by_party' => '',
                'tds_by_self' => '',
                'payment_mode' => '',
            ];
        });

        $paymentLedger = $payments->map(function ($payment) {
            $sale = Sales::find($payment->sales_id);  // Fetch related sale to get the invoice_no
            return [
                'date' => $payment->created_at,
                'transaction_type' => 'Payment',
                'transaction_no' => $payment->payment_code ?? '-',
                'original_invoice_no' =>  '-',
                'credit' => $payment->total_payment,
                'debit' => '',
                'tds_by_party' => '',
                'tds_by_self' => '',
                'payment_mode' => $payment->payment_type ?? '-',
            ];
        });

        // Merge and sort both sales and payment ledgers by date
        $ledger = $salesLedger->concat($paymentLedger)->sortBy('date')->values();

        // Calculate total debits and credits within the date range
        $totalDebits = $sales->sum('grand_total');
        $totalCredits = $payments->sum('total_payment');

        // Calculate closing balance
        $closingBalance = $openingBalance + $totalDebits - $totalCredits;

        // Prepare the final ledger data including opening and closing balances
        $ledgerData = collect([
            [
                'date' => $fromDate,
                'transaction_type' => 'Opening Balance',
                'transaction_no' => '-',
                'original_invoice_no' => '-',
                'credit' => '',
                'debit' => $openingBalance,
                'tds_by_party' => '',
                'tds_by_self' => '',
                'payment_mode' => '',
            ]
        ])->concat($ledger)->concat([
            [
                'date' => $toDate,
                'transaction_type' => 'Closing Balance',
                'transaction_no' => '-',
                'original_invoice_no' => '-',
                'credit' => '',
                'debit' => $closingBalance,
                'tds_by_party' => '',
                'tds_by_self' => '',
                'payment_mode' => '',
            ]
        ]);

        // Return the final response
        return response()->json([
            'party_name' => 'We Service', // Replace with actual customer name if needed
            'opening_balance' => $openingBalance,
            'closing_balance' => $closingBalance,
            'ledger' => $ledgerData,
        ]);
    }  

}
