<?php

namespace App\Http\Controllers\API;

use App\Http\Controllers\Controller;
use App\Http\Requests\API\RegisterRequest;
use App\Http\Helpers\Helper;
use Illuminate\Http\Request;
use App\Http\Resources\api\UserResource;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Validator;
//use Spatie\Permission\Models\Permission;
use App\Models\User;
use App\Models\Plans;
use App\Models\Companies;
use App\Models\Permission;
use App\Models\InvoiceSettings;
use Ramsey\Uuid\Uuid;
use Auth;


class RegisterController extends Controller
{ 
    /**
 * @param Request $request
 * @return Response
 *
 * @OA\Post(
 *      path="/auth/register",
 *      summary="createUser",
 *      tags={"User"},
 *      description="Register User",
 *      @OA\RequestBody(
 *          description="User data",
 *          required=true,
 *          @OA\MediaType(
 *              mediaType="application/json",
 *              @OA\Schema(
 *                  type="object",
 * 
 *                  @OA\Property(
 *                      property="company_name",
 *                      description="User's shop name",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="gst_number",
 *                      description="shop name Gst Number",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="name",
 *                      description="User's name",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="user_name",
 *                      description="username",
 *                      type="string"
 *                  ),
 *                 @OA\Property(
 *                      property="mobile_number",
 *                      description="mobile number",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="company_phone_no",
 *                      description="company phone number",
 *                      type="string"
 *                  ),
 *                 @OA\Property(
 *                      property="currency",
 *                      description="currency",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="email",
 *                      description="User's email",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="password",
 *                      description="User's password",
 *                      type="string"
 *                  ),
 *                  @OA\Property(
 *                      property="logo",
 *                      description="store logo",
 *                      type="string"
 *                  )
 *              )
 *          )
 *  
 *      ),
 *      @OA\Response(
 *          response=200,
 *          description="successful operation",
 *          @OA\Schema(
 *              type="object",
 *              @OA\Property(
 *                  property="success",
 *                  type="boolean"
 *              ),
 *              @OA\Property(
 *                  property="data",
 *                  ref="#/definitions/Customer"
 *              ),
 *              @OA\Property(
 *                  property="message",
 *                  type="string"
 *              )
 *          )
 *      )
 * )
 */   
    public function register(RegisterRequest $request){
        
        if (User::where('mobile_number', $request->mobile_number)->exists()) {
            return response()->json([
                'message' => 'Mobile number already registered',
                'status' => false
            ], 400);
        }

       
        //$plan = Plans::where('title','Advanced')->first();

        $user = User::create([
            'name'  => $request->name,
            'email' => $request->email,
            'password' => bcrypt($request->confirm_password),
            'user_name' => $request->user_name,
            'user_type' => 'admin',
            'status' => 1,
            'mobile_number' => $request->mobile_number ?? $request->company_phone_no,
         	'plan' => json_encode([
                            'messages_limit' => 100,
                            'employee_limit' => 2
                        ]),
            'will_expire'=> now()->addDays(1)            
        ]);

        $user_role = Role::where(['id' => 1])->first();
        
        if($user_role){

            $user->assignRole($user_role);

        }
        
        // $permissions = Permission::pluck('name')->all(); 
        // foreach ($permissions as $permission) {
        //     $user_role->givePermissionTo($permission);
        // }
        
        //  $permissions = Permission::pluck('id')->all(); // Get all permission names
      
        
        // foreach ($permissions as $permissionName) {
            
        //     $user->permissions()->attach($permissionName);
           
        //   // $user->givePermissionTo($permissionName);
        // }

        $uuid = Uuid::uuid4();

       
        $company = Companies::create([
            'company_name' => isset($request->company_name) ? $request->company_name : null,
            'gst_number' => isset($request->gst_number) ? $request->gst_number : null,
            'status' => 'enabled',
            'logo' => isset($request->logo) ? $request->logo : null,
            'currency' => isset($request->currency) ? $request->currency : null,
            'address' => isset($request->address) ? $request->address : null,
            'company_phone_no' => isset($request->company_phone_no) ? $request->company_phone_no : $request->mobile_number,
            'email' => $user->email ? $user->email : null,
            'user_id' => isset($user->id) ? $user->id : null,
            'id' => isset($uuid) ? (string)$uuid : null
        ]);




        $user->update(['company_id'  => (string)$uuid]);     
        
        
        InvoiceSettings::create([
            'name' => $company->company_name,
            'gst_number' => $company->gst_number,
            'business_contact' => $company->company_phone_no,
            'payment_opt' => '[{"type":"Cash","status":true},{"type":"Online","status":false},{"type":"Card","status":false},{"type":"Other","status":false}]',
            'selected_tax' => '[{"tax_name":"GST","value":0,"status":true},{"tax_name":"GST","value":18},{"tax_name":"GST","value":28},{"tax_name":"GST","value":3,"status":false},{"tax_name":"GST","value":5,"status":false},{"tax_name":"GST","value":6,"status":false},{"tax_name":"GST","value":12,"status":false},{"tax_name":"GST","value":14,"status":false}]',
            'invoice_prefix' => '[{"name":"Group A","value":0,"status":true,"prefix":"INVA0000"},{"name":"Group B","value":1,"status":false,"prefix":"INVB0000"}]',

            'minus_sale'=> '0',
            'company_id' => (string) $uuid
            ]);
        

        // if(!Auth::attempt($request->only('email','password'))){
        //     Helper::sendError('Email Or Password is wrong !!!');

        // }
        return response()->json([
            'message' => 'Register Successfully',
            //'user' => new UserResource($user)
            'status' => true
        ], 200);        
    }
  
    public function tokenRegister(Request $request){
      $user = Auth::user();
      if (!$user) {
              return response()->json(['error' => 'User not found'], 404);
          }

       $users = User::findOrFail($user->id); // Find the user by user_id
          // Update the user fields
          $users->fcm_token = $request->fcm_token;
          //$users->email = $request->email;



          // Save the changes
          $users->save();

          // Return response
          return response()->json([
              'message' => 'Token updated successfully',

              'status' => true
          ], 200);


    }
    

     
     public function profileUpdate(Request $request){
         
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }
         
        $users = User::findOrFail($user->id); // Find the user by user_id
        // Update the user fields
        $users->name = $request->name;
        //$users->email = $request->email;
    
        $users->user_name = $request->name;
        
        if(isset($request->avatar) && !empty($request->avatar)){
            $users->avatar = $request->avatar;
        }
        
         if ($request->filled('confirm_password')) {
            $users->password = bcrypt($request->confirm_password);
        }
    
        // Save the changes
        $users->save();
    
        // Return response
        return response()->json([
            'message' => 'Profile updated successfully',
           'user' => [
                'name' => $users->name,
                'avatar' => $users->avatar,
                'email' => $users->email
            ],
            'status' => true
        ], 200);
    }
    
    public function adminRegister(Request $request){
        
        
        if (User::where('email', $request->input('email'))->exists()) {
            return response()->json([
                'message' => 'Email already registered',
                'status' => false
            ], 400);
        }
        
        $plan = Plans::where('status',1)->first();
       
        $user = Auth::user();

        if (!$user) {
            return response()->json(['error' => 'User not found'], 404);
        }
        
        $uuid = Uuid::uuid4();
        
        $userUpdateData = [
            'name' => $request->input('name'),
            'email' => $request->input('email', $user->email),
            'password' => bcrypt($request->input('confirm_password')),
            'user_name' => $request->input('user_name'),
            'user_type' => 'admin',
            'status' => 1,
            'mobile_number' => $request->input('mobile_number'),
            'company_id' => $uuid,
            'plan' =>  json_encode([
                            'messages_limit' => 100,
                            'employee_limit' => 2
                        ]),
            'will_expire'=> now()->addDays(1)
        ];
        
        $user->update($userUpdateData);
        
        $user_role = Role::where(['id' => 1])->first();
                if($user_role){
        
                    $user->assignRole($user_role);
        
                }
        // $permissions = Permission::pluck('id')->all(); // Get all permission names
      
        
        // foreach ($permissions as $permissionName) {
            
        //     $user->permissions()->attach($permissionName);
           
        //   // $user->givePermissionTo($permissionName);
        // }
      
        
        
        // Find or create the company
        $company = Companies::firstOrNew(['user_id' => $user->id]);
        
        $companyData = [
            'company_name' => $request->input('company_name'),
            'gst_number' => $request->input('gst_number'),
            'status' => 'enabled',
            'logo' => $request->input('logo'),
            'currency' => $request->input('currency'),
            'address' => $request->input('address'),
            'company_phone_no' => $request->input('company_phone_no'),
            'email' => $user->email ? $user->email : null,
            'user_id' => $user->id,
            'id' => (string)$uuid
        ];
        
        $company->fill($companyData)->save();
        
        
        
        
         InvoiceSettings::create([
            'name' => $company->company_name,
            'gst_number' => $company->gst_number,
            'business_contact' => $company->company_phone_no,
            'payment_opt' => '[{"type":"Cash","status":true},{"type":"Online","status":false},{"type":"Card","status":false},{"type":"Other","status":false}]',
            'selected_tax' => '[{"tax_name":"GST","value":0,"status":true},{"tax_name":"GST","value":18},{"tax_name":"GST","value":28},{"tax_name":"GST","value":3,"status":false},{"tax_name":"GST","value":5,"status":false},{"tax_name":"GST","value":6,"status":false},{"tax_name":"GST","value":12,"status":false},{"tax_name":"GST","value":14,"status":false}]',
            'invoice_prefix' => '[{"name":"Group A","value":0,"status":true,"prefix":"INVA0000"},{"name":"Group B","value":1,"status":false,"prefix":"INVB0000"}]',

            'minus_sale'=> '0',
            'company_id' => (string)$uuid
            ]);
        
         $users = Auth::user();
       // $token = $users->createToken("Token", ['expires_in' => now()->addYear()->getTimestamp() - time()]);
        
        return response()->json([
            'message' => 'Registered Successfully',
            'user' => new UserResource($users),
            'status' => true
        ], 200);
       
    }
    public function delete($id)
    {
        $user = User::find($id);

        if (!$user) {
            return response()->json(['message' => 'User not found'], 404);
        }

        $user->status = 0;
        $user->save();

        return response()->json([
            'message' => 'deleted successfully',
        ]);
    }
}
