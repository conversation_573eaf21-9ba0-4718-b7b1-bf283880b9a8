<?php

namespace App\Http\Controllers;

use App\DataTables\ProductsDetailsDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateProductsDetailsRequest;
use App\Http\Requests\UpdateProductsDetailsRequest;
use App\Repositories\ProductsDetailsRepository;
use Ramsey\Uuid\Uuid;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class ProductsDetailsController extends AppBaseController
{
    /** @var ProductsDetailsRepository $productsDetailsRepository*/
    private $productsDetailsRepository;

    public function __construct(ProductsDetailsRepository $productsDetailsRepo)
    {
        $this->productsDetailsRepository = $productsDetailsRepo;
    }

    /**
     * Display a listing of the ProductsDetails.
     *
     * @param ProductsDetailsDataTable $productsDetailsDataTable
     *
     * @return Response
     */
    public function index(ProductsDetailsDataTable $productsDetailsDataTable)
    {
        return $productsDetailsDataTable->render('products_details.index');
    }

    /**
     * Show the form for creating a new ProductsDetails.
     *
     * @return Response
     */
    public function create()
    {
        return view('products_details.create');
    }

    /**
     * Store a newly created ProductsDetails in storage.
     *
     * @param CreateProductsDetailsRequest $request
     *
     * @return Response
     */
    public function store(CreateProductsDetailsRequest $request)
    {
        $input = $request->all();

        $productsDetails = $this->productsDetailsRepository->create($input);

        Flash::success('Products Details saved successfully.');

        return redirect(route('productsDetails.index'));
    }

    /**
     * Display the specified ProductsDetails.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $productsDetails = $this->productsDetailsRepository->find($id);

        if (empty($productsDetails)) {
            Flash::error('Products Details not found');

            return redirect(route('productsDetails.index'));
        }

        return view('products_details.show')->with('productsDetails', $productsDetails);
    }

    /**
     * Show the form for editing the specified ProductsDetails.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $productsDetails = $this->productsDetailsRepository->find($id);

        if (empty($productsDetails)) {
            Flash::error('Products Details not found');

            return redirect(route('productsDetails.index'));
        }

        return view('products_details.edit')->with('productsDetails', $productsDetails);
    }

    /**
     * Update the specified ProductsDetails in storage.
     *
     * @param int $id
     * @param UpdateProductsDetailsRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateProductsDetailsRequest $request)
    {
        $productsDetails = $this->productsDetailsRepository->find($id);

        if (empty($productsDetails)) {
            Flash::error('Products Details not found');

            return redirect(route('productsDetails.index'));
        }

        $productsDetails = $this->productsDetailsRepository->update($request->all(), $id);

        Flash::success('Products Details updated successfully.');

        return redirect(route('productsDetails.index'));
    }

    /**
     * Remove the specified ProductsDetails from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $productsDetails = $this->productsDetailsRepository->find($id);

        if (empty($productsDetails)) {
            Flash::error('Products Details not found');

            return redirect(route('productsDetails.index'));
        }

        $this->productsDetailsRepository->delete($id);

        Flash::success('Products Details deleted successfully.');

        return redirect(route('productsDetails.index'));
    }
}
