<template>
  <div v-if="visible" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div
      class="bg-white w-full max-w-md p-4 transform transition-transform-custom ease-in-out duration-300 rounded rouded-full"
      :class="{ 'translate-x-0': isOpen, 'translate-x-full': !isOpen }">
      <p class=" text-[25px] text-gray-600 text-center pb-4">Confirm Message</p>
      <p class="ml-4 mr-4">{{ message }}</p>
      <div class="flex justify-center items-center mt-8">
        <button class="mr-5 bg-gray-700 text-white  hover:bg-red-600 pl-5 pr-5 p-2 rounded-l rounded-r"
          @click="handleCancel">Cancel</button>
        <button class="bg-red-700 text-white  hover:bg-red-600 pl-8 pr-8 p-2 rounded-l rounded-r mr-5 ml-5"
          @click="handleOk">{{ type === 'invoice' ? 'Discard' : 'OK' }}</button>
        <button v-if="type === 'invoice' || type === 'sales' || type === 'website'"
          class="bg-green-700 text-white  hover:bg-green-600 pl-8 pr-8 p-2 rounded-l rounded-r mr-5 ml-5"
          @click="saveData">Save</button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    visible: Boolean,
    message: String,
    type: String,
  },
  data() {
    return {
      isOpen: false,
    };
  },
  methods: {
    handleOk() {
      this.$emit('ok', true);
    },
    handleCancel() {
      // this.$emit('cancel', false);
      this.isOpen = false;
      setTimeout(() => {
        this.$emit('cancel', false);
      }, 300);
    },
    saveData() {
      this.$emit('save');
    }
  },
  watch: {
    visible(newValue) {
      setTimeout(() => {
        this.isOpen = newValue;
      }, 100);
    },
  }
};
</script>

<style scoped>
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
}

.modal {
  background: white;
  padding: 20px;
  border-radius: 5px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.3);
}
</style>