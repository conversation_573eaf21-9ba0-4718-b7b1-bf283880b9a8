<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateWebsiteTemplatesAPIRequest;
use App\Http\Requests\API\UpdateWebsiteTemplatesAPIRequest;
use App\Models\WebsiteTemplates;
use App\Repositories\WebsiteTemplatesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class WebsiteTemplatesController
 * @package App\Http\Controllers\API
 */

class WebsiteTemplatesAPIController extends AppBaseController
{
    /** @var  WebsiteTemplatesRepository */
    private $websiteTemplatesRepository;

    public function __construct(WebsiteTemplatesRepository $websiteTemplatesRepo)
    {
        $this->websiteTemplatesRepository = $websiteTemplatesRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/websiteTemplates",
     *      summary="getWebsiteTemplatesList",
     *      tags={"WebsiteTemplates"},
     *      description="Get all WebsiteTemplates",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/WebsiteTemplates")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $websiteTemplates = $this->websiteTemplatesRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($websiteTemplates->toArray(), 'Website Templates retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/websiteTemplates",
     *      summary="createWebsiteTemplates",
     *      tags={"WebsiteTemplates"},
     *      description="Create WebsiteTemplates",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/WebsiteTemplates"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateWebsiteTemplatesAPIRequest $request)
    {
        $input = $request->all();

        $websiteTemplates = $this->websiteTemplatesRepository->create($input);

        return $this->sendResponse($websiteTemplates->toArray(), 'Website Templates saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/websiteTemplates/{id}",
     *      summary="getWebsiteTemplatesItem",
     *      tags={"WebsiteTemplates"},
     *      description="Get WebsiteTemplates",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of WebsiteTemplates",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/WebsiteTemplates"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var WebsiteTemplates $websiteTemplates */
        $websiteTemplates = $this->websiteTemplatesRepository->find($id);

        if (empty($websiteTemplates)) {
            return $this->sendError('Website Templates not found');
        }

        return $this->sendResponse($websiteTemplates->toArray(), 'Website Templates retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/websiteTemplates/{id}",
     *      summary="updateWebsiteTemplates",
     *      tags={"WebsiteTemplates"},
     *      description="Update WebsiteTemplates",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of WebsiteTemplates",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/WebsiteTemplates"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateWebsiteTemplatesAPIRequest $request)
    {
        $input = $request->all();

        /** @var WebsiteTemplates $websiteTemplates */
        $websiteTemplates = $this->websiteTemplatesRepository->find($id);

        if (empty($websiteTemplates)) {
            return $this->sendError('Website Templates not found');
        }

        $websiteTemplates = $this->websiteTemplatesRepository->update($input, $id);

        return $this->sendResponse($websiteTemplates->toArray(), 'WebsiteTemplates updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/websiteTemplates/{id}",
     *      summary="deleteWebsiteTemplates",
     *      tags={"WebsiteTemplates"},
     *      description="Delete WebsiteTemplates",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of WebsiteTemplates",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var WebsiteTemplates $websiteTemplates */
        $websiteTemplates = $this->websiteTemplatesRepository->find($id);

        if (empty($websiteTemplates)) {
            return $this->sendError('Website Templates not found');
        }

        $websiteTemplates->delete();

        return $this->sendSuccess('Website Templates deleted successfully');
    }
}
