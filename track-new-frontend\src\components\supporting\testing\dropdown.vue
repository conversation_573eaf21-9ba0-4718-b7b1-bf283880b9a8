<template>
    <div class="relative mt-1">
        <input type="text" v-model="query" @input="handleInputChange" @blur="handleBlur"
            class="w-full border-none py-2 pl-3 pr-10 text-sm leading-5 text-gray-900 focus:ring-0" />
        <div class="absolute inset-y-0 right-0 flex items-center pr-2">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
            </svg>
        </div>
        <div v-if="isOpen"
            class="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
            <div v-if="filteredPeople.length === 0 && query !== ''"
                class="relative cursor-default select-none px-4 py-2 text-gray-700">
                Nothing found.
            </div>
            <div v-else v-for="person in filteredPeople" :key="person.id"
                class="relative cursor-default select-none py-2 pl-10 pr-4"  @click=" handleOptionClick(person)">
                <span class="block truncate hover:bg-red-200">{{ person.name }}</span>
            </div>
        </div>
    </div>
</template>
  
<script>
export default {
    data() {
        return {
            query: '',
            isOpen: false,
            selected: null,
            people: [
                { id: 1, name: 'Wade Cooper' },
                { id: 2, name: 'Arlene Mccoy' },
                { id: 3, name: 'Devon Webb' },
                { id: 4, name: 'Tom Cook' },
                { id: 5, name: 'Tanya Fox' },
                { id: 6, name: 'Hellen Schmidt' },
            ],
        };
    },
    computed: {
        filteredPeople() {
            return this.query === ''
                ? this.people
                : this.people.filter((person) =>
                    person.name.toLowerCase().includes(this.query.toLowerCase())
                );
        },
    },
    methods: {
        handleInputChange() {
            this.isOpen = true;
        },
        handleOptionClick(person) {
            console.log(person, 'What happening...!');            
            this.selected = person;
            this.query = person.name;
            this.isOpen = false;
        },       
        // handleBlur() {
        //     this.isOpen = false;
        // },
    },
};
</script>
  
<style scoped>
/* Add your component-specific styles here */
</style>
  