<?php

namespace App\Http\Controllers;

use App\DataTables\CustomerCategoryDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateCustomerCategoryRequest;
use App\Http\Requests\UpdateCustomerCategoryRequest;
use App\Repositories\CustomerCategoryRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class CustomerCategoryController extends AppBaseController
{
    /** @var CustomerCategoryRepository $customerCategoryRepository*/
    private $customerCategoryRepository;

    public function __construct(CustomerCategoryRepository $customerCategoryRepo)
    {
        $this->customerCategoryRepository = $customerCategoryRepo;
    }

    /**
     * Display a listing of the CustomerCategory.
     *
     * @param CustomerCategoryDataTable $customerCategoryDataTable
     *
     * @return Response
     */
    public function index(CustomerCategoryDataTable $customerCategoryDataTable)
    {
        return $customerCategoryDataTable->render('customer_categories.index');
    }

    /**
     * Show the form for creating a new CustomerCategory.
     *
     * @return Response
     */
    public function create()
    {
        return view('customer_categories.create');
    }

    /**
     * Store a newly created CustomerCategory in storage.
     *
     * @param CreateCustomerCategoryRequest $request
     *
     * @return Response
     */
    public function store(CreateCustomerCategoryRequest $request)
    {
        $input = $request->all();

        $customerCategory = $this->customerCategoryRepository->create($input);

        Flash::success('Customer Category saved successfully.');

        return redirect(route('customerCategories.index'));
    }

    /**
     * Display the specified CustomerCategory.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $customerCategory = $this->customerCategoryRepository->find($id);

        if (empty($customerCategory)) {
            Flash::error('Customer Category not found');

            return redirect(route('customerCategories.index'));
        }

        return view('customer_categories.show')->with('customerCategory', $customerCategory);
    }

    /**
     * Show the form for editing the specified CustomerCategory.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $customerCategory = $this->customerCategoryRepository->find($id);

        if (empty($customerCategory)) {
            Flash::error('Customer Category not found');

            return redirect(route('customerCategories.index'));
        }

        return view('customer_categories.edit')->with('customerCategory', $customerCategory);
    }

    /**
     * Update the specified CustomerCategory in storage.
     *
     * @param int $id
     * @param UpdateCustomerCategoryRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateCustomerCategoryRequest $request)
    {
        $customerCategory = $this->customerCategoryRepository->find($id);

        if (empty($customerCategory)) {
            Flash::error('Customer Category not found');

            return redirect(route('customerCategories.index'));
        }

        $customerCategory = $this->customerCategoryRepository->update($request->all(), $id);

        Flash::success('Customer Category updated successfully.');

        return redirect(route('customerCategories.index'));
    }

    /**
     * Remove the specified CustomerCategory from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $customerCategory = $this->customerCategoryRepository->find($id);

        if (empty($customerCategory)) {
            Flash::error('Customer Category not found');

            return redirect(route('customerCategories.index'));
        }

        $this->customerCategoryRepository->delete($id);

        Flash::success('Customer Category deleted successfully.');

        return redirect(route('customerCategories.index'));
    }
}
