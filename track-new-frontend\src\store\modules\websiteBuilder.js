// store/modules/websiteBuilder.js
const state = {
  selected_id: 2,
  preview_url: '',
  is_modified: true,
  additional_info: '',
  };

  const mutations = {
    SET_SELECTEDID(state, item) {
          state.selected_id = item;
    },
    SET_UPDATEURL(state, url) {
      state.preview_url = url;
    },
    SET_USERNAME(state, additional_data) {      
      state.additional_info = additional_data;
    },
    SET_ISMODIFIED(state, status) {
      state.is_modified = status;
    },
    RESET_STATE(state) {
      state.selected_id = 2;
      state.preview_url =  '';
      state.is_modified =  true;
      state.additional_info =  '';
    }
  };

const actions = {
  async updateSelectdItem({ commit }, item) {
    try {
        
      if (item) {
        commit('SET_SELECTEDID', item);
      }
    } catch (error) {
      console.error('Error fetching item list:', error);
    }
  },
  async updatePreviewUrl({ commit }, url) {
    try {
        
      if (url) {
        commit('SET_UPDATEURL', url);
      }
    } catch (error) {
      console.error('Error fetching preview:', error);
    }
  },
  //--get url---
  async fetchWebsiteUrl({ commit }) {
    try {
      const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
      if (company_id && company_id !== '') {
        await axios.get(`/website-list/${company_id}`)
          .then(response => {
            // Handle response
            // console.log(response.data, 'website list..!');
            const data_one = response.data.data;
            let preview_link = '';
            let additional_data = '';
            if (data_one && data_one.domain_name) {
              preview_link = 'https://' + data_one.domain_name;
            } else {
              preview_link = 'https://buziness.me/website/' + data_one.username;
            }
            if (data_one) {
              additional_data = { id:data_one.id, username: data_one.username ? data_one.username: '', website_name: data_one.website_name, template_id: data_one.template_id, theme_settings: data_one.theme_settings };
            }
            commit('SET_UPDATEURL', preview_link);
            commit('SET_USERNAME', additional_data)
            return data_one;
          })
          .catch(error => {
            // Handle error
            console.error('Error:', error);
            return error;
          });
      }
    } catch (error) {
      console.error('Error fetching item list:', error);
    }
  },
  //---validate form modified---
  async validateForm({ commit }, [oldFormData, newFormData]) {
    // console.log(oldFormData , 'TTTTTTTTT', newFormData);    
    if (oldFormData && newFormData) {
      // Compare keys and values
      let isMatched = true;
      for (let key in newFormData) {
        if (newFormData[key] !== oldFormData[key]) {
          isMatched = false;
          break;
        }
      }
      // Commit the result
      commit("SET_ISMODIFIED", isMatched);
    } else if (newFormData) {
      // Compare keys and values
      let isMatched = true;
      for (let key in newFormData) {
        if (newFormData[key] !== '' && newFormData[key] !== null && newFormData[key]) {
          isMatched = false;
          break;
        }
      }      
      // Commit the result
      commit("SET_ISMODIFIED", isMatched);
    } else {
      commit('SET_ISMODIFIED', false);
      return false;
    }      
  }

  };

  const getters = {
    
    selectedId(state) {
      return state.selected_id;
    },
    previewUrl(state) {
      return state.preview_url;
    },
    isModified(state) {
      return state.is_modified;
    },
    additionalInfo(state) {
      return state.additional_info;
    },
    companyID() {
      const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
      return company_id;
    }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
