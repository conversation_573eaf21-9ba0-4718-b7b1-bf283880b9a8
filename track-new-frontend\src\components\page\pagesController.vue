<template>
    <div class="flex h-screen"
        :class="{ 'mb-[70px]': isMobile && (selected_page === 'privacy policy' || selected_page === 'terms & conditions'), 'pt-12': isMobile && !page_open }">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mt-[60px]': !isMobile, 'mt-[10px] mb-[70px]': isMobile }">
            <div class="my-custom-margin p-4">
                <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                    <div v-for="(page, index) in filteredMenuItems" :key="index"
                        class="bg-white shadow-lg rounded-lg p-4 flex flex-col items-center hover:shadow-xl transform hover:scale-105 transition duration-300">
                        <div class="text-center text-xl font-semibold text-blue-800">{{ page.page }}</div>
                        <div class="mt-2 text-gray-600 text-sm text-center">
                            {{ page.descriptions || 'This is a placeholder description for the card.' }}
                        </div>
                        <button @click="selectPage(page)"
                            class="mt-4 bg-blue-500 text-white py-2 px-4 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-300 focus:ring-offset-2">
                            View
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"></sidebar>
        </div> -->
        <pagesDropdown :showModal="page_open" :selected_page="selected_page" @close-modal="handleClose">
        </pagesDropdown>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'pages'"></bottombar> -->
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/pages/headbar.vue';
import pagesDropdown from '../supporting/dialog_box/pagesDropdown.vue';
// import bottombar from '../supporting/dashboard/bottombar.vue';
import { useMeta } from '@/composables/useMeta';
import { isRestrictedDevice } from '@/utils/deviceUtils';

export default {
    name: 'pagescontroller',
    components: {
        // sidebar,
        // headbar,
        pagesDropdown,
        // bottombar
    },
    data() {
        return {
            isMobile: false,
            pages: [{ page: 'About Us', url: 'https://track-new.com/about-us/', descriptions: 'Learn more about our mission, vision, and the values that drive our organization.' },
            { page: 'Refund Policy', url: 'https://track-new.com/refund-policy/', descriptions: 'Understand the terms and conditions for refunds and returns for our products and services.', blockOnApple: true },
            { page: 'Privacy Policy', url: 'https://track-new.com/privacy-policy/', descriptions: 'Discover how we handle your personal information and protect your privacy.' },
            { page: 'Terms & Conditions', url: 'https://track-new.com/terms-conditions/', descriptions: 'Review the rules and guidelines governing the use of our services and website.' }],
            // pages: [{ page: 'about', url: 'https://eaglemindsdev.github.io/track-new-privacy-policy/about_us.html' }, { page: 'contact', url: 'https://eaglemindsdev.github.io/track-new-privacy-policy/contact_us.html' }, { page: 'privacy policy', url: 'https://eaglemindsdev.github.io/track-new-privacy-policy/privacy_policy.html' }, { page: 'terms & conditions', url: 'https://eaglemindsdev.github.io/track-new-privacy-policy/terms&conditions.html' }],
            isSidebarOpen: false,
            route_item: 13,
            viewLeads: [],
            getFilteredData: [],
            dataFromChild: [],
            page_open: false,
            selected_page: null,
        };
    },
    setup() {
        const pageTitle = 'Pages';
        const pageDescription = 'Learn about our mission, terms, privacy practices, and refund policy—all in one place. Transparency and trust are at the core of everything we do."';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    computed: {
        filteredMenuItems() {
            return this.pages.filter(item => !(isRestrictedDevice() && item.blockOnApple));
        }
    },
    methods: {
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleDataFromChild(data) {
            this.dataFromChild = data;
            // console.log(data,'WWWWWW');
        },
        getFiteredDataList(data) {
            // console.log(data, 'WWWW');
            this.getFilteredData = data;
        },
        handleClose() {
            this.page_open = false;
        },
        selectPage(page) {
            if (page) {
                // console.log(page.url, 'RRRRR');

                this.selected_page = page;
                // this.page_open = true;
                window.open(page.url, '_blank', 'noopener,noreferrer');
            }
        }

    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>
