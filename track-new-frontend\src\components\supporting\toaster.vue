<!-- Toaster.vue -->
<template>
    <transition name="toast">
        <div v-if="show" :class="[getToastClass(type), 'fixed top-0 right-0 m-2 rounded-md shadow-lg z-50']">
            <div class="flex justify-between items-center p-5">
                <div>
                    <span v-if="type === 'info'">
                        <font-awesome-icon :icon="['fas', 'info-circle']" class="mr-2" size="lg"></font-awesome-icon>
                    </span>
                    <span v-else-if="type === 'success'">
                        <font-awesome-icon :icon="['fas', 'check-circle']" class="mr-2" size="lg"></font-awesome-icon>
                    </span>
                    <span v-else-if="type === 'warning'">
                        <font-awesome-icon :icon="['fas', 'exclamation-triangle']" class="mr-2"
                            size="lg"></font-awesome-icon>
                    </span>
                    {{ message }}
                </div>
                <button @click="closeToast" class="ml-4 text-white"><font-awesome-icon icon="fa-solid fa-xmark"
                        size="lg" /></button>
            </div>
            <div class="status-bar" :class="type"></div>
        </div>
    </transition>
</template>

<script>

export default {
    props: {
        show: {
            type: Boolean,
            default: false
        },
        message: {
            type: String,
            default: ''
        },
        type: {
            type: String,
            default: 'info'
        }
    },
    methods: {
        getToastClass(type) {
            switch (type) {
                case 'success':
                    return 'bg-green-500 text-white';
                case 'error':
                    return 'bg-red-500 text-white';
                case 'warning':
                    return 'bg-yellow-500 text-black';
                default:
                    return 'bg-blue-500 text-white';
            }
        },
        closeToast() {
            this.$emit('update:show', false);
        }
    },
    watch: {
        show(newVal) {
            if (newVal) {
                setTimeout(() => {
                    this.closeToast();
                }, 3000);
            }
        }
    },

};
</script>

<style scoped>
.toast-enter-active,
.toast-leave-active {
    transition: all 0.3s ease;
}

.toast-enter-from,
.toast-leave-to {
    transform: translateY(-20px);
    opacity: 0;
}

.status-bar {
    height: 4px;
    width: 100%;
    margin-top: 5px;
    background-color: currentColor;
    animation: status-bar 3s linear;
}

@keyframes status-bar {
    from {
        width: 100%;
    }

    to {
        width: 0;
    }
}
</style>
