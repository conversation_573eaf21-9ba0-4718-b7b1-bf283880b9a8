const { DataTypes } = require('sequelize');
const sequelize = require('../config/database');

const Company = sequelize.define('Company', {
  id: {
    type: DataTypes.INTEGER,
    primaryKey: true,
    autoIncrement: true
  },
  company_name: {
    type: DataTypes.STRING,
    allowNull: false,
    validate: {
      notEmpty: true,
      len: [2, 255]
    }
  },
  company_email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  company_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  company_mobile: {
    type: DataTypes.STRING(20),
    allowNull: true
  },
  company_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  company_city: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  company_state: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  company_pincode: {
    type: DataTypes.STRING(10),
    allowNull: true
  },
  company_country: {
    type: DataTypes.STRING(100),
    defaultValue: 'India'
  },
  company_logo: {
    type: DataTypes.STRING,
    allowNull: true
  },
  gst_number: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: [15, 15]
    }
  },
  pan_number: {
    type: DataTypes.STRING(50),
    allowNull: true,
    validate: {
      len: [10, 10]
    }
  },
  website: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isUrl: true
    }
  },
  industry: {
    type: DataTypes.STRING(100),
    allowNull: true
  },
  company_size: {
    type: DataTypes.ENUM('1-10', '11-50', '51-200', '201-500', '500+'),
    allowNull: true
  },
  established_year: {
    type: DataTypes.INTEGER,
    allowNull: true,
    validate: {
      min: 1900,
      max: new Date().getFullYear()
    }
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  status: {
    type: DataTypes.BOOLEAN,
    defaultValue: true
  },
  subscription_status: {
    type: DataTypes.ENUM('trial', 'active', 'suspended', 'cancelled'),
    defaultValue: 'trial'
  },
  trial_ends_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  subscription_ends_at: {
    type: DataTypes.DATE,
    allowNull: true
  },
  settings: {
    type: DataTypes.JSON,
    allowNull: true,
    defaultValue: {
      timezone: 'Asia/Kolkata',
      currency: 'INR',
      date_format: 'DD/MM/YYYY',
      time_format: '24h',
      language: 'en',
      notifications: {
        email: true,
        sms: true,
        push: true
      },
      features: {
        service_management: true,
        lead_management: true,
        amc_management: true,
        sales_management: true,
        inventory_management: true,
        reporting: true
      }
    }
  },
  billing_address: {
    type: DataTypes.TEXT,
    allowNull: true
  },
  billing_contact_name: {
    type: DataTypes.STRING,
    allowNull: true
  },
  billing_contact_email: {
    type: DataTypes.STRING,
    allowNull: true,
    validate: {
      isEmail: true
    }
  },
  billing_contact_phone: {
    type: DataTypes.STRING(20),
    allowNull: true
  }
}, {
  tableName: 'companies',
  hooks: {
    beforeCreate: (company) => {
      // Set trial period (30 days from creation)
      if (!company.trial_ends_at) {
        const trialEnd = new Date();
        trialEnd.setDate(trialEnd.getDate() + 30);
        company.trial_ends_at = trialEnd;
      }
    }
  }
});

// Instance methods
Company.prototype.isTrialExpired = function() {
  return this.trial_ends_at && new Date() > this.trial_ends_at;
};

Company.prototype.isSubscriptionActive = function() {
  return this.subscription_status === 'active' && 
         (!this.subscription_ends_at || new Date() < this.subscription_ends_at);
};

Company.prototype.canAccessFeature = function(feature) {
  if (!this.settings || !this.settings.features) {
    return false;
  }
  
  // Check if subscription is active or in trial
  const hasAccess = this.isSubscriptionActive() || !this.isTrialExpired();
  
  return hasAccess && this.settings.features[feature] === true;
};

Company.prototype.updateSettings = async function(newSettings) {
  const currentSettings = this.settings || {};
  const updatedSettings = {
    ...currentSettings,
    ...newSettings
  };
  
  return this.update({ settings: updatedSettings });
};

module.exports = Company;
