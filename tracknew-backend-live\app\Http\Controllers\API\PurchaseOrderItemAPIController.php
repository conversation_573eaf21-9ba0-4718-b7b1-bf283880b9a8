<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreatePurchaseOrderItemAPIRequest;
use App\Http\Requests\API\UpdatePurchaseOrderItemAPIRequest;
use App\Models\PurchaseOrderItem;
use App\Repositories\PurchaseOrderItemRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class PurchaseOrderItemController
 * @package App\Http\Controllers\API
 */

class PurchaseOrderItemAPIController extends AppBaseController
{
    /** @var  PurchaseOrderItemRepository */
    private $purchaseOrderItemRepository;

    public function __construct(PurchaseOrderItemRepository $purchaseOrderItemRepo)
    {
        $this->purchaseOrderItemRepository = $purchaseOrderItemRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/purchase_order_items",
     *      summary="getPurchaseOrderItemList",
     *      tags={"PurchaseOrderItem"},
     *      description="Get all PurchaseOrderItems",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/PurchaseOrderItem")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $purchaseOrderItems = $this->purchaseOrderItemRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($purchaseOrderItems->toArray(), 'Purchase Order Items retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/purchase_order_items",
     *      summary="createPurchaseOrderItem",
     *      tags={"PurchaseOrderItem"},
     *      description="Create PurchaseOrderItem",
  *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/PurchaseOrderItem")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrderItem"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreatePurchaseOrderItemAPIRequest $request)
    {
        $input = $request->all();

        $purchaseOrderItem = $this->purchaseOrderItemRepository->create($input);

        return $this->sendResponse($purchaseOrderItem->toArray(), 'Purchase Order Item saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/purchase_order_items/{id}",
     *      summary="getPurchaseOrderItemItem",
     *      tags={"PurchaseOrderItem"},
     *      description="Get PurchaseOrderItem",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrderItem",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrderItem"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var PurchaseOrderItem $purchaseOrderItem */
        $purchaseOrderItem = $this->purchaseOrderItemRepository->find($id);

        if (empty($purchaseOrderItem)) {
            return $this->sendError('Purchase Order Item not found');
        }

        return $this->sendResponse($purchaseOrderItem->toArray(), 'Purchase Order Item retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/purchase_order_items/{id}",
     *      summary="updatePurchaseOrderItem",
     *      tags={"PurchaseOrderItem"},
     *      description="Update PurchaseOrderItem",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrderItem",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/PurchaseOrderItem")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/PurchaseOrderItem"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdatePurchaseOrderItemAPIRequest $request)
    {
        $input = $request->all();

        /** @var PurchaseOrderItem $purchaseOrderItem */
        $purchaseOrderItem = $this->purchaseOrderItemRepository->find($id);

        if (empty($purchaseOrderItem)) {
            return $this->sendError('Purchase Order Item not found');
        }

        $purchaseOrderItem = $this->purchaseOrderItemRepository->update($input, $id);

        return $this->sendResponse($purchaseOrderItem->toArray(), 'PurchaseOrderItem updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/purchase_order_items/{id}",
     *      summary="deletePurchaseOrderItem",
     *      tags={"PurchaseOrderItem"},
     *      description="Delete PurchaseOrderItem",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of PurchaseOrderItem",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var PurchaseOrderItem $purchaseOrderItem */
        $purchaseOrderItem = $this->purchaseOrderItemRepository->find($id);

        if (empty($purchaseOrderItem)) {
            return $this->sendError('Purchase Order Item not found');
        }

        $purchaseOrderItem->delete();

        return $this->sendSuccess('Purchase Order Item deleted successfully');
    }
}
