<template>
    <div v-if="showModal" class="text-sm fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50"
        :class="{ 'mb-[60px]': isMobile }">
        <div class="bg-white w-full sm:w-3/4  transform transition-transform ease-in-out duration-300 rounded overflow-auto sm:h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div
                class="flex justify-between items-center relative text-xl w-full px-4 py-3 rounded rounded-b-none set-header-background">
                <p for="salesType" class="text-white font-bold text-center flex justify-end ml-3">
                    {{ editData ? 'Edit Item' : 'Add New Item' }}
                </p>
                <p class="close" @click.stop="closeModal">&times;</p>
            </div>

            <!-- Modal Content -->
            <div class="p-4 pt-0 space-y-4 relative">
                <!---search items--->
                <div class="flex" @mouseover="tooltip.item = true"
                    @mouseleave="tooltip.item = false, enableNonItem = false"
                    :class="{ 'mt-3': isMobile, 'mt-5': isAndroid }">
                    <div class="border border-r-0 py-2 px-2">
                        <font-awesome-icon icon="fa-solid fa-barcode" size="lg" />
                    </div>
                    <!---search item--->
                    <input class="flex border justify-between py-2 px-2 w-full outline-none rounded-none" type="text"
                        v-model="selected_item" ref="selectItemField" @input="filterProducts()"
                        @keydown.enter="handleEnterKey(0, 'product')"
                        @keydown.down.prevent="handleDownArrow(filteredProductList)"
                        @keydown.up.prevent="handleUpArrow(filteredProductList)"
                        @focus="isFormFocus.selected_item = true" @blur="blurItemDropdown"
                        :class="{ 'border-blue-600': isFormFocus.selected_item }" placeholder="Item name / barcode" />
                    <!--Add new-->
                    <div class="border border-l-0 py-2 px-2 cursor-pointer" @click="addNewItemModal">
                        <font-awesome-icon icon="fa-solid fa-circle-plus" size="lg" style="color: blue" />
                    </div>
                    <!--Item dropdown-->
                    <ul v-if="productDropdown && selected_item && selected_item.length > 1"
                        class="absolute mt-10 ml-8 sm:ml-8 lg:ml-9 w-1/4 max-h-60 overflow-auto bg-white text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm cursor-pointer"
                        style="z-index: 150;" :style="{ width: $refs.selectItemField.offsetWidth + 'px' }"
                        @mouseover="mouseIsOnOption(true)" @mouseleave="mouseIsOnOption(false)">
                        <li class="hover:bg-gray-300 px-3 py-1 border" v-for="(product, i) in filteredProductList"
                            :key="i" :class="{ 'bg-gray-200': i === selectedIndex }"
                            @click="selectProduct(index, product)">
                            {{ product.barcodes.barcode + ' - ' + product.products.product_name }}
                        </li>
                        <li v-if="filteredProductList.length === 0 && selected_item && selected_item.length > 1 && findExistItem()"
                            @click="addNewItemModal"
                            class="px-3 py-1 border new-product-btn text-green-700 hover:bg-gray-300">+
                            New Product</li>
                    </ul>
                    <!---tooltip-->
                    <div v-if="tooltip.item"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8 ml-[100px] lg:ml-[150px]">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Select Item</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                </div>
                <!--add non inventory items-->
                <div class="flex justify-between items-center bg-gray-300 p-2 rounded" @click="toggleNonItems">
                    <div>
                        <button>+ Add non inventory items</button>
                    </div>
                    <span>
                        <font-awesome-icon v-if="enableNonItem" icon="fa-solid fa-angle-up" />
                        <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                    </span>
                </div>
                <!-- Transition for non-inventory items -->
                <transition name="slide-down">
                    <div v-if="enableNonItem" class="space-y-1">
                        <!-- Item Name -->
                        <div class="flex flex-col">
                            <label class="text-gray-700">Product Details</label>
                            <textarea rows="5" v-model="formData.product_name" ref="productName"
                                @keyup.enter="focusInputFormData('qty')"
                                class="py-2 px-3 border border-blue-500 rounded w-full focus:ring-2 focus:ring-blue-300"
                                placeholder="Enter product details"></textarea>
                        </div>
                        <div class="grid grid-cols-2 gap-2">
                            <!-- Quantity Section -->
                            <div class="flex flex-col">
                                <label class="text-gray-700">Quantity</label>
                                <div class="flex justify-between items-center space-x-2">
                                    <button @click="removeQuantity()"
                                        class="bg-gray-200 text-red-700 p-2 rounded font-bold hover:bg-gray-300">-</button>
                                    <input type="number" v-model="formData.qty" @input="updateFormDataTotal('qty')"
                                        ref="formDataQty" @keyup.enter="focusInputFormData('price')"
                                        class="px-2 py-1 text-center w-full border rounded focus:ring-2 focus:ring-blue-300"
                                        min="0" pattern="[0-9]*" />
                                    <button @click="addQuantity()"
                                        class="bg-gray-200 text-green-700 p-2 rounded font-bold hover:bg-gray-300">+</button>
                                </div>
                            </div>

                            <!-- Price -->
                            <div class="flex flex-col">
                                <label class="text-gray-700">Price</label>
                                <input type="number" v-model="formData.price" @input="updateFormDataTotal('price')"
                                    ref="formDataPrice" @keyup.enter="focusInputFormData('tax')"
                                    class="py-2 px-3 border rounded focus:ring-2 focus:ring-blue-300 w-full" min="0" />
                            </div>
                        </div>
                        <div class="grid grid-cols-2 gap-2">
                            <!-- Discount -->
                            <div class="flex flex-col relative">
                                <label class="text-gray-700">Discount</label>
                                <p class="py-2 px-3 border bg-gray-100 rounded cursor-pointer"
                                    @click="openItemTaxDiscountDialog(false, false, 'formData', 'discount')">{{
                                        formData.discount }}
                                </p>
                                <!-- Tooltip -->
                                <div v-if="tooltip['discount' + 'formData']"
                                    class="absolute top-0 left-1/2 transform -translate-x-1/2">
                                    <div class="bg-black text-white text-xs p-1 rounded">Click to change</div>
                                </div>
                            </div>

                            <!-- Tax -->
                            <div class="flex flex-col relative">
                                <label class="text-gray-700">Tax</label>
                                <p class="py-2 px-3 border bg-gray-100 rounded cursor-pointer"
                                    @click="openItemTaxDiscountDialog(false, false, 'formData', 'tax')">{{ formData.tax
                                    }}</p>
                                <!-- Tooltip -->
                                <div v-if="tooltip['tax' + 'formData']"
                                    class="absolute top-0 left-1/2 transform -translate-x-1/2">
                                    <div class="bg-black text-white text-xs p-1 rounded">Click to change</div>
                                </div>
                            </div>
                        </div>

                        <!-- Subtotal -->
                        <div class="flex flex-col">
                            <label class="text-gray-700">Subtotal</label>
                            <input v-model.number="formData.total" @input="updatePriceFomData"
                                @keyup.enter="focusInputFormData('submit')" ref="formDataTotal"
                                class="py-2 px-3 border rounded w-full focus:ring-2 focus:ring-blue-300" type="number"
                                min="0" />
                        </div>
                        <!--buttons-->
                        <div class="flex justify-between items-center py-2">
                            <button class="rounded-full bg-red-600 text-white p-1 px-2"
                                @click="resetForm">Reset</button>
                            <button class="rounded-full bg-green-600 text-white p-1 px-2" @click="addRowData">Add
                                Item</button>
                        </div>
                    </div>
                </transition>
                <!--items-list-->
                <div v-if="isAndroid && isMobile" class="-mt-1 flex flex-wrap justify-center table-container"
                    :class="{ 'h-[410px]': items.length > 1 }">
                    <!---sales itms add data overflow-y-auto-->
                    <div v-for="(item, index) in items" :key="index"
                        class="bg-white rounded-lg shadow-md m-2 p-2 w-full md:w-1/2 lg:w-1/3 2xl:w-1/5 border border-gray-300">
                        <!-- Card Header -->
                        <div class="flex justify-between items-center mb-1">
                            <h3 class="text-sm font-semibold truncate">{{ item.product_name }}</h3>
                            <div class="flex justify-between items-center px-1">
                                <button @click="moveUp(index)" :class="{ 'hidden': index === 0 }" title="Move Up"
                                    class="w-full flex justify-center text-blue-700 hover:text-blue-600 px-1">
                                    <font-awesome-icon icon="fa-solid fa-arrow-up" />
                                </button>
                                <button @click="moveDown(index)" :class="{ 'hidden': index === items.length - 1 }"
                                    title="Move Down"
                                    class="w-full flex justify-center text-blue-700 hover:text-blue-600 px-1">
                                    <font-awesome-icon icon="fa-solid fa-arrow-down" />
                                </button>
                                <button @click="deleteRow(index)"
                                    class="text-red-700 font-bold cursor-pointer hover:bg-gray-100 w-full flex justify-center px-1">
                                    <font-awesome-icon icon="fa-solid fa-trash-can" style="color: red" />
                                </button>
                            </div>
                        </div>
                        <!-- Quantity -->
                        <div class="grid grid-cols-2 gap-4 mb-1">
                            <div>
                                <label class="block text-xs font-bold mr-2 pb-1">Qty:</label>
                                <div class="flex items-center relative">
                                    <button @click="removeQuantity(index)"
                                        class="px-3 py-1 border border-blue-600 border-r-0 hover:bg-gray-300 text-red-700 font-bold text-xs rounded-l-lg">
                                        <font-awesome-icon icon="fa-solid fa-minus" />
                                    </button>
                                    <input type="number" v-model="item.qty" ref="itemqty"
                                        class="w-full px-2 py-1 border border-blue-600 rounded-none text-xs"
                                        @input="updateTotal(index), is_updated = true" />
                                    <button @click="addQuantity(index)"
                                        class="px-3 py-1 border border-blue-600 border-l-0 hover:bg-gray-300 text-green-700 font-bold text-xs rounded-r-lg">
                                        <font-awesome-icon icon="fa-solid fa-plus" />
                                    </button>
                                </div>
                            </div>

                            <!-- Price -->
                            <div class="items-center relative">
                                <label class="block text-xs font-bold mr-2 pb-1">Price:</label>
                                <input type="number" v-model="item.price"
                                    class="w-full py-1 px-1 border rounded focus:outline-none focus:shadow-outline text-xs"
                                    @input="updateTotal(index), is_updated = true" />
                            </div>
                        </div>

                        <!-- Discount and Tax -->
                        <div class="grid grid-cols-2 gap-4 mb-1">
                            <div class="flex items-center">
                                <label class="block text-xs font-bold mr-2">Discount:</label>
                                <div class="w-full flex justify-between items-center relative">
                                    <p class="border py-1 px-1 rounded bg-gray-100 cursor-pointer text-xs truncate"
                                        @click="openItemTaxDiscountDialog(item, index, '', 'discount')">
                                        {{ item.discount }}
                                    </p>
                                    <button @click="openItemTaxDiscountDialog(item, index, '', 'discount')">
                                        <font-awesome-icon icon="fa-regular fa-pen-to-square" />
                                    </button>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <label class="block text-xs font-bold mr-2">Tax:</label>
                                <div class="w-full flex justify-between items-center relative">
                                    <p class="border py-1 px-1 rounded bg-gray-100 cursor-pointer text-xs truncate"
                                        @click="openItemTaxDiscountDialog(item, index, '', 'tax')">
                                        {{ item.tax }}
                                    </p>
                                    <button @click="openItemTaxDiscountDialog(item, index, '', 'tax')">
                                        <font-awesome-icon icon="fa-regular fa-pen-to-square" />
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- Subtotal -->
                        <div class="grid grid-cols-2 gap-4 mb-1">
                            <label class="block text-xs font-bold mr-2">Subtotal:</label>
                            <!-- <input v-model="item.total" type="number"
                                class="w-full py-1 px-1 border rounded focus:outline-none focus:shadow-outline text-xs"
                                @input="updatePrice(index)" /> -->
                            <input v-model="item.total" type="number"
                                class="w-full py-1 px-1 border rounded focus:outline-none focus:shadow-outline text-xs"
                                @input="handleSubtotalChange(index)" @focus="flag_subtotal = true"
                                @blur="flag_subtotal = false" />
                        </div>
                        <!-- Add Serial No Button -->
                        <!---add serial number-->
                        <div v-if="item.product_type === 'Product' && typeOfInvoice !== 'estimation'"
                            class="flex justify-start px-1 py-2 text-xs">
                            <button
                                v-if="(item.serial_no === undefined || item.serial_no === '' || !item.serial_no || (Array.isArray(item.serial_no) && item.serial_no.length === 0)) && (!item.notes || item.notes == '')"
                                class="bg-blue-400 text-white px-3 rounded rounded-full p-[2px] hover:bg-blue-500"
                                @click="openSerialNumber(item)">Add Serial No</button>
                            <div
                                v-if="(item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0) || (item.notes && item.notes !== '')">
                                <p v-if="typeOfInvoice !== 'estimation' && (item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0)"
                                    class="flex flex-wrap items-center space-x-1">
                                    <span class="font-semibold">Serial No:</span>
                                    <span v-for="serial in item.serial_no" :key="serial"
                                        class="truncate inline-block max-w-[30ch] flex-wrap">
                                        {{ serial }},
                                    </span>
                                </p>

                                <p v-if="item.notes && item.notes !== ''" class="flex flex-col space-y-1">
                                    <span class="font-semibold">Des:</span>
                                    <span class="truncate max-w-[50ch]">
                                        {{ item.notes }}
                                    </span>
                                </p>

                                <button
                                    class="text-[10px] text-green-700 border px-2 border-gray-700 rounded hover:border-green-700 ml-1"
                                    @click="openSerialNumber(item)">Edit</button>
                            </div>
                        </div>
                        <!-- <div v-if="typeOfInvoice !== 'estimation'" class="flex justify-start items-center mb-1">
                            <button
                                v-if="(!item.serial_no || (Array.isArray(item.serial_no) && item.serial_no.length === 0)) && (!item.notes || item.notes == '')"
                                @click="openSerialNumber(item)"
                                class="text-xs bg-blue-400 text-white px-2 rounded hover:bg-blue-500">
                                Add Serial No
                            </button>
                            <div v-if="(item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0) || (item.notes && item.notes !== '')"
                                class="flex flex-wrap text-xs">
                                <p v-if="(item.serial_no && Array.isArray(item.serial_no) && item.serial_no.length > 0)"
                                    class="line-clamp-2"><span class="font-bold">Serial No:</span>
                                    {{ item.serial_no.join(', ') }}</p>
                                <p v-if="item.notes && item.notes !== ''" class="pl-2">Des: {{ item.notes }}</p>
                                <button @click="openSerialNumber(item)"
                                    class="text-xs text-green-700 border px-1 border-gray-700 rounded hover:border-green-700 ml-1">
                                    Edit
                                </button>
                            </div>
                        </div> -->
                        <!---add Note to estimation-->
                        <div v-if="item.product_type === 'Product' && typeOfInvoice === 'estimation'"
                            class="flex justify-start px-7 py-2">
                            <button v-if="!item.notes || item.notes == ''"
                                class="text-xs bg-blue-400 text-white px-3 rounded rounded-full p-[2px] hover:bg-blue-500"
                                @click="openSerialNumber(item)">Add Note</button>
                            <div v-if="item.notes && item.notes !== ''" class="text-[12px]">
                                <p v-if="item.notes && item.notes !== ''">{{ item.notes }}</p>
                                <button
                                    class="text-[10px] text-green-700 border px-2 border-gray-700 rounded hover:border-green-700 ml-1"
                                    @click="openSerialNumber(item)">Edit</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- Bottom Fixed Buttons -->
        <div class="fixed inset-x-0 bottom-0 bg-white border-t p-4 px-2 flex justify-around">
            <!-- Cancel Button -->
            <button @click="closeModal"
                class="bg-red-500 text-white py-2 px-4 rounded-full flex items-center space-x-2 hover:bg-red-600">
                <font-awesome-icon icon="fa-solid fa-times" />
                <span>Cancel</span>
            </button>
            <!--grand total-->
            <div class="flex justify-center item-center" :class="{ 'bg-white rounded px-2 py-1': isMobile }">

                <p class="text-sm flex flex justify-center items-center font-normal text-green-800">
                    <span class="text-black px-1">Total:</span> {{ currentCompanyList && currentCompanyList.currency ===
                        'INR' ? '\u20b9' : currentCompanyList.currency }}
                    {{ grandTotal }}
                </p>
            </div>

            <!-- Save Button -->
            <button @click="closeModal"
                class="bg-green-500 text-white py-2 px-4 rounded-full flex items-center space-x-2 hover:bg-green-600">
                <font-awesome-icon icon="fa-solid fa-check" />
                <span>Save</span>
            </button>
        </div>
        <posAddTaxDiscount :showModal="open_item_tax_discount" @close-modal="closeItemTaxDiscountDialog"
            :invoice_setting="invoice_setting" :currentCompanyList="currentCompanyList"
            :itemData="open_item_tax_discount_index === null ? formData : selected_item_data"
            :type="type_tax_or_discount"></posAddTaxDiscount>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <addNewItem :showModal="open_add_newItem" @close-modal="closeItemModal" :product_name="selected_item">
        </addNewItem>
        <!--confirm box---->
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <posAddSerialNumberItem :showModal="openSerial" @close-modal="closeSerialModel" :typeOfInvoice="typeOfInvoice"
            :item_data="selected_serial_item">
        </posAddSerialNumberItem>
    </div>
</template>

<script>
import posAddTaxDiscount from './posAddTaxDiscount.vue';
import { mapActions, mapGetters } from 'vuex';
import addNewItem from './addNewItem.vue';
import confirmbox from './confirmbox.vue';
import posAddSerialNumberItem from './posAddSerialNumberItem.vue';

export default {
    components: {
        posAddTaxDiscount,
        addNewItem,
        confirmbox,
        posAddSerialNumberItem
    },
    props: {
        showModal: Boolean,
        formData: Object,
        editData: Object,
        isAndroid: Boolean,
        typeOfInvoice: String,
        isMobile: Boolean,
        invoice_setting: Object,
        editIndex: Number,
        sales_items: Object,
        over_all_discount: {
            type: Number,
            default: 0
        },
        shipping_details: Object,
        currentCompanyList: Object,
        companyId01: String
    },
    data() {
        return {
            isOpen: false,
            open_item_tax_discount: false,
            //---tooltip---
            tooltip: {},
            //--toaster---
            message: '',
            show: false,
            type_toaster: 'success',
            //---dropdown items---
            enableNonItem: false,
            //---items data
            selected_item: '',
            product: [],
            productDropdown: false,
            filteredProductList: [],
            items: [],
            isMouseInOption: false,
            //---index--
            selectedIndex: 0,
            isFormFocus: {},
            pagination: {},
            open_add_newItem: false,
            //--add tax and discount
            open_item_tax_discount_index: null,
            selected_item_data: null,
            is_updated: false,
            open_confirmBox: false,
            deleteIndex: null,
            focustaxdiscount: false,
            get_all_data: {},
            //--serial items field--
            openSerial: false,
            selected_serial_item: null,
            //----flag subtotal---
            flag_subtotal: false,
            type_tax_or_discount: '',
        }
    },
    computed: {
        ...mapGetters('items', ['currentItems', 'currentItemsPagination']),
        //--total qty---
        totalQuantity() {
            let count_total = this.items.reduce((sum, item) => sum + item.qty, 0);
            if (this.get_all_data === null) {
                this.get_all_data = { total_qty: count_total };
            } else {
                this.get_all_data.total_qty = count_total;
            }
            return count_total;
        },
        //---total amount--
        totalAmount() {
            let sub_total = this.items.reduce((sum, item) => (1 * sum) + (1 * item.total), 0);
            if (this.get_all_data === null) {
                this.get_all_data = { sub_total: sub_total };
            } else {
                this.get_all_data.sub_total = sub_total;
            }
            return sub_total;
        },
        //---total discount--
        totalDiscount() {
            return this.items.reduce((sum, item) => (1 * sum) + (1 * item.discount), 0);
        },
        //---over all discount---
        overAllDiscount() {
            if (this.over_all_discount !== null) {
                return this.over_all_discount.type === 'Percentage' ? (this.totalAmount * (this.over_all_discount.value / 100)).toFixed(2) : this.over_all_discount.value;
            } else {
                return 0;
            }
        },
        //--total tax---
        overAllTax() {
            let calculate_tax = this.items.reduce((sum, item) => (1 * sum) + (1 * item.tax), 0);
            return calculate_tax;
        },
        //--grandTotal--
        grandTotal() {
            if (this.items && this.items.length > 0) {
                let totalDiscount = this.totalDiscount;
                let overallDiscount = this.over_all_discount !== null ? this.overAllDiscount : 0;
                let totalAmount = this.items.reduce((sum, item) => sum + parseFloat(item.total), 0);
                let shipping_charge = this.shipping_details.shipping > 0 ? this.shipping_details.shipping : 0;
                let total_tax = this.overAllTax.toFixed(2);
                let grandTotal = (totalAmount + shipping_charge) - (isNaN(1 * overallDiscount) ? 0 : (1 * overallDiscount));
                if (this.get_all_data === null) {
                    this.get_all_data = { discount_total: (overallDiscount).toFixed(2), shipping: shipping_charge, grand_total: Math.round(grandTotal), total_tax: total_tax };
                } else {
                    this.get_all_data.discount_total = (1 * overallDiscount).toFixed(2);
                    this.get_all_data.shipping = shipping_charge;
                    this.get_all_data.grand_total = Math.round(grandTotal);
                    this.get_all_data.total_tax = total_tax;
                }
                return Math.round(grandTotal);
            }
        },
    },
    methods: {
        ...mapActions('items', ['fetchItemList']),
        // updateIsMobile() {
        //     this.isMobile = window.innerWidth < 1024;
        // },
        closeModal(data) {
            this.isOpen = false;
            this.message = '';
            setTimeout(() => {
                this.$emit('close-modal', this.items);
            }, 300);
        },
        //---add new items--
        calculateTaxDiscountform() {
            if (this.formData.tax_type === "Exclusive") {
                // Calculate tax value   
                if (this.formData.discount_data && Object.keys(this.formData.discount_data).length > 0) {
                    this.formData.discount = this.formData.discount_data && this.formData.discount_data.type === 'Percentage' ? ((this.formData.qty * this.formData.price) * ((1 * this.formData.discount_data.value) / 100)).toFixed(2) : this.formData.discount_data ? this.formData.discount_data.value : 0;
                }
                let make_price = (this.formData.qty * this.formData.price) - (1 * this.formData.discount);
                let qty_price = 1 * make_price;
                let tax_amount = qty_price * (1 + ((1 * this.formData.taxvalue) / 100));
                this.formData.tax = (tax_amount - qty_price).toFixed(2);
                //--get total
                this.formData.total = (tax_amount).toFixed(2);
            } else {
                if (this.formData.discount_data && Object.keys(this.formData.discount_data).length > 0) {
                    this.formData.discount = this.formData.discount_data && this.formData.discount_data.type === 'Percentage' ? ((this.formData.qty * this.formData.price) * ((1 * this.formData.discount_data.value) / 100)).toFixed(2) : this.formData.discount_data ? this.formData.discount_data.value : 0;
                }
                let make_price = (this.formData.qty * this.formData.price) - (1 * this.formData.discount);
                this.formData.total = (make_price).toFixed(2);


                if (this.formData.taxvalue >= 0) {
                    // Calculate tax value
                    this.formData.tax = ((make_price) - ((make_price) / (1 + (1 * this.formData.taxvalue) / 100))).toFixed(2);
                }
            }
        },
        updateFormDataTotal(type) {
            if (type === 'price' && this.formData.price >= 0) {
                this.formData.total = this.formData.qty * this.formData.price;
                this.calculateTaxDiscountform();
            } else if (type === 'qty' && this.formData.qty >= 0) {
                this.formData.total = this.formData.qty * this.formData.price;
                this.calculateTaxDiscountform();
            } else {
                this.calculateTaxDiscountform();
            }
        },
        removeQuantity() {
            this.formData.qty = this.formData.qty - 1;
            this.updateFormDataTotal('qty');
        },
        addQuantity() {
            this.formData.qty = this.formData.qty + 1;
            this.updateFormDataTotal('qty');
        },
        openItemTaxDiscountDialog(data, index, type, from) {
            this.type_tax_or_discount = from;
            if (data && index >= 0 && type !== 'formData') {
                this.selected_item_data = data;
                this.open_item_tax_discount = true;

                this.open_item_tax_discount_index = index;
            } else if (type === 'formData') {
                this.selected_item_data = this.formData;
                this.open_item_tax_discount_index = null;
                this.open_item_tax_discount = true;
            }
        },
        closeItemTaxDiscountDialog(data) {
            if (data) {
                if (this.open_item_tax_discount_index >= 0 && this.open_item_tax_discount_index !== null) {
                    // console.log(data, 'Item tax and discount updaed succesfully');
                    this.items[this.open_item_tax_discount_index].tax_type = data.tax_type ? data.tax_type : 'Inclusive';
                    this.items[this.open_item_tax_discount_index].taxvalue = data.tax_value >= 0 ? data.tax_value : 0;
                    this.items[this.open_item_tax_discount_index].tax_name = data.tax_name ? data.tax_name : '';
                    if (this.items[this.open_item_tax_discount_index].discount_data) {
                        this.items[this.open_item_tax_discount_index].discount_data.type = data.discount_type ? data.discount_type : 'Fixed';
                        this.items[this.open_item_tax_discount_index].discount_data.value = data.discount_value >= 0 ? data.discount_value : 0;
                    } else {
                        this.items[this.open_item_tax_discount_index].discount_data = { type: data.discount_type ? data.discount_type : 'Fixed', value: data.discount_value >= 0 ? data.discount_value : 0 };
                    }
                    this.updateTotal();

                } else {
                    this.formData.tax_type = data.tax_type ? data.tax_type : 'Inclusive';
                    this.formData.taxvalue = data.tax_value >= 0 ? data.tax_value : 0;
                    this.formData.tax_name = data.tax_name ? data.tax_name : '';
                    if (this.formData.discount_data) {
                        this.formData.discount_data.type = data.discount_type ? data.discount_type : 'Fixed';
                        this.formData.discount_data.value = data.discount_value >= 0 ? data.discount_value : 0;
                    } else {
                        this.formData.discount_data = { type: data.discount_type ? data.discount_type : 'Fixed', value: data.discount_value >= 0 ? data.discount_value : 0 };
                    }
                    this.updateFormDataTotal();
                }
            }
            this.open_item_tax_discount = false;
            //--foucs fields---
            if (this.$refs.formDataTotal && this.focustaxdiscount) {
                this.$refs.formDataTotal.focus();
            }
        },
        //---user add new product details----
        addRowData() {
            if (this.formData) {
                const { product_name, price, qty, total } = this.formData;
                if (product_name && product_name !== '' && qty >= 0) {
                    // this.$emit('close-modal', this.formData);
                    this.items.push(this.formData);
                    this.$emit('resetForm');
                    this.enableNonItem = false;
                    if (this.$refs.selectItemField) {
                        this.$nextTick(() => {
                            this.$refs.selectItemField.focus();
                        })
                    }
                } else {
                    this.message = 'Please enter the product details and then store them in the sales list.';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            } else {
                this.message = 'There is an issue, please wait...';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        resetForm() {
            this.$emit('resetForm');

        },
        updatePriceFomData() {
            let price_per_qty = this.formData.total / this.formData.qty;
            let tax_per_qty = price_per_qty / (1 + (this.formData.taxvalue / 100));
            // console.log(price_per_qty, 'Price', tax_per_qty, 'for tax data..!', this.formData, 'What happening..1');
            if (this.formData.tax_type === "Exclusive") {
                this.formData.price = (price_per_qty / (1 + (this.formData.taxvalue / 100))).toFixed(2);
                // Calculate tax value
                this.formData.tax = (price_per_qty - tax_per_qty).toFixed(2);
            } else {
                let tax_per_qty01 = price_per_qty * (1 + (this.formData.taxvalue / 100));
                this.formData.price = (price_per_qty).toFixed(2);
                // Calculate tax value
                this.formData.tax = (tax_per_qty01 - price_per_qty).toFixed(2);
            }
            this.formData.discount = this.formData.discount_data.type === 'Percentage' ? this.formData.total * (this.formData.discount_data.value / 100).toFixed(2) : this.formData.discount_data.value;

        },
        //---form enables----
        toggleNonItems() {
            this.enableNonItem = !this.enableNonItem;
            setTimeout(() => {
                if (this.enableNonItem && this.$refs.productName) {
                    this.$nextTick(() => {
                        this.$refs.productName.focus();
                    });
                }
            }, 100);
        },
        //---items--
        //-----filter product--
        filterProducts(is_from_watch) {
            const enteredProductName = this.selected_item.toLowerCase();
            this.productDropdown = true;
            // console.log(enteredProductName, 'Product name');
            if (this.product.length !== 0 && enteredProductName.length > 1) {
                // console.log('validation is done...1');
                let existingProducts = [];
                let existingProductCodes = [];
                if (this.items.length > 0) {
                    existingProducts = !this.items.map((item, i) => item.product_name.toLowerCase());
                    existingProductCodes = !this.items.map((item, i) => item.barcode && typeof item.barcode === 'string' ? item.barcode && item.barcode.toLowerCase() : null).filter(code => code !== null);
                }
                this.filteredProductList = this.product.filter(opt => {
                    let isExistingName = false;
                    let isExistingCode = false;
                    if (existingProducts.length > 0 && opt.products && opt.products.product_name) {
                        isExistingName = existingProducts.includes(opt.products.product_name.toLowerCase());
                    }
                    if (existingProductCodes.length > 0 && opt.barcodes && opt.barcodes.barcode) {
                        isExistingCode = existingProductCodes.includes(opt.barcodes.barcode.toLowerCase());
                    }
                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.products && opt.products.product_name ? opt.products.product_name.toLowerCase().includes(enteredProductName) : false;
                    const codeMatch = opt.barcodes && opt.barcodes.barcode ? opt.barcodes.barcode.toLowerCase().includes(enteredProductName) : false;
                    // return nameMatch || codeMatch;
                    return (!isExistingName || !isExistingCode) && (nameMatch || codeMatch);
                });
                if (this.filteredProductList.length === 0 && this.pagination.product && Number(this.pagination.product.current_page) < this.pagination.product.last_page && !is_from_watch) {
                    this.fetchItemList(Number(this.pagination.product.current_page) + 1, 1000);
                }
            }
        },
        //---add product option controll--
        findExistItem() {
            const enteredProductName = this.selected_item.toLowerCase();
            if (this.product.length !== 0 && enteredProductName.length > 1) {
                const existingProducts = this.product.map(item => item.products.product_name.toLowerCase());
                const existingProductCodes = this.product.map(item => item.barcodes && item.barcodes.barcode.toLowerCase());
                // Use a separate array to store existing product names and codes
                const existingNamesAndCodes = [...existingProducts, ...existingProductCodes];
                // Check if the entered product name or product code matches any existing product name or product code
                const isExisting = existingNamesAndCodes.includes(enteredProductName);
                // console.log(isExisting, 'Waht happening....!');
                return !isExisting;
            }
            return true;
        },
        selectProduct(index, selectedProduct) {
            // console.log(this.invoice_setting, 'EEEEEE');
            let selectIndex = null;
            //--validate duplicate--
            let validateDuplicate = this.items.map((item, i) => {
                if (selectedProduct.products && item.product_name === selectedProduct.products.product_name && item.product_id === selectedProduct.product_id && item.barcode_id === selectedProduct.barcode_id) {
                    selectIndex = i;
                    return true;
                }
            });
            // console.log(selectIndex, 'selected index data....');

            if (selectIndex !== null && selectIndex >= 0) {
                // console.log(validateDuplicate, 'what happening..!')
                let total_stock = this.items[selectIndex].total_qty;
                if (total_stock > this.items[selectIndex].qty || (this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].minus_sale === '1')) {
                    this.items[selectIndex] = { ...this.items[selectIndex], qty: this.items[selectIndex].qty + 1 };
                    this.selected_item = '';
                    // console.log(this.items, 'object pushed successfully...!');
                    this.updateTotal(selectIndex);
                    this.calculateTax(selectIndex);
                    this.is_updated = true;

                } else {
                    this.minus_sales_open = true;
                    // alert('Low stock alert: Please enable the minus sale option in settings to continue allowing sales that minus stock');
                }
                //---reset---
                validateDuplicate = [];
                selectIndex = null;
            } else {
                // console.log(selectedProduct, 'What happening...!', selectedProduct.sales_price * (selectedProduct.gst_value / 100));
                let get_data = {
                    company_id: this.companyId01,
                    product_type: selectedProduct.products.product_type ? selectedProduct.products.product_type : 'Product',
                    product_name: selectedProduct.products.product_name,
                    total_qty: selectedProduct.total_qty,
                    description: '',
                    price: selectedProduct.sales_price,
                    hsn_code: selectedProduct.products.hsn_code,
                    taxvalue: selectedProduct.gst_value,
                    tax_name: selectedProduct.tax_name ? selectedProduct.tax_name : 'GST',
                    tax_type: selectedProduct.gst_type,
                    qty: 1,
                    tax: selectedProduct.sales_price * (selectedProduct.gst_value / 100),
                    // total: selectedProduct.gst_type,
                    discount_data: { type: selectedProduct.discount_type, value: selectedProduct.discount },
                    discount: selectedProduct.discount_type === 'Percentage' ? selectedProduct.sales_price * (selectedProduct.discount / 100) : selectedProduct.discount,
                    product_id: selectedProduct.product_id,
                    barcode_id: selectedProduct.barcode_id,
                    product_code: selectedProduct.products.product_code,
                    serial_no: ''
                };
                this.items.push(get_data);

                this.selected_item = '';
                this.is_updated = true;
            }
            this.productDropdown = false;
            this.filteredProductList = [];

            this.focusItemFields();
            this.updateTotal();
            if (this.pagination && this.pagination.product && Number(this.pagination.product.current_page) !== 1) {
                this.getProductListData(Number(this.pagination.product.current_page) + 1, 1000);
            }
        },
        //----table--
        handleEnterKey(index, type, product_name) {
            // Check if filteredProductList has at least one item
            if (type === 'product') {
                if (this.filteredProductList.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectProduct(index, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                } else {
                    this.addNewItemModal();
                }
            }
        },
        //---add new item--
        addNewItemModal() {
            this.open_add_newItem = true;
            this.productDropdown = false;
        },
        closeItemModal(data) {
            if (data && data.id) {
                this.product.push(data);
                this.selectProduct(this.items.length, data);
            }
            this.open_add_newItem = false;
            this.focusItemFields();
        },
        //--always focus item fields--
        focusItemFields() {
            this.$nextTick(() => {
                if (this.$refs.selectItemField) {
                    this.$refs.selectItemField.focus();
                }
            });
        },
        //---blur item dropdown--
        blurItemDropdown() {
            this.isFormFocus.selected_item = false;
            if (!this.isMouseInOption) {
                this.productDropdown = false;
            }
        },
        mouseIsOnOption(value) {
            this.isMouseInOption = value;
        },
        //---sales calculations----
        //----calculate--
        validateQuantity(index) {
            let total_stock = this.items[index].total_qty;
            let qty = this.items[index].qty;
            if (qty < 0) {
                this.items[index].qty = 0;
            }
            // console.log(index, 'Waht happening...!!!!!!')
            if (this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].minus_sale === '1') {
                if (!(total_stock > this.items[index].qty)) {
                    // alert('low stock');
                    this.message = 'Item stock is Low..!';
                    this.type_toaster = 'warning';
                    this.show = true;
                }
            }
            else {
                if (!(total_stock > this.items[index].qty)) {
                    this.items[index].qty = total_stock;
                    this.minus_sales_open = true;
                    // alert('Low stock alert: Please enable the minus sale option in settings to continue allowing sales that minus stock');
                }
            }
            this.calculateTotal(index);

        },
        addQuantity(index) {
            // console.log(index, 'WWWWW');
            if (index >= 0) {
                let total_stock = this.items[index].total_qty;
                if (total_stock > this.items[index].qty) {
                    this.items[index].qty = this.items[index].qty + 1;
                    this.calculateTotal(index);
                    this.is_updated = true;
                } else {
                    if (this.invoice_setting && this.invoice_setting[0] && this.invoice_setting[0].minus_sale === '1') {
                        this.items[index].qty = this.items[index].qty + 1;
                        this.is_updated = true;
                        if (this.items[index].qty === total_stock + 1) {
                            // alert('low stock alert');
                            this.message = 'Item stock is Low..!';
                            this.type_toaster = 'warning';
                            this.show = true;
                        }
                    } else {
                        this.minus_sales_open = true;
                        // alert('Low stock alert: Please enable the minus sale option in settings to continue allowing sales that minus stock');
                        // this.items[index].qty = this.items[index].qty;
                    }
                    this.calculateTotal(index); //-----based on minus sale do alert
                }
            } else {
                this.formData.qty = this.formData.qty + 1;
                this.updateFormDataTotal('qty');
            }
        },
        removeQuantity(index) {
            if (index >= 0 && this.items[index].qty > 1) {
                this.items[index].qty = this.items[index].qty - 1;
                this.calculateTotal(index);
                this.is_updated = true;
            } else {
                this.formData.qty = this.formData.qty - 1;
                this.updateFormDataTotal('qty')
            }
        },
        updateTotal() {
            // Iterate through each item to update totals and tax values
            this.items.forEach((item) => {
                // Calculate total based on price and tax
                // let item_total_value = item.qty * item.price;
                if (item.tax_type === "Exclusive") {
                    // Calculate tax value    
                    item.discount = item.discount_data && item.discount_data.type === 'Percentage' ? ((item.qty * item.price) * ((1 * item.discount_data.value) / 100)).toFixed(2) : item.discount_data ? item.discount_data.value : 0;
                    let make_price = (item.qty * item.price) - (1 * item.discount);
                    let qty_price = 1 * make_price;
                    let tax_amount = qty_price * (1 + ((1 * item.taxvalue) / 100));
                    item.tax = (tax_amount - qty_price).toFixed(2);
                    //--get total
                    item.total = (tax_amount).toFixed(2);

                } else {
                    item.discount = item.discount_data && item.discount_data.type === 'Percentage' ? ((item.qty * item.price) * ((1 * item.discount_data.value) / 100)).toFixed(2) : item.discount_data ? item.discount_data.value : 0;
                    let make_price = (item.qty * item.price) - (1 * item.discount);
                    item.total = (make_price).toFixed(2);
                    // Calculate tax value
                    item.tax = ((make_price) - ((make_price) / (1 + (1 * item.taxvalue) / 100))).toFixed(2);
                }
                // this.is_updated = true;
            });

            // Update sub-total and grand total
            this.subTotal = this.calculateSubTotal();
            // this.grandTotal = this.calculateGrandTotal();
            // console.log('this.paymentData what happening', this.paymentData);

            // Update balance if payment is entered
            // if (this.paymentData.length > 0 && this.paymentData[0].payment_amount !== '') {
            //     this.updateBalance();
            // }
        },
        handleSubtotalChange(index) {
            // this.is_updated = true;
            // Trigger the price update when the subtotal changes
            this.updatePrice(index);
        },
        updatePrice(index) {
            // let findData = this.items.find((opt, i) => i === index);
            // let price_per_qty = findData.total / findData.qty;
            // let tax_per_qty = price_per_qty / (1 + (findData.taxvalue / 100));
            // // console.log(price_per_qty, 'Price', tax_per_qty, 'for tax data..!', findData, 'What happening..1');
            // if (findData.tax_type === "Exclusive") {
            //     findData.price = (price_per_qty / (1 + (findData.taxvalue / 100))).toFixed(2);
            //     // Calculate tax value
            //     findData.tax = (price_per_qty - tax_per_qty).toFixed(2);
            // } else {
            //     let tax_per_qty01 = price_per_qty * (1 + (findData.taxvalue / 100));
            //     findData.price = (price_per_qty).toFixed(2);
            //     // Calculate tax value
            //     findData.tax = (tax_per_qty01 - price_per_qty).toFixed(2);
            // }
            // findData.discount = findData.discount_data.type === 'Percentage' ? findData.total * (findData.discount_data.value / 100).toFixed(2) : findData.discount_data.value;
            // findData.price = (findData.total / (1 + findData.tax / 100)).toFixed(2);
            // findData.taxvalue = (findData.total - findData.price).toFixed(2);
            // this.is_updated = true;
            const item = this.items[index];

            // Price per quantity calculation based on the subtotal
            let price_per_qty = item.total / item.qty;
            let tax_per_qty = price_per_qty / (1 + (item.taxvalue / 100));

            if (item.tax_type === "Exclusive") {
                item.price = (price_per_qty / (1 + (item.taxvalue / 100))).toFixed(2);
                item.tax = (price_per_qty - tax_per_qty).toFixed(2);
            } else {
                let tax_per_qty01 = price_per_qty * (1 + (item.taxvalue / 100));
                item.price = (price_per_qty).toFixed(2);
                item.tax = (tax_per_qty01 - price_per_qty).toFixed(2);
            }

            // Calculate discount
            item.discount = item.discount_data.type === 'Percentage'
                ? (item.total * (item.discount_data.value / 100)).toFixed(2)
                : item.discount_data.value;

            // Mark as updated
            this.is_updated = true;
        },

        calculateSubTotal() {
            const subTotal = this.items.reduce((sum, item) => sum + Number(item.total), 0);
            this.calculateTotalTax();
            // console.log('Sub Total:', subTotal);
            return subTotal;
        },

        calculateGrandTotal() {
            const subTotal = this.calculateSubTotal();

            if (this.discount !== '') {
                // Check if the discount value is in percentage format (e.g., "100%")
                if (this.discountType === '%') {
                    // Extract the numeric value from the percentage
                    const discountPercentage = parseFloat(this.discount);

                    // Calculate the discounted amount based on the percentage
                    const discountAmount = (subTotal * discountPercentage) / 100;

                    // Apply the discount to the subTotal
                    return (subTotal - discountAmount).toFixed();
                } else {
                    // If the discount value is not in percentage format, apply it directly
                    return (subTotal - Number(this.discount)).toFixed();
                }
            } else {
                return subTotal;
            }
        },

        calculateTotalTax() {
            this.totalTaxValue = this.items.reduce((sum, item) => sum + Number(item.taxvalue), 0);
        },
        //--calculate total
        calculateTotal() {
            // console.log('UUUU', this.paymentData, 'RRRRRRRRRRRR');
            // if (event.key === 'Enter') {
            this.updateTotal();
            // }
        },
        //---calculate discount value--
        calculateDiscount() {
            // this.grandTotal = this.calculateGrandTotal();
            this.calculateShipping();
            // Update balance if payment is entered
            if (this.paymentData.length > 0 && this.paymentData[0].payment_amount !== '') {
                this.updateBalance();
            }
        },
        calculateTax() {
            this.updateTotal();
            // this.grandTotal = this.calculateGrandTotal();
            this.subTotal = this.calculateSubTotal();

            // if (this.paymentData.length > 0 && this.paymentData[0].payment_amount !== '') {
            //     this.updateBalance();
            // }
        },
        //--move up & down functions---
        moveUp(index) {
            if (index > 0) {
                let temp = this.items[index - 1];
                this.items.splice(index - 1, 1, this.items[index]);
                this.items.splice(index, 1, temp);
            }
        },
        moveDown(index) {
            if (index < this.items.length - 1) {
                let temp = this.items[index + 1];
                this.items.splice(index + 1, 1, this.items[index]);
                this.items.splice(index, 1, temp);
            }
        },
        //---arrow on key press
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //-----delete record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.items.splice(this.deleteIndex, 1);
                this.updateTotal();
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },

        deleteRow(index) {
            // this.formData.selectedTax.splice(index, 1);
            this.deleteIndex = index;
            this.open_confirmBox = true;
            this.is_updated = true
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },
        //---focus the data----
        focusInputFormData(type) {
            if (type === 'qty') {
                this.$refs.formDataQty.focus();
            } else if (type === 'price') {
                this.$refs.formDataPrice.focus();
            } else if (type === 'tax') {
                this.focustaxdiscount = true;
                this.openItemTaxDiscountDialog(false, false, 'formData', 'tax');
            } else if (type === 'submit') {
                this.addRowData();
            }
        },
        openSerialNumber(item) {
            this.selected_serial_item = item;
            this.openSerial = true;
        },
        closeSerialModel(data) {
            // if (Array.isArray(data)) {
            // console.log(data, 'WWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWWW happening.......');
            if (data) {
                let findIndexData = this.items.findIndex(opt => opt.product_id === this.selected_serial_item.product_id);
                if (findIndexData !== -1) {
                    if (this.typeOfInvoice !== 'estimation') {
                        if (this.items[findIndexData].hasOwnProperty('serial_no')) {
                            // If 'serial_no' property exists, update it with the new data
                            this.items[findIndexData].serial_no = data.serial_no;
                            this.items[findIndexData].notes = data.notes;
                        } else {
                            // If 'serial_no' property doesn't exist, add it and set its value to 'data'
                            this.items[findIndexData].serial_no = data.serial_no;
                            this.items[findIndexData].notes = data.notes;
                            // console.log(this.items[findIndexData], 'how to updated data..!');
                        }
                        this.is_updated = true;
                    }
                    else {
                        this.items[findIndexData].notes = data.notes;
                        this.is_updated = true;
                    }
                }
            }
            this.openSerial = false; // Close the modal
        },

    },
    mounted() {
        // this.updateIsMobile(); // Initial check
        // window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        // Remove the resize event listener when the component is destroyed
        // window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    if (this.product.length === 0 && this.companyId01) {
                        if (this.currentItems && this.currentItems.length > 0) {
                            this.product = this.currentItems;
                            if (this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0) {
                                this.pagination.product = this.currentItemsPagination;
                            }
                            this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
                        }
                        this.fetchItemList(this.pagination.product && this.pagination.product.total ? Number(this.pagination.product.total) + 50 : 2000);
                    }

                    if (this.$refs.selectItemField && this.items && this.items.length > 0 && !this.editData) {
                        this.$nextTick(() => {
                            this.$refs.selectItemField.focus();
                        })
                    } else if (!this.editData) {
                        this.enableNonItem = true;
                        setTimeout(() => {
                            if (this.$refs.productName) {
                                this.$nextTick(() => {
                                    this.$refs.productName.focus();
                                })
                            }
                        }, 100);
                    }
                }
            }, 100);
        },
        currentItems: {
            deep: true,
            handler(newValue) {
                this.product = newValue;
                if (this.currentItemsPagination && Object.keys(this.currentItemsPagination).length > 0) {
                    this.pagination.product = this.currentItemsPagination;
                }
                this.filterProducts(true);
            }
        },
        editIndex: {
            deep: true,
            handler(newValue) {
                setTimeout(() => {
                    if (newValue >= 0 && this.$refs.itemqty && this.$refs.itemqty[newValue]) {
                        // Add a small delay to ensure rendering completes before focusing
                        this.$nextTick(() => {
                            this.$refs.itemqty[newValue].focus();
                        });
                    }
                }, 100);
            }
        },
        sales_items: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.items = newValue;
                    if (!this.flag_subtotal) {
                        this.updateTotal();
                    }
                }
            }

        }
    }

};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}

/* Add top-to-bottom slide effect for non-inventory items */
.slide-down-enter-active,
.slide-down-leave-active {
    transition: all 0.3s ease;
}

.slide-down-enter,
.slide-down-leave-to {
    transform: translateY(-20px);
    opacity: 0;
}
</style>