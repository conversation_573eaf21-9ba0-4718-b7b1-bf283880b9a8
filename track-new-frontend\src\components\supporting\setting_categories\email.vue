<template>
    <!--loader-->
    <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
        :rows="number_of_rows" :gap="gap" :type="'grid'">
    </skeleton>
    <div v-if="!open_skeleton" class="flex flex-col items-center">
        <div class="w-full sm:w-1/2 mb-4">
            <!-- Username -->
            <div class="mb-4">
                <label class="block text-md font-bold">Username</label>
                <input type="text" v-model="formValues.username" class="mt-1 p-2 border rounded-md w-full"
                    placeholder="enter username" />
            </div>

            <!-- Password -->
            <div class="mb-4">
                <label class="block text-md font-bold">Password</label>
                <input type="password" v-model="formValues.password" class="mt-1 p-2 border rounded-md w-full"
                    placeholder="enter password" />
            </div>

            <!-- <PERSON><PERSON> Mailer -->
            <div class="mb-4">
                <label class="block text-md font-bold">SMTP Mailer</label>
                <input type="text" v-model="formValues.smtp" class="mt-1 p-2 border rounded-md w-full"
                    placeholder="enter SMTP Mailer" />
            </div>
            <!-- Encryption -->
            <div class="mb-4">
                <label class="block text-md font-bold">Encryption</label>
                <select v-model="formValues.encryption" class="mt-1 p-2 border rounded-md w-full">
                    <option value='1'>Enable</option>
                    <option value='0'>Disable</option>
                </select>
            </div>

            <!-- Save Button -->
            <button @click="saveData"
                class="bg-green-600 text-white py-2 px-5 rounded-md mx-auto block hover:bg-green-500">
                Save
            </button>
        </div>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
    </div>
</template>

<script>
import dialogAlert from '../dialog_box/dialogAlert.vue';
export default {
    name: 'email',
    props: {
        companyId: String,
        userId: String
    },
    components: {
        dialogAlert,
    },
    data() {
        return {
            formValues: {},
            open_message: false,
            message: '',
            type: 'add',
            //--skeleton
            open_skeleton: false,
            number_of_columns: 1,
            number_of_rows: 4,
            gap: 5,
        };
    },
    methods: {
        saveCredentials() {
            if (this.formValues.username && this.formValues.smtp && this.formValues.password && this.formValues.encryption !== undefined) {
                let sent_data = {
                    company_id: this.companyId,
                    ...this.formValues
                };
                if (this.type === 'add') {
                    // axios.post('/')
                }

            }
        },
        openMessage(msg) {
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.open_message = false;
            this.message = '';
        },
        getExistdata() {

        }
    },
    mounted() {
        if (this.companyId) {
            this.getExistdata();
        }
    }
};
</script>

<style scoped>
/* Add any additional styling specific to this component */
</style>