// services/imageService.js

import axios from 'axios';

// Compresses an image if it exceeds the specified maximum size
const compressImage = (file, targetSizeKB = 500, qualityStep = 0.05) => {
  return new Promise((resolve, reject) => {
    if (file.size / 1024 <= targetSizeKB) {
      // If the file size is already <= 500 KB, return it as is
      resolve(file);
      return;
    }

    const reader = new FileReader();
    reader.onload = (event) => {
      const img = new Image();
      img.onload = () => {
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Set canvas dimensions to the original image dimensions
        canvas.width = img.width;
        canvas.height = img.height;
        ctx.drawImage(img, 0, 0, img.width, img.height);

        // Compress with iterative quality adjustment to achieve ~500 KB
        const compressWithQuality = (quality) => {
          canvas.toBlob(
            (blob) => {
              if (!blob) {
                reject(new Error('Compression failed.'));
                return;
              }

              const compressedFileSizeKB = blob.size / 1024;

              if (compressedFileSizeKB <= targetSizeKB || quality <= 0.1) {
                // Resolve with the compressed file if within the target size or quality too low
                const compressedFile = new File([blob], file.name, {
                  type: 'image/jpeg',
                  lastModified: Date.now(),
                });
                resolve(compressedFile);
              } else {
                // Retry with reduced quality
                compressWithQuality(quality - qualityStep);
              }
            },
            'image/jpeg',
            quality
          );
        };

        // Start compression with initial quality
        compressWithQuality(0.9);
      };
      img.src = event.target.result;
    };
    reader.onerror = (error) => reject(error);
    reader.readAsDataURL(file);
  });
};

// Uploads an image to the server
const uploadImage = async (file, type, companyId, link = '') => {
  const formData = new FormData();
  formData.append('image', file);
  formData.append('model', 
    type === 'hero' ? 'website/HeroImage' : 
    type === 'slider' ? 'website/SliderImage' : 
    type === 'services' ? 'website/ServiceImage' : 
    type === 'testimonials' ? 'website/TestimonialImage' : 
    type === 'products' ? 'website/ProductImage' : 
    type === 'gallery' ? 'website/gallery/'+link : 
    type === 'brochure' ? 'website/BrochurePDF' : type === 'about' ? 'website/AboutImage': type === 'editor' ? 'website/CKEditorImage' : type === 'store' ? 'ecommerce/store' : 'website/Others'
    
  );
  formData.append('company_id', companyId);
  formData.append('link', link);

  try {
    const response = await axios.post('/image', formData);
    return response.data;
  } catch (error) {
    console.error('Error uploading image:', error);
    throw error;
  }
};



// Deletes an image from the server
const deleteImage = async (image_url, type, link= '') => {
  try {
    let model = type === 'hero' ? 'website/HeroImage' : 
    type === 'slider' ? 'website/SliderImage' : 
    type === 'services' ? 'website/ServiceImage' : 
    type === 'testimonials' ? 'website/TestimonialImage' : 
    type === 'products' ? 'website/ProductImage' : 
    type === 'gallery' ? 'website/gallery/'+link : 
                type === 'brochure' ? 'website/BrochurePDF' : type === 'about' ? 'website/AboutImage' : type === 'editor' ? 'website/CKEditorImage'
                : type === 'store' ? 'ecommerce/store' : 'website/Others'
    const response = await axios.delete(`/delete-image`, { data: { image_url, model } });
    return response.data;
  } catch (error) {
    console.error('Error deleting image:', error);
    throw error;
  }
};


const uploadFile = async (file, type, companyId, link = '') => {
    const formData = new FormData();
    formData.append('image', file);
    formData.append('model',   type === 'brochure' ? 'website/Brochure' : 'website/Others');
    formData.append('company_id', companyId);
    formData.append('link', link);
  
    try {
      const response = await axios.post('/image', formData);
      return response.data;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error;
    }
  };

  const deleteFile = async (image_url, model) => {
    try {
      const response = await axios.delete(`/delete-image`, { data: { image_url, model } });
      return response.data;
    } catch (error) {
      console.error('Error deleting image:', error);
      throw error;
    }
};
const compressVideo = (file) => {  
  return new Promise((resolve, reject) => {
    const video = document.createElement("video");
    video.preload = "metadata";
    video.src = URL.createObjectURL(file);

    video.onloadedmetadata = async () => {
      URL.revokeObjectURL(video.src);

      const videoDuration = Math.min(video.duration, 60); // Crop to 1 minute max
      const canvas = document.createElement("canvas");
      const context = canvas.getContext("2d");

      // Set target dimensions for compression
      const targetWidth = 640; // Adjust target resolution
      const targetHeight = (video.videoHeight / video.videoWidth) * targetWidth;
      canvas.width = targetWidth;
      canvas.height = targetHeight;

      const chunks = [];
      const stream = canvas.captureStream();
      const recorder = new MediaRecorder(stream);

      recorder.ondataavailable = (event) => chunks.push(event.data);
      recorder.onstop = () => {
        const blob = new Blob(chunks, { type: "video/mp4" });
        if (blob.size > 5 * 1024 * 1024) {
          resolve(null); // Video still too large
        } else {
          resolve(new File([blob], file.name, { type: "video/mp4" })); // Return cropped and compressed video
        }
      };

      recorder.start();

      let currentTime = 0;
      const interval = 100; // Capture frames every 100ms

      const captureFrame = () => {
        if (currentTime < videoDuration) {
          video.currentTime = currentTime;
          video.onseeked = () => {
            context.drawImage(video, 0, 0, targetWidth, targetHeight);
            currentTime += interval / 1000; // Move to the next frame
            captureFrame();
          };
        } else {
          recorder.stop();
        }
      };

      captureFrame();
    };

    video.onerror = () => reject("Failed to load video for compression.");
  });
};

export default {
  compressImage,
  uploadImage,
  deleteImage,
  uploadFile,
  deleteFile,
  compressVideo
};
