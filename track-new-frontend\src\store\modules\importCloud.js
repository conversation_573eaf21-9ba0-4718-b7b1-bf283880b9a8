// store/modules/importCloud.js
import axios from "axios";

const state = {
  category_list: {},
  };

  const mutations = {
      SET_CATEGORYSLIST(state, { data}) {
        //   console.log(data, 'data', pagination, 'pagination', status_counts, 'status');
          state.category_list = data;
    },
      RESET_STATE(state) {
          state.category_list = {};
      }

  };

  const actions = {
    updateCategoryName({ commit }, category_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update category_list name
      setTimeout(() => {
        // Commit mutation to update category_list name
        commit('SET_CATEGORYSLIST', category_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchCategoryList({ commit }) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get('/service_forms', { params: { company_id: company_id, page: 1, per_page: 'all' } })
            .then(response => {
              // Handle response
              console.log(response.data, 'Import Category list..!');
              if (response.data.data && response.data.data.length > 0) {
                const getArray = response.data.data.map(opt => 
                  typeof opt.form === 'string' ? {...opt, form: JSON.parse(opt.form)} :{ ...opt, form: opt.form}
                );
                commit('SET_CATEGORYSLIST', { data: getArray });
                return getArray;
              } else {
                const { data } = response.data;
                commit('SET_CATEGORYSLIST', { data });
                return data;
              }
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    
  };

  const getters = {
    currentCategoryList(state) {
      return state.category_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
