<template>
    <div class="flex h-screen relative">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mb-[60px] mt-[57px]': isMobile, 'mt-[60px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" :servicesList="dataFromChild" @searchData="getFiteredDataList">
            </headbar> -->

            <!-- services home -->
            <div class="relative">
                <bannerDesign :move_bottom="true"></bannerDesign>
                <serviceCategories @dataToParent="handleDataFromChild" :searchedData="getFilteredData"
                    :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </serviceCategories>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/categories/home/<USER>';
import serviceCategories from '../supporting/categories/home/<USER>';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';
import bannerDesign from '../supporting/banner/bannerDesign.vue';

export default {
    name: 'categories',
    components: {
        // sidebar,
        // headbar,
        serviceCategories,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            showViewCustomer: false,
            viewCustomerData: null,
            route_item: 3,
            dataFromChild: [],
            getFilteredData: [],
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Service / Customer Categories List';
        const pageDescription = 'Organize and manage comprehensive lists of service and customer categories, streamlining access and enhancing service delivery';
        useMeta(pageTitle, pageDescription);
        return { pageTitle };
    },
    methods: {
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        showViewCustomerComponent(data) {
            // console.log(data, 'what about data..!');
            this.showViewCustomer = true;
            this.viewCustomerData = data;
        },
        // Method to go back to home
        goBackToHome() {
            this.showViewCustomer = false;
        },

        handleDataFromChild(data) {
            // console.log(data, 'WWWW');
            this.dataFromChild = data;
        },
        getFiteredDataList(data) {
            if (data) {
                // console.log(data, 'WWWW');
                this.getFilteredData = data;
            }
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }

    },
    mounted() {
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
        let find_query = this.$route.query.page;
        if (find_query !== undefined) {
            this.route_item = (find_query === 'services' ? 3 : 4);
        }
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>