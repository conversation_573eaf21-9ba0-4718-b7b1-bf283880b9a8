<template>
    <div>
        <div v-if="!open_skeleton && data.length > 0" class="m-2">
            <div
                :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                <!--Table view-->
                <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                    <table class="table w-full">
                        <thead>
                            <tr class="border-b border-gray-400">
                                <!--dynamic-->
                                <th v-for="(column, index) in dynamicFields" :key="index"
                                    :class="{ 'hidden': !column.visible }" class="px-2 py-2 text-left">
                                    <p>{{ column.label }}</p>
                                </th>
                                <th class="px-2 py-2 leading-none">
                                    Actions
                                </th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for="(record, index) in data" :key="index"
                                class="border-b border-gray-400 hover:bg-gray-200 cursor-pointer">
                                <td v-for="(column, colIndex) in columns" :key="colIndex"
                                    :class="{ 'hidden': !column.visible }" class="px-2 py-2" @click="startEdit(record)">
                                    <span
                                        v-if="!Array.isArray(record[column.field]) && column.field !== 'title' && column.field !== 'lead_type' && column.field !== 'lead_status' && column.field !== 'assign_date' && column.field !== 'lead_date' && column.field !== 'follow_up' && column.field !== 'customer'">
                                        <template v-if="column.field !== 'created_by' && column.field !== 'updated_by'">
                                            {{ record[column.field] }}
                                        </template>
                                        <template v-if="column.field === 'created_by' || column.field === 'updated_by'">
                                            {{ record[column.field].name ? record[column.field].name : '' }}
                                        </template>
                                    </span>
                                    <span v-if="column.field === 'title'" class="hover:text-blue-500 cursor-pointer">{{
                                        record[column.field] }}</span>
                                    <span v-if="column.field === 'lead_type'">{{ record[column.field].name }}</span>
                                    <span v-if="column.field === 'lead_status'"
                                        class=" px-1 text-white py-1 rounded  cursor-pointer text-xs"
                                        :class="{ 'bg-yellow-500': record['lead_status'] == '0', 'bg-[#0D7CBF]': record['lead_status'] == '1', 'bg-green-500': record['lead_status'] == '2', 'bg-[#DA1C1C]': record['lead_status'] == '3', 'bg-[#8D9689]': record['lead_status'] == '4' }">
                                        {{ record[column.field] == '0' ? 'Open' : record[column.field] == '1' ?
                                            'Progress' :
                                            record[column.field] == '2' ? 'Completed' : record[column.field] == '3' ?
                                                'Cancelled' : record[column.field] == '4' ? 'Hold' : '' }}</span>
                                    <span v-if="column.field === 'assign_date' || column.field === 'lead_date'"
                                        :title="formattedDate(record[column.field])">
                                        {{ column.field === 'assign_date' ?
                                            formatAssigndate(record[column.field]) :
                                            calculateDaysAgo(formattedDate(record[column.field])) }}</span>
                                    <span v-if="Array.isArray(record[column.field]) && column.field !== 'follow_up'">
                                        {{record[column.field].map((opt) => opt.name).join(', ')}}
                                    </span>
                                    <span v-if="column.field === 'customer'"
                                        class="text-sky-700 hover:underline cursor-pointer"
                                        @click="viewRecordCustomer(record[column.field])">
                                        {{ record[column.field].first_name + ' ' + record[column.field].last_name +
                                            ' - ' + record[column.field].contact_number }}</span>
                                    <span class="hover:text-blue-500 cursor-pointer"
                                        v-if="column.field === 'follow_up' && (record['lead_status'] === 0 || record['lead_status'] === 1)"
                                        :class="getDateStatusClass(record[column.field])">
                                        {{ record['lead_status'] === 0 || record['lead_status'] === 1 ?
                                            getFollowUpDate(record[column.field]) : record['lead_status'] === 2 ?
                                                'Lead Completed' : record['lead_status'] === 3 ? 'Lead Cancelled' :
                                                    'Lead Holded' }}
                                    </span>
                                    <span
                                        v-if="column.field === 'follow_up' && record['lead_status'] !== 0 && record['lead_status'] !== 1"
                                        :class="{ 'text-green-700': record['lead_status'] === 2, 'text-red-700': record['lead_status'] === 3, 'text-gray-500': record['lead_status'] === 4 }">
                                        {{ record['lead_status'] === 0 || record['lead_status'] === 1 ?
                                            getFollowUpDate(record[column.field]) : record['lead_status'] === 2 ?
                                                'Lead Completed' : record['lead_status'] === 3 ? 'Lead Cancelled' :
                                                    'Lead Holded' }}
                                    </span>
                                </td>
                                <td class="py-1">
                                    <div class="flex justify-center">
                                        <div class="flex relative">
                                            <button v-if="!record.editing" @click="startEdit(record)" title="Edit"
                                                class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" />
                                            </button>
                                            <button v-if="!record.editing && checkRoles(['admin'])" title="Delete"
                                                @click="confirmDelete(index)"
                                                class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                    style="color: #ef4444" />
                                            </button>
                                            <!-- <button @click.stop="displayAction(index)" class="px-1 py-1">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                    class="bg-blue-100 text-blue-800 px-2 py-1 rounded" />
                                            </button> -->
                                        </div>
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 mt-7 absolute bg-white divide-y divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing" @click="startEdit(record)"
                                                        class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li class="hover:bg-gray-200">
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                        <span class="px-2">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--card view-->
                <div v-if="items_category === 'list'" class="grid grid-cols-1 custom-scrollbar-hidden" :class="{
                    'sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4': showContactInfo == undefined,
                    'sm:grid-cols-1  lg:grid-cols-2 xl:grid-cols-2 2xl:grid-cols-3 gap-2 lg:gap-4': showContactInfo == false,
                    'sm:grid-cols-1  lg:grid-cols-1 xl:grid-cols-1 2xl:grid-cols-2 gap-2 lg:gap-4': showContactInfo
                }">
                    <div v-for="(record, index) in data" :key="index" class="w-full">
                        <div
                            class="bg-white rounded-lg border border-gray-300 overflow-hidden relative w-full shadow-lg">
                            <!-- Top Section -->
                            <div class="flex justify-between items-center p-2">
                                <!-- Left Side -->
                                <div class="text-xs text-red-400" :title="formattedDate(record['lead_date'])">
                                    <p>{{ calculateDaysAgo(formattedDate(record['lead_date'])) }}</p>
                                </div>
                                <div class="text-xs px-3 py-1 rounded flex items-center cursor-pointer"
                                    @click="startEdit(record)" v-if="record['lead_status'] >= 0"
                                    :class="{ 'bg-yellow-100 text-yellow-800': record['lead_status'] == '0', 'bg-blue-200 text-blue-900': record['lead_status'] == '1', 'bg-green-200 text-green-900': record['lead_status'] == '2', 'text-red-800 bg-red-100': record['lead_status'] == '3', 'text-gray-800 bg-gray-100': record['lead_status'] == '4' }">
                                    <span>
                                        {{ record['lead_status'] == '0' ? 'Open' : record['lead_status'] == '1' ?
                                            'Progress'
                                            :
                                            record['lead_status'] == '2' ? 'Completed' : record['lead_status'] == '3' ?
                                                'Cancelled' :
                                                record['lead_status'] == '4' ? 'Hold' : '' }}</span>
                                </div>
                                <!-- Right Side -->
                                <div class="flex items-center px-2">
                                    <span class="bg-gray-100 text-gray-600 text-xs font-semibold px-2 py-1 rounded">{{
                                        record['lead_type']
                                            ? record['lead_type'].name : '' }}</span>
                                    <!--menu-->
                                    <div class="font-semibold  ml-2 rounded relative">
                                        <button @click.stop="displayAction(index)">
                                            <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                class="bg-blue-100 text-blue-800 px-2 py-1 rounded" />
                                        </button>
                                        <!--dropdown-->
                                        <div v-if="display_option === index" :ref="'dropdown' + index"
                                            class="z-10 text-sm absolute mt-3 right-0 bg-white divide-y divide-gray-100 rounded-lg items-center shadow-lg"
                                            style="display: flex; justify-content: center; align-items: center;">
                                            <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                <li>
                                                    <button v-if="!record.editing" @click="startEdit(record)"
                                                        class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-solid fa-pencil" size="lg"
                                                            style="color: #3b82f6;" />
                                                        <span class="px-2">Edit</span>
                                                    </button>
                                                </li>
                                                <li>
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can" size="lg"
                                                            style="color: #ef4444" />
                                                        <span class="px-2 ">Delete</span>
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Customer Details and Lead Status -->
                            <div class="text-gray-900 px-2 py-1">
                                <!-- <div class="flex items-center mb-2">
                                <div class="h-12 w-12 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                    :class="{ 'bg-gray-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-blue-600': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                    {{ record.customer && record.customer.first_name ?
                                        record.customer.first_name[0].toUpperCase() :
                                        'C' }}
                                </div>
                                <div>
                                    <h4 class="text-sm leading-6 font-semibold text-gray-900 mb-1">{{
                                        record.customer && record.customer.first_name ? record.customer.first_name
                                            + ' ' + (record.customer.last_name ? record.customer.last_name : '') : '' }}
                                    </h4>
                                    <p class="text-sm text-gray-500 cursor-pointer"
                                        @click="dialPhoneNumber(record['customer'].contact_number)">+91
                                        - {{
                                            record.customer.contact_number }}</p>
                                </div>
                            </div> -->
                                <div class="grid grid-cols-3 gap-1">
                                    <div class="col-span-2 border-l-4 border-r-4 px-1">
                                        <h3 class="text-sm font-semibold cursor-pointer line-clamp-1"
                                            @click="startEdit(record)">{{
                                                record['title'] }}</h3>
                                        <p class="text-sm line-clamp-1"><span>{{ record['description'] ?
                                            record['description']

                                            : '-' + ' ' + '-' + ' ' + '-'
                                                }}</span></p>
                                    </div>
                                    <div class="block text-xs">
                                        <h4 class="font-bold text-gray-800">Next Follow Date:</h4>
                                        <p class="flex items-center"
                                            v-if="Array.isArray(record['follow_up']) && record['follow_up'].length > 0 && (record['lead_status'] === 0 || record['lead_status'] === 1)">
                                            <span @click="startEdit(record)" class="hover:text-blue-500 cursor-pointer"
                                                v-if="record['lead_status'] === 0 || record['lead_status'] === 1"
                                                :class="getDateStatusClass(record['follow_up'])">
                                                {{ record['lead_status'] === 0 || record['lead_status'] === 1 ?
                                                    getFollowUpDate(record['follow_up']) : record['lead_status'] === 2 ?
                                                        'Lead Completed' : record['lead_status'] === 3 ? 'Lead Cancelled' :
                                                            'Lead Holded' }}
                                            </span>
                                        </p>
                                        <p v-else class="flex items-center">{{ '-' + ' ' + '-' + ' ' + '-' }}</p>
                                    </div>
                                </div>
                            </div>

                            <!-- Assign To -->
                            <div class="px-2 py-2">
                                <div class="relative">
                                    <!-- First Circle -->
                                    <div class="absolute  left-1 z-0">
                                        <i class="fas fa-user-circle text-blue-300 text-lg"></i>

                                    </div>
                                    <!-- Second Circle -->
                                    <div class="absolute  left-4 z-[1]">
                                        <i class="fas fa-user-circle text-red-300 text-lg"></i>
                                    </div>
                                    <!-- Third Circle -->
                                    <div class="absolute  left-7 z-[2]">
                                        <i class="fas fa-user-circle text-purple-300 text-lg"></i>
                                    </div>
                                </div>

                                <div class="flex flex-wrap gap-2 ml-[50px]">
                                    <p v-if="Array.isArray(record['assign_to']) && record['assign_to'].length > 0"
                                        v-for="(opt, i) in record['assign_to']" :key="i">
                                        <span v-if="record['assign_to'].length > 0"
                                            class="inline-flex items-center px-3 py-1 rounded mr-2"
                                            :class="{ 'bg-blue-100 text-blue-800': i === 0 || i % 3 === 0, 'bg-blue-100 text-blue-300': i % 2 === 0, 'bg-green-100 text-green-800': (i === 1 || i % 1 === 0) && i % 2 !== 0 && i % 3 !== 0 }">
                                            {{ opt.name }}
                                        </span>
                                    </p>
                                    <p v-else class="inline-flex items-center px-1 py-2">- -</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--no data found-->
                    <div class="text-sm mb-[100px]">
                        <p class="font-bold text-center py-2 text-green-700">
                            <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                            Finished !
                        </p>
                    </div>
                </div>
            </div>
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
            <Loader :showModal="open_loader"></Loader>
        </div>
    </div>
</template>
<script>
import { mapState, mapActions, mapGetters } from 'vuex';
export default {
    props: {
        isMobile: Boolean,
        data: Object,
        companyId: String,
        userId: String,
        selected_category: String,
        items_category: String,
        open_skeleton: Boolean,
        now: Date,
        confirm_del: Boolean,
        updateModalOpen: Boolean,
        showContactInfo: Boolean
    },

    data() {
        return {
            display_option: false,
            deleteIndex: null,
            open_loader: false,
            empty_data: '/images/dashboard/empty.svg',
        }
    },
    computed: {
        dynamicFields() {
            const fields = [];
            let order = [];
            if (this.selected_category === 'services') {
                order = ['created_at', 'problem_title', 'category', 'expected_date', 'service_code', 'assign_to', 'service_type', 'invoice_id', 'status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'leads') {
                order = ['lead_date', 'title', 'assign_to', 'assign_date', 'lead_type', 'follow_up', 'lead_status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'amcs') {
                order = ['created_at', 'title', 'assign_to', 'amc_payment_type', 'amc_details', 'number_of_interval', 'number_of_service', 'amc_status', 'created_by', 'updated_by'];
            } else if (this.selected_category === 'sales') {
                order = ['current_date', 'invoice_no', 'invoice_to', 'invoice_type', 'discount', 'due_amount', 'discount_type', 'shipping'];
            } else if (this.selected_category === 'estimations') {
                order = ['current_date', 'estimate_num', 'estimate_type', 'grand_total', 'invoice_id'];
            }

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data && this.data.length > 0) {
                order.forEach((key) => {
                    // Check if the key exists in the data object
                    if (this.data && this.data.length > 0) { //---&& this.data[0].hasOwnProperty(key)
                        const label = formatLabel(key);
                        if (key !== 'created_by' && key !== 'updated_by') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                });
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
        ...mapGetters('localStorageData', ['currentLocalDataList']),
    },
    mounted() {
        this.fetchLocalDataList();
    },
    methods: {
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //--format date    
        formatDate(dateString) {
            if (!dateString || typeof dateString !== 'string') {
                return '';
            }
            // Parse the input date string as a Date object
            const dateObject = new Date(dateString);

            if (isNaN(dateObject.getTime())) {
                return ''; // Return empty string if parsing fails
            }

            // Define an array of month names
            const monthNames = ["January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"];

            // Extract day, month, and year components from the date object
            const day = dateObject.getDate();
            const monthIndex = dateObject.getMonth(); // Months are zero-based (0 = January)
            const year = dateObject.getFullYear();

            // Format the date as "Month day, year"
            const formattedDateData = `${monthNames[monthIndex]} ${day}, ${year}`;

            return formattedDateData;
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        //---formated display date---
        formatAssigndate(timestamp) {
            const date = new Date(timestamp);
            // Format the date as "DD-MM-YYYY"
            const formattedDate = `${date.getUTCDate().toString().padStart(2, '0')}-${(date.getUTCMonth() + 1).toString().padStart(2, '0')}-${date.getUTCFullYear()}`;

            return formattedDate;
        },
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },

        //----services data--- 
        //--image
        hasDocument(record) {
            return (
                record.document &&
                Array.isArray(JSON.parse(record.document)) &&
                JSON.parse(record.document).length > 0
            );
        },
        getLastDocumentUrl(record) {
            if (this.hasDocument(record)) {
                const documents = JSON.parse(record.document);
                return documents[documents.length - 1].url;
            }
            return null;
        },
        //---formated display date---
        formattedDate(timestamp) {
            //---formatted display date---
            const date = new Date(timestamp);
            date.setHours(date.getHours() + 5); // Add 5 hours
            date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---record---
        startEdit(record) {
            this.$router.push({
                name: 'leadEdit',
                query: {
                    recordId: record.id
                }
            });
        },
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                this.open_loader = true;
                axios.delete(`/leads/${this.data[this.deleteIndex].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // console.log(response.data, 'What happening...!!!!');
                        this.open_loader = false;
                        this.deleteIndex = null;
                        window.location.reload();
                    })
                    .catch(error => {
                        this.open_loader = false;
                        console.error('Error', error);
                    })
            }

        },

        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.$emit('openconfirmbox', index);
        },
        cancelDelete() {
            this.deleteIndex = null;
        },
        getDateStatusClass(fieldValue) {
            try {
                const parsedValue = typeof fieldValue === 'string' ? JSON.parse(fieldValue) : fieldValue;
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    const followUpDate = new Date(parsedValue[0].date_and_time);
                    const currentDate = new Date();

                    if (followUpDate < currentDate) {
                        return 'text-red-500';
                    } else {
                        return 'text-green-500';
                    }
                }
            } catch (error) {
                console.error('Error parsing date:', error);
            }
            return ''; // Default empty string if date cannot be parsed
        },
        getFollowUpDate(fieldValue) {
            // console.log(fieldValue, 'EEEEEEEEEEEEEEEEEEEEEEE');
            if (fieldValue !== '' && typeof fieldValue === 'string') {
                const parsedValue = JSON.parse(fieldValue);
                if (Array.isArray(parsedValue) && parsedValue.length > 0 && parsedValue[0].date_and_time) {
                    return this.generateDate(parsedValue[0].date_and_time);
                }
            } else if (typeof fieldValue === 'object') {
                if (Array.isArray(fieldValue) && fieldValue.length > 0 && fieldValue[0].date_and_time) {
                    return this.generateDate(fieldValue[0].date_and_time);
                }
            }
        },
        generateDate(data) {
            // console.log(data, 'What about the data......');
            const dateTimeString = data;
            const date = new Date(dateTimeString);
            const formattedDate = date.toLocaleDateString('en-GB', { year: 'numeric', month: '2-digit', day: '2-digit' });
            const formattedTime = date.toLocaleTimeString('en-US', { hour12: true, hour: '2-digit', minute: '2-digit' });
            return formattedDate + ' ' + formattedTime;
        },
        //--role based on--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
    },
    watch: {
        confirm_del: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.deleteRecord();
                } else {
                    this.cancelDelete();
                }
            }
        }
    }
}
</script>
<style>
.image-container01 {
    width: 150px;
    height: 70px;
    /* Set your desired fixed height */
    object-fit: cover;
    /* Maintain aspect ratio and crop as needed */
    object-position: center;
    border: 1px solid rgb(218, 218, 218);
    /* box-shadow: 1px 1px 2px 2px rgb(82, 81, 81); */
}
</style>