<template>
    <div class="relative bg-white non-printable"
        :class="{ 'p-1 shadow-lg border rounded': !isMobile, 'h-[89px]': isMobile }">
        <div class="overflow-x-auto overflow-hidden custom-scrollbar-hidden"
            :class="{ 'flex flex-row': isMobile, 'flex flex-col mt-2': !isMobile, }">
            <!-- Repeat the following block for each horizontal item -->
            <div v-for="item in horizontalItems" :key="item.id"
                class="flex-shrink-0 flex flex-col cursor-pointer transition-all duration-300 mt-1 px-6 sm:px-2 lg:px-6  overflow-hidden"
                :class="{ 'items-center justify-center': isMobile, 'mb-1': !isMobile }"
                @mouseover="handleMouseOver(item.id)" @click="handleItemClick(item.id)" @mouseout="handleMouseOut">
                <!-- <img class="w-8 h-8 mb-2" :src="item.imageSrc" :alt="item.alt" /> -->
                <font-awesome-icon v-if="isMobile" :icon="item.imageSrc.split(' ')"
                    class="text-xl mb-2 text-[#324148]" />
                <p class="hover:text-blue-700"
                    :class="{ 'text-xs': isMobile, 'text-sm': !isMobile, 'text-green-700 font-bold': item.isClicked }">
                    {{ item.label }}</p>
                <div class="w-full h-2" :class="{ 'bg-green-400 rounded-tl-full rounded-tr-full ': item.isClicked }">
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        isMobile: Boolean,
    },
    data() {
        return {
            horizontalItems: [
                { id: 1, label: 'Company Setting', imageSrc: 'fa-solid fa-gear', alt: 'general', isHovered: false, isClicked: true },
                { id: 14, label: 'Default Setting', imageSrc: 'fa-solid fa-gears', alt: 'default', isHovered: false, isClicked: false },
                { id: 2, label: 'Invoice', imageSrc: 'fa-solid fa-file-invoice-dollar', alt: 'invoice', isHovered: false, isClicked: false },
                { id: 11, label: 'Proforma', imageSrc: 'fa-solid fa-file-invoice', alt: 'invoice', isHovered: false, isClicked: false },
                { id: 12, label: 'Estimation', imageSrc: 'fa-solid fa-calculator', alt: 'invoice', isHovered: false, isClicked: false },
                { id: 13, label: 'Jobsheet / Servicess', imageSrc: 'fa-solid fa-clipboard-list', alt: 'services', isHovered: false, isClicked: false },
                // { id: 3, label: 'SMS Setting', imageSrc: '/images/horizontal_bar/Chat.png', alt: 'sms', isHovered: false, isClicked: false },
                // { id: 4, label: 'WhatsApp Setting', imageSrc: '/images/horizontal_bar/Whatsapp.png', alt: 'whatsapp', isHovered: false, isClicked: false },
                // { id: 5, label: 'Email Setting', imageSrc: '/images/horizontal_bar/Mail.png', alt: 'email', isHovered: false, isClicked: false },
                { id: 6, label: 'Profile Setting', imageSrc: 'fa-regular fa-user', alt: 'account', isHovered: false, isClicked: false },
                // { id: 7, label: 'Employee Management', imageSrc: 'fa-solid fa-user-tie', alt: 'management', isHovered: false, isClicked: false },
                // { id: 8, label: 'Roles', imageSrc: '/images/horizontal_bar/role.png', alt: 'roles', isHovered: false, isClicked: false },
                { id: 9, label: 'Social Media', imageSrc: 'fa-solid fa-hashtag', alt: 'social', isHovered: false, isClicked: false },
                { id: 10, label: 'Feature Settings', imageSrc: 'fa-solid fa-list-check', alt: 'feature setting', isHovered: false, isClicked: false },

            ],
            selectedItem: null,

        };
    },
    name: 'horizontalbar',

    methods: {
        handleMouseOver(itemId) {
            this.horizontalItems.forEach(item => {
                item.isHovered = item.id === itemId;
            });
        },
        handleMouseOut() {
            this.horizontalItems.forEach(item => {
                item.isHovered = false;
            });
        },
        handleItemClick(itemId) {
            this.horizontalItems.forEach(item => {
                item.isClicked = item.id === itemId;
                // Store the selected item
                this.selectedItem = itemId;
                // Emit an event to notify the parent component
                this.$emit('item-selected', itemId);
            });
        },
    },
    mounted() {
        let findPage = localStorage.getItem('previousView');
        if (findPage) {
            let parseData = JSON.parse(findPage);
            if (parseData) {
                // this.item_isopen = parseData.view;
                this.horizontalItems.forEach(item => {
                    item.isClicked = item.id === parseData.view;
                    // Store the selected item
                    this.selectedItem = parseData.view;
                    // Emit an event to notify the parent component
                    this.$emit('item-selected', parseData.view);
                });
            }
        }
    }
};
</script>
<style scoped>
@media (max-width: 768px) {
    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }

    @media print {

        /* Additional styles for printing */
        body {
            background-color: white;
        }

        .non-printable {
            display: none;
        }
    }
}
</style>
