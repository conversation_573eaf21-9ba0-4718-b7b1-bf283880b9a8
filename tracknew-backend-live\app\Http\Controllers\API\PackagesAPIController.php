<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreatePackagesAPIRequest;
use App\Http\Requests\API\UpdatePackagesAPIRequest;
use App\Models\Packages;
use App\Repositories\PackagesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class PackagesController
 * @package App\Http\Controllers\API
 */

class PackagesAPIController extends AppBaseController
{
    /** @var  PackagesRepository */
    private $packagesRepository;

    public function __construct(PackagesRepository $packagesRepo)
    {
        $this->packagesRepository = $packagesRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/packages",
     *      summary="getPackagesList",
     *      tags={"Packages"},
     *      description="Get all Packages",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Packages")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $packages = $this->packagesRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($packages->toArray(), 'Packages retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/packages",
     *      summary="createPackages",
     *      tags={"Packages"},
     *      description="Create Packages",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Packages"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreatePackagesAPIRequest $request)
    {
        $input = $request->all();

        $packages = $this->packagesRepository->create($input);

        return $this->sendResponse($packages->toArray(), 'Packages saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/packages/{id}",
     *      summary="getPackagesItem",
     *      tags={"Packages"},
     *      description="Get Packages",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Packages",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Packages"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Packages $packages */
        $packages = $this->packagesRepository->find($id);

        if (empty($packages)) {
            return $this->sendError('Packages not found');
        }

        return $this->sendResponse($packages->toArray(), 'Packages retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/packages/{id}",
     *      summary="updatePackages",
     *      tags={"Packages"},
     *      description="Update Packages",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Packages",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Packages"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdatePackagesAPIRequest $request)
    {
        $input = $request->all();

        /** @var Packages $packages */
        $packages = $this->packagesRepository->find($id);

        if (empty($packages)) {
            return $this->sendError('Packages not found');
        }

        $packages = $this->packagesRepository->update($input, $id);

        return $this->sendResponse($packages->toArray(), 'Packages updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/packages/{id}",
     *      summary="deletePackages",
     *      tags={"Packages"},
     *      description="Delete Packages",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Packages",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Packages $packages */
        $packages = $this->packagesRepository->find($id);

        if (empty($packages)) {
            return $this->sendError('Packages not found');
        }

        $packages->delete();

        return $this->sendSuccess('Packages deleted successfully');
    }
}
