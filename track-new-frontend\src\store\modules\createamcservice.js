import axios from "axios";

const state = {
    amc_category: {},
    lastFetchTime: null,
  isFetching: false,
    amc_data: null,
    formfields: [{
        id: 1,
        editData: "Dropdown", fieldKey: "customer", lableName: "Customer",
        required: "yes", type: "single", enable: true, edit: false, image: '/images/service_page/User.png', dropdownOpen: false, place: 'div1'},
    { id: 2, editData: 'Radio', lableName: 'Support type', option: ['Carry in', 'Pickup', 'Onsite', 'Remote', 'Installation', 'Warranty', 'Courtesy', 'AMC', 'NC AMC', 'Materials Supply', 'In Home', 'Others'], option_status: [true, true, true, true, false, true, false, true, true, false, false, false], fieldKey: "service_type", type: 'radio', required: 'no', enable: true, edit: true, image: '/images/service_page/seviceIcon.png', dropdownOpen: false, place: 'div1' },
    { id: 22, editData: 'Radio', lableName: 'Service Type', option: ['Paid', 'Free'], fieldKey: "warranty_type", type: 'radio', required: 'no', enable: true, edit: true, image: '/images/service_page/Money.png', dropdownOpen: false, place: 'div1' },
    //---div 01----
    { id: 3, editData: 'Dropdown', lableName: 'Brand', option: [], fieldKey: "brand", type: 'single', required: 'no', enable: true, edit: true, image: '/images/service_page/brand.png', dropdownOpen: false, place: 'div01' },
    { id: 4, editData: 'Dropdown', lableName: 'Device Model', option: [], fieldKey: "device_model", type: 'single', required: 'no', enable: true, edit: true, image: '/images/service_page/device_model.png', dropdownOpen: false, place: 'div01' },
    { id: 5, editData: 'Text', lableName: 'Serial Number', placeholderText: 'Enter device serial number', fieldKey: 'serial_number', type: 'text', required: 'no', enable: true, edit: true, image: '/images/service_page/serial_number.png', dropdownOpen: false, place: 'div01' },
    { id: 6, editData: 'Dropdown', lableName: 'Problem Title', option: [], fieldKey: "problem_title", type: 'multiple', required: 'no', enable: true, edit: true, image: '/images/service_page/issue_title.png', dropdownOpen: false, place: 'div01' },
    { id: 7, editData: 'Notes', lableName: 'Problem Report By Customer', placeholderText: 'Enter some data', fieldKey: 'problem_description', rowsCount: 3, colsCount: 10, required: 'no', enable: true, edit: true, image: '/images/service_page/issue_description.png', dropdownOpen: false, place: 'div01' },
      //--div2 custom fields---
    // { id: 26, editData: 'Text', lableName: 'Service Booked By', placeholderText: 'Enter the name of the service creator', fieldKey: 'booked_by', type: 'text', required: 'no', enable: true, edit: true, image: '/images/service_page/order_taken.png', dropdownOpen: false, place: 'div2' },
    // { id: 27, editData: 'Text', lableName: 'Service Completed / Deliver By', placeholderText: 'Enter the name of the deliverer/completed by', fieldKey: 'deliver_by', type: 'text', required: 'no', enable: true, edit: true, image: '/images/service_page/delivered.png', dropdownOpen: false, place: 'div2' },
    //----div3--
    {id: 8, editData: 'Dropdown', lableName: 'Pre Repair Checklist', fieldKey: 'pre_repair', type: 'single', required: 'no', option: ['display'], image: '/images/service_page/checklist.png', enable: false, dropdownOpen: false, edit: true, place: 'div3', preChecklist: [
            { value: 'yes', label: '✔', color: '#228B22' },
            { value: 'no', label: '✗', color: '#DC143C' },
            { value: 'not_applicable', label: 'N/A', color: '#efe3e6' },
        ]
    },
    { id: 9, editData: 'Device Password', lableName: 'Device Password', placeholderText: 'Enter the device password', fieldKey: "device_password", type: 'text', required: 'no', enable: false, edit: true, image: '/images/service_page/secure.png', dropdownOpen: false, place: 'div3' },
    { id: 20, editData: 'Device Pattern', lableName: 'Device Pattern', placeholderText: 'Enter the device password', fieldKey: "device_pattern", type: 'text', required: 'no', enable: false, edit: true, image: '/images/service_page/secure.png', dropdownOpen: false, place: 'div3' },
    //---div4--
    { id: 10, editData: 'Estimate Amount', lableName: 'Estimate Service Amount', placeholderText: 'Enter service estimate amount', fieldKey: "estimateAmount", type: 'number', required: 'no', enable: true, edit: true, image: '/images/service_page/Money.png', dropdownOpen: false, place: 'div4' },
    { id: 24, editData: 'Discount', lableName: 'Discount', placeholderText: 'Enter discount value', fieldKey: 'discountValue', type: 'number', required: 'no', enable: true, edit: true, image: '/images/service_page/discount.png', dropdownOpen: false, place: 'div4' },
    { id: 11, editData: 'Advance Amount', lableName: 'Paid Amount', placeholderText: 'Enter advance paid amount', fieldKey: 'advanceAmount', type: 'number', required: 'no', enable: true, edit: true, image: '/images/service_page/Zakat.png', dropdownOpen: false, place: 'div4' },
    // { id: 12, editData: 'Service Amount', lableName: 'Service Amount', placeholderText: 'Enter total service amount', fieldKey: 'serviceAmount', type: 'number', required: 'no', enable: true, edit: true, image: '/images/service_page/Money_priority.png', dropdownOpen: false, place: 'div4' },
    //---div5--
    { id: 13, editData: "Dropdown", fieldKey: "assignWork", lableName: "Assign Work", option: [], required: 'no', type: "multiple", enable: true, edit: false, image: '/images/service_page/personService.png', dropdownOpen: false, place: 'div5' },
    { id: 14, editData: 'Radio', lableName: 'Service Priority', option: ['High', 'Medium', 'Low'], fieldKey: "service_priority", type: 'radio', required: 'no', enable: true, edit: true, image: '/images/service_page/prioritize.png', dropdownOpen: false, place: 'div5' },
    { id: 15, editData: 'Notes', lableName: 'Notes To Technician', placeholderText: 'Enter some data', fieldKey: 'notes', rowsCount: 3, colsCount: 10, required: 'no', enable: true, edit: true, image: '/images/service_page/Writing.png', dropdownOpen: false, place: 'div5' },
    { id: 16, editData: 'Date', lableName: 'Expected Completion Date', fieldKey: 'expected_date', type: 'date', required: 'no', enable: true, edit: true, image: '/images/service_page/schedule.png', dropdownOpen: false, place: 'div5' },
    { id: 17, editData: 'File', lableName: 'Product Image', fieldKey: 'document', type: 'file', required: 'no', enable: true, edit: true, image: '/images/service_page/folder.png', dropdownOpen: false, place: 'div5' },
    { id: 18, editData: 'Checkbox', lableName: 'Send Notification', option: ['SMS', 'WhatsApp', 'Email'], type: 'checkbox', fieldKey: 'notification', required: 'no', enable: true, edit: true, image: '/images/service_page/notification.png', dropdownOpen: false, place: 'div5' },
    //---div8---
    { id: 23, editData: "Additional", fieldKey: "additional", lableName: "Additional material/services", required: "no", type: "text", enable: true, edit: true, image: '/images/customer_page/product.png', dropdownOpen: false, place: 'div8' },
    //---div7--
    { id: 21, editData: "Expense", fieldKey: "service_expense", lableName: "Expense Details", required: "no", type: "text", enable: true, edit: true, image: '/images/service_page/Money.png', dropdownOpen: false, place: 'div7' },

    { id: 25, editData: 'Date', lableName: 'Schedule Date', fieldKey: 'schedule_date', type: 'date', required: 'no', enable: true, edit: true, image: '/images/service_page/schedule.png', dropdownOpen: false, place: 'div7' },
    //--div6---
    { id: 19, editData: "Dropdown", fieldKey: "status", lableName: "Status", option: ['Service Taken', 'Hold', 'In-progress', 'New Estimate', 'Ready To Deliver', 'Delivered', 'Cancelled', 'Completed'], option_status: [true, true, true, true, true, true, true, true], required: "yes", type: "single", enable: true, edit: false, image: '/images/service_page/statusIcon.png', dropdownOpen: false, place: 'div6' },
    ],
};  
  const mutations = {
      SET_AMC_CATEGORY(state, data) {           
          state.amc_category = data;
    },
      RESET_STATE(state) {
          state.amc_category= {};
          state.lastFetchTime= null;
        state.isFetching = false;
        state.amc_data = null;
      },
      SET_LAST_FETCH_TIME(state, time) {
        state.lastFetchTime = time; // Save the timestamp when the API was last accessed
      },
      SET_IS_FETCHING(state, status) {
        state.lastFetchTime = status; // Set the API request status (true for ongoing, false for completed)
    },
    RESET_FETCHTIME(state) {
      state.lastFetchTime = null;      
      state.amc_category = {};
      state.isFetching= false;
    },
    SET_AMC_DATA(state, data) {   
      state.amc_data = data;
    },
    RESET_AMC_DATA(state) {
      state.amc_data = null;
    }
};
  function formatDateService(dateString) {
    const inputDate = new Date(dateString);
    const day = inputDate.getUTCDate().toString().padStart(2, '0');
    const month = (inputDate.getUTCMonth() + 1).toString().padStart(2, '0');
    const year = inputDate.getUTCFullYear();

    return `${year}-${month}-${day}`;
};

  const actions = {    
      async fetchAMCCategory({ state, commit, rootState }) {
        const now = new Date().toISOString();
        const thirtySecondsInMilliseconds = 5 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['service_category_update'];
      const serviceCategories_list = rootState.serviceCategories.service_category;
      const serviceCategories_isFetching = rootState.serviceCategories.isFetching;
      const serviceCategories_lastFetchTime = rootState.serviceCategories.lastFetchTime;
      if (serviceCategories_isFetching) {
        return;
      } else if (((serviceCategories_lastFetchTime && new Date(now) - new Date(serviceCategories_lastFetchTime) < thirtySecondsInMilliseconds) || (lastUpdateTime < serviceCategories_lastFetchTime)) && serviceCategories_list) {
        let service_category_list = serviceCategories_list;
                      let find_category = service_category_list.find(opt => opt.service_category === 'AMC_Services');                     
                      if (find_category) {
                        commit('SET_AMC_CATEGORY', find_category);
                      } else {
                        commit('SET_AMC_CATEGORY', {});
                      }                  
                    commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
        commit('SET_IS_FETCHING', false);
        return;
      } 
      
      if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || (lastUpdateTime < state.lastFetchTime)) {
        return; // Skip request if less than 30 seconds have passed since the last request
      }  
        try {
              const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
              if (company_id && company_id !== '') {
                commit('SET_IS_FETCHING', true);
                axios.get('/service_categories', { params: { company_id: company_id, page: 1, per_page: 'all' } })
                  .then(response => {
                      let service_category_list = response.data.data;
                      let find_category = service_category_list.find(opt => opt.service_category === 'AMC_Services');                     
                      if (find_category) {
                        commit('SET_AMC_CATEGORY', find_category);
                      } else {
                        commit('SET_AMC_CATEGORY', {});
                      }                  
                    commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
                    commit('SET_IS_FETCHING', false);
                    return service_category_list;
                  })
                  .catch(error => {
                    // Handle error
                    commit('SET_IS_FETCHING', false);
                    console.error('Error:', error);
                    return error;
                  });
              }  
            } catch (error) {
              commit('SET_IS_FETCHING', false);
              console.error('Error AMC Services:', error);
            }
      }, 
      async createAMCCategory({ state, commit, rootState, dispatch }) {
        try {
            const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
            if (company_id && company_id !== '') {
                axios.post('/service_categories', { service_categories: [{service_category: 'AMC_Services', service_status: '1', form: JSON.stringify(state.formfields), company_id: company_id }], company_id: company_id })
                    .then(response => {
                       // Call another module's action
                        dispatch('apiUpdates/updateKeyWithTime', 'service_category_update', { root: true });
                        if (response.data.data && response.data.data.length > 0) {
                            commit('SET_AMC_CATEGORY', response.data.data[0]);
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);                        
                    })
            }
         } catch (error) {
        commit('SET_IS_FETCHING', false);
        console.error('Error AMC Services:', error);
        }
    },
    resetFetchTime({commit}) {
      commit('RESET_FETCHTIME');
    },
    async getAMCData({ commit }, amc_id) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '' && amc_id) {
          await axios.get(`/amcs/${amc_id}`, { params: { company_id: company_id } })
            .then(response => {
              let record = response.data.data;
              if (typeof record.product_lists === 'string') {
                record.product_lists = JSON.parse(record.product_lists);
              }
              if (typeof record.date_description === 'string') {
                record.date_description = JSON.parse(record.date_description);
                if (typeof record.date_description === 'object' && Array.isArray(record.date_description) === false) {
                  record.date_description = [record.date_description];
                }
                record.date_description = record.date_description.map(opt => {
                  opt.date = formatDateService(opt.date);
                  return opt;
                });
              } else {
                record.date_description = record.date_description.map(opt => {
                  opt.date = formatDateService(opt.date);
                  return opt;
                });
              }
              commit('SET_AMC_DATA', record);
              return record;
            })
            .catch(error => {
              console.error('Error', error);
              this.open_skeleton = false;
            })
        }
        } catch (error) {
          commit('SET_IS_FETCHING', false);
          console.error('Error AMC Services:', error);
        }      
      
    },
    updateAMCData({ commit, state }, data) {
      if (data) {
        commit('SET_AMC_DATA', data);
      }
    },
    resetAMCData({ commit, state }) {
      if (data) {
        commit('RESET_AMC_DATA');
      }
    }
  };

const getters = {
    currentAMCCategory(state) {
        return state.amc_category;
  },
  currenAMCData(state) {
    return state.amc_data;
  }
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
