// store/modules/brandUnitCategoryItem.js
import axios from "axios";

const state = {
    unit: [],
    brand: [],
    category: []
  };

  const mutations = {
      SET_UNITLIST(state, { data}) {
          state.unit = data;
      },
      SET_BRANDLIST(state, { data }) {
          state.brand = data;          
      },
      SET_CATEGORYLIST(state, { data }) {
          state.category = data;
          
      },
      RESET_STATE(state) {
          state.unit = [];
          state.brand = [];
          state.category = [];
      }

  };

  const actions = {
    updateUnitName({ commit }, unitData) {
      // Simulate an asynchronous operation (e.g., API call) to update unit name
      setTimeout(() => {
        // Commit mutation to update unit name
        commit('SET_UNITLIST', unitData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchUnitList({ commit }) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get('/units', { params: { company_id: company_id, page: 1, per_page: 'all' } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Unit list..!');
                let { data, pagination } = response.data;               
              commit('SET_UNITLIST', {data});
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
      },
      async fetchBrandList({ commit }) {
        try {
          const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
          if (company_id && company_id !== '') {
            axios.get('/brands', { params: { company_id: company_id, page: 1, per_page: 'all' } })
              .then(response => {
                // Handle response
                // console.log(response.data, 'brand list..!');
                  let { data, pagination } = response.data;               
                commit('SET_BRANDLIST', {data});
                return data;
              })
              .catch(error => {
                // Handle error
                console.error('Error:', error);
                return error;
              });
          }  
        } catch (error) {
          console.error('Error fetching item list:', error);
        }
      },
      async fetchCategoryList({ commit }) {
        try {
          const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
          if (company_id && company_id !== '') {
            axios.get('/categories', { params: { company_id: company_id, page: 1, per_page: 'all' } })
              .then(response => {
                // Handle response
                // console.log(response.data, 'Category list..!');
                  let { data, pagination } = response.data;               
                commit('SET_CATEGORYLIST', {data});
                return data;
              })
              .catch(error => {
                // Handle error
                console.error('Error:', error);
                return error;
              });
          }  
        } catch (error) {
          console.error('Error fetching item list:', error);
        }
      },
    
  };

  const getters = {
    currentUnitList(state) {
      return state.unit;
      },
      currentBrandList(state) {
        return state.brand;
      },
      currentCategoryList(state) {
        return state.category;
      },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
