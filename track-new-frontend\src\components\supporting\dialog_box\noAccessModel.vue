<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded-xl shadow-lg"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <!-- Close Button -->
            <div class="flex justify-end p-2 text-2xl pb-1">
                <font-awesome-icon icon="fa-regular fa-circle-xmark" color="red" @click="cancel"
                    class="cursor-pointer" />
            </div>

            <!-- Modal Content -->
            <div class="text-center p-4">
                <!-- Header -->
                <h1 class="text-3xl font-extrabold text-gray-900 mb-4">Upgrade Required</h1>

                <!-- Message -->
                <p class="text-md text-gray-600 mb-6">
                    You do not have access to this page based on your current plan. Please upgrade your plan to unlock
                    this feature.
                </p>

                <!-- Action Button -->
                <router-link to="/subscription"
                    class="inline-block px-6 py-3 bg-blue-600 text-white rounded-full hover:bg-blue-700 transition-all duration-200 transform hover:scale-105">
                    Upgrade Plan
                </router-link>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'NoAccessPageModal',
    props: {
        showModal: Boolean
    },
    data() {
        return {
            isOpen: false,
        };
    },
    methods: {
        cancel() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close');
            }, 300); // Wait for the transition before emitting close event
        }
    },
    watch: {
        showModal: {
            deep: true,
            handler(newvalue) {
                setTimeout(() => {
                    this.isOpen = newvalue;
                }, 100); // Delayed transition
            }
        }
    }
};
</script>

<style scoped>
/* Styling for modal */
.bg-white {
    background-color: #ffffff;
}

.text-center {
    text-align: center;
}

.p-8 {
    padding: 2rem;
}

.text-3xl {
    font-size: 1.875rem;
}

.font-extrabold {
    font-weight: 800;
}

.text-gray-900 {
    color: #1f2937;
}

.text-md {
    font-size: 1rem;
}

.text-gray-600 {
    color: #4b5563;
}

.bg-blue-600 {
    background-color: #2563eb;
}

.text-white {
    color: #ffffff;
}

.rounded-full {
    border-radius: 9999px;
}

.transition-all {
    transition: all 0.2s ease-in-out;
}

.transform {
    transform: scale(1);
}

.hover\:scale-105:hover {
    transform: scale(1.05);
}

.shadow-lg {
    box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
}

.scale-100 {
    transform: scale(1);
}

.scale-0 {
    transform: scale(0);
}

.cursor-pointer {
    cursor: pointer;
}

.hover\:bg-blue-700:hover {
    background-color: #1d4ed8;
}

.transition-transform {
    transition: transform 0.3s ease-in-out;
}

/* Hide scrollbar for a cleaner look */
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>
