<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateServiceFormsAPIRequest;
use App\Http\Requests\API\UpdateServiceFormsAPIRequest;
use App\Models\ServiceForms;
use App\Repositories\ServiceFormsRepository;
use App\Http\Resources\api\ServiceFormResource;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class ServiceFormsController
 * @package App\Http\Controllers\API
 */

class ServiceFormsAPIController extends AppBaseController
{
    /** @var  ServiceFormsRepository */
    private $serviceFormsRepository;

    public function __construct(ServiceFormsRepository $serviceFormsRepo)
    {
      
        $this->serviceFormsRepository = $serviceFormsRepo;
    }

    /**
     * Display a listing of the ServiceForms.
     * GET|HEAD /serviceForms
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
      	$companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please Provide Company.', 400);
        }
    
      	$perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);      
      
        $serviceFormQuery = ServiceForms::where('status', 1);

        if ($perPage === 'all') {
            $perPage = $serviceFormQuery->count();
        }     

        $serviceForms = $serviceFormQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);   

        $response = [
            'success' => true,
            'data' => ServiceFormResource::collection($serviceForms), // Get the paginated items
            'pagination' => [
                'total' => $serviceForms->total(),
                'per_page' => $serviceForms->perPage(),
                'current_page' => $serviceForms->currentPage(),
                'last_page' => $serviceForms->lastPage(),
                'from' => $serviceForms->firstItem(),
                'to' => $serviceForms->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }

    /**
     * Store a newly created ServiceForms in storage.
     * POST /serviceForms
     *
     * @param CreateServiceFormsAPIRequest $request
     *
     * @return Response
     */
    public function store(CreateServiceFormsAPIRequest $request)
    {
        $input = $request->all();

        $serviceForms = $this->serviceFormsRepository->create($input);

        return $this->sendResponse($serviceForms->toArray(), 'Service Forms saved successfully');
    }

    /**
     * Display the specified ServiceForms.
     * GET|HEAD /serviceForms/{id}
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        /** @var ServiceForms $serviceForms */
        $serviceForms = $this->serviceFormsRepository->find($id);

        if (empty($serviceForms)) {
            return $this->sendError('Service Forms not found');
        }

        return $this->sendResponse($serviceForms->toArray(), 'Service Forms retrieved successfully');
    }

    /**
     * Update the specified ServiceForms in storage.
     * PUT/PATCH /serviceForms/{id}
     *
     * @param int $id
     * @param UpdateServiceFormsAPIRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateServiceFormsAPIRequest $request)
    {
        $input = $request->all();

        /** @var ServiceForms $serviceForms */
        $serviceForms = $this->serviceFormsRepository->find($id);

        if (empty($serviceForms)) {
            return $this->sendError('Service Forms not found');
        }

        $serviceForms = $this->serviceFormsRepository->update($input, $id);

        return $this->sendResponse($serviceForms->toArray(), 'ServiceForms updated successfully');
    }

    /**
     * Remove the specified ServiceForms from storage.
     * DELETE /serviceForms/{id}
     *
     * @param int $id
     *
     * @throws \Exception
     *
     * @return Response
     */
    public function destroy($id)
    {
        /** @var ServiceForms $serviceForms */
        $serviceForms = $this->serviceFormsRepository->find($id);

        if (empty($serviceForms)) {
            return $this->sendError('Service Forms not found');
        }

        $serviceForms->delete();

        return $this->sendSuccess('Service Forms deleted successfully');
    }
}
