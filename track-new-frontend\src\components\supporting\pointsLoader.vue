<template>
    <div class="loader flex space-x-1 justify-center items-center">
        <div class="w-2 h-2 bg-gray-800 rounded-full animate-bounce"></div>
        <div class="w-2 h-2 bg-gray-800 rounded-full animate-bounce"></div>
        <div class="w-2 h-2 bg-gray-800 rounded-full animate-bounce"></div>
    </div>
</template>
<script>
export default {
    data() {
        return {

        };
    }

};
</script>
<style>
.loader div {
    animation-duration: 0.5s;
}

.loader div:first-child {
    animation-delay: 0.1s;
}

.loader div:nth-child(2) {
    animation-delay: 0.3s;
}

.loader div:nth-child(3) {
    animation-delay: 0.6s;
}
</style>