<template>
    <div>
        <!--search bar-->
        <div class="relative">
            <span v-if="searchQuery !== ''" class="absolute items-center text-red-500 px-3 cursor-pointer text-lg"
                @click="clearSearchQuery()">
                <font-awesome-icon icon="fa-solid fa-xmark" class="items-center" />
            </span>
            <span v-else class="absolute text-gray-400 items-center px-3 text-lg">
                <font-awesome-icon icon="fa-solid fa-magnifying-glass" class="items-center" />
            </span>
            <input type="text" placeholder='Enter Pruchase Code or supplier name or mobile'
                class="border border-gray-300 rounded-lg p-1 focus:border-blue-500 outline-none lg:w-[300px] w-full pl-[35px]"
                v-model="searchQuery" @input="filteredProduct" @change="filteredProduct" ref="searchInput"
                @keydown.enter="handleEnterKey" @keydown.down.prevent="handleDownArrow(filteredItems_list)"
                @keydown.up.prevent="handleUpArrow(filteredItems_list)" />
            <div v-if="showSuggestions" class="absolute mt-0 bg-white border shadow-md w-full"
                :style="{ 'z-index': 999 }">
                <ul style="max-height: 200px;" class="overflow-auto" v-if="searchQuery && searchQuery.length > 2">
                    <li v-for="(product, index) in filteredItems_list" :key="product.id"
                        class="px-2 cursor-pointer py-1" @click="selectProduct(product)"
                        :class="{ 'bg-gray-200': index === selectedIndex }">
                        {{ product.purchase_order + ' - ' + product.supplier_name + ' - ' + product.supplier_contact }}
                    </li>
                    <li v-if="filteredItems_list && filteredItems_list.length === 0 && searchQuery && searchQuery.length > 3"
                        class="py-1 px-2 bg-gray-100 text-gray-400">Item not found
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
<script>
import { mapGetters, mapActions } from 'vuex';
export default {
    props: {
        isMobile: Boolean,
        resetData: Boolean,
    },
    data() {
        return {
            searchQuery: '',
            showSuggestions: false,
            selectedIndex: 0,
            filteredItems_list: []
        };
    },
    computed: {
        ...mapGetters('purchase', ['currentItems']),
        ...mapGetters('supplier', ['currentSupplier']),
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleDocumentClick);
    },

    methods: {
        ...mapActions('purchase', ['fetchItemList']),
        ...mapActions('supplier', ['fetchISupplierList']),
        filteredProduct() {
            if (this.currentItems && this.currentItems.length === 0) {
                this.fetchItemList();
            }
            if (this.currentSupplier && this.currentSupplier.length === 0) {
                this.fetchISupplierList();
            }
            if (this.searchQuery !== '' && this.searchQuery.length > 2) {
                this.showDropdown();
                const query = this.searchQuery.toLowerCase();
                this.filteredItems_list = this.currentItems.filter(product => {
                    // console.log(product, 'What we can do for sales.....!');
                    // const barcode = product.barcodes.barcode.toLowerCase();

                    // const name = product.products.product_name.toLowerCase();
                    let supplier = product.supplier_contact.includes(query);
                    let supplierName = product.supplier_name.toLowerCase().includes(query);
                    let purchase = product.purchase_order.toLowerCase().includes(query);

                    if (supplier || supplierName || purchase) {
                        return product;
                    }

                    // return (
                    //     // barcode.includes(query) || name.includes(query) || (barcode + ' - ' + name).includes(query)
                    //     product
                    // );
                });
            } else {
                this.filteredItems_list = this.currentItems;
            }
        },
        clearSearchQuery() {
            this.searchQuery = '';
            this.$emit('resetData', {});
        },
        //------search bar
        //----dropdown---
        filterProduct() {
            if (this.searchQuery !== '' && this.searchQuery.length > 1) {
                // console.log(this.searchQuery, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            } else {
                this.showSuggestions = false;
            }
        },
        showDropdown() {
            // console.log('What happening go there currentItems......!!!!!!');
            if (this.searchQuery !== '') {
                // Show dropdown on input focus
                this.showSuggestions = true;
                // Add a click event listener to the document to close the dropdown when clicking outside
                document.addEventListener('click', this.handleDocumentClick);
            }
        },
        hideDropdown() {
            // Hide dropdown on input blur
            this.showSuggestions = false;
        },
        selectProduct(product) {
            // Handle the selected customer (e.g., emit an event, update currentItems, etc.)
            // console.log('Selected product:', product);
            // this.searchQuery = product.barcodes.barcode + ' - ' + product.products.product_name; // Set the input value to the selected customer name
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            this.$emit('searchData', product);
            document.removeEventListener('click', this.handleDocumentClick);
        },
        handleDocumentClick(event) {
            try {
                // Close the dropdown when clicking outside the input and dropdown
                const isClickInside = this.$refs.searchInput.contains(event.target);
                if (!isClickInside) {
                    this.hideDropdown();
                }
            } catch (error) {
                this.showSuggestions = false;
            }
        },
        isNumeric(str) {
            // Helper method to check if a string is numeric
            return /^\d+$/.test(str);
        },
        //--on press enter key--
        handleEnterKey() {
            // Check if filteredProductList has at least one item
            if (this.filteredItems_list.length > 0) {
                // Call selectedProductData with the first item in filteredProductList
                this.selectProduct(this.filteredItems_list[this.selectedIndex]);
                this.selectedIndex = 0;
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //---find any one record is editing is true--
        findEditingValue() {
            let findData = this.currentItems.filter((opt) => opt.editing === true);
            if (findData.length !== 0) {
                return true;
            } else {
                return false;
            }
        },
        //---product list
        //---item product name--
        handleProductChange(product_name, index) {
            this.isDropdownOpenProduct = index;
            if (product_name) {
                const enteredProduct = product_name.trim().toLowerCase();

                this.filteredProductList = this.currentItems.filter(opt => {
                    // Check if the entered product name or product code matches any existing product name or product code
                    const nameMatch = opt.product_name.toLowerCase().includes(enteredProduct);

                    return nameMatch;
                });
                // console.log(this.filteredProductList, 'Waht happening.......@@@');
            }
        },

        //---selected product
        selectedProductData(item, index, option) {
            // console.log(option, 'option', index, 'index', 'item', item);
            console.log('selected product...!');
            this.isDropdownOpenProduct = false;
        },
        //----control dropdown--
        //----customer dropdown--
        closeDropdown() {
            // console.log('hello input');
            if (!this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpenProduct = false;
            }
        },
    },
    watch: {
        resetData: {
            deep: true,
            handler(newValue) {
                if (newValue !== undefined) {
                    this.clearSearchQuery();
                }
            }
        }
    }
};
</script>

<style>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}
</style>