<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateEmployeeAPIRequest;
use App\Http\Requests\API\UpdateEmployeeAPIRequest;
use App\Models\Employee;
use App\Models\User;
//use App\Models\Permission;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;
use App\Repositories\EmployeeRepository;
use App\Http\Resources\api\EmployeeResource;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Http\Controllers\AppBaseController;
use Response;
use Auth;

/**
 * Class EmployeeController
 * @package App\Http\Controllers\API
 */

class EmployeeAPIController extends AppBaseController
{
    /** @var  EmployeeRepository */
    private $employeeRepository;

    public function __construct(EmployeeRepository $employeeRepo)
    {
        $this->employeeRepository = $employeeRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/employees",
     *      summary="getEmployeeList",
     *      tags={"Employee"},
     *      description="Get all Employees",
     *      @OA\Parameter(
     *         name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Employee")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if (!$companyId) {
            return $this->sendError('Please provide company.', 400);
        }

        // Fetch admin user and their plan
        $adminUser = User::where('company_id', $companyId)
            ->where('user_type', 'admin')
            ->first();

        if (!$adminUser) {
            return $this->sendError('Admin user not found.', 404);
        }

        $employeeLimit = json_decode($adminUser->plan, true)['employee_limit'] ?? 0;

        // Build the employees query
        $employeesQuery = User::where('company_id', $companyId)        
            ->where('status', 1);

        // Determine the per_page value
        $perPage = $request->query('per_page', 10);
        if ($perPage === 'all' || $employeeLimit === -1) {
            $perPage = $employeesQuery->count();
        } elseif ($employeeLimit > 0) {
            $perPage = $employeeLimit;
        }

        // Apply the employee limit to the query
        if ($employeeLimit > 0) {
            $employeesQuery->take($employeeLimit);
        }

        // Paginate the results
        $employees = $employeesQuery->orderBy('id', 'desc')
            ->paginate($perPage, ['*'], 'page', $request->query('page', 1));

        // Calculate pagination metadata
        $total = $employeeLimit > 0 ? min($employeeLimit, $employees->total()) : $employees->total();
          if($total === 0){
            $lastPage = 1;
          }else{
            $lastPage = max(1, ceil($total / $perPage));
          }


        // Prepare the response
        $response = [
            'success' => true,
            'data' => EmployeeResource::collection($employees),
            'pagination' => [
                'total' => $total,
                'per_page' => $employees->perPage(),
                'current_page' => $employees->currentPage(),
                'last_page' => $lastPage,
                'from' => $employees->firstItem(),
                'to' => $employees->lastItem(),
            ],
        ];

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/employees",
     *      summary="createEmployee",
     *      tags={"Employee"},
     *      description="Create Employee",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Employee")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Employee"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
  

public function store(CreateEmployeeAPIRequest $request)
{
    $input = $request->all();
  	$companyId = $input['company_id'];
  
  
    $adminQuery = User::where('company_id', $companyId)->where('user_type', 'admin')->first();

    if (!$adminQuery) {
        return $this->sendError('Admin user not found.', 404);
    }

 
    $employeeLimits = json_decode($adminQuery->plan, true);
    $employeeLimit = $employeeLimits['employee_limit'] ?? 0;

    // Count current active employees
    $activeEmployeeCount = User::where('company_id', $companyId)
        ->where('user_type', 'employee')
        ->where('status', 1) 
        ->count();

    // Check if adding another employee exceeds the limit
    if ($employeeLimit != -1 && $activeEmployeeCount >= $employeeLimit) {
        return response()->json([
            'success' => false,
            'message' => 'Employee limit exceeded. Please upgrade your plan.',
        ], 403); // HTTP 403 Forbidden
    }
  
  	 	
    
    if (Employee::where('email', $input['email'])->where('company_id', $input['company_id'])->exists()) {
        return $this->sendError('Email already exists', 400); 
    }
   
    if (!isset($input['company_id']) || empty($input['company_id'])) {
        return $this->sendError('Company ID is required', 400); 
    }
    
    $user_role = Role::where(['id' => $input['role_id']])->first();
   
    if (!$user_role) {
        return $this->sendError('Role not found', 404); 
    }
    
    $inputData = [
        "address" => isset($input['address']) && $input['address'] !== '' ? $input['address'] : NULL,
        "password" => bcrypt($input['confirm_password']),
        "dob" => isset($input['dob']) && $input['dob'] !== '' ? $input['dob'] : NULL,
        "email" => $input['email'],
        "mobile_number" => $input['mobile_number'],
        "name" => $input['name'],
        "notes" => isset($input['notes']) && $input['notes'] !== '' ? $input['notes'] : NULL,
        "proof" => isset($input['proof']) && $input['proof'] !== '' ? $input['proof'] : NULL,
        "skills" => isset($input['skills']) && $input['skills'] !== '' ? $input['skills'] : NULL,
        "status" => $input['status'],
        "total_experience" => isset($input['total_experience']) && $input['total_experience'] !== '' ? $input['total_experience'] : NULL,
        "user_name" => $input['name'],
        "avatar" => isset($input['avatar']) && $input['avatar'] !== '' ? $input['avatar'] : null,
        'company_id' => $input['company_id'],
        "user_type" => 'employee'
    ];
    
    $employee = User::create($inputData);
    //  $employee->assignRole($user_role);

    // foreach ($user_role->permissions as $permission) {
    //     $employee->permissions()->attach($permission->id);
    // }
    
    
    
         
                if($user_role){
        
                    $employee->assignRole($user_role);
                    
            //                          // Retrieve permissions associated with the assigned role
            //         $permissions = $user_role->permissions()->pluck('id')->toArray();
        
            // // Attach retrieved permissions to the employee
            // $employee->permissions()->sync($permissions);
                        
                }
       // $permissions = Permission::pluck('id')->all(); // Get all permission names
      
        
        // foreach ($permissions as $permissionName) {
            
        //     $employee->permissions()->attach($permissionName);
           
        //   // $user->givePermissionTo($permissionName);
        // }

    return $this->sendResponse(new EmployeeResource($employee), 'Employee saved successfully');
}




    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/employees/{id}",
     *      summary="getEmployeeItem",
     *      tags={"Employee"},
     *      description="Get Employee",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Employee",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Employee"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Employee $employee */
        $employee = $this->employeeRepository->find($id);

        if (empty($employee)) {
            return $this->sendError('Employee not found');
        }

        return $this->sendResponse(new EmployeeResource($employee), 'Employee retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/employees/{id}",
     *      summary="updateEmployee",
     *      tags={"Employee"},
     *      description="Update Employee",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Employee",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Employee")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Employee"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
public function update($id, UpdateEmployeeAPIRequest $request)
{
    // Retrieve input data from the request
    $input = $request->all();
    
   


    // Find the employee by ID
    $employee = User::find($id);
    
    if (isset($input['confirm_password']) && $input['confirm_password'] != NULL) {
            $input['password'] =  bcrypt($input['confirm_password']);
        }

    // If the employee doesn't exist, return an error
    if (!$employee) {
        return $this->sendError('Employee not found');
    }

    // Check if there's a change in the role
    if (isset($input['role_id'])) {
           // Find the new role by ID
        $new_role = Role::find($input['role_id']);
    
        // If the new role exists, assign it to the employee
        if ($new_role) {
            // Detach all current roles
            $employee->roles()->detach();
    
            // Assign the new role
            $employee->assignRole($new_role);
             // Retrieve permissions associated with the assigned role
            // $permissions = $new_role->permissions()->pluck('id')->toArray();
        
            // // Attach retrieved permissions to the employee
            // $employee->permissions()->sync($permissions);
        } else {
            // If the role does not exist, return an error
            return $this->sendError('Role not found', [], 404);
        }
    }
    
    
  

    // Sync permissions with the user, adding any new permissions while keeping existing ones
    

    // Remove role_id from input as it's already processed
    unset($input['role_id']);

    // Update the employee with the remaining input data
    $employee->update($input);

    // Return success response
    return $this->sendResponse(new EmployeeResource($employee), 'Employee updated successfully');
}




    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/employees/{id}",
     *      summary="deleteEmployee",
     *      tags={"Employee"},
     *      description="Delete Employee",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Employee",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy(Request $request, $id)
    {
        
        
        
        $user = Auth::user();
        $companyId = $request->input('company_id');
        if($user->company_id == $companyId){
        /** @var Employee $employee */
            $employee = $this->employeeRepository->find($id);
            
         
    
            if (empty($employee)) {
                return $this->sendError('Employee not found');
            }
    
            $employee->delete();
    
            return $this->sendSuccess('Employee deleted successfully');
        }
        else{
            
            return $this->sendError('Employee delete Failed');
            
        }
    }
}
