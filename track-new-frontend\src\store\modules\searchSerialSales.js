// store/modules/searchSerialSales.js
import axios from "axios";

const state = {
  sales_list: {data: [],
    pagination: null,
        status_counts: null
    },
  };

  const mutations = {
      SET_SALESLIST(state, { data, pagination, status_counts }) {
        const currentData = state.sales_list.data || [];
          if (pagination && pagination.current_page > 1) {
              // Append new data if the current page is greater than 1
              state.sales_list = {
                  data: [...currentData, ...data], // Append new data
                  pagination: pagination,
                  status_counts: status_counts,
              };
          } else {
              // Replace data if it's the first page or no pagination
              state.sales_list = {
                  data: data, // Replace with new data
                  pagination: pagination,
                  status_counts: status_counts,
              };
          }
    },
      RESET_STATE(state) {
        state.sales_list = {
            data: [],
            pagination: null,
            status_counts: null,
          };
        },
  };

  const actions = {
    updateSalesName({ commit }, sales_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update sales_list name
      setTimeout(() => {
        // Commit mutation to update sales_list name
        commit('SET_SALESLIST', sales_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchSalesListSearch({ commit }, {page, per_page}) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get('/sales', { params: { company_id: company_id, page: page, per_page: per_page } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Sales list..!', response.data.total_revenue, 'TTTTTTT',  response.data.total_sum);
              let { data, pagination} = response.data;
              let status_counts = { total_revenue: response.data.total_revenue, total_sum: response.data.total_sum, due_count: response.data.due_count, due_sum: response.data.due_sum, total_count: response.data.pagination.total }
                
                // console.log(data, 'data', pagination, 'pagination', status_counts, 'status', 'Initialllllly');
              commit('SET_SALESLIST', {data, pagination, status_counts});
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    
  };

  const getters = {
    currentSalesListSearch(state) {
      return state.sales_list;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
