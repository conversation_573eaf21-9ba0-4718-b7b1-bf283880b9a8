<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <div class="dialog-header">
                <p class="text-white text-lg font-semibold"><span>{{ type === 'edit' ? 'EDIT' : 'ADD' }}</span> Role</p>
                <!-- <button @click="cancelModel" class="close-icon">
                    <img class="w-7 h-7" :src="close_img" alt="Close Icon" />
                </button> -->
                <p class="close pr-5" @click="cancelModel">&times;</p>
            </div>
            <div class="dialog-body p-4">
                <div class="mb-4">
                    <label for="serviceCategory" class="text-md font-bold">Role</label>
                    <input type="text" id="serviceCategory" v-model="roleName" class="mt-1 p-2 border rounded-md w-full"
                        @keyup.enter="saveData" ref="roleName" />
                </div>

                <div class="flex justify-center mt-10">
                    <button @click="cancelModel"
                        class="hover:bg-red-500 bg-red-600 text-white px-4 py-2 rounded-md mr-4">Cancel</button>
                    <button type="button" @click="saveData"
                        class="bg-green-600 text-white px-4 py-2 rounded-md ml-4 hover:bg-green-500 ">{{type === 'edit' ? 'Update' : 'Save'}}</button>
                </div>
            </div>
        </div>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import dialogAlert from './dialogAlert.vue';
export default {
    name: 'dialog_role',
    components: {
        dialogAlert
    },
    props: {
        showModal: Boolean,
        data_value: Object,
        type: String,
        companyId: String,
        userId: String
    },
    data() {
        return {
            roleName: '',
            close_img: '/images/setting_page/carbon_close-outline.png',
            isOpen: false,
            open_message: false,
            message: '',
            open_loader: false,
        };
    },
    methods: {
        cancelModel() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        closeDialog(data) {
            if (data) {
                this.isOpen = false;
                setTimeout(() => {
                   this.closeMessage();
                    this.$emit('close-modal', data);
                }, 300);
            }
            this.roleName = '';
        },
        // Add a new method to initialize data when editData is provided
        initializeData() {
            // console.log(this.data_value, 'Waht happening...!');
            if (this.data_value) {
                this.roleName = this.data_value ? this.data_value.name : '';
            } else {
                // Reset data when editData is not provided
                this.roleName = '';
            }
        },
        saveData() {
            if (this.roleName !== '') {
                this.open_loader= true;
                let sent_data = {
                    name: this.roleName,
                    guard_name: "api",
                    company_id: this.companyId
                };
                if (this.type === 'edit') {
                    axios.put(`/roles/${this.data_value.id}`, sent_data)
                        .then(response => {
                            console.log(response.data, 'What happening...!');
                            this.open_loader= false;
                            this.openMessage(response.data.message);
                            this.closeDialog(response.data.data);

                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader= false;
                        })

                } else
                    axios.post('/roles', sent_data)
                        .then(response => {
                            console.log(response.data, 'What happening...!');
                            this.open_loader= false;
                            this.openMessage(response.data.message);                           
                            this.closeDialog(response.data.data);
                           
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_loader= false;
                        })
            }
        },
        openMessage(msg) {
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.message = '';
            this.open_message = false;
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                this.$nextTick(() => {
                    if (this.$refs.roleName) {
                        this.$refs.roleName.focus();
                        this.$refs.roleName.click();
                    }
                })
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        data_value: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
    },
};
</script>

<style scoped>

.dialog-container {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 50;
    max-width: 400px;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.dialog-header {
    background-color: #008383;
    /* Remove the quotes around "green" */
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
}

.close-icon {
    background: none;
    border: none;
    cursor: pointer;
}

.dialog-body {
    padding: 16px;
}

/* Additional styles for form elements */
select {
    appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg fill="%23353F51" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M7 10l5 5 5-5z"/></svg>');
    background-repeat: no-repeat;
    background-position: right 0.5rem top 50%;
    padding-right: 2.5rem;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}
</style>