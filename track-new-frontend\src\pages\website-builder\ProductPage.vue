<template>
  <div class="builder-page">
    <div class="flex flex-col md:flex-row justify-between items-center">
      <h1 class="web-head">Product Details</h1>
      <div class="w-full md:w-auto md:self-end">
        <EnablePageName :pages="is_onSetting" @updatePagesEnabled="handleToggle"></EnablePageName>
      </div>
    </div>

    <!-- Add Product Button -->
    <div class="flex justify-end mb-6">
      <button @click="openModal" class="bg-blue-500 text-white py-2 px-4 rounded shadow">
        Add Product <font-awesome-icon icon="fa-solid fa-plus-circle" />
      </button>
    </div>

    <!-- Product Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <div v-for="(product, index) in products" :key="index" :draggable="true" @dragstart="onDragStart(index, $event)"
        @dragover.prevent @drop="onDrop(index, $event)" @dragenter="onDragEnter(index, $event)" @dragend="onDragEnd"
        class="bg-white shadow-lg rounded-lg p-4 relative">
        <div @click="editProduct(index)" class="cursor-pointer">
          <img v-if="!Array.isArray(product.imageUrl)" :src="product.imageUrl" alt="Product Image"
            class="w-full h-48 object-cover rounded-lg mb-4" />
          <div v-if="Array.isArray(product.imageUrl)" class="grid grid-cols-2 gap-1">
            <div v-for="(img, index) in product.imageUrl" :key="index">
              <img :src="img" alt="Product Image" class="h-16 w-16 object-cover rounded-lg" />
            </div>
          </div>
          <h3 class="font-semibold">{{ product.title }}</h3>
          <p class="text-gray-700">{{ extractPlainText(product.shortDescription) }}</p>
        </div>
        <div class="mt-4 flex justify-between">
          <!-- Edit Button -->
          <button @click="editProduct(index)" class="text-blue-500">
            <font-awesome-icon icon="fa-solid fa-edit" />
          </button>
          <!-- Delete Button -->
          <button @click="deleteProduct(index)" class="text-red-500">
            <font-awesome-icon icon="fa-solid fa-trash" />
          </button>
        </div>
      </div>
    </div>

    <!-- Bottom Navigation -->
    <div class="mt-6">
      <NavigationButtons :pageTitle="'Product Details'" @goToNextPage="goToNextPage" @goToPrevPage="goToPrevPage" />
    </div>

    <!-- Add/Edit Product Modal -->
    <product-modal :showModal="showModal" :isEditMode="isEditMode" :product="currentProduct" :categories="categories"
      :company_id="companyId" @save="handleSaveProduct" @close="closeModal" @updatecategory="updatecategory" />
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
  </div>
</template>
<script>
import ProductModal from './ProductModal.vue'; // Update with the correct modal component
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import imageService from '../../services/imageService';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import EnablePageName from './EnablePageName.vue';

export default {
  props: {
    companyId: {
      type: String,
      required: true
    },
    productsData: { // Receive initial products data from parent
      type: Array,
      default: () => []
    },
    productCategoriessData: {
      type: Array,
      default: () => [{ id: 100, name: 'General' }]
    },
    pages: {
      type: Object,
      required: true
    },
    is_updated: { type: Boolean, required: true },
  },
  components: {
    ProductModal,
    NavigationButtons,
    FontAwesomeIcon,
    confirmbox,
    EnablePageName
  },
  data() {
    return {
      products: [],// List of products
      categories: [],
      showModal: false, // Control modal visibility
      isEditMode: false, // To toggle between add/edit mode
      currentProduct: null, // Product to edit
      currentIndex: null, // Index of product being edited
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      //----is on settings---
      is_onSetting: { is_on: true, name: 'Products' },
      draggedIndex: null,
    };
  },
  watch: {
    products: {
      handler(newProducts) {
        this.$emit('updateProducts', newProducts); // Emit updated products to parent
      },
      deep: true // Watch nested changes
    },
    is_updated: {
      deep: true,
      handler(newValue) {
        this.$emit('updateProducts', this.products);
        this.$emit('updatePagesSetting', { product: this.is_onSetting });
      }
    },
    productCategoriessData: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.categories = [...newValue];
        }
      }
    },
    // productsData: {
    //   deep: true,
    //   handler(newValue) {
    //     if (newValue) {
    //       this.products = [...newValue];
    //     }
    //   }
    // },
    is_onSetting: {
      deep: true,
      handler(newValue) {
        this.$emit('updatePagesSetting', { product: newValue });
      }
    }
  },
  mounted() {
    if (this.productsData) {
      this.products = [...this.productsData];
    }
    if (this.productCategoriessData) {
      this.categories = [...this.productCategoriessData];
    }
    if (this.pages && this.pages.product !== undefined) {
      this.is_onSetting = this.pages.product;
    }
  },
  methods: {
    openModal() {
      this.isEditMode = false;
      this.currentProduct = null;
      this.showModal = true;
    },
    editProduct(index) {
      this.isEditMode = true;
      this.currentIndex = index;
      this.currentProduct = { ...this.products[index] }; // Load product data
      this.showModal = true;
    },
    async handleSaveProduct(productData) {
      if (this.isEditMode) {
        this.products.splice(this.currentIndex, 1, productData); // Update existing product
        this.$emit('updateProducts', this.products);
        this.$emit('updatePagesSetting', { product: this.is_onSetting });
        this.$emit('submitData');
      } else {
        this.products.push(productData); // Add new product
        // console.log(this.products, 'RWRWRWRWR');
        this.$emit('updateProducts', this.products);
        this.$emit('updatePagesSetting', { product: this.is_onSetting });
        this.$emit('submitData');
      }
      // this.showModal = false;
    },
    async deleteProduct(index) {
      this.deleteIndex = index;
      this.open_confirmBox = true;
    },
    closeModal() {
      this.showModal = false;
    },
    goToNextPage() {
      this.$emit('updateProducts', this.products);
      this.$emit('updatePagesSetting', { product: this.is_onSetting });
      this.$emit('submitData');
      this.$emit('goToNextPage'); // Emit event to the parent component
    },
    goToPrevPage() {
      this.$emit('updateProducts', this.products);
      this.$emit('updatePagesSetting', { product: this.is_onSetting });
      this.$emit('submitData');
      this.$emit('goToPrevPage'); // Emit event to the parent component
    },
    //--file delete actions--
    async deleteRecord() {
      this.$emit('updateLoader', true);
      const product = this.products[this.deleteIndex];
      try {
        if (product.imageUrl) {
          if (Array.isArray(product.imageUrl)) {
            // Delete each image in the array
            await Promise.all(product.imageUrl.map(async (file) => {
              if (file) {
                try {
                  await imageService.deleteImage(file, 'products');
                } catch (error) {
                  console.error('Error deleting image:', error);
                }
              }
            }));
          } else {
            await imageService.deleteImage(product.imageUrl, 'products'); // Delete associated image
          }
        }
        if (product.product_brochure && product.product_brochure !== '') {
          const response = await imageService.deleteImage(product.product_brochure, 'products');
        }
        this.products.splice(this.deleteIndex, 1);
        this.closeconfirmBoxData();
        this.$emit('toasterMessages', { msg: 'Product details removed successfully', type: 'success' });
        // } else {
        //   this.products.splice(this.deleteIndex, 1);
        //   this.closeconfirmBoxData();
        //   this.$emit('toasterMessages', { msg: 'Product cannot found', type: 'warning' });
        // }
      } catch (error) {
        console.error('Error deleting product image:', error);
        if (error.response.data.error === 'Image not found') {
          this.products.splice(this.deleteIndex, 1);
        }
        this.closeconfirmBoxData();
        this.$emit('toasterMessages', { msg: 'Product cannot found', type: 'warning' });
      }
    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
      this.open_loader = false;
    },
    //--categories--
    async updatecategory(data) {
      if (data && data.length > 0) {
        this.$emit('updateProductCategories', data);
        this.$emit('submitData');
        this.categories = data;
      }
    },
    //--handle switch--
    handleToggle(data) {
      // Perform any additional actions when toggled
      if (data) {
        this.is_onSetting = data;
      }
    },
    //--editor--
    isValidBase64(str) {
      try {
        // Check if the string matches Base64 format
        return !!str.match(/^(?:[A-Za-z0-9+/]{4})*(?:[A-Za-z0-9+/]{2}==|[A-Za-z0-9+/]{3}=)?$/);
      } catch (error) {
        console.error("Error validating Base64 string:", error);
        return false;
      }
    },
    base64Decode(encodedData) {
      try {
        return atob(encodedData); // Decode Base64 data
      } catch (error) {
        console.error("Error decoding Base64 data:", error);
        return ""; // Return empty string on error
      }
    },
    extractPlainText(htmlString) {
      if (htmlString && this.isValidBase64(htmlString)) {
        const decoded = this.base64Decode(htmlString); // Decode Base64       

        // Create a temporary DOM element to parse HTML and extract plain text
        const tempDiv = document.createElement("div");
        tempDiv.innerHTML = decoded;
        return tempDiv.textContent || tempDiv.innerText || ""; // Extract text content
      } else {
        return htmlString;
      }
    },
    //---drag and drop reorder the list--
    onDragStart(index, event) {
      this.draggingIndex = index;
      event.dataTransfer.effectAllowed = 'move'; // Set drag effect
      event.dataTransfer.setData('text/plain', index); // Store the index of the dragged item
    },
    onDragEnter(index, event) {
      if (this.draggingIndex !== index) {
        // Visually highlight the target element
        const target = event.target;
        target.classList.add('drag-over');
      }
    },
    onDragEnd() {
      this.draggingIndex = null;
      // Clean up any visual highlight after dragging ends
      const draggedItems = document.querySelectorAll('.drag-over');
      draggedItems.forEach(item => item.classList.remove('drag-over'));
    },
    onDrop(targetIndex, event) {
      const sourceIndex = event.dataTransfer.getData('text/plain');
      const draggedItem = this.products[sourceIndex];

      // Move the dragged item to the target position
      this.products.splice(sourceIndex, 1);
      this.products.splice(targetIndex, 0, draggedItem);

      this.$emit('updateProducts', this.products); // Emit updated services list if necessary
    }
  }
};
</script>