<template>
    <div class="flex h-screen relative">
        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mt-[60px]': isMobile, 'mt-[57px]': !isMobile }">

            <div class="relative">
                <bannerDesign></bannerDesign>
                <users :companyId="companyId" :userId="userId" :store_refresh="false" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></users>
            </div>
        </div>
    </div>
</template>

<script>
import users from '../supporting/setting_categories/users.vue';
import { useMeta } from '@/composables/useMeta';
import bannerDesign from '../supporting/banner/bannerDesign.vue';

export default {
    name: 'users Page',
    components: {
        users,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            route_item: 28,
            dataFromChild: null,
            getFilteredData: null,
            companyId: null,
            userId: null,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'User';
        const pageDescription = 'Centralize your billing and invoice management with powerful tools for tracking, updating, and organizing financial data, improving accuracy and efficiency across your operations.';
        useMeta(pageTitle, pageDescription);
        return { pageTitle };
    },
    methods: {
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleDataFromChild(data) {
            // console.log(data, 'OOOOOOOOOOOOOO');
            this.dataFromChild = data;
        },
        getFiteredDataList(data) {
            // console.log(data, 'WWWW');
            this.getFilteredData = data;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }

    },
    created() {
        this.updateIsMobile(); // Initial check
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '' + '';
        }

        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>