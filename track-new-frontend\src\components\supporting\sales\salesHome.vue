<template>
    <div :class="{ 'manualStyle text-sm sm:mb-[30px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        ref="scrollContainer" @scroll="handleScroll" class="p-2">
        <div class="my-custom-margin">
            <!--search-->
            <div v-if="isMobile" class="mt-2 sm:m-4" :class="{ 'mt-3': isMobile }">
                <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer" @resetData="resetToSearch"
                    :resetSearch="resetSearch">
                </searchCustomer>
            </div>
            <!--page header-->
            <div v-if="!isMobile" class="m-1 flex items-center space-x-4">
                <p class="font-bold text-xl">Sales</p>
            </div>
            <!-- Due Information -->
            <div v-if="due_details && Object.keys(due_details).length > 0"
                class="bg-white shadow-md rounded-xl border border-gray-300 mt-2" :class="{ 'mt-2': isMobile }">
                <!--:class="{ 'mt-5 w-full': isMobile, 'w-3/4': !isMobile }"-->
                <div class="grid grid-cols-2 sm:grid-cols-5 gap-0 items-center text-center sm:text-md lg:text-lg">
                    <!-- Total Count -->
                    <div class="p-4 rounded-xl bg-purple-200 hover:bg-purple-300 rounded-r-none cursor-pointer border-r-2 border-gray-300  sm:rounded-bl-xl rounded-bl-none "
                        @click="getSalesDataAll()">
                        <p class="text-purple-800 font-normal mb-1">
                            <font-awesome-icon icon="fa-solid fa-list-ol" /> Total Count
                        </p>
                        <p class="font-bold text-gray-800">
                            {{ due_details.total_count }}
                        </p>
                    </div>

                    <!-- Due Count -->
                    <div class="p-4 bg-yellow-200 hover:bg-yellow-300 cursor-pointer sm:border-r-2 sm:border-gray-300 border-r-none sm:rounded-tr-none rounded-tr-xl "
                        @click="navigateDue(1, 20)">
                        <p class="text-yellow-800 font-normal mb-1">
                            <font-awesome-icon icon="fa-solid fa-list-ol" /> Due Count
                        </p>
                        <p class="font-bold text-gray-800">
                            {{ due_details.due_count }}
                        </p>
                    </div>
                    <!-- Total Sales -->
                    <div v-if="due_details.total_sum >= 0"
                        class="p-4 bg-blue-200 hover:bg-blue-300 cursor-pointer sm:border-r-2 sm:border-gray-300 border-r-none sm:col-span-1 col-span-2"
                        @click="getSalesDataAll()">
                        <p class="text-blue-800 font-normal mb-1">
                            <font-awesome-icon icon="fa-solid fa-file-invoice" /> Total Sales
                        </p>
                        <p class="font-bold text-gray-800">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }} {{ formatCurrency(due_details.total_sum) }}
                        </p>
                    </div>

                    <!-- Paid Amount -->
                    <div v-if="due_details.total_revenue >= 0"
                        class="p-4 bg-green-200 hover:bg-green-300 border-r-2 border-gray-300 sm:rounded-bl-none rounded-bl-xl">
                        <p class="text-green-800 font-normal mb-1">
                            <font-awesome-icon :icon="['fas', 'money-bill-1']" /> Paid
                        </p>
                        <p class="font-bold text-gray-800">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }} {{ formatCurrency(due_details.total_revenue) }}
                        </p>
                    </div>

                    <!-- Unpaid Amount -->
                    <div class="p-4 sm:rounded-xl rounded-br-xl bg-red-200  hover:bg-red-300 sm:rounded-l-none cursor-pointer"
                        @click="navigateDue(1, recordsPerPage)">
                        <p class="text-red-600 font-normal mb-1">
                            <font-awesome-icon icon="fa-solid fa-credit-card" /> Unpaid
                        </p>
                        <p class="font-bold text-gray-800">
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }} {{ formatCurrency(due_details.due_sum) }}
                        </p>
                    </div>
                    <!-- <div class="col-span-2 sm:col-span-1">
                        <p class="text-blue-600 mb-1"><font-awesome-icon icon="fa-solid fa-layer-group" /> Invoice
                            Group:
                        </p>
                        <select id="status" v-model="invoice_group"
                            class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full">
                            <option :value="'all'">All</option>
                            <option :value="0">Group A</option>
                            <option :value="1">Group B</option>
                        </select>
                    </div> -->
                </div>
            </div>
            <!--new design header-->
            <div v-if="!isMobile" class="flex justify-between mt-4">
                <div class="flex mr-2 space-x-4">
                    <button @click="openSalesModal" :class="{ 'mr-2': isMobile }"
                        class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center"><span
                            class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /></span>
                        <span v-if="!isMobile" class="text-center">New Sale</span>
                    </button>
                    <!--view design-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="info-msg p-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200"
                            @click="toggleView" :title02="'Table view'"
                            :class="{ 'bg-white': items_category === 'tile' }">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="p-1 rounded-lg border border-gray-400 border flex-shrink-0 cursor-pointer hover:bg-blue-200 info-msg"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="'Card view'">

                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
                <!----filter options----->
                <div ref="dropdownContainerFilter" class="items-center relative">
                    <button @click="toggleMainDropdown" :disabled="data.length == 0"
                        class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                        :class="{ 'cursor-not-allowed': data.length == 0 }">
                        <span class="inline-flex items-center w-full pointer-events-none">
                            <font-awesome-icon icon="fa-solid fa-filter" class="px-1" /> Filter
                            <font-awesome-icon v-if="!isMainDropdownOpen" icon="fa-solid fa-angle-down" class="pl-3" />
                            <font-awesome-icon v-if="isMainDropdownOpen" icon="fa-solid fa-angle-up" class="pl-3" />
                        </span>
                    </button>
                    <!-- Main Dropdown -->
                    <div v-if="isMainDropdownOpen" ref="mainDropdown"
                        class="absolute mt-2 w-48 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 right-0 border">
                        <div class="py-1">
                            <!-- Other Options -->
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('by Date')">By Date</button>
                            <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                @click.stop="changeFilter('serial_number')">By Serial Number</button>
                        </div>
                    </div>
                </div>
            </div>
            <!---fiter information-->
            <div v-if="Object.keys(filteredBy).length > 0" class="text-xs flex -mb-3 flex-row overflow-auto">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2">
                    <p class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }} = </p>
                    <p>{{ key === 'assign_to' ? value.join(', ') : key === 'type' ? typeList[value] : key === 'status' ?
                        statusList[value] : key === 'invoice_to' ? invoice_to[value] : value }}</p>
                </div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton && data && data.length > 0" class="text-sm mt-5"
                :class="{ 'm-1': !isMobile, 'm-1': isMobile }">
                <div
                    :class="{ 'sm:p-4 p-0 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <!--table header-->
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                        <!--search bar--->
                        <div>
                            <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                @resetData="resetToSearch" :resetSearch="resetSearch">
                            </searchCustomer>
                        </div>
                    </div>
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ 'hidden': !column.visible }" class="py-2 table-border cursor-pointer"
                                        @mouseover="showSortIcons(column)" @mouseleave="hideSortIcons(column)"
                                        @click="getSortType((column), column.field)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.field === 'current_date' ? 'Date' : column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <!-- Ascending Icon -->
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>

                                                <!-- Descending Icon -->
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[5px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in paginatedData" :key="index"
                                    class="hover:bg-gray-200 cursor-pointer">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex"
                                        :class="{ 'hidden': !column.visible }" class="px-2 py-1 table-border"
                                        @click="printRecord(record)">
                                        <span
                                            v-if="column.field !== 'customer' && column.field !== 'current_date' && column.field !== 'invoice_no' && column.field !== 'status'"
                                            @mouseover="record.status && record.status !== 'Cancel' ? showModal({ due_amount: record[column.field], record: record, whatsapp: companywhatsapp }, $event) : ''"
                                            @mouseleave="hideModal">
                                            {{ ((column.field === 'discount' && record[column.field]) || (column.field
                                                === 'due_amount' && record[column.field]) ||
                                                (column.field === 'grand_total' && record[column.field])) ?
                                                currentCompanyList &&
                                                    currentCompanyList.currency === 'INR' ?
                                                    '\u20b9' : currentCompanyList.currency : '' }}
                                            {{
                                                record[column.field] }}
                                            <!--due in-->
                                            <span v-if="column.field === 'due_amount' && record['status'] == 'Success'"
                                                class="block text-xs flex">
                                                <p v-if="calculateDaysUntilDue(record['current_date']) >= 0 && record['due_amount'] > 0"
                                                    class="flex text-blue-800">
                                                    <span>Due:</span> <span class="pl-1">{{
                                                        calculateDaysUntilDue(record['current_date']) }} days</span>
                                                </p>
                                                <p v-else-if="record['due_amount'] > 0" class="flex text-red-600">
                                                    <span>Overdue:</span>
                                                    <span class="pl-1">{{
                                                        Math.abs(calculateDaysUntilDue(record['current_date'])) }}
                                                        days</span>
                                                </p>
                                            </span>
                                        </span>
                                        <span v-if="column.field === 'customer'" class="hover:text-blue-600"
                                            @mouseover="showModal(record[column.field], $event)"
                                            @mouseleave="hideModal">
                                            {{ record[column.field] ? (record[column.field].first_name ?
                                                record[column.field].first_name : '') + ' ' +
                                                (record[column.field].last_name ? record[column.field].last_name : '') +
                                                ' - ' + (record[column.field].contact_number ?
                                                    record[column.field].contact_number :
                                                    '') : '' }}
                                        </span>

                                        <!--status-->
                                        <span v-if="column.field === 'status'" class="py-1 px-2 text-xs"
                                            :class="{ 'text-green-700 bg-green-200 rounded-full': record['status'] === 'Success', 'text-red-700 bg-red-200 rounded-full': record['status'] == 'Cancel' }">
                                            {{ record['status'] == 'Cancel' ? 'Cancelled' : record['status'] }}
                                        </span>
                                        <!--payment_status-->
                                        <span v-if="column.field === 'payment_status' && record['status'] == 'Success'"
                                            class="py-1 px-2 text-xs"
                                            :class="{ 'text-green-700 bg-green-200 rounded-full': !record['due_amount'], 'text-red-700 bg-red-200 rounded-full': record['due_amount'] }">
                                            {{ record['due_amount'] ? 'Unpaid' : 'Paid' }}
                                        </span>
                                        <span v-if="column.field === 'current_date'"
                                            class="cursor-pointer created-at-color"
                                            :title="calculateDaysAgo(formattedDate(record.current_date, true))">
                                            {{ getFormattedDate(record[column.field]) }}
                                        </span>
                                        <span v-if="column.field === 'invoice_no'">
                                            <span class="block text-xs uppercase text-blue-800">{{ record['invoice_to']
                                            }}</span>
                                            {{ typeof record['invoice_id'] === 'string' ? record['invoice_id'] : ''
                                            }}</span>
                                    </td>
                                    <td class=" px-2 text-center table-border">
                                        <div class="flex justify-center">
                                            <div class="flex relative">
                                                <div class="flex">
                                                    <button v-if="!record.editing" @click="printRecord(record)"
                                                        class="px-1">
                                                        <a v-if="!record.editing" @click.prevent="printRecord(record)"
                                                            :href="`/sales/invoice/preview?type=sales_home&invoice_no=${record.id}`"
                                                            class=" text-violet-700 px-2 py-1 flex justify-center
                                                            items-center">
                                                            <font-awesome-icon icon="fa-solid fa-print" />
                                                        </a>
                                                    </button>
                                                    <button v-if="!record.editing && record['status'] !== 'Cancel'"
                                                        @click="startEdit(record)" class="px-1">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" />
                                                    </button>
                                                    <!--&& record['status'] !== 'Cancel'-->
                                                    <button
                                                        v-if="!isDue && index === 0 && ((this.pagination && this.pagination.invoice && this.pagination.invoice.current_page == 1) || isMobile) && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)"
                                                        class="text-red-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                    </button>
                                                </div>
                                                <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 text-blue-800 rounded"
                                                    :class="{ 'bg-blue-100': display_option === index }">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button>
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-7 absolute border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center right-0 "
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700 text-xs"
                                                    aria-labelledby="dropdownDefaultButton">
                                                    <li class="hover:bg-gray-200">
                                                        <a v-if="!record.editing" @click.prevent="printRecord(record)"
                                                            :href="`/sales/invoice/preview?type=sales_home&invoice_no=${record.id}`"
                                                            class=" text-violet-700 px-2 py-1 flex justify-left
                                                            items-center">
                                                            <font-awesome-icon icon="fa-solid fa-print" />
                                                            <span class="px-2">Print / View</span>
                                                        </a>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && record['status'] !== 'Cancel'"
                                                            @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200"
                                                        v-if="!isDue && index === 0 && ((this.pagination && this.pagination.invoice && this.pagination.invoice.current_page == 1) || isMobile) && checkRoles(['admin'])">
                                                        <button v-if="!record.editing" @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                    <li v-if="record['status'] == 'Success'" class="hover:bg-gray-200">
                                                        <button v-if="!record.editing"
                                                            @click="openConfirmSalesCancel(record)"
                                                            class="text-orange-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-xmark" />
                                                            <span class="px-2">Cancel</span>
                                                        </button>
                                                    </li>
                                                    <li v-if="record['due_amount'] > 0 && record['status'] != 'Cancel'"
                                                        class="hover:bg-gray-200 text-sm">
                                                        <button
                                                            class="text-fuchsia-700 px-2 py-1 flex justify-center items-center"
                                                            @click="openPaymentModal(record)" title="Add Payment">
                                                            {{ currentCompanyList && currentCompanyList.currency ===
                                                                'INR' ? '\u20b9' : currentCompanyList.currency }}
                                                            <span class="line-clamp-1 px-2">Add Payment</span>
                                                        </button>
                                                    </li>
                                                    <li v-if="record['due_amount'] > 0 && record['status'] != 'Cancel'"
                                                        class="hover:bg-gray-200 text-sm">
                                                        <button v-if="companywhatsapp"
                                                            class="text-green-700 px-2 py-1 flex justify-center items-center"
                                                            @click="openWhatsApp(record)" title="Share Payment Link">
                                                            <font-awesome-icon icon="fa-brands fa-whatsapp" />
                                                            <span class="line-clamp-1 px-2">Share Payment
                                                                Link</span>
                                                        </button>
                                                        <button v-else
                                                            class="text-red-700 px-2 py-1 flex justify-center items-center"
                                                            @click="navigateToWhatsApp(record)"
                                                            title="Share Payment Link">
                                                            <font-awesome-icon icon="fa-brands fa-whatsapp" />
                                                            <span class="line-clamp-1 px-2">Connect WhatsAPP</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex table-border"
                                    v-if="!data || data.length === 0">
                                    <td>
                                        <button
                                            class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="openSalesModal">
                                            + Sale
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                        <!-- Hover Modal -->
                        <customerDataTable v-if="isModalVisible" :showModal="isModalVisible" :modalData="modalData"
                            :modalPosition="modalPosition" @mouseover="toggleHoverModal(true)"
                            @mouseleave="toggleHoverModal(false)" @mousemove="showModal(null, $event)"
                            @sendRemainder="openWhatsApp" @connectwhatsapp="navigateToWhatsApp">
                        </customerDataTable>
                    </div>

                    <!--card view-->
                    <div>
                        <div v-if="items_category === 'list'"
                            class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4 custom-scrollbar-hidden">
                            <div v-for="(record, index) in data" :key="index" class="w-full">
                                <div
                                    class="max-w-md mx-auto bg-white rounded border md:max-w-2xl relative shadow-lg border border-gray-300">
                                    <!-- Top Section -->
                                    <div class="flex justify-between items-center p-4 py-2">
                                        <!-- Left Side (Can be your dynamic content) -->
                                        <div class="text-xs text-red-500">
                                            <p>{{ calculateDaysAgo(formattedDate(record.current_date, true)) }}</p>
                                        </div>
                                        <!-- Right Side (Actions) -->
                                        <div class="flex items-center">
                                            <div class="rounded-md p-2 py-1 items-center cursor-pointer"
                                                :class="{ 'bg-green-200 text-green-700': record['status'] == 'Success', 'bg-red-200 text-red-700': record['status'] == 'Cancel' }"
                                                @click="printRecord(record)">
                                                <p class="text-sm">{{ record['status'] == 'Cancel' ? 'Cancelled' :
                                                    record['status'] }}</p>
                                            </div>

                                            <div class="flex items-center">
                                                <div class="relative ml-2">
                                                    <button @click.stop="displayAction(index)"
                                                        class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                        <font-awesome-icon icon="fa-solid fa-ellipsis-vertical"
                                                            size="lg" />
                                                    </button>
                                                </div>
                                                <div v-if="display_option === index" :ref="'dropdown' + index"
                                                    class="z-10 mt-[130px] absolute bg-white right-0 divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                    style="display: flex; justify-content: center; align-items: center;">
                                                    <ul class="py-1 text-gray-700 text-xs"
                                                        aria-labelledby="dropdownDefaultButton">
                                                        <li class="hover:bg-gray-200">
                                                            <a v-if="!record.editing"
                                                                @click.prevent="printRecord(record)"
                                                                :href="`/sales/invoice/preview?type=sales_home&invoice_no=${record.id}`"
                                                                class=" text-violet-700 px-2 py-1 flex justify-left items-center">
                                                                <font-awesome-icon icon="fa-solid fa-print" />
                                                                <span class="px-2">Print / View</span>
                                                            </a>
                                                        </li>
                                                        <li class="hover:bg-gray-200">
                                                            <button
                                                                v-if="!record.editing && record['status'] !== 'Cancel'"
                                                                @click="startEdit(record)"
                                                                class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-pencil"
                                                                    style="color: #3b82f6;" />
                                                                <span class="px-2">Edit</span>
                                                            </button>
                                                        </li>
                                                        <!--&& record['status'] !== 'Cancel'-->
                                                        <li class="hover:bg-gray-200"
                                                            v-if="!isDue && index === 0 && ((this.pagination && this.pagination.invoice && this.pagination.invoice.current_page == 1) || isMobile) && checkRoles(['admin'])">
                                                            <button v-if="!record.editing" @click="confirmDelete(index)"
                                                                class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                    style="color: #ef4444" />
                                                                <span class="px-2">Delete</span>
                                                            </button>
                                                        </li>
                                                        <li v-if="record['status'] == 'Success'"
                                                            class="hover:bg-gray-200">
                                                            <button v-if="!record.editing"
                                                                @click="openConfirmSalesCancel(record)"
                                                                class="text-orange-500 px-2  py-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-xmark" />
                                                                <span class="px-2">Cancel</span>
                                                            </button>
                                                        </li>
                                                    </ul>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- <div class="flex space-x-4">
                                    <button @click="printRecord(record)"
                                        class="text-sm font-medium bg-blue-100 text-blue-800 rounded-full px-3 py-1">
                                        <i class="fas fa-print"></i> Print
                                    </button>
                                    <button @click="startEdit(record)"
                                        class="text-sm text-gray-500 bg-gray-100 rounded-full px-3 py-1">
                                        <i class="fas fa-edit"></i> Edit
                                    </button>
                                </div> -->
                                    </div>

                                    <!-- Middle Section -->
                                    <div class="px-4 pt-2">
                                        <!-- Customer Details (Can be your dynamic content) -->
                                        <div class="flex items-center -mt-4">
                                            <!-- <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                        :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-lime-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-blue-600': index === 0 }">
                                        {{ record.customer && record.customer.first_name ?
                                            record.customer.first_name[0].toUpperCase() :
                                            'C' }}
                                    </div> -->
                                            <div>
                                                <h4 class="leading-6 font-semibold text-gray-900 cursor-pointer"
                                                    @click="printRecord(record)">
                                                    {{ record.customer && record.customer.first_name ?
                                                        record.customer.first_name + ' ' + (record.customer.last_name ?
                                                            record.customer.last_name : '') : '' }}</h4>
                                                <p class="text-gray-500 cursor-pointer text-sm"
                                                    @click="dialPhoneNumber(record.customer.contact_number)">+91-{{
                                                        record.customer.contact_number }}</p>
                                            </div>
                                        </div>
                                        <!---new design-->
                                        <div class="flex justify-between items-center">
                                            <div class="text-gray-600 cursor-pointer" @click="printRecord(record)">
                                                <p v-if="record['due_amount'] === 0">Payment Received
                                                    <span class="block text-gray-500 py-1">#{{
                                                        record['invoice_id'] }}</span>
                                                </p>
                                                <p v-else>Invoice <span class="text-gray-500 py-1">#{{
                                                    record['invoice_id']
                                                        }}</span>
                                                </p>
                                                <!--due-->
                                                <div class="text-sm flex py-1">
                                                    <!-- <p>{{ formatDateTime(record.current_date, true) }}</p> -->
                                                    <div
                                                        v-if="record['due_amount'] > 0 && record['status'] == 'Success'">
                                                        <p v-if="calculateDaysUntilDue(record['current_date']) >= 0 && record['due_amount'] > 0"
                                                            class="flex flex-col">
                                                            <!-- <span>Due:</span>  -->
                                                            <span>{{
                                                                calculateDaysUntilDue(record['current_date']) }} days to
                                                                due</span>
                                                        </p>
                                                        <p v-else-if="record['due_amount'] > 0"
                                                            class="flex flex-col text-red-500">
                                                            <!-- <span>Overdue:</span> -->
                                                            <span>{{
                                                                Math.abs(calculateDaysUntilDue(record['current_date']))
                                                            }}
                                                                days to overdue</span>
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                            <div>
                                                <div v-if="record['due_amount'] === 0" class="text-center">
                                                    <p>
                                                        {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                            '\u20b9' : currentCompanyList.currency }} {{
                                                            record['grand_total'] }}
                                                    </p>
                                                </div>
                                                <div v-else>
                                                    <p v-if="record['due_amount'] > 0 && record['status'] == 'Success'"
                                                        class="text-center">{{ currentCompanyList &&
                                                            currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                            currentCompanyList.currency }}
                                                        {{ record['due_amount'] }}
                                                    </p>
                                                    <button v-if="record['status'] == 'Success'"
                                                        class="px-2 text-red-700 bg-red-100 rounded rounded-full text-xs">Unpaid</button>

                                                </div>
                                            </div>
                                        </div>
                                        <!-- Invoice Details (Should iterate over your data) -->
                                        <!-- <div class="grid grid-cols-2 gap-2 mb-1">
                                    <div class="bg-gray-100 rounded-md p-2 items-center">
                                        <p class="text-sm text-gray-700 font-semibold">Invoice No: </p>
                                        <p class="text-sm text-gray-500">{{ record['invoice_id'] }}</p>
                                    </div>
                                    <div class="bg-gray-100 rounded-md p-2 items-center">
                                        <p class="text-sm text-gray-700 font-semibold">Invoice Date: </p>
                                        <p class="text-sm text-gray-500">{{ formatDateTime(record['current_date']) }}
                                        </p>
                                    </div>
                                </div> -->
                                        <!-- Invoice Actions (Can be your dynamic actions) -->
                                        <!-- <div class="flex justify-between bg-gray-100 rounded-md p-3 py-1">
                                    <div>
                                        <p class="text-sm text-gray-700 font-semibold">Grand Total:</p>
                                        <p class="text-sm text-gray-900 font-semibold">{{currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency}} {{ record['grand_total'] }}</p>
                                    </div>
                                    <div class="flex items-center">
                                        <p class="flex flex-col">
                                            <span class="px-3 rounded-full text-sm font-medium mr-2"
                                                :class="{ 'bg-green-100 text-green-800': record['due_amount'] === 0, 'bg-red-100 text-red-800': record['due_amount'] !== 0 }">
                                                {{ record['due_amount'] === 0 ? 'Paid' : 'Due' }}</span>
                                            <span v-if="record['due_amount'] > 0" class="text-red-800 ">{{currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' : currentCompanyList.currency}} {{
                                                record['due_amount'] }}</span>
                                        </p>
                                        <div class="items-center ml-1 text-sm">
                                            <p v-if="calculateDaysUntilDue(record['current_date']) >= 0 && record['due_amount'] > 0"
                                                class="flex flex-col">
                                                <span>Due:</span> <span>{{
                                                    calculateDaysUntilDue(record['current_date']) }} days</span>
                                            </p>
                                            <p v-else-if="record['due_amount'] > 0" class="flex flex-col text-red-500">
                                                <span>Overdue:</span>
                                                <span>{{
                                                    Math.abs(calculateDaysUntilDue(record['current_date'])) }}
                                                    days</span>
                                            </p>
                                        </div>
                                        <button @click="printRecord(record)"
                                            class="px-2 py-1 ml-1 rounded-md text-sm font-medium bg-blue-500 text-white hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
                                            View
                                        </button>
                                    </div>
                                </div> -->
                                    </div>
                                    <!---bottom section-->
                                    <div v-if="record['due_amount'] > 0 && record['status'] != 'Cancel'"
                                        class="px-4 py-1 text-xs">
                                        <div class="flex justify-between items-center">
                                            <button class="text-fuchsia-700" @click="openPaymentModal(record)">
                                                {{ currentCompanyList && currentCompanyList.currency === 'INR' ?
                                                    '\u20b9'
                                                    : currentCompanyList.currency }}
                                                Add Payment
                                            </button>
                                            <button v-if="companywhatsapp" class="text-green-700"
                                                @click="openWhatsApp(record)">
                                                <font-awesome-icon icon="fa-brands fa-whatsapp" />
                                                Share Payment Link
                                            </button>
                                            <button v-else class="text-red-700" @click="navigateToWhatsApp(record)">
                                                <font-awesome-icon icon="fa-brands fa-whatsapp" />
                                                Connect WhatsApp
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && this.pagination.invoice.last_page > 0 && this.pagination.invoice.last_page === this.pagination.invoice.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>

                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="pagination.invoice && !isMobile">
                        <!-- <p class="pagination-info">Page {{ currentPage }} of {{ totalPages }}</p> -->
                        <!-- <div class="mt-4">
                            <select v-model="recordsPerPage" @change="changePage" id="recordsPerPage"
                                class="mt-1 p-2 border border-gray-300 rounded pr-5 mr-3 mb-6">
                                <option v-for="option in options" :key="option" :value="option" class="text-xs">{{
                                    option }}
                                </option>
                            </select>
                            <label v-if="!isMobile" for="recordsPerPage">Records per page</label>
                        </div> -->
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.invoice.current_page - 1) *
                                pagination.invoice.per_page) + 1 }} to {{
                                Math.min(pagination.invoice.current_page * pagination.invoice.per_page,
                                    pagination.invoice.total) }} of
                            {{ pagination.invoice.total }} entries
                        </p>

                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none overflow-auto">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                    <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                        class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span>
                                    </button>
                                </li>
                                <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                            pageNumber
                                        }}</button>
                                </li>
                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': currentPage === this.pagination.invoice.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== this.pagination.invoice.last_page }">
                                    <button @click="updatePage(currentPage + 1)"
                                        :disabled="currentPage === this.pagination.invoice.last_page"
                                        class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                                        <span class="pr-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!---total records per page-->
                    <!-- <div v-if="pagination.invoice && !isMobile" class="text-xs -mt-3">
                        <p>Total records: <span class="font-bold">{{ this.pagination.invoice.total }}</span></p>
                    </div> -->
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!---in mobile view Filter Service-->
        <div v-if="isMobile" ref="dropdownContainerFilter">
            <div class="fixed bottom-36 right-5 z-50 bg-green-700 rounded-full">
                <div class="flex justify-end">
                    <button @click="toggleMainDropdown" type="button"
                        class="flex items-center justify-center px-[10px] py-2 text-white "
                        :class="{ 'cursor-not-allowed blur-sm': data.length == 0 }" :disabled="data.length == 0">
                        <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                    </button>
                </div>
            </div>
            <!-- Main Dropdown -->
            <div v-if="isMainDropdownOpen" ref="mainDropdown"
                class="fixed bottom-48 sm:bottom-48  left-full transform -translate-x-full w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border">
                <div class="py-1">

                    <!-- Other Options -->
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('by Date')">By Date</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('serial_number')">By Serial Number</button>
                </div>
            </div>
        </div>
        <!---in mobile view create new sale-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openSalesModal" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!-- Use the SalesModal component -->
        <salesModal :showSalesModal="showSalesModal" :selectedSalesType="selectedSalesType"
            :selectedServiceType="selectedServiceType" @closeSalesModal="closeSalesModal"
            @handleSalesTypeSelection="handleSalesTypeSelection"
            @handleServiceTypeSelection="handleServiceTypeSelection" :serviceCategoriesList="serviceCategoriesList">
        </salesModal>
        <!--filter-->
        <salesfilter :showModal="lead_filter" @closeLeadFilter="closeLeadFilter" :typeList="typeList"
            :category_list="serviceCategoriesList" :statusList="statusList" :invoice_to="invoice_to"
            :selectedByValue="selectedByValue" :invoice_group="invoice_group">
        </salesfilter>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'sale'"></bottombar> -->
        <salesCancelConfirm :showModal="sales_cancel_confirmation" @onCancel="closeSalesConfirm"
            @onConfirm="confirmSalesCancel"></salesCancelConfirm>
        <!---payment type and collect-->
        <paymentCollectPos :showModal="open_paymentModal" @close-modal="closePaymentModal" :isMobile="isMobile"
            :invoice_setting="currentInvoice" :get_all_data="get_all_data" :itemData="payment_value"
            :payment_data="paymentData" :type="payment_type" :from="'sales'" :balance_amount="balance_due_amount"
            :currentCompanyList="currentCompanyList">
        </paymentCollectPos>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <!--whatsapp message-->
        <whatsappMessage :showModal="openWhatsAppMessage" :type="'sales'" :data="selected_sales"
            @close="closeWhatsappMessage">
        </whatsappMessage>
    </div>
</template>

<script>
import confirmbox from '../dialog_box/confirmbox.vue';
import dialogAlert from '../dialog_box/dialogAlert.vue';
import salesModal from '../dialog_box/salesModal.vue';
import salesfilter from '../dialog_box/filter_Modal/salesfilter.vue';
import axios from 'axios';
// import bottombar from '../dashboard/bottombar.vue';
import { mapGetters, mapActions } from 'vuex';
import searchCustomer from '../customers/searchCustomer.vue';
import salesCancelConfirm from '@/components/supporting/dialog_box/salesCancelConfirm.vue';
import paymentCollectPos from '../dialog_box/paymentCollectPos.vue';
import customerDataTable from '../dialog_box/customerDataTable.vue';
import whatsappMessage from '../dialog_box/whatsappMessage.vue';
export default {
    emits: ['updateIsOpen', 'dataToParent'],
    name: 'sales_home',
    components: {
        confirmbox,
        dialogAlert,
        salesModal,
        salesfilter,
        // bottombar,
        searchCustomer,
        salesCancelConfirm,
        paymentCollectPos,
        customerDataTable,
        whatsappMessage
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        companyId: String,
        userId: String,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            length_category: null,
            serviceCategories: null,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataList: [],
            columns: [],
            serviceList: [],
            data: [],
            originalData: [],
            isImportModalOpen: false,
            open_message: false,
            message: '',
            customersList: [],
            //---sales model--
            showSalesModal: false,
            selectedSalesType: '',
            selectedServiceType: '',
            serviceCategoriesList: [],
            //---filter--
            showFilterOptions: false,
            mouseOverIsNot: false,
            filterOptions: ['by Date', 'by Customer', 'by category/Type', 'by Invoice_to', 'by Status', 'Custom'],
            selectedByValue: null,
            lead_filter: false,
            typeList: ['product', 'service'],
            statusList: ['undue', 'due'],
            invoice_to: ['b2c', 'b2b'],
            filteredBy: {},
            pagination: {},
            service_data: {},
            due_details: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 9,
            number_of_rows: 10,
            gap: 5,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            now: null,
            isDue: false,
            customer_id: '',
            //--sales cancel---
            sales_cancel_confirmation: false,
            cancelrecords_data: null,
            //---payment modal--
            open_paymentModal: false,
            add_payment_record: null,
            get_all_data: null,
            payment_value: null,
            payment_type: null,
            paymentData: [],
            printing: false,
            open_loader: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            balance_due_amount: 0,
            //---modal---mouse over---
            isModalVisible: false,
            modalData: {},
            modalPosition: { x: 0, y: 0 },
            actionPosition: { x: 0, y: 0 },
            isHoveringModal: false,
            hoverTimer: null,
            lastMousePosition: { x: 0, y: 0 },
            tolerance: 50, // Tolerance for mouse movement in pixels
            //---table data filter asending and decending----
            is_filter: false,
            //----invoice group---
            invoice_group: 'all',
            //----filter options are-----
            isMainDropdownOpen: false,
            is_phone: false,
            //--short--
            short_visible: [],
            resetSearch: false,
            //---whats app message---
            openWhatsAppMessage: false,
            selected_sales: null
        };
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('customer', ['currentCustomer']),
        ...mapGetters('salesList', ['currentSalesList']),
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('serviceCategories', ['currentServiceCategoryPagination']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        ...mapGetters('whatsappSetting', ['currentWhatsappData', 'companywhatsapp']),
        paginatedData() {
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                this.length_category = filteredData.length;
                return filteredData;
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data.length > 0) {
                const order = ['current_date', 'customer', 'invoice_no', 'due_amount', 'discount_type', 'grand_total', 'payment_status', 'status'];

                for (const key of order) {
                    if (key in this.data[0] && key !== 'id' && key !== 'company_id' && key !== 'user_id' && key !== 'sales_item_data' && key !== 'sales_payment' && key !== 'payment_mode' && key !== 'invoice_id' || key === 'due_in' || key === 'payment_status') {
                        const label = formatLabel(key);
                        if (key !== 'discount_type' && key !== 'shipping_type') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.invoice.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
    },
    created() {
        this.fetchApiUpdates();
        if (this.isMobile) {
            this.recordsPerPage = 20;
        }
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        this.fetchCompanyList();
        this.fetchWhatsappList();
        //---get category list---
        // this.getserviceCategoryList(1, 100);
        if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
            this.serviceCategoriesList = this.currentServiceCategory;
            if (this.currentServiceCategoryPagination && Object.keys(this.currentServiceCategoryPagination).length > 0) {
                this.pagination.service_cat = this.currentServiceCategoryPagination;
            }
            this.fetchServiceCategoryList();
        } else {
            this.fetchServiceCategoryList();
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        //---get invoice list--
        // this.getInvoiceList(this.currentPage, this.recordsPerPage);
        // console.log(this.currentSalesList, 'Waht happpninggg.....', this.currentSalesList && this.currentSalesList.data && this.currentSalesList.data.length > 0);
        if (this.currentSalesList && this.currentSalesList.data && this.currentSalesList.data.length > 0) {
            // this.open_skeleton = true;
            this.getInitialData(this.currentSalesList);
            this.fetchSalesList({ page: this.currentPage, per_page: this.recordsPerPage });
        } else {
            if (this.currentSalesList && Object.keys(this.currentSalesList).length === 0) {
                this.open_skeleton = true;
                this.fetchSalesList({ page: this.currentPage, per_page: this.recordsPerPage });
            } else {
                this.due_details = { total_revenue: 0, total_sum: 0, due_count: 0, due_sum: 0, total_count: 0 };
            }
        }
        //---get customer list--
        // this.getCustomerList(1, 100);
        // Update current time every second
        const view = localStorage.getItem('sales_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        setInterval(() => {
            this.now = new Date();
            if (this.$route.query.type === 'due_list' && !this.isDue) {
                this.getSalesDueList(1, 20);
            }
            else if (this.$route.query.type !== 'due_list' && this.isDue) {
                this.isDue = false;
                this.fetchSalesList({ page: 1, per_page: 20 });
            }
        }, 1000);
        if (this.isMobile) {
            this.items_category = 'list';
        }
        // console.log(this.currentInvoice);
        if (this.currentInvoice && this.currentInvoice.length === 0) {
            this.fetchInvoiceSetting();
        }
        //---create short list array--
        this.short_visible = ['current_date', 'customer', 'invoice_id', 'due_amount'].map((field) => ({
            label: field,
            type: '',
            visible: false,
        }));
    },
    mounted() {
        // Add and remove event listeners
        window.addEventListener("keydown", this.handleKeyPress);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        window.removeEventListener("keydown", this.handleKeyPress);
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('customer', ['fetchCustomerList']),
        ...mapActions('salesList', ['fetchSalesList']),
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData']),
        ...mapActions('dataFilter', ['updateFilter']),
        ...mapActions('dataFilter', ['updateFilterParams']),
        ...mapActions('dataFilter', ['fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('whatsappSetting', ['fetchWhatsappList', 'sendWhatsAppMessage']),
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },
        getInvoiceList(page, per_page, is_delete) {
            if (page == 1) {
                // this.open_skeleton = true;
                this.fetchSalesList({ page, per_page, is_delete });
                if (this.currentSalesList && this.currentSalesList.data) {
                    this.data = this.currentSalesList.data;
                    this.pagination.invoice = this.currentSalesList.pagination;
                }
            } else {
                this.open_skeleton = true;
                this.isDue = false;
                // console.log(this.companyId, 'EEEEEEEEEEEEEEEE');
                axios.get('/sales', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        // console.log(response.data, 'What happening...!');
                        this.open_skeleton = false;
                        this.data = response.data.data;
                        this.originalData = this.data;
                        this.pagination.invoice = response.data.pagination;
                        this.due_details = { total_revenue: response.data.total_revenue, total_sum: response.data.total_sum, due_count: response.data.due_count, due_sum: response.data.due_sum, total_count: response.data.pagination.total };
                        if (this.$route.query.type === 'due_list') {
                            this.getSalesDueList(1, 20);
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        getserviceCategoryList(page, per_page) {
            axios.get('/service_categories', { params: { company_id: this.companyId, page: page, per_page: 'all' } })
                .then(response => {
                    // console.log(response.data, 'What happening...!');
                    this.serviceCategoriesList = response.data.data;
                    this.pagination.service_cat = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })

        },
        getCustomerList(page, per_page) {
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // console.log(response.data, 'What happening...!');
                    this.customersList = response.data.data;
                    this.pagination.customer = response.data.pagination;
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.invoice.last_page) {
                this.currentPage = pageNumber;
            }
        },
        //---delete the record
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                let find_data = this.data[this.deleteIndex];
                if (find_data.id) {
                    axios.delete(`/sales/${find_data.id}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(async response => {
                            await this.updateKeyWithTime('sales_update');
                            // console.log(response.data, 'sales delete request..!');
                            this.getInvoiceList(this.data.length > 1 ? this.pagination.invoice.current_page : this.pagination.invoice.current_page - 1 !== 0 ? this.pagination.invoice.current_page - 1 : 1, this.recordsPerPage, true);
                            this.message = response.data.message;
                            this.open_message = true;
                            this.deleteIndex = null;
                        })
                        .catch(error => {
                            console.error('Error', error);
                        })
                }
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.deleteIndex = null;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.isDropdownOpen || this.items_category === 'tile') {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            // const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
            // if (!isClickInside) {
            //     this.toggleDropdown();
            // }
            if (!this.$refs.settingOPtion) {
                // Exit early if the reference is not available            
                this.isDropdownOpen = false;
                return;
            }
            const isClickInside = this.$refs.settingOPtion.contains(event.target);
            if (!isClickInside) {
                this.isDropdownOpen = false;
                // this.toggleDropdown();
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        //---sales navigation
        openSalesModal() {
            // this.showSalesModal = true;
            this.handleSalesTypeSelection('product');
        },
        closeSalesModal() {
            this.showSalesModal = false;
        },
        handleSalesTypeSelection(selectedType) {
            // console.log(selectedType, 'what type of the data....!');
            if (selectedType === 'product') {
                this.$router.push({
                    name: 'sales-invoice',
                    query: { type: 'add' }
                });
            }
        },
        handleServiceTypeSelection(selectedService) {
            // Handle service type selection logic here
            // console.log(selectedService, 'what is the selected service type...!');
            let findSeviceList = this.serviceCategoriesList.find((opt) => opt.id === selectedService);
            if (selectedService && findSeviceList) {
                // path: '/services/:type/:id/invoice/:serviceId',
                // Set query parameters in the URL
                this.$router.push({
                    name: 'generate-invoice',
                    params: { type: findSeviceList.service_category, id: findSeviceList.id, serviceId: 'sales' },
                    query: { type: 'add' }
                });
            }
        },
        //---close the message--
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //--get service data---
        getServiceData(record) {
            axios.get(`/services/${record.service_id}`, { company_id: this.companyId })
                .then(response => {
                    // console.log(response.data, 'What happening the get service..!');
                    this.service_data = response.data.data;
                    // let service_track_data = JSON.parse(newValue.service_data);
                    this.$router.push({
                        name: 'generate-invoice',
                        params: { type: this.service_data.servicecategory.service_category, id: this.service_data.servicecategory.id, serviceId: record.service_id },
                        query: { type: 'edit', invoice_no: record.id }
                    });
                })
                .catch(error => {
                    console.error('Error', error);
                })
        },
        //---record---
        startEdit(record) {
            // console.log(record, 'What happening...!', record.invoice_type instanceof Object);
            if (record.invoice_type === 'Services') {
                // console.log(this.serviceList, 'what happening in the datta', this.serviceCategoriesList);
                // let findSeviceList = this.serviceCategoriesList.find((opt) => opt.id === record.invoice_type['id']);
                // localStorage.setItem('invoice', JSON.stringify(this.data));
                this.getServiceData(record);

            } else if (record.invoice_type === 'Product' || record.invoice_type === 'product') {
                this.$router.push({
                    name: 'sales-invoice',
                    query: { type: 'edit', invoice_no: record.id }
                });
            } else if (record.invoice_type === 'Direct_Services') {
                this.$router.push({
                    name: 'sales-invoice',
                    query: { type: 'edit', invoice_no: record.id, invoice_type: 'direct' }
                });
            }
        },
        //---print record---
        printRecord(record) {
            this.$router.push({ name: 'print-preview', query: { type: 'sales_home', invoice_no: record.id } });
        },
        //--get customer list--
        getCustomerData(idValue) {
            // console.log(idValue, 'What about the value..!');
            if (this.customersList.length > 0 && idValue) {
                let findcustomer = this.customersList.find((opt) => opt.id === idValue);
                if (findcustomer) {
                    if (findcustomer.lastName) {
                        return `${findcustomer.firstName} ${findcustomer.lastName} - ${findcustomer.contactNumber}`;
                    } else {
                        return `${findcustomer.firstName} - ${findcustomer.contactNumber}`
                    }
                }
            }
        },
        //---filter dropdown
        toggleFilter() {
            this.showFilterOptions = !this.showFilterOptions;
        },
        //--hidden dropdown--
        hiddenDropdown() {
            if (!this.mouseOverIsNot) {
                this.showFilterOptions = false;
            }
        },
        mouseOverOption() {
            this.mouseOverIsNot = true;
        },
        mouseLeaveOption() {
            this.mouseOverIsNot = false;
        },
        toggleFilterSelected(option) {
            // console.log(option, 'GGGGGGGGGGGGGGG');
            // this.typeList = this.leadType.map((opt) => opt.name);
            // this.statusList = this.leadStatus.map((opt) => opt.name);
            this.selectedByValue = option;
            this.lead_filter = true;
            this.data = this.originalData;
            this.showFilterOptions = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //--filter
        closeLeadFilter(searchData, bySerial, search) {
            if (searchData) {
                // const keysData = Object.keys(searchData);
                this.filteredBy = searchData;
                this.searchByCustomer = {};
                // this.status_select = null;
                this.followup_select = 'all';
                this.open_loader = true;
                let send_data = { type: 'sales', q: this.isDue ? 'due' : 'search', per_page: this.recordsPerPage, page: 1, customer_id: searchData.customer_id ? searchData.customer_id : '', from_date: searchData.from ? searchData.from : '', to_date: searchData.to ? searchData.to : '', invoice_group: this.invoice_group >= 0 ? this.invoice_group : '' };

                axios.get('/searchs', { params: { ...send_data } })
                    .then(response => {
                        // console.log(response.data, 'Status Data');
                        this.open_loader = false;
                        this.data = response.data.data;
                        // this.pagination = response.data.pagination;
                        this.pagination.invoice = response.data.pagination;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            } else if (bySerial && !Array.isArray(bySerial) && Object.keys(bySerial).length > 0) {
                this.data = [{ ...bySerial }];
                this.filteredBy = { serial_no: search };
            } else if (bySerial && Array.isArray(bySerial)) {
                this.data = [...bySerial];
                this.filteredBy = { serial_no: search };
            }

            this.lead_filter = false;
        },
        // if (searchData) {
        //     const keysData = Object.keys(searchData);

        //     let filterTheData = this.data.filter(option => {
        //         // Check if all search criteria match
        //         return keysData.every(key => {
        //             if (key === 'date') {
        //                 return option.current_date === this.formatDate(searchData.date);
        //             }
        //             if (key === 'customer') {
        //                 return this.getCustomerData(option.customer) === searchData.customer;
        //             }

        //             if (key === 'type') {
        //                 if (option.invoice_type === 'product') {
        //                     return option.invoice_type === this.typeList[searchData.type];
        //                 } else {
        //                     return option.invoice_type.id === searchData.category;
        //                 }
        //             }
        //             if (key === 'invoice_to') {
        //                 return option.invoice_to === this.invoice_to[searchData.invoice_to];
        //             }
        //             if (key === 'status') {
        //                 return searchData.status ? option.due_amount >= searchData.status : option.due_amount === searchData.status;
        //             }
        //             return true;
        //         });
        //     });

        //     if (filterTheData.length > 0) {
        //         this.filteredBy = searchData;
        //         this.data = filterTheData;
        //     } else {
        //         filterTheData = this.data.filter(option => {
        //             return (searchData.date && this.formatDate(option.current_date) === this.formatDate(searchData.date)) ||
        //                 (searchData.customer && this.getCustomerData(option.customer) === searchData.customer) ||                            
        //                 (searchData.type && (option.invoice_type === 'product' ? option.lead_type === this.typeList[searchData.type] : option.invoice_type.id === searchData.type)) ||
        //                 (searchData.invoice_to && option.invoice_to === searchData.invoice_to) ||
        //                 (searchData.status && (option.due_amount >= searchData.status || option.due_amount === searchData.status));
        //         });
        //         if (filterTheData.length > 0) {
        //             this.filteredBy = searchData;
        //             this.data = filterTheData;
        //         } else {
        //             this.message = 'The filter does not match any records..!';
        //             this.open_message = true;
        //             this.data = this.originalData;
        //         }
        //     }
        // }
        // this.lead_filter = false; 
        // },
        //--get formayt date
        formatDate(dateString) {
            // Create a new Date object using the components in the correct order (year, month - 1, day)
            const date = new Date(dateString);
            // Get the parts of the date (year, month, day) in the correct format
            const formattedYear = date.getFullYear();
            const formattedMonth = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-indexed
            const formattedDay = String(date.getDate()).padStart(2, '0');
            // Concatenate the parts with dashes to form the desired format
            return `${formattedMonth}/${formattedDay}/${formattedYear}`;
        },
        //---reset filter--
        resetTheFilter() {
            this.data = this.originalData;
            this.filteredBy = {};
            this.customer_id = '';
            this.resetSearch = true;
            // this.currentPage = 1;
            this.fetchSalesList({ page: this.currentPage, per_page: this.recordsPerPage });
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown 
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---formated display date---
        formattedDate(timestamp, add) {
            // const date = new Date(timestamp);
            // return date.toISOString().slice(0, 16).replace('T', ' '); 
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {

            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;
                if (this.filteredBy.serial_no && this.filteredBy.serial_no !== '') {
                    this.open_skeleton_isMobile = false;
                    return;
                }
                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.invoice.last_page > this.pagination.invoice.current_page && !this.open_skeleton_isMobile && this.pagination.invoice.last_page > this.pagination.invoice.current_page) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.invoice.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            // let send_data = {
            //     type: 'leads', q: this.status_select >= 0 && this.status_select !== 5 ? this.status_select : '', filter: this.followup_select >= 0 ? this.followup_select : '', per_page: this.recordsPerPage, page: page,
            //     customer_id: this.searchByCustomer.id ? this.searchByCustomer.id : ''
            // };
            // if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
            //     // console.log(this.filteredBy, 'helllo');
            //     if (this.filteredBy.customer_id) {
            //         send_data.customer_id = this.filteredBy.customer_id
            //     }
            //     if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
            //         send_data.employer_id = this.filteredBy.assign_to[0].id;
            //     }
            //     if (this.filteredBy.type) {
            //         send_data.category = this.filteredBy.type;
            //     }
            // }
            // // console.log(this.category_type !== null && this.category_type !== 'all', 'Waht happening...!!!');
            // if (this.category_type !== null && this.category_type !== 'all') {
            //     send_data.category_id = this.category_type;
            // }
            // axios.get('/searchs', { params: { ...send_data } })
            if (this.customer_id !== '') {
                this.getSalesSearchList(page, 20, true);
            }
            else if (!this.isDue) {
                axios.get('/sales', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                    .then(response => {
                        // console.log(response.data, 'Status Data');
                        if (response.data) {
                            this.pagination.invoice = response.data.pagination;
                            this.data = [...this.data, ...response.data.data];
                        }
                        this.open_skeleton_isMobile = false;
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
            else {
                this.getSalesDueList(page, this.recordsPerPage, true);
            }
        },
        //----Overdue calculation--
        calculateDaysUntilDue(invoiceDateData) {
            if (this.now) {
                const invoiceDate = new Date(invoiceDateData);
                let dueDays = this.currentInvoice && this.currentInvoice.length > 0 ? this.currentInvoice[0].due_duration : 30;
                // console.log(dueDays, 'TT waht happeninggggg going the data....!');
                if (isNaN(invoiceDate.getTime())) {
                    console.error('Invalid invoice date');
                    return 0; // Return 0 as a fallback value in case of error
                }
                if (isNaN(this.now.getTime())) {
                    console.error('Invalid invoice date');
                    return 0; // Return 0 as a fallback value in case of error               
                }

                // Create a new date object for the due date
                const dueDate = new Date(invoiceDate);
                dueDate.setDate(invoiceDate.getDate() + dueDays);

                const currentTime = this.now.getTime();
                const dueTime = dueDate.getTime();

                // Calculate the difference in milliseconds
                const difference = dueTime - currentTime;

                // Convert milliseconds to days
                const differenceInDays = Math.ceil(difference / (1000 * 60 * 60 * 24));

                // If today is counted as the first day, increment the due days
                if (differenceInDays >= 0 && dueDate > this.now && differenceInDays <= dueDays) {
                    return differenceInDays;
                } else if (differenceInDays > dueDays) {
                    return dueDays;
                } else {
                    // Calculate the days until due (positive if due date is in the future, negative if overdue)
                    return differenceInDays;
                }
            }
        },
        //----get duelist data
        navigateDue() {
            this.$router.push({
                path: '/sales',
                query: { type: 'due_list' }
            });
            this.getSalesDueList(1, this.recordsPerPage);
        },
        getSalesDueList(page, per_page, status) {
            this.isDue = true;
            this.open_skeleton = true;
            axios.get('/searchs', { params: { company_id: this.companyId, type: 'sales', q: 'due', invoice_group: this.invoice_group, page: page, per_page: per_page } })
                .then(response => {
                    this.open_skeleton = false;
                    if (this.isMobile && status) {
                        this.data = [...this.data, ...response.data.data];
                        this.open_skeleton_isMobile = false;
                    } else {
                        this.data = response.data.data;
                    }
                    this.originalData = this.data;
                    this.pagination.invoice = response.data.pagination;
                    // this.due_details = { due_count: response.data.due_count, due_sum: response.data.due_sum };
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        getSalesDataAll() {
            this.isDue = false;
            if (this.$route.query.type === 'due_list') {
                this.$router.push('/sales');
                this.getInvoiceList(1, 20);
            } else {
                this.getInvoiceList(1, 20);
            }
        },
        getSalesSearchList(page, per_page, status) {
            // console.log(this.customer_id, 'RRRRRRRRRRRRRRRRR');

            if (this.customer_id) {
                this.open_skeleton = true;
                // console.log(this.companyId, 'EEEEEEEEEEEEEEEE');
                axios.get('/searchs', { params: { company_id: this.companyId, type: 'sales', q: this.isDue ? 'due' : 'search', invoice_group: this.invoice_group, customer_id: this.customer_id, page: page, per_page: per_page } })
                    .then(response => {
                        // console.log(response.data, 'What happening...!');
                        this.open_skeleton = false;
                        if (this.isMobile && status) {
                            this.data = [...this.data, ...response.data.data];
                            this.open_skeleton_isMobile = false;
                        } else {
                            this.data = response.data.data;
                        }
                        this.originalData = this.data;
                        this.pagination.invoice = response.data.pagination;
                        // this.due_details = { due_count: response.data.due_count, due_sum: response.data.due_sum };
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            } else {
                this.open_skeleton = true;
                // console.log(this.companyId, 'EEEEEEEEEEEEEEEE');
                let send_data = { company_id: this.companyId, type: 'sales', q: this.isDue ? 'due' : 'search', from_date: this.filteredBy.from ? this.filteredBy.from : '', to_date: this.filteredBy.to ? this.filteredBy.to : '', invoice_group: this.invoice_group >= 0 ? this.invoice_group : '', page: this.currentPage, per_page: this.recordsPerPage };
                axios.get('/searchs', { params: { ...send_data } })
                    .then(response => {
                        // console.log(response.data, 'What happening...!');
                        this.open_skeleton = false;
                        if (this.isMobile && status) {
                            this.data = [...this.data, ...response.data.data];
                            this.open_skeleton_isMobile = false;
                        } else {
                            this.data = response.data.data;
                        }
                        this.originalData = this.data;
                        this.pagination.invoice = response.data.pagination;
                        // this.due_details = { due_count: response.data.due_count, due_sum: response.data.due_sum };
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                this.customer_id = selectedData.id;
                this.filteredBy = { first_name: selectedData.first_name, contact_number: selectedData.contact_number };
                this.getSalesSearchList(1, this.isMobile ? 20 : this.recordsPerPage);
            }
        },
        resetToSearch() {
            this.customer_id = '';
            this.filteredBy = {};
            if (this.isDue && !this.resetSearch) {
                this.getSalesDueList(1, this.isMobile ? 20 : this.recordsPerPage);
            } else if (!this.resetSearch) {
                this.getInvoiceList(1, this.isMobile ? 20 : this.recordsPerPage);
            }
            this.resetSearch = false;
        },
        //----get initialize sales data
        getInitialData(salesData) {
            this.open_skeleton = false;
            if (salesData) {
                this.data = salesData.data;
                this.originalData = this.data;
                this.pagination.invoice = salesData.pagination;
                // console.log(salesData.status_counts, 'Waht happning....');
                this.due_details = { total_revenue: salesData.status_counts.total_revenue, total_sum: salesData.status_counts.total_sum, due_count: salesData.status_counts.due_count, due_sum: salesData.status_counts.due_sum, total_count: salesData.status_counts.total_count };
                if (this.$route.query.type === 'due_list') {
                    this.getSalesDueList(1, 20);
                }
            }
        },
        //---sales cancel confirmation---
        openConfirmSalesCancel(record_data) {
            this.cancelrecords_data = record_data;
            this.sales_cancel_confirmation = true;
        },
        closeSalesConfirm() {
            this.sales_cancel_confirmation = false;
        },
        confirmSalesCancel(reason_data) {
            if (this.cancelrecords_data) {
                if (this.cancelrecords_data && this.cancelrecords_data.id) {
                    this.cancelrecords_data.status = 'Cancel';
                    if (!this.cancelrecords_data.payment_code) {
                        this.cancelrecords_data.payment_code = '';
                    }
                    if (!this.cancelrecords_data.payment_date) {
                        this.cancelrecords_data.payment_date = this.cancelrecords_data.sales_payment.payment_date;
                    }
                    if (!this.cancelrecords_data.payment_amount) {
                        this.cancelrecords_data.payment_amount = this.cancelrecords_data.sales_payment.payment_amount;
                    }
                    if (!this.cancelrecords_data.customer_id) {
                        this.cancelrecords_data.customer_id = this.cancelrecords_data.sales_payment.customer_id;
                    }
                    if (!this.cancelrecords_data.payment_notes) {
                        this.cancelrecords_data.payment_notes = this.cancelrecords_data.sales_payment.payment_notes;
                    }
                    if (!this.cancelrecords_data.created_by) {
                        this.cancelrecords_data.created_by = this.cancelrecords_data.sales_payment.created_by;
                    }
                    if (!this.cancelrecords_data.payment_type) {
                        this.cancelrecords_data.payment_type = this.cancelrecords_data.sales_payment.payment_type;
                    }
                    // if (this.cancelrecords_data.sales_item_data) {
                    //     this.cancelrecords_data.sales_item_data = JSON.stringify(this.cancelrecords_data.sales_item_data);
                    // }
                    // if (this.cancelrecords_data.sales_payment) {
                    //     this.cancelrecords_data.sales_payment = JSON.stringify(this.cancelrecords_data.sales_payment);
                    // }
                    axios.put(`/sales/${this.cancelrecords_data.id}`, { company_id: this.companyId, reason: reason_data, user_id: this.userId, invoice_id: this.cancelrecords_data.invoice_id, sales_data: { ...this.cancelrecords_data } })
                        .then(response => {
                            this.updateKeyWithTime('sales_update');
                            // console.log(response.data, 'sales delete request..!');
                            this.getInvoiceList(1, this.recordsPerPage);
                            this.message = response.data.message;
                            this.open_message = true;
                            this.deleteIndex = null;
                        })
                        .catch(error => {
                            console.error('Error', error);
                        })
                }
            }
            this.sales_cancel_confirmation = false;
        },
        //---payment---
        openPaymentModal(record_data) {
            // console.log(record_data, 'what happening....');
            this.get_all_data = record_data;
            // console.log(this.get_all_data.sales_payment, 'TTTTTTTTTTTTTTTTTTTT');
            // this.paymentData = typeof this.get_all_data.payment_mode === 'string' ? JSON.parse(this.get_all_data.payment_mode) : this.get_all_data.payment_mode;
            this.paymentData = this.get_all_data.sales_payment;
            this.balance_due_amount = this.get_all_data.due_amount;
            // if(this.currentInvoice)
            this.payment_type = 'multiple';
            this.open_paymentModal = true;

        },
        closePaymentModal(data) {
            if (data && data.type) {
                this.paymentData = data.payment_data;
                if (data.type === 'print') {
                    // this.saveData('Success');
                    this.printing = true;
                    this.saveData('Success', data.form_data ? data.form_data : '');
                }
                else {
                    this.saveData('Success', data.form_data ? data.form_data : '');
                    // console.log('save the data..!');
                    // this.$router.go(-1);
                }
            }
            this.open_paymentModal = false;
        },
        saveData(type, formValues) {
            if (this.get_all_data && this.get_all_data.id) {
                this.open_loader = true;

                let invoice_data = {
                    company_id: this.companyId,
                    // payment_mode: JSON.stringify(this.paymentData),
                };
                let sales_data = {
                    due_amount: formValues.balance,
                    // payment_mode: JSON.stringify(this.paymentData),
                    balance_amount: formValues.balance,
                    return_amount: formValues.return,
                    // payment_notes: formValues.note ? formValues.note : '',
                    // payment_code: '',
                    // payment_date: this.getCurrentDate(),
                    // payment_type: JSON.stringify(this.paymentData),
                    // payment_amount: this.get_all_data.grand_total,
                    created_by: this.userId,
                    company_id: this.companyId,
                    customer_id: this.get_all_data.customer.id,
                };

                // console.log(servicesItems, 'hello', otherItems);
                axios.put(`/sales/${this.get_all_data.id}`, { company_id: this.companyId, user_id: this.userId, sales_data: { ...sales_data, sales_item_data: this.get_all_data.sales_item_data, invoice_data: { ...invoice_data }, sales_payment: this.paymentData } })
                    .then(response => {
                        // console.log(response.data, 'update response...!');
                        this.open_loader = false;
                        if (this.printing) {
                            this.$router.push({ name: 'print-preview', query: { invoice_no: response.data.data.id } })
                        }
                        this.getInvoiceList(1, this.recordsPerPage);
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                        this.openMessage(error.response.data.message);
                    })
            }
        },
        getCurrentDate() {
            // Get current date
            const currentDateObj = new Date();

            const year = currentDateObj.getFullYear();
            const month = String(currentDateObj.getMonth() + 1).padStart(2, '0'); // Adding 1 because months are zero-indexed
            const day = String(currentDateObj.getDate()).padStart(2, '0');
            const hours = String(currentDateObj.getHours()).padStart(2, '0');
            const minutes = String(currentDateObj.getMinutes()).padStart(2, '0');
            const seconds = String(currentDateObj.getSeconds()).padStart(2, '0');

            // Format the date and time as YYYY-MM-DD HH:MM:SS
            let current_date_data = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            return current_date_data;
        },
        //---Share payment link--
        openWhatsApp(record) {
            this.selected_sales = { ...record };
            this.openWhatsAppMessage = true;
            // console.log(record, 'what happening the data.....', this.currentInvoice);
            // let message_data = `Hi ${record?.customer?.first_name || 'Customer'} sales invoice for #${record?.invoice_id || 'invoice'}. your payment is due ${this.currentCompanyList && this.currentCompanyList.currency === 'INR' ? '\u20b9' : this.currentCompanyList.currency} ${record.due_amount}. \nyou can pay to the \n${this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].bank_details ? this.currentInvoice[0].bank_details : this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].business_contact ? this.currentInvoice[0].business_contact : 'Please contact support'}  \n by ${this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].name ? this.currentInvoice[0].name : 'Company'}`;
            // let encodedMessage = encodeURIComponent(message_data);
            // let phone = record?.customer?.contact_number || '';

            // // Check the user agent to determine the device type
            // const userAgent = navigator.userAgent || navigator.vendor || window.opera;
            // let whatsappUrl = '';
            // if (/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
            //     var message = encodeURIComponent(message_data);
            //     var whatsapp_url = `whatsapp://send?phone=${phone}&text=${message}`;
            //     window.location.href = whatsapp_url;                
            // }

            // else if (/ iPad | iPhone | iPod /.test(userAgent) && !window.MSStream) {
            //     // iOS devices
            //     whatsappUrl = `whatsapp://send?phone=+91${phone}&text=${encodedMessage}`;
            //     // Open WhatsApp in a new window or tab
            //     window.open(whatsappUrl, '_blank');
            // } else {
            //     // Default to WhatsApp Web for other devices
            //     whatsappUrl = `https://web.whatsapp.com/send?phone=+91${phone}&text=${encodedMessage}`;
            //     // Open WhatsApp in a new window or tab
            //     window.open(whatsappUrl, '_blank');
            // }
        },
        closeWhatsappMessage() {
            this.openWhatsAppMessage = false;
            this.selected_sales = {};
        },
        navigateToWhatsApp() {
            this.$router.push({ name: 'whatsappsetting' });
        },
        //--validate the role--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        filterDataBy(type, key) {
            this.showSortIcons(false, type, key);

            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'sales' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'sales', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'sales' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'sales', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        //----table action----
        showModal(index, event) {
            if (index) {
                this.lastMousePosition = { x: event.layerX * 1, y: event.layerY * 1 };
                // Start a timer to check after 5000ms
                this.hoverTimer = setTimeout(() => {
                    if (this.isMouseInSamePosition(event)) {
                        this.modalData = index;
                        this.modalPosition = { x: ((event.layerX * 1) + 25), y: (event.layerY * 1) + 25 };
                        this.isModalVisible = true;
                    }
                }, 200);
            }
        },
        hideModal() {
            clearTimeout(this.hoverTimer); // Clear any existing timer
            setTimeout(() => {
                if (!this.isHoveringModal) {
                    this.isModalVisible = false;
                }
            }, 200);
        },
        toggleHoverModal(status) {
            this.isHoveringModal = status;
            if (!status) {
                this.hideModal();
            }
        },
        isMouseInSamePosition(event) {
            // Allow for a 10-pixel tolerance in mouse movement
            const xDiff = Math.abs(this.lastMousePosition.x - event.layerX);
            const yDiff = Math.abs(this.lastMousePosition.y - event.layerY);
            return xDiff <= this.tolerance && yDiff <= this.tolerance;
        },
        closeAllModals() {
            // Close all modals
            this.open_confirmBox = false;
            this.open_message = false;
            this.showSalesModal = false;
            this.lead_filter = false;
            this.sales_cancel_confirmation = false;
            this.open_paymentModal = false;
        },
        //--filter dropdown---
        toggleMainDropdown() {
            this.isMainDropdownOpen = !this.isMainDropdownOpen;
            if (this.isMainDropdownOpen) {
                document.addEventListener('click', this.handleClickOutsideFilter);
            } else {
                // this.showSubDropdown = false;
                document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },

        handleClickOutsideFilter(event) {
            const mainDropdown = this.$refs.dropdownContainerFilter;
            // console.log(event.target, 'Waht happning the data....', mainDropdown.contains(event.target));
            if (mainDropdown && !mainDropdown.contains(event.target)) {
                this.toggleMainDropdown();
                // document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        //--filter option--
        changeFilter(opt) {
            this.filter_option = opt;
            this.toggleMainDropdown();
            if (opt === 'date') {
                this.filter_date = !this.filter_date;
                if (!this.filter_date) {
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutsideDate);
                } else {
                    document.addEventListener('click', this.handleClickOutsideDate);
                }
            }
            if (opt === 'customer') {
                this.toggleFilterSelected('by Customer');
            }
            if (opt === 'employee') {
                this.toggleFilterSelected('by Employee');
            }
            if (opt === 'by Date') {
                this.toggleFilterSelected('by Date');
            }
            if (opt === 'serial_number') {
                this.toggleFilterSelected('by Serial');
            }
        },
        //-move to outstanding--
        moveOutstanding() {
            if (this.due_details.due_sum > 0) {
                this.$router.push({ path: '/reports', query: { type: 'Outstanding' } });
            }
        },
        getFormattedDate(date) {
            // Check for the specific invalid date and format if valid
            return date.substring(0, 10) !== '-000001-11' ? this.formatDateTime(date) : '';
        },
        //--short icon for filter---
        showSortIcons(data, type, key) {
            if (data) {
                this.short_visible.forEach(opt => {
                    if (opt.label === data.field) {
                        opt.visible = true;
                    }
                });
            } else if (type && key) {
                this.short_visible.forEach(opt => {
                    if (opt.label === key) {
                        opt.type = type;
                    } else {
                        opt.type = '';
                    }
                });
            }
        },
        hideSortIcons(data) {
            if (data) {
                this.short_visible.forEach(opt => {
                    if (opt.label === data.field) {
                        opt.visible = false;
                    }
                });
            }
        },
        //--reload the table data----
        refreshDataTable() {
            // this.open_skeleton = true;
            this.fetchSalesList({ page: this.currentPage, per_page: this.recordsPerPage });
            this.showSortIcons(false, 'type', 'key');
        },
        //--filter--
        getSortType(column) {
            if (column && this.short_visible && this.short_visible.length > 0) {
                // Find the relevant short_visible entry
                const shortVisibleEntry = this.short_visible.find(
                    (entry) => entry.label === column.field
                );

                if (!shortVisibleEntry) {
                    return ''; // Default sort type if not found
                }

                // Determine sort type based on current type
                if (shortVisibleEntry.type === '') {
                    this.filterDataBy('asc', column.field);
                } else if (shortVisibleEntry.type === 'asc') {
                    this.filterDataBy('desc', column.field);
                } else if (shortVisibleEntry.type === 'desc') {
                    this.filterDataBy('asc', column.field);
                }
            }
        },
        isShortVisible(field) {
            const entry = this.short_visible.find((item) => item.label === field);
            if (entry) {

                return entry.visible || entry.type !== '';
            } else {
                return false;
            }
        },
        getSortTypeDisplay(field) {
            const entry = this.short_visible.find((item) => item.label === field);
            if (entry) {
                return entry?.type || ''; // Returns 'asc', 'desc', ,or ''
            } else {
                return false;
            }
        },
        isHighlighted(field, type) {
            const entry = this.short_visible.find((item) => item.label === field);
            if (entry) {
                return entry.type !== '';
            } else {
                return false;
            }
        },
        //--format currency data---
        formatCurrency(value) {
            if (!value) return '0';
            let roundValue = value.toFixed();
            return new Intl.NumberFormat('en-IN').format(roundValue);
        },
        //---on key press to make pagination--
        handleKeyPress(event) {
            if (event.key === "ArrowRight" && this.currentPage < this.pagination.invoice.last_page) {
                this.currentPage++;
            } else if (event.key === "ArrowLeft" && this.currentPage > 1) {
                this.currentPage--;
            }
        }
    },
    watch: {
        originalData(newValue) {
            // Automatically send data to the parent when dataToSend changes
            this.$emit('dataToParent', newValue);
        },
        searchedData(newValue) {
            // console.log(newValue, 'RRRRR');
            if (!this.isEmptyObject(newValue)) {
                this.data = [{ ...newValue }];
            }
            else {
                this.data = this.originalData;
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                // console.log(this.customer_id !== '', 'RWRWRWRW', this.filteredBy, 'Datatatatta', Object.keys(this.filteredBy).length > 0, 'RWRWRWRWRWR');

                if (this.customer_id !== '' || Object.keys(this.filteredBy).length > 0) {
                    this.getSalesSearchList(newValue, this.recordsPerPage);
                }
                else if (!this.isDue) {
                    this.getInvoiceList(newValue, this.recordsPerPage);
                } else {
                    this.getSalesDueList(newValue, this.recordsPerPage);
                }
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                this.currentPage = 1;
                if (this.customer_id !== '' || Object.keys(this.filteredBy).length > 0) {
                    this.getSalesSearchList(1, newValue);
                }
                else if (!this.isDue) {
                    this.getInvoiceList(1, newValue);
                } else {
                    this.getSalesDueList(1, newValue);
                }
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                    if (this.recordsPerPage < 10) {
                        if (!this.isDue) {
                            this.getInvoiceList(1, 10);
                        } else {
                            this.getSalesDueList(1, 10);
                        }
                    }
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('sales_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.serviceCategoriesList = newValue;
                    if (this.currentServiceCategoryPagination && Object.keys(this.currentServiceCategoryPagination).length > 0) {
                        this.pagination.service_cat = this.currentServiceCategoryPagination;
                    }
                }
            }
        },
        currentSalesList: {
            deep: true,
            handler(newValue) {
                this.getInitialData(newValue);
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    // console.log(newValue, 'New Value data...............');
                    this.is_filter = false;

                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        showSalesModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        lead_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        sales_cancel_confirmation: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_paymentModal: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        invoice_group: {
            deep: true,

            handler(newValue) {
                // console.log('llllllllllllllllll', newValue, 'TTTTTTTTTTTT');
                if (this.isDue) {
                    this.getSalesSearchList(1, this.recordsPerPage);
                } else {
                    this.getSalesSearchList(1, this.recordsPerPage);
                }

            }
        }
    },
}
</script>

<style scoped>
.manualStyle {
    overflow: auto;
    height: 100vh;
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}
</style>
