// store/modules/device.js
const state = {
    isIOS: false, // Store the iOS detection result
  };
  
  const mutations = {
    // Mutation to update the state
    setIsIOS(state, value) {
      state.isIOS = value;
      },
      RESET_STATE(state) {
        state.isIOS = false;
    },
  };
  
  const actions = {
    // Action to detect if it's an iOS device (both native or browser)
    detectIOS({ commit }) {
      // Check if the app is running as a native iOS app (e.g., using Capacitor or Cordova)
      const isNativeIOS = window.cordova || window.Capacitor; // Modify based on your native app platform
  
      if (isNativeIOS) {
        // If running as a native app on iOS, set isIOS to true
        commit('setIsIOS', true);
      } else {
        // If it's a browser and the user is on an iOS device
        const isBrowserIOS = /iPhone|iPad|iPod/i.test(navigator.userAgent);
        commit('setIsIOS', isBrowserIOS);
      }
    },
  };
  
  const getters = {
    // Getter to access the isIOS state
    isIOS: (state) => state.isIOS,
  };
  
export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters,
  };
  