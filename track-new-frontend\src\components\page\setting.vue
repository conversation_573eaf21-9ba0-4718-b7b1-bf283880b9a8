<template>
  <div class="flex text-sm h-screen relative">
    <!-- Sidebar (conditionally rendered based on isMobile) -->
    <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
      <div>
        <sidebar v-if="!isMobile" :route_item="route_item" :update_list="update_sidebar"
          :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
      </div>
    </div> -->
    <!-- Content area with its own scroll -->
    <div class="flex flex-col flex-grow overflow-y-auto w-full" :class="{ 'manualStyle mt-[57px]': !isMobile }">
      <!-- Headbar with its content -->
      <!-- <headbar @toggle-sidebar="toggleSidebar" @refresh_store="refresh_store"></headbar> -->
      <div class="my-custom-margin" :class="{ 'flex flex-col': isMobile, 'flex flex-row': !isMobile }">
        <div :class="{ 'mt-[60px] m-1': isMobile, 'm-2 pt-5 p-2 w-[300px]': !isMobile }">
          <horizontalbar @item-selected="handleItemSelected" :item="item_isopen" :isMobile="isMobile"
            class="non-printable"></horizontalbar>
        </div>
        <div class="w-full">
          <div v-if="item_isopen === 1" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <general :companyId="companyId" :userId="userId" :store_refresh="store_refresh"
              @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate"></general>
          </div>
          <div v-if="item_isopen === 2" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <invoice :companyId="companyId" :userId="userId" :isMobile="isMobile" :store_refresh="store_refresh"
              @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate">
            </invoice>
          </div>
          <div v-if="item_isopen === 11" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <proforma :companyId="companyId" :userId="userId" :isMobile="isMobile" :store_refresh="store_refresh"
              @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate">
            </proforma>
          </div>
          <div v-if="item_isopen === 12" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <estimation :companyId="companyId" :userId="userId" :isMobile="isMobile" :store_refresh="store_refresh"
              @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate">
            </estimation>
          </div>
          <div v-if="item_isopen === 13" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <jobsheet :companyId="companyId" :userId="userId" :isMobile="isMobile" :store_refresh="store_refresh"
              @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate">
            </jobsheet>
          </div>
          <div v-if="item_isopen === 3" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <sms :companyId="companyId" :userId="userId"></sms>
          </div>
          <div v-if="item_isopen === 4" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <whatsapp :companyId="companyId" :userId="userId"></whatsapp>
          </div>
          <div v-if="item_isopen === 5" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <email :companyId="companyId" :userId="userId"></email>
          </div>
          <div v-if="item_isopen === 6" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <account :companyId="companyId" :userId="userId" :store_refresh="store_refresh"
              @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate"></account>
          </div>
          <!-- <div v-if="item_isopen === 7" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-5 m-0 ml-2 mr-2 mb-2 mt-2': isMobile, 'p-5 m-5 p-5 ml-0  mt-7': !isMobile }">
            <users :companyId="companyId" :userId="userId" :store_refresh="store_refresh"
              :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></users>
          </div> -->
          <!-- <div v-if="item_isopen === 8" class="p-5 relative m-2">
        <roles :companyId="companyId" :userId="userId"></roles>
      </div> -->
          <div v-if="item_isopen === 9" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-5 m-0 ml-2 mr-2 mb-2 mt-2': isMobile, 'p-5 m-5 p-5 ml-0  mt-7': !isMobile }">
            <socialMedia :companyId="companyId" :userId="userId" :store_refresh="store_refresh"
              @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate">
            </socialMedia>
          </div>
          <div v-if="item_isopen === 10" class=" rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-5 m-0 ml-2 mr-2 mb-2 mt-2': isMobile, 'p-5 m-5 p-5 ml-0  mt-7': !isMobile }">
            <enablefeatures :companyId="companyId" :userId="userId" :store_refresh="store_refresh"
              @updateBar="handlechangeStatus" @is-sales-save="salessaveData" :save_success="save_success"
              @updatesalesData="saveDataUpdate"></enablefeatures>
          </div>
          <!---default setting-->
          <div v-if="item_isopen === 14" class="rounded border relative bg-white shadow-lg"
            :class="{ 'mb-[60px] p-2 m-0 ml-1 mt-2 mb-2 mr-1': isMobile, 'm-5 p-5 ml-0  mt-7': !isMobile }">
            <defaultSetting :companyId="companyId" :userId="userId" :isMobile="isMobile" :store_refresh="store_refresh"
              @is-sales-save="salessaveData" :save_success="save_success" @updatesalesData="saveDataUpdate">
            </defaultSetting>
          </div>
          <dialogConfirmBox :visible="show_confirm_box" :message="message" :type="'sales'" @save="saveData"
            @ok="withoutSave" @cancel="cancelData"></dialogConfirmBox>
        </div>
      </div>
    </div>

    <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
    <!-- <div v-if="isMobile && isSidebarOpen" class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden"
      @click="closeSidebar">
      <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item" :update_list="update_sidebar"
        :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
      </sidebar>
    </div>
    <bottombar v-if="isMobile" :selected_btn_btm="'setting'" :update_list="update_sidebar"></bottombar> -->
  </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/setting_categories/headbar.vue';
import horizontalbar from '../supporting/horizontalbar.vue';
import general from '../supporting/setting_categories/general.vue';
import account from '../supporting/setting_categories/accounting.vue';
import invoice from '../supporting/setting_categories/invoice.vue';
import sms from '../supporting/setting_categories/sms.vue';
import whatsapp from '../supporting/setting_categories/whatsapp.vue';
import email from '../supporting/setting_categories/email.vue';
// import users from '../supporting/setting_categories/users.vue';
// import roles from '../supporting/setting_categories/roles.vue';
// import bottombar from '../supporting/dashboard/bottombar.vue';
import socialMedia from '../supporting/setting_categories/socialMedia.vue';
import enablefeatures from '../supporting/setting_categories/enablefeatures.vue';
import proforma from '../supporting/setting_categories/proforma.vue';
import estimation from '../supporting/setting_categories/estimation.vue';
import jobsheet from '../supporting/setting_categories/jobsheet.vue';
import dialogConfirmBox from '../supporting/dialog_box/dialogConfirmBox.vue';
import defaultSetting from '../supporting/setting_categories/defaultSetting.vue';
import { useMeta } from '@/composables/useMeta';

export default {
  name: 'settings',
  components: {
    // sidebar,
    // headbar,
    horizontalbar,
    general,
    account,
    invoice,
    sms,
    whatsapp,
    email,
    // users,
    // roles,
    // bottombar,
    socialMedia,
    //---enable features--
    enablefeatures,
    proforma,
    estimation,
    jobsheet,
    dialogConfirmBox,
    defaultSetting
  },
  data() {
    return {
      isMobile: false,
      isSidebarOpen: false,
      item_isopen: 1,
      route_item: 9,
      companyId: null,
      userId: null,
      store_refresh: false,
      update_sidebar: false,
      //---open the modal box don't go back---
      anyModalOpen: false,
      updateModalOpen: false,
      //---store data----
      isStoreData: false,
      save_success: false,
      //---confirmbox data---
      show_confirm_box: false,
      message: '',
    };
  },
  setup() {
    const pageTitle = 'Settings';
    const pageDescription = 'Configure general settings for your account or application, including basic information, display preferences, and default configurations to optimize user experience.';
    useMeta(pageTitle, pageDescription);

    return { pageTitle };
  },
  methods: {
    refresh_store() {
      this.store_refresh = !this.store_refresh;
    },
    toggleSidebar() {
      // console.log('hello......');
      this.isSidebarOpen = !(this.isSidebarOpen);
      // console.log(isSidebarOpen, 'What about value...', this.isMobile);
    },
    closeSidebar() {
      this.isSidebarOpen = false;
    },
    handleSelectSidebarOption() {
      // Logic to handle selected sidebar option
      // Close sidebar after option is selected (if needed)
      this.closeSidebar();
    },
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
    handleItemSelected(item) {
      // Handle the selected item in the parent component
      // console.log('Selected Item:', item);
      // You can pass the selected item to other child components or perform any other actions.
      this.item_isopen = item;
      localStorage.setItem('previousView', JSON.stringify({ view: item }));
    },
    handlechangeStatus() {
      this.update_sidebar = true;
    },
    //---is modal any open---
    emitUpdateIsOpen(value) {
      if (value !== undefined) {
        this.anyModalOpen = value;
      }
    },
    //---in case user not store data ---
    salessaveData(value) {
      // console.log(value, 'what about the data.....');
      this.isStoreData = value;
    },
    //---save data---
    saveData() {
      // console.log('save data....');
      this.show_confirm_box = false;
      this.save_success = true;
    },
    cancelData() {
      // console.log('cancel data....');
      this.show_confirm_box = false;
    },
    withoutSave() {
      // console.log('without save data...');
      this.show_confirm_box = false;
      this.isStoreData = false;
      this.$router.go(-1);
    },
    //--update sles save--
    saveDataUpdate() {
      this.save_success = false;
    }
  },
  mounted() {
    const collectForm = localStorage.getItem('track_new');
    if (collectForm) {
      let dataParse = JSON.parse(collectForm);
      // // console.log(dataParse, 'WWWWWWWWWWWW');

      this.companyId = dataParse.company_id;
      this.userId = dataParse.user_id + '';
    }
    this.updateIsMobile(); // Initial check
    window.addEventListener('resize', this.updateIsMobile);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateIsMobile);
  },
  watch: {
    isSidebarOpen: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.anyModalOpen = newValue;
        } else {
          this.anyModalOpen = newValue;
        }
      }
    }
  },
  //---validate is any modal open andstrict the page naviagtion-
  beforeRouteLeave(to, from, next) {
    if (this.anyModalOpen || this.isStoreData) {
      if (this.isStoreData) {
        // alert('are you sure get back?');
        this.message = 'Are you sure you want to go back? Unsaved data may be lost...!';
        this.show_confirm_box = true;
      }
      if (this.anyModalOpen) {
        // If any modal is open, close all modals
        this.updateModalOpen = !this.updateModalOpen;
      }
      // Prevent the route from changing
      next(false);
    } else {
      // If no modals are open, allow the navigation
      next();
    }
  },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
  display: none;
}

@media print {

  /* Additional styles for printing */
  body {
    background-color: white;
  }

  .non-printable {
    display: none;
  }
}
</style>
