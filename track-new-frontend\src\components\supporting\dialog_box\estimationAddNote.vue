<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded-lg overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <!-- Modal Header -->
            <h3 class="text-xl font-bold mb-4 text-center text-gray-900 rounded-t-lg py-4 bg-gray-300">
                Add Notes </h3>
            <button class="absolute top-0 right-1 text-xl text-red-700 hover:text-red-600" @click="closeModal">
                <font-awesome-icon icon="fa-regular fa-circle-xmark" />
            </button>
            <div class="p-6 pt-1">
                <div class="pb-3">
                    <label class="text-gray-900 py-1 block">Notes:</label>
                    <textarea ref="notes" rows="4" v-model="formValues.notes"
                        class="p-1 border border-gray-400 w-full"></textarea>
                </div>
                <!-- Modal Footer -->
                <div class="flex justify-end space-x-4">
                    <button @click="closeModal"
                        class="py-2 px-4 bg-gray-300 shadow-inner shadow-gray-200 border border-gray-400 text-gray-800 rounded-lg hover:bg-gray-400 transition duration-200">
                        Cancel
                    </button>
                    <!-- :disabled="!canProceed" -->
                    <button @click="saveNotes"
                        class="py-2 px-4 bg-green-600 text-white rounded-lg shadow-inner shadow-green-200 border border-green-700 hover:bg-green-700 transition duration-200 disabled:opacity-50 disabled:cursor-not-allowed">
                        Save
                    </button>
                </div>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        existData: String,
    },
    data() {
        return {
            formValues: {},
            isOpen: false,
            //---toaster----
            type_toaster: 'info',
            message: '',
            show: false,
        };
    },
    computed: {

    },
    methods: {
        closeModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close');
            }, 300);
        },
        saveNotes() {
            if (this.formValues.notes) {
                this.isOpen = false;
                setTimeout(() => {
                    let share_data = { notes: this.formValues.notes };
                    this.$emit('close', share_data);
                }, 300);
            } else {
                this.message = 'Please fill notes content..!';
                this.show = true;
            }
        },
        handleFocus() {
            this.$nextTick(() => {
                const refName = 'notes';
                const inputElement = this.$refs[refName];
                // console.log(inputElement, 'EEEEe');
                if (inputElement) {
                    inputElement.focus();
                }
            });
        },
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
                if (newValue) {
                    this.handleFocus();
                }
            }, 100);
        },
        existData: {
            deep: true,
            handler(newValue) {
                if (newValue !== '') {
                    this.formValues.notes = newValue;
                }
            }
        }
    }
};
</script>

<style scoped>
/* Optional: Additional styling for modal */
</style>