export function isRestrictedDevice() {
  return /iPad|iPhone|iPod|Macintosh/.test(navigator.userAgent);
  // const userAgent = navigator.userAgent;

  // // Check if it's an iOS device and is not a browser
  // const isIOSApp = /iPad|iPhone|iPod/.test(userAgent) && userAgent.includes("TrackNew"); // Replace "MyAppName" with your app's unique identifier

  // // Return true only if it's an iOS app
  // return isIOSApp;
  }
