import { createRouter, createWebHistory } from "vue-router";
import store from "@/store";

//---login----
import login from "../components/page/login.vue";

//--website bulder--
import WebsiteBuilder from "@/pages/website-builder/WebsiteBuilder.vue";
import WebsiteDashboard from "@/pages/website-builder/DashboardPage.vue";
import WebsiteTemplate from "@/pages/website-builder/TemplatePage.vue";

//---mobile login---
import mobileLogin from "../components/page/mobileLogin.vue";

//---sign up----
import signup from "../components/page/signup.vue";

//----Dashboard---
import dashboard from "../components/page/dashboard.vue";

//----Services-----
import serviceHome from "../components/page/serviceHome.vue";

import servicesCategoryList from "../components/page/servicesCategoryList.vue";

import addAndEditServices from "../components/page/addAndEditService.vue";

import displayServiceData from "../components/page/displayServiceData.vue";

import viewServiceData from "../components/page/viewServiceData.vue";

//----invoice--
import generateInvoice from "../components/page/generateInvoice.vue";

//---Customers-----
import customers from "../components/page/customers.vue";

import customersView from "../components/page/customersView.vue";

//---Categories----
import categories from "../components/page/categories.vue";

import serviceCategoryCreateForm from "../components/page/serviceCategoryCreateForm.vue";

//---reports---
import reports from "../components/page/reports.vue";

//---setting---
import setting from "../components/page/setting.vue";

//---leads---
import leads from "../components/page/leads.vue";
import viewLead from "../components/page/viewLead.vue";

//---estimation----
import estimation from "../components/page/estimation.vue";
import addEstimation from "../components/page/addEstimation.vue";

//---proforma----
import proformaInvoice from "../components/page/proformaInvoice.vue";
import addProformaInvoice from "@/components/page/addProformaInvoice.vue";

//---AMc--
import amcHome from "../components/page/amcHome.vue";
import amcView from "../components/page/amcView.vue";

//---sales---
import sales from "../components/page/sales.vue";

import salesInvoice from "../components/page/salesInvoice.vue";

import printPreview from "../components/page/printPreview.vue";

import paymentInHome from "@/components/page/paymentInHome.vue";

import paymentInInvoice from "../components/page/paymentInInvoice.vue";

//---inventory---
import inventory from "../components/page/inventory.vue";

import purchaseOrder from "../components/page/purchaseOrder.vue";

import addPurchaseOrder from "../components/page/addPurchaseOrder.vue";

import warehouse from "../components/page/warehouse.vue";

import supplier from "../components/page/supplier.vue";

import stockAdjust from "../components/page/stockAdjust.vue";

import paymentOutHome from "@/components/page/paymentOutHome.vue";

import paymentOutHomeCreate from "@/components/page/paymentOutHomeCreate.vue";

//---Open RMA----
import openRMA from "@/components/page/openRMA.vue";

import editRma from "@/components/page/editRma.vue";

//---expense---
import expense from "../components/page/expense.vue";

//----page not found---
import notfound from "../components/notFound.vue";

import underMaintain from "@/components/page/underMaintain.vue";

//---pages about---
import pagesController from "../components/page/pagesController.vue";

//---subscriptions----
import subscriptions from "@/components/page/subscriptions.vue";

import subscriptionOrders from "@/components/page/subscriptionOrders.vue";

//---web enquiry---
import webEnquiryPage from "@/components/page/webEnquiryPage.vue";

//----whatsapp settings--
import whatsappSetting from "@/components/page/whatsappSetting.vue";
//---settings---
import users from "@/components/page/usersPage.vue";
//--no-access---
import NoAccessPage from "@/components/page/NoAccessPage.vue";

const routes = [
  //-----login-----
  { path: "/login", name: "username_login", component: login },

  //---mobile login----
  { path: "/mobilelogin", name: "mobile_login", component: mobileLogin },

  //---sign up----
  { path: "/signup", name: "signup", component: signup },

  //----Dashboard---
  {
    path: "/",
    name: "dashboard",
    component: dashboard,
  },
  //----leads---
  {
    path: "/leads",
    name: "lead",
    component: leads,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "Sub_Admin",
        "admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
        "Service Engineer",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['leads'],
    },
  },
  {
    path: "/leads/edit",
    name: "leadEdit",
    component: viewLead,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['leads'],
    },
  },
  //--website enquiry---
  {
    path: "/websiteenquiry",
    name: "websiteEnquiry",
    component: webEnquiryPage,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "Sub_Admin",
        "admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
        "Service Engineer",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['leads'],
    },
  },
  //---estimation----
  {
    path: "/estimation",
    name: "estimation",
    component: estimation,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['estimations'],
    },
  },
  {
    path: "/estimation/:type",
    name: "addEstimation",
    component: addEstimation,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['estimations'],
    }
  },
  {
    path: "/estimation/preview",
    name: "estimate-preview",
    component: printPreview,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['estimations'],
    }
  },
  //---proforma invoice----
  {
    path: "/proforma",
    name: "proforma",
    component: proformaInvoice,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['proforma'],
    },
  },
  {
    path: "/proforma/:type",
    name: "addProformaInvoice",
    component: addProformaInvoice,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['proforma'],
    }
  },
  {
    path: "/proforma/preview",
    name: "proforma-preview",
    component: printPreview,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['proforma'],
    }
  },
  //----Services-----
  {
    path: "/services",
    name: "services",
    component: serviceHome,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
        "Service Engineer",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['services'],
    },
  },

  // {
  //     path: '/services/:type/:id',
  //     name: 'service-category',
  //     component: servicesCategoryList,
  //     props: true,
  // },

  {
    path: "/services/:type/:id/add",
    name: "service-category-add",
    component: addAndEditServices,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['services'],
    }
  },
  {
    path: "/services/:type/:id/edit/:viewId",
    name: "service-category-view",
    component: displayServiceData,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['services'],
    }
  },
  {
    path: "/services/:type/:id/view/:serviceId",
    name: "service-data-view",
    component: viewServiceData,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['services'],
    }
  },
  {
    path: "/services/:type/:id/invoice/:serviceId",
    name: "generate-invoice",
    component: generateInvoice,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['services'],
    }
  },
  //---AMC----
  {
    path: "/amc",
    name: "amc",
    component: amcHome,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
        "Service Engineer",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['amcs'],
    },
  },
  {
    path: "/amc/view",
    name: "amcView",
    component: amcView,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['amcs'],
    }
  },

  //---Customers-----
  {
    path: "/customers",
    name: "customers",
    component: customers,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
        "Service Engineer",
      ],
      requiredPermissions: ["create_leads"],
    },
  },
  {
    path: "/customers/view/:id",
    name: "customers-view",
    component: customersView,
  },

  //---sales---
  {
    path: "/sales",
    name: "sales",
    component: sales,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['sales'],
    },
  },
  {
    path: "/sales/invoice",
    name: "sales-invoice",
    component: salesInvoice,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['sales'],
    }
  },
  {
    path: "/sales/invoice/preview",
    name: "print-preview",
    component: printPreview,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['sales'],
    }
  },
  {
    path: "/sales/invoice/paymentin",
    name: "payment_home",
    component: paymentInHome,
  },

  {
    path: "/sales/invoice/paymentin/createnew",
    name: "payment_in",
    component: paymentInInvoice,
  },

  //---inventory---
  {
    path: "/items",
    name: "items",
    component: inventory,
    meta: {
      requiresAuth: true,
      requiredRoles: ["admin", "Sub_Admin"],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['inventroy'],
    },
  },
  {
    path: "/items/purchaseOrder",
    name: "purchase_order",
    component: purchaseOrder,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['inventroy'],
    }
  },
  {
    path: "/items/purchaseOrder/form",
    name: "add_purchase_order",
    component: addPurchaseOrder,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['inventroy'],
    }
  },

  {
    path: "/items/purchaseOrder/warehouse",
    name: "warehouse",
    component: warehouse,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['inventroy'],
    }
  },
  {
    path: "/items/purchaseOrder/supplier",
    name: "supplier",
    component: supplier,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['inventroy'],
    }
  },
  {
    path: "/items/purchaseOrder/supplier/stock",
    name: "stock",
    component: stockAdjust,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['inventroy'],
    }
  },
  {
    path: "/items/purchaseOrder/paymentout",
    name: "payment_home_out",
    component: paymentOutHome,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['inventroy'],
    }
  },
  {
    path: "/items/purchaseOrder/paymentout/createnew",
    name: "payment_out",
    component: paymentOutHomeCreate,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['inventroy'],
    }
  },

  //---Categories----
  {
    path: "/categories",
    name: "categories",
    component: categories,
    meta: {
      requiresAuth: true,
      requiredRoles: ["admin", "Sub_Admin"],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['services'],
    },
  },
  {
    path: "/categories/:type/:serviceId",
    name: "service_category_create_form",
    component: serviceCategoryCreateForm,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['services'],
    }
  },

  //---reports---
  {
    path: "/reports",
    name: "reports",
    component: reports,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
      ],
      requiredPermissions: ["create_leads"],
    },
  },

  //---expense---
  {
    path: "/expense",
    name: "expense",
    component: expense,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
        "Service Engineer",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['expenses'],
    },
  },

  //---setting---
  {
    path: "/setting",
    name: "setting",
    component: setting,
    meta: {
      requiresAuth: true,
      requiredRoles: ["admin"],
      requiredPermissions: ["create_leads"],
    },
  },
  //---users---
  {
    path: "/users",
    name: "users",
    component: users,
    meta: {
      requiresAuth: true,
      requiredRoles: ["admin", "subadmin"],
      requiredPermissions: ["create_leads"],
    },
  },
  
  //---open RMA---
  {
    path: "/openrma",
    name: "open_rma_home",
    component: openRMA,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
        "Service Engineer",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['rma'],
    },
  },
  {
    path: "/openrma/:rmaId/:type",
    name: "open_rma_edit",
    component: editRma,
    meta: {
      requiresAuth: true,
      requiredRoles: [
        "admin",
        "Sub_Admin",
        "Account Manager",
        "Service Manager",
        "Sales man",
        "Service Engineer",
      ],
      requiredPermissions: ["create_leads"],
      byPlanPermission: ['rma'],
    },
  },
  //---pages----
  {
    path: "/pages",
    name: "pages",
    component: pagesController,
  },
  //---WebsiteBuilder ---
  {
    path: "/websites/settings",
    name: "website",
    component: WebsiteBuilder,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['custom_website'],
    }
  },
  {
    path: "/websites/dashboard",
    name: "website-dashboard",
    component: WebsiteDashboard,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['custom_website'],
    }
  },
  {
    path: "/websites/template",
    name: "website-template",
    component: WebsiteTemplate,
    meta: {
      requiresAuth: true,
      byPlanPermission: ['custom_website'],
    }
  },

  //---subscriptions---
  {
    path: "/subscription",
    component: subscriptions,
    name: "subscriptions",
    meta: {
      requiresAuth: true,
      requiredRoles: ["admin"],
      requiredPermissions: ["create_leads"],
    },
  },
  {
    path: "/subscription/history",
    component: subscriptionOrders,
    name: "subscriptionsHistory",
    meta: {
      requiresAuth: true,
      requiredRoles: ["admin"],
      requiredPermissions: ["create_leads"],
    },
  },

  //----whatsapp settings--
  {
    path: "/whatsapp",
    component: whatsappSetting,
    name: "whatsappsetting",
    meta: {
      requiresAuth: true,
      byPlanPermission: ['WhatsApp_Alert'],
    }
  },

  //----page not found---
  {
    path: "/:pathMatch(.*)*",
    component: notfound,
  },

  //---page under maintain----
  {
    path: "/undermaintenance",
    component: underMaintain,
  },
  // Define a no-access page
{
  path: '/no-access',
  name: 'no-access',
  component: NoAccessPage,  // Create a NoAccessPage component
}
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// Set up Axios interceptors to handle HTTP errors globally
axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {    
    if (error.response && error.response.status === 401) {
      // Check if the error message matches (if available)
      const isUnauthenticated = error.response.data?.message === "Unauthenticated.";      
      // Get current route path from the router
      const currentRoute = router.currentRoute.value;
      const protectedPaths = ['/login', '/signup', '/mobilelogin'];
      
      if (!protectedPaths.includes(currentRoute.path)) {
        alert("Session Expired, Please login again.");
        localStorage.removeItem("track_new");
        
        // Redirect to login with return URL
        router.push({
          path: '/login',
          query: { redirect: currentRoute.fullPath }
        });
      }
    } else if (
      error.response &&
      error.response.status === 503 &&
      error.response.data.code === "maintenance"
    ) {
      // Redirect to login page
      router.push("/undermaintenance");
    }
    return Promise.reject(error);
  }
);
// Define the allowed routes when subscription is expired
const allowedRoutes = ["/subscription", "/subscription/history", "/pages"];

// Navigation guard to check authentication
router.beforeEach((to, from, next) => {
  const isAuthenticated = localStorage.getItem("track_new");
  const isLoginPage = to.path === "/login";
  const isSignupPage = to.path === "/signup";
  const isMobileLoginPage = to.path === "/mobilelogin";

  // Access Vuex store's state
  const companyInfo = store.getters["companies/currentCompanyList"];
  // Allow access to login and signup pages if not authenticated
  if ((isLoginPage || isSignupPage || isMobileLoginPage) && !isAuthenticated) {
    next();
  }
  // Redirect to login page if not authenticated and trying to access other pages
  else if (to.path !== "/login" && !isAuthenticated) {
    next("/login");
  } else {
    if (companyInfo && companyInfo.expiry_date) {
      const expireDate = companyInfo.expiry_date;
      const today = new Date().toISOString().split("T")[0];
      if (expireDate < today) {
        if (allowedRoutes.includes(to.path)) {
          next(); // Allow navigation to subscription-related pages
        } else if (to.path !== "/") {
          // Redirect to the dashboard and blur the page
          next("/");
        }
      }
    }

    if (to.meta.requiresAuth) {
      const currentUser = JSON.parse(localStorage.getItem("track_new"));
      //---strict by permission---
      //  const userPermissions = currentUser ? currentUser.permissions || [] : [];
      // const requiredPermissions = to.meta.requiredPermissions || [];
      //---strict by plans---
      const userPlanId = companyInfo && companyInfo.plans && companyInfo.plans.id ? companyInfo.plans.id || null : null;
      const userPlan = to.meta.byPlanPermission || [];
      const requiredPlans = companyInfo && companyInfo.plans && companyInfo.plans.data ? Object.keys(companyInfo.plans.data) || [] : []; 
      const canAccessPlans = requiredPlans.some((plan) =>
        userPlan.includes(plan)
      );
       //---strict by role---
       const userRoles = currentUser ? currentUser.roles || [] : [];
       const requiredRoles = to.meta.requiredRoles || []; 
       // const canAccessRoute = requiredPermissions.every(permission => userPermissions.includes(permission));
       const canAccessRoute = requiredRoles.some((role) =>
         userRoles.includes(role)
      );      

      // if (!canAccessPlans && userPlan.length > 0 && requiredPlans.length > 0 && ([12, 13].includes(userPlanId))) {
      //   // User does not have all required permissions for the route
      //   next("/no-access");
      // } else
      if (!canAccessRoute && requiredRoles.length > 0) {
        // User does not have all required permissions for the route
        next("/unauthorized");
      } else {
        // User has required permissions, proceed to the route
        next();
      }
    } else {
      // No authentication required for the route
      next();
    }

    //console.log(to.meta.requiresAuth);
  }
});

export default router;
