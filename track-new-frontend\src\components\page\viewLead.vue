<template>
    <div class="flex h-screen text-sm">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" :leadData="dataFromChild" @searchData="getFiteredDataList"
                @refreshData="reloadData">
            </headbar> -->


            <!-- services home -->
            <div class="p-1 relative p-1 pt-5">
                <addNotesLead :isMobile="isMobile" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"
                    :reload_data="reload_data">
                </addNotesLead>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->

    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/leads/Edit/headbar.vue';
import addNotesLead from '../supporting/leads/Edit/addNotesLead.vue';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';

export default {
    name: 'leads',
    components: {
        // sidebar,
        // headbar,
        addNotesLead,
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            route_item: 2,
            viewLeads: [],
            getFilteredData: [],
            dataFromChild: [],
            reload_data: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Followup Lead';
        const pageDescription = 'Manage and follow up on all your leads in one place, ensuring timely actions and maximizing opportunities';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    methods: {
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        handleDataFromChild(data) {
            this.dataFromChild = data;
            // console.log(data,'WWWWWW');
        },
        getFiteredDataList(data) {
            // console.log(data, 'WWWW');
            this.getFilteredData = data;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            // console.log(value, 'What happening.....!');

            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        },
        reloadData() {
            this.reload_data = !this.reload_data;
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        this.updateIsEnableBottom(false);
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        this.updateIsEnableBottom(true);

        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnableBottom(true);
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>