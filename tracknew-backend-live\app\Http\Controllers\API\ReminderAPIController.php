<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateReminderAPIRequest;
use App\Http\Requests\API\UpdateReminderAPIRequest;
use App\Models\Reminder;
use App\Repositories\ReminderRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class ReminderController
 * @package App\Http\Controllers\API
 */

class ReminderAPIController extends AppBaseController
{
    /** @var  ReminderRepository */
    private $reminderRepository;

    public function __construct(ReminderRepository $reminderRepo)
    {
        $this->reminderRepository = $reminderRepo;
    }

    /**
     * Display a listing of the Reminder.
     * GET|HEAD /reminders
     *
     * @param Request $request
     * @return Response
     */
    public function index(Request $request)
    {
        $reminders = $this->reminderRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($reminders->toArray(), 'Reminders retrieved successfully');
    }

    /**
     * Store a newly created Reminder in storage.
     * POST /reminders
     *
     * @param CreateReminderAPIRequest $request
     *
     * @return Response
     */
    public function store(CreateReminderAPIRequest $request)
    {
        $input = $request->all();

        $reminder = $this->reminderRepository->create($input);

        return $this->sendResponse($reminder->toArray(), 'Reminder saved successfully');
    }

    /**
     * Display the specified Reminder.
     * GET|HEAD /reminders/{id}
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        /** @var Reminder $reminder */
        $reminder = $this->reminderRepository->find($id);

        if (empty($reminder)) {
            return $this->sendError('Reminder not found');
        }

        return $this->sendResponse($reminder->toArray(), 'Reminder retrieved successfully');
    }

    /**
     * Update the specified Reminder in storage.
     * PUT/PATCH /reminders/{id}
     *
     * @param int $id
     * @param UpdateReminderAPIRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateReminderAPIRequest $request)
    {
        $input = $request->all();

        /** @var Reminder $reminder */
        $reminder = $this->reminderRepository->find($id);

        if (empty($reminder)) {
            return $this->sendError('Reminder not found');
        }

        $reminder = $this->reminderRepository->update($input, $id);

        return $this->sendResponse($reminder->toArray(), 'Reminder updated successfully');
    }

    /**
     * Remove the specified Reminder from storage.
     * DELETE /reminders/{id}
     *
     * @param int $id
     *
     * @throws \Exception
     *
     * @return Response
     */
    public function destroy($id)
    {
        /** @var Reminder $reminder */
        $reminder = $this->reminderRepository->find($id);

        if (empty($reminder)) {
            return $this->sendError('Reminder not found');
        }

        $reminder->delete();

        return $this->sendSuccess('Reminder deleted successfully');
    }
}
