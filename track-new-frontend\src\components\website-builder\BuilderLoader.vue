<template>
    <div v-if="isLoading" class="loader-overlay">
      <div class="loader">
        <div class="loader-text">Loading Website Builder...</div>
      
        <div class="loader-bar">
          <div class="loader-progress" :style="{ width: progress + '%' }"></div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  
  export default {
    props: {
      isLoading: {
        type: Boolean,
        default: true,
      },
      progress: {
        type: Number,
        default: 0, // Progress percentage, if you have it
      },
    },
  };
  </script>
  
  <style scoped>
  .loader-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  .loader {
    text-align: center;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: #333;
  }
  
  .loader-text {
    font-size: 1.2em;
    margin-bottom: 20px;
    color: #333;
  }
  
  .loader-bar {
    width: 80%;
    height: 8px;
    background: #e5e5e5;
    border-radius: 5px;
    overflow: hidden;
    margin: 0 auto;
  }
  
  .loader-progress {
    height: 100%;
    background: #4CAF50; /* Loader progress color */
    transition: width 0.3s;
  }
  
 
  
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  </style>
  