// Ensure the correct Firebase version is used
importScripts('https://www.gstatic.com/firebasejs/9.6.10/firebase-app-compat.js');
importScripts('https://www.gstatic.com/firebasejs/9.6.10/firebase-messaging-compat.js');

// Your Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCjqL_bt_hQyZQz7YBhTyG-2HtT0PUfHK4",
  authDomain: "tracknew-b2a8d.firebaseapp.com",
  projectId: "tracknew-b2a8d",
  storageBucket: "tracknew-b2a8d.appspot.com",
  messagingSenderId: "419327651932",
  appId: "1:419327651932:web:ba3b19e891deaed0647699",
};

// Initialize Firebase app in the service worker
firebase.initializeApp(firebaseConfig);

// Retrieve an instance of Firebase Messaging so that it can handle background messages.
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  // Customize notification here
  const notificationTitle = payload.notification.title;
  const notificationOptions = {
    body: payload.notification.body,
    icon: '/firebase-logo.png', // Replace with your own app icon
    data: payload.data,  // Attach any custom data to use during notification click
  };

  // Display notification
  self.registration.showNotification(notificationTitle, notificationOptions);
});

// Handle notification click events
self.addEventListener('notificationclick', function (event) {
  const notificationData = event.notification.data;

  event.notification.close(); // Close the notification

  // Logic to handle navigation based on notification data
  event.waitUntil(
    clients.matchAll({ type: 'window', includeUncontrolled: true }).then(function (clientList) {
      // If a client window is already open, just focus it
      for (let i = 0; i < clientList.length; i++) {
        const client = clientList[i];
        if (client.url === '/' && 'focus' in client) {
          return client.focus();
        }
      }

      // Otherwise, open a new window and navigate to the desired route
      if (clients.openWindow) {
        // Construct the URL for navigation based on notification data
        const url = buildUrlFromNotificationData(notificationData);
        return clients.openWindow(url);
      }
    })
  );
});

// Helper function to construct URL based on notification data
function buildUrlFromNotificationData(data) {
  // Safely handle if data or type doesn't exist
  if (!data || !data.type) {
    console.error('Missing notification data or type');
    return '/';
  }

  // Split the type field and extract the feature
  let split_data = data.type.split('\\');
  let feature = split_data[split_data.length - 1].toLowerCase();

  // Route handling logic based on the feature
  if (feature === 'services') {
    return `/services/${data.category_name}/${data.category_id}/${data.id}`;
  } else if (feature === 'leads') {
    return `/leads/edit?recordId=${data.id}`;
  } else if (feature === 'amcs') {
    return `/amc/view?recordId=${data.id}`;
  } else if (feature === 'sales') {
    return `/sales/invoice/preview?type=sales_home&invoice_no=${data.id}`;
  } else if (feature === 'estimations') {
    return `/sales/invoice/preview?type=estimation&est_no=${data.id}&back=home`;
  } else if (feature === 'proforma') {
    return `/sales/invoice/preview?type=proforma&proforma_no=${data.id}&back=home`;
  } else if (feature === 'customers') {
    return `/customers/view/${data.id}`;
  } else if (feature === 'rma') {
    return `/openrma/${data.id}/edit`;
  } else if (feature === 'purchaseorder') {
    return `/purchase_order`;
  } else if (feature === 'supplier') {
    return `/items/purchaseOrder/supplier`;
  } else if (feature === 'warehouse') {
    return `/items/purchaseOrder/warehouse`;
  } else if (feature === 'paymentout') {
    return `/items/purchaseOrder/paymentout`;
  } else if (feature === 'paymentin') {
    return `/sales/invoice/paymentin`;
  } else if (feature === 'productdetails') {
    return `/items`;
  }else{
      // Default case if no specific feature is handled
      return '/';
  }
}
