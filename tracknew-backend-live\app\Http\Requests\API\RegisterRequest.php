<?php

namespace App\Http\Requests\API;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use App\Http\Helpers\Helper;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name'  => 'required',
            'email' => 'required|email|unique:users,email',
            //'mobile_number' => 'required|mobile_number|unique:users,mobile_number',
            'password' => 'required'
        ];
    }

    public function failedValidation(Validator $validator){

        Helper::sendError('validation error', $validator->errors());

    }
}
