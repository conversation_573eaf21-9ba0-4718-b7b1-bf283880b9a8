<template>
    <!-- @mouseover="toggleHoverModal(true)" @mouseleave="toggleHoverModal(false)"
     @mousemove="showModal(null, $event)"  -->
    <div v-if="showModal" :style="{ top: `${modalPosition.y}px`, left: `${modalPosition.x}px` }"
        class="absolute text-black bg-white shadow-md shadow-gray-500 border border-gray-300 rounded-lg p-2 z-50 transition-opacity duration-1000 ease-in-out">
        <!-- Add more details as needed -->
        <div class="text-xs">
            <div v-if="modalData && modalData.first_name" class="w-full">
                <p class="py-1"><font-awesome-icon icon="fa-solid fa-user" class="px-1" /> {{
                    modalData.first_name +
                    ' ' + (modalData.last_name ? modalData.last_name : '') }}
                </p>
                <p class="py-1 cursor-pointer" @click="dialPhoneNumber(modalData.contact_number)"><font-awesome-icon
                        icon="fa-solid fa-phone" class="px-1" /> {{
                            modalData.contact_number }}</p>
                <div class="flex justify-center items-center w-full ">
                    <button @click="viewRecordCustomer(modalData)"
                        class="py-1 text-white hover:bg-blue-500 border px-2 rounded-full border-blue-500 shadow-inner shadow-blue-200 bg-blue-600">
                        <font-awesome-icon icon="fa-solid fa-eye" class="px-1" />
                        View</button>
                </div>
            </div>
            <div v-if="modalData && modalData.invoice_id" class="w-full">
                <p class="py-1">Invoice: {{
                    modalData.invoice_id }}
                </p>
                <p v-if="modalData.grand_total >= 0" class="py-1 font-bold">Total: <font-awesome-icon
                        icon="fa-solid fa-indian-rupee-sign" class="px-1" />
                    {{
                        modalData.grand_total }}</p>
                <p v-if="modalData.due_amount >= 0" class="py-1 text-red-600">Due: <font-awesome-icon
                        icon="fa-solid fa-indian-rupee-sign" class="px-1" />
                    {{
                        modalData.due_amount }}</p>
                <div class="flex justify-center items-center w-full ">
                    <button @click="printRecord(modalData)"
                        class="py-1 text-white hover:bg-blue-500 border px-2 rounded-full border-blue-500 shadow-inner shadow-blue-200 bg-blue-600">
                        <font-awesome-icon icon="fa-solid fa-eye" class="px-1" />
                        View</button>
                </div>
            </div>
            <div v-if="modalData && modalData.estimate_id" class="w-full">
                <p class="py-1">Estimate: {{
                    modalData.estimate_id }}
                </p>
                <div class="flex justify-center items-center w-full ">
                    <button @click="goEstimation(modalData)"
                        class="py-1 text-white hover:bg-blue-500 border px-2 rounded-full border-blue-500 shadow-inner shadow-blue-200 bg-blue-600">
                        <font-awesome-icon icon="fa-solid fa-eye" class="px-1" />
                        View</button>
                </div>
            </div>
            <div v-if="modalData && modalData.proforma_num" class="w-full">
                <p class="py-1">Proforma: {{
                    modalData.proforma_num }}
                </p>
                <div class="flex justify-center items-center w-full ">
                    <button @click="goProforma(modalData)"
                        class="py-1 text-white hover:bg-blue-500 border px-2 rounded-full border-blue-500 shadow-inner shadow-blue-200 bg-blue-600">
                        <font-awesome-icon icon="fa-solid fa-eye" class="px-1" />
                        View</button>
                </div>
            </div>
            <div v-if="modalData && modalData.service_code" class="w-full">
                <p class="py-1">Service: {{ modalData.service_code }}
                </p>
                <div class="flex justify-center items-center w-full ">
                    <a :href="'https://tracking.track-new.com/' + modalData.service_code" target="_blank"
                        class="flex items-center justify-center text-blue-600 hover:text-blue-700 border border-blue-600 font-normal py-1 px-3 rounded-lg transition-all shadow-lg hover:shadow-xl">
                        <font-awesome-icon icon="fa-solid fa-location-dot" size="lg" class="mr-2" />
                        <span>Track</span>
                    </a>
                </div>
            </div>
            <div v-if="modalData && modalData.id && modalData.amc_id">
                <p class="py-1">AMC: # {{ modalData.amc_id }}
                </p>
                <div class="flex justify-center items-center">
                    <button @click="goAMC(modalData)"
                        class="py-1 text-white hover:bg-blue-500 border px-2 rounded-full border-blue-500 shadow-inner shadow-blue-200 bg-blue-600">
                        <font-awesome-icon icon="fa-solid fa-eye" class="px-1" />
                        View</button>
                </div>
            </div>
            <div v-if="modalData && modalData.due_amount">
                <div class="flex justify-center items-center">
                    <button @click="sendReminder(modalData)"
                        class="py-1 text-white hover:bg-blue-500 border px-2 rounded-full border-blue-500 shadow-inner shadow-blue-200 bg-blue-600">
                        <font-awesome-icon icon="fa-brands fa-whatsapp" class="px-1" />
                        Reminder</button>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        showModal: Boolean,
        modalData: Object,
        modalPosition: Object,
    },
    data() {
        return {}
    },
    methods: {
        //---print record---
        printRecord(record) {
            if (record.sale_id) {
                this.$router.push({ name: 'print-preview', query: { type: 'sales_home', invoice_no: record.sale_id } });
            }
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        goEstimation(data) {
            if (data && data.estimate_id && data.id) {
                this.$router.push({ name: 'estimate-preview', query: { type: 'estimation', est_no: data.id } });
            }
        },
        goProforma(data) {
            if (data && data.id) {
                this.$router.push({ name: 'proforma-preview', query: { type: 'proforma', proforma_no: data.id, back: 'home' } });
            }
        },
        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        goAMC(modalData) {
            if (modalData && modalData.amc_id) {
                this.$router.push({ name: 'amcView', query: { recordId: modalData.amc_id } });
            }

        },
        sendReminder(data) {
            if (data.whatsapp) {
                this.$emit('sendRemainder', data.record ? data.record : '');
            } else {
                this.$emit('connectwhatsapp');
            }
        }
    }
};
</script>
