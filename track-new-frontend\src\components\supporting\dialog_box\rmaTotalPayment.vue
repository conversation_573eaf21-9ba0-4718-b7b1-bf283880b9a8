<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-hidden"
            :class="{ 'scale-100': isOpen_pay, 'scale-0': !isOpen_pay }">
            <div class="flex items-center justify-center z-50">
                <div class="bg-gray-600 bg-opacity-50" @click="closeModal"></div>

                <div class="bg-white rounded-lg shadow-lg w-full max-w-lg mx-auto p-6 relative">
                    <div class="flex justify-between items-center border-b mb-3 py-2">
                        <h3 class="text-md font-semibold">Repair Payments</h3>
                        <button @click="closeModal" class="text-gray-600 hover:text-gray-900"><font-awesome-icon
                                icon="fa-solid fa-xmark" /></button>
                    </div>

                    <form @submit.prevent="submitForm">
                        <div class="mb-4">
                            <label class="block text-gray-700">Method</label>
                            <div class="relative inline-block w-full" ref="rmaPayment">
                                <div @click="toggleDropdown('payment')"
                                    class="w-full bg-white border border-gray-300 rounded shadow-sm cursor-pointer flex items-center justify-between px-4 py-2">
                                    <span class="px-2 rounded"
                                        :class="{ 'text-gray-400': !formValues.method || formValues.method === '' }">
                                        {{ formValues.method && formValues.method !== '' ?
                                            formValues.method :
                                            'Select an method' }}</span>
                                    <font-awesome-icon icon="fa-solid fa-angle-down" class="items-center" />
                                </div>
                                <div v-if="isOpen.payment"
                                    class="absolute mt-1 p-1 w-full bg-white border border-gray-300 rounded shadow-lg z-10">
                                    <input type="text" ref="paymentInput" v-model="searchTerm.payment"
                                        @keydown.enter="handleEnterKeyProduct('payment', filteredOptions)"
                                        @keydown.down.prevent="handleDownArrow(filteredOptions)"
                                        @keydown.up.prevent="handleUpArrow(filteredOptions)" placeholder="Search..."
                                        class="w-full p-2 border-b border-gray-300 focus:outline-none" />
                                    <ul class="max-h-60 overflow-auto">
                                        <li v-for="(option, index) in filteredOptions" :key="index"
                                            @click="selectOptionData(option)"
                                            class="px-4 py-2 cursor-pointer flex items-center border-b hover:bg-gray-200"
                                            :class="{ 'bg-gray-200': index === selectedIndex }">
                                            <span class="py-1 px-2 rounded">
                                                {{ option.type }}
                                            </span>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Date *</label>
                            <input type="date" v-datepicker v-model="formValues.date"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Amount *</label>
                            <input type="number" v-model="formValues.amount"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Reference</label>
                            <input type="text" v-model="formValues.reference"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3" />
                        </div>

                        <div class="mb-4">
                            <label class="block text-gray-700">Notes</label>
                            <textarea v-model="formValues.notes"
                                class="mt-1 w-full border-gray-300 rounded border py-2 px-3"></textarea>
                        </div>

                        <div class="flex justify-end space-x-2">
                            <button type="button" @click="closeModal"
                                class="bg-gray-500 text-white px-4 py-2 rounded">Cancel</button>
                            <button type="submit" class="bg-blue-500 text-white px-4 py-2 rounded">Submit</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import { mapActions, mapGetters } from 'vuex';
export default {
    props: {
        showModal: Boolean,
    },
    data() {
        return {
            isModalOpen: false,
            formValues: {},
            isOpen_pay: false,
            isOpen: { payment: false },
            searchTerm: { payment: '' },
            selectedOption: { payment: null },
            selectedIndex: 0,
            //--toaster---
            show: false,
            type_toaster: 'warning',
            message: ''
        };
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
        filteredOptions() {
            if (this.isOpen.payment && this.currentInvoice && this.currentInvoice.length > 0 && this.currentInvoice[0].payment_opt
            ) {
                return JSON.parse(this.currentInvoice[0].payment_opt).filter(option =>
                    option.type.toLowerCase().includes(this.searchTerm.payment.toLowerCase())
                );
            }
        },
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        openModal() {
            this.isModalOpen = true;
        },
        closeModal(data) {
            this.isModalOpen = false;
            if (data && data.amount) {
                setTimeout(() => {
                    this.$emit('close-modal', data);
                }, 100)
                this.formValues = {};

            } else {
                setTimeout(() => {
                    this.$emit('close-modal');
                }, 100)
            }
        },
        submitForm() {
            if (this.formValues && this.formValues.amount && this.formValues.amount > 0 && this.formValues.date && this.formValues.date !== '') {
                // Handle form submission logic here
                this.closeModal(this.formValues);
            } else {
                this.message = this.formValues && (!this.formValues.amount || this.formValues.amount < 0) ? 'Please Enter valid amount value' :
                    this.formValues && (!this.formValues.date || this.formValues.date === '') ? 'Please select the payment date' :
                        'Please fill ad * fields';
                this.show = true;
            }
        },
        handleClickOutside(event, type) {
            if (type !== 'all') {
                if (this.isOpen.payment) {
                    // Check if the click is outside the dropdown
                    const dropdownRef = this.$refs.rmaPayment;
                    // console.log(dropdownRef, 'What happening......!!!', event.target);
                    if (dropdownRef && !dropdownRef.contains(event.target)) {
                        this.isOpen.payment = false;
                    }
                }
            } else {
                this.isOpen = { payment: false };
            }
        },
        toggleDropdown(type) {
            if (type === 'payment') {
                this.isOpen.payment = !this.isOpen.payment;;
                document.addEventListener('click', this.handleClickOutside);
                if (this.isOpen.payment) {
                    this.$nextTick(() => {
                        this.$refs.paymentInput.focus();
                    });
                }
            }
        },
        selectOptionData(option) {
            if (this.isOpen.payment) {
                this.formValues.method = option.type;
                this.formValues.date = this.getCurrentDateFormatted();
                // this.formValues.tax = option.value;
                this.isOpen.payment = false;
                this.searchTerm.payment = '';
            }
        },
        handleEnterKeyProduct(type, list_data) {
            if (type === 'payment') {
                if (list_data && list_data.length > 0) {
                    let find_data = list_data.find((opt, i) => i === this.selectedIndex);
                    if (find_data) {
                        this.selectOptionData(find_data);
                    }
                    this.selectedIndex = 0;
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        getCurrentDateFormatted() {
            const date = new Date();
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside($event, 'all'));
    },
    watch: {
        showModal(newValue) {
            if (newValue) {
                this.fetchInvoiceSetting();
            }
            setTimeout(() => {
                this.isOpen_pay = newValue;
            }, 100);
        },

    },
};
</script>

<style>
/* Modal styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0, 0, 0, 0.4);
}

.modal-content {
    background-color: #fefefe;
    margin: 15% auto;
    padding: 20px;
    border: 1px solid #888;
    width: 80%;
}

.close {
    color: white;
    float: right;
    font-size: 28px;
    font-weight: bold;
}

.close:hover,
.close:focus {
    color: red;
    text-decoration: none;
    cursor: pointer;
}
</style>