import { apiMethods } from './api';

const authAPI = {
  // Authentication endpoints
  login: (credentials) => {
    return apiMethods.post('/auth/login', credentials);
  },

  register: (userData) => {
    return apiMethods.post('/auth/register', userData);
  },

  logout: () => {
    return apiMethods.post('/auth/logout');
  },

  refreshToken: (refreshToken) => {
    return apiMethods.post('/auth/refresh', { refreshToken });
  },

  getCurrentUser: () => {
    return apiMethods.get('/auth/me');
  },

  forgotPassword: (data) => {
    return apiMethods.post('/auth/forgot-password', data);
  },

  resetPassword: ({ token, password }) => {
    return apiMethods.post(`/auth/reset-password/${token}`, { password });
  },

  updatePassword: (data) => {
    return apiMethods.patch('/auth/update-password', data);
  },

  verifyEmail: (token) => {
    return apiMethods.post(`/auth/verify-email/${token}`);
  },

  resendVerificationEmail: (email) => {
    return apiMethods.post('/auth/resend-verification', { email });
  },

  // Profile management
  updateProfile: (data) => {
    return apiMethods.patch('/auth/profile', data);
  },

  uploadAvatar: (file) => {
    const formData = new FormData();
    formData.append('avatar', file);
    return apiMethods.post('/auth/upload-avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  deleteAvatar: () => {
    return apiMethods.delete('/auth/avatar');
  },

  // Two-factor authentication
  enableTwoFactor: () => {
    return apiMethods.post('/auth/2fa/enable');
  },

  disableTwoFactor: (code) => {
    return apiMethods.post('/auth/2fa/disable', { code });
  },

  verifyTwoFactor: (code) => {
    return apiMethods.post('/auth/2fa/verify', { code });
  },

  generateBackupCodes: () => {
    return apiMethods.post('/auth/2fa/backup-codes');
  },

  // Session management
  getSessions: () => {
    return apiMethods.get('/auth/sessions');
  },

  revokeSession: (sessionId) => {
    return apiMethods.delete(`/auth/sessions/${sessionId}`);
  },

  revokeAllSessions: () => {
    return apiMethods.delete('/auth/sessions');
  },

  // Account security
  getLoginHistory: (params = {}) => {
    return apiMethods.get('/auth/login-history', { params });
  },

  getSecurityEvents: (params = {}) => {
    return apiMethods.get('/auth/security-events', { params });
  },

  updateSecuritySettings: (settings) => {
    return apiMethods.patch('/auth/security-settings', settings);
  },

  // Account deletion
  requestAccountDeletion: (password) => {
    return apiMethods.post('/auth/delete-account', { password });
  },

  confirmAccountDeletion: (token) => {
    return apiMethods.post(`/auth/confirm-deletion/${token}`);
  },

  cancelAccountDeletion: () => {
    return apiMethods.post('/auth/cancel-deletion');
  },
};

export default authAPI;
