<?php
namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateRmaAPIRequest;
use App\Http\Requests\API\UpdateRmaAPIRequest;
use App\Models\Rma;
use App\Models\RmaItems;
use App\Models\RmaPayments;
use App\Models\RmaUsers;
use App\Models\Companies;
use App\Models\Customer;
use App\Models\Products;
use App\Models\Brands;
use App\Models\User;
use App\Models\RmaAdditionalProducts;
use App\Models\RmaAssignedAccessories;
use App\Repositories\RmaRepository;
use App\Repositories\RmaItemsRepository;
use App\Repositories\RmaAdditionalProductsRepository;
use App\Repositories\RmaPaymentsRepository;
use App\Repositories\RmaUsersRepository;
use App\Repositories\RmaAssignedAccessoriesRepository;
use App\Http\Resources\api\RmaResource;
use App\Http\Services\RelayMessage;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Illuminate\Support\Facades\DB;
use Barryvdh\DomPDF\Facade\Pdf;
use Response;

/**
 * Class rmaController
 * @package App\Http\Controllers\API
 */

class RmaAPIController extends AppBaseController
{
       /** @var RmaRepository */
    private $rmaRepository;

    /** @var RmaItemsRepository */
    private $rmaItemsRepository;

    /** @var RmaAdditionalProductsRepository */
    private $rmaAdditionalProductsRepository;

    /** @var RmaPaymentsRepository */
    private $rmaPaymentsRepository;
  
    /** @var RmaUsersRepository */
    private $rmaUsersRepository;
  
  	private $rmaAssignedAccessoriesRepository;

    public function __construct(
        RmaRepository $rmaRepo,
        RmaItemsRepository $rmaItemsRepo,
        RmaAdditionalProductsRepository $rmaAdditionalProductsRepo,
        RmaPaymentsRepository $rmaPaymentsRepo,
      	RmaUsersRepository $rmaUsersRepo,
      	RmaAssignedAccessoriesRepository $rmaAssignAccessRepo
    ) {
        //$this->middleware('doNotCacheResponse', ['only' => ['index', 'createPDF', 'viewPDF', 'outCreatePDF', 'outViewPDF']]);
        $this->rmaRepository = $rmaRepo;
        $this->rmaItemsRepository = $rmaItemsRepo;
        $this->rmaAdditionalProductsRepository = $rmaAdditionalProductsRepo;
        $this->rmaPaymentsRepository = $rmaPaymentsRepo;
        $this->rmaUsersRepository = $rmaUsersRepo;
      	$this->rmaAssignedAccessoriesRepository = $rmaAssignAccessRepo;
    }
  
  	public function createPDF($id)
    {
        $rma = Rma::where('id', $id)->first(); 
        if (empty($rma)) {
          return $this->sendError('Rma`s not found');
        }

        $company_id = $rma->company_id;         
        $customer =  Customer::where('id', $rma->customer_id)->first();                  
        $company = Companies::where('id', $company_id)->first();
        $user = User::select('name', 'mobile_number')
          ->find($rma->updated_by ?? auth()->id());

        $product = Products::where('id', $rma->product_id)->first();
        $brand = Brands::where('id', $rma->brand_id)->first();
        $pdf = Pdf::loadView('pdf.rma-inward', [
          'data' => $rma,
          'company' => $company,              
          'customer' => $customer,
          'product' => $product,  
          'brand' => $brand,
          'user' => $user,
        ]);
        $filename = $rma->rma_id ?? $rma->cutomer_invoice ?? '';
        return $pdf->download($filename.'.pdf');     
    }
  
  	public function viewPDF($id)
   	{       
        $rma = Rma::where('id', $id)->first(); 

        if (empty($rma)) {
          return $this->sendError('Rma`s not found');
        }

        $company_id = $rma->company_id;         
        $customer =  Customer::where('id', $rma->customer_id)->first();                  
        $company = Companies::where('id', $company_id)->first();
        $user = User::select('name', 'mobile_number')
          ->find($rma->updated_by ?? auth()->id());

        $product = Products::where('id', $rma->product_id)->first();
        $brand = Brands::where('id', $rma->brand_id)->first();
        return view('pdf.rma-inward')->with([
          'data' => $rma,
          'company' => $company,              
          'customer' => $customer,
          'product' => $product,  
          'brand' => $brand,
          'user' => $user,
        ]);       
    }
  
  	public function outCreatePDF($id)
    {

         $rma = Rma::where('id', $id)->first(); 

        	if (empty($rma)) {
            	return $this->sendError('Rma`s not found');
        	}

           	$company_id = $rma->company_id;         
           	$customer =  Customer::where('id', $rma->customer_id)->first();                  
           	$company = Companies::where('id', $company_id)->first();
      		$user = User::select('name', 'mobile_number')
                 ->find($rma->updated_by ?? auth()->id());
      		
      		$product = Products::where('id', $rma->product_id)->first();
    		$brand = Brands::where('id', $rma->brand_id)->first();
        	$pdf = Pdf::loadView('pdf.rma_outward', [
             	  'data' => $rma,
                      'company' => $company,              
                      'customer' => $customer,
                      'product' => $product,  
                      'brand' => $brand,
              		  'user' => $user,
            ]);
          	$filename = $rma->rma_id ?? $rma->cutomer_invoice ?? '';
        	return $pdf->download($filename.'.pdf');     

     
    }
  
  	public function outViewPDF($id)
   	{       
        
        	$rma = $this->rmaRepository->find($id); 

        	if (empty($rma)) {
            	return $this->sendError('Rma`s not found');
        	}

           	$company_id = $rma->company_id;         
           	$customer =  Customer::where('id', $rma->customer_id)->first();                  
           	$company = Companies::where('id', $company_id)->first();
      		$user = User::select('name', 'mobile_number')
                 ->find($rma->updated_by ?? auth()->id());
      		
      		$product = Products::where('id', $rma->product_id)->first();
    		$brand = Brands::where('id', $rma->brand_id)->first();
      	  	$rmaResource = new RmaResource($rma);

            return view('pdf.rma_outward')->with([
                      'data' =>  $rmaResource,
                      'company' => $company,              
                      'customer' => $customer,
                      'product' => $product,  
                      'brand' => $brand,
              		  'user' => $user,
           ]);       
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/rmas",
     *      summary="getrmaList",
     *      tags={"rma"},
     *      description="Get all rmas",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/rma")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {      
        $companyId = $request->query('company_id');
        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }
      
        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);      	
      	$from_date = $request->query('from_date');    			
      	$to_date = $request->query('to_date'); 
      	$customer_id = $request->query('customer_id');
      	$status = $request->query('q'); 
        
        $rmaQuery = Rma::where('company_id', $companyId);
      	
        if(isset($customer_id) && $customer_id !== ''){
            $rmaQuery->where('customer_id', $customer_id);
        }
    	
      	if ($from_date) {
        	$rmaQuery->whereDate('updated_at', '>=', $from_date);
      	}

      	if ($to_date) {
        	$rmaQuery->whereDate('updated_at', '<=', $to_date);
      	}
      
        if (isset($status) && $status !== '') { 

            $rmaQuery->where('rma_status', $status);
          
        }
      	
        // Adjust perPage for all records request
        if ($perPage === 'all') {
            $perPage = $rmaQuery->count();
        }
    
        $rma = $rmaQuery->orderBy('updated_at', 'desc')->paginate($perPage, ['*'], 'page', $page);
    

        $response = [
            'success' => true,
            'data' => RmaResource::collection($rma),//$sales->items(),LeadResource::collection($sales), // Get the paginated items
            'pagination' => [
                'total' => $rma->total(),
                'per_page' => $rma->perPage(),
                'current_page' => $rma->currentPage(),
                'last_page' => $rma->lastPage(),
                'from' => $rma->firstItem(),
                'to' => $rma->lastItem()
            ],
        
        ];
        
        return response()->json($response);
      
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/rmas",
     *      summary="createrma",
     *      tags={"rma"},
     *      description="Create rma",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/rma"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
	public function store(CreatermaAPIRequest $request)
    {
        $input = $request->all();
      
      if(isset($input['company_id'])){   
        $input['created_by'] = auth()->user()->id;
        $input['updated_by'] = auth()->user()->id;

        $rma = $this->rmaRepository->create($input);  
        
        if(isset($input['assign_to'])){
            $assignTo = $input['assign_to'];           
              foreach ($assignTo as $userData) {              
                $user_id = $userData['user_id'];
                $data['service_id'] =  $rma->id;
                $data['user_id'] =  $user_id;                   
                $this->rmaUsersRepository->create($data);              
             }  
            
         }	
        
          if(isset($input['accessories'])){
            $access = $input['accessories'];           
              foreach ($access as $accessData) {            
                $data['accessories_id'] =  $accessData['id'];
                $data['rma_id'] = $rma->id;              
                $this->rmaAssignedAccessoriesRepository->create($data);              
             }  
            
         }
        
         // Fetch the related customer and product information
        $customer = $rma->customer;  // Using the customer() relationship
        $product = $rma->product;    // Using the product() relationship

        // Get the customer's mobile number and the product's name
        $customerMobileNumber = $customer ? $customer->contact_number : null;
        $customerName = $customer ? $customer->first_name : $customer->last_name;
        $productName = $product ? $product->product_name : 'N/A';
        $rmaCode = $rma->rma_id ?? '';
        
         $smsTransactionData = [
           	'temp_id' => 1707172502718597123,
            'template' => 'RMA Registration',
            'message' => 'RMA registered successfully for product ' . $productName . ' with RMA Code: ' . $rmaCode,
            'status' => 0,  
            'user_id' => auth()->user()->id,
            'company_id' => $input['company_id'],
            'sent_date' => \Carbon\Carbon::now()->toDateString(),
            'to' => $customerMobileNumber,
            'customer_id' => $customer->id ?? null,
            'created_at' => \Carbon\Carbon::now(),
            'updated_at' => \Carbon\Carbon::now(),
        ];
        
         if (!empty($customerMobileNumber)) {
           	if(checkSmsBalance($input['company_id'])){
              $relayMessage = new RelayMessage();
              $response = $relayMessage->sendRmaRegister($customerName, $customerMobileNumber, $rmaCode, $productName);
              if ($response) {
                  $smsTransactionData['status'] = 1;  
              }
            }
         }
        
        \DB::table('sms_transactions')->insert($smsTransactionData);
       
      	return $this->sendResponse(new RmaResource($rma), 'Rma saved successfully');
      }
      else{
         return $this->sendError('Failed to create, Please provide Company');
      }
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/rmas/{id}",
     *      summary="getrmaItem",
     *      tags={"rma"},
     *      description="Get rma",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of rma",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/rma"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var rma $rma */
        $rma = $this->rmaRepository->find($id);

        if (empty($rma)) {
            return $this->sendError('Rma not found');
        }
		
      return $this->sendResponse(new RmaResource($rma), 'Rma retrieved successfully');
       // return $this->sendResponse($rma->toArray(), 'Rma retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/rmas/{id}",
     *      summary="updaterma",
     *      tags={"rma"},
     *      description="Update rma",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of rma",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/rma"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */


    public function update($id, UpdateRmaAPIRequest $request)
    {
        $input = $request->all();
      	$input['updated_by'] = auth()->user()->id;

        /** @var rma $rma */
        $rma = $this->rmaRepository->find($id);

        if (empty($rma)) {
            return $this->sendError('Rma not found');
        }

        DB::beginTransaction();

        try {
            $rma = $this->rmaRepository->update($input, $id);

            // Update RMA Items
            if (!empty($input['rma_items'])) {
                $itemIdsToKeep = [];
                foreach ($input['rma_items'] as $itemData) {
                    if (isset($itemData['id'])) {
                        $itemIdsToKeep[] = $itemData['id'];
                    }
                }

                RmaItems::where('rma_id', $id)->whereNotIn('id', $itemIdsToKeep)->delete();

                foreach ($input['rma_items'] as $itemData) {
                    $itemData['rma_id'] = $id; // Ensure the rma_id is set
                    if (isset($itemData['id'])) {
                        $this->rmaItemsRepository->update($itemData, $itemData['id']);
                    } else {
                        $this->rmaItemsRepository->create($itemData);
                    }
                }
            }

            // Update Additional Products
            if (!empty($input['rma_additional_products'])) {
                $additionalProductIdsToKeep = [];
                foreach ($input['rma_additional_products'] as $additionalProductData) {
                    if (isset($additionalProductData['id'])) {
                        $additionalProductIdsToKeep[] = $additionalProductData['id'];
                    }
                }

                RmaAdditionalProducts::where('rma_id', $id)->whereNotIn('id', $additionalProductIdsToKeep)->delete();

                foreach ($input['rma_additional_products'] as $additionalProductData) {
                    $additionalProductData['rma_id'] = $id; // Ensure the rma_id is set
                    if (isset($additionalProductData['id'])) {
                        $this->rmaAdditionalProductsRepository->update($additionalProductData, $additionalProductData['id']);
                    } else {
                        $this->rmaAdditionalProductsRepository->create($additionalProductData);
                    }
                }
            }

            // Update Payments
            if (!empty($input['rma_payments'])) {
                $paymentIdsToKeep = [];
                foreach ($input['rma_payments'] as $paymentData) {
                    if (isset($paymentData['id'])) {
                        $paymentIdsToKeep[] = $paymentData['id'];
                    }
                }

                RmaPayments::where('rma_id', $id)->whereNotIn('id', $paymentIdsToKeep)->delete();

                foreach ($input['rma_payments'] as $paymentData) {
                    $paymentData['rma_id'] = $id; // Ensure the rma_id is set
                    if (isset($paymentData['id'])) {
                        $this->rmaPaymentsRepository->update($paymentData, $paymentData['id']);
                    } else {
                        $this->rmaPaymentsRepository->create($paymentData);
                    }
                }
            }
          
     
              // Update assigned users
              $keepUserIds = [];
              foreach ($input['assign_to'] as $itemUserData) {
                  if (isset($itemUserData['id'])) {
                      $keepUserIds[] = $itemUserData['id'];
                  }
              }

              // Delete users not in the provided user IDs
              \App\Models\RmaUsers::where('rma_id', $id)
                  ->whereNotIn('user_id', $keepUserIds)
                  ->forceDelete();

              foreach ($input['assign_to'] as $itemUserData) {
                  $itemUserData_u['rma_id'] = $id;
                  $itemUserData_u['user_id'] = $itemUserData['id'];

                  if (isset($itemUserData['id'])) {
                      // Check if the record exists
                      $existingRecord = \App\Models\RmaUsers::where('rma_id', $id)
                          ->where('user_id', $itemUserData['id'])
                          ->first();

                      if ($existingRecord) {
                          // Update the existing record
                          $existingRecord->update($itemUserData_u);
                      } else {
                          // Create a new record if not found
                          $this->rmaUsersRepository->create($itemUserData_u);
                      }
                  } else {
                      // Create new record if 'user_id' is not set in the input
                      $this->rmaUsersRepository->create($itemUserData_u);
                  }
              }
          
          	
            // Update Assigned Accessories
            if (!empty($input['accessories'])) {
                $accessoryIdsToKeep = [];
                foreach ($input['accessories'] as $accessUpdateDatas) {
                    if (isset($accessUpdateDatas['id'])) {
                        $accessoryIdsToKeep[] = $accessUpdateDatas['id'];
                    }
                }

              RmaAssignedAccessories::where('rma_id', $id)->whereNotIn('accessories_id', $accessoryIdsToKeep)->forceDelete();

                foreach ($input['accessories'] as $accessUpdateData) {
                    $updateData['rma_id'] = $id; // Ensure the rma_id is set
                    if (isset($accessUpdateData['id'])) {
                      	$updateData['accessories_id'] = $accessUpdateData['id'];

                      // Check if the record exists
                      $existingRecord = RmaAssignedAccessories::where('rma_id', $id)
                        ->where('accessories_id', $accessUpdateData['id'])
                        ->first();

                      if ($existingRecord) {
                        // Update the existing record
                        $existingRecord->update($updateData);
                      } else {
                        // Create a new record if not found
                        RmaAssignedAccessories::create($updateData);
                      }
                    } else {
                     
                        $this->rmaAssignedAccessoriesRepository->create($updateData);
                    }
                }
            }
          
    

            DB::commit();
                $customer = $rma->customer;
                $product = $rma->product;
                $customerMobileNumber = $customer ? $customer->contact_number : null;
                $customerName = $customer ? ($customer->first_name ?: $customer->last_name) : 'N/A';
                $productName = $product ? $product->product_name : 'N/A';
                $rmaCode = $rma->rma_id ?? '';

                if (!empty($customerMobileNumber)) {
                  	if(checkSmsBalance($rma->company_id)){
                      $relayMessage = new RelayMessage();
                      $smsTemplate = '';
                      $smsMessage = '';
                      $tempId = 0;
                      $smsStatus = 0; // default to failure

                      // Send SMS based on the RMA status
                      if ($rma->rma_status == 6) {
                          $res = $relayMessage->sendRmaReadyToDelivery($customerName, $customerMobileNumber, $rmaCode, $productName);
                          $smsTemplate = 'RMA Ready to Delivery';                      	
                          $tempId = 1707172502832616175;
                          $smsMessage = "Your RMA (Code: $rmaCode) for product $productName is ready for delivery.";
                      } elseif ($rma->rma_status == 13) {
                          $res = $relayMessage->sendRmaDelivered($customerName, $customerMobileNumber, $rmaCode, $productName);
                          $smsTemplate = 'RMA Delivered';
                          $tempId = 1707172508129603753;
                          $smsMessage = "Your RMA (Code: $rmaCode) for product $productName has been successfully delivered.";
                      }

                      // If the SMS was successfully sent, set status to success
                      if (isset($res)) {
                          $smsStatus = 1; // success
                      }

                      // Store SMS transaction
                      $smsTransactionData = [
                          'temp_id'  => $tempId,
                          'template' => $smsTemplate,
                          'message' => $smsMessage,
                          'status' => $smsStatus,
                          'user_id' => auth()->user()->id,
                          'company_id' => auth()->user()->company_id,
                          'sent_date' => \Carbon\Carbon::now()->toDateString(),
                          'to' => $customerMobileNumber,
                          'customer_id' => $customer->id ?? null,
                          'created_at' => \Carbon\Carbon::now(),
                          'updated_at' => \Carbon\Carbon::now(),
                      ];

                      \DB::table('sms_transactions')->insert($smsTransactionData);
                    }
                }
        
			return $this->sendResponse(new RmaResource($rma), 'Rma updated successfully');
           // return $this->sendResponse($rma->toArray(), 'RMA updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->sendError('Failed to update RMA: ' . $e->getMessage());
        }
    }



    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/rmas/{id}",
     *      summary="deleterma",
     *      tags={"rma"},
     *      description="Delete rma",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of rma",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var rma $rma */
        $rma = $this->rmaRepository->find($id);

        if (empty($rma)) {
            return $this->sendError('Rma not found');
        }

        $rma->delete();

        return $this->sendSuccess('Rma deleted successfully');
    }
}
