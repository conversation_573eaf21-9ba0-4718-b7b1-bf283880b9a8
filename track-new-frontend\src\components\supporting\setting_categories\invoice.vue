<template>
    <div class="w-full text-sm">
        <!-- <div class="flex non-printable">
            <div class="w-1/2 flex justify-center items-center p-1 sm:p-2" :style="{
                backgroundColor: hovered ? 'rgba(25, 111, 61, 0.5)' : page === 'setting' ? 'rgba(25, 111, 61, 1)' : 'rgba(25, 111, 61, 0.1)',
                transition: 'background-color 0.3s ease-in-out'
            }" @mouseover="hovered = true" @mouseleave="hovered = false" @click="goSetting">
                <button class="font-bold text-xl text-white pl-2 pr-2">Setting</button>
            </div>
            <div class="w-1/2 flex justify-center items-center  p-2 bg-green-700" :style="{
                backgroundColor: hoveredPreview ? 'rgba(25, 111, 61, 0.5)' : page !== 'setting' ? 'rgba(25, 111, 61, 1)' : 'rgba(25, 111, 61, 0.1)',
                transition: 'background-color 0.3s ease-in-out'
            }" @mouseover="hoveredPreview = true" @mouseleave="hoveredPreview = false" @click="goPreview">
                <button class="font-bold text-xl text-white pl-2 pr-2">Preview</button>
            </div>
        </div> -->
        <!--loader-->
        <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
            :rows="number_of_rows" :gap="gap" :type="'grid'">
        </skeleton>
        <div v-if="page === 'setting' && !open_skeleton" class="non-printable">
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 p-1 sm:p-4">
                <!-- Column 1 - Desktop View -->
                <!-- Name -->
                <!-- <div class="w-full mb-4">
                    <label for="name" class="block font-bold">Name</label>
                    <div v-if="tooltip_focus && tooltip_focus === 'name'"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Please update this in the company setting</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                    <input type="text" id="name" v-model="formData.name" @focus="tooltip_focus = 'name'"
                        @blur="tooltip_focus = null" class="mt-1 p-2 border border-gray-300 w-full"
                        placeholder="Enter name" readonly />
                </div> -->
                <!-- GST Number -->
                <!-- <div class="w-full mb-4">
                    <label for="gst_number" class="block font-bold">GST Number</label>
                    <div v-if="tooltip_focus && tooltip_focus === 'gst_number'"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Please update this in the company setting</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                    <input type="text" id="gst_number" v-model="formData.gst_number" @input="validateGST"
                        @focus="tooltip_focus = 'gst_number'" @blur="tooltip_focus = null"
                        class="mt-1 p-2 border border-gray-300 w-full"
                        :class="{ 'outline-red-700': formData.gst_number !== '' && gstValidation !== '' }"
                        placeholder="Enter GST number" readonly />
                    <span v-if="formData.gst_number !== '' && gstValidation !== ''"
                        class="text-red-700 text-xs absolute block">{{ gstValidation }}</span>
                </div> -->
                <!-- Business Contact Number -->
                <!-- <div class="w-full mb-4">
                    <label for="business_contact" class="block font-bold">Business Contact
                        Number<span class="text-red-700">*</span></label>
                    <div v-if="tooltip_focus && tooltip_focus === 'business_contact'"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Please update this in the company setting</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                    <input type="tel" id="business_contact" v-model="formData.business_contact"
                        @focus="tooltip_focus = 'business_contact'" @blur="tooltip_focus = null"
                        @input="validatePhoneNumber(formData.business_contact)"
                        class="mt-1 p-2 border border-gray-300 w-full"
                        :class="{ 'outline-red-700': validationMessage !== '' }" placeholder="Enter contact number"
                        readonly />
                    <span v-if="validationMessage !== ''" class="block text-xs text-red-500">{{ validationMessage
                        }}</span>
                </div> -->
                <!--Email ID-->
                <!-- <div class="mb-4">
                    <label for="email" class="block font-bold">Company Email ID</label>
                    <div v-if="tooltip_focus && tooltip_focus === 'email'"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Please update this in the company setting</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                    <input type="email" id="email" v-model="formData.email" @focus="tooltip_focus = 'email'"
                        @blur="tooltip_focus = null" class="mt-1 p-2 border border-gray-300 w-full"
                        placeholder="Enter the email id" readonly />
                </div> -->
                <!-- Address -->
                <!-- <div class="w-full mb-4 sm:col-span-2">
                    <label for="address" class="block font-bold">Address</label>
                    <div v-if="tooltip_focus && tooltip_focus === 'address'"
                        class="absolute flex flex-col items-center group-hover:flex -mt-8">
                        <span
                            class="relative rounded-md z-10 px-2 py-2 text-xs leading-none text-white whitespace-no-wrap bg-black shadow-lg">
                            <p>Please update this in the company setting</p>
                        </span>
                        <div class="w-3 h-3 -mt-2 rotate-45 bg-black"></div>
                    </div>
                    <textarea id="address" v-model="formData.address" rows="3" @focus="tooltip_focus = 'address'"
                        @blur="tooltip_focus = null"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="Enter address" readonly></textarea>
                </div> -->

                <!-- HSN Number -->
                <!-- <div class="w-full  mb-4">
                <label for="hsnNumber" class="block font-bold">HSN Number</label>
                <input type="text" id="hsnNumber" v-model="formData.hsnNumber"
                    class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter HSN number" />
            </div> -->
                <!---invoice group by-->
                <!-- <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <p class="font-bold">Select Invoice Group as default</p>
                    <div class="flex items-center mt-2">
                        <div v-for="(group, index) in formData.invoice_prefix" :key="index" class="flex items-center"
                            :class="{ 'ml-3': index > 0 }">
                            <input type="radio" :id="group.name" :name="'group' + index" :value="group.value"
                                v-model="selectedGroupValue" @click="updateStatus(group)" @change="is_updated = true">
                            <label :for="group.name">{{ group.name }}</label>
                        </div>
                    </div>
                </div> -->
                <!-- Financial Year Reset Option -->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold pb-1">Financial Year Reset</label>
                    <div class="grid grid-cols-1 gap-2 items-center">
                        <!-- Enable Option -->
                        <div class="flex items-center">
                            <input type="radio" id="year_true" name="year_fy" value="1" v-model="formData.reset_fy"
                                @change="is_updated = true" class="mr-2 h-5 w-5 text-green-500" />
                            <label for="year_true" class="text-sm text-gray-700">Enable Financial Year Reset</label>
                        </div>
                        <!-- Disable Option -->
                        <div class="flex items-center">
                            <input type="radio" id="year_false" name="year_fy" value="0" v-model="formData.reset_fy"
                                @change="is_updated = true" class="mr-2 h-5 w-5 text-red-500" />
                            <label for="year_false" class="text-sm text-gray-700">Disable Financial Year Reset</label>
                        </div>
                    </div>
                </div>
                <!--Invoice Number prfix-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label for="inv_prefix" class="block font-bold">Invoice Number Prefix</label>
                    <div class="grid grid-cols-1 gap-3">
                        <div v-for="(group, index) in formData.invoice_prefix" :key="index"
                            :class="{ 'hidden': index !== 0 }">
                            <!-- <p class="font-bold text-xs">{{ group.name }}</p> -->
                            <input type="text" id="inv_prefix_group_A" v-model="group.prefix"
                                @change="is_updated = true" class="mt-1 p-2 border border-gray-300 w-full"
                                :placeholder="group.name" />
                        </div>
                        <!-- <div>
                            <p class="font-bold text-xs">Group B</p>
                            <input type="text" id="inv_prefix_group_B" v-model="formData.invoice_prefix[1].invoice_prefix"
                                class="mt-1 p-2 border border-gray-300 w-full"
                                placeholder="Group B" />
                        </div> -->
                    </div>
                </div>
                <!--Enable Minus Sale-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold">Minus Sale Setting</label>
                    <div class="flex items-center mt-2">
                        <div class="flex items-center">
                            <input type="radio" id="minus_sale_false" name="minus_sale" value="0"
                                v-model="formData.minus_sale" @change="is_updated = true">
                            <label for="minus_sale_false">Disable</label>
                        </div>
                        <div class="ml-5 flex items-center">
                            <input type="radio" id="minus_sale_true" name="minus_sale" value="1"
                                v-model="formData.minus_sale" @change="is_updated = true">
                            <label for="minus_sale_true">Enable</label>
                        </div>

                    </div>
                </div>
                <!--TAX Enable/Disable-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold">Tax enable/disable:</label>
                    <label class="inline-flex items-center cursor-pointer mt-2">
                        <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                            @click="toggleSwitch('is_tax')"
                            :class="{ 'bg-blue-600': formData.is_tax, 'bg-gray-200': !formData.is_tax }">
                            <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-6': formData.is_tax, 'translate-x-0': !formData.is_tax }">
                            </div>
                        </div>
                        <span class="pl-3"
                            :class="{ 'text-green-600': formData.is_tax, 'text-red-600': !formData.is_tax }">{{
                                formData.is_tax ? 'Enabled' : 'Disabled' }}</span>
                    </label>
                </div>
                <!--set has default due days-->
                <div class="w-full border shadow-inner shadow-gray-100 p-3 rounded">
                    <label for="favcolor" class="block font-bold">Set default due days:</label>
                    <div class="flex mt-1 text-sm">
                        <select v-model="formData.due_duration" @change="is_updated = true"
                            class="py-2 px-1 border border-gray-300 w-full">
                            <option disabled value="" class="text-gray-400">select due days</option>
                            <option value=0>0 Days</option>
                            <option value=1>1 Days</option>
                            <option value=3>3 Days</option>
                            <option value=5>5 Days</option>
                            <option value=7>7 Days</option>
                            <option value=9>9 Days</option>
                            <option value=12>12 Days</option>
                            <option value=15>15 Days</option>
                            <option value=20>20 Days</option>
                            <option value=30>30 Days</option>
                            <option value=45>45 Days</option>
                            <option value=60>60 Days</option>
                            <option value=90>90 Days</option>
                            <option value=100>100 Days</option>
                            <option value=120>120 Days</option>
                            <option value=200>200 Days</option>
                        </select>
                    </div>
                </div>
                <!--pick color-->
                <div class="w-full border shadow-inner shadow-gray-100 p-3 rounded">
                    <label for="favcolor" class="block font-bold">Select invoice theme color:</label>
                    <input type="color" id="favcolor" class="mt-1 p-2 h-10 border border-gray-300 w-full"
                        v-model="formData.color" @change="is_updated = true" />
                </div>
                <!--Text color-->
                <div class="w-full border shadow-inner shadow-gray-100 p-3 rounded mb-4 sm:mb-0">
                    <label for="text_color" class="block font-bold">Select invoice text color:</label>
                    <input type="color" id="text_color" class="mt-1 p-2 h-10 border border-gray-300 w-full"
                        v-model="formData.text_color" @change="is_updated = true" />
                </div>
                <!-- points type -->
                <!-- <div class="w-full mb-4">
                    <label for="points" class="block font-bold">Select invoice points style:</label>
                    <select id="points" v-model="points">
                        <option value="&#9733;">&#9733;</option>
                        <option value="&#9734;">&#9734;</option>
                        <option value="&#9737;">&#9737;</option>
                        <option value="&#9745;">&#9745;</option>
                        <option value="&#9755;">&#9755;</option>
                        <option value="&#9758;">&#9758;</option>
                        <option value="&#9830;">&#9830;</option>
                        <option value="&#11162;">&#11162;</option>
                        <option value="&#11166;">&#11166;</option>
                        <option value="&#11208;">&#11208;</option>
                        <option value=""></option>
                        <option value=""></option>
                    </select>
                </div> -->
            </div>
            <div class="grid grid-cols-1 sm:grid-cols-2 gap-4 p-1 sm:p-4">
                <!-- bank details -->
                <div class="w-full border shadow-inner shadow-gray-100 p-3 rounded sm:col-span-2">
                    <label for="bank_details" class="block font-bold">Payment / Bank Details</label>
                    <!-- <textarea id="bank_details" v-model="formData.bank_details" rows="5" @change="is_updated = true"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="A/c No: &#10;Bank name:&#10;IFSC:&#10;UPI ID:&#10;G-pay"></textarea> -->
                    <!-- Add informational text to guide the user -->
                    <!-- <p class=" text-sm text-gray-500 mb-2">Note: Please include any specific terms or conditions you wish
                        to
                        communicate in the invoice. Separate each point with a new line.</p> -->
                    <!-- Display Separate Fields for Bank Details -->
                    <div v-if="splitBankDetails" class="grid grid-cols-1 sm:grid-cols-2 gap-4 py-1">
                        <div>
                            <label for="account_no" class="block font-normal py-1">Account No</label>
                            <input type="text" id="account_no" v-model="splitBankDetails.account_no"
                                class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter Account Number" />
                        </div>
                        <div>
                            <label for="account_no" class="block font-normal py-1">Account Holder Name</label>
                            <input type="text" id="account_no" v-model="splitBankDetails.account_name"
                                class="mt-1 p-2 border border-gray-300 w-full"
                                placeholder="Enter Account Holder Name" />
                        </div>
                        <div>
                            <label for="bank" class="block font-normal py-1">Bank Name</label>
                            <input type="text" id="bank" v-model="splitBankDetails.bank"
                                class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter Bank Name" />
                        </div>
                        <div>
                            <label for="branch" class="block font-normal py-1">Branch</label>
                            <input type="text" id="branch" v-model="splitBankDetails.branch"
                                class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter Branch" />
                        </div>
                        <div>
                            <label for="ifsc" class="block font-normal py-1">IFSC Code</label>
                            <input type="text" id="ifsc" v-model="splitBankDetails.ifsc"
                                class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter IFSC Code" />
                        </div>
                    </div>
                    <div v-if="splitBankDetails" class="grid grid-cols-1 sm:grid-cols-2 gap-4 py-1">
                        <div>
                            <label for="upi_id" class="block font-normal py-1">UPI ID</label>
                            <input type="text" id="upi_id" v-model="splitBankDetails.upi_id"
                                class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter UPI ID" />
                        </div>
                        <div>
                            <label for="g_pay" class="block font-normal py-1">GPay</label>
                            <input type="text" id="g_pay" v-model="splitBankDetails.g_pay"
                                class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter GPay" />
                        </div>
                        <div>
                            <label for="phone_pay" class="block font-normal py-1">PhonePay</label>
                            <input type="text" id="phone_pay" v-model="splitBankDetails.phone_pay"
                                class="mt-1 p-2 border border-gray-300 w-full" placeholder="Enter PhonePay" />
                        </div>
                        <div>
                            <label for="other" class="block font-normal py-1">Other Payment Info</label>
                            <textarea id="other" v-model="splitBankDetails.other"
                                class="mt-1 p-2 border border-gray-300 w-full" placeholder="Other payment info">
                                </textarea>
                        </div>
                    </div>
                </div>
                <!-- Disclaimer -->
                <div class="w-full border shadow-inner shadow-gray-100 p-3 rounded sm:col-span-2">
                    <label for="disclaimer" class="block font-bold">Disclaimer for invoices</label>
                    <textarea id="disclaimer" v-model="formData.disclaimer" rows="5" @change="is_updated = true"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="Enter disclaimer"></textarea>
                    <!-- Add informational text to guide the user -->
                    <!-- <p class="text-sm text-gray-500 mb-2">Note: Please include any specific terms or conditions you wish
                        to
                        communicate in the invoice. Separate each point with a new line.</p> -->
                </div>
                <!-- Disclaimer -->
                <!-- <div class="w-full">
                    <label for="disclaimer" class="block font-bold">Estimation Disclaimer</label>
                    <textarea id="disclaimer" v-model="formData.est_disclaimer" rows="5"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="Enter disclaimer"></textarea> -->
                <!-- Add informational text to guide the user
                    <p class="text-sm text-gray-500 mb-2">Note: Please include any specific terms or conditions you wish
                        to
                        communicate in the estimation. Separate each point with a new line.</p> -->
                <!-- </div> -->
                <!-- Disclaimer Job sheet-->
                <!-- <div class="w-full">
                    <label for="disclaimer_jobsheet" class="block font-bold">Disclaimer jobsheet for
                        SERVICES</label>
                    <textarea id="disclaimer" v-model="formData.jobsheet_disclaimer" rows="5"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="Enter job sheet disclaimer"></textarea>
                </div> -->
                <!-- Disclaimer Proforma-->
                <!-- <div class="w-full">
                    <label for="disclaimer_proforma" class="block font-bold">Disclaimer for Proforma</label>
                    <textarea id="disclaimer_proforma" v-model="formData.proforma_disclaimer" rows="5"
                        class="mt-1 p-2 border border-gray-300 w-full max-h-[150px] overflow-y-auto resize-none"
                        placeholder="Enter the proforma disclaimer"></textarea>
                </div> -->
            </div>
            <div> <!-- Add informational text to guide the user -->
                <p class="text-sm text-gray-500 mb-2 text-center">
                    Note: Please include any specific terms or conditions you wish to
                    communicate in the invoice. Separate each point with a new line.
                </p>
            </div>

            <div class="grid grid-cols-1 gap-4 px-4">
                <!-- Tax -->
                <div class="w-full">
                    <label for="tax" class="block font-bold">Tax Setting:</label>
                    <!-- <input type="number" id="tax" v-model="formData.selected_tax" class="mt-1 p-2 border border-gray-300 w-full"
                    placeholder="Enter the tax value" /> -->
                    <!-- <select id="tax" v-model="formData.selected_tax" class="mt-1 p-2 border border-gray-300 w-full">
                    <option value="true">Enable</option>
                    <option value="false">Disable</option>
                </select> -->
                    <table class="w-full border-collapse border border-gray-300 bg-white">
                        <thead>
                            <tr class="text-sm">
                                <th class="border p-2">Sr.No</th>
                                <th class="border p-2">Tax Name</th>
                                <th class="border p-2">Tax Value</th>
                                <th class="border p-2">Default</th>
                                <th class="border p-2">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-sm">
                            <tr v-for="(tax, index) in formData.selected_tax" :key="index">
                                <td class="border p-2 text-center">{{ index + 1 }}</td>
                                <td class="border p-2 text-center">
                                    <input type="text" v-model="tax.tax_name" @change="is_updated = true"
                                        class="p-2 w-full" :ref="'tax' + index" placeholder="Enter tax name" />
                                </td>
                                <td class="border p-2 text-center">
                                    <input type="number" v-model="tax.value" class="p-2 w-full" min="0"
                                        @focus="title_switch.tax = index" @blur="title_switch.tax = false"
                                        @change="is_updated = true" placeholder="Enter tax value" @keyup.enter="addRow"
                                        @input="preventNegativeValue"
                                        :title="title_switch.tax === index ? 'Press Enter ⏎ key to create new' : ''" />
                                </td>
                                <td class="border p-2 text-center">
                                    <input type="radio" v-model="formData.selected_tax[index].status" :value="true"
                                        class="text-blue-500 bg-green-600 border-red-600"
                                        @change="checkStatus(index), is_updated = true"
                                        :title="formData.selected_tax[index].status ? 'Default' : 'Set as default'" />
                                </td>
                                <td class="border p-2 text-center">
                                    <button @click="deleteRow(index)"
                                        class="text-red-700 font-bold cursor-pointer hover:bg-gray-100">
                                        <img :src="del_icon" alt="delete icon" class="w-6 h-6" />
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="5" @click="addRow"
                                    class="text-center sm:text-lg text-sm cursor-pointer hover:bg-gray-100 text-green-700 font-bold">
                                    <span class="flex justify-center items-center"> + Add Row</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--tax group switch-->
                <!-- <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold">Tax Group:</label>
                    <label class="inline-flex items-center cursor-pointer mt-2">
                        <font-awesome-icon icon="fa-solid fa-layer-group" class="text-xl px-2 text-yellow-800" />
                        <div class="relative w-11 h-4 rounded-full transition-colors duration-300 flex items-center"
                            @click="toggleSwitch('tax_group_setting')"
                            :class="{ 'bg-blue-600': formData.tax_group_setting, 'bg-gray-200': !formData.tax_group_setting }">
                            <div class="absolute top-0.2 w-5 h-5 bg-white border border-gray-300 rounded-full transition-transform duration-300"
                                :class="{ 'translate-x-6': formData.tax_group_setting, 'translate-x-0': !formData.tax_group_setting }">
                            </div>
                        </div>
                        <span class="pl-3"
                            :class="{ 'text-green-600': formData.tax_group_setting, 'text-red-600': !formData.tax_group_setting }">{{
                                formData.tax_group_setting ? 'Enabled' : 'Disabled' }}</span>
                    </label>
                </div> -->
                <!---tax group-->
                <!-- <div v-if="formData && formData.tax_group_setting" class="w-full">
                    <label for="tax_groups" class="block font-bold">Tax Groups Option:</label>
                    <table class="w-full border-collapse border border-gray-300 bg-white">
                        <thead>
                            <tr class="text-sm">
                                <th class="border p-2">Group <span v-if="!isMobile">Name</span></th>
                                <th class="border p-2">Tax Options</th>
                                <th class="border p-2"><span v-if="!isMobile">Actions</span> <span
                                        v-else><font-awesome-icon icon="fa-solid fa-check" /></span></th>
                            </tr>
                        </thead>
                        <tbody class="text-sm">
                            <tr v-for="(group, groupIndex) in formData.tax_groups" :key="groupIndex">
                                <td class="border p-2">
                                    <input v-model="group.name" type="text" placeholder="Enter group name"
                                        class="p-2 w-full border rounded" />
                                </td>
                                <td class="border p-2">
                                    <div v-for="(tax, taxIndex) in group.taxes" :key="taxIndex"
                                        class="flex items-center space-x-2 mb-2">
                                        <input v-model="tax.name" type="text" placeholder="Tax Name"
                                            class="p-2 w-1/3 border rounded" />
                                        <input v-model="tax.percentage" type="number" placeholder="Tax Percentage"
                                            class="p-2 w-1/3 border rounded" min="0" />
                                        <button @click="deleteTax(groupIndex, taxIndex)"
                                            class="text-red-700 font-bold cursor-pointer hover:bg-gray-100">
                                            <font-awesome-icon icon="fa-solid fa-trash-can text-xs" />
                                        </button>
                                    </div>
                                    <div class="flex justify-start items-center">
                                        <button @click="addTax(groupIndex)"
                                            class="text-green-700 font-bold text-xs cursor-pointer">
                                            + Add Tax Option
                                        </button>
                                    </div>
                                </td>
                                <td class="border p-2 text-center">
                                    <div class="flex justify-center">
                                        <button @click="deleteGroup(groupIndex)"
                                            class="text-red-700 font-bold cursor-pointer hover:bg-gray-100"
                                            title="Delete Group">
                                            <font-awesome-icon icon="fa-solid fa-trash-can" />
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="3">
                                    <div class="flex justify-center">
                                        <button @click="addGroup"
                                            class="text-center sm:text-lg text-sm cursor-pointer hover:bg-gray-100 text-green-700 font-bold"
                                            title="Delete Group">
                                            + Add New Tax Group
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div> -->
                <!-- Payment -->
                <div class="w-full">
                    <label for="payment_opt" class="block font-bold">Payment options setting:</label>
                    <!-- <select id="payment_opt" v-model="formData.payment_opt" class="mt-1 p-2 border border-gray-300 w-full">
                    <option value="true">Enable</option>
                    <option value="false">Disable</option>
                </select> -->
                    <table class="w-full border-collapse border border-gray-300 bg-white">
                        <thead>
                            <tr class="text-sm">
                                <th class="border p-2">Sr.No</th>
                                <th class="border p-2">Payment Type</th>
                                <th class="border p-2">Default</th>
                                <th class="border p-2">Action</th>
                            </tr>
                        </thead>
                        <tbody class="text-sm">
                            <tr v-for="(tax, index) in formData.payment_opt" :key="index">
                                <td class="border p-2 text-center">{{ index + 1 }}</td>
                                <td class="border p-2 text-center">
                                    <input type="text" v-model="tax.type" class="p-2 w-full" :ref="'payment' + index"
                                        @focus="title_switch.payment = index" @blur="title_switch.payment = false"
                                        @change="is_updated = true" placeholder="Enter payment type"
                                        @keyup.enter="addRowPayment"
                                        :title="title_switch.payment === index ? 'Press Enter ⏎ key to create new' : ''" />
                                </td>
                                <td class="border p-2 text-center">
                                    <input type="radio" v-model="formData.payment_opt[index].status" :value="true"
                                        class="text-blue-500 bg-green-600 border-red-600"
                                        @change="checkStatusPayment(index), is_updated = true"
                                        :title="formData.payment_opt[index].status ? 'Default' : 'Set as default'" />
                                </td>
                                <td class="border p-2 text-center">
                                    <button @click="deleteRowPayment(index)"
                                        class="text-red-700 font-bold cursor-pointer hover:bg-gray-100">
                                        <img :src="del_icon" alt="delete icon" class="w-6 h-6" />
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="5" @click="addRowPayment"
                                    class="w-full text-center sm:text-lg text-sm cursor-pointer hover:bg-gray-100 text-green-700 font-bold">
                                    <span class="flex justify-center items-center"> + Add Row</span>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <!--dynamic fields-->
                <div class="border shadow-inner shadow-gray-100 p-3 rounded">
                    <label class="block font-bold pb-1">Custom Fields</label>
                    <!-- Dynamic Fields List -->
                    <div v-if="fields && fields.length > 0" class="space-y-4 w-full">
                        <div v-for="(field, index) in fields" :key="index"
                            class="grid grid-cols-3 gap-2 sm:gap-4 items-center">
                            <div class="flex items-center w-full">
                                <!-- Label Input -->
                                <input v-model="field.label" type="text" class="p-2 border rounded-md w-full"
                                    placeholder="Field Name" :disabled="!field.enabled" />
                            </div>
                            <div class="flex items-center">
                                <!-- Dropdown for Header/Footer -->
                                <select v-model="field.type" class="p-2 border rounded-md w-full">
                                    <option value="header">Header</option>
                                    <option value="footer">Footer</option>
                                </select>
                            </div>

                            <div class="flex justify-end items-center space-x-2">
                                <!-- Enable/Disable Toggle -->
                                <label class="flex items-center cursor-pointer">
                                    <span class="mr-2"
                                        :class="{ 'text-green-600': field.enabled, 'text-red-600': !field.enabled }">
                                        <!-- Eye icon to toggle enabled/disabled -->
                                        <font-awesome-icon v-if="!field.enabled" icon="fa-solid fa-eye-slash"
                                            @click="toggleEnabled(field)" class="cursor-pointer" />
                                        <font-awesome-icon v-if="field.enabled" icon="fa-solid fa-eye"
                                            @click="toggleEnabled(field)" class="cursor-pointer" />
                                    </span>
                                    <input type="checkbox" v-model="field.enabled" class="toggle-checkbox hidden" />
                                </label>
                                <!-- Delete Button -->
                                <button @click="deleteField(index)" class="text-red-600 hover:text-red-700"
                                    title="delete">
                                    <font-awesome-icon icon="fa-solid fa-trash-can" />
                                </button>
                            </div>
                        </div>
                    </div>
                    <div>
                        <!-- Add Field Button -->
                        <button @click="addField"
                            class="mt-4 bg-blue-500 text-white p-2 rounded-md hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-400">
                            <font-awesome-icon icon="fa-solid fa-plus" /> Add Field
                        </button>
                    </div>


                </div>
            </div>

            <!-- Save Button - Centered -->
            <button @click="saveData"
                class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 text-lg text-white py-2 px-5 rounded-md mx-auto block hover:bg-green-500 mt-5">
                Save
            </button>
        </div>
        <div v-if="page !== 'setting'">
            <!--What about the data-->
            <template_2 :formData="[shareData]" @goSetting="goSetting" :logo_img="logo_img"
                :invoice_data="{ invoice_number: (typeof shareData.invoice_prefix === 'string' ? JSON.parse(shareData.invoice_prefix)[this.selectedGroupValue] ? JSON.parse(shareData.invoice_prefix)[this.selectedGroupValue].prefix + '00' + 1 : JSON.parse(shareData.invoice_prefix)[this.selectedGroupValue] : shareData.invoice_prefix[this.selectedGroupValue].prefix + '00' + 1) }"
                :page_name="'setting'"></template_2>
        </div>
        <dialogConfirmBox :visible="modalVisible" :message="modalMessage" @ok="handleModalOk"
            @cancel="handleModalCancel" :type="type" @save="saveData">
        </dialogConfirmBox>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import template_2 from './invoiceTemplates/template_2.vue';
import dialogConfirmBox from '../dialog_box/dialogConfirmBox.vue';
import confirmbox from '../dialog_box/confirmbox.vue';
import dialogAlert from '../dialog_box/dialogAlert.vue';
import axios from 'axios';
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'invoice',
    emits: ['is-sales-save', 'updatesalesData'],
    props: {
        companyId: String,
        userId: String,
        isMobile: Boolean,
        store_refresh: Boolean,
        save_success: Boolean
    },
    components: {
        template_2,
        dialogConfirmBox,
        confirmbox,
        dialogAlert
    },
    data() {
        return {
            formData: {
                name: '',
                business_contact: '',
                address: '',
                disclaimer: '',
                gst_number: '',
                // hsnNumber: '',
                selected_tax: [{ tax_name: 'GST', value: 0, status: true }],
                invoice_prefix: [{ name: 'Group A', value: 0, status: true, prefix: 'INVA0000' }, { name: 'Group B', value: 1, status: false, prefix: 'INVB0000' }],
                email: '',
                color: '#F0EDF1',
                text_color: '#000000',
                payment_opt: [{ type: 'Cash', status: true }],
                minus_sale: "0",
                // tax_groups: [
                //     {
                //         name: 'Group 1',
                //         taxes: [
                //             { name: 'CGST', percentage: 9 },
                //             { name: 'SGST', percentage: 9 },
                //         ]
                //     },
                //     {
                //         name: 'Group 2',
                //         taxes: [
                //             { name: 'VAT', percentage: 18 }
                //         ]
                //     }
                // ]
                // invoice_prefix: 'INV0000'
            },
            tooltip_focus: null,
            selectedGroupValue: 0,
            shareData: {},
            del_icon: '/images/service_page/del.png',
            page: 'setting',
            hovered: false,
            hoveredPreview: false,
            modalVisible: false,
            modalMessage: '',
            type: 'invoice',
            open_confirmBox: false,
            open_message: false,
            message: '',
            deleteIndex: null,
            type_del: null,
            validationMessage: '',
            gstValidation: '',
            //---api integration--
            // companyId: null,
            // userId: null,
            logo_img: '/images/head_bar/logo_01.png',
            //--skeleton
            open_skeleton: false,
            number_of_columns: 3,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            title_switch: { tax: false, payment: false },
            bank_palceholder: 'A/c No: \nBank name: \nIFSC: \nUPI ID: \nG-pay',
            is_updated: false,
            //--banck info--
            splitBankDetails: null,
            fields: [],
        };
    },
    computed: {
        ...mapGetters('invoice_setting', ['currentInvoice']),
        ...mapGetters('companies', ['currentCompanyList']),
    },
    methods: {
        ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
        ...mapActions('companies', ['fetchCompanyList']),
        //---group by----
        updateStatus(selectedGroup) {
            // Update the status property of each group based on the selected group
            this.formData.invoice_prefix.forEach(group => {
                group.status = group === selectedGroup;
            });
        },
        //--mobile number validation--
        validatePhoneNumber(inputtxt) {
            // Regular expression for phone numbers
            var phoneno = /^(?:0|\+91)?[6-9]\d{9}$/;
            inputtxt = typeof inputtxt === 'string' ? inputtxt : inputtxt + ''
            // Check if the input matches the regular expression
            if (inputtxt.match(phoneno)) {
                this.validationMessage = '';
                return true; // Phone number is valid
            } else {
                // alert("Invalid phone number format. Please enter a valid phone number.");
                // this.message = "Invalid phone number format. Please enter a valid phone number.";
                // this.open_message = true;
                this.validationMessage = 'Enter valid contact number';
                return false; // Phone number is invalid
            }
        },
        saveData() {
            this.open_loader = true;
            if (this.formData && this.formData.business_contact && this.validatePhoneNumber(this.formData.business_contact)) {
                // Clone the formData object to avoid modifying the original object
                // let data = this.filteredDisclaimer(this.formData.disclaimer);
                // this.shareData = { ...this.formData, disclaimer: data };
                // Handle disclaimer data
                let send_data = { ...this.formData };
                if (Array.isArray(this.formData.disclaimer)) {
                    // Modify the disclaimer for logging or displaying
                    this.formData.disclaimer = this.formData.disclaimer.join('\n');
                } else {
                    // If not an array, assume it's already a string
                    send_data.disclaimer = this.formData.disclaimer;
                }
                if (this.formData.selected_tax && typeof this.formData.selected_tax === 'object') {
                    send_data.selected_tax = this.formData.selected_tax.filter(opt => opt.tax_name !== '');
                    send_data.selected_tax = JSON.stringify(this.formData.selected_tax);
                }
                if (this.formData.payment_opt && typeof this.formData.payment_opt === 'object') {
                    send_data.payment_opt = this.formData.payment_opt.filter(opt => opt.type !== '');
                    send_data.payment_opt = JSON.stringify(this.formData.payment_opt);
                }
                if (this.formData.invoice_prefix && typeof this.formData.invoice_prefix === 'object') {
                    send_data.invoice_prefix = JSON.stringify(this.formData.invoice_prefix);
                }
                if (typeof this.formData.minus_sale === 'boolean') {
                    send_data.minus_sale = JSON.stringify(this.formData.minus_sale);
                }
                if (this.splitBankDetails) {
                    send_data.bank_details = JSON.stringify(this.splitBankDetails);
                }
                if (this.fields && this.fields.length > 0) {
                    send_data.header_custom = JSON.stringify(this.fields);
                } else if (this.fields && this.fields.length === 0) {
                    send_data.header_custom = '';
                }

                if (Object.keys(this.shareData).length > 0) {
                    axios.put(`/invoice_settings/${this.shareData.id}`, { ...send_data, company_id: this.companyId })
                        .then(response => {
                            this.shareData = response.data.data;
                            // this.page = 'preview';
                            this.modalVisible = false;
                            this.fetchInvoiceSetting(true);
                            this.$emit('is-sales-save', false);
                            setTimeout(() => {
                                this.open_loader = false;
                            }, 300);

                        })
                        .catch(error => {
                            console.error('Error', error);
                            setTimeout(() => {
                                this.open_loader = false;
                            }, 300);
                            if (typeof this.formData.selected_tax === 'string') {
                                this.formData.selected_tax = JSON.parse(this.formData.selected_tax);
                            }
                            if (typeof this.formData.payment_opt === 'string') {
                                this.formData.payment_opt = JSON.parse(this.formData.payment_opt);
                            }
                            if (typeof this.formData.invoice_prefix === 'string') {
                                this.formData.invoice_prefix = JSON.parse(this.formData.invoice_prefix);
                            }
                            if (typeof this.formData.minus_sale === 'string') {
                                this.formData.minus_sale = JSON.parse(this.formData.minus_sale);
                            }
                            this.message = error.response.data.message;
                            this.open_message = true;
                        })
                } else {
                    axios.post('/invoice_settings', { ...this.formData, company_id: this.companyId })
                        .then(response => {
                            // console.log(response.data, 'Response create');
                            this.shareData = response.data.data;
                            setTimeout(() => {
                                this.open_loader = false;
                            }, 300);
                            if (this.$route.query.page === 'Sales') {
                                this.$router.go(-1);
                            } else {
                                this.page = 'preview';
                                this.modalVisible = false;
                            }
                            this.fetchInvoiceSetting(true);
                            this.$emit('is-sales-save', false);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            setTimeout(() => {
                                this.open_loader = false;
                            }, 300);
                            if (typeof this.formData.selected_tax === 'string') {
                                this.formData.selected_tax = JSON.parse(this.formData.selected_tax);
                            }
                            if (typeof this.formData.payment_opt === 'string') {
                                this.formData.payment_opt = JSON.parse(this.formData.payment_opt);
                            }
                            if (typeof this.formData.invoice_prefix === 'string') {
                                this.formData.invoice_prefix = JSON.parse(this.formData.invoice_prefix);
                            }
                            if (typeof this.formData.minus_sale === 'string') {
                                this.formData.minus_sale = JSON.parse(this.formData.minus_sale);
                            }
                            this.message = error.response.data.message;
                            this.open_message = true;
                        })
                }
                // localStorage.setItem('invoiceSetting', JSON.stringify(this.formData));
                // // console.log(this.shareData, 'OOOOO');

            }
            else {
                this.open_loader = false;
                this.message = 'Please fill in Business contact number in general setting..!'
                this.open_message = true;
                this.$emit('updatesalesData');
            }
        },

        filteredDisclaimer(data) {
            if (this.formData.disclaimer !== '') {
                return data.split('\n').filter(line => line.trim() !== '');
            }
        },
        //-----tax--
        addRow() {
            this.formData.selected_tax.push({ tax_name: '', value: 0, status: false });
            this.$nextTick(() => {
                if (this.$refs['tax' + (this.formData.selected_tax.length - 1)]) {
                    this.$refs['tax' + (this.formData.selected_tax.length - 1)][0].focus();
                }
            })
            this.is_updated = true;
        },
        deleteRecord() {
            if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                // console.log(this.deleteIndex, 'Whatjjjjjj');
                if (this.type_del === 'tax') {
                    this.formData.selected_tax.splice(this.deleteIndex, 1);
                }
                else if (this.type_del === 'payment') {
                    this.formData.payment_opt.splice(this.deleteIndex, 1);
                }
                else if (this.type_del === 'custom') {
                    this.fields.splice(this.deleteIndex, 1);
                }
            }
            this.open_confirmBox = false;
            this.deleteIndex = null;
            this.type_del = null;
        },
        deleteRow(index) {
            // this.formData.selected_tax.splice(index, 1);
            this.type_del = 'tax';
            this.deleteIndex = index;
            this.open_confirmBox = true;
            this.is_updated = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        //---open dislog message--
        closeMessage() {
            this.open_message = false;
        },

        checkStatus(index) {
            this.formData.selected_tax.map((opt, i) => {
                if (i === index) {
                    opt.status = true;
                } else {
                    opt.status = false;

                }
            })
        },
        //----payment----
        addRowPayment() {
            this.formData.payment_opt.push({ type: '', status: false });
            this.$nextTick(() => {
                if (this.$refs['payment' + (this.formData.payment_opt.length - 1)]) {
                    this.$refs['payment' + (this.formData.payment_opt.length - 1)][0].focus();
                }
            });
            this.is_updated = true;
        },
        deleteRowPayment(index) {
            this.type_del = 'payment';
            this.deleteIndex = index;
            this.open_confirmBox = true;
            // this.formData.payment_opt.splice(index, 1);
            this.is_updated = true;
        },
        checkStatusPayment(index) {
            this.formData.payment_opt.map((opt, i) => {
                if (i === index) {
                    opt.status = true;
                } else {
                    opt.status = false;
                }
            })
        },
        goPreview() {
            // Perform your validation here
            // console.log(this.isValidData(), 'Waht');
            if (this.isValidData()) {
                // Data is valid, navigate to preview page
                this.page = 'preview';
            } else {
                this.modalMessage = 'Some data is missing or invalid. Do you still want to navigate to the preview page?';
                this.modalVisible = true;
            }
        },
        goSetting() {
            this.page = 'setting';
            this.getInvoiceSetting();

        },
        isValidData() {
            const shareDataKeys = Object.keys(this.shareData);
            const formDataKeys = Object.keys(this.formData);

            if (shareDataKeys.length !== formDataKeys.length) {
                return false;
            }

            for (const key of shareDataKeys) {
                // if (key === 'disclaimer' && this.shareData[key].join('\n') !== this.formData[key]) {
                //     // For the 'disclaimer' key, compare the joined strings
                //     return false;
                // } else 
                if (this.shareData[key] !== this.formData[key]) {
                    return false;
                }
            }
            return true;
        },
        handleModalOk(value) {
            if (value) {
                this.page = 'preview';
            }
            this.modalVisible = false;
        },
        handleModalCancel(value) {
            if (!value) {
                this.page = 'setting';
            }
            this.modalVisible = false;
        },
        //--validate GST--
        validateGST() {
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.formData.gst_number !== '') {
                // Check if the input matches the expected format
                if (regex.test(this.formData.gst_number)) {
                    this.gstValidation = '';
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                }
            }
        },
        //--get invoice setting----
        getInvoiceSetting() {
            this.open_skeleton = true;
            axios.get('/invoice_settings', { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'What happening...!');
                    let parseData = response.data.data;
                    if (parseData.length > 0) {
                        if (parseData[0].selected_tax) {
                            parseData[0].selected_tax = JSON.parse(parseData[0].selected_tax);
                            if (typeof parseData[0].selected_tax === 'string') {
                                parseData[0].selected_tax = JSON.parse(parseData[0].selected_tax);
                            }
                        }
                        if (parseData[0].payment_opt) {
                            parseData[0].payment_opt = JSON.parse(parseData[0].payment_opt);
                            if (typeof parseData[0].payment_opt === 'string') {
                                parseData[0].payment_opt = JSON.parse(parseData[0].payment_opt);
                            }
                        }
                        if (parseData[0].text_color === '') {
                            parseData[0].text_color = "#000000";
                        }
                        if (parseData[0].color === '' || !parseData[0].color) {
                            parseData[0].color = "#FAF6F5";
                        }
                        if (parseData[0].minus_sale === '') {
                            parseData[0].minus_sale = false;
                        }
                        if (parseData[0].minus_sale !== '' && typeof parseData[0].minus_sale === 'string') {
                            parseData[0].minus_sale = JSON.parse(parseData[0].minus_sale);
                        }
                        if (parseData[0] && parseData[0].invoice_prefix) {
                            parseData[0].invoice_prefix = JSON.parse(parseData[0].invoice_prefix);
                            if (typeof parseData[0].invoice_prefix === 'string') {
                                parseData[0].invoice_prefix = JSON.parse(parseData[0].invoice_prefix);
                            }
                        }
                        // console.log(parseData[0], 'IIIIIIIIIII');
                        if (parseData.length !== 0 && parseData[0].disclaimer !== '') {
                            // let disclaimerMSG = parseData[0].disclaimer.split('\n');
                            this.formData = { ...parseData[0] };
                            this.shareData = { ...parseData[0] };
                            // console.log(get_company_data, 'What happeniggg.......');                         
                        }
                        if (parseData[0].header_custom && parseData[0].header_custom !== '') {
                            try {
                                // Assuming parseData is your JSON data
                                const headerCustom = JSON.parse(parseData[0].header_custom);
                                if (headerCustom && headerCustom !== '') {
                                    this.fields = headerCustom;
                                }
                            } catch (error) {
                                console.error('Error parsing header_custom:', error);
                            }
                        }
                    }
                    this.getExistData();
                    this.open_skeleton = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        getExistData() {
            axios.get(`/companies/${this.companyId}`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'Response');
                    let response_data = response.data.data;
                    // this.openMessage(response.data.message);
                    this.formData.name = response_data.company_name;
                    this.formData.gst_number = response_data.gst_number;
                    this.formData.business_contact = response_data.company_phone_no;
                    this.formData.email = response_data.email;
                    this.formData.address = response_data.address;
                    this.logo_img = response_data.logo;
                })
                .catch(error => {
                    console.error('Error', error);
                    // this.openMessage(error.response.data.message);
                })
        },
        //---get initial store data--
        getCompanyData(store_data) {
            this.formData.name = store_data.company_name;
            this.formData.gst_number = store_data.gst_number;
            this.formData.business_contact = store_data.company_phone_no;
            this.formData.email = store_data.email;
            this.formData.address = store_data.address;
            this.logo_img = store_data.logo;
        },
        getInitialStoreData(store_data) {
            let parseData = JSON.parse(JSON.stringify(store_data));
            if (parseData && parseData.length > 0) {
                if (parseData[0].selected_tax && typeof parseData[0].selected_tax === 'string') {
                    parseData[0].selected_tax = JSON.parse(parseData[0].selected_tax);
                    if (typeof parseData[0].selected_tax === 'string') {
                        parseData[0].selected_tax = JSON.parse(parseData[0].selected_tax);
                    }
                }
                if (parseData[0].payment_opt && typeof parseData[0].payment_opt === 'string') {
                    parseData[0].payment_opt = JSON.parse(parseData[0].payment_opt);
                    if (typeof parseData[0].payment_opt === 'string') {
                        parseData[0].payment_opt = JSON.parse(parseData[0].payment_opt);
                    }
                }
                if (parseData[0].text_color === '') {
                    parseData[0].text_color = "#000000";
                }
                if (parseData[0].color === '' || !parseData[0].color) {
                    parseData[0].color = "#FAF6F5";
                }
                if (parseData[0].minus_sale === '') {
                    parseData[0].minus_sale = false;
                }
                if (parseData[0].minus_sale !== '' && typeof parseData[0].minus_sale === 'string') {
                    parseData[0].minus_sale = JSON.parse(parseData[0].minus_sale);
                }
                if (parseData[0] && parseData[0].invoice_prefix && typeof parseData[0].invoice_prefix === 'string') {
                    parseData[0].invoice_prefix = JSON.parse(parseData[0].invoice_prefix);
                    if (typeof parseData[0].invoice_prefix === 'string') {
                        parseData[0].invoice_prefix = JSON.parse(parseData[0].invoice_prefix);
                    }
                }
                // console.log(parseData[0], 'IIIIIIIIIII');
                if (parseData.length !== 0 && parseData[0].disclaimer !== '') {
                    // let disclaimerMSG = parseData[0].disclaimer.split('\n');
                    this.formData = { ...parseData[0] };
                    this.shareData = { ...parseData[0] };
                    // console.log(get_company_data, 'What happeniggg.......');                         
                }
                if (parseData[0].header_custom && parseData[0].header_custom !== '') {
                    try {
                        // Assuming parseData is your JSON data
                        const headerCustom = JSON.parse(parseData[0].header_custom);
                        if (headerCustom && headerCustom !== '') {
                            this.fields = headerCustom;
                        }
                    } catch (error) {
                        console.error('Error parsing header_custom:', error);
                    }
                }
            }
            setTimeout(() => {
                this.open_skeleton = false;
            }, 300);

        },
        preventNegativeValue(event) {
            if (event.target.value < 0) {
                event.target.value = 0;
            }
        },
        //--is sms and whatsapp enble set as default---
        toggleSwitch(key) {
            if (key) {
                this.formData[key] = this.formData[key] == 0 ? 1 : 0;
            }
        },
        //---parse filter banck details---
        parseBankDetails() {
            if (this.formData.bank_details) {
                if (!this.isValidJson(this.formData.bank_details)) {
                    const details = this.formData.bank_details.split("\n");
                    const parsedData = {
                        account_no: "",
                        account_name: "",
                        bank: "",
                        branch: "",
                        ifsc: "",
                        upi_id: "",
                        g_pay: "",
                        phone_pay: "",
                        other: "",
                    };

                    details.forEach((line) => {
                        // Regular expressions for matching different bank fields
                        if (/account no|account number|AC\s?No|A\/C\s?No|A\/C|A\/C Number|Bank Account No.|A\/c no/i.test(line)) {
                            parsedData.account_no = line.split(":")[1]?.trim() || "";
                        } else if (/account name|account\s?name|name|Account Holder's Name|Account Holder/i.test(line)) {
                            parsedData.account_name = line.split(":")[1]?.trim() || "";
                        } else if (/bank name|bank\s?name/i.test(line)) {
                            parsedData.bank = line.split(":")[1]?.trim() || "";
                        } else if (/branch/i.test(line)) {
                            parsedData.branch = line.split(":")[1]?.trim() || "";
                        } else if (/ifsc|ifsc code|bank ifsc code/i.test(line)) {
                            parsedData.ifsc = line.split(":")[1]?.trim() || "";
                        } else if (/upi|upi id/i.test(line)) {
                            parsedData.upi_id = line.split(":")[1]?.trim() || "";
                        } else if (/gpay|phonepe|google pay|Gpay No/i.test(line)) {
                            parsedData.g_pay = line.split(":")[1]?.trim() || "";
                        } else if (/phonepay|phone pay|Phone Pe/i.test(line)) {
                            parsedData.phone_pay = line.split(":")[1]?.trim() || "";
                        }
                        //  else if (/other/i.test(line)) {
                        //     parsedData.other = line.split(":")[1]?.trim() || "";
                        // } 
                        else {
                            parsedData.other += line.trim() + "\n"; // Adding unmatched lines to 'other'
                        }
                    });
                    this.splitBankDetails = parsedData;
                } else {
                    // Convert the parsedData object into a JSON string
                    this.splitBankDetails = JSON.parse(this.formData.bank_details);
                }
            } else {
                this.splitBankDetails = {
                    account_no: "",
                    account_name: "",
                    bank: "",
                    branch: "",
                    ifsc: "",
                    upi_id: "",
                    g_pay: "",
                    phone_pay: "",
                    other: "",
                };
            }

        },
        isValidJson(data) {
            // Check if the string is not empty and is a string
            if (typeof data === 'string' && data.trim() !== '') {
                try {
                    // Try to parse the string as JSON
                    const parsed = JSON.parse(data);

                    // Check if it's an object
                    return typeof parsed === 'object' && parsed !== null;
                } catch (e) {
                    // If parsing fails, it's not valid JSON
                    return false;
                }
            }
            return false; // Not a valid JSON string
        },
        //---tax group---
        addGroup() {
            if (this.formData.tax_groups) {
                this.formData.tax_groups.push({
                    name: 'New Group',
                    taxes: [{ name: 'Tax Name', percentage: 0 }]
                });
            } else {
                this.formData.tax_groups = [{
                    name: 'New Group',
                    taxes: [{ name: 'Tax Name', percentage: 0 }]
                }];
            }
        },
        addTax(groupIndex) {
            this.formData.tax_groups[groupIndex].taxes.push({
                name: 'New Tax',
                percentage: 0
            });
        },
        deleteTax(groupIndex, taxIndex) {
            this.formData.tax_groups[groupIndex].taxes.splice(taxIndex, 1);
        },
        deleteGroup(groupIndex) {
            this.formData.tax_groups.splice(groupIndex, 1);
        },
        //--is sms and whatsapp enble set as default---
        toggleSwitch(key) {
            if (key) {
                this.formData[key] = this.formData[key] == 0 ? 1 : 0;
            }
        },
        //---custom fields--
        // Add a new field
        addField() {
            this.fields.push({ label: 'Modal', enabled: true, type: 'header' });
        },

        // Delete a specific field
        deleteField(index) {
            this.open_confirmBox = true;
            this.deleteIndex = index;
            this.type_del = 'custom';
        }
    },
    mounted() {
        // const collectForm = localStorage.getItem('track_new');
        // if (collectForm) {
        //     let dataParse = JSON.parse(collectForm);
        //     this.companyId = dataParse.company_id;
        //     this.userId = dataParse.user_id + '' + '';
        // }
        if (this.companyId !== null) {
            // this.getInvoiceSetting();
            // this.getExistData();
            if (this.currentInvoice && this.currentInvoice.length > 0) {
                this.open_skeleton = true;
                this.getInitialStoreData(this.currentInvoice);
                this.fetchInvoiceSetting();
                this.parseBankDetails();
            } else {
                this.open_skeleton = true;
                this.fetchInvoiceSetting();
            }
            //---comapny data----
            if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0) {
                this.getCompanyData(this.currentCompanyList);
                this.fetchCompanyList();
            } else {
                this.fetchCompanyList();
            }
        }
    },
    watch: {
        currentCompanyList: {
            deep: true,
            handler(newValue) {
                if (newValue && Object.keys(newValue).length > 0) {
                    this.getCompanyData(newValue);
                }
            }
        },
        currentInvoice: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0) {
                    this.getInitialStoreData(newValue);
                    this.parseBankDetails();
                } else {
                    this.open_skeleton = false;
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.fetchInvoiceSetting();
                this.fetchCompanyList();
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.saveData();
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.is_updated = false;
                    this.$emit('is-sales-save', newValue);
                }
            }
        }

    }
};
</script>

<style scoped>
input[type="radio"] {
    /* remove standard background appearance */
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    /* create custom radiobutton appearance */
    display: inline-block;
    width: 20px;
    height: 20px;
    padding: 3px;
    /* background-color only for content */
    background-clip: content-box;
    border: 2px solid #bbbbbb;
    background-color: #e7e6e7;
    border-radius: 50%;
}

/* appearance for checked radiobutton */
input[type="radio"]:checked {
    background-color: #05810f;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
        /* Use the actual background color of your content */
        color: black;
        /* Use the actual text color of your content */
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */
    }

    .non-printable {
        display: none;
    }
}
</style>