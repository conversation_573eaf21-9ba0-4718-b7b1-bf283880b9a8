<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-50 ">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 w-full -top-12 sm:top-[20px] sm:bottom-[10px] transform ease-in-out duration-300 text-sm h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <!-- <div class="modal-content "> -->
            <div class="set-header-background justify-between items-center flex py-3 px-2">
                <h2 class="text-lg font-semibold text-white">Notifications</h2>
                <p class="pr-5 cursor-pointer text-white font-bold" @click="cancelModal"><font-awesome-icon
                        icon="fa-solid fa-xmark" /></p>
            </div>
            <!-- Content based on selected option -->
            <div class="min-h-screen bg-gray-100 p-2" :class="{ 'mb-[60px]': isMobile }">
                <div v-if="notifications && notifications.length > 0">
                    <div class="flex justify-end items-center space-x-2 py-1">
                        <button @click="markAllAsRead" class="text-white text-sm px-2 py-1 rounded-full text-xs"
                            :class="{ 'blur-2 pointer-events-none opacity-50 bg-gray-300': unreadCount === 0, 'bg-blue-500': unreadCount > 0 }">
                            Mark All as Read ({{ unreadCount }})
                        </button>
                    </div>
                    <!-- Notifications List @click="navigateToPage(notification, index)"-->
                    <div class="overflow-y-auto h-[calc(100vh-6rem)] p-2" @scroll="onScroll">
                        <ul class=" divide-y divide-gray-200 mb-[64]" @scroll="onScroll">
                            <li v-for="(notification, index) in notifications" :key="notification.id"
                                class="p-2 hover:bg-blue-100 cursor-pointer"
                                :class="{ 'bg-white': notification.read_at === null }">
                                <div class="flex justify-between items-center space-x-3">
                                    <div class="flex items-center space-x-3"
                                        @click="navigateToPage(notification, index)">
                                        <img :src="notification.image ? notification.image : notify_img"
                                            alt="User Avatar" class="h-10 w-10 rounded-full" />
                                        <div v-if="notification.data" class="flex-1 min-w-0">
                                            <h4 class="text-sm text-gray-800 uppercase"
                                                :class="{ 'font-semibold': notification.read_at === null }">
                                                {{ notification.data.subject }}
                                            </h4>
                                            <p class="text-xs text-gray-700 line-clamp-1">{{ notification.data.message
                                                }}
                                            </p>
                                            <p class="text-xs text-gray-500"
                                                :title="formattedDate(notification.created_at)">
                                                {{ calculateDaysAgo(formattedDate(notification.created_at)) }}</p>
                                        </div>
                                    </div>
                                    <div v-if="notification.read_at === null">
                                        <button @click="markAsRead(notification, index)" class="text-blue-500 text-xs">
                                            Mark as Read
                                        </button>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <!--no data found-->
                        <div v-if="pagination && pagination.last_page > 0 && pagination.last_page === pagination.current_page"
                            class="text-sm">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                        <!--in case empty-->
                        <div v-if="notifications && notifications.length === 0">
                            <div class="flex justify-center items-center">
                                <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                            </div>
                            <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
                        </div>
                        <!-- Skeleton loading -->
                        <div v-if="loading">
                            <ul class="divide-y divide-gray-200">
                                <li v-for="i in 10" :key="i" class="p-4 hover:bg-gray-50 cursor-pointer animate-pulse">
                                    <div class="flex items-center space-x-3">
                                        <div class="h-10 w-10 bg-gray-300 rounded-full"></div>
                                        <div class="flex-1 min-w-0">
                                            <div class="h-4 bg-gray-300 rounded w-1/2 mb-2"></div>
                                            <div class="h-3 bg-gray-300 rounded w-3/4"></div>
                                        </div>
                                    </div>
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- Pagination Controls -->
                    <!-- <div class="flex justify-between items-center p-4 border-t border-gray-200 text-black">
                        <div class="flex items-center space-x-2">
                            <label for="perPage" class="text-xs sm:text-sm">Per Page:</label>
                            <select v-model="perPage" id="perPage" class="border rounded px-2 py-1 text-xs sm:text-sm">
                                <option v-for="size in perPageOptions" :key="size" :value="size">{{ size }}</option>
                            </select>
                        </div> -->

                    <!-- Page Numbers -->
                    <!-- <div class="flex space-x-2">
                            <button v-for="page in totalPages" :key="page" @click="setCurrentPage(page)" :class="{
                                'bg-gray-300 text-gray-700': currentPage === page,
                                'bg-gray-200 hover:bg-gray-300': currentPage !== page,
                            }" class="px-3 py-1 text-xs sm:text-sm rounded">
                                {{ page }}
                            </button>
                        </div>
                    </div> -->
                </div>

            </div>
        </div>
    </div>
</template>

<script>
import axios from 'axios';
import { mapActions, mapGetters } from 'vuex';
export default {

    props: {
        showModal: Boolean,
        isMobile: Boolean,
        notifications: Object,
        pagination: Object,
        unreadCount: Number
    },
    data() {
        return {
            isOpen: false,
            perPageOptions: [1, 10, 20, 30, 40, 50, 100],
            notify_img: '/images/service_page/comments.png',
            now: null,
            loading: false, // Initial loading state for skeleton
            empty_data: '/images/dashboard/empty.svg',

        };
    },
    created() {
        setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    methods: {
        ...mapActions('notificationsList', ['fetchNotificationList', 'updateNotificationAtIndex']),
        cancelModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        //---formated display date---
        formattedDate(timestamp) {
            const date = new Date(timestamp);
            date.setUTCHours(date.getUTCHours() + 5); // Add 5 hours for the time zone offset
            date.setUTCMinutes(date.getUTCMinutes() + 30); // Add 30 minutes for the time zone offset
            const formattedDate = date.toISOString().slice(0, 16).replace('T', ' ');
            return formattedDate;
        },

        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        async onScroll(event) {
            const bottomReached = event.target.scrollTop + event.target.clientHeight >= event.target.scrollHeight - 10;
            if (bottomReached) {
                await this.loadMoreNotifications(); // Load more notifications when the bottom is reached
            }
        },
        async loadMoreNotifications() {
            if (!this.loading) {
                if (this.pagination && this.pagination.current_page < this.pagination.last_page) {
                    this.loading = true; // Show loading more indicator
                    setTimeout(() => {
                        this.fetchNotificationList({ page: this.pagination.current_page + 1, per_page: this.pagination.per_page });
                    }, 1000);
                } else {
                    this.loading = false;
                }
            }
        },
        // Mark a single notification as read
        async markAsRead(notification, index) {
            let send_data = {};
            if (notification && notification.read_at === null) {
                send_data = { notification_id: notification.id, type: 'markas_read' };
            } else {
                send_data = { type: 'markas_read' };
            }
            axios.get('/notification-lists', { params: send_data })
                .then(response => {
                    if (notification && notification.read_at === null) {
                        let find_data = response.data.notification_data.find(opt => opt.id == notification.id);
                        if (find_data) {
                            this.updateNotificationAtIndex({ index: index, newNotification: find_data });
                        }
                    } else {
                        this.fetchNotificationList({ page: 1, per_page: 10 });
                    }
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                    return error;
                });

        },

        // Mark all notifications as read
        async markAllAsRead() {
            await this.markAsRead();
        },
        //---navigate to page---
        navigateToPage(notification, index) {
            if (notification) {
                if (notification.read_at === null) {
                    this.markAsRead(notification, index);
                }
                let type_data = notification.type.split('\\');
                if (type_data && type_data.length > 0 && type_data[type_data.length - 1]) {
                    this.selectedOptions(type_data[type_data.length - 1].toLowerCase(), notification.data);
                }
            }
        },
        selectedOptions(feature, data) {
            this.$emit('movePage');
            switch (feature) {
                case 'services': {
                    if (data && data.id && data.category_id && data.category_name) {
                        this.$router.push({
                            name: 'service-data-view',
                            params: { type: data.category_name, id: data.category_id, serviceId: data.id }
                        });
                    }
                    break;
                }
                case 'leads': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'leadEdit',
                            query: { recordId: data.id }
                        });
                    }
                    break;
                }
                case 'amcs': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'amcView',
                            query: { recordId: data.id }
                        });
                    }
                    break;
                }
                case 'sales': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'print-preview',
                            query: { type: 'sales_home', invoice_no: data.id }
                        });
                    }
                    break;
                }
                case 'estimations': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'print-preview',
                            query: { type: 'estimation', est_no: data.id, back: 'home' }
                        });
                    }
                    break;
                }
                case 'proforma': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'print-preview',
                            query: { type: 'proforma', proforma_no: data.id, back: 'home' }
                        });
                    }
                    break;
                }
                case 'productdetails': {
                    if (data && data.id) {
                        this.$router.push('/items');
                    }
                    break;
                }
                case 'customers': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'customers-view',
                            params: { id: data.id }
                        });
                    }
                    break;
                }
                case 'expenses': {
                    if (data && data.id) {
                        this.$router.push('/expense');
                    }
                    break;
                }
                case 'categories': {
                    if (data && data.id) {
                        this.$router.push('/categories');
                    }
                    break;
                }
                case 'rma': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'open_rma_edit',
                            params: { rmaId: data.id, type: 'edit' }
                        });
                    }
                    break;
                }
                case 'purchaseorder': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'purchase_order',
                        });
                    }
                    break;
                }
                case 'supplier': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'supplier',
                        });
                    }
                    break;
                }
                case 'warehouse': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'warehouse',
                        });
                    }
                    break;
                }
                case 'paymentout': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'payment_home_out',
                        });
                    }
                    break;
                }
                case 'paymentin': {
                    if (data && data.id) {
                        this.$router.push({
                            name: 'payment_home',
                        });
                    }
                    break;
                }
                default: {
                    break;
                }
            }
            this.cancelModal();
        },
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
        notifications: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.loading = false;
                }
            }
        }
    },
    computed: {
        ...mapGetters('notificationsList', ['currentNotificationList']),

    }
};
</script>

<style scoped></style>