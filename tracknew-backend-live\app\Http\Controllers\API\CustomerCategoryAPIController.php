<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateCustomerCategoryAPIRequest;
use App\Http\Requests\API\UpdateCustomerCategoryAPIRequest;
use App\Models\CustomerCategory;
use App\Repositories\CustomerCategoryRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class CustomerCategoryController
 * @package App\Http\Controllers\API
 */

class CustomerCategoryAPIController extends AppBaseController
{
    /** @var  CustomerCategoryRepository */
    private $customerCategoryRepository;

    public function __construct(CustomerCategoryRepository $customerCategoryRepo)
    {
        $this->customerCategoryRepository = $customerCategoryRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/customer-categories",
     *      summary="getCustomerCategoryList",
     *      tags={"CustomerCategory"},
     *      description="Get all CustomerCategories",
     *      @OA\Parameter(
     *         name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/CustomerCategory")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {   
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }    

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $customersQuery = CustomerCategory::where('company_id', $companyId)->orderBy('id', 'desc');

        if ($perPage === 'all') {
            $perPage = $customersQuery->count();
        }

        $customers = $customersQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
        
        $response = [
            'success' => true,
            'data' => $customers->items(), 
            'pagination' => [
                'total' => $customers->total(),
                'per_page' => $customers->perPage(),
                'current_page' => $customers->currentPage(),
                'last_page' => $customers->lastPage(),
                'from' => $customers->firstItem(),
                'to' => $customers->lastItem(),
            ],
        ];
        
        return response()->json($response);
   }


    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/customer-categories",
     *      summary="createCustomerCategory",
     *      tags={"CustomerCategory"},
     *      description="Create CustomerCategory",
   *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/CustomerCategory")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CustomerCategory"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateCustomerCategoryAPIRequest $request)
    {
        $input = $request->all();

        $customerCategory = $this->customerCategoryRepository->create($input);

        return $this->sendResponse($customerCategory->toArray(), 'Customer Category saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/customer-categories/{id}",
     *      summary="getCustomerCategoryItem",
     *      tags={"CustomerCategory"},
     *      description="Get CustomerCategory",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CustomerCategory",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CustomerCategory"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var CustomerCategory $customerCategory */
        $customerCategory = $this->customerCategoryRepository->find($id);

        if (empty($customerCategory)) {
            return $this->sendError('Customer Category not found');
        }

        return $this->sendResponse($customerCategory->toArray(), 'Customer Category retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/customer-categories/{id}",
     *      summary="updateCustomerCategory",
     *      tags={"CustomerCategory"},
     *      description="Update CustomerCategory",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CustomerCategory",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
   *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/CustomerCategory")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CustomerCategory"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateCustomerCategoryAPIRequest $request)
    {
        $input = $request->all();

        /** @var CustomerCategory $customerCategory */
        $customerCategory = $this->customerCategoryRepository->find($id);

        if (empty($customerCategory)) {
            return $this->sendError('Customer Category not found');
        }

        $customerCategory = $this->customerCategoryRepository->update($input, $id);

        return $this->sendResponse($customerCategory->toArray(), 'CustomerCategory updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/customer-categories/{id}",
     *      summary="deleteCustomerCategory",
     *      tags={"CustomerCategory"},
     *      description="Delete CustomerCategory",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CustomerCategory",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var CustomerCategory $customerCategory */
        $customerCategory = $this->customerCategoryRepository->find($id);

        if (empty($customerCategory)) {
            return $this->sendError('Customer Category not found');
        }

        $customerCategory->delete();

        return $this->sendSuccess('Customer Category deleted successfully');
    }
}
