<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateSupplierAPIRequest;
use App\Http\Requests\API\UpdateSupplierAPIRequest;
use App\Models\Supplier;
use App\Models\PurchaseOrder;
use App\Repositories\SupplierRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class SupplierController
 * @package App\Http\Controllers\API
 */

class SupplierAPIController extends AppBaseController
{
    /** @var  SupplierRepository */
    private $supplierRepository;

    public function __construct(SupplierRepository $supplierRepo)
    {
        $this->supplierRepository = $supplierRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/suppliers",
     *      summary="getSupplierList",
     *      tags={"Supplier"},
     *      description="Get all Suppliers",
     *      @OA\Parameter(
     *         name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/Supplier")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        // $suppliers = $this->supplierRepository->all(
        //     $request->except(['skip', 'limit']),
        //     $request->get('skip'),
        //     $request->get('limit')
        // );
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        }

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);
       
        $suppliersQuery = Supplier::where('company_id', $companyId);


        if ($perPage === 'all') {
            $perPage = $suppliersQuery->count();
        }    

        $suppliers = $suppliersQuery->orderBy('id', 'desc')->paginate($perPage, ['*'], 'page', $page);
        
        $suppliers->each(function ($supplier) {
            $supplier->balance_amount = (float) PurchaseOrder::where('supplier_id', $supplier->id)->sum('balance_amount');
            $supplier->return_amount  = (float) PurchaseOrder::where('supplier_id', $supplier->id)->sum('return_amount');
        });
        

        $response = [
            'success' => true,
            'data' => $suppliers->items(), // Get the paginated items
            'pagination' => [
                'total' => $suppliers->total(),
                'per_page' => $suppliers->perPage(),
                'current_page' => $suppliers->currentPage(),
                'last_page' => $suppliers->lastPage(),
                'from' => $suppliers->firstItem(),
                'to' => $suppliers->lastItem(),
            ]
        ];

        return response()->json($response);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/suppliers",
     *      summary="createSupplier",
     *      tags={"Supplier"},
     *      description="Create Supplier",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Supplier")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Supplier"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateSupplierAPIRequest $request)
    {
        $input = $request->all();

        $supplier = $this->supplierRepository->create($input);

        return $this->sendResponse($supplier->toArray(), 'Supplier saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/suppliers/{id}",
     *      summary="getSupplierItem",
     *      tags={"Supplier"},
     *      description="Get Supplier",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Supplier",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Supplier"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var Supplier $supplier */
        $supplier = $this->supplierRepository->find($id);

        if (empty($supplier)) {
            return $this->sendError('Supplier not found');
        }

        return $this->sendResponse($supplier->toArray(), 'Supplier retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/suppliers/{id}",
     *      summary="updateSupplier",
     *      tags={"Supplier"},
     *      description="Update Supplier",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Supplier",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/Supplier")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/Supplier"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateSupplierAPIRequest $request)
    {
        $input = $request->all();

        /** @var Supplier $supplier */
        $supplier = $this->supplierRepository->find($id);

        if (empty($supplier)) {
            return $this->sendError('Supplier not found');
        }

        $supplier = $this->supplierRepository->update($input, $id);

        return $this->sendResponse($supplier->toArray(), 'Supplier updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/suppliers/{id}",
     *      summary="deleteSupplier",
     *      tags={"Supplier"},
     *      description="Delete Supplier",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of Supplier",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var Supplier $supplier */
        $supplier = $this->supplierRepository->find($id);

        if (empty($supplier)) {
            return $this->sendError('Supplier not found');
        }

        $supplier->delete();

        return $this->sendSuccess('Supplier deleted successfully');
    }
}
