<template>
    <div class="scanner-container">
        <video ref="videoElement" class="w-full rounded-lg border-2 border-gray-200"></video>

        <div class="scan-overlay relative">
            <div class="scan-frame"></div>
        </div>

        <div class="mt-4 flex gap-2 justify-center">
            <button @click="toggleScan" :class="isScanning ? 'bg-red-500' : 'bg-blue-500'"
                class="text-white px-4 py-2 rounded">
                {{ isScanning ? 'Stop Scanning' : 'Start Scanning' }}
            </button>
            <button @click="handleFileUpload" class="bg-purple-500 text-white px-4 py-2 rounded">
                Upload Image
            </button>
        </div>

        <input type="file" ref="fileInput" accept="image/*" class="hidden" @change="onFileSelected">
    </div>
</template>

<script setup>
import { ref, onBeforeUnmount } from 'vue';
import { BrowserMultiFormatReader } from '@zxing/library';

const emit = defineEmits(['code-scanned']);

const videoElement = ref(null);
const fileInput = ref(null);
const isScanning = ref(false);
const codeReader = new BrowserMultiFormatReader();

let scanningTimeout;

const toggleScan = async () => {
    if (isScanning.value) {
        stopScanning();
    } else {
        await startScanning();
    }
};

const startScanning = async () => {
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        if (videoDevices.length === 0) {
            alert('No camera found. Please ensure you have a camera connected and permissions granted.');
            return;
        }

        const selectedDevice = videoDevices.find(device =>
            device.label.toLowerCase().includes('back') ||
            device.label.toLowerCase().includes('rear')
        ) || videoDevices[0];

        const stream = await navigator.mediaDevices.getUserMedia({
            video: {
                deviceId: selectedDevice.deviceId,
                facingMode: { ideal: 'environment' }
            }
        });

        videoElement.value.srcObject = stream;

        scanningTimeout = setTimeout(async () => {
            try {
                const result = await codeReader.decodeFromVideoElement(videoElement.value);
                if (result) {
                    emit('code-scanned', result.text);
                    stopScanning();
                }
            } catch (error) {
                console.error('Scanning error:', error);
                if (isScanning.value) {
                    startScanning();
                }
            }
        }, 100);

        isScanning.value = true;
    } catch (error) {
        console.error('Camera error:', error);
        alert(`Camera error: ${error.message}`);
        stopScanning();
    }
};

const stopScanning = () => {
    clearTimeout(scanningTimeout);
    if (videoElement.value?.srcObject) {
        videoElement.value.srcObject.getTracks().forEach(track => track.stop());
        videoElement.value.srcObject = null;
    }
    isScanning.value = false;
    codeReader.reset();
};

const handleFileUpload = () => {
    fileInput.value.click();
};

const onFileSelected = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Reset input for future uploads
    event.target.value = '';

    const img = new Image();
    const objectUrl = URL.createObjectURL(file);
    img.src = objectUrl;

    try {
        // Wait for image to load
        await new Promise((resolve, reject) => {
            img.onload = resolve;
            img.onerror = () => reject(new Error('Failed to load image'));
        });

        // console.log('Image loaded, dimensions:', img.width, 'x', img.height);

        // Create canvas for better processing
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');

        // Maintain aspect ratio but scale up if too small
        const scaleFactor = Math.max(1, 800 / Math.max(img.width, img.height));
        canvas.width = img.width * scaleFactor;
        canvas.height = img.height * scaleFactor;

        ctx.drawImage(img, 0, 0, canvas.width, canvas.height);

        // Try different decoding approaches
        let result;
        try {
            // console.log('Attempting to decode from canvas...');
            result = await codeReader.decodeFromCanvas(canvas);
        } catch (canvasError) {
            // console.log('Canvas decode failed, trying image directly...', canvasError);
            result = await codeReader.decodeFromImageElement(img);
        }

        if (result) {
            // console.log('Decoding successful:', result.text);
            emit('code-scanned', result.text);
        } else {
            throw new Error('No barcode or QR code detected');
        }

    } catch (error) {
        console.error('Scan error details:', {
            error: error,
            message: error.message,
            stack: error.stack
        });

        // Try enhancing the image and scanning again
        try {
            const enhancedCanvas = enhanceImageForScanning(img);
            const enhancedResult = await codeReader.decodeFromCanvas(enhancedCanvas);
            emit('code-scanned', enhancedResult.text);
            return;
        } catch (enhancedError) {
            console.log('Enhanced scan also failed');
        }

        alert(`Scan failed: ${error.message}\n\nPlease ensure:\n1. The barcode/QR code is clear\n2. The image is well-lit\n3. The code fills most of the image`);
    } finally {
        URL.revokeObjectURL(objectUrl);
    }
};

// Helper function to enhance image for better scanning
const enhanceImageForScanning = (img) => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    canvas.width = img.width;
    canvas.height = img.height;
    ctx.drawImage(img, 0, 0);

    // Apply contrast enhancement
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // Simple contrast adjustment
    for (let i = 0; i < data.length; i += 4) {
        const r = data[i];
        const g = data[i + 1];
        const b = data[i + 2];

        // Convert to grayscale and increase contrast
        const gray = 0.299 * r + 0.587 * g + 0.114 * b;
        const contrast = 1.5; // Increase contrast
        const adjusted = (gray - 128) * contrast + 128;

        data[i] = data[i + 1] = data[i + 2] = adjusted < 0 ? 0 : adjusted > 255 ? 255 : adjusted;
    }

    ctx.putImageData(imageData, 0, 0);
    return canvas;
};

onBeforeUnmount(() => {
    stopScanning();
});
</script>

<style scoped>
.scanner-container {
    position: relative;
    max-width: 600px;
    margin: 0 auto;
}

.scan-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.scan-frame {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 60%;
    height: 60%;
    border: 4px solid #48bb78;
    animation: scan 2s infinite;
}

@keyframes scan {
    0% {
        clip-path: inset(0 0 90% 0);
    }

    50% {
        clip-path: inset(90% 0 0 0);
    }

    100% {
        clip-path: inset(0 0 90% 0);
    }
}
</style>