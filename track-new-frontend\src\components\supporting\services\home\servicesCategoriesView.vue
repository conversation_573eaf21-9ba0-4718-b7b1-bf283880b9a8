<template>
    <div :class="{ 'manualStyle text-sm font-medium sm:mb-[70px] lg:mb-[0px]': isMobile, 'text-sm font-medium': !isMobile }"
        class="custom-scrollbar-hidden" ref="scrollContainer" @scroll="handleScroll">
        <!--Category list 'border-b-4 rounded border-blue-600': category_type === opt.id -->
        <div v-if="category_list && category_list.length > 0"
            class="flex overflow-auto w-full headBar custom-scrollbar-hidden set-header-background px-5"
            :class="{ 'mb-2': !isMobile }">
            <div v-for="(opt, index) in category_list" :key="index" @click="selectedCategory(opt)"
                class="cursor-pointer text-left center items-center flex justify-center ml-1 px-2 py-1 cursor-pointer rounded-tr-[10px]  rounded-tl-[10px]"
                :class="{ 'text-blue-600 bg-white': category_type === opt.id, 'text-white': category_type !== opt.id, 'hidden': !opt.service_status }">
                <div class="flex justify-center items-center h-[30px]">
                    <p style="white-space: nowrap;" class="px-1 text-nowrap">{{ opt.service_category }}</p>
                    <p class="pl-1">({{ opt.services_count }})</p>
                    <span v-if="category_type !== opt.id" class="pl-1"><font-awesome-icon
                            icon="fa-solid fa-angle-right" /></span>
                    <span v-if="category_type === opt.id" class="pl-1"><font-awesome-icon
                            icon="fa-solid fa-angle-down" /></span>
                </div>
            </div>
        </div>
        <!--new design header-->
        <div class="px-1" :class="{ 'my-custom-margin': !isMobile }">
            <!---status view  -->
            <div v-if="getStatusOption && getStatusOption.length > 0" class="custom-scrollbar-hidden"
                :class="{ 'flex overflow-auto bg-white px-2': isMobile, 'grid sm:grid-cols-4 lg:grid-cols-5 gap-2 font-normal': !isMobile }">
                <!-- Pending Status Card -->
                <div class="p-2 cursor-pointer mr-2 flex justify-between items-center bg-amber-600 text-white"
                    :class="{ 'text-blue-800 border-b-4 border-blue-800': status_select === 'pending', 'shadow border rounded': !isMobile }"
                    @click="getStatusLabel('pending', 1)">
                    <div class="px-2 w-full">
                        <div class="flex justify-between items-center rounded rounded-full">
                            <p class='flex justify-between items-center'>
                                <span class="pr-1"><font-awesome-icon icon="fa-solid fa-hourglass-half"
                                        :style="{ color: status_select === 'pending' ? 'rgb(30 64 175)' : 'gray' }"
                                        size="lg" /></span>
                                <span>Pending</span>
                            </p>
                            <p class="ml-2"> ({{ pending_status_data.total }}) </p>
                        </div>
                    </div>
                </div>
                <div v-for="(opt, index) in getStatusOption" :key="index"
                    class="p-2 cursor-pointer flex justify-between items-center text-white"
                    @click="getStatusLabel(index, 1)" :ref="`statusOption_${index}`"
                    :class="{ 'border-b-4 border-blue-800': status_select === index, 'shadow border rounded hover:text-blue-700': !isMobile, 'hidden': index !== 4 && index !== 7, 'bg-lime-500': index == 4, 'bg-green-600': index == 7 }">
                    <div class=" px-2 flex justify-between items-center w-full" :class="{ 'pr-6': isMobile }">
                        <img :src="statusIcons[index]" :class="{ 'grayscale': status_select !== index }"
                            class="w-5 h-5 mr-2" />
                        <p style="white-space: nowrap;">
                            <span class="text-nowrap">{{ opt.name }}</span>
                        </p>
                        <p style="white-space: nowrap;" class='text-center pl-2'>({{ opt.total }})</p>
                    </div>
                </div>
                <div class="bg-fuchsia-500 text-white p-2 cursor-pointer mr-2 flex justify-between items-center w-full"
                    :class="{ 'text-blue-800 border-b-4 border-blue-800 bg-blue-100': status_select === 12, 'shadow border rounded': !isMobile }"
                    @click="getStatusLabel(12, 1)">
                    <div class="px-2 w-full">
                        <div class="flex justify-between items-center rounded rounded-full">
                            <p class='flex justify-between items-center'>
                                <span class="material-icons mr-1 text-sm"
                                    :class="{ 'bg-blue-800 text-white rounded-full px-1': status_select === 12 }">
                                    done_all
                                </span> All
                            </p>
                            <p class="ml-2"> ({{ all_count_status }}) </p>
                        </div>
                    </div>
                </div>
                <!-- Render the button at index 8 separately if needed -->
                <div v-if="getStatusOption[8]"
                    class="p-2 cursor-pointer flex justify-between items-center bg-yellow-800 text-white"
                    @click="getStatusLabel(8, 1)" :ref="'statusOption_8'" :class="{
                        'text-blue-800 border-b-4 border-blue-800': status_select === 8,
                        'shadow border rounded hover:text-blue-700': !isMobile,
                        'bg-yello-900': getStatusOption[8].name === 'New Service',
                    }">
                    <div class="px-2 flex justify-between items-center w-full" :class="{ 'pr-6': isMobile }">
                        <img :src="statusIcons[8]" :class="{ 'grayscale': status_select !== 8 }" class="w-5 h-5 mr-2" />
                        <p style="white-space: nowrap;">
                            <span class="text-nowrap">{{ getStatusOption[8].name }}</span>
                        </p>
                        <p style="white-space: nowrap;" class="text-center pl-2">({{ getStatusOption[8].total }})</p>
                    </div>
                </div>

                <!-- Render the other options in a loop excluding index 8 -->
                <div v-for="(opt, index) in getStatusOption" :key="index"
                    class="p-2 cursor-pointer flex justify-between items-center text-white"
                    @click="getStatusLabel(index, 1)" :ref="`statusOption_${index}`" :class="{
                        'text-blue-800 border-b-4 border-blue-800': status_select === index,
                        'shadow border rounded hover:text-blue-700': !isMobile,
                        'hidden': index == 4 || index == 7 || index == 8,
                        'bg-blue-500': opt.name === 'Service Taken',
                        'bg-rose-500': opt.name === 'Hold',
                        'bg-yellow-500': opt.name === 'In-progress',
                        'bg-violet-500': opt.name === 'New Estimate',
                        'bg-[#14532d]': opt.name === 'Delivered',
                        'bg-red-500': opt.name === 'Cancelled',
                        'bg-amber-700': opt.name === 'New Service', /* Unique color for index 9 */
                        'bg-indigo-500': opt.name === 'Awaiting For Payment', /* Unique color for index 10 */
                        'bg-green-600': opt.name === 'Service Completed', /* Unique color for index 11 */
                        'bg-red-600': opt.name === 'Return To Customer', /* Unique color for index 11 */
                    }">
                    <div class="px-2 flex justify-between items-center w-full" :class="{ 'pr-6': isMobile }">
                        <img :src="statusIcons[index]" :class="{ 'grayscale': status_select !== index }"
                            class="w-5 h-5 mr-2" />
                        <p style="white-space: nowrap;">
                            <span class="text-nowrap">{{ opt.name }}</span>
                        </p>
                        <p style="white-space: nowrap;" class="text-center pl-2">({{ opt.total }})</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="my-custom-margin">
            <div v-if="!isMobile" class="flex justify-between m-1 relative mt-3">
                <div class="flex mr-2 space-x-4">
                    <button @click="addServices" :class="{ 'mr-2': isMobile }"
                        class="bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-center shadow-inner shadow-green-200 border border-green-500 ">
                        <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /></span>
                        <span v-if="!isMobile" class="text-center">New Service</span>
                    </button>
                    <!--view design-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="px-2 py-1 flex-shrink-0 cursor-pointer info-msg border rounded-lg border-gray-500"
                            :class="{ 'bg-white': items_category === 'tile' }" @click="toggleView"
                            :title02="`Table view`">
                            <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                            <!-- <font-awesome-icon v-else icon="fa-solid fa-grip" size="lg" /> -->
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border rounded-lg border border-gray-500"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="`Card view`">
                            <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>

                </div>
                <!--Setting-->
                <div class="flex icon-color relative">
                    <div class="mr-3">
                        <button @click="startScanner = true"
                            class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300">
                            <font-awesome-icon icon="fa-solid fa-expand" class="pr-1" /> Scan
                        </button>
                    </div>
                    <div v-if="data && data.length > 0" class="flex items-center">
                        <button
                            class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                            :class="{ 'cursor-not-allowed blur-sm': data.length == 0 }" :disabled="data.length == 0"
                            @click="openSearch"><font-awesome-icon icon="fa-solid fa-magnifying-glass" class="pr-1" />
                            Advance Search </button>
                    </div>
                    <!----filter options----->
                    <!-- Main Filter Button -->
                    <div ref="dropdownContainerFilter" class="ml-3 items-center relative">

                        <button @click="toggleMainDropdown" :disabled="data.length == 0"
                            class="inline-flex justify-between items-center w-full rounded-lg border border-gray-400 shadow-inner shadow-gray-100 bg-gray-200 p-1 hover:bg-blue-300"
                            :class="{ 'cursor-not-allowed': data.length == 0 }">
                            <span class="inline-flex items-center w-full pointer-events-none">
                                <font-awesome-icon icon="fa-solid fa-filter" class="px-1" /> Filter
                                <font-awesome-icon v-if="!isMainDropdownOpen" icon="fa-solid fa-angle-down"
                                    class="pl-3" />
                                <font-awesome-icon v-if="isMainDropdownOpen" icon="fa-solid fa-angle-up" class="pl-3" />
                            </span>
                        </button>
                        <!-- Main Dropdown -->
                        <div v-if="isMainDropdownOpen" ref="mainDropdown"
                            class="absolute mt-2 w-56 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 right-0 border">
                            <div class="py-1">
                                <!-- By Due Option with Sub-Dropdown -->
                                <div class="relative">
                                    <button @click="toggleSubDropdown" @mouseenter="toggleSubDropdown"
                                        @mouseleave="toggleSubDropdown"
                                        class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200">By
                                        Due</button>

                                    <!-- Sub-Dropdown for By Due -->
                                    <div v-if="showSubDropdown" @mouseenter="toggleMouserHoverSub"
                                        @mouseleave="toggleMouserHoverSub" ref="subDropdown"
                                        class="absolute right-full top-0 mr-2 w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none border">
                                        <ul class="py-1 text-left">
                                            <li v-for="(option, index) in followupOptions" :key="index"
                                                class="cursor-pointer py-2 px-5 hover:bg-blue-200"
                                                :class="{ 'bg-blue-200 text-blue-700': followup_select === option.value }"
                                                @click="handleFollowup(option.value, 1)">
                                                {{ option.label }}
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <!-- Other Options -->
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('by Date')">By Date</button>
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('customer')">By Customer</button>
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('employee')">By Assigned to</button>
                                <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                                    @click.stop="changeFilter('serial_number')">By Serial Number</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- </div> -->
            <!---fiter information-->
            <div v-if="(Object.keys(filteredBy).length > 0 || Object.keys(searchByCustomer).length > 0 || (status_select !== null) || (followup_select && followup_select !== 'all'))"
                class="text-xs flex -mb-2 flex-row overflow-auto mx-4">
                <p class="text-blue-600 mr-1">Filtered By:</p>
                <div v-if="Object.keys(filteredBy).length > 0" v-for="(value, key) in filteredBy" :key="key"
                    class="flex flex-row text-blue-600 mr-1">
                    <p v-if="key !== 'customer_id'" class="mr-1">{{ key === 'assign_to' ? 'Assigne to' : key }}
                        =
                    </p>
                    <p v-if="key !== 'customer_id'">{{key === 'assign_to' ? value.name : key === 'type' ?
                        leadType.find(opt => opt.id === value).name : key === 'status' ?
                            value === 0 ? 'Open' : value === 1 ? 'Progress' : value === 2 ? 'Completed' : value ===
                                3 ?
                                'Cancelled' : value === 4 ? 'Hold' : '' : value}}</p>
                </div>
                <div v-if="Object.keys(searchByCustomer).length > 0" class="text-blue-600 mr-2">Search By
                    Customer =
                    {{
                        searchByCustomer.first_name + '' + (searchByCustomer.last_name ? searchByCustomer.last_name
                            :
                            '') +
                        ' - ' +
                        searchByCustomer.contact_number }}</div>
                <div v-if="status_select !== null" class="text-blue-600 mr-2">
                    Status = {{ getStatusOption[status_select] ? getStatusOption[status_select].name : status_select ===
                        'pending' ? 'Pending' : 'All' }}
                </div>
                <div v-if="followup_select && followup_select !== 'all'" class="text-blue-600 mr-2"> FollowUp =
                    {{ followup_select }}</div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton && data.length > 0" class="text-sm mt-4 " :class="{ 'm-1': !isMobile }">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">

                    <div v-if="!isMobile" class="flex justify-between relative"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                            <div v-if="((status_select !== null))">
                                <div v-if="status_select !== null" class="mr-2 font-semibold" :class="{
                                    'text-blue-700': status_select == 0, 'text-red-700': status_select == 1, 'text-yellow-700': status_select == 2, 'text-violet-700': status_select == 3, 'text-lime-700': status_select == 4, 'text-[#14532d]': status_select == 5, 'text-pink-700': status_select == 6, 'text-green-700': status_select == 7, 'text-amber-700': status_select == 'pending', 'text-rose-700': ![0, 1, 2, 3, 4, 5, 6, 7, 'pending'].includes(status_select)
                                }">
                                    {{ getStatusOption[status_select] ? getStatusOption[status_select].name
                                        :
                                        status_select ===
                                            'pending' ? 'Pending' : 'All' }} Services
                                </div>

                            </div>
                        </div>
                        <!--search bar--->
                        <div class="w-full sm:w-1/2 lg:w-1/4 relative">
                            <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                @resetData="resetToSearch" :resetSearch="resetSearchData">
                            </searchCustomer>
                            <!-- <div class="relative">
                                <serviceSearch :selectedCategory="this.category_type" :isMobile="true"></serviceSearch>
                            </div> -->
                        </div>
                    </div>

                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <!-- Set a max height for the table container to enable scrolling -->
                        <!-- <div class="overflow-auto lg:max-h-[400px] 2xl:max-h-[600px]"> -->
                        <table class="table w-full px-1">
                            <thead class="font-medium">
                                <!---sticky top-0  z-50-->
                                <tr class="table-head text-black">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ hidden: !column.visible }"
                                        class="py-2 cursor-pointer relative table-border"
                                        @mouseover="onMouseOver(column)" @mouseleave="onMouseLeave(column)"
                                        @click="getSortType(column)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <!--column.label === 'Created At' ? 'Created Date' : column.label ===
                                                'Updated At' ? 'Updated Date' : -->
                                            <p>{{ column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    class="info-msg"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="px-4 py-2 leading-none table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="text-black">
                                <tr v-for="(record, index) in paginatedData" :key="index"
                                    class="border-b px-2 border-gray-400 hover:bg-gray-200">
                                    <td class="py-2 text-center cursor-pointer table-border"
                                        v-for="(column, index) in columns" :key="index" :class="{
                                            'hidden': !column.visible, 'w-[110px]': column.field === 'status', 'px-1': column.field !== 'status'
                                        }" @click="viewRecord(record)">
                                        <p v-if="!column.category" class="text-center">
                                            <span v-if="column.field === 'notification'">
                                                {{ parseNotification(record[column.field]).join(', ') }}
                                            </span>
                                            <!-- <span>{{serviceTrack[Number(record[column.field])]}}</span> -->
                                            <template
                                                v-if="column.field === 'status' && record[column.field] !== null && serviceTrack && Array.isArray(serviceTrack) && serviceTrack.length > 0">
                                                <span :class="{
                                                    'bg-blue-500': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[0] ? serviceTrack[0].name : 'service taken'),
                                                    'bg-pink-500': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[1] ? serviceTrack[1].name : 'hold'),
                                                    'bg-yellow-500': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[2] ? serviceTrack[2].name : 'in -progress'),
                                                    'bg-violet-500': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[3] ? serviceTrack[3].name : 'new estimate'),
                                                    'bg-lime-500': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[4] ? serviceTrack[4].name : 'to be delivered'),
                                                    'bg-[#14532d]': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[5] ? serviceTrack[5].name : 'delivered'),
                                                    'bg-red-500': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[6] ? serviceTrack[6].name : 'cancelled'),
                                                    'bg-green-600': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[7] ? serviceTrack[7].name : 'completed'),
                                                    'bg-amber-700': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[8] && serviceTrack[8].name ? serviceTrack[8].name : 'new service'),
                                                    'bg-indigo-500': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[9] && serviceTrack[9].name ? serviceTrack[9].name : 'awaiting for payment'),
                                                    'bg-green-800': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[10] && serviceTrack[10].name ? serviceTrack[10].name : 'service completed'),
                                                    'bg-red-600': column.field === 'status' && serviceTrack[Number(record[column.field])].name === (serviceTrack && serviceTrack[11] && serviceTrack[11].name ? serviceTrack[11].name : 'return to customer')
                                                }"
                                                    class="text-xs cursor-pointer px-1 py-1 ml-1 rounded text-white whitespace-nowrap">
                                                    {{ getStatusOption[Number(record[column.field])].name }}
                                                </span><br>
                                                <span
                                                    class="cursor-pointer hover:text-[#0000FF] text-[10px] py-1 text-green-700"
                                                    v-if="record['invoice_id']"
                                                    @mouseover="showModal({ invoice_id: record['invoice_id'], due_amount: record['due_amount'], grand_total: record['grand_total'], sale_id: record['sale_id'] }, $event)"
                                                    @mouseleave="hideModal">
                                                    #INV: {{ record['invoice_id'] }}</span>
                                            </template>
                                            <span
                                                v-if="!Array.isArray(record[column.field]) && typeof record[column.field]
                                                    !== 'object' && column.field !== 'notification' && column.field
                                                    !== 'notification' && column.field !== 'status' && column.field
                                                    !== 'invoice_id' && column.field !== 'created_at' && column.field !== 'updated_at' && column.field !== 'service_id' && column.field !== 'amc'">
                                                {{column.field === 'expected_date' ?
                                                    /^\d{4}-\d{2}-\d{2}$/.test(record[column.field].substring(0,
                                                        10)) &&
                                                        !isNaN(Date.parse(record[column.field].substring(0, 10))) ?
                                                        formattedDate(record[column.field], true, true) : '' :
                                                    column.field === " materials" ? typeof record[column.field] ===
                                                        'string'
                                                        ?
                                                        JSON.parse(record[column.field]).reduce((total, item) => total +
                                                            (item.qty *
                                                                item.price), 0)
                                                        : record[column.field] === 'object' &&
                                                        Array.isArray(record[column.field])
                                                        &&
                                                        record[column.field].reduce((total, item) => total + (item.qty *
                                                            item.price), 0)
                                                        :
                                                        record[column.field]}}</span>
                                            <span
                                                v-if="Array.isArray(record[column.field]) && record[column.field].length > 0 && column.field !== 'notification' && column.field !== 'status' && column.field !== 'invoice_id' && column.field !== 'amc'"
                                                v-for="(opt, i) in record[column.field]" :key="i">
                                                <!-- <template v-if="column.field === 'notification'">{{ 'hello' }}</template>-->
                                                <template
                                                    v-if="typeof opt === 'object' && opt !== null && column.field !== 'assign_to' && column.field !== 'created_by' && column.field !== 'updated_by' && column.field !== 'materials' && column.field !== 'amc'">
                                                    {{Object.keys(opt).map(key => `${key}:
                                                    ${opt[key]}`).join(', ')}}
                                                </template>
                                                <template
                                                    v-else-if="column.field === 'assign_to' && record[column.field].length > 0">
                                                    <span class="flex flex-wrap gap-1" :class="{ 'mt-1': i > 0 }">
                                                        <span
                                                            class="flex items-center px-1 py-0.5 rounded-full text-white text-xs "
                                                            :style="{ color: getUserColor(i), border: `1px solid ${getUserColor(i)}` }">
                                                            <font-awesome-icon icon="fa-solid fa-circle-user"
                                                                class="pr-1" />
                                                            <span class="text-nowrap">{{ opt.name }}</span>
                                                        </span>
                                                    </span>
                                                </template>
                                                <template
                                                    v-else-if="column.field === 'created_by' && record[column.field].length > 0">
                                                    {{ opt.name ? opt.name : '' }}<br>
                                                </template>
                                                <template
                                                    v-else-if="column.field === 'updated_by' && record[column.field].length > 0">
                                                    {{ opt.name ? opt.name : '' }}<br>
                                                </template>
                                                <!-- <template v-else>
                                        {{ opt + 'hello' }}
                                    </template> -->
                                            </span>
                                            <span class="cursor-pointer hover:text-[#0000FF]"
                                                v-if="!Array.isArray(record[column.field]) && typeof record[column.field] === 'object' && record[column.field] !== null && column.field !== 'notification' && column.field !== 'status' && column.field !== 'invoice_id' && column.field !== 'servicecategory' && column.field !== 'amc'"
                                                @mouseover="showModal(record[column.field], $event)"
                                                @mouseleave="hideModal">
                                                {{ record[column.field].first_name + ' ' +
                                                    record[column.field].last_name +
                                                    ' - ' + record[column.field].contact_number }}<br>
                                            </span>

                                            <span class="cursor-pointer hover:text-[#0000FF]"
                                                v-if="column.field === 'invoice_id'"
                                                @mouseover="showModal({ invoice_id: record['invoice_id'], due_amount: record['due_amount'], grand_total: record['grand_total'], sale_id: record['sale_id'] }, $event)"
                                                @mouseleave="hideModal">
                                                {{ record[column.field] }}</span>
                                            <!-- <span v-if="column.field === 'category'" class="text-violet-700">{{
                                                record['servicecategory'].service_category
                                                }}</span> -->
                                            <span v-if="column.field === 'category'"
                                                :class="getCategoryColor(record['servicecategory'].id)">
                                                {{ record['servicecategory'].service_category }}
                                            </span>
                                            <span v-if="column.field === 'service_type'">{{
                                                JSON.parse(record.service_data)
                                                    ?
                                                    JSON.parse(record.service_data)[column.field] : '' }}</span>
                                            <span v-if="column.field === 'problem_title'" class="text-red-600">{{
                                                JSON.parse(record.service_data) ?
                                                    Array.isArray(JSON.parse(record.service_data)[column.field]) ?
                                                        JSON.parse(record.service_data)[column.field].join(', ') :
                                                        JSON.parse(record.service_data)[column.field] : ''
                                            }}</span>
                                            <span v-if="column.field === 'created_at' || column.field === 'updated_at'"
                                                class="text-blue-800 text-xs whitespace-nowrap"
                                                :title="calculateDaysAgo(formattedDate(record[column.field]))">
                                                {{ formattedDate(record[column.field], true) }}
                                            </span>
                                            <span v-if="column.field === 'service_id'" class="hover:text-blue-700"
                                                @mouseover="showModal({ service_id: record['service_id'], service_code: record['service_code'] }, $event)"
                                                @mouseleave="hideModal">
                                                {{ record[column.field] }}
                                            </span>
                                            <span @mouseover="showModal(record[column.field], $event)"
                                                @mouseleave="hideModal" class="text-amber-600 hover:text-amber-700"
                                                v-if="column.field === 'amc' && record[column.field]">
                                                # {{ record[column.field] && record[column.field].id ?
                                                    record[column.field].id : '' }}
                                            </span>
                                            <!-- <span
                                    v-if="!Array.isArray(JSON.parse(record.service_data)[column.field]) && typeof JSON.parse(record.service_data)[column.field] !== 'object'">
                                    {{ JSON.parse(record.service_data)[column.field] }}</span>
                                <span
                                    v-if="Array.isArray(JSON.parse(record.service_data)[column.field]) && JSON.parse(record.service_data)[column.field].length > 0"
                                    v-for="(opt, i) in JSON.parse(record.service_data)[column.field]" :key="i">
                                    {{ typeof opt === 'object' && opt !== null ? Object.keys(opt).map(key => `${key}:
                                    ${opt[key]}`).join(', ') : opt }}<br>
                                </span>
                                <span
                                    v-if="!Array.isArray(JSON.parse(record.service_data)[column.field]) && typeof JSON.parse(record.service_data)[column.field] === 'object'">
                                    {{ Object.keys(JSON.parse(record.service_data)[column.field]).map(key => `${key}:
                                    ${JSON.parse(record.service_data)[column.field][key]}`).join(', ') }}<br></span> -->
                                        </p>
                                        <p v-else-if="record.service_data">
                                            <span>{{ formatData(JSON.parse(record.service_data)[column.field],
                                                column.field) }}</span>
                                        </p>
                                    </td>
                                    <!-- <td v-if="record.service_data"
                                class="border py-2 sm:text-[14px] text-xs text-left sm:text-center">
                                <span>{{ JSON.parse(record.service_data) ? JSON.parse(record.service_data).service_type
                                    : '' }}</span>
                            </td> -->
                                    <td class="py-2 sm:text-[14px] text-xs sm:text-center table-border">
                                        <div class="flex justify-center">
                                            <div class="flex relative">
                                                <div class="flex relative">
                                                    <button v-if="!record.editing" @click="viewRecord(record)"
                                                        class="px-1" title="View Record">
                                                        <font-awesome-icon icon=" fa-solid fa-eye"
                                                            style="color: #8b5cf6;" />
                                                    </button>
                                                    <button v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                        @click="editRecord(record)" class="px-1" title="Edit Record">
                                                        <font-awesome-icon icon="fa-solid fa-pencil"
                                                            style="color: #3b82f6;" />
                                                    </button>
                                                    <button v-if="!record.editing && checkRoles(['admin'])"
                                                        @click="confirmDelete(index)" title="Delete Record"
                                                        class="text-red-500 px-1 py-1 flex justify-center items-center">
                                                        <font-awesome-icon icon="fa-regular fa-trash-can"
                                                            style="color: #ef4444" />
                                                    </button>
                                                    <!--track-->
                                                    <button v-if="record.service_code && !getplanfeatures('service')"
                                                        class="px-1 text-red-600 hover:text-red-700"
                                                        title="Track Service">
                                                        <a :href="'https://tracking.track-new.com/' + record.service_code"
                                                            target="_blank">
                                                            <font-awesome-icon icon="fa-solid fa-location-dot" />
                                                        </a>
                                                    </button>
                                                </div>
                                                <!-- <button @click.stop="displayAction(index, $event)">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                        class="px-1 py-1 rounded"
                                                        :class="{ 'bg-blue-200': display_option === index }" />
                                                </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-7 absolute border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li>
                                                        <button v-if="!record.editing" @click="viewRecord(record)"
                                                            class="text-violet-500 px-1 py-1  w-full flex justify-center items-center hover:bg-gray-200">
                                                            <font-awesome-icon icon="fa-solid fa-eye"
                                                                style="color: #8b5cf6;" />
                                                            <span class="px-2">View</span>
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button
                                                            v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                            @click="editRecord(record)"
                                                            class="text-blue-500 px-1 py-1  w-full flex justify-center items-center hover:bg-gray-200">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li>
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-1 py-1 w-full flex justify-center items-center hover:bg-gray-200">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                                <tr v-if="!data || data.length === 0" class="items-center justify-center flex border">
                                    <button class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                        @click="addServices">+ Create
                                        Services</button>
                                </tr>
                            </tbody>
                        </table>
                        <!-- Hover Modal -->
                        <customerDataTable v-if="isModalVisible" :showModal="isModalVisible" :modalData="modalData"
                            :modalPosition="modalPosition" @mouseover="toggleHoverModal(true)"
                            @mouseleave="toggleHoverModal(false)" @mousemove="showModal(null, $event)">
                        </customerDataTable>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-4 gap-2 lg:gap-4 custom-scrollbar-hidden">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <!-- Card Container -->
                            <div
                                class="max-w-md mx-auto bg-white shadow-lg rounded-xl border border-gray-200  overflow-hidden md:max-w-2xl relative">
                                <div class="flex justify-between items-center px-4 py-2">
                                    <div>
                                        <p class="flex items-center cursor-pointer text-red-500 text-xs"
                                            :title="formattedDate(record['created_at'])">
                                            {{ calculateDaysAgo(formattedDate(record['created_at'])) }}
                                        </p>
                                    </div>
                                    <!--service code-->
                                    <div v-if="record['service_id']"
                                        class="text-xs items-center px-2 py-1 bg-gray-200 rounded-full">
                                        <!-- <h4 class="font-medium text-gray-400 mr-2">Service Code:</h4> -->
                                        <p class="text-gray-700"># {{ record['service_id'] }}</p>
                                    </div>
                                    <div class="flex justify-between items-center text-xs">
                                        <!--Status-->
                                        <div>
                                            <p v-if="record['status'] !== null && serviceTrack && Array.isArray(serviceTrack) && serviceTrack.length > 0"
                                                :class="{
                                                    'bg-blue-500': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[0] ? serviceTrack[0].name : 'service taken'),
                                                    'bg-red-500': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[1] ? serviceTrack[1].name : 'hold'),
                                                    'bg-yellow-500': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[2] ? serviceTrack[2].name : 'in-progress'),
                                                    'bg-violet-500': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[3] ? serviceTrack[3].name : 'new estimate'),
                                                    'bg-lime-500': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[4] ? serviceTrack[4].name : 'to be delivered'),
                                                    'bg-[#14532d]': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[5] ? serviceTrack[5].name : 'delivered'),
                                                    'bg-red-500': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[6] ? serviceTrack[6].name : 'cancelled'),
                                                    'bg-green-600': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[7] ? serviceTrack[7].name : 'completed'),
                                                    'bg-amber-700': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[8] ? serviceTrack[8].name : 'new service'),
                                                    'bg-indigo-500': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[9] ? serviceTrack[9].name : 'awaiting for payment'),
                                                    'bg-green-700': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[10] ? serviceTrack[10].name : 'service completed'),
                                                    'bg-red-600': serviceTrack[Number(record['status'])].name === (serviceTrack && serviceTrack[11] ? serviceTrack[11].name : 'return to customer'),
                                                }" class="text-center cursor-pointer px-2 py-1 rounded-full text-white"
                                                @click="viewRecord(record)">
                                                {{ getStatusOption[Number(record['status'])].name }}
                                            </p>
                                        </div>
                                        <!--menu-->
                                        <div class="flex justify-end relative">
                                            <button @click.stop="displayAction(index, $event)" class="pl-3">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg"
                                                    class="bg-blue-200 px-1 py-1 rounded" />
                                            </button>
                                            <!--dropdown-->
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-7 absolute border bg-white  divide-gray-100 rounded-lg border border-gray-400 shadow-inner shadow-gray-300 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing" @click="viewRecord(record)"
                                                            class="text-violet-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-eye"
                                                                style="color: #8b5cf6;" size="lg" />
                                                            <span class="px-2">View</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button
                                                            v-if="!record.editing && checkRoles(['Sub_Admin', 'admin'])"
                                                            @click="editRecord(record)"
                                                            class="text-blue-500 px-1 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-1  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" size="lg" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                    <li class="hover:bg-gray-200">
                                                        <button
                                                            v-if="record.service_code && !getplanfeatures('service')"
                                                            class="flex justify-center items-center text-red-600 hover:text-red-700">
                                                            <a :href="'https://tracking.track-new.com/' + record.service_code"
                                                                target="_blank"
                                                                class="p-1 flex justify-center items-center">
                                                                <font-awesome-icon icon="fa-solid fa-location-dot"
                                                                    size="lg" />
                                                                <span class="px-2">Track</span>

                                                            </a>
                                                        </button>
                                                    </li>
                                                </ul>s
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="md:flex">
                                    <div class="p-4 py-1">
                                        <div class="mt-1 grid grid-cols-3 gap-1">
                                            <!-- Customer Details -->
                                            <div class="col-span-2 items-center">
                                                <h4 class="text-lg leading-6 font-bold text-sky-900 cursor-pointer line-clamp-1 py-1"
                                                    @click="viewRecord(record)">
                                                    {{ JSON.parse(record.service_data) ?
                                                        Array.isArray(JSON.parse(record.service_data)['problem_title'])
                                                            ?
                                                            JSON.parse(record.service_data)['problem_title'].join(', ')
                                                            :
                                                            JSON.parse(record.service_data)['problem_title'] ?
                                                                JSON.parse(record.service_data)['problem_title'] :
                                                                record.servicecategory.service_category + ' ' + 'Service' :
                                                        record.servicecategory.service_category + ' ' + 'Service' }}
                                                </h4>
                                                <p class="text-sm text-sky-500 line-clamp-1"
                                                    @click="viewRecord(record)">{{
                                                        record.servicecategory.service_category }}
                                                </p>

                                            </div>
                                            <!--attachment-->
                                            <div>
                                                <div v-if="hasDocument(record)" @click="viewRecord(record)">
                                                    <img :src="getLastDocumentUrl(record)" alt="image"
                                                        class="image-container items-center" />
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-1 grid grid-cols-3 gap-1 mt-2">
                                            <!-- Service Details -->
                                            <div class="flex items-center col-span-2 border-l-4 px-1 ">
                                                <div class="flex-shrink-0 h-10 w-10 rounded-full flex items-center justify-center text-white font-bold"
                                                    :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-green-600': index % 2 === 0 && index > 1, 'bg-yellow-600': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-lime-800': index === 0 }">
                                                    {{ record.customer && record.customer.first_name ?
                                                        record.customer.first_name[0].toUpperCase() : 'C' }}
                                                </div>
                                                <div class="ml-2">
                                                    <div class="text-sm leading-6 font-semibold text-gray-900 line-clamp-2 cursor-pointer"
                                                        @click="viewRecord(record)">
                                                        {{
                                                            record.customer && record.customer.first_name ?
                                                                record.customer.first_name
                                                                + ' ' + (record.customer.last_name ?
                                                                    record.customer.last_name :
                                                                    '')
                                                                :
                                                                '' }}
                                                    </div>
                                                    <p class="text-sm text-gray-500 cursor-pointer"
                                                        @click="dialPhoneNumber(record['customer'].contact_number)">
                                                        +91
                                                        - {{
                                                            record.customer.contact_number }}</p>
                                                </div>
                                            </div>
                                            <div class="text-xs items-center px-1 border-l-4 cursor-pointer "
                                                @click="viewRecord(record)">
                                                <h4 class="font-medium text-gray-400 mr-2">Due Date:</h4>
                                                <p class="text-gray-700">{{
                                                    /^\d{4}-\d{2}-\d{2}$/.test(record['expected_date'].substring(0,
                                                        10))
                                                        &&
                                                        !isNaN(Date.parse(record['expected_date'].substring(0, 10)))
                                                        ?
                                                        formatDate(record['expected_date'].substring(0, 10)) : '' }}
                                                </p>
                                            </div>
                                        </div>
                                        <!--invoice no-->
                                        <div v-if="record['invoice_id']" class="grid grid-cols-3 gap-1">
                                            <div class="flex col-span-2 border-l-4 px-1 items-center">
                                                <h4 class="font-medium text-gray-400 mr-1">#Inv:</h4>
                                                <p class="text-sm  text-gray-700 cursor-pointer hover:text-blue-500"
                                                    @click="record['invoice_id'] ? printRecord(record) : ''">
                                                    {{ record['invoice_id'] ?
                                                        record['invoice_id']
                                                        : '- -' }}
                                                </p>
                                            </div>
                                            <div class="flex px-1 border-l-4 text-xs items-center"
                                                :class="{ 'text-green-700': !record['due_amount'], 'text-red-700': record['due_amount'] }">
                                                <p v-if="record['due_amount'] && record['invoice_id']"
                                                    class="mr-1 items-center">
                                                    {{ currentCompanyList && currentCompanyList.currency ===
                                                        'INR' ?
                                                        '\u20b9' :
                                                        currentCompanyList.currency }} {{
                                                        record['due_amount'] }}
                                                </p>
                                                <p v-if="!record['due_amount'] && record['invoice_id']"
                                                    class="mr-1 items-center">
                                                    {{ currentCompanyList && currentCompanyList.currency ===
                                                        'INR' ?
                                                        '\u20b9' :
                                                        currentCompanyList.currency }}
                                                    {{ record['grand_total'] }}
                                                </p>
                                                <button v-if="record['invoice_id']"
                                                    @click="record['invoice_id'] ? printRecord(record) : ''">
                                                    {{ record['due_amount'] ? 'Unpaid' : 'Paid' }}
                                                </button>
                                            </div>
                                        </div>
                                        <!-- Assigned To -->
                                        <div class="flex py-1 cursor-pointer" @click="viewRecord(record)">
                                            <div class="relative">
                                                <!-- First Circle -->
                                                <div class="absolute  left-1 z-0">
                                                    <i class="fas fa-user-circle text-blue-300 text-lg"></i>

                                                </div>
                                                <!-- Second Circle -->
                                                <div class="absolute  left-4 z-[2]">
                                                    <i class="fas fa-user-circle text-red-300 text-lg"></i>
                                                </div>
                                                <!-- Third Circle -->
                                                <div class="absolute  left-7 z-[3]">
                                                    <i class="fas fa-user-circle text-purple-300 text-lg"></i>
                                                </div>
                                            </div>
                                            <div class="flex flex-wrap gap-2 ml-[55px] mt-1 text-xs">
                                                <template
                                                    v-if="Array.isArray(record.assign_to) && record.assign_to.length > 0">
                                                    <template v-for="(opt, i) in record.assign_to.slice(0, 3)" :key="i">
                                                        <span class="inline-flex items-center p-1 rounded text-white"
                                                            :class="{
                                                                'bg-blue-600': i === 0 || i % 3 === 0,
                                                                'bg-cyan-600': i === 1,
                                                                'bg-green-600': i % 2 === 0,
                                                                'bg-purple-600': !(i === 0 || i % 3 === 0) && !(i % 2 === 0 && 1 > 1)
                                                            }">
                                                            {{ opt.name }}
                                                        </span>
                                                    </template>
                                                    <span v-if="record.assign_to.length > 3"
                                                        class="inline-flex items-center p-1 rounded bg-gray-500 text-white"
                                                        @mouseover="showAll = index" @mouseleave="showAll = null">
                                                        +{{ record.assign_to.length - 3 }} Others
                                                        <div v-if="showAll === index"
                                                            class="absolute bg-white border p-2 shadow-lg mb-10 ml-12 rounded-lg w-28 z-20 text-black right-0">
                                                            <p v-for="(opt, i) in record.assign_to.slice(3)" :key="i"
                                                                class="px-2 py-1 border-b last:border-b-0">
                                                                {{ opt.name }}
                                                            </p>
                                                        </div>
                                                    </span>
                                                </template>
                                                <p v-else class="inline-flex items-center px-3 py-1">- -</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!--no data found-->
                        <div v-if="pagination && pagination.last_page > 0 && pagination.last_page === pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>

                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="totalPages && !isMobile">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>
                        <div class="flex justify-end w-1/2">
                            <ul class="flex list-none overflow-auto">
                                <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                    :class="{ 'bg-gray-500': pagination.current_page === 1, 'bg-teal-600 hover:bg-teal-500': pagination.current_page !== 1 }">
                                    <button @click="updatePage(pagination.current_page - 1)"
                                        :disabled="pagination.current_page === 1"
                                        class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                        <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                        <span class="pl-1" v-if="!isMobile">Prev</span>
                                    </button>
                                </li>
                                <li v-for="pageNumber in visiblePageNumbers()" :key="pageNumber">
                                    <button @click="updatePage(pageNumber)"
                                        :class="{ 'bg-teal-600 text-white': pageNumber === pagination.current_page }"
                                        class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">{{
                                            pageNumber
                                        }}</button>
                                </li>

                                <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                    :class="{ 'bg-gray-500': pagination.current_page === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': pagination.current_page !== pagination.last_page }">
                                    <button @click="updatePage(pagination.current_page + 1)"
                                        :disabled="pagination.current_page === pagination.last_page"
                                        class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                                        <span class="pl-1" v-if="!isMobile">Next</span>
                                        <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                    </button>
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data</p>
            </div>
        </div>
        <!---in mobile view Filter Service-->
        <div v-if="isMobile" ref="dropdownContainerFilter">
            <div class="fixed bottom-64 right-5 z-50 bg-green-700 w-10 h-10 rounded-full">
                <div class="flex justify-center items-center w-full h-full">
                    <button @click="startScanner = true" class="flex items-center justify-center text-white">
                        <font-awesome-icon icon="fa-solid fa-expand" class="text-lg" />
                    </button>
                </div>
            </div>

            <!--advance search-->
            <div class="fixed bottom-52 right-5 z-50 bg-green-700 w-10 h-10 rounded-full">
                <div class="flex justify-center items-center w-full h-full">
                    <button @click="openSearch" type="button" class="flex items-center justify-center text-white"
                        :class="{ 'cursor-not-allowed blur-sm': data.length == 0 }" :disabled="data.length == 0">
                        <font-awesome-icon icon="fa-solid fa-magnifying-glass" class="text-lg" />
                    </button>
                </div>
            </div>
            <!--filter-->
            <div class="fixed bottom-36 right-5 z-50 bg-green-700 w-10 h-10 rounded-full">
                <div class="flex justify-center items-center w-full h-full">
                    <button @click="toggleMainDropdown" type="button"
                        class="flex items-center justify-center text-white"
                        :class="{ 'cursor-not-allowed blur-sm': data.length == 0 }" :disabled="data.length == 0">
                        <font-awesome-icon icon="fa-solid fa-filter" class="text-lg" />
                    </button>
                </div>
            </div>
            <!-- Main Dropdown -->
            <div v-if="isMainDropdownOpen" ref="mainDropdown"
                class="fixed bottom-48 sm:bottom-48  left-full transform -translate-x-full w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 border">
                <div class="py-1">
                    <!-- By Due Option with Sub-Dropdown -->
                    <div class="relative">
                        <button @click="toggleSubDropdown"
                            class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200">
                            By Due
                        </button>
                        <!-- Sub-Dropdown for By Due -->
                        <div v-if="showSubDropdown" @mouseenter="toggleMouserHoverSub"
                            @mouseleave="toggleMouserHoverSub" ref="subDropdown"
                            class="absolute right-full top-0 ml-2 w-40 rounded-md shadow-lg shadow-gray-400 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none border">
                            <ul class="py-1 text-left">
                                <li v-for="(option, index) in followupOptions" :key="index"
                                    class="cursor-pointer py-2 px-5 hover:bg-blue-200"
                                    :class="{ 'bg-blue-200 text-blue-700': followup_select === option.value }"
                                    @click="handleFollowup(option.value, 1)">
                                    {{ option.label }}
                                </li>
                            </ul>
                        </div>
                    </div>
                    <!-- Other Options -->
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('by Date')">By Date</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('customer')">By Customer</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('employee')">By Assigned to</button>
                    <button class="block px-4 py-2 text-sm w-full text-left hover:bg-blue-200"
                        @click.stop="changeFilter('serial_number')">By Serial Number</button>
                </div>
            </div>
        </div>
        <!---in mobile view create new lead-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="addServices" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <!-- <MyComponent :chart="myDynamicValue" /> -->
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'services'"></bottombar> -->
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
        <dialogAlert :show-modal="open_message" :message="message" @close="closeMessage"></dialogAlert>
        <!--filter-->
        <serviceFilter :showModal="lead_filter" @closeFilter="closeLeadFilter" :typeList="typeList"
            :statusList="statusList" :selectedByValue="selectedByValue" :category="category_type"></serviceFilter>
        <Loader :showModal="open_loader"></Loader>
        <serviceCategory :showModal="open_service_category" @close-modal="closeCategory" @updateMode="updateStatus">
        </serviceCategory>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <serviceAdvanceSearch :showModal="isSearch" @close="closesearch" :isMobile="isMobile" :empty_data="empty_data">
        </serviceAdvanceSearch>
        <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
        <servicecodemanage v-if="startScanner" :isModalOpen="startScanner" :type="'scan'" @close="closeQrcode">
        </servicecodemanage>
    </div>
</template>

<script>
import confirmbox from '../../dialog_box/confirmbox.vue';
import serviceFilter from '../../dialog_box/filter_Modal/serviceFilter.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import serviceCategory from '../../dialog_box/serviceCategory.vue';
import { mapState, mapActions, mapGetters } from 'vuex';
// import bottombar from '../../dashboard/bottombar.vue';
import customerDataTable from '../../dialog_box/customerDataTable.vue';
import searchCustomer from '../../customers/searchCustomer.vue';
import serviceAdvanceSearch from '../../dialog_box/serviceAdvanceSearch.vue';
// import serviceSearch from '../Search/serviceSearch.vue';
import noAccessModel from '../../dialog_box/noAccessModel.vue';
import servicecodemanage from '../Search/servicecodemanage.vue';

export default {
    name: 'services_home',
    emits: ['filter_update', 'paginationGetData', 'updateIsOpen'],
    components: {
        confirmbox,
        serviceFilter,
        dialogAlert,
        serviceCategory,
        // bottombar,
        customerDataTable,
        searchCustomer,
        serviceAdvanceSearch,
        // serviceSearch,
        noAccessModel,
        servicecodemanage
    },
    props: {
        isMobile: Boolean,
        searchedData: Object,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            length_category: null,
            serviceCategories: null,
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            filteredDataList: [],
            columns: [],
            data: [],
            pagination: {},
            originalData: this.data,
            //--filter
            statusIcons: ['/images/service_page/order_taken.png', '/images/service_page/hold.png', '/images/service_page/work-in-progress.png', '/images/service_page/estimate.png', '/images/service_page/delivery.png', '/images/service_page/delivered.png', '/images/service_page/cancelled.png', '/images/service_page/checked.png', '/images/service_page/new_service.png', '/images/service_page/awaiting_payment.png', '/images/service_page/service_complete.png', '/images/service_page/retrun_customer.png'],
            showFilterOptions: false,
            filterOptions: ['by Date', 'by Customer', 'by Employee', 'Custom'], //---, 'by category/Type', 'by Status',
            selectedByValue: null,
            lead_filter: false,
            typeList: [],
            statusList: [],
            //--dialog alert
            open_message: false,
            message: '',
            //--filter list--
            mouseOverIsNot: false,
            filteredBy: {},
            serviceTrack: [{ name: 'service taken' }, { name: 'hold' }, { name: 'in-progress' }, { name: 'new estimate' }, { name: 'ready to deliver' }, { name: 'delivered' }, { name: 'cancelled' }, { name: 'completed' }, { name: 'new service' }, { name: 'awaiting for payment' }, { name: 'service completed' }, { name: 'return to customer' }],
            labelsName: {},
            fieldKey: {},
            //--skeleton
            open_skeleton: true,
            open_skeleton_isMobile: false,
            number_of_columns: 10,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            items_category: 'tile',
            getStatusOption: [{ name: 'Service Taken', total: 0 }, { name: 'Hold', total: 0 }, { name: 'In-progress', total: 0 }, { name: 'New Estimate', total: 0 }, { name: 'Ready To Deliver', total: 0 }, { name: 'Delivered', total: 0 }, { name: 'Cancelled', total: 0 }, { name: 'Completed', total: 0 }, { name: 'New Service', total: 0 }, { name: 'Awaiting For Payment', total: 0 }, { name: 'Service Completed', total: 0 }, { name: 'Return To Customer', total: 0 }],
            status_select: 'pending',
            followup_select: 'all',
            category_type: 'all',
            followupOptions: [
                { label: 'All', value: 'all' },
                { label: 'Today', value: 'today' },
                { label: 'Tomorrow', value: 'tomorrow' },
                { label: 'Week', value: 'this_week' },
                { label: 'Month', value: 'this_month' },
                { label: 'Year', value: 'this_year' },
            ],
            all_count: 0,
            all_count_status: 0,
            open_service_category: false,
            category_list: [],
            companyId: null,
            userId: null,
            searchByCustomer: {},
            display_option: false,
            now: null,
            timer: null,
            filter_option: null,
            filter_date: false,
            pending_status_data: { type: 'pending', total: 0 },
            //--toaster---
            show: false,
            type_toaster: 'warning',
            //---modal---mouse over---
            isModalVisible: false,
            modalData: {},
            modalPosition: { x: 0, y: 0 },
            actionPosition: { x: 0, y: 0 },
            isHoveringModal: false,
            hoverTimer: null,
            lastMousePosition: { x: 0, y: 0 },
            tolerance: 50, // Tolerance for mouse movement in pixels
            //---table data filter asending and decending----
            is_filter: false,
            //----filter options are-----
            isMainDropdownOpen: false,
            showSubDropdown: false,
            is_openSub: false,
            is_phone: false,
            //---service navigation----
            serviceIsGo: false,
            resetSearchData: false,
            //--search variables--
            isSearch: false,
            //----"service_data", ---
            special_keys: ["created_at", "id", "status", "updated_at", "servicecategory", "service_data", "service_track", "customer_id", "pre_repair", "notification", "id", "customer", "customer_id", "assignWork"],
            sub_keys: ["id", "pivot", "user_id", "service_id"],
            //---employess--
            showAll: null,
            is_started: false,
            //--color category--
            categoryColors: [], // Store category colors
            usedColors: new Set(),
            //---no access---
            no_access: false,
            //---QR code scan---
            startScanner: false,
            //--selected category---
            selected_category: false,
        };
    },
    created() {
        this.timer = setInterval(() => {
            this.now = new Date();
        }, 1000);
    },
    computed: {
        ...mapState('service', ['search_query']),
        ...mapState('service', ['search_data']),
        ...mapGetters('serviceCategories', ['currentServiceCategory']),
        ...mapGetters('serviceList', ['currentServiceList']),
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('sortIcons', ['isShortVisible', 'getSortTypeDisplay', 'isHighlighted']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        paginatedData() {
            // console.log(this.labelsName, 'IIIIIIIIIII', this.data);
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                // Filter records based on category_type and category_id
                // const filteredData = this.data.filter(record => {
                //     return (
                //         (record.category_type === this.category_type) &&
                //         (record.category_id === Number(this.category_id))
                //     );
                // });
                this.length_category = filteredData.length;
                return this.data;
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                // const totalFilteredRecords = this.data.filter(record => {
                //     return (
                //         (record.category_type === this.category_type) &&
                //         (record.category_id === Number(this.category_id))
                //     );
                // }).length;

                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];
            //--- 'problem_title',
            const order = ['created_at', 'updated_at', 'service_id', 'problem_title', 'customer', 'category', 'expected_date', 'assign_to', 'service_type', 'invoice_id', 'status', 'created_by', 'updated_by', 'amc']

            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                if (!key == 'problem_title') {
                    const words = key.split(/(?=[A-Z])|_/);
                    const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                    return formattedLabel;
                } else {
                    return validateExistLable(key);
                }
            };
            // Validate if the field exists in the form based on the category
            const validateExist = (key) => {
                if (this.category_type && this.category_type !== 'all' && this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                    const category = this.currentServiceCategory.find(opt => opt.id == this.category_type);
                    if (category && category.form) {
                        try {
                            const formData = JSON.parse(category.form);
                            return formData.some(opt => opt.fieldKey === key && opt.enable);
                        } catch (e) {
                            console.error('Parse error:', e);
                        }
                    }
                }
                return true;
            };
            // Validate and return label name based on category form data
            const validateExistLable = (key) => {
                if (this.category_type && this.category_type !== 'all' && this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                    const category = this.currentServiceCategory.find(opt => opt.id == this.category_type);
                    if (category && category.form) {
                        try {
                            const formData = JSON.parse(category.form);
                            // Look for the label name from the form if it exists
                            const field = formData.find(opt => opt.fieldKey === key);
                            return field ? field.lableName : capitalizeFirstLetter(key); // Use the label from form if available, else fall back to default format
                        } catch (e) {
                            console.error('Parse error:', e);
                        }
                    }
                }
                return capitalizeFirstLetter(key);  // Return the default formatted label if not found
            };

            // Iterate over the first record in data to get field names
            if (this.data && this.data.length > 0) {
                if (!validateExist('problem_title')) order.splice(1, 1);
                // console.log(JSON.parse(this.data[0].service_data), 'Test 0001');
                // for (const key in this.data[0]) {
                //     if (key !== 'id' && key !== 'comments' && key !== 'service_data' && key !== 'document' && key !== 'created_at' && key !== 'deleted_at' && key !== 'updated_at' && key !== 'servicecategory' && key !== 'service_track' && key !== 'sale_id' && key !== 'estimate_amount' && key !== 'advance_amount' && key !== 'service_amount' && key !== 'materials' && key !== 'notification' && key !== 'invoice_id') { // Exclude the 'id' field
                //         const label = formatLabel(key);
                //         fields.push({ label, field: key, visible: true });
                //     }
                // }
                //----new--
                // Iterate over the order array to ensure fields are generated in the specified order
                order.forEach((key) => {
                    // Check if the key exists in the data object
                    if (this.data && this.data.length > 0) { //---&& this.data[0].hasOwnProperty(key)
                        const label = formatLabel(key);
                        if (key !== 'created_by' && key !== 'updated_by' && key !== 'invoice_id' && key !== 'amc') {
                            fields.push({ label, field: key, visible: true, category: false });
                        } else {
                            fields.push({ label, field: key, visible: false, category: false });
                        }
                    }
                });
                // Handle dynamic service data based on category
                if (this.category_type && this.category_type !== 'all' && this.data[0].service_data) {
                    const serviceData = JSON.parse(this.data[0].service_data);
                    if (serviceData) {
                        const filterKeys = Object.keys(serviceData).filter(key => !(this.special_keys.includes(key) || order.includes(key)));
                        filterKeys.forEach((key) => {
                            if (validateExist(key)) {
                                const label = validateExistLable(key);
                                fields.push({ label, field: key, visible: false, category: true });
                            }
                        });
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },

    },
    methods: {
        ...mapActions('serviceCategories', ['fetchServiceCategoryList']),
        ...mapActions('serviceList', ['fetchServiceList']),
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('sortIcons', ['showSortIcon', 'hideSortIcon', 'updateSortType']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        dialPhoneNumber(phoneNumber) {
            // Check if the device supports the tel: URI scheme
            if ('href' in HTMLAnchorElement.prototype) {
                // Create a hidden anchor element with the tel: URI
                const anchor = document.createElement('a');
                anchor.setAttribute('href', 'tel:' + phoneNumber);
                anchor.style.display = 'none';

                // Append the anchor to the body and trigger a click
                document.body.appendChild(anchor);
                anchor.click();

                // Clean up by removing the anchor from the DOM
                document.body.removeChild(anchor);
            } else {
                // Fallback if the device doesn't support the tel: URI scheme
                console.log('Device does not support tel: URI scheme');
                // You can add a fallback behavior here, like displaying an error message
            }
        },
        //--image
        hasDocument(record) {
            return (
                record.document &&
                Array.isArray(JSON.parse(record.document)) &&
                JSON.parse(record.document).length > 0
            );
        },
        getLastDocumentUrl(record) {
            if (this.hasDocument(record)) {
                const documents = JSON.parse(record.document);
                return documents[documents.length - 1].url;
            }
            return null;
        },
        //--format date    
        formatDate(dateString) {
            if (!dateString || typeof dateString !== 'string') {
                return '';
            }
            // Parse the input date string as a Date object
            const dateObject = new Date(dateString);

            if (isNaN(dateObject.getTime())) {
                return ''; // Return empty string if parsing fails
            }

            // Define an array of month names
            const monthNames = ["January", "February", "March", "April", "May", "June",
                "July", "August", "September", "October", "November", "December"];

            // Extract day, month, and year components from the date object
            const day = dateObject.getDate();
            const monthIndex = dateObject.getMonth(); // Months are zero-based (0 = January)
            const year = dateObject.getFullYear();

            // Format the date as "Month day, year"
            const formattedDateData = `${monthNames[monthIndex]} ${day}, ${year}`;

            return formattedDateData;
        },

        //--filter option--
        changeFilter(opt) {
            this.filter_option = opt;
            this.toggleMainDropdown();
            if (opt === 'date') {
                this.filter_date = !this.filter_date;
                if (!this.filter_date) {
                    // Remove event listener to avoid memory leaks
                    document.removeEventListener('click', this.handleClickOutsideDate);
                } else {
                    document.addEventListener('click', this.handleClickOutsideDate);
                }
            }
            if (opt === 'customer') {
                this.toggleFilterSelected('by Customer');
            }
            if (opt === 'employee') {
                this.toggleFilterSelected('by Employee');
            }
            if (opt === 'by Date') {
                this.toggleFilterSelected('by Date');
            }
            if (opt === 'serial_number') {
                this.toggleFilterSelected('by Serial');
            }
        },
        handleClickOutsideDate(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs.componentContainer;
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.filter_date = !this.filter_date;
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutsideDate);
            }
        },
        //---display action
        displayAction(index, event) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                this.actionPosition = { x: (event.clientX * 1) - 250, y: (event.clientY * 1) + 100 };
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        //---reset filter--
        resetTheFilter() {
            this.filteredBy = {};
            this.status_select = 12;
            this.searchByCustomer = {};
            this.followup_select = 'all';
            this.resetSearchData = true;
            this.resetSearch('');
            this.currentPage = 1;
            // console.log(this.category_type, 'Category type data..........!');
            this.serviceDataList(this.category_type, 1, this.recordsPerPage);
        },
        ...mapActions('service', ['updateSearchQuery']),
        ...mapActions('service', ['updateSearchData']),
        resetSearch(newValue) {
            const newQuery = '';
            this.updateSearchQuery(newQuery);
            const newData = {};
            this.updateSearchData(newData);
        },
        //--expected completion
        handleFollowup(optionValue, page) {
            this.toggleMainDropdown();
            this.followup_select = optionValue;
            this.filter_date = false;
            document.removeEventListener('click', this.handleClickOutsideDate);
            if (optionValue === 'all') {
                this.resetSearch('');
                this.searchByCustomer = {};
            }
            if (this.status_select === null) {
                this.status_select = 12;
            }
            this.open_skeleton = true;
            let send_data = { type: 'services', q: this.status_select !== 12 ? this.status_select : '', filter: optionValue !== 'all' ? optionValue : '', per_page: this.recordsPerPage, page: page, customer_id: Object.keys(this.searchByCustomer).length > 0 && this.searchByCustomer.id ? this.searchByCustomer.id : '' };
            if (this.category_type !== null && this.category_type !== 'all') {
                send_data.category = this.category_type;
            }
            if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
                // console.log(this.filteredBy, 'helllo');
                if (this.filteredBy.customer_id) {
                    send_data.customer_id = this.filteredBy.customer_id
                }
                if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
                    send_data.employer_id = this.filteredBy.assign_to[0].id;
                }
            }
            // Handle followup option selection (e.g., perform an action based on the selected value)
            // console.log('Selected followup option:', optionValue);
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Followup Data');
                    this.open_skeleton = false;
                    this.pagination = response.data.pagination;
                    this.data = response.data.data;
                    this.currentPage = page ? page : this.currentPage;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        //--status--
        getStatusLabel(data, page) {
            if (this.followup_select !== null) {
                this.followup_select = 'all';
            }
            // if (this.filteredBy) {
            //     this.filteredBy = {};
            // }
            this.status_select = data;
            if (data == 12) {
                this.resetSearch('');
                this.searchByCustomer = {};
            }
            this.open_skeleton = true;
            let send_data = { type: 'services', q: data !== 12 || data === 'pending' ? data : '', filter: this.followup_select >= 0 && data !== 12 ? this.followup_select : '', per_page: this.recordsPerPage, page: page, customer_id: Object.keys(this.searchByCustomer).length > 0 && this.searchByCustomer.id ? this.searchByCustomer.id : '' };
            if (this.category_type !== null && this.category_type !== 'all') {
                send_data.category = this.category_type;
            }
            if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
                if (this.filteredBy.customer_id) {
                    send_data.customer_id = this.filteredBy.customer_id
                }
                if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
                    send_data.employer_id = this.filteredBy.assign_to[0].id;
                }
            }
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    this.open_skeleton = false;
                    this.pagination = response.data.pagination;
                    this.data = response.data.data;
                    this.currentPage = page ? page : this.currentPage;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_skeleton = false;
                })
        },
        //---selected the category
        selectedCategory(option_data) {
            if (option_data && option_data.id) {
                this.category_type = option_data.id;
            } else if (this.category_type == null || this.category_type == undefined) {
                this.category_type = 'all';
            }
            if (this.followup_select === null || this.followup_select === undefined) {
                //  && this.followup_select !== 'all'
                this.followup_select = 'all';
            }
            if (this.status_select === null || this.status_select === undefined) {
                //  && this.status_select !== 8
                this.status_select = 12;
            }
            if (this.searchByCustomer) {
                this.resetSearch('');
            }
        },
        //---close category--
        closeCategory(data) {
            // console.log(data);
            this.open_service_category = false;
        },
        updatePage(pageNumber) {
            // console.log('jjjjj', pageNumber, this.pagination.last_page, 'uuuu', pageNumber >= 1 && pageNumber <= this.pagination.last_page);
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
                this.$emit('paginationGetData', pageNumber, this.recordsPerPage);
            }
        },
        editRecord(record) {
            if (this.getplanfeatures('service')) {
                this.no_access = true;
            } else {
                if (!record.invoice_id) {
                    this.$router.push({ name: 'service-category-view', params: { viewId: record.id, type: record.servicecategory.service_category, id: record.servicecategory.id } });
                } else {
                    this.message = 'This service has already been invoiced, so we cannot edit it';
                    this.type_toaster = 'warning';
                    // this.open_message = true;
                    this.show = true;
                }
            }
        },
        viewRecord(record) {
            if (this.getplanfeatures('service')) {
                this.no_access = true;
            } else {
                // this.$emit('showAddServiceComponent', record);
                if (!this.isModalVisible) {
                    this.$router.push({ name: 'service-data-view', params: { serviceId: record.id, type: record.servicecategory.service_category, id: record.servicecategory.id } });
                }
            }
        },
        deleteRecord() {
            if (this.deleteIndex !== null) {
                this.open_loader = true;
                axios.delete(`/services/${this.data[this.deleteIndex].id}`, {
                    params: {
                        company_id: this.companyId
                    }
                })
                    .then(response => {
                        // console.log(this.data[this.deleteIndex], 'EEEEE');
                        this.message = `Service has been deleted successfully...!`;
                        this.type_toaster = 'success';
                        this.show = true;
                        this.data.splice(this.deleteIndex, 1);
                        this.deleteIndex = null;
                        this.open_loader = false;
                        this.updateKeyWithTime('service_update');
                        // this.$emit('paginationGetData', this.pagination.current_page, this.recordsPerPage);
                        this.open_skeleton = true;
                        if (this.status_select > 11) {
                            this.serviceDataList(this.category_id, this.currentPage, this.recordsPerPage);
                        } else {
                            this.fetchServiceList({ id: this.category_type, page: 1, per_page: this.recordsPerPage });
                            this.getStatusLabel(this.status_select, this.currentPage);
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })

            }
            this.open_confirmBox = false;
        },
        confirmDelete(index) {
            if (this.getplanfeatures('service')) {
                this.no_access = true;
            } else {
                if (this.data[index] && this.data[index].invoice_id && this.data[index].invoice_id !== '') {
                    this.message = `This service cannot be deleted because it is linked to an existing invoice (Invoice ID: ${this.data[index].invoice_id}). Please delete the associated sales record before attempting to delete this service.`;
                    this.type_toaster = 'warning';
                    this.show = true;
                } else {
                    this.deleteIndex = index;
                    this.open_confirmBox = true;
                }
            }
        },
        cancelDelete() {
            this.open_confirmBox = false;
            this.open_loader = false;
        },
        addServices() {
            this.open_service_category = true;
            // this.$router.push({ name: 'service-category-add' });
        },
        goBackToHomePage() {
            this.$router.push('/services');
        },
        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            if (this.items_category === 'tile' || this.isDropdownOpen) {
                this.isDropdownOpen = !this.isDropdownOpen;
                if (this.isDropdownOpen) {
                    // Add event listener when dropdown is opened
                    document.addEventListener('click', this.handleOutsideClick);
                } else {
                    // Remove event listener when dropdown is closed
                    document.removeEventListener('click', this.handleOutsideClick);
                }
            } else {
                this.message = 'This option only for table view..!';
                this.type_toaster = 'warning';
                this.show = true;
            }
        },
        handleOutsideClick(event) {
            try {
                // console.log(event, 'What happening.....');
                const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
                if (!isClickInside) {
                    this.toggleDropdown();
                }
            } catch (error) {
                this.isDropdownOpen = false;
            }
        },
        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },
        //---filter
        openCustomerModal() {
            // Implement logic to open customer modal
        },
        toggleFilterDropdown() {
            this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
        },
        // closeFilterModal() {
        //     this.showFilterModal = false;
        //     this.resetFilter();
        // },
        resetFilter() {
            // Reset filter values
            this.columns.forEach((column) => {
                column.filterValue = '';
            });
            this.filteredData = [...this.originalData];
        },
        applyFilter() {
            // Implement logic to filter data based on filter values
            // For simplicity, this example applies a basic filter
            this.filteredData = this.originalData.filter((record) => {
                return this.columns.every((column) => {
                    return record[column.field].includes(column.filterValue);
                });
            });
            this.showFilterModal = false;
        },
        refreshPage() {
            window.location.reload();
        },
        //----print prechecklist
        formattedString(listData) {
            let returnData = Object.entries(listData)
                .map(([key, value]) => `${key}: ${value}`)
                .join(', ');
            return returnData;
        },
        //---print all arrat data
        formattedData(data) {
            if (Array.isArray(data)) {
                return data.map(item => {
                    if (typeof item === 'object' && item !== null) {
                        return Object.keys(item).map(key => `${key}: ${item[key]}`).join(', ');
                    } else {
                        return item.toString();
                    }
                });
            } else {
                return data.toString();
            }
        },
        //------Open filter---
        //---Filter---
        toggleFilter() {
            // console.log('hello');
            this.showFilterOptions = !this.showFilterOptions;
            // this.typeList = this.category_data.form.find((opt) => opt.fieldKey === 'service_type').option;
            // this.statusList = this.category_data.form.find((opt) => opt.fieldKey === 'status').option;
            // this.lead_filter = true;

            // this.filteredDataList = this.data;
            // console.log(this.filteredDataList, 'EEERRRASR');
        },
        //---filter---
        toggleFilterSelected(option) {
            // console.log(option, 'GGGGGGGGGGGGGGG');
            this.selectedByValue = option;
            this.lead_filter = true;
            this.$emit('filter_update', 'reset');
            this.showFilterOptions = false;
            // console.log(this.lead_filter, 'EEERRRASR');
        },
        //--close filter
        closeLeadFilter(page, searchData, bySerial, searchdata, status_select) {
            // console.log(searchData, 'What is happening.....!', page);
            if (searchData) {
                const keysData = Object.keys(searchData);
                this.filteredBy = searchData;
                this.searchByCustomer = {};
                this.status_select = status_select >= 0 && status_select !== 12 ? status_select : 'pending';
                // this.followup_select = 'all';
                this.open_loader = true;
                let send_data = { type: 'services', q: this.status_select >= 0 && this.status_select !== 12 ? this.status_select : this.status_select === 'pending' ? 'pending' : '', filter: '', per_page: this.recordsPerPage, page: page, customer_id: searchData.customer_id ? searchData.customer_id : '', employer_id: searchData.assign_to && searchData.assign_to.length > 0 ? searchData.assign_to[0].id : '', from_date: searchData.from ? searchData.from : '', to_date: searchData.to ? searchData.to : '' };
                if (this.status_select !== null && this.status_select !== 12) {
                    send_data.q = this.status_select;
                }
                if (this.category_type !== null && this.category_type !== 'all') {
                    send_data.category_id = this.category_type;
                }
                axios.get('/searchs', { params: { ...send_data } })
                    .then(response => {
                        // console.log(response.data, 'Status Data');
                        this.open_loader = false;
                        this.data = response.data.data;
                        this.pagination = response.data.pagination;
                        this.selectedCategory();
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            } else if (bySerial && !Array.isArray(bySerial) && Object.keys(bySerial).length > 0) {
                this.data = [{ ...bySerial }];
                this.filteredBy = { serial_no: JSON.parse(bySerial.service_data).serial_number ? JSON.parse(bySerial.service_data).serial_number : '' };
            } else if (bySerial && Array.isArray(bySerial)) {
                this.data = [...bySerial];
                this.filteredBy = { serial_no: searchdata ? searchdata : '' };
            }

            this.lead_filter = false;
        },
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //--hidden dropdown--
        hiddenDropdown() {
            if (!this.mouseOverIsNot) {
                this.showFilterOptions = false;
            }
        },
        mouseOverOption() {
            this.mouseOverIsNot = true;
        },
        mouseLeaveOption() {
            this.mouseOverIsNot = false;
        },
        //--change page--
        changePage() {
            // console.log(this.pagination.current_page, 'ppppp', this.recordsPerPage);
            this.$emit('paginationGetData', 1, this.recordsPerPage);
        },
        //---handle the data---
        parseNotification(notification) {
            try {
                // Attempt to parse as JSON
                const parsedNotification = JSON.parse(notification.replace(/\\"/g, '"'));
                if (Array.isArray(parsedNotification)) {
                    // Remove square brackets from each item in the array, if present
                    return parsedNotification.map(item => typeof item === 'string' ? item.replace(/^\[|\]$/g, '') : item);
                } else {
                    // Wrap non-array value in an array and remove square brackets if present
                    return [parsedNotification.replace(/^\[|\]$/g, '')];
                }
            } catch (error) {
                // Parsing as JSON failed, handle the case where notification is a string
                return notification.split(',').map(item => item.trim());
            }
        },

        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available
            // console.log(Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index), 'RRRRR');
            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        //---print record---
        printRecord(record) {
            if (record.sale_id) {
                this.$router.push({ name: 'print-preview', query: { type: 'sales_home', invoice_no: record.sale_id } });
            }
        },
        viewRecordCustomer(data) {
            this.$router.push({ name: 'customers-view', params: { id: data.id } });
        },
        //---service category---
        async serviceDataList(id, page, per_page) {
            if (page == 1) {
                await this.fetchServiceList({ id, page, per_page });
                if (this.currentServiceList && this.currentServiceList.list) {
                    if (this.currentServiceList.list && this.currentServiceList.list.length > 0) {
                        let find_data = this.currentServiceList.list.find(opt => opt.category == id);
                        if (find_data) {
                            this.data = find_data.data;
                            this.pagination = find_data.pagination;
                        }
                    }
                }
            } else {
                this.open_loader = true;
                let send_data = { company_id: this.companyId, page: page, per_page: per_page };
                const numberPattern = /^[0-9]+$/;
                if (id !== 'all' && numberPattern.test(id)) {
                    send_data.category_id = id;
                }
                this.open_skeleton = true;
                axios.get(`/services`, { params: { ...send_data } })
                    .then(response => {
                        // console.log(response.data, 'service gatgory get..!');
                        this.open_skeleton = false;
                        this.pagination = response.data.pagination;
                        this.data = response.data.data;
                        this.open_loader = false;
                        if (response.data.status_counts && (!Array.isArray(response.data.status_counts) || response.data.data.length === 0)) {
                            if (response.data.status_counts[0] >= 0) {
                                this.getStatusOption[0].total = response.data.status_counts[0];
                            } else {
                                this.getStatusOption[0].total = 0;
                            }
                            if (response.data.status_counts[1] >= 0) {
                                this.getStatusOption[1].total = response.data.status_counts[1];
                            } else {
                                this.getStatusOption[1].total = 0;
                            }
                            if (response.data.status_counts[2] >= 0) {
                                this.getStatusOption[2].total = response.data.status_counts[2];
                            } else {
                                this.getStatusOption[2].total = 0;
                            }
                            if (response.data.status_counts[3] >= 0) {
                                this.getStatusOption[3].total = response.data.status_counts[3];
                            } else {
                                this.getStatusOption[3].total = 0;
                            }
                            if (response.data.status_counts[4] >= 0) {
                                this.getStatusOption[4].total = response.data.status_counts[4];
                            } else {
                                this.getStatusOption[4].total = 0;
                            }
                            if (response.data.status_counts[5] >= 0) {
                                this.getStatusOption[5].total = response.data.status_counts[5];
                            } else {
                                this.getStatusOption[5].total = 0;
                            }
                            if (response.data.status_counts[6] >= 0) {
                                this.getStatusOption[6].total = response.data.status_counts[6];
                            } else {
                                this.getStatusOption[6].total = 0;
                            }
                            if (response.data.status_counts[7] >= 0) {
                                this.getStatusOption[7].total = response.data.status_counts[7];
                            } else {
                                this.getStatusOption[7].total = 0;
                            }
                            if (response.data.status_counts[8] >= 0) {
                                this.getStatusOption[8].total = response.data.status_counts[8];
                            } else {
                                this.getStatusOption[8].total = 0;
                            }
                            if (response.data.status_counts[9] >= 0) {
                                this.getStatusOption[9].total = response.data.status_counts[9];
                            } else {
                                this.getStatusOption[9].total = 0;
                            }
                            if (response.data.status_counts[10] >= 0) {
                                this.getStatusOption[10].total = response.data.status_counts[10];
                            } else {
                                this.getStatusOption[10].total = 0;
                            }
                            if (response.data.status_counts[11] >= 0) {
                                this.getStatusOption[11].total = response.data.status_counts[11];
                            } else {
                                this.getStatusOption[11].total = 0;
                            }
                            if (response.data.pagination && response.data.pagination.total >= 0) {
                                if (id === 'all') {
                                    this.all_count = response.data.pagination.total;
                                }
                                this.all_count_status = response.data.pagination.total;
                                if (this.category_list && Array.isArray(this.category_list) && this.category_list.length > 0 && id === 'all') {
                                    this.category_list[0].services_count = response.data.pagination.total;
                                }
                            } else {
                                this.all_count = 0;
                                if (this.category_list && Array.isArray(this.category_list) && this.category_list.length > 0) {
                                    this.category_list[0].services_count = 0;
                                }
                            }
                            if (response.data.status_counts[0] >= 0 || response.data.status_counts[1] >= 0 || response.data.status_counts[2] >= 0 || response.data.status_counts[3] >= 0 || response.data.status_counts[4] >= 0) {
                                this.pending_status_data.total = ((1 * response.data.status_counts[0] >= 0 ? response.data.status_counts[0] : 0) + (1 * response.data.status_counts[1] >= 0 ? response.data.status_counts[1] : 0) + (1 * response.data.status_counts[2] >= 0 ? response.data.status_counts[2] : 0) + (1 * response.data.status_counts[3] >= 0 ? response.data.status_counts[3] : 0) + (1 * response.data.status_counts[4] >= 0 ? response.data.status_counts[4] : 0));
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        this.open_skeleton = false;
                        this.open_loader = false;
                    })
            }
        },
        //----initialize the data---
        serviceDataListInit(service_data, status_counts) {
            if (!this.is_started) {
                this.data = service_data.data;
                this.pagination = service_data.pagination;
            } else {
                this.is_started = false;
            }

            // this.category_list[0].services_count = response.data.pagination.total;
            if (status_counts && (!Array.isArray(status_counts))) {
                if (status_counts[0] && status_counts[0] >= 0) {
                    this.getStatusOption[0].total = status_counts[0];
                } else {
                    this.getStatusOption[0].total = 0;
                }
                if (status_counts[1] && status_counts[1] >= 0) {
                    this.getStatusOption[1].total = status_counts[1];
                } else {
                    this.getStatusOption[1].total = 0;
                }
                if (status_counts[2] && status_counts[2] >= 0) {
                    this.getStatusOption[2].total = status_counts[2];
                } else {
                    this.getStatusOption[2].total = 0;
                }
                if (status_counts[3] && status_counts[3] >= 0) {
                    this.getStatusOption[3].total = status_counts[3];
                } else {
                    this.getStatusOption[3].total = 0;
                }
                if (status_counts[4] >= 0) {
                    this.getStatusOption[4].total = status_counts[4];
                } else {
                    this.getStatusOption[4].total = 0;
                }
                if (status_counts[5] && status_counts[5] >= 0) {
                    this.getStatusOption[5].total = status_counts[5];
                } else {
                    this.getStatusOption[5].total = 0;
                }
                if (status_counts[6] && status_counts[6] >= 0) {
                    this.getStatusOption[6].total = status_counts[6];
                } else {
                    this.getStatusOption[6].total = 0;
                }
                if (status_counts[7] && status_counts[7] >= 0) {
                    this.getStatusOption[7].total = status_counts[7];
                } else {
                    this.getStatusOption[7].total = 0;
                }
                if (status_counts[8] && status_counts[8] >= 0) {
                    this.getStatusOption[8].total = status_counts[8];
                } else {
                    this.getStatusOption[8].total = 0;
                }
                if (status_counts[9] >= 0) {
                    this.getStatusOption[9].total = status_counts[9];
                } else {
                    this.getStatusOption[9].total = 0;
                }
                if (status_counts[10] >= 0) {
                    this.getStatusOption[10].total = status_counts[10];
                } else {
                    this.getStatusOption[10].total = 0;
                }
                if (status_counts[11] >= 0) {
                    this.getStatusOption[11].total = status_counts[11];
                } else {
                    this.getStatusOption[11].total = 0;
                }
                if (service_data.pagination && service_data.pagination.total >= 0) {
                    if (this.category_type === 'all') {
                        // console.log(id, 'What happening......');
                        this.all_count = service_data.pagination.total;
                    }
                    this.all_count_status = service_data.pagination.total;
                    if (this.category_list && Array.isArray(this.category_list) && this.category_list.length > 0 && this.category_type === 'all') {
                        this.category_list[0].services_count = service_data.pagination.total;
                    }
                } else {
                    this.all_count = 0;
                    if (this.category_list && Array.isArray(this.category_list) && this.category_list.length > 0) {
                        this.category_list[0].services_count = 0;
                    }
                }
                if (status_counts[0] >= 0 || status_counts[1] >= 0 || status_counts[2] >= 0 || status_counts[3] >= 0 || status_counts[4] >= 0) {
                    this.pending_status_data.total = ((1 * status_counts[0] >= 0 ? status_counts[0] : 0) + (1 * status_counts[1] >= 0 ? status_counts[1] : 0) + (1 * status_counts[2] >= 0 ? status_counts[2] : 0) + (1 * status_counts[3] >= 0 ? status_counts[3] : 0));
                    //+ (1 * status_counts[4] >= 0 ? status_counts[4] : 0)
                }
                else {
                    this.pending_status_data.total = 0;
                }
            } else {
                if (status_counts[0] >= 0) {
                    this.getStatusOption[0].total = status_counts[0];
                } else {
                    this.getStatusOption[0].total = 0;
                }
                if (status_counts[1] && status_counts[1] >= 0) {
                    this.getStatusOption[1].total = status_counts[1];
                } else {
                    this.getStatusOption[1].total = 0;
                }
                if (status_counts[2] && status_counts[2] >= 0) {
                    this.getStatusOption[2].total = status_counts[2];
                } else {
                    this.getStatusOption[2].total = 0;
                }
                if (status_counts[3] && status_counts[3] >= 0) {
                    this.getStatusOption[3].total = status_counts[3];
                } else {
                    this.getStatusOption[3].total = 0;
                }
                if (status_counts[4] && status_counts[4] >= 0) {
                    this.getStatusOption[4].total = status_counts[4];
                } else {
                    this.getStatusOption[4].total = 0;
                }
                if (status_counts[5] && status_counts[5] >= 0) {
                    this.getStatusOption[5].total = status_counts[5];
                } else {
                    this.getStatusOption[5].total = 0;
                }
                if (status_counts[6] && status_counts[6] >= 0) {
                    this.getStatusOption[6].total = status_counts[6];
                } else {
                    this.getStatusOption[6].total = 0;
                }
                if (status_counts[7] && status_counts[7] >= 0) {
                    this.getStatusOption[7].total = status_counts[7];
                } else {
                    this.getStatusOption[7].total = 0;
                }

                if (service_data.pagination && service_data.pagination.total >= 0) {
                    if (this.category_type === 'all') {
                        // console.log(id, 'What happening......');
                        this.all_count = service_data.pagination.total;
                    }
                    this.all_count_status = service_data.pagination.total;
                    if (this.category_list && Array.isArray(this.category_list) && this.category_list.length > 0 && this.category_type === 'all') {
                        this.category_list[0].services_count = service_data.pagination.total;
                    }
                } else {
                    this.all_count = 0;
                    if (this.category_list && Array.isArray(this.category_list) && this.category_list.length > 0) {
                        this.category_list[0].services_count = 0;
                    }
                }
                if (status_counts[0] >= 0 || status_counts[1] >= 0 || status_counts[2] >= 0 || status_counts[3] >= 0 || status_counts[4] >= 0) {
                    this.pending_status_data.total = ((1 * status_counts[0] >= 0 ? status_counts[0] : 0) + (1 * status_counts[1] >= 0 ? status_counts[1] : 0) + (1 * status_counts[2] >= 0 ? status_counts[2] : 0) + (1 * status_counts[3] >= 0 ? status_counts[3] : 0));
                    //--+ (1 * status_counts[4] >= 0 ? status_counts[4] : 0)
                } else {
                    this.pending_status_data.total = 0;
                }
            }

            this.open_skeleton = false;
        },
        //---service category---
        serviceCategoryList() {
            this.open_skeleton = true;
            /*axios.get(`/service_categories`, { params: { company_id: this.companyId } })
                .then(response => {
                    // console.log(response.data, 'service gatgory get..!');
                    this.category_list = response.data.data;
                    // console.log(this.category_list && Array.isArray(this.category_list), 'TTTTTTTTTTTT');
                    if (this.category_list && Array.isArray(this.category_list)) {
                        this.category_list.unshift({ id: 'all', service_category: 'All', service_status: 1, services_count: 0 });
                        if (this.category_list[1].form) {
                            const labelsNameSubset = JSON.parse(this.category_list[1].form).map((opt) => opt.lableName);
                            const fieldKeySubset = JSON.parse(this.category_list[1].form).map((opt) => opt.fieldKey);

                            // Collect values at indices 0 to 3
                            this.labelsName = labelsNameSubset.slice(0, 4);
                            this.fieldKey = fieldKeySubset.slice(0, 4);

                            // let find_status_list = JSON.parse(this.category_list[1].form).find(opt => opt.fieldKey === 'status');
                            // if (find_status_list) {
                            //     this.serviceTrack = find_status_list.option.map((opt, i) => {
                            //         return { name: opt, date: '', status: false };
                            //     });
                            // }
                        }
                    } else {
                        this.open_skeleton = false;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    this.open_skeleton = false;
                })*/
            if (this.currentServiceCategory && this.currentServiceCategory.length > 0) {
                this.category_list = [...this.currentServiceCategory];
                // console.log(this.currentServiceCategory, 'rrwrwrwrwrwr what hhhhhh');

                if (this.category_list && Array.isArray(this.category_list)) {
                    this.categoryColors = this.category_list.map((category, index) => {
                        return { id: category.id, color: this.assignUniqueColor(category.id, index) };
                    });
                    this.category_list.unshift({ id: 'all', service_category: 'All', service_status: 1, services_count: 0 });
                    if (this.category_list[1].form) {
                        const labelsNameSubset = JSON.parse(this.category_list[1].form).map((opt) => opt.lableName);
                        const fieldKeySubset = JSON.parse(this.category_list[1].form).map((opt) => opt.fieldKey);

                        // Collect values at indices 0 to 3
                        this.labelsName = labelsNameSubset.slice(0, 4);
                        this.fieldKey = fieldKeySubset.slice(0, 4);

                        // let find_status_list = JSON.parse(this.category_list[1].form).find(opt => opt.fieldKey === 'status');
                        // if (find_status_list) {
                        //     this.serviceTrack = find_status_list.option.map((opt, i) => {
                        //         return { name: opt, date: '', status: false };
                        //     });
                        // }                        
                    }
                }

                this.open_skeleton = false;
            }

        },
        assignUniqueColor(categoryId) {
            if (!categoryId) return 'text-gray-900'; // Default color for undefined IDs

            // Check if the category already has an assigned color
            let existingColor = this.categoryColors.find(entry => entry.id === categoryId);
            if (existingColor) {
                return existingColor.color;
            }

            const baseColors = [
                'red', 'blue', 'green', 'yellow', 'purple', 'pink',
                'indigo', 'orange', 'teal', 'fuchsia', 'cyan', 'lime',
                'amber', 'violet', 'rose'
            ];
            const darkShades = ['700', '800', '900']; // Only dark colors

            // Generate a stable unique color using hashing
            let hash = 0;
            let idString = categoryId.toString();
            for (let i = 0; i < idString.length; i++) {
                hash = idString.charCodeAt(i) + ((hash << 5) - hash);
            }

            // Assign colors based on hash
            const colorIndex = Math.abs(hash % baseColors.length);
            const shadeIndex = Math.abs(hash % darkShades.length);
            const assignedColor = `text-${baseColors[colorIndex]}-${darkShades[shadeIndex]}`;

            // Store assigned color to prevent duplication
            this.categoryColors.push({ id: categoryId, color: assignedColor });

            return assignedColor;
        },
        getSearchedData(page) {
            if (this.followup_select == 'all' || this.status_select === 12) {
                this.status_select = 12;
                this.followup_select = 'all';
            }
            if (this.filteredBy) {
                this.filteredBy = {};
            }
            if (Object.keys(this.searchByCustomer).length > 0 && this.searchByCustomer.id) {
                this.open_loader = true;
                let send_data = { type: 'services', q: this.status_select >= 0 && this.followup_select !== 'all' ? this.status_select : '', filter: this.followup_select >= 0 && this.status_select !== 5 ? this.followup_select : '', per_page: this.recordsPerPage, page: page, customer_id: this.searchByCustomer.id };
                // console.log(this.category_type !== null && this.category_type !== 'all', 'Waht happening...!!!');
                if (this.category_type !== null && this.category_type !== 'all') {
                    send_data.category_id = this.category_type;
                }
                axios.get('/searchs', { params: { ...send_data } })
                    .then(response => {
                        // console.log(response.data, 'Status Data');
                        this.open_loader = false;
                        this.pagination = response.data.pagination;
                        this.data = response.data.data;
                        this.currentPage = page ? page : this.currentPage;
                        if (response.data.status_counts && (!Array.isArray(response.data.status_counts) || response.data.data.length === 0)) {
                            if (response.data.status_counts[0] >= 0) {
                                this.getStatusOption[0].total = response.data.status_counts[0];
                            } else {
                                this.getStatusOption[0].total = 0;
                            }
                            if (response.data.status_counts[1] >= 0) {
                                this.getStatusOption[1].total = response.data.status_counts[1];
                            } else {
                                this.getStatusOption[1].total = 0;
                            }
                            if (response.data.status_counts[2] >= 0) {
                                this.getStatusOption[2].total = response.data.status_counts[2];
                            } else {
                                this.getStatusOption[2].total = 0;
                            }
                            if (response.data.status_counts[3] >= 0) {
                                this.getStatusOption[3].total = response.data.status_counts[3];
                            } else {
                                this.getStatusOption[3].total = 0;
                            }
                            if (response.data.status_counts[4] >= 0) {
                                this.getStatusOption[4].total = response.data.status_counts[4];
                            } else {
                                this.getStatusOption[4].total = 0;
                            }
                            if (response.data.status_counts[5] >= 0) {
                                this.getStatusOption[5].total = response.data.status_counts[5];
                            } else {
                                this.getStatusOption[5].total = 0;
                            }
                            if (response.data.status_counts[6] >= 0) {
                                this.getStatusOption[6].total = response.data.status_counts[6];
                            } else {
                                this.getStatusOption[6].total = 0;
                            }
                            if (response.data.status_counts[7] >= 0) {
                                this.getStatusOption[7].total = response.data.status_counts[7];
                            } else {
                                this.getStatusOption[7].total = 0;
                            }
                            if (response.data.pagination && response.data.pagination.total >= 0) {
                                this.all_count_status = response.data.pagination.total;
                            } else {
                                this.all_count_status = 0;
                            }
                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_loader = false;
                    })
            }
        },
        //---formated display date---
        formattedDate(timestamp, isdate, isexpect) {
            const date = new Date(timestamp);
            // date.setHours(date.getHours() + 5); // Add 5 hours
            // date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            if (isdate) {
                // Get the day, month, year, hours, and minutes
                const day = String(date.getDate()).padStart(2, '0'); // Ensures 2 digits
                const month = String(date.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
                const year = date.getFullYear();
                const hours = date.getHours() % 12 || 12; // Convert to 12-hour format
                const minutes = String(date.getMinutes()).padStart(2, '0');
                const ampm = date.getHours() >= 12 ? 'PM' : 'AM';
                if (isexpect) {
                    return `${day}-${month}-${year} `;
                } else {
                    return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
                }

            } else {
                return date.toISOString().slice(0, 16);
            }
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            dateTime.setHours(dateTime.getHours() + 5); // Add 5 hours
            dateTime.setMinutes(dateTime.getMinutes() + 30); // Add 30 minutes
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);
                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    return `${hoursAgo} ${hoursAgo === 1 ? 'hour' : 'hours'} ago`;
                } else if (minutesAgo >= 1) {
                    return `${minutesAgo} ${minutesAgo === 1 ? 'min' : 'mins'} ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;
                if (this.filteredBy.serial_no && this.filteredBy.serial_no !== '') {
                    this.open_skeleton_isMobile = false;
                    return;
                }
                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            let send_data = {
                type: 'services', q: this.status_select >= 0 && this.status_select !== 12 ? this.status_select : this.status_select === 'pending' ? 'pending' : '', filter: this.followup_select >= 0 ? this.followup_select : '', per_page: this.recordsPerPage, page: page,
                customer_id: this.searchByCustomer.id ? this.searchByCustomer.id : ''
            };
            if (this.filteredBy && Object.keys(this.filteredBy).length > 0) {
                // console.log(this.filteredBy, 'helllo');
                if (this.filteredBy.customer_id) {
                    send_data.customer_id = this.filteredBy.customer_id
                }
                if (this.filteredBy.assign_to && this.filteredBy.assign_to.length > 0) {
                    send_data.employer_id = this.filteredBy.assign_to[0].id;
                }
            }
            // console.log(this.category_type !== null && this.category_type !== 'all', 'Waht happening...!!!');
            if (this.category_type !== null && this.category_type !== 'all') {
                send_data.category_id = this.category_type;
            }
            axios.get('/searchs', { params: { ...send_data } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },
        //--role based on--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        //----table action----
        showModal(index, event) {
            if (index) {
                this.lastMousePosition = { x: event.layerX * 1, y: event.layerY * 1 };
                // Start a timer to check after 5000ms
                this.hoverTimer = setTimeout(() => {
                    if (this.isMouseInSamePosition(event)) {
                        this.modalData = index;
                        this.modalPosition = { x: ((event.layerX * 1) + 25), y: (event.layerY * 1) + 25 };
                        this.isModalVisible = true;
                    }
                }, 200);
            }
        },
        hideModal() {
            clearTimeout(this.hoverTimer); // Clear any existing timer
            setTimeout(() => {
                if (!this.isHoveringModal) {
                    this.isModalVisible = false;
                }
            }, 200);
        },
        toggleHoverModal(status) {
            this.isHoveringModal = status;
            if (!status) {
                this.hideModal();
            }
        },
        isMouseInSamePosition(event) {
            // Allow for a 10-pixel tolerance in mouse movement
            const xDiff = Math.abs(this.lastMousePosition.x - event.layerX);
            const yDiff = Math.abs(this.lastMousePosition.y - event.layerY);
            return xDiff <= this.tolerance && yDiff <= this.tolerance;
        },
        performAction(action) {
            // Perform different actions based on the action parameter
            if (action === 'edit') {
                // Logic to edit the record
                console.log('Editing record', this.currentRecord);
            } else if (action === 'delete') {
                // Logic to delete the record
                console.log('Deleting record', this.currentRecord);
            }
            this.hideModal(); // Hide modal after action
        },
        filterDataBy(type, key) {
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'service' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'service', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'service' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'service', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        //--filter dropdown---
        toggleMainDropdown() {
            this.isMainDropdownOpen = !this.isMainDropdownOpen;
            if (this.isMainDropdownOpen) {
                document.addEventListener('click', this.handleClickOutsideFilter);
            } else {
                // this.showSubDropdown = false;
                document.removeEventListener('click', this.handleClickOutsideFilter);
            }
        },
        toggleSubDropdown() {
            setTimeout(() => {
                if (!this.is_openSub) {
                    this.showSubDropdown = !this.showSubDropdown;
                }
            }, 200)
        },
        toggleMouserHoverSub() {
            this.is_openSub = !this.is_openSub;
            if (!this.is_openSub && this.showSubDropdown) {
                this.toggleSubDropdown();
            }
        },
        handleClickOutsideFilter(event) {
            try {
                const mainDropdown = this.$refs.dropdownContainerFilter;
                // console.log(event.target, 'Waht happning the data....', mainDropdown.contains(event.target));
                if (mainDropdown && !mainDropdown.contains(event.target)) {
                    this.is_openSub = false;
                    this.showSubDropdown = false;
                    this.toggleMainDropdown();
                    // document.removeEventListener('click', this.handleClickOutsideFilter);
                }
            } catch (error) {
                this.showSubDropdown = false;
            }
        },
        //---update is mobile--
        updateIsMobile() {
            this.is_phone = window.innerWidth < 768;;
        },
        closeAllModals() {
            this.open_confirmBox = false;
            this.open_message = false;
            this.lead_filter = false;
            this.open_loader = false;
            if (!this.serviceIsGo) {
                this.open_service_category = false;
            }
        },
        updateStatus() {
            this.serviceIsGo = true;
            this.$emit('updateIsOpen', false);
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                let searchDatacustomer = {}
                searchDatacustomer.customer_id = selectedData.id;
                searchDatacustomer.customer = selectedData.first_name + (selectedData.last_name ? ' ' + selectedData.last_name : '') + ' - ' + selectedData.contact_number;
                this.closeLeadFilter(1, searchDatacustomer);
            }
        },
        resetToSearch() {
            this.filteredBy = {};
            this.resetSearchData = false;
            this.resetTheFilter();
        },
        //---filter sort--
        onMouseOver(column) {
            this.showSortIcon(column.field);
        },
        onMouseLeave(column) {
            this.hideSortIcon(column.field);
        },
        getSortType(column) {
            const currentType = this.getSortTypeDisplay(column.field);
            const newType = currentType === '' ? 'asc' : currentType === 'asc' ? 'desc' : 'asc';
            this.updateSortType({ field: column.field, type: newType });
            // Call your data filtering function
            this.filterDataBy(newType, column.field);
        },
        //--refresh page--
        refreshDataTable() {
            this.resetTheFilter();
        },
        //--advance search options---
        openSearch() {
            this.isSearch = true;
        },
        closesearch() {
            this.isSearch = false;
        },

        //--category format data--
        // Format the value to display it correctly in the table
        formatData(value, key) {
            // Handle Arrays
            if (Array.isArray(value)) {
                if (typeof value[0] === 'object') {
                    // If array contains objects
                    if (key === 'assignWork') {
                        return value.map(item => `Name: ${item.name || ''}`).join(', ');
                    } else if (key === 'comments') {
                        return value.map(item =>
                            `Message: ${item.comments || ''}, Date: ${item.current_date || ''}, Updated By: ${item.updated_by?.name || ''}`
                        ).join(', ');
                    } else if (key === 'document') {
                        return value.map(item => `File Name: ${item.image || ''}`).join(', ');
                    } else if (key === 'additional') {
                        return value.map(item =>
                            `Item Name: ${item.product_name || ''} -> QTY: ${item.qty || ''}`
                        ).join(', ');
                    } else {
                        return value.map(item =>
                            Object.entries(item)
                                .map(([subKey, subVal]) => !this.sub_keys.includes(subKey) ? `${subKey}: ${this.formatData(subVal)}` : null)
                                .filter(Boolean) // Filter out undefined/null values
                                .join(', ')
                        ).join(', '); // Join all object representations with commas
                    }
                } else {
                    // If it's an array of strings
                    return value.join(', ');
                }
            }

            // Handle Objects
            if (typeof value === 'object' && value !== null) {
                return Object.entries(value)
                    .map(([subKey, subVal]) => {
                        if (!this.sub_keys.includes(subKey)) {
                            return `${subKey}: ${this.formatData(subVal)}`;
                        }
                        return null;
                    })
                    .filter(Boolean) // Filter out undefined/null values
                    .join(', ');
            }

            // For other types like string or primitives
            return value || '';
        },
        getUserColor(userId) {
            const colors = ['#3e14e3', '#2a9d8f', '#264653', '#8a4f7d', '#457b9d', '#e36ae9'];
            return colors[userId % colors.length];
        },
        //---on key press to make pagination--
        handleKeyPress(event) {
            if (event.key === "ArrowRight" && this.currentPage < this.pagination.last_page) {
                this.currentPage++;
            } else if (event.key === "ArrowLeft" && this.currentPage > 1) {
                this.currentPage--;
            }
        },
        getCategoryColor(categoryId) {
            if (categoryId && this.categoryColors && this.categoryColors.length > 0) {
                let find_color = this.categoryColors.find(opt => opt.id == categoryId);
                if (find_color && find_color.color) {
                    return find_color.color;
                }
            }
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        },
        closeQrcode() {
            this.startScanner = false;
        }
    },
    mounted() {
        let id_data = this.$route.query.employerId;
        let status_data = this.$route.query.status;
        this.fetchApiUpdates();
        this.updateIsMobile();
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        // this.getStatusLabel('pending', 1);
        const view = localStorage.getItem('service_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
            // if (parse_data.category_type) {
            //     this.category_type = parse_data.category_type;
            // }
            // if (parse_data.filteredBy) {
            //     this.filteredBy = parse_data.filteredBy;
            // }
            // if (parse_data.status_select) {
            //     this.status_select = parse_data.status_select;
            // }
        }
        this.fetchCompanyList();

        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
            this.open_skeleton = false;
        }
        if (this.currentServiceCategory && this.currentServiceCategory.length === 0) {
            this.fetchServiceCategoryList();
        } else {
            this.open_skeleton = false;
            this.fetchServiceCategoryList();
        }
        if (Array.isArray(this.category_list) && this.category_list.length === 0) {
            this.serviceCategoryList();
        } else {
            this.open_skeleton = false;
        }
        // this.serviceDataList(this.category_type, 1, this.recordsPerPage);
        let store_data = this.currentServiceList;
        this.is_started = true;
        if (store_data && Object.keys(store_data).length > 0 && store_data.list.length > 0) {
            let find_category = store_data.list.filter(opt => opt.category == this.category_type);
            if (find_category[0].data.length > 0 && find_category[0].data.length !== this.data.length) {
                this.serviceDataListInit(find_category[0], store_data.status_counts);
            }
            else {
                this.serviceDataListInit(find_category[0], store_data.status_counts);
            }
            this.open_skeleton = false;
            this.fetchServiceList({ id: this.category_type, page: 1, per_page: this.recordsPerPage });
            if (id_data || status_data) {
                let status_collect = status_data == 'canceled' ? 6 : status_data == 'completed' ? 7 : status_data == 'total' ? 'all' : 'pending';
                this.closeLeadFilter(1, { assign_to: [{ id: id_data }] }, null, null, status_collect);
            } else {
                this.getStatusLabel(this.status_select, 1);
            }
        } else {
            this.fetchServiceList({ id: this.category_type, page: 1, per_page: this.recordsPerPage });
            if (id_data || status_data) {
                let status_collect = status_data == 'canceled' ? 6 : status_data == 'completed' ? 7 : status_data == 'total' ? 'all' : 'pending';
                this.closeLeadFilter(1, { assign_to: [{ id: id_data }] }, null, null, status_collect);
            } else {
                this.getStatusLabel(this.status_select, 1);
            }
        }
        //---sortIcons---
        const initialShortVisible = ['customer', 'invoice_id', 'expected_date', 'service_code', 'status'].map((field) => ({
            label: field,
            visible: false,
            type: ''
        }));
        this.$store.dispatch('sortIcons/setShortVisible', initialShortVisible);

        window.addEventListener('resize', this.updateIsMobile);
        // Add and remove event listeners
        window.addEventListener("keydown", this.handleKeyPress);

    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        document.removeEventListener('click', this.handleClickOutsideDate());
        window.removeEventListener('resize', this.updateContainerWidth);
        document.removeEventListener('click', this.handleClickOutsideFilter);
        window.removeEventListener("keydown", this.handleKeyPress);
        clearInterval(this.timer);
    },
    watch: {
        category_type: {
            deep: true,
            handler(newValue) {
                this.is_filter = false;
                if (newValue) {
                    this.open_skeleton = true;
                    this.serviceDataList(newValue, 1, this.recordsPerPage);
                    this.selected_category = true;
                }
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                this.is_filter = false;
                if ((this.status_select === null || this.status_select === 12) && (this.followup_select === null || this.followup_select === 'all') && Object.keys(this.searchByCustomer).length === 0 && Object.keys(this.filteredBy).length === 0 && this.category_type === 'all') {
                    this.serviceDataList('all', newValue, this.recordsPerPage);
                }
                else {
                    if (this.status_select !== null && this.status_select >= 0 && this.status_select !== 12) {
                        this.getStatusLabel(this.status_select, newValue);
                    } else if (this.followup_select && this.followup_select !== 'all') {
                        this.handleFollowup(this.followup_select, newValue);
                    } else if (Object.keys(this.searchByCustomer).length !== 0) {
                        this.getSearchedData(newValue);
                    } else if (Object.keys(this.filteredBy).length !== 0) {
                        this.closeLeadFilter(newValue, this.filteredBy);
                    } else if (this.status_select !== null && this.status_select === 'pending') {
                        this.getStatusLabel(this.status_select, newValue);
                    } else {
                        this.serviceDataList(this.category_type, newValue, this.recordsPerPage);
                    }
                }
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                this.is_filter = false;
                if ((this.status_select === null || this.status_select === 12) && (this.followup_select === null || this.followup_select === 'all') && Object.keys(this.searchByCustomer).length === 0 && Object.keys(this.filteredBy).length === 0 && this.category_type === 'all') {
                    this.serviceDataList('all', 1, newValue);
                } else {
                    if (this.status_select !== null && this.status_select >= 0 && this.status_select !== 12) {
                        this.getStatusLabel(this.status_select, 1);
                    } else if (this.followup_select && this.followup_select !== 'all') {
                        this.handleFollowup(this.followup_select, 1);
                    } else if (Object.keys(this.searchByCustomer).length !== 0) {
                        this.getSearchedData(1);
                    } else if (Object.keys(this.filteredBy).length !== 0) {
                        this.closeLeadFilter(1, this.filteredBy);
                    } else if (this.status_select !== null && this.status_select === 'pending') {
                        this.getStatusLabel(this.status_select, 1);
                    }
                    else {
                        this.serviceDataList(this.category_type, 1, newValue);
                    }
                }
                this.number_of_rows = newValue;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        search_query(newValue) {
            this.is_filter = false;
            // console.log(newValue, 'WWWWWWWWWW happening the data');
            // Watch for changes in Vuex state and update local searchQuery
            if (!this.isEmptyObject(newValue) && !this.isEmptyObject(this.search_data)) {
                // console.log(newValue, 'What happening...!!!!');
                this.filteredBy = {};
                this.searchByCustomer = this.search_data;
                this.category_type = 'all';
                this.getSearchedData(1);
                // this.leadData = this.leadData.filter(opt => opt.customer.id === newValue.customer.id);
            }
            else {
                this.searchByCustomer = {};
                this.serviceDataList(this.category_type, 1, newValue);
            }
        },
        searchedData(newValue) {
            this.is_filter = false;
            // console.log(newValue, 'RRRRR');
            if (!this.isEmptyObject(newValue)) {
                // console.log(newValue, 'What happening...!!!!');
                this.filteredBy = {};
                this.searchByCustomer = newValue;
                this.getSearchedData(1);
                // this.leadData = this.leadData.filter(opt => opt.customer.id === newValue.customer.id);
            }
            else {
                this.searchByCustomer = {};
                this.serviceDataList(this.category_type, 1, newValue);
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                // this.open_skeleton = false;
                if (newValue) {
                    let find_data = JSON.parse(localStorage.getItem('service_home'));
                    localStorage.setItem('service_home', JSON.stringify({ ...find_data, view: newValue }));
                }
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        currentServiceCategory: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && Array.isArray(this.category_list)) {
                    this.serviceCategoryList();
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.filter_option = null;
                this.resetTheFilter();
                this.fetchServiceList({ id: this.category_type, page: 1, per_page: this.recordsPerPage });
                this.fetchServiceCategoryList();
            }
        },
        currentServiceList: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.list) {
                    this.open_skeleton = false;
                    let find_category = newValue.list.filter(opt => opt.category == this.category_type);
                    if (find_category.length > 0 && find_category[0].data && find_category[0].data.length > 0 && find_category[0].data.length !== this.data.length) {
                        this.serviceDataListInit(find_category[0], newValue.status_counts, this.is_started);
                    }
                    else {
                        this.serviceDataListInit(find_category[0], newValue.status_counts, this.is_started);
                    }
                }
                //---get status data---
                if (this.status_select !== 'all' && this.category_type && this.selected_category) {
                    this.getStatusLabel(this.status_select, 1);
                    this.selected_category = false;
                }
                this.open_loader = false;
                this.open_skeleton = false;
            }


        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_message: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        lead_filter: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_loader: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_service_category: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        data: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.open_skeleton) {
                    this.open_skeleton = false;
                }
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];
                    this.is_filter = false;
                    // console.log(newValue, 'New Value data...............');
                }
            }
        },
        category_list: {
            deep: true,
            handler(newValue) {
                if (newValue && Array.isArray(newValue) && newValue.length > 0 && this.category_type === 'all' && newValue[0].services_count !== this.all_count) {
                    newValue[0].services_count = this.all_count;
                }
            }
        }
    }
}
</script>

<style scoped>
.manualStyle {
    overflow: auto;
    height: 100vh;
}

/* Style the scrollbar */
::-webkit-scrollbar {
    width: 3px;
    /* Set the width of the scrollbar */
    height: 5px;
}

/* Track (the area around the scrollbar) */
::-webkit-scrollbar-track {
    background: #CFD8DC;
    /* Background color of the scrollbar track */
}

/* Handle (the draggable part of the scrollbar) */
::-webkit-scrollbar-thumb {
    background: #90A4AE;
    /* Color of the scrollbar handle */
}

/* Handle on hover */
::-webkit-scrollbar-thumb:hover {
    background: #90A4AE;
    /* Color of the scrollbar handle on hover */
}

.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    max-width: 100px;
    z-index: 100;
    /* Adjust the max-width as needed */
    white-space: nowrap;
    /* Prevents text wrapping */
    overflow: hidden;
    text-overflow: ellipsis;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.image-container {
    width: 150px;
    height: 50px;
    /* Set your desired fixed height */
    object-fit: cover;
    /* Maintain aspect ratio and crop as needed */
    object-position: center;
    border: 1px solid rgb(218, 218, 218);
    /* box-shadow: 1px 1px 2px 2px rgb(82, 81, 81); */
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
    }
}

@media (max-width: 768px) {
    .custom-scrollbar-hidden::-webkit-scrollbar {
        display: none;
    }

}

.fa-icons {
    background: conic-gradient(from -45deg, #ea4335 110deg, #4285f4 90deg 180deg, #34a853 180deg 270deg, #fbbc05 270deg) 73% 55%/150% 150% no-repeat;
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
}
</style>
