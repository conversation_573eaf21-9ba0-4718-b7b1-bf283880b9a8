<template>
    <div class="w-full mx-auto p-4">
        <!-- <h1 class="text-lg font-bold mb-4 text-center">Social Media Links (Testing)</h1> -->
        <form>
            <div class="my-4">
                <label class="block text-gray-700"><font-awesome-icon icon="fa-solid fa-globe" class="px-1" />
                    Google Review Link</label>
                <input v-model="formValues.website" @change="is_updated = true" type="text"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Enter the website URL">
            </div>
            <!-- <div class="mb-4">
                <label class="block text-gray-700"><font-awesome-icon icon="fa-brands fa-facebook" class="px-1" />
                    Facebook</label>
                <input v-model="formValues.facebook" type="url"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Enter the facebook link">
            </div>
            <div class="mb-4">
                <label class="block text-gray-700"><font-awesome-icon icon="fa-brands fa-twitter" class="px-1" />
                    Twitter</label>
                <input v-model="formValues.twitter" type="url"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Enter the twitter link ">
            </div>
            <div class="mb-4">
                <label class="block text-gray-700"><font-awesome-icon icon="fa-brands fa-instagram" class="px-1" />
                    Instagram</label>
                <input v-model="formValues.instagram" type="url"
                    class="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
                    placeholder="Enter the instagram link">
            </div>-->
            <div class="flex justify-between mt-12">
                <button type="button" @click="submitForm"
                    class="shadow-inner shadow-green-100 border border-green-500 bg-green-500 text-white py-2 px-4 rounded-md hover:bg-green-600 focus:outline-none focus:bg-green-600">Save
                    Link</button>
                <button @click="cancelForm" type="button"
                    class="shadow-inner shadow-gray-100 border border-gray-500 bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 focus:outline-none focus:bg-gray-600">Cancel</button>
            </div>
        </form>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
import axios from 'axios';
import { mapActions, mapGetters } from 'vuex';

export default {
    emits: ['is-sales-save', 'updatesalesData'],
    props: {
        companyId: String,
        userId: String,
        store_refresh: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            formValues: {},
            //--toaster---
            show: false,
            type_toaster: 'success',
            message: '',
            is_updated: false,
            open_loader: false,
        };
    },
    computed: {
        ...mapGetters('companies', ['currentCompanyList']),
    },
    created() {
        if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0 && this.currentCompanyList.social_links && !Array.isArray(this.currentCompanyList.social_links)) {
            this.formValues = JSON.parse(this.currentCompanyList.social_links);
            this.fetchCompanyList();
        } else {
            this.fetchCompanyList();
        }
    },
    methods: {
        ...mapActions('companies', ['fetchCompanyList']),
        submitForm() {
            this.open_loader = true;
            let send_data = JSON.stringify(this.formValues);
            axios.put(`/sociallinks-update/${this.companyId}`, { social_links: send_data })
                .then(response => {
                    this.message = 'Social media links saved successfully!';
                    this.show = true;
                    this.$emit('is-sales-save', false);
                    this.open_loader = false;
                })
                .catch(error => {
                    console.error('Error saving social links:', error);
                    this.open_loader = false;
                });
        },
        cancelForm() {
            if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0 && !Array.isArray(this.currentCompanyList.social_links)) {
                this.formValues = JSON.parse(this.currentCompanyList.social_links);
            } else {
                this.formValues = {};
            }
        },
    },
    watch: {
        currentCompanyList: {
            deep: true,
            handler(newValue) {
                if (newValue && Object.keys(newValue).length > 0 && newValue.social_links) {
                    if (typeof newValue.social_links === 'string') {
                        // If social_links is a string, parse it
                        try {
                            this.formValues = JSON.parse(newValue.social_links);
                        } catch (e) {
                            console.error('Error parsing social_links:', e);
                            this.formValues = {};
                        }
                    } else if (typeof newValue.social_links === 'object') {
                        // If social_links is already an object, just access the 'website' key
                        this.formValues = newValue.social_links.website || {};
                    }
                }
            }
        },
        save_success: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.submitForm();
                }
            }
        },
        is_updated: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.is_updated = false;
                    this.$emit('is-sales-save', newValue);
                }
            }
        }
    }
};
</script>

<style scoped>
/* Add any scoped styles here */
</style>