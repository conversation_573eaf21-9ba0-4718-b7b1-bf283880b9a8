<template>
    <div class="relative h-screen w-screen ">
        <canvas ref="confettiCanvas" id="confetti" class="absolute top-0 left-0"></canvas>

    </div>
</template>
<script>
export default {
    data() {
        return {
            canvas: null,
            ctx: null,
            confettiPieces: [],
        };
    },
    methods: {
        // Initialize the confetti animation
        initConfetti() {
            this.canvas = this.$refs.confettiCanvas;
            this.ctx = this.canvas.getContext("2d");

            // Set the canvas size
            this.canvas.width = window.innerWidth;
            this.canvas.height = window.innerHeight;

            // Generate confetti pieces
            for (let i = 0; i < 150; i++) {
                this.confettiPieces.push(this.createConfettiPiece());
            }

            this.animateConfetti();
        },

        // Create a single confetti piece
        createConfettiPiece() {
            return {
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height - this.canvas.height,
                width: Math.random() * 10 + 5,
                height: Math.random() * 10 + 5,
                color: `hsl(${Math.random() * 360}, 100%, 50%)`,
                velocityX: Math.random() * 2 - 1,
                velocityY: Math.random() * 3 + 2,
                rotation: Math.random() * 360,
                rotationSpeed: Math.random() * 5,
            };
        },

        // Animate the confetti
        animateConfetti() {
            this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

            this.confettiPieces.forEach((piece) => {
                piece.x += piece.velocityX;
                piece.y += piece.velocityY;
                piece.rotation += piece.rotationSpeed;

                // Reset piece if it goes out of bounds
                if (piece.y > this.canvas.height) {
                    piece.y = -piece.height;
                    piece.x = Math.random() * this.canvas.width;
                }

                // Draw confetti piece
                this.ctx.save();
                this.ctx.translate(piece.x, piece.y);
                this.ctx.rotate((piece.rotation * Math.PI) / 180);
                this.ctx.fillStyle = piece.color;
                this.ctx.fillRect(-piece.width / 2, -piece.height / 2, piece.width, piece.height);
                this.ctx.restore();
            });

            requestAnimationFrame(this.animateConfetti);
        },

        // Replay animation
        startConfetti() {
            this.confettiPieces = [];
            for (let i = 0; i < 150; i++) {
                this.confettiPieces.push(this.createConfettiPiece());
            }
            this.animateConfetti();
        },

        // Navigate to home
        goHome() {
            console.log("Navigating to home...");
        },
    },
    mounted() {
        this.initConfetti();
        window.addEventListener("resize", this.resizeCanvas);
    },
    beforeDestroy() {
        window.removeEventListener("resize", this.resizeCanvas);
    },
};
</script>
<style scoped>
canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
}
</style>