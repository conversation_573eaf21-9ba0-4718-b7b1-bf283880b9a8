<template>
  <div class="builder-page">
    <div class="flex flex-col md:flex-row justify-between items-center">
      <h1 class="web-head">Hero or Slideshow Images</h1>
      <div class="w-full md:w-auto md:self-end">
        <EnablePageName :pages="is_onSetting" @updatePagesEnabled="handleToggle"></EnablePageName>
      </div>
    </div>

    <!-- Hero or Slider Selection -->
    <div class="mb-6">
      <label class="block font-bold mb-2">Choose Section Type</label>
      <div class="flex space-x-4">
        <button type="button" @click="selectSection('hero')"
          :class="sectionType === 'hero' ? 'bg-blue-500 text-white border-blue-500' : 'bg-gray-200 text-gray-700 border-gray-300'"
          class="py-2 px-4 rounded-full border-2 shadow flex items-center space-x-2 focus:outline-none transition duration-150 ease-in-out">
          <span class="w-4 h-4 rounded-full border-2 flex items-center justify-center"
            :class="sectionType === 'hero' ? 'bg-white border-white' : 'bg-gray-200 border-gray-500'">
            <span v-if="sectionType === 'hero'" class="w-2 h-2 rounded-full bg-blue-500"></span>
          </span>
          <span>Hero Section</span>
        </button>
        <button type="button" @click="selectSection('slider')"
          :class="sectionType === 'slider' ? 'bg-blue-500 text-white border-blue-500' : 'bg-gray-200 text-gray-700 border-gray-300'"
          class="py-2 px-4 rounded-full border-2 shadow flex items-center space-x-2 focus:outline-none transition duration-150 ease-in-out">
          <span class="w-4 h-4 rounded-full border-2 flex items-center justify-center"
            :class="sectionType === 'slider' ? 'bg-white border-white' : 'bg-gray-200 border-gray-500'">
            <span v-if="sectionType === 'slider'" class="w-2 h-2 rounded-full bg-blue-500"></span>
          </span>
          <span>Slider Section</span>
        </button>
      </div>
    </div>

    <!-- Hero Section with Additional Fields -->
    <div v-if="sectionType === 'hero'">
      <h2 class="text-lg font-bold mb-4 text-center items-center">Hero Section</h2>
      <div class="mb-4">
        <label class="block font-bold mb-2">Hero Image
          <span class="text-gray-500 text-xs">1200 <font-awesome-icon icon="fa-solid fa-xmark" /> 600</span>
          <span class="text-red-500">*</span>
        </label>
        <input type="file" @change="onFileChangeHero" class="border p-2 rounded w-full" ref="heroImage"
          accept="image/png, image/jpeg" />
        <div v-if="circle_loader_photo"
          class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
          <CircleLoader :loading="true"></CircleLoader>
        </div>
        <div v-if="heroData.imageUrl" class="mt-4 relative">
          <img :src="heroData.imageUrl" alt="Hero Image Preview" class="w-full h-48 object-cover rounded-lg" />
          <button @click="removeImageData({ type: 'hero', url: heroData.imageUrl })"
            class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
            &times;
          </button>
        </div>
      </div>
      <!-- <div class="mb-4">
        <label class="block font-bold mb-2">Title <span class="text-red-500">*</span></label>
        <input type="text" v-model="heroData.title" class="border p-2 rounded w-full" placeholder="Enter Title" />
      </div>
      <div class="mb-4">
        <label class="block font-bold mb-2">Description</label>
        <textarea v-model="heroData.description" class="border p-2 rounded w-full"
          placeholder="Enter Description"></textarea>
      </div>
      <div class="mb-4">
        <label class="block font-bold mb-2">Button Name</label>
        <input type="text" v-model="heroData.buttonName" class="border p-2 rounded w-full"
          placeholder="Enter Button Name" />
      </div> -->
      <div class="mb-4">
        <label class="block font-bold mb-2">Link</label>
        <input type="url" v-model="heroData.link" class="border p-2 rounded w-full" placeholder="Enter Button Link" />
      </div>
    </div>

    <!-- Slider Section for Multiple Images -->
    <div v-if="sectionType === 'slider'">
      <h2 class="text-xl font-bold mb-4">Slider Section</h2>
      <div class="flex justify-end mb-6">
        <button @click="openModal" class="bg-blue-500 text-white py-2 px-4 rounded shadow">
          Add Images <font-awesome-icon icon="fa-solid fa-plus-circle" />
        </button>
      </div>

      <!-- Preview Grid for Selected Images -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div v-for="(image, index) in slideshowImages" :key="index" class="relative bg-white shadow-lg rounded-lg p-4"
          :draggable="true" @dragstart="dragStart(index, $event)" @dragover.prevent @drop="drop(index, $event)">
          <img :src="image.imageUrl" alt="Slideshow Image" class="w-full h-48 object-cover rounded-lg mb-4" />
          <div class="mb-2">
            <label class="block font-bold mb-2">Link for this Image</label>
            <input type="url" v-model="image.link" class="border p-2 rounded w-full" placeholder="Enter Image Link" />
          </div>
          <button @click="removeImageData({ type: 'slider', index: index, url: image.imageUrl })"
            class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
            &times;
          </button>
        </div>
      </div>
    </div>

    <!-- Add Images Modal -->
    <Modal v-if="showModal" @close="closeModal">
      <template #header>
        <h2 class="text-xl font-semibold">Add Slideshow Images</h2>
      </template>
      <template #body>
        <div class="mb-4">
          <label class="block font-bold mb-2">Choose Images
            <span class="text-gray-500 text-xs">1200 <font-awesome-icon icon="fa-solid fa-xmark" /> 600</span>
            <span class="text-red-500">*</span>
          </label>
          <input type="file" @change="onFileChangeMultiple" multiple class="border p-2 rounded w-full"
            accept="image/png, image/jpeg" />
        </div>
        <div v-if="circle_loader_photo"
          class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
          <CircleLoader :loading="true"></CircleLoader>
        </div>
        <div v-if="previewImages.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="(preview, index) in previewImages" :key="index" class="relative">
            <img :src="preview" alt="Image Preview" class="w-full h-24 object-cover rounded-lg" />
          </div>
        </div>
        <dialogConfirmBox :visible="show_dialog" :message="message" :type="'website'" @ok="closeModalconfirm"
          @cancel="cancelcloseModal" @save="uploadMultipleImages"></dialogConfirmBox>
      </template>
      <template #footer>
        <!--:disabled="multipleImageData.length === 0"-->
        <button @click="uploadMultipleImages" class="bg-green-500 text-white py-2 px-4 rounded shadow">
          Add Images
        </button>
      </template>
    </Modal>
    <div class="mt-6">
      <NavigationButtons :pageTitle="'Service Details'" @goToNextPage="goToNextPage" @goToPrevPage="goToPrevPage" />
    </div>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
  </div>

</template>

<script>
import imageService from "../../services/imageService";
import NavigationButtons from '../../components/website-builder/NavigationButtons.vue';
import Modal from "../../components/website-builder/Modal.vue";
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import EnablePageName from "./EnablePageName.vue";
import dialogConfirmBox from "@/components/supporting/dialog_box/dialogConfirmBox.vue";

export default {
  props: {
    companyId: {
      type: String,
      required: true
    },
    sectionType: { type: String, required: true },
    heroData: { type: Object, required: true },
    slideshowImages: { type: Array, required: true },
    is_updated: { type: Boolean, required: true },
    pages: { type: Object, required: true },
  },
  components: { Modal, FontAwesomeIcon, NavigationButtons, confirmbox, EnablePageName, dialogConfirmBox },
  data() {
    return {
      showModal: false,
      multipleImageData: [], // Array to store actual image files for slider upload
      previewImages: [], // Array to store preview URLs for slider images//--confirmbox--
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      //--loader--
      circle_loader_photo: false,
      //----is on settings---
      is_onSetting: { is_on: true, name: 'Slider' },
      //--confirm box dialog--
      show_dialog: false,
      message: '',
    };
  },
  mounted() {
    if (this.pages && this.pages.slider_home !== undefined) {
      this.is_onSetting = this.pages.slider_home;
    }
  },
  methods: {
    selectSection(type) {
      this.$emit("updateSectionType", type);
    },
    async onFileChangeHero(event) {
      this.circle_loader_photo = true;
      const file = event.target.files[0];
      if (!file) return;

      const maxSizeBytes = 500 * 1024;
      const uploadFile = file.size > maxSizeBytes ? await imageService.compressImage(file) : file;
      const response = await imageService.uploadImage(uploadFile, "hero", this.companyId);
      this.circle_loader_photo = false;
      this.$emit("updateHeroData", { ...this.heroData, imageUrl: response.media_url });
      this.$emit('submitData');
    },

    async onFileChangeMultiple(event) {
      this.circle_loader_photo = true;
      const files = event.target.files;
      if (!files || files.length === 0) {
        return;
      }

      this.multipleImageData = [];
      this.previewImages = [];

      for (const file of files) {
        const uploadFile = file.size > 500 * 1024 ? await imageService.compressImage(file) : file;
        this.previewImages.push(URL.createObjectURL(uploadFile));
        this.multipleImageData.push(uploadFile);
      }
      this.circle_loader_photo = false;
    },
    async uploadMultipleImages() {
      if (this.multipleImageData && this.multipleImageData.length > 0) {
        this.$emit('updateLoader', true);
        const updatedImages = [...this.slideshowImages];
        for (const file of this.multipleImageData) {
          const response = await imageService.uploadImage(file, "slider", this.companyId);
          updatedImages.push({ imageUrl: response.media_url, link: "" });
        }
        this.$emit("updateSlideshowImages", updatedImages);
        this.previewImages = [];
        this.multipleImageData = [];
        this.showModal = false;
        this.$emit('updateLoader', false);
      } else {
        this.$emit('toasterMessages', { msg: 'Please upload at least one image.', type: 'warning' });
      }
    },

    // async removeImage(index) {
    //   const imageUrl = this.slideshowImages[index].imageUrl;
    //   try {
    //     await imageService.deleteImage(imageUrl);
    //     this.slideshowImages.splice(index, 1);
    //   } catch (error) {
    //     console.error("Failed to delete image:", error);
    //   }
    // },
    // Reorder functions for moving images up and down
    moveImageUp(index) {
      if (index > 0) {
        const temp = this.slideshowImages[index - 1];
        this.slideshowImages.splice(index - 1, 1, this.slideshowImages[index]);
        this.slideshowImages.splice(index, 1, temp);
      }
    },
    moveImageDown(index) {
      if (index < this.slideshowImages.length - 1) {
        const temp = this.slideshowImages[index + 1];
        this.slideshowImages.splice(index + 1, 1, this.slideshowImages[index]);
        this.slideshowImages.splice(index, 1, temp);
      }
    },
    openModal() {
      this.showModal = true;
    },
    closeModal() {
      if (this.multipleImageData.length === 0) {
        this.showModal = false;
      } else if (this.multipleImageData.length > 0) {
        this.message = 'You have unsaved changes. Do you want to save them or ok or cancel?'
        this.show_dialog = true;
      } else {
        this.showModal = false;
      }
    },
    closeModalconfirm() {
      this.show_dialog = false;
      this.showModal = false;
    },
    cancelcloseModal() {
      this.show_dialog = false;
    },
    goToNextPage() {
      this.$emit("updateHeroData", this.heroData);
      this.$emit("updateSlideshowImages", this.slideshowImages);
      this.$emit('updatePagesSetting', { slider_home: this.is_onSetting });
      this.$emit('submitData');
      this.$emit('goToNextPage'); // Emit event to the parent component
    },
    goToPrevPage() {
      this.$emit("updateHeroData", this.heroData);
      this.$emit("updateSlideshowImages", this.slideshowImages);
      this.$emit('updatePagesSetting', { slider_home: this.is_onSetting });
      this.$emit('submitData');
      this.$emit('goToPrevPage'); // Emit event to the parent component
    },
    //---remove hero images--
    async removeHeroImage(type) {
      // type.type === 'hero' ? this.heroData.imageUrl : type.type === 'slider' ? this.slideshowImages[type.index].imageUrl : ''
      const imageUrl = type.url;
      try {
        const response = await imageService.deleteImage(imageUrl, type.type);
        if (response.status) {
          if (type.type === 'hero') {
            this.$refs.heroImage.value = '';
            this.$emit("updateHeroData", { ...this.heroData, imageUrl: false });
          }
          if (type.type === 'slider') {
            let slideImages = [...this.slideshowImages];
            slideImages.splice(type.index, 1);
            this.$emit("updateSlideshowImages", slideImages);
          }
          this.$emit('toasterMessages', { msg: 'Image deleted successfully', type: 'success' });
        }
      } catch (error) {
        console.error("Failed to delete image:", error);
        this.$emit('toasterMessages', { msg: 'Image not found', type: 'warning' });
        if (type.type === 'hero') {
          this.$refs.heroImage.value = '';
          this.$emit("updateHeroData", { ...this.heroData, imageUrl: false });
        }
        if (type.type === 'slider') {
          let slideImages = [...this.slideshowImages];
          slideImages.splice(type.index, 1);

          this.$emit("updateSlideshowImages", slideImages);
        }
      }
    },
    //--remove Image
    removeImageData(type) {
      if (type) {
        this.deleteIndex = type;
        this.open_confirmBox = true;
      } else {
        this.open_confirmBox = true;
      }
    },
    //---delete records--
    async deleteRecord() {
      if (this.deleteIndex) {
        this.$emit('updateLoader', true);
        await this.removeHeroImage(this.deleteIndex);
        this.closeconfirmBoxData();
      } else {
        this.closeconfirmBoxData()
      }
    },
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
    },
    //--handle switch--
    handleToggle(data) {
      // Perform any additional actions when toggled
      if (data) {
        this.is_onSetting = data;
      }
    },
    //---drag to reorder the sliders--
    dragStart(index, event) {
      this.dragIndex = index;
      // Optionally set a data transfer item for more complex drag scenarios
      event.dataTransfer.setData('index', index);
    },

    drop(index, event) {
      if (this.dragIndex !== index) {
        // Reorder the images array based on the drop index
        const movedItem = this.slideshowImages[this.dragIndex];
        this.slideshowImages.splice(this.dragIndex, 1); // Remove the dragged item
        this.slideshowImages.splice(index, 0, movedItem); // Insert the item at the new index
      }
    },
  },
  watch: {
    is_updated: {
      deep: true,
      handler(newValue) {
        this.$emit("updateHeroData", this.heroData);
        this.$emit("updateSlideshowImages", this.slideshowImages);
        this.$emit('updatePagesSetting', { slider_home: this.is_onSetting });
      }
    },
    heroData: {
      deep: true,
      handler(newValue) {
        this.$emit("updateHeroData", this.heroData);
      }
    },
    slideshowImages: {
      deep: true,
      handler(newValue) {
        this.$emit("updateSlideshowImages", this.slideshowImages);
      }
    },
    is_onSetting: {
      deep: true,
      handler(newValue) {
        this.$emit('updatePagesSetting', { slider_home: newValue });
      }
    }

  }
};
</script>

<style scoped>
/* Additional styles */
</style>
