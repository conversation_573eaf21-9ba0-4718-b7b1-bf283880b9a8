<template>
  <div class="flex justify-between">
    <button v-if="showBackButton" class="bg-gray-500 text-white py-2 px-4 rounded" @click="goBack">
      Back
    </button>
    <button v-if="isFinishPage" class="bg-green-500 text-white py-2 px-4 rounded" @click="finish">
      Finish
    </button>
    <button v-if="isFinishPage" class="bg-blue-500 text-white py-2 px-4 rounded" @click="preview">
      Preview
    </button>
    <button v-else class="bg-blue-500 text-white py-2 px-4 rounded" @click="goNext">
      Next
    </button>
  </div>

</template>

<script>
export default {
  props: {
    pageTitle: {
      type: String,
      required: true
    },
    showBackButton: {
      type: Boolean,
      default: true
    },
    isFinishPage: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    goBack() {
      this.$emit('goToPrevPage');
    },
    goNext() {
      this.$emit('goToNextPage');
    },
    finish() {
      this.$emit('finish');

      // alert('Finish clicked!');
      // Implement finish logic here
    },
    preview() {
      this.$emit('preview');
      //alert('Preview clicked!');
      // Implement preview logic here
    }
  }
};
</script>