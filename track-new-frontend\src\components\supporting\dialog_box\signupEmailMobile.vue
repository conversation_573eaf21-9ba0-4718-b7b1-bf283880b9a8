<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded-lg shadow-lg overflow-hidden"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">

            <!-- Header -->
            <div class="flex justify-between items-center px-5 py-3 bg-blue-600 text-white">
                <h3 class="text-lg font-bold">Alert</h3>
                <button class="text-xl font-bold hover:text-red-400" @click="closeModal">&times;</button>
            </div>

            <!-- Content -->
            <div class="px-6 py-5">
                <h4 class="text-md font-semibold text-gray-800 mb-3">{{ message }} already registered!</h4>
                <p class="text-sm text-gray-600 mb-6">
                    This mobile number or email ID is already registered. Please choose an action below.
                </p>
                <div class="flex justify-between">
                    <button @click="login"
                        class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition">
                        <font-awesome-icon icon="fa-solid fa-right-to-bracket" />
                        Login
                    </button>
                    <button @click="mobileOtpLogin"
                        class="bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 transition">
                        <font-awesome-icon icon="fa-solid fa-mobile-screen-button" />
                        Mobile OTP Login
                    </button>
                    <button @click="closeModal"
                        class="bg-gray-300 text-gray-800 px-4 py-2 rounded-lg hover:bg-gray-400 transition">
                        <font-awesome-icon icon="fa-solid fa-xmark" /> Cancel
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
export default {
    props: {
        showModal: {
            type: Boolean,
            required: true,
        },
        message: {
            type: String,
            required: true,
        },
        email: {
            type: String,
            required: true
        },
        mobile: {
            type: String,
            required: true
        }
    },
    data() {
        return {
            isOpen: false,
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit("closeModal");
            }, 300);
        },
        login() {
            const queryData = {
                email: this.email || null, // Replace with the actual email data
                mobile: this.mobile || null, // Replace with the actual mobile number data
            };
            this.$router.push({ path: "/login", query: queryData });
            this.closeModal();
        },
        mobileOtpLogin() {
            const queryData = {
                mobile: this.mobile || null, // Replace with the actual mobile number data
            };
            this.$router.push({ path: "/mobilelogin", query: queryData });
            this.closeModal();
        },
    },
    watch: {
        showModal(newVal) {
            setTimeout(() => {
                this.isOpen = newVal;
            }, 100);
        },
    },
};
</script>


<style scoped>
/* Modal Animation */
.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

/* Close Button */
button {
    transition: color 0.3s ease;
}

/* Hover States */
button:hover {
    color: whitesmoke
        /* Light Red */
}

/* Transition Effect for Buttons */
button.transition {
    transition: background-color 0.3s ease-in-out, transform 0.2s ease;
}

button.transition:hover {
    transform: scale(1.05);
    /* Slightly enlarge on hover */
}
</style>