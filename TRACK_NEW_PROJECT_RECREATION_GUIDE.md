# Track New - Service Management System Recreation Guide

## Project Overview

Track New is a comprehensive **Service Management Web Application** designed to streamline service tracking, estimation, proforma, sales, and task management. The system consists of:

- **Backend**: Laravel 8.x API with JWT authentication
- **Frontend**: Vue.js 3 with Vite, Tai<PERSON>wind CSS, and Vuex state management
- **Database**: MySQL with comprehensive migrations
- **Features**: Service management, AMC tracking, sales, inventory, reporting, and more

## System Architecture

### Backend (Laravel API)
- **Framework**: Laravel 8.x
- **Authentication**: JWT (tymon/jwt-auth)
- **Database**: MySQL
- **File Storage**: AWS S3 (Laravel Flysystem)
- **PDF Generation**: DomPDF
- **API Documentation**: Swagger (L5-Swagger)
- **Permissions**: <PERSON><PERSON> Permission
- **Excel Import/Export**: Maatwebsite Excel

### Frontend (Vue.js SPA)
- **Framework**: Vue.js 3 with Composition API
- **Build Tool**: Vite
- **State Management**: Vuex 4
- **Routing**: Vue Router 4
- **UI Framework**: Tailwind CSS
- **Charts**: Chart.js with Vue-ChartJS
- **PDF Generation**: jsPDF
- **Excel Export**: SheetJS (xlsx)
- **Notifications**: Firebase Cloud Messaging
- **PWA**: Vite PWA Plugin

## Core Features & Modules

### 1. Authentication & User Management
- **JWT-based authentication**
- **Role-based access control** (Admin, Sub_Admin, Account Manager, Service Manager, Sales man, Service Engineer)
- **Permission-based authorization**
- **Multi-company support**
- **User registration with company creation**
- **Password reset functionality**
- **Mobile and email login support**

### 2. Dashboard & Analytics
- **Real-time dashboard with key metrics**
- **Service completion statistics**
- **Revenue tracking**
- **Lead conversion analytics**
- **AMC renewal tracking**
- **Interactive charts and graphs**
- **Date range filtering**

### 3. Lead Management
- **Lead creation and tracking**
- **Lead status management** (Open, In Progress, Closed, etc.)
- **Lead type categorization**
- **Lead assignment to employees**
- **Lead follow-up tracking**
- **Lead conversion to services/sales**

### 4. Service Management
- **Service request creation**
- **Service tracking with unique service codes**
- **Service assignment to engineers**
- **Service status tracking** (Pending, In Progress, Completed, etc.)
- **Service categories and subcategories**
- **Material requirement tracking**
- **Service completion with customer signature**
- **Service history and notes**

### 5. AMC (Annual Maintenance Contract) Management
- **AMC creation and tracking**
- **AMC renewal management**
- **Automated renewal reminders**
- **AMC product tracking**
- **AMC service scheduling**
- **AMC payment tracking**
- **AMC reports and analytics**

### 6. Customer Management
- **Customer registration and profiles**
- **Customer categorization**
- **Customer service history**
- **Customer contact management**
- **Customer feedback and reviews**
- **Customer communication tracking**

### 7. Inventory & Product Management
- **Product catalog management**
- **Brand and category management**
- **Unit and tax management**
- **Barcode generation and scanning**
- **Stock tracking**
- **Warehouse management**
- **Supplier management**

### 8. Sales & Purchase Management
- **Sales order creation**
- **Purchase order management**
- **Payment tracking**
- **Invoice generation**
- **Proforma invoice creation**
- **Hold invoices management**
- **Payment gateway integration** (PhonePe)

### 9. Estimation & Quotation
- **Service estimation creation**
- **Quotation generation**
- **Estimation approval workflow**
- **Estimation to service conversion**
- **PDF generation for estimates**

### 10. RMA (Return Merchandise Authorization)
- **RMA creation and tracking**
- **Inward and outward management**
- **RMA status tracking**
- **RMA payment management**
- **RMA accessories tracking**

### 11. Expense Management
- **Expense tracking**
- **Expense categorization**
- **Expense approval workflow**
- **Expense reporting**

### 12. Reporting & Analytics
- **Comprehensive reporting system**
- **Service reports**
- **Sales reports**
- **AMC reports**
- **Financial reports**
- **Custom date range reports**
- **Export to Excel/PDF**

### 13. Communication & Notifications
- **WhatsApp integration**
- **SMS notifications**
- **Email notifications**
- **Firebase push notifications**
- **In-app notifications**
- **Notification preferences**

### 14. Subscription & Plan Management
- **Multiple subscription plans**
- **Feature-based access control**
- **Payment gateway integration**
- **Subscription renewal tracking**
- **Plan upgrade/downgrade**

### 15. Website Builder (Additional Feature)
- **Website template management**
- **Company website creation**
- **Template customization**
- **Website enquiry management**

## Database Schema Overview

### Core Tables
1. **users** - User accounts and authentication
2. **companies** - Company/organization data
3. **customers** - Customer information
4. **services** - Service requests and tracking
5. **leads** - Lead management
6. **amc** - Annual maintenance contracts
7. **sales** - Sales orders and transactions
8. **products** - Product catalog
9. **estimations** - Service estimates
10. **expenses** - Expense tracking
11. **invoices** - Invoice management
12. **rma** - Return merchandise authorization

### Supporting Tables
- **service_assigns** - Service to engineer assignments
- **amc_products** - AMC product relationships
- **sales_items** - Sales order line items
- **purchase_orders** - Purchase order management
- **warehouses** - Inventory locations
- **suppliers** - Supplier information
- **brands**, **categories**, **units** - Product attributes
- **permissions**, **roles** - Access control
- **notifications** - System notifications
- **reminders** - Automated reminders
- **tickets** - Support ticket system

## Technology Stack Details

### Backend Dependencies
```json
{
  "php": "^7.3|^8.0",
  "laravel/framework": "^8.75",
  "tymon/jwt-auth": "dev-develop",
  "spatie/laravel-permission": "^6.3",
  "spatie/laravel-medialibrary": "^9.6",
  "barryvdh/laravel-dompdf": "^2.1",
  "maatwebsite/excel": "^3.1",
  "darkaonline/l5-swagger": "^8.5",
  "league/flysystem-aws-s3-v3": "*",
  "phonepe/phonepe-pg-php-sdk": "^1.0",
  "google/apiclient": "^2.18"
}
```

### Frontend Dependencies
```json
{
  "vue": "^3.4.21",
  "vue-router": "^4.3.2",
  "vuex": "^4.1.0",
  "axios": "^1.7.2",
  "tailwindcss": "^3.4.3",
  "chart.js": "^4.4.3",
  "vue-chartjs": "^5.3.1",
  "firebase": "^11.3.1",
  "jspdf": "^1.5.3",
  "xlsx": "^0.18.5",
  "qrcode": "^1.5.4",
  "jsbarcode": "^3.11.6"
}
```

## Key Integrations

### 1. Firebase Integration
- **Cloud Messaging** for push notifications
- **Authentication** (optional)
- **Real-time database** for live updates

### 2. Payment Gateway
- **PhonePe** payment integration
- **Subscription management**
- **Payment tracking**

### 3. Communication Services
- **WhatsApp Business API**
- **SMS gateway integration**
- **Email services**

### 4. File Storage
- **AWS S3** for file uploads
- **Local storage** for development
- **Media library** for file management

### 5. PDF Generation
- **Backend**: DomPDF for server-side PDF generation
- **Frontend**: jsPDF for client-side PDF creation

## Security Features

### 1. Authentication & Authorization
- **JWT token-based authentication**
- **Role-based access control**
- **Permission-based authorization**
- **Session management**
- **Password encryption**

### 2. Data Protection
- **Input validation and sanitization**
- **SQL injection prevention**
- **XSS protection**
- **CSRF protection**
- **Rate limiting**

### 3. API Security
- **API authentication**
- **Request validation**
- **Error handling**
- **Logging and monitoring**

## Performance Optimizations

### 1. Frontend Optimizations
- **Code splitting** with Vite
- **Lazy loading** of components
- **Image optimization**
- **Caching strategies**
- **PWA capabilities**

### 2. Backend Optimizations
- **Database indexing**
- **Query optimization**
- **Caching** (Redis/Memcached)
- **API response optimization**
- **File compression**

## Development Environment Setup

### Prerequisites
- **PHP 8.0+**
- **Node.js 16+**
- **MySQL 8.0+**
- **Composer**
- **NPM/Yarn**

### Backend Setup
1. Clone repository
2. Install dependencies: `composer install`
3. Configure environment: `.env` setup
4. Generate application key: `php artisan key:generate`
5. Run migrations: `php artisan migrate --seed`
6. Generate JWT secret: `php artisan jwt:secret`
7. Start server: `php artisan serve`

### Frontend Setup
1. Navigate to frontend directory
2. Install dependencies: `npm install`
3. Configure environment variables
4. Start development server: `npm run dev`
5. Build for production: `npm run build`

## Deployment Considerations

### 1. Server Requirements
- **Web server**: Apache/Nginx
- **PHP**: 8.0+ with required extensions
- **Database**: MySQL 8.0+
- **Storage**: AWS S3 or local storage
- **SSL certificate** for HTTPS

### 2. Environment Configuration
- **Production environment variables**
- **Database configuration**
- **File storage configuration**
- **Email service configuration**
- **Payment gateway configuration**

### 3. Performance Monitoring
- **Application monitoring**
- **Database performance**
- **Error tracking**
- **User analytics**

This comprehensive guide provides the foundation for recreating the Track New Service Management System. Each module should be implemented incrementally, starting with core authentication and user management, then building out the service management features.
