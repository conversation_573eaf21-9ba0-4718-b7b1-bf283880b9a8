<template>
  <div class="flex items-center justify-center">
    <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
      <h2 class="text-2xl font-semibold text-gray-800 text-center mb-6">Website Registration</h2>

      <form @submit.prevent="registerWebsite" class="space-y-4">

        <div>
          <label for="websiteName" class="block text-gray-700 font-medium">Website Name</label>
          <input type="text" id="websiteName" v-model="websiteName" @input="convertToSlug"
            @blur="checkUsernameAvailability"
            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
            placeholder="Enter website name" required />
        </div>

        <div>
          <label for="username" class="block text-gray-700 font-medium">Website Username</label>
          <input type="text" id="username" v-model="username" @blur="checkUsernameAvailability"
            @keyup.enter="convertToSlug"
            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500"
            placeholder="Enter website username" required />
          <p v-if="usernameMessage"
            :class="{ 'text-green-500': isUsernameAvailable, 'text-red-500': !isUsernameAvailable }"
            class="text-sm mt-1">
            {{ usernameMessage }}
          </p>
        </div>

        <button type="submit" :disabled="!isUsernameAvailable" :class="{
          'bg-blue-500 text-white': isUsernameAvailable,
          'bg-gray-400 text-gray-600 cursor-not-allowed': !isUsernameAvailable
        }" class="w-full py-2 rounded-lg font-semibold transition duration-200">
          Register
        </button>

      </form>
    </div>
  </div>
</template>

<script>
export default {
  name: 'WebsiteRegisterForm',
  props: {
    checkUsername: {
      type: Function,
      required: true
    }
  },
  data() {
    return {
      websiteName: '',
      username: '',
      isUsernameAvailable: null,
      usernameMessage: ''
    };
  },
  watch: {
    websiteName(newValue) {

      this.username = this.slugify(newValue);

    }
  },

  methods: {
    slugify(text) {
      // Convert any string to a slug format
      return text
        .toString()
        .toLowerCase()
        .trim()
        .replace(/[^a-z0-9]+/g, '-')  // Replace non-alphanumeric characters with hyphens
        .replace(/^-+|-+$/g, '');     // Remove leading or trailing hyphens
    },
    convertToSlug() {
      this.username = this.slugify(this.username);
    },
    checkUsernameAvailability() {
      this.checkUsername(this.username).then(isAvailable => {
        this.isUsernameAvailable = isAvailable;
        this.usernameMessage = isAvailable ? 'Username is available' : 'Username is already taken';
      });
    },
    registerWebsite() {

      if (!this.isUsernameAvailable) {
        alert("Username is already taken. Please choose another.");
        return;
      }
      const websiteData = {
        websiteName: this.websiteName,
        username: this.username
      };

      this.$emit('registrationComplete', websiteData);
    }
  }
};
</script>

<style scoped>
/* You can add additional custom styling if needed */
</style>