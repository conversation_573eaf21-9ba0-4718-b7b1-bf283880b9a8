<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-display relative"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <div class="flex justify-between items-center w-full px-4 py-3 set-header-background">
                <p for="leadFilter" class="text-white text-center flex justify-end ml-3"> {{ page }} Filter</p>
                <p class="close" @click="closeModal">&times;</p>

            </div>
            <div class="block mt-1 text-sm bg pl-4 pr-4 pb-4 mt-5">
                <div v-if="selectedByValue === 'Custom'" class="text-xs text-green-600">
                    <p>Note: At least fill in any one field..!</p>
                </div>
                <!---Date-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'" class="flex justify-end">
                    <button @click="resetTheValues(['from', 'to'])"
                        class="absolute text-xs -mt-5 rounded rounded-full text-red-700"><font-awesome-icon
                            icon="fa-solid fa-xmark" /></button>
                </div>
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Date'"
                    class="grid grid-cols-2 gap-4">
                    <!-- From Date -->
                    <div class="relative">
                        <label for="fromDate"
                            class="text-sm absolute left-2 -top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700 transition-top linear duration-300"
                            :class="{ 'text-blue-700': isInputFocused.from }">
                            From Date
                        </label>

                        <input type="date" v-datepicker id="fromDate" v-model="formValues.from" :max="formValues.to"
                            @change="updateMinToDate"
                            class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full" />
                    </div>

                    <!-- To Date -->
                    <div class="relative">
                        <label for="toDate"
                            class="absolute left-2 -top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700 transition-top linear duration-300"
                            :class="{ 'text-blue-700': isInputFocused.to }">
                            To Date
                        </label>
                        <!-- @input="validateDates" -->
                        <input type="date" v-datepicker id="toDate" v-model="formValues.to" :min="minDate"
                            :max="maxDate"
                            class="border col-span-2 px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 w-full" />
                    </div>
                </div>
                <!--Customer-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Customer'" class="mt-5">
                    <div v-if="formValues.customer" class="flex justify-end ">
                        <button @click="resetTheValue('customer')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="relative flex w-full mr-2">
                        <label for="customer"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top linear duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': formValues.customer || isInputFocused.customer, 'text-blue-700': isInputFocused.customer }">Customer<span
                                v-if="formValues.customer || isInputFocused.customer"
                                class="text-red-600">*</span></label>
                        <input id="customer" v-model="formValues.customer" @input="handleDropdownInput(fields)"
                            @focus="isDropdownOpen = true, isInputFocused.customer = true"
                            @blur="closeDropdown('customer')"
                            @keydown.enter="handleEnterKey('customer', filteredCustomerOptions)"
                            @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                            @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="customer"
                            class="w-full border py-2 pl-2 pr-10 leading-5 text-gray-900 focus:ring-0 rounded rounded-tr-none rounded-br-none outline-none" />

                        <!-- Right-aligned icon based on isDropdownOpen -->
                        <span class="absolute py-2 ml-[80%] sm:ml-[78%] lg:ml-[85%] cursor-pointer"
                            @click="isDropdownOpen = !isDropdownOpen">
                            <font-awesome-icon v-if="!isDropdownOpen" icon="fa-solid fa-angle-up" />
                            <font-awesome-icon v-else icon="fa-solid fa-angle-down" />
                        </span>
                    </div>
                </div>
                <!-- Display filtered options as the user types -->
                <div v-if="isDropdownOpen && formValues.customer && formValues.customer.length > 1"
                    class="absolute mt-1 max-h-60 w-3/4 ml-10 overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm"
                    style="z-index: 100;" @mousedown.prevent="preventBlur('customer')">
                    <p v-for="(option, index) in filteredCustomerOptions" :key="index"
                        @click="selectDropdownOption(option)" :class="{ 'bg-gray-200': index === selectedIndex }"
                        class="cursor-pointer hover:bg-gray-100 p-2 border-b">
                        {{ option.firstName + (option.lastName ? ' ' + option.lastName : '') }} - {{
                            option.contactNumber }}
                    </p>
                </div>
                <!--type-->
                <!-- <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by category/Type'" class="flex items-center mt-5 border">
                    <div class="w-full mr-2 relative">
                        <label for="type"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': isInputFocused.type, 'text-blue-700': isInputFocused.type }">
                            Type
                        </label>
                        <div class="flex py-4 px-2 items-center">
                            <button v-for="(data, index) in typeList" :key="index"
                                class="flex items-center bg-gray-200 py-2 px-3 rounded mr-3"
                                :class="{ 'bg-gray-700': formValues['type'] === index }"
                                @click="selectStatusOption(data, 'type', index)">
                                <span class="text-sm text-gray-500"
                                    :class="{ 'text-white': formValues['type'] === index }">
                                    {{ data }}
                                </span>
                            </button>
                        </div>
                    </div>
                </div> -->
                <!--status-->
                <div v-if="selectedByValue === 'Custom' || selectedByValue === 'by Status'"
                    class="items-center mt-5 border">
                    <div v-if="formValues.status >= 0" class="flex justify-end ">
                        <button @click="resetTheValue('status')"
                            class="absolute text-xs  -mt-5 rounded rounded-full text-red-700">&#128940;</button>
                    </div>
                    <div class="w-full mr-2 relative">
                        <label for="type"
                            class="text-sm font-bold absolute left-2 top-3 text-gray-300 transition-top duration-300"
                            :class="{ '-top-3 sm:-top-2 text-xs bg-white px-1 text-gray-700': isInputFocused.status, 'text-blue-700': isInputFocused.status }">
                            Status
                        </label>
                        <div class="flex py-4 px-2 items-center">
                            <button v-for="(data, index) in statusList" :key="index"
                                class="flex items-center bg-gray-200 py-2 px-3 rounded mr-3"
                                :class="{ 'bg-gray-700': formValues['status'] === index }"
                                @click="selectStatusOption(data, 'status', index)">
                                <span class="text-sm text-gray-500"
                                    :class="{ 'text-white': formValues['status'] === index }">{{ data }}</span>
                            </button>
                        </div>
                    </div>
                </div>
                <!-- Buttons -->
                <div class="flex justify-center items-center m-2 mt-5">
                    <button @click="closeModal"
                        class="bg-red-700 hover:bg-red-600 rounded-[30px] text-white  px-4 mr-8 py-2">Cancel</button>
                    <button v-if="showResetButton && selectedByValue === 'Custom'" @click="resetAll"
                        class="bg-blue-700 hover:bg-blue-600 rounded-[30px] text-white px-4 mr-8 py-2">Reset
                        All</button>
                    <button @click="submitFilter"
                        class="bg-green-700 hover:bg-green-600 rounded-[30px] text-white px-4 mr-8 py-2">Submit</button>
                </div>
            </div>
        </div>
    </div>
</template>


<script>
export default {
    props: {
        showModal: Boolean,
        typeList: Object,
        statusList: Object,
        selectedByValue: String,
        page: String,
    },
    data() {
        return {
            close_icon: '/images/service_page/close.png',
            isOpen: false,
            selectedSalesType: 0,
            formValues: {},
            customer_list: [],
            isInputFocused: { date: true, type: true, status: true },
            isDropdownOpen: false,
            selectedIndex: 0,
            showOptions: false,
            search: '',
            maxDate: new Date().toISOString().split('T')[0], // current date
            minDate: '',
        };
    },
    methods: {
        closeModal() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter');
            }, 300);
        },
        //--submit---
        submitFilter() {
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('closeFilter', this.formValues);
                this.formValues = {};
            }, 300);
        },

        //----Customer----
        handleDropdownInput() {
            // console.log(this.customer_list, 'This is customer list..@')
            this.isInputFocused.customer = true;
            const inputValue = this.formValues.customer;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 1) {
                // console.log('Waht happening....!', this.isDropdownOpen);
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contactNumber.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.lastName ? (option.firstName + ' ' + option.lastName + ' - ' + option.contactNumber).toLowerCase().includes(inputName) :
                            (option.firstName + ' - ' + option.contactNumber).toLowerCase().includes(inputName)
                    );
                }
                this.isDropdownOpen = !this.isDropdownOpen ? true : this.isDropdownOpen;

            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = this.customer_list;
            }

            // console.log(this.isDropdownOpen && this.formValues.customer && this.formValues.customer.length > 1, 'what happening...!', this.filteredCustomerOptions)
        },
        handleEnterKey(type, optionArray) {
            // Check if filteredProductList has at least one item
            if (type === 'customer') {
                if (optionArray && optionArray.length > 0) {
                    // Call selectedProductData with the first item in filteredProductList
                    this.selectDropdownOption(optionArray[this.selectedIndex])
                    // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                    this.selectedIndex = 0;
                    // this.$refs.enterQuantity.focus();
                } else {
                    // this.openModal();
                    // this.openModal(product_name);
                }
            }
            else if (type === 'multiple') {
                // console.log(optionArray, 'WWWWW', optionArray.length > 0);

                if (optionArray && optionArray.length > 0) {
                    // console.log(optionArray[this.selectedIndex], 'WWWWW');
                    this.selectOptionMultiple(optionArray[this.selectedIndex]);
                    this.selectedIndex = 0;
                }
                else {
                    this.openModalEmployee();
                }
            }
        },
        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else if (this.selectedIndex > 0) {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },
        //----customer dropdown--
        closeDropdown(type) {
            if (type && type === 'customer' && !this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.isDropdownOpen = false;
                this.isInputFocused.customer = false;

            }
        },
        //---customers dropdown
        selectDropdownOption(option) {
            this.formValues.customer = option.firstName + (option.lastName ? ' ' + option.lastName : '') + ' - ' + option.contactNumber;
            this.isDropdownOpen = false; // Close the dropdown
            this.selectedIndex = 0;
        },
        preventBlur(type) {
            if (type && type === 'customer') {
                this.mouseDownOnDropdown = true;
                setTimeout(() => {
                    this.mouseDownOnDropdown = false;
                });
            }
        },
        //---multiple dropdown---
        filterOptions() {
            // console.log(('hello'));
            this.showOptions = true;
            this.isInputFocused.assign_to = true;
            // this.filteredOptions();
        },
        filteredOptions() {
            if (this.search) {
                let enteredValue = this.search.trim(); // Trim any leading or trailing whitespace
                // console.log(enteredValue, 'What happenig...@', this.employeeList, 'llll', /^\d+$/.test(enteredValue), 'pppp', this.formValues.assign_to, 'pppp', enteredValue.length > 1);
                // Check if the entered value contains only digits
                if (/^\d+$/.test(enteredValue)) {
                    // Filter employees based on their contact numbers
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.contactNumber.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }

                    } else {
                        let findArray = this.employeeList.filter(option => option.contactNumber.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                } else if (enteredValue.length > 1) {
                    // Filter employees based on their names if the entered value is not a number
                    if (this.formValues.assign_to) {
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()) && !this.formValues.assign_to.includes(option.name));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    } else {
                        // console.log(this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase())), 'WEWERWRWR');
                        let findArray = this.employeeList.filter(option => option.name.toLowerCase().includes(enteredValue.toLowerCase()));
                        if (findArray.length > 0) {
                            this.showAddNew = null;
                            return findArray;
                        } else {
                            this.showAddNew = true;
                            return [];
                        }
                    }
                }
            }
            // Return an empty array if no options match the filter
            return this.formValues.assign_to ? this.employeeList.filter(option => !this.formValues.assign_to.includes(option.name)) : this.employeeList;
        },

        selectOptionMultiple(option) {
            if (!this.formValues.assign_to) {
                this.formValues.assign_to = []; // Initialize assign_to as an array if it's not already
            }
            this.formValues.assign_to.push(option.name); // Add the selected option to assign_to
            this.search = ''; // Clear the search input
            this.showOptions = false; // Hide the options dropdown
            this.$nextTick(() => {
                // Focus on the search input after selecting an option
                if (this.$refs['search']) {
                    this.$refs['search'].focus();
                    this.$refs['search'].click();
                }
            });
            this.selectedIndex = 0;
        },
        removeOption(option) {
            this.formValues['assign_to'] = this.formValues['assign_to'].filter(selected => selected !== option);
        },

        hideOptions() {
            setTimeout(() => {
                this.showOptions = false;
                this.isInputFocused.assign_to = false;
            }, 300); // Add delay to allow click events on options
        },
        openModalEmployee() {
            this.showModal_employee = true;
            this.EmployeeName = this.search;
            this.showOptions = false;
            this.showAddNew = null;
        },
        handleFocus() {
            // Set focus to the input field
            const inputField = this.$refs.customer;
            if (inputField) {
                inputField.focus();
                inputField.click();
            }
        },
        //---status and type--
        selectStatusOption(option, type, index) {
            if (option && type) {
                this.formValues[type] = index;
            }
        },
        //---reset All---
        resetAll() {
            this.formValues = {};
        },
        //---reset---
        resetTheValue(key) {
            delete this.formValues[key];
        },
        resetTheValues(fields) {
            // Reset specified form fields
            fields.forEach(field => {
                this.formValues[field] = null;
            });
        },

    },
    computed: {
        showResetButton() {
            // Check if formValues is not empty
            return Object.keys(this.formValues).length > 0;
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
            if (this.selectedByValue == 'by Date') {
                const today = new Date().toISOString().substr(0, 10);
                this.maxDate = today;
                this.formValues.from = today;
                this.formValues.to = today;
            }
        },
    },
    mounted() {
        let getCustomer = localStorage.getItem('userData');
        if (getCustomer) {
            let parseCustomer = JSON.parse(getCustomer);
            if (parseCustomer) {
                this.customer_list = parseCustomer;
            }
        }
        let getEmployee = localStorage.getItem('employee');
        if (getEmployee) {
            let parseEmployee = JSON.parse(getEmployee);
            if (parseEmployee) {
                this.employeeList = parseEmployee;
            }
        }
    }
};
</script>

<style scoped>
/* Add your modal styles here */
.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}

.scale-0 {
    transform: scale(0);
}

.scale-100 {
    transform: scale(1);
}

.rotate-0 {
    transform: rotate(0deg);
}

.rotate-720 {
    transform: rotate(720deg);
}
</style>