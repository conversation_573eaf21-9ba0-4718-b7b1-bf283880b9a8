<?php

namespace App\Http\Controllers;

use App\DataTables\lead_typeDataTable;
use App\Http\Requests;
use App\Http\Requests\Createlead_typeRequest;
use App\Http\Requests\Updatelead_typeRequest;
use App\Repositories\lead_typeRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class lead_typeController extends AppBaseController
{
    /** @var lead_typeRepository $leadTypeRepository*/
    private $leadTypeRepository;

    public function __construct(lead_typeRepository $leadTypeRepo)
    {
        $this->leadTypeRepository = $leadTypeRepo;
    }

    /**
     * Display a listing of the lead_type.
     *
     * @param lead_typeDataTable $leadTypeDataTable
     *
     * @return Response
     */
    public function index(lead_typeDataTable $leadTypeDataTable)
    {
        return $leadTypeDataTable->render('lead_types.index');
    }

    /**
     * Show the form for creating a new lead_type.
     *
     * @return Response
     */
    public function create()
    {
        return view('lead_types.create');
    }

    /**
     * Store a newly created lead_type in storage.
     *
     * @param Createlead_typeRequest $request
     *
     * @return Response
     */
    public function store(Createlead_typeRequest $request)
    {
        $input = $request->all();

        $leadType = $this->leadTypeRepository->create($input);

        Flash::success('Lead Type saved successfully.');

        return redirect(route('leadTypes.index'));
    }

    /**
     * Display the specified lead_type.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $leadType = $this->leadTypeRepository->find($id);

        if (empty($leadType)) {
            Flash::error('Lead Type not found');

            return redirect(route('leadTypes.index'));
        }

        return view('lead_types.show')->with('leadType', $leadType);
    }

    /**
     * Show the form for editing the specified lead_type.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $leadType = $this->leadTypeRepository->find($id);

        if (empty($leadType)) {
            Flash::error('Lead Type not found');

            return redirect(route('leadTypes.index'));
        }

        return view('lead_types.edit')->with('leadType', $leadType);
    }

    /**
     * Update the specified lead_type in storage.
     *
     * @param int $id
     * @param Updatelead_typeRequest $request
     *
     * @return Response
     */
    public function update($id, Updatelead_typeRequest $request)
    {
        $leadType = $this->leadTypeRepository->find($id);

        if (empty($leadType)) {
            Flash::error('Lead Type not found');

            return redirect(route('leadTypes.index'));
        }

        $leadType = $this->leadTypeRepository->update($request->all(), $id);

        Flash::success('Lead Type updated successfully.');

        return redirect(route('leadTypes.index'));
    }

    /**
     * Remove the specified lead_type from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $leadType = $this->leadTypeRepository->find($id);

        if (empty($leadType)) {
            Flash::error('Lead Type not found');

            return redirect(route('leadTypes.index'));
        }

        $this->leadTypeRepository->delete($id);

        Flash::success('Lead Type deleted successfully.');

        return redirect(route('leadTypes.index'));
    }
}
