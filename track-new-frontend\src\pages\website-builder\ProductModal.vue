<template>
  <div v-if="showModal" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50  text-sm">
    <div class="bg-white p-6 rounded-lg shadow-lg w-full lg:w-3/4 h-screen overflow-y-auto">
      <div class="web-modal-head">
        <h2 class="font-bold">{{ isEditMode ? 'Edit Product' : 'Add Product' }}</h2>
        <button @click="close" class="text-red-600 hover:text-red-500"><span><font-awesome-icon
              icon="fa-solid fa-xmark" /></span></button>
      </div>
      <!-- Product Title -->
      <!-- <div class="flex gap-4 mb-4"> -->
      <div class="mb-1">
        <label class="block text-gray-600 text-sm mb-1">Product Title <span class="text-red-600">*</span></label>
        <input type="text" v-model="productData.title" placeholder="Enter title here" class="border p-2 rounded w-full"
          required />
      </div>
      <!-- Product Short Description -->
      <div class="mb-1">
        <label class="block text-gray-600 text-sm mb-1">Product Short Description <span
            class="text-red-600">*</span></label>
        <!-- <input type="text" v-model="productData.shortDescription" placeholder="Enter short description here"
          class="border p-2 rounded w-full" /> -->
        <CkEditorForm label="Product Short Description" :textData="productData.shortDescription || ''"
          @editorSubmit="handleEditorSubmitShort" :company_id="company_id"></CkEditorForm>
      </div>
      <!-- </div> -->

      <!-- Product Description -->
      <div class="mb-1">
        <label class="block text-gray-600 text-sm mb-1">Product Description <span class="text-red-600">*</span></label>
        <!-- <textarea v-model="productData.description" placeholder="Enter brief description here"
          class="border p-2 rounded w-full" rows="3"></textarea> -->
        <CkEditorForm label="Product Long Description" :textData="productData.description || ''"
          @editorSubmit="handleEditorSubmitLong" :company_id="company_id"></CkEditorForm>
      </div>
      <!-- Product Images Upload -->
      <div class="mb-1">
        <label class="block text-gray-600 text-sm mb-1">Product Images
          <span class="text-gray-500 text-xs">500 <font-awesome-icon icon="fa-solid fa-xmark" /> 500</span>
        </label>
        <input type="file" @change="onFileChange" ref="productImg" accept="image/png, image/jpeg, image/webp"
          class="border p-2 rounded w-full" multiple />
        <div v-if="circle_loader_photo"
          class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
          <CircleLoader :loading="true"></CircleLoader>
        </div>
        <div v-if="productData.imageUrl && !Array.isArray(productData.imageUrl)" class="mt-4 relative">
          <p class="text-xs text-gray-500 mt-1">Upload format: png, webp, jpg</p>
          <img v-if="productData.imageUrl" :src="productData.imageUrl" alt="Image Preview"
            class="w-full h-16 object-cover rounded-lg" />
          <button @click="removeImage"
            class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
            &times;
          </button>
        </div>
        <!-- Image Preview List -->
        <div v-if="productData.imageUrl && Array.isArray(productData.imageUrl) && productData.imageUrl.length"
          class="space-y-3">
          <p class="text-xs text-gray-500 mt-1">Upload format: png, webp, jpg (Max 4 images & size 500 * 500)</p>
          <div v-for="(image, index) in productData.imageUrl" :key="index"
            class="relative inline-block mb-2 mr-2 w-24 h-24 px-2" draggable="true"
            @dragstart="onDragStart(index, $event)" @dragover="onDragOver($event)" @drop="onDrop(index, $event)"
            @dragenter.prevent @dragleave.prevent>
            <img :src="image" alt="Image Preview" class="w-full h-full object-cover rounded-lg" />

            <button @click="removeImage(index)"
              class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
              &times;
            </button>
          </div>
        </div>
      </div>
      <!---product videos-->
      <div class="mb-1 space-y-2">
        <label class="block text-gray-600 text-sm">Product video URL
          <span class="text-xs">(Give public video access)</span>
          <!-- <span class="text-xs">(duration: 1 min & size:less than 5MB)</span> -->
        </label>
        <!-- <div v-if="circle_loader_video"
          class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
          <CircleLoader :loading="true"></CircleLoader>
        </div>
        <input type="file" @change="onVideoUpload" ref="productVideo" accept="video/mp4,video/webm,video/ogg"
          class="border p-2 rounded w-full" /> -->
        <!-- Input field for the video URL -->
        <div class="flex">
          <input type="url" ref="productVideo" v-model="productData.product_video"
            placeholder="Enter video URL (e.g., YouTube, Google Drive, etc.)" class="border p-2 rounded-l w-full" />

          <!-- Button to open the video -->
          <div v-if="productData.product_video && productData.product_video !== ''" class="flex ">
            <button @click="viewVideo" class="bg-blue-500 text-white p-1 px-2 rounded-r shadow hover:bg-blue-600">
              <font-awesome-icon icon="fa-solid fa-eye" />
            </button>
          </div>
        </div>
      </div>

      <!---product brochure-->
      <div class="mb-1 space-y-2">
        <label class="block text-gray-600 text-sm">Product Brochure (<span class="text-gray-500 text-xs">Max
            2MB</span>)</label>
        <div class="flex">
          <input type="file" ref="brochurefile" @change="handleFileUploadBrochure"
            class="border p-2 rounded-l w-full" />
          <div v-if="circle_loader_brochure"
            class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
            <CircleLoader :loading="true"></CircleLoader>
          </div>
          <!-- Button to open the brochure -->
          <div v-if="productData.product_brochure && productData.product_brochure !== ''" class="flex ">
            <button @click="viewBrochure" class="bg-blue-500 text-white p-1 px-2 rounded-r shadow hover:bg-blue-600">
              <font-awesome-icon icon="fa-solid fa-eye" />
            </button>
          </div>
        </div>
      </div>

      <!-- Price and Units -->
      <div class="flex gap-4 mb-4">
        <div class="flex-1">
          <label class="block text-gray-600 text-sm mb-1">Price (Optional)</label>
          <input type="number" v-model="productData.price" placeholder="Enter Price"
            class="border p-2 rounded w-full" />
        </div>
        <div class="flex-1">
          <label class="block text-gray-600 text-sm mb-1">Units Per </label>
          <input type="text" v-model="productData.unit" placeholder="Enter Price unit"
            class="border p-2 rounded w-full" />
          <!-- <select v-model="productData.unit" class="border p-2 rounded w-full">
            <option disabled value="">Select unit</option>
            <option>Piece</option>
            <option>Box</option>
            <option>Kg</option>
          </select> -->
        </div>
      </div>
      <div class="flex-1">
        <label class="block text-gray-600 text-sm mb-1">Product category</label>
        <div class="flex">
          <select v-model="productData.category" class="border p-2 rounded w-full">
            <option disabled value="">Select category</option>
            <option v-for="opt in categories" :key="opt.id" :value="opt.id">
              {{ opt.name }}
            </option>
          </select>
          <button @click="openAddCategoryModal"
            class="bg-green-600 text-white px-2 rounded-r hover:bg-green-500"><span><font-awesome-icon
                icon="fa-solid fa-plus" /></span></button>
        </div>
      </div>

      <!-- Action Buttons -->
      <div class="flex justify-end gap-4 mt-6">
        <button @click="close" class="px-4 py-2 bg-gray-200 rounded shadow">Cancel</button>
        <button @click="save" class="px-4 py-2 bg-blue-500 text-white rounded shadow">Save</button>
      </div>
    </div>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <Loader :showModal="open_loader"></Loader>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    <!-- Categories Modal -->
    <productCategoryModal :show="isModalVisible" :categories="categories" @close="closeModal" @save="saveCategory"
      @toasterMessages="toasterMessages" />
    <dialogConfirmBox :visible="show_dialog" :message="message" :type="'website'" @ok="closeModalconfirm"
      @cancel="cancelcloseModal" @save="save"></dialogConfirmBox>
  </div>
</template>
<script>
import imageService from '../../services/imageService';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import productCategoryModal from './productCategoryModal.vue';
import CkEditorForm from './CkEditorForm.vue';
import { mapActions, mapGetters } from 'vuex';
import dialogConfirmBox from '@/components/supporting/dialog_box/dialogConfirmBox.vue';
export default {
  props: {
    company_id: {
      type: String,
      required: true
    },
    showModal: {
      type: Boolean,
      required: true
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    product: {
      type: Object,
      // default: () => ({
      //   title: '',
      //   description: '',
      //   shortDescription: '',
      //   imageUrl: null,
      //   price: null,
      //   unit: '',
      //   link: '',
      //   category: 100,
      //   product_video: ''
      // })
      required: false,
    },
    categories: {
      type: Object,
      default: () => [{ id: 100, name: 'General' }]
    }
  },
  components: {
    confirmbox,
    productCategoryModal,
    CkEditorForm,
    dialogConfirmBox
  },
  data() {
    return {
      productData: {}, // Local copy of the product data
      //--loader--
      circle_loader_photo: false,
      circle_loader_video: false,
      circle_loader_brochure: false,
      open_loader: false,
      //--toaster---
      show: false,
      type_toaster: 'success',
      message: '',
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      //--category modal--
      isModalVisible: false,
      //--url link---
      isVideoValid: false, // Tracks if the video URL is valid
      isEmbeddable: false, // Tracks if the video is embeddable (YouTube, etc.)
      embeddedUrl: '', // URL for embedding (YouTube iframe or others)
      videoError: '', // Stores any error message
      //--confirm box dialog--
      show_dialog: false,
    };
  },
  computed: {
    ...mapGetters('websiteBuilder', ['isModified']),
  },
  mounted() {

    if (this.product && this.isEditMode) {

      this.productData = { ...this.product };
    }
    if (this.productData && this.productData.imageUrl && !Array.isArray(this.productData.imageUrl)) {
      let makeArr = [];
      makeArr.push(this.productData.imageUrl);
      this.productData.imageUrl = makeArr;
    }
    // else if (!this.isEditMode && !this.product) {
    //   this.productData = { title: '', description: '', shortDescription: '', imageUrl: null, price: null, unit: '', link: '', category: 100, product_video: '' };
    // }
  },
  watch: {
    product: {
      deep: true,
      handler(newProduct) {
        if (newProduct) {
          this.productData = { ...newProduct }; // Update productData when product prop changes      
          if (this.productData && this.productData.imageUrl && !Array.isArray(this.productData.imageUrl)) {
            let makeArr = [];
            makeArr.push(this.productData.imageUrl);
            this.productData.imageUrl = makeArr;
          }
        }
      }
    },
    productData: {
      deep: true,
      handler(newValue) {
        // console.log(this.product, 'hello .......', newValue);
        if (newValue) {
          this.validateForm([this.product, newValue]);
        }
      }
    },
    showModal: {
      deep: true,
      handler(newValue) {
        if (newValue && Object.keys(this.productData).length === 0) {
          this.productData = {
            title: '',
            description: '',
            shortDescription: '',
            imageUrl: null,
            price: null,
            unit: '',
            link: '',
            category: '',
            product_video: ''
          };
        }
        if (newValue) {
          this.show = false;
        }
      }
    }
    // 'productData.title': function (newTitle) {
    //   // Update link only if it's empty (either on add or edit)
    //   if (!this.productData.link && newTitle) {
    //     this.productData.link = this.generateSlug(newTitle);
    //   }
    // }
  },

  methods: {
    ...mapActions('websiteBuilder', ['validateForm']),
    generateSlug(title) {
      // Replace spaces with dashes, remove special characters, and convert to lowercase
      return title
        .toLowerCase()
        .trim()
        .replace(/[\s]+/g, '-')
        .replace(/[^\w-]+/g, '');
    },
    // async onFileChange(event) {
    //   this.circle_loader_photo = true;
    //   const file = event.target.files[0];
    //   if (file) {
    //     this.imageFile = file; // Store the file for upload
    //     try {
    //       // Upload the image and store the returned URL
    //       const response = await imageService.uploadImage(file, 'products', this.company_id);
    //       this.productData.imageUrl = response.media_url; // Use response URL for image
    //       this.circle_loader_photo = false;
    //       this.toasterMessages({ msg: 'Image uploaded successfully!', type: 'success' });
    //     } catch (error) {
    //       console.error('Error uploading image:', error);
    //       this.toasterMessages({ msg: 'Failed to upload image.', type: 'warning' });
    //       this.circle_loader_photo = false;
    //     }
    //   }
    // },
    async onFileChange(event) {
      this.circle_loader_photo = true;
      const files = event.target.files; // Get all selected files

      if (files.length + (this.productData.imageUrl ? this.productData.imageUrl.length : 0) > 4) {
        this.toasterMessages({ msg: 'You can upload a maximum of 4 images.', type: 'warning' });
        this.circle_loader_photo = false;
        this.$refs.productImg.value = '';
        return;
      }

      const newImages = [];
      try {
        // Loop over each file and upload them one by one
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          if (file) {

            // Upload the image and store the returned URL
            const response = await imageService.uploadImage(file, 'products', this.company_id);
            newImages.push(response.media_url); // Push each image URL to newImages array

          }
        }
        this.toasterMessages({ msg: 'Image uploaded successfully!', type: 'success' });
      } catch (error) {
        console.error('Error uploading image:', error);
        this.toasterMessages({ msg: 'Failed to upload image.', type: 'warning' });
      }

      // Add uploaded images to the productData array
      if (newImages.length > 0 && Array.isArray(this.productData.imageUrl)) {
        this.productData.imageUrl.push(...newImages); // Add new images to existing images
      } else if (newImages.length > 0 && !Array.isArray(this.productData.imageUrl)) {
        this.productData.imageUrl = [];
        this.productData.imageUrl.push(...newImages);
      }
      this.circle_loader_photo = false;
    },
    async removeImage(index) {
      this.deleteIndex = index;
      this.open_confirmBox = true;
    },
    // Drag Start: Store the index of the item being dragged
    onDragStart(index, event) {
      event.dataTransfer.setData('text/plain', index);
    },

    // Allow drop by preventing the default action
    onDragOver(event) {
      event.preventDefault();
    },

    // Drop: Reorder the image array when dropping
    onDrop(index, event) {
      const draggedIndex = event.dataTransfer.getData('text/plain');
      const draggedImage = this.productData.imageUrl[draggedIndex];

      // Move the dragged image to the new position
      this.productData.imageUrl.splice(draggedIndex, 1);
      this.productData.imageUrl.splice(index, 0, draggedImage);
    },
    async save() {
      if (this.productData.title && this.productData.description && this.productData.shortDescription) {
        // Save the image URL only on product save
        // if (this.tempImageUrl) {
        //   this.productData.imageUrl = this.tempImageUrl;
        // }
        if (!this.productData.link) {
          this.productData.link = this.generateSlug(this.productData.title);
        }
        this.$emit('save', this.productData); // Emit save event with product data
        this.resetForm(true);
        this.$emit('close');
      } else {
        this.toasterMessages({ msg: 'Please fill in all required fields', type: 'warning' });
      }
      this.show_dialog = false;
    },
    close() {
      if (!this.isModified) {
        this.message = 'You have unsaved changes. Do you want to save them or ok or cancel?'
        this.show_dialog = true;
      } else {
        this.resetForm();
        this.$emit('close');

      }
    },
    closeModalconfirm() {
      this.show_dialog = false;
      this.resetForm();
      this.$emit('close'); // Emit close event to hide modal
    },
    cancelcloseModal() {
      this.show_dialog = false;
      // this.$emit('close');
    },
    resetForm(istrue) {
      if (this.isEditMode || istrue) {
        this.productData = {};
      }
    },
    async deleteRecord() {
      if (this.productData.imageUrl) {
        this.open_loader = true;
        let imageUrl = Array.isArray(this.productData.imageUrl) ? this.productData.imageUrl[this.deleteIndex] : this.productData.imageUrl;
        try {
          await imageService.deleteImage(imageUrl, 'products'); // Delete image from server

          if (Array.isArray(this.productData.imageUrl)) {
            this.productData.imageUrl.splice(this.deleteIndex, 1);
          } else {
            this.productData.imageUrl = null; // Clear image URL
          }
          this.imageFile = null; // Clear image file
          this.open_loader = false;
          this.$refs.productImg.value = '';
          this.toasterMessages({ msg: 'Image removed successfully.', type: 'success' });
          this.closeconfirmBoxData();
        } catch (error) {
          console.error('Error deleting image:', error);
          this.toasterMessages({ msg: 'Failed to delete image', type: 'warning' });
          if (error.response.data && error.response.data.error === 'Image not found') {
            if (Array.isArray(this.productData.imageUrl) && this.deleteIndex >= 0) {
              this.productData.imageUrl.splice(this.deleteIndex, 1);
            } else {
              this.productData.imageUrl = null; // Clear image URL
            }
          }
          this.open_loader = false;
          this.closeconfirmBoxData();
        }
      } else {
        this.closeconfirmBoxData();
      }
    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
      this.open_loader = false;
    },
    //--toaster message---
    toasterMessages(data) {
      this.message = data.msg;
      this.type_toaster = data.type;
      this.show = true;
    },
    //--category moadl---
    openAddCategoryModal() {
      this.isModalVisible = true;
    },
    closeModal() {
      this.isModalVisible = false;
    },
    saveCategory(data) {
      if (data && data.length > 0) {
        this.$emit('updatecategory', data);
        this.toasterMessages({ msg: 'Categories save successfully', type: 'success' });
        this.isModalVisible = false;
      }
    },
    //--video upload--
    async onVideoUpload(event) {
      const file = event.target.files[0]; // Get the uploaded file
      if (!file) {
        this.toasterMessages({ msg: 'Please select a video.', type: 'warning' });
        return;
      }
      this.circle_loader_video = true;
      try {
        // Validate file size
        if (file.size > 5 * 1024 * 1024) {
          this.toasterMessages({ msg: 'Video size exceeds 5MB. Please upload a smaller video.', type: 'warning' });
          this.circle_loader_video = false;
          return;
        }

        // Validate video duration
        const isValidDuration = await this.validateVideoDuration(file);
        if (!isValidDuration) {
          this.toasterMessages({ msg: 'Video duration exceeds 1 minute. Please upload a shorter video.', type: 'warning' });
          this.circle_loader_video = false;
          return;
        }

        // Proceed to upload if both validations pass
        const response = await imageService.uploadImage(file, 'products', this.company_id);
        if (response && response.media_url) {
          this.productData.product_video = response.media_url;
          this.toasterMessages({ msg: 'Video uploaded successfully.', type: 'success' });
        } else {
          this.toasterMessages({ msg: 'Failed to upload video.', type: 'warning' });
        }
      } catch (error) {
        console.error('Error uploading video:', error);
        this.toasterMessages({ msg: 'An error occurred during video upload.', type: 'error' });
      } finally {
        this.circle_loader_video = false;
      }
    },

    validateVideoDuration(file) {
      return new Promise((resolve) => {
        const video = document.createElement("video");
        video.preload = "metadata";
        video.onloadedmetadata = () => {
          URL.revokeObjectURL(video.src); // Release object URL
          const duration = video.duration;
          resolve(duration <= 60); // Return true if duration <= 60 seconds
        };
        video.onerror = () => {
          console.error('Error loading video metadata for duration validation.');
          resolve(false); // Handle invalid video file
        };
        video.src = URL.createObjectURL(file);
      });
    },
    //--video--
    viewVideo() {
      if (this.productData.product_video) {
        window.open(this.productData.product_video, '_blank'); // Open the video URL in a new tab
      } else {
        alert('No video URL entered.'); // Optional alert if no URL is provided
      }
    },
    //--brochure---
    viewBrochure() {
      if (this.productData.product_brochure) {
        window.open(this.productData.product_brochure, '_blank'); // Open the brochure URL in a new tab
      } else {
        alert('No video URL entered.');
      }
    },
    //--brochure upload----
    async handleFileUploadBrochure(event) {
      if (this.productData.product_brochure && this.productData.product_brochure !== '') {
        const response = await imageService.deleteImage(this.productData.product_brochure, 'products');
        this.productData.product_brochure = '';
      }
      this.circle_loader_brochure = true;
      const file = event.target.files[0]; // Get the single selected file
      if (file) {
        try {
          // Upload the file and add it to the brochure list
          const response = await imageService.uploadFile(file, 'products', this.companyId);
          if (response.media_url) {
            this.productData.product_brochure = response.media_url;
            this.$refs.brochurefile.value = '';
            this.toasterMessages({ msg: `Brochure "${file.name}" uploaded successfully!`, type: 'success' });
            // this.$emit('save', this.productData);
          }
        } catch (error) {
          console.error('Error uploading brochure:', error);
          this.toasterMessages({ msg: 'Failed to upload brochure.', type: 'warning' });
        } finally {
          this.circle_loader_brochure = false;
        }
      } else {
        this.toasterMessages({ msg: 'Please select a brochure file to upload.', type: 'warning' });
        this.circle_loader_brochure = false;
      }
    },
    //--descriptions--
    handleEditorSubmitShort(data) {
      if (data) {
        this.productData.shortDescription = data;
      } else {
        this.productData.shortDescription = '';
      }
    },
    handleEditorSubmitLong(data) {
      if (data) {
        this.productData.description = data;
      } else {
        this.productData.description = '';
      }
    }
  }
};
</script>