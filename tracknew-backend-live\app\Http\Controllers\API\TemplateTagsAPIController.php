<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateTemplateTagsAPIRequest;
use App\Http\Requests\API\UpdateTemplateTagsAPIRequest;
use App\Models\TemplateTags;
use App\Repositories\TemplateTagsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class TemplateTagsController
 * @package App\Http\Controllers\API
 */

class TemplateTagsAPIController extends AppBaseController
{
    /** @var  TemplateTagsRepository */
    private $templateTagsRepository;

    public function __construct(TemplateTagsRepository $templateTagsRepo)
    {
        $this->templateTagsRepository = $templateTagsRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/templateTags",
     *      summary="getTemplateTagsList",
     *      tags={"TemplateTags"},
     *      description="Get all TemplateTags",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/TemplateTags")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $templateTags = $this->templateTagsRepository->allWith(
                            ['templates'], 
                            ['status' => 1], 
                            $request->get('skip'),
                            $request->get('limit')
                        );

        return $this->sendResponse($templateTags->toArray(), 'Template Tags retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/templateTags",
     *      summary="createTemplateTags",
     *      tags={"TemplateTags"},
     *      description="Create TemplateTags",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/TemplateTags"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateTemplateTagsAPIRequest $request)
    {
        $input = $request->all();

        $templateTags = $this->templateTagsRepository->create($input);

        return $this->sendResponse($templateTags->toArray(), 'Template Tags saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/templateTags/{id}",
     *      summary="getTemplateTagsItem",
     *      tags={"TemplateTags"},
     *      description="Get TemplateTags",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of TemplateTags",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/TemplateTags"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var TemplateTags $templateTags */
        $templateTags = $this->templateTagsRepository->find($id);

        if (empty($templateTags)) {
            return $this->sendError('Template Tags not found');
        }

        return $this->sendResponse($templateTags->toArray(), 'Template Tags retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/templateTags/{id}",
     *      summary="updateTemplateTags",
     *      tags={"TemplateTags"},
     *      description="Update TemplateTags",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of TemplateTags",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/TemplateTags"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateTemplateTagsAPIRequest $request)
    {
        $input = $request->all();

        /** @var TemplateTags $templateTags */
        $templateTags = $this->templateTagsRepository->find($id);

        if (empty($templateTags)) {
            return $this->sendError('Template Tags not found');
        }

        $templateTags = $this->templateTagsRepository->update($input, $id);

        return $this->sendResponse($templateTags->toArray(), 'TemplateTags updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/templateTags/{id}",
     *      summary="deleteTemplateTags",
     *      tags={"TemplateTags"},
     *      description="Delete TemplateTags",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of TemplateTags",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var TemplateTags $templateTags */
        $templateTags = $this->templateTagsRepository->find($id);

        if (empty($templateTags)) {
            return $this->sendError('Template Tags not found');
        }

        $templateTags->delete();

        return $this->sendSuccess('Template Tags deleted successfully');
    }
}
