// store/modules/auth.js

const state = {
    isAuthenticated: false, // Initial authentication state
    user: null // Initially no user data
  };

  const mutations = {
    SET_AUTHENTICATED(state, isAuthenticated) {
      state.isAuthenticated = isAuthenticated;
    },
    SET_USER(state, user) {
      state.user = user;
    },
    RESET_STATE(state) {
      state.isAuthenticated = false;
      state.user = null;        
    },
  };

  const actions = {
    login({ commit }, user) {
        // console.log(user, 'What happening........');
      // Simulate a login API call (e.g., using Axios)
      // Replace this with your actual login API call
      return new Promise((resolve, reject) => {
        // Assuming successful login with userData returned
        // const user = { id: 1, username: userData.username }; // Example user object
        commit('SET_AUTHENTICATED', true);
        commit('SET_USER', user);
        // localStorage.setItem('user', JSON.stringify(user)); // Store user in localStorage
        resolve(user);
      });
    },
    logout({ commit }) {
      // Perform logout actions (e.g., clear authentication state and user data)
      commit('SET_AUTHENTICATED', false);
      commit('SET_USER', null);
    //   localStorage.removeItem('user'); // Remove user data from localStorage
    },
    checkAuth({ commit }) {
      // Check authentication status on app load (e.g., from localStorage)
      const user = JSON.parse(localStorage.getItem('track_new'));
      if (user) {
        commit('SET_AUTHENTICATED', true);
        commit('SET_USER', user);
      } else {
        commit('SET_AUTHENTICATED', false);
        commit('SET_USER', null);
      }
    }
  };

  const getters = {
    isAuthenticated: (state) => state.isAuthenticated,
    currentUser: (state) => state.user
  };

  export default {
    namespaced: true, 
    state,
    mutations,
    actions,
    getters
  };
