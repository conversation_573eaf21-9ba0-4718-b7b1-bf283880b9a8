<template>
    <div v-if="showModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white w-full max-w-sm transform transition-transform ease-in-out duration-300 rounded-lg shadow-lg overflow-hidden"
            :class="{ 'scale-100 opacity-100': isOpen, 'scale-95 opacity-0': !isOpen }">

            <div class="flex flex-col items-center p-6">
                <img :src="info_img" class="w-16 h-16 mb-4 cursor-pointer" @click="cancel" />
                <p class="text-2xl font-semibold text-gray-700 text-center">Are you sure?</p>

                <p v-if="message" class="text-gray-600 text-center mt-2 px-4">{{ message }}</p>
            </div>

            <div class="flex border-t p-4">
                <button @click="confirm"
                    class="flex-1 bg-green-600 text-white font-semibold px-4 py-2 rounded-md hover:bg-green-500 transition">OK</button>
                <button @click="cancel"
                    class="flex-1 bg-red-600 text-white font-semibold px-4 py-2 rounded-md hover:bg-red-500 transition ml-2">Cancel</button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: 'ConfirmDialog',
    props: {
        showModal: Boolean,
        message: {
            type: String,
            default: ""
        }
    },
    emits: ['onConfirm', 'onCancel'],
    data() {
        return {
            info_img: '/images/head_bar/info.png',
            isOpen: false,
        }
    },
    methods: {
        confirm() {
            this.$emit('onConfirm');
        },
        cancel() {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('onCancel');
            }, 300);
        }
    },
    watch: {
        showModal(newValue) {
            setTimeout(() => {
                this.isOpen = newValue;
            }, 100);
        },
    }
};
</script>

<style scoped>
.transition-transform {
    transition: transform 0.3s ease, opacity 0.3s ease;
}
</style>
