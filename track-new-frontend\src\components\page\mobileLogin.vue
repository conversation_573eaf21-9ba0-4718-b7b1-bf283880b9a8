<template>
    <div class="bg-gradient-to-r from-green-400 to-blue-500 min-h-screen flex items-center justify-center">
        <div
            class="flex flex-col md:flex-row w-full max-w-5xl h-auto md:h-[90vh] shadow-lg rounded-lg overflow-hidden bg-white">
            <!-- Left Side -->
            <div class="relative w-full md:w-1/2 hidden md:block">
                <img :src="backgroundImage" class="absolute inset-0 w-full h-full object-cover" />
                <div class="bg-blue-500 opacity-85 absolute inset-0"></div>
                <img :src="'/images/head_bar/tracknew logo 1.png'" class="relative h-10 w-44 inset-0 mt-4">
                <div class="relative flex flex-col justify-center items-center h-full p-6 z-10 text-center">
                    <h1 class="text-3xl text-white font-bold mt-32 mb-2  glow">TRACK NEW</h1>
                    <div class="quote-selector flex flex-col items-start pl-4">
                        <fa icon="quote-left" class="text-green-500 text-4xl mb-2" />
                        <p class="text-white italic">{{ currentQuote }}</p>
                    </div>
                    <div class="icon-selector flex space-x-4 mt-4 mb-4 justify-center">
                        <fa icon="minus" class=" text-white" :class="{ 'glow-icon': currentIndex === 0 }" />
                        <fa icon="minus" class=" text-white" :class="{ 'glow-icon': currentIndex === 1 }" />
                        <fa icon="minus" class="text-white" :class="{ 'glow-icon': currentIndex === 2 }" />
                    </div>
                </div>
            </div>

            <!-- Right Side with Mobile Login Form -->
            <div class="w-full md:w-1/2 bg-white flex flex-col justify-center items-center p-8">
                <!-- Mobile screen: show the image, hide it on larger screens -->
                <img :src="'/images/head_bar/logo-back.png'" class="h-10 w-44 mt-6 mb-4  block mx-auto md:hidden">
                <h2 v-if="type === null" class="text-2xl font-bold text-blue-700 mt-2 mb-4">Mobile Login!</h2>
                <img v-if="type === null" :src="phoneImage" class="phone-image rotate-and-ring-animation mb-4" />

                <!-- Message box for instructions -->
                <div v-if="type === null" class="reset-box mb-4 bg-light-orange text-light-brown text-sm">
                    <p>Enter your mobile number and instructions will be sent to you!</p>
                </div>

                <!-- Mobile number input section -->
                <div v-if="type === null" class="  mt-5 mb-3">
                    <p class="absolute  mt-3 ml-1">+91</p>
                    <input type="tel" id="business_contact" v-model="formValues.mobile_number" ref="mobile_number"
                        @input="validatePhoneNumber(formValues.mobile_number)" @keyup.enter="login"
                        class="mt-1 p-2 px-8 border border-gray-300 w-full"
                        :class="{ 'outline-red-700': validationMessage !== '' }" placeholder="Mobile Number" />

                    <!-- Display error message below the input -->
                    <p v-if="validationMessage !== ''" class="text-xs text-red-500 mt-2">
                        {{ validationMessage }}</p>
                </div>


                <div class="flex justify-center mt-4 mb-3">
                    <button v-if="type === null" @click="login"
                        class="shadow-inner input-large shadow-green-200 border border-green-500 bg-green-500  text-sm text-white px-8 py-3 rounded hover:bg-green-600">
                        Send OTP
                    </button>
                </div>



                <!-- OTP verification section -->
                <div v-if="type !== null && formValues.mobile_number" class="mt-5 mb-3">

                    <div class="flex justify-center mb-4">
                        <h2 class="text-2xl font-bold text-blue-700 mb-2 otp-heading">Verify Your SMS</h2>
                    </div>
                    <div class="flex justify-center mb-4">
                        <!-- Circular background div to cover only the icon -->
                        <div class="bg-blue-100 p-4 rounded-full flex justify-center items-center">
                            <!-- Mobile screen icon -->
                            <fa icon="mobile-screen-button" class="moving-icon text-blue-500 text-3xl" />
                        </div>
                    </div>
                    <!-- OTP Success Message -->
                    <div v-if="otpSuccessMessage" class="text-green-500 text-center mt-1">
                        {{ otpSuccessMessage }}
                    </div>
                    <p class="text-gray-600 text-base text-center mb-6">Please enter the 4-digit code sent to +91 -
                        <strong>{{
                            formValues.mobile_number }}</strong>
                    </p>
                    <div class="flex justify-center items-center">
                        <input v-for="(digit, index) in otpDigits" :key="index" :ref="'otpInput' + index" type="number"
                            class="border w-10 mr-2 py-2 text-center" maxlength="1" v-model="otp[index]"
                            @keyup="handleKeyUp(index, $event)" @keydown="handleKeyDown(index, $event)"
                            @input="handleInput(index, $event)" />
                    </div>


                    <!-- Error Message Below OTP Inputs -->
                    <p v-if="otpErrorMessage" class="text-xs text-center text-red-500 mt-2">{{ otpErrorMessage }}</p>


                    <div class="py-5 flex-col text-sm justify-center items-center">
                        <p class=" text-center text-gray-600 py-2">Didn't get the OTP?
                            <span v-if="timer > 0" class="text-red-500">Resend SMS in {{ timer }}s</span>
                            <span v-else @click="resendOtp" class="text-red-500 cursor-pointer hover:underline">Resend
                                OTP</span>
                        </p>
                    </div>

                    <!-- Submit button -->
                    <div class="flex justify-center mt-26 mb-3">
                        <button @click="otpValidation"
                            class="shadow-inner input-large shadow-green-200 border border-green-500 bg-green-500  text-sm text-white px-8 py-3 rounded hover:bg-green-600">
                            Submit
                        </button>
                    </div>
                </div>

                <!-- Account links -->
                <div class=" text-center">
                    <p class="text-sm text-gray-600">
                        <span>Don't have an account? </span>
                        <button @click="$router.push('/signup')"
                            class="text-md text-blue-500 hover:text-blue-600 hover:underline">Sign
                            Up</button>
                    </p>
                </div>
                <div class="mt-2 text-center">
                    <p class="text-sm text-gray-600">
                    <p>Already registered <button @click="$router.push('/login')"
                            class="text-blue-500 hover:text-blue-700 hover:underline">Login</button></p>
                    </p>
                </div>
            </div>
        </div>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>



<script>
import axios from 'axios';
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import { mapActions, mapGetters } from 'vuex';

export default {
    components: {
        fa: FontAwesomeIcon,
    },
    data() {
        return {
            formValues: {},
            validationMessage: '',
            otpSuccessMessage: '',
            type: null,
            timer: 60,
            intervalId: null,
            otp: ['', '', '', ''],
            otpDigits: [0, 1, 2, 3],
            message: '',
            quotes: [
                "If we want users to like our software, we should design it to behave like a likeable person.",
                "People who are really serious about software should make their own hardware.",
                "Biology is the most powerful technology ever created. DNA is software, proteins are hardware, cells are factories."
            ],
            currentIndex: 0,
            backgroundImage: './images/login/wallper.png',
            phoneImage: './images/login/phone.png',
            isVerifying: false,
            otpErrorMessage: '',
            open_loader: false,
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        currentQuote() {
            return this.quotes[this.currentIndex];
        },
    },
    mounted() {
        this.startQuoteRotation();
        this.handleFocus();
        const mobile = this.$route.query.mobile || null;
        if (mobile) {
            // console.log(`Mobile: ${mobile}`);
            this.formValues.mobile_number = mobile;
        }
        this.fetchLocalDataList();
        if(this.currentLocalDataList && this.currentLocalDataList.company_id){
            this.$router.push('/');
        }
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        startQuoteRotation() {
            setInterval(() => {
                this.currentIndex = (this.currentIndex + 1) % this.quotes.length;
            }, 5000);
        },
        login() {
            // Validate the mobile number before proceeding
            if (!this.formValues.mobile_number || this.validationMessage !== '') {
                this.validationMessage = 'Please enter a valid mobile number!';
                return;
            }
            this.open_loader = true;
            axios.post('/auth/mobile-login', { ...this.formValues })
                .then(response => {
                    // console.log(response.data, 'What happening...!');
                    // this.open_loader = false;
                    this.type = 'otp';
                    this.startTimer();
                    this.handleFocus();
                    this.otpSuccessMessage = 'OTP sent successfully...!';

                    // Hide the message after 10 seconds
                    setTimeout(() => {
                        this.otpSuccessMessage = ''; // Clear the message after 10 seconds
                    }, 5000);
                    this.open_loader = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                    // this.openMessage(error.response.data.message);
                })
        },
        handleFocus() {
            this.$nextTick(() => {
                if (!this.type) {
                    let ref_data = this.$refs.mobile_number;
                    // console.log(ref_data, 'What happening...!!!!');
                    if (ref_data) {
                        ref_data.focus();
                    }
                } else {
                    let ref_data = this.$refs['otpInput' + 0];
                    // console.log(ref_data, 'What happening...!!!!');
                    if (ref_data) {
                        ref_data[0].focus();
                    }
                }
            })
        },

        otpValidation() {
            // Check if OTP is entered and valid
            if (!this.validateOTP()) {
                this.otpErrorMessage = 'Please enter a valid 4-digit OTP!';
                return;
            }

            // Reset the error message if OTP is valid
            this.otpErrorMessage = '';
            this.open_loader = true;
            axios.post('/auth/mobile-verify', { ...this.formValues, otp: this.otp.join('') })
                .then(response => {
                    let response_data = response.data.user;
                    localStorage.setItem('track_new', JSON.stringify(response_data));
                    this.open_loader = false;

                    this.$router.push('/');
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                    this.otpErrorMessage = 'Invalid OTP. Please try again.'; // Show error if OTP verification fails
                });
        },

        // openMessage(msg) {
        //     this.message = msg;
        //     this.show = true;
        // },
        validatePhoneNumber(inputtxt) {
            const phoneno = /^\d{10}$/;
            if (inputtxt.match(phoneno)) {
                this.validationMessage = '';
                return true;
            } else {
                this.validationMessage = 'Enter a valid 10-digit contact number';
                return false;
            }
        },
        validateOTP() {
            // Check if the OTP has exactly 4 characters
            if (this.otp.join('').length !== 4) {
                return false;
            }
            // Check if each character is a number
            for (let i = 0; i < 4; i++) {
                if (isNaN(parseInt(this.otp[i]))) {
                    return false;
                }
            }
            return true; // OTP is valid
        },
        startTimer() {    
            this.timer = 60;
    this.intervalId = setInterval(() => {
      if (this.timer > 0) {
        this.timer--; //
      } else {
        clearInterval(this.intervalId); // Stop the interval when timer reaches 0
      }
    }, 1000);
  },
        handleInput(index, event) {
            const value = event.target.value;
            if (value.length > 1) {
                this.otp[index] = value.charAt(0);
            }
            if (index < this.otpDigits.length - 1) {
                this.$refs['otpInput' + (index + 1)][0].focus();
            }
        },
        handleKeyUp(index, event) {
            if (event.keyCode === 8) {
                // Call your method to handle backspace
                this.deleteIndex(index);
            }
            else if (event.keyCode === 13) {
                // Enter key pressed
                this.otpValidation();
            }
        },
        handleKeyDown(index, event) {
            if (event.key === 'Backspace' && this.otp[index] === '') {
                this.deleteIndex(index);
            }
        },

        deleteIndex(index) {
            if (index > 0) {
                this.$refs['otpInput' + (index - 1)][0].focus();
            }
        },
        validateOTP() {
            return this.otp.join('').length === 4 && this.otp.every(digit => !isNaN(parseInt(digit)));
        },
        resendOtp() {
            this.login();
            // this.startTimer();
        },
    },
};
</script>

<style scoped>
body {
    margin: 0;
    padding: 0;
    overflow-y: auto;
}

.quote-selector {
    margin: 20px 0;
}

.glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.7), 0 0 20px rgba(0, 255, 255, 0.5);
}

.glow-icon {
    animation: glow 1.5s infinite alternate;
}

@keyframes glow {
    0% {
        color: rgba(255, 255, 255, 1);
    }

    100% {
        color: rgba(255, 255, 255, 0.5);
    }
}

.phone-image {
    max-width: 80%;
    height: 90px;
}

.rotate-and-ring-animation {
    animation: rotate 2s linear infinite, ring 0.5s ease-in-out infinite;
}

.input-rounded {
    border-radius: 0.5rem;
}

.reset-box {
    padding: 10px;
    border-radius: 5px;
}

.bg-light-orange {
    background-color: #FFE5B4;
}

.text-light-brown {
    color: #A0522D;
}

.text-sm {
    font-size: 0.875rem;
}

.otp-heading {
    margin-top: -40px;

}

@keyframes rotate {
    0% {
        transform: rotateY(0deg);
    }

    100% {
        transform: rotateY(360deg);
    }
}

@keyframes ring {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.1);
        /* Scale up for a ringing effect */
    }
}

@keyframes glow {
    0% {
        color: rgba(255, 255, 255, 1);
    }

    100% {
        color: rgba(255, 255, 255, 0.5);
    }
}

/* Responsive Styles */
@media (max-width: 870px) {

    /* Adjust styles for smaller screens */
    .bg-gradient-to-r {
        padding: 2rem 1rem;
    }

    .quote-selector {
        margin: 10px 0;
    }

    .icon-selector {
        margin: 20px 0;
        flex-direction: column;
        /* Stack icons vertically on smaller screens */
    }

    .icon-selector fa {
        font-size: 3rem;
        /* Larger icons for smaller screens */
    }
}

.icon-selector {

    justify-content: center;
    align-items: center;
    flex-direction: row;

}

.icon-selector fa {
    font-size: 2.5rem;
    transition: transform 0.3s ease;
}

.icon-selector fa:hover {
    transform: scale(1.2);
}

.moving-icon {
    font-size: 2rem;
    animation: ring 0.5s ease-in-out infinite;
}

.bg-gradient {
    background: linear-gradient(to right, #68d391, #4299e1);
    /* Custom gradient */
    height: 100vh;
}

.bg-gradient-to-r .flex {
    display: flex;
    width: 100%;
    height: 100%;
}

.bg-gradient-to-r .flex>div {
    flex: 1;
    min-width: 0;
}

.bg-gradient-to-r .flex>div {
    flex: 2;
    min-width: 0;
}

.text-sm {
    font-size: 0.875rem;
}

.text-gray-300 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

@media (max-width: 870px) {
    .text-gray-300 {
        font-size: 0.875rem;
        white-space: nowrap;
    }
}

@media (max-width: 870px) {
    .flex.justify-center.mb-6 {
        display: flex;
        justify-content: center;
        align-items: center;

    }

    .text-2xl {
        font-size: 1.5rem;
    }
}

@media (max-width: 150px) {

    .text-gray-300 {
        display: none;
    }

    .flex.justify-center.space-x-4 {
        flex-direction: column;
        align-items: center;
    }

    .flex.justify-center.space-x-4 button {
        margin-bottom: 10px;
        width: 50px;
        height: 50px;
        display: block;
    }

    .flex.justify-center.space-x-4 button .fa {
        font-size: 1.5rem;
    }
}
</style>
