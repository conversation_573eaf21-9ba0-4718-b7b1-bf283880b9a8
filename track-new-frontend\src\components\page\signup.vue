<template>
    <div class="bg-gradient-to-r from-green-400 to-blue-500 min-h-screen flex items-center justify-center">
        <div
            class="flex flex-col md:flex-row mb-36 mt-16 w-full max-w-5xl h-auto md:h-screen shadow-lg rounded-lg overflow-hidden bg-white">
            <!-- Left Side -->
            <div class="relative w-full md:w-1/2 hidden md:block">
                <img :src="imgUrl" class="absolute inset-0 w-full h-full object-cover" />
                <div class="bg-blue-500 opacity-85 absolute inset-0"></div>
                <img :src="'/images/head_bar/tracknew logo 1.png'" class="relative h-10 w-44 inset-0 mt-4">
                <div class="relative flex flex-col justify-center items-center h-full p-6 z-10 text-center">
                    <h1 class="text-3xl text-white font-bold mt-16 mb-2 glow">TRACK NEW</h1>
                    <div class="quote-selector flex flex-col items-start pl-4">
                        <fa icon="quote-left" class="text-green-500 text-4xl mb-2" />
                        <p class="text-white italic">{{ currentQuote }}</p>
                    </div>
                    <div class="icon-selector flex space-x-4 mt-2 mb-20 justify-center">
                        <fa icon="minus" class=" text-white" :class="{ 'glow-icon': currentIndex === 0 }" />
                        <fa icon="minus" class=" text-white" :class="{ 'glow-icon': currentIndex === 1 }" />
                        <fa icon="minus" class="text-white" :class="{ 'glow-icon': currentIndex === 2 }" />
                    </div>
                </div>
            </div>

            <!-- Right Side with Registration Form -->
            <div class="w-1/2 bg-white flex flex-col justify-center items-center p-8 overflow-y-auto">
                <!-- Mobile screen: show the image, hide it on larger screens -->
                <img :src="'/images/head_bar/logo-back.png'" class="h-10 w-44 mt-6 mb-4  block mx-auto md:hidden">
                <h2 class="text-2xl font-bold text-blue-700  mt-2 mb-2">Register Account!</h2>
                <p class="text-gray-500 mb-2">Sign Up</p>
                <div v-if="page === 2" class="block  text-sm font-medium text-gray-700">
                    <button @click="page = 1" class="text-blue-500 px-4 py-2">
                        <span class="text-xl font-bold px-1 mr-1" style="font-size: 0.8rem;">&#8592;</span>
                        <span class="hover:underline">Back</span>
                    </button>
                </div>
                <div class="w-full" v-if="page === 1">
                    <!-- Company Name Field -->
                    <div class="mb-4">
                        <label for="companyName" class="block text-sm font-medium text-gray-700">Company
                            Name</label>
                        <input type="text" id="companyName" v-model="formValues.company_name" name="companyName"
                            ref="company_name" class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-red-400': companyNameError }"
                            placeholder="Company / shop / business name" />
                        <p v-if="companyNameError" class="absolute text-red-500 text-[10px]">{{ companyNameError }}
                        </p>
                    </div>
                    <!-- GST Number Field -->
                    <div class="mb-4">
                        <label for="gst_number" class="block text-sm font-medium text-gray-700">GST Number
                            (optional)</label>
                        <input type="text" id="gst_number" v-model="formValues.gst_number"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-red-700': formValues.gst_number !== '' && gstValidation !== '' }"
                            placeholder="GST Number (optional)" />
                        <p v-if="formValues.gst_number !== '' && gstValidation !== ''"
                            class="absolute text-red-500 text-[10px]">{{ gstValidation }}</p>
                    </div>
                    <!-- Business Contact Number Field -->
                    <div class="mb-4">
                        <label for="business_contact" class="block text-sm font-medium text-gray-700">Business
                            Contact Number</label>
                        <input type="tel" id="business_contact" v-model="formValues.company_phone_no"
                            @input="validatePhoneNumber(formValues.company_phone_no)" @keyup.enter="saveData"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-red-700': validationMessage !== '' }"
                            placeholder="Business Contact Number" />
                        <p v-if="validationMessage !== ''" class="absolute text-red-500 text-[10px]">{{
                            validationMessage }}
                        </p>
                    </div>
                    <div class="flex justify-center w-full mt-5 mb-3">
                        <button type="button" @click="nextPage"
                            class="bg-green-500 text-white px-4 py-2 rounded shadow-md hover:bg-green-600 transition">Next</button>
                    </div>
                </div>

                <div class="w-full" v-if="page === 2">
                    <!-- Name Field -->
                    <div class="mb-2">
                        <label class="block text-sm font-medium text-gray-700">Name</label>
                        <input type="text" id="name" v-model="formValues.name" ref="user_name"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-red-400': nameError }" placeholder="Your Name" />
                        <p v-if="nameError" class="absolute text-red-500 text-[10px]">{{ nameError }}</p>
                    </div>
                    <!-- Address Field -->
                    <div class="mb-2">
                        <label for="address" class="block text-sm font-medium text-gray-700">Address</label>
                        <textarea v-model="formValues.address"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-red-400': addressError }" placeholder="Address" rows="2"></textarea>
                        <p v-if="addressError" class="text-red-500 text-[10px] absolute">{{ addressError }}</p>
                    </div>

                    <!-- Email Field -->
                    <div class="mb-2">
                        <label for="email" class="block text-sm font-medium text-gray-700"
                            :class="{ emailError }">Email</label>
                        <input type="email" id="email" v-model="formValues.email" @input="validateEmail"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-red-400': emailError }" placeholder="Login Email ID" />
                        <p v-if="emailError" class="text-red-500 text-[10px] absolute">{{ emailError }}</p>
                    </div>

                    <!-- Password Field -->
                    <div class="mb-4 relative">
                        <label for="password" class="block text-sm font-medium text-gray-700"
                            :class="{ passwordError }">Password</label>
                        <input type="password" v-model="formValues.password"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-red-400': formValues.password && formValues.password !== '' && formValues.password.length < 8 }"
                            placeholder="Create new password" />
                        <p v-if="formValues.password && formValues.password !== '' && formValues.password.length < 8"
                            class="text-red-500 text-[10px] absolute">Password length minimum 8 characters
                        </p>
                        <p v-if="formValues.password && formValues.password.length >= 8"
                            class="text-red-500 text-[10px] absolute"></p>
                    </div>

                    <!-- Confirm Password -->
                    <div class="mb-4">
                        <label for="confirm_password" class="block text-sm font-medium text-gray-700">Confirm
                            Password</label>
                        <input type="password" v-model="formValues.confirm_password" @keyup.enter="saveData"
                            class="mt-1 p-2 border rounded w-full text-sm font-medium text-gray-700"
                            :class="{ 'border-red-400': (formValues.password && formValues.password.length > 8) || formValues.confirm_password !== formValues.password }"
                            placeholder="Confirm the password" />
                        <p v-if="formValues.password && formValues.password.length >= 8 && formValues.confirm_password !== formValues.password"
                            class="text-red-500 text-[10px] absolute">Please enter valid Password</p>
                    </div>

                    <!-- Sign Up Button -->
                    <div class="flex justify-center mt-5 mb-3">
                        <button @click="saveData"
                            class="shadow-inner shadow-green-200 border border-green-500 bg-green-500 text-sm w-3/4 text-white px-7 py-2 rounded hover:bg-green-600 transition">
                            {{ page === 1 ? 'Next' : 'Signup' }}
                        </button>
                    </div>

                    <!-- Divider and Social Login Options -->
                    <!-- <div class="mt-4 text-center">
                            <p class="text-gray-300 mb-4">
                                ------------------------- <span class="text-gray-600">Sign In with</span>
                                ---------------------------
                            </p>
                            <div class="flex justify-center space-x-4">
                                <button class="p-2 bg-blue-600 text-white rounded-full">
                                    <font-awesome-icon :icon="['fab', 'facebook-f']" />
                                </button>
                                <button class="p-2 bg-red-500 text-white rounded-full">
                                    <font-awesome-icon :icon="['fab', 'google']" />
                                </button>
                                <button class="p-2 bg-gray-800 text-white rounded-full">
                                    <font-awesome-icon :icon="['fab', 'github']" />
                                </button>
                                <button class="p-2 bg-blue-400 text-white rounded-full">
                                    <font-awesome-icon :icon="['fab', 'twitter']" />
                                </button>
                            </div>

                        </div> -->
                </div>

                <div class="flex justify-center mt-5 text-gray-500">
                    <p>Already registered ? <button @click="$router.push('/login')"
                            class="text-blue-500 hover:text-blue-700">Login</button></p>
                </div>
            </div>
            <Loader :showModal="open_loader"></Loader>
            <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
            <signupEmailMobile :showModal="is_exit_data" :message="message_data" :email="email" :mobile="mobile"
                @close-Modal="closedialogbox">
            </signupEmailMobile>
            <signupSuccess v-if="open_success" :showModal="open_success" :mobile="formValues.company_phone_no"
                :email="formValues.email"></signupSuccess>
        </div>
    </div>
</template>

<script>
import { FontAwesomeIcon } from '@fortawesome/vue-fontawesome';
import signupEmailMobile from '../supporting/dialog_box/signupEmailMobile.vue';
import signupSuccess from '../supporting/dialog_box/signupSuccess.vue';
import { mapActions, mapGetters } from 'vuex';
import axios from 'axios';

export default {
    components: {
        fa: FontAwesomeIcon,
        signupEmailMobile,
        signupSuccess
    },
    data() {
        return {
            formValues: {
                company_name: '',
                gst_number: '',
                company_phone_no: '',
                name: '',
                address: '',
                email: '',
                password: '',
                confirm_password: ''
            },
            open_message: false,
            message: '',
            emailError: '',
            passwordError: null,
            companyNameError: '',
            validationMessage: '',
            gstValidation: '',
            nameError: '',
            addressError: '',
            quotes: [
                "The theme is really great with amazing customer support.",
                "People who are really serious about software should make their own hardware.",
                "Biology is the most powerful technology ever created. DNA is software, proteins are hardware, cells are factories."
            ],
            show: false,
            currentIndex: 0,
            imgUrl: './images/login/wallper.png',
            page: 1, // 1 for the first page, 2 for the second
            type_toaster: 'warning',
            //--dialog box---
            is_exit_data: false,
            message_data: '',
            email: '',
            mobile: '',
            //---success--
            open_success: false,
            //---loader--
            open_loader: false,
        };
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        currentQuote() {
            return this.quotes[this.currentIndex];
        }
    },
    mounted() {
        this.startQuoteRotation();
        this.fetchLocalDataList();
        if(this.currentLocalDataList && this.currentLocalDataList.company_id){
            this.$router.push('/');
        }
    },
    methods: {
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        nextPage() {
            // Validate page 1 before proceeding
            if (!this.validateFormPage1()) return;
            this.page = 2;
        },
        validateFormPage1() {
            this.companyNameError = this.formValues.company_name ? '' : 'Company Name is required';
            this.validationMessage = this.formValues.company_phone_no ? '' : 'Business Contact Number is required';

            if (this.companyNameError || this.validationMessage) return false;
            return true;
        },
        validateFormPage2() {
            this.nameError = this.formValues.name ? '' : 'Name is required';
            this.addressError = this.formValues.address ? '' : 'Address is required';
            this.emailError = this.formValues.email ? '' : 'Email is required';
            this.passwordError = this.formValues.password.length >= 8 ? '' : 'Password must be at least 8 characters';
            if (this.formValues.password && this.formValues.confirm_password !== this.formValues.password) {
                this.passwordError = 'Passwords must match';
            }

            if (this.nameError || this.addressError || this.emailError || this.passwordError) return false;
            return true;
        },
        startQuoteRotation() {
            setInterval(() => {
                this.currentIndex = (this.currentIndex + 1) % this.quotes.length;
            }, 5000); // Change quote every 5 seconds
        },
        validatePhoneNumber(inputtxt) {
            var phoneno = /^(?:0|\+91)?[6-9]\d{9}$/;
            if (inputtxt.match(phoneno)) {
                this.validationMessage = '';
                return true; // Phone number is valid
            } else {
                this.validationMessage = 'Enter valid contact number';
                return false; // Phone number is invalid
            }
        },
        //--validate GST--
        validateGST() {
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.formValues.gst_number !== '') {
                if (regex.test(this.formValues.gst_number.toUpperCase())) {
                    this.gstValidation = '';
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                }
            }
        },
        //---validate email--
        validateEmail() {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(this.formValues.email)) {
                this.emailError = 'Please enter a valid email address.';
            } else {
                this.emailError = '';
            }
        },
        saveData() {
            if (this.page === 1) {
                if (this.validateFormPage1()) this.page = 2;
            } else if (this.page === 2) {
                if (this.validateFormPage2()) {
                    let sent_data = {
                        ...this.formValues,
                        mobile_number: this.formValues.company_phone_no,
                    };
                    this.open_loader = true;
                    axios.post('/auth/register', sent_data)
                        .then(response => {
                            console.log(response.data, 'Response');
                            // localStorage.setItem('track_new', JSON.stringify(response.data.user));
                            this.message = 'Welcome to TRACK-NEW';
                            // this.$router.push('/login');
                            this.open_loader = false;
                            this.open_success = true;
                            this.message = response.data.message;
                            this.type_toaster = 'success';
                            this.show = true;
                        })
                        .catch(error => {
                            this.open_loader = false;
                            if (error.response) {
                                console.error('Response data:', error.response.data);
                                // Check if email is already taken
                                if (error.response.data.data && error.response.data.data.email && error.response.data.data.email.length > 0) {
                                    this.message = error.response.data.data.email[0];
                                    this.email = this.formValues.email;
                                    this.mobile = this.formValues.company_phone_no;
                                    this.message_data = 'This email id is';
                                    this.is_exit_data = true;
                                } else if (error.response.data.message == "Mobile number already registered") {
                                    this.message = error.response.data.message;
                                    this.email = this.formValues.email;
                                    this.mobile = this.formValues.company_phone_no;
                                    this.message_data = 'This mobile number is';
                                    this.is_exit_data = true;
                                } else {
                                    this.message = error.response.data.message;
                                }
                                this.type_toaster = 'warning';
                                this.show = true;

                            } else {
                                console.error('Error', error);
                                this.openMessage('Network error or server is down.');
                                this.message = "Network error or server is down";
                                this.type_toaster = 'warning';
                                this.show = true;
                            }
                        });
                }
            }
            this.handleFocus();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 768;
        },
        openMessage(msg) {
            // console.log(msg, 'WWWWWW');
            this.message = msg;
            this.open_message = true;
        },
        closeMessage() {
            this.open_message = false;
            this.message = '';
        },
        handleFocus() {
            if (this.page) {
                this.$nextTick(() => {
                    let ref_data = this.$refs.user_name;
                    if (ref_data) {
                        ref_data.focus();
                    }
                })
            } else {
                this.$nextTick(() => {
                    let ref_data = this.$refs.company_name;
                    if (ref_data) {
                        ref_data.focus();
                    }
                })
            }
        },
        //---dialog box data---
        closedialogbox() {
            this.is_exit_data = false;
            this.message_data = '';
        }
    },
    watch: {
        'formValues.company_name'(newValue) {
            if (newValue) {
                this.companyNameError = ''; // Hide company name error if there's a value
            }
        },
        'formValues.address'(newValue) {
            if (newValue) {
                this.addressError = ''; // Hide address error if there's a value
            }
        },
        'formValues.name'(newValue) {
            if (newValue) {
                this.nameError = ''; // Hide name error if there's a value
            }
        },
        'formValues.email'(newValue) {
            if (newValue) {
                this.emailError = ''; // Hide email error if there's a value
            }
        },
        'formValues.password'(newValue) {
            if (newValue) {
                this.passwordError = ''; // Hide password error if there's a value
            }
        },
        'formValues.confirm_password'(newValue) {
            if (newValue) {
                this.passwordError = ''; // Hide confirm password error if there's a value
            }
        },
    }

};
</script>


<style scoped>
#gst_number {
    text-transform: uppercase;
}

body {
    margin: 0;
    padding: 0;
    overflow-y: auto;
}

.quote-selector {
    margin: 20px 0;
}

.glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.7), 0 0 20px rgba(0, 255, 255, 0.5);
}

.glow-icon {
    animation: glow 1.5s infinite alternate;
    /* Animation for glowing effect */
}

.text-xs.text-red-700 {
    color: #e53e3e;
    /* Red color for error messages */
    font-size: 0.75rem;
}

@keyframes glow {
    0% {
        color: rgba(255, 255, 255, 1);
    }

    100% {
        color: rgba(255, 255, 255, 0.5);
    }
}

/* Responsive Styles */
@media (max-width: 870px) {

    /* Adjust styles for smaller screens */
    .bg-gradient-to-r {
        padding: 2rem 1rem;
    }

    .quote-selector {
        margin: 10px 0;
    }

    .icon-selector {
        margin: 20px 0;
        flex-direction: column;
    }

    .icon-selector fa {
        font-size: 3rem;
    }
}

.icon-selector {

    justify-content: center;
    align-items: center;
    flex-direction: row;

}

.icon-selector fa {
    font-size: 2.5rem;
    transition: transform 0.3s ease;
}

.icon-selector fa:hover {
    transform: scale(1.2);
}

.bg-gradient {
    background: linear-gradient(to right, #68d391, #4299e1);
    /* Custom gradient */
    height: 100vh;
}

.bg-gradient-to-r .flex {
    display: flex;
    width: 100%;
    height: 100%;
}

.bg-gradient-to-r .flex>div {
    flex: 1;
    min-width: 0;
}

.bg-gradient-to-r .flex>div {
    flex: 2;
    min-width: 0;
}

.text-sm {
    font-size: 0.875rem;
}

.text-gray-300 {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}


@media (max-width: 870px) {
    .text-gray-300 {
        font-size: 0.875rem;
        white-space: nowrap;

    }
}


@media (max-width: 150px) {


    .text-gray-300 {
        display: none;
    }


    .flex.justify-center.space-x-4 {
        flex-direction: column;
        align-items: center;
    }


    .flex.justify-center.space-x-4 button {
        margin-bottom: 10px;
        width: 50px;
        height: 50px;
        display: block;

    }


    .flex.justify-center.space-x-4 button .fa {
        font-size: 1.5rem;
    }
}
</style>
