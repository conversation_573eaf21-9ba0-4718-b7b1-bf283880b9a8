<template>
    <div class="chart-container">
        <line-chart v-if="datacollection && datacollection.labels && datacollection.labels.length > 0"
            :data="datacollection" :options="options" :key="chartKey"></line-chart>
    </div>
</template>

<script>
import { Line } from 'vue-chartjs';
import { Chart as ChartJS, Title, Tooltip, Legend, LineElement, CategoryScale, LinearScale, PointElement } from 'chart.js';

ChartJS.register(Title, Tooltip, Legend, LineElement, CategoryScale, LinearScale, PointElement);

export default {
    components: {
        LineChart: Line
    },
    props: {
        labels: Object,
        datasets: Object,
    },
    data() {
        return {
            datacollection: {
                labels: [], //this.generateLabels()
                datasets: []
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    x: {
                        type: 'category',
                        title: {
                            display: true,
                            text: 'Date'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: 'Counts'
                        },
                        ticks: {
                            stepSize: 2,
                            callback: function (value) {
                                if (Number.isInteger(value)) {
                                    return value;
                                }
                            }
                        }
                    }
                }
            },
            chartKey: 0,
        }
    },
    mounted() {
        if (this.labels && this.labels.length > 0) {
            this.datacollection.labels = this.labels;
        }
        if (this.datasets && this.datasets.length > 0) {
            this.datacollection.datasets = this.datasets;
        }
    },
    methods: {
        generateLabels() {
            const labels = [];
            for (let i = 29; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
            }
            return labels;
        },
        refreshChart() {
            this.chartKey++;
        }
    },
    watch: {
        labels: {
            deep: true,
            handler(newValue) {
                this.datacollection.labels = newValue;
                this.refreshChart();
            }
        },
        datasets: {
            deep: true,
            handler(newValue) {
                this.datacollection.datasets = newValue;
                this.refreshChart();
            }
        }
    }
}
</script>

<style scoped>
.chart-container {
    height: 300px;
}
</style>