<template>
    <div class="main-content w-full" :class="{ 'mt-[60px]': isMobile, 'mb-[60px]': !isMobile }">
        <div class="my-custom-margin">
            <!--loader-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton" :cols="number_of_columns"
                :rows="number_of_rows" :gap="gap" :type="'grid'">
            </skeleton>
            <!--category-->
            <div class="flex sm:flex-row justify-end lg:justify-between sm:items-center pt-2 text-sm">
                <div v-if="!open_skeleton && !isMobile" class="pt-2">
                    <p class="font-semibold">
                        <span @click="goBackToHomePage"
                            class="text-gray-500 hover:text-black hover:underline cursor-pointer">Services</span>
                        <span class="px-2 text-gray-500"><font-awesome-icon icon="fa-solid fa-angles-right" /></span>
                        <span @click="goBack" class="text-gray-500 hover:text-black hover:underline cursor-pointer">
                            {{ category_name }}</span>
                        <span class="px-2 text-gray-500"><font-awesome-icon icon="fa-solid fa-angles-right" /></span>
                        <span @click="reloadPage" class="text-blue-700 cursor-pointer hover:text-blue-500">{{ type ===
                            'edit' ? 'Update' :
                            'Create New'
                        }}</span>
                    </p>
                </div>
                <div class="flex justify-end sm:justify-end items-center pt-2 text-xs">
                    <label for="create_at" class="font-bold pr-2 text-blue-800 cursor-pointer hover:text-blue-700"
                        @click="showServiceDateField = !showServiceDateField"><span v-if="!showServiceDateField">Click
                            to add </span> Service Creation
                        date</label>
                    <div v-if="showServiceDateField" class="flex flex-col">
                        <!-- Conditionally render the input based on the 'showServiceDateField' value -->
                        <input type="datetime-local" id="create_at" v-model="formValues.created_at" v-datepicker
                            class="p-1 rounded border mb-1" />
                    </div>
                </div>
            </div>
            <!-- Dynamic form creation -->
            <div v-if="!isFormEmpty && dynamicForm && !open_skeleton">
                <preview :dynamicForm="dynamicForm" @collectData="collectData" :status="getDataFromForm"
                    :category_id="category_id" :category_name="category_name" :companyId="companyId" :userId="userId"
                    :updateModalOpen="updateModalOpen" @update-is-modal-open="isModalOpen"
                    @collectSearchData="collectSearchData" :amc_data="amc_data">
                </preview>
            </div>

            <!--Buttons-->
            <div v-if="!isFormEmpty && dynamicForm && !open_skeleton" class="fixed-buttons-container">
                <!--sms, Whatsapp & email--->
                <div class="flex justify-center items-center ">
                    <!--save and cancel-->
                    <div v-if="!isMobile" class="flex items-center w-full"
                        :class="{ 'justify-center': !sidebaropen, 'justify-start': sidebaropen }">
                        <div class=" flex rounded items-center bg-red-700 p-2 w-1/6 hover:bg-red-600 text-white justify-center mr-5 mb-1 cursor-pointer"
                            @click="cancelData">
                            <font-awesome-icon icon="fa-regular fa-rectangle-xmark" class="px-2" />
                            <p class="text-center text-sm">Cancel</p>
                        </div>
                        <div class=" flex rounded items-center justify-center p-2 w-1/6 mr-5 mb-1 cursor-pointer bg-green-700 hover:bg-green-600 text-white"
                            @click="submitData">
                            <font-awesome-icon icon="fa-solid fa-floppy-disk" class="px-2" />
                            <p class="text-center text-sm">Save</p>
                        </div>
                    </div>
                    <!---IsMobile view-->
                    <div v-if="isMobile"
                        class="fixed bottom-0 left-0 z-[5] w-full bg-white border-t border-gray-200 dark:bg-gray-700 dark:border-gray-600">
                        <div class="grid h-full max-w-lg grid-cols-2 mx-auto text-md text-white">
                            <button type="button" @click="cancelData"
                                class="bg-red-600 flex justify-center items-center px-5 py-2 hover:bg-red-500"
                                :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'cancel' }">
                                <font-awesome-icon icon="fa-regular fa-rectangle-xmark" size="xl"
                                    :style="{ color: 'white' }" />
                                <span class="pl-2">Cancel</span>
                            </button>
                            <button type="button" @click="submitData"
                                class="bg-green-700 flex justify-center items-center  px-5 py-2 hover:bg-green-500"
                                :class="{ 'border-t-2 border-blue-700': selected_btn_btm === 'save' }">
                                <font-awesome-icon icon="fa-solid fa-floppy-disk" size="xl"
                                    :style="{ color: 'white' }" />
                                <span class="pl-2">Save</span>
                            </button>

                        </div>
                    </div>
                </div>
            </div>

            <!--create form-->
            <div v-if="isFormEmpty || !dynamicForm && !open_skeleton" class="flex justify-center items-center">
                <div>
                    <button
                        class="flex justify-center border border-green-600 bg-white rounded text-[20px] p-3 hover:bg-gray-100"
                        @click="createFormFun">
                        <span class="text-green-600 font-semibold pr-2">Create form</span><img :src="form_icon"
                            class="w-7 h-7" />
                    </button>
                </div>
            </div>
        </div>
        <customerRegister :show-modal="showModal_customer" @close-modal="closeModal" :userName="userName">
        </customerRegister>
        <addServiceCategory :show-modal="showModal_add_service" @close-modal="closeModalService" :companyId="companyId">
        </addServiceCategory>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <jobsheet v-if="jobsheet_data" :showModal="show_job" :item_data="jobsheet_data" @close-modal="closeJobSheet"
            :companyId="companyId" :messageData="'Service has been booked successfully'" :userId="userId"></jobsheet>
    </div>
    <Loader :showModal="open_loader"></Loader>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <notificationAlert :showModal="show_notification" @onCancel="cancelTheNotification"
        @onConfirm="enableTheNotification"></notificationAlert>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
</template>

<script>
import customerRegister from '../../dialog_box/customerRegister.vue';
import addServiceCategory from '../../dialog_box/addServiceCategory.vue';
import dialogAlert from '../../dialog_box/dialogAlert.vue';
import preview from '../../categories/create_form/preview.vue';
import jobsheet from '../../dialog_box/jobsheet.vue';
import notificationAlert from '../../dialog_box/notificationAlert.vue';
import { mapState, mapActions, mapGetters } from 'vuex';
import noAccessModel from '../../dialog_box/noAccessModel.vue';

export default {
    emits: ['updateIsOpen'],
    name: 'add_services',
    components: {
        customerRegister,
        addServiceCategory,
        dialogAlert,
        preview,
        jobsheet,
        notificationAlert,
        noAccessModel
    },
    props: {
        data: Object,
        category_name: String,
        type: String,
        category_id: String,
        companyId: String,
        userId: String,
        form_data: Object,
        open_skeleton: Boolean,
        updateModalOpen: Boolean,
    },
    data() {
        return {
            isMobile: false,
            formValues: { ...this.data, created_at: '' },
            dynamicForm: null,
            isFormEmpty: false,
            filteredCustomerOptions: [],
            isDropdownOpen: false,
            form_icon: '/images/categories_page/form_icon.png',
            userName: null,
            categories_values: null,
            isMessageDialogVisible: false,
            goPermission: false,
            //-----
            showModal_customer: false,
            showModal_add_service: false,
            add_user: '/images/service_page/Add_user.png',
            selectedServiceCategoryName: "",
            serviceCategories: [],
            getDataFromForm: true,
            message: null,
            //--skeleton
            number_of_columns: 2,
            number_of_rows: 20,
            gap: 5,
            jobsheet_data: null,
            show_job: false,
            open_loader: false,
            selected_btn_btm: '',
            //--toaster---
            show: false,
            type_toaster: 'success',
            show_notification: false,
            notification_validate: false,
            //---search data----
            searchData: null,
            //--amc data---
            amc_data: null,
            //---no access---
            no_access: false,
            showServiceDateField: false,
        }
    },
    computed: {
        ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures', 'sidebaropen']),
    },
    methods: {
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        openJobSheet() {
            this.show_job = true;
        },
        closeJobSheet() {
            this.show_job = false;
            if (this.goPermission === true) {
                this.$router.go(-1);
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        openModal(data) {
            this.userName = data;
            // console.log(data, 'What happening...!');
            this.isDropdownOpen = false;
            this.showModal_customer = true;
        },
        closeModal() {
            this.showModal_customer = false;
        },
        openModalService() {
            this.showModal_add_service = true;
        },
        closeModalService() {
            this.showModal_add_service = false;
        },
        cancelData() {
            this.selected_btn_btm = 'cancel';
            // console.log('cancel the data');
            this.$router.go(-1);
        },
        collectSearchData(data) {
            if (data) {
                this.searchData = data;
            }
        },
        addFormUpdatedData() {
            let is_find = false;
            if (this.searchData && this.searchData.problem_title && this.searchData.problem_title !== '') {
                let findData = this.dynamicForm.find(opt => opt.fieldKey == 'problem_title');
                if (findData && !findData.option.includes(this.searchData.problem_title)) {
                    findData.option.push(this.searchData.problem_title);
                    if (this.formValues.problem_title) {
                        this.formValues.problem_title.push(this.searchData.problem_title);
                    } else {
                        this.formValues.problem_title = [this.searchData.problem_title];
                    }
                    is_find = true;
                } else if (!this.formValues.problem_title.includes(this.searchData.problem_title)) {
                    this.formValues.problem_title.push(this.searchData.problem_title);
                }
            }
            if (this.formValues && this.formValues.brand && this.formValues.brand !== '') {
                let findData = this.dynamicForm.find(opt => opt.fieldKey == 'brand');
                if (findData && !findData.option.includes(this.formValues.brand)) {
                    findData.option.push(this.formValues.brand);
                    is_find = true;
                }
            }
            if (this.formValues && this.formValues.device_model && this.formValues.device_model !== '') {
                let findData = this.dynamicForm.find(opt => opt.fieldKey == 'device_model');
                if (findData && !findData.option.includes(this.formValues.device_model)) {
                    findData.option.push(this.formValues.device_model);
                    is_find = true;
                }
            }
            if (this.category_id && is_find) {
                //---service category---
                axios.put(`/service_categories/${this.category_id}`, { company_id: this.companyId, form: JSON.stringify(this.dynamicForm), service_category: this.category_name })
                    .then(response => {
                        // console.log(response.data.data, 'response for updated options');
                        if (response.data.data.form) {
                            this.dynamicForm = JSON.parse(response.data.data.form);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                    })
            }
        },
        async submitData() {
            if (this.getplanfeatures('service')) {
                this.no_access = true;
            } else {
                this.selected_btn_btm = 'save';
                //--&& ((this.formValues.notification && this.formValues.notification.length > 0) || this.notification_validate)
                if (this.formValues && this.formValues.customer_id) {
                    await this.addFormUpdatedData();
                    this.open_loader = true;
                    // Validate required fields
                    const requiredFields = this.dynamicForm.filter(field =>
                        field.required === 'yes' &&
                        field.enable &&
                        !['estimateAmount', 'advanceAmount', 'serviceAmount'].includes(field.fieldKey)  // Exclude specific field keys
                    );
                    const invalidFields = requiredFields.filter(field => {
                        if (!(this.formValues['warranty_type'] || this.formValues['warranty_type'] !== 'Free')) {
                            if ((field.fieldKey === 'document' && this.getplanfeatures('service_image')) || (field.fieldKey === 'additional' && this.getplanfeatures('additional_materials'))) {
                                // console.log('it is working......');
                            } else {
                                const value = this.formValues[field.fieldKey];
                                return value === undefined || value === null;
                            }
                        } else if (field.fieldKey !== 'estimateAmount' && field.fieldKey !== 'advanceAmount' && field.fieldKey !== 'serviceAmount') {
                            if ((field.fieldKey === 'document' && this.getplanfeatures('service_image')) || (field.fieldKey === 'additional' && this.getplanfeatures('additional_materials'))) {
                                // console.log('it is working......');
                            } else {
                                const value = this.formValues[field.fieldKey];
                                return value === undefined || value === null;
                            }
                        }
                    });
                    if (invalidFields.length > 0) {
                        // Show error message for empty required fields
                        this.openMessageDialog(`Please fill in all required fields are ${invalidFields.map(field => field.lableName).join(', ')} `);
                        this.open_loader = false;
                        return;
                    }
                    let serviceTrack = [];
                    let find_status_list = this.dynamicForm.find(opt => opt.fieldKey === 'status');
                    if (find_status_list) {
                        serviceTrack = find_status_list.option.map((opt, i) => {
                            return { name: opt, date: '', status: false };
                        });
                        // }
                    }
                    if (Number(this.category_id) >= 0) {
                        if (this.type === 'edit') {
                            // console.log('only post page..!');
                        } else {
                            let { customer_id, expected_date, estimateAmount, advanceAmount, serviceAmount, status, notification, document, additional, assignWork, created_at } = this.formValues;
                            let findIndex = serviceTrack.findIndex(opt => opt.name === status);
                            if (additional && additional.length > 0) {
                                // Filter out items with qty === 0 and calculate total of remaining items
                                let filteredAdditional = additional.filter(item => item.qty !== 0);
                                // Calculate the total of remaining items
                                let total = filteredAdditional.reduce((acc, item) => acc + item.total, 0);
                                // Update the 'additional' array with filtered items (optional)
                                additional = filteredAdditional;
                                // Use 'total' for further processing
                                // console.log('Total of non-zero qty items:', total, filteredAdditional);
                            }
                            if (findIndex !== -1) {
                                serviceTrack[findIndex].status = true;
                                serviceTrack[findIndex].date = this.getCurrentDateTime();
                                serviceTrack.map((opt, i) => {
                                    if (i !== findIndex && (i > findIndex || i === 1 || i === 6)) {
                                        opt.status = false;
                                    } else if (i !== findIndex && i < findIndex && i !== 1 && i !== 6) {
                                        if (opt.status === false) {
                                            opt.status = true;
                                        }
                                        if (opt.date === '') {
                                            opt.date = this.getCurrentDateTime();
                                        }
                                    }
                                })
                            }
                            let adjustedTime = '';
                            if (created_at) {
                                // Convert to Date object
                                let date = new Date(created_at);
                                // Subtract 5 hours and 30 minutes
                                date.setHours(date.getHours() - 5);  // Subtract 5 hours
                                date.setMinutes(date.getMinutes() - 30);  // Subtract 30 minutes
                                // Format the date as 'YYYY-MM-DDTHH:mm'
                                let year = date.getFullYear();
                                let month = String(date.getMonth() + 1).padStart(2, '0');  // Month is zero-based
                                let day = String(date.getDate()).padStart(2, '0');
                                let hours = String(date.getHours()).padStart(2, '0');
                                let minutes = String(date.getMinutes()).padStart(2, '0');
                                // Construct the formatted string
                                adjustedTime = `${year}-${month}-${day}T${hours}:${minutes}`;
                            }
                            let service_send_data = {
                                customer_id: customer_id,
                                servicecategory_id: Number(this.category_id),
                                expected_date: expected_date && expected_date,
                                estimate_amount: estimateAmount ? estimateAmount : 0,
                                advance_amount: advanceAmount ? advanceAmount : 0,
                                service_amount: serviceAmount ? serviceAmount : 0,
                                status: JSON.stringify(findIndex >= 0 ? findIndex : ''),
                                notification: JSON.stringify(notification),
                                document: JSON.stringify(document),
                                materials: JSON.stringify(additional),
                                assign_to: assignWork && JSON.stringify(assignWork.map(obj => ({ user_id: obj.id }))),
                                service_track: JSON.stringify(serviceTrack),
                                service_expense: this.formValues['service_expense'] ? this.formValues['service_expense'].filter(opt => opt.value > 0) : [],
                            };
                            if (adjustedTime && adjustedTime !== '') {
                                service_send_data.created_at = adjustedTime;
                            }
                            if (this.amc_data && this.amc_data.id && this.amc_data.date_id) {
                                service_send_data.amc = {
                                    amc_id: this.amc_data.id,
                                    amcdate_id: this.amc_data.date_id
                                };
                            }
                            axios.post('/services', { company_id: this.companyId, ...service_send_data, service_data: JSON.stringify({ ...this.formValues, additional: additional }) })
                                .then(response => {
                                    // console.log(response.data, 'Success response...!');
                                    this.jobsheet_data = response.data.data;
                                    this.open_loader = false;
                                    this.goPermission = true;
                                    // this.openMessageDialog(response.data.message);
                                    this.type_toaster = 'success';
                                    this.message = 'Service has been booked successfully..!'
                                    this.show = true;
                                    this.openJobSheet();
                                    this.updateKeyWithTime('service_update');
                                    this.updateKeyWithTime('service_category_update');
                                })
                                .catch(error => {
                                    console.error('Error', error);
                                    this.open_loader = false;
                                    this.openMessageDialog(error.response.data.message);
                                })
                        }
                    } else {
                        // Handle case where the category is not found
                        console.error('Category not found');
                    }
                } else {
                    // if (this.formValues && this.formValues.customer_id) {
                    //     this.show_notification = true;
                    // } else {
                    this.message = `Select a customer or click 'Add New Customer' and save`;
                    this.type_toaster = 'info';
                    this.show = true;
                    // }
                }
            }
        },
        //---status track function--
        // Get current date and time in the desired format (e.g., "2024-03-19 6:20PM")
        getCurrentDateTime() {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');
            const hours = String(now.getHours()).padStart(2, '0');
            const minutes = String(now.getMinutes()).padStart(2, '0');
            const meridiem = (hours < 12) ? 'AM' : 'PM';
            const formattedHours = (hours % 12) || 12;
            return `${year}-${month}-${day} ${formattedHours}:${minutes}${meridiem}`;
        },
        // Update status and date in the serviceTrack array
        updateServiceTrack(serviceTrack, statusName) {
            const currentDate = this.getCurrentDateTime();
            return serviceTrack.map(step => {
                // console.log(step.name, 'YYYY', statusName);
                if (step.name === statusName) {
                    step.status = true;
                    step.date = currentDate;
                }
            });
        },

        goBackToHomePage() {
            this.$router.push('/services');
        },
        reloadPage() {
            window.location.reload();
        },
        goBack() {
            // Go back to the previous page , params: { type: this.category_name, id: Number(this.category_id) } 
            this.$router.push({ name: 'categories' });
        },
        //-----
        //---customers dropdown
        selectDropdownOption(fields, option) {
            this.formValues[fields.fieldKey] = option.name;
            this.isDropdownOpen = false; // Close the dropdown
        },

        handleDropdownInput(fields) {
            const inputValue = this.formValues[fields.fieldKey];

            if (inputValue !== undefined && inputValue !== null) {
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = fields.option.filter(
                        (option) => option.phone.toLowerCase().includes(inputNumber)
                    );
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = fields.option.filter(
                        (option) => option.name.toLowerCase().includes(inputName)
                    );
                }
            } else {
                // Handle the case where formValues[fields.fieldKey] is undefined or null
                // You might want to set this.filteredCustomerOptions to an empty array or another default value
                this.filteredCustomerOptions = [];
            }
        },
        //---update based on category--
        // updateComponentBasedOnCategoryName(newCategoryName) {
        //     console.log(this.serviceCategories, 'RRRRR', this.category_name, this.category_id);
        //     let findCategory = this.serviceCategories.find((opt) => opt.id === this.category_id);

        //     // console.log(findCategory, 'Weeeae', 'Category', this.category_name, 'Is mobile', this.isMobile);
        //     if (findCategory.form && JSON.parse(findCategory.form).length !== 0) {
        //         this.dynamicForm = JSON.parse(findCategory.form);
        //         console.log(this.dynamicForm, 'EEEEE');
        //     } else {
        //         this.isFormEmpty = true;
        //     }
        // },
        createFormFun() {
            this.$router.push({ name: 'service_category_create_form', params: { type: this.category_name, serviceId: this.category_id } });
        },
        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.goPermission === true) {
                this.$router.go(-1);
            }
        },
        //---collect data from form
        collectData(data) {
            // console.log(data, 'What is the data...!');
            this.formValues = data;
        },
        //---service category---
        serviceCategoryList() {
            axios.get(`/service_categories`, { params: { company_id: this.companyId, page: 1, per_page: 'all' } })
                .then(response => {
                    console.log(response.data, 'service gatgory get..!', 'eeeee');
                    this.serviceCategories = response.data.data;
                    // this.updateComponentBasedOnCategoryName(this.category_name);
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---service category---
        serviceDataList(id) {
            axios.get(`/services/${id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    console.log(response.data, 'service gatgory get..!');
                    return response.data.data;
                })
                .catch(error => {
                    console.error('Error:', error);
                })
        },
        //---notification--
        cancelTheNotification() {
            this.show_notification = false;
            this.notification_validate = true;
            this.submitData();
        },
        enableTheNotification() {
            this.show_notification = false;
            this.notification_validate = true;
            this.formValues.notification = ['SMS'];
            this.submitData();
        },
        //----update is modal open--
        isModalOpen(type) {
            if (type !== undefined) {
                this.$emit('updateIsOpen', type);
            }
        },
        //---plan based on strict--
        getplanfeatures(key) {
            if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
                return true;
            } else {
                return false;
            }
        },
        //--close no-access model--
        closeNoAccess() {
            this.no_access = false;
        }
    },
    watch: {
        // Watch the 'data' prop
        data: {
            handler(newData) { },
            immediate: true,
        },
        form_data: {
            deep: true,
            handler(newValue) {
                //     console.log(newValue, 'RRRRR');
                if (newValue) {
                    this.dynamicForm = newValue;
                } else {
                    this.isFormEmpty = true;
                }
            }
        },
        // category_name(newCategoryName) {
        //     // console.log(newCategoryName, 'What about data...!');
        //     this.updateComponentBasedOnCategoryName(newCategoryName);
        // },
    },
    mounted() {
        this.fetchApiUpdates();
        this.updateIsMobile();
        this.fetchLocalDataList();
        // if (this.companyId) {
        //     this.serviceCategoryList();
        // }
        window.addEventListener('resize', this.updateIsMobile);

        // const collectForm = localStorage.getItem('CategoriesForm');
        // console.log(collectForm, 'ooooooooooooo');
        // if (collectForm) {
        //     let dataParse = JSON.parse(collectForm);
        // console.log(dataParse, 'WWWWWWWWWWWW');
        // this.serviceCategories = dataParse;
        // } 
        // Extract query parameters
        const amc_id = this.$route.query.amc_id;
        const amc_date_id = this.$route.query.amc_date_id;
        if (amc_id && amc_date_id) {
            this.amc_data = { id: amc_id, date_id: amc_date_id };
        }
    },
}
</script>
<style>
.fixed-buttons-container {
    position: fixed;
    bottom: 0;
    width: 100%;
    background-color: #fff;
    z-index: 10;
}


.main-content {
    margin-bottom: 50px;
}
</style>
