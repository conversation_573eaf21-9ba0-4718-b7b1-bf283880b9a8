<template>
    <div v-if="showModal"
        class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 h-screen overflow-auto pt-[130px]">
        <div class="bg-white w-full max-w-md transform transition-transform ease-in-out duration-300 rounded overflow-auto p-4"
            :class="{ 'scale-100': isOpen, 'scale-0': !isOpen }">
            <h2 class="text-2xl font-bold text-gray-800 text-center mb-4">Verify OTP</h2>
            <p v-if="service_data && Object.keys(service_data).length > 0 && service_data.customer"
                class="text-gray-600 text-center mb-6">
                Sent OTP to: <span class="font-bold">{{ service_data.customer }}</span>
            </p>

            <!-- OTP Input Fields -->
            <div class="flex justify-center space-x-2 mb-6">
                <input v-for="(digit, index) in otp" :key="index" ref="otpInput" v-model="otp[index]" type="text"
                    maxlength="1"
                    class="w-12 h-12 text-center text-xl border-2 border-gray-300 rounded-lg focus:border-blue-500 focus:outline-none transition-colors duration-200"
                    @input="focusNextInput(index)" @keydown.backspace="focusPreviousInput(index)" />
            </div>

            <!-- Countdown or Resend OTP -->
            <div v-if="isCounting">
                <p class="text-center text-gray-600">Resend OTP in {{ countdown }} seconds</p>
            </div>
            <div v-else>
                <button @click="resendOtp"
                    class="w-full bg-blue-500 text-white py-2 rounded-lg hover:bg-blue-600 transition-colors duration-200">
                    Resend OTP
                </button>
            </div>

            <!-- Action Buttons -->
            <div class="mt-6 flex space-x-4">
                <button @click="validateOtp"
                    class="w-full bg-green-500 text-white py-2 rounded-lg hover:bg-green-600 transition-colors duration-200">
                    Validate OTP
                </button>
                <button @click="closeModal"
                    class="w-full bg-gray-300 text-gray-700 py-2 rounded-lg hover:bg-gray-400 transition-colors duration-200">
                    Cancel
                </button>
            </div>
        </div>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
        <Loader :showModal="open_loader"></Loader>
    </div>
</template>

<script>
export default {
    props: {
        showModal: Boolean,
        service_data: Object
    },
    data() {
        return {
            otp: ['', '', '', ''], // Array to hold the 4 OTP digits
            countdown: 60, // Countdown timer starts from 60 seconds
            isCounting: true, // Determines whether countdown is active
            countdownInterval: null, // Holds the countdown interval
            isOpen: false,
            open_loader: false,
            message: '',
            type_toaster: 'success',
            show: false,
        };
    },
    methods: {
        // Simulated API call to send OTP (you can replace this with actual API logic)
        sendOtp() {
            if (this.service_data && Object.keys(this.service_data).length > 0 && this.service_data.id) {
                this.open_loader = true;
                axios.post('services/send-otp', { service_id: this.service_data.id })
                    .then(response => {
                        // console.log('waht about response', response.data);
                        this.startCountdown();
                        this.open_loader = false;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })
            }
        },
        // Resend OTP
        resendOtp() {
            this.otp = ['', '', '', '']; // Clear the OTP input fields
            this.sendOtp(); // Resend OTP via API
        },
        // Focus on the next input field
        focusNextInput(index) {
            if (this.otp[index] && index < 3) {
                this.$refs.otpInput[index + 1].focus();
            }
        },
        // Focus on the previous input field when pressing backspace
        focusPreviousInput(index) {
            if (!this.otp[index] && index > 0) {
                this.$refs.otpInput[index - 1].focus();
            }
        },
        // Start the countdown from 60 seconds
        startCountdown() {
            this.isCounting = true;
            this.countdown = 60;
            clearInterval(this.countdownInterval); // Clear any existing intervals
            this.countdownInterval = setInterval(() => {
                if (this.countdown > 0) {
                    this.countdown--;
                } else {
                    this.isCounting = false; // Stop the countdown
                    clearInterval(this.countdownInterval); // Clear the interval
                }
            }, 1000); // Decrease the countdown every second
        },
        // Validate OTP (you can replace this with actual API validation logic)
        validateOtp() {
            this.open_loader = true;
            if (this.otp.join('').length === 4) {
                axios.post('services/verify-otp', { service_id: this.service_data.id, otp: this.otp.join('') })
                    .then(response => {
                        // console.log('waht about response', response.data);
                        this.open_loader = false;
                        this.message = response.data.message;
                        this.type_toaster = 'success';
                        this.show = true;
                        this.closeModal(true);
                    })
                    .catch(error => {
                        console.error('Error itm post', error);
                        this.message = error.response.data.message ? error.response.data.message : error.response.data.error;
                        this.type_toaster = 'warning';
                        this.show = true;
                        this.open_loader = false;
                    })

            } else {
                this.message = 'Please enter a valid 4-digit OTP.';
                this.type_toaster = 'warning';
                this.show = true;
                this.open_loader = false;
            }
        },
        // Close the modal
        closeModal(data) {
            this.isOpen = false;
            setTimeout(() => {
                this.$emit('close-Modal', data);
            }, 300);
        },
        handleFocus(index) {
            // Using nextTick to ensure the DOM is updated before focusing
            this.$nextTick(() => {
                const inputElement = this.$refs.otpInput;
                if (inputElement && inputElement[index]) {
                    inputElement[index].focus();  // Focus on the specific input
                }
            });
        },

    },
    mounted() {
    },
    watch: {
        showModal(newValue) {
            // console.log(newValue, 'Waht happening..!');
            if (newValue) {
                this.sendOtp();
            }
            setTimeout(() => {
                this.isOpen = newValue;
                this.handleFocus(0);
            }, 100);
        },
    }
};
</script>

<style scoped>
/* Add any additional custom styling here if needed */
</style>