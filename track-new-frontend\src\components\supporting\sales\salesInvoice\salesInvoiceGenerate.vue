<template>
    <div class="w-full" :class="{ 'text-sm mt-[20px] mb-[60px]': isMobile, 'text-sm mt-[5px]': !isMobile }">
        <!-- <template_1 :formData="shareData" @goSetting="goBackPage" :typeOfInvoice="typeOf"></template_1> -->
        <pos :companyId="companyId" :userId="userId" :typeOfInvoice="'sales'" :enable_hold="enable_hold"
            @close_hold="closeHold" @refresh_hold="refrshHoldInv" :store_refresh="store_refresh"
            :updateModalOpen="updateModalOpen" @update-is-modal-open="isModalOpen" @is-sales-save="salesSaveReq"
            :save_success="save_success" @updatesalesData="savedataUpdate"></pos>
    </div>
</template>
<script>
// import template_1 from '../../setting_categories/invoiceTemplates/template_1.vue';
import pos from '../pos/pos.vue';
export default {
    emits: ['updateIsOpen', 'close_hold', 'refresh_hold', 'sales-save-req', 'updatesalesdata'],
    components: {
        // template_1,
        pos
    },
    props: {
        companyId: String,
        userId: String,
        enable_hold: Boolean,
        store_refresh: Boolean,
        updateModalOpen: Boolean,
        save_success: Boolean
    },
    data() {
        return {
            shareData: {},
            viewServiceData: null,
            invoiceNum: null,
            invoiceType: null,
            typeOf: 'sales',
            editData: null
        }
    },
    methods: {
        goBackPage() {
            this.$router.go(1);
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        parseUrl() {
            const urlParts = window.location.pathname.split('/');
            const id = urlParts[urlParts.length - 1];

            this.invoiceNum = decodeURIComponent(id);
            // let findCategory = this.servicecategory_data.find((data) => data.id === Number(categoryID));
            // if (findCategory) {
            //     this.viewServiceData = findCategory.data.find((record => record.id === Number(id)));
            // console.log( this.viewServiceData, 'RRERERTETRT');
            // }
        },
        closeHold() {
            this.$emit('close_hold');
        },
        refrshHoldInv() {
            this.$emit("refresh_hold");
        },
        //----update is modal open--
        isModalOpen(type) {
            if (type !== undefined) {
                this.$emit('updateIsOpen', type);
            }
        },
        //---sales is not save confirm box----
        salesSaveReq(type) {
            this.$emit('sales-save-req', type);
        },
        //--update data--
        savedataUpdate() {
            this.$emit('updatesalesdata');
        }
    },
    created() {
        // console.log('hello');
        this.updateIsMobile(); // Initial check
        // let existData = localStorage.getItem('invoiceSetting');
        // // console.log(existData, 'What happening ....@@@');
        // if (existData) {
        //     let parseData = JSON.parse(existData);
        //     if (parseData.disclaimer !== '') {
        //         let disclaimerMSG = parseData.disclaimer.split('\n');
        //         this.shareData = { ...parseData, disclaimer: disclaimerMSG };

        //     } else {
        //         this.shareData = parseData;
        //     }
        // }   

        // this.parseUrl(); // Call the function to parse the URL
        window.addEventListener('resize', this.updateIsMobile);
    },
    mounted() {
        this.invoiceType = this.$route.query.type;
        //     if(this.invoiceType === 'edit'){
        //         const getExistData = localStorage.getItem('invoices');
        //         console.log(getExistData, 'Get Exist data...!');
        //         if(getExistData){
        //             const parseData = JSON.parse(getExistData);
        //             console.log(parseData, 'Parse Data...!');
        //             const invoiceNumber = this.$route.query.invoiceNum;
        //             // this.editData = parseData.find((opt)=> opt.)
        //         }        
        //     }
        //    let getEst = this.$route.query.estimate_num;
        //    console.log(getEst, 'Waht happening...!');
        //    if(this.invoiceType === 'add' && getEst){

        //    }
    },
    watch: {
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                if (this.enable_hold) {
                    this.closeHold();
                }
            }
        },
        enable_hold: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
    }

}
</script>