<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateSalesItemsAPIRequest;
use App\Http\Requests\API\UpdateSalesItemsAPIRequest;
use App\Models\SalesItems;
use App\Repositories\SalesRepository;
use App\Repositories\SalesItemsRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class SalesItemsController
 * @package App\Http\Controllers\API
 */

class SalesItemsAPIController extends AppBaseController
{
    /** @var  SalesItemsRepository */
    private $salesItemsRepository;
    private $salesRepository;

    public function __construct(SalesItemsRepository $salesItemsRepo, SalesRepository $salesrepo)
    {
      	//$this->middleware('doNotCacheResponse', ['only' => ['index','getBySerialNumber']]);
        $this->salesItemsRepository = $salesItemsRepo;
        $this->salesRepository = $salesrepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/sales_items",
     *      summary="getSalesItemsList",
     *      tags={"SalesItems"},
     *      description="Get all SalesItems",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/SalesItems")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $salesItems = $this->salesItemsRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($salesItems->toArray(), 'Sales Items retrieved successfully');
    }
  
  	public function getBySerialNumber($serialno)
    {
       

        $salesItems = SalesItems::with([
            'sale.customer',
            'product.brand',
            'product.productDetails.purchaseOrderItem.purchaseOrder.supplier'
        ])->whereJsonContains('serial_no', $serialno)->get();

        if ($salesItems->isEmpty()) {
            return response()->json(['message' => 'No sales items found'], 404);
        }

         $response = $salesItems->map(function ($salesItem) {
            $product = $salesItem->productData;
            $sale = $salesItem->sale;
            $client = $sale ? $sale->customer : null;

            $suppliers = [];
            if ($product) {
                foreach ($product->productDetails as $productDetail) {
                    $purchaseOrderItem = $productDetail->purchaseOrderItem;
                    $purchaseOrder = $purchaseOrderItem ? $purchaseOrderItem->purchaseOrder : null;

                    if ($purchaseOrder) {
                        $supplier = $purchaseOrder->supplier;

                        if ($supplier) {
                            $suppliers[] = [
                                'supplier_id' => $supplier->id,
                                'supplier_name' => $supplier->name
                            ];
                        }
                    }
                }

                $brand = $product->brand;
            }

            return [
                'serial_number' => $salesItem->serial_no,
                'client' => $client ? [[
                    'customer_id' => $client->id,
                    'first_name' => $client->first_name,
                    'last_name' => $client->last_name,
                    'contact_number' => $client->contact_number
                ]] : [],
                'product' => $product ? [[
                    'product_id' => $product->id,
                    'product_name' => $product->product_name,
                  	'warranty' => $product->warranty,
                    'brand' => $brand ? [
                        'brand_id' => $brand->id,
                        'brand_name' => $brand->brand_name
                    ] : null,
                   // 'purchase_order_item_ids' => $product->productDetails->pluck('pd_id')->unique()
                ]] : [],
                'suppliers' => $suppliers,
              	'sale' => [
                    'id' => $sale['id'],
                    'invoice_id' => $sale['invoice_id'],
                  	'created_at' => $sale['created_at']
                ]
            ];
        });

        return response()->json($response, 200);
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/sales_items",
     *      summary="createSalesItems",
     *      tags={"SalesItems"},
     *      description="Create SalesItems",
    *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/SalesItems")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SalesItems"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateSalesItemsAPIRequest $request)
    {
        $input = $request->all();

        // $salesData = [
        //     "invoice_id" => $input['invoice_id'],
        //     "invoice_to" => $input['invoice_to'],
        //     "invoice_type" => $input['invoice_type'],
        //     "discount" => $input['discount'],
        //     "discount_type" => $input['discount_type'],
        //     "due_amount" => $input['due_amount'],
        //     "payment_mode" => $input['payment_mode'],
        //     "shipping" => $input['shipping'],
        //     "shipping_type" => $input['shipping_type'],
        //     "client_id" => $input['client_id'],
        //     "current_date" => $input['current_date'],
        //     "company_id" => $input['company_id']
        // ];

        // $sales = $this->salesRepository->create($salesData);

        // $salesPaymentDate = [
        //     "payment_code" => $input['payment_code'],
        //     "company_id" => $input['company_id'],
        //     "sales_id" => $sales->id,
        //     "payment_date" => $input['payment_date'],
        //     "payment_amount" => $input['payment_amount'],
        //     "customer_id" => $input['customer_id'],
        //     "payment_notes" => $input['payment_notes'],
        //     "created_by" => $input['created_by'],
        //     "payment_type" => $input['payment_type']
        // ];
        // $salesPayment = $this->salesRepository->create($salesPaymentDate);

        // $input['sales_id'] = $sales->id;
        $salesItems = $this->salesItemsRepository->create($input);

        return $this->sendResponse($salesItems->toArray(), 'Sales Items saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/sales_items/{id}",
     *      summary="getSalesItemsItem",
     *      tags={"SalesItems"},
     *      description="Get SalesItems",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SalesItems",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SalesItems"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var SalesItems $salesItems */
        $salesItems = $this->salesItemsRepository->find($id);

        if (empty($salesItems)) {
            return $this->sendError('Sales Items not found');
        }

        return $this->sendResponse($salesItems->toArray(), 'Sales Items retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/sales_items/{id}",
     *      summary="updateSalesItems",
     *      tags={"SalesItems"},
     *      description="Update SalesItems",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SalesItems",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
*      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/SalesItems")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/SalesItems"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateSalesItemsAPIRequest $request)
    {
        $input = $request->all();

        /** @var SalesItems $salesItems */
        $salesItems = $this->salesItemsRepository->find($id);

        if (empty($salesItems)) {
            return $this->sendError('Sales Items not found');
        }

        $salesItems = $this->salesItemsRepository->update($input, $id);

        return $this->sendResponse($salesItems->toArray(), 'SalesItems updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/sales_items/{id}",
     *      summary="deleteSalesItems",
     *      tags={"SalesItems"},
     *      description="Delete SalesItems",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of SalesItems",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var SalesItems $salesItems */
        $salesItems = $this->salesItemsRepository->find($id);

        if (empty($salesItems)) {
            return $this->sendError('Sales Items not found');
        }

        $salesItems->delete();

        return $this->sendSuccess('Sales Items deleted successfully');
    }
}
