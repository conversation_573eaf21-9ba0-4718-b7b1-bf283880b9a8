// store/modules/holds_list.js
import axios from "axios";

const state = {
  holds_list: {},
  isEnableHold: false,
  showHoldList: false,
  lastFetchTime: null, // To track the last fetched hour
  isFetching: false,
  };

  const mutations = {
      SET_HOLDSLIST(state, { data}) {
        //   console.log(data, 'data', pagination, 'pagination', status_counts, 'status');
          state.holds_list = data;
    },
    setUpdateEnable(state, { isEnableHold }) {
      state.isEnableHold = isEnableHold;      
    },
    setShowList(state, { showList }) {
      state.showHoldList = showList;      
    },
    SET_LAST_FETCH_TIME(state, time) {
      state.lastFetchTime = time; // Save the timestamp when the API was last accessed
    },
    SET_IS_FETCHING(state, status) {
      state.isFetching = status; // Set the API request status (true for ongoing, false for completed)
    },
      RESET_STATE(state) {
        state.holds_list = {};
        state.isEnableHold = false;
        state.showHoldList = false;
        state.lastFetchTime = null;
      state.isFetching = false;
      }

  };

  const actions = {
    updateHoldsName({ commit }, holds_listData) {
      // Simulate an asynchronous operation (e.g., API call) to update holds_list name
      setTimeout(() => {
        // Commit mutation to update holds_list name
        commit('SET_HOLDSLIST', holds_listData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchHoldsList({ state, commit, rootState }) {
      const now = new Date().toISOString();
      const thirtySecondsInMilliseconds = 10 * 1000;
      const lastUpdateTime = rootState.apiUpdates.lastApiUpdates['holdlist_update']; 
  
      // If the request is ongoing or if the last request was made within the last 30 seconds, don't send another request
      if (state.isFetching || (state.lastFetchTime && new Date(now) - new Date(state.lastFetchTime) < thirtySecondsInMilliseconds) || lastUpdateTime < state.lastFetchTime)  {
        return; // Skip request if less than 30 seconds have passed since the last request
      }  
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          commit('SET_IS_FETCHING', true);
          axios.get('/hold_invoices', { params: { company_id: company_id, page: 1, per_page: 'all' } })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Holds list..!');
              let { data} = response.data;             
                // console.log(data, 'data', pagination, 'pagination', status_counts, 'status', 'Initialllllly');
              commit('SET_HOLDSLIST', { data });
              commit('SET_LAST_FETCH_TIME', now); // Update the last fetch time to the current time
              commit('SET_IS_FETCHING', false);
              return data;
            })
            .catch(error => {
              // Handle error
              commit('SET_IS_FETCHING', false);
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        commit('SET_IS_FETCHING', false);
        console.error('Error fetching item list:', error);
      }
    },
    updateIsEnable({ commit, state }, isEnable) {
      // console.log(isEnable, 'EEEEEEEEE Enable the list.....');

      commit('setUpdateEnable', { isEnableHold: isEnable });    
    },
    updateIsShowList({ commit, state }, isEnable) {
      // console.log(isEnable, 'EEEEEEEEE');
      
      commit('setShowList', {  showList: isEnable });    
    }    
  };

  const getters = {
    currentHoldsList(state) {
      return state.holds_list;
    },
    isEnable: state => state.isEnableHold,
    isShowList: state => state.showHoldList,
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
