// store/modules/subscription.js
import axios from "axios";

const state = {
  subscription: [],
  };

  const mutations = {
      SET_SUBSCRIPTION(state, {data}) {
          state.subscription = data;
    },
      RESET_STATE(state) {
          state.subscription = [];
      }
  };

  const actions = {
    updateSubscriptionName({ commit }, subscriptionData) {
      setTimeout(() => {
        // Commit mutation to update subscription name
        commit('SET_SUBSCRIPTION', subscriptionData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchSubscriptionList({ commit }) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};        
        if (company_id && company_id !== '') {
          axios.get(`/plans`, { params: { company_id: company_id} })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Subscription list..!');
              let { data} = response.data; 
              commit('SET_SUBSCRIPTION', {data});
              return data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    
  };

  const getters = {
    currentSubscriptionList(state) {
      return state.subscription;
    },
  };

  export default {
    namespaced: true,
    state,
    mutations,
    actions,
    getters
  };
