<template>
    <div class="non-printable headBar" :class="{ 'fixed top-0 left-0 z-50 w-full': isMobile }">
        <div class="hidden fixed-header flex justify-between items-center" style="background-color: #9d174d;">
            <!-- Menu icon button for mobile view -->
            <button v-if="isMobile" @click="toggleSidebar" class="block sm:hidden text-xl px-2 sm:px-4 py-2 text-white">
                ☰
            </button>
            <div v-if="!isMobile"
                class="h-[18px] sm:h-[18px] text-white text-left center text-lg sm:text-lg items-center flex justify-center p-5">
                Services
            </div>
            <!--search bar-->
            <div class="relative text-sm">
                <p v-if="searchQuery !== ''"
                    class="absolute flex justify-end right-1 mt-[5px] text-red-500 cursor-pointer"
                    @click="clearSearchQuery()">
                    <i class="material-icons">clear</i>
                </p>
                <input type="text" :placeholder=placeholderText
                    class="border border-gray-300 rounded p-2 focus:border-blue-500 outline-none lg:w-[500px] bg-search-icon"
                    :class="{ 'w-[250px]': isMobile }" v-model="searchQuery"
                    @input="handleDropdownInput(), searchQuerydata" @change="showDropdown" @focus="handleDropdownInput"
                    @blur="closeDropdown" @keydown.enter="handleEnterKey(filteredCustomerOptions)"
                    @keydown.down.prevent="handleDownArrow(filteredCustomerOptions)"
                    @keydown.up.prevent="handleUpArrow(filteredCustomerOptions)" ref="searchInput" />
                <!-- Dropdown container -->
                <div v-if="showSuggestions" class="absolute mt-2 bg-white border rounded shadow-md w-full"
                    :style="{ 'z-index': 999, 'max-height': '200px', 'overflow-y': 'auto' }"
                    @mousedown.prevent="preventBlur">
                    <!-- List of customer suggestions -->
                    <ul style="max-height: 200px;">
                        <li v-for="(leads, index) in filteredCustomerOptions" :key="index"
                            @click="selectCustomer(leads)" :class="{ 'bg-gray-300': index === selectedIndex }">
                            {{ leads.first_name + ' ' + (leads.last_name ? leads.last_name : '') + ' - ' +
                                leads.contact_number
                            }}
                        </li>
                    </ul>
                </div>
            </div>
            <div class="flex items-center p-2">
                <p v-if="!isMobile" class="text-xs px-3 text-white">
                    <font-awesome-icon icon="fa-solid fa-phone" :style="{ color: 'white' }" />
                    Help: <a href="https://eagleminds.net" target="_blank"
                        class="hover:text-green-700 hover:underline">+91-8233823309</a>
                </p>
                <!-- <img class="h-[25px] sm:h-[30px] mr-3" :src="images[1]" alt="notification" /> -->
                <!-- <span
                class="inline-flex items-center rounded-full bg-red-500 pl-1 pr-1 text-[8px] sm:text-[10px] font-medium text-white ring-1 ring-inset ring-red-600/10 absolute ml-3 sm: mr-5 top-[15px] sm:top-[30px]">
                {{ showBadges_notify }}
            </span>
            <span
                class="inline-flex items-center rounded-full bg-violet-500 pl-1 pr-1 text-[8px] sm:text-[10px] font-medium text-white text-center ring-1 ring-inset ring-green-600/10 right-[15px] mt-5 sm:right-[15px] sm: mt-3 absolute">
                &#10003;
            </span> -->
                <img class="w-[35px] h-[35px] mr-2" :src="images[0]" alt="user" @click="showLogoutModal = true" />
            </div>
            <div v-show="showLogoutModal" class="absolute z-50 transform right-[210px] mt-10">
                <div v-show="showLogoutModal" class="absolute bg-white border border-gray-300 p-4 rounded shadow-md">
                    <h3 class="text-lg font-semibold mb-2">Confirm Logout</h3>
                    <p class="text-sm mb-4">Are you sure you want to logout?</p>
                    <div class="flex justify-between">
                        <button @click="logout"
                            class="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 focus:outline-none focus:bg-red-600 mr-3">Logout</button>
                        <button @click="showLogoutModal = false"
                            class="px-4 py-2 bg-gray-300 text-gray-700 rounded hover:bg-gray-400 focus:outline-none focus:bg-gray-400">Cancel</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex justify-start px-4 py-2 items-center text-white set-header-background">
            <div class="pr-5 py-2 cursor-pointer" @click="goBack">
                <span>
                    <font-awesome-icon icon="fa-solid fa-arrow-left" size="lg" />
                </span>
            </div>
            <div class="w-full">
                <h6 class="text-xl">Services</h6>
            </div>
            <div>
                <button @click="refreshPage" :title01="'Refresh'" class="flex items-center">
                    <!-- <img :src="refresh_icon" alt="refresh_icon" class="w-6 h-6" /> -->
                    <font-awesome-icon icon="fa-solid fa-rotate-right" size="lg" />
                </button>
            </div>
        </div>
    </div>
</template>
<script>
import { mapState, mapGetters, mapActions } from 'vuex';
export default {
    name: 'headbar',
    props: {
        leadData: String,
    },
    data() {
        return {
            images: [
                '/images/head_bar/User.png',
                '/images/head_bar/Notification.png',
            ],
            showBadges_notify: 2,
            showBadges_status: true,
            isMobile: false,
            searchQuery: '',
            showSuggestions: false,
            placeholderText: '',
            selectedIndex: 0,
            mouseDownOnDropdown: false,
            showLogoutModal: false,
            filterData: [],
            customer_list: [],
            pagination_data: {},
            filteredCustomerOptions: [],
            companyId: null,
            userId: null
        };
    },

    methods: {
        goBack() {
            // this.$router.go(-1);
            this.$router.push('/');

        },
        refreshPage() {
            // window.location.reload();
            this.$emit('refresh_store');
        },
        toggleSidebar() {
            this.$emit('toggle-sidebar');
        },
        updateIsMobile() {
            // Update isMobile based on the viewport width
            // this.$forceUpdate();
            this.isMobile = window.innerWidth < 1024;
        },
        //----dropdown---
        filterLeads() {
            if (this.searchQuery !== '' && this.searchQuery.length > 1) {
                // console.log(this.searchQuery, 'YYYYYYY');
                // Update the filtered customer list on input change
                this.showSuggestions = true;
            } else {
                this.showSuggestions = true;
                this.$emit('searchData', {});
            }
        },
        showDropdown() {
            if (this.searchQuery !== '') {
                // Show dropdown on input focus
                this.showSuggestions = true;
                // Add a click event listener to the document to close the dropdown when clicking outside
                // document.addEventListener('click', this.handleDocumentClick);
            }
        },
        hideDropdown() {
            // Hide dropdown on input blur
            this.showSuggestions = false;
            // Remove the click event listener when hiding the dropdown
            // document.removeEventListener('click', this.handleDocumentClick);
        },
        selectCustomer(lead) {
            // Handle the selected customer (e.g., emit an event, update data, etc.)
            // console.log('Selected Customer:', lead);
            this.searchQuery = lead.last_name ? lead.first_name + ' ' + lead.last_name + ' - ' + lead.contact_number : lead.first_name + ' - ' + lead.contact_number;
            this.$store.dispatch('service/updateSearchQuery', this.searchQuery);
            this.$store.dispatch('service/updateSearchData', lead);
            this.showSuggestions = false; // Hide the dropdown after selecting a customer
            this.$emit('searchData', lead); //---filter data
            // document.removeEventListener('click', this.handleDocumentClick); // Remove the event listener after selection
        },
        handleDocumentClick(event) {
            // Close the dropdown when clicking outside the input and dropdown
            const isClickInside = this.$refs.searchInput.contains(event.target) || this.$refs.searchInput.nextElementSibling.contains(event.target);
            if (!isClickInside) {
                this.hideDropdown();
            }
        },
        isNumeric(str) {
            // Helper method to check if a string is numeric
            return /^\d+$/.test(str);
        },
        //---additional function
        handleEnterKey(optionArray) {
            // console.log(optionArray, 'Waht happening...!')
            // Check if filteredProductList has at least one item
            if (optionArray && optionArray.length > 0) {
                // Call selectedProductData with the first item in filteredProductList
                this.selectCustomer(optionArray[this.selectedIndex])
                // this.selectedProductData(this.formValues.items[index], 0, this.filteredProductList[this.selectedIndex]);
                this.selectedIndex = 0;
                // this.$refs.enterQuantity.focus();
            }
        },

        //----up and down arrows functions
        handleDownArrow(passArray) {
            if (passArray.length > this.selectedIndex + 1) {
                this.selectedIndex = this.selectedIndex + 1;
            } else if (passArray.length - 1 === this.selectedIndex) {
                this.selectedIndex = 0;
            }
        },
        handleUpArrow(passArray) {
            if (this.selectedIndex === 0 && passArray.length > 1) {
                this.selectedIndex = passArray.length - 1;
            } else {
                this.selectedIndex = this.selectedIndex - 1;
            }
        },

        //----customer dropdown--
        closeDropdown() {
            if (!this.mouseDownOnDropdown) { //---not passed parameter in fields
                this.showSuggestions = false;
            }
        },

        preventBlur() {
            this.mouseDownOnDropdown = true;
            setTimeout(() => {
                this.mouseDownOnDropdown = false;
            });
        },
        logout() {
            // Clear the 'track_new' key from local storage
            localStorage.removeItem('track_new');
            this.$router.push('/login');
        },
        getCustomerList(page, per_page) {
            // console.log(page, per_page, 'What happening..!', this.companyId)
            axios.get('/customers', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                .then(response => {
                    // Handle response
                    // console.log(response.data, 'Customer list..!');
                    this.customer_list = response.data.data;
                    this.pagination_data = response.data.pagination;
                    if (this.customer_list.length > 2) {
                        // Sorting the array alphabetically based on the 'name' key
                        this.customer_list.sort((a, b) => {
                            // Convert both names to lowercase to ensure case-insensitive sorting
                            const nameA = a.first_name.toLowerCase();
                            const nameB = b.first_name.toLowerCase();
                            // Compare the two names
                            if (nameA < nameB) {
                                return -1;
                            }
                            if (nameA > nameB) {
                                return 1;
                            }
                            return 0;
                        });
                    }
                })
                .catch(error => {
                    // Handle error
                    console.error('Error:', error);
                });
        },
        //---customer--
        ...mapActions('service', ['updateSearchQuery']),
        ...mapActions('service', ['updateSearchData']),
        handleDropdownInput(event) {
            // console.log(this.customer_list, 'This is customer list..@')
            const inputValue = this.searchQuery;
            if (event && event.target.value) {
                this.updateSearchQuery(event.target.value);
            }
            this.showSuggestions = true;
            // console.log(inputValue, 'WWWWWWW');

            if (inputValue !== undefined && inputValue !== null && inputValue.length > 3) {
                if (!isNaN(inputValue)) {
                    // If input is a number, filter options by phone number
                    const inputNumber = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.contact_number.toLowerCase().includes(inputNumber)
                    );
                    if (this.filteredCustomerOptions.length === 0 && this.pagination_data && (1 * this.pagination_data.current_page) !== (1 * this.pagination_data.last_page)) {
                        this.getCustomerList((1 * this.pagination_data.current_page) + 1, 'all');
                    }
                } else {
                    // If input is not a number, filter options by name
                    const inputName = inputValue.toLowerCase();
                    this.filteredCustomerOptions = this.customer_list.filter(
                        (option) => option.last_name ? (option.first_name + ' ' + option.last_name + ' - ' + option.contact_number).toLowerCase().includes(inputName) :
                            (option.first_name + ' - ' + option.contact_number).toLowerCase().includes(inputName)
                    );
                    if (this.filteredCustomerOptions.length === 0 && this.pagination_data && (1 * this.pagination_data.current_page) !== (1 * this.pagination_data.last_page)) {
                        this.getCustomerList((1 * this.pagination_data.current_page) + 1, 'all');
                    }
                    // console.log(this.filteredCustomerOptions, 'What happening...!');
                }


            } else {
                if (this.customer_list.length > 0) {
                    this.filteredCustomerOptions = this.customer_list;
                } else {
                    this.filteredCustomerOptions = [];
                }
            }

        },
        clearSearchQuery() {
            this.searchQuery = '';
            this.$emit('searchData', {});
            this.updateSearchQuery(this.searchQuery);
            const newData = {};
            this.updateSearchData(newData);
        }

    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile(); // Initial check
        // Add a resize event listener to dynamically update isMobile
        window.addEventListener('resize', this.updateIsMobile);
        this.placeholderText = 'Search Customer name or contact number';
        // if (this.customer_list.length === 0 || !(this.pagination && this.pagination.current_page === 1)) {
        //     this.getCustomerList(1, 'all');
        // }
    },
    beforeDestroy() {
        // Remove the resize event listener when the component is destroyed
        window.removeEventListener('resize', this.updateIsMobile);
    },
    computed: {
        ...mapState('service', ['search_query']),
        filteredLeads() {
            if (this.searchQuery !== '') {
                const query = this.searchQuery.toLowerCase().trim();
                const filteredData = {};

                this.leadData.forEach(data => {
                    if (data.customer) {
                        let name = data.customer.last_name ?
                            (data.customer.first_name + ' ' + data.customer.last_name).toLowerCase() :
                            data.customer.first_name.toLowerCase();
                        const contactNumber = data.customer.contact_number.toString(); // Convert contact number to string

                        // Check if query matches customer's name or contact number
                        if (name.includes(query) || contactNumber.includes(query)) {
                            // Store data in an object to ensure uniqueness
                            filteredData[name] = data;
                        }
                    }
                });
                // Convert the object back to an array
                return Object.values(filteredData);
            } else {
                const filteredData = {};
                this.leadData.forEach(data => {
                    if (data.customer) {
                        let name = data.customer.last_name ?
                            (data.customer.first_name + ' ' + data.customer.last_name).toLowerCase() :
                            data.customer.first_name.toLowerCase();
                        // Store data in an object to ensure uniqueness
                        filteredData[data.customer.id] = data;
                    }
                });
                // Convert the object back to an array
                return Object.values(filteredData);
            }
        },

        searchQuerydata() {
            // console.log(this.getSearch, 'RRRRRasaa');
            this.searchQuery = this.getSearch;
        }
    },
    watch: {
        // leadData(newValue) {
        //     console.log(newValue, 'EEEEEE');
        //     this.searchQuery = newValue;
        // },
        search_query(newValue) {
            // Watch for changes in Vuex state and update local searchQuery
            this.searchQuery = newValue;
        },
    }
};
</script>
<style scoped>
.bg-search-icon {
    background-image: url('/images/customer_page/search.png');
    /* Replace with the path to your icon */
    background-repeat: no-repeat;
    background-position: left 8px center;
    /* Adjust the left and top values as needed */
    padding-left: 45px;
    /* Adjust based on the width of your icon */
}

/* Style for the dropdown */
ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

li {
    padding: 8px;
    cursor: pointer;
}

li:hover {
    background-color: #f0f0f0;
}

@media print {

    /* Additional styles for printing */
    body {
        background-color: white;
    }

    .non-printable {
        display: none;
    }
}
</style>
