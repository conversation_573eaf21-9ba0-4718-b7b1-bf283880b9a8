<template>
  <div v-if="showModal" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
    <div class="relative bg-white w-full lg:w-3/4 mx-auto rounded-lg shadow-lg p-4 h-screen overflow-y-auto">
      <!-- Modal Header -->
      <div class="web-modal-head">
        <h2 class="text-md font-medium">{{ isEditMode ? 'Edit Service' : 'Add Service' }}</h2>
        <button @click="closeModal" class="text-red-600 hover:text-red-500"><span><font-awesome-icon
              icon="fa-solid fa-xmark" /></span></button>
      </div>

      <!-- Modal Body -->
      <div class="space-y-2 text-sm">
        <div>
          <label class="block mb-1">Title <span class="text-red-500">*</span></label>
          <input type="text" v-model="serviceData.title" class="border p-2 text-sm rounded w-full"
            placeholder="Enter title here" required />
        </div>
        <div>
          <label class="block mb-1">Short Description <span class="text-red-500">*</span></label>
          <!-- <textarea v-model="serviceData.shortDescription" class="border p-2  rounded w-full"
            placeholder="Enter short description here" required></textarea> -->
          <CkEditorForm label="Short Description" :textData="serviceData.shortDescription || ''"
            @editorSubmit="handleEditorSubmitShort" :company_id="company_id"></CkEditorForm>
        </div>
        <div>
          <label class="block mb-1">Long Description <span class="text-red-500">*</span></label>
          <!-- <textarea v-model="serviceData.description" class="border p-2  rounded w-full"
            placeholder="Enter brief description here" required></textarea> -->
          <CkEditorForm label="Long Description" :textData="serviceData.description || ''"
            @editorSubmit="handleEditorSubmitLong" :company_id="company_id"></CkEditorForm>
        </div>
        <!-- Service Images Upload -->
        <div class="mb-1">
          <label class="block text-gray-600  mb-1">Service Images <span class="text-gray-500 text-xs">500
              <font-awesome-icon icon="fa-solid fa-xmark" /> 500</span></label>
          <input type="file" @change="onImageChange" ref="imageServices" accept="image/png, image/jpeg, image/webp"
            class="border p-2 rounded w-full" multiple />
          <div v-if="circle_loader_photo"
            class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
            <CircleLoader :loading="true"></CircleLoader>
          </div>
          <div v-if="serviceData.imageUrl && !Array.isArray(serviceData.imageUrl)" class="mt-4 relative">
            <p class="text-xs text-gray-500 mt-1">Upload format: png, webp, jpg</p>
            <img v-if="serviceData.imageUrl" :src="serviceData.imageUrl" alt="Image Preview"
              class="w-full h-16 object-cover rounded-lg" />
            <button @click="removeImage"
              class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
              &times;
            </button>
          </div>
          <!-- Image Preview List -->
          <div v-if="serviceData.imageUrl && Array.isArray(serviceData.imageUrl) && serviceData.imageUrl.length"
            class="space-y-3">
            <p class="text-xs text-gray-500 mt-1">Upload format: png, webp, jpg (Max 4 images & size 500 * 500)</p>
            <div v-for="(image, index) in serviceData.imageUrl" :key="index"
              class="relative inline-block mb-2 mr-2 w-24 h-24 px-2" draggable="true"
              @dragstart="onDragStart(index, $event)" @dragover="onDragOver($event)" @drop="onDrop(index, $event)"
              @dragenter.prevent @dragleave.prevent>
              <img :src="image" alt="Image Preview" class="w-full h-full object-cover rounded-lg" />

              <button @click="removeImage(index)"
                class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
                &times;
              </button>
            </div>
          </div>
        </div>
        <!---services videos-->
        <div class="mb-1 space-y-2">
          <label class="block text-gray-600 text-sm">Services video URL
            <span class="text-xs">(Give public video access)</span></label>
          <div class="flex">
            <input type="url" ref="serviceVideo" v-model="serviceData.service_video"
              placeholder="Enter video URL (e.g., YouTube, Google Drive, etc.)" class="border p-2 rounded-l w-full" />

            <!-- Button to open the video -->
            <div v-if="serviceData.service_video && serviceData.service_video !== ''" class="flex ">
              <button @click="viewVideo" class="bg-blue-500 text-white p-1 px-2 rounded-r shadow hover:bg-blue-600">
                <font-awesome-icon icon="fa-solid fa-eye" />
              </button>
            </div>
          </div>
        </div>

        <!---service brochure-->
        <div class="mb-1 space-y-2">
          <label class="block text-gray-600 text-sm">Service Brochure (<span class="text-gray-500 text-xs">Max
              2MB</span>)</label>
          <div class="flex relative">
            <input type="file" ref="brochurefile" @change="handleFileUploadBrochure"
              class="border p-2 rounded-l w-full" />
            <div v-if="circle_loader_brochure"
              class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
              <CircleLoader :loading="true"></CircleLoader>
            </div>
            <!-- Button to open the brochure -->
            <div v-if="serviceData.service_brochure && serviceData.service_brochure !== ''" class="flex ">
              <button @click="viewBrochure" class="bg-blue-500 text-white p-1 px-2 rounded-r shadow hover:bg-blue-600">
                <font-awesome-icon icon="fa-solid fa-eye" />
              </button>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <label class="block mb-1">Price (Optional)</label>
            <input type="number" v-model="serviceData.price" class="border p-2 rounded  w-full"
              placeholder="Enter Price" />
          </div>
          <div>
            <label class="block font-normal mb-1">Services Per Unit</label>
            <input type="text" v-model="serviceData.servicePer" placeholder="Enter Price unit"
              class="border p-2 rounded w-full" />
            <!-- <select v-model="serviceData.servicePer" class="border p-2  rounded w-full">
              <option disabled value="">Select visit</option>
              <option value="per visit">Per Visit</option>
              <option value="per hour">Per Hour</option>
              <option value="per month">Per Month</option>
            </select> -->
          </div>
        </div>
        <!--services category-->
        <!-- <div class="flex-1">
          <label class="block text-gray-600 text-sm mb-1">Service category <span class="text-red-600">*</span></label>
          <div class="flex">
            <select v-model="serviceData.category" class="border p-2 rounded w-full">
              <option disabled value="">Select category</option>
              <option v-for="opt in categories" :key="opt.id" :value="opt.id">
                {{ opt.name }}
              </option>
            </select>
            <button @click="openAddCategoryModal"
              class="bg-green-600 text-white px-2 rounded-r hover:bg-green-500"><span><font-awesome-icon
                  icon="fa-solid fa-plus" /></span></button>
          </div>
        </div> -->
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-end space-x-4 mt-6 border-t pt-4">
        <button @click="closeModal" class="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400">Cancel</button>
        <button @click="saveService" class="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600">
          {{ isEditMode ? 'Save Changes' : 'Add Service' }}
        </button>
      </div>
    </div>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <Loader :showModal="open_loader"></Loader>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    <!-- Categories Modal -->
    <!-- <productCategoryModal :show="isModalVisible" :categories="categories" @close="closeModalCategory"
      @save="saveCategory" @toasterMessages="toasterMessages" /> -->
    <dialogConfirmBox :visible="show_dialog" :message="message" :type="'website'" @ok="closeModalconfirm"
      @cancel="cancelcloseModal" @save="saveService"></dialogConfirmBox>
  </div>
</template>

<script>
import Modal from '../../components/website-builder/Modal.vue';
import imageService from '../../services/imageService';
import confirmbox from '@/components/supporting/dialog_box/confirmbox.vue';
import productCategoryModal from './productCategoryModal.vue';
import CkEditorForm from './CkEditorForm.vue';
import dialogConfirmBox from '@/components/supporting/dialog_box/dialogConfirmBox.vue';
import { mapActions, mapGetters } from 'vuex';

export default {
  components: {
    Modal,
    confirmbox,
    productCategoryModal,
    CkEditorForm,
    dialogConfirmBox
  },
  props: {
    company_id: {
      type: String,
      required: true, // `company_id` must always be passed
    },
    showModal: {
      type: Boolean,
      default: false, // Default to `false` if not explicitly passed
    },
    isEditMode: {
      type: Boolean,
      default: false, // Default to `false` for clarity in edit mode handling
    },
    service: {
      type: Object,
      default: () => ({}), // Default to an empty object if no service data is passed
    },
    categories: {
      type: [Object, undefined], // Accepts both `Object` and `undefined`
      required: false, // Not strictly required if undefined is allowed
      default: undefined, // Defaults to undefined explicitly
    },
  },
  data() {
    return {
      serviceData: {},
      // {
      //   title: '',
      //   description: '',
      //   shortDescription: '',
      //   imageUrl: null,
      //   price: '',
      //   servicePer: '',
      // },
      imagePreview: null,
      imageFile: null,
      //--loader--
      circle_loader_photo: false,
      circle_loader_brochure: false,
      open_loader: false,
      //--toaster---
      show: false,
      type_toaster: 'success',
      message: '',
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      //--categories--
      isModalVisible: false,
      //---dialog alert---
      show_dialog: false,
    };
  },
  computed: {
    ...mapGetters('websiteBuilder', ['isModified']),
  },
  watch: {
    // Watch the service prop for editing
    service: {
      immediate: true,
      handler(newVal) {
        if (newVal) {
          this.serviceData = { ...newVal };
          if (this.serviceData && this.serviceData.imageUrl && !Array.isArray(this.serviceData.imageUrl)) {
            let makeArr = [];
            makeArr.push(this.serviceData.imageUrl);
            this.serviceData.imageUrl = makeArr;
          }
          this.imagePreview = newVal.imageUrl || null;
        }
      },
    },
    serviceData: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.validateForm([this.service, newValue]);
        }
      }
    },
    showModal: {
      deep: true,
      handler(newValue) {
        if (newValue && Object.keys(this.serviceData).length === 0) {
          this.serviceData = {
            title: '',
            description: '',
            shortDescription: '',
            imageUrl: null,
            price: '',
            servicePer: '',
          };
        }
        if (newValue) {
          this.show = false;
        }
      }
    }
  },
  mounted() {
    if (this.service && this.service.imageUrl && !Array.isArray(this.service.imageUrl)) {
      let makeArr = [];
      makeArr.push(this.service.imageUrl);
      this.service.imageUrl = makeArr;
    }
  },
  methods: {
    ...mapActions('websiteBuilder', ['validateForm']),
    generateSlug(title) {
      // Replace spaces with dashes, remove special characters, and convert to lowercase
      return title
        .toLowerCase()
        .trim()
        .replace(/[\s]+/g, '-')
        .replace(/[^\w-]+/g, '');
    },
    async onImageChange(event) {
      this.circle_loader_photo = true;
      const files = event.target.files; // Get all selected files

      if (files.length + (this.serviceData.imageUrl ? this.serviceData.imageUrl.length : 0) > 4) {
        this.toasterMessages({ msg: 'You can upload a maximum of 4 images.', type: 'warning' });
        this.circle_loader_photo = false;
        this.$refs.imageServices.value = '';
        return;
      }

      const newImages = [];
      try {
        // Loop over each file and upload them one by one
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          if (file) {
            // Upload the image and store the returned URL
            const response = await imageService.uploadImage(file, 'services', this.company_id);
            newImages.push(response.media_url); // Push each image URL to newImages array

          }
        }
        this.toasterMessages({ msg: 'Image uploaded successfully!', type: 'success' });
      } catch (error) {
        console.error('Error uploading image:', error);
        this.toasterMessages({ msg: 'Failed to upload image.', type: 'warning' });
      }

      // Add uploaded images to the serviceData array
      if (newImages.length > 0 && Array.isArray(this.serviceData.imageUrl)) {
        this.serviceData.imageUrl.push(...newImages); // Add new images to existing images
      } else if (newImages.length > 0 && !Array.isArray(this.serviceData.imageUrl)) {
        this.serviceData.imageUrl = [];
        this.serviceData.imageUrl.push(...newImages);
      }
      this.circle_loader_photo = false;
    },
    async removeImage(index) {
      this.deleteIndex = index;
      this.open_confirmBox = true;
    },
    // Drag Start: Store the index of the item being dragged
    onDragStart(index, event) {
      event.dataTransfer.setData('text/plain', index);
    },

    // Allow drop by preventing the default action
    onDragOver(event) {
      event.preventDefault();
    },

    // Drop: Reorder the image array when dropping
    onDrop(index, event) {
      const draggedIndex = event.dataTransfer.getData('text/plain');
      const draggedImage = this.serviceData.imageUrl[draggedIndex];

      // Move the dragged image to the new position
      this.serviceData.imageUrl.splice(draggedIndex, 1);
      this.serviceData.imageUrl.splice(index, 0, draggedImage);
    },
    async saveService() {
      if (this.serviceData.title && this.serviceData.shortDescription && this.serviceData.description && this.serviceData.title !== '' && this.serviceData.shortDescription !== '' && this.serviceData.description !== '') {
        try {
          if (!this.serviceData.link) {
            this.serviceData.link = await this.generateSlug(this.serviceData.title);
          }
          this.$emit('save', this.serviceData); // Emit service data to parent
          this.resetForm(true);
          this.$emit('close');
        } catch (error) {
          console.error('Error saving service with image:', error);
        }
      } else {
        this.toasterMessages({ msg: 'Please fill in all required fields marked with *', type: 'warning' })
      }
      this.show_dialog = false;
    },
    // Close modal
    closeModal() {
      if (!this.isModified) {
        this.message = 'You have unsaved changes. Do you want to save them or ok or cancel?'
        this.show_dialog = true;
      } else {
        this.resetForm();
        this.$emit('close');

      }
    },
    closeModalconfirm() {
      this.show_dialog = false;
      this.resetForm();
      this.$emit('close'); // Emit close event to hide modal
    },
    cancelcloseModal() {
      this.show_dialog = false;
      // this.$emit('close');
    },
    resetForm(istrue) {
      if (this.isEditMode || istrue) {
        this.serviceData = {};
      }
    },
    async deleteRecord() {
      if (this.serviceData.imageUrl) {
        this.open_loader = true;
        let imageUrl = Array.isArray(this.serviceData.imageUrl) ? this.serviceData.imageUrl[this.deleteIndex] : this.serviceData.imageUrl;
        try {
          await imageService.deleteImage(imageUrl, 'services'); // Delete image from server

          if (Array.isArray(this.serviceData.imageUrl)) {
            this.serviceData.imageUrl.splice(this.deleteIndex, 1);
          } else {
            this.serviceData.imageUrl = null; // Clear image URL
          }
          this.imageFile = null; // Clear image file
          this.$refs.imageServices.value = '';
          this.toasterMessages({ msg: 'Image removed successfully.', type: 'success' });
          this.closeconfirmBoxData();
        } catch (error) {
          console.error('Error deleting image:', error);
          this.toasterMessages({ msg: 'Failed to delete image', type: 'warning' });
          if (error.response.data && error.response.data.error === 'Image not found') {
            if (Array.isArray(this.serviceData.imageUrl) && this.deleteIndex >= 0) {
              this.serviceData.imageUrl.splice(this.deleteIndex, 1);
            } else {
              this.serviceData.imageUrl = null; // Clear image URL
            }
          }
          this.closeconfirmBoxData();
        }
      } else {
        this.closeconfirmBoxData();
      }
    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
      this.open_loader = false;
    },
    //--toaster message---
    toasterMessages(data) {
      this.message = data.msg;
      this.type_toaster = data.type;
      this.show = true;
    },
    //--video--
    viewVideo() {
      if (this.serviceData.service_video) {
        window.open(this.serviceData.service_video, '_blank'); // Open the video URL in a new tab
      } else {
        alert('No video URL entered.'); // Optional alert if no URL is provided
      }
    },
    //--brochure---
    viewBrochure() {
      if (this.serviceData.service_brochure) {
        window.open(this.serviceData.service_brochure, '_blank'); // Open the brochure URL in a new tab
      } else {
        alert('No video URL entered.');
      }
    },
    //--brochure upload----
    async handleFileUploadBrochure(event) {
      if (this.serviceData.service_brochure && this.serviceData.service_brochure !== '') {
        const response = await imageService.deleteImage(this.serviceData.service_brochure, 'products');
        this.serviceData.service_brochure = '';
      }
      this.circle_loader_brochure = true;
      const file = event.target.files[0]; // Get the single selected file
      if (file) {
        try {
          // Upload the file and add it to the brochure list
          const response = await imageService.uploadFile(file, 'products', this.companyId);
          if (response.media_url) {
            this.serviceData.service_brochure = response.media_url;
            this.$refs.brochurefile.value = '';
            this.toasterMessages({ msg: `Brochure "${file.name}" uploaded successfully!`, type: 'success' });
            // this.$emit('save', this.serviceData);
          }
        } catch (error) {
          console.error('Error uploading brochure:', error);
          this.toasterMessages({ msg: 'Failed to upload brochure.', type: 'warning' });
        } finally {
          this.circle_loader_brochure = false;
        }
      } else {
        this.toasterMessages({ msg: 'Please select a brochure file to upload.', type: 'warning' });
        this.circle_loader_brochure = false;
      }
    },
    //--category moadl---
    openAddCategoryModal() {
      this.isModalVisible = true;
    },
    closeModalCategory() {
      this.isModalVisible = false;
    },
    saveCategory(data) {
      if (data && data.length > 0) {
        this.$emit('updatecategory', data);
        this.toasterMessages({ msg: 'Categories save successfully', type: 'success' });
        this.isModalVisible = false;
      }
    },
    //--descriptions--
    handleEditorSubmitShort(data) {
      if (data) {
        this.serviceData.shortDescription = data;
      } else {
        this.serviceData.shortDescription = '';
      }
    },
    handleEditorSubmitLong(data) {
      if (data) {
        this.serviceData.description = data;
      } else {
        this.serviceData.description = '';
      }
    }

  },
};
</script>
