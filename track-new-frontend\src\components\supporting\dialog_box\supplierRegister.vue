<template>
    <div v-if="showModal" class="fixed sm:top-0 right-0 inset-0 bg-black bg-opacity-50 flex justify-end z-150 "
        style="z-index: 100;">

        <!-- Modal -->
        <div class="model bg-white sm:w-1/2 lg:w-1/3 w-full -top-12 sm:top-[20px] overflow-auto sm:bottom-[10px] transform ease-in-out duration-300 h-screen"
            :class="{ 'translate-x-0': isOpen, 'translate-x-full right-12': !isOpen }">
            <!-- <div class="modal-content "> -->
            <div class="justify-between items-center  flex py-3 set-header-background">
                <h2 class="text-white font-bold text-center ml-12 text-lg">{{ type == 'edit' ? 'Edit a Supplier' :
                    'Add Supplier' }}</h2>
                <p class="close pr-5" @click="cancelModal">&times;</p>
            </div>
            <div class="p-4">
                <!-- Content based on selected option -->
                <div class="w-full p-1 pl-4 pr-4">
                    <label for="name" class="text-sm">Supplier Name<span class="text-red-700">*</span></label>
                    <input id="name" v-model="formValues.name" type="text"
                        :class="{ 'border-blue-700': isInputFocus.name }"
                        class="text-sm p-1 mt-1  border rounded w-full outline-none py-2" ref="nameref"
                        @focus="isInputFocus.name = true" @blur="isInputFocus.name = false" />
                </div>
                <div class="w-full p-1 pl-4 pr-4">
                    <label for="contact_number" class="text-sm">Phone Number<span class="text-red-700">*</span></label>
                    <input id="contact_number" v-model="formValues.contact_number" type="tel"
                        @input="validatePhoneNumber(formValues.contact_number)" @focus="isInputFocus.number = true"
                        @blur="isInputFocus.number = false"
                        class="text-sm p-1 mt-1  border rounded w-full outline-none py-2"
                        :class="{ 'border-blue-700': isInputFocus.number }" />
                    <span v-if="validationMessage !== ''" class="block text-xs text-red-700">{{ validationMessage
                        }}</span>
                </div>
                <div class="w-full p-1 pl-4 pr-4">
                    <label for="email" class="text-sm">Email</label>
                    <input id="email" v-model="formValues.email" type="email" @focus="isInputFocus.email = true"
                        @blur="isInputFocus.email = false"
                        class="text-sm p-1 mt-1  border rounded w-full outline-none py-2"
                        :class="{ 'border-blue-700': isInputFocus.email }" />
                </div>
                <div class="w-full p-1 pl-4 pr-4 mb-2">
                    <label for="gst_number" class="text-sm">GST Number</label>
                    <!-- @input="validateGST"-->
                    <input id="gst_number" v-model="formValues.gst_number" type="text" @focus="isInputFocus.gst = true"
                        @blur="isInputFocus.gst = false"
                        class="text-sm p-1 mt-1  border rounded w-full outline-none py-2"
                        :class="{ 'border-blue-700': isInputFocus.gst }" />
                    <span v-if="formValues.gst_number !== '' && gstValidation !== ''"
                        class="text-red-700 text-xs absolute block"
                        :class="{ 'border-blue-700': isInputFocus.gst_number }">{{ gstValidation }}</span>
                </div>
                <!---state code GST-->
                <div class="w-full p-1 pl-4 pr-4">
                    <label for="code" class="text-sm">GST State Code </label>
                    <select v-model="formValues.gst_code"
                        class="text-sm p-1 mt-1  border rounded w-full outline-none py-2">
                        <option v-if="state_code_list.length > 0" v-for="(opt, index) in state_code_list" :key="index"
                            :valu="opt.code">{{ opt.code + ' - ' + opt.name }}</option>
                    </select>
                </div>
                <!--state-->
                <div class="w-full p-1 pl-4 pr-4">
                    <label for="code" class="text-sm">Select State</label>
                    <select v-model="formValues.state"
                        class="text-sm p-1 mt-1  border rounded w-full outline-none py-2">
                        <option disabled value="" class="text-gray-400">state</option>
                        <option v-if="state_list.length > 0" v-for="(opt, index) in state_list" :key="index"
                            :value="opt.name">{{ opt.name }}</option>
                    </select>
                </div>
                <div class="w-full p-1 pl-4 pr-4">
                    <label for="address" class="text-sm">Address<span class="text-red-700">*</span></label>
                    <textarea id="address" v-model="formValues.address" rows="3" @focus="isInputFocus.address = true"
                        @blur="isInputFocus.address = false"
                        class="text-sm p-1 mt-1  border rounded w-full outline-none"
                        :class="{ 'border-blue-700': isInputFocus.address }"></textarea>
                </div>
                <!-- Buttons -->
                <div class="flex justify-end items-center m-3 text-md">
                    <button @click="cancelModal"
                        class="bg-red-700 hover:bg-red-600 mr-8 px-3 py-2 text-white rounded rounded-lg">Cancel</button>
                    <button @click="sendModal" v-if="showButton"
                        class="bg-green-700 hover:bg-green-600 mr-8 px-5 py-2 text-white rounded rounded-lg ">Save</button>
                </div>
                <!-- </div> -->
            </div>
        </div>
        <dialogAlert :show-modal="isMessageDialogVisible" :message="message" @close="closeMessageDialog"></dialogAlert>
        <Loader :showModal="open_loader"></Loader>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>

<script>
import dialogAlert from './dialogAlert.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
    name: 'supplier_register',
    components: {
        dialogAlert,
    },
    props: {
        showModal: Boolean,
        editData: Object,
        type: String,
        userName: String,
        companyId: String,
    },
    data() {
        return {
            isMobile: false,
            isInputFocus: {},
            formValues: {},
            isMessageDialogVisible: false,
            message: '',
            isOpen: false,
            updatedData: null,
            showButton: true,
            validationMessage: '',
            gstValidation: '',
            state_list: [],
            state_code_list: [
                { name: 'JAMMU AND KASHMIR', code: 1 },
                { name: 'HIMACHAL PRADESH', code: 2 },
                { name: 'PUNJAB', code: 3 },
                { name: 'CHANDIGARH', code: 4 },
                { name: 'UTTARAKHAND', code: 5 },
                { name: 'HARYANA', code: 6 },
                { name: 'DELHI', code: 7 },
                { name: 'RAJASTHAN', code: 8 },
                { name: 'UTTAR PRADESH', code: 9 },
                { name: 'BIHAR', code: 10 },
                { name: 'SIKKIM', code: 11 },
                { name: 'ARUNACHAL PRADESH', code: 12 },
                { name: 'NAGALAND', code: 13 },
                { name: 'MANIPUR', code: 14 },
                { name: 'MIZORAM', code: 15 },
                { name: 'TRIPURA', code: 16 },
                { name: 'MEGHALAYA', code: 17 },
                { name: 'ASSAM', code: 18 },
                { name: 'WEST BENGAL', code: 19 },
                { name: 'JHARKHAND', code: 20 },
                { name: 'ODISHA', code: 21 },
                { name: 'CHATTISGARH', code: 22 },
                { name: 'MADHYA PRADESH', code: 23 },
                { name: 'GUJARAT', code: 24 },
                { name: 'DADRA AND NAGAR HAVELI AND DAMAN AND DIU (NEWLY MERGED UT)', code: 26 },
                { name: 'MAHARASHTRA', code: 27 },
                { name: 'ANDHRA PRADESH(BEFORE DIVISION)', code: 28 },
                { name: 'KARNATAKA', code: 29 },
                { name: 'GOA', code: 30 },
                { name: 'LAKSHADWEEP', code: 31 },
                { name: 'KERALA', code: 32 },
                { name: 'TAMIL NADU', code: 33 },
                { name: 'PUDUCHERRY', code: 34 },
                { name: 'ANDAMAN AND NICOBAR ISLANDS', code: 35 },
                { name: 'TELANGANA', code: 36 },
                { name: 'ANDHRA PRADESH (NEWLY ADDED)', code: 37 },
                { name: 'LADAKH (NEWLY ADDED)', code: 38 },
                { name: 'OTHER TERRITORY', code: 97 },
                { name: 'CENTRE JURISDICTION', code: 99 }
            ],
            open_loader: false,
            //--toaster----
            show: false,
            type_toaster: 'warning'
        }
    },
    methods: {
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
        ...mapActions('supplier', ['fetchISupplierList']),
        //--mobile number validation--
        validatePhoneNumber(inputtxt) {
            // Regular expression for phone numbers
            var phoneno = /^(?:0|\+91)?[6-9]\d{9}$/;

            // Check if the input matches the regular expression
            if (inputtxt.match(phoneno)) {

                this.validationMessage = '';
                return true; // Phone number is valid
            } else {

                this.validationMessage = 'Enter valid mobile number';
                return false; // Phone number is invalid
            }
        },
        cancelModal() {
            // this.$emit('close-modal');
            this.isOpen = false;
            // Add a delay to allow the transition before emitting the close event
            setTimeout(() => {
                this.$emit('close-modal');
            }, 300);
        },
        sendModal() {
            this.hideButton();
            //--&& this.formValues.email && this.formValues.gst_number 
            if (this.formValues.name && this.formValues.contact_number && this.formValues.address) {
                this.open_loader = true;
                if (this.type === 'edit') {
                    axios.put(`/suppliers/${this.editData.id}`, { ...this.formValues, company_id: this.companyId })
                        .then(response => {
                            // console.log(response.data);
                            this.updateKeyWithTime('supplier_update');
                            this.updatedData = response.data.data;
                            this.open_loader = false;
                            this.openMessageDialog(this.type === 'edit' ? 'Existing data updated successfully' : 'Added new supplier successfully');
                            this.fetchISupplierList();
                        })
                        .catch(error => {
                            console.error('Error update', error);
                            this.open_loader = false;
                            // this.openMessageDialog(error.response.data.message);
                            this.message = error.response.data.message;
                            this.show = true;
                        })
                }
                else {
                    axios.post('/suppliers', { ...this.formValues, company_id: this.companyId })
                        .then(response => {
                            // console.log(response.data.data);
                            this.updateKeyWithTime('supplier_update');
                            this.updatedData = response.data.data;
                            this.open_loader = false;
                            this.openMessageDialog(this.type === 'edit' ? 'Existing data updated successfully' : 'Added new supplier successfully');
                        })
                        .catch(error => {
                            console.error('Error update', error);
                            this.open_loader = false;
                            // this.openMessageDialog(error.response.data.message);
                            this.message = error.response.data.message;
                            this.show = true;
                        })
                }

            } else {
                this.message = !this.formValues.name ? 'Please enter the supplier name' : !this.formValues.contact_number ? 'Please enter the supplie contact number' : !this.formValues.address ? 'Please enter the address' : 'Please fill all requird fields..!';
                this.show = true;
                // this.openMessageDialog();
            }
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },

        // Add a new method to initialize data when editData is provided
        initializeData() {
            if (this.editData) {
                this.formValues = this.editData;
            } else {
                this.formValues = {};
            }
        },

        //---open alert--
        openMessageDialog(message) {
            this.isMessageDialogVisible = true;
            this.message = message;
        },

        //---close alert--
        closeMessageDialog() {
            this.isMessageDialogVisible = false;
            this.message = '';
            if (this.updatedData) {
                setTimeout(() => {
                    this.$emit('close-modal', this.updatedData);
                }, 300);
                this.formValues = {};
                // this.initializeData();
            } else {
                setTimeout(() => {
                    this.$emit('close-modal');
                }, 300);
                // this.initializeData();
            }
        },
        //---Add new user--
        userAddNew() {
            // console.log(this.userName);
            if (/^\d+$/.test(this.userName)) {
                this.formValues.contact_number = this.userName;
            } else {
                if (this.userName) {
                    this.formValues.name = this.userName
                }

            }
        },
        //---hidden button
        hideButton() {
            this.showButton = false;
            setTimeout(() => {
                this.showButton = true;
            }, 1000); // 10 seconds in milliseconds
        },
        //--validate GST--
        validateGST() {
            const regex = /^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{2}[A-Z]{1}$/;
            if (this.formValues.gst_number !== '') {
                // Check if the input matches the expected format
                if (regex.test(this.formValues.gst_number)) {
                    this.gstValidation = '';
                } else {
                    this.gstValidation = 'Please enter valid GST number';
                }
            }
        },
        //--get state list ----
        getStateList() {
            if (this.state_list.length === 0) {
                axios.post('https://www.nammav2app.in/api/state-list', { country_id: 101 })
                    .then(response => {
                        // console.log(response.data, 'state');
                        this.state_list = response.data;
                    })
                    .catch(error => {
                        console.error('Error', error);
                    })
            }

        },

    },

    watch: {
        showModal(newValue) {
            if (this.state_list.length === 0) {
                this.getStateList();
            }
            setTimeout(() => {
                this.isOpen = newValue;
                this.$nextTick(() => {
                    if (this.$refs.nameref) {
                        this.$refs.nameref.focus();
                        this.$refs.nameref.click();
                    }
                });
            }, 100);
        },
        // Watch for changes in the editData prop and initialize data accordingly
        editData: {
            immediate: true,
            handler() {
                this.initializeData();
            },
        },
        userName: {
            handler() {
                this.userAddNew();
            },
        }
    },
}
</script>
<style scoped>
.overlay {
    position: fixed;

    background-color: rgba(0, 0, 0, 0.5);
    /* Adjust the alpha value for the desired transparency */
    backdrop-filter: blur(8px);
    /* Add a blur effect */
    z-index: 49;
    /* Ensure the overlay is behind the dialog (z-index of 50) */
    transition: opacity 0.3s ease;
    /* Add a transition for smoother appearance/disappearance */
}

.overlay-active {
    opacity: 1;
}

/* Modal styling */
.modal {
    position: fixed;
    z-index: 50;
    background-color: white;
    border: 1px solid #ddd;
    border-radius: 0px;
}

.close {
    color: #ffffff;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.transition-transform-custom {
    transition-property: transform;
    transition-timing-function: ease-in-out;
    transition-duration: 0.10s;
}

.close:hover,
.close:focus {
    color: rgb(233, 5, 5);
    text-decoration: none;
    cursor: pointer;
}


/* Responsive styling for mobile view */
@media (max-width: 767px) {
    .modal {
        height: 100%;
        /* Adjust the height as needed */
    }

    .modal-content {
        overflow-y: auto;
    }
}
</style>