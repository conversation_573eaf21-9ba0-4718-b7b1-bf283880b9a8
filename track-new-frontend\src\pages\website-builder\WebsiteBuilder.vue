<template>
  <div>
    <main-layout :username="domainName" class="overflow-y-hidden">
      <div class="builder-page flex relative">
        <!-- Main content area -->
        <div class="flex flex-col flex-grow overflow-y-auto margin-web">
          <div class="flex">
            <!-- VerticalBar for desktop and tablets -->
            <!-- <div v-if="!isMobile" class="w-[250px] bg-white border-r">
              <VerticalBar @item-selected="handleItemSelected" :isMobile="isMobile" :selectedItem="selectedItem" />
            </div> -->

            <!-- Main content display -->
            <div class="flex-grow">
              <div class="flex lg:flex-row flex-col justify-between">
                <div class="w-full lg:p-5 lg:border lg:border-gray-300">
                  <component :is="currentPage" :isLoading="isLoading" :progress="progress" :companyId="companyId"
                    :domain="domainName" :username="currentUsername" :websiteSettingsData="websiteSettingsData"
                    :sectionType="homePageSliderData.sectionType" :heroData="homePageSliderData.heroData"
                    :slideshowImages="homePageSliderData.slideshowImages" :aboutData="aboutData"
                    :servicesData="servicesData" :brochureUrl="brochureUrl" :testimonialData="testimonialData || []"
                    :productsData="productsData?.products || []"
                    :productCategoriessData="productsData?.categories || [{ id: 100, name: 'General' }]"
                    :folders="galleryData" :companyVideoData="companyVideoData" :pages="pages"
                    @updateWebsiteSettings="updateWebsiteSettings" @updateSectionType="updateSectionType"
                    @updateHeroData="updateHeroData" @updateSlideshowImages="updateSlideshowImages"
                    @updateAboutData="updateAboutData" @updateServices="updateServices"
                    @updateBrochureUrl="updateBrochureUrl" @updateTestimonials="updateTestimonials"
                    @updateProducts="updateProducts" @updateProductCategories="updateProductCategories"
                    @updateServiceCategories="updateServiceCategories" @goToNextPage="goToNextPage"
                    @goToPrevPage="goToPrevPage" @submitData="submitAllData" @submitGallery="submitGalleryData"
                    :is_updated="is_updated" @toasterMessages="toasterMessages" @updateLoader="updateLoader"
                    @updatecompanyVideo="updatecompanyVideo" @updatePagesSetting="updatePagesSetting"
                    :isMobile="isMobile" />
                </div>
                <div v-if="!isMobile">
                  <sidebar></sidebar>
                </div>
              </div>
            </div>
          </div>
        </div>


        <!-- Horizontal Bar for Mobile -->
        <!-- <div v-if="isMobile" class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t p-2 z-50">
          <VerticalBar @item-selected="handleItemSelected" :isMobile="isMobile" />
        </div> -->
      </div>
      <button @click="submitAllData"
        class="floating-save-btn bg-green-500 hover:bg-green-600 text-white font-normal mx-1  py-2 px-4 rounded-full shadow-lg fixed bottom-5 lg:bottom-5 2xl:bottom-10 right-5 lg:right-64 2xl:right-72 z-10">
        Save
      </button>

    </main-layout>
    <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    <Loader :showModal="open_loader"></Loader>
    <noAccessModel :showModal="no_access" @close="closeNoAccess"></noAccessModel>
  </div>

</template>

<script>
import { markRaw } from 'vue';
import MainLayout from '../../layouts/mainLayout.vue';
import GuestLayout from '../../layouts/guestLayout.vue';
import VerticalBar from '../../components/website-builder/VerticalBar.vue';
import WebsiteSettings from './WebsiteSettings.vue';
import AboutPage from './AboutPage.vue';
import ContactPage from './ContactPage.vue';
import BrochurePage from './BrochurePage.vue';
import TestimonialPage from './TestimonialPage.vue';
import HomePageSlider from './SlideshowImages.vue';
import ServicesPage from './ServiceDetails.vue';
import ProductPage from './ProductPage.vue';
import GalleryPage from './Gallery.vue';
import BuilderLoader from '../../components/website-builder/BuilderLoader.vue';
import CompanyVideo from './CompanyVideo.vue';
import { mapActions, mapGetters } from 'vuex';
import WebsiteRegisterPage from './WebsiteRegisterPage.vue';
import BusinessHours from './BusinessHours.vue';
import SiteSetting from './SiteSetting.vue';
import sidebar from '@/layouts/sidebar.vue';
import Categories from './Categories.vue';
import noAccessModel from '@/components/supporting/dialog_box/noAccessModel.vue';

export default {
  data() {
    return {
      isLoading: true,
      progress: 0,
      companyId: null,
      isMobile: false,
      currentPage: null,
      selectedItem: 12,
      websiteSettingsData: {},
      homePageSliderData: {},
      aboutData: {},
      domainName: '',
      servicesData: {},
      brochureUrl: [],
      testimonialData: {},
      productsData: {},
      galleryData: [],
      companyVideoData: [],
      pages: {},
      currentUsername: null,
      //---latest update---
      is_updated: false,
      //--toaster---
      show: false,
      type_toaster: 'success',
      message: '',
      //---loader--
      open_loader: false,
      //---no access---
      no_access: false,
    };
  },
  components: {
    BuilderLoader,
    VerticalBar,
    MainLayout,
    GuestLayout,
    WebsiteSettings,
    AboutPage,
    ContactPage,
    BrochurePage,
    TestimonialPage,
    HomePageSlider,
    ServicesPage,
    ProductPage,
    GalleryPage,
    CompanyVideo,
    BusinessHours,
    SiteSetting,
    sidebar,
    Categories,
    noAccessModel
  },
  mounted() {
    this.fetchWebsiteSettings();
    this.fetchCompanyList();
    this.fetchLocalDataList();

    if (!this.isLoading) {
      this.updateDefaultdata();
    } else {
      this.updateDefaultdata();
    }
    //---get preview link---
    this.fetchWebsiteUrl();
    this.updateIsMobile();
    window.addEventListener('resize', this.updateIsMobile);

  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateIsMobile);
  },
  computed: {
    ...mapGetters('websiteBuilder', ['selectedId']),
    ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),
  },
  methods: {
    ...mapActions('websiteBuilder', ['updateSelectdItem', 'fetchWebsiteUrl']),
    ...mapActions('localStorageData', ['fetchLocalDataList']),
    ...mapActions('companies', ['fetchCompanyList']),
    updateDefaultdata() {
      this.websiteSettingsData = {
        name: '',
        tagline: '',
        logo: null,
        favicon: null,
        intro: '',
        address: '',
        contacts: [{ type: 'support', number: '' }],
        mapAddress: '',
        emails: [],
        timings: [{ day: 'Mon-Sat', openTime: '09:00', closeTime: '18:00', isClosed: false }, { day: 'Sun', openTime: '09:00', closeTime: '18:00', isClosed: true }],
      };
      this.homePageSliderData = {
        sectionType: 'hero',
        heroData: { title: '', description: '', buttonName: '', link: '', imageUrl: '' },
        slideshowImages: [],
      };
      this.aboutData = {
        headTitle: '',
        description: '',
      };
      this.domainName = '';
      this.servicesData = { services: [], categories: [{ id: 100, name: 'General' }] };
      this.brochureUrl = [];
      this.testimonialData = { testimonials: [] };
      this.productsData = { products: [], categories: [{ id: 100, name: 'General' }] };
      this.galleryData = [
        {
          id: 1,
          name: 'General',
          isEditing: false,
          showMenu: false,
          files: [] // This will be populated with image data from the API
        }
      ];
      this.companyVideoData = [];
      this.pages = {};
    },
    async fetchWebsiteSettings(isNew) {
      if (!isNew) {
        this.isLoading = true; // Start loading
        this.progress = 0
      }
      const collectForm = localStorage.getItem('track_new');
      this.progress += 10;
      if (collectForm) {
        const dataParse = JSON.parse(collectForm);
        this.companyId = dataParse.company_id;
        this.userId = dataParse.user_id + '';
        this.progress += 10;
        if (this.companyId) {
          try {
            const response = await axios.get(`/website-list/${this.companyId}`);
            // console.log(response, 'Responses');
            this.progress += 20;
            if (response.data.success) {
              const data_one = response.data.data;
              let data = null;
              if (data_one.primary_data) {
                data = JSON.parse(data_one.primary_data);
              } else {
                this.updateDefaultdata();
              }
              if (Array.isArray(data_one) && data_one.length === 0) {
                this.isRegistered = false;
                this.currentPage = markRaw(WebsiteRegisterPage);
              } else {
                this.isRegistered = true;
                this.registrationData = data;
                if (!isNew) {
                  if (this.selectedId !== undefined) {
                    this.handleItemSelected(this.selectedId);
                  } else {
                    this.currentPage = markRaw(SiteSetting);
                  }
                }
                this.websiteSettingsData.name = response.data.data.website_name;
                this.currentUsername = response.data.data.username;
                //console.log(response.data.data);
                this.domainName = response.data.data.domain_name;
                this.progress += 10;
                // Populate settings
                this.websiteSettingsData = (data && data.websiteSettings) || this.websiteSettingsData;
                this.progress += 10;
                this.homePageSliderData = (data && data.homePageSlider) || this.homePageSliderData;
                this.progress += 10;
                this.aboutData = (data && data.about) || this.aboutData;
                this.progress += 10;
                this.servicesData = (data && data.services) || {};
                this.brochureUrl = (data && data.brochure) || [];
                this.progress += 10;
                this.testimonialData = (data && data.testimonials) || [];
                this.productsData = (data && data.products) || {};
                this.galleryData = JSON.parse(data_one.image_data) || this.galleryData;
                this.companyVideoData = (data && data.company_video) || [];
                this.pages = (data && data.pages) || {};
                this.progress = 100;
              }
            } else {
              console.error('Error:', response.data.message);
              if (response.data.data == null && !response.data.success) {
                this.toasterMessages({ msg: 'Please register your company on this website builder.', type: 'warning' });
                setTimeout(() => {
                  this.$router.push({ name: 'website-dashboard' });
                }, 500);
              }
            }
          } catch (error) {
            console.error("Failed to fetch initial data:", error);
            this.isRegistered = false;
            this.currentPage = markRaw(WebsiteRegisterPage);
          } finally {
            this.isLoading = false; // End loading
          }
        }
      }
    },
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
    updateDomainSettings(data) {
      this.domainData = data;
    },
    updateProducts(data) {
      this.productsData.products = data; // Update the products data
    },
    updateProductCategories(data) {
      this.productsData.categories = data; // Update the products data
    },
    // Methods to update data from each page
    updateWebsiteSettings(data) {
      this.websiteSettingsData = data;
    },
    updateSectionType(newType) {
      // console.log(newType, 'RRRRRRRRRRRRRRRRR');

      this.homePageSliderData.sectionType = newType;
    },
    updateHeroData(newHeroData) {
      this.homePageSliderData.heroData = newHeroData;
    },
    updateSlideshowImages(newImages) {

      this.homePageSliderData.slideshowImages = newImages;
    },
    updateAboutData(updatedData) {
      this.aboutData = updatedData;
    },
    updateServices(data) {
      this.servicesData = data;
    },
    updateServiceCategories(data) {
      this.servicesData.categories = data; // Update the products data
    },
    updateBrochureUrl(newBroUrl) {
      this.brochureUrl = newBroUrl;
    },
    updateTestimonials(dataTest) {

      this.testimonialData = dataTest;
    },
    //--update company video--
    updatecompanyVideo(data) {
      if (data) {
        this.companyVideoData = data;
      }
    },
    goToNextPage() {
      switch (this.selectedItem) {
        case 1:
          this.currentPage = markRaw(SiteSetting);
          this.selectedItem = 2;
          break;
        case 2:
          this.currentPage = markRaw(WebsiteSettings);
          this.selectedItem = 3;
          break;
        case 3:
          this.currentPage = markRaw(HomePageSlider);
          this.selectedItem = 4;
          break;
        case 4:
          this.currentPage = markRaw(AboutPage);
          this.selectedItem = 5;
          break;
        case 5:
          this.currentPage = markRaw(ServicesPage);
          this.selectedItem = 6;
          break;
        case 6:
          this.currentPage = markRaw(BrochurePage);
          this.selectedItem = 7;
          break;
        case 7:
          this.currentPage = markRaw(ProductPage);
          this.selectedItem = 8;
          break;
        case 8:
          this.currentPage = markRaw(Categories);
          this.selectedItem = 9;
          break;
        case 9:
          this.currentPage = markRaw(TestimonialPage);
          this.selectedItem = 10;
          break;
        case 10:
          this.currentPage = markRaw(GalleryPage);
          this.selectedItem = 11;
          break;
        case 11:
          this.currentPage = markRaw(CompanyVideo);
          this.selectedItem = 12;
          break;
        case 12:
          this.currentPage = markRaw(BusinessHours);
          this.selectedItem = 13;
          break;
        default:
          this.currentPage = markRaw(WebsiteDomainPage);
          this.selectedItem = 1;
          break;
      }
    },
    goToPrevPage() {
      switch (this.selectedItem) {
        case 2:
          this.currentPage = markRaw(WebsiteDomainPage);
          this.selectedItem = 1;
          break;
        case 3:
          this.currentPage = markRaw(SiteSetting);
          this.selectedItem = 2;
          break;
        case 4:
          this.currentPage = markRaw(WebsiteSettings);
          this.selectedItem = 3;
          break;
        case 5:
          this.currentPage = markRaw(HomePageSlider);
          this.selectedItem = 4;
          break;
        case 6:
          this.currentPage = markRaw(AboutPage);
          this.selectedItem = 5;
          break;
        case 7:
          this.currentPage = markRaw(ServicesPage);
          this.selectedItem = 6;
          break;
        case 8:
          this.currentPage = markRaw(BrochurePage);
          this.selectedItem = 7;
          break;
        case 9:
          this.currentPage = markRaw(ProductPage);
          this.selectedItem = 8;
          break;
        case 10:
          this.currentPage = markRaw(Categories);
          this.selectedItem = 9;
          break;
        case 11:
          this.currentPage = markRaw(TestimonialPage);
          this.selectedItem = 12;
          break;
        case 12:
          this.currentPage = markRaw(GalleryPage);
          this.selectedItem = 13;
          break;
        default:
          this.currentPage = markRaw(WebsiteDomainPage);
          this.selectedItem = 1;
          break;
      }
    },
    handleItemSelected(pageKey) {
      switch (pageKey) {
        case 1:
          this.currentPage = markRaw(WebsiteTemplate);
          break;
        case 2:
          this.currentPage = markRaw(SiteSetting);
          break;
        case 3:
          this.currentPage = markRaw(WebsiteSettings);
          break;
        case 4:
          this.currentPage = markRaw(HomePageSlider);
          break;
        case 5:
          this.currentPage = markRaw(AboutPage);
          break;
        case 6:
          this.currentPage = markRaw(ServicesPage);
          break;
        case 7:
          this.currentPage = markRaw(BrochurePage);
          break;
        case 8:
          this.currentPage = markRaw(ProductPage);
          break;
        case 9:
          this.currentPage = markRaw(Categories);
          break;
        case 10:
          this.currentPage = markRaw(TestimonialPage);
          this.selectedItem = 10;
          break;
        case 11:
          this.currentPage = markRaw(GalleryPage);
          this.selectedItem = 11;
          break;
        case 12:
          this.currentPage = markRaw(CompanyVideo);
          this.selectedItem = 12;
          break;
        case 13:
          this.currentPage = markRaw(BusinessHours);
          this.selectedItem = 13;
          break;
        default:
          this.currentPage = markRaw(WebsiteDomainPage);
          break;
      }
      this.selectedItem = pageKey;
    },

    submitAllData() {
      if (this.getplanfeatures('website')) {
        this.no_access = true;
      } else {
        this.updateLoader(true);
        this.is_updated = !this.is_updated;
        setTimeout(() => {
          const allData = {
            company_id: this.companyId,
            websiteSettings: this.websiteSettingsData,
            homePageSlider: this.homePageSliderData,
            services: { ...this.servicesData },
            brochure: this.brochureUrl,
            testimonials: this.testimonialData,
            products: { ...this.productsData },
            about: this.aboutData,
            company_video: this.companyVideoData,
            pages: this.pages
          };
          // console.log('All Page Data:', allData);
          axios.post('/company-sites/save', allData)
            .then(response => {
              // console.log('Company Site Data Saved:', response.data);
              if (response.data.success) {
                this.toasterMessages({ msg: 'Data saved successfully!', type: 'success' });
              }
              this.fetchWebsiteSettings(true);
              this.updateLoader(false);
            })
            .catch(error => {
              console.error('Error saving company site data:', error);
              // alert('Failed to save data. Please try again.');
              this.toasterMessages({ msg: 'Failed to save data. Please try again.', type: 'warning' });
              this.updateLoader(false);
            });
        }, 100);
      }
    },
    submitGalleryData() {
      const allData = {
        company_id: this.companyId,

        image_data: this.galleryData
      };
      //console.log('All Page Data:', allData);
      axios.post('/company-sites/gallery-save', allData)
        .then(response => {
          //console.log('Company Site Data Saved:', response.data);
          if (response.data.success) {
            this.isLoading = false;
            this.toasterMessages({ msg: 'Data saved successfully!', type: 'success' });
          }
        })
        .catch(error => {
          console.error('Error saving company site data:', error);
          // alert('Failed to save data. Please try again.');
          this.toasterMessages({ msg: 'Failed to save data. Please try again.', type: 'warning' });
        });
    },
    //--toaster message---
    toasterMessages(data) {
      this.message = data.msg;
      this.type_toaster = data.type;
      this.show = true;
    },
    //---update loader--
    updateLoader(value) {
      this.open_loader = value;
    },
    //--update pages setting--
    updatePagesSetting(data) {
      if (data) {
        this.pages = { ...this.pages, ...data };
      }

    },
    //--close no-access model--
    closeNoAccess() {
      this.no_access = false;
    },

    //---plan based on strict--
    getplanfeatures(key) {
      if (key && this.getPlanfeatures && Object.keys(this.getPlanfeatures).length > 0 && !this.getPlanfeatures[key]) {
        return true;
      } else {
        return false;
      }
    },

  },
  watch: {
    selectedId: {
      deep: true,
      handler(newValue) {
        if (newValue !== undefined) {
          this.handleItemSelected(newValue);
        }
      }
    },
    selectedItem: {
      deep: true,
      handler(newValue) {
        if (newValue !== undefined && newValue !== this.selectedId) {
          this.updateSelectdItem(newValue);
        }
      }
    }
  }
};
</script>
