<template>
  <div class="p-2 w-full">
    <!--is mobile view count-->
    <div v-if="pagination && isMobile && !open_skeleton" class="text-sm py-1 px-1">
      <p>Total: <span class="font-bold">{{ pagination.total }}</span></p>
    </div>
    <!--new design header-->
    <div v-if="!isMobile" class="flex justify-between m-4 my-1">
      <div class="flex space-x-4">
        <button @click="openModal"
          class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded p-1 text-white hover:bg-green-700 text-sm text-center">
          <span class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /> </span>
          <span>New Employee</span>
        </button>
        <!--view design-->
        <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
          <div v-if="items_category === 'tile'"
            class="px-2 py-1 flex-shrink-0 cursor-pointer info-msg border rounded-lg border-gray-500"
            :class="{ 'bg-white': items_category === 'tile' }" @click="toggleView" :title02="`Table view`">
            <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
            <font-awesome-icon icon="fa-solid fa-bars" />
          </div>
          <div v-if="items_category !== 'tile'"
            class="px-2 py-1 rounded-lg flex-shrink-0 cursor-pointer flex-shrink-0 cursor-pointer info-msg border border border-gray-500"
            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView" :title02="`Card view`">
            <font-awesome-icon icon="fa-solid fa-check" class="pr-1 text-green-600 font-bold" />
            <font-awesome-icon icon="fa-solid fa-grip" />
          </div>
        </div>
      </div>
      <!--Setting-->
      <div class="flex icon-color">
        <div v-if="!open_skeleton" class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
          <p class="text-gray-700">Total Users
            :</p>
          <p class="font-semibold pl-1">
            {{ pagination.total }}
          </p>
        </div>
      </div>
    </div>
    <!--loader-->
    <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
      :cols="items_category === 'tile' ? number_of_columns : 3" :rows="items_category === 'tile' ? number_of_rows : 10"
      :gap="gap" :type="items_category === 'tile' ? 'table' : 'grid'">
    </skeleton>
    <div v-if="!open_skeleton && data.length > 0" class="text-sm" :class="{ 'm-4': !isMobile }">
      <div
        :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
        <div v-if="!isMobile" class="flex justify-between"
          :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
          <!---records per page with refresh-->
          <div class="flex space-x-4 items-center">
            <!-- <div>
              <select v-model="recordsPerPage" @change="changePage" class="border border-gray-300 rounded-lg pr-5 p-1">
                <option v-for="option in options" :key="option" :value="option" class="text-xs">
                  {{ option }}
                </option>
              </select>
            </div> -->
            <div>
              <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg" :title02="'Refersh Data'"
                @click="refreshDataTable">
                <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
              </button>
            </div>
          </div>
        </div>
        <div v-if="!open_skeleton && items_category === 'tile'" class="table-container overflow-x-auto">
          <table class="table w-full">
            <thead>
              <tr class="table-head">
                <th v-for="(column, index) in dynamicFields" :key="index" :class="{ 'hidden': !column.visible }"
                  class="py-2 px-2 table-border">
                  <p>{{ column.label }}</p>
                </th>
                <th class="py-2 table-border">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(record, index) in paginatedData" :key="record.id" class="cursor-pointer hover:bg-gray-200">
                <!-- <td v-for="(column, index) in columns" :key="index" :class="{ 'hidden': !column.visible }"
                class="border px-2 text-xs text-center">
                {{ record[column.field] }}
              </td> -->
                <td class="px-1 table-border" v-for="(column, index) in columns" :key="index"
                  @click="viewRecord(record)" :class="{ 'hidden': !column.visible }">
                  <span
                    v-if="!Array.isArray(record[column.field]) && typeof record[column.field] !== 'object' && column.field !== 'status' && column.field !== 'total_experience' && column.field !== 'roles'"
                    :class="{ 'no-capitalize': column.field == 'email' }">
                    {{ record[column.field] }}
                  </span>
                  <span v-if="column.field === 'status'" class="rounded px-1 py-1 text-white text-xs"
                    :class="{ 'bg-green-500': record[column.field] !== '0', 'bg-red-500': record[column.field] === '0' }">{{
                      record[column.field] == 0 ? 'Deactive' : 'Active' }}</span>
                  <span v-if="column.field === 'total_experience'">{{ record[column.field] === 0 ? 'Fresher' :
                    record[column.field] === 1 ? '0-1 year' : record[column.field] === 2 ? '1-2 years' :
                      record[column.field] === 3 ? '2-3 years' : record[column.field] === 4 ? '3-4 years' :
                        record[column.field] === 5 ? '4-5 years' : record[column.field] === 6 ? 'More than 5 years' : ''
                  }}</span>
                  <span v-if="column.field === 'roles' && Array.isArray(record[column.field])">
                    {{ record[column.field][0].name ? record[column.field][0].name : '' }}
                  </span>
                  <span
                    v-if="Array.isArray(record[column.field]) && record[column.field].length > 0 && column.field !== 'roles'"
                    v-for="(opt, i) in record[column.field]" :key="i">
                    {{typeof opt === 'object' && opt !== null ? Object.keys(opt).map(key => `${key}:
                    ${opt[key]}`).join(',') : opt}}<br>
                  </span>
                  <!-- <span v-if="!Array.isArray(record[column.field]) && typeof record[column.field] === 'object'">
              {{Object.keys(record[column.field]).map(key => `${key}: ${record[column.field][key]}`).join(', ') }}<br>
            </span> -->
                </td>
                <td class="text-center px-2 py-2 table-border">
                  <div class="flex justify-center">
                    <div class="flex relative space-x-2 text-xs">
                      <!-- View Button -->
                      <button v-if="!record.editing" @click="viewRecord(record)" title="View"
                        class="flex items-center gap-2 px-3 py-1 text-purple-600 border border-purple-300 rounded-lg shadow-sm hover:bg-purple-100 transition">
                        <font-awesome-icon icon="fa-solid fa-eye" />
                        <span>View</span>
                      </button>

                      <!-- Edit Button -->
                      <button
                        v-if="!record.editing && !(record['roles'] && record['roles'].length > 0 && record['roles'][0].name == 'admin')"
                        @click="editRecord(record)" title="Edit"
                        class="flex items-center gap-2 px-3 py-1 text-blue-600 border border-blue-300 rounded-lg shadow-sm hover:bg-blue-100 transition">
                        <font-awesome-icon icon="fa-solid fa-pencil" />
                        <span>Edit</span>
                      </button>

                      <!-- Delete Button (Only for Admins) -->
                      <button
                        v-if="!record.editing && checkRoles(['admin']) && !(record['roles'] && record['roles'].length > 0 && record['roles'][0].name == 'admin')"
                        @click="confirmDelete(index)" title="Delete"
                        class="flex items-center gap-2 px-3 py-1 text-red-600 border border-red-300 rounded-lg shadow-sm hover:bg-red-100 transition">
                        <font-awesome-icon icon="fa-regular fa-trash-can" />
                        <span>Delete</span>
                      </button>
                      <!-- <button @click.stop="displayAction(index)" class="px-2 py-1 text-blue-800 rounded"
                        :class="{ 'bg-blue-100': display_option === index }">
                        <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                      </button> -->
                    </div>
                    <div v-if="display_option === index" :ref="'dropdown' + index"
                      class="z-10 mt-7 absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                      style="display: flex; justify-content: center; align-items: center;">
                      <ul class="py-2 text-gray-700" aria-labelledby="dropdownDefaultButton">
                        <li class="hover:bg-gray-200">
                          <button v-if="!record.editing" @click="viewRecord(record)"
                            class="text-gray-700 px-2 py-1 flex justify-center items-center">
                            <font-awesome-icon icon="fa-solid fa-eye" style="color: #8b5cf6;" size="lg" />
                            <span class="px-2 text-[#8b5cf6]">View</span>
                          </button>
                        </li>
                        <li class="hover:bg-gray-200">
                          <button v-if="!record.editing" @click="editRecord(record)"
                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                            <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" size="lg" />
                            <span class="px-2">Edit</span>
                          </button>
                        </li>
                        <li class="hover:bg-gray-200">
                          <button v-if="!record.editing" @click="confirmDelete(index)"
                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                            <font-awesome-icon icon="fa-regular fa-trash-can" style="color: #ef4444" />
                            <span class="px-2">Delete</span>
                          </button>
                        </li>
                      </ul>
                    </div>
                  </div>
                </td>
              </tr>
              <tr v-if="!data || data.length === 0" class="items-center justify-center flex border">
                <button class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full" @click="openModal">+
                  Add
                  Employee</button>
              </tr>
            </tbody>
          </table>
        </div>
        <!--card view-->
        <div>
          <div v-if="items_category === 'list'"
            class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 lg:gap-4 custom-scrollbar-hidden m-2 sm:m-4">
            <div v-for="(record, index) in data" :key="index" class="w-full">
              <div class="max-w-md mx-auto bg-white rounded-xl border border-gray-300 overflow-hidden md:max-w-2xl">
                <!-- Top Section -->
                <div class="flex justify-between items-center p-4 py-2">
                  <!-- Left Side (Can be your dynamic content) -->
                  <div v-if="record['roles'] && Array.isArray(record['roles']) && record['roles'].length > 0">
                    <button class="text-xs bg-gray-300 rounded py-1 px-1" @click="editRecord(record)">
                      {{ record['roles'][0].name ? record['roles'][0].name : '' }}
                    </button>
                  </div>
                  <div v-else>
                  </div>
                  <!-- Right Side (Actions) -->
                  <div class="flex justify-center items-center">
                    <div class="mr-2">
                      <button class="rounded text-white px-1 py-1 text-xs" @click="editRecord(record)"
                        :class="{ 'bg-green-500': record['status'] == 1, 'bg-red-500': record['status'] == 0 }">{{
                          record['status'] == 0 ? 'Deactive' : 'Active' }}</button>
                    </div>
                    <div class="flex justify-center items-center">
                      <div class="relative">
                        <button @click.stop="displayAction(index)" class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                          <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                        </button>
                      </div>
                      <div v-if="display_option === index" :ref="'dropdown' + index"
                        class="z-10 mt-[130px] absolute bg-white divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                        style="display: flex; justify-content: center; align-items: center;">
                        <ul class="py-2 text-gray-700 text-xs" aria-labelledby="dropdownDefaultButton">
                          <li class="hover:bg-gray-200">
                            <button v-if="!record.editing" @click="viewRecord(record)"
                              class="text-gray-700 px-2 py-1 flex justify-center items-center">
                              <font-awesome-icon icon="fa-solid fa-eye" style="color: #8b5cf6;" size="lg" />
                              <span class="px-2 text-[#8b5cf6]">View</span>
                            </button>
                          </li>
                          <li class="hover:bg-gray-200">
                            <button
                              v-if="!record.editing && !(record['roles'] && record['roles'].length > 0 && record['roles'][0].name == 'admin')"
                              @click="editRecord(record)"
                              class="text-blue-500 px-2 py-1 flex justify-center items-center">
                              <font-awesome-icon icon="fa-solid fa-pencil" style="color: #3b82f6;" size="lg" />
                              <span class="px-2">Edit</span>
                            </button>
                          </li>
                          <li class="hover:bg-gray-200">
                            <button
                              v-if="!record.editing && !(record['roles'] && record['roles'].length > 0 && record['roles'][0].name == 'admin')"
                              @click="confirmDelete(index) && checkRoles(['admin']) && !(record['roles'] && record['roles'].length > 0 && record['roles'][0].name == 'admin')"
                              class="text-red-500 px-2  py-1 flex justify-center items-center">
                              <font-awesome-icon icon="fa-regular fa-trash-can" style="color: #ef4444" />
                              <span class="px-2">Delete</span>
                            </button>
                          </li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
                <!--employee name and contact number-->
                <div class="px-4 py-2 mt-2">
                  <div class="flex items-center mb-2 -mt-4">
                    <div class="mr-4">
                      <!-- :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-lime-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-blue-600': index === 0 }" {{ record['name'] ?
                    record['name'][0].toUpperCase() :
                    'E' }} -->
                      <img v-if="record['avatar']" :src="record['avatar']" alt="employee image"
                        class="h-12 w-12 object-contain" />
                      <img v-else :src="upload_profile" alt="default img" class="h-12 w-12 object-contain" />
                    </div>
                    <div>
                      <h4 class="leading-6 font-semibold text-gray-900 mb-1 cursor-pointer" @click="viewRecord(record)">
                        {{ record['name'] }}</h4>
                      <p class="text-gray-500 cursor-pointer" @click="dialPhoneNumber(record['mobile_number'])">
                        +91-{{
                          record['mobile_number'] }}</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <!--no data found-->
            <div
              v-if="pagination && this.pagination.last_page > 0 && this.pagination.last_page === this.pagination.current_page && isMobile && !open_skeleton_isMobile"
              class="text-sm mb-[100px]">
              <p class="font-bold text-center py-2 text-green-700">
                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                Finished !
              </p>
            </div>
          </div>
        </div>
        <!--loader-->
        <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
          :cols="items_category === 'tile' ? number_of_columns : 3"
          :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
          :type="items_category === 'tile' ? 'table' : 'grid'">
        </skeleton>

        <div class="pagination flex items-center justify-between mt-2 sm:text-md text-xs"
          v-if="pagination && !isMobile">
          <p class="text-sm text-gray-700">
            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
            {{ pagination.total }} entries
          </p>
          <div class="flex justify-end w-1/2">
            <!-- <ul class="flex list-none overflow-auto">
              <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                  class="px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                  <font-awesome-icon icon="fa-solid fa-arrow-left" />
                  <span class="pl-1">Prev</span>
                </button>
              </li>
              <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                <button @click="updatePage(pageNumber)"
                  :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                  class="flex px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">
                  {{ pageNumber }}</button>
              </li>
              <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                :class="{ 'bg-gray-500': currentPage === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== pagination.last_page }">
                <button @click="updatePage(currentPage + 1)" :disabled="currentPage === pagination.last_page"
                  class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs items-center">
                  <span class="pr-1">Next</span>
                  <font-awesome-icon icon="fa-solid fa-arrow-right" />
                </button>
              </li>
            </ul> -->
          </div>
        </div>
      </div>
    </div>
    <!--in case empty-->
    <div v-if="!open_skeleton && data && data.length === 0">
      <div class="flex justify-center items-center">
        <img class="w-64 h-64" :src="empty_data" alt="image empty states">
      </div>
      <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
    </div>
    <!---in mobile view create new Employee-->
    <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
      <div class="flex justify-end">
        <button @click="openModal" type="button" class="flex items-center justify-center px-[10px] py-2">
          <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
        </button>
      </div>
    </div>
    <!-- </div> -->
    <Loader :showModal="open_loader"></Loader>
    <employeeRegister :show-modal="showModal_customer" @close-modal="closeModal" :editData="editData"
      :type="typeOfRegister"></employeeRegister>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
  </div>
</template>

<script>
import axios from 'axios';
import confirmbox from '../dialog_box/confirmbox.vue';
import employeeRegister from '../dialog_box/employeeRegister.vue';
import { mapActions, mapGetters } from 'vuex';
export default {
  name: 'users',
  emits: ['updateIsOpen'],
  props: {
    companyId: String,
    userId: String,
    store_refresh: Boolean,
    updateModalOpen: Boolean,
  },
  components: {
    confirmbox,
    employeeRegister,
  },
  data() {
    return {
      selectedRole: 'service-manager',
      upload_profile: '/images/setting_page/profile.png',
      empty_data: '/images/dashboard/empty.svg',
      options: [10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
      recordsPerPage: 'all',
      currentPage: 1,
      data: [],
      originalData: [],
      showModal_customer: false,
      editData: null,
      typeOfRegister: 'add',
      open_confirmBox: false,
      deleteIndex: null,
      columns: [],
      isMobile: false,
      isDropdownOpen: false,
      isFilterDropdownOpen: false,
      filteredData: [],
      //---api integration--
      // companyID: null,
      // userId: null,
      pagination: {},
      //--skeleton
      open_skeleton: false,
      number_of_columns: 4,
      number_of_rows: 10,
      gap: 5,
      items_category: 'tile',
      open_skeleton_isMobile: false,
      display_option: false,
      now: null,
      open_loader: false,
    };
  },
  computed: {
    ...mapGetters('employeesList', ['currentEmployeesList']),
    ...mapGetters('localStorageData', ['currentLocalDataList', 'getPlanfeatures']),

    dynamicFields() {
      const fields = [];
      const order = ['name', 'mobile_number', 'user_name', 'password', 'roles', 'dob', 'email', 'address', 'total_experience', 'skills', 'proof', 'status'];

      // Helper function to capitalize the first letter of each word
      const capitalizeFirstLetter = (str) => {
        return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
      };

      // Helper function to format the label based on specific cases
      const formatLabel = (key) => {
        const words = key.split(/(?=[A-Z])|_/);
        const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
        return formattedLabel;
      };

      // Iterate over the first record in data to get field names
      if (this.data.length > 0) {

        for (const key of order) {
          if (key in this.data[0] && key !== 'id' && key !== 'company_id') {
            const label = formatLabel(key);
            if (key !== 'password' && key !== 'profile_image' && key !== 'proof' && key !== 'dob' && key !== 'skills' && key !== 'user_name' && key !== 'address') {
              fields.push({ label, field: key, visible: true });
            } else {
              fields.push({ label, field: key, visible: false });

            }
          }
        }
        this.columns = fields;
        return fields;
      } else {
        return
      }

    },
    paginatedData() {
      const startIndex = (this.currentPage - 1) * this.recordsPerPage;
      const endIndex = startIndex + this.recordsPerPage;
      return this.data;
    },
    totalPages() {
      return Math.ceil(this.data.length / this.recordsPerPage);
    },
    visiblePageNumbers() {
      // Calculate the range of page numbers to display
      const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
      const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available

      // Generate an array of page numbers to display
      return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
    }

  },
  methods: {
    ...mapActions('employeesList', ['fetchEmployeesList']),
    ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),
    ...mapActions('localStorageData', ['fetchLocalDataList']),

    updatePage(pageNumber) {
      if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
        this.currentPage = pageNumber;
      }
    },
    viewRecord(record) {
      // Handle view action
      // console.log("View", record);
      // this.$emit('showViewCustomer', record);
      // this.$router.push({ name: 'customers-view', params: { id: record.id } });
      // console.log(record, 'What happening...!');
      this.editData = record;
      // openModal();
      this.showModal_customer = true;
      this.typeOfRegister = 'view';
    },
    editRecord(record) {
      // Handle edit action
      // console.log("Edit", record);
      // this.$emit('showAddService', record, 'edit');
      this.editData = record;
      // openModal();
      this.showModal_customer = true;
      this.typeOfRegister = 'edit';
    },
    deleteRecord() {
      if (this.deleteIndex !== null) {
        axios.delete(`/employees/${this.data[this.deleteIndex].id}`,
          {
            params: {
              company_id: this.companyId
            }
          })
          .then(response => {
            // console.log(response.data, 'Delete request');
            this.updateKeyWithTime('employee_update');
            this.data.splice(this.deleteIndex, 1);
            this.open_confirmBox = false;
            this.deleteIndex = null;
          })
          .catch(error => {
            console.error('Error', error);
            this.open_confirmBox = false;
            this.deleteIndex = null;
          })
      }
    },
    confirmDelete(index) {
      // console.log(index, 'What happening...');
      this.deleteIndex = index;
      this.open_confirmBox = true;
    },
    cancelDelete() {
      this.open_confirmBox = false;
    },

    addServices() {
      this.$emit('showAddService', null, 'add');
    },
    openModal() {
      this.typeOfRegister = 'add';
      this.showModal_customer = true;

    },
    closeModal(val) {
      // console.log(val);
      if (val) {
        if (this.typeOfRegister === 'edit') {
          let findDuplication = this.data.findIndex((opt) => opt.id === val.id)
          if (findDuplication !== -1) {
            this.data[findDuplication] = val;
          }
        } else {
          this.data.unshift(val);
          this.fetchEmployeesList({ page: 1, per_page: this.recordsPerPage });
        }
      }

      this.showModal_customer = false;
      this.editData = null;
    },
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
    },
    //--setting
    toggleDropdown() {
      this.isDropdownOpen = !this.isDropdownOpen;
      if (this.isDropdownOpen) {
        // Add event listener when dropdown is opened
        document.addEventListener('click', this.handleOutsideClick);
      } else {
        // Remove event listener when dropdown is closed
        document.removeEventListener('click', this.handleOutsideClick);
      }
    },
    handleOutsideClick(event) {
      const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
      if (!isClickInside) {
        this.toggleDropdown();
      }
    },
    //---filter
    openCustomerModal() {
      // Implement logic to open customer modal
    },
    toggleFilterDropdown() {
      this.isFilterDropdownOpen = !this.isFilterDropdownOpen;
    },
    // closeFilterModal() {
    //     this.showFilterModal = false;
    //     this.resetFilter();
    // },
    resetFilter() {
      // Reset filter values
      this.columns.forEach((column) => {
        column.filterValue = '';
      });
      this.filteredData = [...this.originalData];
    },
    applyFilter() {
      // Implement logic to filter data based on filter values
      // For simplicity, this example applies a basic filter
      this.filteredData = this.originalData.filter((record) => {
        return this.columns.every((column) => {
          return record[column.field].includes(column.filterValue);
        });
      });
      this.showFilterModal = false;
    },
    refreshPage() {
      window.location.reload();
    },
    //---check emty object
    isEmptyObject(obj) {
      return Object.keys(obj).length === 0 && obj.constructor === Object;
    },
    //--get Employee list---
    getEmployeeList(page, per_page) {
      this.open_skeleton = true;
      if (page == 1) {
        this.fetchEmployeesList({ page, per_page });
        if (this.currentEmployeesList && this.currentEmployeesList.data) {
          this.open_skeleton = false;
          this.data = this.currentEmployeesList.data;
          this.pagination = this.currentEmployeesList.pagination;
        }
      } else {
        if (this.isMobile && page === 1 && per_page < 20) {
          per_page = 'all';
        }
        axios.get('/employees', { params: { company_id: this.companyId, page: page, per_page: per_page } })
          .then(response => {
            // console.log(response.data, 'GGGG');
            this.open_skeleton = false;
            this.data = response.data.data;
            this.pagination = response.data.pagination;
          })
          .catch(error => {
            console.error('Error employees', error);
          })
      }
    },
    //---display action
    displayAction(index) {
      if (this.display_option === index) {
        this.display_option = null; // Close dropdown if already open
        // Remove event listener to avoid memory leaks
        document.removeEventListener('click', this.handleClickOutside);
      } else {
        this.display_option = index; // Open dropdown
        // Attach event listener to close dropdown when clicking outside
        document.addEventListener('click', this.handleClickOutside);
      }
    },
    handleClickOutside(event) {
      // Check if the click is outside the dropdown
      const dropdownRef = this.$refs['dropdown' + this.display_option][0];
      // console.log(dropdownRef, 'What happening......!!!', event.target);
      if (dropdownRef && !dropdownRef.contains(event.target)) {
        this.display_option = null; // Close dropdown
        // Remove event listener to avoid memory leaks
        document.removeEventListener('click', this.handleClickOutside);
      }
    },
    dialPhoneNumber(phoneNumber) {
      // Check if the device supports the tel: URI scheme
      if ('href' in HTMLAnchorElement.prototype) {
        // Create a hidden anchor element with the tel: URI
        const anchor = document.createElement('a');
        anchor.setAttribute('href', 'tel:' + phoneNumber);
        anchor.style.display = 'none';

        // Append the anchor to the body and trigger a click
        document.body.appendChild(anchor);
        anchor.click();

        // Clean up by removing the anchor from the DOM
        document.body.removeChild(anchor);
      } else {
        // Fallback if the device doesn't support the tel: URI scheme
        console.log('Device does not support tel: URI scheme');
        // You can add a fallback behavior here, like displaying an error message
      }
    },
    //---toggle view---
    toggleView() {
      if (this.items_category === 'tile') {
        this.items_category = 'list';
      } else {
        this.items_category = 'tile'
      }
    },
    //--close all modals...
    closeAllModels() {
      this.showModal_customer = false;
      this.open_confirmBox = false;
    },
    //---refresh--
    refreshDataTable() {
      this.open_loader = true;
      this.fetchEmployeesList({ page: 1, per_page: this.recordsPerPage });
      this.currentPage = 1;
    },
    //---validate the roles
    checkRoles(roles) {
      if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
        return roles.includes(this.currentLocalDataList.roles[0]);
      }
      return false;
    },
  },
  mounted() {
    this.fetchApiUpdates();
    this.updateIsMobile(); // Initial check
    this.fetchLocalDataList();
    window.addEventListener('resize', this.updateIsMobile);
    this.open_skeleton = true;
    //--get employee list--
    // this.getEmployeeList(this.currentPage, this.recordsPerPage);
    if (this.currentEmployeesList && this.currentEmployeesList.data && this.currentEmployeesList.data.length > 0) {
      this.open_skeleton = false;
      this.data = this.currentEmployeesList.data;
      this.pagination = this.currentEmployeesList.pagination;
      this.fetchEmployeesList({ page: 1, per_page: this.recordsPerPage });
    } else {
      if (this.currentEmployeesList && Object.keys(this.currentEmployeesList).length == 0) {
        this.fetchEmployeesList({ page: 1, per_page: this.recordsPerPage });
      } else {
        this.open_skeleton = false;
      }

    }
    // const getEmp = localStorage.getItem('employee');
    // if (getEmp) {
    //   let parseData = JSON.parse(getEmp);
    //   if (parseData) {
    //     this.data = parseData;
    //   }
    // }
    const view = localStorage.getItem('employee_home');
    if (view) {
      let parse_data = JSON.parse(view);
      if (parse_data.view) {
        this.items_category = parse_data.view;
      }
    }
  },
  beforeDestroy() {
    document.removeEventListener('click', this.handleClickOutside());
    document.removeEventListener('click', this.handleOutsideClick());
  },
  watch: {
    recordsPerPage: {
      deep: true,
      handler(newValue) {
        if (this.currentPage == 1) {
          this.getEmployeeList(1, newValue);
        }
        this.currentPage = 1;
      }
    },
    currentPage: {
      deep: true,
      handler(newValue) {
        this.getEmployeeList(newValue, this.recordsPerPage);
      }
    },
    isMobile: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.items_category = 'list';
        }
      }
    },
    items_category: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          localStorage.setItem('employee_home', JSON.stringify({ view: newValue }));
        }
      }
    },
    currentEmployeesList: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          if (newValue.data) {
            this.data = newValue.data;
          }
          if (newValue.pagination) {
            this.pagination = newValue.pagination;
          }
        }
        this.open_loader = false;
        this.open_skeleton = false;
      }
    },
    store_refresh: {
      deep: true,
      handler(newValue) {
        this.fetchEmployeesList({ page: 1, per_page: this.recordsPerPage });
      }
    },
    //-----navigate controll---
    updateModalOpen: {
      deep: true,
      handler(newValue) {
        this.closeAllModels();
      }
    },
    //---modalbox data----
    open_confirmBox: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
    showModal_customer: {
      deep: true,
      handler(newValue) {
        this.$emit('updateIsOpen', newValue);
      }
    },
  }
};
</script>

<style scoped>
/* Add any additional styling specific to this component */
</style>