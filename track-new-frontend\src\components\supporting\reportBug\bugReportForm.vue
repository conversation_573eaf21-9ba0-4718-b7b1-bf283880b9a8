<template>
    <div class="max-w-3xl mx-auto p-4 bg-white rounded shadow">
        <h2 class="text-2xl font-bold mb-4">Report Bug</h2>
        <form @submit.prevent="submitBugReport">
            <div class="mb-4">
                <label class="block text-gray-700">Bug Title</label>
                <input type="text" v-model="title" class="w-full px-3 py-2 border border-gray-300 rounded"
                    placeholder="Enter bug title" required />
            </div>
            <div class="mb-4">
                <label class="block text-gray-700">Description</label>
                <textarea v-model="description" class="w-full px-3 py-2 border border-gray-300 rounded"
                    placeholder="Describe the bug" required></textarea>
            </div>
            <div class="mb-4">
                <label class="block text-gray-700">Severity</label>
                <select v-model="severity" class="w-full px-3 py-2 border border-gray-300 rounded">
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                </select>
            </div>
            <button type="submit" class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
                Submit Bug Report
            </button>
        </form>

        <!-- DataTable to display submitted bugs -->
        <div class="mt-8">
            <h3 class="text-lg font-semibold mb-4">Submitted Bugs</h3>
            <table class="min-w-full bg-white border border-gray-300">
                <thead>
                    <tr class="bg-gray-100">
                        <th class="px-4 py-2 text-left">Title</th>
                        <th class="px-4 py-2 text-left">Description</th>
                        <th class="px-4 py-2 text-left">Severity</th>
                    </tr>
                </thead>
                <tbody>
                    <tr v-for="bug in bugReports" :key="bug.id">
                        <td class="px-4 py-2">{{ bug.title }}</td>
                        <td class="px-4 py-2">{{ bug.description }}</td>
                        <td class="px-4 py-2">{{ bug.severity }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            title: '',
            description: '',
            severity: 'low',
            bugReports: [] // Store submitted bug reports
        };
    },
    methods: {
        submitBugReport() {
            const newBugReport = {
                id: Date.now(),
                title: this.title,
                description: this.description,
                severity: this.severity
            };
            this.bugReports.push(newBugReport);

            // Clear form fields after submission
            this.title = '';
            this.description = '';
            this.severity = 'low';
        }
    }
};
</script>