<template>
  <div v-if="show" class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg w-11/12 md:w-1/2 lg:w-1/3 p-6 relative">
      <!-- Modal Header -->
      <div class="web-modal-head">
        <h2 class="text-sm font-normal">{{ isEditMode ? 'Edit Testimonial' : 'Add Testimonial' }}</h2>
        <button @click="close" class="text-red-600 hover:text-red-700">
          <font-awesome-icon icon="fa-solid fa-times" />
        </button>
      </div>

      <!-- Modal Body -->
      <div class="space-y-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
          <div>
            <label class="block font-normal text-xs mb-1">Customer Name <span class="text-red-500">*</span></label>
            <input type="text" v-model="localTestimonial.customerName" class="border p-2 rounded w-full text-xs"
              placeholder="Customer Name" required />
          </div>

          <div>
            <label class="block font-normal text-xs mb-1">Rating </label>
            <!-- <span class="text-red-500">*</span> -->
            <div class="flex items-center space-x-2">
              <span v-for="star in 5" :key="star" @click="setRating(star)">
                <font-awesome-icon :icon="star <= localTestimonial.rating ? 'fas fa-star' : 'far fa-star'"
                  class="text-yellow-500 cursor-pointer text-xs" />
              </span>
            </div>
          </div>
        </div>

        <div>
          <label class="block font-normal text-xs mb-1">Review <span class="text-red-500">*</span></label>
          <textarea v-model="localTestimonial.review" class="border p-2 rounded w-full text-xs" placeholder="Feedback"
            rows="3"></textarea>
        </div>

        <div>
          <label class="block font-normal text-xs mb-1">Customer Image</label>
          <input type="file" @change="onFileChange" ref="imageServices" class="border p-2 rounded w-full text-xs"
            accept="image/png, image/jpeg" />
          <div v-if="circle_loader_photo"
            class="absolute top-0 left-0 w-full h-full flex justify-center items-center bg-white bg-opacity-80 z-10">
            <CircleLoader :loading="true"></CircleLoader>
          </div>
          <div v-if="localTestimonial.customerImageUrl" class="mt-4 relative">
            <img :src="localTestimonial.customerImageUrl" alt="Customer Image Preview"
              class="w-full h-16 object-cover text-sm rounded-lg" />
            <button @click="removeImage"
              class="absolute top-2 right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center">
              &times;
            </button>
          </div>
        </div>
      </div>

      <!-- Modal Footer -->
      <div class="flex justify-end mt-6">
        <button @click="save" class="bg-green-500 text-white py-2 px-4 rounded shadow">
          {{ isEditMode ? 'Update' : 'Save' }}
        </button>
      </div>
    </div>
    <Toaster :show="showModal" :message="message" :type="type_toaster" @update:show="showModal = false"></Toaster>
    <Loader :showModal="open_loader"></Loader>
    <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>
    <dialogConfirmBox :visible="show_dialog" :message="message" :type="'website'" @ok="closeModalconfirm"
      @cancel="cancelcloseModal" @save="save"></dialogConfirmBox>
  </div>
</template>

<script>
import { FontAwesomeIcon } from "@fortawesome/vue-fontawesome";
import imageService from "../../services/imageService"; // Ensure the correct path to imageService
import confirmbox from "@/components/supporting/dialog_box/confirmbox.vue";
import dialogConfirmBox from "@/components/supporting/dialog_box/dialogConfirmBox.vue";
import { mapActions, mapGetters } from 'vuex';

export default {
  props: {
    show: {
      type: Boolean,
      required: true
    },
    isEditMode: {
      type: Boolean,
      default: false
    },
    testimonial: {
      type: Object,
      // default: () => ({
      //   customerName: "",
      //   rating: 0,
      //   review: "",
      //   customerImageUrl: null
      // })
      required: false,
    },
    companyId: {
      type: String,
      required: true
    }
  },
  components: {
    FontAwesomeIcon,
    confirmbox,
    dialogConfirmBox
  },
  data() {
    return {
      localTestimonial: {}, // Local copy for modal form
      //--loader--
      circle_loader_photo: false,
      open_loader: false,
      //--toaster---
      showModal: false,
      type_toaster: 'success',
      message: '',
      //--confirm box--
      open_confirmBox: false,
      deleteIndex: null,
      //--confirm box dialog--
      show_dialog: false,
    };
  },
  watch: {
    show: {
      deep: true,
      handler(newValue) {
        if (newValue && this.testimonial && this.isEditMode) {
          this.localTestimonial = { ...this.testimonial }
        } else if (newValue && Object.keys(this.localTestimonial).length === 0) {
          this.localTestimonial = {
            customerName: "",
            rating: 0,
            review: "",
            customerImageUrl: null
          }
        }
        this.showModal = false;
      }
    },
    // testimonial(newVal) {
    //   this.localTestimonial = { ...newVal };
    // }
    localTestimonial: {
      deep: true,
      handler(newValue) {
        if (newValue) {
          this.validateForm([this.testimonial, newValue]);
        }
      }
    }

  },
  computed: {
    ...mapGetters('websiteBuilder', ['isModified']),
  },
  methods: {
    ...mapActions('websiteBuilder', ['validateForm']),
    setRating(star) {
      this.localTestimonial.rating = star;
    },
    async onFileChange(event) {
      this.circle_loader_photo = true;
      const file = event.target.files[0];
      if (file) {
        try {
          if (this.localTestimonial.customerImageUrl && this.localTestimonial.customerImageUrl !== '') {
            const response = await imageService.deleteImage(this.localTestimonial.customerImageUrl, 'testimonials', this.companyId);
            this.toasterMessages({ msg: 'Exist iamge removed successfully', type: 'success' });
          }
          const response = await imageService.uploadImage(file, 'testimonials', this.companyId);
          this.localTestimonial.customerImageUrl = response.media_url;
          this.circle_loader_photo = false;
          this.toasterMessages({ msg: 'Image uploaded successfully', type: 'success' });
        } catch (error) {
          console.error("Error uploading image:", error);
          this.toasterMessages({ msg: 'Failed to upload image.', type: 'warning' });
          this.circle_loader_photo = false;
        }
      }
    },
    async removeImage() {
      this.open_confirmBox = true;
    },
    save() {
      if (this.localTestimonial.customerName && this.localTestimonial.customerName !== '' && this.localTestimonial.review && this.localTestimonial.review !== "") {
        // Emit save event with data
        this.$emit("save", this.localTestimonial);
        this.resetForm(true);
        this.$emit('close');
      } else {
        this.toasterMessages({ msg: 'Please fill in all required fields marked with *', type: 'warning' })
      }
    },
    close() {
      if (!this.isModified) {
        this.message = 'You have unsaved changes. Do you want to save them or ok or cancel?'
        this.show_dialog = true;
      } else {
        this.resetForm();
        this.$emit('close');

      }
    },
    closeModalconfirm() {
      this.show_dialog = false;
      this.resetForm();
      this.$emit('close'); // Emit close event to hide modal
    },
    cancelcloseModal() {
      this.show_dialog = false;
      // this.$emit('close');
    },
    resetForm(istrue) {
      if (this.isEditMode || istrue) {
        this.localTestimonial = {};
      }
    },
    async deleteRecord() {
      if (this.localTestimonial.customerImageUrl) {
        this.open_loader = true;
        try {
          await imageService.deleteImage(this.localTestimonial.customerImageUrl, 'testimonials'); // Delete image from server
          this.localTestimonial.customerImageUrl = null; // Clear image URL    
          this.$refs.imageServices.value = '';
          this.toasterMessages({ msg: 'Image uploaded successfully', type: 'success' });
          this.closeconfirmBoxData();
        } catch (error) {
          console.error('Error deleting image:', error);
          this.toasterMessages({ msg: error.response.data.error, type: 'warning' });
          if (error.response.data && error.response.data.error === 'Image not found') {
            this.localTestimonial.customerImageUrl = null;
          }
          this.closeconfirmBoxData();
        }
      } else {
        this.closeconfirmBoxData();
      }
    },
    //--confirmbox delete cancel
    cancelDelete() {
      this.closeconfirmBoxData();
    },
    closeconfirmBoxData() {
      this.deleteIndex = null;
      this.$emit('updateLoader', false);
      this.open_confirmBox = false;
      this.open_loader = false;
    },
    //--toaster message---
    toasterMessages(data) {
      this.message = data.msg;
      this.type_toaster = data.type;
      this.showModal = true;
    },
  }
};
</script>

<style scoped>
/* Optional modal styles */
</style>