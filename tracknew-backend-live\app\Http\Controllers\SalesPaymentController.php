<?php

namespace App\Http\Controllers;

use App\DataTables\SalesPaymentDataTable;
use App\Http\Requests;
use App\Http\Requests\CreateSalesPaymentRequest;
use App\Http\Requests\UpdateSalesPaymentRequest;
use App\Repositories\SalesPaymentRepository;
use Flash;
use App\Http\Controllers\AppBaseController;
use Response;

class SalesPaymentController extends AppBaseController
{
    /** @var SalesPaymentRepository $salesPaymentRepository*/
    private $salesPaymentRepository;

    public function __construct(SalesPaymentRepository $salesPaymentRepo)
    {
        $this->salesPaymentRepository = $salesPaymentRepo;
    }

    /**
     * Display a listing of the SalesPayment.
     *
     * @param SalesPaymentDataTable $salesPaymentDataTable
     *
     * @return Response
     */
    public function index(SalesPaymentDataTable $salesPaymentDataTable)
    {
        return $salesPaymentDataTable->render('sales_payments.index');
    }

    /**
     * Show the form for creating a new SalesPayment.
     *
     * @return Response
     */
    public function create()
    {
        return view('sales_payments.create');
    }

    /**
     * Store a newly created SalesPayment in storage.
     *
     * @param CreateSalesPaymentRequest $request
     *
     * @return Response
     */
    public function store(CreateSalesPaymentRequest $request)
    {
        $input = $request->all();

        $salesPayment = $this->salesPaymentRepository->create($input);

        Flash::success('Sales Payment saved successfully.');

        return redirect(route('salesPayments.index'));
    }

    /**
     * Display the specified SalesPayment.
     *
     * @param int $id
     *
     * @return Response
     */
    public function show($id)
    {
        $salesPayment = $this->salesPaymentRepository->find($id);

        if (empty($salesPayment)) {
            Flash::error('Sales Payment not found');

            return redirect(route('salesPayments.index'));
        }

        return view('sales_payments.show')->with('salesPayment', $salesPayment);
    }

    /**
     * Show the form for editing the specified SalesPayment.
     *
     * @param int $id
     *
     * @return Response
     */
    public function edit($id)
    {
        $salesPayment = $this->salesPaymentRepository->find($id);

        if (empty($salesPayment)) {
            Flash::error('Sales Payment not found');

            return redirect(route('salesPayments.index'));
        }

        return view('sales_payments.edit')->with('salesPayment', $salesPayment);
    }

    /**
     * Update the specified SalesPayment in storage.
     *
     * @param int $id
     * @param UpdateSalesPaymentRequest $request
     *
     * @return Response
     */
    public function update($id, UpdateSalesPaymentRequest $request)
    {
        $salesPayment = $this->salesPaymentRepository->find($id);

        if (empty($salesPayment)) {
            Flash::error('Sales Payment not found');

            return redirect(route('salesPayments.index'));
        }

        $salesPayment = $this->salesPaymentRepository->update($request->all(), $id);

        Flash::success('Sales Payment updated successfully.');

        return redirect(route('salesPayments.index'));
    }

    /**
     * Remove the specified SalesPayment from storage.
     *
     * @param int $id
     *
     * @return Response
     */
    public function destroy($id)
    {
        $salesPayment = $this->salesPaymentRepository->find($id);

        if (empty($salesPayment)) {
            Flash::error('Sales Payment not found');

            return redirect(route('salesPayments.index'));
        }

        $this->salesPaymentRepository->delete($id);

        Flash::success('Sales Payment deleted successfully.');

        return redirect(route('salesPayments.index'));
    }
}
