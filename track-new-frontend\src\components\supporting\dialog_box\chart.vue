<template>
    <div class="chart-container">
      <div class="chart">
        <div v-for="(choiceValues, choiceIndex) in stackedData" :key="choiceIndex" class="stacked-bar">
          <div v-for="(categoryValue, categoryIndex) in choiceValues" :key="categoryIndex"
               class="bar" :style="{ height: categoryValue + 'px', backgroundColor: categoryColors[categoryIndex] }">
            <span class="bar-label">{{ categoryNames[categoryIndex] }}</span>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script>
  export default {
    name: 'StackedArea<PERSON>hart',
    props: {
      stackedData: {
        type: Array,
        required: true
      },
      categoryNames: {
        type: Array,
        required: true
      },
      categoryColors: {
        type: Array,
        required: true
      }
    }
  };
  </script>
  
  <style scoped>
  .chart-container {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    height: 300px; /* Adjust the height as needed */
  }
  
  .chart {
    display: flex;
    align-items: flex-end;
    width: 100%;
  }
  
  .stacked-bar {
    width: 100%;
    display: flex;
  }
  
  .bar {
    flex-grow: 1;
    text-align: center;
    position: relative;
  }
  
  .bar-label {
    position: absolute;
    bottom: -20px;
    left: 0;
    right: 0;
    font-size: 12px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  </style>
  