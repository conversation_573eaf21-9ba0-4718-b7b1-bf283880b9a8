const crypto = require('crypto');
const { User, Company } = require('../models');
const { AppError, catchAsync } = require('../middleware/errorHandler');
const { createSendToken } = require('../middleware/auth');
const Email = require('../utils/email');
const SMS = require('../utils/sms');

// Register new user with company
const register = catchAsync(async (req, res, next) => {
  const {
    name,
    email,
    password,
    mobile_number,
    company_name,
    company_email,
    company_phone,
    company_address
  } = req.body;

  // Check if user already exists
  const existingUser = await User.findOne({
    where: { email }
  });

  if (existingUser) {
    return next(new AppError('User with this email already exists', 400));
  }

  // Create company first
  const company = await Company.create({
    company_name,
    company_email: company_email || email,
    company_phone: company_phone || mobile_number,
    company_address
  });

  // Create admin user for the company
  const user = await User.create({
    name,
    email,
    password,
    mobile_number,
    company_id: company.id,
    user_type: 'admin',
    status: true
  });

  // Load user with company data
  const userWithCompany = await User.findByPk(user.id, {
    include: [
      {
        model: Company,
        as: 'company',
        attributes: ['id', 'company_name', 'company_email', 'status']
      }
    ]
  });

  // Send welcome email
  try {
    await new Email(userWithCompany, `${process.env.APP_URL}/login`).sendWelcome();
  } catch (err) {
    console.log('Error sending welcome email:', err);
  }

  createSendToken(userWithCompany, 201, res);
});

// Login user
const login = catchAsync(async (req, res, next) => {
  const { login, password } = req.body;

  // 1) Check if email/mobile and password exist
  if (!login || !password) {
    return next(new AppError('Please provide email/mobile and password!', 400));
  }

  // 2) Check if user exists and password is correct
  // Determine if login is email or mobile
  const isEmail = login.includes('@');
  const whereClause = isEmail ? { email: login } : { mobile_number: login };

  const user = await User.findOne({
    where: whereClause,
    include: [
      {
        model: Company,
        as: 'company',
        attributes: ['id', 'company_name', 'status', 'subscription_status']
      }
    ]
  });

  if (!user) {
    return next(new AppError('Invalid credentials', 401));
  }

  // Check if account is locked
  if (user.isAccountLocked()) {
    return next(new AppError('Account is temporarily locked due to multiple failed login attempts', 423));
  }

  // Check password
  const isPasswordCorrect = await user.comparePassword(password);

  if (!isPasswordCorrect) {
    // Increment login attempts
    await user.incrementLoginAttempts();
    return next(new AppError('Invalid credentials', 401));
  }

  // Check if user is active
  if (!user.status) {
    return next(new AppError('Your account has been deactivated', 401));
  }

  // Check if company is active
  if (user.company && !user.company.status) {
    return next(new AppError('Your company account has been deactivated', 401));
  }

  // Reset login attempts on successful login
  if (user.login_attempts > 0) {
    await user.resetLoginAttempts();
  }

  // Update last login
  await user.update({ last_login: new Date() });

  // 3) If everything ok, send token to client
  createSendToken(user, 200, res);
});

// Logout user
const logout = (req, res) => {
  res.cookie('jwt', 'loggedout', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });
  res.cookie('refreshToken', 'loggedout', {
    expires: new Date(Date.now() + 10 * 1000),
    httpOnly: true
  });
  res.status(200).json({ status: 'success' });
};

// Forgot password
const forgotPassword = catchAsync(async (req, res, next) => {
  // 1) Get user based on POSTed email
  const user = await User.findOne({ where: { email: req.body.email } });
  if (!user) {
    return next(new AppError('There is no user with that email address.', 404));
  }

  // 2) Generate the random reset token
  const resetToken = crypto.randomBytes(32).toString('hex');
  const passwordResetToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');

  const passwordResetExpires = new Date(Date.now() + 10 * 60 * 1000); // 10 minutes

  await user.update({
    password_reset_token: passwordResetToken,
    password_reset_expires: passwordResetExpires
  });

  // 3) Send it to user's email
  try {
    const resetURL = `${process.env.APP_URL}/reset-password/${resetToken}`;

    await new Email(user, resetURL).sendPasswordReset();

    res.status(200).json({
      status: 'success',
      message: 'Token sent to email!'
    });
  } catch (err) {
    await user.update({
      password_reset_token: null,
      password_reset_expires: null
    });

    return next(
      new AppError('There was an error sending the email. Try again later.', 500)
    );
  }
});

// Reset password
const resetPassword = catchAsync(async (req, res, next) => {
  // 1) Get user based on the token
  const hashedToken = crypto
    .createHash('sha256')
    .update(req.params.token)
    .digest('hex');

  const user = await User.findOne({
    where: {
      password_reset_token: hashedToken,
      password_reset_expires: { [require('sequelize').Op.gt]: Date.now() }
    }
  });

  // 2) If token has not expired, and there is user, set the new password
  if (!user) {
    return next(new AppError('Token is invalid or has expired', 400));
  }

  await user.update({
    password: req.body.password,
    password_reset_token: null,
    password_reset_expires: null
  });

  // 3) Log the user in, send JWT
  createSendToken(user, 200, res);
});

// Update password for logged in user
const updatePassword = catchAsync(async (req, res, next) => {
  // 1) Get user from collection
  const user = await User.findByPk(req.user.id);

  // 2) Check if POSTed current password is correct
  if (!(await user.comparePassword(req.body.passwordCurrent))) {
    return next(new AppError('Your current password is wrong.', 401));
  }

  // 3) If so, update password
  await user.update({ password: req.body.password });

  // 4) Log user in, send JWT
  createSendToken(user, 200, res);
});

// Get current user
const getMe = catchAsync(async (req, res, next) => {
  const user = await User.findByPk(req.user.id, {
    include: [
      {
        model: Company,
        as: 'company',
        attributes: ['id', 'company_name', 'company_email', 'status', 'subscription_status']
      }
    ]
  });

  res.status(200).json({
    status: 'success',
    data: {
      user
    }
  });
});

// Verify email
const verifyEmail = catchAsync(async (req, res, next) => {
  const { token } = req.params;

  // Find user with verification token
  const hashedToken = crypto
    .createHash('sha256')
    .update(token)
    .digest('hex');

  const user = await User.findOne({
    where: {
      email_verification_token: hashedToken,
      email_verification_expires: { [require('sequelize').Op.gt]: Date.now() }
    }
  });

  if (!user) {
    return next(new AppError('Token is invalid or has expired', 400));
  }

  // Update user as verified
  await user.update({
    is_verified: true,
    email_verified_at: new Date(),
    email_verification_token: null,
    email_verification_expires: null
  });

  res.status(200).json({
    status: 'success',
    message: 'Email verified successfully'
  });
});

// Resend verification email
const resendVerificationEmail = catchAsync(async (req, res, next) => {
  const user = await User.findOne({ where: { email: req.body.email } });

  if (!user) {
    return next(new AppError('No user found with that email address', 404));
  }

  if (user.is_verified) {
    return next(new AppError('Email is already verified', 400));
  }

  // Generate verification token
  const verifyToken = crypto.randomBytes(32).toString('hex');
  const emailVerificationToken = crypto
    .createHash('sha256')
    .update(verifyToken)
    .digest('hex');

  const emailVerificationExpires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours

  await user.update({
    email_verification_token: emailVerificationToken,
    email_verification_expires: emailVerificationExpires
  });

  // Send verification email
  try {
    const verifyURL = `${process.env.APP_URL}/verify-email/${verifyToken}`;
    await new Email(user, verifyURL).sendEmailVerification();

    res.status(200).json({
      status: 'success',
      message: 'Verification email sent!'
    });
  } catch (err) {
    await user.update({
      email_verification_token: null,
      email_verification_expires: null
    });

    return next(
      new AppError('There was an error sending the email. Try again later.', 500)
    );
  }
});

module.exports = {
  register,
  login,
  logout,
  forgotPassword,
  resetPassword,
  updatePassword,
  getMe,
  verifyEmail,
  resendVerificationEmail
};
