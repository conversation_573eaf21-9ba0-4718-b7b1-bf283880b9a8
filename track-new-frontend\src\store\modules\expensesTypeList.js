// store/modules/expensesTypeList.js
import axios from "axios";

const state = {
  expense_type: [],
  pagination: {}
  };

  const mutations = {
    SET_EXPENSETYPE(state, itemData, pagination) {
      state.expense_type = itemData;
      state.pagination = pagination;
    },
    RESET_STATE(state) {
      state.expense_type = [];
      state.pagination = {};
    },
  };

  const actions = {
    updateExpenseName({ commit }, itemData) {
      // Simulate an asynchronous operation (e.g., API call) to update customer name
      setTimeout(() => {
        // Commit mutation to update customer name
        commit('SET_EXPENSETYPE', itemData);
      }, 1000); // Simulated delay of 1 second
    },
    async fetchExpenseTypeList({ commit }) {
      try {
        const { company_id } = JSON.parse(localStorage.getItem('track_new')) || {};
        if (company_id && company_id !== '') {
          axios.get('/expensesTypes', { params: { company_id: company_id, page: 1, per_page: 'all'} })
            .then(response => {
              // Handle response
              // console.log(response.data, 'Expenses list..!');
              let expense_type_data = response.data.data;
              let pagination = response.data.pagination;              
              commit('SET_EXPENSETYPE', expense_type_data, pagination);
              return expense_type_data;
            })
            .catch(error => {
              // Handle error
              console.error('Error:', error);
              return error;
            });
        }  
      } catch (error) {
        console.error('Error fetching item list:', error);
      }
    },
    
  };

  const getters = {
    currentExpenseType(state) {
        return state.expense_type;
    },
    currentExpenseTypePagination(state) {
      return state.pagination;
    },
  };

  export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters,
};