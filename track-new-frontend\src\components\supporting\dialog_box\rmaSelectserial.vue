<template>
    <div v-if="showModal" class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50">
        <div class="bg-white p-6 rounded-lg w-3/4 max-w-lg relative">
            <button @click="closeModal" class="absolute top-2 right-2 rounded-full p-2 hover:text-red-700">
                X
            </button>
            <h2 class="text-xl font-bold mb-4">Customer Details</h2>
            <p class="mb-2"><span class="font-semibold">Name:</span> {{ customer.first_name + ' ' + (customer.last_name
                ?
                customer.last_name : '') }}</p>
            <p class="mb-4"><span class="font-semibold">Mobile:</span> {{ customer.contact_number }}</p>

            <h3 class="text-lg font-semibold mb-2">Select Invoice</h3>
            <select v-model="selectedInvoice" @change="fetchInvoiceItems()"
                class="mb-4 p-2 border border-gray-300 rounded-md w-full">
                <option v-for="invoice in invoices" :key="invoice.id" :value="invoice.id">
                    {{ invoice.invoice_id }}
                </option>
            </select>

            <div v-if="items.length > 0">
                <h4 class="text-lg font-semibold mb-2">Items in Invoice</h4>
                <ul class="relative overflow-auto">
                    <li v-for="(item, j) in items" :key="j" class="mb-2 relative" @click="selectedTheSerialNum(item)">
                        <span @click="showTooltip(item)" class="cursor-pointer hover:text-blue-500">
                            {{ item.product_name }}
                        </span>
                        <div @mouseover="showTooltip(j)" class="rounded-md whitespace-nowrap z-10">
                            <select v-if="item.serial_no && item.serial_no.length > 0" v-model="selectedSerial"
                                @change="selectedTheSerialNum(item)"
                                class="bg-gray-700 text-white border border-gray-600 rounded-md p-1">
                                <option v-for="(serial, k) in item.serial_no" :key="k" :value="serial">
                                    {{ serial }}
                                </option>
                            </select>
                            <p v-else class="text-gray-500">Serial numbers are empty</p>
                        </div>
                    </li>
                </ul>
            </div>
            <div v-if="items && items.length === 0 && selectedInvoice && selectedInvoice !== ''" class="text-gray-500">
                No sales items</div>
        </div>
    </div>
</template>
<script>
export default {
    props: {
        showModal: Boolean,
        customer: Object,
        invoices: Array,
        companyId: String,
    },
    data() {
        return {
            selectedInvoice: null,
            items: [],
            tooltipItem: null,
            selectedSerial: null,
            overItemSerial: false,
        };
    },
    methods: {
        closeModal() {
            this.$emit('close');
        },
        async fetchInvoiceItems() {
            // console.log(this.selectedInvoice, 'WWWWWWWWWWWWWWWWWWWW');
            if (this.selectedInvoice) {
                this.getItemsList(this.selectedInvoice);
            }
        },
        getItemsList(id) {
            axios.get(`/sales/${id}`, { params: { company_id: this.companyId } })
                .then(response => {
                    let exist_data = response.data.data;
                    // console.log(exist_data, 'EQEQEQEQ');

                    this.items = exist_data.sales_item_data;
                    // Loop through each object in the array
                    this.items.forEach(item => {
                        if (item.serial_no) {
                            // Stringify the discount_data object
                            item.serial_no = JSON.parse(item.serial_no);
                        }
                    });
                    // console.log(this.items, 'RWRWRWRW');
                })
                .catch(error => {
                    console.error('Error', error);
                })

        },
        showTooltip(item, j) {
            if (j >= 0) {
                this.overItemSerial = j;
            } else {
                this.tooltipItem = item;
            }
        },
        hideTooltip() {
            if (this.overItemSerial === false) {
                setTimeout(() => {
                    this.tooltipItem = null;
                }, 100);
            }
        },
        selectedTheSerialNum(item) {

            if (this.selectedInvoice && item && this.selectedSerial && item.serial_no && item.serial_no.includes(this.selectedSerial)) {
                let find_invoices = this.invoices.find(opt => opt.id === this.selectedInvoice);
                if (find_invoices) {
                    let data = { invoice: find_invoices, item: item, serial_no: this.selectedSerial };
                    this.$emit('close', data);
                }
            } else if (this.selectedInvoice && item && ((item.serial_no && item.serial_no.length === 0) || !item.serial_no)) {
                let find_invoices = this.invoices.find(opt => opt.id === this.selectedInvoice);
                if (find_invoices) {
                    let data = { invoice: find_invoices, item: item };
                    this.$emit('close', data);
                }
            }
        }
    },
};
</script>