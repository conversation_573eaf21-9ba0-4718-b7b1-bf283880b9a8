<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\Createlead_typeAPIRequest;
use App\Http\Requests\API\Updatelead_typeAPIRequest;
use App\Models\lead_type;
use App\Repositories\lead_typeRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class lead_typeController
 * @package App\Http\Controllers\API
 */

class lead_typeAPIController extends AppBaseController
{
    /** @var  lead_typeRepository */
    private $leadTypeRepository;

    public function __construct(lead_typeRepository $leadTypeRepo)
    {
        $this->leadTypeRepository = $leadTypeRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/lead_types",
     *      summary="getlead_typeList",
     *      tags={"lead_type"},
     *      description="Get all lead_types",
     *      @OA\Parameter(
    *          name="company_id",
    *          description="ID of the company whose services are to be fetched",
    *          @OA\Schema(
    *              type="string"
    *          ),
    *          required=true,
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="page",
    *          description="Page number for pagination",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
    *      @OA\Parameter(
    *          name="per_page",
    *          description="Number of items per page",
    *          @OA\Schema(
    *              type="integer"
    *          ),
    *          in="query"
    *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/lead_type")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companyId = $request->query('company_id');

        if ($companyId === null) {
            return $this->sendError('Please provide company.', 400);
        } 

        $perPage = $request->query('per_page', 10);
        $page = $request->query('page', 1);

        $leadsQuery = lead_type::where('company_id', $companyId);

        if ($perPage === 'all') {
            $perPage = $leadsQuery->count();
        }

        $leads = $leadsQuery->orderBy('name', 'asc')->paginate($perPage, ['*'], 'page', $page);

        $response = [
            'success' => true,
            'data' => $leads->items(), // Get the paginated items
            'pagination' => [
                'total' => $leads->total(),
                'per_page' => $leads->perPage(),
                'current_page' => $leads->currentPage(),
                'last_page' => $leads->lastPage(),
                'from' => $leads->firstItem(),
                'to' => $leads->lastItem(),
            ],
        ];
        
        return response()->json($response);
    }


    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/lead_types",
     *      summary="createlead_type",
     *      tags={"lead_type"},
     *      description="Create lead_type",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/lead_type")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/lead_type"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(Createlead_typeAPIRequest $request)
    {
        $input = $request->all();

        $leadType = $this->leadTypeRepository->create($input);

        return $this->sendResponse($leadType->toArray(), 'Lead Type saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/lead_types/{id}",
     *      summary="getlead_typeItem",
     *      tags={"lead_type"},
     *      description="Get lead_type",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of lead_type",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/lead_type"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var lead_type $leadType */
        $leadType = $this->leadTypeRepository->find($id);

        if (empty($leadType)) {
            return $this->sendError('Lead Type not found');
        }

        return $this->sendResponse($leadType->toArray(), 'Lead Type retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/lead_types/{id}",
     *      summary="updatelead_type",
     *      tags={"lead_type"},
     *      description="Update lead_type",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of lead_type",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
    *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/lead_type")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/lead_type"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, Updatelead_typeAPIRequest $request)
    {
        $input = $request->all();

        /** @var lead_type $leadType */
        $leadType = $this->leadTypeRepository->find($id);

        if (empty($leadType)) {
            return $this->sendError('Lead Type not found');
        }

        $leadType = $this->leadTypeRepository->update($input, $id);

        return $this->sendResponse($leadType->toArray(), 'lead_type updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/lead_types/{id}",
     *      summary="deletelead_type",
     *      tags={"lead_type"},
     *      description="Delete lead_type",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of lead_type",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var lead_type $leadType */
        $leadType = $this->leadTypeRepository->find($id);

        if (empty($leadType)) {
            return $this->sendError('Lead Type not found');
        }

        $leadType->delete();

        return $this->sendSuccess('Lead Type deleted successfully');
    }
}
