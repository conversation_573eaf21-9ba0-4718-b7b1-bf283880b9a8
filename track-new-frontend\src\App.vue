<template>
  <div class="relative">
    <!-- Headbar with its content && companyInfo -->
    <headbar v-if="isLoginEnable" @toggle-sidebar="toggleSidebar" :updateModalOpen="updateModalOpen"
      :isSidebarOpen="isSidebarOpen" @updateIsOpen="emitUpdateIsOpen" @refreshRouterView="refreshRouterView">
    </headbar>
    <!-- Main content area below the headbar -->
    <div class="flex h-screen overflow-auto relative">
      <!-- Sidebar (conditionally rendered based on isMobile) -->
      <div v-if="!isMobile && currentLocalDataList && currentLocalDataList.company_id && isSidebarOpen && isLoginEnable"
        class="custom-scrollbar-hidden w-1/5">
        <div>
          <sidebar v-if="!isMobile" :route_item="sidebarselected" :page_name="'dashboard'" :update_list="update_sidebar"
            :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen" @refreshRouterView="refreshRouterView">
          </sidebar>
        </div>
      </div>
      <!-- Content area with its own scroll -->
      <div class="w-full" :class="{ 'flex flex-col flex-grow overflow-y-auto': isLoginEnable }">
        <router-view :key="refreshKey"></router-view> <!-- Bind refreshKey as key -->
        <ExpirePlan :showModal="modalState.showExpirePlan" @update:show="modalState.showExpirePlan = $event" />
      </div>
      <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
      <div v-if="isMobile && isSidebarOpen && currentLocalDataList && currentLocalDataList.company_id"
        class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden">
        <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="sidebarselected"
          :update_list="update_sidebar" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"
          @refreshRouterView="refreshRouterView">
        </sidebar>
      </div>
    </div>
    <bottombar v-if="isMobile && isEnableBottom && isLoginEnable" :selected_btn_btm="bottomSelected"
      :opensidebar="openSidebarMobile" @togglesidebar="toggleSidebar">
    </bottombar>
    <!--financial year setting--->
    <yearendNotification :showModal="open_financial" @close-modal="closefinancial" :invoice_setting="currentInvoice"
      :companyId="currentLocalDataList && currentLocalDataList.company_id ? currentLocalDataList.company_id : ''">
    </yearendNotification>
  </div>
</template>
<script>
import { inject, watch } from 'vue';
import expirePlan from './components/supporting/dialog_box/expirePlan.vue';
import { mapActions, mapGetters } from 'vuex';
import notificationEnable from './components/supporting/dialog_box/notificationEnable.vue';
import { registerSW } from './firebaseConfig';
import sidebar from './components/supporting/sidebar.vue';
import headbar from './components/supporting/dashboard/headbar.vue';
import bottombar from './components/supporting/dashboard/bottombar.vue';
import yearendNotification from './components/supporting/dialog_box/yearendNotification.vue';
export default {
  components: {
    expirePlan,
    notificationEnable,
    sidebar,
    headbar,
    bottombar,
    yearendNotification
  },
  setup() {
    const modalState = inject('modalState');
    return {
      modalState
    };
  },
  data() {
    return {
      companyInfo: null,
      showModal: false,
      notificationMessageVisible: false,
      //---sidebar and headbar options---
      isSidebarOpen: true,
      isMobile: false,
      isPad: false,
      route_item: 1, //---for sidebar---
      bottombar_item: 'home', //--- for bottom bar--
      update_sidebar: false,
      //---open the modal box don't go back---
      // anyModalOpen: false,
      updateModalOpen: false,
      //--sidebar open---
      openSidebarMobile: false,
      refreshKey: 0, // Reactive key for router-view
      is_choose_plan: false,
      //--open financial--
      open_financial: false,
    };
  },
  computed: {
    ...mapGetters('companies', ['currentCompanyList']),
    ...mapGetters('localStorageData', ['currentLocalDataList']),
    ...mapGetters('notificationAllow', ['currentLocalNotifyList']),
    ...mapGetters('features_list', ['currentFeatureList']),
    ...mapGetters('modalIsOpen', ['anyModalOpen']),
    ...mapGetters('sidebarandBottombarList', ['bottomSelected', 'sidebarselected', 'isEnableBottom', 'isLoginEnable']),
    ...mapGetters('invoice_setting', ['currentInvoice']),
  },
  methods: {
    ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
    ...mapActions('localStorageData', ['fetchLocalDataList', 'sidebarUpdate']),
    ...mapActions('notificationAllow', ['fetchLocalNotifyList']),
    ...mapActions('features_list', ['fetchFeatureList', 'updateFeatureList']),
    // Access modal control actions from the namespaced modal module
    ...mapActions('modalIsOpen', ['openModal', 'closeModal', 'toggleModal']),
    ...mapActions('device', ['detectIOS']),
    ...mapActions('invoice_setting', ['fetchInvoiceSetting']),
    // Set cookie helper
    /*setCookie(name, value, days) {
         let expires = "";
         if (days) {
           const date = new Date();
           date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
           expires = "; expires=" + date.toUTCString();
         }
         document.cookie = name + "=" + (value || "") + expires + "; path=/";
       },*/

    // Get cookie helper
    /*getCookie(name) {
      const nameEQ = name + "=";
      const cookiesArray = document.cookie.split(';');
      for (let i = 0; i < cookiesArray.length; i++) {
        let cookie = cookiesArray[i];
        while (cookie.charAt(0) === ' ') cookie = cookie.substring(1, cookie.length);
        if (cookie.indexOf(nameEQ) === 0) return cookie.substring(nameEQ.length, cookie.length);
      }
      return null;
    },*/
    // Fetch company data
    async fetchCompanyData() {
      try {
        if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0) {
          this.checkPlanExpiration();
        } else {
          await this.fetchCompanyList();
        }
      } catch (error) {
        console.error("Error fetching company data:", error);
      }
    },

    // Check if the stored cookie date matches today's date
    shouldFetchCompanyData() {
      const lastFetchDate = this.getCookie('lastCompanyFetchDate');
      const today = new Date().toISOString().split('T')[0];

      return !lastFetchDate || lastFetchDate !== today;
    },
    // Compare expire date with today's date
    checkPlanExpiration() {
      // console.log('Hello how many times it will will naviagate .....!');

      if (this.companyInfo && this.companyInfo.expiry_date) {
        const expireDate = this.companyInfo.expiry_date;
        const today = new Date().toISOString().split('T')[0];

        if (expireDate < today) {
          // Optional: Redirect to home page
          // this.$router.push('/');
        }
      }
    },
    // This method calls Firebase push notification setup
    async startNotifications() {
      try {
        // Request notification permission and initialize Firebase push
        const permission = await Notification.permission;
        if (permission === 'default') {
          console.warn('Notifications permission is in default state.');
          this.showModal = true; // Show a modal to encourage users to enable notifications
          this.showEnableNotificationsMessage(); // Call a method to display a message to enable notifications manually
        } else if (permission === 'denied') {
          console.warn('Notifications permission denied by the user.');
          this.showModal = true; // Same modal can be used here
          this.showEnableNotificationsMessage(); // Encourage user to change notification settings
        }
      } catch (error) {
        console.error('Error during notification setup:', error);
      }
    },

    // Show a message informing the user to enable notifications manually
    showEnableNotificationsMessage() {
      this.notificationMessageVisible = true; // Show the notification message
    },
    //----sidebar and headbar options----
    toggleSidebar() {
      this.isSidebarOpen = !this.isSidebarOpen;
      this.sidebarUpdate(this.isSidebarOpen);
    },
    //---is modal any open---
    emitUpdateIsOpen(value) {
      if (value !== undefined) {
        // this.anyModalOpen = value;
        if (value) {
          this.openModal();
        } else {
          this.closeModal();
        }
      }
    },
    closeSidebar() {
      this.isSidebarOpen = false;
    },
    handleSelectSidebarOption() {
      // Logic to handle selected sidebar option
      // Close sidebar after option is selected (if needed)
      this.closeSidebar();
    },
    updateIsMobile() {
      this.isMobile = window.innerWidth < 1024;
      this.isPad = window.innerWidth < 768;
    },
    refreshRouterView() {
      // Increment the key to force re-rendering of router-view
      this.refreshKey += 1;
    },
    validateSubscriptions() {
      const currentCompanyList = this.currentCompanyList;
      if (currentCompanyList && Object.keys(currentCompanyList).length > 0) {
        let validate_expire = false;
        const companyInfo = currentCompanyList;
        // Check if the expiry date exists and if the plan has expired
        if (companyInfo.expiry_date) {
          // Set expiry date to the end of the day (11:59 PM)
          const expiry_date = new Date(companyInfo.expiry_date);

          // Normalize expiry date to midnight (00:00:00)
          expiry_date.setHours(0, 0, 0, 0); // Set time to 00:00:00.000

          const current_date = new Date();
          current_date.setHours(0, 0, 0, 0); // Set time to 00:00:00.000

          // If the plan is still valid
          if (current_date > expiry_date) {
            validate_expire = true;
          }
        }
        // If no valid plan, or expired plan, and not already on subscription page, redirect to subscription page
        if ((!companyInfo.expiry_date || validate_expire) && this.currentLocalDataList && ['admin', 'Sub Admin'].includes(this.currentLocalDataList.roles[0])) {
          this.$router.push('/subscription'); // Redirect to subscription page
        }
      }
    },
    //--financial year---
    openfinancial() {
      this.open_financial = true;
    },
    closefinancial(data) {
      if (data) {
        this.fetchInvoiceSetting();
        this.open_financial = false;
      } else {
        this.open_financial = false;
      }
    }
  },
  mounted() {
    this.updateIsMobile();
    this.detectIOS();
    // Fetch company data if necessary
    if (!this.companyInfo) {
      this.fetchCompanyData();
    }
    if (this.isMobile) {
      this.isSidebarOpen = false;
    }
    // else if (this.currentCompanyList && Object.keys(this.currentCompanyList).length > 0) {
    //   this.checkPlanExpiration();
    // }
    this.fetchLocalNotifyList();
    if (this.currentLocalNotifyList !== undefined) {
      this.notification_allow = this.currentLocalNotifyList;
    }
    const collectForm = localStorage.getItem('track_new');
    if (collectForm && this.notification_allow) {
      this.startNotifications();
    }
    window.addEventListener('resize', this.updateIsMobile);
    this.validateSubscriptions();
    if (this.currentLocalDataList && !this.currentInvoice) {
      this.fetchInvoiceSetting();
    } else if (this.currentLocalDataList && this.currentInvoice && this.currentInvoice.length > 0) {
      if (!this.currentInvoice[0].reset_fy) {
        this.openfinancial();
      }

    }
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.updateIsMobile);
  },
  watch: {
    currentCompanyList: {
      deep: true,
      handler(newValue) {
        if (newValue && Object.keys(newValue).length > 0) {
          this.companyInfo = newValue;
          this.checkPlanExpiration();
          // Store today's date in cookies after successful API call
          // const today = new Date().toISOString().split('T')[0];
          // this.setCookie('lastCompanyFetchDate', today, 1);
          this.validateSubscriptions();
        } else {
          this.fetchCompanyData();
        }
      }
    },
    // currentLocalDataList: {
    //   deep: true,
    //   handler(newValue) {
    //     if (newValue && Object.keys(newValue).length > 0 && this.notification_allow) {
    //       this.startNotifications();
    //     }
    //   }
    // },
    currentLocalNotifyList: {
      deep: true,
      handler(newValue) {
        this.notification_allow = newValue;
      }
    },
    anyModalOpen: {
      deep: true,
      handler(newValue) {
        if (!newValue) {
          this.updateModalOpen = !this.updateModalOpen;
        }
      }
    },
    currentInvoice: {
      deep: true,
      handler(newvalue) {
        // == 0
        if (newvalue && newvalue.length > 0 && !newvalue[0].reset_fy) {
          this.openfinancial();
        }
      }
    }
  },
};
</script>
