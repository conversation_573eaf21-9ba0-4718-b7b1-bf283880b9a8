<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateProductsBarcodeAPIRequest;
use App\Http\Requests\API\UpdateProductsBarcodeAPIRequest;
use App\Models\ProductsBarcode;
use App\Repositories\ProductsBarcodeRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;

/**
 * Class ProductsBarcodeController
 * @package App\Http\Controllers\API
 */

class ProductsBarcodeAPIController extends AppBaseController
{
    /** @var  ProductsBarcodeRepository */
    private $productsBarcodeRepository;

    public function __construct(ProductsBarcodeRepository $productsBarcodeRepo)
    {
        $this->productsBarcodeRepository = $productsBarcodeRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/products_barcodes",
     *      summary="getProductsBarcodeList",
     *      tags={"ProductsBarcode"},
     *      description="Get all ProductsBarcodes",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/ProductsBarcode")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $productsBarcodes = $this->productsBarcodeRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($productsBarcodes->toArray(), 'Products Barcodes retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/products_barcodes",
     *      summary="createProductsBarcode",
     *      tags={"ProductsBarcode"},
     *      description="Create ProductsBarcode",
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/ProductsBarcode")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ProductsBarcode"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateProductsBarcodeAPIRequest $request)
    {
        $input = $request->all();

        $productsBarcode = $this->productsBarcodeRepository->create($input);

        return $this->sendResponse($productsBarcode->toArray(), 'Products Barcode saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/products_barcodes/{id}",
     *      summary="getProductsBarcodeItem",
     *      tags={"ProductsBarcode"},
     *      description="Get ProductsBarcode",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ProductsBarcode",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ProductsBarcode"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var ProductsBarcode $productsBarcode */
        $productsBarcode = $this->productsBarcodeRepository->find($id);

        if (empty($productsBarcode)) {
            return $this->sendError('Products Barcode not found');
        }

        return $this->sendResponse($productsBarcode->toArray(), 'Products Barcode retrieved successfully');
    }

    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/products_barcodes/{id}",
     *      summary="updateProductsBarcode",
     *      tags={"ProductsBarcode"},
     *      description="Update ProductsBarcode",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ProductsBarcode",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        description="Item object that needs to be added",
     *        @OA\JsonContent(ref="#/components/schemas/ProductsBarcode")
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/ProductsBarcode"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateProductsBarcodeAPIRequest $request)
    {
        $input = $request->all();

        /** @var ProductsBarcode $productsBarcode */
        $productsBarcode = $this->productsBarcodeRepository->find($id);

        if (empty($productsBarcode)) {
            return $this->sendError('Products Barcode not found');
        }

        $productsBarcode = $this->productsBarcodeRepository->update($input, $id);

        return $this->sendResponse($productsBarcode->toArray(), 'ProductsBarcode updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/products_barcodes/{id}",
     *      summary="deleteProductsBarcode",
     *      tags={"ProductsBarcode"},
     *      description="Delete ProductsBarcode",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of ProductsBarcode",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var ProductsBarcode $productsBarcode */
        $productsBarcode = $this->productsBarcodeRepository->find($id);

        if (empty($productsBarcode)) {
            return $this->sendError('Products Barcode not found');
        }

        $productsBarcode->delete();

        return $this->sendSuccess('Products Barcode deleted successfully');
    }
}
