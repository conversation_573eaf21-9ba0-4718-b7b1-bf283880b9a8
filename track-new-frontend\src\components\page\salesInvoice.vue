<template>
    <div class="flex h-screen">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->
        <div class="flex-grow overflow-y-auto w-full" :class="{ 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" class="non-printable" @display_hold_list="displayHold"
                :refresh="refresh" @refresh_store="refresh_store"></headbar> -->
            <div class="relative center-screen p-1 relative m-1 mt-1 overflow-y-auto">
                <salesInvoiceGenerateVue :companyId="companyId" :userId="userId" @display_hold_list="displayHold"
                    :enable_hold="enable_hold" @close_hold="displayHold" @refresh_hold="refreshhold"
                    :store_refresh="store_refresh" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"
                    @sales-save-req="salessaveData" :save_success="save_success" @updatesalesdata="saveDataUpdate">
                </salesInvoiceGenerateVue>
            </div>
        </div>
        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden non-printable"
            @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
        <dialogConfirmBox :visible="show_confirm_box" :message="message" :type="'sales'" @save="saveData"
            @ok="withoutSave" @cancel="cancelData"></dialogConfirmBox>
    </div>
</template>
<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/sales/salesInvoice/headbar.vue';
import salesInvoiceGenerateVue from '../supporting/sales/salesInvoice/salesInvoiceGenerate.vue';
import dialogConfirmBox from '../supporting/dialog_box/dialogConfirmBox.vue';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';
export default {
    name: 'sales_invoice',
    components: {
        // sidebar,
        // headbar,
        salesInvoiceGenerateVue,
        dialogConfirmBox
    },

    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            viewServiceData: null,
            typeofService: null,
            route_item: 5,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            data: [],
            shareData: {},
            service_id: null,
            //---api integration---
            companyId: null,
            userId: null,
            enable_hold: false,
            refresh: false,
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
            //---store data----
            isStoreData: false,
            save_success: false,
            //---confirmbox data---
            show_confirm_box: false,
            message: '',
        }
    },
    setup() {
        const pageTitle = 'Add / Update Invoice';
        const pageDescription = 'Create or update invoices seamlessly with detailed input options for accurate billing, ensuring efficient tracking and streamlined financial management.';
        useMeta(pageTitle, pageDescription);
        return { pageTitle };
    },
    methods: {
        ...mapActions('sidebarandBottombarList', ['updateIsEnableBottom']),
        ...mapActions('holdsList', ['fetchHoldsList', 'updateIsEnable', 'updateIsShowList']),

        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            this.isMobile = window.innerWidth < 1024;
        },
        // Method to parse the URL
        parseUrl() {
            // const urlParts = window.location.pathname.split('/');
            // const category = urlParts[urlParts.length - 4];
            // const categoryID = urlParts[urlParts.length - 3]
            // const id = urlParts[urlParts.length - 1];
            // this.category_id = Number(categoryID);
            // this.service_id = decodeURIComponent(id);
            // this.category_name = decodeURIComponent(category);
            // let findCategory = this.servicecategory_data.find((data) => data.id === Number(categoryID));
            // if (findCategory && id !== 'sales') {
            //     this.viewServiceData = findCategory.data.find((record => record.id === Number(id)));
            //     // console.log( this.viewServiceData, 'RRERERTETRT');
            // }
        },
        goBackPage() {
            this.$router.go(1);
        },
        displayHold() {
            this.enable_hold = !this.enable_hold;
        },
        refreshhold() {
            this.refresh = !this.refresh;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        },
        //---in case user not store data ---
        salessaveData(value) {
            // console.log(value, 'what about the data.....');
            this.isStoreData = value;
        },
        //---save data---
        saveData() {
            // console.log('save data....');
            this.show_confirm_box = false;
            this.save_success = true;
        },
        cancelData() {
            // console.log('cancel data....');
            this.show_confirm_box = false;
        },
        withoutSave() {
            // console.log('without save data...');
            this.show_confirm_box = false;
            this.isStoreData = false;
            this.$router.go(-1);
        },
        //--update sles save--
        saveDataUpdate() {
            this.save_success = false;
        }
    },
    mounted() {
        this.updateIsMobile(); // Initial check
        this.updateIsEnableBottom(false);
        this.updateIsEnable(true);
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            // // console.log(dataParse, 'WWWWWWWWWWWW');
            // this.servicecategory_data = dataParse;
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }

        // let existData = localStorage.getItem('invoiceSetting');
        // if (existData) {
        //     let parseData = JSON.parse(existData);
        //     if (parseData.disclaimer !== '') {
        //         let disclaimerMSG = parseData.disclaimer.split('\n');
        //         this.shareData = { ...parseData, disclaimer: disclaimerMSG };

        //     } else {
        //         this.shareData = parseData;
        //     }
        // }
        // const collectForm = localStorage.getItem('CategoriesForm');
        // if (collectForm) {
        //     let dataParse = JSON.parse(collectForm);
        //     // console.log(dataParse, 'WWWWWWWWWWWW');
        //     this.servicecategory_data = dataParse;
        // }
        // this.parseUrl(); // Call the function to parse the URL
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        this.updateIsEnable(false);
        this.updateIsShowList(false);
        this.updateIsEnableBottom(true);
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen || this.isStoreData) {
            if (this.isStoreData) {
                // alert('are you sure get back?');
                this.message = 'Are you sure you want to go back? Unsaved data may be lost...!';
                this.show_confirm_box = true;
            }
            if (this.anyModalOpen) {
                // If any modal is open, close all modals
                this.updateModalOpen = !this.updateModalOpen;
            }
            // Prevent the route from changing
            next(false);
        } else {
            this.updateIsEnable(false);
            this.updateIsShowList(false);
            this.updateIsEnableBottom(true);
            // If no modals are open, allow the navigation
            next();
        }
    },
};
</script>

<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}

/* Center content on larger screens */
.center-screen {
    display: flex;
    align-items: center;
    justify-content: center;
}

@media print {

    /* Additional styles for printing */
    body {
        overflow-y: hidden;
        /* Hide vertical scrollbar during printing */
    }

    .non-printable {
        display: none;
    }

    /* Apply different styles for printing */
    .center-content {
        width: 100%;
        margin: 0;
    }
}
</style>