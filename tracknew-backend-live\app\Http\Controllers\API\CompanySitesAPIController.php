<?php

namespace App\Http\Controllers\API;

use App\Http\Requests\API\CreateCompanySitesAPIRequest;
use App\Http\Requests\API\UpdateCompanySitesAPIRequest;
use App\Models\CompanySites;
use App\Models\WebsiteTemplates;
use App\Repositories\CompanySitesRepository;
use Illuminate\Http\Request;
use App\Http\Controllers\AppBaseController;
use Response;
use Auth;

/**
 * Class CompanySitesController
 * @package App\Http\Controllers\API
 */

class CompanySitesAPIController extends AppBaseController
{
    /** @var  CompanySitesRepository */
    private $companySitesRepository;

    public function __construct(CompanySitesRepository $companySitesRepo)
    {
        $this->companySitesRepository = $companySitesRepo;
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Get(
     *      path="/companySites",
     *      summary="getCompanySitesList",
     *      tags={"CompanySites"},
     *      description="Get all CompanySites",
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="array",
     *                  @OA\Items(ref="#/definitions/CompanySites")
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function index(Request $request)
    {
        $companySites = $this->companySitesRepository->all(
            $request->except(['skip', 'limit']),
            $request->get('skip'),
            $request->get('limit')
        );

        return $this->sendResponse($companySites->toArray(), 'Company Sites retrieved successfully');
    }

    /**
     * @param Request $request
     * @return Response
     *
     * @OA\Post(
     *      path="/companySites",
     *      summary="createCompanySites",
     *      tags={"CompanySites"},
     *      description="Create CompanySites",
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CompanySites"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function store(CreateCompanySitesAPIRequest $request)
    {
        $input = $request->all();

        $companySites = $this->companySitesRepository->create($input);

        return $this->sendResponse($companySites->toArray(), 'Company Sites saved successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Get(
     *      path="/companySites/{id}",
     *      summary="getCompanySitesItem",
     *      tags={"CompanySites"},
     *      description="Get CompanySites",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CompanySites",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CompanySites"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function show($id)
    {
        /** @var CompanySites $companySites */
        $companySites = $this->companySitesRepository->find($id);

        if (empty($companySites)) {
            return $this->sendError('Company Sites not found');
        }

        return $this->sendResponse($companySites->toArray(), 'Company Sites retrieved successfully');
    }


    public function getWebsiteByCompanyId($company_id, Request $request)
    {
        

     
        /** @var CompanySites $companySites */
        $companySites = CompanySites::where('company_id', $company_id)->first();

        

        if (empty($companySites)) {
          
          return response()->json([
            'success' => false,
            'data' => $companySites,
            'message' => 'no company  found'
        ]);
           
        }
        

        return $this->sendResponse($companySites , 'Company Sites retrieved successfully');
    }

    public function checkUsernameAvailability(Request $request)
    {
        $request->validate(['username' => 'required|string']);

      
    
        $usernameExists = CompanySites::where('username', $request->username)->exists();
    
        return response()->json([
            'available' => !$usernameExists,
            'message' => $usernameExists ? 'Username is already taken' : 'Username is available'
        ]);
    }
    
    public function registerWebsite(Request $request){

        $validated = $request->validate([
            'websiteName' => 'required|string|max:255',
            'username' => 'required|string|max:255|unique:company_sites,username',
            'company_id' => 'required|string',
          	'template_id' => 'required'
        ]);
      
     $web_temp = WebsiteTemplates::where('id', $validated['template_id'])->first();


        $companySite = CompanySites::create([
            'website_name' => $validated['websiteName'],
            'username' => $validated['username'],
            'company_id' => $validated['company_id'],
            'template_id' => $validated['template_id'] ?? 1,
          	'domain_status' => 0,
            'theme_path'=> $web_temp->path,
            'theme_settings'=> $web_temp->theme_settings,
            'header' =>  $web_temp->header_items
            
        ]);

        return response()->json([
            'success' => true,
            'data' => $companySite
        ]);

    }
  
  
  	 public function updateDomain(Request $request)
    {
        $data = $request->validate([
            'company_id' => 'required|string', // Assuming company_id is provided            
        ]);  
        // Use updateOrCreate for conditional update or creation
        $companySite = CompanySites::updateOrCreate(
            ['company_id' => $data['company_id']], // Condition to check
            [
                'domain_name' => $request->input('domain_name', []), // Store all data in primary_data as JSON
              	'domain_status' => 2,
                'updated_at' => now(), // Update timestamp if needed
            ]
        );
    
        return $this->sendResponse($companySite->toArray(), 'Company site data saved successfully');
       
     }

    public function updateOrCreateCompanySite(Request $request)
    {
        $data = $request->validate([
            'company_id' => 'required|string',     
        ]);    
      
      	$user = Auth::user();        
        $company_id = $user->company_id;
      	$companySite = CompanySites::where('company_id', $company_id)->first();
    
        // Prepare data to store in primary_data as JSON
        $primaryData = [
            'websiteSettings' => $request->input('websiteSettings', []),
            'homePageSlider' => $request->input('homePageSlider', []),
            'services' => $request->input('services', []),
            'brochure' => $request->input('brochure', null),
            'testimonials' => $request->input('testimonials', []),
          	'company_video' => $request->input('company_video', []),
          	'pages' => $request->input('pages', []),
            'products' => $request->input('products', []),
            'about' => $request->input('about', [])
        ];
      
      	$history = json_decode($companySite->history, true) ?? []; // Decode JSON to an array

        if (!empty($companySite)) {
            $currentData = [
                'primary_data' => json_decode($companySite->primary_data, true) ?? [],
                'updated_at' => $companySite->updated_at,
            ];        
            $history = array_slice($history, -9);
            $history[] = $currentData;
        }
    
        // Use updateOrCreate for conditional update or creation
        $companySiteData = CompanySites::updateOrCreate(
            ['company_id' => $company_id], 
            [
              	 'services' => json_encode($request->input('services', [])),
        		 'products' => json_encode($request->input('products', [])),
        		 'testimonials' => json_encode($request->input('testimonials', [])),
        		 'company_data' => json_encode($request->input('websiteSettings', [])),
              	 'company_video' => json_encode($request->input('company_video', [])),
        		 'pages' => json_encode($request->input('pages', [])),
        		 'primary_data' => json_encode($primaryData),
              	 'history' => json_encode($history),
              	// 'template_id' => $request->input('template_id') ?? $companySiteData->template_id,
        		 'updated_at' => now(),
            ]
        );    
        return $this->sendResponse($companySiteData->toArray(), 'Company site data saved successfully');
    }


    public function updateGallery(Request $request)
    {
        $data = $request->validate([
            'company_id' => 'required|string', // Assuming company_id is provided
            
        ]);
    
        
    
        // Use updateOrCreate for conditional update or creation
        $companySite = CompanySites::updateOrCreate(
            ['company_id' => $data['company_id']], // Condition to check
            [
                'image_data' => json_encode($request->input('image_data', '')), // Store all data in primary_data as JSON
                'updated_at' => now(), // Update timestamp if needed
            ]
        );
    
        return $this->sendResponse($companySite->toArray(), 'Company site data saved successfully');
    }
    


    /**
     * @param int $id
     * @param Request $request
     * @return Response
     *
     * @OA\Put(
     *      path="/companySites/{id}",
     *      summary="updateCompanySites",
     *      tags={"CompanySites"},
     *      description="Update CompanySites",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CompanySites",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\RequestBody(
     *        required=true,
     *        @OA\MediaType(
     *            mediaType="application/x-www-form-urlencoded",
     *            @OA\Schema(
     *                type="object",
     *                required={""},
     *                @OA\Property(
     *                    property="name",
     *                    description="desc",
     *                    type="string"
     *                )
     *            )
     *        )
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  ref="#/definitions/CompanySites"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function update($id, UpdateCompanySitesAPIRequest $request)
    {
        $input = $request->all();

        /** @var CompanySites $companySites */
        $companySites = $this->companySitesRepository->find($id);
      
      	$temp = WebsiteTemplates::where('id', $companySites->template_id)->first();
      	$input['theme_path'] =  $temp->path;

        if (empty($companySites)) {
            return $this->sendError('Company Sites not found');
        }

        $companySites = $this->companySitesRepository->update($input, $id);

        return $this->sendResponse($companySites->toArray(), 'CompanySites updated successfully');
    }

    /**
     * @param int $id
     * @return Response
     *
     * @OA\Delete(
     *      path="/companySites/{id}",
     *      summary="deleteCompanySites",
     *      tags={"CompanySites"},
     *      description="Delete CompanySites",
     *      @OA\Parameter(
     *          name="id",
     *          description="id of CompanySites",
     *           @OA\Schema(
     *             type="integer"
     *          ),
     *          required=true,
     *          in="path"
     *      ),
     *      @OA\Response(
     *          response=200,
     *          description="successful operation",
     *          @OA\Schema(
     *              type="object",
     *              @OA\Property(
     *                  property="success",
     *                  type="boolean"
     *              ),
     *              @OA\Property(
     *                  property="data",
     *                  type="string"
     *              ),
     *              @OA\Property(
     *                  property="message",
     *                  type="string"
     *              )
     *          )
     *      )
     * )
     */
    public function destroy($id)
    {
        /** @var CompanySites $companySites */
        $companySites = $this->companySitesRepository->find($id);

        if (empty($companySites)) {
            return $this->sendError('Company Sites not found');
        }

        $companySites->delete();

        return $this->sendSuccess('Company Sites deleted successfully');
    }
}
