# Track New - Deployment & Configuration Guide

## Overview

This guide covers the complete deployment process for the Track New Service Management System, including server setup, environment configuration, security considerations, and production optimizations.

## Server Requirements

### Minimum System Requirements
- **OS**: Ubuntu 20.04 LTS or CentOS 8+
- **CPU**: 2 vCPUs (4+ recommended for production)
- **RAM**: 4GB (8GB+ recommended for production)
- **Storage**: 50GB SSD (100GB+ recommended)
- **Network**: 1Gbps connection

### Software Requirements
- **Web Server**: Nginx 1.18+ or Apache 2.4+
- **PHP**: 8.0+ with required extensions
- **Database**: MySQL 8.0+ or MariaDB 10.5+
- **Node.js**: 16+ for frontend build
- **SSL Certificate**: Let's Encrypt or commercial SSL
- **Process Manager**: PM2 or Supervisor (optional)

## Server Setup

### 1. Initial Server Configuration

#### Update System
```bash
sudo apt update && sudo apt upgrade -y
sudo apt install curl wget git unzip software-properties-common -y
```

#### Install PHP 8.0+
```bash
sudo add-apt-repository ppa:ondrej/php -y
sudo apt update
sudo apt install php8.1 php8.1-fpm php8.1-mysql php8.1-xml php8.1-curl php8.1-mbstring php8.1-zip php8.1-gd php8.1-bcmath php8.1-json php8.1-intl -y
```

#### Install MySQL 8.0
```bash
sudo apt install mysql-server -y
sudo mysql_secure_installation
```

#### Install Nginx
```bash
sudo apt install nginx -y
sudo systemctl enable nginx
sudo systemctl start nginx
```

#### Install Composer
```bash
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
sudo chmod +x /usr/local/bin/composer
```

#### Install Node.js & NPM
```bash
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install nodejs -y
```

### 2. Database Setup

#### Create Database and User
```sql
CREATE DATABASE tracknew_production CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'tracknew_user'@'localhost' IDENTIFIED BY 'secure_password_here';
GRANT ALL PRIVILEGES ON tracknew_production.* TO 'tracknew_user'@'localhost';
FLUSH PRIVILEGES;
```

#### Configure MySQL for Production
```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

Add/modify these settings:
```ini
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
max_connections = 200
query_cache_size = 64M
query_cache_type = 1
```

## Backend Deployment

### 1. Deploy Laravel Application

#### Clone and Setup
```bash
cd /var/www
sudo git clone https://github.com/your-repo/tracknew-backend.git
sudo chown -R www-data:www-data tracknew-backend
cd tracknew-backend
```

#### Install Dependencies
```bash
composer install --optimize-autoloader --no-dev
```

#### Environment Configuration
```bash
cp .env.example .env
nano .env
```

#### Production .env Configuration
```env
APP_NAME="Track New"
APP_ENV=production
APP_KEY=base64:your_generated_key_here
APP_DEBUG=false
APP_URL=https://yourdomain.com

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=tracknew_production
DB_USERNAME=tracknew_user
DB_PASSWORD=secure_password_here

BROADCAST_DRIVER=log
CACHE_DRIVER=redis
FILESYSTEM_DRIVER=s3
QUEUE_CONNECTION=redis
SESSION_DRIVER=redis
SESSION_LIFETIME=120

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=your-smtp-host
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=your-aws-key
AWS_SECRET_ACCESS_KEY=your-aws-secret
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=your-s3-bucket
AWS_USE_PATH_STYLE_ENDPOINT=false

JWT_SECRET=your-jwt-secret-key
JWT_TTL=60

FIREBASE_SERVER_KEY=your-firebase-server-key
WHATSAPP_API_KEY=your-whatsapp-api-key
SMS_API_KEY=your-sms-api-key

PHONEPE_MERCHANT_ID=your-phonepe-merchant-id
PHONEPE_SALT_KEY=your-phonepe-salt-key
PHONEPE_SALT_INDEX=1
```

#### Generate Application Key
```bash
php artisan key:generate
php artisan jwt:secret
```

#### Run Migrations and Seeders
```bash
php artisan migrate --force
php artisan db:seed --force
```

#### Optimize for Production
```bash
php artisan config:cache
php artisan route:cache
php artisan view:cache
php artisan optimize
```

#### Set Permissions
```bash
sudo chown -R www-data:www-data /var/www/tracknew-backend
sudo chmod -R 755 /var/www/tracknew-backend
sudo chmod -R 775 /var/www/tracknew-backend/storage
sudo chmod -R 775 /var/www/tracknew-backend/bootstrap/cache
```

### 2. Nginx Configuration for Backend

```bash
sudo nano /etc/nginx/sites-available/tracknew-api
```

```nginx
server {
    listen 80;
    server_name api.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.yourdomain.com;
    root /var/www/tracknew-backend/public;
    index index.php;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/api.yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/api.yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied expired no-cache no-store private must-revalidate auth;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript;

    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.1-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_hide_header X-Powered-By;
    }

    location ~ /\.(?!well-known).* {
        deny all;
    }

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
    limit_req zone=api burst=20 nodelay;

    # File Upload Size
    client_max_body_size 50M;
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/tracknew-api /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## Frontend Deployment

### 1. Build and Deploy Vue.js Application

#### Clone and Setup
```bash
cd /var/www
sudo git clone https://github.com/your-repo/tracknew-frontend.git
sudo chown -R www-data:www-data tracknew-frontend
cd tracknew-frontend
```

#### Install Dependencies
```bash
npm ci --production
```

#### Environment Configuration
```bash
nano .env.production
```

```env
VITE_APP_NAME="Track New"
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_APP_URL=https://yourdomain.com
VITE_FIREBASE_API_KEY=your-firebase-api-key
VITE_FIREBASE_AUTH_DOMAIN=your-project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your-project-id
VITE_FIREBASE_STORAGE_BUCKET=your-project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=123456789
VITE_FIREBASE_APP_ID=1:123456789:web:abcdef
VITE_FIREBASE_VAPID_KEY=your-vapid-key
```

#### Build for Production
```bash
npm run build
```

#### Set Permissions
```bash
sudo chown -R www-data:www-data /var/www/tracknew-frontend
sudo chmod -R 755 /var/www/tracknew-frontend
```

### 2. Nginx Configuration for Frontend

```bash
sudo nano /etc/nginx/sites-available/tracknew-frontend
```

```nginx
server {
    listen 80;
    server_name yourdomain.com www.yourdomain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name yourdomain.com www.yourdomain.com;
    root /var/www/tracknew-frontend/dist;
    index index.html;

    # SSL Configuration
    ssl_certificate /etc/letsencrypt/live/yourdomain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/yourdomain.com/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/x-javascript application/xml+rss application/javascript application/json;

    # Cache Static Assets
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Handle Vue.js Routing
    location / {
        try_files $uri $uri/ /index.html;
    }

    # Service Worker
    location /sw.js {
        add_header Cache-Control "no-cache";
        proxy_cache_bypass $http_pragma;
        proxy_cache_revalidate on;
        expires off;
        access_log off;
    }

    # Firebase Messaging Service Worker
    location /firebase-messaging-sw.js {
        add_header Cache-Control "no-cache";
        expires off;
        access_log off;
    }
}
```

Enable the site:
```bash
sudo ln -s /etc/nginx/sites-available/tracknew-frontend /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## SSL Certificate Setup

### Using Let's Encrypt (Certbot)

#### Install Certbot
```bash
sudo apt install certbot python3-certbot-nginx -y
```

#### Generate Certificates
```bash
sudo certbot --nginx -d yourdomain.com -d www.yourdomain.com
sudo certbot --nginx -d api.yourdomain.com
```

#### Auto-renewal Setup
```bash
sudo crontab -e
```

Add this line:
```bash
0 12 * * * /usr/bin/certbot renew --quiet
```

## Performance Optimizations

### 1. Redis Setup for Caching

#### Install Redis
```bash
sudo apt install redis-server -y
sudo systemctl enable redis-server
sudo systemctl start redis-server
```

#### Configure Redis
```bash
sudo nano /etc/redis/redis.conf
```

Update these settings:
```ini
maxmemory 1gb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### 2. PHP-FPM Optimization

```bash
sudo nano /etc/php/8.1/fpm/pool.d/www.conf
```

```ini
pm = dynamic
pm.max_children = 50
pm.start_servers = 5
pm.min_spare_servers = 5
pm.max_spare_servers = 35
pm.max_requests = 500
```

### 3. MySQL Optimization

```bash
sudo nano /etc/mysql/mysql.conf.d/mysqld.cnf
```

```ini
[mysqld]
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_flush_log_at_trx_commit = 2
max_connections = 200
query_cache_size = 64M
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
```

## Security Configurations

### 1. Firewall Setup (UFW)

```bash
sudo ufw enable
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw allow mysql
```

### 2. Fail2Ban Setup

```bash
sudo apt install fail2ban -y
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/*error.log
findtime = 600
bantime = 7200
maxretry = 10
```

### 3. Regular Security Updates

Create update script:
```bash
sudo nano /usr/local/bin/security-updates.sh
```

```bash
#!/bin/bash
apt update
apt upgrade -y
apt autoremove -y
systemctl restart nginx
systemctl restart php8.1-fpm
systemctl restart mysql
```

```bash
sudo chmod +x /usr/local/bin/security-updates.sh
sudo crontab -e
```

Add weekly security updates:
```bash
0 2 * * 0 /usr/local/bin/security-updates.sh
```

## Monitoring and Logging

### 1. Log Rotation

```bash
sudo nano /etc/logrotate.d/tracknew
```

```
/var/www/tracknew-backend/storage/logs/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
}
```

### 2. System Monitoring

Install monitoring tools:
```bash
sudo apt install htop iotop nethogs -y
```

### 3. Application Monitoring

Set up Laravel Telescope for production monitoring:
```bash
cd /var/www/tracknew-backend
composer require laravel/telescope
php artisan telescope:install
php artisan migrate
```

## Backup Strategy

### 1. Database Backup Script

```bash
sudo nano /usr/local/bin/backup-database.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/tracknew"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="tracknew_production"
DB_USER="tracknew_user"
DB_PASS="secure_password_here"

mkdir -p $BACKUP_DIR
mysqldump -u$DB_USER -p$DB_PASS $DB_NAME | gzip > $BACKUP_DIR/db_backup_$DATE.sql.gz

# Keep only last 30 days of backups
find $BACKUP_DIR -name "db_backup_*.sql.gz" -mtime +30 -delete
```

### 2. File Backup Script

```bash
sudo nano /usr/local/bin/backup-files.sh
```

```bash
#!/bin/bash
BACKUP_DIR="/var/backups/tracknew"
DATE=$(date +%Y%m%d_%H%M%S)

mkdir -p $BACKUP_DIR

# Backup application files
tar -czf $BACKUP_DIR/app_backup_$DATE.tar.gz /var/www/tracknew-backend /var/www/tracknew-frontend

# Keep only last 7 days of file backups
find $BACKUP_DIR -name "app_backup_*.tar.gz" -mtime +7 -delete
```

### 3. Automated Backup Schedule

```bash
sudo crontab -e
```

```bash
# Daily database backup at 2 AM
0 2 * * * /usr/local/bin/backup-database.sh

# Weekly file backup on Sunday at 3 AM
0 3 * * 0 /usr/local/bin/backup-files.sh
```

## Deployment Checklist

### Pre-deployment
- [ ] Server provisioned and configured
- [ ] Domain and SSL certificates configured
- [ ] Database created and secured
- [ ] Environment variables configured
- [ ] Firewall and security measures in place

### Backend Deployment
- [ ] Code deployed and dependencies installed
- [ ] Database migrations run
- [ ] Application optimized for production
- [ ] File permissions set correctly
- [ ] Nginx configuration tested

### Frontend Deployment
- [ ] Code built for production
- [ ] Static assets optimized
- [ ] PWA configuration verified
- [ ] CDN configured (if applicable)

### Post-deployment
- [ ] Application functionality tested
- [ ] SSL certificates verified
- [ ] Performance monitoring enabled
- [ ] Backup systems tested
- [ ] Security scans completed
- [ ] Documentation updated

This deployment guide ensures a secure, optimized, and maintainable production environment for the Track New Service Management System.
