<template>
    <div class="flex h-screen overflow-auto relative">
        <!-- Sidebar (conditionally rendered based on isMobile) -->
        <!-- <div v-if="!isMobile" class="custom-scrollbar-hidden">
            <div>
                <sidebar v-if="!isMobile" :route_item="route_item" :updateModalOpen="updateModalOpen"
                    @updateIsOpen="emitUpdateIsOpen"></sidebar>
            </div>
        </div> -->

        <!-- Content area with its own scroll -->
        <div class="flex flex-col flex-grow overflow-y-auto w-full"
            :class="{ 'mb-[60px] mt-[57px]': isMobile, 'mt-[57px]': !isMobile }">
            <!-- Headbar with its content -->
            <!-- <headbar @toggle-sidebar="toggleSidebar" :servicesList="dataFromChild" @searchData="getFiteredDataList"
                @refresh_store="refresh_store">
            </headbar> -->

            <!-- services home -->
            <div class="relative">
                <bannerDesign></bannerDesign>
                <servicesCategoriesView :isMobile="isMobile" :searchedData="getFilteredData"
                    :store_refresh="store_refresh" :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen">
                </servicesCategoriesView>
            </div>
        </div>

        <!-- Mobile Sidebar (conditionally rendered based on isMobile and isSidebarOpen) -->
        <!-- <div v-if="isMobile && isSidebarOpen"
            class="fixed inset-0 bg-gray-800 bg-opacity-50 z-50 custom-scrollbar-hidden" @click="closeSidebar">
            <sidebar @selectSidebarOption="handleSelectSidebarOption" :route_item="route_item"
                :updateModalOpen="updateModalOpen" @updateIsOpen="emitUpdateIsOpen"></sidebar>
        </div> -->
    </div>
</template>

<script>
// import sidebar from '../supporting/sidebar.vue';
// import headbar from '../supporting/services/home/<USER>';
import servicesCategoriesView from '../supporting/services/home/<USER>';
import { mapActions, mapGetters } from 'vuex';
import { useMeta } from '@/composables/useMeta';
import bannerDesign from '../supporting/banner/bannerDesign.vue';

export default {
    name: 'services_home',
    components: {
        // sidebar,
        // headbar,
        servicesCategoriesView,
        bannerDesign
    },
    data() {
        return {
            isMobile: false,
            isSidebarOpen: false,
            typeofService: null,
            route_item: 3,
            category_id: null,
            category_name: null,
            servicecategory_data: [],
            dataFromChild: [],
            getFilteredData: {},
            originalData: [],
            category_data: [],
            //---api integration--
            companyId: null,
            userId: null,
            pagination: {},
            serviceTrack: [],
            store_refresh: false,
            //---open the modal box don't go back---
            anyModalOpen: false,
            updateModalOpen: false,
        };
    },
    setup() {
        const pageTitle = 'Services';
        const pageDescription = 'Maintain high service standards with tools for tracking quality, feedback, and continuous improvement.';
        useMeta(pageTitle, pageDescription);

        return { pageTitle };
    },
    methods: {
        toggleSidebar() {
            // console.log('hello......');
            this.isSidebarOpen = !(this.isSidebarOpen);
            // console.log(isSidebarOpen, 'What about value...', this.isMobile);
        },
        closeSidebar() {
            this.isSidebarOpen = false;
        },
        handleSelectSidebarOption() {
            // Logic to handle selected sidebar option
            // Close sidebar after option is selected (if needed)
            this.closeSidebar();
        },
        updateIsMobile() {
            //---768
            this.isMobile = window.innerWidth < 1024;
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        getFiteredDataList(data) {
            if (!this.isEmptyObject(data)) {
                this.getFilteredData = data;
            }

        },
        refresh_store() {
            this.store_refresh = !this.store_refresh;
        },
        //---is modal any open---
        emitUpdateIsOpen(value) {
            if (value !== undefined) {
                this.anyModalOpen = value;
            }
        }

    },
    mounted() {
        const collectForm = localStorage.getItem('track_new');
        if (collectForm) {
            let dataParse = JSON.parse(collectForm);
            this.companyId = dataParse.company_id;
            this.userId = dataParse.user_id + '';
        }
        this.updateIsMobile(); // Initial check
        window.addEventListener('resize', this.updateIsMobile);
    },
    beforeDestroy() {
        window.removeEventListener('resize', this.updateIsMobile);
    },
    watch: {
        isSidebarOpen: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.anyModalOpen = newValue;
                } else {
                    this.anyModalOpen = newValue;
                }
            }
        }
    },
    //---validate is any modal open andstrict the page naviagtion-
    beforeRouteLeave(to, from, next) {
        if (this.anyModalOpen) {
            // If any modal is open, close all modals
            this.updateModalOpen = !this.updateModalOpen;
            // Prevent the route from changing
            next(false);
        } else {
            // If no modals are open, allow the navigation
            next();
        }
    },

};
</script>


<style scoped>
.custom-scrollbar-hidden::-webkit-scrollbar {
    display: none;
}
</style>
