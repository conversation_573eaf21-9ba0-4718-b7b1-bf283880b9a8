<template>
    <div class="sm:px-3 py-3"
        :class="{ 'manualStyle text-sm sm:mb-[60px] lg:mb-[0px]': isMobile, 'text-sm': !isMobile }"
        ref="scrollContainer" @scroll="handleScroll">
        <div class="my-custom-margin">
            <!--search-->
            <div v-if="isMobile" class="mt-2 sm:m-4" :class="{ 'mt-2': isMobile }">
                <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer" @resetData="resetToSearch"
                    :resetSearch="resetSearch">
                </searchCustomer>
            </div>
            <!--page header-->
            <div v-if="!isMobile" class="m-1 flex items-center space-x-4">
                <p class="font-bold text-xl">Payment In</p>
                <div v-if="!open_skeleton"
                    class="flex bg-green-100  px-3 p-1 text-center border rounded-lg text-sm shadow">
                    <p class="text-gray-700">Total Payment In
                        :</p>
                    <p class="font-semibold pl-1">
                        {{ pagination.total }}
                    </p>
                </div>
            </div>
            <!--new design header-->
            <div v-if="!isMobile" class="flex justify-between my-4">
                <div class="flex mr-2 space-x-4">
                    <button @click="openPayment" :class="{ 'mr-2': isMobile }"
                        class="shadow-inner shadow-green-100 border border-green-600 bg-green-600 px-3 rounded-lg p-1 text-white hover:bg-green-700 text-sm text-center"><span
                            class="text-center px-1"><font-awesome-icon icon="fa-solid fa-plus" /></span>
                        <span v-if="!isMobile" class="text-center">New Payment In</span>
                    </button>
                    <!--view design-->
                    <div class="flex items-center sm:mr-2" :class="{ 'mr-2': isMobile }">
                        <div v-if="items_category === 'tile'"
                            class="info-msg px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200"
                            @click="toggleView" :title02="'Table view'"
                            :class="{ 'bg-white': items_category === 'tile' }">
                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-bars" />
                        </div>
                        <div v-if="items_category !== 'tile'"
                            class="px-2 py-1 rounded-lg border border-gray-400 flex-shrink-0 cursor-pointer hover:bg-blue-200 info-msg"
                            :class="{ 'bg-white': items_category !== 'tile' }" @click="toggleView"
                            :title02="'Card view'">

                            <font-awesome-icon icon="fa-solid fa-check" class="text-green-600 pr-2" />
                            <font-awesome-icon icon="fa-solid fa-grip" />
                        </div>
                    </div>
                </div>
            </div>
            <!---fiter information-->
            <div v-if="Object.keys(filteredBy).length > 0" class="text-xs flex -mb-3 flex-row overflow-auto">
                <p class="text-blue-600 mr-2">Filtered By:</p>
                <div v-for="(value, key) in filteredBy" :key="key" class="flex flex-row text-blue-600 mr-2">
                    <p class="mr-1">{{ key }} = </p>
                    <p>{{ key === 'expense_type' ? 'type' : value }}</p>
                </div>
                <button @click="resetTheFilter" title="reset filter"
                    class="text-red-600 border hover:border-red-600 rounded rounded-full px-1">X</button>
            </div>
            <!---load skeleton-->
            <skeleton class="mt-5" v-if="open_skeleton" :isLoading="open_skeleton"
                :cols="items_category === 'tile' ? number_of_columns : 3"
                :rows="items_category === 'tile' ? number_of_rows : 20" :gap="gap"
                :type="items_category === 'tile' ? 'table' : 'grid'">
            </skeleton>
            <div v-if="!open_skeleton && data.length > 0" class="text-sm mt-3" :class="{ 'm-0': !isMobile }">
                <div
                    :class="{ 'sm:p-4 p-2 py-2 bg-white border rounded rounded-lg border-gray-200': !isMobile && items_category === 'tile' }">
                    <!--table header-->
                    <div v-if="!isMobile" class="flex justify-between"
                        :class="{ 'bg-white p-2 rounded-lg border my-3': items_category !== 'tile', 'mb-3': items_category === 'tile' }">
                        <!---records per page with refresh-->
                        <div class="flex space-x-4 items-center">
                            <div>
                                <select v-model="recordsPerPage" @change="changePage"
                                    class="border border-gray-300 rounded-lg pr-5 p-1">
                                    <option v-for="option in options" :key="option" :value="option" class="text-xs">
                                        {{ option }}
                                    </option>
                                </select>
                            </div>
                            <div>
                                <button class="border rounded-lg bg-white p-1 px-3 hover:bg-gray-300 info-msg"
                                    :title02="'Refersh Data'" @click="refreshDataTable">
                                    <font-awesome-icon icon="fa-solid fa-arrows-rotate" />
                                </button>
                            </div>
                        </div>
                        <!--search bar--->
                        <div>
                            <searchCustomer :isMobile="isMobile" @searchData="selectedCustomer"
                                @resetData="resetToSearch" :resetSearch="resetSearch">
                            </searchCustomer>
                        </div>
                    </div>
                    <div v-if="items_category === 'tile'" class="table-container overflow-x-auto">
                        <table class="table w-full">
                            <thead>
                                <tr class="table-head">
                                    <!--dynamic-->
                                    <th v-for="(column, index) in dynamicFields" :key="index"
                                        :class="{ 'hidden': !column.visible }" class="py-2 cursor-pointer relative"
                                        @mouseover="showSortIcons(column)" @mouseleave="hideSortIcons(column)"
                                        @click="getSortType((column), column.field)">
                                        <div class="flex justify-between items-center px-2 relative">
                                            <p>{{ column.label === 'Payment Date' ? 'Date' : column.label }}</p>
                                            <div v-if="isShortVisible(column.field)"
                                                class="text-xs px-1 absolute right-0 top-1/2 transform -translate-y-1/2">
                                                <!-- Ascending Icon -->
                                                <span
                                                    v-if="getSortTypeDisplay(column.field) === 'desc' || getSortTypeDisplay(column.field) === ''"
                                                    :class="{ 'bg-gray-300 rounded-lg px-1': isHighlighted(column.field, 'asc') }"
                                                    :title02="'Ascending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-up" />
                                                </span>

                                                <!-- Descending Icon -->
                                                <span v-if="getSortTypeDisplay(column.field) === 'asc'"
                                                    class="block hover:bg-gray-200 info-msg bg-gray-300 rounded-lg px-1"
                                                    :title02="'Descending order'">
                                                    <font-awesome-icon icon="fa-solid fa-chevron-down" />
                                                </span>
                                            </div>
                                        </div>
                                    </th>
                                    <th class="leading-none text-center table-border">
                                        <div class="flex justify-center items-center space-x-2">
                                            <p>Actions</p>
                                            <div class="relative">
                                                <button @click.stop="toggleDropdown"
                                                    class="flex items-center p-1 rounded-lg">
                                                    <font-awesome-icon icon="fa-solid fa-filter" />
                                                </button>
                                                <div v-if="isDropdownOpen" ref="settingOPtion"
                                                    class="absolute mt-1 bg-white border border-gray-400 rounded top-[25px] right-[0px] z-10 overflow-auto h-48 w-48"
                                                    style="max-height: 300px;">
                                                    <div v-for="(column, index) in columns" :key="index"
                                                        class="flex items-center p-2">
                                                        <input type="checkbox" v-model="column.visible"
                                                            class="form-checkbox h-5 w-5 text-gray-600 border" />
                                                        <span class="text-xs ml-2">{{ column.label }}</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="(record, index) in data" :key="index">
                                    <td v-for="(column, colIndex) in columns" :key="colIndex"
                                        :class="{ 'hidden': !column.visible }"
                                        class="border px-1 py-1 text-sm text-center">
                                        <span v-if="column.field === 'payment_date'" class="text-blue-800"
                                            :title="calculateDaysAgo(formattedDate(record[column.field]))">{{
                                                validateDateTime(record[column.field]) ?
                                                    formatDateTime(record[column.field]) : '' }}</span>
                                        <span v-if="column.field === 'customer'">{{ record['customer'].first_name
                                            + ' - ' + record['customer'].contact_number }}</span>
                                        <span v-if="column.field === 'payment_amount'">
                                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }} {{ record['payment_amount'] }}</span>
                                        <span v-if="column.field === 'payment_type'">{{ record['payment_type']
                                            }}</span>

                                    </td>
                                    <td class="border px-4 py-2 sm:text-[14px] text-xs sm:text-center">
                                        <div class="flex justify-center">
                                            <!--More actions-->
                                            <div class="flex items-center relative">
                                                <button v-if="!record.editing" @click="startEdit(record)"
                                                    class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-solid fa-pencil"
                                                        style="color: #3b82f6;" />
                                                    <span class="px-2">Edit</span>
                                                </button>
                                                <button v-if="!record.editing && checkRoles(['admin'])"
                                                    @click="confirmDelete(index)"
                                                    class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                    <font-awesome-icon icon="fa-regular fa-trash-can"
                                                        style="color: #ef4444" />
                                                    <span class="px-2">Delete</span>
                                                </button>
                                                <!-- <button @click.stop="displayAction(index)"
                                                class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                            </button> -->
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-8 absolute bg-slate-200 divide-y divide-gray-100 rounded-lg shadow-lg items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700" aria-labelledby="dropdownDefaultButton">

                                                    <li :class="{ 'hidden': record.status === '1' }">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li :class="{ 'hidden': record.status === '1' }">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>

                                    </td>
                                </tr>
                                <tr class="items-center justify-center flex border" v-if="!data || data.length === 0">
                                    <td v-if="Object.keys(filteredBy).length === 0" colspan="5">
                                        <button
                                            class="text-green-600 text-[20px] font-bold p-2 hover:bg-gray-100 w-full"
                                            @click="openPayment">
                                            + Create Payment
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <!--card view-->
                    <div v-if="items_category === 'list'"
                        class="grid grid-cols-1 sm:grid-cols-2  lg:grid-cols-3 xl:grid-cols-3 2xl:grid-cols-5 gap-2 sm:gap-4 custom-scrollbar-hidden mt-2 sm:mt-4">
                        <div v-for="(record, index) in data" :key="index" class="w-full">
                            <div
                                class="max-w-md mx-auto shadow-lg bg-white rounded-xl border border-gray-200 md:max-w-2xl">
                                <!-- Top Section -->
                                <div class="flex justify-between items-center px-4 py-2">
                                    <!-- Left Side (Can be your dynamic content) -->
                                    <div class="text-xs text-red-500 items-center cursor-pointer"
                                        :title="formatDateTime(record['payment_date'])">
                                        <p>{{ calculateDaysAgo(formatDate(record['payment_date'])) }}</p>
                                    </div>
                                    <!-- Right Side (Actions) -->
                                    <div class="flex space-x-4 relative items-center">
                                        <!--category-->
                                        <!-- <div v-if="record.expense_type.name" class="text-xs">
                                    <p class="bg-gray-300 rounded px-2 py-1">{{ record.expense_type.name }}</p>
                                </div> -->

                                        <div class="flex justify-center">
                                            <!--More actions-->
                                            <div class="relative">
                                                <button @click.stop="displayAction(index)"
                                                    class="px-2 py-1 bg-blue-100 text-blue-800 rounded">
                                                    <font-awesome-icon icon="fa-solid fa-ellipsis-vertical" size="lg" />
                                                </button>
                                            </div>
                                            <div v-if="display_option === index" :ref="'dropdown' + index"
                                                class="z-10 mt-8 absolute bg-white right-0 divide-y divide-gray-100 rounded-lg shadow-inner shadow-gray-400 border border-gray-400 items-center"
                                                style="display: flex; justify-content: center; align-items: center;">
                                                <ul class="py-1 text-gray-700 text-xs"
                                                    aria-labelledby="dropdownDefaultButton">

                                                    <li :class="{ 'hidden': record.status === '1' }"
                                                        class="hover:bg-gray-300">
                                                        <button v-if="!record.editing" @click="startEdit(record)"
                                                            class="text-blue-500 px-2 py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-solid fa-pencil"
                                                                style="color: #3b82f6;" size="lg" />
                                                            <span class="px-2">Edit</span>
                                                        </button>
                                                    </li>
                                                    <li :class="{ 'hidden': record.status === '1' }"
                                                        class="hover:bg-gray-300">
                                                        <button v-if="!record.editing && checkRoles(['admin'])"
                                                            @click="confirmDelete(index)"
                                                            class="text-red-500 px-2  py-1 flex justify-center items-center">
                                                            <font-awesome-icon icon="fa-regular fa-trash-can"
                                                                style="color: #ef4444" />
                                                            <span class="px-2">Delete</span>
                                                        </button>
                                                    </li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="px-4 py-2">
                                    <!-- Customer Details (Can be your dynamic content) -->
                                    <div class="flex items-center -mt-4">
                                        <div class="h-10 w-10 rounded-full flex items-center justify-center text-white font-bold mr-4"
                                            :class="{ 'bg-blue-500': index % 3 === 0 && index > 1, 'bg-lime-600': index % 2 === 0 && index > 1, 'bg-green-800': index % 2 !== 0 && index % 3 !== 0 && index > 0, 'bg-blue-600': index === 0 }">
                                            {{ record.customer && record.customer.first_name ?
                                                record.customer.first_name[0].toUpperCase() :
                                                'C' }}
                                        </div>
                                        <div>
                                            <h4 class="leading-6 font-semibold text-gray-900">
                                                {{ record.customer && record.customer.first_name ?
                                                    record.customer.first_name + ' ' + (record.customer.last_name ?
                                                        record.customer.last_name : '') : '' }}</h4>
                                            <p class="text-gray-500 cursor-pointer text-sm"
                                                @click="dialPhoneNumber(record.customer.contact_number)">+91-{{
                                                    record.customer.contact_number }}</p>
                                        </div>
                                    </div>

                                </div>
                                <!--expense data-->
                                <div class="flex justify-between items-center px-4 py-1">
                                    <!--date data-->
                                    <div>
                                        <p class="pr-1 text-gray-400">Payment Type: </p>
                                        <p>{{ record['payment_type'] }} </p>
                                    </div>
                                    <div>
                                        <p class="pr-1 text-gray-400">Amount:</p>
                                        <p class="text-green-700">
                                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                                currentCompanyList.currency }} {{ record['payment_amount'] }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--no data found-->
                        <div v-if="pagination && this.pagination.last_page > 0 && this.pagination.last_page === this.pagination.current_page && isMobile && !open_skeleton_isMobile"
                            class="text-sm mb-[100px]">
                            <p class="font-bold text-center py-2 text-green-700">
                                <font-awesome-icon icon="fa-solid fa-hand" size="lg" style="color: green" />
                                Finished !
                            </p>
                        </div>
                    </div>
                    <!--loader-->
                    <skeleton class="mt-2" v-if="open_skeleton_isMobile" :isLoading="open_skeleton_isMobile"
                        :cols="items_category === 'tile' ? number_of_columns : 3"
                        :rows="items_category === 'tile' ? number_of_rows : 10" :gap="gap"
                        :type="items_category === 'tile' ? 'table' : 'grid'">
                    </skeleton>
                    <!---Filter total payment In-->
                    <div v-if="Object.keys(filteredBy).length > 0 && this.data.length > 0">
                        <p class="font-bold text-md text-center my-2">Total Amount:
                            {{ currentCompanyList && currentCompanyList.currency === 'INR' ? '\u20b9' :
                                currentCompanyList.currency }} {{
                                getTotalExpenses }}</p>
                    </div>
                    <div class="pagination flex items-center justify-between mt-4 sm:text-md text-xs"
                        v-if="pagination && !isMobile">
                        <p class="text-sm text-gray-700">
                            Showing {{ ((pagination.current_page - 1) * pagination.per_page) + 1 }} to
                            {{ Math.min(pagination.current_page * pagination.per_page, pagination.total) }} of
                            {{ pagination.total }} entries
                        </p>
                        <ul class="flex list-none">
                            <li class="mr-2 rounded-bl-[20px] rounded-tl-[20px]"
                                :class="{ 'bg-gray-500': currentPage === 1, 'bg-teal-600 hover:bg-teal-500': currentPage !== 1 }">
                                <button @click="updatePage(currentPage - 1)" :disabled="currentPage === 1"
                                    class="flex px-3 py-2 text-white justify-between sm:text-md text-xs items-center rounded-bl-[20px] rounded-tl-[20px]">
                                    <font-awesome-icon icon="fa-solid fa-arrow-left" />
                                    <span class="pl-1" v-if="!isMobile">Prev</span>
                                </button>
                            </li>
                            <li v-for="pageNumber in visiblePageNumbers" :key="pageNumber">
                                <button @click="updatePage(pageNumber)"
                                    :class="{ 'bg-teal-600 text-white': pageNumber === currentPage }"
                                    class="px-3 py-2 rounded text-black hover:bg-teal-500 sm:text-md text-xs">
                                    {{ pageNumber }}</button>
                            </li>
                            <li class="ml-2 rounded-br-[20px] rounded-tr-[20px]"
                                :class="{ 'bg-gray-500': currentPage === pagination.last_page, 'bg-teal-600 hover:bg-teal-500': currentPage !== pagination.last_page }">
                                <button @click="updatePage(currentPage + 1)"
                                    :disabled="currentPage === pagination.last_page"
                                    class="flex px-3 py-2 text-white rounded-br-[20px] rounded-tr-[20px] sm:text-md text-xs">
                                    <span class="pr-1" v-if="!isMobile">Next</span>
                                    <font-awesome-icon icon="fa-solid fa-arrow-right" />
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
            <!--in case empty-->
            <div v-if="!open_skeleton && data && data.length === 0">
                <div class="flex justify-center items-center">
                    <img class="w-64 h-64" :src="empty_data" alt="image empty states">
                </div>
                <p class="text-2xl font-bold text-gray-500 text-center">No Data Found</p>
            </div>
        </div>
        <!-- <div v-if="isMobile" class="fixed bottom-36 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="toggleFilterSelected" type="button"
                    class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-filter" size="xl" />
                </button>
            </div>
        </div> -->
        <!---in mobile view create new expense-->
        <div v-if="isMobile" class="fixed bottom-20 right-5 z-50 bg-green-700 text-white rounded-full">
            <div class="flex justify-end">
                <button @click="openPayment" type="button" class="flex items-center justify-center px-[10px] py-2">
                    <font-awesome-icon icon="fa-solid fa-plus" size="xl" />
                </button>
            </div>
        </div>
        <confirmbox :show-modal="open_confirmBox" @onConfirm="deleteRecord" @onCancel="cancelDelete"></confirmbox>

        <!--filter-->
        <!-- <expenseFilter :showModal="expense_filter" :companyId="companyId" :userId="userId" @closeFilter="closeFilter">
        </expenseFilter> -->
        <Loader :showModal="open_loader"></Loader>
        <!-- <bottombar v-if="isMobile" :selected_btn_btm="'categories'"></bottombar> -->
        <paymentInDialog :showModal="open_payment" :sales_data="sales_payment_data" @onCancel="closePayment"
            :type="'payment_in'" :currentCompanyList="currentCompanyList">
        </paymentInDialog>
        <Toaster :show="show" :message="message" :type="type_toaster" @update:show="show = false"></Toaster>
    </div>
</template>
<script>
import confirmbox from '@/components/supporting/dialog_box//confirmbox.vue';
// import expenseFilter from '@/components/supporting/dialog_box/filter_Modal/expenseFilter.vue';
import paymentInDialog from '@/components/supporting/dialog_box/paymentInDialog.vue';
// import bottombar from '@/components/supporting/dashboard/bottombar.vue';
import { mapActions, mapGetters } from 'vuex';
import searchCustomer from '@/components/supporting/customers/searchCustomer.vue';
export default {
    emits: ['updateIsOpen'],
    components: {
        confirmbox,
        // expenseFilter,
        // bottombar,
        paymentInDialog,
        searchCustomer
    },
    props: {
        isMobile: Boolean,
        isAndroid: Boolean,
        companyId: String,
        userId: String,
        store_refresh: Boolean,
        updateModalOpen: Boolean
    },
    data() {
        return {
            empty_data: '/images/dashboard/empty.svg',
            options: [2, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100],
            recordsPerPage: 10,
            currentPage: 1,
            open_confirmBox: false,
            deleteIndex: null,
            //---
            isDropdownOpen: false,
            isFilterDropdownOpen: false,
            columns: [],
            data: [],
            originalData: [],
            open_message: false,
            message: '',
            //----expense----
            open_expense_model: false,
            expense_type: 'add',
            edit_record: null,
            //---filter--
            expense_filter: false,
            filteredBy: {},
            pagination: {},
            //--skeleton
            open_skeleton: false,
            number_of_columns: 5,
            number_of_rows: 10,
            gap: 5,
            open_loader: false,
            now: null,
            items_category: 'tile',
            open_skeleton_isMobile: false,
            display_option: false,
            open_skeleton_isMobile: false,
            //---payment in edit
            sales_payment_data: {},
            open_payment: false,
            //--toaster---
            show: false,
            type_toaster: 'success',
            //---table data filter asending and decending----
            is_filter: false,
            short_visible: [],
            customer_id: null,
            resetSearch: false,
        }
    },

    computed: {
        ...mapGetters('paymentInList', ['currentPaymentInList']),
        ...mapGetters('localStorageData', ['currentLocalDataList']),
        ...mapGetters('dataFilter', ['filteredData', 'pageStatus', 'originalDataFilter', 'typeData', 'filterTypeKey']),
        ...mapGetters('companies', ['currentCompanyList']),
        ...mapGetters('recordsPerPage', ['recordsPerPageCount']),
        paginatedData() {
            if (this.data && this.data.length !== 0) {
                const startIndex = (this.currentPage - 1) * this.recordsPerPage;
                const endIndex = startIndex + this.recordsPerPage;
                const filteredData = this.data
                this.length_category = filteredData.length;
                return filteredData.slice(startIndex, endIndex);
            }
        },
        totalPages() {
            if (this.data && this.data.length !== 0) {
                const totalFilteredRecords = this.data.length;
                return Math.ceil(totalFilteredRecords / this.recordsPerPage);
            }
        },
        dynamicFields() {
            const fields = [];
            const order = ['payment_date', 'customer', 'payment_amount', 'payment_type'];
            // Helper function to capitalize the first letter of each word
            const capitalizeFirstLetter = (str) => {
                return str.replace(/_/g, ' ').replace(/\b\w/g, (match) => match.toUpperCase());
            };

            // Helper function to format the label based on specific cases
            const formatLabel = (key) => {
                const words = key.split(/(?=[A-Z])|_/);
                const formattedLabel = words.map(word => capitalizeFirstLetter(word)).join(' ');
                return formattedLabel;
            };

            // Iterate over the first record in data to get field names
            if (this.data) {
                for (const key of order) {
                    if (key !== 'id') {
                        const label = formatLabel(key);
                        if (key !== 'attachment') {
                            fields.push({ label, field: key, visible: true });
                        } else {
                            fields.push({ label, field: key, visible: false });
                        }
                    }
                }
                this.columns = fields;
                return fields;
            } else {
                return
            }
        },
        visiblePageNumbers() {
            // Calculate the range of page numbers to display
            const startPage = Math.floor((this.currentPage - 1) / 10) * 10 + 1; // Start from multiples of 10
            const endPage = Math.min(startPage + 9, this.pagination.last_page); // Display 10 pages or less if not available

            // Generate an array of page numbers to display
            return Array.from({ length: endPage - startPage + 1 }, (_, index) => startPage + index);
        },
        getTotalExpenses() {
            if (!this.data || this.data.length === 0) {
                return 0; // Return 0 if there are no expenses
            }
            // Using parseFloat to ensure proper addition of floating-point numbers
            let total = this.data.reduce((sum, expense) => sum + parseFloat(expense.payment_amount), 0);
            return total;
        }
    },

    methods: {
        ...mapActions('paymentInList', ['fetchPaymentInList']),
        ...mapActions('localStorageData', ['validateRoles']),
        ...mapActions('localStorageData', ['fetchLocalDataList']),
        ...mapActions('dataFilter', ['fetchData', 'updateFilter', 'updateFilterParams', 'fetchDataParams']),
        ...mapActions('companies', ['fetchCompanyList', 'updateCompanyList']),
        ...mapActions('recordsPerPage', ['updateRecordsPerPage', 'initialize']),
        ...mapActions('apiUpdates', ['fetchApiUpdates', 'updateKeyWithTime', 'updateKeyIsUpdate']),

        validateDateTime(dateTimeString) {
            const datePart = dateTimeString.substring(0, 10);
            const isValidDate = /^\d{4}-\d{2}-\d{2}$/.test(datePart);
            return isValidDate;
        },
        //---get expenses list---
        getPaymentsList(page, per_page, is_delete) {
            if (page == 1) {
                this.fetchPaymentInList({ page, per_page, customer_id: null, is_delete });
                if (this.currentPaymentInList && this.currentPaymentInList.data && !is_delete) {
                    this.data = this.currentPaymentInList.data;
                    this.pagination = this.currentPaymentInList.pagination;
                }
            } else {
                this.open_skeleton = true;
                if (this.isMobile && page === 1 && per_page < 20) {
                    per_page = 20;
                }
                axios.get('/sales_payments', { params: { company_id: this.companyId, page: page, per_page: per_page } })
                    .then(response => {
                        // console.log(response.data, 'Response...!');
                        if (Object.keys(this.filteredBy).length === 0) {
                            this.data = response.data.data;
                            this.pagination = response.data.pagination;
                            this.open_skeleton = false;
                        } else {
                            // Apply filters to the response data
                            const filteredData = response.data.data.filter(expense => {
                                // Check if the expense matches the filters
                                let matchesFilters = true;
                                // console.log(this.filteredBy.start_date, 'Helloooo', this.filteredBy.end_date);
                                // Check if start_date and end_date are in between the provided dates
                                if (this.filteredBy.start_date && this.filteredBy.end_date) {
                                    const startDate = new Date(expense.date);
                                    const endDate = new Date(expense.date);
                                    const filterStartDate = new Date(this.filteredBy.start_date);
                                    const filterEndDate = new Date(this.filteredBy.end_date);
                                    matchesFilters = startDate >= filterStartDate && endDate <= filterEndDate;
                                    // console.log(matchesFilters, 'Date filters');
                                }
                                // Check if expense_type matches the filtered value
                                if (this.filteredBy.expense_type !== undefined) {
                                    matchesFilters = matchesFilters && expense.expense_type.id === this.filteredBy.expense_type;
                                    // console.log(matchesFilters, 'expense type filters');
                                }

                                return matchesFilters;
                            });
                            // console.log(filteredData, 'What happning...!');
                            // Set the filtered data and pagination
                            this.data = filteredData;
                            this.pagination = response.data.pagination;
                            this.open_skeleton = false;

                        }
                    })
                    .catch(error => {
                        console.error('Error', error);
                        this.open_skeleton = false;
                    })
            }
        },

        updatePage(pageNumber) {
            if (pageNumber >= 1 && pageNumber <= this.pagination.last_page) {
                this.currentPage = pageNumber;
            }
        },

        //---delete the record
        deleteRecord() {
            try {
                if (this.deleteIndex !== undefined && this.deleteIndex !== null) {
                    axios.delete(`/sales-payments/delete-by-code/${this.data[this.deleteIndex].payment_code}`, {
                        params: {
                            company_id: this.companyId
                        }
                    })
                        .then(response => {
                            // console.log(response.data, 'Response data...!');
                            this.open_confirmBox = false;
                            this.deleteIndex = null;
                            this.getPaymentsList((this.data.length > 1 ? this.currentPage - 1 : 1), this.recordsPerPage, true);
                        })
                        .catch(error => {
                            console.error('Error', error);
                            this.open_confirmBox = false;
                        })
                }
            } catch (error) {
                console.error('Error', error);
            }
        },
        confirmDelete(index) {
            // console.log(index, 'What happening...', this.data);
            this.deleteIndex = index;
            this.open_confirmBox = true;
        },
        cancelDelete() {
            this.open_confirmBox = false;
        },

        reloadPage() {
            window.location.reload();
        },
        //--setting
        toggleDropdown() {
            this.isDropdownOpen = !this.isDropdownOpen;
            if (this.isDropdownOpen) {
                // Add event listener when dropdown is opened
                document.addEventListener('click', this.handleOutsideClick);
            } else {
                // Remove event listener when dropdown is closed
                document.removeEventListener('click', this.handleOutsideClick);
            }
        },
        handleOutsideClick(event) {
            // const isClickInside = this.$refs.settingOPtion.contains(event.target) || this.$refs.settingOPtion.nextElementSibling.contains(event.target);
            // if (!isClickInside) {
            //     this.toggleDropdown();
            // }
            if (!this.$refs.settingOPtion) {
                // Exit early if the reference is not available            
                this.isDropdownOpen = false;
                return;
            }
            const isClickInside = this.$refs.settingOPtion.contains(event.target);
            if (!isClickInside) {
                this.isDropdownOpen = false;
                // this.toggleDropdown();
            }
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },


        refreshPage() {
            window.location.reload();
        },

        //---check emty object
        isEmptyObject(obj) {
            return Object.keys(obj).length === 0 && obj.constructor === Object;
        },

        //---close the message--
        //---message box---
        closeMessage() {
            this.open_message = false;
        },
        //---record---
        startEdit(record) {
            // console.log(record);
            this.sales_payment_data = record;
            this.open_payment = true;
        },

        //------Open filter---
        //---filter--- 
        toggleFilterSelected() {
            this.expense_filter = true;
            this.resetTheFilter();
        },
        //--close lead
        closeFilter(searchData) {
            // console.log(searchData, 'What is happening.....!');
            if (searchData) {
                this.filteredBy = { ...searchData };
                this.getPaymentsList(1, 'all');
            }

            this.expense_filter = false;
        },
        //--get formayt date
        formatDate(dateString) {
            // Create a new Date object using the components in the correct order (year, month - 1, day)
            const date = new Date(dateString);
            // Get the parts of the date (year, month, day) in the correct format
            const formattedYear = date.getFullYear();
            const formattedMonth = String(date.getMonth() + 1).padStart(2, '0'); // Month is zero-indexed
            const formattedDay = String(date.getDate()).padStart(2, '0');
            // Concatenate the parts with dashes to form the desired format
            return `${formattedMonth}/${formattedDay}/${formattedYear}`;
        },
        //---reset filter--
        resetTheFilter() {
            this.filteredBy = {};
            this.getPaymentsList(1, this.recordsPerPage);
        },
        //---open expense modal
        openPayment() {
            // this.expense_type = 'add';
            // this.open_expense_model = true;
            this.$router.push({ name: 'payment_in' });
        },
        //--close expense modal
        closeExpenseModel(data) {
            try {
                if (data && data.id) {
                    if (this.expense_type === 'add') {
                        this.data.unshift(data)
                    } else if (this.expense_type === 'edit') {
                        let find_data = this.data.findIndex(opt => opt.id === data.id);
                        if (find_data !== -1) {
                            this.data.splice(find_data, 1, data);
                        }
                    }
                }
                this.open_expense_model = false;
                // console.log('hhhhhhhhh');
            } catch (error) {
                console.error('Error'.error);
                this.open_expense_model = false;
            }
        },
        //---display action
        displayAction(index) {
            if (this.display_option === index) {
                this.display_option = null; // Close dropdown if already open
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            } else {
                this.display_option = index; // Open dropdown
                // Attach event listener to close dropdown when clicking outside
                document.addEventListener('click', this.handleClickOutside);
            }
        },
        handleClickOutside(event) {
            // Check if the click is outside the dropdown
            const dropdownRef = this.$refs['dropdown' + this.display_option][0];
            // console.log(dropdownRef, 'What happening......!!!', event.target);
            if (dropdownRef && !dropdownRef.contains(event.target)) {
                this.display_option = null; // Close dropdown
                // Remove event listener to avoid memory leaks
                document.removeEventListener('click', this.handleClickOutside);
            }
        },
        formatDateTime(dynamicDate) {
            // Convert the dynamicDate string to a Date object (assuming it's in UTC)
            const trimmedDate = dynamicDate.replace('.000000Z', '');
            const date = new Date(trimmedDate);

            // Extract date and time components
            const day = ('0' + date.getDate()).slice(-2);
            const month = ('0' + (date.getMonth() + 1)).slice(-2);
            const year = date.getFullYear();
            const hours = date.getHours();
            const minutes = ('0' + date.getMinutes()).slice(-2);
            const period = hours >= 12 ? 'PM' : 'AM';
            const formattedHours = hours % 12 || 12;

            // Construct the formatted date and time string
            const formattedDateTime = `${day}-${month}-${year} ${formattedHours}:${minutes} ${period}`;

            return formattedDateTime;
        },

        //---formated display date---
        formattedDate(timestamp, add) {
            const date = new Date(timestamp);
            if (!add) {
                date.setHours(date.getHours() + 5); // Add 5 hours
                date.setMinutes(date.getMinutes() + 30); // Add 30 minutes
            } else {
                date.setHours(date.getHours());
                date.setMinutes(date.getMinutes());
            }
            return date.toISOString().slice(0, 16).replace('T', ' ');
        },
        calculateDaysAgo(data) {
            const dateTime = new Date(data); // Replace with your datetime
            if (this.now) {
                const diff = this.now.getTime() - dateTime.getTime();
                const secondsAgo = Math.floor(diff / 1000);
                const minutesAgo = Math.floor(secondsAgo / 60);
                const hoursAgo = Math.floor(minutesAgo / 60);
                const daysAgo = Math.floor(hoursAgo / 24);

                if (daysAgo > 1) {
                    // More than 1 day ago
                    return `${daysAgo} days ago`;
                } else if (daysAgo === 1) {
                    // Yesterday
                    return 'Yesterday';
                } else if (hoursAgo >= 1) {
                    // X hours ago
                    return `${hoursAgo} hours ago`;
                } else if (minutesAgo >= 1) {
                    // X minutes ago
                    return `${minutesAgo} min ago`;
                } else {
                    // Less than a minute ago
                    return 'Just now';
                }
            }
        },
        //---is mobile view auto load page data
        handleScroll() {
            if (this.isMobile) {
                const container = this.$refs.scrollContainer;
                const scrollPosition = container.scrollTop + container.clientHeight;
                const scrollHeight = container.scrollHeight;

                // Check if user has scrolled to 50% of the scrollable container
                if (scrollPosition >= scrollHeight * 0.5 && this.pagination.last_page > this.pagination.current_page && !this.open_skeleton_isMobile) {
                    this.open_skeleton_isMobile = true;
                    setTimeout(() => {
                        this.loadNextPage(this.pagination.current_page + 1);
                    }, 1000)
                }
            }
        },
        loadNextPage(page) {
            axios.get('/sales_payments', { params: { company_id: this.companyId, page: page, per_page: this.recordsPerPage } })
                .then(response => {
                    // console.log(response.data, 'Status Data');
                    if (response.data) {
                        this.pagination = response.data.pagination;
                        this.data = [...this.data, ...response.data.data];
                    }
                    this.open_skeleton_isMobile = false;
                })
                .catch(error => {
                    console.error('Error', error);
                    this.open_loader = false;
                })
        },

        //---validate the roles--
        checkRoles(roles) {
            if (this.currentLocalDataList && this.currentLocalDataList.roles && Array.isArray(this.currentLocalDataList.roles)) {
                return roles.includes(this.currentLocalDataList.roles[0]);
            }
            return false;
        },
        //---close payment in---
        closePayment(data) {
            if (data && data.id && this.sales_payment_data && this.sales_payment_data.id) {
                let find_index = this.data.findIndex(opt => opt.id === this.sales_payment_data.id);
                if (find_index !== -1) {
                    this.data[find_index] = data;
                    this.message = 'Payment in updated successfully..!';
                    this.show = true;
                }

                this.open_payment = false;
            } else {
                this.open_payment = false;
            }
        },
        //---toggle view---
        toggleView() {
            if (this.items_category === 'tile') {
                this.items_category = 'list';
            } else {
                this.items_category = 'tile'
            }
        },
        //--close all modal---
        closeAllModals() {
            this.open_confirmBox = false;
            this.open_payment = false;
        },
        //---filter ascending / descending order
        filterDataBy(type, key) {
            this.showSortIcons(false, type, key);
            if (type == 'asc') {
                if (this.is_filter) {
                    if (this.typeData === 'payment_in' && this.filterTypeKey && this.filterTypeKey.type == 'asc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! here will filter second asending order data', this.filteredData);
                        this.updateFilter({ key: key, type: 'asc' });

                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'payment_in', filterParams: { key: key, type: 'asc' } });
                    // console.log(this.is_filter, 'What happening.....! else part', this.filteredData);
                }
            }
            else if (type == 'desc') {
                if (this.is_filter) {
                    if (this.typeData === 'payment_in' && this.filterTypeKey && this.filterTypeKey.type == 'desc' && this.filterTypeKey.key == key) {
                        // console.log(this.originalDataFilter, 'here will get original value...');
                        this.data = [...this.originalDataFilter];
                        this.updateFilterParams({ key: '', type: '' });
                    } else {
                        // console.log(this.is_filter, 'What happening.....! desending', this.filteredData);
                        this.updateFilter({ key: key, type: 'desc' });
                    }
                } else {
                    this.is_filter = true;
                    this.fetchData({ data: this.data, type: 'payment_in', filterParams: { key: key, type: 'desc' } });
                    // console.log(this.is_filter, 'What happening.....! desending else part', this.filteredData);
                }
            }
        },
        //---reset filter--
        resetTheFilter() {
            this.data = this.originalData;
            this.filteredBy = {};
            this.customer_id = '';
            this.resetSearch = true;
            this.fetchPaymentInList({ page: this.currentPage, per_page: this.recordsPerPage });
        },
        //-----Search functions------
        selectedCustomer(selectedData) {
            if (selectedData && selectedData.id) {
                this.customer_id = selectedData.id;
                this.filteredBy = { first_name: selectedData.first_name, contact_number: selectedData.contact_number };
                this.fetchPaymentInList({ page: 1, per_page: this.recordsPerPage, customer_id: this.customer_id });
            }
        },
        resetToSearch() {
            this.customer_id = '';
            this.filteredBy = {};
            if (!this.resetSearch) {
                this.fetchPaymentInList({ page: 1, per_page: this.recordsPerPage });
            }
            this.resetSearch = false;

        },

        //--short icon for filter---
        showSortIcons(data, type, key) {
            if (data) {
                this.short_visible.forEach(opt => {
                    if (opt.label === data.field) {
                        opt.visible = true;
                    }
                });
            } else if (type && key) {
                this.short_visible.forEach(opt => {
                    if (opt.label === key) {
                        opt.type = type;
                    } else {
                        opt.type = '';
                    }
                });
            }
        },
        hideSortIcons(data) {
            if (data) {
                this.short_visible.forEach(opt => {
                    if (opt.label === data.field) {
                        opt.visible = false;
                    }
                });
            }
        },
        //--reload the table data----
        refreshDataTable() {
            // this.open_skeleton = true;
            this.fetchPaymentInList({ page: 1, per_page: this.recordsPerPage });
            this.showSortIcons(false, 'type', 'key');
        },
        //--filter--
        getSortType(column) {
            if (column && this.short_visible && this.short_visible.length > 0) {
                // Find the relevant short_visible entry
                const shortVisibleEntry = this.short_visible.find(
                    (entry) => entry.label === column.field
                );
                if (!shortVisibleEntry) {
                    return ''; // Default sort type if not found
                }
                // Determine sort type based on current type
                if (shortVisibleEntry.type === '') {
                    this.filterDataBy('asc', column.field);
                } else if (shortVisibleEntry.type === 'asc') {
                    this.filterDataBy('desc', column.field);
                } else if (shortVisibleEntry.type === 'desc') {
                    this.filterDataBy('asc', column.field);
                }
            }
        },
        isShortVisible(field) {
            const entry = this.short_visible.find((item) => item.label === field);
            if (entry) {
                return entry.visible || entry.type !== '';
            } else {
                return false;
            }
        },
        getSortTypeDisplay(field) {
            const entry = this.short_visible.find((item) => item.label === field);
            if (entry) {
                return entry?.type || ''; // Returns 'asc', 'desc', ,or ''
            } else {
                return false;
            }
        },
        isHighlighted(field, type) {
            const entry = this.short_visible.find((item) => item.label === field);
            if (entry) {
                return entry.type !== '';
            } else {
                return false;
            }
        },
        //---on key press to make pagination--
        handleKeyPress(event) {
            if (event.key === "ArrowRight" && this.currentPage < this.pagination.last_page) {
                this.currentPage++;
            } else if (event.key === "ArrowLeft" && this.currentPage > 1) {
                this.currentPage--;
            }
        }
    },
    mounted() {
        this.fetchApiUpdates();
        this.fetchCompanyList();
        this.initialize();
        this.recordsPerPage = this.recordsPerPageCount ? this.recordsPerPageCount : 10;
        const view = localStorage.getItem('payment_home');
        if (view) {
            let parse_data = JSON.parse(view);
            if (parse_data.view) {
                this.items_category = parse_data.view;
            }
        }
        if (this.isMobile && this.recordsPerPage < 15) {
            this.recordsPerPage = 20;
        }
        if (this.currentPaymentInList && this.currentPaymentInList.data && this.currentPaymentInList.data.length > 0) {
            // this.open_skeleton = true;
            this.fetchPaymentInList({ page: 1, per_page: this.recordsPerPage });
            this.data = this.currentPaymentInList.data;
            this.pagination = this.currentPaymentInList.pagination;
        } else {
            if (this.currentPaymentInList && Object.keys(this.currentPaymentInList).length == 0) {
                this.open_skeleton = true;
                this.fetchPaymentInList({ page: 1, per_page: this.recordsPerPage });
            }
        }
        if (this.currentLocalDataList && Object.keys(this.currentLocalDataList).length > 0) {
            this.fetchLocalDataList();
        } else {
            this.fetchLocalDataList();
        }
        setInterval(() => {
            this.now = new Date();
        }, 1000);
        //---create short list array--
        this.short_visible = ['payment_date', 'customer', 'payment_amount', 'payment_type'].map((field) => ({
            label: field,
            type: '',
            visible: false,
        }));
        // Add and remove event listeners
        window.addEventListener("keydown", this.handleKeyPress);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.handleClickOutside());
        document.removeEventListener('click', this.handleOutsideClick());
        window.removeEventListener("keydown", this.handleKeyPress);
    },
    watch: {
        companyId: {
            deep: true,
            handler(newValue) {
                this.getPaymentsList(this.currentPage, this.recordsPerPage);
            }
        },
        recordsPerPage: {
            deep: true,
            handler(newValue) {
                if (newValue && this.recordsPerPageCount !== newValue) {
                    this.updateRecordsPerPage(newValue);
                }
                if (this.currentPage == 1) {
                    this.getPaymentsList(1, newValue);
                }
                this.currentPage = 1;
            }
        },
        recordsPerPageCount: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.recordsPerPage = newValue;
                }
            }
        },
        currentPage: {
            deep: true,
            handler(newValue) {
                this.getPaymentsList(newValue, this.recordsPerPage);
            }
        },
        isMobile: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.items_category = 'list';
                    this.recordsPerPage = this.recordsPerPage < 20 ? 20 : this.recordsPerPage;
                }
            }
        },
        items_category: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    localStorage.setItem('payment_home', JSON.stringify({ view: newValue }));
                }
            }
        },
        store_refresh: {
            deep: true,
            handler(newValue) {
                this.resetTheFilter();
                this.fetchPaymentInList({ page: 1, per_page: this.recordsPerPage });
            }
        },
        currentPaymentInList: {
            deep: true,
            handler(newValue) {
                if (newValue) {
                    this.data = newValue.data;
                    this.pagination = newValue.pagination;
                    this.open_skeleton = false;
                }
            }
        },
        //-----navigate controll---
        updateModalOpen: {
            deep: true,
            handler(newValue) {
                this.closeAllModals();
            }
        },
        //---modalbox data----
        open_confirmBox: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        open_payment: {
            deep: true,
            handler(newValue) {
                this.$emit('updateIsOpen', newValue);
            }
        },
        filteredData: {
            deep: true,
            handler(newValue) {
                if (newValue && newValue.length > 0 && this.is_filter) {
                    this.data = [...newValue];

                }
            }
        },
    },
}
</script>

<style scoped>
.refresh-button {
    position: relative;
    cursor: pointer;
}

.refresh-button:hover::before {
    content: attr(title01);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    white-space: nowrap;
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

.info-msg {
    position: relative;
    cursor: pointer;
}

.info-msg:hover::before {
    content: attr(title02);
    position: absolute;
    top: 120%;
    left: 50%;
    transform: translateX(-50%);
    padding: 3px 5px;
    background-color: rgb(116, 114, 114);
    color: white;
    border-radius: 4px;
    width: 200px;
    /* Adjust the max-width as needed */
    white-space: pre-wrap;
    /* Use pre-wrap to preserve line breaks and spaces */
    opacity: 1;
    transition: opacity 0.3s ease-in-out;
    pointer-events: none;
    font-size: 12px;
}

@media (max-width: 640px) {
    .table-container {
        overflow-x: auto;
        white-space: nowrap;
        max-width: 100%;
    }
}
</style>